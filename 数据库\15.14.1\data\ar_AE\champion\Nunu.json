{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nunu": {"id": "<PERSON><PERSON><PERSON>", "key": "20", "name": "نونو وويلامب", "title": "الفتى ورجله الثلجي", "image": {"full": "Nunu.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "20000", "num": 0, "name": "default", "chromas": false}, {"id": "20001", "num": 1, "name": "نونو وويلامب القدم الكبيرة", "chromas": false}, {"id": "20002", "num": 2, "name": "نونو وويلامب الورشة", "chromas": false}, {"id": "20003", "num": 3, "name": "نونو وويلامب الوسخ", "chromas": false}, {"id": "20004", "num": 4, "name": "روبوت نونو وويلامب", "chromas": false}, {"id": "20005", "num": 5, "name": "نونو وويلامب المهدم", "chromas": false}, {"id": "20006", "num": 6, "name": "نونو وويلامب TPA", "chromas": false}, {"id": "20007", "num": 7, "name": "نونو وويلامب الزومبي", "chromas": false}, {"id": "20008", "num": 8, "name": "نونو وويلامب الورقي", "chromas": true}, {"id": "20016", "num": 16, "name": "نونو وويلامب راقصا الفضاء", "chromas": true}, {"id": "20026", "num": 26, "name": "نونو وويلامب النحلة", "chromas": true}, {"id": "20035", "num": 35, "name": "نونو وويلامب الفارسان الكونيان", "chromas": true}, {"id": "20044", "num": 44, "name": "نونو وويلامب ليلة الرعب", "chromas": true}], "lore": "كان هناك ذات يوم صبي أراد أن يثبت بطولته عبر قتل وحش مخيف – إلا أنه اكتشف أن ذلك الوحش كان رجل ثلج سحريًا وحيدًا، وكان بحاجة إلى صديق فقط. ومع ارتباطهما سوية بقوة قديمة وبحب مشترك لكرات الثلج، أصبح نونو وويلامب ينتزهان الآن بحرية عبر الفريليورد، حيث يبثان الحياة في مغامرات تخيلية. وهما يأملان بأن يعثرا على والدة نونو في مكان ما من هذا العالم. وإن تمكنا من إنقاذها، فربما يصبحان بطلين في نهاية الأمر...", "blurb": "كان هناك ذات يوم صبي أراد أن يثبت بطولته عبر قتل وحش مخيف – إلا أنه اكتشف أن ذلك الوحش كان رجل ثلج سحريًا وحيدًا، وكان بحاجة إلى صديق فقط. ومع ارتباطهما سوية بقوة قديمة وبحب مشترك لكرات الثلج، أصبح نونو وويلامب ينتزهان الآن بحرية عبر الفريليورد، حيث...", "allytips": ["تسمح قدرة الالتهام لنونو بالبقاء في المسار أمام خصوم بعيدي المدى.", "يمكنك اختيار مقاطعة قدرة الصفر المطلق مبكرًا لإلحاق ضرر جزئي إن أوشك أحد الخصوم على الخروج من المدى.", "غالبًا ما يكون من المفيد تأخير إلقاء قدرة الصفر المطلق إلى أن يتم استخدام الجولة الأولى من المعطلات. حاول البقاء في المؤخرة قبل الاندفاع إلى قتال فرق."], "enemytips": ["يؤدي اعتراض شحن قدرة الصفر المطلق إلى تخفيض قدر الضرر الذي يتلقاه فريقك.", "إن استعمال وميض المستدعي هو وسيلة أكيدة النجاح للإفلات من الصفر المطلق.", "تتحرك أكبر كرة ثلج على الإطلاق بسرعة كبيرة، ولكن لا يمكنها الاستدارة بنفس السرعة، لذا لا تحاول الهرب منها عبر الركض في خط مستقيم.  بل انعطف عدة مرات بشكل حاد ومفاجئ."], "tags": ["Tank", "Mage"], "partype": "المانا", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 90, "mp": 280, "mpperlevel": 42, "movespeed": 345, "armor": 29, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "NunuQ", "name": "استهلاك", "description": "يعض ويلامب تابعًا أو وحشًا أو بطلًا معاديًا، فيلحق ضررًا بالعدو ويعالج نفسه.", "tooltip": "يطلب نونو من ويلامب أن يعض العدو فيلحق <trueDamage>{{ monsterminiondamage }} ضرر حقيقي</trueDamage> ويستعيد <healing>{{ monsterhealing }} الصحة</healing> عند عض تابع أو وحش الأدغال. أما ضد الأبطال، فيلحق بدلًا من ذلك <magicDamage>{{ totalchampiondamage }} ضرر سحر</magicDamage> ويستعيد <healing>{{ championhealing }} الصحة</healing>.<br /><br />يزداد <healing>العلاج</healing> بنسبة {{ lowhealthhealingscalar*100 }}% عندما يمتلك نونو وويلامب مقدارًا أقل من {{ lowhealththreshhold*100 }}% من الصحة.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ضرر الوحش", "ضرر البطل", "علاج", "فترة التبريد"], "effect": ["{{ monsterminiondamage }}-> {{ monsterminiondamageNL }}", "{{ championdamage }}-> {{ championdamageNL }}", "{{ basehealing }}-> {{ basehealingNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " مانا", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "NunuQ.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} مانا"}, {"id": "NunuW", "name": "أكبر كرة ثلج على الإطلاق!", "description": "يشكّل ويلامب كرة ثلج يزداد حجمها وسرعتها مع دحرجته لها.  وتلحق كرة الثلج الضرر بالأعداء وتطيح بهم في الهواء.", "tooltip": "يشكّل نونو وويلامب كرة ثلج يزداد حجمها وسرعتها مع دحرجتهما لها. يتحركان ببطء أثناء دحرجتها، لكن بإمكانهما زيادة سرعة الدوران عن طريق الاستمرار بالضغط على الدوران.<br /><br />تسبب كرة الثلج بين <magicDamage>{{ noimpactsnowballdamage }}</magicDamage> و <magicDamage>{{ maximumsnowballdamage }} ضرر سحر</magicDamage> وتسبب <status>الإطاحة بالهواء</status> لمدة تتراوح من {{ baseknockupduration }} إلى {{ maximumstunduration }} ثوان عند اصطدامها ببطل أو وحش كبير أو حائط. تتزايد هذه القيم مع ازدياد مسافة الدحرجة.<br /><br />يستطيع نونو وويلامب <recast>إعادة الإلقاء</recast> لإطلاق الكرة الثلجية مبكرًا.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الضرر الأساسي", "تكلفة @AbilityResourceName@"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "NunuW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuE", "name": "وابل كرات الثلج", "description": "يرمي نونو عدة كرات ثلج تلحق الضرر بالأعداء.  عند انتهائه، يثبت ويلامب أي بطل أو وحش كبير أصيب بكرة ثلج في مكانه.", "tooltip": "يرمي نونو ثلاث كرات ثلج ملحقًا <magicDamage>{{ totalsnowballdamage }} ضرر سحر</magicDamage> لكل كرة ثلج ويسبب <status>إبطاء</status> الأعداء المصابين بجميع الكرات الثلاث بنسبة {{ slowamount*-100 }}% لمدة {{ slowduration }} ثوان. يستطيع نونو <recast>إعادة إلقاء</recast> هذه القدرة حتى مرتين إضافيتين.<br /><br />بعد {{ totalspellduration }}ثوانٍ، يقوم نونو <status>بتثبيت</status> جميع الأعداء القريبين الذين تسبب <status>بإبطائهم</status> بواسطة كرات الثلج لمدة {{ rootduration }} ثوان ويلحق مقدارًا إضافيًا من <magicDamage>{{ totalrootdamage }} ضرر السحر</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الضرر", "تكلفة المانا", "إبطاء الحركة", "فترة التبريد"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cost }}-> {{ costNL }}", "{{ slowamount*-100.000000 }}%-> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "NunuE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuR", "name": "الصفر المطلق", "description": "يخلق نونو وويلامب عاصفة ثلجية شديدة في منطقة تبطئ الأعداء وتلحق ضررًا هائلًا بهم في النهاية.", "tooltip": "يوجه نونو وويلامب عاصفة ثلجية شديدة لمدة تصل إلى {{ channelduration }} ثوان. يتم <status>إبطاء</status> الأعداء في الداخل بنسبة {{ slowstartamount*-100 }}%، تزداد إلى {{ maxslowamount*-100 }}% خلال المدة. يكتسب نونو وويلامب أيضًا <shield>{{ totalshieldamount }} درعًا</shield> طيلة المدة قبل أن يتلاشى خلال {{ shielddecayduration }} ثوان بعد ذلك.<br /><br />تنفجر العاصفة الثلجية عند انتهائها فتلحق حدًا أقصى من <magicDamage>{{ maximumdamage }} ضرر السحر</magicDamage> بحسب زمن التوجيه. يستطيع نونو وويلامب <recast>إعادة الإلقاء</recast> لإنهاء العاصفة الثلجية مبكرًا.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الضرر", "مقدار الدرع", "فترة التبريد"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ baseshieldamount }}-> {{ baseshieldamountNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "NunuR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "نداء فريليورد", "description": "يزيد نونو من سرعة هجوم وسرعة حركة ويلامب وحليف مجاور، كما يجعل نونو هجمات ويلامب الأساسية تلحق الضرر بالأعداء الذين يحيطون بالهدف.", "image": {"full": "NunuPassive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}