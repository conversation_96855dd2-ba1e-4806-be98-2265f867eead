{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "<PERSON><PERSON><PERSON>", "title": "inima Freljordului", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "<PERSON><PERSON><PERSON>, spaima dragonilor", "chromas": true}, {"id": "201002", "num": 2, "name": "Braum El Tigre", "chromas": false}, {"id": "201003", "num": 3, "name": "Braum Inimă de Leu", "chromas": false}, {"id": "201010", "num": 10, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "201011", "num": 11, "name": "Braum din Orașul crimelor", "chromas": true}, {"id": "201024", "num": 24, "name": "Braum din împărăția dulciurilor", "chromas": true}, {"id": "201033", "num": 33, "name": "Braum la piscină", "chromas": true}, {"id": "201042", "num": 42, "name": "<PERSON><PERSON><PERSON>, maest<PERSON>l grătarului", "chromas": true}], "lore": "Binecuvântat cu mușchi masivi și cu o inimă și mai mare, Braum este un erou mult-iubit al Freljordului. Cei din hanurile de la nord de Frostheld ridică deseori pahare cu mied în cinstea forței lui legendare, despre care se povestește că ar fi dărâmat o pădure de stejar într-o singură noapte și că ar fi transformat un munte întreg în pietricele. Folosește o ușă vrăjită pe post de scut și cutreieră nordul înghețat cu un zâmbet mustăcios la fel de mare pe cât îi sunt și puterile. E un adevărat prieten pentru toți cei aflați la ananghie.", "blurb": "Binecuvântat cu mușchi masivi și cu o inimă și mai mare, Braum este un erou mult-iubit al Freljordului. Cei din hanurile de la nord de Frostheld ridică deseori pahare cu mied în cinstea forței lui legendare, despre care se povestește că ar fi dărâmat o...", "allytips": ["Colaborează cu aliații tăi pentru a aplica ''Lovituri grele'', încurajându-i să aplice atacuri de bază asupra țintelor marcate.", "Sari în fața aliaților vulnerabili și protejează-i de proiectile cu ''Indestructibil''.", "''Fisura glacială'' lasă o zonă de încetinire puternică; poziționeaz-o bine pentru a diviza luptele de echipă și pentru a încetini apropierea inamicului."], "enemytips": ["Braum trebuie să aplice ''Mușcătura iernii'' sau un atac de bază pentru a începe să dea ''Lovituri grele''. Dacă ești marcat, ieși din luptă înainte de a mai primi încă 3 lovituri, pentru a evita amețirea.", "Suprema lui Braum se activează după o animație. Folosește-o pentru a te feri. Dacă mergi pe terenul înghețat lăsat în urmă de el, vei fi încetinit; poziționează-te astfel încât să nu fie nevoie să îl traversezi.", "''Indestructibil'' îi oferă lui Braum o defensivă direcționată; așteaptă până când nu mai poate folosi abilitatea sau repoziționează-te."], "tags": ["Tank", "Support"], "partype": "Mană", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "Mușcătura i<PERSON>ii", "description": "Braum aruncă gheață din scutul lui, încetinind inamicii și provocându-le daune magice.<br><br>Aplică un cumul de <font color='#FFF673'>''Lovituri grele''</font>.", "tooltip": "Braum aruncă gheață din scutul lui, provocându-i <magicDamage>{{ totaldamage }} daune magice</magicDamage> primului inamic lovit și <status>încetinindu-l</status> cu {{ e2 }}%, valoare ce scade de-a lungul a {{ e5 }} secunde.<br /><br />Aplică un cumul de <keywordMajor>''Lovituri grele''</keywordMajor>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumW", "name": "Te apăr eu", "description": "Braum sare spre un campion sau minion aliat țintă. La aterizare, Braum și aliatul respectiv câștigă armură și rezistență la magie timp de câteva secunde.", "tooltip": "Braum sare spre un campion sau minion aliat. Când ajunge la ținta sa, Braum îi oferă acesteia <scaleArmor>{{ grantedallyarmor }} armură</scaleArmor> și <scaleMR>{{ grantedallymr }} rezistență la magie</scaleMR> timp de {{ e1 }} secunde. Braum primește <scaleArmor>{{ grantedbraumarmor }} armură</scaleArmor> și <scaleMR>{{ grantedbraummr }} rezistență la magie</scaleMR> pentru aceeași durată.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rezistențe de bază", "Timp de reactivare"], "effect": ["{{ baseresists }} -> {{ baseresistsNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumE", "name": "Indestructibil", "description": "Braum își ridică scutul într-o anumită direcție timp de câteva secunde, interceptând toate proiect<PERSON><PERSON>, care îl lovesc și sunt apoi distruse. Respinge complet daunele primului atac și reduce daunele tuturor atacurilor următoare provenite din direcția respectivă.", "tooltip": "Braum își ridică scutul timp de {{ e2 }} secunde. Acesta interceptează proiectilele inamice venite din direcția aleasă, care îl lovesc pe Braum și apoi sunt distruse. Primul proiectil pe care Braum îl blochează nu provoacă daune, iar proiectilele ulterioare provoacă daune reduse cu {{ e3 }}%.<br /><br />Braum primește <speed>{{ e4 }}% viteză de mișcare</speed> cât timp are scutul ridicat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Reducere daune", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumRWrapper", "name": "Fisură <PERSON>ă", "description": "Braum izbește pământul, aruncându-i în sus pe inamicii din apropiere și pe cei aflați în linie dreaptă în fața lui. În urma acestei linii rămâne o fisură care încetinește inamicii.", "tooltip": "Braum izbește pământul, creând o fisură care <status>aruncă în sus</status> inamicii din calea sa și din apropierea lui Braum și le provoacă <magicDamage>{{ totaldamage }} daune magice</magicDamage>. Prima țintă lovită este <status>aruncată în sus</status> timp de {{ minknockup }}-{{ maxknockup }} secunde, crescând în funcție de distanța față de Braum. Toate celelalte ținte lovite sunt <status>aruncate în sus</status> timp de {{ minknockup }} secunde.<br /><br />Fisura creează și o zonă timp de {{ slowzoneduration }} secunde care <status>încetinește</status> cu {{ movespeedmod }}%.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON><PERSON> aruncare în sus", "Încetinire", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ firstknockupduration }} -> {{ firstknockupdurationNL }}", "{{ movespeedmod }}% -> {{ movespeedmodNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Lo<PERSON><PERSON><PERSON> grele", "description": "Atacurile de bază ale lui Braum aplică ''Lovituri grele''. După ce este aplicat primul cumul, și atacurile de bază ale <font color='#FFF673'>aliaților</font> aplică ''Lovituri grele''. <br><br><PERSON><PERSON>d ajunge la 4 cumuluri, ținta este amețită și suferă daune magice. În următoarele câteva secunde nu mai primește cumuluri, dar suferă daune magice bonus din atacurile lui Braum.", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}