{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Garen": {"id": "<PERSON><PERSON><PERSON>", "key": "86", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON> <PERSON><PERSON> von De<PERSON>cia", "image": {"full": "Garen.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "86000", "num": 0, "name": "default", "chromas": true}, {"id": "86001", "num": 1, "name": "Blu<PERSON><PERSON> Garen", "chromas": false}, {"id": "86002", "num": 2, "name": "Wüstentruppen-Garen", "chromas": false}, {"id": "86003", "num": 3, "name": "Kommando-Garen", "chromas": false}, {"id": "86004", "num": 4, "name": "Schreckensritter Garen", "chromas": false}, {"id": "86005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "86006", "num": 6, "name": "Stahllegion-Garen", "chromas": false}, {"id": "86010", "num": 10, "name": "Abt<PERSON>ünniger Admiral <PERSON>", "chromas": false}, {"id": "86011", "num": 11, "name": "Kriegsherr G<PERSON>n", "chromas": true}, {"id": "86013", "num": 13, "name": "Gottkönig G<PERSON>", "chromas": false}, {"id": "86014", "num": 14, "name": "De<PERSON>cia <PERSON>", "chromas": true}, {"id": "86022", "num": 22, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86023", "num": 23, "name": "<PERSON><PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "86024", "num": 24, "name": "Academia Certaminis-Garen", "chromas": true}, {"id": "86033", "num": 33, "name": "Sagenschöpfer Garen", "chromas": true}, {"id": "86044", "num": 44, "name": "Gefallener Gottkönig Garen", "chromas": false}], "lore": "<PERSON><PERSON> sto<PERSON> und edler Krieger kämpft Garen in der Furchtlosen Vorhut. Bei seinen Kameraden beliebt und von seinen Gegnern respektiert ist er nicht zuletzt auch ein Nachfahre der namhaften Kronwacht-Familie, die Demacia und ihre Ideale verteidigt. Mit magieresistenter Rüstung und einem mächtigen Breitschwert stellt sich Garen auf dem Schlachtfeld in einem regelrechten Wirbel aus rechtschaffenem Stahl gegen Magier und Zaubermeister.", "blurb": "<PERSON><PERSON> sto<PERSON> und edler Krieger kämpft Garen in der Furchtlosen Vorhut. Bei seinen Kameraden beliebt und von seinen Gegnern respektiert ist er nicht zuletzt auch ein Nachfahre der namhaften Kronwacht-Familie, die Demacia und ihre Ideale verteidigt. Mit...", "allytips": ["Garens Regeneration erhöht sich auße<PERSON><PERSON><PERSON><PERSON> stark, wenn er einige Sekunden lang Schaden vermeiden kann.", "„Richten“ verursacht maximalen Schaden, wenn es nur ein einzelnes Ziel trifft. Um effektiv zu sein, versuche dich so zu positionieren, dass nur der gegnerische Champion getroffen wird.", "Garen wird nur durch Abklingzeiten in seine Schranken gewiesen, deshalb sind Gegenstände wie „Schwarzes Beil“ für ihn besonders lohnenswert."], "enemytips": ["Kombiniere verschiedene Gegenstände, die deine Rüstung erhöhen, um Garens großen normalen Schaden zu verringern.", "<PERSON><PERSON>, wenn dein Leben zu stark abnimmt, da er dir mit „Demacianische Gerechtigkeit“ ein schnelles Ende bereiten kann.", "Pass auf, wenn du Garen im Gebüsch angreifst. Du kannst dir dabei viel Schaden durch „Richten“ einhandeln.", "„Richten“ verursacht max. <PERSON><PERSON><PERSON>, wenn er nur ein Ziel trifft. Ist es nicht möglich, aus dem Radius her<PERSON>zu<PERSON>mmen, bewege dich durch verbündete Vasallen, um den Schaden zu verringern."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 7, "magic": 1, "difficulty": 5}, "stats": {"hp": 690, "hpperlevel": 98, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 38, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.5, "attackspeedperlevel": 3.65, "attackspeed": 0.625}, "spells": [{"id": "GarenQ", "name": "Entscheidender Schlag", "description": "Garen erhält einen Lauftemposchub und befreit sich von allen Verlangsamungen. Sein nächster Angriff trifft den Gegner an einer empfindlichen Stelle, verursacht zusätzlichen Schaden und lässt das Ziel verstummen.", "tooltip": "Garen entfernt alle <status>verlangsamenden</status> Effekt<PERSON> von sich und erhält {{ movementspeedduration }}&nbsp;Sekunde(n) lang <speed>{{ movementspeedamount*100 }}&nbsp;% Lauftempo</speed>.<br /><br />Sein nächster Angriff lässt das Ziel {{ silenceduration }}&nbsp;Sekunden lang <status>verstummen</status> und fügt ihm <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedduration }} -> {{ movementspeeddurationNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "GarenQ.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GarenW", "name": "<PERSON><PERSON>", "description": "Garen erhöht passiv Rüstung und Magieresistenz, wenn er Gegner tötet. Außerdem kann er diese Fähigkeit aktivieren, um für einen kurzen Moment einen Schild und Zähigkeit zu erhalten. Danach erhält er über einen längeren Zeitraum einen geringeren Betrag an Schadensverringerung.", "tooltip": "<spellPassive>Passiv:</spellPassive> Garen verfügt über <scaleArmor>{{ resistsfortooltip }}&nbsp;zusätzliche Rüstung</scaleArmor> und <scaleMR>{{ resistsfortooltip }}&nbsp;zusätzliche Magieresistenz</scaleMR>. Das Töten von Einheiten gewährt dauerhaft <attention>{{ resistgainonkilltooltip }}&nbsp;Resistenzen</attention>, bis zu einem Maximum von <attention>{{ resistmax }}</attention>.<br /><br /><spellActive>Aktiv:</spellActive> Garen nimmt {{ drduration }}&nbsp;Sekunden lang seinen Mut zusammen und verringert jeglichen erlittenen Schaden um {{ drpercent*100 }}&nbsp;%. Außerdem erhält er {{ upfrontduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ totalshield }} und <slow>{{ upfronttenacity*100 }}&nbsp;% Zähigkeit</slow>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Schadensverringerung", "Abklingzeit"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ drpercent*100.000000 }}&nbsp;% -> {{ drpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GarenW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GarenE", "name": "<PERSON><PERSON>", "description": "Garen lässt sein Schwert schnell um seinen Körper herumwirbeln und fügt nahen Gegnern normalen Schaden zu.", "tooltip": "Garen lässt sein Schwert {{ duration }}&nbsp;Sekunden lang schnell herumwirbeln und verursacht über die Wirkdauer hinweg {{ f1 }}-mal <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>. Der nächste Gegner erleidet <physicalDamage>{{ nearestenemybonus*100 }}&nbsp;% erhöhten Schaden</physicalDamage>. Champions, die {{ stackstoshred }}&nbsp;Treffer erleiden, verlieren {{ shredduration }}&nbsp;Sekunden lang <scaleArmor>{{ shredamount*100 }}&nbsp;% Rüstung</scaleArmor>.<br /><recast>Reaktivierung:</recast> Garen beendet diese Fähigkeit frühzeitig.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden pro Drehung", "Angriffsschadenskalierung pro Drehung", "Abklingzeit"], "effect": ["{{ basedamagepertick }} -> {{ basedamagepertickNL }}", "{{ adratiopertick*100.000000 }}&nbsp;% -> {{ adratioperticknl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "GarenE.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GarenR", "name": "Demacianische Gerechtigkeit", "description": "Garen beschwört die Macht von Demacia herauf und versucht, den gegnerischen Champion hinzurichten.", "tooltip": "Garen beschwört die Macht von Demacia herauf und fügt seinem G<PERSON>ner <trueDamage>absoluten Schaden</trueDamage> in <PERSON>ö<PERSON> von {{ basedamage }} plus {{ executedamage*100 }}&nbsp;% seines fehlenden Lebens zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>en basierend auf fehlendem Leben (%)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ executedamage*100.000000 }}&nbsp;% -> {{ executedamagenl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "GarenR.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Beharrlichkeit", "description": "<PERSON><PERSON> in letzter Zeit keinen Schaden erlitten hat und nicht von gegnerischen Fähigkeiten getroffen wurde, so regeneriert er jede Sekunde einen Prozentsatz seines maximalen Lebens.", "image": {"full": "Garen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}