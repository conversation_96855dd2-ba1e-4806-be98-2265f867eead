{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "Akademi <PERSON>ü<PERSON>", "chromas": true}, {"id": "350011", "num": 11, "name": "Gönül Avcısı Yuumi", "chromas": true}, {"id": "350019", "num": 19, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "350028", "num": 28, "name": "Cadı Yuumi", "chromas": true}, {"id": "350037", "num": 37, "name": "EDG <PERSON>", "chromas": true}, {"id": "350039", "num": 39, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "350049", "num": 49, "name": "<PERSON>ber Ke<PERSON>", "chromas": true}, {"id": "350050", "num": 50, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "350061", "num": 61, "name": "Gecenin <PERSON>", "chromas": true}], "lore": "<PERSON>le Şehri'nden gelen sihirli bir kedi olan <PERSON>, eskiden Norra adlı efsuncu bir yordle'ın evcil hayvanıydı. <PERSON><PERSON> gizemli bir ş<PERSON> ka<PERSON>, <PERSON><PERSON>nın canlı Geçitler Kitabı'nın koruyucusu oldu ve onu bulmak için kitabın sayfalarındaki boyut kapılarını kullanarak gezmeye başladı. <PERSON><PERSON><PERSON> is<PERSON>, yolculuğunda ona eşlik edecek dostlar arıyor. <PERSON><PERSON> sihirli kedi, dostlarını ışıldayan kalkanıyla ve yıkılmaz azmiyle koruyor. Kitap her ne kadar ona görevini hatırlatmaya çalışsa da Yuumi sık sık uyku ve balık gibi dünyevi zevklerin peşinde koşuyor; fakat ne olursa olsun, arkadaşını bulmak için çıktığı yolculuğa eninde sonunda geri dö<PERSON>ü<PERSON>.", "blurb": "<PERSON>le Şehri'nden gelen sihirli bir kedi o<PERSON>, eskiden Norra adlı efsuncu bir yordle'ın evcil hayvanıydı. <PERSON><PERSON> gize<PERSON><PERSON> bir <PERSON><PERSON>, <PERSON><PERSON>ra'nın canlı Geçitler Kitabı'nın koruyucusu oldu ve onu bulmak için kitabın sayfalarındaki...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "Sinsi Atış", "description": "<PERSON>umi isabet ettiği ilk hedefe hasar veren ve onu yavaşlatan bir atış yapar. Bu atış ilave hasar verir ve hedefe ulaşması en az 1,35 saniye sürerse güçlendirilmiş yavaşlatma uygular. Yuumi en iyi arkadaşının üstündeyken daima güçlendirilmiş yavaşlatma uygular ve takım arkadaşının isabet halinde ilave hasar vermesini sağlar.<br><br><PERSON>umi yapışık haldeyken atışı kısa süreliğine fareyle kontrol edebilir.", "tooltip": "<PERSON>umi isabet ettiği ilk rakibe <magicDamage>{{ totalmissiledamage }} Büyü Hasarı</magicDamage> veren serseri bir atış yapar ve hedefi %{{ slowamount }} <status>yavaşlatır</status>.<br /><br /><PERSON><PERSON> <keywordMajor>yap<PERSON><PERSON><PERSON>k</keywordMajor> haldeyken kullanırsa atış düz bir hat üstünde hızlanmadan önce onu kısa süreliğine fareyle kontrol edebilir. Hızlanmış atış <magicDamage>{{ totalmissiledamageempowered }} Büyü Hasarı</magicDamage> verir ve hedefi {{ empoweredslowduration }} saniyeliğine %{{ empoweredslowamount }} <status>yavaşlatır</status>.<br /><br /><keywordMajor>En İyi Arkadaş İlavesi:</keywordMajor> <spellName>Sinsi Atış</spellName> daima güçlendirilmiş <status>yava<PERSON>latma</status> uygular ve atışı bir rakip şampiyona isabet ettirmek en iyi arkadaşın {{ buffduration }} saniyeliğine <OnHit>isabet halinde %i:OnHit%</OnHit> <magicDamage>{{ onhitdamagecalc }} Büyü Hasarı</magicDamage> vermesini sağlar.<br /><br /><rules>İsabet halinde verilen ilave hasar, Yuumi'nin takım arkadaşının kritik vuruş ihtimaline bağlı olarak %{{ allycritchancemaxamp*100 }} artabilir.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> hasar", "Güçlendiril<PERSON><PERSON>", "Güçlendirilmiş Yavaşlatma Miktarı", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "%{{ empoweredslowamount }} -> %{{ empoweredslowamountNL }}", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YuumiW", "name": "Sen ve Ben!", "description": "Yuumi hedef takım arkadaşının üstüne atılarak kuleler dışındaki birimlerce hedef alınamaz hale gelir. En iyi arkadaşının üstündeyken iyileştirme ve kalkan gücü kazanır. Ayrıca takım arkadaşına isabet halinde iyileştirme sağlar.", "tooltip": "<spellPassive>Pasif:</spellPassive> <PERSON><PERSON> <keywordMajor>en iyi arkadaşının</keywordMajor> üstündeyken fazladan <keywordMajor>%{{ healandshieldpower*100 }} İyileştirme ve Kalk<PERSON></keywordMajor> kazanır ve takım arkadaşı <OnHit>isabet halinde %i:OnHit%</OnHit> <healing>{{ healthonhit }} Can</healing> yeniler.<br /><br /><spellActive>Aktif:</spellActive> <PERSON><PERSON> bir takım arkadaşının üstüne atılarak ona <keywordMajor>yapışır</keywordMajor>. <PERSON><PERSON> <keywordMajor>yapışık</keywordMajor> haldeyken arkadaşıyla birlikte hareket eder ve kuleler dışındaki birimlerce hedef alınamaz.<br /><br /><PERSON><PERSON>'nin üstünde kullan<PERSON>lan <status>harekets<PERSON> bırakma</status> etki<PERSON><PERSON>, bu yet<PERSON><PERSON>i {{ ccattachlockout }} saniyelik bekleme süresine sokar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["İsabet Halinde İyileştirme", "İlave İyileştirme ve Kalkan Gücü"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "YuumiE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>'ye kalkan sağlar ve hareket hızı ile saldırı hızını arttırır. <PERSON><PERSON> bir takım arkadaşına yapışık haldeyse bu etkiyi ona aktarır.<br>", "tooltip": "<PERSON><PERSON> {{ msduration }} sani<PERSON>liğine <shield>{{ totalshielding }} <PERSON><PERSON><PERSON></shield> ve <attackSpeed>%{{ totalattackspeed }} Saldırı Hızı</attackSpeed> kazanır. Kalkan etkinken hedef aynı zamanda <speed>%{{ msamount }} Hareket Hızı</speed> kazanır.<br /><br /><PERSON><PERSON> <keywordMajor>yap<PERSON><PERSON><PERSON><PERSON></keywordMajor> haldeyse bu yetenek takım arkadaşını etkiler ve ona <magicDamage>{{ manarestore }} Mana</magicDamage> sağlar. Bu değer hedefin eksik manasına bağlı olarak en fazla %{{ maxmanapercincrease*100 }} artar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Saldırı Hızı"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YuumiR", "name": "<PERSON>", "description": "<PERSON><PERSON> yoğ<PERSON>laşarak rakiplere hasar veren ve takım arkadaşlarını iyileştiren beş dalga gönderir. <PERSON><PERSON> yoğunlaşma esnasında hareket edebilir, bir şampiyona yapışabilir ve Çıldırrr yeteneğini kullanabilir. Ayrıca bu yetenek Yuumi en iyi arkadaşının üstündeyken fareyi takip eder.", "tooltip": "<PERSON><PERSON> {{ ultduration }} saniyeliğine yoğunlaşarak iki takımı da etkileyen {{ numberofwaves }} büyülü dalga gönderir. Yuumi, yeteneği <keywordMajor>yapı<PERSON>ık haldeyken</keywordMajor> kullanırsa dalgaları fareyle yönlendirebilir.<br /><br />İsabet alan rakipler <magicDamage>{{ totalmissiledamage }} Büyü Hasarı</magicDamage> alır ve {{ ccduration }} saniyeliğine %{{ baseslow*-100 }} <status>yavaşlar</status>. Yavaşlatma etkisi isabet eden dalga başına %{{ bonusslowperwave*-100 }} artar.<br /><br />Takım arkadaşları dalga başına <healing>{{ totalhealperwave }} Can</healing> iyileşir. Fazlalık iyileştirme <shield>kalkana</shield> dön<PERSON>ştürülür.<br /><br /><keywordMajor>En İyi Arkadaş İlavesi:</keywordMajor> <keywordMajor>En iyi arkadaş</keywordMajor> için iyileştirme artarak <healing>{{ enhancedhealperwave }} Can</healing> olur.<br /><br /><rules><spellName>Sen ve Ben!</spellName> yeteneğini kullanmak dalgaları mevcut yöne doğru sabitler.<br />Yuumi yoğunlaşma esnasında hareket edebilir ve <spellName>Çıldırrr</spellName> yeteneğini kullanabilir.</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Atış başına taban hasar:", "Dalga Başına Taban İyileştirme"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "En İyi Arkedişim", "description": "Yuumi bir şampiyona normal saldırı veya yeteneklerle hasar verdiğinde belli aralıklarla kendi canını ve yapıştığı sonraki takım arkadaşının canını yeniler.<br><br><PERSON>umi yapışık haldeyken takım arkadaşlarıyla özel bir bağ kurar. En güçlü bağa sahip takım arkadaşı Yuumi ona yapışıkken Yuumi'nin yeteneklerini güçlendirir.", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}