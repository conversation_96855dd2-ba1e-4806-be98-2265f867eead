{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiddlesticks": {"id": "Fiddlesticks", "key": "9", "name": "Fiddlesticks", "title": "the Ancient Fear", "image": {"full": "Fiddlesticks.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "9000", "num": 0, "name": "default", "chromas": false}, {"id": "9001", "num": 1, "name": "Spectral Fiddlesticks", "chromas": false}, {"id": "9002", "num": 2, "name": "Union Jack Fiddlesticks", "chromas": false}, {"id": "9003", "num": 3, "name": "Bandito Fiddlesticks", "chromas": true}, {"id": "9004", "num": 4, "name": "Pumpkinhead Fiddlesticks", "chromas": false}, {"id": "9005", "num": 5, "name": "Fiddle Me Timbers", "chromas": false}, {"id": "9006", "num": 6, "name": "Surprise Party Fiddlesticks", "chromas": true}, {"id": "9007", "num": 7, "name": "Dark Candy Fiddlesticks", "chromas": false}, {"id": "9008", "num": 8, "name": "Risen Fiddlesticks", "chromas": false}, {"id": "9009", "num": 9, "name": "Praetorian Fiddlesticks", "chromas": true}, {"id": "9027", "num": 27, "name": "Star Nemesis Fiddlesticks", "chromas": true}, {"id": "9037", "num": 37, "name": "Blood Moon Fiddlesticks", "chromas": true}], "lore": "Ada yang terbangkitkan di Runeterra. <PERSON><PERSON><PERSON><PERSON> yang kuno, dan men<PERSON>ikan. Te<PERSON>r abadi yang dikenal sebagai Fiddlesticks mengintai umat manusia, tertarik pada area yang penuh dengan paranoia dan memangsa korban-korban yang ketakutan. Membawa sabit bergerigi, makhluk kuyu dan tak berdaya ini menuai ketakutan ini, menghancurkan pikiran mereka yang tak cukup beruntung untuk bertahan hidup di hadapannya. Waspadalah terhadap suara burung gagak, atau bisikan dari bentuk yang <i>hampir</i> menyerupai manusia … Fiddlesticks telah kembali.", "blurb": "Ada yang terbangkitkan di Runeterra. <PERSON><PERSON><PERSON><PERSON> yang kuno, dan men<PERSON>n. Te<PERSON>r abadi yang dikenal sebagai Fiddlesticks mengintai umat manusia, tertarik pada area yang penuh dengan paranoia dan memangsa korban-korban yang ketakutan. Membawa sabit...", "allytips": [], "enemytips": [], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 9, "difficulty": 9}, "stats": {"hp": 650, "hpperlevel": 106, "mp": 500, "mpperlevel": 28, "movespeed": 335, "armor": 34, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 480, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.65, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "FiddleSticksQ", "name": "Terrify", "description": "Fiddlesticks memberi damage pada musuh dengan spell saat tak terlihat atau menarget musuh dengan aktivasi Terrify. Menyerang unit target dengan fear, menyebabkan mereka mundur ketakutan beberapa saat.", "tooltip": "<spellPassive>Pasif:</spellPassive> Saat tak terlihat dan di luar combat atau sedang menyerupai <keywordMajor>Effigy</keywordMajor>, menghasilkan damage ke musuh dengan ability <status>Fear</status> selama {{ fearduration }} detik.<br /><br /><spellActive>Aktif:</spellActive> Menerapkan <status>Fear</status> pada musuh selama {{ fearduration }} detik dan menghasilkan <magicDamage>{{ totalpercenthealthdamage }} magic damage dari Health saat ini</magicDamage>. Jika target baru saja terkena <status>Fear</status> oleh Fiddlesticks, menghasilkan <magicDamage>{{ totalpercenthealthdamagefeared }} magic damage Health saat ini</magicDamage> sebagai gantinya.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Cooldown", "Health Saat Ini %"], "effect": ["{{ fearduration }}-> {{ feardurationNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}%-> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 14.5, 14, 13.5, 13], "cooldownBurn": "15/14.5/14/13.5/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "FiddleSticksQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksW", "name": "Bountiful Harvest", "description": "Fiddlesticks men<PERSON>ras health musuh di sekitar, <PERSON><PERSON><PERSON><PERSON><PERSON> damage execute bonus di akhir durasi.", "tooltip": "Fiddlesticks melakukan channeling dan menguras jiwa musuh di sekitar, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ draindamagecalc }} magic damage</magicDamage> per detik selama 2 detik, plus <magicDamage>{{ percentfortooltip }}% magic damage dari Health yang hilang</magicDamage> di akhir. Fiddlesticks memulihkan <healing>{{ vamppercentage }}% damage menjadi Health</healing>.<br /><br />Jika Fiddlesticks mengakhiri channeling tanpa gangguan, sisa Cooldown akan dikurangi sebesar 60%.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per <PERSON>", "Damage Health yang Hilang", "Persentase Heal", "Cooldown"], "effect": ["{{ damagepersecond }}-> {{ damagepersecondNL }}", "{{ percentfortooltip }}%-> {{ percentfortooltipNL }}%", "{{ vamppercentage }}%-> {{ vamppercentageNL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "FiddleSticksW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksE", "name": "Reap", "description": "Fiddlesticks menebas area dengan sabitnya, menerapkan slow dan silence ke semua musuh yang kena di tengah-tengah tebasan.", "tooltip": "Fiddlesticks melepaskan dark magic, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damage }} magic damage</magicDamage> dan menera<PERSON>kan <status>Slow</status> sebesar {{ slowamount*-100 }}% selama {{ silenceduration }} detik. Musuh di tengah juga terkena <status>Silence</status> selama durasi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}%-> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "FiddleSticksE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksR", "name": "Crowstorm", "description": "Para burung gagak pembunuh muncul di sekitar Fiddlesticks, menghasilkan damage per detik pada semua unit musuh di area sekitar.", "tooltip": "Fiddlesticks melakukan channeling selama {{ channeltime }} detik, lalu teleport dan melepaskan gagak pembunuh, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> selama {{ duration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per <PERSON>", "Cooldown"], "effect": ["{{ damagepersecond }}-> {{ damagepersecondNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 225, 325], [5, 5, 5], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "125/225/325", "5", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "FiddleSticksR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "A Harmless Scarecrow", "description": "Trinket Fiddlesticks digantikan oleh Scarecrow Effigy.", "image": {"full": "FiddlesticksP.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}