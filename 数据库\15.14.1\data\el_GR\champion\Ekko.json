{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ekko": {"id": "<PERSON><PERSON><PERSON>", "key": "245", "name": "Έκκο", "title": "το Αγόρι που Θρυμμάτισε τον Χρόνο", "image": {"full": "Ekko.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "245000", "num": 0, "name": "default", "chromas": false}, {"id": "245001", "num": 1, "name": "Έκκο της Αμμοθύελλας", "chromas": true}, {"id": "245002", "num": 2, "name": "Μαθητής Έκκο", "chromas": false}, {"id": "245003", "num": 3, "name": "PROJECT: Έκκο", "chromas": false}, {"id": "245011", "num": 11, "name": "SKT T1 Έκκο", "chromas": false}, {"id": "245012", "num": 12, "name": "Έκκο Φάρσα ή Κέρασμα", "chromas": true}, {"id": "245019", "num": 19, "name": "True Damage Έκκο", "chromas": true}, {"id": "245028", "num": 28, "name": "Pulsefire Έκκο", "chromas": true}, {"id": "245036", "num": 36, "name": "<PERSON><PERSON> Φλογοφτέρουγος Έκκο", "chromas": true}, {"id": "245045", "num": 45, "name": "Έκκο Φύλακας των Άστρων", "chromas": true}, {"id": "245046", "num": 46, "name": "Έκκο Φύλακας των Άστρων - Έκδοση Κύρους", "chromas": false}, {"id": "245056", "num": 56, "name": "Breakout True Damage Έκκο", "chromas": false}, {"id": "245057", "num": 57, "name": "Arcane Έκκο Ύστατης Προσπάθειας", "chromas": true}], "lore": "Ο Έκκο είναι ένα παιδί-θαύμα που μεγάλωσε στις σκληρές γειτονιές του Ζάουν και έχει την ικανότητα να ελέγχει τον χρόνο, ώστε να φέρνει κάθε κατάσταση στα μέτρα του. Χρησιμοποιώντας τη Συσκευή Ζ, που έχει εφεύρει ο ίδιος, μπορεί να εξερευνά όλες τις πιθανές πραγματικότητες και να δημιουργεί την τέλεια στιγμή για να κατορθώνει, φαινομενικά, το ακατόρθωτο με την πρώτη προσπάθεια και κάθε φορά. Αν και ο Έκκο λατρεύει την ελευθερία, όταν παρουσιάζεται κάποιος κίνδυνος για εκείνους που αγαπά θα κάνει τα πάντα, μαζί με τις Φωτιές, για να τους προστατεύσει.", "blurb": "Ο Έκκο είναι ένα παιδί-θαύμα που μεγάλωσε στις σκληρές γειτονιές του Ζάουν και έχει την ικανότητα να ελέγχει τον χρόνο, ώστε να φέρνει κάθε κατάσταση στα μέτρα του. Χρησιμοποιώντας τη Συσκευή Ζ, που έχει εφεύρει ο ίδιος, μπορεί να εξερευνά όλες τις...", "allytips": ["Το Χρονικό Ρήγμα είναι ένα πολύ ισχυρό εργαλείο απόδρασης, αλλά είναι αρκετά αποτελεσματικό και όταν χρησιμοποιείται επιθετικά. Μην υποτιμάτε τη ζημιά που μπορεί να προκαλέσει.", "Αν μπορείτε να εφαρμόσετε την Αντήχηση Συσκευής Z σε έναν αντίπαλο Ήρωα, αξίζει να παίξετε λίγο πιο ριψοκίνδυνα για να το πετύχετε. Η μπόνους Ταχύτητα Κίνησης διευκολύνει την απόδρασή σας.", "Το Άλμα Φάσης είναι ένα ιδανικό εργαλείο για να προετοιμάζετε τη χρήση των υπόλοιπων ικανοτήτων του Έκκο. Χρησιμοποιήστε την για να καταφέρετε διπλά χτυπήματα με τον Χρονοστρόβιλο ή για να τοποθετηθείτε σωστά ώστε να πυροδοτήσετε την έκρηξη της Παράλληλης Σύγκλισης."], "enemytips": ["Ο Έκκο είναι σαφώς πιο αδύναμος όταν δεν είναι διαθέσιμη η Υπέρτατη ικανότητά του. Παρατηρήστε αν αφήνει πίσω του ένα ίχνος για να καταλάβετε αν είναι διαθέσιμο το Χρονικό Ρήγμα.", "Η ζώνη κινητοποίησης του Έκκο χρειάζεται 3 δευτ. για να οπλιστεί. Παρακολουθήστε το είδωλο που δημιουργεί κατά τη χρήση και προσπαθήστε να μαντέψετε πού τοποθετήθηκε η ζώνη.", "Το δεύτερο χτύπημα του Χρονοστρόβιλου προκαλεί περισσότερη ζημιά από το πρώτο. Προσπαθήστε να το αποφύγετε."], "tags": ["Assassin", "Mage"], "partype": "Μάνα", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 280, "mpperlevel": 70, "movespeed": 340, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.688}, "spells": [{"id": "EkkoQ", "name": "Χρον<PERSON><PERSON>τρ<PERSON><PERSON>ιλος", "description": "Ο Έκκο πετάει μια χρονική χειροβομβίδα που ανοίγει και δημιουργεί ένα πεδίο αλλοίωσης του χρόνου όταν χτυπά έναν αντίπαλο Ήρωα, επιβ<PERSON><PERSON><PERSON>ύνοντας και προκαλώντας ζημιά σε όσους βρίσκονται εντός του πεδίου επιρροής. Μετά από μικρή καθυστέρηση, η χειροβομβίδα επιστρέφει στον Έκκο, προκ<PERSON>λώντας ζημιά στους στόχους που θα βρεθούν στον δρόμο της.", "tooltip": "Ο Έκκο πετάει μια συσκευή που προκαλεί <magicDamage>{{ initialdamage }} Μαγική Ζημιά</magicDamage>. Όταν πετύχει έναν Ήρωα ή αφού φτάσει στο τέλος της εμβέλειάς της, επεκτείνεται σε ένα πεδίο που <status>Επιβραδύνει</status> τους εχθρούς εντός του κατά {{ e2 }}%. Αφού επεκταθεί, ο Έκκο την ανακαλεί, προκαλώντας <magicDamage>{{ recalldamage }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@", "Ζημιά εκτόξευσης", "Επιβράδυνση", "Ζημιά επιστροφής"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ outgoingdamage }} -> {{ outgoingdamageNL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ returndamage }} -> {{ returndamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 85, 100, 115, 130], [40, 45, 50, 55, 60], [40, 65, 90, 115, 140], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [165, 165, 165, 165, 165], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/85/100/115/130", "40/45/50/55/60", "40/65/90/115/140", "100", "0", "165", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EkkoQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoW", "name": "Παράλληλη Σύγκλιση", "description": "Οι βασικές επιθέσεις του Έκκο προκαλούν μπόνους ζημιά σε εχθρούς που έχουν λίγη Ζωή. Μπορεί να χρησιμοποιήσει την Παράλληλη Σύγκλιση για να διακόψει τη ροή του χρόνου, δημιουργώντας μια διαταραχή μετά από λίγα δευτερόλεπτα, η οποία επιβραδύνει τους εχθρούς που βρίσκονται στο πεδίο επιρροής της. Αν ο Έκκο μπει στη διαταραχή, αποκτ<PERSON> ασπίδα και ακινητοποιεί τους εχθρούς, παγιδεύοντάς τους στον χρόνο.", "tooltip": "<spellPassive>Παθητική:</spellPassive> Οι επιθέσεις του Έκκο ενάντια σε αντιπάλους με κάτω από 30% Ζωής προκαλούν <magicDamage>{{ missinghealthpercent }} Μαγική Ζημιά ίση με τη Ζωή που λείπει</magicDamage>.<br /><br /><spellActive>Ενεργή:</spellActive> Ο Έκκο εκτοξεύει μια χρονόσφαιρα που διαρκεί 1,5 δευτ. μετά από μια καθυστέρηση που <status>Επιβραδύνει</status> τους αντιπάλους στο εσωτερικό της κατά {{ e0 }}%. Αν ο Έκκο μπει στη σφαίρα, την ανατινάζει, <status>Ακινητοποιώντας</status> για {{ e2 }} δευτ. και αποκτώντας <shield>{{ totalshield }} Ασπίδα</shield>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Αντοχή Ασπίδας", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [375, 375, 375, 375, 375], [2.25, 2.25, 2.25, 2.25, 2.25], [3, 3, 3, 3, 3], [100, 120, 140, 160, 180], [150, 150, 150, 150, 150], [15, 15, 15, 15, 15], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [40, 40, 40, 40, 40]], "effectBurn": [null, "375", "2.25", "3", "100/120/140/160/180", "150", "15", "1.5", "3", "2", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1600, 1600, 1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "EkkoW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoE", "name": "Άλμα Φάσης", "description": "Ο Έκκο κάνει έναν ελιγμό αποφυγής και φορτίζει τη Συσκευή Ζ. Η επόμενη επίθεση θα προκαλέσει μπόνους ζημιά και θα αλλοιώσει την πραγματικότητα, με αποτέλεσμα να τηλεμεταφερθεί στον στόχο του.", "tooltip": "Ο Έκκο εφορμά κι ενισχύει την επόμενη επίθεση του, ώστε να έχει μπόνους εμβέλεια, να τον τηλεμεταφέρει στον στόχο του και να προκαλεί επιπλέον <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [350, 350, 350, 350, 350], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "350", "3", "300", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "EkkoE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoR", "name": "Χρονικό Ρήγμα", "description": "Ο Έκκο καταστρέφει τη ροή του χρόνου, με αποτέλεσμα να μην μπορεί να γίνει στόχος και επιστρέφει σε ένα πιο ευνοϊκό σημείο στον χρόνο. Επιστρέφει στο σημείο που βρισκόταν πριν από 4 δευτ. και θεραπεύει ένα μέρος της ζημιάς που δέχτηκε σε αυτό το χρονικό διάστημα. Οι εχθροί που βρίσκονται κοντά στη ζώνη άφιξης δέχονται τεράστια ζημιά.", "tooltip": "Ο Έκκο γυρνάει πίσω τον χρόνο, μπα<PERSON>ν<PERSON>ι σε Καταστολή ενώ τηλεμεταφέρεται στο σημείο που βρισκόταν πριν από 4 δευτ. και προκαλεί <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> στους εχθρούς που βρίσκονται κοντά. Επιπλέον, ο Έκκο αναπληρώνει <healing>{{ totalbaseheal }} Ζωή</healing>, που αυξάνεται κατά {{ percenthealampperpercentmissinghealth }}% για κάθε 1% Ζωής που έχασε τα τελευταία 4 δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Θεραπεία", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ flatheal }} -> {{ flathealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 80, 50], "cooldownBurn": "110/80/50", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850], "rangeBurn": "850", "image": {"full": "EkkoR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Αντήχηση Συσκευής Z", "description": "Κάθε τρίτη επίθεση ή ικανότητα που προκαλεί ζημιά στον ίδιο στόχο, προκαλεί μπόνους Μαγική Ζημιά και δίνει στον Έκκο μια σύντομη αύξηση Ταχύτητας Κίνησης αν ο στόχος είναι Ήρωας.<br><br>", "image": {"full": "Ekko_P.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}