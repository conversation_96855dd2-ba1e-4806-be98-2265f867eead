{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "Alistar", "title": "minotaurul", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "Alistar negru", "chromas": false}, {"id": "12002", "num": 2, "name": "Alistar auriu", "chromas": false}, {"id": "12003", "num": 3, "name": "Alistar toreadorul", "chromas": false}, {"id": "12004", "num": 4, "name": "Alistar din Texas", "chromas": false}, {"id": "12005", "num": 5, "name": "Alistar dezlănțuit", "chromas": false}, {"id": "12006", "num": 6, "name": "Alistar infernal", "chromas": false}, {"id": "12007", "num": 7, "name": "Alistar fundaș", "chromas": false}, {"id": "12008", "num": 8, "name": "Alistar brigand", "chromas": false}, {"id": "12009", "num": 9, "name": "Alistar SKT T1", "chromas": false}, {"id": "12010", "num": 10, "name": "Alistar, văcuța mu", "chromas": true}, {"id": "12019", "num": 19, "name": "Alistar hextech", "chromas": false}, {"id": "12020", "num": 20, "name": "Alistar învingător", "chromas": true}, {"id": "12022", "num": 22, "name": "Alistar îngheț întunecat", "chromas": true}, {"id": "12029", "num": 29, "name": "Alistar bestie selenară", "chromas": true}], "lore": "Alistar este un războinic viteaz cu o reputație pe măsură, ce caută să-și răzbune clanul ucis de imperiul noxian. Deși a fost capturat și forțat să devină gladiator, voința lui de neclintit l-a ajutat să nu se transforme cu adevărat într-o bestie. Fiind eliberat din lanțurile foștilor săi stăpâni, acesta luptă acum în numele celor năpăstuiți și dezavantajați, folosindu-și atât coarnele, copitele și pumnii, cât și furia.", "blurb": "Alistar este un războinic viteaz cu o reputație pe măsură, ce caută să-și răzbune clanul ucis de imperiul noxian. Deși a fost capturat și forțat să devină gladiator, voința lui de neclintit l-a ajutat să nu se transforme cu adevărat într-o bestie. Fiind...", "allytips": ["Dacă folo<PERSON> ''Pulverizare'', îți vei găsi o poziție mai bună pentru a plasa ''Lovitura de cap''.", "Viteza de mișcare a lui Alistar e foarte importantă. Gândește-te bine ce cizme alegi.", "<PERSON><PERSON><PERSON> folo<PERSON> ''Flash'', îți poți lua ținta pe nepregătite, aruncând-o în rândurile aliaților cu ''Pulverizare'' și ''Lovitură de cap''."], "enemytips": ["Alistar e foarte periculos, dar foarte rezistent – încearcă să ataci adversarii mai fragili.", "Ai grijă la combinația ''Pulverizare'' – ''Lovitură de cap'' când ești lângă turnuri.", "Când Alistar își folosește abilitatea supremă, e de preferat să te retragi și să aștepți ca efectul să dispară înainte să îl ataci."], "tags": ["Tank", "Support"], "partype": "Mană", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "Pulverizare", "description": "Alistar izbește solul, provocându-le daune tuturor inamicilor din apropiere și aruncându-i în sus.", "tooltip": "Alistar izbește pământul, <status>aruncând în sus</status> inamicii timp de {{ knockupduration }} secundă și provocându-le <magicDamage>{{ totaldamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Cost de @AbilityResourceName@", "Daune"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Headbutt", "name": "Lovitură de cap", "description": "Alistar izbește ținta cu capul, provocându-i daune și proiectând-o în spate.", "tooltip": "Alistar se năpustește asupra unui inamic și îl <status>arun<PERSON><PERSON></status> <status>în spate</status>, provocându-i <magicDamage>{{ totaldamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Cost de @AbilityResourceName@", "Daune"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AlistarE", "name": "Zdrobire", "description": "Alistar zdrobește unitățile inamice din apropiere, poate trece prin unități și primește cumuluri atunci când îi provoacă daune unui campion inamic. Când ajunge la numărul maxim de cumuluri, următorul său atac de bază împotriva unui campion inamic îi provoacă daune magice bonus și îl amețește.", "tooltip": "Alistar începe să zdrobească pământul, primind efectul ''Fantomă'' și provocându-le inamicilor din apropiere <magicDamage>{{ totaldamage }} daune magice</magicDamage> de-a lungul a {{ e3 }} secunde. Pentru fiecare izbitură care îi provoacă daune unui campion, primește un cumul.<br /><br />La {{ e5 }} cumuluri, următorul atac al lui Alistar împotriva campionilor <status>amețește</status> ținta timp de {{ e6 }} secunde și îi provoacă încă <magicDamage>{{ attackbonusdamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Cost de @AbilityResourceName@", "Daune"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FerociousHowl", "name": "Voință de neclintit", "description": "Alistar scoate un răget sălbatic, elimin<PERSON>d toate efectele de control al maselor care îl afectează și reducând daunele fizice și magice pe care le suferă pe toată durata efectului.", "tooltip": "Alistar se purifică imediat de toate efectele de <status>neutralizare</status> și suferă daune reduse cu {{ rdamagereduction }}% timp de {{ rduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Reducere daune"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamagereduction }}% -> {{ rdamagereductionNL }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON> triumf<PERSON>", "description": "Alistar își încarcă răcnetul atunci când amețește sau mută campioni inamici sau atunci când mor inamici aflați în apropiere. Când răgetul se încar<PERSON><PERSON> complet, Alistar se vindecă pe el însuși și pe toți campionii aliați din apropiere.", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}