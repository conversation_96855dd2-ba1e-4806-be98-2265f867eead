{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "Δρ. <PERSON>ο", "title": "ο Σαδιστής Γιατρός του Ζάουν", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "Το<PERSON><PERSON><PERSON><PERSON>ς Δρ. Μούντο", "chromas": false}, {"id": "36002", "num": 2, "name": "Μίστερ Μούντο-υφήλιος", "chromas": false}, {"id": "36003", "num": 3, "name": "Μούντο ο Γι<PERSON>ης", "chromas": true}, {"id": "36004", "num": 4, "name": "Μούντο Μούντο", "chromas": false}, {"id": "36005", "num": 5, "name": "Μούντο ο Εκτελεστής", "chromas": false}, {"id": "36006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ο <PERSON><PERSON><PERSON> της Οργής", "chromas": false}, {"id": "36007", "num": 7, "name": "TPA Μούντο", "chromas": false}, {"id": "36008", "num": 8, "name": "Μούντο στην Πισίνα", "chromas": false}, {"id": "36009", "num": 9, "name": "Ελ Μάτσ<PERSON>το", "chromas": false}, {"id": "36010", "num": 10, "name": "<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ρίγ<PERSON><PERSON><PERSON><PERSON>ς <PERSON>το", "chromas": true}, {"id": "36021", "num": 21, "name": "Δρ. Μούντο των Δαιμόνων του Δρόμου", "chromas": true}], "lore": "Θεότρελος, με τραγικά δολοφονι<PERSON><PERSON> ένστικτα και τρομακτικό μωβ χρώμα, ο Δρ. Μούντο κρατά πολλούς από τους κατοίκους του Ζάουν κλεισμένους στα σπίτια τους τις πολύ σκοτεινές νύχτες. Αυτοανακηρυχθείς γιατρός πλέον, ήταν κάποτε ασθενής του πιο διαβόητου ασύλου στο Ζάουν. Αφού «θεράπευσε» ολόκληρο το προσωπικό, ο Δρ. Μούντο έστησε το ιατρείο του στους άδειους θαλάμους όπου παλιά δεχόταν ο ίδιος θεραπεία και άρχισε να μιμείται τις εξαιρετικά αντιδεοντολογικές επεμβάσεις στις οποίες είχε υποβληθεί τόσο συχνά και ο ίδιος. Με ένα ερμάριο γεμάτο φάρμακα και με μηδενικές ιατρικές γνώσεις, πλέον κάνει τον εαυτό του ακόμη πιο τερατώδη με κάθε ένεση και τρομοκρατεί τους δύσμοιρους «ασθενείς» που τριγυρνούν κοντά στο ιατρείο του.", "blurb": "Θεότρελος, με τραγικά δολοφο<PERSON><PERSON><PERSON><PERSON> ένστικτα και τρομακτικό μωβ χρώμα, ο Δρ. Μούντο κρατά πολλούς από τους κατοίκους του Ζάουν κλεισμένους στα σπίτια τους τις πολύ σκοτεινές νύχτες. Αυτοανακηρυχθείς γιατρός πλέον, ήταν κάποτε ασθενής του πιο διαβόητου...", "allytips": ["Η χρήση ενός Σαδισμού τη σωστή στιγμή θα ξεγελάσει τους εχθρικούς Ήρωες να σας επιτεθούν, ακόμα κι αν δεν μπορούν να προκαλέσουν αρκετή ζημιά για να σας αποτελειώσουν.", "Η Όψη Πνευμάτων αυξάνει τη θεραπεία που κάνει η υπέρατατή σας και μειώνει τον Χρόνο Επαναφόρτισης όλων των ικανοτήτων.", "Οι μπαλτάδες είναι έξοχα όπλα για την εξόντωση ουδέτερων τεράτων. Αντί να επιστρέψετε στη βάση, σκοτώστε ουδέτερα τέρατα για Χρυσό μέχρι να σας θεραπεύσει η Υπέρτατή σας."], "enemytips": ["Προσπαθήστε να συντονίσετε τις ικανότητες με την υψηλότερη ζημιά όλων των συμμάχων σας, αμέσως μόλις χρησιμοποιήσει ο Δρ. Μούντο την Υπέρτατη ικανότητά του. Σκοτώστε τον γρήγορα με μπόλικη άμεση ζημιά γιατί θα αντισταθμίσει την ζημιά με την θεραπεία του.", "Όταν χρησιμοποιεί τον Σαδισμό, ρίξτε του μια Ανάφλεξη για να εξουδετερώσετε μεγάλο μέρος της θεραπείας του."], "tags": ["Tank", "Fighter"], "partype": "Καθόλου", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "Μολυσμένο Πριόνι", "description": "Ο Δρ. Μούντο πετάει ένα μολυσμένο πριόνι, το οποίο Επιβραδύνει τον πρώτο εχθρό που χτυπά και του προκαλεί ζημιά, ανάλογα με την τρέχουσα Ζωή του.", "tooltip": "Ο Δρ. Μούντο πετά το πριόνι του και προκαλεί στον πρώτο εχθρό που χτυπά <magicDamage>Μαγική Ζημιά ίση με το {{ currenthealthdamage*100 }}% της τρέχουσας Ζωής του</magicDamage> και τον <status>Επιβραδύνει</status> κατά {{ slowamount*100 }}% για {{ slowduration }} δευτ.<br /><br />Αν το πριόνι χτυπήσει Ήρωα ή τέρας, ο Δρ. Μούντο αναπληρώνει <healing>{{ healthrestoreonhitchampionmonster }} Ζωή</healing>. Αν χτυπήσει μονάδα εκτός Ηρώων ή τεράτων, ο Δρ. Μούντο αναπληρώνει <healing>{{ healthrestoreonhitminion }} Ζωή</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά από τρέχουσα Ζωή", "Ελάχιστη ζημιά", "Όριο ζημιάς σε τέρατα", "Κόστος σε Ζωή"], "effect": ["{{ currenthealthdamage*100.000000 }}% -> {{ currenthealthdamagenl*100.000000 }}%", "{{ minimumdamage }} -> {{ minimumdamageNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}", "{{ healthcost }} -> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Ζωή", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ healthcost }} Ζωή"}, {"id": "DrMundoW", "name": "Απινιδωτής", "description": "Ο Δρ. Μούντο προκαλεί ηλεκτροπληξία στον εαυτό του και προκαλεί διαρκή ζημιά στους κοντινούς εχθρούς. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, αποθηκεύει ένα ποσοστό της ζημιάς που δέχεται. Μετά το πέρας της επίδρασης ή αν χρησιμοποιήσει ξανά την ικανότητα, ο Δρ. Μούντο προκαλεί μια έκρηξη που κάνει ζημιά στους κοντινούς εχθρούς. Αν η έκρηξη χτυπήσει έναν εχθρό, ο Δρ. Μούντο Θεραπεύει ένα ποσοστό της ζημιάς που έχει αποθηκεύσει.", "tooltip": "Ο Δρ. Μούντο φορτίζει έναν απινιδωτή, προκαλώντας <magicDamage>{{ damagepertick*4 }} Μαγική Ζημιά</magicDamage> κάθε δευτερόλεπτο για έως {{ duration }} δευτ. σε κοντινούς εχθρούς. Επιπλέον, αποθηκεύει {{ grayhealthstorageinitial }} Ζημιά που δέχεται στα πρώτα {{ grayhealthinitialduration }} δευτ. και {{ grayhealthstorage*100 }}% της Ζημιάς για την υπόλοιπη διάρκεια της ικανότητας ως γκρίζα Ζωή και μπορεί να τη <recast>χρησιμοποιήσει ξανά</recast>.<br /><br /><recast>Νέα Χρήση:</recast> Ο απινιδωτής ανατινάζεται και προκαλεί <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> στους κοντινούς εχθρούς. Εάν ο Δρ. Μούντο πετύχει τουλάχιστον έναν Ήρωα, αναπληρώνει το <healing>{{ grayhealthbigmod*100 }}% της γκρίζας Ζωής</healing>. Σε διαφορετική περίπτωση αναπληρώνει <healing>{{ grayhealthsmallmod*100 }}% της γκρίζας Ζωής</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά ανά Ενεργοποίηση", "Ζημιά από Νέα Χρήση", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ damagepertick }} -> {{ damagepertickNL }}", "{{ recastbasedamage }} -> {{ recastbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% Τρέχουσας Ζωής", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ currenthealthcost*100 }}% Τρέχουσας Ζωής"}, {"id": "DrMundoE", "name": "Αμβλύ Τραύμα", "description": "Παθητική: Ο Δρ. Μούντο αποκτά μπόνους Ζημιά Επίθεσης, η οποία αυξάνεται ανάλογα με τη μέγιστη Ζωή.<br><br>Ενεργή: Ο Δρ. Μούντο χτυπά έναν αντίπαλο με τον «ιατρικό» σάκο του και προκαλεί επιπλέον ζημιά, ανάλογα με τη Ζωή που του λείπει. Αν ο εχθρός πεθάνει, πετάγεται μακριά και προκαλεί ζημιά σε όλους τους εχθρούς που διαπερνά.", "tooltip": "<spellPassive>Παθητική:</spellPassive> Ο Δρ. Μούντο αποκτά <physicalDamage>{{ passivebonusad }} Ζημιά Επίθεσης</physicalDamage>.<br /><br /><spellActive>Ενεργή:</spellActive> Ο Δρ. Μούντο κραδαίνει τον «ιατρικό» σάκο του και η επόμενη επίθεσή του προκαλεί <physicalDamage>{{ additionaldamage }} επιπλέον Σωματική Ζημιά</physicalDamage>, που αυξάνεται έως {{ maxdamageamptooltip }}, ανάλογα με τη Ζωή που του λείπει. Αν ο εχθρός πεθάνει, ο Δρ. Μούντο τον πετάει μακριά και προκαλεί <physicalDamage>{{ additionaldamage }} Σωματική Ζημιά</physicalDamage> στους εχθρούς που διαπερνά.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Βασική ζημιά", "Κόστος σε Ζωή", "Ζωή σε Ζημιά Επίθεσης"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ flathealthcost }} -> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}% -> {{ healthtoadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Ζωή", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ flathealthcost }} Ζωή"}, {"id": "DrMundoR", "name": "Υπερδοσολογία", "description": "Ο Δρ. Μούντο ενισχύει τον εαυτό του με χημικά και θεραπεύει άμεσα ένα ποσοστό της Ζωής που του λείπει. Στη συνέχεια, αποκτά μπόνους Ταχύτητα Κίνησης και αναπληρώνει ένα ποσοστό της μέγιστης Ζωής του σε μεγάλο χρονικό διάστημα.", "tooltip": "Ο Δρ. Μούντο ενισχύει τον εαυτό του με χημικά, αποκτώντας το <healing>{{ missinghealthheal*100 }}% της Ζωής που του λείπει ως μέγιστη Ζωή</healing>, <speed>{{ speedboostamount*100 }}% Ταχύτητα Κίνησης</speed> και αναπληρώνει το <healing>{{ maxhealthhot*100 }}% της μέγιστης Ζωής του</healing> σε διάστημα {{ duration }} δευτ.<br /><br />Στη βαθμίδα 3, οι επιδράσεις Θεραπείας αυξάνονται κατά επιπλέον {{ bonuspernearbychampion*100 }}% για κάθε κοντινό εχθρικό Ήρωα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Μπόνους Ζωή", "Ταχύτητα Κίνησης", "% Μέγιστης Ζωής"], "effect": ["{{ missinghealthheal*100.000000 }}% -> {{ missinghealthhealnl*100.000000 }}%", "{{ speedboostamount*100.000000 }}% -> {{ speedboostamountnl*100.000000 }}%", "{{ maxhealthhot*100.000000 }}% -> {{ maxhealthhotnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}], "passive": {"name": "Πάει Όπου Θέλει Αυτός", "description": "Ο Δρ. Μούντο αντιστέκεται στην πρώτη επίδραση Ακινητοποίησης που δέχεται, αλλά χάνει Ζωή και αφήνει ένα χημικό δοχείο κοντά του. Ο Δρ. Μούντο μπορεί να σηκώσει το δοχείο περνώντας από πάνω του, για να αναπληρώσει ένα ποσοστό της Ζωής του και να μειώσει τον Χρόνο Επαναφόρτισης αυτής της ικανότητας.<br><br>Ο Δρ. Μούντο αποκτά επίσης σημαντικά αυξημένη Αναπλήρωση Ζωής.<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}