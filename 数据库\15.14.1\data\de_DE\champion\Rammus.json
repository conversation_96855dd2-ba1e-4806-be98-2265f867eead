{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rammus": {"id": "<PERSON><PERSON>", "key": "33", "name": "<PERSON><PERSON>", "title": "das Panzergürteltier", "image": {"full": "Rammus.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "33000", "num": 0, "name": "default", "chromas": false}, {"id": "33001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "33002", "num": 2, "name": "Chrom-Rammus", "chromas": false}, {"id": "33003", "num": 3, "name": "Geschmolzener Rammus", "chromas": false}, {"id": "33004", "num": 4, "name": "Freljord-Rammus", "chromas": false}, {"id": "33005", "num": 5, "name": "<PERSON>-<PERSON><PERSON>", "chromas": false}, {"id": "33006", "num": 6, "name": "Fullmetal-Rammus", "chromas": false}, {"id": "33007", "num": 7, "name": "Sandwächter Rammus", "chromas": false}, {"id": "33008", "num": 8, "name": "Libero-Rammus", "chromas": true}, {"id": "33016", "num": 16, "name": "Hextech-Rammus", "chromas": false}, {"id": "33017", "num": 17, "name": "Astronauten-Rammus", "chromas": true}, {"id": "33026", "num": 26, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> wird von vielen idealisi<PERSON>, von einigen kaum ernst genommen und ist generell ein kuriose<PERSON>, das voller Rätsel steckt. Er steckt in einem stacheligen Panzer, der ihn schützt, und ist Gegenstand immer verrückterer Theorien bezüglich seines Ursprungs, die von Halbgö<PERSON>n, heiligen Orakeln bis hin zum einfachen Tier, das magisch verwandelt wurde, alles abdecken. Was auch immer die Wahrheit sein mag, <PERSON><PERSON> behält seine Gedanken bei sich und hält auf seinen Streifzügen durch die Wüste von Shurima für niemanden an.", "blurb": "<PERSON><PERSON> wird von viel<PERSON> idealisi<PERSON>, von einigen kaum ernst genommen und ist generell ein kuriose<PERSON>, das voller Rätsel steckt. Er steckt in einem stacheligen Panzer, der ihn schützt, und ist Gegenstand immer verrückterer Theorien bezüglich seines...", "allytips": ["„Dornenwalze“ eignet sich hervorragend zur Flucht.", "Gegnerische Champions im Umfeld verbündeter Türme können verspottet werden, um den Angriff der Türme auf diese zu lenken.", "„Beben“ und „Eingerollte Abwehrhaltung“ eignen sich im späten Spiel dazu, <PERSON><PERSON><PERSON><PERSON> ausz<PERSON>. Wenn es im Teamkampf nicht voran<PERSON>, kann es nützlich sein, auszubrechen und Gebäude anzugreifen."], "enemytips": ["<PERSON><PERSON><PERSON> da<PERSON>, wann seine „Eingerollte Abwehrhaltung“ abgelaufen ist. Rammus hat viel geringere Werte als ein normaler Tank, wenn er sich nicht in dieser Haltung befindet.", "<PERSON><PERSON>t häufig seine Rüstung, was ihn besonders anfällig gegen Zauberschaden macht, wenn er „Eingerollte Abwehrhaltung“ nicht aktiviert hat."], "tags": ["Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 10, "magic": 5, "difficulty": 5}, "stats": {"hp": 675, "hpperlevel": 100, "mp": 310, "mpperlevel": 33, "movespeed": 335, "armor": 35, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.55, "mpregen": 7.85, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.215, "attackspeed": 0.7}, "spells": [{"id": "PowerBall", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Rammus rollt als dornige Kugel auf seine Gegner zu und wird immer schneller. Dabei verursacht er Schaden und verlangsamt die Ziele, die vom Aufprall betroffen sind.", "tooltip": "Rammus rollt sich zusammen, erhält <speed>{{ minimummovespeed }}&nbsp;Lauftempo</speed> und beschleunigt im Laufe von {{ rollduration }}&nbsp;Sekunden auf bis zu <speed>{{ maximummovespeed }}&nbsp;Lauftempo</speed>. <PERSON><PERSON> hält an, wenn er mit einem <PERSON> kollidiert, verursacht <magicDamage>{{ powerballdamage }}&nbsp;magischen Schaden</magicDamage>, <status>stößt</status> <PERSON><PERSON><PERSON> in der Nähe zurück und <status>verlangsamt</status> sie {{ slowduration }}&nbsp;Sekunde lang um {{ slowpercent }}&nbsp;%.<br /><br /><recast>Reaktivierung:</recast> Rammus beendet diese Fähigkeit frühzeitig.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ slowpercent }}&nbsp;% -> {{ slowpercentNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1000, 1000, 1000, 1000, 1000]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerBall.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DefensiveBall<PERSON>url", "name": "Eingerollte Abwehrhaltung", "description": "Rammus nimmt eine Abwehrhaltung ein, in der sich seine Rüstung und seine Magieresistenz stark erhöhen und er den Schaden normaler Angriffe auf den angreifenden Gegner zurückwirft.", "tooltip": "<PERSON>mus nimmt {{ buffduration }}&nbsp;Sekunden lang eine defensive Haltung ein und erhält <scaleArmor>{{ bonusarmortooltip }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ bonusmrtooltip }}&nbsp;Magieresistenz</scaleMR>. Außerdem fügt er <PERSON>, die ihn angreifen, <magicDamage>{{ returndamagecalc }}&nbsp;magischen Schaden</magicDamage> zu.<br /><br /><recast>Reaktivierung:</recast> Ram<PERSON> beendet diese Fähigkeit frühzeitig.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzliche Rüstung", "Zusätzliche Magieresistenz", "Zusätzliche Rüstung (%)", "Zusätzliche Magieresistenz (%)"], "effect": ["{{ flatbonusarmor }} -> {{ flatbonusarmorNL }}", "{{ flatbonusmr }} -> {{ flatbonusmrNL }}", "{{ bonusarmorpercent*100.000000 }}&nbsp;% -> {{ bonusarmorpercentnl*100.000000 }}&nbsp;%", "{{ bonusmrpercent*100.000000 }}&nbsp;% -> {{ bonusmrpercentnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DefensiveBallCurl.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PuncturingTaunt", "name": "Provokation", "description": "Rammus verspottet einen gegnerischen Champion oder ein Monster und stachelt sie so zu einem leichtsinnigen Angriff gegen ihn an.", "tooltip": "Ram<PERSON> <status>verspottet</status> {{ duration }}&nbsp;Sekunden lang einen gegnerischen Champion oder ein Monster. Monster erleiden <magicDamage>{{ monsterdamagecalc }}&nbsp;magischen <PERSON></magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> an <PERSON>"], "effect": ["{{ duration }} -> {{ durationNL }}", "{{ monsterdamage }} -> {{ monsterdamageNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "PuncturingTaunt.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Tremors2", "name": "Aufsteigender Schlag", "description": "<PERSON><PERSON> springt in die Luft und schlägt an einem Zielbereich auf, wodurch er magischen Schaden verursacht und Gegner verlangsamt. Wenn diese Fähigkeit ausgeführt wird, während „Dornenwalze“ aktiv ist, werden Gegner nahe des Zentrums zusätzlich hochgeschleudert.", "tooltip": "<PERSON><PERSON> springt in die Luft und schlägt an einem Zielbereich auf, verursacht <magicDamage>{{ initialdamagecalc }} magischen Schaden</magicDamage> und <status>verlangsamt</status> Gegner {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%. Bei Aktivierung während <spellName>Dorne<PERSON>walze</spellName> erleiden Gegner im Zentrum zusätzlich <magicDamage>{{ spell.powerball:powerballdamage }}&nbsp;magischen Schaden</magicDamage> und werden {{ knockupduration }}&nbsp;Sekunden lang <status>hochgeschleudert</status>.<br /><br />Anschließend erzeugt Rammus im Bereich {{ buffduration }}&nbsp;Sekunden lang {{ numberofpulses }}&nbsp;Nachbeben, die erneut <status>verlangsamen</status>.<br /><br />Die Reichweite dieser Fähigkeit wird durch Rammus' <speed>Lauftempo</speed> erhöht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "Tremors2.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Rammus erhält zusätzlichen Angriffsschaden, der mit seiner Rüstung und Magieresistenz skaliert.", "image": {"full": "Armordillo_ScavengeArmor.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}