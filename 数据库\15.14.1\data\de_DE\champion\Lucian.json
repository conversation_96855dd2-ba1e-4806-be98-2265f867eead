{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lucian": {"id": "Lucian", "key": "236", "name": "Lucian", "title": "<PERSON> Läuterer", "image": {"full": "Lucian.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "236000", "num": 0, "name": "default", "chromas": true}, {"id": "236001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "236002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "236006", "num": 6, "name": "PROJEKT: <PERSON>", "chromas": true}, {"id": "236007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "236008", "num": 8, "name": "High Noon-Lucian", "chromas": true}, {"id": "236009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "236018", "num": 18, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "236019", "num": 19, "name": "<PERSON><PERSON><PERSON><PERSON>uer<PERSON>Lucian (Prestige)", "chromas": false}, {"id": "236025", "num": 25, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "236031", "num": 31, "name": "Arkana-Lucian", "chromas": true}, {"id": "236040", "num": 40, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "236052", "num": 52, "name": "Wintergeweihter Lucian", "chromas": true}, {"id": "236062", "num": 62, "name": "Maskierte Gerechtigkeit Lucian", "chromas": true}], "lore": "<PERSON>, ein <PERSON><PERSON><PERSON><PERSON> Lichts, ist ein unerbittliche<PERSON> <PERSON><PERSON><PERSON> von unsterblichen Geisterwesen. Er verfolgt diese unablässig und vernichtet sie mit seinen beiden Reliktpistolen. Als der Geist Thresh seine Frau tötete, beschrit<PERSON> <PERSON> den Weg der Rache – doch auch, nachdem sie ins Leben zurückgekehrt ist, bleibt sein Z<PERSON> unvermindert. Erbarmungslos und zielstrebig macht Lucian vor nichts <PERSON>, um die Lebenden vor den längst verstorbenen Gräueln des schwarzen Nebels zu schützen.", "blurb": "<PERSON>, ein <PERSON><PERSON><PERSON> Lichts, ist ein unerbittlicher Jä<PERSON> von unsterblichen Geisterwesen. Er verfolgt diese unablässig und vernichtet sie mit seinen beiden Reliktpistolen. Als der Geist Thresh seine Frau tötete, beschritt <PERSON> den Weg der Rache –...", "allytips": ["<PERSON><PERSON><PERSON> <PERSON>e Schadensspitzen solltest du versuchen, „Erbarmungslose Verfolgung“ und „Durchdringendes Licht“ zu kombinieren.", "„Flammenschuss“ explodiert sternförmig. Versuche ihn so abzu<PERSON>uern, dass die Strahlen gegnerische Champions treffen.", "Wenn du erst einmal einen Winkel für „Niedermähen“ gewählt hast, kannst du diesen nicht mehr ändern. Wähle den Augenblick also mit Bedacht!", "<PERSON><PERSON> „Lichtschütze“ profitiert <PERSON> von Angriffsschaden als von Angriffstempo."], "enemytips": ["<PERSON> hat hohe Schadensspitzen, jedoch wenig anhaltenden Schaden.", "<PERSON> kann die Richtung von Niedermähen nicht ändern. <PERSON><PERSON><PERSON> dies zu deinem Vorteil, indem du die Geschosse umgehst.", "Durchdringendes Licht verleiht Lucian keine zusätzliche Angriffsreichweite. <PERSON><PERSON> brauch<PERSON> also trotzdem ein Z<PERSON> in Reichweite, um den Schuss ausführen zu können. Umgehe Durchdringendes Licht, indem du dir im Voraus überlegst, welchen Win<PERSON> Lucian wählen wird."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 641, "hpperlevel": 100, "mp": 320, "mpperlevel": 43, "movespeed": 335, "armor": 28, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.9, "attackspeedperlevel": 3.3, "attackspeed": 0.638}, "spells": [{"id": "LucianQ", "name": "Durchdringendes Licht", "description": "<PERSON> feuert einen Blitz durchdringenden Lichts durch ein Ziel <PERSON>ch.", "tooltip": "<PERSON> feuert einen Blitz aus durchdringendem Licht ab, der <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Angriffsschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [48, 56, 64, 72, 80], "costBurn": "48/56/64/72/80", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [60, 75, 90, 105, 120], [1000, 1000, 1000, 1000, 1000], [0.41, 0.41, 0.41, 0.41, 0.41], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "60/75/90/105/120", "1000", "0.41", "100", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "LucianQ.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> feuert ein G<PERSON> ab, das sternförmig explodiert und Gegner markiert sowie kurzzeitig aufdeckt. <PERSON> erhält Lauftempo, wenn er markierte Gegner angreift.", "tooltip": "<PERSON> feuert einen <PERSON> ab, der am Ende seiner Reichweite oder am ersten getroffenen Gegner explodiert und <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> ve<PERSON><PERSON><PERSON>, <PERSON>egner kurzzeitig aufdeckt und sie 6&nbsp;Sekunden lang markiert.<br /><br /><PERSON><PERSON> oder ein Verbündeter einem markierten Gegner Schaden zufügt, erhält Lucian 1&nbsp;Sekunde lang <speed>{{ e2 }}&nbsp;Lauftempo</speed>. Verbündete, die diesen Effekt auslösen, gewähren Lucian außerdem <attention>Wachsamkeit</attention>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Lauftempo", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [60, 65, 70, 75, 80], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [700, 700, 700, 700, 700]], "effectBurn": [null, "75/110/145/180/215", "60/65/70/75/80", "900", "0", "1", "200", "1", "6", "1", "700"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "LucianW.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianE", "name": "Erbarmungslose Verfolgung", "description": "<PERSON> springt eine kurze Strecke. Angriffe durch „Lichtschütze“ verringern die Abklingzeit von „Erbarmungslose Verfolgung“.", "tooltip": "<PERSON> springt.<br /><br />Die Abklingzeit wird um {{ e1 }}&nbsp;<PERSON><PERSON><PERSON> verring<PERSON>, wenn <PERSON> einen G<PERSON>ner mit <spellName>Lichtschütze</spellName> trifft ({{ e2 }}&nbsp;Sekunden für Champions). {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [425, 425, 425, 425, 425], [200, 200, 200, 200, 200], [1350, 1350, 1350, 1350, 1350], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1", "2", "425", "200", "1350", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [445, 445, 445, 445, 445], "rangeBurn": "445", "image": {"full": "LucianE.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianR", "name": "Niedermähen", "description": "<PERSON><PERSON><PERSON><PERSON> entfesseln einen Kugelhagel.", "tooltip": "<PERSON> feuert {{ duration }}&nbsp;<PERSON><PERSON>nden lang (oder bis er die Fähigkeit <recast>reaktiviert</recast>) <keywordMajor>{{ totalnumshots }}</keywordMajor>&nbsp;<PERSON>h<PERSON><PERSON> in eine Richtung ab. Jeder Schuss fügt dem ersten getroffenen Gegner <physicalDamage>{{ damageperbullet }}&nbsp;normalen Schaden</physicalDamage> zu.<br /><br />W<PERSON>hrend er feuert, kann <PERSON> <spellName>Erbarmungslose Verfolgung</spellName> einsetzen.<br /><br />Gesamtschaden: <physicalDamage>{{ totaldamage }}&nbsp;normaler Schaden</physicalDamage><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1400, 1400, 1400], "rangeBurn": "1400", "image": {"full": "LucianR.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Lichtschütze", "description": "<PERSON><PERSON> wenn Lucian eine Fähigkeit e<PERSON>etzt, wird sein nächster Angriff zu einem Doppelschuss. Wenn <PERSON> durch einen Verbündeten geheilt oder mit einem Schild geschützt wird, oder wenn ein gegnerischer Champion in der Nähe bewegungsunfähig gemacht wird, verursachen seine nächsten beiden normalen Angriffe zusätzlichen magischen Schaden.", "image": {"full": "Lucian_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}