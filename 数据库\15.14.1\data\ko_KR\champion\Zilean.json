{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "질리언", "title": "시간의 수호자", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "산타 질리언", "chromas": false}, {"id": "26002", "num": 2, "name": "신바람난 질리언", "chromas": false}, {"id": "26003", "num": 3, "name": "슈리마 사막의 질리언", "chromas": false}, {"id": "26004", "num": 4, "name": "타임머신 질리언", "chromas": false}, {"id": "26005", "num": 5, "name": "핏빛달 질리언", "chromas": false}, {"id": "26006", "num": 6, "name": "달콤 가득 질리언", "chromas": true}, {"id": "26014", "num": 14, "name": "겨울의 축복 질리언", "chromas": true}], "lore": "한때 이케시아의 강력한 마법사였던 질리언은 자신의 고국이 공허에 파괴되는 것을 목격한 후 시간의 흐름에 사로잡히게 되었다. 질리언은 처참한 상실감에 슬퍼할 틈도 없이, 미래의 모든 경우의 수를 예측하기 위해 시간 마법의 주문을 외웠다. 이제 사실상 불멸의 몸이 된 질리언은 과거, 현재, 미래를 표류하며 주변 시간의 흐름을 구부리거나 왜곡하여 시간을 되감고 있다. 이케시아를 멸망하기 전으로 돌려 놓을 시점을 찾아 모든 시간대에서 망연히 부유하는 것이다.", "blurb": "한때 이케시아의 강력한 마법사였던 질리언은 자신의 고국이 공허에 파괴되는 것을 목격한 후 시간의 흐름에 사로잡히게 되었다. 질리언은 처참한 상실감에 슬퍼할 틈도 없이, 미래의 모든 경우의 수를 예측하기 위해 시간 마법의 주문을 외웠다. 이제 사실상 불멸의 몸이 된 질리언은 과거, 현재, 미래를 표류하며 주변 시간의 흐름을 구부리거나 왜곡하여 시간을 되감고 있다. 이케시아를 멸망하기 전으로 돌려 놓을 시점을 찾아 모든 시간대에서 망연히 부유하는 것이다.", "allytips": ["시한 폭탄과 되감기를 조합하여 하나의 목표에 빠르게 두 개의 시한 폭탄을 설치할 수 있습니다. 두 번째 폭탄을 설치하면 첫 번째 폭탄이 폭발하여 주위 모든 적을 기절시킵니다.", "시간 왜곡은 적을 마무리하거나 전장에서 탈출할 때 효과적입니다.", "시간 역행은 공격을 방지하는 강력한 수단이지만, 너무 빨리 사용하면 적이 금방 목표를 바꾸기 때문에 효과가 떨어질 수 있습니다."], "enemytips": ["질리언의 이동 속도에 맞출 수 있다면 그의 궁극기가 끝날 때까지 기다렸다가 결정타를 날리는 편이 좋습니다.", "질리언은 협공에 약하지만 1대1로 맞서면 제거하기 어려운 상대입니다. 팀원과 협력해 그를 제거하십시오."], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "시한 폭탄", "description": "대상 위치에 가까이 오는 유닛(챔피언 우선)에게 들러붙는 폭탄을 던집니다. 폭탄은 3초 뒤 폭발하여 적에게만 광역 피해를 입힙니다. 다른 시한 폭탄 때문에 시한 폭탄이 일찍 폭발하면 적을 기절시킵니다.", "tooltip": "질리언이 주변 작은 반경 안에 들어오는 첫 번째 유닛에게 부착되는 시한 폭탄을 던집니다. {{ e2 }}초가 지나면 폭탄이 터지면서 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />이미 폭탄이 있는 유닛에 두 번째 폭탄을 설치하면 바로 첫 번째 폭탄이 터지면서 {{ e4 }}초 동안 폭발 반경 안의 적을 <status>기절</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "소모값 @AbilityResourceName@", "피해량", "기절 지속시간:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ZileanW", "name": "되감기", "description": "질리언이 기본 스킬 재사용 대기시간을 줄여 이후 시작될 전투를 준비할 수 있습니다.", "tooltip": "질리언이 시간을 돌려 다른 기본 스킬의 재사용 대기시간을 {{ e2 }}초 감소시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "마나 {{ e3 }}"}, {"id": "TimeWarp", "name": "시간 왜곡", "description": "질리언이 유닛 주변의 시간을 왜곡시켜 짧은 시간 동안 적의 이동 속도를 줄이거나 아군의 이동 속도를 높입니다.", "tooltip": "질리언이 {{ e1 }}초 동안 적 챔피언을 {{ e2 }}% <status>둔화</status>시키거나 아군 챔피언의 <speed>이동 속도를 {{ e2 }}%</speed> 높입니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["둔화", "이동 속도"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ChronoShift", "name": "시간 역행", "description": "질리언이 아군 챔피언에게 보호용 시간 룬을 주어 해당 챔피언이 치명적인 피해를 입으면 피해를 입기 전의 시간으로 되돌립니다.", "tooltip": "질리언이 아군 챔피언에게 {{ rduration }}초 동안 보호용 시간 룬을 부여합니다. 대상이 죽을 위기에 처하면 룬이 시간을 되돌려 {{ revivestateduration }}초 동안 대상을 경직 상태로 만든 후 부활시켜 <healing>{{ rtotalheal }}의 체력</healing>을 회복시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "소모값 @AbilityResourceName@", "회복량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "시간의 유리병", "description": "질리언은 시간을 경험치의 형태로 보존하여 아군에게 줄 수 있습니다. 아군의 레벨을 올려줄 수 있을 만큼 경험치가 모이면 해당 아군을 우클릭하여 건네줄 수 있습니다. 질리언 역시 아군에게 준 만큼의 경험치를 얻습니다.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}