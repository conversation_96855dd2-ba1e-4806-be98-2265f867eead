{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kindred": {"id": "Kindred", "key": "203", "name": "Kindred", "title": "i cacciatori eterni", "image": {"full": "Kindred.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "203000", "num": 0, "name": "default", "chromas": false}, {"id": "203001", "num": 1, "name": "Kindred Ombra di Fuoco", "chromas": false}, {"id": "203002", "num": 2, "name": "Kindred <PERSON>", "chromas": false}, {"id": "203003", "num": 3, "name": "Kindred <PERSON> spirituale", "chromas": true}, {"id": "203012", "num": 12, "name": "<PERSON><PERSON> di Porcellana", "chromas": true}, {"id": "203022", "num": 22, "name": "Kindred Bau e <PERSON>lla", "chromas": true}, {"id": "203023", "num": 23, "name": "Kindred DRX", "chromas": true}, {"id": "203033", "num": 33, "name": "Kindred di Porcellana (edizione prestigio)", "chromas": false}, {"id": "203034", "num": 34, "name": "Kindred <PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, ma mai separato, <PERSON><PERSON> rappresenta le essenze gemelle della morte. La freccia dell'Agnella offre una fine rapida a chi accetta il suo destino. Il Lupo caccia coloro che fuggono dalla loro fine, divorandoli con le sue tremende fauci. Se<PERSON>ne a Runeterra ci siano molte interpretazioni relative alla natura del Kindred, ogni mortale deve scegliere il volto della sua morte.", "blurb": "<PERSON><PERSON><PERSON>, ma mai separato, <PERSON><PERSON> rappresenta le essenze gemelle della morte. La freccia dell'Agnella offre una fine rapida a chi accetta il suo destino. Il Lupo caccia coloro che fuggono dalla loro fine, divorandoli con le sue tremende fauci. Se<PERSON>ne a...", "allytips": ["Muoversi tra un attacco e l'altro nella giungla ti aiuterà a evitare i danni e a generare più guarigione con Frenesia del Lupo.", "<PERSON><PERSON><PERSON> attentamente le prede a cui vuoi dare la caccia; prenderne molte è il segreto del successo, man mano che la partita continua.", "Non entrare per primo in un grande combattimento a squadre. Aspetta che lo inizino i tuoi compagni."], "enemytips": ["Kindred è fragile. Tenendolo sotto pressione, sarà costretto a giocare con cautela.", "Elimina le prede scelte dal <PERSON>po nella giungla per rallentare la quantità di danni di Kindred.", "Quando Kindred usa Riposo dell'Agnella, cerca di entrare nell'area, perché impedisce ai campioni di morire."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 4}, "stats": {"hp": 595, "hpperlevel": 104, "mp": 300, "mpperlevel": 35, "movespeed": 325, "armor": 29, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 3.25, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "KindredQ", "name": "Danza delle frecce", "description": "Kindred rotola e spara fino a tre frecce ai bersagli nelle vicinanze.", "tooltip": "Kindred salta e spara frecce contro un massimo di 3 nemici, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e guadagnando <attackSpeed>{{ totalqattackspeed }} velocità d'attacco</attackSpeed> per {{ e8 }} secondi.<br /><br />Mentre è nel raggio di <spellName>Frenesia del Lupo</spellName>, la ricarica di questa abilità è ridotta di {{ e4 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica in Frenesia del Lupo"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e4 }} -> {{ e4NL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [40, 65, 90, 115, 140], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 3.5, 3, 2.5, 2], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0.35, 0.35, 0.35, 0.35, 0.35], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/65/90/115/140", "0", "500", "4/3.5/3/2.5/2", "100", "12", "0.35", "4", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [340, 340, 340, 340, 340], "rangeBurn": "340", "image": {"full": "KindredQ.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KindredW", "name": "Frenesia del Lupo", "description": "Il Lupo si infuria e attacca i nemici intorno a lui. L'Agnella accumula passivamente cariche muovendosi e attaccando. Quando è caricato al massimo, il prossimo attacco dell'Agnella ripristina salute.", "tooltip": "<spellPassive>Passiva:</spellPassive> Kindred ottiene cariche muovendosi e attaccando. A 100 cariche, l'attacco successivo di Kindred recupera fino a <healing>{{ attackheal }} salute</healing> in base alla salute mancante.<br /><br /><spellActive>Attiva:</spellActive> Kindred reclama un territorio ordinando a Lupo di mordere l'ultimo nemico attaccato da Agnella. Il morso di Lupo infligge <magicDamage>{{ basewolfdamage }}</magicDamage> più <magicDamage>{{ percentwolfdamage }} della salute attuale in danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [40, 45, 50, 55, 60], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [8.5, 8.5, 8.5, 8.5, 8.5], [25, 30, 35, 40, 45], [800, 800, 800, 800, 800], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/45/50/55/60", "1", "1.5", "8.5", "25/30/35/40/45", "800", "0.5", "0.5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [560, 560, 560, 560, 560], "rangeBurn": "560", "image": {"full": "KindredW.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KindredEWrapper", "name": "Timore incombente", "description": "L'Agnella spara un colpo ben p<PERSON>, rallentando il bersaglio. Se l'Agnella attacca il bersaglio altre due volte, il terzo attacco fa balzare il Lupo addosso al bersaglio, straziandolo con una grande quantità di danni.", "tooltip": "Kindred indebolisce un nemico, <status>rallentandolo</status> di un {{ totalslow }}% per {{ slowduration }} secondo.<br /><br />Al terzo attacco di Kindred contro lo stesso bersaglio entro {{ totalduration }} secondi, <PERSON><PERSON> balza per colpire il nemico infliggendo <physicalDamage>{{ basebitedamage }} danni fisici</physicalDamage> più <physicalDamage>{{ percentbitedamage }} della salute mancante in danni fisici</physicalDamage>.<br /><br />I colpi di Lupo possono diventare critici infliggendo <physicalDamage>{{ basebitedamage }}</physicalDamage> più <physicalDamage>{{ critdamage }} della salute mancante in danni fisici</physicalDamage> se il nemico è sotto {{ healththreshold }} di salute.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "KindredEWrapper.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KindredR", "name": "Riposo dell'Agnella", "description": "L'Agnella conferisce a tutti gli esseri viventi all'interno di una zona una tregua con la morte. Fino alla fine dell'effetto, nulla può morire. Alla fine, tutte le unità vengono curate.", "tooltip": "Kindred benedice il terreno per {{ e2 }} secondi, non permettendo a nessuna unità alleata, nemica o neutrale di morire al suo interno. A 10% di salute le unità non possono essere danneggiate o guarite mentre sono dentro la zona.<br /><br />Al termine della benedizione, tutte le unità all'interno vengono curate per <healing>{{ e1 }} salute</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [225, 300, 375], [4, 4, 4], [530, 530, 530], [400, 400, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "225/300/375", "4", "530", "400", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "KindredR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Marchio dei cacciatori", "description": "Kindred può marcare i bersagli da cacciare. Completare una caccia potenzia in modo permanente le abilità base di Kindred. Ogni 4 cacce completate aumenta anche la gittata d'attacco base di Kindred.", "image": {"full": "Kindred_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}