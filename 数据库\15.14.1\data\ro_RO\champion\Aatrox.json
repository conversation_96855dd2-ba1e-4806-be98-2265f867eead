{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aatrox": {"id": "Aatrox", "key": "266", "name": "Aatrox", "title": "sabia darkin", "image": {"full": "Aatrox.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "266000", "num": 0, "name": "default", "chromas": false}, {"id": "266001", "num": 1, "name": "Aatrox justițiar", "chromas": false}, {"id": "266002", "num": 2, "name": "Aatrox Mecha", "chromas": true}, {"id": "266003", "num": 3, "name": "Aatrox, spaima oceanelor", "chromas": false}, {"id": "266007", "num": 7, "name": "Aatrox lună sângerie", "chromas": false}, {"id": "266008", "num": 8, "name": "Aatrox lună sângerie (Prestigiu)", "chromas": false}, {"id": "266009", "num": 9, "name": "Aatrox victorios", "chromas": true}, {"id": "266011", "num": 11, "name": "Aatrox din Odisee", "chromas": true}, {"id": "266020", "num": 20, "name": "Aatrox lună sângerie (Prestigiu) – 2022", "chromas": false}, {"id": "266021", "num": 21, "name": "Aatrox eclipsă selenară", "chromas": true}, {"id": "266030", "num": 30, "name": "Aatrox DRX", "chromas": true}, {"id": "266031", "num": 31, "name": "Aatrox DRX (Prestigiu)", "chromas": false}, {"id": "266033", "num": 33, "name": "Aatrox primordian", "chromas": true}], "lore": "Aatrox și cei din poporul său au fost odată onorați pentru lupta pe care o purtau pentru a apăra Shurima de invazia Vidului. În cele din urmă, însă, au devenit un pericol și mai mare pentru Runeterra, fiind învinși doar de magia vicleană a muritorilor. După secole de întemnițare, Aatrox a fost primul care s-a eliberat, corupându-i și transformându-i pe cei suficient de nesăbuiți încât să încerce să mânuiască arma magică care-i conține esența. Acum, într-un corp furat, schimonosit și transformat în amintirea formei sale de demult, cutreieră Runeterra și vrea să-și dezlănțuie mult-așteptata răzbunare catastrofală.", "blurb": "Aatrox și cei din poporul său au fost odată onorați pentru lupta pe care o purtau pentru a apăra Shurima de invazia Vidului. În cele din urmă, însă, au devenit un pericol și mai mare pentru Runeterra, fiind învinși doar de magia vicleană a muritorilor...", "allytips": ["Folosește ''Năpustirea umbrei'' c<PERSON><PERSON> ''Tăișul darkin'' pentru a avea mai multe șanse să-ți nimerești inamicul.", "Abilitățile de control al maselor, precum ''Chemarea infernului'' sau efectele de imobilizare ale aliaților, te vor ajuta când vrei să-ți folosești ''Tăișul darkin''.", "Folosește ''Distrugătorul lumilor'' când îți poți forța inamicii să lupte."], "enemytips": ["Atacurile lui Aatrox sunt foarte lente, așa că folosește-te de decalajul dintre ele pentru a evita zonele de impact.", "E mai ușor să scapi din ''Chemarea infernului'' a lui Aatrox dacă fugi spre el sau spre margini.", "Stai departe de Aatrox când își folosește suprema pentru a-l împiedica să reînvie."], "tags": ["Fighter"], "partype": "Fântâna de sânge", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 38, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.651}, "spells": [{"id": "AatroxQ", "name": "Tăișul darkin", "description": "Aatrox izbește puternic cu sabia, provocând daune fizice. Poate lovi de trei ori, fiecare lovitură având o zonă AoE diferită.", "tooltip": "Aatrox izbește puternic cu sabia, provocând <physicalDamage>{{ qdamage }} daune fizice</physicalDamage>. Dacă inamicii sunt loviți de margine, sunt <status>aruncați în sus</status> pentru scurt timp și suferă <physicalDamage>{{ qedgedamage }}</physicalDamage> daune. Această abilitate poate fi <recast>refolosită</recast> de două ori. Dup<PERSON> fiecare folosire, abilitatea își schimbă forma și provoacă cu 25% mai multe daune.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune", "Raport daune din atac totale"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ qtotaladratio*100.000000 }}% -> {{ qtotaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "AatroxW", "name": "Chemarea in<PERSON>nu<PERSON>i", "description": "Aatrox lovește pământul cu o forță enormă, provocându-i daune primului inamic lovit. Campionii și monștrii mari trebuie să părăsească rapid zona de impact. În caz contrar, vor fi trași înapoi în centru și vor suferi din nou daune.", "tooltip": "Aatrox aruncă un lanț, <status>încetinind</status> primul inamic lovit cu {{ wslowpercentage*-100 }}% timp de {{ wslowduration }} secunde și provocându-i <physicalDamage>{{ wdamage }} daune fizice</physicalDamage>. Campionii și monștrii mari din junglă au {{ wslowduration }} secunde să părăsească zona de impact. În caz contrar, vor fi <status>trași</status> înapoi în centru și vor suferi din nou aceeași cantitate de daune.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune", "Încetinire"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wbasedamage }} -> {{ wbasedamageNL }}", "{{ wslowpercentage*-100.000000 }}% -> {{ wslowpercentagenl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AatroxW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "AatroxE", "name": "Năpustirea umbrei", "description": "Pasiv, Aatrox se vindecă dacă le provoacă daune campionilor inamici. La activare, se năpustește în direcția-țintă.", "tooltip": "<spellPassive>Pasiv<PERSON>:</spellPassive> Aatrox se vindecă în valoare de <lifeSteal>{{ totalevamp }}</lifeSteal> din daunele pe care le provoacă campionilor.<br /><br /><spellActive>Activă:</spellActive> Aatrox se năpustește. Poate folosi această abilitate în timp ce pregătește alte abilități.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxE.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "AatroxR", "name": "Distrugătorul lumilor", "description": "Aatrox își dezlănțuie forma demonică, terifiază minionii inamici din apropiere, primește daune din atac și viteză de mișcare, iar vindecările sale sunt mai eficiente. Dacă obține o doborâre, efectul este extins.", "tooltip": "Aatrox își dezvăluie adevărata formă demonică, <status>terifiind</status> minionii din apropiere timp de {{ rminionfearduration }} secunde și primind <speed>{{ rmovementspeedbonus*100 }}% viteză de mișcare</speed>, valoare ce scade de-a lungul a {{ rduration }} secunde. Primește și <scaleAD>{{ rtotaladamp*100 }}% daune din atac</scaleAD> și își creș<PERSON> <healing>vindecarea proprie cu {{ rhealingamp*100 }}%</healing> pe durata abilității.<br /><br />Doborârile de campioni extind durata efectului cu {{ rextension }} secunde și reîmprospătează bonusul la <speed>viteza de mișcare</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Creștere totală a daunelor din atac", "Îmbunătăț<PERSON> vindecă<PERSON>", "Viteză de mișcare", "Timp de reactivare"], "effect": ["{{ rtotaladamp*100.000000 }}% -> {{ rtotaladampnl*100.000000 }}%", "{{ rhealingamp*100.000000 }}% -> {{ rhealingampnl*100.000000 }}%", "{{ rmovementspeedbonus*100.000000 }}% -> {{ rmovementspeedbonusnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxR.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Fără cost"}], "passive": {"name": "Ipostaza îngerului morții", "description": "Periodic, următorul atac al lui Aatrox provoacă <physicalDamage>daune fizice</physicalDamage> bonus și îi reface lui Aatrox o parte din viață, ambele în funcție de viața maximă a țintei. ", "image": {"full": "Aatrox_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}