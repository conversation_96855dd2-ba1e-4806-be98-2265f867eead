{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Teemo": {"id": "<PERSON><PERSON><PERSON>", "key": "17", "name": "ティーモ", "title": "俊足の斥候", "image": {"full": "Teemo.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "17000", "num": 0, "name": "default", "chromas": false}, {"id": "17001", "num": 1, "name": "ほがらかエルフ ティーモ", "chromas": false}, {"id": "17002", "num": 2, "name": "偵察兵ティーモ", "chromas": false}, {"id": "17003", "num": 3, "name": "アナグマ ティーモ", "chromas": false}, {"id": "17004", "num": 4, "name": "宇宙飛行士ティーモ", "chromas": true}, {"id": "17005", "num": 5, "name": "もこもこしっぽティーモ", "chromas": true}, {"id": "17006", "num": 6, "name": "スーパー ティーモ", "chromas": false}, {"id": "17007", "num": 7, "name": "パンダ ティーモ", "chromas": false}, {"id": "17008", "num": 8, "name": "オメガ小隊ティーモ", "chromas": true}, {"id": "17014", "num": 14, "name": "ちび悪魔ティーモ", "chromas": true}, {"id": "17018", "num": 18, "name": "ビィーモ", "chromas": true}, {"id": "17025", "num": 25, "name": "精霊の花祭りティーモ", "chromas": false}, {"id": "17027", "num": 27, "name": "プレステージ精霊の花祭りティーモ", "chromas": false}, {"id": "17037", "num": 37, "name": "爆発花火ティーモ", "chromas": true}, {"id": "17047", "num": 47, "name": "スペースグルーヴ ティーモ", "chromas": true}], "lore": "どのような恐ろしい危険や脅威が待っていようとも、ティーモは底知れぬ情熱と陽気さで世界を偵察し続ける。揺らぐことなき道徳観を持ったこのヨードルは、誇りを持ってひたむきに「バンドルの偵察兵の掟」を守っている。時には自らの行動が周囲に与える影響に気づかないこともあるが…。そもそも偵察兵の必要性自体を疑問視する声もある中、ひとつだけはっきりしていることがある──ティーモの強い信念を侮る者は、痛い目を見ることになる。", "blurb": "どのような恐ろしい危険や脅威が待っていようとも、ティーモは底知れぬ情熱と陽気さで世界を偵察し続ける。揺らぐことなき道徳観を持ったこのヨードルは、誇りを持ってひたむきに「バンドルの偵察兵の掟」を守っている。時には自らの行動が周囲に与える影響に気づかないこともあるが…。そもそも偵察兵の必要性自体を疑問視する声もある中、ひとつだけはっきりしていることがある──ティーモの強い信念を侮る者は、痛い目を見ることになる。", "allytips": ["「毒キノコ」をうまく活用すれば、ミニオンの群れを一気に倒して効率よくゴールドを稼げる。", "ドラゴンやバロンナッシャーの居場所など、マップ上の重要な地点に「毒キノコ」を仕掛けておこう。敵チームがこれらのモンスターを倒そうとしたら、すぐに分かって便利だ。"], "enemytips": ["ティーモの「毒たっぷり吹き矢」が命中すると、戦闘から逃走しても継続ダメージを受ける。交戦の準備が整うまでは、敵から十分距離を取って安全を確保しよう。", "要所に設置された「毒キノコ」は、あらかじめ駆除しておくのが吉。"], "tags": ["Marksman", "Mage"], "partype": "マナ", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 6}, "stats": {"hp": 615, "hpperlevel": 104, "mp": 334, "mpperlevel": 25, "movespeed": 330, "armor": 24, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 9.6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3, "attackspeedperlevel": 3.38, "attackspeed": 0.69}, "spells": [{"id": "TeemoQ", "name": "目つぶしダーツ", "description": "強力な毒で敵1体の視力を低下させる。攻撃を受けた敵はダメージを受け、一定時間ブラインド状態になる。", "tooltip": "ダーツを放ち、対象に{{ blindduration }}秒間の<status>ブラインド効果</status>と、<magicDamage>{{ calculateddamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "効果時間", "ダメージ"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ blindduration }} -> {{ blinddurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoQ.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TeemoW", "name": "駆け足！", "description": "自動効果: 移動速度が増加。ただし、敵チャンピオンまたはタワーから攻撃を受けると効果が消滅する。<br><br>発動効果: 数秒間、移動速度が増加。この間は、攻撃を受けても効果が持続する。", "tooltip": "<spellPassive>自動効果:</spellPassive> チャンピオンまたはタワーから{{ passivecooldownondamagetaken }}秒間ダメージを受けないでいると、<speed>移動速度が{{ passivemovespeedbonus*100 }}%</speed>増加する。<br /><br /><spellActive>発動効果:</spellActive> {{ activemovespeedbuffduration }}秒間全力疾走し、<speed>移動速度が{{ activemovespeedbonus*100 }}%</speed>増加する。この効果は攻撃を受けても消滅しない。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果による増加移動速度", "発動効果による増加移動速度"], "effect": ["{{ passivemovespeedbonus*100.000000 }}% -> {{ passivemovespeedbonusnl*100.000000 }}%", "{{ activemovespeedbonus*100.000000 }}% -> {{ activemovespeedbonusnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TeemoW.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TeemoE", "name": "毒たっぷり吹き矢", "description": "通常攻撃のたびに、対象を毒状態にする。攻撃を受けた対象は命中時にダメージを受け、さらにその後4秒にわたって毎秒ダメージを受ける。", "tooltip": "<spellPassive>自動効果:</spellPassive> 通常攻撃で毒を付与し、追加で<magicDamage>{{ impactcalculateddamage }}の魔法ダメージ</magicDamage>を与える。さらに{{ poisonduration }}秒かけて<magicDamage>{{ totaldotdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["追加ダメージ", "毎秒ダメージ"], "effect": ["{{ impactbasedamage }} -> {{ impactbasedamageNL }}", "{{ tickbasedamage }} -> {{ tickbasedamageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "固有スキル", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoE.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "固有スキル"}, {"id": "TeemoR", "name": "毒キノコ", "description": "バックパックに収納した「毒キノコ」を1つ取り出し、破裂性の毒トラップを仕掛ける。敵がトラップを踏むと毒霧が放出され、近くにいる敵ユニットをスロウ状態にし、継続ダメージを与える。毒キノコを他の毒キノコに投げつけると、バウンドしてさらに遠くに飛んでいく。", "tooltip": "キノコのトラップを投げる。トラップは踏まれると爆発して{{ slowamount }}%の<status>スロウ効果</status>を与え、{{ debuffduration }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。その間、対象は可視化される。<br /><br />トラップはステルス状態になり、{{ mushroomduration }}分間持続する。投げたトラップが設置済みのトラップに当たると、バウンドしてから設置される。このスキルは{{ maxammo }}つまでチャージできる({{ ammorechargetime }}秒でリチャージ)。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "投げの射程", "最大バウンド距離", "トラップ最大数", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ maxbouncedistance }} -> {{ maxbouncedistanceNL }}", "{{ maxammo }} -> {{ maxammoNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 3, "cooldown": [0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [75, 55, 35], "costBurn": "75/55/35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "3", "range": [600, 750, 900], "rangeBurn": "600/750/900", "image": {"full": "TeemoR.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "やぶからヨードル", "description": "短時間行動せずにいるとインビジブル状態になる。茂みの中であれば移動中でもインビジブル状態になり、動き回っても解除されない。インビジブル状態が解除されると「奇襲モード」になり、攻撃速度が数秒間増加する。", "image": {"full": "TeemoPassive.ASU_Teemo.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}