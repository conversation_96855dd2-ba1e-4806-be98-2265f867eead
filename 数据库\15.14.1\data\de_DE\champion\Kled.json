{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kled": {"id": "<PERSON><PERSON>", "key": "240", "name": "<PERSON><PERSON>", "title": "der übellaunige Streiter", "image": {"full": "Kled.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "240000", "num": 0, "name": "default", "chromas": false}, {"id": "240001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "240002", "num": 2, "name": "<PERSON>", "chromas": true}, {"id": "240009", "num": 9, "name": "Marodeur<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "240018", "num": 18, "name": "Futternapf-Kled", "chromas": true}], "lore": "Kled ist ein beliebter noxianischer Yordle und ein Krieger, der so stur wie furchtlos ist. Er gilt al<PERSON>, das bei den Soldaten des Reiches beliebt sowie beim Adel verhasst ist, und von seinen Offizieren argwöhnisch beäugt wird. <PERSON><PERSON><PERSON> beha<PERSON>, dass Kled an jedem Feldzug der noxianischen Legionen teilgenommen, sich jeden nur erdenklichen militärischen Rang erarbeitet und nicht ein einziges Mal vor einem Kampf gekniffen hat. Obwohl der Wahrheitsgehalt oft fraglich ist, lässt sich Folgendes kaum leugnen: Kled galoppiert auf seinem mehr oder weniger treuen Reittier Skaarl in die Schlacht, beschützt mit Zähnen und Klauen, was ihm gehört … und schnappt sich alles, was er kriegen kann.", "blurb": "Kled ist ein beliebter noxianischer Yordle und ein Krieger, der so stur wie furchtlos ist. Er gilt al<PERSON> Vorbild, das bei den Soldaten des Reiches beliebt sowie beim Adel verhasst ist, und von seinen Offizieren argwöhnisch beäugt wird. Viele behaupten...", "allytips": ["<PERSON><PERSON> gene<PERSON><PERSON>, indem er Vasallen tötet, aber erhält wesentlich mehr, indem er gegen Champions kämpft.", "Der letzte Treffer von Jewalttätige Neigungen fügt mehr Schaden zu als die ersten drei - stell also sicher, dass du ihn auch landest!", "Aaangriiiff!!! kann auf sehr große Distanz genutzt werden. Versuche vorherzusehen, wo sich das gegnerische Team zum Zeitpunkt deiner Ankunft befinden würde."], "enemytips": ["<PERSON><PERSON> gene<PERSON><PERSON>, indem er <PERSON>nern mit seinem Schießeisen oder normalen Angriffen Schaden zufügt, <PERSON>asa<PERSON> tötet oder Gebäude und epische Monster angreift.", "Behalte Kleds Mutleiste im Auge, wenn er zu Fuß unterwegs ist – sobald sie 100 % erreicht, wird er wieder auf Skaarl aufsteigen und einen beträchtlichen Betrag seines Lebens zurückgewinnen.", "Kled ist wesentlich gefährlicher, wenn Jewalttätige Neigungen einsatzbereit ist."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 7}, "stats": {"hp": 410, "hpperlevel": 84, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "<PERSON>led<PERSON>", "name": "Bärenfalle am Seil", "description": "Kled wirft eine Bärenfalle, die dem gegnerischen Champion Schaden zufügt und ihn an den Haken nimmt. Wenn es für kurze Zeit gefesselt war, nimmt das Ziel zusätzlichen normalen Schaden und wird zu Kled gezogen.<br><br>Im abgesessenen Zustand wird diese Fähigkeit durch das Schießeisen ersetzt, ein Fernkampf-Geweh<PERSON>uss, welcher Kled zurück katapultiert und Mut wiederherstellt.", "tooltip": "<keywordMajor>Auf Skaarl:</keywordMajor> Kled wirft eine Bärenfalle, die <physicalDamage>{{ totaldamage }} normalen Schaden</physicalDamage> verursacht und sich am ersten getroffenen gegnerischen Champion oder großen Dschungelmonster verhakt.<br /><br /><PERSON><PERSON> {{ tetherpoptime }}&nbsp;Sekunden lang in der Nähe des getroffenen Gegners bleibt, reißt er die Falle heraus, fügt ihm <physicalDamage>{{ totalyankdamage }}&nbsp;normalen Schaden</physicalDamage> zu, <status>zieht</status> ihn heran und <status>verlangsamt</status> ihn {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*-100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Wurfschaden", "Re<PERSON>ßschaden", "Verlangsamung", "Abklingzeit"], "effect": ["{{ firsthitbasedamage }} -> {{ firsthitbasedamageNL }}", "{{ firsthitbasedamage*2.000000 }} -> {{ firsthitbasedamagenl*2.000000 }}", "{{ slowamount*-100.000000 }}&nbsp;% -> {{ slowamountnl*-100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KledQ.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "KledW", "name": "Jewalttätige Neigungen", "description": "Kled erhält für 4 Angriffe einen massiven Bonus auf sein Angriffstempo. Der 4. Angriff fügt außerdem mehr Schaden zu.", "tooltip": "<spellPassive>Passiv:</spellPassive> Kleds nächster Angriff gewährt {{ activeduration }}&nbsp;Sekunden lang oder für 4&nbsp;Angriffe <attackSpeed>{{ attackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />Der 4.&nbsp;Treffer verursacht zusätzlich <physicalDamage>normalen Schaden in Höhe von {{ baseflatdamage }} plus {{ percentdamage }} des maximalen Lebens</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (%)", "Abklingzeit"], "effect": ["{{ baseflatdamage }} -> {{ baseflatdamageNL }}", "{{ 4hitmaxhealthdamage }}&nbsp;% -> {{ 4hitmaxhealthdamageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "KledW.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "<PERSON>led<PERSON>", "name": "Schweinsgalopp", "description": "Kled stü<PERSON><PERSON> voran, fügt <PERSON>n normalen Schaden zu und erhält kurzfristig einen Temposchub. Kled kann diese Fähigkeit erneut wirken, um durch sein erstes Ziel zurück zu sprinten und den gleichen Schaden zu verursachen.", "tooltip": "Kled stürmt voran, fügt <PERSON> auf seinem Weg <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und zieht Vasallen und kleine Monster zu sich heran.<br /><br />Falls diese Fähigkeit einen gegnerischen Champion oder ein großes Dschungelmonster trifft, erhält Kled {{ movespeedduration }}&nbsp;Sekunde(n) lang <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed>. Er kann diese Fähigkeit innerhalb von {{ recastwindow }}&nbsp;Sekunden <recast>reaktivieren</recast>, um noch einmal durch dasselbe Ziel zu stürmen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KledE.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "KledR", "name": "Aaangriiiff!!!", "description": "<PERSON><PERSON> und Skaarl stürmen zu e<PERSON>m Zielort, hinterlassen dabei eine Spur, die Verbündeten Lauftempo gewährt, und bauen einen Schild auf. <PERSON>ka<PERSON>l visiert den ersten Champion an, auf den er trifft, und rammt ihn.", "tooltip": "Kled stürmt zum Zielbereich und hinterlässt dabei eine Spur, die Verbündeten erhöhtes <speed>Lauftempo</speed> verleiht. Während Kled losstürmt und 2&nbsp;Sekunden danach erhält er einen <shield><PERSON><PERSON><PERSON></shield> in <PERSON><PERSON><PERSON> von bis zu {{ maximumshield }}. Skaarl rammt den ersten getroffenen gegnerischen Champion, verursacht <magicDamage>magischen Schaden</magicDamage> in Höhe von <magicDamage>{{ minimumdamagetooltip }} bis {{ maximumchargedamage }} des maximalen Lebens</magicDamage> (abhäng<PERSON> von der zurückgelegten Distanz) und <status>stößt</status> ihn kurzzeitig zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Reichweite", "Abklingzeit"], "effect": ["{{ percenthpbase*3.000000 }}&nbsp;% -> {{ percenthpbasenl*3.000000 }}&nbsp;%", "{{ shieldcapbase }} -> {{ shieldcapbaseNL }}", "{{ tooltiprange }} -> {{ tooltiprangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 125, 110], "cooldownBurn": "140/125/110", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [3500, 4000, 4500], "rangeBurn": "3500/4000/4500", "image": {"full": "KledR.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>, die rückgratlose Echse", "description": "Kled reitet auf seiner „treuen“ Begleitechse Skaarl, die für ihn Schaden nimmt. <PERSON><PERSON>ls Leben auf null sinkt, steigt Kled ab.<br><br><PERSON><PERSON> <PERSON> ändern sich Kleds Fähigkeiten und er verursacht weniger Schaden an Champions. Kled kann Skaarls Mut wiederaufbauen, indem er Gegner bekämpft. Bei vollem Mut steigt Kled mit einem Teil von Skaarls Leben wieder auf.", "image": {"full": "Kled_P.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}