{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vex": {"id": "Vex", "key": "711", "name": "Vex", "title": "l'Ombramante", "image": {"full": "Vex.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "711000", "num": 0, "name": "default", "chromas": false}, {"id": "711001", "num": 1, "name": "Vex Portatrice dell'Alba", "chromas": true}, {"id": "711010", "num": 10, "name": "Vex Empirea", "chromas": true}, {"id": "711020", "num": 20, "name": "Vex Astronoma", "chromas": true}], "lore": "Nelle più nere profondità delle Isole Ombra, una yordle solitaria passeggia tra la nebbia spettrale assaporandone la cupa miseria. Armata di un'inesauribile riserva di disagio e di una potentissima ombra, Vex vive nel suo piccolo angolo di tristezza, lontano dalla rivoltante felicità del mondo e dagli ''sfigati'' che vi abitano. Pur non avendo ambizioni, non esita ad agire per cancellare sorrisi e felicità, abbattendo qualsiasi scocciatore col potere della sua angoscia.", "blurb": "Nelle più nere profondità delle Isole Ombra, una yordle solitaria passeggia tra la nebbia spettrale assaporandone la cupa miseria. Armata di un'inesauribile riserva di disagio e di una potentissima ombra, Vex vive nel suo piccolo angolo di tristezza...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 490, "mpperlevel": 32, "movespeed": 335, "armor": 23, "armorperlevel": 4.45, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 2.75, "attackspeedperlevel": 1, "attackspeed": 0.669}, "spells": [{"id": "VexQ", "name": "Spet<PERSON>puls<PERSON>", "description": "Lancia un proiettile che infligge danni e accelera durante il volo.", "tooltip": "Vex lancia un'onda di nebbia che infligge <magicDamage>{{ qdamagecalc }} danni magici</magicDamage>. <PERSON><PERSON> un ritardo, l'onda diventa più piccola e veloce.<br /><br />Consuma <keywordMajor>Dolore</keywordMajor> sui nemici colpiti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VexQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VexW", "name": "Spazio personale", "description": "Ottiene uno scudo e danneggia i nemici vicini.", "tooltip": "Vex ottiene uno <shield>{{ shieldcalc }} scudo</shield> per {{ shieldduration }} secondi ed emette un'onda d'urto che infligge <magicDamage>{{ wdamagecalc }} danni magici</magicDamage>.<br /><br /><PERSON>suma <keywordMajor><PERSON>lore</keywordMajor> sui nemici colpiti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldamount }} -> {{ shieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "VexW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VexE", "name": "<PERSON> potere", "description": "Evoca una zona di rallentamento che infligge danni e applica Dolore ai nemici.", "tooltip": "Vex ordina all'Ombra di volare verso una posizione, aumentando di dimensioni mentre si muove. Al suo arrivo infligge <magicDamage>{{ edamagecalc }} danni magici</magicDamage> e <status>rallenta</status> di un {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />L'uccisione di un nemico con questa abilità riduce di un {{ gloomcdnonchamptooltip*100 }}% la ricarica di <keywordMajor>Dolore e Noia</keywordMajor>.<br /><br />Applica <keywordMajor>Dolore</keywordMajor> ai nemici colpiti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Rapporto potere magico totale"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ apratio*100.000000 }} -> {{ aprationl*100.000000 }}"]}, "maxrank": 5, "cooldown": [13, 13, 13, 13, 13], "cooldownBurn": "13", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "VexE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VexR", "name": "Cucitura d'Ombra", "description": "Spara un proiettile che marchia un campione nemico. Rilancia per scattare verso di lui e infliggere danni.", "tooltip": "Ombra si spinge in avanti, infliggendo <magicDamage>{{ spell.vexr:rdamagecalc }} danni magici</magicDamage> e marchiando per 4 secondi il primo campione nemico colpito.<br /><br /><recast>Rilancio</recast>: scatta verso il campione marchiato, infliggendo <magicDamage>{{ spell.vexr:recastdamagecalc }} danni magici</magicDamage> quando arriva.<br /><br />Se il campione marchiato muore entro {{ spell.vexr:takedownwindow }} secondi dopo aver subito danni da quest'abilità, il tempo di ricarica viene azzerato temporaneamente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "<PERSON><PERSON>", "Gitt<PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ recastdamage }} -> {{ recastdamageNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "VexR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Noia e Dolore", "description": "Vex viene periodicamente potenziata, permettendo alla sua abilità successiva di impaurire i nemici e di interrompere gli scatti. Ogni volta che un nemico vicino scatta, Vex applica un marchio che può essere consumato per infliggere danni bonus, riducendo anche la ricarica del suo stato potenziato.", "image": {"full": "Icons_Vex_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}