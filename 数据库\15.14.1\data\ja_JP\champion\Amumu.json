{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "アムム", "title": "めそめそミイラ", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "ファラオ アムム", "chromas": false}, {"id": "32002", "num": 2, "name": "冬の祭典アムム", "chromas": false}, {"id": "32003", "num": 3, "name": "エモムム", "chromas": false}, {"id": "32004", "num": 4, "name": "返品済みアムム", "chromas": false}, {"id": "32005", "num": 5, "name": "プロムキング？アムム", "chromas": false}, {"id": "32006", "num": 6, "name": "小さな騎士アムム", "chromas": false}, {"id": "32007", "num": 7, "name": "めそめそロボ アムム", "chromas": false}, {"id": "32008", "num": 8, "name": "楽しいパーティ アムム", "chromas": true}, {"id": "32017", "num": 17, "name": "地獄の業火アムム", "chromas": true}, {"id": "32023", "num": 23, "name": "ヘクステック アムム", "chromas": false}, {"id": "32024", "num": 24, "name": "カボチャの王子アムム", "chromas": true}, {"id": "32034", "num": 34, "name": "磁器アムム", "chromas": true}, {"id": "32044", "num": 44, "name": "愛の疼きアムム", "chromas": true}, {"id": "32053", "num": 53, "name": "点心の天使アムム", "chromas": true}], "lore": "孤独で悲しい魂を抱いて古代シュリーマで生まれたアムムは、友達を探して世界中を彷徨っている。アムムは古代の呪いによって永遠に独りぼっちでいる運命を背負わされており、触れた者は死を免れず、愛した者は破滅の一途を辿る。アムムを目にした者曰く「彼は生ける屍だ。その体は小さく、苔の生した包帯でぐるぐる巻きにされている。」アムムは何世代にも渡って語り継がれる神話や民話、伝説にも登場する。こうした物語には、往々にして空想と真実が入り混じっている。", "blurb": "孤独で悲しい魂を抱いて古代シュリーマで生まれたアムムは、友達を探して世界中を彷徨っている。アムムは古代の呪いによって永遠に独りぼっちでいる運命を背負わされており、触れた者は死を免れず、愛した者は破滅の一途を辿る。アムムを目にした者曰く「彼は生ける屍だ。その体は小さく、苔の生した包帯でぐるぐる巻きにされている。」アムムは何世代にも渡って語り継がれる神話や民話、伝説にも登場する。こうした物語には、往々にして空想と真実が入り混じっている。", "allytips": ["アムムはチームメイトとの協力が不可欠。レーンを攻める時は、味方と一緒に行動しよう。", "アムムはクールダウン短縮ととても相性がいいが、防御力を上げることも忘れないようにしよう。クールダウン短縮と防御力を増加させるアイテムを手に入れることができれば強力だ。", "「めそめそ」は体力の多い敵に対して特に有効だ。相手チームの体力が多いチャンピオンを倒すために、彼らにまとわりつくことも考慮しよう。"], "enemytips": ["アムムのアルティメットスキルが発動可能になっている間は、味方と一ヵ所に固まらないよう気をつけよう。", "意表を突く動きをしたり、ミニオンの群れの後方にとどまるようにすれば「絡みつく包帯」で狙われにくくなる。", "「めそめそ」は体力を割合で削るスキルだ、体力が多いからといって油断しないようにしよう。"], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "絡みつく包帯", "description": "指定方向にべとべとの包帯を投げつける。敵に当たるとダメージとスタンを与え、包帯をたぐって相手の近くに素早く移動する。", "tooltip": "包帯を飛ばして最初に命中した敵のところに自身を引き寄せ、{{ e2 }}秒間<status>スタン</status>させて、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />このスキルは2回までチャージできる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "リチャージ時間", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "めそめそ", "description": "涙を流し、触れた敵に最大体力の一定割合のダメージを毎秒与え、対象に付与された<font color='#9b0f5f'>「呪い」</font>の効果時間を更新する。", "tooltip": "<toggle>発動中:</toggle> しくしく泣き出し、周囲の敵に毎秒<magicDamage>{{ basedamage }}(+最大体力の{{ totalhealthdamage }}%)の魔法ダメージ</magicDamage>を与え、<keywordMajor>「呪い」</keywordMajor>の効果時間を更新する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力割合ダメージ"], "effect": ["{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "毎秒{{ cost }}{{ abilityresourcename }}"}, {"id": "Tantrum", "name": "だだっこ", "description": "敵から受ける物理ダメージを恒久的に軽減する。発動して怒りを爆発させると、周囲の敵にダメージを与える。自身が攻撃を受けるたびに「だだっこ」のクールダウンが短縮される。", "tooltip": "<spellPassive>自動効果:</spellPassive> 自身の受ける物理ダメージが{{ damagereduction }}軽減される。また通常攻撃を受けると、このスキルのクールダウンが{{ e3 }}秒短縮される。<br /><br /><spellActive>発動効果:</spellActive> だだをこねて周囲の敵に<magicDamage>{{ tantrumdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ軽減", "クールダウン", "ダメージ"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "めそめそミイラの呪い", "description": "周囲の敵ユニットを包帯で拘束して<keywordMajor>「呪い」</keywordMajor>を付与する。さらにダメージを与えてスタンさせる。", "tooltip": "包帯を押し広げて対象を{{ rduration }}秒間<status>スタン</status>させ、<magicDamage>{{ rcalculateddamage }}の魔法ダメージ</magicDamage>を与えて<keywordMajor>「呪い」</keywordMajor>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "クールダウン", "ダメージ"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamage }} -> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "呪いの手", "description": "通常攻撃で敵に<font color='#9b0f5f'>「呪い」</font>をかける。呪われた対象は、魔法ダメージを受ける際に追加確定ダメージを受けるようになる。", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}