{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Hwei": {"id": "<PERSON><PERSON>", "key": "910", "name": "<PERSON><PERSON>", "title": "the Visionary", "image": {"full": "Hwei.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "910000", "num": 0, "name": "default", "chromas": false}, {"id": "910001", "num": 1, "name": "Winterblessed Hwei", "chromas": true}, {"id": "910011", "num": 11, "name": "Spirit Blossom Hwei", "chromas": true}], "lore": "<PERSON><PERSON> is a brooding painter who creates brilliant art in order to confront <PERSON><PERSON>'s criminals and comfort their victims. Beneath his melancholy roils a torn, emotional mind—haunted by both the vibrant visions of his imagination and the gruesome memories of his temple's massacre. <PERSON><PERSON> seeks to understand this light and dark, which drives him inevitably toward the artist who unraveled him. With paintbrush and palette, <PERSON><PERSON> shapes endless possibilities as he draws ever closer to earning closure or embracing despair.", "blurb": "<PERSON><PERSON> is a brooding painter who creates brilliant art in order to confront <PERSON><PERSON>'s criminals and comfort their victims. Beneath his melancholy roils a torn, emotional mind—haunted by both the vibrant visions of his imagination and the gruesome memories...", "allytips": ["Calm down, breathe, and focus.", "Each stroke requires precision and intention."], "enemytips": ["<PERSON><PERSON> needs to predict your positioning and take time to cast, catch him by surprise.", "<PERSON><PERSON>'s lightning is deadly but slow; it deals more damage when you are alone or unable to move.", "After <PERSON><PERSON> paints a Torment spell he is vulnerable to being rushed down."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 1, "magic": 8, "difficulty": 9}, "stats": {"hp": 580, "hpperlevel": 109, "mp": 480, "mpperlevel": 30, "movespeed": 330, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.69}, "spells": [{"id": "HweiQ", "name": "Subject: Disaster", "description": "<PERSON><PERSON> envisions a series of disasters, allowing him to paint a devastating blow.<br><br>This ability replaces <PERSON><PERSON>'s abilities with damaging abilities: Devastating Fire, Severing Bolt, and Molten Fissure.", "tooltip": "<PERSON><PERSON> paints visions of disaster that deal large damage to enemies.<br /><br /><spellName>Devastating Fire</spellName><br /><PERSON><PERSON> launches a blazing fast fireball that explodes on the first enemy hit, dealing <magicDamage>{{ tooltip_qqdamage }} plus {{ tooltip_qqbonusdamage }}% max health magic damage</magicDamage>.<br /><br /><spellName>Severing Bolt</spellName><br /><PERSON><PERSON> strikes over far distances with a delayed lightning bolt, dealing <magicDamage>{{ tooltip_qwdamage }} magic damage</magicDamage>. Hitting an Isolated or <status>Immobilized</status> target increases the damage up to <magicDamage>{{ tooltip_qwbonusdamage }} magic damage</magicDamage>, based on the target's missing health.<br /><br /><spellName>Molten Fissure</spellName><br /><PERSON><PERSON> unleashes a path of eruptions, dealing <magicDamage>{{ tooltip_qedamage }} magic damage</magicDamage> in an area and leaving behind pools of lava that <status>Slow</status> enemies by {{ spell.hweiqe:slowpercent }}% and deal <magicDamage>{{ tooltip_qedamagepersecond }} magic damage per second</magicDamage> for {{ spell.hweiqe:duration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName>Devastating Fire</spellName> | Damage", "<spellName>Devastating Fire</spellName> | Maximum Health Damage", "<spellName>Severing Bolt</spellName> | Damage", "<spellName>Severing Bolt</spellName> | Maximum Bonus Damage", "<spellName>Molten Fissure</spellName> | Explosion Damage", "<spellName>Molten Fissure</spellName> | Damage Per Second", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ tooltip_qqbasedamage }} -> {{ tooltip_qqbasedamageNL }}", "{{ tooltip_qqbonusdamage }}% -> {{ tooltip_qqbonusdamageNL }}%", "{{ tooltip_qwbasedamage }} -> {{ tooltip_qwbasedamageNL }}", "{{ tooltip_qwbonusdamagemult*100.000000 }}% -> {{ tooltip_qwbonusdamagemultnl*100.000000 }}%", "{{ tooltip_qebasedamage }} -> {{ tooltip_qebasedamageNL }}", "{{ tooltip_qebasedps }} -> {{ tooltip_qebasedpsNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "HweiQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HweiW", "name": "Subject: Serenity", "description": "<PERSON><PERSON> envisions a series of serenities, allowing him to paint invigorating settings.<br><br>This ability replaces <PERSON><PERSON>'s abilities with utility abilities: Fleeting Current, Pool of Reflection, and Stirring Lights.", "tooltip": "<PERSON><PERSON> paints visions of serenity that provide utility for himself and allied champions.<br /><br /><spellName>Fleeting Current</spellName><br /><PERSON><PERSON> launches a current of swift waters in a line that grants allies <speed>{{ tooltip_wqmovespeed }} Move Speed</speed>.<br /><br /><spellName>Pool of Reflection</spellName><br /><PERSON><PERSON> forms a protective pool that grants <shield>{{ tooltip_wwshieldamount }} Shields</shield> over time to allied champions inside reduced by {{ spell.hweiww:tooltipallymod*100 }}% for allies.<br /><br /><spellName>Stirring Lights</spellName><br /><PERSON><PERSON> creates three swirling lights that empower his next three Abilities or Attacks to deal an additional <magicDamage>{{ tooltip_weonhitdamage }} magic damage</magicDamage> and restore <scaleMana>{{ tooltip_weonhitmanarestore }} Mana</scaleMana> each.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName>Fleeting Current</spellName> | Move Speed", "<spellName>Fleeting Current</spellName> | Duration", "<spellName>Pool of Reflection</spellName> | Shield Amount", "<spellName>Stirring Lights</spellName> | Additional Damage", "<spellName>Stirring Lights</spellName> | Total Mana Restore", "Cooldown", "<PERSON><PERSON>"], "effect": ["{{ tooltip_wqbasemovespeed }}% -> {{ tooltip_wqbasemovespeedNL }}%", "{{ tooltip_wqareaduration }} -> {{ tooltip_wqareadurationNL }}", "{{ tooltip_wwshieldamountbase }} -> {{ tooltip_wwshieldamountbaseNL }}", "{{ tooltip_webaseonhitdamage }} -> {{ tooltip_webaseonhitdamageNL }}", "{{ tooltip_weonhitmanarestore*3.000000 }} -> {{ tooltip_weonhitmanarestorenl*3.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [18, 17.5, 17, 16.5, 16], "cooldownBurn": "18/17.5/17/16.5/16", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "HweiW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HweiE", "name": "Subject: Torment", "description": "<PERSON><PERSON> envisions a series of torments, allowing him to paint controlling visages.<br><br>This ability replaces <PERSON><PERSON>'s abilities with crowd control abilities: G<PERSON> Visage, <PERSON>aze of the Abyss, and Crushing <PERSON><PERSON>.", "tooltip": "<PERSON><PERSON> paints visions of torment that control enemies.<br /><br /><spellName>Grim Visage</spellName><br /><PERSON><PERSON> launches a terrifying face that causes the first enemy hit to <status>Flee</status> for {{ tooltip_eqfleeduration }} second(s) and take <magicDamage>{{ tooltip_eqdamage }} magic damage</magicDamage>.<br /><br /><spellName>Gaze of the Abyss</spellName><br /><PERSON><PERSON> forms an eye that lingers and provides vision locking onto the first enemy champion that enters and launching a homing missile that <status>Roots</status> the first enemy hit for {{ tooltip_ewrootduration }} seconds dealing <magicDamage>{{ tooltip_ewdamage }} magic damage</magicDamage>.<br /><br /><spellName>Crushing Maw</spellName><br /><PERSON><PERSON> paints a set of jaws at a target location that <status>Drags</status> enemies to the center dealing <magicDamage>{{ tooltip_eedamage }} magic damage</magicDamage> and <status>Slowing</status> enemies caught by {{ tooltip_eeslowamount }}% decaying over 1.25 seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName>Grim Visage</spellName> | Damage", "<spellName>Grim Visage</spellName> | Duration", "<spellName>Gaze of the Abyss</spellName> | Damage", "<spellName>Gaze of the Abyss</spellName> | Root Duration", "<spellName>Crushing Maw</spellName> | Damage", "<spellName>Crushing Maw</spellName> | Slow Amount", "Cooldown", "<PERSON><PERSON>"], "effect": ["{{ tooltip_eqbasedamage }} -> {{ tooltip_eqbasedamageNL }}", "{{ tooltip_eqfleeduration }} -> {{ tooltip_eqfleedurationNL }}", "{{ tooltip_ewbasedamage }} -> {{ tooltip_ewbasedamageNL }}", "{{ tooltip_ewrootduration }} -> {{ tooltip_ewrootdurationNL }}", "{{ tooltip_eebasedamage }} -> {{ tooltip_eebasedamageNL }}", "{{ tooltip_eeslowamount }}% -> {{ tooltip_eeslowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12.5, 12, 11.5, 11], "cooldownBurn": "13/12.5/12/11.5/11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "HweiE.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HweiR", "name": "Spiraling Despair", "description": "<PERSON><PERSON> paints a vision of pure despair. The first enemy champion struck becomes the center of an expanding painting that slows and damages nearby enemies. The vision explodes after reaching its maximum size or when the champion dies.", "tooltip": "<PERSON><PERSON> launches a vision of pure despair that sticks to an enemy champion for {{ duration }} seconds. The vision expands over time applying a <status>Slow</status> to enemies for {{ slowpercentperstack }}% stacking every 0.25 seconds, and dealing <magicDamage>{{ damageovertime }} magic damage per second</magicDamage>.<br /><br />Upon completion, the vision shatters, dealing <magicDamage>{{ damage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage Per Second", "Cooldown"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ basedamageovertime }} -> {{ basedamageovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 80], "cooldownBurn": "140/115/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "HweiR.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Signature of the Visionary", "description": "<PERSON><PERSON> primes enemy champions he damages with his abilities for his signature finishing touch.<br><br>Hitting an enemy with a second damaging spell completes his signature, leaving it beneath them. The signature detonates after a short delay, dealing magic damage to all enemies in range.", "image": {"full": "HweiPassive.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}