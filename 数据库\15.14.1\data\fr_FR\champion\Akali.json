{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akali": {"id": "Akali", "key": "84", "name": "Akali", "title": "As<PERSON><PERSON> rebelle", "image": {"full": "Akali.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "84000", "num": 0, "name": "default", "chromas": false}, {"id": "84001", "num": 1, "name": "Akali aiguillon", "chromas": false}, {"id": "84002", "num": 2, "name": "Akali infernale", "chromas": false}, {"id": "84003", "num": 3, "name": "Akali All-Star", "chromas": false}, {"id": "84004", "num": 4, "name": "Akali infirmière", "chromas": true}, {"id": "84005", "num": 5, "name": "Akali lune de sang", "chromas": false}, {"id": "84006", "num": 6, "name": "Akali crocs d'argent", "chromas": false}, {"id": "84007", "num": 7, "name": "Akali chasseuse de t<PERSON>", "chromas": true}, {"id": "84008", "num": 8, "name": "Akali chef sushi", "chromas": false}, {"id": "84009", "num": 9, "name": "K/DA Akali", "chromas": false}, {"id": "84013", "num": 13, "name": "K/DA Akali prestige", "chromas": false}, {"id": "84014", "num": 14, "name": "PROJET : <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "84015", "num": 15, "name": "True Damage Akali", "chromas": true}, {"id": "84032", "num": 32, "name": "K/DA ALL OUT Akali", "chromas": true}, {"id": "84050", "num": 50, "name": "Akali cauchemar criminel", "chromas": false}, {"id": "84060", "num": 60, "name": "K/DA Akali prestige (2022)", "chromas": false}, {"id": "84061", "num": 61, "name": "Akali gardienne des étoiles", "chromas": false}, {"id": "84068", "num": 68, "name": "DRX Akali", "chromas": false}, {"id": "84070", "num": 70, "name": "Akali de l'assemblée", "chromas": false}, {"id": "84071", "num": 71, "name": "Akali de l'assemblée prestige", "chromas": true}, {"id": "84082", "num": 82, "name": "Akali <PERSON>", "chromas": false}, {"id": "84092", "num": 92, "name": "Akali fleur spirituelle", "chromas": false}], "lore": "Ayant abandonn<PERSON> l'Ordre Ki<PERSON> et le titre de <PERSON> de l'ombre, Akali combat aujourd'hui seule, prête à devenir l'arme mortelle dont son peuple a besoin. Bien qu'elle n'oublie rien de tout ce que son maître Shen lui a enseigné, elle a juré de défendre Ionia contre ses ennemis, une élimination après l'autre. Akali tue sans faire de bruit, mais son message est fort et clair : craignez l'assassin sans maître.", "blurb": "Ayant abandon<PERSON><PERSON> l'Ordre Ki<PERSON> et le titre de <PERSON>ing de l'ombre, Akali combat aujourd'hui seule, prête à devenir l'arme mortelle dont son peuple a besoin. Bien qu'elle n'oublie rien de tout ce que son maître Shen lui a enseigné, elle a juré de défendre...", "allytips": ["Akali peut facilement tuer les champions fragiles. Laissez votre équipe engager le combat, puis frappez les adversaires en retrait.", "Linceul nébuleux vous protège même dans les situations les plus dangereuses. Profitez-en pour refaire le plein d'énergie avant de lancer un nouvel assaut."], "enemytips": ["Quand Akali est occultée par Linceul nébuleux, elle peut toujours être touchée par les effets de zone. <PERSON>la révèle momentanément sa position.", "La Vague de kunais d'Akali est puissante quand elle est utilisée à portée maximale et avec le maximum d'énergie. Attaquez-la quand il lui reste peu d'énergie pour accroître vos chances de gagner vos échanges de coups.", "Rentrez à la base si vos PV sont bas et si Akali dispose de son ultime."], "tags": ["Assassin"], "partype": "Énergie", "info": {"attack": 5, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 119, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 23, "armorperlevel": 4.7, "spellblock": 37, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.625}, "spells": [{"id": "AkaliQ", "name": "Vague de k<PERSON>is", "description": "Akali lance cinq kunais, infligeant des dégâts selon ses dégâts d'attaque supplémentaires et sa puissance et ralentissant les ennemis.", "tooltip": "Akali lance des kunais dans un arc de cercle, infligeant <magicDamage>{{ damage }} pts de dégâts magiques</magicDamage>. Les ennemis touchés au maximum de la portée sont <status>ralentis</status> de {{ slowpercentage*100 }}% pendant {{ slowduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Coût en @AbilityResourceName@", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamagenamed }} -> {{ basedamagenamedNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [110, 100, 90, 80, 70], "costBurn": "110/100/90/80/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "AkaliQ.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliW", "name": "Linceul nébuleux", "description": "Akali crée un nuage de fumée et augmente brièvement sa vitesse de déplacement. Dans ce nuage, Akali est invisible et impossible à cibler. Si elle attaque ou utilise des compétences, elle est temporairement révélée.  ", "tooltip": "Akali lâche une bombe fumigène, créant un nuage de fumée qui s'étend et dure {{ baseduration }} sec et gagnant <speed>+{{ movementspeed }}% de vitesse de déplacement</speed> (ce bonus diminue en {{ movementspeedduration }} sec).<br /><br />Akali augmente son énergie max de {{ energyrestore }} tant que le nuage de fumée est actif. <br /><br />Dans la fumée, Akali est <keywordStealth>invisible</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vitesse de d<PERSON>placement", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ movementspeed }}% -> {{ movementspeedNL }}%", "{{ baseduration }} -> {{ basedurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [140, 140, 140, 140, 140], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [250, 250, 250, 250, 250], [60, 65, 70, 75, 80], [0.3, 0.35, 0.4, 0.45, 0.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [5, 5.5, 6, 6.5, 7], [0, 0, 0, 0, 0]], "effectBurn": [null, "140", "4", "0", "250", "60/65/70/75/80", "0.3/0.35/0.4/0.45/0.5", "1", "0", "5/5.5/6/6.5/7", "0"], "vars": [], "costType": " pts d'énergie", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AkaliW.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "Rend {{ <PERSON>restore }} pts d'énergie"}, {"id": "AkaliE", "name": "Lancer acrobatique", "description": "Fait un salto arrière et lance un shuriken vers l'avant, infligeant des dégâts magiques. Le premier ennemi ou nuage de fumée touché est marqué. Réactivez la compétence pour vous ruer sur la cible marquée et infliger des dégâts supplémentaires.", "tooltip": "Akali fait un salto arrière en lançant un shuriken, infligeant <magicDamage>{{ e1damage }} pts de dégâts magiques</magicDamage> et marquant le premier ennemi ou nuage de fumée touché. Akali peut <recast>réactiver</recast> cette compétence une fois pour se ruer vers la cible marquée, infligeant <magicDamage>{{ e2damagecalc }} pts de dégâts magiques</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Dégâts de base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AkaliE.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliR", "name": "<PERSON><PERSON><PERSON><PERSON> absolue", "description": "Akali bondit dans une direction, blessant les ennemis qu'elle frappe. Réactivation : Akali se rue dans une direction, exécutant tous les ennemis qu'elle frappe.", "tooltip": "Akali passe par-dessus un champion ennemi, infligeant <magicDamage>{{ cast1damage }} pts de dégâts magiques</magicDamage> à tous les ennemis sur son chemin. <br /><br />Akali peut <recast>réactiver</recast> cette compétence après {{ cooldownbetweencasts }} sec pour effectuer une percée, se ruant dans une direction et infligeant de <magicDamage>{{ cast2damagemin }}</magicDamage> à <magicDamage>{{ cast2damagemax }} pts de dégâts magiques</magicDamage> selon les PV manquants des ennemis.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Dégâts initiaux", "Dég<PERSON><PERSON> minimum", "Dégâts maximum"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ r2basedamage }} -> {{ r2basedamageNL }}", "{{ r2basedamage*3.000000 }} -> {{ r2basedamagenl*3.000000 }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [1, 1, 1], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [675, 675, 675], "rangeBurn": "675", "image": {"full": "AkaliR.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}], "passive": {"name": "<PERSON><PERSON> d'assassin", "description": "Blesser un champion avec une compétence crée un cercle d'énergie autour de lui. Quitter ce cercle renforce la prochaine attaque d'Akali en augmentant sa portée et ses dégâts.", "image": {"full": "Akali_P.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}