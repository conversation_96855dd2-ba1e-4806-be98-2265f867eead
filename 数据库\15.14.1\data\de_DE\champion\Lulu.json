{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lulu": {"id": "<PERSON>", "key": "117", "name": "<PERSON>", "title": "die Zauberin der Fae", "image": {"full": "Lulu.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "117000", "num": 0, "name": "default", "chromas": false}, {"id": "117001", "num": 1, "name": "Bittersüße Lulu", "chromas": false}, {"id": "117002", "num": 2, "name": "Verruchte Lulu", "chromas": false}, {"id": "117003", "num": 3, "name": "Drache<PERSON>ähmer-<PERSON>", "chromas": true}, {"id": "117004", "num": 4, "name": "Winterwunder-<PERSON>", "chromas": false}, {"id": "117005", "num": 5, "name": "Poolparty-Lulu", "chromas": true}, {"id": "117006", "num": 6, "name": "Sternenwächterin Lulu", "chromas": false}, {"id": "117014", "num": 14, "name": "Kosmische Zauberin Lulu", "chromas": true}, {"id": "117015", "num": 15, "name": "Pyjamawächterin Lulu", "chromas": false}, {"id": "117026", "num": 26, "name": "Weltraum-Groove-<PERSON>", "chromas": true}, {"id": "117027", "num": 27, "name": "Weltraum-Groove-Lulu (Prestige)", "chromas": false}, {"id": "117037", "num": 37, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": true}, {"id": "117046", "num": 46, "name": "Konditorei-Lulu", "chromas": true}], "lore": "Die Yordle-Magierin Lulu ist da<PERSON><PERSON><PERSON> bekan<PERSON>, auf ihrem Streifzug durch Runeterra traumartige Illusionen und fantasievolle Kreaturen zu beschwören, und ist stets in Begleitung ihrer Fee Pix unterwegs. Lulu verformt die Realität aus Lust und Laune heraus, und verzerrt die Materie der realen Welt sowie alles, was sie als Beschränkung dieser banalen, gewöhnlichen Welt versteht. W<PERSON>hrend andere ihre Magie als bestenfalls unnatürlich und schlimmstenfalls gefährlich beschreiben, glaubt sie, dass jeder eine Prise Magie vertragen kann.", "blurb": "Die Yordle-Magierin Lulu ist da<PERSON><PERSON><PERSON> be<PERSON>, auf ihrem Streifzug durch Runeterra traumartige Illusionen und fantasievolle Kreaturen zu beschwören, und ist stets in Begleitung ihrer Fee Pix unterwegs. Lulu verformt die Realität aus Lust und Laune heraus...", "allytips": ["„Glitterlanze“ kann mit verschiedenen Winkeln genutzt werden, je nachdem wo sich der Mauszeiger befindet – der abgedeckte Bereich kann sich so massiv ändern.", "„<PERSON><PERSON>, Pix!“ gewährt Fernkämpfern zusätzlichen Schaden und „Wildwuchs“ lässt Tanks oder Kämpfer besser eröffnen."], "enemytips": ["Die Stöße von Lulus Fee können abgefangen werden – verstecke dich hinter deinen Vasallen, um zusätzlichen Treffern zu entgehen.", "<PERSON> glänzt besonders dann, wenn G<PERSON>ner aufs Ganze gehen. Riskier<PERSON> dies nicht! Setze ihr wiederholt zu, um sie und ihren Partner aus der Lane zu drängen."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 565, "hpperlevel": 92, "mp": 350, "mpperlevel": 55, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.6, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "LuluQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Pix und Lulu feuern jeweils einen Stoß magischer Energie ab, der getroffenen Gegnern Schaden zufügt und sie stark verlangsamt.", "tooltip": "<PERSON> und Pix feuern jeweils ein durchdringendes Geschoss ab, das Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt und sie um {{ slowamount*-100 }}&nbsp;% <status>verlangsamt</status>. Diese Verlangsamung fällt über {{ slowduration }}&nbsp;Sekunden hinweg ab.<br /><br />Weitere Geschosse fügen Gegnern <magicDamage>{{ bonusmissiledamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "LuluQ.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuluW", "name": "<PERSON><PERSON>", "description": "Auf einen Verbündeten gewirkt, erhöht sich kurzzeitig dessen Angriffs- und Lauftempo. Auf einen Gegner gewirkt, verwandelt sich dieser in ein niedliches Tierchen, das weder angreifen noch zaubern kann.", "tooltip": "Wird die Fähigkeit auf einen Verbündeten angewendet, gew<PERSON>hrt Lulu {{ e5 }}&nbsp;Sekunden lang <speed>{{ totalms }}&nbsp;Lauftempo</speed> und <attackSpeed>{{ e7 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />Wird die Fähigkeit auf einen Gegner angewendet, <status>verwandelt</status> <PERSON> diesen {{ e3 }}&nbsp;Sekunden lang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dauer des Lauf- und Angriffstempos", "Angriffstempo", "Verwandlungsdauer"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ effect7amount*100.000000 }}&nbsp;% -> {{ effect7amountnl*100.000000 }}&nbsp;%", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [18, 18, 18, 18, 18], "cooldownBurn": "18", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [1.2, 1.4, 1.6, 1.8, 2], [-60, -60, -60, -60, -60], [3, 3.25, 3.5, 3.75, 4], [0.01, 0.01, 0.01, 0.01, 0.01], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.25", "0", "1.2/1.4/1.6/1.8/2", "-60", "3/3.25/3.5/3.75/4", "0.01", "20/22.5/25/27.5/30", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "LuluW.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuluE", "name": "<PERSON><PERSON>, <PERSON><PERSON>!", "description": "Auf einen Verbündeten gewirkt springt Pix zu diesem und beschützt ihn. Er folgt diesem dann und verstärkt dessen Angriffe. Auf einen Gegner gewirkt springt Pix zu diesem und fügt ihm Schaden zu. Er folgt diesem und gewährt währenddessen Sicht.", "tooltip": "Wird die Fähigkeit auf einen Verbündeten angewendet, springt Pix zu ihm und gewährt ihm {{ e1 }}&nbsp;Sekunden lang <spellName>Pix, Feenbegleiter</spellName>. Wenn der Verbündete ein Champion ist, gewährt Pix ihm außerdem {{ e7 }}&nbsp;Sekunden lang einen <shield>Schild</shield> in Hö<PERSON> von {{ totalshield }}.<br /><br />Wird die Fähigkeit auf einen gegnerischen Champion angewendet, piesackt Pix diesen Gegner, fügt ihm <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu und gewährt zudem {{ e6 }}&nbsp;Sekunden lang <keywordStealth>absolute Sicht</keywordStealth> auf ihn.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [80, 120, 160, 200, 240], [25, 25, 25, 25, 25], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "80/120/160/200/240", "50", "80/120/160/200/240", "25", "4", "2.5", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "LuluE.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuluR", "name": "Wildwu<PERSON>", "description": "<PERSON> vergrößert einen Verbündeten, st<PERSON><PERSON><PERSON> in dessen Nähe in die Luft und erhöht das Leben des Verbündeten sehr stark. Einige Sekunden lang erhält der Verbündete zudem eine Aura, die nahe Gegner verlangsamt.", "tooltip": "Lulu vergrößert einen Verbündeten und <status>schleudert</status> umstehende Gegner {{ knockbackduration }}&nbsp;Sekunde lang hoch. Der vergrößerte Verbündete erhält <healing>{{ totalbonushealth }}&nbsp;maximales Leben</healing> und <status>verlangsamt</status> umstehende Gegner {{ buffduration }}&nbsp;Sekunden lang um {{ slowpercent }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzliches Leben", "Verlangsamung", "Abklingzeit"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ slowpercent }}&nbsp;% -> {{ slowpercentNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "LuluR.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>, Feenbegleiter", "description": "Pix feuert magische Geschosse aus Energie, wenn der Champion, dem er folgt, eine gegnerische Einheit angreift. Diese Geschosse verfolgen ihr Ziel, können aber von anderen Einheiten abgefangen werden.", "image": {"full": "Lulu_PixFaerieCompanion.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}