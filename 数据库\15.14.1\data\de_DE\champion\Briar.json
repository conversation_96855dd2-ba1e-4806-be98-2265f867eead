{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Briar": {"id": "<PERSON><PERSON><PERSON>", "key": "233", "name": "<PERSON><PERSON><PERSON>", "title": "der gebändigte Hunger", "image": {"full": "Briar.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "233000", "num": 0, "name": "default", "chromas": false}, {"id": "233001", "num": 1, "name": "Straßendämonen-Briar", "chromas": true}, {"id": "233010", "num": 10, "name": "Primordianische Briar", "chromas": true}], "lore": "Als gescheitertes Experiment der Schwarzen Rose erforderte Briars unkontrollierbarer Blutrausch spezielle Fesseln, um ihre rasenden Gedanken zu zügeln. Nach jahrelanger Gefangenschaft befreite sich diese lebende Waffe aus ihrem Gefängnis, um die Welt zu erkunden. Jetzt wird sie von niemandem mehr kontrolliert – sie folgt nur noch ihrem Hunger nach Wissen und Blut – und genießt die Gelegenheiten, sich auszutoben, auch wenn es ihr nicht leicht fällt, den Blutrausch zu zügeln.", "blurb": "Als gescheitertes Experiment der Schwarzen Rose erforderte Briars unkontrollierbarer Blutrausch spezielle Fesseln, um ihre rasenden Gedanken zu zügeln. Nach jahrelanger Gefangenschaft befreite sich diese lebende Waffe aus ihrem Gefängnis, um die Welt zu...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 95, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 30, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 0, "hpregenperlevel": 0, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "BriarQ", "name": "Hals über Kopf", "description": "<PERSON><PERSON><PERSON> springt zu einer Einheit und verpasst ihrem Gegner einen Fußtritt, um ihn zu betäuben und seine Rüstung zu verringern.", "tooltip": "<PERSON>riar springt zu e<PERSON>m Ziel, <status>betäubt</status> es {{ stunduration }}&nbsp;Sekunden lang, verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>en</magicDamage> und verringert dessen <scaleArmor>Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR> {{ shredduration }}&nbsp;Sekunden lang um {{ shredpercent*100 }}&nbsp;%.<br /><br /><rules>Briar priorisiert nicht länger Champions, wenn sie diese Fähigkeit während eines <keywordMajor>Blutrauschs</keywordMajor> auf einen Vasallen oder ein Monster wirkt.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Rüstungs-/Magieresistenzverringerung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredpercent*100.000000 }}&nbsp;% -> {{ shredpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "BriarQ.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Kostet {{ spell.briarp:currenthealthpercentcost*100 }}&nbsp;% des aktuellen Lebens"}, {"id": "BriarW", "name": "<PERSON><PERSON><PERSON>ch / Heißhunger", "description": "Briar springt nach vorn, zersch<PERSON><PERSON> ihre Fesseln und gerät in einen Blutrausch, in dem sie den nächstbefindlichen Gegner unerbittlich verfolgt (Champions werden priorisiert). Während des Blutrauschs erhält sie ein erhöhtes Angriffs- und Lauftempo und ihre Angriffe verursachen Schaden in einem Bereich um ihr Ziel.<br><br><PERSON><PERSON><PERSON> kann diese Fähigkeit während des Blutrauschs reaktivieren, um beim nächsten Angriff ORDENTLICH zuzubeißen. Dabei verursacht sie zusätzlichen Schaden abhängig vom fehlenden Leben des Ziels und wird abhängig vom verursachten Schaden geheilt.", "tooltip": "Briar springt nach vorn, verfällt in einen <keywordMajor><PERSON><PERSON><PERSON>ch</keywordMajor> und bewegt sich {{ berserkduration }}&nbsp;Sekunden lang automatisch auf den nächstbefindlichen Gegner (priorisiert Champions) zu. Der <keywordMajor>Blutrausch</keywordMajor> verleiht ihr <attackSpeed>{{ berserkas*100 }}&nbsp;% Angriffstempo</attackSpeed> sowie <speed>{{ berserkms*100 }}&nbsp;% Lauftempo</speed>, und ihre Angriffe verursachen <physicalDamage>{{ totalaoedamage }}&nbsp;normalen Schaden</physicalDamage> um das Ziel herum.<br /><br />Briar kann diese Fähigkeit <recast>reaktivieren</recast>, um ihren nächsten Angriff zu verstärken. Dieser verursacht dann <physicalDamage>normalen Schaden</physicalDamage> in <PERSON><PERSON><PERSON> von {{ totalattackbonusdamage }} + {{ totalattackpercentmissinghealth }}&nbsp;% des fehlenden Lebens und <healing>heilt</healing> Briar um {{ attackmaxhpheal }} + {{ attackhealpercent*100 }}&nbsp;% des verursachten Schadens.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Lauftempo", "Angriffsschadenskalierung des Flächenschadens", "<PERSON><PERSON><PERSON>", "Heilung", "Abklingzeit"], "effect": ["{{ berserkas*100.000000 }}&nbsp;% -> {{ berserkasnl*100.000000 }}&nbsp;%", "{{ berserkms*100.000000 }}&nbsp;% -> {{ berserkmsnl*100.000000 }}&nbsp;%", "{{ aoeattackdamagepercent*100.000000 }}&nbsp;% -> {{ aoeattackdamagepercentnl*100.000000 }}&nbsp;%", "{{ attackbonusdamage }} -> {{ attackbonusdamageNL }}", "{{ attackhealpercent*100.000000 }}&nbsp;% -> {{ attackhealpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "BriarW.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Kostet {{ spell.briarp:currenthealthpercentcost*100 }}&nbsp;% des aktuellen Lebens"}, {"id": "BriarE", "name": "Blutrünstiger Schrei", "description": "<PERSON><PERSON><PERSON> sammelt sich, beendet den Blutrausch und kanalisiert Energie in einen mächtigen Schrei, der Gegnern Schaden zufügt und sie verlangsamt. Während der Kanalisierung erleidet sie verringerten Schaden und heilt sich um einen Teil ihres maximalen Lebens. Ein maximal aufgeladener Schrei stößt Gegner zurück, verursacht zusätzlichen Schaden und betä<PERSON>t Gegner, die gegen eine Mauer stoßen.", "tooltip": "<charge>Aufladungsbeginn:</charge> <PERSON>riar beendet den <keywordMajor><PERSON><PERSON><PERSON><PERSON></keywordMajor> und sammelt Energie, wodurch sie {{ drpercent }}&nbsp;% Schadensverringerung erhält und über 1&nbsp;Sekunde hinweg <healing>{{ percentmaxhpheal }}&nbsp;Leben</healing> wiederherstellt.<br /><br /><release>Loslassen:</release> Briar entfesselt einen Schrei, der basierend auf der Aufladungsdauer <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht und Gegner {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercent*100 }}&nbsp;% <status>verlangsamt</status>. Bei voller Aufladung <status>stößt</status> der Schrei Gegner zurück, fügt ihnen <magicDamage>{{ wallhitdamage }}&nbsp;magischen Schaden</magicDamage> zu, wenn sie gegen eine Mauer stoßen, und <status>betäubt</status> sie {{ wallstunduration }}&nbsp;Sekunden lang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzlicher Schaden", "Heilungsskalierung"], "effect": ["{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ wallhitbasedamage }} -> {{ wallhitbasedamageNL }}", "{{ healhppercent*100.000000 }}&nbsp;% -> {{ healhppercentnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "BriarE.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Kostet {{ spell.briarp:currenthealthpercentcost*100 }}&nbsp;% des aktuellen Lebens"}, {"id": "BriarR", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>r <PERSON>t den Hämatiten ihrer Fesseln, der den ersten getroffenen Champion als Beute markiert. Dann fliegt sie direkt zu ihm, versetzt bei der Ankunft an ihrem Ziel umstehende Gegner in Furcht und verfällt in einen Zustand ungebremster Blutgier. Sie verfolgt ihre Beute bis zum Tod und erhält dabei die Vorteile von „Blutrausch“ sowie zusätzliche Rüstung, Magieresistenz, Lebensraub und Lauftempo.", "tooltip": "Briar kickt den Hämatiten ihrer Fesseln und springt zum ersten gegnerischen Champion, der davon getroffen wird. Dieser wird als Beute markiert. Bei der Landung verursacht sie <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> an allem in ihrer Nähe und Gegner, die nicht ihre Beute sind, <status>fliehen</status> {{ fearduration }}&nbsp;Sekunden lang. Sie verfällt dann in einen verstärkten <keywordMajor>Blutrausch</keywordMajor> und verfolgt ihre Beute bis zum Tod. Während der Dauer der Fähigkeit erhält sie {{ totalresists }}&nbsp;<scaleArmor>Rüstung</scaleArmor> sowie <scaleMR>Magieresistenz</scaleMR>, {{ lifestealpercent*100 }}&nbsp;% Lebensraub und zusätzlich <speed>{{ extramovespeedpercent*100 }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Lauftempo", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ lifestealpercent*100.000000 }}&nbsp;% -> {{ lifestealpercentnl*100.000000 }}&nbsp;%", "{{ extramovespeedpercent*100.000000 }}&nbsp;% -> {{ extramovespeedpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "BriarR.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Kostet {{ spell.briarp:currenthealthpercentcost*100 }}&nbsp;% des aktuellen Lebens"}], "passive": {"name": "Blutroter Fluch", "description": "Briars Angriffe und Fähigkeiten belegen Gegner mit einer steigerbaren Blutung, die Briar um einen Teil des verursachten Schadens heilt. Da sie ständig hungrig ist, erhält sie eine erhöhte Heilung, die auf ihrem fehlenden Leben basiert, aber sie verfügt über keine eigene Lebensregeneration.", "image": {"full": "BriarP.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}