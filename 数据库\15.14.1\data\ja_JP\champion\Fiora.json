{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiora": {"id": "<PERSON><PERSON>", "key": "114", "name": "フィオラ", "title": "高潔なるデュエリスト", "image": {"full": "Fiora.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "114000", "num": 0, "name": "default", "chromas": false}, {"id": "114001", "num": 1, "name": "ロイヤルガード フィオラ", "chromas": false}, {"id": "114002", "num": 2, "name": "闇夜の凶刃フィオラ", "chromas": false}, {"id": "114003", "num": 3, "name": "フィオラ先生", "chromas": true}, {"id": "114004", "num": 4, "name": "PROJECT: <PERSON><PERSON>", "chromas": true}, {"id": "114005", "num": 5, "name": "プールパーティ フィオラ", "chromas": true}, {"id": "114022", "num": 22, "name": "天翔の剣フィオラ", "chromas": false}, {"id": "114023", "num": 23, "name": "愛の純剣フィオラ", "chromas": true}, {"id": "114031", "num": 31, "name": "iG フィオラ", "chromas": true}, {"id": "114041", "num": 41, "name": "パルスファイア フィオラ", "chromas": false}, {"id": "114050", "num": 50, "name": "月の聖獣フィオラ", "chromas": false}, {"id": "114051", "num": 51, "name": "プレステージ月の聖獣フィオラ", "chromas": false}, {"id": "114060", "num": 60, "name": "魅惑の魔女フィオラ", "chromas": false}, {"id": "114069", "num": 69, "name": "妖精の王宮フィオラ", "chromas": true}, {"id": "114080", "num": 80, "name": "龍術師フィオラ", "chromas": false}, {"id": "114089", "num": 89, "name": "バトルクイーン フィオラ", "chromas": true}], "lore": "ヴァロランでもっとも恐れられる決闘士フィオラは、冷ややかで狡猾、そして目にも止まらぬ速さで強靭な両刃の剣レイピアを振るう。フィオラはデマーシア王国の名家であるローラン家に生まれた。ある時、一族を貶める事件が起こり、その結果フィオラは父親から一家の実権を奪うことになった。この事件によってローラン家の評判は地に落ちたが、フィオラは一族の名誉を回復してデマーシアの貴族として再起させるべく力を尽くしている。", "blurb": "ヴァロランでもっとも恐れられる決闘士フィオラは、冷ややかで狡猾、そして目にも止まらぬ速さで強靭な両刃の剣レイピアを振るう。フィオラはデマーシア王国の名家であるローラン家に生まれた。ある時、一族を貶める事件が起こり、その結果フィオラは父親から一家の実権を奪うことになった。この事件によってローラン家の評判は地に落ちたが、フィオラは一族の名誉を回復してデマーシアの貴族として再起させるべく力を尽くしている。", "allytips": ["「デュエリスト・ダンス」の恩恵を受けたフィオラは反撃の素早さに長けている。「急所」を突いて移動速度を増加させ、無傷で立ち回り、次の一撃に備えよう。", "「グランドチャレンジ」はいかなる頑強な相手さえもねじ伏せる。倒せば体力が回復するので、積極的に敵の前線を崩していこう。"], "enemytips": ["「デュエリスト・ダンス」は自身の急所を表示する。フィオラは優先してこの急所を狙ってくるだろう、反撃できるように備える事が大切だ。", "フィオラに行動妨害効果を与えるスキルを使用する時は注意が必要だ。彼女が「リポスト」を発動できる場合、効果が自身に跳ね返ってくる。"], "tags": ["Fighter", "Assassin"], "partype": "マナ", "info": {"attack": 10, "defense": 4, "magic": 2, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 345, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.69}, "spells": [{"id": "FioraQ", "name": "ファント", "description": "指定地点に向かってダッシュし、移動した地点から一番近くにいる敵ユニットをレイピアで突いて、物理ダメージと通常攻撃時効果を与える。", "tooltip": "指定方向にダッシュして、最も近くの敵、ワード、建造物のいずれかを突き、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。この攻撃は<keywordMajor>「急所」</keywordMajor>およびこの一撃で倒せる敵を優先する。<br /><br />敵に命中させると、このスキルのクールダウンが{{ cdrefundpercent*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "増加攻撃力反映率", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ badratio*100.000000 }}% -> {{ badrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.25, 9.5, 7.75, 6], "cooldownBurn": "13/11.25/9.5/7.75/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FioraQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FioraW", "name": "リポスト", "description": "短時間自身への全ての攻撃を受け流し、指定方向に鋭い突きを行う。反撃が命中した最初の敵チャンピオンにスロウ効果を付与する。このスキルで移動妨害効果を無効化した場合、スロウ効果のかわりにスタン効果を付与する。", "tooltip": "{{ parryduration }}秒間、あらゆるダメージ、行動妨害効果、ステータス弱化効果を受け流した後、鋭い突きを行う。突きが最初に命中したチャンピオンに<magicDamage>{{ stabdamage }}の魔法ダメージ</magicDamage>を与え、{{ ccduration }}秒間<speed>移動速度</speed>を{{ msslowpercent*-100 }}%、<attackSpeed>攻撃速度を{{ attackslowpercent*-100 }}%</attackSpeed>低下させる<status>スロウ効果</status>を付与する。このスキルで<status>移動不能効果</status>を防いだ場合、突きを当てた敵には<status>スロウ効果</status>の代わりに<status>スタン効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "FioraW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FioraE", "name": "ブレードワーク", "description": "次の2回の攻撃速度が増加し、追加効果が付与される。最初の攻撃は対象にスロウ効果を付与し、次の攻撃はクリティカルとなる。", "tooltip": "次の2回の通常攻撃は<attackSpeed>攻撃速度が{{ aspercent*100 }}%</attackSpeed>増加する。最初の通常攻撃は{{ slowduration }}秒間{{ slowpercent*-100 }}%の<status>スロウ効果</status>を与える。2回目の攻撃は必ずクリティカルになり、<physicalDamage>{{ attacktwopercenttad*100 }}%のダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クリティカルダメージ", "クールダウン", "攻撃速度"], "effect": ["{{ attacktwopercenttad*100.000000 }}% -> {{ attacktwopercenttadnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ aspercent*100.000000 }}% -> {{ aspercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "FioraE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FioraR", "name": "グランドチャレンジ", "description": "敵チャンピオンの「急所」を4カ所特定して、その近くにいる間は移動速度が増加する。4カ所の「急所」をすべて攻撃するか、敵チャンピオンが倒れるまでに「急所」を1 カ所以上攻撃していると、その後数秒間、自身と味方の体力を回復するフィールドが展開される。", "tooltip": "<spellPassive>自動効果: </spellPassive><spellName>デュエリスト・ダンス</spellName>による<speed>増加移動速度</speed>が{{ percentms*100 }}%に上昇する。<br /><br /><spellActive>発動効果: </spellActive>チャンピオンの<keywordMajor>「急所」</keywordMajor>を4箇所すべて明らかにして、合計で<trueDamage>最大体力の{{ spell.fiorapassive:rdamagetotal }}にあたる確定ダメージ</trueDamage>を与える。また、対象の近くにいる間は<spellName>「デュエリスト・ダンス」</spellName>の<speed>増加移動速度</speed>を獲得する。<br /><br />{{ markduration }}秒以内に4箇所の<keywordMajor>「急所」</keywordMajor>すべてを攻撃するか、敵チャンピオンが倒れるまでに<keywordMajor>「急所」</keywordMajor>を1箇所以上攻撃すると、その後{{ healduration }}秒間、周囲の味方チャンピオンの<healing>体力が毎秒{{ healpersecondcalc }}</healing>回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "「デュエリスト」発動時増加移動速度", "毎秒体力回復量"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ percentms*100.000000 }}% -> {{ percentmsnl*100.000000 }}%", "{{ healpersecond }} -> {{ healpersecondNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "FioraR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "デュエリスト・ダンス", "description": "このチャンピオンの<keywordMajor>「急所」</keywordMajor>を特定している。<keywordMajor>「急所」</keywordMajor>を攻撃すると、自身は<healing>体力が回復</healing>して、<speed>移動速度</speed>が増加する。", "image": {"full": "Fiora_P.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}