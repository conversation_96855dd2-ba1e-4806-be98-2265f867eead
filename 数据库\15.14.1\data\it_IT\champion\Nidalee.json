{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nidalee": {"id": "<PERSON><PERSON><PERSON>", "key": "76", "name": "<PERSON><PERSON><PERSON>", "title": "la cacciatrice bestiale", "image": {"full": "Nidalee.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "76000", "num": 0, "name": "default", "chromas": false}, {"id": "76001", "num": 1, "name": "Nidalee delle Nevi", "chromas": false}, {"id": "76002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "76003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "76004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "76005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "76006", "num": 6, "name": "Nidalee Cacciatrice di Teste", "chromas": false}, {"id": "76007", "num": 7, "name": "Nidalee dei Regni in Guerra", "chromas": false}, {"id": "76008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "76009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "76011", "num": 11, "name": "Nidalee Portatrice dell'Alba", "chromas": true}, {"id": "76018", "num": 18, "name": "<PERSON><PERSON><PERSON> Cac<PERSON>trice Cosmica", "chromas": true}, {"id": "76027", "num": 27, "name": "Nidalee DWG", "chromas": true}, {"id": "76029", "num": 29, "name": "Nidalee Canto dell'Oceano", "chromas": true}, {"id": "76039", "num": 39, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "76048", "num": 48, "name": "Nidalee la Ilusión", "chromas": false}, {"id": "76058", "num": 58, "name": "<PERSON><PERSON><PERSON> spirituale", "chromas": false}], "lore": "Cresciuta nella giungla più profonda, Nidale<PERSON> è una grande cacciatrice, in grado di trasformarsi a piacimento in un feroce puma. Non è completamente donna, né completamente animale, e difende il suo territorio da tutti gli intrusi, con trappole piazzate ad arte e abili colpi della sua lancia. Ama ferire le sue prede per poi assalirle nella sua forma felina. I pochi fortunati che sopravvivono raccontano di una donna selvaggia con istinti affilati come rasoi e artigli ancor più pericolosi...", "blurb": "Cresciuta nella giungla più profonda, <PERSON><PERSON><PERSON> è una grande cacciatrice, in grado di trasformarsi a piacimento in un feroce puma. Non è completamente donna, né completamente animale, e difende il suo territorio da tutti gli intrusi, con trappole piazzate...", "allytips": ["Approfitta sempre dell'erba alta! Muoversi continuamente dentro e fuori dall'erba alta aumenta la tua efficacia in battaglia.", "Dai il via agli scontri a squadre con Lancio del giavellotto e poi passa alla forma del puma per in seguire i bersagli cacciati.", "Trappola dannosa infligge danni in base alla salute attuale della vittima. Piazzala vicino alla tua squadra, prima che inizi lo scontro durante gli assedi, per aiutare la tua squadra a danneggiare la prima linea nemica."], "enemytips": ["Il debuff di Caccia di Nidalee si applica solo con Lancio del giavellotto e Trappola dannosa. Evitando di essere colpiti da queste abilità le impedirà di infliggere altri danni.", "Il Lancio del giavellotto di Nidalee infligge più danni in base a quanto va lontano. Se è molto lontana, è imperativo schivarlo.", "Il Colpo fatale di Nidalee infligge molti danni ai bersagli cacciati, ma deve avvicinarsi per utilizzarlo. Conserva i tuoi effetti di controllo o le abilità difensive per quando sta cercando l'uccisione."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 4, "magic": 7, "difficulty": 8}, "stats": {"hp": 610, "hpperlevel": 109, "mp": 295, "mpperlevel": 45, "movespeed": 335, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.45, "attackrange": 525, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.22, "attackspeed": 0.638}, "spells": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON> del g<PERSON>vel<PERSON>to / Colpo fatale", "description": "Nella sua forma umana, <PERSON><PERSON><PERSON> lancia un giavellotto appuntito che infligge danni proporzionalmente alla distanza percorsa. Come puma, il suo prossimo attacco cercherà di ferire fatalmente il proprio bersaglio infliggendo danni in misura inversamente proporzionale alla salute del bersaglio.", "tooltip": "<keywordMajor>Forma umana:</keywordMajor> Ni<PERSON><PERSON> scaglia il suo giavellotto infliggendo <magicDamage>{{ humanminimumdamage }} danni magici</magicDamage>, che possono aumentare fino a <magicDamage>{{ humanmaximumdamage }} danni magici</magicDamage> in base alla distanza percorsa.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi gia<PERSON>", "<PERSON><PERSON> massimi gia<PERSON>", "Costo in mana giavellotto"], "effect": ["{{ spearminimumdamage }} -> {{ spearminimumdamageNL }}", "{{ spearmaximumdamage }} -> {{ spearmaximumdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [70, 90, 110, 130, 150], [227.5, 292.5, 357.5, 422.5, 487.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.4, 0.4, 0.4, 0.4, 0.4], [1.2, 1.2, 1.2, 1.2, 1.2], [525, 525, 525, 525, 525], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/90/110/130/150", "227.5/292.5/357.5/422.5/487.5", "0", "0", "0.4", "1.2", "525", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "JavelinToss.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Bushwhack", "name": "Trappola dannosa / Balzo", "description": "Nella sua forma umana, <PERSON><PERSON><PERSON> piazza una trappola per i suoi sprovveduti avversari che, se attivata, danneggia e rivela il bersaglio. In forma di puma, balza in avanti e infligge una piccola quantità di danni quando atterra.", "tooltip": "<keywordMajor>Forma umana:</keywordMajor> <PERSON><PERSON><PERSON> piazza una trappola invisibile della durata di 2 minuti. Quando un nemico passa sopra di essa, subisce <magicDamage>{{ damagepersecond }} danni magici</magicDamage> al secondo per {{ e3 }} secondi.<br /><br />È possibile avere fino a {{ maxtraps }} trappole attive contemporaneamente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica trappola", "Costo in mana trappola"], "effect": ["{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [13, 12, 11, 10, 9], [10, 20, 30, 40, 50], [120, 120, 120, 120, 120], [40, 80, 120, 160, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "4", "13/12/11/10/9", "10/20/30/40/50", "120", "40/80/120/160/200", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "Bushwhack.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PrimalSurge", "name": "Spirito primitivo / Artiglio", "description": "Nella sua forma umana, <PERSON><PERSON><PERSON> richiama lo spirito del puma per guarire i suoi alleati e infonderli di velocità d'attacco per un breve periodo. Come puma, si scaglia con i suoi artigli in una direzione infliggendo danni ai nemici davanti a lei.", "tooltip": "<keywordMajor>Forma umana:</keywordMajor> <PERSON><PERSON><PERSON> ripristina <healing>{{ totalhealing }} salute</healing> aumentata fino a <healing>{{ maxhealing }}</healing> in base alla salute mancante e fornisce un <attackSpeed>{{ bonusas*100 }}% di velocità d'attacco</attackSpeed> per {{ asduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione Spirito primitivo", "Velocità d'attacco Spirito primitivo", "Costo in mana Spirito primitivo"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ bonusas*100.000000 }}% -> {{ bonusasnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "PrimalSurge.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AspectOfTheCougar", "name": "Aspet<PERSON> del puma", "description": "<PERSON><PERSON><PERSON> si trasforma in un puma, ottenendo nuove abilità.", "tooltip": "<spellPassive>Passiva:</spellPassive> in <keywordMajor>forma umana</keywordMajor>, applicare <keywordMajor>Braccato</keywordMajor> azzera la ricarica di questa abilità.<br /><br /><keywordMajor>Forma umana:</keywordMajor> <PERSON><PERSON><PERSON> assume la sua <keywordMajor>forma di puma</keywordMajor>, che le fornisce attacchi base in corpo a corpo e sostituisce le sue abilità attive.<br /><br /><keywordMajor>Forma di puma:</keywordMajor> <PERSON><PERSON><PERSON> assume la sua <keywordMajor>forma umana</keywordMajor>, che le fornisce attacchi base a distanza e sostituisce le sue abilità attive.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> fatale", "<PERSON><PERSON> fatale aumentati", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tempo di ricarica <PERSON>"], "effect": ["{{ takedownbasedamage }} -> {{ takedownbasedamageNL }}", "{{ takedowndamageamp*100.000000 }}% -> {{ takedowndamageampnl*100.000000 }}%", "{{ pouncedamage }} -> {{ pouncedamageNL }}", "{{ swipedamage }} -> {{ swipedamageNL }}", "{{ pouncecooldown }} -> {{ pouncecooldownNL }}"]}, "maxrank": 4, "cooldown": [3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [6, 6, 6, 6], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "AspectOfTheCougar.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "A caccia", "description": "Muoversi nell'erba alta aumenta la velocità di movimento di Nidalee del 10% per 2 secondi, e del 30% se si dirige verso un campione nemico visibile nell'arco di 1400 unità.<br><br>Colpire campioni o mostri con Lancio del giavellotto o Trappola dannosa attiva una <font color='#FFF673'>Caccia</font>, che dona <font color='#ee91d7'>Visione magica</font> contro di loro per 4 secondi. In questo periodo, Nidalee ottiene il 10% di velocità di movimento (che aumenta a 30% verso i bersagli <font color='#FFF673'>cacciati</font>) e il suo Colpo fatale e Balzo ai loro danni sono potenziati.", "image": {"full": "Nidalee_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}