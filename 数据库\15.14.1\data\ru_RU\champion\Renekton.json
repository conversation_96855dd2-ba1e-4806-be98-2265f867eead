{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renekton": {"id": "Renekton", "key": "58", "name": "Ренектон", "title": "Мясник из песков", "image": {"full": "Renekton.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "58000", "num": 0, "name": "default", "chromas": false}, {"id": "58001", "num": 1, "name": "Галактический Ренектон", "chromas": false}, {"id": "58002", "num": 2, "name": "Ренектон из пустоши", "chromas": false}, {"id": "58003", "num": 3, "name": "Разъяренный Ренектон", "chromas": false}, {"id": "58004", "num": 4, "name": "Ренектон эпохи Рунических войн", "chromas": false}, {"id": "58005", "num": 5, "name": "Ренектон, выжигающий землю", "chromas": false}, {"id": "58006", "num": 6, "name": "Тусовый Ренектон", "chromas": false}, {"id": "58007", "num": 7, "name": "Доисторический Ренектон", "chromas": false}, {"id": "58008", "num": 8, "name": "SKT T1 Ренектон", "chromas": false}, {"id": "58009", "num": 9, "name": "Игрушечный Ренектон", "chromas": true}, {"id": "58017", "num": 17, "name": "Хекстековый Ренектон", "chromas": false}, {"id": "58018", "num": 18, "name": "Ренектон Черная Стужа", "chromas": true}, {"id": "58026", "num": 26, "name": "ПРОЕКТ: Ренектон", "chromas": true}, {"id": "58033", "num": 33, "name": "Предвестник зари Ренектон", "chromas": true}, {"id": "58042", "num": 42, "name": "Ренектон Чемпионата мира 2023", "chromas": true}, {"id": "58048", "num": 48, "name": "Ренектон Чернильная Тень", "chromas": true}], "lore": "Ренектон – ужасное вознесшееся существо из опаленной пустыни Шурима, движимое яростью. Некогда он был самым почитаемым воином империи и вел ее армии к многочисленным победам. Но когда империя пала, Ренектона погребли пески; и пока мир наверху менялся, воин понемногу сходил с ума. Теперь, вновь обретя свободу, он одержим мыслью разыскать и убить своего брата Насуса, которого обезумевший Ренектон винит за свое многовековое заточение во тьме.", "blurb": "Ренектон – ужасное вознесшееся существо из опаленной пустыни Шурима, движимое яростью. Некогда он был самым почитаемым воином империи и вел ее армии к многочисленным победам. Но когда империя пала, Ренектона погребли пески; и пока мир наверху менялся...", "allytips": ["Режь и руби - прекрасный инструмент для нанесения урона в начале матча. Можно прорваться к врагу, использовать другие умения и оторваться от него на безопасное расстояние вторым рывком.", "Смерть слабым вытягивает из врагов огромное количество здоровья в разгар битвы. Это можно использовать для навязывания врагу чувства ложного превосходства.", "Приобретение предметов с сокращением перезарядки является хорошей стратегией при игре за Ренектона, поскольку этот атрибут позволяет быстрее накапливать и тратить ярость."], "enemytips": ["Обращайте внимание на запас ярости Ренектона, так как он позволяет понять, когда тот собирается атаковать.", "Если помешать Ренектону участвовать в борьбе и накапливать ярость, эффективность его умений резко снизится."], "tags": ["Fighter", "Tank"], "partype": "Ярость", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 660, "hpperlevel": 111, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.15, "attackspeedperlevel": 2.75, "attackspeed": 0.665}, "spells": [{"id": "RenektonCleave", "name": "Смерть слабым", "description": "Ренектон рассекает всех врагов вокруг себя, нанося им умеренный физический урон и восстанавливая себе здоровье на долю от этого урона. Если запас ярости Ренектона составляет 50 или более, урон и восстановление здоровья увеличиваются.", "tooltip": "Ренектон совершает круговой удар клинком, нанося врагам <physicalDamage>{{ basicdamage }} физического урона</physicalDamage>, а также восстанавливая себе <healing>{{ nonchamphealing }} здоровья</healing> за каждого пораженного врага, не являющегося чемпионом, и <healing>{{ champhealing }} здоровья</healing> за каждого пораженного чемпиона. Кроме того, он получает <keywordMajor>{{ minionfurygain }} ярости</keywordMajor> за каждого врага, не являющегося чемпионом, и <keywordMajor>{{ championfurygain }} ярости</keywordMajor> за каждого чемпиона.<br /><br /><keywordMajor>Бонус ярости</keywordMajor>: урон увеличивается до <physicalDamage>{{ empdamage }}</physicalDamage>, а лечение – до <healing>{{ empnonchamphealing }}</healing> против врагов, не являющихся чемпионами, и до <healing>{{ empchamphealing }}</healing> против чемпионов. Усиленное умение не дает <keywordMajor>ярости</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Лечение за чемпиона", "Лечение за другие цели", "Максимальное лечение"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ champheal }} -> {{ champhealNL }}", "{{ baseminionheal }} -> {{ baseminionhealNL }}", "{{ basichealcap }} -> {{ basichealcapNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RenektonCleave.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "RenektonPreExecute", "name": "Беспощадный хищник", "description": "При следующей автоатаке Ренектон бьет дважды, нанося умеренный физический урон цели и оглушая ее на 0.75 секунды. Если запас ярости Ренектона составляет 50 или более, он бьет трижды, разрушая щиты от урона на цели, нанося ей большой физический урон и оглушая ее на 1.5 секунды.", "tooltip": "При следующей автоатаке Ренектон бьет дважды, <status>оглушая</status> цель на {{ stunduration }} сек. и нанося ей в общей сложности <physicalDamage>{{ basictotaldamage }} физического урона</physicalDamage>. Поражая чемпиона, Ренектон дополнительно получает <keywordMajor>{{ bonusfuryvschamps }} ярости</keywordMajor>.<br /><br /><keywordMajor>Бонус ярости</keywordMajor>: количество ударов увеличивается до 3. Ренектон уничтожает <shield>щиты</shield>, а затем наносит <physicalDamage>{{ emptotaldamage }} физического урона</physicalDamage> и <status>оглушает</status> цель на {{ enragedstunduration }} сек. Усиленное умение не дает <keywordMajor>ярости</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Усиленный урон", "Перезарядка"], "effect": ["{{ basedamageperhit*2.000000 }} -> {{ basedamageperhitnl*2.000000 }}", "{{ basedamageperhit*3.000000 }} -> {{ basedamageperhitnl*3.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "RenektonPreExecute.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "RenektonSliceAndDice", "name": "Режь и руби", "description": "Ренектон совершает рывок, нанося урон всем врагам на своем пути. Если запас ярости Ренектона составляет 50 или более, он наносит дополнительный урон и уменьшает броню пораженных врагов.", "tooltip": "Ренектон совершает рывок, нанося <physicalDamage>{{ basicdamage }} физического урона</physicalDamage>. При этом он получает <keywordMajor>{{ minionragegeneration }} ярости</keywordMajor> за каждого пораженного врага, не являющегося чемпионом, и <keywordMajor>{{ championragegeneration }} ярости</keywordMajor> за каждого пораженного чемпиона. Если Ренектон поразил хотя бы одного врага, он может один раз <recast>повторно применить</recast> это умение в течение {{ dicetimer }} сек. <br /><br /><keywordMajor>Бонус ярости</keywordMajor>: урон рывка при <recast>повторном применении</recast> увеличивается до <physicalDamage>{{ empdamage }}</physicalDamage>. Кроме того, этот рывок уменьшает <scaleArmor>броню</scaleArmor> врагов на <scaleArmor>{{ enragedarmorshred }}%</scaleArmor> на {{ shredtimer }} сек. Усиленное умение не дает <keywordMajor>ярости</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Усиленный урон", "Уменьшение брони", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ enragedbasedamage }} -> {{ enragedbasedamageNL }}", "{{ enragedarmorshred }}% -> {{ enragedarmorshredNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RenektonSliceAndDice.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "RenektonReignOfTheTyrant", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ренектон принимает обличье великого тирана, увеличивая свой запас здоровья и нанося периодический магический урон всем окружающим врагам. Кроме того, текущий запас ярости Ренектона во время действия умения постоянно увеличивается.", "tooltip": "Ренектон окружает себя темной энергией на {{ buffduration }} сек., увеличивая свой <healing>максимальный запас здоровья на {{ healthgain }}</healing> и получая <keywordMajor>{{ furyoncast }} ярости</keywordMajor>. Пока действует умение, Ренектон каждую секунду наносит врагам <magicDamage>{{ totaldamagepersecond }} магического урона</magicDamage> и получает <keywordMajor>{{ furypersecond }} ярости</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Дополнительное здоровье", "Урон в секунду", "Перезарядка"], "effect": ["{{ healthgain }} -> {{ healthgainNL }}", "{{ auradamagepersecond }} -> {{ auradamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "RenektonReignOfTheTyrant.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Всепоглощающий гнев", "description": "При каждой автоатаке Ренектон увеличивает запас ярости, которая способна усиливать его умения. Кроме того, Ренектон получает дополнительную ярость, когда у него мало здоровья.", "image": {"full": "Renekton_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}