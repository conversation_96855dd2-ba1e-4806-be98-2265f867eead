{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yorick": {"id": "<PERSON><PERSON>", "key": "83", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Yorick.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "83000", "num": 0, "name": "default", "chromas": false}, {"id": "83001", "num": 1, "name": "<PERSON><PERSON>biorca Pogrzebowy", "chromas": false}, {"id": "83002", "num": 2, "name": "<PERSON><PERSON>kill", "chromas": false}, {"id": "83003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "83004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "83012", "num": 12, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "83021", "num": 21, "name": "<PERSON><PERSON> z <PERSON>takill III: Lost Chapter", "chromas": true}, {"id": "83030", "num": 30, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "83040", "num": 40, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "83050", "num": 50, "name": "<PERSON><PERSON> w Samo Południe", "chromas": true}], "lore": "<PERSON><PERSON>, ostatni ocalały z dawno zapomnianego zakonu religijnego, jest zar<PERSON><PERSON> błogosławiony, jak i przeklęty mocą władania nad nieumarłymi. Tkwi uwięziony na Wyspach Cienia, a jego jedynymi towarzyszami są gnijące zwłoki i wyjące upiory, które gromadzi u swego boku. Potworne działania Yoricka skrywają jednak szlachetny cel — chce uwolnić swój dom od klątwy Zrujnowania.", "blurb": "<PERSON><PERSON>, ostatni ocalały z dawno zapomnianego zakonu religijnego, jest zar<PERSON><PERSON> błogosławiony, jak i przeklęty mocą władania nad nieumarłymi. Tkwi uwięziony na Wyspach Cienia, a jego jedynymi towarzyszami są gnijące zwłoki i wyjące upiory, które gromadzi...", "allytips": ["<PERSON>e musisz korzystać z Przebudzenia, aby ponownie móc użyć Ostatniego Rytuału.", "Dziewica będzie starała się pomóc ci w walce, więc rozważnie wybieraj swoje cele.", "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>wi<PERSON> do alei samą, ale zachowaj ostrożność — twoja siła w walce w dużej mierze zależy właśnie od niej."], "enemytips": ["Na Istotach Mgły i Mglistej Dziewicy możesz użyć Porażenia, aby zadać im obrażenia lub je zabić.", "Postaraj się przerzedzić stwory Yoricka zanim podejmiesz z nim walkę. Podstawowy atak lub nieobszarowy czar zlikwi<PERSON><PERSON> Istotę Mgły.", "Zaataku<PERSON> <PERSON><PERSON>, aby ją z<PERSON>."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 36, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON><PERSON>", "name": "Ostatni Rytuał", "description": "Przy następnym ataku Yorick zada dodatkowe obrażenia i uleczy się. <PERSON><PERSON><PERSON> celem jest bohater lub duży potwór albo cel zginie, wykopany zostanie grób.", "tooltip": "Następny atak Yoricka zadaje dodatkowo <physicalDamage>{{ bonusdamage }} pkt. obrażeń fizycznych</physicalDamage> i przywraca <healing>{{ qheal }} pkt. zdrowia plus {{ missinghealthratio }}% jego brakującego zdrowia</healing>. Wartość leczenia jest zmniejszona o {{ healreduction }}% przeciwko jednostkom niebędącym bohaterami. Jeśli ten atak trafi bohatera lub dużego potwora albo zabije cel, pozostawia po sobie grób.<br /><br />Gdy w pobliżu znajdują się co najmniej 3 groby, a ta umiejętność została użyta, <PERSON><PERSON> może <recast>ponownie jej u<PERSON></recast>, by prz<PERSON><PERSON><PERSON><PERSON> <keywordMajor>Is<PERSON>ty Mgły</keywordMajor> ze wszystkich pobliskich grobów.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dodatkowe obrażenia", "Leczenie", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ missinghealthratio }}% -> {{ missinghealthratioNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YorickQ.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "YorickW", "name": "Mroczna Procesja", "description": "<PERSON><PERSON> p<PERSON>wołuje zniszczalną ścianę wokoło wybranego obszaru, która uniemożliwi wrogom opuszczenie go.", "tooltip": "<PERSON><PERSON>, b<PERSON><PERSON><PERSON><PERSON><PERSON> drogę wrogom, ale nie sojusznikom. <PERSON><PERSON>a ma <healing>{{ wallhealthtooltip }} pkt. zdrowia</healing> i znika po {{ circleduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Zdrowie"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wallhealthtooltip }} -> {{ wallhealthtooltipNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "YorickW.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "<PERSON><PERSON>E", "name": "Żałobna Mgła", "description": "<PERSON>rick ciska kulą Czarnej <PERSON>, która zmniejsza pancerz i obrażenia, spowalnia oraz oznacza wrogów. Przywołane jednostki zyskują prędkość ruchu podczas poruszania się w kierunku oznaczonych celów. ", "tooltip": "Yorick ciska kulą <PERSON>zar<PERSON>j <PERSON>ł<PERSON>, która zadaje obrażenia magiczne równe <magicDamage>{{ calc_healthdamage }} maksymalnego zdrowia</magicDamage>, <status>spowalnia</status> o {{ calc_slow }} na {{ slowduration }} sek. i oznacza bohaterów oraz potwory na {{ markduration }} sek. Oznaczeni wrogowie stale <spellName>przebudzają</spellName> pobliskie groby (do maks. {{ spell.yorickpassive:yorickpassiveghoulmax }}), a ich pancerz zmniejsza się o <scaleArmor>{{ armorshred*100 }}%</scaleArmor>.<br /><br />Yorick i jego przyzwane jednostki zyskują <speed>{{ hasteamount*100 }}% prędkości ruchu</speed>, gdy poruszają się w kierunku znaku. Gdy oznaczeni wrogowie odejdą wystarczaj<PERSON>co dale<PERSON>, <keywordMajor>Istoty Mgły</keywordMajor> raz do nich doskoczą.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Pro<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Czas odnowienia", "Koszt (@AbilityResourceName@)", "Obrażenia od maksymalnego zdrowia"], "effect": ["{{ armorshred*100.000000 }}% -> {{ armorshrednl*100.000000 }}%", "{{ hasteamount*100.000000 }}% -> {{ hasteamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YorickE.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "Yo<PERSON>R", "name": "Chluba <PERSON>", "description": "<PERSON><PERSON> przyzywa Mglistą Dziewicę. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jej cel, <PERSON><PERSON> zada<PERSON> dodatkowe obrażenia. Dziewica automatycznie wskrzesza również Istoty Mgły z martwych wrogów.", "tooltip": "<PERSON><PERSON> p<PERSON> <keywordMajor>M<PERSON><PERSON><PERSON></keywordMajor> z <healing>{{ yorickbigghoulhealth }} pkt. zdrowia</healing> i <magicDamage>{{ yorickbigghouldamage }} pkt. obrażeń magicznych</magicDamage> oraz następującą liczbę <keywordMajor>Istot <PERSON>ł<PERSON></keywordMajor>: {{ rghoulnumbers }}. <keywordMajor>Dziewica</keywordMajor> automatycznie wskrzesza <keywordMajor>Istoty Mgły</keywordMajor> z ginących w pobliżu wrogów i oznacza wrogich bohaterów swoimi atakami. Ki<PERSON><PERSON> Yo<PERSON> atakuje cel <keywordMajor>Dziewicy</keywordMajor>, zadaje mu <magicDamage>obrażenia magiczne równe {{ rmarkdamagepercent }}% jego maks. zdrowia</magicDamage>.<br /><br />Po 10 sek. <PERSON><PERSON> mo<PERSON>e <recast>p<PERSON><PERSON><PERSON></recast> tej umie<PERSON>, by <PERSON><PERSON><PERSON><PERSON> <keywordMajor><PERSON><PERSON><PERSON><PERSON><PERSON></keywordMajor>, wysyłając ją wzdłuż najbliższej alei.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od ataku", "Obrażenia znaku", "<PERSON><PERSON><PERSON>", "Czas odnowienia"], "effect": ["{{ rbigghoulbonusad }} -> {{ rbigghoulbonusadNL }}", "{{ rmarkdamagepercent }}% -> {{ rmarkdamagepercentNL }}%", "{{ rghoulnumbers }} -> {{ rghoulnumbersNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 130, 100], "cooldownBurn": "160/130/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "YorickR.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "<font color='#FF9900'>Przek<PERSON><PERSON><PERSON>:</font> <PERSON><PERSON> prz<PERSON><PERSON>wa Istoty Mgły, które osaczają i atakują pobliskich wrogów.", "image": {"full": "Yo<PERSON>_P.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}