{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "Fizz", "title": "der Gezeitentäuscher", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "Atlantischer Fizz", "chromas": false}, {"id": "105002", "num": 2, "name": "Tundra-Fizz", "chromas": false}, {"id": "105003", "num": 3, "name": "Fischers Fizz", "chromas": false}, {"id": "105004", "num": 4, "name": "<PERSON>ren-Fizz", "chromas": false}, {"id": "105008", "num": 8, "name": "Plüschhäschen-Fizz", "chromas": false}, {"id": "105009", "num": 9, "name": "Supergalaktischer Fizz", "chromas": false}, {"id": "105010", "num": 10, "name": "Omegatrupp-Fizz", "chromas": true}, {"id": "105014", "num": 14, "name": "Flauschiger Fizz", "chromas": false}, {"id": "105015", "num": 15, "name": "Flauschiger Fizz (Prestige)", "chromas": false}, {"id": "105016", "num": 16, "name": "Teufelchen-Fizz", "chromas": true}, {"id": "105025", "num": 25, "name": "Flauschiger Fizz (Prestige 2022)", "chromas": false}, {"id": "105026", "num": 26, "name": "Astronauten-Fizz", "chromas": true}, {"id": "105035", "num": 35, "name": "Regenhirte Fizz", "chromas": true}], "lore": "Fizz ist ein amphibischer Yordle, der sich gern in den Riffen um Bilgewasser aufhält. Er holt sich oft die Zehntabgaben aus dem Meer, die von abergläubischen Kapitänen über Bord geworfen wurden, und händigt sie ihren Besitzern wieder aus. Doch trotz dieser Freundlichkeit gehen ihm auch die großspurigsten Seemänner meist lieber aus dem Weg, denn die Geschichten, die man sich über ihn erzählt, drehen sich vor allem um die vielen Seelen, die diese schwer zu fassende Kreatur unterschätzt haben. Er wird oft mit einem launischen Meeresgeist verwechselt, befehligt riesige Bestien aus der Tiefe und liebt es, Verbündete wie Feinde gleichermaßen an der Nase herumzuführen.", "blurb": "Fizz ist ein amphibischer Yordle, der sich gern in den Riffen um Bilgewasser aufhält. Er holt sich oft die Zehntabgaben aus dem Meer, die von abergläubischen Kapitänen über Bord geworfen wurden, und händigt sie ihren Besitzern wieder aus. Doch trotz...", "allytips": ["Da sich Fizz durch Einheiten bewegen kann, solltest du in deiner Lane nach Gelegenheiten suchen, dich durch Vasallen hindurchzubewegen und das Passiv von „Seestein-Dreizack“ anzuwenden&nbsp;– ein paar Sekunden später kannst du dann mit dem aktiven Angriff der Fähigkeit nachsetzen.", "Fizz' ultimative Fähigkeit, „Köder auswerfen“, kann direkt auf einen Gegner oder dorthin gezielt werden, wo du ihn in Kürze vermutest.", "Fizz skaliert vor allem mit Fähigkeitsstärke&nbsp;– setzt das gegnerische Team auf Schadensspitzen, solltest du dir Gegenstände wie Zhonyas Stundenglas oder Schleier der Todesfee zulegen. Gegenstände wie Fluch des Lichs oder Rabadons Todeshaube sind ebenfalls gut für Fizz, wenn du glaubst, auf das zusätzliche Leben verzichten zu können."], "enemytips": ["Nachdem Fizz seinen verstärkten Angriff eingesetzt hat, werden seine Angriffe ein paar Sekunden lang tödlicher – halte dich fern, solange sein Dreizack leuchtet!", "Fizz kann sehr schwer zu fassen sein, wenn seine Fähigkeiten nicht abklingen – verleite ihn dazu, sie zu früh zu benutzen, und setze dann mit Massenkontrolle oder schweren Angriffen nach!"], "tags": ["Assassin", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Fizz springt durch sein <PERSON>, wobei er magischen Schaden verursacht und Treffereffekte auslöst.", "tooltip": "Fizz springt durch einen Gegner und fügt ihm <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> sowie <magicDamage>{{ qdamage }}&nbsp;magischen <PERSON>en</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzW", "name": "Seestein-Dreizack", "description": "Fizz' Ang<PERSON>e lassen seine G<PERSON>ner bluten, wodurch sie im Laufe von einigen Sekunden zusätzlichen magischen Schaden erleiden. Fizz kann seinen nächsten Angriff verstärken, so dass er zusätzlichen Schaden anrichtet und seine weiteren Angriffe für kurze Zeit stärker werden.", "tooltip": "<spellPassive>Passiv</spellPassive>: Fizz' Angriffe fügen seinen Gegnern Blutungen zu, wodurch sie im Verlauf von {{ bleedduration }}&nbsp;Sekunden <magicDamage>{{ dotdamage }}&nbsp;magischen Schaden</magicDamage> erleiden. <br /><br /><spellActive>Aktiv</spellActive>: Fizz' nächster Angriff verursacht zusätzlich <magicDamage>{{ activedamage }}&nbsp;magischen Schaden</magicDamage>. Wen<PERSON> dieser Angriff sein Ziel tötet, wird Fizz <scaleMana>{{ onkillmanarefund }}&nbsp;Mana</scaleMana> zurückerstattet und die Abklingzeit dieser Fähigkeit wird auf {{ onkillnewcooldown }}&nbsp;Sekunde verringert. Wenn es nicht getötet wird, verursachen Fizz' Angriffe {{ onhitbuffduration }}&nbsp;Sekunden lang zusätzlich <magicDamage>{{ onhitbuffdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Sc<PERSON>en (Passiv)", "Schaden (Aktiv)", "Trefferschaden", "Manarückerstattung", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ dotbasedamage }} -> {{ dotbasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }} -> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }} -> {{ onkillmanarefundNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzE", "name": "Verspielt / Täuscher", "description": "Fizz hüpft in die Luft, landet geschickt auf seinem Speer und kann nicht anvisiert werden. Aus dieser Position heraus kann Fizz schwungvoll auf den Boden krachen oder noch ein weiteres Mal springen.", "tooltip": "Fizz springt auf seinen Dreizack und kann 0,75&nbsp;Sekunden lang nicht anvisiert werden. Danach fügt er nahen Gegnern <magicDamage>{{ edamage }}&nbsp;magischen Schaden</magicDamage> zu und <status>verlangsamt</status> sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%. <br /><br />Während er nicht anvisiert werden kann, kann Fizz diese Fähigkeit <recast>reaktivieren</recast> und erneut springen. Dadurch beendet er den Effekt frühzeitig, verursacht Schaden in einem kleineren Bereich und <status>verlangsamt</status> Geg<PERSON> nicht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzR", "name": "Köder auswerfen", "description": "Fizz wirft einen Fisch in eine Richtung, der sich bei Berührung an einen Champion heftet und diesen verlangsamt. Nach kurzer Verzögerung bricht ein Hai aus dem Boden, der das Ziel hoch- und weitere Gegner in der Nähe zur Seite schleudert. Getroffene Gegner erleiden magischen Schaden und werden verlangsamt.", "tooltip": "Fizz wirft einen <PERSON>sch, der am ersten getroffenen Champion hängen bleibt. Das Opfer ist von <keywordStealth>absoluter Sicht</keywordStealth> betroffen und wird um 40&nbsp;% bis 80&nbsp;% <status>verlangsamt</status> (abhäng<PERSON> von der Entfernung, die der Fisch vor der Landung zurückgelegt hat). <br /><br />Nach {{ detonationtime }}&nbsp;Sekunden greift ein Hai das Ziel an, wodurch das Ziel mit dem Fisch 1&nbsp;Sekunde lang <status>hochgeschleudert</status> und alles andere <status>zurückgestoßen</status> wird. Abhängig von der zurückgelegten Entfernung des Fisches wird dabei zwischen <magicDamage>{{ smallsharkdamage }} und {{ bigsharkdamage }}&nbsp;magischen Schaden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Schaden des kleinen Hais", "Schaden des mittelgroßen Hais", "Schaden des großen Hais"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ smalldamage }} -> {{ smalldamageNL }}", "{{ middamage }} -> {{ middamageNL }}", "{{ bigdamage }} -> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Geschickter Kämpfer", "description": "Fizz kann sich durch Einheiten bewegen und erleidet verminderten Schaden in festgelegter Höhe aus allen Quellen.", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}