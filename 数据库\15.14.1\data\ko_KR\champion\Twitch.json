{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Twitch": {"id": "Twitch", "key": "29", "name": "트위치", "title": "역병 쥐", "image": {"full": "Twitch.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "29000", "num": 0, "name": "default", "chromas": false}, {"id": "29001", "num": 1, "name": "형님 트위치", "chromas": false}, {"id": "29002", "num": 2, "name": "평창 트위치", "chromas": false}, {"id": "29003", "num": 3, "name": "중세 트위치", "chromas": true}, {"id": "29004", "num": 4, "name": "범죄 도시 트위치", "chromas": false}, {"id": "29005", "num": 5, "name": "파괴단 트위치", "chromas": false}, {"id": "29006", "num": 6, "name": "소매치기 트위치", "chromas": false}, {"id": "29007", "num": 7, "name": "삼성 화이트 트위치", "chromas": false}, {"id": "29008", "num": 8, "name": "오메가 분대 트위치", "chromas": true}, {"id": "29012", "num": 12, "name": "빙하의 제왕 트위치", "chromas": true}, {"id": "29027", "num": 27, "name": "그림자 도적 트위치", "chromas": false}, {"id": "29036", "num": 36, "name": "용 사냥꾼 트위치", "chromas": false}, {"id": "29045", "num": 45, "name": "하이 눈 트위치", "chromas": false}, {"id": "29055", "num": 55, "name": "치즈 대장 트위치", "chromas": false}, {"id": "29064", "num": 64, "name": "수영장 파티 트위치", "chromas": false}], "lore": "자운에서 전염병을 옮기는 쥐로 태어난 트위치는 열정적인 오물 전문가로, 앞발을 더럽히는 것을 두려워하지 않는다. 트위치는 필트오버의 부유층을 향해 화학물질로 강화한 석궁을 겨누며, 지상의 인간들의 더러움을 적나라하게 드러내기로 다짐했다. 지하동굴에 웅크리고 있을 때를 제외하면 늘 비밀스럽게 교활한 움직임을 일삼는 트위치는 버려진 보물을 찾아 쓰레기 더미를 뒤지고 있다. 곰팡이 슨 샌드위치라도 찾을 수 있을까 하며.", "blurb": "자운에서 전염병을 옮기는 쥐로 태어난 트위치는 열정적인 오물 전문가로, 앞발을 더럽히는 것을 두려워하지 않는다. 트위치는 필트오버의 부유층을 향해 화학물질로 강화한 석궁을 겨누며, 지상의 인간들의 더러움을 적나라하게 드러내기로 다짐했다. 지하동굴에 웅크리고 있을 때를 제외하면 늘 비밀스럽게 교활한 움직임을 일삼는 트위치는 버려진 보물을 찾아 쓰레기 더미를 뒤지고 있다. 곰팡이 슨 샌드위치라도 찾을 수 있을까 하며.", "allytips": ["트위치는 공격 속도가 챔피언 중 가장 높은 편입니다. 칠흑의 양날 도끼 또는 마법사의 최후처럼 적중 시 발동 효과가 있는 아이템을 구입하여 효과를 극대화하십시오.", "오염 스킬은 사거리가 긴 편입니다. 적에게 맹독을 최대한 많이 맞춘 후 사용하십시오.", "공격 사거리 밖으로 나간 적은 독약 병을 사용하여 따라잡을 수 있습니다."], "enemytips": ["트위치는 체력이 매우 낮습니다. 위장 상태의 트위치를 찾아내면 팀원들과 힘을 합쳐 빨리 처치하십시오.", "마법 보호막은 트위치의 맹독 스킬이 가지는 피해 자체를 막을 수는 없으나 기타 다른 효과는 막아낼 수 있습니다.", "트위치가 공격로에서 사라진 것 같으면 동료들에게 트위치가 행방불명(미아)되었다는 것을 꼭 알리십시오."], "tags": ["Marksman", "Assassin"], "partype": "마나", "info": {"attack": 9, "defense": 2, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 7.25, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.1, "attackspeedperlevel": 3.38, "attackspeed": 0.679}, "spells": [{"id": "TwitchHideInShadows", "name": "매복", "description": "트위치의 이동 속도가 빨라지고 짧은 시간 동안 위장 상태가 됩니다. 위장 효과가 끝나면 잠시 공격 속도가 증가합니다.<br><br>맹독에 걸린 적 챔피언이 죽으면 매복의 재사용 대기시간이 초기화됩니다.", "tooltip": "트위치가 <keywordStealth>위장</keywordStealth> 상태에 돌입해 {{ e2 }}초 동안 <speed>이동 속도가 {{ e3 }}%</speed> 증가합니다. 트위치를 볼 수 없는 적 챔피언 근처에서는 이동 속도가 {{ e3 }}%까지 증가합니다. <keywordStealth>위장</keywordStealth>이 끝나면 {{ e6 }}초 동안 트위치의 <attackSpeed>공격 속도가 {{ e1 }}%</attackSpeed> 증가합니다.<br /><br /><keywordMajor>독</keywordMajor>에 중독된 적 챔피언이 죽으면 이 스킬의 재사용 대기시간이 초기화됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["위장 지속시간", "공격 속도"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}% -> {{ e1NL }}%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [45, 50, 55, 60, 65], [10, 11, 12, 13, 14], [30, 30, 30, 30, 30], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [500, 500, 500, 500, 500], [1000, 1000, 1000, 1000, 1000], [30, 30, 30, 30, 30]], "effectBurn": [null, "45/50/55/60/65", "10/11/12/13/14", "30", "1", "1", "6", "3", "500", "1000", "30"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TwitchHideInShadows.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TwitchVenomCask", "name": "독약 병", "description": "트위치가 명중하면 폭발하는 독약 병을 던져, 범위 내 대상들에게 둔화 효과를 부여하고 맹독을 적용합니다.", "tooltip": "트위치가 독약 병을 던져 맞힌 모든 적에게 <spellName>맹독</spellName>을 중첩시키고 {{ duration }}초 동안 지속되는 독구름을 남깁니다.<br /><br />독구름 안의 적은 이동 속도가 {{ totalslowamount }}% <status>감소</status>하고 매초 <spellName>맹독</spellName> 중첩이 쌓입니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동속도 감소량", "재사용 대기시간"], "effect": ["{{ baseslowamount }}% -> {{ baseslowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "TwitchVenomCask.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TwitchExpunge", "name": "오염", "description": "트위치가 역병을 퍼뜨려 중독된 적들에게 더 큰 피해를 줍니다.", "tooltip": "<spellName>맹독</spellName>에 감염된 주위 적 모두에게 <physicalDamage>{{ basedamage }}의 물리 피해</physicalDamage>를 입히고 추가적으로 중첩된 <spellName>맹독</spellName> 하나당 <physicalDamage>{{ physicaldamageperstack }}의 물리 피해</physicalDamage>와 <magicDamage>{{ magicdamageperstack }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />최대 피해량: <physicalDamage>{{ maxphysicaldamage }}의 물리 피해</physicalDamage>와 <magicDamage>{{ maxmagicdamage }}의 마법 피해</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "중첩 당 피해량", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basephysicaldamageperstack }} -> {{ basephysicaldamageperstackNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchExpunge.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TwitchFullAutomatic", "name": "무차별 난사", "description": "트위치가 석궁의 위력을 최대한으로 끌어올려 멀리로 관통 화살들을 발사하며, 경로상의 모든 적을 꿰뚫습니다.", "tooltip": "트위치가 석궁을 꺼내 {{ duration }}초 동안 공격 사거리가 {{ bonusrange }}, <scaleAD>공격력이 {{ bonusad }}</scaleAD> 증가하며 기본 공격은 적을 관통합니다. 이 공격은 통과하는 모든 적에게 적중하지만 한 번 관통할 때마다 피해량이 {{ falloffdamage*100 }}%씩 감소됩니다. 피해량은 최소 {{ minimumfalloffdamage*100 }}%까지 내려갑니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["공격력"], "effect": ["{{ bonusad }} -> {{ bonusadNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchFullAutomatic.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "맹독", "description": "트위치의 기본 공격은 대상을 중독시켜 초당 고정 피해를 입힙니다.", "image": {"full": "Twitch_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}