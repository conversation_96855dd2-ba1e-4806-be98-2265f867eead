{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gnar": {"id": "<PERSON><PERSON>", "key": "150", "name": "<PERSON><PERSON><PERSON>", "title": "Недостающее звено", "image": {"full": "Gnar.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "150000", "num": 0, "name": "default", "chromas": false}, {"id": "150001", "num": 1, "name": "Гнарозавр", "chromas": true}, {"id": "150002", "num": 2, "name": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Гнар", "chromas": false}, {"id": "150003", "num": 3, "name": "Снежный Гнар", "chromas": false}, {"id": "150004", "num": 4, "name": "<PERSON><PERSON>ь Леон Гнар", "chromas": false}, {"id": "150013", "num": 13, "name": "Супергалактический Гнар", "chromas": false}, {"id": "150014", "num": 14, "name": "SSG Гнар", "chromas": false}, {"id": "150015", "num": 15, "name": "Астр<PERSON>нав<PERSON> Г<PERSON>р", "chromas": true}, {"id": "150022", "num": 22, "name": "Дух леса Г<PERSON>р", "chromas": true}, {"id": "150031", "num": 31, "name": "Потусторонний Гнар", "chromas": true}], "lore": "Гнар – первобытный йордл, чье веселое выражение мордочки может в мгновение ока смениться гримасой негодования, превратив его в огромного монстра с тягой к разрушениям. Это любопытное создание тысячи лет было сковано Истинным льдом и теперь, освободившись, попало в изменившийся мир, который кажется ему экзотическим и дивным. Гнару нравится опасность, и он бросает в своих врагов все, что попадется ему под руку, – будь то костяной бумеранг или ближайшее здание.", "blurb": "Гнар – первобытный йордл, чье веселое выражение мордочки может в мгновение ока смениться гримасой негодования, превратив его в огромного монстра с тягой к разрушениям. Это любопытное создание тысячи лет было сковано Истинным льдом и теперь...", "allytips": ["Очень важно научиться управлять своей яростью. Старайтесь рассчитывать время своих трансформаций таким образом, чтобы максимально эффективно использовать умения обеих форм.", "Занимайте позицию рядом с препятствиями, чтобы заманить врагов и оглушить их при помощи своего абсолютного умения.", "Изучите свои сильные стороны! Мини-Гнар - быстрый, хрупкий и способен наносить высокий урон на постоянной основе. Мега-Гнар - медленный, сильный и может нанести огромное количество урона за короткое время."], "enemytips": ["Гнар не может накапливать ярость в течение 15 секунд после превращения из Мега-Гнара в Мини-Гнара. Воспользуйтесь этим промежутком, чтобы организовать нападение на его команду.", "Анимация Гнара и его шкала ярости меняются незадолго до трансформации.", "Абсолютное умение Гнара отбрасывает вас и оглушает, если вы врежетесь в препятствие. Будьте осторожны, сражаясь с Гнаром возле одного из них."], "tags": ["Fighter", "Tank"], "partype": "Ярость", "info": {"attack": 6, "defense": 5, "magic": 5, "difficulty": 8}, "stats": {"hp": 540, "hpperlevel": 79, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 32, "armorperlevel": 3.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 175, "hpregen": 4.5, "hpregenperlevel": 1.25, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.2, "attackspeedperlevel": 6, "attackspeed": 0.625}, "spells": [{"id": "GnarQ", "name": "Бросок бумеранга / Бросок валуна", "description": "Гнар бросает бумеранг, который наносит урон и замедляет врагов, прежде чем вернуться к Гнару. Если Гнар ловит бумеранг, время перезарядки сокращается.<br><br>Мега-Гнар швыряет огромный валун, который летит, пока не столкнется с противником, нанося урон ему и всем врагам вокруг и уменьшив их скорость передвижения. Подбирание валуна сокращает время перезарядки.", "tooltip": "<keywordMajor>Мини-Гнар:</keywordMajor> Гнар бросает бумеранг, который наносит <physicalDamage>{{ spell.gnarq:minitotaldamage }} физического урона</physicalDamage> и <status>замедляет</status> на {{ spell.gnarq:slowamount*100 }}% на {{ spell.gnarq:slowduration }} сек. После поражения врага бумеранг возвращается, нанося меньше урона последующим целям. Бумеранг может поразить одну и ту же цель только один раз. Если Гнар ловит бумеранг, его перезарядка сокращается на {{ spell.gnarq:minicdrefund*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон от бумеранга", "Урон от валуна", "Замедление", "Замедление от валуна", "Перезарядка"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ megabasedamage }} -> {{ megabasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ megaslowamount*100.000000 }}% -> {{ megaslowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 17.5, 15, 12.5, 10], "cooldownBurn": "20/17.5/15/12.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "GnarQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "GnarW", "name": "Торопыжка / Бум", "description": "Автоатаки Гнара против одной и той же цели наносят дополнительный урон и увеличивают его скорость передвижения.<br><br>Мега-Гнар слишком большой, чтобы быть Торопыжкой, вместо этого он становится на задние лапы и бьет кулаками по земле, нанося урон всем врагам перед собой и оглушая их на короткое время.", "tooltip": "<keywordMajor>Мини-Гнар – пассивно:</keywordMajor> каждые третьи автоатака или умение против одного и того же врага дополнительно наносят <magicDamage>магический урон в размере {{ spell.gnarw:minitotaldamage }} плюс {{ spell.gnarw:minipercenthpdamage*100 }}% от максимального запаса здоровья</magicDamage>. Гнар при этом увеличивает свою <speed>скорость передвижения на {{ spell.gnarr:rhypermovementspeedpercent }}%</speed> (уменьшается в течение {{ minihasteduration }} сек.).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон Торопыжки", "Коэффициент урона Торопыжки от запаса здоровья", "Урон Бума"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ minipercenthpdamage*100.000000 }}% -> {{ minipercenthpdamagenl*100.000000 }}%", "{{ megabasedamage }} -> {{ megabasedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GnarW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "GnarE", "name": "Прыг / Хрясь", "description": "Гнар прыгает в выбранном направлении, увеличивая свою скорость атаки. Если он приземляется на любого бойца, он совершает еще один прыжок в том же направлении. Если этот боец вражеский, Гнар наносит ему урон и замедляет его.<br><br>Мега-Гнар слишком большой для второго прыжка - прыгнув один раз, он обрушивается на землю с ужасающей силой, нанося урон всем врагам вокруг.", "tooltip": "<keywordMajor>Мини-Гнар:</keywordMajor> Гнар совершает прыжок, увеличивая свою <attackSpeed>скорость атаки на {{ spell.gnare:minibas*100 }}%</attackSpeed> на {{ spell.gnare:miniasduration }} сек. При приземлении на любого бойца Гнар совершает еще один прыжок в том же направлении. Если этот боец вражеский, Гнар наносит ему <physicalDamage>{{ spell.gnare:minitotaldamage }} физического урона</physicalDamage> и ненадолго <status>замедляет</status> на {{ spell.gnare:movespeedmod*-100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон умения Прыг", "Урон умения Хрясь", "Доп. скорость атаки", "Перезарядка"], "effect": ["{{ minidamage }} -> {{ minidamageNL }}", "{{ megadamage }} -> {{ megadamageNL }}", "{{ minibas*100.000000 }}% -> {{ minibasnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "GnarE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "GnarR", "name": "ГНА-А-А-Р!", "description": "Гнар хватает всех врагов вокруг себя и бросает их в выбранном направлении, нанося им урон и замедляя их. Если враг врезается в препятствие, он не замедляется, а оглушается, а наносимый ему урон увеличивается.", "tooltip": "<keywordMajor>Мини-Гнар - пассивно:</keywordMajor> дополнительная <speed>скорость передвижения</speed> от <spellName>Торопыжки</spellName> увеличена.<br /><br /><keywordMajor>Мега-Гнар:</keywordMajor> Гнар толкает врагов поблизости, нанося им <physicalDamage>{{ damage }} физического урона</physicalDamage>, а также <status>отбрасывая</status> и <status>замедляя</status> на {{ rslowpercent }}% на {{ rccduration }} сек. Если враги врезаются в стену, они вместо этого получают <physicalDamage>{{ walldamage }} физического урона</physicalDamage> и <status>оглушаются</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Продолжительность замедления/оглушения", "Скорость передвижения от Торопыжки", "Перезарядка"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rccduration }} -> {{ rccdurationNL }}", "{{ rhypermovementspeedpercent }}% -> {{ rhypermovementspeedpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [90, 60, 30], "cooldownBurn": "90/60/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [590, 590, 590], "rangeBurn": "590", "image": {"full": "GnarR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Ген ярости", "description": "Гнар накапливает ярость в бою. Когда шкала ярости заполняется, следующее использованное умение превращает Гнара в Мега-Гнара, увеличивая его выживаемость и изменяя доступный ему набор умений.", "image": {"full": "Gnar_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}