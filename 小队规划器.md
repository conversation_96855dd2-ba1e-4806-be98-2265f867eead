
# TFT Team Planner Codes
This gist describes how team planner codes are generated. This allows you to create your own team comps for pasting in the client without needing to use the client.

The code is a hexadecimal representation of champions. Here's a breakdown of the bits in the code.
Example code 010102030405060708090ATFTSet13

|01|01|02|03|04|05|06|07|08|09|0A|TFTSet13|
|--|--|--|--|--|--|--|--|--|--|--|--|
|Always starts with 01|Champion 1|Champion 2|Champion 3|Champion 4|Champion 5|Champion 6|Champion 7|Champion 8|Champion 9|Champion 10|TFT Set ID|

As you can see the codes will always start with 01 and end with the TFT set identifier.

## Getting Champion IDs
Champion IDs are pulled from lol-game-data. You can use Community Dragon to get this JSON. [https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/tftchampions-teamplanner.json](https://raw.communitydragon.org/latest/plugins/rcp-be-lol-game-data/global/default/v1/tftchampions-teamplanner.json) (latest can be replaced with a patch number (14.23 or pbe))

Champion IDs are found by sorting the set by ``character_id`` alphabetically and then taking the 2 digit hexadecimal representation of champion's order in the list, with the first champion being 1 (01).

Slots that are blank are represented by 00. Blanks can be inserted in any champion slot but will be pushed to the end.

TFT Set IDs that can be imported also can be found as keys in that JSON.

## Example Champion List
As of writing this we are on set 13 (TFTSet13)
If you have properly sorted your champion list it should look like this
|Hex|Champion ID|Name|
|--|--|--|
|01|TFT13_Akali|Akali|
|02|TFT13_Ambessa|Ambessa|
|03|TFT13_Amumu|Amumu|
|04|TFT13_Beardy|Loris|
|05|TFT13_Blitzcrank|Blitzcrank|
|06|TFT13_Blue|Powder|
|07|TFT13_Caitlyn|Caitlyn|
|08|TFT13_Camille|Camille|
|09|TFT13_Cassiopeia|Cassiopeia|
|0A|TFT13_Chainsaw|Renni|


## Questions
If you have further questions join the [Riot Games Third Party Developer Discord](https://discord.com/invite/riotgamesdevrel) and ask in the #tft-dev channel.