{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Великий мастер оружия", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "Могуч<PERSON> Д<PERSON>с", "chromas": false}, {"id": "24002", "num": 2, "name": "Ван<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "24003", "num": 3, "name": "Рыболов Джакс", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX Джакс", "chromas": false}, {"id": "24005", "num": 5, "name": "Джаксимус", "chromas": false}, {"id": "24006", "num": 6, "name": "Храмовник Джакс", "chromas": false}, {"id": "24007", "num": 7, "name": "Непобедимый Джакс", "chromas": true}, {"id": "24008", "num": 8, "name": "SKT T1 Джакс", "chromas": false}, {"id": "24012", "num": 12, "name": "Хра<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Джакс", "chromas": false}, {"id": "24013", "num": 13, "name": "Посох бога Джакс", "chromas": false}, {"id": "24014", "num": 14, "name": "Джакс из Механических царств", "chromas": true}, {"id": "24020", "num": 20, "name": "Завоеватель Джакс", "chromas": false}, {"id": "24021", "num": 21, "name": "Завоеватель Джакс (престижный)", "chromas": false}, {"id": "24022", "num": 22, "name": "Эмпир<PERSON><PERSON><PERSON> Джакс", "chromas": true}, {"id": "24032", "num": 32, "name": "Новый PAX Джакс", "chromas": false}, {"id": "24033", "num": 33, "name": "ПРОЕКТ: Д<PERSON>а<PERSON><PERSON>", "chromas": true}], "lore": "Непревзойденный как в искусстве сражения уникальным оружием, так и в едком сарказме, Джакс – последний известный мастер оружия из Икатии. После того как его родина пала из-за собственной гордыни и желания подчинить Бездну, Джакс и его род поклялись защищать то малое, что осталось. По мере того как в мире становилось больше магии, былая угроза снова обретала форму, и теперь Джакс путешествует по Валорану, вооруженный последним фонарем Икатии, и испытывает всех встреченных воинов, проверяя, достойны ли они сражаться вместе с ним...", "blurb": "Непревзойденный как в искусстве сражения уникальным оружием, так и в едком сарказме, Джакс – последний известный мастер оружия из Икатии. После того как его родина пала из-за собственной гордыни и желания подчинить Бездну, Джакс и его род поклялись...", "allytips": ["Умение Удар в прыжке можно применять на любом живом союзнике, а также тотемах. Поставив тотем заранее, можно обеспечить себе путь отхода.", "Для Джакса полезнее всего предметы, дающие и силу умений, и силу атаки, такие как Клинок ярости Гинсу и Хекстековый штык."], "enemytips": ["Старайтесь вовлекать его в короткие схватки вместо того, чтобы устраивать затяжные бои. Если не дать нанести ему много атак подряд, можно значительно снизить наносимый им урон.", "Джакс на короткое время может уворачиваться от всех входящих атак и оглушает врагов поблизости, когда заканчивает уворот. Атакуйте его тогда, когда его уворот кончится."], "tags": ["Fighter"], "partype": "Мана", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "Удар в прыжке", "description": "Джакс прыгает к цели. Если это враг, Джакс наносит удар.", "tooltip": "Джакс прыгает к союзному или вражескому бойцу либо тотему, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>, если это враг.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "JaxW", "name": "Усиление", "description": "Джакс заряжает свое оружие энергией, в результате чего его следующая автоатака наносит дополнительный урон.", "tooltip": "Джакс заряжает свое оружие энергией, из-за чего его следующая автоатака или <spellName>Удар в прыжке</spellName> дополнительно наносят <magicDamage>{{ totaldamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "JaxE", "name": "Встречный удар", "description": "Искусность Джакса позволяет ему некоторое время уклоняться ото всех автоатак, а затем контратаковать, оглушая всех противников вокруг себя.", "tooltip": "Джакс принимает защитную стойку на срок до {{ dodgeduration }} сек., уклоняясь от всех автоатак и получая на {{ aoedamagereduction }}% меньше урона от умений, действующих по площади. Через {{ dodgeduration }} сек. или при <recast>повторном применении</recast> Джакс наносит врагам поблизости <magicDamage>магический урон в размере {{ totaldamage }} + {{ percenthealthdamage }}% от максимального запаса здоровья</magicDamage> и <status>оглушает</status> их на {{ stunduration }} сек.<br /><br />Умение наносит на {{ percentincreasedperdodge*100 }}% больше урона за каждую автоатаку, от которой уклонился Джакс (максимальная прибавка – <magicDamage>{{ maxdamage }} + {{ maxpercenthealthdamage }}% от максимального запаса здоровья</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "JaxR", "name": "Мастер оружия", "description": "Каждая третья последовательная автоатака Джакса наносит дополнительный магический урон. Кроме того, Джакс может активировать это умение. При этом он наносит урон окружающим врагам и ненадолго увеличивает свои сопротивление магии и броню.", "tooltip": "<spellPassive>Пассивно:</spellPassive> каждая третья автоатака Джакса в пределах {{ passivefallofftime }} сек. дополнительно наносит <magicDamage>{{ onhitdamage }} магического урона</magicDamage>.<br /><br /><spellActive>Активно:</spellActive> Джакс бьет фонарем, нанося <magicDamage>{{ swingdamagetotal }} магического урона</magicDamage> врагам поблизости. Поразив чемпиона, он получает <scaleArmor>{{ basearmor }} брони</scaleArmor> и <scaleMR>{{ basemr }} сопротивления магии</scaleMR>. Прибавки увеличиваются на <scaleArmor>{{ bonusarmor }}</scaleArmor> и <scaleMR>{{ bonusmr }}</scaleMR> за каждого пораженного чемпиона (время действия – {{ duration }} сек.). В течение этого времени Джакс наносит дополнительный <magicDamage>магический урон</magicDamage> каждую вторую автоатаку, а не каждую третью.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Дополнительный урон (пассивно)", "Урон активного эффекта", "Базовая броня", "Базовое сопротивление магии", "Броня за каждого дополнительного чемпиона", "Сопротивление магии за каждого дополнительного чемпиона", "Перезарядка"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ swingdamagebase }} -> {{ swingdamagebaseNL }}", "{{ baseresists }} -> {{ baseresistsNL }}", "{{ baseresists*0.600000 }} -> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }} -> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }} -> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON> уда<PERSON><PERSON>", "description": "Последовательные автоатаки Джакса увеличивают его скорость атаки.", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}