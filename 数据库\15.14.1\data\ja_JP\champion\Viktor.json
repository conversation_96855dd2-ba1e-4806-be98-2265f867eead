{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viktor": {"id": "<PERSON>", "key": "112", "name": "ビクター", "title": "アーケインの先触れ", "image": {"full": "Viktor.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "112000", "num": 0, "name": "default", "chromas": false}, {"id": "112001", "num": 1, "name": "フルカスタム ビクター", "chromas": false}, {"id": "112002", "num": 2, "name": "プロトタイプ ビクター", "chromas": false}, {"id": "112003", "num": 3, "name": "創造主ビクター", "chromas": false}, {"id": "112004", "num": 4, "name": "死の契りビクター", "chromas": false}, {"id": "112005", "num": 5, "name": "PsyOps ビクター", "chromas": true}, {"id": "112014", "num": 14, "name": "荒野のビクター", "chromas": true}, {"id": "112024", "num": 24, "name": "Arcane 救世主ビクター", "chromas": false}], "lore": "かつての姿から完全なる生物力学的な進化を遂げたビクターは、輝ける進化を受け入れ、彼の支持者にとって救世主となる何かへと変わった。彼は感情を排除することで苦痛を排除できるという論理に基づき、自らの人間性を犠牲にして、ヘクスコアの啓示を世界に与えようとしている──その恩恵が誰にも理解されなかったとしても。このアーケインの達人にとっては、暴力は究極の方程式を解くために必要な変数でしかないのだ。", "blurb": "かつての姿から完全なる生物力学的な進化を遂げたビクターは、輝ける進化を受け入れ、彼の支持者にとって救世主となる何かへと変わった。彼は感情を排除することで苦痛を排除できるという論理に基づき、自らの人間性を犠牲にして、ヘクスコアの啓示を世界に与えようとしている──その恩恵が誰にも理解されなかったとしても。このアーケインの達人にとっては、暴力は究極の方程式を解くために必要な変数でしかないのだ。", "allytips": ["「ヘクステック レイ」は、離れた敵の体力を削ったり、牽制にも使える強力なスキルだ。「グラビティフィールド」と組み合わせて使い、敵に思い通りの位置取りをさせないようにしよう。", "適切なタイミングで適切なスキルを強化するのがポイント。選択はくれぐれも慎重に。"], "enemytips": ["ビクターは中距離戦で本領を発揮する。彼の得意な距離に入らないよう注意しよう。", "ビクターがどれだけスキルを強化しているかは、杖の玉の色(紫、黄、青、赤)から判断できる。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 600, "hpperlevel": 100, "mp": 405, "mpperlevel": 45, "movespeed": 335, "armor": 23, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 8, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "ViktorQ", "name": "パワーブラスト", "description": "敵ユニットを狙い撃って魔法ダメージを与え、自身にシールドを張り、次の通常攻撃のダメージが強化される。<br><br>強化: 「パワーブラスト」のシールドが60%増加し、このスキルを発動後、移動速度が増加する。<br>", "tooltip": "敵を狙い撃って<magicDamage>{{ totalmissiledamage }}の魔法ダメージ</magicDamage>を与え、自身に{{ buffduration }}秒間、<shield>耐久値{{ shieldlevelscaling }}のシールド</shield>を付与する。<br /><br />3.5秒以内に行う次の通常攻撃が<magicDamage>{{ attacktotaldmg }}の魔法ダメージ</magicDamage>を追加で与える。<br /><br /><keywordMajor>アップグレード:</keywordMajor> <shield>耐久値{{ totalaugmentedshieldvalue }}のシールド</shield>を付与し、さらに{{ buffduration }}秒間、<speed>移動速度が{{ augmentmovespeedbonus }}%</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ(スキル)", "ダメージ(通常攻撃)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ aabonusdamage }} -> {{ aabonusdamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [2.5, 2.5, 2.5, 2.5, 2.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0.08, 0.08, 0.08, 0.08, 0.08], [0.18, 0.18, 0.18, 0.18, 0.18], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "2.5", "30", "0", "20/45/70/95/120", "0.08", "0.18", "0.6", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViktorQ.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ViktorW", "name": "グラビティフィールド", "description": "強力な重力場を発生させる装置を展開し、範囲内の敵にスロウ効果を付与する。 また、装置から抜け出せなかった敵にスタン効果を付与する。<br><br>強化: スキルが敵にスロウ効果を付与する。<br>", "tooltip": "{{ fieldduration }}秒間、重力で拘束する装置を設置し、範囲内にいる敵に{{ slowpotency*-1 }}%の<status>スロウ効果</status>を付与する。範囲内に1.25秒間いた敵は{{ stunduration }}秒間の<status>スタン効果</status>を受ける。<br /><br /><keywordMajor>固有スキルをアップグレード: </keywordMajor>1秒間、スキルの<status>スロウ効果</status>が{{ augmentslow }}%増加する。<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "スロウ効果"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ slowpotency*-1.000000 }}% -> {{ slowpotencynl*-1.000000 }}%"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ViktorW.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ViktorE", "name": "ヘクステック レイ", "description": "バイオメカニカルアームから「ヘクステック レイ」を発射し、一直線にフィールドを焼き払いながら、命中した敵ユニットにダメージを与える。<br><br>強化: 「ヘクステック レイ」が焼き払った地面が爆発し、魔法ダメージを与える。<br>", "tooltip": "指定方向にヘクステック レイを発射し、命中した敵に<magicDamage>{{ laserdamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><keywordMajor>強化:</keywordMajor> ヘクステック レイの通過した跡に爆発が起こり、<magicDamage>{{ aftershockdamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ(ヘクステック レイ)", "ダメージ(アフターバーン)", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamagelaser }} -> {{ basedamagelaserNL }}", "{{ basedamageaftershock }} -> {{ basedamageaftershockNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [20, 60, 100, 140, 180], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "20/60/100/140/180", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "ViktorE.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ViktorR", "name": "アーケインストーム", "description": "フィールド上に「アーケインストーム」を召喚し、魔法ダメージを与えて、敵の詠唱を中断させる。嵐は一定時間ごとに周囲のすべての敵に魔法ダメージを与え、ビクターが移動方向を変えられる。<br><br>強化: 「アーケインストーム」の移動速度が25%増加し、嵐でダメージを受けたチャンピオンが倒されるたびに嵐が大きくなり、効果時間が延長される。<br><br>", "tooltip": "{{ stormduration }}秒間、範囲内に「アーケインストーム」を召喚する。この嵐は周囲の敵に直ちに<magicDamage>{{ initialburstdamage }}の魔法ダメージ</magicDamage>を与え、その後は毎秒<magicDamage>{{ subsequentburstdamage }}の魔法ダメージ</magicDamage>を与える。嵐は自動的に直前にダメージを与えたチャンピオンを追いかける。<br /><br /><recast>再発動:</recast> ビクターは手動で嵐を移動できる。<br /><br /><keywordMajor>アップグレード:</keywordMajor> 嵐の移動速度が{{ augmentboost*100 }}%増加する。嵐がダメージを与えたチャンピオンが倒されると、嵐のサイズが増加し、効果時間が{{ tooltip_durationextension }}秒増加する(最大{{ maxgrowths }}回)。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ(発動時ダメージ)", "ダメージ(継続ダメージ)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ initialburstbasedamage }} -> {{ initialburstbasedamageNL }}", "{{ subsequentburstbasedamage }} -> {{ subsequentburstbasedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "ViktorR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "グロリアス・エヴォリューション", "description": "敵を倒すたびに「ヘクス フラグメント」を獲得する。「ヘクス フラグメント」を100個獲得するごとに、発動効果スキルが1つ恒久的に強化される。すべての通常スキルをアップグレードすると、「ヘクス フラグメント」を100個獲得してアルティメットスキルが強化できる。", "image": {"full": "Viktor_Passive.ViktorVGU.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}