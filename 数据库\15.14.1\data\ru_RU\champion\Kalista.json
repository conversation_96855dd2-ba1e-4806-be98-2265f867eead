{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kalista": {"id": "Kalista", "key": "429", "name": "Калиста", "title": "Копье возмездия", "image": {"full": "Kalista.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "429000", "num": 0, "name": "default", "chromas": false}, {"id": "429001", "num": 1, "name": "Калиста Кровавая Луна", "chromas": false}, {"id": "429002", "num": 2, "name": "Калиста Чемпионата мира 2015", "chromas": true}, {"id": "429003", "num": 3, "name": "SKT T1 Калиста", "chromas": false}, {"id": "429005", "num": 5, "name": "Мародер Калиста", "chromas": true}, {"id": "429014", "num": 14, "name": "Калиста из Королевства фей", "chromas": true}, {"id": "429024", "num": 24, "name": "Предвестница зари Калиста", "chromas": true}], "lore": "Калиста – это призрак гнева и воздаяния, бессмертный дух возмездия, облеченный в доспехи кошмар, вызванный с Сумрачных островов, чтобы преследовать изменников и предателей. Жертвы предательства могут взывать об отмщении бесконечно, но Калиста отвечает только тем, кто готов заплатить собственной душой. Горе тем, на кого Калиста обратит свой гнев, ибо любой заключенный с этой мрачной охотницей договор может закончиться только в холодном, пронизывающем огне ее призрачных копий.", "blurb": "Калиста – это призрак гнева и воздаяния, бессмертный дух возмездия, облеченный в доспехи кошмар, вызванный с Сумрачных островов, чтобы преследовать изменников и предателей. Жертвы предательства могут взывать об отмщении бесконечно, но Калиста отвечает...", "allytips": ["Раздирание - отличный помощник в добивании, так как перезарядка этого умения обнуляется, если цель умирает после использования умения.", "Делая один прыжок при помощи Боевой готовности, Калиста не теряет цель своих автоатак.", "Благодаря пассивному умению скорость передвижения Калисты повышается с увеличением скорости атаки."], "enemytips": ["Мобильность Калисты зависит от атакующих действий: отсутствие цели для атаки и эффекты уменьшения скорости атаки влияют на ее способность перемещаться во время боя.", "Калиста не может прервать свою автоатаку. Если вы сумеете угадать момент начала ее атаки, вы сможете успешно использовать свои умения, несмотря на ее высокую мобильность.", "Если успеете скрыться (например, в кусты), пока она готовится бросить копье, она промахнется."], "tags": ["Marksman"], "partype": "Мана", "info": {"attack": 8, "defense": 2, "magic": 4, "difficulty": 7}, "stats": {"hp": 560, "hpperlevel": 114, "mp": 300, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 6.3, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 4, "attackspeedperlevel": 4.5, "attackspeed": 0.694}, "spells": [{"id": "KalistaMysticShot", "name": "Пронзающее копье", "description": "Калиста мечет тонкое быстролетящее копье, которое пробивает убитых им врагов насквозь, продолжая свой путь.", "tooltip": "Калиста швыряет копье, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> первой пораженной цели. Если копье убивает цель, оно летит дальше, перенося все заряды умения <spellName>Раздирание</spellName> на следующего пораженного врага.<br /><br />Калиста может совершить рывок после этого умения с помощью умения <spellName>Боевая готовность</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Стоимость – @AbilityResourceName@", "Урон"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "KalistaMysticShot.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KalistaW", "name": "Дух-храни<PERSON><PERSON><PERSON>ь", "description": "Когда Калиста и чемпион, принесший ей Клятву верности, атакуют одну и ту же цель, та получает дополнительный урон. <br><br>При активации умения Калиста посылает на разведку духа, который раскрывает область перед собой.", "tooltip": "<spellPassive>Пассивно:</spellPassive> когда Калиста и союзник, связанный с ней <keywordMajor>Клятвой верности</keywordMajor>, применяют автоатаки к одному и тому же врагу, она наносит ему <magicDamage>магический урон в размере {{ maxhealthdamage*100 }}% от максимального запаса здоровья</magicDamage>. Перезарядка эффекта для каждой цели – {{ pertargetcooldown }} сек. Он наносит не более {{ maximummonsterdamage }} урона целям, не являющимся чемпионами.<br /><br /><spellPassive>Активно:</spellPassive> Калиста выпускает призрака, который делает три круга по указанной области. Замеченные им чемпионы раскрываются на 4 сек. У этого умения может быть до 2 зарядов (новый заряд дается раз в {{ ammorechargetooltip }} сек.).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Коэффициент урона от максимального запаса здоровья", "Периодичность получения зарядов", "Максимальный урон монстрам"], "effect": ["{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ ammorechargetooltip }} -> {{ ammorechargetooltipNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}"]}, "maxrank": 5, "cooldown": [30, 30, 30, 30, 30], "cooldownBurn": "30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 80, 80, 80, 80], [0, 0, 0, 0, 0], [125, 150, 175, 200, 225], [90, 80, 70, 60, 50], [0, 0, 0, 0, 0], [75, 100, 125, 150, 175], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80", "0", "125/150/175/200/225", "90/80/70/60/50", "0", "75/100/125/150/175", "10", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "2", "range": [5000, 5000, 5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "KalistaW.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "KalistaExpungeWrapper", "name": "Раздирание", "description": "Копья Калисты застревают в атакуемых ею целях. При активации умения Калиста вырывает копья из находящихся неподалеку врагов, нанося им увеличивающийся в зависимости от числа копий физический урон и замедляя их.", "tooltip": "<spellPassive>Пассивно:</spellPassive> копья Калисты застревают в пораженных целях на 4 сек. (количество копий в каждой цели не ограничено).<br /><br /><spellActive>Активно:</spellActive> Калиста вырывает копья из врагов поблизости, нанося им <physicalDamage>{{ normaldamage }} физического урона</physicalDamage> плюс еще <physicalDamage>{{ additionaldamage }} физического урона</physicalDamage> за каждое копье после первого, а также <status>замедляя</status> их на <attention>{{ totalslowamount }}</attention> на {{ slowduration }} сек.<br /><br />Если это умение убивает хотя бы одну цель, его перезарядка сбрасывается, а Калиста восстанавливает себе <scaleMana>{{ manarefund }} маны</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Урон за заряд", "Урон от силы атаки за заряд", "Замедление", "Восстановление маны", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ additionalbasedamage }} -> {{ additionalbasedamageNL }}", "{{ additionaladratio*100.000000 }}% -> {{ additionaladrationl*100.000000 }}%", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ manarefund }} -> {{ manarefundNL }}", "{{ fakedcooldown }} -> {{ fakedcooldownNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "2", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaExpungeWrapper.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KalistaRx", "name": "Зов судьбы", "description": "Калиста телепортирует к себе давшего ей Клятву верности союзника, который по прибытии может совершить полет в указанном направлении, разбрасывая вражеских чемпионов.", "tooltip": "Калиста помещает союзника, связанного с ней <keywordMajor>Клятвой верности</keywordMajor>, в стазис на время до 4 сек. После этого союзник может щелкнуть по карте, чтобы запустить себя в выбранное место. При столкновении с чемпионом он останавливается и <status>отбрасывает</status> всех врагов поблизости, а также перемещается от цели на расстояние атаки.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Продолжительность подбрасывания"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [1, 1.5, 2], [40, 60, 80], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "1/1.5/2", "40/60/80", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaRx.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Боевая готовность", "description": "Если во время автоатаки или при использовании Пронзающего копья Калиста решает сдвинуться с места, она прыгает в указанном курсором направлении после броска копья.", "image": {"full": "Kalista_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}