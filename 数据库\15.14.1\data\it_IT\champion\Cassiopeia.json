{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Cassiopeia": {"id": "Cassiopeia", "key": "69", "name": "Cassiopeia", "title": "l'abbraccio del serpente", "image": {"full": "Cassiopeia.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "69000", "num": 0, "name": "default", "chromas": true}, {"id": "69001", "num": 1, "name": "Cassiopeia Desperada", "chromas": false}, {"id": "69002", "num": 2, "name": "Cassiopeia <PERSON>", "chromas": false}, {"id": "69003", "num": 3, "name": "Cassiopeia Mitologica", "chromas": false}, {"id": "69004", "num": 4, "name": "Cassiopeia Zanna di Giada", "chromas": false}, {"id": "69008", "num": 8, "name": "Cassiopeia Eternum", "chromas": false}, {"id": "69009", "num": 9, "name": "Cassiopeia Fiore spirituale", "chromas": true}, {"id": "69018", "num": 18, "name": "Cassiopeia della Congrega", "chromas": true}, {"id": "69028", "num": 28, "name": "Strega Cassiopeia", "chromas": true}, {"id": "69038", "num": 38, "name": "Cassiopeia Artefice di Miti (edizione prestigio)", "chromas": false}], "lore": "Cassiopeia è una creatura letale, determinata a piegare gli altri alla sua sinistra volontà. La più giovane e incantevole figlia della nobile famiglia Du Couteau di Noxus, si è avventurata nelle cripte di Shurima in cerca di un potere antico. Lì è stata morsa da un terrificante guardiano della tomba, il cui veleno l'ha trasformata in una predatrice simile a una vipera. Scaltra e agile, ora Cassiopeia striscia sotto il velo della notte, pietrificando i nemici con il suo terribile sguardo.", "blurb": "Cassiopeia è una creatura letale, determinata a piegare gli altri alla sua sinistra volontà. La più giovane e incantevole figlia della nobile famiglia Du Couteau di Noxus, si è avventurata nelle cripte di Shurima in cerca di un potere antico. Lì è stata...", "allytips": ["Usa Zanne gemelle sui bersagli stazionari avvelenati come i mostri e le unità stordite da Sguardo pietrificante per infliggere il massimo dei danni.", "Anticipa il nemico quando cerchi di colpirlo con Esplosione tossica in modo da assicurarti di mandare il colpo a segno."], "enemytips": ["Fai attenzione al potenziale danno che Cassiopeia può infliggerti con Zanne gemelle una volta che ti ha avvelenato.", "G<PERSON><PERSON> dall'altra parte quando Cassiopeia sta lanciando Sguardo pietrificante in modo da essere rallentato invece che stordito."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 9, "difficulty": 10}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 400, "mpperlevel": 40, "movespeed": 328, "armor": 18, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 1.5, "attackspeed": 0.647}, "spells": [{"id": "CassiopeiaQ", "name": "Esplosione tossica", "description": "Dopo un breve istante, Cassiopeia intossica un'area con una nuvola che infligge ingenti danni velenosi, ottenendo un bonus alla velocità di movimento se colpisce un campione.", "tooltip": "Cassiopeia emette un gas tossico, <keywordMajor>av<PERSON><PERSON><PERSON></keywordMajor> i nemici e infliggendo <magicDamage>{{ tooltiptotaldamage }} danni magici</magicDamage> nell'arco di {{ e2 }} secondi. Se colpisce un campione, Cassiopeia ottiene <speed>{{ e3 }}% velocità di movimento</speed> che decade nell'arco di {{ e4 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [3, 3, 3, 3, 3], [30, 35, 40, 45, 50], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/110/145/180/215", "3", "30/35/40/45/50", "3", "7", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "CassiopeiaQ.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CassiopeiaW", "name": "Miasma", "description": "Cassiopeia rilascia varie nubi tossiche, che rallentano, ancorano e danneggiano lievemente i nemici che le attraversano. I nemici ancorati non possono usare abilità di movimento.", "tooltip": "Cassiopeia sputa veleno, lasciando nubi tossiche per {{ e4 }} secondi. I nemici nelle nubi subiscono <magicDamage>{{ damagepersecond }} danni magici</magicDamage> al secondo, sono <keywordMajor>av<PERSON><PERSON><PERSON></keywordMajor>, <status>ancorati</status> e <status>rallentati</status> del {{ e2 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [20, 25, 30, 35, 40], [40, 50, 60, 70, 80], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/25/30/35/40", "40/50/60/70/80", "1", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "CassiopeiaW.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CassiopeiaE", "name": "<PERSON><PERSON> g<PERSON>", "description": "Cassiopeia esegue un attacco che infligge danni amplificati ai bersagli avvelenati e la cura per una percentuale dei danni inflitti. Se il bersaglio muore per l'attacco, Cassiopeia recupera mana.", "tooltip": "Cassiopeia lancia delle zanne letali, infliggendo <magicDamage>{{ basicdamage }} danni magici</magicDamage>. Se un nemico è <keywordMajor>avvelenato</keywordMajor>, subisce <magicDamage>{{ bonuspoisoneddamage }} danni magici</magicDamage> aggiuntivi e Cassiopeia recupera <healing>{{ healcalc }} salute</healing> ridotta a <healing>{{ healcalcminion }} salute</healing> contro i minion in corsia e i mostri piccoli.<br /><br />Se il bersaglio muore, Cassiopeia recupera <scaleMana>{{ cost }} mana</scaleMana>.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>ni bonus veleno", "Guarigione da potere magico"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ healratio*100.000000 }}% -> {{ healrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0.75, 0.75, 0.75, 0.75, 0.75], "cooldownBurn": "0.75", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "CassiopeiaE.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CassiopeiaR", "name": "Sguardo pietrificante", "description": "Cassiopeia rilascia un turbinio di energia dagli occhi, stordendo i nemici rivolti verso di lei e rallentando chi le dà le spalle.", "tooltip": "Cassiopeia emette uno sguardo paralizzante, che infligge <magicDamage>{{ rdamage }} danni magici</magicDamage> e <status>stordisce</status> i nemici rivolti verso di lei per {{ rccduration }} secondi. I nemici rivolti in altre direzioni sono invece <status>rallentati</status> del {{ rslowpercent }}%, che decade nell'arco della stessa durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "CassiopeiaR.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Grazia serpentina", "description": "Cassiopeia ottiene velocità di movimento per livello, ma non può acquistare stivali.", "image": {"full": "Cassiopeia_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}