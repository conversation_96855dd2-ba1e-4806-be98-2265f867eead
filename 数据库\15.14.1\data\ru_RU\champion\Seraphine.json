{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Seraphine": {"id": "<PERSON><PERSON><PERSON>", "key": "147", "name": "Серафина", "title": "Мечтательная певица", "image": {"full": "Seraphine.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "147000", "num": 0, "name": "default", "chromas": false}, {"id": "147001", "num": 1, "name": "Серафина из K/DA ''ALL OUT'' – инди", "chromas": false}, {"id": "147002", "num": 2, "name": "Серафина из K/DA ''ALL OUT'' – звезда", "chromas": false}, {"id": "147003", "num": 3, "name": "Серафина из K/DA ''ALL OUT'' – суперзвезда", "chromas": false}, {"id": "147004", "num": 4, "name": "Грациозный феникс Серафина", "chromas": true}, {"id": "147014", "num": 14, "name": "Пляжная Серафина", "chromas": true}, {"id": "147015", "num": 15, "name": "Пляжная Серафина (престижный)", "chromas": false}, {"id": "147024", "num": 24, "name": "Серафина из Королевства фей", "chromas": true}, {"id": "147034", "num": 34, "name": "Звездная защитница Серафина", "chromas": true}, {"id": "147043", "num": 43, "name": "Боевой голубь Серафина", "chromas": true}, {"id": "147050", "num": 50, "name": "Лакомка Серафина", "chromas": true}], "lore": "Серафина родилась в Пилтовере в семье выходцев из Зауна. Она слышит чужие сердца. Мир поет для нее, и она отвечает ему своей песней. В детстве диссонанс звуков казался ей нестерпимым, но сейчас она черпает из этой разноголосицы вдохновение и обращает ее в симфонию. Она выступает перед зрителями из обоих городов и напоминает, что они не одни, что сила в единстве и что их потенциал воистину безграничен.", "blurb": "Серафина родилась в Пилтовере в семье выходцев из Зауна. Она слышит чужие сердца. Мир поет для нее, и она отвечает ему своей песней. В детстве диссонанс звуков казался ей нестерпимым, но сейчас она черпает из этой разноголосицы вдохновение и обращает ее...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "Мана", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 25, "movespeed": 330, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.669}, "spells": [{"id": "SeraphineQ", "name": "Высокая нота", "description": "Серафина наносит урон по площади.", "tooltip": "Серафина выдает чистую ноту, нанося <magicDamage>{{ explosiondamage }} магического урона</magicDamage>. Урон против чемпионов увеличивается в зависимости от недостающего здоровья цели - вплоть до <magicDamage>{{ totalempowereddamage }}</magicDamage>, когда у цели меньше {{ executethreshold*100 }}% здоровья.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Начальный урон", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "SeraphineQ.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SeraphineW", "name": "Объемный звук", "description": "Серафина накладывает щит на союзников поблизости и ускоряет их. Если на Серафину уже наложен щит, она также лечит союзников поблизости.", "tooltip": "Серафина поет песню для союзных чемпионов поблизости, увеличивая <speed>их скорость передвижения на {{ hastevalueallies }}</speed> и <speed>свою скорость передвижения на {{ wmsbonustotal }} (эффект ослабевает со временем)</speed>, а также накладывая на всех <shield>щит прочностью {{ shieldvalueseraphine }}</shield> на {{ shieldduration }} сек.<br /><br />Если на Серафину уже действует <shield>щит</shield>, она приглашает союзников присоединиться к ней, восстанавливая им <healing>{{ wmissinghpheal }}% от недостающего здоровья</healing> за каждого союзного чемпиона поблизости через {{ whealsplitdelay }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Прочность щита", "Лечение", "Стоимость – @AbilityResourceName@"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ wmissinghpbase }} -> {{ wmissinghpbaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 22, 22, 22, 22], "cooldownBurn": "22", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SeraphineW.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SeraphineE", "name": "Ударный ритм", "description": "Серафина наносит урон врагам на прямой и ограничивает их передвижение.", "tooltip": "Серафина выпускает мощную звуковую волну, нанося <magicDamage>{{ finaldamage }} магического урона</magicDamage> врагам на прямой и <status>замедляя</status> их на {{ slowvalue }}% на {{ slowduration }} сек.<br /><br /><status>Замедленных</status> врагов она <status>обездвиживает</status>, а <status>обездвиженных</status> <status>оглушает</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Продолжительность замедления", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1300, 1300, 1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "SeraphineE.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SeraphineR", "name": "Исполнение на бис", "description": "Серафина очаровывает пораженных врагов и наносит им урон. Когда заряд касается союзного или вражеского чемпиона, дальность полета обновляется.", "tooltip": "Серафина начинает выступление, выпуская волну, которая <status>очаровывает</status> врагов на {{ rchannelduration }} сек. и наносит <magicDamage>{{ r1totaldamage }} магического урона</magicDamage>.<br /><br />Все затронутые чемпионы (включая союзников) становятся частью выступления, увеличивая дальность полета волны. Союзные чемпионы при этом получают максимальное количество <keywordMajor>нот</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Начальный урон", "Продолжительность очарования", "Перезарядка"], "effect": ["{{ r1basedamage }} -> {{ r1basedamageNL }}", "{{ rchannelduration }} -> {{ rchanneldurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SeraphineR.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Сценическое обаяние", "description": "Каждое третье базовое умение Серафины применяется дважды. Кроме того, если она применяет умения рядом с союзниками, дальность и магический урон ее следующей автоатаки увеличиваются.", "image": {"full": "Seraphine_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}