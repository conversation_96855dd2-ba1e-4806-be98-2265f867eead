{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jhin": {"id": "<PERSON><PERSON>", "key": "202", "name": "<PERSON><PERSON>", "title": "il virtuoso", "image": {"full": "Jhin.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "202000", "num": 0, "name": "default", "chromas": false}, {"id": "202001", "num": 1, "name": "Jhin Mezzogiorno di Fuoco", "chromas": true}, {"id": "202002", "num": 2, "name": "<PERSON><PERSON> di Sangue", "chromas": false}, {"id": "202003", "num": 3, "name": "Jhin SKT T1", "chromas": false}, {"id": "202004", "num": 4, "name": "PROGETTO: <PERSON><PERSON>", "chromas": false}, {"id": "202005", "num": 5, "name": "<PERSON><PERSON> O<PERSON>rit<PERSON> Cosmica", "chromas": false}, {"id": "202014", "num": 14, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "202023", "num": 23, "name": "Jhin DWG", "chromas": true}, {"id": "202025", "num": 25, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "202036", "num": 36, "name": "<PERSON><PERSON> <PERSON>", "chromas": false}, {"id": "202037", "num": 37, "name": "Jhin Estinzione dell'Oscurità Cosmica", "chromas": true}, {"id": "202047", "num": 47, "name": "Jhin Artefice di Miti", "chromas": false}], "lore": "Jhin è un criminale psicopatico e meticoloso che vede la morte come un'opera d'arte. In passato fu prigioniero dal popolo di Ionia, ma fu liberato da uomini misteriosi appartenenti al consiglio decisionale di Ionia, diventando l'assassino della congrega. Usa la sua arma come pennello per creare opere artistiche dalle sfumature brutali, terrorizzando le sue vittime e gli spettatori. Avverte un perfido piacere quando sale sul palcoscenico del suo teatro degli orrori, l'occasione ideale per mandare uno dei suoi messaggi più forti: terrore.", "blurb": "Jhin è un criminale psicopatico e meticoloso che vede la morte come un'opera d'arte. In passato fu prigioniero dal popolo di Ionia, ma fu liberato da uomini misteriosi appartenenti al consiglio decisionale di Ionia, diventando l'assassino della congrega...", "allytips": ["Florilegio letale ha una portata incredibile. Quando stai per entrare in un combattimento controlla l'eventuale presenza di nemici bloccati.", "La tua suprema infligge molti meno danni ai nemici che hanno la salute al massimo. Cerca di colpire i bersagli indeboliti mentre fuggono.", "Mentre ricarichi puoi comunque lanciare le tue abilità. Usale per impiegare il tempo di ricarica."], "enemytips": ["Florilegio letale blocca solo chi è stato colpito negli ultimi 4 secondi da un attacco base, trappola o alleato di Jhin.", "<PERSON>hin piazza trappole invisibili sulla mappa. Occhio a dove metti i piedi!", "Gli attacchi di Jhin sono piuttosto potenti, ma termina le munizioni dopo il quarto colpo. Usa questa finestra di opportunità per attaccarlo e distruggerlo."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 2, "magic": 6, "difficulty": 6}, "stats": {"hp": 655, "hpperlevel": 107, "mp": 300, "mpperlevel": 50, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.4, "attackspeedperlevel": 0, "attackspeed": 0.625}, "spells": [{"id": "JhinQ", "name": "Ispirazione improvvisa", "description": "Jhin spara un bossolo magico a un nemico. Può colpire fino a quattro bersagli e aumenta i suoi danni ogni volta che uccide.", "tooltip": "Jhin lancia un bossolo sul nemico bersaglio che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> per poi rimbalzare su un bersaglio nelle vicinanze che non è ancora stato colpito.<br /><br />Il bossolo può colpire fino a un massimo di {{ tooltipmaxtargetshit }} volte. Ogni nemico morto entro pochi secondi dal colpo aumenta i danni dei colpi successivi del {{ percentamponkill*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico totale", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "JhinQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinW", "name": "Flor<PERSON><PERSON> letale", "description": "<PERSON><PERSON> brandisce il suo bastone, sparando un singolo colpo con una gittata incredibile. Perfora minion e mostri, ma si ferma al primo campione. Se il bersaglio era stato colpito di recente dagli alleati di Jhin, dalle trappole di loto o dagli attacchi base, viene immobilizzato.", "tooltip": "Jhin spara un colpo a lunga gittata che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al primo nemico colpito e agli altri nemici lungo il tragitto.<br /><br />Se questa abilità colpisce un campione danneggiato da un campione alleato negli ultimi {{ spottingduration }} secondi, lo <status>immobilizza</status> per {{ rootduration }} secondi e fornisce a Jhin la velocità di movimento di <spellName>Morte in quattro atti</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata immobilizzazione", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "JhinW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinE", "name": "Catturare il pubblico", "description": "<PERSON>hin piazza una trappola di loto invisibile che sboccia quando viene calpestata. Rallenta i nemici nelle vicinanze per poi infliggere danni con un'esplosione di petali taglienti. <br><br><font color='#FFFFFF'>Bellezza nella morte:</font> quando Jhin uccide un campione nemico, una trappola di loto sboccia vicino al cadavere.", "tooltip": "<passive>Passiva:</passive> I campioni uccisi da Jhin generano e fanno esplodere una trappola di loto nel punto in cui muoiono.<br /><br /><active>Attiva:</active> <PERSON><PERSON> piazza una trappola di loto invisibile per {{ trapduration }} minuti che genera un'area in grado di <status>rallentare</status> del {{ trapslowamount*100 }}% i nemici che la attraversano. Dopo {{ trapdetonationtime }} secondi, la trappola esplode infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />Questa abilità ha 2 cariche ({{ ammorechargeratetooltip }} secondo/i di ricarica).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Tempo di ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [2, 2, 2, 2, 2], "cooldownBurn": "2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "JhinE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinR", "name": "Chiamata alla ribalta", "description": "<PERSON><PERSON> canal<PERSON>, t<PERSON><PERSON><PERSON><PERSON>urro in un maestoso cannone da spalla. È in grado di sparare 4 colpi potenziati con grande gittata, capaci di perforare minion e mostri, ma che si fermano al primo campione colpito. Sussurro menoma i nemici colpiti, rallentandoli e infliggendo danni da esecuzione. Il quarto colpo è perfetto e ha una potenza epica che garantisce il colpo critico.", "tooltip": "Jhin si prepara e inizia la canalizzazione, che gli consente di sparare 4 super colpi, ciascuno dei quali infligge tra <physicalDamage>{{ damagecalc }}</physicalDamage> e <physicalDamage>{{ maxincreasecalc }} danni fisici</physicalDamage> al primo campione che colpisce in base alla sua percentuale di salute persa e lo <status>rallenta</status> del {{ slowpercent*100 }}% per {{ slowduration }} secondi. Il quarto colpo è sempre critico e infligge il {{ fourthshotmultiplier*100 }}% dei danni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JhinR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Morte in quattro atti", "description": "L'arma di <PERSON>, <PERSON><PERSON><PERSON><PERSON>, è uno strumento preciso progettato per infliggere danni sproporzionati. Spara con una cadenza fissa e ha solo quattro proiettili. Jhin incanta l'ultimo proiettile con magie nere per mettere a segno un colpo critico e infliggere danni bonus da esecuzione. Quando Sussurro mette a segno un colpo critico, ispira in Jhin un aumento di velocità di movimento.", "image": {"full": "Jhin_P.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}