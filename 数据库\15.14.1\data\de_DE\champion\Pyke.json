{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pyke": {"id": "<PERSON><PERSON>", "key": "555", "name": "<PERSON><PERSON>", "title": "der Schlitzer vom Bluthafen", "image": {"full": "Pyke.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "555000", "num": 0, "name": "default", "chromas": false}, {"id": "555001", "num": 1, "name": "Sandgeist Pyke", "chromas": true}, {"id": "555009", "num": 9, "name": "Blutmond-Pyke", "chromas": true}, {"id": "555016", "num": 16, "name": "PROJEKT: <PERSON><PERSON>", "chromas": true}, {"id": "555025", "num": 25, "name": "PsyOps-Pyke", "chromas": true}, {"id": "555034", "num": 34, "name": "<PERSON><PERSON><PERSON><PERSON> Pyke", "chromas": true}, {"id": "555044", "num": 44, "name": "<PERSON><PERSON><PERSON><PERSON> Ritter <PERSON>", "chromas": true}, {"id": "555045", "num": 45, "name": "Elysischer Pyke", "chromas": true}, {"id": "555053", "num": 53, "name": "Soul Fighter Pyke", "chromas": true}, {"id": "555054", "num": 54, "name": "Soul Fighter Pyke (Prestige)", "chromas": false}, {"id": "555064", "num": 64, "name": "Schreckensnacht-Pyke", "chromas": true}, {"id": "555074", "num": 74, "name": "Tintenschatten-Pyke", "chromas": true}], "lore": "<PERSON><PERSON>, ein ber<PERSON>hmter Harpunier vom Schlachterhafen in Bilgewasser, hätte seinen Tod im Bauch eines gigantischen Jaullfisches finden sollen … doch er kehrte zurück. Jetzt treibt er in den feuchten Straßen und Gassen seiner ehemaligen Heimatstadt sein Unwesen und nutzt seine neuen übernatürlichen Gaben, um denjenigen ein schnelles und grausames Ende zu bereiten, die sich auf Kosten anderer bereichern. Die Stadt, die sich mit der Monsterjagd rühmt, wird nun selbst von einem Monster gejagt.", "blurb": "<PERSON><PERSON>, ein berühmter Harpunier vom Schlachterhafen in Bilgewasser, hätte seinen Tod im Bauch eines gigantischen Jaullfisches finden sollen … doch er kehrte zurück. Jetzt treibt er in den feuchten Straßen und Gassen seiner ehemaligen Heimatstadt sein...", "allytips": ["Pyke hält nicht viel aus, also solltest du du vorübergehend Kampfpausen einlegen. Du kannst einen erheblichen Teil deines Lebens durch „Gabe der Ertrunkenen“ regenerieren, wenn die Gegner dich nicht sehen können.", "Hältst du „Knochenspieß“ g<PERSON>, ziehst du den Gegner immer gleich weit zu dir. Befindest du dich in Nahkampfreichweite, kannst du dein Ziel hinter dich werfen.", "Die einfache Version von „Knochenspieß“ ist wesentlich schneller und verursacht zusätzlichen Schaden.", "Viele deiner aggressiven Fähigkeiten dienen auch zur Flucht. Plane immer schon im Voraus, wie du dich wieder in Sicherheit bringen kannst."], "enemytips": ["Pyke regeneriert einen erheblichen Teil des Schadens, den er durch Champions erlitten hat, wenn du ihn nicht sehen kannst!", "<PERSON><PERSON> in deiner Nähe auf „Geisterhaftem Tauchgang“ ist, ziehen Haie ihre Kreise unter deinen Füßen.", "<PERSON>t dich von Verbündeten mit wenig Leben fern. <PERSON><PERSON> sie mit „Tod aus der Tiefe“ exekutiert, könntest du der nächste auf der Liste sein."], "tags": ["Support", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 670, "hpperlevel": 110, "mp": 415, "mpperlevel": 50, "movespeed": 330, "armor": 43, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2, "attackspeedperlevel": 2.5, "attackspeed": 0.667}, "spells": [{"id": "PykeQ", "name": "Knochenspieß", "description": "<PERSON><PERSON> spießt entweder einen Gegner vor ihm auf oder zieht einen Gegner zu sich.", "tooltip": "<tap>Antippen:</tap> Pyke greift an und fügt dem ersten getroffenen Gegner <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu. Dabei werden Champions priorisiert. Danach wird der Gegner {{ slowduration }}&nbsp;Sekunde lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br /><hold>Halten:</hold> Pyke wirft seine Harpune, fügt dem ersten getroffenen Gegner <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und <status>zieht</status> ihn zu sich. Dann wird der Gegner {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br />Wenn Pyke erfolgreich einen gegnerischen Champion trifft oder die Kanalisierung nicht erfolgreich abgeschlossen wird, werden {{ manarefund*100 }}&nbsp;% der Manakosten zurückerstattet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PykeQ.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PykeW", "name": "Geisterhafter Tauchgang", "description": "Pyke camoufliert sich und erhält einen erheblichen Lauftemposchub, der mit der Zeit abklingt.", "tooltip": "<PERSON><PERSON> erhält <keywordStealth>Camouflage</keywordStealth> und <speed>{{ movespeed }}&nbsp;% Lauftempo</speed>, das über {{ e0 }}&nbsp;Sekunden hinweg abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0.3, 0.3, 0.3, 0.3, 0.3], [0.03, 0.03, 0.03, 0.03, 0.03], [80, 80, 80, 80, 80], [0.55, 0.55, 0.55, 0.55, 0.55], [8, 8, 8, 8, 8], [0.01, 0.01, 0.01, 0.01, 0.01], [1.15, 1.15, 1.15, 1.15, 1.15], [5, 5, 5, 5, 5]], "effectBurn": [null, "45", "0", "0.3", "0.03", "80", "0.55", "8", "0.01", "1.15", "5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PykeW.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PykeE", "name": "Phantomsog", "description": "Pyke springt davon und hinterlässt ein Phantom, das nach kurzer Zeit zu ihm zurückkehrt und gegnerische Champions auf dem Weg betäubt.", "tooltip": "Pyke springt nach vorn und hinterlässt ein ertrunkenes Phantom, das kurz darauf zu ihm zurückkehrt. Das Phantom <status>betäubt</status> Gegner {{ stunduration }}&nbsp;Sekunden lang und fügt Champions <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [100, 150, 200, 250, 300], [1.25, 1.25, 1.25, 1.25, 1.25], [550, 550, 550, 550, 550], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/150/200/250/300", "1.25", "550", "1", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "PykeE.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PykeR", "name": "Tod aus der Tiefe", "description": "Pyke teleportiert sich zu Gegnern mit wenig Leben und richtet sie hin. Bei Erfolg kann er die Fähigkeit erneut wirken und unterstützende Verbündeten erhalten zusätzliches Gold.", "tooltip": "Pyke trifft alle gegnerischen Champions in einem X-förmigen Bereich und teleportiert sich zu Zielen mit weniger als <scaleAD>{{ rdamage }}</scaleAD>&nbsp;<PERSON><PERSON>, um sie zu <danger>exekutieren</danger>. Champions mit ausreichend Leben und Einheiten, die keine Champion sind, erleiden stattdessen normalen Schaden in Höhe von {{ reduceddamage*100 }}&nbsp;% dieses Betrags (<physicalDamage>{{ reduceddamagefinal }}</physicalDamage>). <br /><br />Wenn ein gegnerischer Champion innerhalb des X-förmigen Bereichs stirbt, kann Pyke die Fähigkeit innerhalb von {{ rrecastduration }}&nbsp;Sekunden kostenlos <recast>reaktivieren</recast>. Wenn er diesen Champion exekutiert hat, erhält der letzte unterstützende Verbündete ebenfalls Kill-Gold. Wen<PERSON> nicht, erhält Pyke das Kill-Gold trotzdem.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "PykeR.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON>", "description": "<PERSON>n Pyke vor Gegnern verborgen ist, regeneriert er Schaden, den er vor Kurzem durch Champions erlittenen hat. Pyke erhält außerdem kein zusätzliches maximales Leben jeglichen Ursprungs, sondern stattdessen zusätzlichen Angriffsschaden.", "image": {"full": "PykePassive.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}