{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Maokai": {"id": "Maokai", "key": "57", "name": "Maokai", "title": "der verdorb<PERSON>", "image": {"full": "Maokai.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "57000", "num": 0, "name": "default", "chromas": false}, {"id": "57001", "num": 1, "name": "Verkohlter Maokai", "chromas": false}, {"id": "57002", "num": 2, "name": "Totem-Maokai", "chromas": false}, {"id": "57003", "num": 3, "name": "Fest<PERSON>r <PERSON>", "chromas": false}, {"id": "57004", "num": 4, "name": "<PERSON><PERSON><PERSON>ser <PERSON>", "chromas": false}, {"id": "57005", "num": 5, "name": "Torwart-Mao<PERSON>", "chromas": false}, {"id": "57006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "57007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "57016", "num": 16, "name": "Weltenbrecher-Maokai", "chromas": true}, {"id": "57024", "num": 24, "name": "Astronauten-Maokai", "chromas": true}, {"id": "57033", "num": 33, "name": "DRX-Maokai", "chromas": true}], "lore": "Maokai ist ein wutentbrannter und gewaltiger Baumriese, der es sich zum Ziel gemacht hat, gegen die unnatürlichen Schrecken der Schatteninseln zu kämpfen. Nachdem eine magische Katastrophe seine Heimat zerstört hatte und er nur durch das Wasser des Lebens, das sein Kernholz tränkt, dem Fluch des Untodes entkommen war, ist er nun zu einem Rachegeist geworden. Einst als friedvoller Naturgeist bekannt, kämpft Maokai nun mit aller Macht, um die Plage des Unlebens von den Schatteninseln zu verbannen und seiner Heimat wieder zu ihrer einstigen Schönheit zu verhelfen.", "blurb": "Maokai ist ein wutentbrannter und gewaltiger Baumriese, der es sich zum Ziel gemacht hat, gegen die unnatürlichen Schrecken der Schatteninseln zu kämpfen. Nachdem eine magische Katastrophe seine Heimat zerstört hatte und er nur durch das Wasser des...", "allytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die ins Gebüsch geworfen werden, sind stärker – doch in der Gruppe nicht unbedingt effektiver.", "<PERSON><PERSON><PERSON>, den Gegner zu flankieren, oder Situationen auszunutzen, in denen „Wurzelfänge“ unausweichlich ist oder den Gegner überrascht.", "Maokais Passiv macht ihn extrem widerstandsfähig gegenüber konzentrierten Beschuss mit Fähigkeiten – zumindest solange er normale Angriffe ausführen kann."], "enemytips": ["Sprösslinge verfolgen den 1. <PERSON><PERSON><PERSON>, der zu nahe kommt, explodieren aber nach wenigen Sekunden oder wenn sie mit Gegnern zusammenst<PERSON>ßen. Sei besonders vorsichtig bei Sprösslingen im Gebüsch, denn sie sind weitaus gefährlicher.", "Die Abklingzeit von Maokais Selbstheilung hängt stark von deiner Trefferhäufigkeit ab. <PERSON><PERSON> v<PERSON>, dass du keine Fähigkeiten auf ihn verschwendest.", "Maokai erleidet keinen <PERSON>en, während er „Gewundener Vormarsch“ einsetzt, also spare dir deine Fähigkeiten auf."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 8, "magic": 6, "difficulty": 3}, "stats": {"hp": 665, "hpperlevel": 109, "mp": 375, "mpperlevel": 43, "movespeed": 335, "armor": 35, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.75, "mpregen": 6, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.125, "attackspeed": 0.8}, "spells": [{"id": "MaokaiQ", "name": "Dornenschlag", "description": "Maokai schlägt nahe Gegner mit einer Schockwelle zurück, die magischen Schaden verursacht und sie verlangsamt.", "tooltip": "Mao<PERSON> schlägt mit seiner Faust auf den Boden, verursacht <magicDamage>magischen <PERSON>haden</magicDamage> in <PERSON><PERSON><PERSON> von {{ totaldamage }} plus {{ basepercenthealth*100 }}&nbsp;% seines maximalen Lebens und <status>verlangsamt</status> Gegner kurzzeitig. Gegner in der Nähe werden außerdem <status>zurückgestoßen</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON> %", "<PERSON><PERSON><PERSON> an <PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basepercenthealth*100.000000 }}&nbsp;% -> {{ basepercenthealthnl*100.000000 }}&nbsp;%", "{{ bonusmonsterdamage }} -> {{ bonusmonsterdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "MaokaiQ.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MaokaiW", "name": "Gewundener Vormarsch", "description": "Maokai verkrümmt sich zu einer Masse sich bewegender Wurzeln, kann nicht mehr anvisiert werden und stürmt auf ein Ziel zu. Wenn er sein <PERSON> erreicht, wird es festgehalten.", "tooltip": "Maokai wird zu einem wandelnden Wurzelgestrüpp, kann nicht länger anvisiert werden und zieht sich zu einem Gegner. Sobald er ihn erreicht hat, wird der Gegner {{ e2 }}&nbsp;Sekunde(n) lang <status>festgehalten</status> und erleidet <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Festhaltedauer", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [60, 85, 110, 135, 160], [1, 1.1, 1.2, 1.3, 1.4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/85/110/135/160", "1/1.1/1.2/1.3/1.4", "0", "0", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "MaokaiW.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MaokaiE", "name": "Sprösslingwurf", "description": "Maokai schleudert einen Sprössling zum Zielgebiet, um dort Wache zu halten. Sie sind in hohem Gras effektiver.", "tooltip": "Maokai wirft einen Sprössling zum Zielort, der dort {{ saplingduration }}&nbsp;Sekunden lang Ausschau hält. Sprösslinge verfolgen nahe Gegner und explodieren, sobald sie diese erreicht haben. Dabei verursachen sie <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamen</status> umstehende Gegner {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%. Trifft der Sprössling einen gegnerischen Champion oder ein episches Monster, verringert sich die Abklingzeit von <keywordMajor>Magie auslaugen</keywordMajor> um weitere 4&nbsp;Sekunden.<br /><br />Ins hohe Gras geworfene Sprösslinge bleiben stattdessen {{ empoweredsaplingduration }}&nbsp;Sekunden lang bestehen und erzeugen eine größere Explosion, die über {{ empowereddotduration }}&nbsp;Sekunden hinweg <magicDamage>{{ totalempowereddamage }}&nbsp;magischen Schaden</magicDamage> verursacht und Gegner um {{ empoweredslowamount }} <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verstärkter Schaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ empoweredbasedam<PERSON> }} -> {{ empoweredbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "MaokaiE.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MaokaiR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Maokai beschwört eine gewaltige Wand aus Gestrüpp und Dornen, die sich langsam vorwärts bewegt und alle getroffenen Gegner festhält und ihnen Schaden zufügt.", "tooltip": "Maokai beschwört eine gewaltige Welle aus Gestrüpp und Dornen, die Gegner {{ minrootduration }} bis {{ maxrootduration }}&nbsp;Sekunden lang <status>festhält</status> (erhöht sich mit der zurückgelegten Distanz) und ihnen <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt. Ein Championtreffer verleiht Maokai <speed>{{ movehaste*100 }}&nbsp;% Lauftempo</speed>, das über {{ hasteduration }}&nbsp;Sekunden hinweg abfällt. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Lauftempo", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movehaste*100.000000 }}&nbsp;% -> {{ movehastenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0.6, 0.6, 0.6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0.6", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "MaokaiR.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> au<PERSON>", "description": "Maokais normaler Angriff heilt ihn außerdem und verursacht zusätzlichen Schaden mit moderater Abklingzeit. Wenn <PERSON> eine Fähigkeit einsetzt oder von der Fähigkeit eines Gegners getroffen wird, wird diese Abklingzeit verringert.", "image": {"full": "Maokai_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}