{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ambessa": {"id": "<PERSON><PERSON><PERSON>", "key": "799", "name": "<PERSON><PERSON><PERSON>", "title": "Matriarche de guerre", "image": {"full": "Ambessa.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "799000", "num": 0, "name": "default", "chromas": false}, {"id": "799001", "num": 1, "name": "Ambessa de la Chasse éternelle", "chromas": true}], "lore": "Tous ceux qui connaissent le nom de Medarda savent qu'il faut craindre et respecter sa matriarche, Ambessa. En tant que générale noxienne, elle incarne la force impitoyable et la détermination sans peur. Son rôle de matriarche n'est pas bien différent. Elle se doit de faire preuve d'une grande ruse tout en ne laissant aucune place à l'échec ou à la compassion. Dédiée à la voie du Loup, Ambessa fera tout ce qui est en son pouvoir pour protéger l'héritage de sa famille, même si cela doit lui coûter l'amour de ses enfants.", "blurb": "Tous ceux qui connaissent le nom de Medarda savent qu'il faut craindre et respecter sa matriarche, Ambessa. En tant que générale noxienne, elle incarne la force impitoyable et la détermination sans peur. Son rôle de matriarche n'est pas bien différent...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Énergie", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 10}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 200, "mpperlevel": 0, "movespeed": 335, "armor": 35, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "AmbessaQ", "name": "Frappe fourbe / <PERSON>appe fracassante", "description": "Ambessa fait tourner ses doubles chiens-dragons en demi-cercle devant elle, infligeant des dégâts bonus aux ennemis touchés par les lames. Si elle touche un ennemi, elle transforme le prochain lancement de cette compétence pendant une courte période, ce qui lui permet de projeter ses doubles chiens-dragons en ligne droite devant elle, infligeant des dégâts bonus au premier ennemi touché.", "tooltip": "<spellActive>Frappe fourbe</spellActive> : <PERSON><PERSON><PERSON> fait tourner ses lames devant elle, infligeant <physicalDamage>{{ calc_damage_1_max }} + {{ calc_damage_1_percent_max }} des PV max en dégâts physiques</physicalDamage> aux ennemis touchés par le bord de l'attaque. Tous les autres ennemis subissent {{ calc_damage_1_min_ratio }} pts de dégâts. Toucher un ennemi prépare une <spellActive>Frappe fracassante</spellActive>.<br /><br /><spellActive>Frappe fracassante</spellActive> : Ambessa frappe le sol avec ses lames, infligeant <physicalDamage>{{ calc_damage_2_max }} + {{ calc_damage_2_percent_max }} des PV max en dégâts physiques</physicalDamage> au premier ennemi touché. Tous les autres ennemis subissent {{ calc_damage_2_min_ratio }} pts de dégâts.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Frappe fourbe | D<PERSON>gâts", "Frappe fourbe | PV max en dégâts", "Frappe fracassante | Dégâts", "Frappe fracassante | PV max en dégâts"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage_1_base }} -> {{ damage_1_baseNL }}", "{{ damage_1_percent*100.000000 }}% -> {{ damage_1_percentnl*100.000000 }}%", "{{ damage_2_base }} -> {{ damage_2_baseNL }}", "{{ damage_1_percent*100.000000 }}% -> {{ damage_1_percentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "AmbessaQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaW", "name": "Égide d<PERSON>", "description": "Ambessa gagne un bouclier, se protège brièvement puis frappe le sol pour infliger des dégâts aux ennemis proches. Pendant qu'elle se protège, si elle bloque les dégâts d'unités autres qu'un sbire, cette compétence inflige des dégâts bonus.", "tooltip": "Ambes<PERSON> gagne un <shield>bouclier de {{ calc_shield }} PV</shield> pendant {{ shield_duration }} sec et se protège pendant {{ buff_duration }} sec. Elle frappe ensuite le sol, infligeant <physicalDamage>{{ calc_damage_low }} pts de dégâts physiques</physicalDamage> aux ennemis proches. Ces dégâts sont augmentés à <physicalDamage>{{ calc_damage_high }} pts de dégâts physiques</physicalDamage> si elle a bloqué des dégâts d'un champion ennemi, d'un grand monstre ou d'un bâtiment.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaE", "name": "Lacération", "description": "Ambessa fait tourner ses doubles chiens-dragons autour d'elle, blessant et ralentissant les ennemis proches. Utiliser Ruée des chiens-dragons à partir de cette compétence déclenche une frappe supplémentaire à la fin de la ruée.", "tooltip": "Ambessa fait tourner ses chaînes autour d'elle, infligeant <physicalDamage>{{ calc_damage_flat }} pts de dégâts physiques</physicalDamage> aux ennemis touchés et les <status>ralentissant</status> de <status>{{ slow_amount*100 }}%</status> (diminue en {{ slow_duration }} sec). Utiliser <spellName>Ruée des chiens-dragons</spellName> à partir de cette compétence déclenche une frappe supplémentaire.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ratio de dégâts d'attaque"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage_flat_base }} -> {{ damage_flat_baseNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaE.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaR", "name": "Exécution publique", "description": "Ambessa se téléporte sur l'ennemi le plus éloigné sur une ligne droite de son choix, ce qui le neutralise à son arrivée. Elle le projette ensuite au sol pour lui infliger des dégâts et l'étourdir.", "tooltip": "<spellPassive>Passive</spellPassive> : <PERSON><PERSON><PERSON> gagne <armorPen>%i:scaleAPen% +{{ armor_penetration*100 }}% de pénétration d'armure</armorPen> et ses compétences <healing>lui rendent des PV équivalents à {{ calc_omnivamp }} des dégâts infligés</healing>.<br /><br /><spellActive>Active</spellActive> : Ambessa devient <attention>impossible à arrêter</attention> et se téléporte sur l'ennemi le plus éloigné sur une ligne droite, le <status>neutralisant</status> pendant {{ suppress_duration }} sec avant de le projeter au sol, ce qui lui inflige <physicalDamage>{{ calc_damage }} pts de dégâts physiques</physicalDamage> et l'<status>étourdit</status> pendant {{ stun_duration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Pénétration d'armure", "Soins", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ armor_penetration*100.000000 }}% -> {{ armor_penetrationnl*100.000000 }}%", "{{ omnivamp*100.000000 }}% -> {{ omnivampnl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AmbessaR.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}], "passive": {"name": "Ruée des chiens-dragons", "description": "Déclencher un déplacement ou une attaque lorsqu'elle lance une compétence permet à Ambessa de se ruer sur une courte distance une fois la compétence lancée. Cela octroie à sa prochaine attaque de la portée bonus, des dégâts bonus, de la vitesse d'attaque bonus, et lui rend de l'énergie.", "image": {"full": "Icon_Ambessa_Passive.Domina.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}