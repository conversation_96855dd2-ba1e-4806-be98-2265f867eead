{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nunu": {"id": "<PERSON><PERSON><PERSON>", "key": "20", "name": "Νούνου και Γ<PERSON>υ<PERSON>λ<PERSON>μπ", "title": "το Αγόρι και το Γέτι του", "image": {"full": "Nunu.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "20000", "num": 0, "name": "default", "chromas": false}, {"id": "20001", "num": 1, "name": "Σάσκ<PERSON><PERSON><PERSON><PERSON><PERSON>νου και Γουίλαμπ", "chromas": false}, {"id": "20002", "num": 2, "name": "Νούνου και Γου<PERSON>λαμπ, Βοηθοί του Άη Βασίλη", "chromas": false}, {"id": "20003", "num": 3, "name": "Τερατάκια Νούνου και Γουίλαμπ", "chromas": false}, {"id": "20004", "num": 4, "name": "Νούνου και <PERSON>", "chromas": false}, {"id": "20005", "num": 5, "name": "Νούνου και Γ<PERSON><PERSON><PERSON>μ<PERSON>, οι Κατεδαφιστές", "chromas": false}, {"id": "20006", "num": 6, "name": "TPA Νούνου και Γουίλαμπ", "chromas": false}, {"id": "20007", "num": 7, "name": "Ζόμπι Νούνου και Γουίλαμπ", "chromas": false}, {"id": "20008", "num": 8, "name": "Οριγκάμι Νούνου και Γουίλαμπ", "chromas": true}, {"id": "20016", "num": 16, "name": "Νούνου και Γουίλαμπ του Διαστημικού Ρυθμού", "chromas": true}, {"id": "20026", "num": 26, "name": "Νούνου και Μελισσαμπάκι", "chromas": true}, {"id": "20035", "num": 35, "name": "Κοσμικ<PERSON><PERSON> Σταυροφόροι Νούνου και Γουίλαμπ", "chromas": true}, {"id": "20044", "num": 44, "name": "Νούνου και Γουίλαμπ της Νύχτας Τρόμου", "chromas": true}], "lore": "Κ<PERSON><PERSON><PERSON><PERSON><PERSON>, ήτα<PERSON> <PERSON>να παιδί που ήθελε ν' αποδείξει ότι είναι ήρωας σκοτώνοντας ένα φοβερό τέρας, μέχρι που ανα<PERSON><PERSON><PERSON>υ<PERSON>ε ότι το τέρας αυτό, <PERSON><PERSON><PERSON> μο<PERSON>α<PERSON>, μα<PERSON><PERSON><PERSON><PERSON>, το μόνο που ήθελε ήταν έναν φίλο. Δεμένοι με μια πανάρχαια δύναμη και την κοινή αγάπη για τις χιονόμπαλες, ο Νούνου και ο Γουίλαμπ γυρίζουν πλέον όλο το Φρέλιορντ και κάνουν πραγματικότητα τις περιπέτειες της φαντασίας τους. Ελ<PERSON><PERSON>ζ<PERSON>υν ότι κάπου, κ<PERSON><PERSON>οτε, θα βρουν τη μητέρα του Νούνου. Αν μπορέσουν να τη σώσουν, ίσως καταφέρουν να γίνουν ήρωες τελικά...", "blurb": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ήταν ένα παιδί που ήθελε ν' αποδείξει ότι είναι ήρωας σκοτώνοντας ένα φοβερό τέρας, μέχρι που ανακ<PERSON><PERSON>υψε ότι το τέρας αυτό, <PERSON><PERSON><PERSON> μοναχ<PERSON><PERSON>, μαγι<PERSON><PERSON>, το μόνο που ήθελε ήταν έναν φίλο. Δεμένοι με μια πανάρχαια δύναμη και την κοινή αγάπη για τις...", "allytips": ["Η ικανότητα Καταβρόχθισμα επιτρέπει στον Νούνου να μένει σε μια λωρίδα ενάντια σε αντιπάλους που χρησιμοποιούν επιθέσεις από μακριά.", "Μπορείτε να διακόψετε νωρίς την ικανότητα Απόλυτο Μηδέν για μερική ζημιά, στην περίπτωση που ένας αντί<PERSON>αλος ετοιμάζεται να βγει εκτός εμβέλειας.", "Συχνά είναι χρήσιμο να καθυστερείτε τη χρήση της ικανότητας Απόλυτο Μηδέν, μέχρι να χρησιμοποιηθεί ο αρχικός γύρος εξουδετερώσεων. Προσπαθήστε να μείνετε πίσω πριν εμπλακείτε σε μια ομαδική μάχη."], "enemytips": ["Διακόψτε τη φόρτιση της ικανότητας Απόλυτο Μηδέν για να γλυτώσετε την ομάδα σας από τη συνολική ζημιά.", "Ένας σίγουρος τρόπος να ξεφύγετε από το Απόλυτο Μηδέν είναι το Φλας.", "Η Μεγαλύτερη Χιονόμπαλα Όλων των Εποχών κινείται πολύ γρήγορα αλλά δεν στρίβει εξίσου γρήγορα, οπότε μην προσπαθήσετε να της ξεφύγετε τρέχοντας σε ευθεία γραμμή.  Αντίθετα, κάντε απότομες, ξαφνικές στροφές."], "tags": ["Tank", "Mage"], "partype": "Μάνα", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 90, "mp": 280, "mpperlevel": 42, "movespeed": 345, "armor": 29, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "NunuQ", "name": "Καταβρόχθισμα", "description": "Ο Γουίλαμ<PERSON> δαγκώνει έναν υπηρέτη, τέρας ή αντίπαλο Ήρωα προκαλώντας του ζημιά και θεραπεύοντας τον εαυτό του.", "tooltip": "Ο Νούνου ζητά από τον Γουίλαμπ να δαγκώσει έναν εχθρό, προκαλώντας <trueDamage>{{ monsterminiondamage }} Πραγματική Ζημιά</trueDamage> και αναπληρώνοντας <healing>{{ monsterhealing }} Ζωή</healing> όταν η ικανότητα χρησιμοποιείται εναντίον υπηρέτη ή τέρατος της Ζούγκλας. Όταν χρησιμοποιείται εναντίον Ήρωα, προκαλεί <magicDamage>{{ totalchampiondamage }} Μαγική Ζημιά</magicDamage> και αναπληρώνει <healing>{{ championhealing }} Ζωή</healing>.<br /><br />Η <healing>Θεραπεία</healing> αυξάνεται κατά {{ lowhealthhealingscalar*100 }}%, όταν η Ζωή του Νούνου και του Γουίλαμπ είναι κάτω από {{ lowhealththreshhold*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά σε Τέρατα", "Ζημιά σε Ήρωες", "Θεραπεία", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ monsterminiondamage }} -> {{ monsterminiondamageNL }}", "{{ championdamage }} -> {{ championdamageNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Μάνα", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "NunuQ.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} Μάνα"}, {"id": "NunuW", "name": "Η Μεγαλύτερη Χιονόμπαλα Όλων των Εποχών!", "description": "Ο Γουίλαμπ δημιουργεί μια χιονόμπαλα που μεγαλώνει σε μέγεθος και ταχύτητα καθώς την τσουλάει.  Η χιονόμπαλα προκαλεί ζημιά και εκτοξεύει στον αέρα εχθρούς.", "tooltip": "Ο Νούνου και ο Γουίλαμπ δημιουργούν μια χιονόμπαλα που μεγαλώνει σε μέγεθος και ταχύτητα καθώς την κυλάνε. Όσο την κυλάνε κινούνται πιο αργά, όμως η ταχύτητα στροφής μπορεί να αυξηθεί, αν η κίνηση συνεχιστεί υπό την ίδια γωνία.<br /><br />Η χιονόμπαλα προκαλεί από <magicDamage>{{ noimpactsnowballdamage }}</magicDamage> έως <magicDamage>{{ maximumsnowballdamage }} Μαγική Ζημιά</magicDamage> και <status>Εκτοξεύει στον Αέρα</status> τους εχθρούς για διάστημα {{ baseknockupduration }} έως {{ maximumstunduration }} δευτ., όταν συγκρουστεί με έναν Ήρωα, μεγάλο τέρας ή τείχος. Αυτές οι τιμές προσαρμόζονται ανάλογα με την απόσταση κύλισης.<br /><br />Ο Νούνου και ο Γουίλαμπ μπορούν να κάνουν <recast>Νέα Χρήση</recast> για να απελευθερώσουν νωρίτερα τη χιονόμπαλα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική ζημιά", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "NunuW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuE", "name": "Καται<PERSON><PERSON>σ<PERSON><PERSON><PERSON>παλων", "description": "Ο Νούνου πετάει πολλές χιονόμπαλες που προκαλούν ζημιά στους αντιπάλους.  Όταν τελειώσει, ο Γουίλαμπ ακινητοποιεί Ήρωες ή μεγάλα τέρατα που έχουν δεχτεί χτύπημα από χιονόμπαλα.", "tooltip": "Ο Νούνου πετά τρεις χιονόμπαλες, προκαλώντας <magicDamage>{{ totalsnowballdamage }} Μαγική Ζημιά</magicDamage> ανά χιονόμπαλα, καθώ<PERSON> και <status>Επιβράδυνση</status> των εχθρών που χτυπούν και οι τρεις χιονόμπαλες κατά {{ slowamount*-100 }}% για {{ slowduration }} δευτ. Ο Νούνου μπορεί να κάνει <recast>Νέα Χρήση</recast> αυτής της ικανότητας έως και για δύο ακόμα φορές.<br /><br />Μετ<PERSON> από {{ totalspellduration }} δευτ., ο Νούνου προκαλεί <status>Ρίζωμα</status> σε όλους τους κοντινούς εχθρούς που είχαν <status>Επιβραδύνει</status> οι χιονόμπαλες για {{ rootduration }} δευτ., ενώ προκαλεί και επιπλέον <magicDamage>{{ totalrootdamage }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Κ<PERSON>σ<PERSON><PERSON>", "Επιβράδυνση Κίνησης", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "NunuE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuR", "name": "Απόλυτ<PERSON>", "description": "Οι Νούνου και Γουίλαμπ δημιουργούν μια πανίσχυρη χιονοθύελλα σε μια περιοχή που επιβραδύνει τους αντίπαλους και προκαλεί τεράστια ζημιά στο τέλος της ικανότητας.", "tooltip": "Ο Νούνου και ο Γουίλαμπ διοχετεύουν μια πανίσχυρη χιονοθύελλα για έως και {{ channelduration }} δευτ. Οι εχθροί που βρίσκονται μέσα της <status>Επιβραδύνονται</status> κατά {{ slowstartamount*-100 }}%, ποσοστό που αυξάνεται σε {{ maxslowamount*-100 }}% κατά τη διάρκεια της ικανότητας. Επίσης, ο Νούνου και ο Γουίλαμπ κερδίζουν <shield>{{ totalshieldamount }} Θωράκιση</shield> για τη διάρκεια της ικανότητας, η οποία στη συνέχεια μειώνεται σταδιακά μέσα σε {{ shielddecayduration }} δευτ.<br /><br />Όταν η χιονοθύελλα τελειώσει, ανατινάζεται, προκαλώντας έως <magicDamage>{{ maximumdamage }} Μαγική Ζημιά</magicDamage>, ανάλογα με τον χρόνο διοχέτευσης. Ο Νούνου και ο Γουίλαμπ μπορούν να κάνουν <recast>Νέα Χρήση</recast> για να λήξει η χιονοθύελλα νωρίτερα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Αντοχή Ασπίδας", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "NunuR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Κάλεσμα του Φρέλιορντ", "description": "Ο Νούνου αυξάνει την Ταχύτητα Επίθεσης και την Ταχύτητα Κίνησης του Γουίλαμπ και ενός κοντινού συμμάχου και κάνει τις βασικές επιθέσεις του Γουίλαμπ να προκαλούν ζημιά σε εχθρούς γύρω από τον στόχο.", "image": {"full": "NunuPassive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}