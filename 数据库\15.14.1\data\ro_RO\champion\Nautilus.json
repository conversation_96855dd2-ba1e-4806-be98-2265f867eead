{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nautilus": {"id": "Na<PERSON><PERSON>", "key": "111", "name": "Na<PERSON><PERSON>", "title": "titanul ad<PERSON><PERSON><PERSON>or", "image": {"full": "Nautilus.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "111000", "num": 0, "name": "default", "chromas": false}, {"id": "111001", "num": 1, "name": "Nautilus atlantul", "chromas": false}, {"id": "111002", "num": 2, "name": "Nautilus subteran", "chromas": false}, {"id": "111003", "num": 3, "name": "AstroNautilus", "chromas": true}, {"id": "111004", "num": 4, "name": "<PERSON><PERSON><PERSON> gardian", "chromas": false}, {"id": "111005", "num": 5, "name": "Na<PERSON>lus, distrugătorul de lumi", "chromas": false}, {"id": "111006", "num": 6, "name": "Nauti<PERSON>n<PERSON>", "chromas": false}, {"id": "111009", "num": 9, "name": "Nautilus din Pergamentele Shan Hai", "chromas": false}, {"id": "111018", "num": 18, "name": "Nautilus noapte de groază", "chromas": false}, {"id": "111027", "num": 27, "name": "<PERSON><PERSON><PERSON> paladin cosmic", "chromas": false}, {"id": "111036", "num": 36, "name": "Na<PERSON>lus <PERSON>", "chromas": false}], "lore": "O legendă singuratică, mai veche decât primele pontoane scufundate din Bilgewater, uriașul în armură numit Nautilus străbate apele întunecate de pe coasta Arhipelagului Flăcărilor Albastre. Mânat de o trădare uitată, lovește fără avertisment, rotindu-și ancora enormă ca să-i salveze pe cei sărmani și să-i înece pe cei lacomi. Se spune că îi atacă mai ales pe cei care uită să plătească ''jertfa din Bilgewater'', trăgându-i sub valuri și reamintindu-le că nimeni nu poate scăpa din adâncuri.", "blurb": "O legendă singuratică, mai veche decât primele pontoane scufundate din Bilgewater, uriașul în armură numit Nautilus străbate apele întunecate de pe coasta Arhipelagului Flăcărilor Albastre. Mânat de o trădare uitată, lovește fără avertisment, rotindu-și...", "allytips": ["Când pregătești ambuscade, țintește cu ''<PERSON><PERSON>are'' către terenul din apropiere și continuă cu ''Val de străpungere'' pentru o forță de lovire mai mare.", "''Valul de străpungere'' are o explozie întârziată la activare – îl poți folosi când te retragi sau când vin inamici spre tine pentru a-i pune pe fugă."], "enemytips": ["Dacă Nautilus foloseș<PERSON> ''Val de străpungere'' chiar lân<PERSON> tine, răm<PERSON>i pe loc până ce valul se potolește înainte să fugi. Dacă fugi prea devreme, vei intra drept în exploziile secundare, putând suferi daune suplimentare și efecte de încetinire.", "Cât Nautilus este protejat de scut, poate provoca daune AoE mari cu atacurile sale de bază – ar fi de preferat să distrugi scutul dacă ai timp."], "tags": ["Tank", "Support"], "partype": "Mană", "info": {"attack": 4, "defense": 6, "magic": 6, "difficulty": 6}, "stats": {"hp": 646, "hpperlevel": 100, "mp": 400, "mpperlevel": 47, "movespeed": 325, "armor": 39, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.65, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1, "attackspeed": 0.706}, "spells": [{"id": "NautilusAnchorDrag", "name": "<PERSON><PERSON>", "description": "Nautilus își aruncă ancora înainte.  Dacă ni<PERSON>te un inamic, el și adversarul sunt trași unul spre altul, iar ținta suferă daune magice.  Dacă se lovește de teren, îl trage pe Nautilus spre ea.", "tooltip": "Nautilus își aruncă ancora înainte. Dac<PERSON> nimerește un inamic, el și adversarul sunt trași unul spre altul, iar ț<PERSON>a suferă <magicDamage>{{ qdamagecalc }} daune magice</magicDamage> și este <status>amețită</status> pentru scurt timp. Dacă ancora nimerește o porțiune de teren, Nautilus se trage spre aceasta.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "0", "0.5", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "NautilusAnchorDrag.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusPiercingGaze", "name": "<PERSON><PERSON><PERSON> titanului", "description": "Nautilus primește un scut temporar.  Cât timp scutul rezistă, atacurile lui le provoacă daune în timp țintei și inamicilor din jur.", "tooltip": "Nautilus primește un <shield>scut în valoare de {{ shieldcalc }}</shield> timp de {{ shieldduration }} secunde. Cât timp <shield>scutul</shield> rezistă, atacurile lui Nautilus le provoacă <magicDamage>{{ dotdamagecalc }} daune magice</magicDamage> bonus de-a lungul a 2 secunde țintei și tuturor inamicilor din jur.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Valoarea scutului", "Daune magice", "% din viața maximă"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ dotdamagebase }} -> {{ dotdamagebaseNL }}", "{{ shieldhealthratio*100.000000 }}% -> {{ shieldhealthrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "NautilusPiercingGaze.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusSplashZone", "name": "Val de străpungere", "description": "Nautilus creează trei valuri care explodează în jurul lui. Fiecare explozie încetinește inamicii și le provoacă daune.", "tooltip": "Nautilus creează trei valuri care explodează în jurul lui, fiecare provocându-le <magicDamage>{{ damagecalc }} daune magice</magicDamage> inamicilor din zonă și <status>încetinindu-i</status> cu {{ slowpercent*100 }}%, valoare ce scade de-a lungul a {{ slowduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Cost de @AbilityResourceName@", "Daune", "Încetinire"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NautilusSplashZone.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusGrandLine", "name": "Grenadă submarină", "description": "Nautilus lansează o undă de șoc în pământ care urmărește un adversar. Unda de șoc brăzdează solul de deasupra sa, aruncându-i pe adversari în sus. Când ajunge la adversar, unda de șoc erupe, aruncându-și ținta în sus și amețind-o.", "tooltip": "Nautilus lansează o undă de șoc care urmărește un campion inamic, provocându-i <magicDamage>{{ primarytargetdamage }} daune magice</magicDamage>, <status>aruncându-l</status> <status>în sus</status> și <status>amețindu-l</status> timp de {{ stunduration }} secunde. Și ceilalți inamici loviți de unda de șoc sunt <status>aruncați în sus</status> și <status>amețiți</status>, suferind <magicDamage>{{ secondarytargetdamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune din străpungere", "Durată amețire:", "Daune explozie"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ primarydamage }} -> {{ primarydamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "NautilusGrandLine.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Lovitură năucitoare", "description": "Primul atac al lui Nautilus împotriva unei ținte îi provoacă daune fizice crescute și o țintuiește pentru scurt timp.", "image": {"full": "Nautilus_StaggeringBlow.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}