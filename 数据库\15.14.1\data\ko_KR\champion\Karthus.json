{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "카서스", "title": "죽음을 노래하는 자", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "유령 카서스", "chromas": false}, {"id": "30002", "num": 2, "name": "자유의 카서스상", "chromas": false}, {"id": "30003", "num": 3, "name": "사신 카서스", "chromas": true}, {"id": "30004", "num": 4, "name": "펜타킬 카서스", "chromas": false}, {"id": "30005", "num": 5, "name": "Fnatic 카서스", "chromas": false}, {"id": "30009", "num": 9, "name": "빛의 파괴자 카서스", "chromas": false}, {"id": "30010", "num": 10, "name": "지옥의 카서스", "chromas": true}, {"id": "30017", "num": 17, "name": "펜타킬 III: 사라진 양피지 카서스", "chromas": true}, {"id": "30026", "num": 26, "name": "나무정령 카서스", "chromas": true}], "lore": "망각을 부르는 자 카서스는 섬뜩한 노랫소리를 전주곡 삼아 악몽처럼 등장하는 언데드이다. 산 자는 영원히 죽지 않는 언데드를 두려워하나, 그는 언데드를 삶과 죽음의 완벽한 결합으로 오직 아름답고 순수하다 여긴다. 카서스는 언데드로서 존재하는 기쁨을 온 세계에 전하기 위해 그림자 군도로부터 출몰한다.", "blurb": "망각을 부르는 자 카서스는 섬뜩한 노랫소리를 전주곡 삼아 악몽처럼 등장하는 언데드이다. 산 자는 영원히 죽지 않는 언데드를 두려워하나, 그는 언데드를 삶과 죽음의 완벽한 결합으로 오직 아름답고 순수하다 여긴다. 카서스는 언데드로서 존재하는 기쁨을 온 세계에 전하기 위해 그림자 군도로부터 출몰한다.", "allytips": ["다른 공격로에 있는 아군과 상의하여 시기적절하게 진혼곡을 쓰면 다른 공격로에 있는 적을 처치할 수 있습니다.", "황폐화는 미니언 사냥과 적 챔피언 괴롭히기에 유용합니다."], "enemytips": ["카서스는 죽은 뒤 잠시 동안 스킬을 사용할 수 있으므로 시체에서 멀리 떨어지는 편이 좋습니다.", "진혼곡을 맞고 살아남으려면 항상 충분한 체력을 유지하십시오. 본진에 자주 돌아가는 경우가 있더라도 말입니다."], "tags": ["Mage"], "partype": "마나", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "황폐화", "description": "카서스가 특정 위치에 잠시 후 폭발을 일으켜 근처 적에게 피해를 입힙니다. 단일 대상에게는 더 큰 피해를 입힙니다.", "tooltip": "카서스가 마법으로 폭발을 일으켜 <magicDamage>{{ qdamage }}의 마법 피해</magicDamage>를 입힙니다. 하나의 적만 맞힐 경우 <magicDamage>{{ qsingletargetdamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "고통의 벽", "description": "카서스가 에너지를 빨아들이는 통행 가능한 벽을 생성합니다. 이 벽을 지나는 모든 적 유닛의 이동 속도와 마법 저항력이 일정 시간 감소합니다.", "tooltip": "카서스가 {{ e4 }}초 동안 유지되는 벽을 생성합니다. 벽을 지나는 적은 {{ e5 }}초간 <scaleMR>마법 저항력이 {{ e1 }}%</scaleMR> 감소하고 {{ e3 }}% <status>둔화</status>됩니다. 둔화 효과는 시간이 지나면서 점차 사라집니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["벽 길이", "이동속도 감소량"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "부패", "description": "카서스는 적에게서 마력을 훔쳐 적을 처치할 때마다 마나를 획득합니다. 또 카서스는 희생양의 영혼을 자신 주위에 둘러 적들에게 피해를 입힐 수도 있지만, 이럴 경우 마나가 매우 빨리 소모됩니다.", "tooltip": "<spellPassive>기본 지속 효과: </spellPassive>카서스가 적 유닛을 처치할 때마다 <scaleMana>{{ e2 }}의 마나</scaleMana>를 회복합니다.<br /><br /><toggle>활성화/비활성화: </toggle>카서스가 죽음의 영역을 생성해 근처 적들에게 초당 <magicDamage>{{ totaldps }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["초당 피해량", "마나 회복", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "초당 마나 {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "진혼곡", "description": "카서스가 3초 동안 정신 집중을 마치면 모든 적 챔피언이 피해를 입습니다.", "tooltip": "카서스가 3초 동안 정신 집중을 하여 거리와 관계없이 모든 적 챔피언에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "죽음 극복", "description": "카서스가 죽으면 영혼이 되어 스킬을 사용할 수 있습니다.", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}