{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taric": {"id": "<PERSON><PERSON>", "key": "44", "name": "<PERSON><PERSON>", "title": "lo scudo di Valoran", "image": {"full": "Taric.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "44000", "num": 0, "name": "default", "chromas": false}, {"id": "44001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "44002", "num": 2, "name": "Taric Armat<PERSON> della Quinta Era", "chromas": false}, {"id": "44003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "44004", "num": 4, "name": "Taric Festa in Piscina", "chromas": true}, {"id": "44009", "num": 9, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "44018", "num": 18, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "44027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON>ric è l'incarnazione della Protezione ed è dotato di incredibili poteri in quanto guardiano della vita, dell'amore e della bellezza di tutta Runeterra. Disonorato per inadempienza e bandito dalla sua patria, <PERSON><PERSON><PERSON>, <PERSON><PERSON> scalò il Monte Targon in cerca di redenzione e trovò il suo vero destino nascosto fra le stelle. Pervaso dal potere dell'antico Targon, lo Scudo di Valoran ora si erge sempre vigile contro l'insidiosa corruzione del Vuoto.", "blurb": "<PERSON><PERSON> è l'incarnazione della Protezione ed è dotato di incredibili poteri in quanto guardiano della vita, dell'amore e della bellezza di tutta Runeterra. Disonorato per inadempienza e bandito dalla sua patria, <PERSON><PERSON><PERSON>, <PERSON><PERSON> s<PERSON> il Monte Targon in...", "allytips": ["La riduzione della ricarica di Spavalderia ha un'ottima sinergia con oggetti come Fulcro dei geli, Guanto del gelo e Corazza spirituale, che offrono un effetto simile, rendendoli particolarmente potenti su Taric.", "Usare Tocco delle stelle con il minimo delle cariche renderà gli effetti di guarigione meno efficienti a livello di mana, ma potrà aumentare di molto i danni inflitti nel tempo da Taric tramite Spavalderia.", "Anzi<PERSON><PERSON> conservare Splendore cosmico e rischiare che un alleato venga ucciso durante quel periodo di ritardo, può essere più vantaggioso lanciarla non appena si è sicuri dell'imminente inizio di uno scontro tra squadre."], "enemytips": ["La suprema di Taric, <PERSON><PERSON><PERSON><PERSON> cosmico, ha effetto dopo un lungo ritardo. Valuta velocemente se liberarti dallo scontro o tentare di uccidere i suoi alleati prima che diventi attiva.", "<PERSON><PERSON> può ridurre la ricarica della sua abilità attaccando i nemici con Spavalderia. Cerca di tenerlo a distanza nei combattimenti a squadre e attaccalo quando prova ad avvicinarsi all'ondata di minion sulla corsia."], "tags": ["Support", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 40, "armorperlevel": 4.3, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "TaricQ", "name": "Tocco delle stelle", "description": "Cura gli alleati vicini in base al numero di cariche accumulate. Gli attacchi potenziati da Spavalderia conferiscono una carica di Tocco delle stelle.", "tooltip": "<spellPassive>Passiva:</spellPassive> guadagna una carica (massimo {{ e6 }}) ogni {{ stackcooldown }} secondi e quando mette a segno un attacco base con <spellName>Spavalderia</spellName>.<br /><br /><spellActive>Attiva:</spellActive> consuma tutte le cariche per ripristinare <healing>{{ healingperstack }} salute</healing> per carica ai campioni alleati vicini (<healing>{{ maxstackhealing }}</healing> a {{ e6 }} cariche).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cariche massime", "Guarigione massima"], "effect": ["{{ e6 }} -> {{ e6NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [1, 1, 1, 1, 1], [0.15, 0.3, 0.45, 0.6, 0.75], [0.75, 1.5, 2.25, 3, 3.75], [25, 50, 75, 100, 125], [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "1", "0.15/0.3/0.45/0.6/0.75", "0.75/1.5/2.25/3/3.75", "25/50/75/100/125", "1/2/3/4/5", "0", "0", "0", "0"], "vars": [], "costType": " mana, tutte le cariche", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "TaricQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} mana, tutte le cariche"}, {"id": "TaricW", "name": "Bastione", "description": "La passiva di Bastione aumenta l'armatura di Taric e dei campioni alleati.<br><br>L'attiva protegge un alleato e crea con esso un legame che durerà fino a quando non si allontana da Taric. Le abilità di Taric vengono lanciate anche dal compagno di Bastione.", "tooltip": "<spellPassive>Passiva:</spellPassive> <PERSON><PERSON> ottiene <scaleArmor>{{ bonusarmor }} armatura</scaleArmor> e crea un legame tra se stesso e l'alleato vincolato da questa abilità. <PERSON><PERSON> sono vicini tra loro, <PERSON><PERSON> lancia tutte le sue abilità anche dall'alleato collegato, che inoltre ottiene <scaleArmor>{{ bonusarmor }} armatura</scaleArmor>.<br /><br /><spellPassive>Attiva:</spellPassive> Taric si lega ad un campione alleato, conferendogli uno <shield>scudo per un {{ e2 }}% della salute massima</shield> dell'alleato per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Armatura passiva", "Rapporto scudo"], "effect": ["{{ armorbonuspercentage*100.000000 }}% -> {{ armorbonuspercentagenl*100.000000 }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [7, 8, 9, 10, 11], [2.5, 2.5, 2.5, 2.5, 2.5], [1000, 1000, 1000, 1000, 1000], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "7/8/9/10/11", "2.5", "1000", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "TaricW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TaricE", "name": "<PERSON> a<PERSON>", "description": "<PERSON><PERSON> prepara un fascio di luce stellare che, dopo un breve ritardo, infligge danni magici e stordisce i nemici.", "tooltip": "<PERSON>ric proietta un fascio di luce stellare che esplode dopo {{ e3 }} secondo infliggendo <magicDamage> {{ totaldamage }} danni magici</magicDamage> e <status>stordendo</status> i nemici per {{ e2 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [90, 130, 170, 210, 250], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/130/170/210/250", "1.5", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "TaricE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TaricR", "name": "Splendore cosmico", "description": "<PERSON><PERSON> un ritardo, rilascia energia cosmica sui campioni alleati vicini, rendendoli invulnerabili per un breve periodo di tempo.", "tooltip": "Taric invoca la protezione celeste. Dopo {{ initialdelay }} secondi, i campioni alleati vicini diventano invulnerabili per {{ invulnduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "TaricR.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Spavalderia", "description": "I lanci delle abilità potenziano i prossimi 2 attacchi base di Taric consentendogli di infliggere danni magici bonus, ridurre la ricarica delle abilità base e attaccare in rapida successione.", "image": {"full": "Taric_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}