{"type": "language", "version": "15.14.1", "data": {"Back": "Zurück", "Continue": "<PERSON><PERSON>", "Language": "<PERSON><PERSON><PERSON>", "ItemInfo": "Informationen", "NextRank_": "Nächster Rang:", "Rank_": "Rang:", "PlayingAs": "Spielt als", "PlayingAgainst": "Spiel gegen", "CD_": "Abklingzeit:", "Range": "Reichweite", "Range_": "Reichweite:", "Details_": "Einzelheiten:", "PrimaryRole": "Primärrollen", "mobileCompanion": "Begleiter", "mobileForum": "Forum", "mobileFriends": "Freunde", "mobilePleaseWait": "Bitte warten ...", "mobileNews": "Neuigkeiten", "modeClassic": "Klassisch", "modeOdin": "Eindeutig nicht Dominion", "modeAram": "ARAM", "modeTutorial": "Tutorial", "modeOneforall": "FRONTEND_oneforall_game_mode_name", "modeFirstblood": "FRONTEND_firstblood_game_mode_name", "mode6v6": "FRONTEND_6v6_game_mode_name", "modeCof": "FRONTEND_cof_game_mode_name", "Map1": "Kluft der Beschwörer", "Map8": "Kristallnarbe", "Map10": "Gewundener Wald", "Map12": "Heulende Schlucht", "categoryChampion": "Champions", "categoryItem": "Gegenstände", "categoryMastery": "Meisterschaft", "categoryRune": "<PERSON><PERSON>", "categorySummoner": "Beschwörerzauber", "Gold": "Gold", "Level": "Stuf<PERSON>", "Abilities": "Fähigkeiten", "ChampionInfo": "Champion-Info", "Lore": "Geschichte", "Stats": "<PERSON><PERSON>", "Tips": "<PERSON><PERSON><PERSON>", "statAbility": "Fähigkeiten", "statAttack": "Angriff", "statDefense": "Abwehr", "statDifficulty": "Schwierigkeit", "statUtility": "Wissen", "Assassin": "Assassine", "Fighter": "<PERSON><PERSON><PERSON><PERSON>", "Marksman": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mage": "Magier", "Support": "Supporter", "Tank": "Tank", "spells_Self": "Se<PERSON>bst", "spells_target_0": "Se<PERSON>bst", "spells_target_1": "<PERSON><PERSON>", "spells_target_2": "Wirkbereich", "spells_target_3": "<PERSON><PERSON>", "spells_target_4": "Umgebung", "spells_target_5": "Variabel", "spells_target_6": "Ort", "spells_target_7": "<PERSON><PERSON><PERSON>", "spells_target_8": "Vektorrichtung", "spells_target_100": "Global", "AllItems": "Alle Gegenstände", "Armor": "Rüstung", "Attack": "Angriff", "AttackSpeed": "Angriffstempo", "Consumable": "Tränke/Augen", "CooldownReduction": "Abklingzeitverringerung", "CriticalStrike": "Kritische Treffer", "Damage": "<PERSON><PERSON><PERSON>", "Defense": "Abwehr", "Health": "<PERSON><PERSON>", "HealthRegen": "Lebensregeneration", "LifeSteal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Magic": "<PERSON><PERSON>", "Mana": "<PERSON><PERSON>", "ManaRegen": "Manaregeneration", "Movement": "Bewegung", "SpellBlock": "Magieresistenz", "SpellDamage": "Fähigkeitsstärke", "Boots": "<PERSON><PERSON><PERSON>", "NonbootsMovement": "Lauftempo", "Tenacity": "Zähigkeit", "SpellVamp": "Zaubervampir", "GoldPer": "<PERSON><PERSON><PERSON><PERSON>", "Slow": "Verlangsamen", "Aura": "<PERSON>ra", "Active": "Aktiv", "MagicPenetration": "Magiedurchdringung", "ArmorPenetration": "Rüstungsdurchdringung", "colloq_Armor": ";armour;rüstung", "colloq_Attack": ";", "colloq_AttackSpeed": ";as", "colloq_Consumables": ";", "colloq_CriticalStrike": ";", "colloq_Damage": ";ad", "colloq_Defense": ";", "colloq_Health": ";hp", "colloq_HealthRegen": ";hpregen;hp5", "colloq_LifeSteal": ";lifesteal", "colloq_Magic": ";", "colloq_Mana": ";mp", "colloq_ManaRegen": ";mpregen;mp5", "colloq_Movement": ";movespeed", "colloq_SpellBlock": ";mr", "colloq_SpellDamage": ";ap", "colloq_Consumable": ";", "colloq_Boots": ";", "colloq_NonbootsMovement": ";", "colloq_CooldownReduction": ";cdr", "colloq_Tenacity": ";", "colloq_SpellVamp": ";spellvamp", "colloq_GoldPer": ";gp10", "colloq_Slow": ";", "colloq_Aura": ";", "colloq_Active": ";", "colloq_MagicPenetration": ";", "colloq_ArmorPenetration": ";", "RecommendedItems": "Empfohlene Gegenstände", "recommended_starting": "Start-Gegenstände", "recommended_essential": "Unerlässliche Gegenstände", "recommended_offensive": "Offensive Gegenstände", "recommended_defensive": "Defensive Gegenstände", "recommended_consumables": "Tränke/Augen", "Require_": "Erfordert:", "Cost_": "Kaufpreis:", "OriginalCost_": "Ursprünglicher Kaufpreis:", "SellsFor_": "Verkaufserlös: ", "UpgradeCost_": "Aufwertungskosten:", "Builds_": "<PERSON><PERSON> <PERSON>:", "ButtonBuy": "KAUFEN", "ButtonSell": "VERKAUFEN", "SpecialRecipeSmall": "*", "SpecialRecipeLarge": "Spezial", "FlatArmorMod": "Rüstung", "FlatAttackSpeedMod": "Angriffstempo", "FlatBlockMod": "Block", "FlatCritChanceMod": "Chance auf kritische Treffer", "FlatCritDamageMod": "Kritischer Schaden", "FlatEnergyPoolMod": "Energie", "FlatEnergyRegenMod": "Energieregeneration alle 5 s", "FlatEXPBonus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FlatHPPoolMod": "<PERSON><PERSON>", "FlatHPRegenMod": "Lebensregeneration alle 5 s", "FlatMagicDamageMod": "Fähigkeitsstärke", "FlatMovementSpeedMod": "Lauftempo", "FlatMPPoolMod": "<PERSON><PERSON>", "FlatMPRegenMod": "Manaregeneration alle 5 s", "FlatPhysicalDamageMod": "<PERSON><PERSON>", "FlatSpellBlockMod": "Magieresistenz", "PercentArmorMod": "Rüstung %", "PercentAttackSpeedMod": "Angriffstempo %", "PercentBlockMod": "Block %", "PercentCritChanceMod": "% Chance auf kritische Treffer", "PercentCritDamageMod": "Kritischer Schaden %", "PercentDodgeMod": "Ausweichen %", "PercentEXPBonus": "Erfahrungsbonus %", "PercentHPPoolMod": "<PERSON><PERSON> %", "PercentHPRegenMod": "Leben % alle 5 s", "PercentMagicDamageMod": "Max. Fähigkeitsstärke %", "PercentMovementSpeedMod": "Lauftempo %", "PercentMPPoolMod": "<PERSON>. <PERSON> %", "PercentMPRegenMod": "Mana % alle 5 s", "PercentPhysicalDamageMod": "Normaler Schaden %", "PercentSpellBlockMod": "Magieresistenz %", "rFlatArmorModPerLevel": "Rüstung auf Stufe 18", "rFlatArmorPenetrationMod": "Rüstungsdur.", "rFlatArmorPenetrationModPerLevel": "Rüstungsdur. auf Stufe 18", "rFlatCritChanceModPerLevel": "Chance krit. Treffer auf Stufe 18", "rFlatCritDamageModPerLevel": "Kritischer Schaden auf Stufe 18", "rFlatDodgeMod": "Ausweichen", "rFlatDodgeModPerLevel": "Ausweichen auf Stufe 18", "rFlatEnergyModPerLevel": "Energie auf Stufe 18", "rFlatEnergyRegenModPerLevel": "Energieregen. alle 5 s auf Stufe 18", "rFlatGoldPer10Mod": "Gold pro 10", "rFlatHPModPerLevel": "Leben auf Stufe 18", "rFlatHPRegenModPerLevel": "Lebensregen. alle 5 s auf Stufe 18", "rFlatMagicDamageModPerLevel": "Fähigkeitsstärke auf Stufe 18", "rFlatMagicPenetrationMod": "Magie<PERSON><PERSON><PERSON>.", "rFlatMagicPenetrationModPerLevel": "Magiedurchdr. auf Stufe 18", "rFlatMovementSpeedModPerLevel": "Lauftempo auf Stufe 18", "rFlatMPModPerLevel": "Mana auf Stufe 18", "rFlatMPRegenModPerLevel": "Manaregen. alle 5 s auf Stufe 18", "rFlatPhysicalDamageModPerLevel": "Normaler Schaden auf Stufe 18", "rFlatSpellBlockModPerLevel": "Magieresistenz auf Stufe 18", "rFlatTimeDeadMod": "Wartezeit nach Tod", "rFlatTimeDeadModPerLevel": "Wartezeit nach Tod auf Stufe 18", "rPercentArmorPenetrationMod": "Rüstungsdur. %", "rPercentArmorPenetrationModPerLevel": "Rüstungsdur. % auf Stufe 18", "rPercentAttackSpeedModPerLevel": "Angriffstempo % auf Stufe 18", "rPercentCooldownMod": "Abklingzeit %", "rPercentCooldownModPerLevel": "Abklingzeit % auf Stufe 18", "rPercentMagicPenetrationMod": "Magiedurchdr. %", "rPercentMagicPenetrationModPerLevel": "Magiedurchdr. % auf Stufe 18", "rPercentMovementSpeedModPerLevel": "Lauftempo % auf Stufe 18", "rPercentTimeDeadMod": "Wartezeit nach Tod %", "rPercentTimeDeadModPerLevel": "Wartezeit nach Tod % pro Stufe", "PercentLifeStealMod": "Lebensraubbonus %", "PercentSpellVampMod": "Zaubervampirbonus %", "masteryFerocity": "Wildheit", "masteryCunning": "Gerissenheit", "masteryResolve": "Entschlossenheit", "native_﻿ar": "العربية", "native_bg": "български език", "native_cs": "čeština", "native_de": "De<PERSON>ch", "native_el": "ελληνικά", "native_en": "English", "native_es": "español", "native_fr": "français", "native_hu": "magyar", "native_id": "Bahasa Indonesia", "native_it": "Italiano", "native_ja": "日本語", "native_ko": "조선말", "native_nl": "Nederlands", "native_pl": "język polski", "native_pt": "português", "native_ro": "română", "native_ru": "русский язык", "native_th": "ภาษาไทย", "native_tr": "Türkçe", "native_vi": "Tiếng <PERSON>", "native_zh": "汉语", "native_zh_CN": "简体字", "native_zh_MY": "简体字", "native_zh_TW": "繁體字"}, "tree": {"searchKeyIgnore": "", "searchKeyRemap": [{"k": "A", "v": "AÄä"}, {"k": "O", "v": "OÖö"}, {"k": "U", "v": "UÜü"}, {"k": "SS", "v": "ß"}]}}