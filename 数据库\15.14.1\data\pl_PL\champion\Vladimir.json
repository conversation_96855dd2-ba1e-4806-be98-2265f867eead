{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vladimir": {"id": "Vladimir", "key": "8", "name": "Vladimir", "title": "Krwiożerczy Żniwiarz", "image": {"full": "Vladimir.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "8000", "num": 0, "name": "default", "chromas": false}, {"id": "8001", "num": 1, "name": "Hrab<PERSON> Vladimir", "chromas": false}, {"id": "8002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "8003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "8004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "8005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "8006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "8007", "num": 7, "name": "Licealista Vladimir", "chromas": false}, {"id": "8008", "num": 8, "name": "<PERSON> z Mrocznych Wód", "chromas": true}, {"id": "8014", "num": 14, "name": "<PERSON>", "chromas": true}, {"id": "8021", "num": 21, "name": "Kosmiczny Pożeracz Vladimir", "chromas": true}, {"id": "8030", "num": 30, "name": "Kawiarniany Cukiereczek Vladimir", "chromas": true}, {"id": "8039", "num": 39, "name": "<PERSON>", "chromas": true}, {"id": "8048", "num": 48, "name": "<PERSON> Czarne<PERSON>", "chromas": false}], "lore": "Potwór pragnący krwi śmiertelników, Vladimir wpływa na sprawy Noxusu od zarania imperium. Poza nienaturalnym wydłużaniem swojego życia, jego mistrzowskie władanie krwią pozwala mu na kontrolowanie umysłów i ciał innych, jakby były jego własnymi. Umożliwiło mu to stworzenie fanatycznego kultu własnej osoby na krzykliwych salonach noxiańskiej arystokracji. Ta zdolność potrafi również sprawić, że w najciemniejszych zaułkach jego wrogowie wykrwawiają się na śmierć.", "blurb": "Potwór pragnący krwi śmiertelników, Vladimir wpływa na sprawy Noxusu od zarania imperium. Poza nienaturalnym wydłużaniem swojego życia, jego mistrzowskie władanie krwią pozwala mu na kontrolowanie umysłów i ciał innych, jakby były jego własnymi...", "allytips": ["Transfuzja zadaje natychmiastowe obrażenia, zani<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> z niej jedną z najlepszych umiejętności do zabijania w grze.", "Użyj Hemoplagi tak, aby trafiła jak najwięcej jednostek.", "Kałuża Krwi niszczy nadlatujące pociski, pozwalając na uniknięcie osłabień."], "enemytips": ["Postaraj się zlikwidować Vladimira zanim dojdzie do detonacji Hemoplagi, g<PERSON><PERSON> zostanie uleczony za każdego trafionego nią bohatera.", "Zmuszenie Vladimira do użycia Kałuży Krwi na początku walki maksymalnie zwiększy koszt życia tej umiejętności.", "<PERSON><PERSON><PERSON><PERSON><PERSON>, kt<PERSON>re przeciwdziała<PERSON><PERSON> kumulo<PERSON>iu zdrowia, takie jak Udręka Liandry'ego lub Ostrze Zniszczonego Króla, są bardzo skuteczne przeciwko Vladimirowi."], "tags": ["Mage", "Fighter"], "partype": "Krwiożerczy Pęd", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 7}, "stats": {"hp": 607, "hpperlevel": 110, "mp": 2, "mpperlevel": 0, "movespeed": 330, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "VladimirQ", "name": "Transfuzja", "description": "Vladimir kradnie zdrowie wybranego wroga. Kiedy Vladimir zaspokoi swój głód krwi, Transfuzja na chwilę zyskuje znacznie zwiększone obrażenia i leczenie.", "tooltip": "<PERSON> wysysa życie z celu, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ basedamagetooltip }} pkt. obrażeń magicznych</magicDamage> i przywracając sobie <healing>{{ basehealtooltip }} pkt. zdrowia</healing>. Po dwukrotnym użyciu tej umiejętności Vladimir zyskuje <speed>{{ movementspeedonq2 }}% prędkości ruchu</speed> na 0,5 sek. i wzmacnia kolejne użycie tej umiejętności na {{ e8 }} sek.<br /><br />Wzmocniona wersja tej umiejętności zadaje <magicDamage>{{ empowereddamagetooltip }} pkt. obrażeń magicznych</magicDamage> i przywraca dodatkowo <healing>{{ empoweredhealtooltip }} pkt. zdrowia + {{ empoweredhealpercenttooltip }} jego brakującego zdrowia</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Leczenie", "Czas odnowienia"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 7.9, 6.8, 5.7, 4.6], "cooldownBurn": "9/7.9/6.8/5.7/4.6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 100, 120, 140, 160], [20, 25, 30, 35, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0.04, 0.04, 0.04, 0.04, 0.04], [85, 85, 85, 85, 85], [2.5, 2.5, 2.5, 2.5, 2.5], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/100/120/140/160", "20/25/30/35/40", "0", "0", "5", "0.04", "85", "2.5", "35", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "VladimirSanguinePool", "name": "Kałuża Krwi", "description": "Vladimir rozpływa się w kału<PERSON><PERSON> krwi, <PERSON><PERSON><PERSON><PERSON> czemu przez 2 sekundy nie można go obrać za cel. Poza tym wrogowie stojący w kałuży zostają spowolnieni, a Vladimir wysysa z nich życie.", "tooltip": "Vladimir na 2 sek. rozpływa się w kału<PERSON><PERSON> krwi, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <speed>{{ hasteboost*100 }}% zanikającej prędkości ruchu</speed> na {{ hasteduration }} sek. <PERSON><PERSON> <keyword>prz<PERSON><PERSON><PERSON> przez jed<PERSON>ki</keyword> i <keyword>nie mo<PERSON><PERSON> obra<PERSON> go na cel</keyword>, a ponadto <status>spowalnia</status> wrogów znajdujących się w kałuży o {{ movespeedmod*-100 }}%.<br /><br />Podczas trwania tej umiejętności Vladimir zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i przywraca sobie <healing>{{ totalheal }} pkt. zdrowia</healing> za każdego wroga objętego jej działaniem.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 25, 22, 19, 16], "cooldownBurn": "28/25/22/19/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% aktualnego zdrowia ( pkt.).", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "VladimirSanguinePool.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "Kosztuje {{ healthcost*100 }}% aktualnego zdrowia ( pkt.)."}, {"id": "VladimirE", "name": "Przypływ Krwi", "description": "Vladimir płaci własnym zdrowiem, a<PERSON> <PERSON><PERSON><PERSON><PERSON> zbiornik krwi. Opróżniając go, zadaje obrażenia na obszarze wokół siebie, ale efekt może zostać zablokowany przez wrogie jednostki.", "tooltip": "<charge>Ładowanie: </charge><PERSON> zbiornik krwi, co kosztuje go do <span class=\"colorCC3300\">{{ chargehealthtooltip }} pkt. zdrowia</span>. Po pełnym naładowaniu Vladimir zostaje <status>spowolniony</status> o 20%.<br /><br /><release>Wypuszczenie: </release>Vladimir wyrzuca z siebie strumień krwi w kierunku pobliskich wrogów, zadając <magicDamage>{{ mindamagetooltip }}</magicDamage>-<magicDamage>{{ maxdamagetooltip }} pkt. obrażeń magicznych</magicDamage> w zależności od czasu ładowania. Je<PERSON><PERSON> umiej<PERSON>tność ta była ładowana przez co najmniej 1 sek., <status>spowolni</status> rów<PERSON>ż cele o {{ slowpercent }}% na 0,5 sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Minimalne obrażenia", "Maksymalne obrażenia", "Spowolnienie", "Czas odnowienia"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e0 }} -> {{ e0NL }}", "{{ e9 }}% -> {{ e9NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11, 9, 7, 5], "cooldownBurn": "13/11/9/7/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [30, 45, 60, 75, 90], [6, 6, 6, 6, 6], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [40, 45, 50, 55, 60], [60, 90, 120, 150, 180]], "effectBurn": [null, "0", "8", "30/45/60/75/90", "6", "150", "0", "1.5", "1", "40/45/50/55/60", "60/90/120/150/180"], "vars": [], "costType": "% maksymalnego zdrowia ({{ chargehealthtooltip }} pkt.).", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "Koszt ładowania: {{ e2 }}% maksymalnego zdrowia ({{ chargehealthtooltip }} pkt.)."}, {"id": "VladimirHemoplague", "name": "Hemoplaga", "description": "Vladimir zaraża pewien obszar wirulentną plagą. Dotknięci nią wrogowie otrzymują zwiększone obrażenia w trakcie jej działania. Po kilku sekundach Hemoplaga zadaje obrażenia magiczne zarażonym wrogom i leczy Vladimira za każdego trafionego wrogiego bohatera.", "tooltip": "<PERSON> w<PERSON> plagę, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że jej ofiary otrzymują obrażenia zwiększone o {{ e2 }}% ze wszys<PERSON><PERSON><PERSON> źró<PERSON>ł przez {{ e4 }} sek. <PERSON><PERSON>, <PERSON> <magicDamage>{{ damage }} pkt. obrażeń magicznych</magicDamage> wszystkim zainfe<PERSON> celom. Vladimir przywraca sobie <healing>{{ damage }} pkt. zdrowia</healing>, jeśli trafi bohatera, i dodatkowe <healing>{{ secondaryhealingtooltip }} pkt. zdrowia</healing> za każdego kolejnego trafionego bohatera.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [10, 10, 10], [100, 100, 100], [4, 4, 4], [40, 40, 40], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "10", "100", "4", "40", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "VladimirHemoplague.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "Darmowa"}], "passive": {"name": "Krwiożerczy Pakt", "description": "Każde 30 pkt. dodatkowego zdrowia daje Vladimirowi 1 pkt. mocy umiejętności, a każdy 1 pkt. mocy umiejętności — 1,6 pkt. zdrowia (efekt nie nakłada się na siebie).", "image": {"full": "VladimirP.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}