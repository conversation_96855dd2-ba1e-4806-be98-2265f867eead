{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Graves": {"id": "<PERSON>", "key": "104", "name": "<PERSON>", "title": "der Gesetzlose", "image": {"full": "Graves.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "104000", "num": 0, "name": "default", "chromas": false}, {"id": "104001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "104002", "num": 2, "name": "Ausbruch-Graves", "chromas": false}, {"id": "104003", "num": 3, "name": "Gangster<PERSON><PERSON>", "chromas": false}, {"id": "104004", "num": 4, "name": "Riot-Graves", "chromas": false}, {"id": "104005", "num": 5, "name": "Poolparty-Graves", "chromas": true}, {"id": "104006", "num": 6, "name": "Raufbold-Graves", "chromas": false}, {"id": "104007", "num": 7, "name": "Schneetag<PERSON>Graves", "chromas": true}, {"id": "104014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "104018", "num": 18, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "104025", "num": 25, "name": "Academia-Professor <PERSON>", "chromas": true}, {"id": "104035", "num": 35, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "104042", "num": 42, "name": "EDG-Graves", "chromas": true}, {"id": "104045", "num": 45, "name": "Porzellan-Graves", "chromas": false}], "lore": "<PERSON> ist ein ber<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> und <PERSON><PERSON>. Er steht überall auf der Fahndungsliste – in jedem <PERSON>, jeder <PERSON> und jede<PERSON>, das er je besucht hat. Trotz seines explosiven Gemüts besitzt er einen strikten Sinn für kriminelle Ehre, die er mit seiner doppelläufigen Schrotflinte Destiny zu verteidigen und durchzusetzen weiß. In den letzten Jahren hat er die problematische Beziehung zu Twisted Fate wieder in Ordnung gebracht und zusammen machen sie die Schattenseite von Bilgewasser unsicher.", "blurb": "<PERSON> ist ein ber<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> und <PERSON><PERSON>. Er steht überall auf der Fahndungsliste – in jedem <PERSON>, jeder St<PERSON> und jede<PERSON> Reich, das er je besucht hat. Trotz seines explosiven Gemüts besitzt er einen strikten Sinn für kriminelle Ehre...", "allytips": ["„Nebelwand“ kann sowohl zur Flucht als auch zur Vorbereitung eines Kills eingesetzt werden.", "„Schnelles Ziehen“ kann genutzt werden, um in Reichweite für seine normalen Angriffe und „Endstation“ zu kommen."], "enemytips": ["Graves verursacht fast nur normalen Schaden, weshalb Rüstung einen effektiven Konter darstellt.", "Verlässt man den Wirkbereich von „Nebelwand“, enden dessen negative Auswirkungen sofort."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 325, "mpperlevel": 40, "movespeed": 340, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 425, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.475}, "spells": [{"id": "GravesQLineSpell", "name": "Endstation", "description": "Graves feuert eine explosive Patrone ab, die nach 1&nbsp;<PERSON><PERSON><PERSON> oder, wenn sie auf Terrain trifft, deton<PERSON>t.", "tooltip": "Graves feuert eine explosive Patrone ab, die <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht. Nach 1&nbsp;Sekunde oder bei Kollision mit Terrain explodiert sie, wodurch sie erneut entlang ihrer Flugstrecke und an Gegnern in der Nähe der Explosion <physicalDamage>{{ totaldetonationdamage }}&nbsp;normalen Schaden</physicalDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Explosionsschaden", "Angriffsschaden bei Detonation – Skalierung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedetonationdamage }} -> {{ basedetonationdamageNL }}", "{{ baddetonationratio*100.000000 }}&nbsp;% -> {{ baddetonationrationl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.25, 9.5, 7.75, 6], "cooldownBurn": "13/11.25/9.5/7.75/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "GravesQLineSpell.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesSmokeGrenade", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Graves feuert einen Rauchkanister in den gewählten Bereich, der ihn in <PERSON>uch e<PERSON>hüllt und die Sichtweite verringert. <PERSON><PERSON><PERSON>, die vom ursprünglichen Aufprall getroffen werden, erleiden magischen Schaden und werden kurzzeitig verlangsamt.", "tooltip": "Graves erschafft 4&nbsp;Sekunden lang eine schwarze Rauchwolke. Gegner innerhalb des Rauchs werden um {{ e2 }}&nbsp;% <status>verlangsamt</status> und haben eine verringerte Sicht<PERSON>te. Der ursprüngliche Aufprall verursacht <magicDamage>{{ impactdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24, 22, 20, 18], "cooldownBurn": "26/24/22/20/18", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [60, 110, 160, 210, 260], [50, 50, 50, 50, 50], [200, 200, 200, 200, 200], [4, 4, 4, 4, 4], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/110/160/210/260", "50", "200", "4", "0.5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "GravesSmokeGrenade.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesMove", "name": "Schnelles Ziehen", "description": "Graves springt nach vorne und erhält zusätzliche Rüstung, die einige Sekunden lang bestehen bleibt. <PERSON><PERSON> in Richtung eines gegnerischen Champions springt, werden ihm stattdessen 2 Steigerungen „Wahrer Schneid“ gewährt. Treffer mit normalen Angriffen verringern die Abklingzeit dieser Fähigkeit und erneuern die erhöhten Resistenzen.", "tooltip": "<PERSON> springt und lädt eine <keywordMajor>Patrone</keywordMajor> in seine Schrotflinte. Außerdem erhält er 4&nbsp;Sekunden lang 1&nbsp;Steigerung (max. {{ e0 }}&nbsp;Steigerungen) oder 2&nbsp;Steigerungen, wenn er in die Richtung eines gegnerischen Champions springt. Steigerungen gewähren ihm <scaleArmor>{{ e5 }}&nbsp;Rüstung</scaleArmor>. Steigerungen werden erneuert, wenn er Schaden an Einheiten verursacht, die keine Vasallen sind.<br /><br />Mit jeder getroffenen Kugel seiner Angriffe verringert sich die Abklingzeit dieser Fähigkeit um {{ e4 }}&nbsp;Sekunden.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rüstung", "Abklingzeit"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [30, 40, 50, 60, 70], [4, 4, 4, 4, 4], [20, 25, 30, 35, 40], [0.5, 0.5, 0.5, 0.5, 0.5], [4, 7, 10, 13, 16], [750, 750, 750, 750, 750], [375, 375, 375, 375, 375], [275, 275, 275, 275, 275], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8]], "effectBurn": [null, "30/40/50/60/70", "4", "20/25/30/35/40", "0.5", "4/7/10/13/16", "750", "375", "275", "60", "8"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "GravesMove.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesChargeShot", "name": "Kollateralschaden", "description": "Graves feuert ein explosives Projektil, das am ersten getroffenen Gegner schweren Schaden verursacht. Wurde ein Champion getroffen oder hat das Projektil seine maximale Reichweite erreicht, explodiert es und verursacht Schaden innerhalb eines Kegels.", "tooltip": "Graves feuert eine explosive Patrone ab, die ihn zurückstößt. Die Patrone fügt dem ersten getroffenen Gegner <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zu. Wurde ein gegnerischer Champion getroffen oder hat die Patrone ihre maximale Reichweite erreicht, explodiert sie und verursacht <physicalDamage>{{ falloffdamage }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Primärschaden", "Kegelschaden", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rfalloffdamage }} -> {{ rfalloffdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GravesChargeShot.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Destiny Deluxe", "description": "Graves' Schrotflinte verfügt über ein paar einzigartige Eigenschaften. Er muss nachladen, wenn ihm die Munition ausgeht. Angriffe feuern 4 Kugeln ab, die sich nicht durch Einheiten hindurchbewegen. Einheiten, die keine Champions sind und von mehr als einem Projektil getroffen wurden, werden zurückgeschleudert.", "image": {"full": "GravesTrueGrit.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}