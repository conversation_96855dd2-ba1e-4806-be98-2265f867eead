{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Leblanc": {"id": "<PERSON><PERSON><PERSON>", "key": "7", "name": "LeBlanc", "title": "die Täuscherin", "image": {"full": "Leblanc.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "7000", "num": 0, "name": "default", "chromas": false}, {"id": "7001", "num": 1, "name": "Verruchte LeBlanc", "chromas": false}, {"id": "7002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "7003", "num": 3, "name": "Mistelzweig-LeBlanc", "chromas": false}, {"id": "7004", "num": 4, "name": "Rabentochter-LeBlanc", "chromas": false}, {"id": "7005", "num": 5, "name": "Ahnenholz-LeBlanc", "chromas": false}, {"id": "7012", "num": 12, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "7019", "num": 19, "name": "iG-LeBlanc", "chromas": true}, {"id": "7020", "num": 20, "name": "Hexenzirkel-LeBlanc", "chromas": true}, {"id": "7029", "num": 29, "name": "WM 2020-LeBlanc", "chromas": true}, {"id": "7033", "num": 33, "name": "Hexenzirkel-LeBlanc (Prestige)", "chromas": false}, {"id": "7035", "num": 35, "name": "<PERSON>rmeur<PERSON>", "chromas": true}, {"id": "7045", "num": 45, "name": "Hexerei-LeBlanc", "chromas": true}, {"id": "7055", "num": 55, "name": "Emporgestiegene Legende LeBlanc", "chromas": true}], "lore": "LeBlanc ist selbst für andere Mitglieder der Schwarzen Rose mysteriös. Ihr Name ist nur einer unter vielen für eine blasse Frau, die Leute und Ereignisse gezielt manipuliert – und das bereits seit Noxus' Kindertagen. <PERSON><PERSON> benutzt ihre Magie, um Spiegelbilder ihrer selbst zu erstellen, so dass die Zauberin überall und sogar an mehreren Orten gleichzeitig erscheinen kann. LeBlanc schmiedet ihre Pläne stets unbeobachtet, wobei ihre Motive so unergründlich sind wie ihre wechselnde Identität.", "blurb": "LeBlanc ist selbst für andere Mitglieder der Schwarzen Rose mysteriös. Ihr Name ist nur einer unter vielen für eine blasse Frau, die Leute und Ereignisse gezielt manipuliert – und das bereits seit Noxus' Kindertagen. Sie benutzt ihre Magie, um...", "allytips": ["„Verzerrung“ erlaubt es dir, mit deinen anderen Fähigkeiten aggressiv vorzugehen, während du dich wieder in Sicherheit bringen kannst.", "<PERSON>t „Verzerrung“ kannst du dich in eine bessere Position für „Ätherische Ketten“ bringen.", "Du kannst einen Gegner mit „Ätherische Ketten“ eine kurze Zeit lang daran hindern, dir zu entkommen."], "enemytips": ["LeBlancs ultimative Fähigkeit kann Doppelgänger beim <PERSON> oder, se<PERSON><PERSON>, an entfernten Orten erschaffen. ", "Die weiter entfernt erschaffene Kopie läuft immer zum nächsten Champion, wirkt einen harmlosen Zauber und verschwindet.", "LeBlanc zuerst anzugreifen vereitelt die meisten ihrer Tricks – vor allem, wenn sie ihren Sprung, „Verzerrung“, schon benutzt hat.", "<PERSON><PERSON><PERSON><PERSON> zu betäuben oder verstummen zu lassen, ver<PERSON><PERSON><PERSON> den Rückkehreffekt von „Verzerrung“."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 598, "hpperlevel": 111, "mp": 400, "mpperlevel": 25, "movespeed": 340, "armor": 22, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 7.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.2, "attackspeedperlevel": 2.35, "attackspeed": 0.658}, "spells": [{"id": "LeblancQ", "name": "Siegel der Bosheit", "description": "LeBlanc projiziert ein Siegel, das Schaden verursacht und das Ziel für 3,5 Sekunden markiert. Wenn ein markiertes Ziel durch eine Fähigkeit Schaden erleidet, explodiert das Siegel und verursacht zusätzlichen Schaden. Falls durch eine dieser Aktionen der Gegner stirbt, werden LeBlanc die Manakosten und etwas von der verbleibenden Abklingzeit der Fähigkeit zurückerstattet.", "tooltip": "LeBlanc projiziert ein Siegel auf einen Gegner, das <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht und ihn {{ markduration }}&nbsp;Sekunden lang markiert.<br /><br />Erleidet der markierte Gegner durch eine andere Fähigkeit Schaden, explodiert das Siegel und verursacht <magicDamage>{{ markdamage }}&nbsp;magischen Schaden</magicDamage>.<br /><br />Falls durch eine dieser Aktionen der Gegner stirbt, werden LeBlanc {{ manarefund*100 }}&nbsp;% der Manakosten und {{ cooldownrefund*100 }}&nbsp;% der verbleibenden Abklingzeit der Fähigkeit zurückerstattet.<br /><br /><rules>Das ursprüngliche Siegel verursacht zusätzlich {{ bonusminiondamage }}&nbsp;Schaden an Vasallen.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Siegelschaden", "Explosionsschaden"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemarkdamage }} -> {{ basemarkdamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LeblancQ.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeblancW", "name": "Verzerrung", "description": "LeBlanc springt zu einem Zielort und verursacht Schaden an Gegnern, die sich in der Nähe aufhalten. <PERSON>ch kann sie „Verzerrung“ 4&nbsp;Sekunden lang erneut einsetzen, um an ihre Ausgangsposition zurückzukehren.", "tooltip": "LeBlanc springt los und fügt <PERSON>n in der Nähe <magicDamage>{{ totaldamage }} magischen Schaden</magicDamage> zu. LeBlanc kann die Fähigkeit innerhalb von {{ e3 }}&nbsp;Sekunden nach dem Sprung <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> <PERSON><PERSON>lanc kehrt wieder an ihre ursprüngliche Position zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 13.75, 12.5, 11.25, 10], "cooldownBurn": "15/13.75/12.5/11.25/10", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [600, 600, 600, 600, 600], [0.2, 0.2, 0.2, 0.2, 0.2], [0.2, 0.2, 0.2, 0.2, 0.2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "2", "4", "600", "0.2", "0.2", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "LeblancW.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeblancE", "name": "Ätherische Ketten", "description": "LeBlanc wirft eine <PERSON>, die die erste getroffene Einheit an sie bindet. Bleibt das Ziel 1,5&nbsp;Sekunden lang angekettet, erleidet es zusätzlichen Schaden und wird festgehalten.", "tooltip": "LeBlanc wirft eine Kette, die die erste getroffene Einheit an sie bindet, <magicDamage>{{ initialdamage }} magischen Schaden</magicDamage> verursacht und <keywordStealth>absolute Sicht</keywordStealth> auf die Einheit gewährt.<br /><br />Bleibt das Ziel {{ e3 }}&nbsp;Sekunden lang angekettet, wird es {{ e4 }}&nbsp;Sekunden lang <status>festgehalten</status> und erleidet zusätzlich <magicDamage>{{ delayeddamage }} magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kettenschaden", "Schaden durch Festhalten", "Abklingzeit"], "effect": ["{{ baseinitialdamage }} -> {{ baseinitialdamageNL }}", "{{ basedelayeddamage }} -> {{ basedelayeddamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.25, 12.5, 11.75, 11], "cooldownBurn": "14/13.25/12.5/11.75/11", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [865, 865, 865, 865, 865], [14, 13.25, 12.5, 11.75, 11], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "1.5", "1.5", "865", "14/13.25/12.5/11.75/11", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "LeblancE.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeblancR", "name": "Imitation", "description": "LeBlanc führt eine imitierte Version einer ihrer Grundfähigkeiten aus.", "tooltip": "LeBlanc imitiert ihre letzte Fähigkeit und setzt sie erneut ein. Die imitierte Fähigkeit verursacht erhöhten Schaden.<br /><br /><spellName>Imitation: <PERSON>l der Bosheit</spellName> verursacht bei Markierung <magicDamage>{{ rq1damage }}&nbsp;magischen Schaden</magicDamage> und dann <magicDamage>{{ rq2damage }} magischen Schaden</magicDamage>, wenn das Siegel explodiert.<br /><spellName>Imitation: Verzerrung</spellName> verursacht <magicDamage>{{ rwdamage }}&nbsp;magischen Schaden</magicDamage>.<br /><spellName>Imitation: Ätherische Ketten</spellName> verursacht bei Verbindung <magicDamage>{{ re1damage }}&nbsp;magischen Schaden</magicDamage> und <magicDamage>{{ re2damage }}&nbsp;magischen Schaden</magicDamage>, wenn der Gegner <status>festgehalten</status> wird.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Imitiertes Siegel der Bosheit / Imitierte Ätherische Ketten: Schaden", "Imitiertes Markieren / Festhalten: Schaden", "Imitierte Verzerrung: <PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rq1base }} -> {{ rq1baseNL }}", "{{ rq2base }} -> {{ rq2baseNL }}", "{{ rwbase }} -> {{ rwbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [45, 35, 25], "cooldownBurn": "45/35/25", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "2", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "LeblancR.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Spiegelbild", "description": "Fällt LeBlancs Leben unter 40&nbsp;%, wird sie 1&nbsp;Sekunde lang unsichtbar und erschafft ein Spiegelbild, das keinen Schaden verursacht und bis zu 8&nbsp;Sekunden lang bestehen bleibt.", "image": {"full": "LeblancP.Leblanc_Rework.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}