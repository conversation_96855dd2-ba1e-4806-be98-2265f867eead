{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nunu": {"id": "<PERSON><PERSON><PERSON>", "key": "20", "name": "ヌヌ＆ウィルンプ", "title": "少年とイエティ", "image": {"full": "Nunu.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "20000", "num": 0, "name": "default", "chromas": false}, {"id": "20001", "num": 1, "name": "ビッグフット ヌヌ＆ウィルンプ", "chromas": false}, {"id": "20002", "num": 2, "name": "クリスマス ヌヌ＆ウィルンプ", "chromas": false}, {"id": "20003", "num": 3, "name": "暴食のヌヌ＆ウィルンプ", "chromas": false}, {"id": "20004", "num": 4, "name": "ヌヌ＆ウィルンプ BOT", "chromas": false}, {"id": "20005", "num": 5, "name": "破壊のヌヌ＆ウィルンプ", "chromas": false}, {"id": "20006", "num": 6, "name": "TPA ヌヌ＆ウィルンプ", "chromas": false}, {"id": "20007", "num": 7, "name": "ゾンビ ヌヌ＆ウィルンプ", "chromas": false}, {"id": "20008", "num": 8, "name": "紙細工ヌヌ＆ウィルンプ", "chromas": true}, {"id": "20016", "num": 16, "name": "スペースグルーヴ ヌヌ＆ウィルンプ", "chromas": true}, {"id": "20026", "num": 26, "name": "ヌヌ＆ビィルンプ", "chromas": true}, {"id": "20035", "num": 35, "name": "宇宙の勇士ヌヌ＆ウィルンプ", "chromas": true}, {"id": "20044", "num": 44, "name": "恐怖の夜ヌヌ＆ウィルンプ", "chromas": true}], "lore": "昔々あるところに、恐ろしい怪物を倒して英雄になりたいと願う少年がいた。しかし怪物の正体を知ってみれば、それは魔力を持った独りぼっちのイエティで、彼はただ友達が欲しいだけだった。古き力によって結ばれ、雪玉遊びの楽しさを共に分かち合ったヌヌとウィルンプは、フレヨルド各地を冒険してまわっている。そしていつかどこかで、ヌヌの母親を見つけ出すことを願っている。彼女を救い出すことができれば、本物の英雄になれるかもしれないのだ…", "blurb": "昔々あるところに、恐ろしい怪物を倒して英雄になりたいと願う少年がいた。しかし怪物の正体を知ってみれば、それは魔力を持った独りぼっちのイエティで、彼はただ友達が欲しいだけだった。古き力によって結ばれ、雪玉遊びの楽しさを共に分かち合ったヌヌとウィルンプは、フレヨルド各地を冒険してまわっている。そしていつかどこかで、ヌヌの母親を見つけ出すことを願っている。彼女を救い出すことができれば、本物の英雄になれるかもしれないのだ…", "allytips": ["遠隔系の敵と対峙することになっても「丸かじり」を活用すればレーンに長くとどまることができる。", "「アブソリュート・ゼロ」は詠唱途中でも発動可能。敵に逃げられそうになったら、早めに発動して小ダメージを与えるのも一計だ。", "「アブソリュート・ゼロ」は、敵の動きが鈍ったところで使用するのが効果的。集団戦では闇雲に突っ込んでいかず、チームの誰かが足止めできるスキルを使うのを待とう。"], "enemytips": ["「アブソリュート・ゼロ」は、詠唱を妨害すれば味方が受けるダメージを軽減できる。", "「アブソリュート・ゼロ」は、サモナースペル「フラッシュ」を使えば確実に回避可能。", "「超特大の雪玉！」はスピードは速いが旋回速度はそれほどでもない。真っ直ぐ逃げるのではなく、鋭い方向転換をして回避しよう。"], "tags": ["Tank", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 90, "mp": 280, "mpperlevel": 42, "movespeed": 345, "armor": 29, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "NunuQ", "name": "丸かじり", "description": "ウィルンプがミニオンかモンスター、または敵チャンピオンにかぶりつき、ダメージを与えて自身の体力を回復する。", "tooltip": "ヌヌがウィルンプに敵を噛みつかせて<trueDamage>{{ monsterminiondamage }}の確定ダメージ</trueDamage>を与え、対象がミニオンかジャングルモンスターの場合は<healing>{{ monsterhealing }}の体力</healing>を回復する。対象がチャンピオンの場合は、<magicDamage>{{ totalchampiondamage }}の魔法ダメージ</magicDamage>を与え、<healing>{{ championhealing }}の体力</healing>を回復する。<br /><br />ヌヌ＆ウィルンプの体力が{{ lowhealththreshhold*100 }}%未満の場合は、<healing>回復量</healing>が{{ lowhealthhealingscalar*100 }}%増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["モンスターへのダメージ", "チャンピオンへのダメージ", "体力回復量", "クールダウン"], "effect": ["{{ monsterminiondamage }} -> {{ monsterminiondamageNL }}", "{{ championdamage }} -> {{ championdamageNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "NunuQ.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}マナ"}, {"id": "NunuW", "name": "超特大の雪玉！", "description": "ウィルンプが雪玉をつくって転がす。雪玉を転がす間、雪玉のサイズとスピードが増加していく。雪玉は敵にダメージを与えてノックアップする。", "tooltip": "雪玉をつくって転がす。雪玉は転がしている間、サイズとスピードが増加していく。転がしている間は旋回速度が落ちるが、同じ方向に曲がり続けると旋回速度が上がる。<br /><br />雪玉はチャンピオン、大型モンスター、壁に当たると、転がった距離に応じて<magicDamage>{{ noimpactsnowballdamage }}</magicDamage> - <magicDamage>{{ maximumsnowballdamage }}の魔法ダメージ</magicDamage>を与え、{{ baseknockupduration }} - {{ maximumstunduration }}秒間<status>ノックアップ</status>させる。<br /><br /><recast>再発動</recast>すると雪玉を早めに離すことができる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "NunuW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NunuE", "name": "雪玉連射", "description": "ヌヌが雪玉を複数投げて敵にダメージを与える。その後、雪玉が当たったチャンピオンと大型モンスターにウィルンプがスネア効果を与える。", "tooltip": "ヌヌが雪玉を3つ投げて、雪玉1つにつき<magicDamage>{{ totalsnowballdamage }}の魔法ダメージ</magicDamage>を与え、3つすべてが当たった敵には{{ slowduration }}秒間、{{ slowamount*-100 }}%の<status>スロウ効果</status>を与える。このスキルは最大2回まで<recast>再発動</recast>できる。<br /><br />{{ totalspellduration }}秒後、雪玉による<status>スロウ効果</status>を受けた周囲の敵すべてに{{ rootduration }}秒間<status>スネア効果</status>を与え、追加で<magicDamage>{{ totalrootdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "マナコスト", "スロウ効果", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "NunuE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NunuR", "name": "アブソリュート・ゼロ", "description": "ヌヌとウィルンプが一定範囲内に強力な猛吹雪をつくりだして敵にスロウ効果を与え、詠唱完了時に大ダメージを与える。", "tooltip": "最大{{ channelduration }}秒間詠唱して猛吹雪を呼び出す。吹雪の範囲内にいる敵に{{ slowstartamount*-100 }}%の<status>スロウ効果</status>を与え、このスロウ効果は効果時間中に{{ maxslowamount*-100 }}%まで増加する。ヌヌとウィルンプは効果時間中に<shield>耐久値{{ totalshieldamount }}のシールド</shield>を獲得する。シールドは効果時間後に{{ shielddecayduration }}秒かけて減衰する。<br /><br />猛吹雪は終了時に爆発を起こし、詠唱時間に応じて最大<magicDamage>{{ maximumdamage }}の魔法ダメージ</magicDamage>を与える。<recast>再発動</recast>すると猛吹雪が早めに終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "シールド量", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "NunuR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "フレヨルドの呼び声", "description": "ヌヌがウィルンプと近くにいる味方1体の攻撃速度と移動速度を増加させる。さらに、ウィルンプの通常攻撃が対象の周囲にいる敵にもダメージを与えるようになる。", "image": {"full": "NunuPassive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}