{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MonkeyKing": {"id": "MonkeyKing", "key": "62", "name": "ووكونغ", "title": "ملك القرود", "image": {"full": "MonkeyKing.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "62000", "num": 0, "name": "default", "chromas": false}, {"id": "62001", "num": 1, "name": "ووكونغ البركاني", "chromas": false}, {"id": "62002", "num": 2, "name": "ووكونغ الجنرال", "chromas": false}, {"id": "62003", "num": 3, "name": "ووكونغ تنين الجاد", "chromas": true}, {"id": "62004", "num": 4, "name": "ووكونغ العالم السفلي", "chromas": false}, {"id": "62005", "num": 5, "name": "ووكونغ المشع", "chromas": false}, {"id": "62006", "num": 6, "name": "ووكونغ الرماح الطبقي", "chromas": false}, {"id": "62007", "num": 7, "name": "ووكونغ باتل أكاديميا", "chromas": true}, {"id": "62016", "num": 16, "name": "ووكونغ إلدروود", "chromas": true}], "lore": "ووكونغ هو محتال من الفاستايا يستخدم قوته، وخفة حركته، وذكاءه لإرباك خصومه، وكسب اليد العليا. بعد أن وجد صديق عمره المتمثل في المحارب المعروف باسم ماستر يي، أصبح ووكونغ آخر تلميذ يتعلم الفن القتالي القديم المعروف باسم الووجو. ومع تسلحه بعصا مسحورة، يسعى ووكونغ لمنع تحول إيونيا إلى خراب.", "blurb": "ووكونغ هو محتال من الفاستايا يستخدم قوته، وخفة حركته، وذكاءه لإرباك خصومه، وكسب اليد العليا. بعد أن وجد صديق عمره المتمثل في المحارب المعروف باسم ماستر يي، أصبح ووكونغ آخر تلميذ يتعلم الفن القتالي القديم المعروف باسم الووجو. ومع تسلحه بعصا مسحورة، يسعى...", "allytips": ["تعمل قدرتا الطعم وضربة السحاب القاتم بشكل جيد سوية من أجل ضرب عدوك والخروج قبل أن يتمكن من الرد عليك.", "حاول استخدام قدرة الطعم بالقرب من الأعشاب لجعل أحد الأعداء يبالغ في ردة الفعل مع أفراد فريقك."], "enemytips": ["غالبًا ما يستخدم ووكونغ قدرة الطُعم بعد قدرة ضربة السحاب القاتم. فحاول تأخير قدراتك لفترة قصيرة كي تضمن ضربك لووكونغ الحقيقي.", "تصبح إمكانية قتل ووكونغ أصعب عندما يكون محاطًا بأعدائه. فحاول عزله لكسب أفضلية."], "tags": ["Fighter", "Tank"], "partype": "المانا", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 330, "mpperlevel": 65, "movespeed": 340, "armor": 31, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.69}, "spells": [{"id": "MonkeyKingDoubleAttack", "name": "الضربة الساحقة", "description": "يلحق هجوم ووكونغ التالي ضررًا إضافيًا ويقلص دروع الهدف لبضع ثوان.", "tooltip": "يكسب الهجوم التالي الذي يشنه ووكونغ و <keywordMajor>نسخته</keywordMajor> {{ attackrangebonus }} مدى، ويلحق <physicalDamage>{{ bonusdamagett }} ضرر مادي</physicalDamage> إضافي، كما يزيل <scaleArmor>{{ armorshredpercent*100 }}% من الدروع</scaleArmor> لمدة {{ shredduration }}ث.<br /><br />تتقلص فترة التبريد هذه بمقدار {{ cooldowndecrease }}ث كلما أصاب ووكونغ أو <keywordMajor>نسخته</keywordMajor> عدوًا بهجوم أو قدرة.<br /><br /><rules>تفعّل هذه القدرة تأثيرات التعويذة عند إلحاق الضرر.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الضرر", "% تقليص الدروع", "الم<PERSON>ى", "فترة التبريد"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ armorshredpercent*100.000000 }}%-> {{ armorshredpercentnl*100.000000 }}%", "{{ attackrangebonus }}-> {{ attackrangebonusNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 275, 300, 325, 350], "rangeBurn": "250/275/300/325/350", "image": {"full": "MonkeyKingDoubleAttack.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingDecoy", "name": "مح<PERSON>ا<PERSON> محارب", "description": "يصبح ووكونغ <font color='#91d7ee'>غير مرئي</font> ويندفع في اتجاه معين، تاركًا خلفه نسخة عنه تهاجم الأعداء في الجوار.", "tooltip": "يندفع ووكونغ ويصبح <keywordStealth>غير مرئي</keywordStealth> لمدة {{ stealthduration }} ثانية، حيث يترك خلفه <keywordMajor>نسخة</keywordMajor> ثابتة لمدة {{ cloneduration }} ثانية.<br /><br />وتهاجم <keywordMajor>النسخة</keywordMajor> الأعداء في الجوار الذين ألحق ووكونغ الضرر بهم مؤخرًا، وتحاكي قدرته الخارقة، حيث تلحق بهم {{ clonedamagemod*100 }}% من الضرر العادي.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["النسبة المئوية للضرر", "فترة التبريد", "تكلفة @AbilityResourceName@"], "effect": ["{{ clonedamagemod*100.000000 }}%-> {{ clonedamagemodnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "MonkeyKingDecoy.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingNimbus", "name": "ضربة السحاب القاتم", "description": "يندفع ووكونغ نحو العدو المستهدف ويرسل توائمه لمهاجمة الأعداء بالقرب من هدفه، مما يلحق ضررًا بكل الأعداء المضروبين.", "tooltip": "يندفع ووكونغ نحو أحد الأعداء، حيث يرسل <keywordMajor>نسخًا</keywordMajor> عنه تحاكي الاندفاعة على ما يصل إلى {{ extratargets }} من الأعداء الإضافيين في الجوار. وكل عدو تتم إصابته يتلقى <magicDamage>{{ totaldamage }} ضرر سحر</magicDamage>. هو و <keywordMajor>نسخته</keywordMajor> يكسبان <attackSpeed>{{ attackspeed*100 }}% سرعة هجوم</attackSpeed> لمدة {{ attackspeedduration }} من الثواني.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الضرر", "سرعة الهجوم", "فترة التبريد", "تكلفة @AbilityResourceName@"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}%-> {{ attackspeednl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.25, 8.5, 7.75, 7], "cooldownBurn": "10/9.25/8.5/7.75/7", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MonkeyKingNimbus.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingSpinToWin", "name": "الإعصار", "description": "يمد ووكونغ عصاه ويديرها في الأرجاء بشكل متكرر، فيكسب سرعة حركة.<br><br>يتلقى الأعداء المصابون بها ضررًا، وتتم الإطاحة بهم في الهواء.", "tooltip": "يكسب ووكونغ <speed>{{ movespeed*100 }}% سرعة حركة</speed> ويدوّر ووكونغ عصاه في المكان ثم <status>يطيح</status> بالأعداء في الجوار لمدة {{ knockupduration }}ث ويتلقى <physicalDamage>{{ totaldamagett }} بالإضافة إلى {{ percenthpdamagett }} من الضرر المادي المتناسب مع الصحة القصوى</physicalDamage> خلال {{ spinduration }}ث.<br /><br />يمكن إلقاء هذه القدرة مرة ثانية خلال {{ recastwindow }}ث قبل أن تدخل فترة التبريد.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ضرر الصحة القصوى", "فترة التبريد"], "effect": ["{{ basepercentmaxhpdmgpersec*200.000000 }}%-> {{ basepercentmaxhpdmgpersecnl*200.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [315, 315, 315], "rangeBurn": "315", "image": {"full": "MonkeyKingSpinToWin.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON> حجري", "description": "يكسب ووكونغ دروعًا متكدسة وتجدد صحة قصوى أثناء قتال الأبطال والوحوش.", "image": {"full": "MonkeyKingStoneSkin.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}