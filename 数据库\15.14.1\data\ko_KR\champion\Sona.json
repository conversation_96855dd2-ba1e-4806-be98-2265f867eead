{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sona": {"id": "<PERSON><PERSON>", "key": "37", "name": "소나", "title": "현의 명인", "image": {"full": "Sona.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "37000", "num": 0, "name": "default", "chromas": false}, {"id": "37001", "num": 1, "name": "음악의 여신 소나", "chromas": false}, {"id": "37002", "num": 2, "name": "펜타킬 소나", "chromas": false}, {"id": "37003", "num": 3, "name": "고요한 밤 소나", "chromas": false}, {"id": "37004", "num": 4, "name": "칠현금 소나", "chromas": true}, {"id": "37005", "num": 5, "name": "아케이드 소나", "chromas": true}, {"id": "37006", "num": 6, "name": "DJ 소나", "chromas": false}, {"id": "37007", "num": 7, "name": "사랑의 소나", "chromas": false}, {"id": "37009", "num": 9, "name": "오디세이 소나", "chromas": true}, {"id": "37017", "num": 17, "name": "초능력특공대 소나", "chromas": true}, {"id": "37026", "num": 26, "name": "펜타킬 III: 사라진 양피지 소나", "chromas": true}, {"id": "37035", "num": 35, "name": "별 수호자 소나", "chromas": true}, {"id": "37045", "num": 45, "name": "불멸의 영웅 소나", "chromas": true}, {"id": "37046", "num": 46, "name": "프레스티지 불멸의 영웅 소나", "chromas": false}, {"id": "37056", "num": 56, "name": "승리의 소나", "chromas": true}], "lore": "소나는 현악기 에트왈 연주에서 데마시아 제일가는 거장으로, 말을 하지 못하며 오로지 우아한 화음과 심금을 울리는 아리아로만 의사를 표현한다. 특유의 고상한 몸가짐 때문에 데마시아 상류층의 사랑을 받지만, 듣는 이의 마음을 사로잡는 소나의 선율이 실제로 마법이 아닌가 의심하는 사람들도 많다. 알다시피 마법은 데마시아에서 금기시되고 있기 때문이다. 소나는 낯선 이들에게 침묵을 지키지만, 가까운 지인들은 그녀의 의사를 어느 정도 이해한다. 소나가 연주하는 화음은 아군의 상처를 어루만질 뿐 아니라 방심하고 있는 적을 공격하기도 한다.", "blurb": "소나는 현악기 에트왈 연주에서 데마시아 제일가는 거장으로, 말을 하지 못하며 오로지 우아한 화음과 심금을 울리는 아리아로만 의사를 표현한다. 특유의 고상한 몸가짐 때문에 데마시아 상류층의 사랑을 받지만, 듣는 이의 마음을 사로잡는 소나의 선율이 실제로 마법이 아닌가 의심하는 사람들도 많다. 알다시피 마법은 데마시아에서 금기시되고 있기 때문이다. 소나는 낯선 이들에게 침묵을 지키지만, 가까운 지인들은 그녀의 의사를 어느 정도 이해한다. 소나가 연주하는...", "allytips": ["소나의 오오라가 활성화되어 있을 때 아군에게 가까이 가서 적용해 주되, 적에게 잡히지 않게 주의하세요.", "크레센도는 중요한 순간을 위해 아껴두십시오.", "인내의 아리아를 잘 사용하면 생존력을 극대화할 수 있습니다."], "enemytips": ["소나를 만나면 산개해야 합니다. 단체로 춤을 추는 일이 없도록 하십시오.", "소나는 내버려두면 동료를 치료하기 때문에 가장 먼저 해치워야 합니다."], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 5, "defense": 2, "magic": 8, "difficulty": 4}, "stats": {"hp": 550, "hpperlevel": 91, "mp": 340, "mpperlevel": 45, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.3, "attackspeed": 0.644}, "spells": [{"id": "SonaQ", "name": "용맹의 찬가", "description": "소나가 용맹의 찬가를 연주하여 강렬한 선율을 발사하여 주변에 있는 두 명의 적에게 마법 피해를 입힙니다. 이때 챔피언과 몬스터를 우선 공격합니다. 또한 이 스킬을 사용하면 잠시 동안 오오라가 생겨서 효과 범위 안에 있는 아군이 다음번 적 공격 시 추가 피해를 입힙니다.", "tooltip": "소나가 근처의 적 두 명(챔피언 우선)에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 그리고 새로운 <keywordMajor>멜로디</keywordMajor>를 연주하기 시작합니다. 이 스킬로 챔피언에게 피해를 입히면 <keywordMajor>아첼레란도</keywordMajor> 중첩을 얻습니다.<br /><br /><keywordMajor>멜로디:</keywordMajor> {{ auraduration }}초 동안 소나 주위에 오오라가 생깁니다. 오오라에 닿은 아군 챔피언들은 {{ onhitduration }}초 안에 다음 기본 공격 시 %i:OnHit% <magicDamage>{{ totalonhitdamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.<br /><br /><keywordMajor>파워 코드 - 스타카토:</keywordMajor> 파워 코드 추가 피해 (<magicDamage>총 {{ totalstaccatodamage }}의 마법 피해</magicDamage>){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량 (사용 시)", "피해량 (멜로디)", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "SonaQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SonaW", "name": "인내의 아리아", "description": "소나가 인내의 아리아를 연주하여 보호의 음률을 내보내 근처 부상 당한 아군 1명과 자신의 체력을 회복합니다. 또한 이 스킬을 사용하면 잠시 동안 오오라가 생겨서 효과 범위 안의 모든 아군에게 일시적으로 보호막을 씌워줍니다.", "tooltip": "<spellPassive>사용 시:</spellPassive> 소나가 자신 및 근처 아군 챔피언 한 명(가장 많이 피해를 입은 챔피언)의 <healing>체력을 {{ totalheal }}</healing> 회복합니다. 그리고 새로운 <keywordMajor>멜로디</keywordMajor>를 연주하기 시작합니다.<br /><br /><keywordMajor>멜로디:</keywordMajor> {{ auraduration }}초 동안 소나 주위에 오오라가 생깁니다. 오오라에 닿은 아군 챔피언들은 {{ shieldduration }}초 동안 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 얻습니다.<br /><br />부상당한 아군의 체력을 회복시키거나 이 보호막으로 다른 아군이 받을 피해를 {{ accelerandoshieldbreakpoint }} 이상 흡수할 때마다 <keywordMajor>아첼레란도</keywordMajor> 중첩을 얻습니다.<br /><br /><keywordMajor>파워 코드 - 디미누엔도:</keywordMajor> 파워 코드는 대상이 가하는 물리 및 마법 피해 또한 {{ diminuendoduration }}초 동안 {{ totaldiminuendoweakenpercent }} 감소시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량 (사용 시)", "보호막 (멜로디)", "소모값 @AbilityResourceName@"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SonaW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SonaE", "name": "기민함의 노래", "description": "소나가 기민함의 노래를 연주하여 주변 아군의 이동 속도를 증가시키고 잠시 동안 오오라를 생성합니다. 오오라에 닿은 아군 챔피언은 추가 이동 속도를 얻습니다.", "tooltip": "<spellPassive>사용 시:</spellPassive> 소나가 새 <keywordMajor>멜로디</keywordMajor>를 연주하기 시작하여 {{ selfmovementspeeddurationmin }}초 동안 <speed>{{ totalselfmovementspeed }}의 이동 속도</speed>를 얻습니다. 소나가 피해를 입지 않으면 최대 {{ selfmovementspeeddurationmax }}초까지 연장됩니다. <br /><br /><keywordMajor>멜로디:</keywordMajor> {{ auraduration }}초 동안 소나 주위에 오오라가 생깁니다. 오오라에 닿은 아군 챔피언들은 {{ allymovementspeedduration }}초 동안 <speed>이동 속도가 {{ totalallymovementspeed }}</speed> 상승합니다.<br /><br /><keywordMajor>파워 코드 - 템포:</keywordMajor> 파워 코드는 대상을 {{ tempoduration }}초 동안 {{ totaltempomovespeedslow }} <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동 속도"], "effect": ["{{ allybasemovementspeed*100.000000 }}% -> {{ allybasemovementspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "SonaE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SonaR", "name": "크레센도", "description": "소나가 궁극의 선율을 연주하여 적 챔피언들을 기절시키며 춤을 추게 만들고, 마법 피해를 입힙니다.", "tooltip": "소나가 저항할 수 없는 선율을 연주하여 적을 {{ stunduration }}초 동안 <status>기절</status>시키고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "SonaR.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "파워 코드", "description": "<passive>아첼레란도</passive>: 소나가 기본 스킬을 잘 사용하면 최대치가 될 때까지 영구적으로 궁극기를 제외한 스킬 가속 효과를 얻습니다. 최대치에 도달한 후 스킬을 성공적으로 사용하면 중첩이 쌓이는 대신 남은 궁극기 재사용 대기시간이 감소합니다.<br><br><passive>파워 코드</passive>소나가 스킬을 특정 횟수 사용하고 나면 다음 기본 공격이 추가 마법 피해를 입히고, 소나가 마지막으로 사용한 기본 스킬에 따라 그 효과가 더 증폭될 수 있습니다.", "image": {"full": "Sona_Passive_Charged.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}