{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> <PERSON>", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "Braum tueur de dragons", "chromas": true}, {"id": "201002", "num": 2, "name": "Braum El Tigre", "chromas": false}, {"id": "201003", "num": 3, "name": "Braum cœur de lion", "chromas": false}, {"id": "201010", "num": 10, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "201011", "num": 11, "name": "<PERSON>", "chromas": true}, {"id": "201024", "num": 24, "name": "Braum folie sucrée", "chromas": true}, {"id": "201033", "num": 33, "name": "Braum cocktails", "chromas": true}, {"id": "201042", "num": 42, "name": "<PERSON><PERSON><PERSON> ma<PERSON><PERSON> keb<PERSON>", "chromas": true}], "lore": "Doté de biceps énormes, et d'un cœur plus grand encore, Braum est un héros admiré par tout <PERSON>. Lors de tous les banquets au nord de Frostheld, on rend hommage à sa force légendaire. On raconte qu'il a abattu une forêt entière de chênes en une seule nuit, ou encore qu'il a réduit en miettes une montagne à coups de poing. Portant une porte enchantée en guise de bouclier, Braum arpente les terres gelées du nord en arborant un sourire aussi large que ses muscles et en venant en aide à ceux dans le besoin.", "blurb": "Doté de biceps énormes, et d'un cœur plus grand encore, Braum est un héros admiré par tout <PERSON>. Lors de tous les banquets au nord de Frostheld, on rend hommage à sa force légendaire. On raconte qu'il a abattu une forêt entière de chênes en une...", "allytips": ["Collaborez avec vos alliés pour cumuler des effets Coups étourdissants. Encouragez-les à lancer des attaques de base contre les cibles marquées.", "Bondissez devant les alliés fragiles et protégez-les contre les projectiles avec Incassable.", "Fissure glaciale laisse une puissante zone de ralentissement. Placez-la de manière à couper les combats d'équipes et à ralentir l'approche ennemie."], "enemytips": ["Braum doit infliger Morsure de l'hiver ou une attaque de base pour commencer à appliquer Coups étourdissants. S'il vous marque, éloignez-vous du combat avant d'être touché 3 fois de plus et d'être étourdi.", "L'ultime de Braum a un long délai d'incantation. Profitez de ce délai pour esquiver. Marcher sur la zone gelée vous ralentira, alors positionnez-vous de façon à ne pas devoir la traverser.", "Incassable offre une défense extrêmement puissante à Braum. Attendez la fin de son effet ou contournez cette défense."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "Morsure de l'hiver", "description": "Braum projette des éclats de glace depuis son bouc<PERSON>, ralentissant l'ennemi touché et lui infligeant des dégâts magiques.<br><br>Applique un effet <font color='#FFF673'>Coups étourdissants</font>.", "tooltip": "Braum projette des éclats de glace depuis son bouclier, infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage> au premier ennemi touché et le <status>ralentissant</status> de {{ e2 }}% (la cible retrouve peu à peu sa vitesse normale en {{ e5 }} sec).<br /><br />Applique un effet <keywordMajor>Coups étourdissants</keywordMajor>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumW", "name": "<PERSON><PERSON><PERSON> hum<PERSON>", "description": "Braum bondit vers le champion ou le sbire allié ciblé. À l'arrivée, Braum et l'allié gagnent de l'armure et de la résistance magique pendant quelques secondes.", "tooltip": "Braum bondit vers un champion ou un sbire allié. À l'arrivée, Braum octroie à la cible <scaleArmor>+{{ grantedallyarmor }} armure</scaleArmor> et <scaleMR>+{{ grantedallymr }} résistance magique</scaleMR> pendant {{ e1 }} sec. Braum s'octroie <scaleArmor>+{{ grantedbraumarmor }} armure</scaleArmor> et <scaleMR>+{{ grantedbraummr }} résistance magique</scaleMR> pendant la même durée.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Résistances de base", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ baseresists }} -> {{ baseresistsNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumE", "name": "Incassable", "description": "Braum lève son bouclier dans une direction pendant plusieurs secondes, interceptant tous les projectiles et les détruisant quand ils le touchent. Il bloque complètement les dégâts de la première attaque et réduit les dégâts de toutes les attaques suivantes provenant de cette direction.", "tooltip": "Braum lève son bouclier pendant {{ e2 }} sec, interceptant les projectiles ennemis venant de la direction choisie et les détruisant quand ils le touchent. Le premier projectile bloqué par Braum ne lui inflige pas de dégâts, et les projectiles suivants lui infligent des dégâts réduits de {{ e3 }}%.<br /><br />Braum gagne <speed>+{{ e4 }}% de vitesse de déplacement</speed> tant que son bouclier est levé.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Réduction des dégâts", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumRWrapper", "name": "Fissure glaciale", "description": "Braum frappe le sol, projetant en l'air les ennemis proches et ceux sur une ligne devant lui. Une fissure se crée sur cette ligne et ralentit les ennemis.", "tooltip": "Braum frappe le sol, créant une fissure qui <status>projette dans les airs</status> les ennemis sur sa trajectoire et près de Braum en plus d'infliger <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>. La première cible touchée est <status>projetée dans les airs</status> pendant {{ minknockup }} à {{ maxknockup }} sec, selon la distance entre elle et Braum. Toutes les autres cibles touchées sont <status>projetées dans les airs</status> pendant {{ minknockup }} sec.<br /><br />La fissure crée également pendant {{ slowzoneduration }} sec une zone de <status>ralentissement</status> de {{ movespeedmod }}%.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Durée de la projection", "Ralentissement", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ firstknockupduration }} -> {{ firstknockupdurationNL }}", "{{ movespeedmod }}% -> {{ movespeedmodNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Coups étourdissants", "description": "Les attaques de base de Braum appliquent Coups étourdissants. Une fois le premier effet appliqué, les attaques de base des <font color='#FFF673'>alliés</font> appliquent aussi l'effet Coups étourdissants. <br><br>À 4 effets cumulés, la cible est étourdie et elle subit des dégâts magiques. Pendant quelques secondes, la cible ne peut plus recevoir cet effet mais les attaques de Braum lui infligent des dégâts magiques supplémentaires.", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}