{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Xerath": {"id": "<PERSON><PERSON><PERSON>", "key": "101", "name": "<PERSON><PERSON><PERSON>", "title": "der aufgestiegene Magier", "image": {"full": "Xerath.png", "sprite": "champion5.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "101000", "num": 0, "name": "default", "chromas": false}, {"id": "101001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>th", "chromas": false}, {"id": "101002", "num": 2, "name": "Stahlkrieger-Xerath", "chromas": false}, {"id": "101003", "num": 3, "name": "Erdenfeuer-Xerath", "chromas": false}, {"id": "101004", "num": 4, "name": "Sandwächter Xerath", "chromas": false}, {"id": "101005", "num": 5, "name": "Sternenvernichter-Xerath", "chromas": true}, {"id": "101012", "num": 12, "name": "Arkana-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "101021", "num": 21, "name": "Astronauten-Xerath", "chromas": true}, {"id": "101030", "num": 30, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Xerath ist ein aufgestiegener Magier aus dem alten Shurima, ein Wesen voller arkaner Kraft, das sich in den zersplitterten Teilen eines magischen Sarkophags windet. Jahrtausende lang war er unter dem Wüstensand gefangen gewesen, doch als Shurima sich wieder erhob, wurde auch er aus seinem uralten Kerker befreit. Sein Machthunger hat ihn in den Wahnsinn getrieben und jetzt will er an sich reißen, was er als seinen rechtmäßigen Besitz ansieht. Die emporgekommenen Zivilisationen dieser Welt sollen nach seinem Willen neu geformt werden.", "blurb": "Xerath ist ein aufgestiegener Magier aus dem alten Shurima, ein Wesen voller arkaner Kraft, das sich in den zersplitterten Teilen eines magischen Sarkophags windet. Jahrtausende lang war er unter dem Wüstensand gefangen gewesen, doch als Shurima sich...", "allytips": ["<PERSON><PERSON> ist leichter, mit Arkanpuls zu treffen, wenn sich die Gegner auf dich zu oder von dir weg bewegen.", "Auge der Zerstörung erleichtert es, mit Arkanpuls zu treffen, da das Ziel verlangsamt wird.", "<PERSON>n du einen Gegner mit Schockkugel bet<PERSON>ub<PERSON>, kannst du danach einen Volltreffer mit Auge der Zerstörung landen."], "enemytips": ["Xeraths Reichweite kann einschüchternd sein, aber ist man erst einmal an ihm dran, können ihm die meisten Champions leicht das Wasser reichen.", "<PERSON><PERSON><PERSON> braucht lange, um Arkanes Ritual nutzen zu können. Achte auf seine Zauberanimationen und beginne rechtzeitig mit dem Ausweichen.", "<PERSON><PERSON><PERSON>er der Todesfee kann es für Xerath wesentlich schwerer machen, <PERSON><PERSON>ckkugel auf dich abzufeuern."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 3, "magic": 10, "difficulty": 8}, "stats": {"hp": 596, "hpperlevel": 106, "mp": 400, "mpperlevel": 22, "movespeed": 340, "armor": 22, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6.85, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 1.36, "attackspeed": 0.658}, "spells": [{"id": "XerathArcanopulseChargeUp", "name": "Arkanpuls", "description": "<PERSON>uert einen weitreichenden Energiestrahl, der an allen getroffenen Zielen magischen Schaden verursacht.", "tooltip": "<charge>Aufladungsbeginn:</charge> <PERSON>erath lädt einen arkanen Strahl auf und <status>verlangsamt</status> sich selbst schrittweise um bis zu 50&nbsp;%. <br /><br /><release>Auslösen:</release> Xerath feuert den Strahl ab, der <magicDamage>{{ tooltiptotaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Die Reichweite erhöht sich mit der Aufladungszeit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [75, 115, 155, 195, 235], [4, 4, 4, 4, 4], [0.5, 0.5, 0.5, 0.5, 0.5], [145, 145, 145, 145, 145], [0.5, 0.5, 0.5, 0.5, 0.5], [-0.2, -0.2, -0.2, -0.2, -0.2], [0.1, 0.1, 0.1, 0.1, 0.1], [0.5, 0.5, 0.5, 0.5, 0.5], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/155/195/235", "4", "0.5", "145", "0.5", "-0.2", "0.1", "0.5", "1.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "XerathArcanopulseChargeUp.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XerathArcaneBarrage2", "name": "Auge der Zerstörung", "description": "Beschw<PERSON>rt ein Trommelfeuer aus arkaner Energie, das Gegner im Wirkbereich verlangsamt und magischen Schaden verursacht. Ziele im Zentrum erleiden zusätzlichen Schaden und werden stärker verlangsamt.", "tooltip": "Xerath ruft einen Stoß arkaner Energie herab, der <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>. Gegner im Zentrum erleiden stattdessen<br /><magicDamage>{{ sweetspottotaldamage }} magischen Schaden</magicDamage> und werden um {{ sweetspotslowamount*100 }}&nbsp;% <status>verlangsamt</status>. Dieser Effekt fällt über {{ slowduration }}&nbsp;Sekunden ab.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ sweetspotslowamount*100.000000 }}&nbsp;% -> {{ sweetspotslowamountnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XerathArcaneBarrage2.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XerathMageSpear", "name": "<PERSON><PERSON><PERSON>ku<PERSON>", "description": "<PERSON><PERSON><PERSON>n magischen Schaden zu und betäubt sie.", "tooltip": "Xerath feuert eine Kugel purer Magie ab, die den ersten getroffenen Gegner bis zu {{ maxstunduration }}&nbsp;Sekunden lang abhä<PERSON><PERSON> von der zurückgelegten Distanz <status>betäubt</status> und <magicDamage>{{ tooltiptotaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12.5, 12, 11.5, 11], "cooldownBurn": "13/12.5/12/11.5/11", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [70, 100, 130, 160, 190], [2.25, 2.25, 2.25, 2.25, 2.25], [0.17, 0.17, 0.17, 0.17, 0.17], [1125, 1125, 1125, 1125, 1125], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/100/130/160/190", "2.25", "0.17", "1125", "0.75", "0", "0", "0.5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "XerathMageSpear.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XerathLocusOfPower2", "name": "Arkanes Ritual", "description": "<PERSON><PERSON><PERSON> macht sich selbst bewegungsunfähig und erhält etliche Trommelfeuerladungen.", "tooltip": "<PERSON>erath steigt zu seiner wahren Form auf und kanalisiert {{ e1 }}&nbsp;Sekunden lang. Während dieser Zeit kann er die Fähigkeit bis zu {{ e2 }}-mal <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> Xerath feuert ein magisches Artilleriegeschoss ab, das <magicDamage>{{ tooltiptotaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Für jeden getroffenen Champion verursacht die Artillerie zusätzlich <magicDamage>{{ rampdamagecalc }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Anzahl der Schüsse", "Nach<PERSON><PERSON>nder zusätzlicher Schaden", "Abklingzeit"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ rampbasedamage }} -> {{ rampbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [10, 10, 10], [4, 5, 6], [170, 220, 270], [200, 200, 200], [5000, 5000, 5000], [0.6, 0.6, 0.6], [0.5, 0.5, 0.5], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "10", "4/5/6", "170/220/270", "200", "5000", "0.6", "0.5", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "XerathLocusOfPower2.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Manareserve", "description": "Xeraths normale Angriffe stellen regelmäßig Mana wieder her. Wenn Xerath eine Einheit tötet, wird diese Abklingzeit verringert.", "image": {"full": "Xerath_Passive1.png", "sprite": "passive5.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}