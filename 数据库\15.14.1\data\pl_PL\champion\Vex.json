{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vex": {"id": "Vex", "key": "711", "name": "Vex", "title": "Ponuraczka", "image": {"full": "Vex.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "711000", "num": 0, "name": "default", "chromas": false}, {"id": "711001", "num": 1, "name": "Vex Zwiastunka Brzasku", "chromas": true}, {"id": "711010", "num": 10, "name": "Vex Empireum", "chromas": true}, {"id": "711020", "num": 20, "name": "Obserwatorka Gwiazd Vex", "chromas": true}], "lore": "W czarnym sercu Wysp Cienia samotna Yordlka brnie przez widmową mgłę zadowolona ze swojej mrocznej niedoli. Vex dysponuje bezbrzeżnymi pokładami nastoletniego buntu i potężnym cieniem, z których pomocą chce wykroić dla siebie kawałek mroku z dala od wstrętnej radości świata „normików”. Może brakuje jej ambicji, ale błyskawicznie burzy wszelkie przejawy koloru i szczęśliwości oraz powstrzymuje natrętów swoim magicznym marazmem.", "blurb": "W czarnym sercu Wysp Cienia samotna Yordlka brnie przez widmową mgłę zadowolona ze swojej mrocznej niedoli. Vex dysponuje bezbrzeżnymi pokładami nastoletniego buntu i potężnym cieniem, z których pomocą chce wykroić dla siebie kawałek mroku z dala od...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 490, "mpperlevel": 32, "movespeed": 335, "armor": 23, "armorperlevel": 4.45, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 2.75, "attackspeedperlevel": 1, "attackspeed": 0.669}, "spells": [{"id": "VexQ", "name": "Porywisty Pocisk", "description": "Wystrzel zadający obrażenia pocisk, który przyspiesza w trakcie lotu.", "tooltip": "Vex uwalnia falę mgły, która zadaje <magicDamage>{{ qdamagecalc }} pkt. obrażeń magicznych</magicDamage>. Po chwili fala staje się mniejsza i szybsza.<br /><br />Zużywa <keywordMajor>Mrok</keywordMajor> nałożony na trafionych wrogów.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Koszt (@AbilityResourceName@)", "Czas odnowienia", "Obrażenia"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VexQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VexW", "name": "Przestrzeń Osobista", "description": "Zyskaj tarczę i zadaj obrażenia pobliskim wrogom.", "tooltip": "Vex zyskuje <shield>{{ shieldcalc }} pkt. tarczy</shield> na {{ shieldduration }} sek. i wytwarza falę uderzeniową, która zadaje <magicDamage>{{ wdamagecalc }} pkt. obrażeń magicznych</magicDamage>.<br /><br />Pochłania <keywordMajor>Mrok</keywordMajor> nałożony na trafionych wrogów.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczy", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldamount }} -> {{ shieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "VexW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VexE", "name": "Otulająca Ciemność", "description": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ry zadaje obrażenia wrogom, spowalnia ich i spowija Mrokiem.", "tooltip": "Vex rozkazuje Cieniowi, by ten poleciał w wybrane miej<PERSON>ce, <PERSON>więks<PERSON>j<PERSON><PERSON> swój rozmiar podczas lotu. Po dotarciu w wybrane miejsce zadaje <magicDamage>{{ edamagecalc }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia</status> o {{ slowamount*100 }}% na {{ slowduration }} sek.<br /><br />Zabicie wroga za pomocą tej umiejętności skraca czas odnowienia <keywordMajor>Zagłady i Mroku</keywordMajor> o {{ gloomcdnonchamptooltip*100 }}%.<br /><br />Nak<PERSON>da <keywordMajor>Mrok</keywordMajor> na trafionych wrogów.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "Całkowite skalowanie z mocą umiejętności"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ apratio*100.000000 }} -> {{ aprationl*100.000000 }}"]}, "maxrank": 5, "cooldown": [13, 13, 13, 13, 13], "cooldownBurn": "13", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "VexE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VexR", "name": "Wzbierający Cień", "description": "<PERSON><PERSON><PERSON><PERSON> poc<PERSON>, k<PERSON><PERSON><PERSON> oznacza wrogich bohaterów.  Użyj ponownie, by do nich dos<PERSON> i zadać obrażenia.", "tooltip": "Podekscytowany Cień rzuca się do przodu i zadaje <magicDamage>{{ spell.vexr:rdamagecalc }} pkt. obrażeń magicznych</magicDamage> oraz oznacza pierwszego trafionego wroga na 4 sek.<br /><br /><recast>Ponowne użycie</recast>: Doskocz do oznaczonego bohatera, zadając <magicDamage>{{ spell.vexr:recastdamagecalc }} pkt. obrażeń magicznych</magicDamage> po dotarciu na miejsce.<br /><br />Jeśli oznaczony bohater zginie w ciągu {{ spell.vexr:takedownwindow }} sek. od otrzymania obrażeń od tej umie<PERSON>ci, jej czas odnowienia zostanie tymczasowo zresetowany.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Obrażenia", "Obrażenia przy ponownym użyciu", "<PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ recastdamage }} -> {{ recastdamageNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "VexR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Zagłada i Mrok", "description": "Vex co jakiś czas zostaje wzmocniona, przez co jej następna podstawowa umiejętność przestrasza wrogów i przerywa ich doskoki. Za każdym razem, gdy wróg w pobli<PERSON>u doskakuje, Vex nakłada na niego znak, który można zużyć i zadać dodatkowe obrażenia. Skraca on również czas odnowienia jej wzmocnionego stanu.", "image": {"full": "Icons_Vex_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}