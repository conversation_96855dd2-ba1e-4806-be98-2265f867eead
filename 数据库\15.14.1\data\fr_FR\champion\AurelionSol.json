{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"AurelionSol": {"id": "AurelionSol", "key": "136", "name": "Aurelion Sol", "title": "Forgeur d'étoiles", "image": {"full": "AurelionSol.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "136000", "num": 0, "name": "default", "chromas": false}, {"id": "136001", "num": 1, "name": "Aurelion Sol seigneur volcanique", "chromas": false}, {"id": "136002", "num": 2, "name": "Mecha Aurelion Sol", "chromas": true}, {"id": "136011", "num": 11, "name": "Aurelion Sol dragon des tempêtes", "chromas": false}, {"id": "136021", "num": 21, "name": "Aurelion Sol esprit d'encre", "chromas": false}, {"id": "136031", "num": 31, "name": "Aurelion Sol protecteur de porcelaine", "chromas": false}], "lore": "<PERSON><PERSON><PERSON><PERSON>, Aurelion Sol façonnait des merveilles célestes dont il parsemait le vide infini du cosmos. À présent, il est forcé d'utiliser ses pouvoirs extraordinaires pour le compte d'un empire de l'espace qui s'est joué de lui et l'a réduit en esclavage. Prêt à tout pour redevenir le forgeron stellaire qu'il était, Aurelion Sol n'hésitera pas à arracher les étoiles de leur ciel nocturne si c'est là le prix à payer pour regagner sa liberté.", "blurb": "<PERSON><PERSON><PERSON><PERSON>, Aurelion Sol façonnait des merveilles célestes dont il parsemait le vide infini du cosmos. À présent, il est forcé d'utiliser ses pouvoirs extraordinaires pour le compte d'un empire de l'espace qui s'est joué de lui et l'a réduit en esclavage...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 90, "mp": 530, "mpperlevel": 40, "movespeed": 335, "armor": 22, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "AurelionSolQ", "name": "Souffle de lumière", "description": "Aurelion Sol canalise son souffle de dragon pendant quelques secondes, ce qui blesse le premier ennemi touché et inflige des dégâts secondaires réduits aux ennemis proches. Pour chaque seconde de canalisation dirigée directement sur un ennemi, la compétence inflige des dégâts supplémentaires augmentés par la quantité de poussière d'étoile collectée. Si la cible est un champion, cette compétence collecte aussi de la poussière d'étoile.", "tooltip": "Aurelion Sol crache du feu stellaire pendant un maximum de {{ maxchannelduration }} sec, infligeant <magicDamage>{{ damagepersecond }} pts de dégâts magiques</magicDamage> par seconde au premier ennemi touché et {{ aoemodifier*100 }}% de ces dégâts aux ennemis proches.<br /><br />À chaque seconde de canalisation sur un même ennemi, une surcharge inflige des <magicDamage>dégâts magiques équivalents à {{ burstdamage }}</magicDamage> plus <magicDamage>{{ burstbonustruedamagetochamps }} des PV max</magicDamage>. Si la cible est un champion, cette surcharge collecte aussi <span class=\"color3458eb\">{{ qmassstolen }} poussière d'étoile</span>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Coût en @AbilityResourceName@", "Dégâts par seconde", "Dégâts instantanés", "Durée max de la canalisation"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ rankdamagepersecond }} -> {{ rankdamagepersecondNL }}", "{{ rankburstdamage }} -> {{ rankburstdamageNL }}", "{{ maxchannelduration }} -> {{ maxchanneldurationNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pts de mana par sec", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolQ.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ manacostpersecond }} pts de mana par sec"}, {"id": "AurelionSolW", "name": "Vol astral", "description": "Aurelion Sol survole le terrain dans la direction ciblée. Pendant ce temps, il peut lancer d'autres compétences. Souffle de lumière n'a plus de délai de récupération ou de durée maximale de canalisation et inflige plus de dégâts pendant le vol.<br><br>Le délai de récupération restant de Vol astral est réduit chaque fois qu'un champion ennemi meurt peu après avoir été blessé par Aurelion Sol.<br><br>La poussière d'étoile augmente la portée maximale de Vol astral.", "tooltip": "Aurelion Sol s'envole dans la direction ciblée. Pendant le vol, <spellName>Souffle de lumière</spellName> n'a ni délai de récupération ni durée maximale de canalisation et ses dégâts fixes sont augmentés de {{ truedamagebonus*100 }}%.<br /><br />Si un champion meurt dans les {{ resetwindow }} sec après avoir été blessé par Aurelion Sol, le délai de récupération de cette compétence est réduit de {{ tooltiptakedowncooldownmultiplier }}%.<br /><br /><recast>Réactivation :</recast> met fin prématurément au vol.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Pourcentage de dégâts magiques supplémentaires", "Coût en @AbilityResourceName@", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ truedamagebonus*100.000000 }}% -> {{ truedamagebonusnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cd }} -> {{ cdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "AurelionSolW.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolE", "name": "Trou noir", "description": "Aurelion Sol invoque un trou noir qui blesse les ennemis et les attire lentement vers son centre. Cette compétence octroie de la poussière d'étoile chaque fois qu'un ennemi meurt dans le trou noir et à chaque seconde qu'un champion ennemi passe dans le trou noir. Le centre du trou noir exécute les ennemis qui ont moins d'un certain pourcentage de leurs PV max. La poussière d'étoile augmente la taille du trou noir et le seuil d'exécution.", "tooltip": "Aurelion Sol invoque un trou noir, infligeant <magicDamage>{{ damagepersecond }} pts de dégâts magiques</magicDamage> par seconde et <status>attirant</status> les ennemis vers son centre pendant {{ duration }} sec. Au centre du trou noir, les ennemis qui ont moins de <scaleHealth>{{ currentexecutionthreshold }}% de leurs PV max</scaleHealth> meurent instantanément.<br /><br />Le trou noir absorbe de la <span class=\"color3458eb\">poussière d'étoile</span> chaque fois qu'un ennemi meurt dans son rayon et à chaque seconde qu'un champion ennemi passe dans son rayon.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts par seconde"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolE.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolR", "name": "Étoile finale/Apocalypse", "description": "Étoile finale : Aurelion Sol fait s'abattre une étoile au sol. L'impact inflige des dégâts magiques et étourdit les ennemis tout en octroyant à Aurelion Sol de la poussière d'étoile pour chaque champion ennemi touché. Collecter suffisamment de poussière d'étoile transforme la prochaine Étoile finale d'Aurelion Sol en Apocalypse.<br><br>Apocalypse : Aurelion Sol fait s'abattre au sol une étoile géante, ce qui augmente considérablement la taille de la zone d'impact, augmente les dégâts infligés et projette les ennemis dans les airs au lieu de les étourdir. Une onde de choc s'étend ensuite à partir de la bordure de la zone d'impact, ce qui blesse et ralentit les ennemis touchés. La poussière d'étoile augmente la taille de la zone d'impact d'Étoile finale et d'Apocalypse.", "tooltip": "Aurelion Sol fait s'abattre une étoile au sol, infligeant <magicDamage>{{ maxdamagetooltip }} pts de dégâts magiques</magicDamage>, <status>étourdissant</status> les ennemis pendant {{ stunduration }} sec et collectant <span class=\"color3458eb\">{{ massstolen }} poussières d'étoile</span> par champion touché.<br /><br />Collecter <span class=\"color3458eb\">{{ calamitystacks }} poussières d'étoile</span> transforme la prochaine <spellName>Étoile finale</spellName> en <spellName>Apocalypse</spellName>.<br /><br /><spellName>Apocalypse</spellName> : Aurelion Sol fait s'abattre la puissance d'une constellation, infligeant <magicDamage>{{ r2damage }} pts de dégâts magiques</magicDamage> dans une zone plus large, <status>projetant dans les airs</status> les ennemis touchés pendant {{ stunduration }} sec et provoquant une énorme onde de choc. Celle-ci inflige <magicDamage>{{ shockwavedamage }} pts de dégâts magiques</magicDamage> aux champions et aux monstres épiques et <status>ralentit</status> tous les ennemis touchés de {{ shockwaveslow*100 }}% pendant 1 sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dég<PERSON>ts renforcés", "Dégâts de l'onde de choc"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*1.250000 }} -> {{ basedamagenl*1.250000 }}", "{{ basedamage*0.900000 }} -> {{ basedamagenl*0.900000 }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AurelionSolR.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Créateur cosmique", "description": "Les compétences d'Aurelion Sol qui blessent des ennemis lui octroient de la <font color='#3458eb'>poussi<PERSON> d'étoile</font>, laquelle améliore définitivement chacune de ses compétences. ", "image": {"full": "AurelionSolP.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}