{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lillia": {"id": "Lillia", "key": "876", "name": "Lillia", "title": "il timido bocciolo", "image": {"full": "Lillia.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "876000", "num": 0, "name": "default", "chromas": false}, {"id": "876001", "num": 1, "name": "Lillia Fiore spirituale", "chromas": true}, {"id": "876010", "num": 10, "name": "Lillia Portatrice della Notte", "chromas": true}, {"id": "876019", "num": 19, "name": "Lillia <PERSON>", "chromas": true}, {"id": "876028", "num": 28, "name": "Lillia Corte fatata", "chromas": true}], "lore": "Estremamente timida, la cerbiatta fatata Lillia vaga incerta per le foreste di Ionia. Nascondendosi ai mortali, da tempo intrigata e spaventata dalla loro natura misteriosa, Lillia spera di scoprire il motivo per cui i loro sogni non raggiungono più l'antico Albero Sognante. Ora viaggia per Ionia brandendo un ramo incantato, in cerca dei desideri non realizzati della gente. Solo così anche lei può sbocciare e aiutare gli altri a liberarsi delle paure in cui sono aggrovigliati per far splendere la loro scintilla. Iik!", "blurb": "Estremamente timida, la cerbiatta fatata Lillia vaga incerta per le foreste di Ionia. Nascondendosi ai mortali, da tempo intrigata e spaventata dalla loro natura misteriosa, Lillia spera di scoprire il motivo per cui i loro sogni non raggiungono più...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 2, "magic": 10, "difficulty": 8}, "stats": {"hp": 605, "hpperlevel": 105, "mp": 410, "mpperlevel": 50, "movespeed": 330, "armor": 22, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 325, "hpregen": 2.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.625}, "spells": [{"id": "LilliaQ", "name": "Colpi in fiore", "description": "Lillia accumula passivamente velocità di movimento quando colpisce i nemici con gli incantesimi. P<PERSON>ò attivarla per infliggere danni magici ai nemici nelle vicinanze, infliggendo danni puri extra sul bordo.", "tooltip": "<spellPassive>Passiva:</spellPassive> i colpi dell'abilità di Lillia conferiscono <speed>{{ prancespeed }} velocità di movimento</speed> per {{ pranceduration }} secondi, accumulandosi fino a {{ prancemaxstacks }} volte.<br /><br /><spellActive>Attiva:</spellActive> Lillia rotea il suo incensiere, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> più <trueDamage>{{ bonustruedamage }} danni puri</trueDamage> aggiuntivi sul bordo esterno.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Velocità di movimento", "<PERSON><PERSON> magici", "<PERSON><PERSON> puri"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ prancebonusperstack*100.000000 }}% -> {{ prancebonusperstacknl*100.000000 }}%", "{{ flatdamagebase }} -> {{ flatdamagebaseNL }}", "{{ flatdamagetrue }} -> {{ flatdamagetrueNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LilliaQ.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LilliaW", "name": "Attenzione! Scusa!", "description": "<PERSON><PERSON> infligge danni in un'area vicina, provocando ingenti danni al centro.", "tooltip": "Lillia carica un potente attacco, infliggendo <magicDamage>{{ flatdamage }} danni magici</magicDamage>. I nemici nel centro subiscono invece <magicDamage>{{ flatdamagesweetspot }} danni</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ flatdamagebase }} -> {{ flatdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "LilliaW.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LilliaE", "name": "<PERSON><PERSON> turbinante", "description": "Lillia lancia un seme che danneggia e rallenta le unità su cui atterra. Se non colpisce nulla, continuerà a rotolare finché non trova un muro o un bersaglio.", "tooltip": "Lillia lancia un Seme turbinante davanti a sé, infliggendo <magicDamage>{{ impactdamagetotal }} danni magici</magicDamage> ai nemici nel punto di impatto, rivelandoli e <status>rallentandoli</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi. Se non viene colpito nessun nemico, il seme rotola finché non colpisce un nemico o il terreno.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ impactdamage }} -> {{ impactdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LilliaE.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LilliaR", "name": "<PERSON><PERSON><PERSON><PERSON>a", "description": "Tutti i nemici con addosso Polvere dei sogni diventano sonnolenti prima di addormentarsi. Quei nemici subiranno danni extra se verranno svegliati con la forza.", "tooltip": "Tutti i nemici con addosso <keywordMajor>Polvere dei sogni</keywordMajor> diventano <status>sonnolenti</status> per {{ drowsyduration }} secondi. Poi si <status>addormentano</status> per {{ sleepduration }} secondi.<br /><br />Quando vengono svegliati subendo danni, subiscono ulteriori <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> da interruzione"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ breakdamagebase }} -> {{ breakdamagebaseNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "LilliaR.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Bastone dei sogni", "description": "Colpire un campione o un mostro con un'abilità infliggerà ulteriori danni in base alla salute massima del bersaglio nel tempo.", "image": {"full": "Lillia_Icon_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}