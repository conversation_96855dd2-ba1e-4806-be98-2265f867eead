{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Senna": {"id": "<PERSON><PERSON>", "key": "235", "name": "<PERSON><PERSON>", "title": "la Redentrice", "image": {"full": "Senna.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "235000", "num": 0, "name": "default", "chromas": false}, {"id": "235001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "235009", "num": 9, "name": "Senna True Damage (edizione prestigio)", "chromas": false}, {"id": "235010", "num": 10, "name": "Senna Mezzogiorno di Fuoco", "chromas": true}, {"id": "235016", "num": 16, "name": "PROGETTO: <PERSON><PERSON>", "chromas": true}, {"id": "235026", "num": 26, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "235027", "num": 27, "name": "<PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "235036", "num": 36, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "235046", "num": 46, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "235056", "num": 56, "name": "Senna Favore dell'Inverno", "chromas": false}, {"id": "235063", "num": 63, "name": "<PERSON><PERSON> mascherata", "chromas": false}], "lore": "Condannata sin dall'infanzia a essere tormentata dalla sovrannaturale Nebbia Oscura, Senna si unì a un ordine sacro noto come le Sentinelle della Luce e combatté con ferocia, ma finì per essere uccisa dal crudele spettro <PERSON>, che ne imprigionò l'anima nella sua lanterna. Rifiutandosi di perdere la speranza, all'interno della sua prigione Senna ha imparato a usare la Nebbia ed è riemersa a nuova vita, cambiata per sempre. Ora brandisce l'oscurità e la luce e cerca di porre fine alla Nebbia Oscura affrontandola con il suo stesso potere, liberando le anime smarrite al suo interno con ogni colpo della sua reliquia.", "blurb": "Condannata sin dall'infanzia a essere tormentata dalla sovrannaturale Nebbia Oscura, Senna si unì a un ordine sacro noto come le Sentinelle della Luce e combatté con ferocia, ma finì per essere uccisa dal crudele spettro <PERSON>, che ne imprigionò...", "allytips": [], "enemytips": [], "tags": ["Support", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 530, "hpperlevel": 89, "mp": 350, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 0, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SennaQ", "name": "Oscurità perforante", "description": "<PERSON><PERSON> due canne del suo cannone reliquia, <PERSON>na spara un unico raggio di luce e ombra attraverso un bersaglio, curando gli alleati e dannegg<PERSON>do i nemici.", "tooltip": "Senna spara un dardo di ombra perforante attraverso un alleato o un nemico, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> ai nemici e <status>rallentandoli</status> di {{ totalslow }} per {{ slowduration }} secondo/i. R<PERSON>ristina <healing>{{ totalheal }} salute</healing> ai campioni alleati. <br /><br />Gli attacchi riducono la ricarica di quest'abilità di {{ cdreductiononhit }} secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione", "<PERSON><PERSON> rallenta<PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SennaQ.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaW", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> emette un'ondata di Nebbia Oscura. Se colpisce un nemico ci si avventa sopra, immobilizzando lui e tutto quello che c'è nelle vicinanze dopo un breve ritardo.", "tooltip": "<PERSON><PERSON> emette Neb<PERSON> O<PERSON>cura, infliggendo <physicalDamage>{{ damage }} danni fisici</physicalDamage> al primo nemico colpito. Dopo un ritardo di {{ delaytime }} secondo, il bersaglio e gli altri nemici nelle vicinanze vengono <status>immobilizzati</status> per {{ rootduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata immobilizzazione", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "SennaW.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaE", "name": "Anatema della Nebbia Oscura", "description": "Senna usa la Nebbia che ha accumulato nella sua arma per creare un tempesta intorno a sé, abbracciando l'oscurità e diventando uno spettro al suo interno. Gli alleati che entrano nell'area sono mimetizzati e appaiono anche come spettri quando la Nebbia li avvolge. Gli spettri ottengono velocità di movimento aumentata, non sono selezionabili e nascondono la propria identità.", "tooltip": "<PERSON><PERSON> scompare in una nuvola di Nebbia Oscura per {{ buffduration }} secondi, diventando uno spettro. I campioni alleati che entrano nella Nebbia sono <keywordStealth>mimet<PERSON><PERSON><PERSON></keywordStealth> e diventano spettri quando escono. Gli spettri guadagnano <speed>{{ totalms }} velocità di movimento</speed>, non sono selezionabili e nascondono le loro identità finché non ci sono campioni nemici nelle vicinanze.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ buffduration }} -> {{ buffdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24.5, 23, 21.5, 20], "cooldownBurn": "26/24.5/23/21.5/20", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SennaE.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaR", "name": "Tenebre dell'Alba", "description": "<PERSON><PERSON> richiama le pietre reliquia delle Sentinelle cadute, dividendo il suo cannone in un arsenale sacro di luce e ombra. Lancia poi un raggio globale che protegge gli alleati dai danni, da<PERSON><PERSON><PERSON><PERSON> i nemici che si trovano al centro.", "tooltip": "Senna spara un raggio di luce che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> a tutti i campioni nemici colpiti. I campioni alleati colpiti in un'area più vasta ricevono uno <shield>scudo da {{ totalshield }}</shield> per {{ shieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ shield }} -> {{ shieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SennaR.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Assoluzione", "description": "Quando le unità muoiono vicino a Senna, le loro anime vengono periodicamente intrappolate nella Nebbia Oscura. <PERSON>na può attaccare queste anime per liberarle, assorbendo la Nebbia che le ha trattenute nella morte. La Nebbia alimenta il potere del suo cannone reliquia con attacco fisico, gittata d'attacco e probabilità di colpo critico aumentati. <br><br>Gli attacchi del cannone reliquia di Senna impiegano più tempo a partire, infliggono danni bonus e le conferiscono brevemente una porzione della velocità di movimento del suo bersaglio.", "image": {"full": "Senna_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}