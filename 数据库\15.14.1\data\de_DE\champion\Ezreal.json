{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ezreal": {"id": "Ezreal", "key": "81", "name": "Ezreal", "title": "der ver<PERSON><PERSON>", "image": {"full": "Ezreal.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "81000", "num": 0, "name": "default", "chromas": false}, {"id": "81001", "num": 1, "name": "Nottingham-Ezreal", "chromas": false}, {"id": "81002", "num": 2, "name": "Stürmer-Ezreal", "chromas": false}, {"id": "81003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "81004", "num": 4, "name": "Erkunder-Ezreal", "chromas": false}, {"id": "81005", "num": 5, "name": "Pulsfeuer-Ezreal", "chromas": false}, {"id": "81006", "num": 6, "name": "TPA-Ezreal", "chromas": false}, {"id": "81007", "num": 7, "name": "Charmeur-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "81008", "num": 8, "name": "Pik-Ass-Ezreal", "chromas": false}, {"id": "81009", "num": 9, "name": "Arcade-Ezreal", "chromas": false}, {"id": "81018", "num": 18, "name": "Sternenwächter Ezreal", "chromas": false}, {"id": "81019", "num": 19, "name": "SSG-Ezreal", "chromas": false}, {"id": "81020", "num": 20, "name": "Pyjamawächter Ezreal", "chromas": false}, {"id": "81021", "num": 21, "name": "Academia Certaminis-Ezreal", "chromas": true}, {"id": "81022", "num": 22, "name": "PsyOps-Ezreal", "chromas": false}, {"id": "81023", "num": 23, "name": "PsyOps-Ezreal (Prestige)", "chromas": false}, {"id": "81025", "num": 25, "name": "Porzellan-Ezreal", "chromas": false}, {"id": "81033", "num": 33, "name": "Feenhof-Ezreal", "chromas": false}, {"id": "81043", "num": 43, "name": "HEARTSTEEL-Ezreal", "chromas": false}, {"id": "81044", "num": 44, "name": "Himmelsschuppen-Ezreal", "chromas": true}, {"id": "81054", "num": 54, "name": "Himmelsschuppen-Ezreal (Prestige)", "chromas": false}, {"id": "81065", "num": 65, "name": "Maske der Schwarzen Rose <PERSON>real", "chromas": false}], "lore": "Ezreal ist ein schneidiger Abenteurer, der sich seiner Begabung für die magischen Künste nicht bewusst ist. Er plündert längst vergessene Katakomben, wird in uralte Flüche verwickelt und bewältigt mit Leichtigkeit scheinbar ausweglose Situationen. Seine Tapferkeit und sein Wagemut kennen keine Grenzen. Er zieht es vor, sich dank seiner Improvisationskunst aus jeder Lage zu befreien, und verlässt sich dabei nur zum Teil auf seinen Verstand. Hauptsächlich vertraut er aber auf seinen mystischen shurimanischen Handschuh und setzt damit verheerende arkane Explosionen frei. Eins ist sicher – wo immer Ezreal sich aufhält, folgt der Ärger auf dem Fuß. Oder er läuft ihm voraus. Oder beides.", "blurb": "Ezreal ist ein schneidiger Abenteurer, der sich seiner Begabung für die magischen Künste nicht bewusst ist. Er plündert längst vergessene Katakomben, wird in uralte Flüche verwickelt und bewältigt mit Leichtigkeit scheinbar ausweglose Situationen. Seine...", "allytips": ["Benutze „Arkaner Sprung“, um deine anderen Fähigkeiten und Schüsse besser einsetzen zu können.", "<PERSON><PERSON><PERSON> kann entweder auf Angriffsschaden oder Fähigkeitsstärke setzen, je nachdem, wie du ihn ausstattest.", "Du kannst „Energietrommelfeuer“ so ausrichten, dass du mehrere Vasallen-Wellen oder sogar Monster triffst."], "enemytips": ["<PERSON>zreal ist ein sehr anfälliger Champion, bringe also den Kampf zu ihm.", "Ezreal ist vollständig auf den Fernkampf mit seinem Bogen ausgebildet. Achte dara<PERSON>, dass immer Vasallen zwischen dir und ihm stehen.", "„Mystischer Schuss“ wendet Treffereffekte an. Dazu gehört auch das „Wappen der Asche“."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 102, "mp": 375, "mpperlevel": 70, "movespeed": 325, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.65, "mpregen": 8.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "EzrealQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>z<PERSON> feuert einen Schaden verursachenden Energieblitz ab, der alle seine Abklingzeiten etwas verringert, wenn er einen Gegner trifft.", "tooltip": "Ezreal feuert ein Energiegeschoss ab, das dem ersten getroffenen Gegner <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zufügt und die Abklingzeiten von Ezreals Fähigkeiten um {{ cdrefund }}&nbsp;Sekunden verringert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5.25, 5, 4.75, 4.5], "cooldownBurn": "5.5/5.25/5/4.75/4.5", "cost": [28, 31, 34, 37, 40], "costBurn": "28/31/34/37/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealQ.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealW", "name": "Essenzflux", "description": "Ezreal feuert eine Kugel ab, die am ersten getroffenen Champion oder Ziel haften bleibt. Falls Ezreal einen Gegner mit der Kugel trifft, explodiert diese und fügt <PERSON> zu.", "tooltip": "Ezreal feuert eine magische Kugel ab, die {{ detonationtimeout }}&nbsp;Sekunden lang am ersten getroffenen Champion, Gebäude oder epischen Dschungelmonster hängen bleibt. Wenn Ezreal dieses Ziel mit einem Angriff oder einer Fähigkeit trifft, explodiert die Kugel und verursacht <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage>. Wird die Explosion durch eine Fähigkeit ausgelöst, werden die Manakosten dieser Fähigkeit und <scaleMana>{{ manareturn }}&nbsp;Mana</scaleMana> zurückerstattet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit gesamter Fähigkeitsstärke"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}&nbsp;% -> {{ aprationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealW.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealE", "name": "Arkaner Sprung", "description": "Ezreal teleportiert zum Zielort in der Nähe und schießt ein zielsuchendes Geschoss auf die nächste gegnerische Einheit ab. <PERSON><PERSON><PERSON>, an denen „Essenzflux“ haftet.", "tooltip": "Ezreal teleportiert sich und feuert ein Geschoss auf den nächstbefindlichen Gegner ab, das <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht. Das Geschoss priorisiert <PERSON>, die von <spellName>Essenzflux</spellName> betroffen sind.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [26, 23, 20, 17, 14], "cooldownBurn": "26/23/20/17/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "EzrealE.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealR", "name": "Energietrommelfeuer", "description": "<PERSON>z<PERSON> holt aus, um ein mächtiges, energiegeladenes Trommelfeuer zu entladen, das an jeder getroffenen Einheit auf seinem Flug massiven Schaden verursacht. (Der Schaden ist für Vasallen und nicht-epische Monster verringert.)", "tooltip": "Ezreal feuert einen gewaltigen Energiebogen ab, der <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht. Der Bogen fügt Vasallen und nicht-epischen Dschungelmonstern {{ damagereductionwaveclear.0*100 }}&nbsp;% <PERSON><PERSON><PERSON> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EzrealR.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Erhöhte Zaubermacht", "description": "Ezreal erhält zusätzliches Angriffstempo, jede<PERSON> wenn er erfolgreich mit einer Fähigkeit trifft. Dieser Effekt kann bis zu 5&nbsp;Mal gesteigert werden.", "image": {"full": "Ezreal_RisingSpellForce.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}