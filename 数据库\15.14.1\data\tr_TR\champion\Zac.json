{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zac": {"id": "<PERSON><PERSON>", "key": "154", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Zac.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "154000", "num": 0, "name": "default", "chromas": true}, {"id": "154001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "154002", "num": 2, "name": "Havuz Partisi Zac", "chromas": false}, {"id": "154006", "num": 6, "name": "SKT T1 Zac", "chromas": false}, {"id": "154007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "154014", "num": 14, "name": "<PERSON><PERSON><PERSON>ümdarı Zac", "chromas": true}, {"id": "154024", "num": 24, "name": "Parçalı Peynir Sosu Zac", "chromas": true}], "lore": "Zac kimyasal teknoloji borusundaki çatlaktan sızıp Zaun'un Kuyu denilen b<PERSON><PERSON>deki, kuytu bir mağarada biriken zehirli akıntının eseri. Bu mütevazı kökenine rağmen Zac ilkel bir kitleden, <PERSON><PERSON><PERSON> boru şebekesinde yaşayan bilinçli bir varlığa dönüştü. Şimdilerde kâh kendi kendine yetemeyenlere yardım etmek, kâh Zaun'un virane altyapısını onarmak için borulardan dışarı adım atıyor.", "blurb": "Zac kimyasal teknoloji borusundaki çatlaktan sızıp Zaun'un Kuyu denilen b<PERSON><PERSON>, kuytu bir mağarada biriken zehirli akıntının eseri. Bu mütevazı kökenine rağmen Zac il<PERSON> bir kitleden, <PERSON><PERSON><PERSON> boru şebekesinde yaşayan bilinçli bir varlığa dönüştü...", "allytips": ["Pelte parçalarını <PERSON>lamak, hayatta kalmak açısından büyük önem taşıyor.", "<PERSON>ücre <PERSON>ünmesi hazır <PERSON>, r<PERSON><PERSON> ta<PERSON>, peltelerini yok etmesini zorlaştıracak bir konumda ölmeye çalış.", "Elastik Sapan'ı savaş sisinden b<PERSON><PERSON>, r<PERSON><PERSON><PERSON> daha geç tepki vermesini sağlar."], "enemytips": ["Zac kendisinden ayrılan pelte parçalarını toplayarak iyileşir. Pelte parçalarının üzerine basarak onları ezebilirsin.", "<PERSON><PERSON>, tüm pelte par<PERSON>larını yok ederek Zac'in dirilmesini önle.", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, sabitleme ve savurma etkileri, Zac'in Elastik Sapan'ını kullanmasını engelleyecektir."], "tags": ["Tank", "Fighter"], "partype": "Yok", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 8}, "stats": {"hp": 685, "hpperlevel": 109, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.6, "attackspeed": 0.736}, "spells": [{"id": "ZacQ", "name": "Esnek Saldırı", "description": "Zac kollarından birini uzatarak bir rakibini yakalar. Başka bir rakibe saldırdığında iki hedefi tokuşturmak için birbirine doğru savurur.", "tooltip": "Zac kolunu uzatarak isabet alan ilk rakibi tutar, ona <magicDamage>{{ totaldamage }} Büyü Hasarı</magicDamage> verir ve onu kısa süreliğine <status>yavaşlatır</status>. Zac'in sonraki saldırısının menzili artar, aynı miktarda hasar verir ve aynı miktarda <status>yavaşlatma</status> uygular. <br /><br />Zac bu saldırıyı <i>farklı</i> bir rakibe isabet ettirirse her iki rakibi de birbirine doğru <status>havaya savurur</status>. Hedefler çarpışırsa hem onlar hem de çevrelerindeki rakipler <magicDamage>{{ totaldamage }} Büyü Hasarı</magicDamage> alır ve kısa süreliğine <status>yavaşlar</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [8, 8, 8, 8, 8], [800, 800, 800, 800, 800], [-0.4, -0.4, -0.4, -0.4, -0.4], [0.5, 0.5, 0.5, 0.5, 0.5], [2.5, 2.5, 2.5, 2.5, 2.5], [900, 900, 900, 900, 900], [700, 700, 700, 700, 700], [300, 300, 300, 300, 300], [300, 300, 300, 300, 300]], "effectBurn": [null, "40/55/70/85/100", "8", "800", "-0.4", "0.5", "2.5", "900", "700", "300", "300"], "vars": [], "costType": " ({{ healthcosttooltip }}) kadarı", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZacQ.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "Bedeli mevcut canının %{{ e2 }} ({{ healthcosttooltip }}) kadarı"}, {"id": "ZacW", "name": "<PERSON><PERSON><PERSON>", "description": "Zac yakınındaki rakiplere doğru patlayarak azami canlarının yüzdelik bir kısmını büyü hasarı olarak verir.", "tooltip": "Zac'in bedeni infilak ederek yakınındaki tüm rakiplere <magicDamage>{{ basedamage }} + azami can<PERSON>n<PERSON>n {{ displaypercentdamage }} kadar<PERSON>na eşdeğer bü<PERSON>ü hasarı</magicDamage> verir.<br /><br /><keywordMajor><PERSON><PERSON><PERSON><PERSON><PERSON> kitle</keywordMajor> so<PERSON><PERSON><PERSON><PERSON>, bu yetene<PERSON>in bekleme süresini 1 saniye azaltır.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "%{{ basemaxhealthdamage*100.000000 }} -> %{{ basemaxhealthdamagenl*100.000000 }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 50, 60, 70, 80], [4, 4, 4, 4, 4], [0.04, 0.05, 0.06, 0.07, 0.08], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/50/60/70/80", "4", "0.04/0.05/0.06/0.07/0.08", "1", "200", "0", "0", "0", "0", "0"], "vars": [], "costType": " ({{ tooltiphealthcost }}) kadarı", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "ZacW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "Bedeli mevcut canının %{{ e2 }} ({{ tooltiphealthcost }}) kadarı"}, {"id": "ZacE", "name": "Elast<PERSON>", "description": "Zac kollarını yere sabitleyip geri geri gider, sonra kendini öne fırlatır.", "tooltip": "<charge>G<PERSON>ç Toplamaya Başla:</charge> Zac vücudunu gererek, atılmak için {{ e4 }} saniye boyunca güç toplar.<br /><br /><release>Bırak:</release> Zac kendisini fırlatarak indiği bölgedeki rakipleri en fazla {{ maxstun }} saniyeliğine (güç toplama süresine bağlı olarak) <status>havaya savurur</status> ve onlara <magicDamage>{{ damage }} Büyü Hasarı</magicDamage> verir. <PERSON><PERSON> isabet alan her rakip şampiyon başına fazladan <keywordMajor>vıc<PERSON>k kitle</keywordMajor> ortaya <PERSON>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Menzil", "<PERSON><PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [4, 4, 4, 4, 4], [1200, 1350, 1500, 1650, 1800], [0.9, 1, 1.1, 1.2, 1.3], [0.5, 0.5, 0.5, 0.5, 0.5], [500, 500, 500, 500, 500], [1350, 1350, 1350, 1350, 1350], [0.6, 0.6, 0.6, 0.6, 0.6], [265, 265, 265, 265, 265], [1, 1, 1, 1, 1]], "effectBurn": [null, "60/105/150/195/240", "4", "1200/1350/1500/1650/1800", "0.9/1/1.1/1.2/1.3", "0.5", "500", "1350", "0.6", "265", "1"], "vars": [], "costType": " ({{ healthcosttooltip }}) kadarı", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "Bedeli mevcut canının %{{ e2 }} ({{ healthcosttooltip }}) kadarı"}, {"id": "ZacR", "name": "Hoplaya Zıplaya!", "description": "Zac dört kez zıplayarak isabet alan raki<PERSON>i havaya savurur ve yavaşlatır.", "tooltip": "<PERSON><PERSON> {{ bounces }} defa z<PERSON>p<PERSON>. Her bir rakibe isabet ettirilen ilk zıplama onları <status>geri savurur</status> ve onlara <magicDamage>{{ damageperbounce }} <PERSON><PERSON>yü Hasarı</magicDamage> verir. <PERSON><PERSON><PERSON> z<PERSON> <magicDamage>{{ damagepersubsequentbounce }} Büyü Hasarı</magicDamage> verir ve {{ slowduration }} saniyeliğine %{{ slowamount*100 }} <status>yavaşlatır</status>.<br /><br />Zac zamanla en fazla <speed>%{{ endingms*100 }} Hareket Hızı</speed> kazanır ve zıplarken <spellName>Mad<PERSON><PERSON></spellName>'ni kullanabilir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>ban zıp<PERSON>a hasarı", "<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamagebounce }} -> {{ basedamagebounceNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [1.1, 1.1, 1.1], [700, 850, 1000], [-0.3, -0.4, -0.5], [2.5, 2.5, 2.5], [275, 275, 275], [300, 300, 300], [1, 1, 1], [0.25, 0.25, 0.25], [0.1, 0.1, 0.1]], "effectBurn": [null, "150/250/350", "1.1", "700/850/1000", "-0.3/-0.4/-0.5", "2.5", "275", "300", "1", "0.25", "0.1"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>ac bir yet<PERSON><PERSON><PERSON><PERSON> rakibe her vurduğunda kendinden bir parça bırakır. Bu parçayı geri alarak Can yenileyebilir. Ölümcül hasar aldığında Zac 4 pelte parçasına bölünür; bu parçalar tekrar birleşmeye çalışır. Sağlam kalan pelte parçaları olursa, Zac bu peltelerin Can değerlerine bağlı olarak değişen Can miktarıyla tekrar dirilir. Sağlam kalan her pelte parçası Zac'in azami Can, Zırh ve Büyü Direnci değerlerinin belli bir yüzdesine sahiptir. Bu yeteneğin bekleme süresi 5 dakikadır.", "image": {"full": "ZacPassive.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}