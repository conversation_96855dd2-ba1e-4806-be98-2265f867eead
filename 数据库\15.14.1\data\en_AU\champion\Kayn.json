{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kayn": {"id": "<PERSON><PERSON>", "key": "141", "name": "<PERSON><PERSON>", "title": "the Shadow Reaper", "image": {"full": "Kayn.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "141000", "num": 0, "name": "default", "chromas": false}, {"id": "141001", "num": 1, "name": "Soulhunter <PERSON>", "chromas": false}, {"id": "141002", "num": 2, "name": "<PERSON>", "chromas": true}, {"id": "141008", "num": 8, "name": "Nightbringer Kayn", "chromas": true}, {"id": "141009", "num": 9, "name": "Prestige Nightbringer Kayn", "chromas": false}, {"id": "141015", "num": 15, "name": "<PERSON>", "chromas": true}, {"id": "141020", "num": 20, "name": "HEARTSTEEL Kayn", "chromas": true}, {"id": "141026", "num": 26, "name": "Battle Academia Kayn", "chromas": true}], "lore": "A peerless practitioner of lethal shadow magic, <PERSON><PERSON> battles to achieve his true destiny—to one day lead the Order of Shadow into a new era of Ionian supremacy. He wields the sentient darkin weapon <PERSON><PERSON><PERSON>, undeterred by its creeping corruption of his body and mind. There are only two possible outcomes: either <PERSON><PERSON> bends the weapon to his will... or the malevolent blade consumes him completely, paving the way for the destruction of all Runeterra.", "blurb": "A peerless practitioner of lethal shadow magic, <PERSON><PERSON> battles to achieve his true destiny—to one day lead the Order of Shadow into a new era of Ionian supremacy. He wields the sentient darkin weapon <PERSON><PERSON><PERSON>, undeterred by its creeping corruption of...", "allytips": ["Look at the line-up of both your team and the enemy's team when picking your form.", "Remember that nearby enemies can see which wall you're in."], "enemytips": ["Umbral Trespass requires <PERSON><PERSON> to damage a target first. Try to dodge <PERSON>'s Reach to deny him a long range cast of Umbral Trespass.", "When <PERSON><PERSON> is in terrain near you, you will get a visual warning on the edge of the terrain he's in.", "<PERSON><PERSON> <PERSON><PERSON> while he's in Shadow Step to shorten its duration, or apply hard crowd control (stun, charm, knockup, etc) to end Shadow Step immediately."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 6, "magic": 1, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 103, "mp": 410, "mpperlevel": 50, "movespeed": 340, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.7, "attackspeed": 0.669}, "spells": [{"id": "KaynQ", "name": "Reaping Slash", "description": "<PERSON><PERSON> dashes, then slashes. Both deal damage.", "tooltip": "<PERSON><PERSON> dashes then spins his scythe, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> to enemies he passes through, then deals the damage again to nearby enemies.<br /><br /><keywordMajor>Darkin Slayer:</keywordMajor> Instead deal <physicalDamage>{{ darkinflatdamage }} plus {{ darkinpercentdamage }} max Health physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Base Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [75, 100, 125, 150, 175], [0.65, 0.65, 0.65, 0.65, 0.65], [5, 5, 5, 5, 5], [3.5, 3.5, 3.5, 3.5, 3.5], [200, 250, 300, 350, 400], [40, 40, 40, 40, 40], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/100/125/150/175", "0.65", "5", "3.5", "200/250/300/350/400", "40", "300", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "KaynQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaynW", "name": "Blade's Reach", "description": "Kayn damages and slows targets in a line.", "tooltip": "<PERSON><PERSON> swipes his scythe upward, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> and <status>Slowing</status> enemies hit by {{ e3 }}%, decaying over {{ e5 }} seconds.<br /><br /><keywordMajor>Shadow Assassin:</keywordMajor> <PERSON><PERSON> can move while using this Ability and increases its range.<br /><br /><keywordMajor><PERSON><PERSON> Slayer:</keywordMajor> Additionally <status>Knocks Up</status> enemies hit for {{ e2 }} second.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [85, 130, 175, 220, 265], [1, 1, 1, 1, 1], [90, 90, 90, 90, 90], [160, 160, 160, 160, 160], [1.5, 1.5, 1.5, 1.5, 1.5], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/130/175/220/265", "1", "90", "160", "1.5", "900", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KaynW.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaynE", "name": "Shadow Step", "description": "<PERSON><PERSON> can walk through terrain.", "tooltip": "<PERSON><PERSON> gains <speed>{{ e1 }}% Move Speed</speed>, becomes Ghost<PERSON>, and can move through Terrain for {{ e2 }} seconds. When he first enters terrain, he restores <healing>{{ totalhealing }} Health</healing>.<br /><br />Being <status>Immobilized</status> and spending more than {{ e3 }} consecutive seconds outside of Terrain ends this Ability early.<br /><br /><keywordMajor>Shadow Assassin:</keywordMajor> Gain <speed>{{ e5 }}% Move Speed</speed>, <status>Slow</status> immunity, and reduce the Cooldown to {{ assassincdreduction }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Duration", "Healing"], "effect": ["{{ f15 }} -> {{ f16 }}", "{{ e2 }} -> {{ e2NL }}", "{{ e7 }} -> {{ e7NL }}"]}, "maxrank": 5, "cooldown": [21, 19, 17, 15, 13], "cooldownBurn": "21/19/17/15/13", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [40, 40, 40, 40, 40], [7, 7.5, 8, 8.5, 9], [1.5, 1.5, 1.5, 1.5, 1.5], [1250, 1250, 1250, 1250, 1250], [70, 70, 70, 70, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [90, 100, 110, 120, 130], [0.45, 0.45, 0.45, 0.45, 0.45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40", "7/7.5/8/8.5/9", "1.5", "1250", "70", "1.5", "90/100/110/120/130", "0.45", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "KaynE.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaynR", "name": "Umbral Trespass", "description": "<PERSON><PERSON> hides in an enemy's body, dealing massive damage when he bursts out.", "tooltip": "<spellPassive>Passive:</spellPassive> Champions damaged by <PERSON><PERSON> are marked for 3.15 seconds.<br /><br /><PERSON><PERSON> infests a marked enemy, becoming Untargetable. After {{ infestduration }} seconds or after <recast>Recasting</recast>, <PERSON><PERSON> bursts out, dealing <physicalDamage>{{ damage }} physical damage</physicalDamage> to the enemy.<br /><br /><keywordMajor>Shadow Assassin:</keywordMajor> Increases this Ability's range, the distance <PERSON><PERSON> exits, and refreshes <spellName>The Darkin Scythe's</spellName> Cooldown on exit.<br /><br /><keywordMajor>Darkin Slayer:</keywordMajor> Instead deals <physicalDamage>{{ slayerdamage }} max Health physical damage</physicalDamage> and restores <healing>{{ healvalue }} Health</healing> ({{ slayerhealpercent*100 }}% damage amount).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [0, 0, 0], [2.5, 2.5, 2.5], [300, 300, 300], [15, 15, 15], [13, 13, 13], [70, 70, 70], [750, 750, 750], [550, 550, 550]], "effectBurn": [null, "150/250/350", "0.5", "0", "2.5", "300", "15", "13", "70", "750", "550"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "KaynR.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON> <PERSON>in <PERSON>", "description": "<PERSON><PERSON> wields an ancient weapon and fights <PERSON><PERSON><PERSON>, the darkin within it, for control. Either the <font color='#fe5c50'>Dark<PERSON></font> will triumph, or <PERSON><PERSON> will master <PERSON><PERSON><PERSON> and become the <font color='#8484fb'>Shadow Assassin</font>.<br><br><font color='#fe5c50'>Darkin:</font> Heal for a percentage of spell damage dealt to champions.<br><br><font color='#8484fb'>Shadow Assassin:</font> For the first few seconds in combat with enemy champions, deal bonus damage.", "image": {"full": "Kayn_Passive_Primary.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}