{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Senna": {"id": "<PERSON><PERSON>", "key": "235", "name": "<PERSON><PERSON>", "title": "die Erlöserin", "image": {"full": "Senna.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "235000", "num": 0, "name": "default", "chromas": false}, {"id": "235001", "num": 1, "name": "True Damage Senna", "chromas": true}, {"id": "235009", "num": 9, "name": "True Damage Senna (Prestige)", "chromas": false}, {"id": "235010", "num": 10, "name": "High Noon-Senna", "chromas": true}, {"id": "235016", "num": 16, "name": "PROJEKT: <PERSON><PERSON>", "chromas": true}, {"id": "235026", "num": 26, "name": "Mondfinsternis-Senna", "chromas": true}, {"id": "235027", "num": 27, "name": "Mondfinsternis-Senna (Prestige)", "chromas": false}, {"id": "235036", "num": 36, "name": "Hexerei-Senna", "chromas": true}, {"id": "235046", "num": 46, "name": "Sternenwächteri<PERSON> Senna", "chromas": true}, {"id": "235056", "num": 56, "name": "Wintergeweihte Senna", "chromas": false}, {"id": "235063", "num": 63, "name": "Maskierte Gerechtigkeit Senna", "chromas": false}], "lore": "Von Kindesbeinen an dazu verflucht, vom übernatürlichen schwarzen Nebel verfolgt zu werden, schloss Senna sich dem heiligen Orden der Wächter des Lichts an und bekämpfte verbissen den Nebel – nur, um getötet zu werden und ihre Seele vom grausamen Geist Thresh in einer Laterne gefangen zu wissen. Doch Senna weigerte sich, die Hoffnung aufzugeben, und lernte in der Laterne, wie sie den Nebel für sich nutzen konnte. Dann tauchte sie, für immer verändert, wieder auf. Jetzt kämpft sie mit Dunkelheit und Licht und will dem schwarzen Nebel ein Ende bereiten, indem sie ihn gegen sich selbst richtet – mit jedem Schuss aus ihrer Reliktwaffe, der die Seelen in ihrem Inneren befreit.", "blurb": "Von Kindesbeinen an dazu verflucht, vom übernatürlichen schwarzen Nebel verfolgt zu werden, schloss Senna sich dem heiligen Orden der Wächter des Lichts an und bekämpfte verbissen den Nebel – nur, um getötet zu werden und ihre Seele vom grausamen Geist...", "allytips": [], "enemytips": [], "tags": ["Support", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 530, "hpperlevel": 89, "mp": 350, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 0, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SennaQ", "name": "Durchdringende Finsternis", "description": "Durch die zwei Läufe ihrer Reliktkanone vereint Senna Licht und Schatten zu einem gebündelten Strahl, der Verbündete heilt und Geg<PERSON>n Schaden zufügt.", "tooltip": "<PERSON><PERSON> feuert einen Blitz aus durchdringender Finsternis durch einen Verbündeten oder einen Gegner, der Gegnern <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zufügt und sie {{ slowduration }}&nbsp;Sekunde(n) lang um {{ totalslow }} <status>verlangsamt</status>. Er stellt bei verbündeten Champions <healing>{{ totalheal }}&nbsp;Le<PERSON></healing> wieder her. <br /><br />Angriffe verringern die Abklingzeit dieser Fähigkeit um {{ cdreductiononhit }}&nbsp;Sekunde(n).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SennaQ.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaW", "name": "Letzte Umarmung", "description": "<PERSON><PERSON> sendet eine Welle aus schwarzem Nebel aus. Trifft der Nebel auf einen Gegner, umklammert er ihn und hält ihn sowie Einheiten in der Nähe nach kurzer Verzögerung fest.", "tooltip": "<PERSON>na sendet schwarzen Nebel aus, der dem ersten getroffenen Gegner <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zufügt. Nach einer Verzögerung von {{ delaytime }}&nbsp;Sekunde(n) werden das Ziel und andere nahe Gegner {{ rootduration }}&nbsp;Sekunde(n) lang <status>festgehalten</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Festhaltedauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "SennaW.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaE", "name": "Fluch des schwarzen Nebels", "description": "<PERSON>na formt aus dem Nebel in ihrer Waffe einen Sturm. In seinem Innern gibt sie sich der Finsternis hin und wird zu einem Geist. Verbündete, die das Gebiet betreten, werden camoufliert und erscheinen ebenfalls als Geister, während der Nebel sie umhüllt. Geister erhalten zusätzliches Lauftempo, können nicht anvisiert werden und verbergen ihre Identität.", "tooltip": "<PERSON><PERSON> l<PERSON> sich {{ buffduration }}&nbsp;<PERSON><PERSON><PERSON> lang in eine Wolke aus schwarzem Nebel auf und wird zu einem Geist. Verbündete Champions, die den Nebel betreten, werden <keywordStealth>camoufliert</keywordStealth> und erscheinen als Geister wieder, wenn sie ihn verlassen. Geister erhalten <speed>{{ totalms }}&nbsp;Lauftempo</speed>, können nicht anvisiert werden und verbergen ihre Identität, solange keine gegnerischen Champions in der Nähe sind.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ buffduration }} -> {{ buffdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24.5, 23, 21.5, 20], "cooldownBurn": "26/24.5/23/21.5/20", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SennaE.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaR", "name": "Schattendämmerung", "description": "<PERSON>na ruft die Reliktsteine gefallener Wächter an, um ihre Reliktkanone in das heilige Spektrum aus Schatten und Licht aufzuspalten. Dann feuert Senna einen globalen Strahl ab, der Verbündete mit einem Schild beschützt und an im Zentrum befindlichen Gegnern Schaden verursacht.", "tooltip": "<PERSON>na feuert einen Lichtstrahl ab, der an allen getroffenen gegnerischen Champions<br /><physicalDamage>{{ totaldamage }} normalen Schaden</physicalDamage> verursacht. Verbündete Champions, die in einem größeren Bereich getroffen werden, erhalten {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ totalshield }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schildstärke", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ shield }} -> {{ shieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SennaR.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Absolution", "description": "Der schwarze Nebel ergreift regelmäßig Seelen von Einheiten, die in Sennas Nähe sterben. Senna kann diese Seelen durch Angriffe befreien und den Nebel absorbieren, in dem die Seelen gefangen waren. Der Nebel verstärkt ihre Reliktkanone und erhöht Angriffsschaden und Angriffsreichweite sowie die Chance auf kritische Treffer. <br><br>Sennas Reliktkanone feuert langsamer, verursacht aber zusätzlichen Schaden und gewährt ihr kurzzeitig einen Teil des Lauftempos ihres Ziels.", "image": {"full": "Senna_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}