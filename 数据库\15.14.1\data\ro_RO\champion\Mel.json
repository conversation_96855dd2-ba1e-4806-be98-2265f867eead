{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mel": {"id": "<PERSON>", "key": "800", "name": "<PERSON>", "title": "<PERSON>ia su<PERSON>i", "image": {"full": "Mel.png", "sprite": "champion2.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "800000", "num": 0, "name": "default", "chromas": false}, {"id": "800001", "num": 1, "name": "Mel consilier din Arcane", "chromas": true}], "lore": "Mel Medarda este moștenitoarea de drept a familiei Medarda, odinioară una dintre cele mai puternice din Noxus. Are înfățișarea unei aristocrate pline de grație, dar sub această aparență se află un politician abil, care află încearcă să afle totul despre orice persoană întâlnită. După o întâlnire cu misteriosul Trandafir Negru, Mel a descoperit profunzimea înșelăciunii mamei ei și, pentru prima oară, s-a confruntat cu o situație pe care s-ar putea să n-o poată controla. Cu nou-dobânditele sale abilități magice, a navigat spre casă în căutarea răspunsurilor – și cu toate că mulți încearcă în continuare să-i umbrească lumina interioară, sufletul lui Mel rămâne etern sfidător.", "blurb": "Mel Medarda este moștenitoarea de drept a familiei Medarda, odinioară una dintre cele mai puternice din Noxus. Are înfățișarea unei aristocrate pline de grație, dar sub această aparență se află un politician abil, care află încearcă să afle totul despre...", "allytips": ["Mel poate reflecta proiectilele inamicilor, inclusiv abilitățile puternice. Așteaptă să-și folosească ''Respingerea'' înainte să lansezi proiectile puternice spre ea.", "Cu cât Mel te lovește de mai multe ori, cu atât îți aplică mai multe cumuluri de ''Copleșire''. Dacă ai prea puțină viață, o să te ucidă cu următoarea lovitură, deci retrage-te până îți expiră cumulurile de ''Copleșire''."], "enemytips": ["Mel poate reflecta proiectilele inamicilor, inclusiv abilitățile puternice. Așteaptă să-și folosească ''Respingerea'' înainte să lansezi proiectile puternice spre ea.", "Cu cât Mel te lovește de mai multe ori, cu atât îți aplică mai multe cumuluri de ''Copleșire''. Dacă ai prea puțină viață, o să te ucidă cu următoarea lovitură, deci retrage-te până îți expiră cumulurile de ''Copleșire''."], "tags": ["Mage", "Support"], "partype": "Mană", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 93, "mp": 480, "mpperlevel": 28, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.9, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "MelQ", "name": "Rafală radiantă", "description": "Mel lansează o rafală de proiectile care explodează în jurul unei locații țintă, provocându-le daune în mod repetat inamicilor din zonă.", "tooltip": "Mel lansează o rafală de {{ explosioncount }} proiectile care explodează în jurul unei locații țintă.<br /><br />Fiecare explozie provoacă <magicDamage>{{ totalexplosiondamage }} daune magice</magicDamage>, pân<PERSON> la <magicDamage>{{ alldamagehit }} daune magice</magicDamage> în total.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune explozie", "Contor explozii", "Timp de reactivare", "Cost de man<PERSON>"], "effect": ["{{ explosiondamage }} -> {{ explosiondamageNL }}", "{{ explosioncount }} -> {{ explosioncountNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "MelQ.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Mel creează în jurul ei o barieră care reflectă proiectilele inamice înapoi spre atacator, previne daunele împotriva ei însăși și îi oferă viteză de mișcare.", "tooltip": "Mel creează în jurul ei o barieră care reflectă proiectilele venite de la campionii inamici, previne daunele împotriva ei însăși și îi oferă <speed>{{ movespeed*100 }}% viteză de mișcare ce scade în timp</speed> timp de {{ duration }} sec.<br /><br />Proiectilele reflectate provoacă <magicDamage>daune magice în valoare de {{ damagepercent }} din daunele inițiale</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune reflectate", "Timp de reactivare", "Cost de man<PERSON>"], "effect": ["{{ basedamagepercent*100.000000 }}% -> {{ basedamagepercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [35, 32, 29, 26, 23], "cooldownBurn": "35/32/29/26/23", "cost": [80, 60, 40, 20, 0], "costBurn": "80/60/40/20/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "MelW.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Mel lansează înainte o sferă radiantă, țintuind inamicii din centrul ei și provocându-le daune în timp. În plus, încetinește inamicii din jur.", "tooltip": "Mel lansează o sferă radiantă, <status>țintuind</status> inamicii din centru timp de {{ rootduration }} secunde și provocându-le <magicDamage>{{ damage }} daune magice</magicDamage>.<br /><br />S<PERSON>a creează o zonă ostilă în jurul ei care <status>încetinește</status> inamicii cu {{ areaslowamount*100 }}% și le provoacă <magicDamage>{{ areadamagepersecond }} daune magice pe secundă</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON><PERSON>", "Daune pe secundă", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ baseareadamage*8.000000 }} -> {{ baseareadamagenl*8.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "MelE.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelR", "name": "Eclipsă aurie", "description": "Mel lovește toți inamicii însemnați cu ''Copleșire'', indiferent de distanța la care se află față de ea, provocându-le daune suplimentare pentru fiecare cumul de ''Copleșire''.<br><br><PERSON><PERSON><PERSON><PERSON> ''Eclipsei aurii'' cresc daunele din ''Copleșire''.", "tooltip": "<spellPassive>Pasi<PERSON><PERSON></spellPassive>: daune<PERSON> din <keywordMajor>''Copleșire''</keywordMajor> cresc la <magicDamage>{{ passiveflatdamage }} daune magice plus {{ passivestackdamage }} daune magice per cumul</magicDamage>.<br /><br /><spellActive>Activă</spellActive>: Mel își de<PERSON>nțuie puterea asupra tuturor inamicilor afectați de <keywordMajor>''Copleșire''</keywordMajor>, provocându-le <magicDamage>{{ ultflatdamage }} daune magice plus {{ ultstackdamage }} daune magice per cumul de <keywordMajor>''Copleșire''</keywordMajor></magicDamage>.<br /><br /><rules>Abilitatea poate fi folosită doar atunci când cel puțin un campion inamic este afectat de <keywordMajor>''Copleșire''</keywordMajor>.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune fixe din <spellName>''Eclipsă aurie''</spellName>", "Daune cumuluri din <spellName>''Eclipsă aurie''</spellName>", "Timp de reactivare", "Daune fixe din <keywordMajor>''Copleșire''</keywordMajor>", "Daune cumuluri din <keywordMajor>''Copleșire''</keywordMajor>"], "effect": ["{{ baseultflatdamage }} -> {{ baseultflatdamageNL }}", "{{ baseultstackdamage }} -> {{ baseultstackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basepassiveflatdamage }} -> {{ basepassiveflatdamageNL }}", "{{ basepassivestackdamage }} -> {{ basepassivestackdamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MelR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Str<PERSON><PERSON><PERSON><PERSON>", "description": "Când Mel folosește o abilitate, obține trei proiectile bonus (maximum nouă în total) pe care le lansează la următorul atac de bază.<br><br><PERSON><PERSON><PERSON> când Mel provoacă daune cu ajutorul unei abilități sau al unui atac, aplic<PERSON> ''Copleșire'', care se poate cumula la infinit. Dacă inamicul este lovit de Mel cu suficiente daune din ''Copleșire'', cumulurile se consumă și execută ținta.", "image": {"full": "Mel_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}