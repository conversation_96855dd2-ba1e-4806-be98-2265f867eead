{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Leblanc": {"id": "<PERSON><PERSON><PERSON>", "key": "7", "name": "LeBlanc", "title": "l'ingannatrice", "image": {"full": "Leblanc.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "7000", "num": 0, "name": "default", "chromas": false}, {"id": "7001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "7002", "num": 2, "name": "La Prestigiosa LeBlanc", "chromas": true}, {"id": "7003", "num": 3, "name": "LeBlanc del Vischio", "chromas": false}, {"id": "7004", "num": 4, "name": "LeBlanc Figlia dei Corvi", "chromas": false}, {"id": "7005", "num": 5, "name": "LeBlanc del Bosco Antico", "chromas": false}, {"id": "7012", "num": 12, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "7019", "num": 19, "name": "LeBlanc iG", "chromas": true}, {"id": "7020", "num": 20, "name": "LeBlanc della Congrega", "chromas": true}, {"id": "7029", "num": 29, "name": "LeBlanc Mondiali 2020", "chromas": true}, {"id": "7033", "num": 33, "name": "LeBlanc della Congrega (edizione prestigio)", "chromas": false}, {"id": "7035", "num": 35, "name": "LeBlanc Ruba<PERSON>ori", "chromas": true}, {"id": "7045", "num": 45, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "7055", "num": 55, "name": "LeBlanc Leggenda Trascesa", "chromas": true}], "lore": "LeBlanc è uno dei tanti nomi della pallida donna che ha manipolato persone ed eventi sin dai primi momenti di Noxus, la cui esistenza è un mistero anche per gli altri membri della Rosa Nera. Con la sua magia è in grado di apparire a chiunque, ovunque, e di essere in più posti contemporaneamente. Trama sempre nell'ombra ed è mossa da obiettivi imperscrutabili come la sua mutevole identità.", "blurb": "LeBlanc è uno dei tanti nomi della pallida donna che ha manipolato persone ed eventi sin dai primi momenti di Noxus, la cui esistenza è un mistero anche per gli altri membri della Rosa Nera. Con la sua magia è in grado di apparire a chiunque, ovunque, e...", "allytips": ["Distorsione spaziale ti permette di giocare in modo aggressivo, ma con la possibilità di tornare a distanza di sicurezza.", "Usare Distorsione spaziale può aiutare a mettere a segno Catene eteree.", "Si possono concatenare Catene eteree seguite da Simulacro per mantenere i nemici sotto impedimento a lungo."], "enemytips": ["La suprema di LeBlanc può creare false immagini di LeBlanc durante il lancio o, più raramente, a distanza. ", "La finta LeBlanc creata a distanza correrà sempre verso il campione più vicino, lancerà un abilità innocua e scomparirà subito dopo.", "Attaccare prima LeBlanc evita la gran parte dei suoi trucchi, specialmente se ha usato da poco Distorsione spaziale.", "Stordire o silenziare LeBlanc le impedirà di attivare il ritorno di Distorsione spaziale."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 598, "hpperlevel": 111, "mp": 400, "mpperlevel": 25, "movespeed": 340, "armor": 22, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 7.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.2, "attackspeedperlevel": 2.35, "attackspeed": 0.658}, "spells": [{"id": "LeblancQ", "name": "<PERSON><PERSON><PERSON> della malvagità", "description": "LeBlanc proietta un sigillo, infliggendo danni e marchiando il bersaglio per 3,5 secondi. Danneggiare il bersaglio marchiato con un'abilità fa esplodere il sigillo, infliggendo danni bonus. Se qualsiasi parte uccide il bersaglio, LeBlanc recupera il costo in mana e parte della ricarica rimanente dell'abilità.", "tooltip": "LeBlanc proietta un sigillo su un nemico, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage> e marchiando il bersaglio per {{ markduration }} secondi.<br /><br />Danneggiare il nemico marchiato con un'abilità fa esplodere il sigillo, infliggendo <magicDamage>{{ markdamage }} danni magici</magicDamage>.<br /><br />Se qualsiasi parte uccide il bersaglio, Leblanc rimborsa un {{ manarefund*100 }}% del costo in mana e un {{ cooldownrefund*100 }}% della ricarica rimanente di questa abilità.<br /><br /><rules>Il sigillo iniziale infligge {{ bonusminiondamage }} danni aggiuntivi ai minion. </rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> si<PERSON>o", "<PERSON>ni detonazione"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemarkdamage }} -> {{ basemarkdamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LeblancQ.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeblancW", "name": "Distorsione spaziale", "description": "LeBlanc scatta verso una posizione, infliggendo danni ai nemici vicini alla sua destinazione. Nei prossimi 4 secondi può attivare Distorsione spaziale di nuovo per ritornare al punto di partenza.", "tooltip": "LeBlanc scatta e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici vicini. Per {{ e3 }} secondi dopo lo scatto, <PERSON><PERSON>lan<PERSON> può <recast>rilanciare</recast>.<br /><br /><recast>Rilancio:</recast> LeBlanc torna al punto di partenza.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 13.75, 12.5, 11.25, 10], "cooldownBurn": "15/13.75/12.5/11.25/10", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [600, 600, 600, 600, 600], [0.2, 0.2, 0.2, 0.2, 0.2], [0.2, 0.2, 0.2, 0.2, 0.2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "2", "4", "600", "0.2", "0.2", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "LeblancW.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeblancE", "name": "Catene eteree", "description": "LeBlanc lancia una catena che lega il primo nemico colpito. Se il bersaglio rimane incatenato per 1,5 secondi, LeBlanc lo immobilizza e infligge danni bonus.", "tooltip": "LeBlanc lancia una catena che lega il primo nemico colpito, infliggendo <magicDamage> {{ initialdamage }} danni magici</magicDamage> e conferendo <keywordStealth>Visione magica</keywordStealth>.<br /><br />Se il bersaglio resta incatenato per {{ e3 }} secondi, LeBlanc lo <status>immobilizza</status> per {{ e4 }} secondi e infligge <magicDamage> {{ delayeddamage }} danni magici</magicDamage> in più.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> a catena", "Danni immobilizzazione", "Ricarica"], "effect": ["{{ baseinitialdamage }} -> {{ baseinitialdamageNL }}", "{{ basedelayeddamage }} -> {{ basedelayeddamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.25, 12.5, 11.75, 11], "cooldownBurn": "14/13.25/12.5/11.75/11", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [865, 865, 865, 865, 865], [14, 13.25, 12.5, 11.75, 11], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "1.5", "1.5", "865", "14/13.25/12.5/11.75/11", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "LeblancE.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeblancR", "name": "<PERSON><PERSON>la<PERSON><PERSON>", "description": "LeBlanc lancia una versione copiata di una delle sue abilità base.", "tooltip": "LeBlanc imita la sua abilità più recente, usandola di nuovo. L'abilità imitata infligge danni aumentati.<br /><br /><spellName>Simulacro di Sigillo della malvagità</spellName> infligge <magicDamage>{{ rq1damage }} danni magici</magicDamage> all'applicazione e <magicDamage>{{ rq2damage }} danni magici</magicDamage> al consumo.<br /><spellName>Simulacro di Distorsione spaziale</spellName> infligge <magicDamage>{{ rwdamage }} danni magici</magicDamage>.<br /><spellName>Simulacro di Catene eteree</spellName> infligge <magicDamage>{{ re1damage }} danni magici</magicDamage> con le catene e <magicDamage>{{ re2damage }} danni magici</magicDamage> quando <status>immobilizza</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Simulacro di Sigillo della malvagità/Danni Catene eteree", "Simulacro di Marchio/Danni Immobilizzazione", "Danni Simulacro di Distorsione spaziale", "Ricarica"], "effect": ["{{ rq1base }} -> {{ rq1baseNL }}", "{{ rq2base }} -> {{ rq2baseNL }}", "{{ rwbase }} -> {{ rwbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [45, 35, 25], "cooldownBurn": "45/35/25", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "2", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "LeblancR.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>uan<PERSON> scende sotto il 40% di salute, diventa invisibile per 1 secondo e crea un'Immagine riflessa che non infligge danni e dura fino a 8 secondi.", "image": {"full": "LeblancP.Leblanc_Rework.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}