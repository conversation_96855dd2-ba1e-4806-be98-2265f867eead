{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jayce": {"id": "<PERSON><PERSON>", "key": "126", "name": "<PERSON><PERSON>", "title": "il difensore del domani", "image": {"full": "Jayce.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "126000", "num": 0, "name": "default", "chromas": false}, {"id": "126001", "num": 1, "name": "Full Metal Jayce", "chromas": false}, {"id": "126002", "num": 2, "name": "<PERSON><PERSON> ", "chromas": false}, {"id": "126003", "num": 3, "name": "<PERSON><PERSON> il Reietto", "chromas": false}, {"id": "126004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "126005", "num": 5, "name": "Jayce dell'Accademia di Battaglia", "chromas": true}, {"id": "126015", "num": 15, "name": "<PERSON><PERSON>en<PERSON>", "chromas": true}, {"id": "126024", "num": 24, "name": "<PERSON><PERSON>: Inventore", "chromas": false}, {"id": "126025", "num": 25, "name": "Jayce dei Giochi dello Zenith", "chromas": true}, {"id": "126034", "num": 34, "name": "Jayce T1", "chromas": true}, {"id": "126035", "num": 35, "name": "<PERSON><PERSON>: Superstite", "chromas": false}, {"id": "126036", "num": 36, "name": "Jayce T1 (edizione prestigio)", "chromas": false}], "lore": "<PERSON><PERSON> è un brillante inventore che, assieme al suo amico Viktor, ha compiuto i primi grandi passi nella scoperta dell'hextech. Osan<PERSON><PERSON> in tutta Piltover, cerca di tener fede alla sua reputazione di \"Uomo del progresso\", ma spesso si sente cedere sotto il peso delle aspettative. Questo lo ha portato a capire come la sua invenzione abbia esacerbato le divisioni tra Piltover e Zaun, e ora si prepara a difendere il domani armato del suo fido martello hextech.", "blurb": "<PERSON><PERSON> è un brillante inventore che, assieme al suo amico Viktor, ha compiuto i primi grandi passi nella scoperta dell'hextech. Osannato in tutta Piltover, cerca di tener fede alla sua reputazione di \"Uomo del progresso\", ma spesso si sente cedere...", "allytips": ["Ricordati di cambiare posizione spesso. Migliora i tuoi attacchi e ti dà piccoli scatti di velocità.", "Se stai subendo molti danni, prova a usare la posizione Martello di Jayce, in quanto ti conferisce difese aggiuntive.", "Prova a lanciare Esplosione shock attraverso Portale acceleratore per aumentare la gittata e i danni."], "enemytips": ["<PERSON><PERSON> può attaccare in mischia o a distanza. Fai attenzione alla sua posizione e al colore della sua arma per sapere come attaccherà.", "Se vedi che Jayce ha appena lanciato Portale acceleratore fai attenzione, sta probabilmente per lanciare Esplosione shock.", "<PERSON><PERSON> è forte all'inizio della partita. Se guadagna un vantaggio, gioca in difesa."], "tags": ["Fighter", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 7}, "stats": {"hp": 590, "hpperlevel": 109, "mp": 375, "mpperlevel": 45, "movespeed": 335, "armor": 22, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3, "attackspeed": 0.658}, "spells": [{"id": "JayceToTheSkies", "name": "Verso l'infinito!/Esplosione shock", "description": "Posizione del Martello: balza su un nemico infliggendo danni fisici e rallentando il bersaglio.<br><br>Posizione del Cannone: spara una sfera di elettricità che esplode quando colpisce un nemico (o dopo aver raggiunto la gittata massima) infliggendo danni fisici a tutti i nemici colpiti.", "tooltip": "<keywordMajor><PERSON><PERSON>rcuri<PERSON>:</keywordMajor> <PERSON><PERSON> balza sul nemico, infliggendo <physicalDamage>{{ spell.jaycetotheskies:damage }} danni fisici</physicalDamage> ai nemici nelle vicinanze e <status>rallentandoli</status> del {{ spell.jaycetotheskies:slow*-100 }}% per {{ spell.jaycetotheskies:slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Rallentamento"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%"]}, "maxrank": 6, "cooldown": [16, 14, 12, 10, 8, 6], "cooldownBurn": "16/14/12/10/8/6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JayceToTheSkies.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceStaticField", "name": "Campo di fulmini/Carica iperattiva", "description": "Posizione del Martello - Passiva: recupera mana a ogni colpo. Attiva: crea un campo di fulmini che infligge danni ai nemici nelle vicinanze per diversi secondi.<br><br>Posizione del Cannone: guadagna una carica di energia che aumenta la sua velocità d'attacco al massimo per diversi attacchi.", "tooltip": "<keywordMajor><PERSON><PERSON> di mercurio - Passiva:</keywordMajor> gli attacchi con il <keywordMajor><PERSON><PERSON></keywordMajor> di <PERSON><PERSON> gli conferiscono <scaleMana>{{ spell.jaycestaticfield:managain }} mana</scaleMana>.<br /><br /><keywordMajor><PERSON><PERSON> di mercurio - Attiva:</keywordMajor> <PERSON><PERSON> crea un'aura elettrica che infligge <magicDamage>{{ spell.jaycestaticfield:damage }} danni magici</magicDamage> nell'arco di {{ spell.jaycestaticfield:duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ managain }} -> {{ managainNL }}"]}, "maxrank": 6, "cooldown": [10, 10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [285, 285, 285, 285, 285, 285], "rangeBurn": "285", "image": {"full": "JayceStaticField.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceThunderingBlow", "name": "Colpo tonante/Portale acceleratore", "description": "Posizione del Martello: infligge danni magici a un nemico e lo respinge a corta distanza.<br><br>Posizione del Cannone: schiera un Portale acceleratore che aumenta la velocità di movimento di tutti i campioni alleati che vi passano attraverso. Se Esplosione Shock viene lanciata attraverso il portale, velocità, gittata e danni del proiettile aumentano.", "tooltip": "<keywordMajor><PERSON><PERSON> del martello</keywordMajor>: <PERSON><PERSON> col<PERSON> con il suo martello, <status>respingendo</status> il bersaglio e infliggendo <magicDamage>{{ spell.jaycethunderingblow:flatdamage }} più {{ spell.jaycethunderingblow:perchpdamage*100 }}% della salute massima in danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% salute massima", "Ricarica", "Limite danni ai mostri"], "effect": ["{{ perchpdamage*100.000000 }}% -> {{ perchpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ monstercap }} -> {{ monstercapNL }}"]}, "maxrank": 6, "cooldown": [20, 18, 16, 14, 12, 10], "cooldownBurn": "20/18/16/14/12/10", "cost": [55, 55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [240, 240, 240, 240, 240, 240], "rangeBurn": "240", "image": {"full": "JayceThunderingBlow.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceStanceHtG", "name": "Cannone di mercurio/Martello di mercurio", "description": "Posizione del Martello: trasforma il Martello di mercurio nel Cannone di mercurio guadagnando nuove abilità e aumentando la gittata. Il primo attacco in questa posizione riduce l'armatura e la resistenza magica del bersaglio.<br><br>Posizione del Cannone: trasforma il Cannone di mercurio nel Martello di mercurio guadagnando nuove abilità e aumentando l'armatura e la resistenza magica. Il primo attacco in questa posizione infligge danni magici in più.", "tooltip": "<keywordMajor><PERSON><PERSON> di mercurio</keywordMajor>: <PERSON><PERSON> trasforma la sua arma nel <keywordMajor><PERSON>e di mercurio</keywordMajor>, ottenendo gittata d'attacco e nuove abilità. Il successivo attacco di Jayce rimuove <scaleArmor>{{ spell.jaycestancehtg:rangedformshred }} armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR> per {{ spell.jaycestancehtg:shredduration }} secondi.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [6], "cooldownBurn": "6", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "JayceStanceHtG.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Condensatore hextech", "description": "Quanto Jayce cambia arma ottiene velocità di movimento per un breve periodo.", "image": {"full": "Jayce_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}