{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "<PERSON><PERSON><PERSON>", "title": "the Sad Mummy", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32002", "num": 2, "name": "Vancouver Amumu", "chromas": false}, {"id": "32003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32004", "num": 4, "name": "Re-Gifted <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32005", "num": 5, "name": "Almost-Prom King <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32006", "num": 6, "name": "Little Knight Amumu", "chromas": false}, {"id": "32007", "num": 7, "name": "Sad Robot <PERSON>u", "chromas": false}, {"id": "32008", "num": 8, "name": "Surprise Party Amumu", "chromas": true}, {"id": "32017", "num": 17, "name": "Infernal Amumu", "chromas": true}, {"id": "32023", "num": 23, "name": "Hextech Amumu", "chromas": false}, {"id": "32024", "num": 24, "name": "Pumpkin Prince <PERSON>", "chromas": true}, {"id": "32034", "num": 34, "name": "Po<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "32044", "num": 44, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "32053", "num": 53, "name": "Dumpling <PERSON><PERSON>", "chromas": true}], "lore": "Legend claims that <PERSON><PERSON><PERSON> is a lonely and melancholy soul from ancient Shurima, roaming the world in search of a friend. Doomed by an ancient curse to remain alone forever, his touch is death, his affection ruin. Those who claim to have seen him describe a living cadaver, small in stature and wrapped in creeping bandages. <PERSON><PERSON><PERSON> has inspired myths, songs, and folklore told and retold for generations—such that it is impossible to separate truth from fiction.", "blurb": "Legend claims that <PERSON><PERSON><PERSON> is a lonely and melancholy soul from ancient Shurima, roaming the world in search of a friend. Doomed by an ancient curse to remain alone forever, his touch is death, his affection ruin. Those who claim to have seen him describe...", "allytips": ["<PERSON><PERSON><PERSON> is highly dependent on teammates, so try laning with your friends for maximum effectiveness.", "Cooldown Reduction on <PERSON><PERSON><PERSON> is very strong, but it's often difficult to itemize for it. <PERSON>rab the Golem buff whenever possible to gain Cooldown Reduction without sacrificing stats.", "Despair is very effective against other tanks, so make sure you're in range of opponents with the highest Health."], "enemytips": ["Avoid bunching up with other allies when <PERSON><PERSON><PERSON> has his ultimate available.", "Erratic movement, or hiding behind creep waves can make it difficult for <PERSON><PERSON><PERSON> to instigate a fight with Bandage Toss.", "Amumu's Despair makes purchasing primarily Health items a risky proposition."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "Bandage Toss", "description": "<PERSON><PERSON><PERSON> tosses a sticky bandage at a target, stunning and damaging the target while he pulls himself to them.", "tooltip": "<PERSON><PERSON><PERSON> launches a bandage, pulling himself to the first enemy hit, <status>Stunning</status> them for {{ e2 }} second, and dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />This Ability has 2 charges.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Recharge Time", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "Despair", "description": "Overcome by anguish, nearby enemies lose a percentage of their maximum Health each second and have their <font color='#9b0f5f'>Curses</font> refreshed.", "tooltip": "<toggle>Toggle:</toggle> <PERSON><PERSON><PERSON> begins crying, dealing <magicDamage>{{ basedamage }} plus {{ totalhealthdamage }}% max Health magic damage</magicDamage> to nearby enemies every second and refreshing <keywordMajor>Curse</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% Health damage"], "effect": ["{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }} per Second", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }} per Second"}, {"id": "Tantrum", "name": "Tantrum", "description": "Permanently reduces the physical damage <PERSON><PERSON><PERSON> would take. <PERSON><PERSON><PERSON> can unleash his rage, dealing damage to surrounding enemies. Each time <PERSON><PERSON><PERSON> is hit, the cooldown on <PERSON><PERSON><PERSON> is reduced.", "tooltip": "<spellPassive>Passive:</spellPassive> <PERSON><PERSON><PERSON> takes {{ damagereduction }} reduced physical damage. Additionally, when <PERSON><PERSON><PERSON> is hit by an Attack, this Ability's Cooldown is reduced by {{ e3 }} seconds.<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON><PERSON> throws a tantrum, dealing <magicDamage>{{ tantrumdamage }} magic damage</magicDamage> to nearby enemies.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Reduced", "Cooldown", "Damage"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "Curse of the Sad Mummy", "description": "<PERSON><PERSON><PERSON> entangles surrounding enemy units in bandages, applying his <keywordMajor>Curse</keywordMajor>, damaging and stunning them.", "tooltip": "<PERSON><PERSON><PERSON> flares out his bandages, <status>Stunning</status> for {{ rduration }} seconds, dealing <magicDamage>{{ rcalculateddamage }} magic damage</magicDamage> and applying <keywordMajor>Curse</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@ Cost", "Cooldown", "Damage"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamage }} -> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Cursed Touch", "description": "<PERSON><PERSON><PERSON>'s basic attacks <font color='#9b0f5f'>Curse</font> his enemies, causing them to take bonus true damage from incoming magic damage.", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}