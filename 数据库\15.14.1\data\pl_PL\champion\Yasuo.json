{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yasuo": {"id": "<PERSON><PERSON><PERSON>", "key": "157", "name": "<PERSON><PERSON><PERSON>", "title": "Pokutnik", "image": {"full": "Yasuo.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "157000", "num": 0, "name": "default", "chromas": false}, {"id": "157001", "num": 1, "name": "Ya<PERSON>o w Samo Południe", "chromas": true}, {"id": "157002", "num": 2, "name": "PROJEKT: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "157003", "num": 3, "name": "<PERSON><PERSON><PERSON>r<PERSON>wego Księżyca", "chromas": false}, {"id": "157009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157010", "num": 10, "name": "<PERSON><PERSON><PERSON> z <PERSON>dy<PERSON>", "chromas": true}, {"id": "157017", "num": 17, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157018", "num": 18, "name": "<PERSON><PERSON><PERSON> z <PERSON>", "chromas": true}, {"id": "157035", "num": 35, "name": "<PERSON><PERSON><PERSON> (Prestiżowy)", "chromas": false}, {"id": "157036", "num": 36, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157045", "num": 45, "name": "Morski Wilk Yasuo", "chromas": true}, {"id": "157054", "num": 54, "name": "Smok Jawy <PERSON>", "chromas": false}, {"id": "157055", "num": 55, "name": "Smok Marzeń Yasuo", "chromas": false}, {"id": "157056", "num": 56, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157057", "num": 57, "name": "<PERSON><PERSON><PERSON> (Prestiżowy)", "chromas": false}, {"id": "157068", "num": 68, "name": "Yasuo z Przepowiedni", "chromas": true}, {"id": "157077", "num": 77, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157087", "num": 87, "name": "<PERSON><PERSON><PERSON> (Geneza)", "chromas": false}], "lore": "<PERSON><PERSON><PERSON>, Ion<PERSON><PERSON><PERSON><PERSON> o wielkiej determinacji, jest zwinnym szermierzem, kt<PERSON>ry używa wiatru przeciwko wrogom. Kiedy był dumnym młodzieńcem, niesłusznie oskarżono go o zamordowanie mistrza. Jak<PERSON> że nie mógł dowieś<PERSON> swoje<PERSON>, prz<PERSON><PERSON><PERSON>o mu zabić własnego brata w akcie samoobrony. Nawet po tym, jak ujawniono prawdziwego zabójcę jego mistrza, <PERSON><PERSON><PERSON> wciąż nie potrafił wybaczyć sobie tego, co zrobił. Teraz włóczy się po ojczyźnie, mając u swego boku tylko wiatr, który kieruje jego ostrzem.", "blurb": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> o wielkiej determinacji, jest zwinnym szermierzem, kt<PERSON><PERSON> używa wiatru przeciwko wrogom. Kiedy był dumnym młodzieńcem, niesłusznie oskarżono go o zamordowanie mistrza. Jak<PERSON> że nie mógł dowieś<PERSON> swoje<PERSON>, prz<PERSON><PERSON>ło mu zabić...", "allytips": ["Doskocz przez stwora, aby Zamaszyste Cięcie było <PERSON>, gdy przeciwnik zdecyduje się uciekać; doskocz do przeciwnika, aby użyć stwora jako drogi ucieczki.", "Na poziomie 18, Nawałnica Stali osiąga maksymalną pręd<PERSON><PERSON><PERSON> ataku przy zwiększeniu prędkości ataku o 55% z przedmiotów.", "Ostatnie Tchnienie może być użyte na każdym celu, kt<PERSON>ry jest wyrzucony w powietrze, nawet przez jednego z twoich sojuszników."], "enemytips": ["Nawałnica Stali ma wąski obszar działania. Zrób unik w bok, je<PERSON><PERSON> to możliwe. ", "Gdy Yasuo trafi dwoma Nawałnicami Stali z rzędu, następna wystrzeli tornado. Zachowaj ostrożność i nasłuchuj charakterystycznego dźwięku, aby wykonać unik.", "<PERSON><PERSON><PERSON> jest najsła<PERSON>zy tuż po wystrzeleniu tornada. Atakuj w tym momencie.", "Tarcza Yasuo z Determinacji trwa 2 sek. Zadanie mu obrażeń aktywuje ją. Wyczekaj właściwego momentu, potem atakuj."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 10}, "stats": {"hp": 590, "hpperlevel": 110, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.9, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.697}, "spells": [{"id": "YasuoQ1Wrapper", "name": "Nawałnica Stali", "description": "Atakuje w przód, z<PERSON>jąc obrażenia wszystkim wrogom w linii.<br><br>Przy trafieniu Nawałnica Stali na kilka sekund przyznaje ładunek Nadchodzącej Burzy. Przy 2 ładunkach Nawałnica Stali wystrzeli tornado, które wyrzuca <font color='#6655CC'>w powietrze</font>.<br><br>Nawałnica Stali jest traktowana jak podstawowy atak i skaluje się tak samo.", "tooltip": "<PERSON><PERSON><PERSON> wykonuje pchni<PERSON> mi<PERSON>, z<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage>. <PERSON><PERSON><PERSON> trafi wroga, zyska ładunek utrzymujący się przez {{ gatheringstormduration }} sek. Przy 2 ładunkach następne użycie tej umiejętności wystrzeliwuje trąbę powietrzną z dystansu, zadając takie same obrażenia i <status>podrzucając</status> cel na {{ knockupdurationtooltiponly }} sek.<br /><br />W przypadku użycia podczas doskoku ta umiejętność uderza w okręgu zamiast na wprost.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "YasuoQ1Wrapper.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YasuoW", "name": "Ś<PERSON><PERSON>", "description": "Tworzy ruchomą <PERSON>, która blokuje wszystkie wrogie pociski przez 4 sekundy.", "tooltip": "<PERSON><PERSON><PERSON>rzy unoszącą się ścianę wiatru, która blokuje wszystkie wrogie pociski przez 4 sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Czas odnowienia"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [25, 23, 21, 19, 17], "cooldownBurn": "25/23/21/19/17", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [15, 20, 25, 30, 35], [60, 90, 120, 150, 180], [3, 6, 9, 12, 15], [300, 350, 400, 450, 500], [320, 390, 460, 530, 600], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "15/20/25/30/35", "60/90/120/150/180", "3/6/9/12/15", "300/350/400/450/500", "320/390/460/530/600", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "YasuoW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YasuoE", "name": "Zamaszyste Cięcie", "description": "Doskakuje przez wybranego wroga, zadając obrażenia magiczne. Każde użycie zwiększa obrażenia następnego doskoku aż do wartości maksymalnej.<br><br><PERSON>e może być użyte ponownie na tym samym wrogu przez kilka sekund.<br><br><font color='#99FF99'>Jeżeli Nawałnica Stali zostanie użyta w trakcie doskoku, uder<PERSON> w okręgu.</font>", "tooltip": "<PERSON><PERSON><PERSON> dos<PERSON> przez wroga, z<PERSON><PERSON><PERSON><PERSON> mu <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>. Każde użycie tej umiejętności zapewnia <magicDamage>{{ bonusdamageperstack }}</magicDamage> pkt. dodatkowych obrażeń przy jej kolejnych użyciach w ciągu {{ stackduration }} sek. Efekt ten kumuluje się do {{ maxstacks }} razy.<br /><br />Umiejętność odnawia się przez {{ e2 }} sek. na każdym celu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Osobny czas odnowienia dla każdej jednostki", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.4, 0.3, 0.2, 0.1], "cooldownBurn": "0.5/0.4/0.3/0.2/0.1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [10, 9, 8, 7, 6], [0.5, 0.4, 0.3, 0.2, 0.1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "10/9/8/7/6", "0.5/0.4/0.3/0.2/0.1", "0", "0", "750", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "YasuoE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YasuoR", "name": "Ostatnie Tchnienie", "description": "Miga do znajdującego się <factionIonia1>w powietrzu</factionIonia1> wrog<PERSON>go bohatera, zada<PERSON><PERSON><PERSON> obrażenia fizyczne. Ponadto wydłuża czas, kt<PERSON>ry spędz<PERSON> <factionIonia1>w powietrzu</factionIonia1> wszyscy pobliscy wrogowie. Maksymalnie napełnia Płynność, ale usuwa wszystkie zgromadzone ładunki Nadciągającej Burzy.<br><br>Następnie na pewien czas krytyczne trafienia Yasuo zyskują znaczną ilość przebicia dodatkowego pancerza.", "tooltip": "Yasuo teleportuje się do znajdującego się <status>w powietrzu</status> wrogiego bohatera, zadając mu <physicalDamage>{{ damage }} pkt. obrażeń fizycznych</physicalDamage>. Ponadto o {{ rknockupduration }} sek. wyd<PERSON><PERSON><PERSON><PERSON>, jaki s<PERSON> <status>w powietrzu</status> wszyscy pobliscy wrogowie. Yasuo zyskuje również maks. <keywordMajor>P<PERSON><PERSON><PERSON><PERSON><PERSON></keywordMajor>, ale <spellName>Nawałnica Stali</spellName> traci wszystkie swoje ładunki.<br /><br />Następnie trafienia krytyczne Yasuo ignorują <scaleArmor>{{ rpercentarmorpen }}% dodatkowego pancerza</scaleArmor> celu przez {{ rbuffduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [70, 50, 30], "cooldownBurn": "70/50/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [1400, 1400, 1400], "rangeBurn": "1400", "image": {"full": "YasuoR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}], "passive": {"name": "Droga Wędrowca", "description": "Szansa na trafienie krytyczne Yasuo jest zwiększona. Dodatkowo Yasuo tworzy tarczę, gdy się porusza. Tarcza aktywuje się, gdy otr<PERSON>ma obrażenia od bohatera lub potwora.", "image": {"full": "Yasuo_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}