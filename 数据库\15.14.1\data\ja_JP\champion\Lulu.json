{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lulu": {"id": "<PERSON>", "key": "117", "name": "ルル", "title": "森の妖精使い", "image": {"full": "Lulu.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "117000", "num": 0, "name": "default", "chromas": false}, {"id": "117001", "num": 1, "name": "ほろ苦ルル", "chromas": false}, {"id": "117002", "num": 2, "name": "ウィキッド ルル", "chromas": false}, {"id": "117003", "num": 3, "name": "龍使いルル", "chromas": true}, {"id": "117004", "num": 4, "name": "冬の奇跡ルル", "chromas": false}, {"id": "117005", "num": 5, "name": "プールパーティ ルル", "chromas": true}, {"id": "117006", "num": 6, "name": "スターガーディアン ルル", "chromas": false}, {"id": "117014", "num": 14, "name": "宇宙の魔女ルル", "chromas": true}, {"id": "117015", "num": 15, "name": "パジャマガーディアン ルル", "chromas": false}, {"id": "117026", "num": 26, "name": "スペースグルーヴ ルル", "chromas": true}, {"id": "117027", "num": 27, "name": "プレステージ スペースグルーヴ ルル", "chromas": false}, {"id": "117037", "num": 37, "name": "モンスターテイマー ルル", "chromas": true}, {"id": "117046", "num": 46, "name": "カフェキューティーズ ルル", "chromas": true}], "lore": "ルルは魔法で夢のような幻想や空想の生き物を作り出すことで知られるヨードルのメイジで、ピックスという妖精の相棒と一緒にルーンテラを放浪している。ルルはこの平凡な物質世界を制約だと感じており、気まぐれに世界の法則を捻じ曲げては物体を作り出している。周囲の者は彼女の魔法を異常で危険なものだと感じているが、彼女はみんなには魔法が足りないと感じている。", "blurb": "ルルは魔法で夢のような幻想や空想の生き物を作り出すことで知られるヨードルのメイジで、ピックスという妖精の相棒と一緒にルーンテラを放浪している。ルルはこの平凡な物質世界を制約だと感じており、気まぐれに世界の法則を捻じ曲げては物体を作り出している。周囲の者は彼女の魔法を異常で危険なものだと感じているが、彼女はみんなには魔法が足りないと感じている。", "allytips": ["「ぴかぴかビーム」は、カーソルの指定位置を変えることで独特の角度をつけて発射することができる。ピックスとルルに近い位置を指定して発動することで、効果範囲の角度が大きく変化する。", "遠隔攻撃系の味方には「ピックス、おねがい！」を使ってピックスに援護させよう。タンク系やファイター系のチャンピオンには「おおきくなぁれ！」をかけて先陣を切りやすくしてあげよう。"], "enemytips": ["ルルの妖精が放つ遠距離攻撃は対象を貫通しない。自軍のミニオンの後ろに下がれば、追加攻撃を受けずに済む。", "ルルは相手が至近距離に飛び込んできた時にとても強いチャンピオンだ。彼女に隙を与えてはならない！ ひたすら遠距離から妨害攻撃を加えて、レーンから退散させよう。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 565, "hpperlevel": 92, "mp": 350, "mpperlevel": 55, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.6, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "LuluQ", "name": "ぴかぴかビーム", "description": "ピックスとルルが同時に魔法のビームを発射し、命中したすべての敵にダメージと重度のスロウ効果を付与する。", "tooltip": "ルルとピックスがそれぞれ貫通するビームを放ち、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて、{{ slowamount*-100 }}%の<status>スロウ効果</status>を付与する。この効果は{{ slowduration }}秒かけて元に戻る。<br /><br />複数のビームが敵に命中した場合、2本目以降のビームは<magicDamage>{{ bonusmissiledamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "LuluQ.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LuluW", "name": "イタズラ", "description": "味方に使用した場合、短時間攻撃速度と移動速度を増加させる。敵に使用した場合、かわいらしい動物に変身させ、通常攻撃とスキルの使用を封じる。", "tooltip": "味方チャンピオンに使用すると、{{ e5 }}秒間、<speed>移動速度を{{ totalms }}</speed>、<attackSpeed>攻撃速度を{{ e7 }}%</attackSpeed>増加させる。<br /><br />敵チャンピオンに使用すると、{{ e3 }}秒間、対象に<status>ポリモーフ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加移動速度および増加攻撃速度効果時間", "攻撃速度", "ポリモーフ効果時間"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ effect7amount*100.000000 }}% -> {{ effect7amountnl*100.000000 }}%", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [18, 18, 18, 18, 18], "cooldownBurn": "18", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [1.2, 1.4, 1.6, 1.8, 2], [-60, -60, -60, -60, -60], [3, 3.25, 3.5, 3.75, 4], [0.01, 0.01, 0.01, 0.01, 0.01], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.25", "0", "1.2/1.4/1.6/1.8/2", "-60", "3/3.25/3.5/3.75/4", "0.01", "20/22.5/25/27.5/30", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "LuluW.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LuluE", "name": "ピックス、おねがい！", "description": "味方に使用した場合、ピックスをそばに送り込んでダメージを防ぐシールドを付与する。その後ピックスは対象にくっついて移動し、通常攻撃時に魔法のビームで援護する。敵に使用した場合、対象のもとへピックスが飛んでいきダメージを与える。その後ピックスは対象にくっついて移動し、可視状態にする。", "tooltip": "味方に使用するとピックスが味方の元へ飛んでいき、{{ e1 }}秒間<spellName>「仲良し妖精ピックス」</spellName>を付与する。味方がチャンピオンの場合は、{{ e7 }}秒間、<shield>耐久値{{ totalshield }}のシールド</shield>も付与する。<br /><br />敵チャンピオンに使用するとピックスが邪魔をして、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて、{{ e6 }}秒間、対象の<keywordStealth>真の視界</keywordStealth>を得る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [80, 120, 160, 200, 240], [25, 25, 25, 25, 25], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "80/120/160/200/240", "50", "80/120/160/200/240", "25", "4", "2.5", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "LuluE.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LuluR", "name": "おおきくなぁれ！", "description": "味方1体を巨大化させ、周囲にいる敵をノックアップさせる。巨大化した味方は体力が大幅に増加し、体の周囲に数秒間光の輪が発生する。輪の中に入った敵はスロウ状態になる。", "tooltip": "味方1体を巨大化させ、周囲の敵を{{ knockbackduration }}秒間<status>ノックアップ</status>させる。巨大化した味方は{{ buffduration }}秒間、<healing>最大体力が{{ totalbonushealth }}</healing> 増加し、周囲の敵に{{ slowpercent }}%の<status>スロウ効果</status> を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加体力", "スロウ効果", "クールダウン"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "LuluR.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "仲良し妖精ピックス", "description": "ピックスがくっついているチャンピオンが敵ユニットを攻撃するたびに、ピックスも魔法のビームで援護する。このビームは対象に追従するが、射線に別のユニットが割り込むと、そのユニットに遮られる。", "image": {"full": "Lulu_PixFaerieCompanion.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}