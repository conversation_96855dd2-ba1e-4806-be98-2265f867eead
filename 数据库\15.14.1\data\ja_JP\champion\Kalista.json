{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kalista": {"id": "Kalista", "key": "429", "name": "カリスタ", "title": "復讐の槍", "image": {"full": "Kalista.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "429000", "num": 0, "name": "default", "chromas": false}, {"id": "429001", "num": 1, "name": "ブラッドムーン カリスタ", "chromas": false}, {"id": "429002", "num": 2, "name": "Worlds 2015 カリスタ", "chromas": true}, {"id": "429003", "num": 3, "name": "SKT T1 カリスタ", "chromas": false}, {"id": "429005", "num": 5, "name": "略奪者カリスタ", "chromas": true}, {"id": "429014", "num": 14, "name": "妖精の王宮カリスタ", "chromas": true}, {"id": "429024", "num": 24, "name": "秩序の光カリスタ", "chromas": true}], "lore": "報復を誓い、復讐を司る亡霊カリスタは、偽り人や裏切りし者を狩るためシャドウアイルから召喚される。裏切られた者が血にまみれて復讐を乞い叫んでも、カリスタはそのために自らの魂を代償として支払う覚悟がある者の呼びかけにしか応えない。そしてひとたび彼女の憤怒を向けられた者は決して破滅から逃れることはできない。非情なる狩手が交わした契約は常に、彼女の魂が放つ冷たい槍で完了の印を捺されるのだ。", "blurb": "報復を誓い、復讐を司る亡霊カリスタは、偽り人や裏切りし者を狩るためシャドウアイルから召喚される。裏切られた者が血にまみれて復讐を乞い叫んでも、カリスタはそのために自らの魂を代償として支払う覚悟がある者の呼びかけにしか応えない。そしてひとたび彼女の憤怒を向けられた者は決して破滅から逃れることはできない。非情なる狩手が交わした契約は常に、彼女の魂が放つ冷たい槍で完了の印を捺されるのだ。", "allytips": ["「引き裂く遺恨」は、敵にとどめを刺すのに便利なスキルだ。「引き裂く遺恨」で対象の体力をゼロにすると、このスキルのクールダウンが解消される。", "移動指示を1回出して「戦の所作」を使用しても、通常攻撃の対象指定はキャンセルされない。", "「戦の所作」の効果により、攻撃速度を上げることで移動速度を強化できる。"], "enemytips": ["カリスタの機動性能は通常攻撃に依存しているため、こちらが射程距離外に留まっていれば、カリスタの機動力を制限できる。また、攻撃速度を低下させることができれば、戦闘中の活動範囲も狭めることができる。", "カリスタは、通常攻撃の準備動作をキャンセルできない。移動能力の高いチャンピオンではあるが、攻撃を始めるタイミングを予測できれば、スキルを命中させやすい。", "茂みに隠れたり、戦場の霧の中に入るなどしてカリスタの視界から消えれば、通常攻撃の槍は命中せずに地面に落ちてしまう。"], "tags": ["Marksman"], "partype": "マナ", "info": {"attack": 8, "defense": 2, "magic": 4, "difficulty": 7}, "stats": {"hp": 560, "hpperlevel": 114, "mp": 300, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 6.3, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 4, "attackspeedperlevel": 4.5, "attackspeed": 0.694}, "spells": [{"id": "KalistaMysticShot", "name": "貫魂の一投", "description": "高速で飛ぶ槍を投げる。命中した敵の体力がゼロになると、槍がその敵を貫通する。", "tooltip": "槍を投げて最初に命中した敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。これで対象をキルすると槍は飛び続け、<spellName>「引き裂く遺恨」</spellName>のスタックが次に命中した対象に持ち越される。<br /><br />このスキルを使用後、<spellName>「戦の所作」</spellName>を使ってダッシュできる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "ダメージ"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "KalistaMysticShot.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KalistaW", "name": "執念の霊魂", "description": "カリスタと「魂盟の同志」が同じ対象を攻撃すると追加ダメージを与える。<br><br>スキルを発動すると霊魂を飛ばして周辺を偵察させ、霊魂の前方エリアを可視状態にする。", "tooltip": "<spellPassive>自動効果: </spellPassive>自身と<keywordMajor>「魂盟の同志」</keywordMajor>が同じ対象に通常攻撃すると、対象の最大体力の<magicDamage>{{ maxhealthdamage*100 }}%にあたる魔法ダメージ</magicDamage>を与える。この効果は、対象ごとに{{ pertargetcooldown }}秒のクールダウンがあり、チャンピオン以外には{{ maximummonsterdamage }}の上限がある。<br /><br /><spellPassive>発動効果: </spellPassive>霊魂を飛ばしてエリアを偵察させる。この際、霊魂はエリア内を3往復する。霊魂が発見したチャンピオンは4秒間可視状態になる。このスキルは2回までチャージできる({{ ammorechargetooltip }}秒でリチャージ)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最大体力ダメージ", "リチャージ時間", "モンスターへのダメージ上限"], "effect": ["{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ ammorechargetooltip }} -> {{ ammorechargetooltipNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}"]}, "maxrank": 5, "cooldown": [30, 30, 30, 30, 30], "cooldownBurn": "30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 80, 80, 80, 80], [0, 0, 0, 0, 0], [125, 150, 175, 200, 225], [90, 80, 70, 60, 50], [0, 0, 0, 0, 0], [75, 100, 125, 150, 175], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80", "0", "125/150/175/200/225", "90/80/70/60/50", "0", "75/100/125/150/175", "10", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "2", "range": [5000, 5000, 5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "KalistaW.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "KalistaExpungeWrapper", "name": "引き裂く遺恨", "description": "通常攻撃するたびに、対象に槍の幻影が残る。発動すると槍の幻影が炸裂し、対象に刺さった槍の本数に比例するダメージを与え、スロウ効果を付与する。", "tooltip": "<spellPassive>自動効果: </spellPassive>通常攻撃が命中した敵に4秒間槍の幻影が残る。槍は何回でもスタックする。<br /><br /><spellActive>発動効果:</spellActive> 範囲内の敵に刺さった槍が炸裂し、<physicalDamage>{{ normaldamage }}</physicalDamage> + 2本目以降の槍1本につき<physicalDamage>{{ additionaldamage }}の物理ダメージ</physicalDamage>を与える。また、命中した敵に{{ slowduration }}秒間、<attention>{{ totalslowamount }}</attention>の<status>スロウ効果</status>を付与する。<br /><br />このスキルで敵を倒すと、このスキルのクールダウンが解消され、<scaleMana>{{ manarefund }}マナ</scaleMana>が還元される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "1スタックごとのダメージ", "1スタックごとの攻撃力反映率", "スロウ効果", "マナ還元", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ additionalbasedamage }} -> {{ additionalbasedamageNL }}", "{{ additionaladratio*100.000000 }}% -> {{ additionaladrationl*100.000000 }}%", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ manarefund }} -> {{ manarefundNL }}", "{{ fakedcooldown }} -> {{ fakedcooldownNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "2", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaExpungeWrapper.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KalistaRx", "name": "宿命の呼び声", "description": "「魂盟の同志」を強制的に自身の近くに吸い寄せる。カリスタの元に吸い寄せられた「魂盟の同志」は自分で指定した地点に突撃でき、範囲内にいる敵ユニットをわずかにノックバックさせる。", "tooltip": "最大4秒間、<keywordMajor>「魂盟の同志」</keywordMajor>を固有時停止状態にして自分の元に引き寄せる。<keywordMajor>「魂盟の同志」</keywordMajor>はクリックすることで飛び出すことができる。このとき、最初にチャンピオンと衝突した時点で停止し、周囲のすべての敵を<status>ノックバック</status>させる。<keywordMajor>「魂盟の同志」</keywordMajor>はチャンピオンに命中すると、その対象から通常攻撃の最大射程分離れた位置に着地する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ノックアップ効果時間"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [1, 1.5, 2], [40, 60, 80], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "1/1.5/2", "40/60/80", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaRx.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "戦の所作", "description": "通常攻撃または「貫魂の一投」の準備アクション中に移動指示を出すと、攻撃時にその方向へ跳躍して移動する。", "image": {"full": "Kalista_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}