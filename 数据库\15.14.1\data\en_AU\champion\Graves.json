{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Graves": {"id": "<PERSON>", "key": "104", "name": "<PERSON>", "title": "the Outlaw", "image": {"full": "Graves.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "104000", "num": 0, "name": "default", "chromas": false}, {"id": "104001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "104002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "104003", "num": 3, "name": "Crime City Graves", "chromas": false}, {"id": "104004", "num": 4, "name": "Riot Graves", "chromas": false}, {"id": "104005", "num": 5, "name": "Pool Party Graves", "chromas": true}, {"id": "104006", "num": 6, "name": "Cutthroat Graves", "chromas": false}, {"id": "104007", "num": 7, "name": "Snow Day Graves", "chromas": true}, {"id": "104014", "num": 14, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "104018", "num": 18, "name": "Praetorian Graves", "chromas": true}, {"id": "104025", "num": 25, "name": "Battle Professor <PERSON>", "chromas": true}, {"id": "104035", "num": 35, "name": "Sentinel Graves", "chromas": true}, {"id": "104042", "num": 42, "name": "EDG Graves", "chromas": true}, {"id": "104045", "num": 45, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "<PERSON> is a renowned mercenary, gambler, and thief—a wanted man in every city and empire he has visited. Even though he has an explosive temper, he possesses a strict sense of criminal honor, often enforced at the business end of his double-barreled shotgun Destiny. In recent years, he has reconciled a troubled partnership with <PERSON><PERSON><PERSON> <PERSON>, and together they have prospered once more in the turmoil of Bil<PERSON><PERSON>'s criminal underbelly.", "blurb": "<PERSON> is a renowned mercenary, gambler, and thief—a wanted man in every city and empire he has visited. Even though he has an explosive temper, he possesses a strict sense of criminal honor, often enforced at the business end of his...", "allytips": ["Smoke Screen can be used both to escape and to set up a kill.", "Using Quickdraw to get into range and delivering a point blank Buckshot can do lots of damage."], "enemytips": ["Graves deals mostly physical damage so Armor is an effective counter.", "Leaving the Smoke Screen's area of effect removes the harmful effect immediately."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 325, "mpperlevel": 40, "movespeed": 340, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 425, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.475}, "spells": [{"id": "GravesQLineSpell", "name": "End of the Line", "description": "<PERSON> fires an explosive shell that detonates after 1 second, or after colliding with terrain.", "tooltip": "Graves fires a powder round dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>. After 1 second or after colliding with terrain, it detonates, dealing <physicalDamage>{{ totaldetonationdamage }} physical damage</physicalDamage> along the travel path again and to enemies near the blast.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Detonation Damage", "Detonation AD Ratio", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedetonationdamage }} -> {{ basedetonationdamageNL }}", "{{ baddetonationratio*100.000000 }}% -> {{ baddetonationrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.25, 9.5, 7.75, 6], "cooldownBurn": "13/11.25/9.5/7.75/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "GravesQLineSpell.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesSmokeGrenade", "name": "Smoke Screen", "description": "Graves fires a smoke canister at the target area creating a cloud of smoke that reduces sight range. Enemies caught in the initial impact are dealt magic damage and have their Move Speed reduced briefly.", "tooltip": "<PERSON> creates a cloud of black smoke for 4 seconds, <status>Slowing</status> enemies inside by {{ e2 }}% and blocking their vision outside the area. The initial impact deals <magicDamage>{{ impactdamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24, 22, 20, 18], "cooldownBurn": "26/24/22/20/18", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [60, 110, 160, 210, 260], [50, 50, 50, 50, 50], [200, 200, 200, 200, 200], [4, 4, 4, 4, 4], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/110/160/210/260", "50", "200", "4", "0.5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "GravesSmokeGrenade.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesMove", "name": "Quickdraw", "description": "<PERSON> dashes forward gaining an Armor boost for several seconds. If <PERSON> dashes towards an enemy champion, gain two stacks of True Grit instead. Hitting enemies with basic attacks lowers the cooldown of this skill and refreshes the resistance boost.", "tooltip": "<PERSON> dashes and reloads one <keywordMajor>Shell</keywordMajor> into his shotgun. He also gains a stack for 4 seconds (max {{ e0 }} stacks) or two stacks if he dashes towards an enemy champion. Stacks grant him <scaleArmor>{{ e5 }} Armor</scaleArmor>. Stacks are refreshed when damaging a non-minion.<br /><br />Each bullet hit from Graves' Attacks reduces this Ability's Cooldown by {{ e4 }} seconds.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Armor", "Cooldown"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [30, 40, 50, 60, 70], [4, 4, 4, 4, 4], [20, 25, 30, 35, 40], [0.5, 0.5, 0.5, 0.5, 0.5], [4, 7, 10, 13, 16], [750, 750, 750, 750, 750], [375, 375, 375, 375, 375], [275, 275, 275, 275, 275], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8]], "effectBurn": [null, "30/40/50/60/70", "4", "20/25/30/35/40", "0.5", "4/7/10/13/16", "750", "375", "275", "60", "8"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "GravesMove.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesChargeShot", "name": "Collateral Damage", "description": "<PERSON> fires an explosive shell dealing heavy damage to the first champion it hits. After hitting a champion or reaching the end of its range, the shell explodes dealing damage in a cone.", "tooltip": "<PERSON> fires an explosive cartridge, blasting himself backwards. The cartridge deals <physicalDamage>{{ damage }} physical damage</physicalDamage> to the first enemy hit. After hitting an enemy champion or reaching the end of its range, the cartridge explodes outward, dealing <physicalDamage>{{ falloffdamage }} physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Primary Damage", "Cone Damage", "Cooldown"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rfalloffdamage }} -> {{ rfalloffdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GravesChargeShot.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "New Destiny", "description": "<PERSON>' shotgun has some unique properties. He must reload when he runs out of ammo. Attacks fire 4 bullets, which cannot pass through units. Non-champions struck by multiple bullets are knocked back.", "image": {"full": "GravesTrueGrit.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}