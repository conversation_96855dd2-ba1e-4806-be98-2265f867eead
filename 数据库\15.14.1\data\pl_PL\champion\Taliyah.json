{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taliyah": {"id": "Taliyah", "key": "163", "name": "Taliyah", "title": "Tkaczka Skał", "image": {"full": "Taliyah.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "163000", "num": 0, "name": "default", "chromas": false}, {"id": "163001", "num": 1, "name": "Taliyah z Freljordu", "chromas": false}, {"id": "163002", "num": 2, "name": "SSG Taliyah", "chromas": false}, {"id": "163003", "num": 3, "name": "Basenowa Taliyah", "chromas": true}, {"id": "163011", "num": 11, "name": "Czarodziejka Gwiazd Taliyah", "chromas": true}, {"id": "163021", "num": 21, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Taliyah to wędrowna czarodziejka z Shurimy, rozdarta pomiędzy młodzieńczym zachwytem a dorosłą odpowiedzialnością. Przemierzyła prawie cały Valoran podczas podróży, której celem jest nauka panowania nad jej rosnącymi mocami, choć ostatnio powróciła, by chronić swoje plemię. Niektórzy odczytali jej skłonność do współczucia jako oznakę słabości i gorzko zapłacili za ten błąd. Pod młodzieńczą postawą Taliyah kryje się wola mogąca przenosić góry i duch tak niezłomny, że aż ziemia drży pod jej stopami.", "blurb": "Taliyah to wędrowna czarodziejka z Shurimy, rozdarta pomiędzy młodzieńczym zachwytem a dorosłą odpowiedzialnością. Przemierzyła prawie cały Valoran podczas podróży, której celem jest nauka panowania nad jej rosnącymi mocami, choć ostatnio powróciła, by...", "allytips": ["Postaraj się rzucić wrogów na Rozprutą Ziemię za pomocą Sejsmicznego Wstrząsu.", "<PERSON><PERSON><PERSON><PERSON><PERSON>, że nie zawsze musisz podążać wraz ze Ścianą Tkaczki.", "Po zakupieniu Kryształowego Kostura Rylai, użycie Utkanej Salwy na ścigających wrogach sprawi, że bardzo tego pożałują."], "enemytips": ["Kiedy Taliyah użyje Rozprutej Ziemi w alei możesz zał<PERSON>, że kolejny będzie Sejsmiczny Wstrząs. Je<PERSON>li rzuci cię na pole minowe, to poczujesz to w kościach.", "Utkana Salwa Taliyah zostanie skierowana w raz wybranym kierunku. Jeśli kamienie lecą w twoją stronę, wykonaj unik w bok!"], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 550, "hpperlevel": 104, "mp": 470, "mpperlevel": 30, "movespeed": 330, "armor": 18, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 6.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1.36, "attackspeed": 0.658}, "spells": [{"id": "TaliyahQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Taliyah ciska salwą kamieni w wybranym kierunku. W czasie używania umiejętności może się swobodnie poruszać. W efekcie pod nią powstaje Wyjałowiona Ziemia. Jeśli Taliyah użyje Utkanej Salwy na Wyjałowionej Ziemi, pochłania ją, aby cisnąć wię<PERSON>zą skałą, która spowalnia wrogów.", "tooltip": "Taliyah ciska 5 kamieniami, spośr<PERSON>d kt<PERSON><PERSON><PERSON> każdy zadaje <magicDamage>{{ rockdamage }} pkt. obrażeń magicznych</magicDamage> na obszarze dookoła pierwszego trafionego wroga, tworząc pod sobą Wyjałowioną Ziemię. Kolejne trafienia tego samego wroga zadają o {{ extramissilereduceddamagepercent }}% mniej obrażeń.<br /><br />Użycie na Wyjałowionej Ziemi kosztuje {{ e7 }} pkt. many, skraca czas odnowienia o {{ workedgroundcdr*100 }}%, zużywa Wyjałowioną Ziemię i ciska kamieniem, który <status>spowalnia</status> trafionych wrogów o {{ slowpercent*100 }}% na {{ slowduration }} sek. i zadaje <magicDamage>{{ bigrockdamage }} pkt. obrażeń magicznych</magicDamage> głównemu celowi. Potwory trafione głazem zostają <status>ogłuszone</status> na {{ monsterstunduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [65, 70, 75, 80, 85], "costBurn": "65/70/75/80/85", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [30, 30, 30, 30, 30], [20, 20, 20, 20, 20], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "30", "20", "400", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "TaliyahQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TaliyahWVC", "name": "Sejsmiczny Wstrząs", "description": "<PERSON><PERSON><PERSON>, że ziemia na pewnym obszarze wybucha, a następnie może rzucić znajdującymi się tam wrogami w wybranym kierunku.", "tooltip": "Ta<PERSON>yah wstrzą<PERSON> zie<PERSON>, <status>odpychając</status> wrogów na danym obszarze w wybranym kierunku.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0.5", "400", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TaliyahWVC.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TaliyahE", "name": "Roz<PERSON><PERSON><PERSON>", "description": "Taliyah tworzy spowalniające pole minowe. Kiedy wrogowie na nie doskoczą albo zostaną przez nie przep<PERSON>ęci, miny eksplodują i ich ogłuszają.", "tooltip": "Taliyah miota znajdującymi się w pobliżu kamieniami, <status>spowal<PERSON>j<PERSON>c</status> trafionych wrogów o {{ slowpercent*100 }}% i zadając im <magicDamage>{{ scatterdamage }} pkt. obrażeń magicznych</magicDamage>. <PERSON><PERSON><PERSON> wybuchają, gdy wrogowie wykonają nad nimi doskok lub zostaną przez nie <status>prz<PERSON><PERSON><PERSON><PERSON><PERSON></status>, <status>ogł<PERSON><PERSON><PERSON><PERSON></status> ich na czas pozostały do zakończenia ruchu + {{ stunduration }} sek. i zadając <magicDamage>{{ detonationdamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Obrażenia wybuchu", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedetonationdamage }} -> {{ basedetonationdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [85, 85, 85, 85, 85], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "4", "85", "0", "4", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "TaliyahE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TaliyahR", "name": "Ściana Tkaczki", "description": "<PERSON><PERSON><PERSON> tworzy bardzo długą <PERSON>, a później podróżuje na jej szczycie.", "tooltip": "Taliyah tworzy potężną ścianę z ziemi na {{ e1 }} sek. <PERSON><PERSON><PERSON> natychmiast <recast>ponownie użyje</recast> tej superumi<PERSON>, zacznie przemieszczać się wzdłuż powstającej ściany. Poruszenie się lub otrzymanie efektu unieruchomienia zakończy ruch.<br /><br />Tej umiej<PERSON>tnoś<PERSON> nie można u<PERSON>, jeżeli Taliyah otrzymała obrażenia od bohaterów lub budowli w ciągu ostatnich {{ damagelockouttime }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Czas odnowienia"], "effect": ["{{ walllength }} -> {{ walllengthNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [4, 4, 4], [2500, 4500, 6500], [0.1, 0.1, 0.1], [2500, 2500, 2500], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "4", "2500/4500/6500", "0.1", "2500", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [2500, 4500, 6500], "rangeBurn": "2500/4500/6500", "image": {"full": "TaliyahR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Skalna <PERSON>", "description": "Taliyah zyskuje prędkość ruchu w pobliżu ściań.", "image": {"full": "Taliyah_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}