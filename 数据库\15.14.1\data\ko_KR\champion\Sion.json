{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sion": {"id": "Sion", "key": "14", "name": "사이온", "title": "언데드 학살병기", "image": {"full": "Sion.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "14000", "num": 0, "name": "default", "chromas": false}, {"id": "14001", "num": 1, "name": "마법공학 사이온", "chromas": false}, {"id": "14002", "num": 2, "name": "야만전사 사이온", "chromas": false}, {"id": "14003", "num": 3, "name": "나무꾼 사이온", "chromas": false}, {"id": "14004", "num": 4, "name": "전쟁광 사이온", "chromas": false}, {"id": "14005", "num": 5, "name": "메카 제로 사이온", "chromas": true}, {"id": "14014", "num": 14, "name": "파괴의 신 사이온", "chromas": true}, {"id": "14022", "num": 22, "name": "어둠서리 사이온", "chromas": true}, {"id": "14030", "num": 30, "name": "하이 눈 사이온", "chromas": true}, {"id": "14040", "num": 40, "name": "우주 성기사 사이온", "chromas": true}, {"id": "14049", "num": 49, "name": "장대한 심판 사이온", "chromas": false}], "lore": "과거 사이온은 데마시아 왕을 맨손으로 목 졸라 살해한 전공으로 녹서스에서 존경을 받았다. 이후에는 지나간 시대의 전쟁 영웅으로 잊혀질 뻔했으나, 죽은 후에 부활하여 다시금 녹서스에 충성을 다하게 되었다. 부활한 사이온은 썩어버린 육체에 조악한 방어구를 고정시켜 걸치고, 자신의 앞을 가로막는 자라면 충성 여부에 상관없이 닥치는 대로 학살했다. 죽기 전의 인간성이 더 이상 남아 있지 않은 것이었다. 하지만 그렇게 앞뒤 재지 않고 무작정 전장으로 뛰어들면서도, 사이온이 휘두르는 도끼날에는 진정한 자아를 기억해 내고 싶다는 열망이 숨어 있다.", "blurb": "과거 사이온은 데마시아 왕을 맨손으로 목 졸라 살해한 전공으로 녹서스에서 존경을 받았다. 이후에는 지나간 시대의 전쟁 영웅으로 잊혀질 뻔했으나, 죽은 후에 부활하여 다시금 녹서스에 충성을 다하게 되었다. 부활한 사이온은 썩어버린 육체에 조악한 방어구를 고정시켜 걸치고, 자신의 앞을 가로막는 자라면 충성 여부에 상관없이 닥치는 대로 학살했다. 죽기 전의 인간성이 더 이상 남아 있지 않은 것이었다. 하지만 그렇게 앞뒤 재지 않고 무작정 전장으로...", "allytips": ["멈출 수 없는 맹공 중에는 방향을 바꿀 수 있는 폭이 매우 좁으므로 직선 방향으로 사용하세요.", "학살자의 포효를 잘 사용하면 강력한 대량 학살 강타를 맞힐 수 있습니다.", "영혼의 용광로 효과에는 현재 남아있는 보호막의 체력이 표시되므로, 이 정보를 활용하여 폭발시킬 정확한 타이밍을 잡으세요."], "enemytips": ["사이온이 대량 학살 강타로 내리칠 때, 충전량을 미리 방출하게 하면 피해량을 줄일 수 있습니다.", "사이온이 죽은 다음에는 다시 유리한 위치를 잡아서 부활한 다음을 노리세요."], "tags": ["Tank", "Fighter"], "partype": "마나", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 655, "hpperlevel": 87, "mp": 400, "mpperlevel": 52, "movespeed": 345, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 1.3, "attackspeed": 0.679}, "spells": [{"id": "SionQ", "name": "대량 학살 강타", "description": "사이온이 강력한 한 방을 충전하여 전방의 적들에게 피해를 입힙니다. 충분한 시간 동안 충전하면, 이 타격에 맞은 적들을 공중으로 띄우고 기절시킵니다.", "tooltip": "<charge>충전 시작 시</charge>: 사이온이 최대 2초간 강력한 일격을 충전합니다.<br /><br /><release>발사 시</release>: 사이온이 도끼를 내리쳐 적들을 잠시 <status>둔화</status>시키고 충전 시간에 비례해 <physicalDamage>{{ mindamagetotal }}~{{ maxdamagetotal }}의 물리 피해</physicalDamage>를 입힙니다. 최소 1초 이상 충전했다면 적들을 <status>공중으로 띄워 올리고</status> {{ basestuntime }}~2.25초 충전했다면 <status>기절</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최소 피해량", "최대 피해량", "공격력 계수", "공격력 계수", "재사용 대기시간"], "effect": ["{{ lowdamage }} -> {{ lowdamageNL }}", "{{ highdamage }} -> {{ highdamageNL }}", "{{ adratiomin*100.000000 }}% -> {{ adratiominnl*100.000000 }}%", "{{ adratiomax*100.000000 }}% -> {{ adratiomaxnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [7.5, 7.5, 7.5, 7.5, 7.5], [60, 60, 60, 60, 60], [150, 150, 150, 150, 150], [-0.8, -0.8, -0.8, -0.8, -0.8]], "effectBurn": [null, "0", "0", "0", "0", "2.5", "0", "7.5", "60", "150", "-0.8"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "SionQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SionW", "name": "영혼의 용광로", "description": "사이온이 자기 자신에게 보호막을 씌우고 3초 후에 재시전하면 주변 적들에게 마법 피해를 입힙니다.", "tooltip": "<spellPassive>기본 지속 효과</spellPassive>: 사이온은 유닛을 하나 처치할 때마다 <scaleHealth>최대 체력이 {{ hpperkill }}</scaleHealth> 증가합니다. 챔피언 처치 관여, 대형 미니언 또는 대형 몬스터 처치 시 최대 체력이 {{ hpperchampkill }} 증가합니다.<br /><br /><spellActive>사용 시</spellActive>: 사이온이 6초간 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 얻습니다. {{ e7 }}초 후에 보호막이 지속 중이라면 스킬을 <recast>재사용</recast>하여 보호막을 폭발시키고 <magicDamage>{{ totaldamage }}+대상 최대 체력의 {{ e4 }}%에 해당하는 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "피해량", "최대 체력 비례 보호막", "마나 소모량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ shieldpercenthealthtooltip*100.000000 }}% -> {{ shieldpercenthealthtooltipnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [65, 70, 75, 80, 85], "costBurn": "65/70/75/80/85", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [40, 65, 90, 115, 140], [8, 10, 12, 14, 16], [14, 14, 14, 14, 14], [4, 4, 4, 4, 4], [15, 15, 15, 15, 15], [3, 3, 3, 3, 3], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "40/65/90/115/140", "8/10/12/14/16", "14", "4", "15", "3", "15", "6", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "SionW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SionE", "name": "학살자의 포효", "description": "사이온이 단거리 충격파를 발사해 첫 번째로 맞는 적에게 피해를 입히고 속도를 늦추며, 방어력을 낮춥니다. 충격파가 미니언이나 몬스터를 맞히면 뒤로 밀려나며 피해를 입고 둔화되며 관통하는 모든 적의 방어력을 낮춥니다.", "tooltip": "사이온이 충격파를 발사해 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초간 적들을 {{ slowamount }}% <status>둔화</status>시키며, {{ armorshredduration }}초간 <scaleArmor>방어력을 {{ armorshred }}%</scaleArmor> 감소시킵니다. 챔피언이 아닌 대상이 공격에 적중당하면 <status>뒤로 밀려납니다</status>. <status>뒤로 밀려난</status> 유닛에게 부딪힌 적들에게는 동일한 피해와 효과가 적용됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [40, 45, 50, 55, 60], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "40/45/50/55/60", "5", "40/45/50/55/60", "20", "30", "4", "2.5", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SionE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SionR", "name": "멈출 수 없는 맹공", "description": "사이온이 한 방향으로 돌진하며 점차 속도가 상승합니다. 돌진 방향은 마우스 커서 위치로 약간 조종할 수 있습니다. 적과 충돌하면 돌진한 거리에 비례하여 피해를 입히고 띄워올립니다.", "tooltip": "사이온이 8초 동안 저지 불가 상태가 되어 마우스 커서 방향으로 돌진합니다. 사이온이 적 챔피언이나 벽과 충돌하거나 스킬을 <recast>재사용</recast>하면 멈춥니다.  <br /><br />돌진이 끝나면 이동 거리에 비례해 <physicalDamage>{{ mindamagetotal }}~{{ maxdamagetotal }}의 물리 피해</physicalDamage>를 입힙니다. 사이온 주변에 있는 적들은 이동 거리에 비례해 {{ minstunduration }}~{{ maxstunduration }}초간 <status>기절</status>합니다. 더 넓은 범위에 있는 적들은 3초간 {{ slowamount }}% <status>둔화</status>됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최소 피해량", "최대 피해량", "둔화", "재사용 대기시간"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 100, 60], "cooldownBurn": "140/100/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "SionR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "영광스러운 죽음", "description": "사이온은 사망한 이후 잠시 되살아나지만 체력이 급속히 떨어집니다. 이 동안 사이온은 매우 빠르게 공격하며 체력을 회복합니다. 공격 적중 시 대상 최대 체력에 비례해 추가 피해를 입힙니다.", "image": {"full": "Sion_Passive1.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}