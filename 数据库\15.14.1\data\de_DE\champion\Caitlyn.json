{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Caitlyn": {"id": "<PERSON><PERSON><PERSON>", "key": "51", "name": "<PERSON><PERSON><PERSON>", "title": "der <PERSON>", "image": {"full": "Caitlyn.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "51000", "num": 0, "name": "default", "chromas": true}, {"id": "51001", "num": 1, "name": "Widerstands-Caitlyn", "chromas": false}, {"id": "51002", "num": 2, "name": "Sheriff <PERSON>", "chromas": false}, {"id": "51003", "num": 3, "name": "Safari-Caitlyn", "chromas": false}, {"id": "51004", "num": 4, "name": "Arktische Caitlyn", "chromas": false}, {"id": "51005", "num": 5, "name": "Officer <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51006", "num": 6, "name": "Kopfjäger-Caitlyn", "chromas": false}, {"id": "51010", "num": 10, "name": "Mondge<PERSON>", "chromas": true}, {"id": "51011", "num": 11, "name": "Pulsfeuer-C<PERSON><PERSON>", "chromas": true}, {"id": "51013", "num": 13, "name": "Poolparty-Caitlyn", "chromas": true}, {"id": "51019", "num": 19, "name": "Arcade-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51020", "num": 20, "name": "Arcade-<PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "51022", "num": 22, "name": "Academia Certaminis-Caitlyn", "chromas": true}, {"id": "51028", "num": 28, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Arcane)", "chromas": false}, {"id": "51029", "num": 29, "name": "Arcade-C<PERSON><PERSON> (Prestige 2022)", "chromas": false}, {"id": "51030", "num": 30, "name": "Sc<PERSON><PERSON>mond-Caitlyn", "chromas": true}, {"id": "51039", "num": 39, "name": "Herzensdame-C<PERSON>lyn", "chromas": true}, {"id": "51048", "num": 48, "name": "DRX-C<PERSON><PERSON>", "chromas": true}, {"id": "51050", "num": 50, "name": "<PERSON><PERSON><PERSON><PERSON> (Arcane)", "chromas": true}, {"id": "51051", "num": 51, "name": "<PERSON><PERSON><PERSON><PERSON> (Arcane) (Prestige)", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> hat den Ruf, die beste Friedenswächterin Piltovers zu sein, und ist die erste Wahl, wenn es darum geht, die Stadt von schwer zu fassenden Kriminellen zu befreien. <PERSON>e wird oft zusammen mit <PERSON><PERSON>, da ihre kühle Art ein Gegenpol zur Impulsivität ihrer Partnerin ist. Trotz ihres einzigartigen Hextech-Gewehrs ist Caitlyns mächtigste Waffe ihr überragender Intellekt, dank dem sie Gesetzesbrechern, die sich in die Stadt des Fortschritts gewagt haben, durchdachte Fallen stellen kann.", "blurb": "<PERSON><PERSON><PERSON> hat den Ruf, die beste Friedenswächterin Piltovers zu sein, und ist die erste Wahl, wenn es darum geht, die Stadt von schwer zu fassenden Kriminellen zu befreien. Sie wird oft zusammen mit Vi eingesetzt, da ihre kühle Art ein Gegenpol...", "allytips": ["Nutze ihre „Yordle-Schlagfalle“, indem du sie zeitig so aufstellst, dass du im Kampf immer eine bereit hast.", "Vermeide es, „Ass im Ärmel“ in großen Teamkämpfen zu benutzen, denn der Schuss könnte so zu leicht den falschen Champion treffen.", "<PERSON><PERSON>e das „Kaliber 90-Netz“ vom Gegner weg, um aufzuholen oder Hindernisse zu überspringen."], "enemytips": ["Bleibe hinter verbündeten Vasallen, wenn Caitlyn auf „Piltover-Friedensstifter“ setzt, da Folgetreffer immer weniger Schaden verursachen.", "Du kannst das Projektil von „Ass im Ärmel“ für einen Verbündeten abfangen, indem du dich einfach selbst in die Schussbahn wirfst."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 6}, "stats": {"hp": 580, "hpperlevel": 107, "mp": 315, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 650, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.8, "attackspeedperlevel": 4, "attackspeed": 0.681}, "spells": [{"id": "CaitlynQ", "name": "Piltover-Friedensstifter", "description": "Caitlyn legt ihr Gewehr 1 Sekunde lang an und feuert einen durchdringenden Schuss ab, der normalen Schaden verursacht (weitere von „Piltover-Friedensstifter“ getroffene Gegner erleiden weniger Schaden).", "tooltip": "Cait<PERSON> legt ihr Gewehr an und feuert einen durchdringenden Schuss ab, der <physicalDamage>{{ initialdamage }}&nbsp;normalen Schaden</physicalDamage> verursacht. Sobald das erste Ziel getroffen wurde, breitet sich das Geschoss aus und verursacht <physicalDamage>{{ secondarydamage }}&nbsp;normalen Schaden</physicalDamage>.<br /><br />Von <spellName>Yordle-Schlagfalle</spellName> aufgedeckte Gegner erleiden immer den vollen Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit Gesamtangriffsschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ tadratio*100.000000 }}&nbsp;% -> {{ tadrationl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1, 1, 1, 1, 1], [1.3, 1.45, 1.6, 1.75, 1.9], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1", "1.3/1.45/1.6/1.75/1.9", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "CaitlynQ.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynW", "name": "Yordle-Schlagfalle", "description": "Caitlyn platziert eine Falle. Wenn ein gegnerischer Champion die Falle auslöst, wird er 1,5&nbsp;Sekunden aufgedeckt und bewegungsunfähig gemacht. Caitlyn erhält dabei die Chance auf einen verstärkten Kopfschuss.", "tooltip": "Cait<PERSON> legt eine <PERSON>e, die den ersten Champion, der auf sie tritt, {{ e6 }}&nbsp;Sekunden lang <status>festhält</status>. Zusätzlich erhält Caitlyn 3&nbsp;Sekunden lang <keywordStealth>absolute Sicht</keywordStealth> auf diesen Champion. Fallen bleiben {{ e3 }}&nbsp;Sekunden lang bestehen. Es können bis zu {{ e5 }}&nbsp;Fallen gleichzeitig aktiv sein. Diese Fähigkeit hat {{ e5 }}&nbsp;Aufladungen ({{ ammorechargetime }}&nbsp;Sekunden Aufladungszeit).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kopfschuss: Zusätzlicher Schaden an Zielen in Fallen", "Wiederaufladungsrate", "Maximale Fallenanzahl", "Fallenbes<PERSON>dsdauer"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [35, 80, 125, 170, 215], [30, 35, 40, 45, 50], [3, 3, 4, 4, 5], [3, 3, 4, 4, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "35/80/125/170/215", "30/35/40/45/50", "3/3/4/4/5", "3/3/4/4/5", "1.5", "30", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CaitlynW.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynE", "name": "Kaliber 90-<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> feuert ein schweres Netz, das ihr Ziel verlangsamt und sie zurückstößt.", "tooltip": "<PERSON><PERSON><PERSON> feuert ein Netz ab und wird dadurch zurückgestoßen. Das Netz <status>verlangsamt</status> das erste getroffene Ziel {{ e2 }}&nbsp;Sekunde lang um {{ e3 }}&nbsp;% und fügt ihm <magicDamage>{{ netdamage }}&nbsp;magischen <PERSON>haden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1, 1, 1, 1, 1], [50, 50, 50, 50, 50], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1", "50", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "CaitlynE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynR", "name": "Ass im Ärmel", "description": "<PERSON><PERSON><PERSON> nimmt sich die Zeit für einen perfekten Schuss, wonach sie an einem einzigen Ziel gewaltigen Schaden auf sehr große Distanz anrichtet. Gegnerische Champions können die Kugel für ihre Verbündeten abfangen.", "tooltip": "<PERSON><PERSON><PERSON> hält kurz inne und kanalisiert, um das Ziel ins Visier zu nehmen. Der daraus resultierende Schuss verursacht <physicalDamage>{{ rtotaldamage }}&nbsp;normalen Schaden</physicalDamage>, aber gegnerische Champions können die Kugel abfangen. Diese Fähigkeit gewährt während der Kanalisierung <keywordStealth>absolute Sicht</keywordStealth> auf das Ziel.<br /><br /><rules>Verursacht zusätzlich bis zu {{ critchanceamp*100 }}&nbsp;% Schaden abhäng<PERSON> von Caitlyns Chance auf kritische Treffer.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3500, 3500, 3500], "rangeBurn": "3500", "image": {"full": "CaitlynR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Alle paar normalen Angriffe sowie alle Angriffe auf Ziele, die in einer Falle oder einem Netz gefangen sind, ermöglichen Caitlyn einen Kopf<PERSON>uss, der zusätzlichen Schaden zufügt und mit ihrer Chance auf kritische Treffer skaliert. <PERSON><PERSON>, die in einer Falle oder einem Netz gefangen sind, verdoppelt sich Caitlyns Reichweite für „Kopfschuss“.", "image": {"full": "Caitlyn_Headshot.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}