{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sylas": {"id": "<PERSON><PERSON><PERSON>", "key": "517", "name": "<PERSON><PERSON><PERSON>", "title": "the Unshackled", "image": {"full": "Sylas.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "517000", "num": 0, "name": "default", "chromas": false}, {"id": "517001", "num": 1, "name": "Lunar <PERSON><PERSON>", "chromas": true}, {"id": "517008", "num": 8, "name": "Freljord Sylas", "chromas": true}, {"id": "517013", "num": 13, "name": "PROJECT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517014", "num": 14, "name": "Prestige PROJECT: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "517024", "num": 24, "name": "<PERSON> <PERSON>", "chromas": true}, {"id": "517034", "num": 34, "name": "Ashen <PERSON>", "chromas": true}, {"id": "517036", "num": 36, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517046", "num": 46, "name": "Dark Star Sylas", "chromas": true}], "lore": "Raised in one of <PERSON><PERSON><PERSON>'s lesser quarters, <PERSON><PERSON><PERSON> of Dregbourne has come to symbolize the darker side of the Great City. As a boy, his ability to root out hidden sorcery caught the attention of the notorious mageseekers, who eventually imprisoned him for turning those same powers against them. Having now broken free, <PERSON><PERSON><PERSON> lives as a hardened revolutionary, using the magic of those around him to destroy the kingdom he once served… and his band of outcast mage followers seems to grow by the day.", "blurb": "Raised in one of <PERSON><PERSON><PERSON>'s lesser quarters, <PERSON><PERSON><PERSON> of Dregbourne has come to symbolize the darker side of the Great City. As a boy, his ability to root out hidden sorcery caught the attention of the notorious mageseekers, who eventually imprisoned him...", "allytips": ["Wait for you or your enemy to reach low health before using Kingslayer for maximum effect.", "Space out your ability usage to get maximum effect from Petricite Burst.", "Clever use of enemy ultimates can open up new possibilities to approach teamfights."], "enemytips": ["<PERSON><PERSON><PERSON>'s healthbar can be deceptive, watch out for his <PERSON><PERSON>!", "Try to fight <PERSON><PERSON><PERSON> when he isn't able to take your ultimate."], "tags": ["Mage", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 122, "mp": 400, "mpperlevel": 70, "movespeed": 340, "armor": 29, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.55, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "<PERSON><PERSON>s<PERSON>", "name": "Chain Lash", "description": "<PERSON><PERSON><PERSON> lashes his chains out, intersecting at his targeted location dealing damage to and slowing enemies. <br><br>After a delay, magical energy explodes from the intersection point dealing damage.", "tooltip": "<PERSON><PERSON><PERSON> lashes his chains out, dealing <magicDamage>{{ damage }} magic damage</magicDamage> and <status>Slowing</status> by {{ slowamountcalc }} for {{ slowduration }} seconds. The place where the chains intersect explodes, dealing a further <magicDamage>{{ explosiondamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Explosion Damage", "Slow", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [775, 775, 775, 775, 775], "rangeBurn": "775", "image": {"full": "SylasQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasW", "name": "Kingslayer", "description": "<PERSON><PERSON><PERSON> lunges at an enemy with magical force dealing damage and healing himself against enemy champions.", "tooltip": "<PERSON>yla<PERSON> lunges at an enemy with magical force dealing <magicDamage>{{ mindamage }} magic damage</magicDamage>. Against champions, <PERSON><PERSON><PERSON> restores between <healing>{{ minhealing }}</healing> and <healing>{{ maxhealing }} Health</healing> based on his missing Health (max healing at or below {{ maxexecutethreshold*100 }}% Health).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Healing", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ healing }} -> {{ healingNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasE", "name": "Abscond / Abduct", "description": "<PERSON><PERSON><PERSON> dashes to a location. <PERSON><PERSON><PERSON> can recast the ability to throw his chains out, pulling himself to an enemy he hits.", "tooltip": "<PERSON><PERSON><PERSON> quickly dashes, and prepares a <recast>Recast</recast> for 3.5 seconds.<br /><br /><recast>Recast:</recast> <PERSON><PERSON><PERSON> throws his chains, pulling himself to the first enemy hit, dealing <magicDamage>{{ damage }} magic damage</magicDamage>, and <status>Knocking Up</status> for {{ knockupduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ pulldamage }} -> {{ pulldamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasR", "name": "Hijack", "description": "<PERSON><PERSON><PERSON> steals the enemy's ultimate ability and can cast it freely.", "tooltip": "<PERSON><PERSON><PERSON> hijacks an enemy champion, allowing him to cast a copy of their ultimate Ability, based on his Ultimate level and using his stats.<br /><br />Hijacking an enemy places a Cooldown on them for {{ pertargetcooldown }}% (modified by <PERSON><PERSON><PERSON>' A<PERSON> Has<PERSON>) of the enemy's ultimate's Cooldown, with a minimum of {{ minimumenemycooldown }} seconds, during which time <PERSON><PERSON><PERSON> cannot hijack them again.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 55, 30], "cooldownBurn": "80/55/30", "cost": [75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "SylasR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "After casting a spell, <PERSON><PERSON><PERSON> stores a charge of Petricite <PERSON><PERSON>. <PERSON><PERSON><PERSON>'s basic attacks will expend a charge and whirl his energized chains around him dealing bonus magic damage to enemies hit. While <PERSON><PERSON><PERSON> has a charge of Petric<PERSON> <PERSON><PERSON><PERSON>, he gains attack speed. ", "image": {"full": "SylasP.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}