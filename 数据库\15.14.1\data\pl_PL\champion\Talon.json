{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Talon": {"id": "Talon", "key": "91", "name": "Talon", "title": "Cień Ostrza", "image": {"full": "Talon.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "91000", "num": 0, "name": "default", "chromas": false}, {"id": "91001", "num": 1, "name": "Renegat Talon", "chromas": false}, {"id": "91002", "num": 2, "name": "Karmazynowy Talon", "chromas": false}, {"id": "91003", "num": 3, "name": "Talon Smocze Ostrze", "chromas": true}, {"id": "91004", "num": 4, "name": "SSW Talon", "chromas": false}, {"id": "91005", "num": 5, "name": "Talon Krwawego Księżyca", "chromas": false}, {"id": "91012", "num": 12, "name": "<PERSON>", "chromas": true}, {"id": "91020", "num": 20, "name": "Talon Czarnodrzew", "chromas": true}, {"id": "91029", "num": 29, "name": "Obumarła Róża Talon", "chromas": true}, {"id": "91038", "num": 38, "name": "Talon w Samo Południe", "chromas": true}, {"id": "91039", "num": 39, "name": "Talon w Samo Południe (Prestiżowy)", "chromas": false}, {"id": "91049", "num": 49, "name": "Talon Pierwotnej Zasadzki", "chromas": true}, {"id": "91059", "num": 59, "name": "Talon O<PERSON>ądu", "chromas": false}], "lore": "Talon jest nożem kryjącym się w cie<PERSON>, bezlitosnym zabójcą, gotow<PERSON> uderzy<PERSON> bez ostrzeżenia i uciec, zanim ktokolwiek się zorientuje. Zdobył niebezpieczną reputację na brutalnych ulicach Noxusu, gdzie zmuszony był <PERSON>, zabija<PERSON> i kraść, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>. Przygarnięty przez słynną rodzin<PERSON>, korzysta teraz ze swoich zabójczych umiejętności na rozkaz imperium, zabijając wrogich dowódców, kapitanów i bohaterów... jak i wszystkich Noxian wystarczająco głupich, by splamić swój honor w oczach panów.", "blurb": "Talon jest nożem kryjącym się w <PERSON>, bezlitosnym zabójcą, gotow<PERSON> uder<PERSON> bez ostrzeżenia i uciec, zanim ktokolwiek się zorientuje. Zdobył niebezpieczną reputację na brutalnych ulicach Noxusu, gdzie zmuszony był wa<PERSON>, zabijać i kraść, by...", "allytips": ["Ścieżka Zabójcy przydaje się, aby dostać się za linię wroga i zająć dogodną pozycję do skorzystania z Noxiańskiej Dyplomacji.", "Napaść z Cienia to bardzo dobry sposób na ucieczkę, ale może również służyć do atakowania grup.", "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby przed walką wybrać cel. Skupienie wszystkich umiejętności Talona na jednym celu może przynieść wspaniałe efekty, natomiast rozdzielenie ich między kilka celów może oznaczać twoją śmierć."], "enemytips": ["Ataki Talona zadają wyłącznie obrażenia fizyczne. <PERSON>by przeci<PERSON><PERSON> jego eksplozywnym obrażeniom, szyb<PERSON> zakup pancerz.", "W celu ucieczki z walki Talon polega na Napaści z Cienia. Kiedy umiejętność nie jest dostępna, staje się o wiele podatniejszy na atak.", "Talon jest wręcz stworzony do wędrowania. Upewnij się, że na bieżąco śledzisz jego pozycję, albo przez naciskanie go wymuś na nim pozostanie w alei."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 658, "hpperlevel": 109, "mp": 400, "mpperlevel": 37, "movespeed": 335, "armor": 30, "armorperlevel": 4.7, "spellblock": 36, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 7.6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.9, "attackspeed": 0.625}, "spells": [{"id": "TalonQ", "name": "Noxiańska Dyplomacja", "description": "Talon dźga wybraną jednostkę. <PERSON>dy jest ona w zasięgu, atak zadaje obrażenia krytyczne. Gdy pozostaje ona poza zasięgiem broni białej, Talon doskoczy do niej przed zadaniem dźgnięcia. <PERSON><PERSON><PERSON> atak zabije jednostkę, Talon odzyskuje nieco zdrowia i czasu odnowienia.", "tooltip": "Talon skacze do celu, z<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ leapdamage }} pkt. obrażeń fizycznych</physicalDamage>. Jeśli użyje tej umiej<PERSON> w zwar<PERSON><PERSON>, trafi ona <PERSON>, z<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ criticaldamage }} pkt. obrażeń fizycznych</physicalDamage>.<br /><br />Jeśli ta umiejętność zabije cel, jej czas odnowienia skróci się o {{ cooldownrefund*100 }}%, a Talon przywróci sobie <healing>{{ totalhealing }} pkt. zdrowia</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "TalonQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TalonW", "name": "Rozpruwanie", "description": "Talon rzuca wieloma sztyletami, które wracają mu do ręki, a każde przejście przez wroga zadaje obrażenia. Powracające ostrza zadają dodatkowe obrażenia i spowalniają trafioną jednostkę.", "tooltip": "Talon ciska salwą ostrzy, z<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totalinitialdamage }} pkt. obrażeń fizycznych</physicalDamage>. Następnie ostrza powracają do niego, zadając <physicalDamage>{{ totalreturndamage }} pkt. obrażeń fizycznych</physicalDamage> i <status>spowalniają</status> o {{ movespeedslow*100 }}% na {{ slowduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Koszt (@AbilityResourceName@)", "Obrażenia początkowe", "Obrażenia przy powrocie", "Spowolnienie", "Czas odnowienia"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ initialbasedamage }} -> {{ initialbasedamageNL }}", "{{ returnbasedamage }} -> {{ returnbasedamageNL }}", "{{ movespeedslow*100.000000 }}% -> {{ movespeedslownl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TalonW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TalonE", "name": "Ścieżka Zabójcy", "description": "Talon przeskakuje ponad terenem lub budowlą do maksymalnej odległości. Sama umiejętność ma niski czas odnowienia, ale wykorzystany teren odnawia się znacznie dłużej.", "tooltip": "Talon przeskakuje nad najbliższym terenem lub budowlą. Talon nie może przeskoczyć przez ten sam teren więcej niż raz na {{ wallcd }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>zas odnowienia terenu"], "effect": ["{{ wallcd }} -> {{ wallcdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [625, 625, 625, 625, 625], [1250, 1250, 1250, 1250, 1250], [2, 2, 2, 2, 2], [160, 135, 110, 85, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "625", "1250", "2", "160/135/110/85/60", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "TalonE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "TalonR", "name": "Napaść z Cienia", "description": "Talon rzuca ostrzami wokół i staje się niewidzialny, otrzymuje premię do prędkości ruchu. Gdy Talon wyjdzie z ukrycia, ostrza skupiają się tam, gdzie się znajduje. Za każdym razem, gdy ostrza się ruszą, Napaść z Cienia zadaje obrażenia fizyczne przeciwnikom trafionym przez przynajmniej jedno ostrze.", "tooltip": "Talon ciska dookoła ostrzami, kt<PERSON><PERSON> zadaj<PERSON> <physicalDamage>{{ damage }} pkt. obraż<PERSON>ń fizycznych</physicalDamage>, zyskuje <speed>{{ movespeed*100 }}% prędkości ruchu</speed> i staje się <keywordStealth>niewid<PERSON>lny</keywordStealth> na {{ duration }} sek. Gdy efekt <keywordStealth>ni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></keywordStealth> się ko<PERSON>, ostrza powracaj<PERSON> do Talona, zadając ponownie <physicalDamage>{{ damage }} pkt. obrażeń fizycznych</physicalDamage>.<br /><br />Jeś<PERSON> anuluje <keywordStealth>ni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></keywordStealth> atakiem lub umiej<PERSON>cią <spellName>Noxiańska Dyplomacja</spellName>, ostrza powrócą do jego celu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TalonR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Koniec Ostrza", "description": "Umiejętności Talona ranią bohaterów i duże potwory, kumulując się do trzech razy. Gdy Talon zaatakuje bohatera ze skumulowanym potrójnie ranieniem, ofiara otrzyma rozłożone w czasie duże obrażenia od krwawienia.", "image": {"full": "TalonP.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}