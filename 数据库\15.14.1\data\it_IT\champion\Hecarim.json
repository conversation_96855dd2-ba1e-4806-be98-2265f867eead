{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Hecarim": {"id": "<PERSON><PERSON><PERSON>", "key": "120", "name": "<PERSON><PERSON><PERSON>", "title": "l'ombra della guerra", "image": {"full": "Hecarim.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "120000", "num": 0, "name": "default", "chromas": false}, {"id": "120001", "num": 1, "name": "<PERSON><PERSON><PERSON>uin<PERSON>", "chromas": false}, {"id": "120002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120004", "num": 4, "name": "He<PERSON>im <PERSON>", "chromas": false}, {"id": "120005", "num": 5, "name": "<PERSON><PERSON><PERSON> del Bosco Antico", "chromas": false}, {"id": "120006", "num": 6, "name": "<PERSON><PERSON><PERSON> del mondo", "chromas": false}, {"id": "120007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120008", "num": 8, "name": "<PERSON><PERSON><PERSON> Fuoco", "chromas": true}, {"id": "120014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "120022", "num": 22, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "120031", "num": 31, "name": "<PERSON><PERSON><PERSON> dell'Inverno", "chromas": true}, {"id": "120041", "num": 41, "name": "<PERSON><PERSON><PERSON>atore della Notte", "chromas": true}], "lore": "Hecarim è una spettrale fusione tra uomo e bestia, condannato ad abbattere le anime dei viventi per l'eternità. Quando le ombre avvolsero le Isole Benedette, questo fiero cavaliere venne travolto dalle energie distruttive della Rovina insieme a tutta la sua cavalleria e ai loro destrieri. <PERSON><PERSON>, ogni volta che la Nebbia Oscura percorre Runeterra, guida la loro devastante carica godendo nel travolgere e massacrare i nemici sotto i suoi zoccoli corazzati.", "blurb": "Hecarim è una spettrale fusione tra uomo e bestia, condannato ad abbattere le anime dei viventi per l'eternità. Quando le ombre avvolsero le Isole Benedette, questo fiero cavaliere venne travolto dalle energie distruttive della Rovina insieme a tutta la...", "allytips": ["Spirito del terrore ripristina la salute quando i nemici circostanti subiscono danni, inclusi quelli inflitti dai propri alleati. Lancialo durante un grosso scontro per massimizzare la sopravvivenza di Hecarim.", "Carica devastante infligge più danni in base alla distanza percorsa. Usa Massacro di ombre o gli incantesimi dell'evocatore come Spettralità o Flash per massimizzare il danno."], "enemytips": ["<PERSON><PERSON><PERSON> ottiene vita dai nemici vicini con Spirito del terrore ma manca in resistenza. Infliggigli danni a raffica.", "L'abilità suprema di Hecarim fa scappare gli avversari in preda al terrore. Dividetevi per ridurre il suo potere nei combattimenti."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 280, "mpperlevel": 40, "movespeed": 345, "armor": 32, "armorperlevel": 5.45, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 2.5, "attackspeed": 0.67}, "spells": [{"id": "HecarimRapidSlash", "name": "<PERSON>ria scatenata", "description": "<PERSON><PERSON><PERSON> colpisce i nemici nelle vicinanze infliggendo danni fisici. Se Hecarim danneggia almeno un nemico, aumenta i danni inflitti e riduce il tempo di ricarica di ogni Furia scatenata seguente.", "tooltip": "<PERSON><PERSON><PERSON> colpisce i nemici nelle vicinanze infliggendo <physicalDamage>{{ damage }} danni fisici</physicalDamage>. Se questa abilità va a segno, ottiene una carica che ne diminuisce la ricarica di {{ rampagecooldownreduction }} secondi e ne aumenta i danni di {{ rampagebonusdamageperc }}% per {{ e6 }} secondi. Si accumula fino a {{ e2 }} volte.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [28, 26, 24, 22, 20], "costBurn": "28/26/24/22/20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3", "1", "3", "60", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HecarimRapidSlash.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HecarimW", "name": "Spirito del terrore", "description": "<PERSON><PERSON><PERSON> ottiene armatura e resistenza magica. <PERSON><PERSON><PERSON> infligge danni magici ai nemici nelle vicinanze e ottiene salute equivalente a una percentuale dei danni subiti dai nemici.", "tooltip": "<PERSON><PERSON><PERSON> infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> in {{ buffduration }} secondi ai nemici nelle vicinanze. <br /><br />Hecar<PERSON> ottiene <passive>{{ resistamount }}</passive> <scaleArmor>armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR> e guarisce per <healing>{{ leechamount }}% danni</healing> che i nemici nelle vicinanze subiscono da Hecarim e per <healing>{{ allytooltipleachvalue }}% danni</healing> subiti dai suoi alleati.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Resistenze bonus", "Limite guarigione", "Costo in @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ resistamount }} -> {{ resistamountNL }}", "{{ minionhealcap }} -> {{ minionhealcapNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "HecarimW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HecarimRamp", "name": "<PERSON>ica devastante", "description": "<PERSON><PERSON><PERSON> ottiene velocità di movimento crescente e può muoversi attraverso le unità per un breve periodo di tempo. Il suo prossimo attacco respinge il bersaglio e infligge danni fisici aggiuntivi in base alla distanza percorsa dall'attivazione dell'abilità.", "tooltip": "<PERSON><PERSON><PERSON> diventa spettrale e ottiene <speed>{{ minmovespeed*100 }}% velocità di movimento</speed>, che aumenta a <speed>{{ maxmovespeed*100 }}%</speed> nell'arco di {{ e5 }} secondi. Il suo attacco successivo <status>respinge</status> i nemici e infligge da <physicalDamage>{{ mindamage }}</physicalDamage> a <physicalDamage>{{ maxdamage }} danni fisici</physicalDamage>. La distanza del <status>respingimento</status> e i danni inflitti aumentano in base alla distanza percorsa durante questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ minbasedamage }} -> {{ minbasedamageNL }}", "{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [150, 150, 150, 150, 150], [350, 350, 350, 350, 350], [60, 90, 120, 150, 180], [30, 45, 60, 75, 90], [4, 4, 4, 4, 4], [0.65, 0.65, 0.65, 0.65, 0.65], [1200, 1200, 1200, 1200, 1200], [0.25, 0.25, 0.25, 0.25, 0.25], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "150", "350", "60/90/120/150/180", "30/45/60/75/90", "4", "0.65", "1200", "0.25", "2.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "HecarimRamp.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HecarimUlt", "name": "Massacro di ombre", "description": "<PERSON><PERSON><PERSON> evoca dei cavalieri spettrali e carica, infliggendo danni magici in linea retta. <PERSON><PERSON><PERSON> crea un'onda d'urto quando termina la carica, facendo fuggire i nemici in preda al panico.", "tooltip": "<PERSON><PERSON><PERSON> evoca dei cavalieri spettrali e carica in avanti, infliggendo <magicDamage>{{ damagedone }} danni magici</magicDamage>. Al termine della carica, <PERSON><PERSON><PERSON> scaglia un'onda d'urto che <status>impaurisce</status> i nemici da {{ feardurationmin }} a {{ feardurationmax }} secondi, aumentando in base alla distanza percorsa durante la carica.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.75, 0.75, 0.75], [1.5, 1.5, 1.5], [1100, 1100, 1100], [1000, 1000, 1000], [950, 950, 950], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.75", "1.5", "1100", "1000", "950", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [50000, 50000, 50000], "rangeBurn": "50000", "image": {"full": "HecarimUlt.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Sentiero di guerra", "description": "<PERSON><PERSON><PERSON> guadagna attacco fisico equivalente a una percentuale della sua velocità di movimento bonus.", "image": {"full": "Hecarim_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}