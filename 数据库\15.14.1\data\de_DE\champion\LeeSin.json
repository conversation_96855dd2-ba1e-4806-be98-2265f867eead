{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"LeeSin": {"id": "<PERSON><PERSON><PERSON>", "key": "64", "name": "<PERSON>", "title": "der blinde <PERSON>", "image": {"full": "LeeSin.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "64000", "num": 0, "name": "default", "chromas": false}, {"id": "64001", "num": 1, "name": "Traditioneller <PERSON>", "chromas": false}, {"id": "64002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "64003", "num": 3, "name": "Drachenfaust-<PERSON>", "chromas": true}, {"id": "64004", "num": 4, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "64005", "num": 5, "name": "Poolparty-<PERSON>", "chromas": false}, {"id": "64006", "num": 6, "name": "SKT T1-<PERSON>", "chromas": false}, {"id": "64010", "num": 10, "name": "K.-o.-<PERSON>", "chromas": false}, {"id": "64011", "num": 11, "name": "Götterfaust-Lee <PERSON>", "chromas": false}, {"id": "64012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": true}, {"id": "64027", "num": 27, "name": "<PERSON>lammen<PERSON>", "chromas": false}, {"id": "64028", "num": 28, "name": "Flammende Finsternis Lee Sin (Prestige)", "chromas": false}, {"id": "64029", "num": 29, "name": "FPX-<PERSON>", "chromas": false}, {"id": "64031", "num": 31, "name": "Sturmdrache Lee Sin", "chromas": false}, {"id": "64039", "num": 39, "name": "Flammende Finsternis Lee Sin (Prestige 2022)", "chromas": false}, {"id": "64041", "num": 41, "name": "Zenitspiele-<PERSON>", "chromas": false}, {"id": "64051", "num": 51, "name": "Himmelsschuppen-<PERSON>", "chromas": false}, {"id": "64052", "num": 52, "name": "Göttliche Himmelsschuppen-<PERSON>", "chromas": false}, {"id": "64068", "num": 68, "name": "T1-<PERSON>", "chromas": false}, {"id": "64072", "num": 72, "name": "Tintenschatten-<PERSON>", "chromas": false}], "lore": "<PERSON>, ein <PERSON><PERSON> der Kampfkünste Ionias, folgt als Kämpfer strengen Prinzipien und kanalisiert die Essenz des Drachengeists, um jeder Herausforderung Herr zu werden. Obwohl der Kampfmönch sein Augenlicht bereits vor vielen Jahren verloren hat, hat er es sich zur Lebensaufgabe gemacht, das geheiligte Gleichgewicht seines Heimatlandes vor Unruhestiftern zu schützen. <PERSON><PERSON><PERSON>, die ihn wegen seines meditativen Auftretens oft unterschätzen, müssen einen Hagel flammender Fäuste und feuriger Drehtritte überstehen.", "blurb": "<PERSON>, ein <PERSON><PERSON> der Kampfkünste Ionias, folgt als Kämpfer strengen Prinzipien und kanalisiert die Essenz des Drachengeists, um jeder Herausforderung Herr zu werden. Obwohl der Kampfmönch sein Augenlicht bereits vor vielen Jahren verloren hat, hat...", "allytips": ["<PERSON><PERSON><PERSON> „Schallwelle“ vor „Zorn des Drachen“, um Gegner mit „Resonanzschlag“ zu verfolgen.", "<PERSON><PERSON>e „Unruhe“ besser aus, indem du normale Angriffe zwischen Zauber einstreust, um mehr Schaden zu verursachen und den Energieverlust zu verringern.", "„Lebensretter“ und „Eiserner Wille“ sind mächtige Werkzeuge im Dschungel."], "enemytips": ["<PERSON><PERSON><PERSON><PERSON> nicht zu dicht zu<PERSON>mmen, um Lee Sins ultimative Fähigkeit, „Zorn des Drachen“, weniger stark wirken zu lassen.", "<PERSON> kann sich mit „Eisernem Willen“ und „Verkrüppeln“ gut gegen normalen Schaden verteidigen, aber ist weiterhin für magischen Schaden anfällig.", "<PERSON> baut <PERSON> da<PERSON>, seine Fähigkeitenkombinationen einzusetzen. <PERSON><PERSON> ihn kampfunfähig, damit er diese nicht mehr effektiv nutzen kann."], "tags": ["Fighter", "Assassin"], "partype": "Energie", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 645, "hpperlevel": 108, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 3, "attackspeed": 0.651}, "spells": [{"id": "LeeSinQOne", "name": "Schallwelle / Resonanzschlag", "description": "Schallwelle: <PERSON> erzeugt eine disharmonische Schallwelle, um seine Gegner aufzudecken, und verursacht am 1.&nbsp;getroffenen Gegner normalen Schaden. Trifft „Schallwelle“ e<PERSON>, kann <PERSON> Sin innerhalb von 3&nbsp;Sekunden „Resonanzschlag“ einsetzen.<br>Resonanzschlag: Lee <PERSON> springt zum von „Schallwelle“ getroffenen Gegner und verursacht normalen Schaden basierend auf dem fehlenden Leben des Ziels.", "tooltip": "<PERSON> erzeugt eine disharmonische Schallwelle, die dem ersten getroffenen Gegner <physicalDamage>{{ initialdamage }}&nbsp;normalen Schaden</physicalDamage> zufügt und absolute Sicht auf ihn gewährt. Lee Sin kann diese Fähigkeit innerhalb der nächsten {{ reactivatetime }}&nbsp;Sekunden <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> <PERSON> springt zu dem getroffenen Gegner und verursacht zwischen <physicalDamage>{{ recastdamage }} und {{ empowereddamage }}&nbsp;normalen Schaden</physicalDamage>, der sich mit dem fehlenden Leben des Ziels erhöht. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schallwelle: <PERSON><PERSON><PERSON>", "Resonanzschlag: Minimaler Grundschaden", "Resonanzschlag: Maximaler Grundschaden", "Abklingzeit"], "effect": ["{{ q1basedamage }} -> {{ q1basedamageNL }}", "{{ q2basedamage }} -> {{ q2basedamageNL }}", "{{ q2basedamage*2.000000 }} -> {{ q2basedamagenl*2.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LeeSinQOne.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinWOne", "name": "Lebensretter / Eiserner Wille", "description": "Lebensretter: <PERSON> springt zum anvisierten Verbündeten und schützt sich selbst vor Schaden. Falls der Verbündete ein Champion ist, wird dieser ebenfalls geschützt. Nach der Verwendung von Lebensretter kann <PERSON> „Eiserner Wille“ einsetzen.<br>E<PERSON><PERSON> Wille: Durch Lee Sins intensives Training entfalten sich seine Kampfkünste. Lee Sin erhält Lebensraub und Zaubervampir.", "tooltip": "<PERSON> springt zu einem Verbündeten oder einem Auge. Ist das Ziel ein Champion, gewährt Lee <PERSON> ihm und sich selbst {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ shieldamount }} und verringert die Abklingzeit dieser Fähigkeit um {{ w1cooldownrecovered*100 }}&nbsp;%. <PERSON> kann diese Fähigkeit innerhalb der nächsten {{ w1reactivatetime }}&nbsp;Sekunden <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> <PERSON> erhält {{ lifestealandspellvamptime }}&nbsp;Sekunden lang {{ lifestealandspellvamp }}&nbsp;% Lebensraub und Zaubervampir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lebensretter: Schadensabsorption", "Eiserner Wille: Lebensraub/Zaubervampir (%)"], "effect": ["{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ lifestealandspellvamp }}&nbsp;% -> {{ lifestealandspellvampNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LeeSinWOne.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinEOne", "name": "Orkan / Verkrüppeln", "description": "Orkan: <PERSON> s<PERSON>gt auf den Boden und erzeugt eine Druck<PERSON>, die magischen Schaden verursacht und getroffene gegnerische Einheiten aufdeckt. Trifft „Orkan“ einen G<PERSON>ner, kann <PERSON> „Verkrüppeln“ einsetzen.<br>Verkrüppeln: <PERSON> verringert das Lauftempo von Gegnern in der Nähe, die durch „Orkan“ Schaden erlitten haben. Das Lauftempo wird während der Dauer langsam wiederhergestellt.", "tooltip": "<PERSON> schlägt auf den Boden und erzeugt eine Schockwelle, die Gegnern <magicDamage>{{ initialdamage }}&nbsp;magischen Schaden</magicDamage> zufügt und sie {{ slowduration }}&nbsp;Sekunden lang aufdeckt. Wird ein G<PERSON><PERSON> von ihr getroffen, kann <PERSON> Sin die Fähigkeit innerhalb der nächsten {{ reactivatetime }}&nbsp;Sekunden <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> <PERSON><PERSON>, die von der Schockwelle getroffen werden, werden um {{ slowamount }}&nbsp;% <status>verlangsamt</status>. Die Verlangsamung fällt über {{ slowduration }}&nbsp;Sekunden ab.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Orkan: <PERSON><PERSON><PERSON>", "Verkrüppeln: Verlangsamung"], "effect": ["{{ e1damage }} -> {{ e1damageNL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LeeSinEOne.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinR", "name": "Zorn des Drachen", "description": "Lee <PERSON> führt einen kräftigen Roundhouse-<PERSON> aus, der sein Ziel und alle G<PERSON>ner, mit dem dieses Ziel zu<PERSON>, zurückschleudert. <PERSON><PERSON><PERSON>, mit denen es kollidiert, werden für kurze Zeit in die Luft geworfen. Diese Technik hat er von <PERSON>, auch wenn <PERSON> keine G<PERSON> von der <PERSON> kickt.", "tooltip": "<PERSON> führt einen kräftigen Roundhouse-<PERSON>, der einen gegnerischen Champion <status>zurückschleudert</status> und <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> verursacht.<br /><br /><PERSON><PERSON><PERSON>, mit denen das Ziel zusammenstößt, werden kurz <status>hochgeschleudert</status> und erleiden <physicalDamage>{{ damage }} plus {{ percenthpcarrythrough }}&nbsp;% des zusätzlichen Lebens des gekickten Gegners als normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzlicher Schaden basierend auf dem Leben", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthpcarrythrough }}&nbsp;% -> {{ percenthpcarrythroughNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 85, 60], "cooldownBurn": "110/85/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [375, 375, 375], "rangeBurn": "375", "image": {"full": "LeeSinR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Unruhe", "description": "Nachdem Lee Sin eine Fähigkeit eingesetzt hat, ist das Angriffstempo seiner nächsten 2 normalen Angriffe erhöht und er erhält durch sie Energie zurück.", "image": {"full": "LeeSinPassive.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}