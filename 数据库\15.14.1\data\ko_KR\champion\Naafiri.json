{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Naafiri": {"id": "<PERSON><PERSON><PERSON>", "key": "950", "name": "나피리", "title": "백 번 무는 사냥개", "image": {"full": "Naafiri.png", "sprite": "champion2.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "950000", "num": 0, "name": "default", "chromas": false}, {"id": "950001", "num": 1, "name": "소울 파이터 나피리", "chromas": true}, {"id": "950011", "num": 11, "name": "프로젝트: 나피리", "chromas": true}, {"id": "950020", "num": 20, "name": "핫도그 나피리", "chromas": true}], "lore": "오늘도 슈리마 사막에는 우렁찬 포효가 합창곡처럼 울려 퍼진다. 게걸스러운 포식자인 모래 언덕의 사냥개들이 이 황량한 땅에서 무리지어 사냥할 권리를 두고 싸우며 울부짖는 소리다. 그중 정점에 오른 무리가 있다. 사냥개의 본능이 아니라, 고대 다르킨의 힘을 품고 움직이는 무리 말이다.", "blurb": "오늘도 슈리마 사막에는 우렁찬 포효가 합창곡처럼 울려 퍼진다. 게걸스러운 포식자인 모래 언덕의 사냥개들이 이 황량한 땅에서 무리지어 사냥할 권리를 두고 싸우며 울부짖는 소리다. 그중 정점에 오른 무리가 있다. 사냥개의 본능이 아니라, 고대 다르킨의 힘을 품고 움직이는 무리 말이다.", "allytips": ["오늘도 슈리마 사막에는 우렁찬 포효가 합창곡처럼 울려 퍼진다. 게걸스러운 포식자인 모래 언덕의 사냥개들이 이 황량한 땅에서 무리지어 사냥할 권리를 두고 싸우며 울부짖는 소리다. 그중 정점에 오른 무리가 있다. 사냥개의 본능이 아니라, 고대 다르킨의 힘을 품고 움직이는 무리 말이다."], "enemytips": [], "tags": ["Assassin", "Fighter"], "partype": "마나", "info": {"attack": 9, "defense": 5, "magic": 0, "difficulty": 2}, "stats": {"hp": 610, "hpperlevel": 105, "mp": 400, "mpperlevel": 55, "movespeed": 340, "armor": 28, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2, "attackspeedperlevel": 2.1, "attackspeed": 0.663}, "spells": [{"id": "Na<PERSON><PERSON><PERSON>", "name": "다르킨 단검", "description": "나피리가 최대 두 개의 단검을 던져 각 단검으로 출혈을 일으킵니다. 대상이 이미 출혈 상태라면 그 대신 추가 피해를 입힙니다.<br><br>무리가 이 스킬에 처음 적중당한 챔피언 또는 몬스터를 향해 도약해 공격합니다.<br>", "tooltip": "나피리가 다르킨의 저주를 받은 칼날을 던져 <physicalDamage>{{ spell.naafiriq:totaldamagefirstcast }}의 물리 피해</physicalDamage>를 입히고 출혈을 일으켜 {{ spell.naafiriq:bleedduration }}초에 걸쳐 <physicalDamage>{{ spell.naafiriq:totalbleeddamage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />나피리는 이 스킬을 <recast>재사용</recast>할 수 있습니다. 적중한 적이 이미 이 스킬로 인한 출혈 상태라면 남은 출혈 피해+잃은 체력에 비례한 <physicalDamage>{{ spell.naafiriq:totalmindamagesecondcast }}</physicalDamage>~<physicalDamage>{{ spell.naafiriq:totalmaxdamagesecondcast }}의 물리 피해</physicalDamage>를 입힙니다. 해당 대상이 챔피언 또는 대형 몬스터면 나피리가 <healing>{{ spell.naafiriq:totalhealsecondcast }}의 체력</healing>을 회복합니다.<br /><br /><keywordMajor>무리</keywordMajor>가 처음 적중한 챔피언 또는 몬스터에게 도약해 {{ spell.naafirip:packmatetauntduration }}초 동안 공격합니다. <br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최초 피해량", "두 번째 사용 시 피해", "출혈 피해", "회복량", "소모값 @AbilityResourceName@", "재사용 대기시간"], "effect": ["{{ basedamagefirstcast }} -> {{ basedamagefirstcastNL }}", "{{ basedamagesecondcast }} -> {{ basedamagesecondcastNL }}", "{{ bleedbasedamage }} -> {{ bleedbasedamageNL }}", "{{ basehealsecondcast }} -> {{ basehealsecondcastNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NaafiriQ.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NaafiriR", "name": "무리의 부름", "description": "나피리가 대상으로 지정할 수 없는 상태가 되고 무리를 강화하며 추가로 무리를 소환하고 이동 속도와 공격력이 증가합니다.<br>", "tooltip": "나피리가 {{ untargetableduration }}초 동안 대상으로 지정할 수 없는 상태가 되고 사냥을 준비하며 <keywordMajor>추가 무리를 {{ packmatestoadd }}마리</keywordMajor> 소환하고 {{ duration }}초 동안 <physicalDamage>공격력이 {{ bonusad }}</physicalDamage>, <speed>이동 속도가 {{ movespeedamount*100 }}%</speed> 증가합니다.<br /><br /><keywordMajor>무리</keywordMajor>가 대상으로 지정할 수 없는 상태가 되며 나피리에게 돌아갑니다.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동 속도", "재사용 대기시간"], "effect": ["{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24, 22, 20, 18], "cooldownBurn": "26/24/22/20/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "NaafiriR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NaafiriE", "name": "적출", "description": "나피리가 돌진해 주변 적에게 피해를 입히고 무리를 다시 불러 체력을 최대로 회복시킵니다.", "tooltip": "나피리가 전방으로 돌진해 <physicalDamage>{{ totaldamagefirstslash }}의 물리 피해</physicalDamage>를 입힌 후 칼날 폭발을 일으켜 <physicalDamage>{{ totaldamagesecondslash }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br /><keywordMajor>무리</keywordMajor>가 대상으로 지정할 수 없는 상태가 되며 나피리에게 돌아가 <healing>100%의 체력을 회복</healing>합니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해 (첫 번째 베기)", "피해 (두 번째 베기)", "재사용 대기시간"], "effect": ["{{ basedamagefirstslash }} -> {{ basedamagefirstslashNL }}", "{{ basedamagesecondhit }} -> {{ basedamagesecondhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "NaafiriE.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NaafiriW", "name": "사냥개의 추적", "description": "나피리와 무리가 챔피언에게 돌진해 피해를 입힙니다. 나피리가 처치 관여를 달성하면 주위 적들을 드러내고 이 스킬을 한 번 재사용할 수 있습니다. 두 번째 사용 시 보호막을 얻습니다.", "tooltip": "나피리가 적 챔피언에게 돌진해 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 잠시 <status>둔화</status>시킵니다. <keywordMajor>무리</keywordMajor>가 대상으로 지정할 수 없는 상태가 되어 나피리와 함께 돌진하며 <keywordMajor>한 마리</keywordMajor>당 <physicalDamage>{{ packmatedamage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />나피리가 {{ takedownwindow }}초 안에 처치 관여를 달성하면 주위 적들을 드러내고 이 스킬을 한 번 재사용할 수 있습니다. 두 번째 사용 시 {{ shieldduration }}초 동안 <shield>{{ shieldtotal }}의 피해를 흡수하는 보호막</shield>을 얻습니다.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "보호막 흡수량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldsize }} -> {{ shieldsizeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "NaafiriW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "늘어나는 무리", "description": "나피리가 자신이 공격하거나 스킬을 사용하는 대상을 함께 공격하는 무리를 소환합니다.", "image": {"full": "Icons_Naafiri_P.png", "sprite": "passive2.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}