{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Twitch": {"id": "Twitch", "key": "29", "name": "Twitch", "title": "die Seuchenratte", "image": {"full": "Twitch.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "29000", "num": 0, "name": "default", "chromas": false}, {"id": "29001", "num": 1, "name": "Verbrecherboss Twitch", "chromas": false}, {"id": "29002", "num": 2, "name": "Winterspiele-Twitch", "chromas": false}, {"id": "29003", "num": 3, "name": "Mittelalterlicher Twitch", "chromas": true}, {"id": "29004", "num": 4, "name": "Gangsterkönig Twitch", "chromas": false}, {"id": "29005", "num": 5, "name": "Vandalen-Twitch", "chromas": false}, {"id": "29006", "num": 6, "name": "Taschendieb-Twitch", "chromas": false}, {"id": "29007", "num": 7, "name": "SSW-Twitch", "chromas": false}, {"id": "29008", "num": 8, "name": "Omegatrupp-Twitch", "chromas": true}, {"id": "29012", "num": 12, "name": "Eiskönig Twitch", "chromas": true}, {"id": "29027", "num": 27, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "29036", "num": 36, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": false}, {"id": "29045", "num": 45, "name": "High Noon-Twitch", "chromas": false}, {"id": "29055", "num": 55, "name": "Dreikäsehoch-Twitch", "chromas": false}, {"id": "29064", "num": 64, "name": "Poolparty-Twitch", "chromas": false}], "lore": "Twitch ist nicht nur eine zhaunitische Pestratte, sondern auch ein leidenschaftlicher Kenner von <PERSON>ü<PERSON> und Dreck, und hat kein Problem damit, sich die Pfoten schmutzig zu machen. Er hat sich geschworen, den Bewohnern der Stadt über ihm mit seiner Chemtech-Armbrust, die er Piltover auf die goldene Brust setzt, zu zeigen, wie verwahrlost sie wirklich sind. Er verschwindet gerne im Schatten, und wenn er nicht gerade in der Grube herumschnüffelt, gräbt er im Müll anderer Leute nach weggeworfenen Schätzen … und – mit etwas Glück – einer schimmligen Stulle.", "blurb": "Twitch ist nicht nur eine zhaunitische Pestratte, sondern auch ein leidenschaftlicher Kenner von <PERSON> und Dreck, und hat kein Problem damit, sich die Pfoten schmutzig zu machen. Er hat sich geschworen, den Bewohnern der Stadt über ihm mit seiner...", "allytips": ["Twitchs Angriffstempo gehört zu den schnellsten im Spiel. Versuche, dies mit Gegenständen zu unterstützen, die Treffereffekte bewirken, etwa „Schwarzes Beil“ oder „Ende der Weisheit“.", "„Kontaminieren“ besitzt eine große Reichweite. Versuch zuvor, so viel „Tödliches Gift“ wie möglich zu verteilen.", "Du kannst fliehende Gegner außerhalb deiner Angriffsreichweite mit „Giftflasche“ verlangsamen."], "enemytips": ["Twitch ist ein zerbrechlicher Geselle. Arbeite mit Teammitgliedern zusammen und fokussiere ihn, wenn er sich gerade nicht camoufliert.", "Zauberschilde wehren zwar den Schaden von „Tödliches Gift“ nicht ab, dafür aber die Effekte, die Twitch damit auslösen kann.", "<PERSON><PERSON> <PERSON>, dass Twitch die Lane verlassen hat, dann lass es deine Teammitglieder umgehend wissen."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 7.25, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.1, "attackspeedperlevel": 3.38, "attackspeed": 0.679}, "spells": [{"id": "TwitchHideInShadows", "name": "<PERSON>nterhalt", "description": "Twitch camoufliert sich für kurze Zeit und erhält zusätzliches Lauftempo. Wenn die Tarnung endet, erhält er kurzzeitig Angriffstempo.<br><br><PERSON><PERSON> e<PERSON> von „Tödliches Gift“ betroffener Champion stirbt, wird die Abklingzeit von „Hinterhalt“ zurückgesetzt.", "tooltip": "Twitch <keywordStealth>camoufliert</keywordStealth> sich und erhält {{ e2 }}&nbsp;Sekunden lang <speed>{{ e3 }}&nbsp;% Lauftempo</speed>. Das Lauftempo erhöht sich auf {{ e3 }}&nbsp;% in der Nähe eines gegnerischen Champions, der ihn nicht sehen kann. Wenn er die <keywordStealth>Camouflage</keywordStealth> beendet, erhält Twitch {{ e6 }}&nbsp;Sekunden lang <attackSpeed>{{ e1 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />Stirbt ein gegnerischer Champion, während er mit <keywordMajor>Gift</keywordMajor> infiziert ist, wird die Abklingzeit dieser Fähigkeit zurückgesetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Angriffstempo"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}&nbsp;% -> {{ e1NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [45, 50, 55, 60, 65], [10, 11, 12, 13, 14], [30, 30, 30, 30, 30], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [500, 500, 500, 500, 500], [1000, 1000, 1000, 1000, 1000], [30, 30, 30, 30, 30]], "effectBurn": [null, "45/50/55/60/65", "10/11/12/13/14", "30", "1", "1", "6", "3", "500", "1000", "30"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TwitchHideInShadows.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwitchVenomCask", "name": "Giftflasche", "description": "Twitch wirft eine Giftflasche, die beim Aufprall explodiert und Flächenschaden verursacht. Getroffene Ziele werden verlangsamt und mit „Tödliches Gift“ belegt.", "tooltip": "Twitch wirft eine Flasche, die getroffene Gegner mit 1&nbsp;Steigerung <spellName>Tödl<PERSON><PERSON> Gift</spellName> belegt und eine Giftwolke hinterlässt, die {{ duration }}&nbsp;Sekunden lang bestehen bleibt.<br /><br /><PERSON><PERSON><PERSON>, die sich innerhalb der Wolke befinden, werden um {{ totalslowamount }}&nbsp;% <status>verlangsamt</status> und erhalten pro Sekunde eine zusätzliche Steigerung von <spellName>Tödl<PERSON>s Gift</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Verlangsamung", "Abklingzeit"], "effect": ["{{ baseslowamount }}&nbsp;% -> {{ baseslowamountNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "TwitchVenomCask.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwitchExpunge", "name": "Kontaminieren", "description": "Twitch infiziert vergiftete Gegner zusätzlich mit seinen üblen Seuchen.", "tooltip": "<PERSON>ügt allen <PERSON> in der Nähe, die von <spellName>Tö<PERSON><PERSON><PERSON> Gift</spellName> betroffen sind, <physicalDamage>{{ basedamage }}&nbsp;normalen Schaden</physicalDamage> zu, sowie pro Steigerung von <spellName>Tödl<PERSON><PERSON> Gift</spellName> zu<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ physicaldamageperstack }}&nbsp;normalen Schaden</physicalDamage> und <magicDamage>{{ magicdamageperstack }}&nbsp;magischen Schaden</magicDamage>.<br /><br />Maximaler Schaden: <physicalDamage>{{ maxphysicaldamage }}&nbsp;normaler Schaden</physicalDamage> und <magicDamage>{{ maxmagicdamage }}&nbsp;magischer Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden pro Steigerung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basephysicaldamageperstack }} -> {{ basephysicaldamageperstackNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchExpunge.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwitchFullAutomatic", "name": "<PERSON><PERSON><PERSON> und beten!", "description": "Twitch entfesselt die geballte Kraft seiner Armbrust und feuert Bolzen über eine große Distanz ab. Diese durchdringen alle Gegner, die ihren Weg kreuzen.", "tooltip": "Twitch setzt seine Armbrust ein und erhält {{ bonusrange }}&nbsp;Angriffsreichweite sowie <scaleAD>{{ bonusad }}&nbsp;Angriffsschaden</scaleAD>. Seine Angriffe werden außerdem {{ duration }}&nbsp;Sekunden lang zu gefährlichen Geschossen. Diese Geschosse durchdringen getroffene Gegner, aber sie fügen nachfolgenden Zielen {{ falloffdamage*100 }}&nbsp;% weniger <PERSON>haden zu, bis zu einem Minimum von {{ minimumfalloffdamage*100 }}&nbsp;% Schaden.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffsschaden"], "effect": ["{{ bonusad }} -> {{ bonusadNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchFullAutomatic.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tödliches Gift", "description": "Twitchs normale Angriffe vergiften sein <PERSON>, wodurch dieses jede Sekunde absoluten Schaden erleidet.", "image": {"full": "Twitch_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}