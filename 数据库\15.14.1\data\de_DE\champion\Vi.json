{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vi": {"id": "Vi", "key": "254", "name": "Vi", "title": "Piltovers Vollstreckerin", "image": {"full": "Vi.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "254000", "num": 0, "name": "default", "chromas": false}, {"id": "254001", "num": 1, "name": "Neonschlag-Vi", "chromas": false}, {"id": "254002", "num": 2, "name": "Officer Vi", "chromas": true}, {"id": "254003", "num": 3, "name": "Charmante Vi", "chromas": false}, {"id": "254004", "num": 4, "name": "Dämonen-Vi", "chromas": false}, {"id": "254005", "num": 5, "name": "Kriegsherrin Vi", "chromas": false}, {"id": "254011", "num": 11, "name": "PROJEKT: Vi", "chromas": false}, {"id": "254012", "num": 12, "name": "Herzensbrecherin-Vi", "chromas": true}, {"id": "254020", "num": 20, "name": "PsyOps-Vi", "chromas": true}, {"id": "254029", "num": 29, "name": "Unterstadt-Vi (Arcane)", "chromas": false}, {"id": "254030", "num": 30, "name": "Herzschmerz-Vi", "chromas": true}, {"id": "254039", "num": 39, "name": "Krallen des Tigers-Vi", "chromas": true}, {"id": "254048", "num": 48, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Arcane)", "chromas": false}], "lore": "Vi, die in den berüchtigten Straßen Zhauns aufwuchs, ist eine hitzköpfige, impulsive und furchteinflößende Frau, die nur wenig Respekt vor Autoritätspersonen zeigt. Sie war schon immer eine geschickte Überlebenskünstlerin, sowohl während ihrer Jugend als Unruhestifterin in der Oberstadt als auch während der ungerechtfertigt langen Zeit, die sie in der Stillwasser-Haftanstalt abgesessen hat. Sie arbeitet jetzt mit den Vollstreckern von Piltover zu<PERSON>mmen, um den Frieden zu wahren, anstatt ihn zu brechen. Dabei schwingt sie ihre mächtigen Hextech-Handschuhe, mit denen sie Wände und Verdächtige gleichermaßen mit Leichtigkeit zerschmettern kann.", "blurb": "Vi, die in den berüchtigten Straßen Zhauns aufwuchs, ist eine hitzköpfige, impulsive und furchteinflößende Frau, die nur wenig Respekt vor Autoritätspersonen zeigt. Sie war schon immer eine geschickte Überlebenskünstlerin, sowohl während ihrer Jugend...", "allytips": ["Ein vollständig aufgeladener „Mauerbrecher“ verursacht doppelten Schaden. Er eignet sich bestens, um fliehende Gegner zu schnappen und zu erledigen.", "„Schlagkräftige Beweise“ fügt jede<PERSON>, der von der Schockwelle erfasst wird, vollen <PERSON>haden zu. Setze es bei Vasallen auf der <PERSON> ein, um G<PERSON>ner zu treffen, die sich hinter ihnen verstecken.", "„Anzeige ist raus“ ist ein mächtiges Werkzeug, um einen Kampf zu beginnen. Achte aber darauf, dass du deinem Team nicht zu weit voraus rennst."], "enemytips": ["Ein vollständig aufgeladener „Mauerbrecher“ verursacht doppelten Schaden. Falls du also siehst, wie Vi ihn auflädt, solltest du zurückweichen oder versuchen auszuweichen.", "Vi zertrümmert deine Rüstung und erhält zusätzliches Angriffstempo, wenn sie es schafft, dich 3-mal nacheinander zu treffen. Versuche keine langen Auseinandersetzungen mit ihr auszufechten.", "Vi ist während ihrer ultimativen Fähigkeit unaufhaltbar. Hebe deine Verdrängungseffekte auf, bis sie mit Aufladen fertig ist."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 295, "mpperlevel": 65, "movespeed": 340, "armor": 30, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 10, "hpregenperlevel": 1, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "ViQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Vi lädt ihre Handschuhe auf und entfesselt einen mauernerschütternden Hieb, der sie vorwärts trägt. <PERSON><PERSON><PERSON>, die sie trifft, werden zurückgestoßen und erhalten 1 Steigerung „Zermürbende Hiebe“.", "tooltip": "<charge>Aufladungsbeginn:</charge> Vi lädt einen mächtigen Schlag auf und <status>verlangsamt</status> sich selbst um {{ e4 }}&nbsp;%.<br /><br /><release>Auslösen:</release> Vi springt nach vorn, verursacht zwischen <physicalDamage>{{ totaldamage }} und {{ maxdamagetooltip }}&nbsp;normalen Schaden</physicalDamage> abhä<PERSON><PERSON> von der Aufladungsdauer und belegt alle getroffenen Gegner mit <spellName>Zermürbende Hiebe</spellName>. Vi hält an, wenn sie mit einem gegnerischen Champion kollidiert, und <status>stößt</status> diesen <status>zurück</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mindestschaden", "<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ mindamage*2.500000 }} -> {{ mindamagenl*2.500000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "15", "6", "5", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ViQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViW", "name": "Zermürbende Hiebe", "description": "Vis Hiebe zerstören die Rüstung des Ziels, verursachen zusätzlichen Schaden und gewähren ihr Angriffstempo.", "tooltip": "<spellPassive>Passiv:</spellPassive> Jeder 3.&nbsp;Angriff auf dasselbe Ziel verursacht zusätzlich <physicalDamage>{{ totaldamagetooltip }}&nbsp;normalen Schaden</physicalDamage> basierend auf dem maximalen Leben, entfernt <scaleArmor>{{ shredamount }}&nbsp;% Rüstung</scaleArmor> und gewährt Vi {{ sharedbuffsduration }}&nbsp;Sekunden lang <attackSpeed>{{ attackspeed }}&nbsp;% Angriffstempo</attackSpeed>. Außerdem wird dadurch die verbleibende Abklingzeit von <spellName>Sc<PERSON><PERSON>child</spellName> um {{ spell.vipassive:cdreductionon3hit }}&nbsp;Sekunden verringert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden abhängig vom maximalen Leben ", "Angriffstempo"], "effect": ["{{ maxhealthdamage }}&nbsp;% -> {{ maxhealthdamageNL }}&nbsp;%", "{{ attackspeed }}&nbsp;% -> {{ attackspeedNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiv", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Passiv"}, {"id": "ViE", "name": "Schlagkräftige Beweise", "description": "Vis nächster Angriff stößt durch ihr Ziel und verursacht Schaden an den Gegnern hinter diesem.", "tooltip": "Vis nächster Angriff fügt dem Z<PERSON> und Gegnern hinter ihm <physicalDamage>{{ totaldamagetooltip }}&nbsp;normalen Schaden</physicalDamage> zu.<br /><br />Diese Fähigkeit hat 2&nbsp;Ladungen ({{ ammorechargetime }}&nbsp;Sekunden Aufladungszeit).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Aufladungsdauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [26, 32, 38, 44, 50], "costBurn": "26/32/38/44/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1.5", "6", "1", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViR", "name": "Anzeige ist raus", "description": "Vi rennt einen G<PERSON>ner um, wobei sie jeden, der im Weg steht, zur Seite stößt. <PERSON><PERSON><PERSON> sie ihr Z<PERSON> erreicht, schl<PERSON>gt sie es in die Luft, springt hinterher und schleudert es wieder zu Boden.", "tooltip": "Vi visiert einen gegnerischen Champion an, deckt ihn auf und springt unaufhaltsam auf ihn zu. Wenn sie ihn erreicht, <status>schleudert</status> Vi ihn {{ rstunduration }}&nbsp;Sekunden lang <status>in die Luft</status> und verursacht <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage>.<br /><br /><PERSON><PERSON> <PERSON><PERSON><PERSON>, mit denen <PERSON><PERSON>oll<PERSON>, erle<PERSON> Schaden, werden zur Seite gestoßen und {{ secondarytargetstunduration }}&nbsp;Sekunden lang <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ViR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Vi lädt einen Schild über Zeit auf. Der Schild kann aktiviert werden, indem ein G<PERSON>ner mit einer Fähigkeit getroffen wird.", "image": {"full": "ViPassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}