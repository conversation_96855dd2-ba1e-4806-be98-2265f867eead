{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "Alistar", "title": "the Minotaur", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "Black Alistar", "chromas": false}, {"id": "12002", "num": 2, "name": "Golden Alistar", "chromas": false}, {"id": "12003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "12004", "num": 4, "name": "Longhorn Alistar", "chromas": false}, {"id": "12005", "num": 5, "name": "Unchained <PERSON><PERSON>", "chromas": false}, {"id": "12006", "num": 6, "name": "Infernal Alistar", "chromas": false}, {"id": "12007", "num": 7, "name": "<PERSON>weeper <PERSON>", "chromas": false}, {"id": "12008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12009", "num": 9, "name": "SKT T1 Alistar", "chromas": false}, {"id": "12010", "num": 10, "name": "<PERSON><PERSON> Cow Alistar", "chromas": true}, {"id": "12019", "num": 19, "name": "Hextech Alistar", "chromas": false}, {"id": "12020", "num": 20, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "12022", "num": 22, "name": "Blackfrost Alistar", "chromas": true}, {"id": "12029", "num": 29, "name": "Lunar Beast Alistar", "chromas": true}], "lore": "Selalu menjadi warrior per<PERSON><PERSON> dengan reputasi men<PERSON>, <PERSON><PERSON> berusaha membalas dendam atas kematian klannya di tangan kerajaan Noxus. Meskipun diperbudak dan dipaksa menja<PERSON> kehidupan sebagai gladiator, tekadnya yang tak tergoyahkan membuat dia tidak berubah seutuhnya menjadi monster. <PERSON><PERSON><PERSON>, setelah bebas dari belenggu mantan tuannya, dia bertarung atas nama kaum tertindas dan malang, kema<PERSON><PERSON>ya jadi senjata yang sama kuatnya dengan tanduk, kuku kaki, dan tinjunya.", "blurb": "Se<PERSON>u menjadi warrior per<PERSON><PERSON> dengan reputasi men<PERSON>, <PERSON><PERSON> be<PERSON><PERSON>a membalas dendam atas kematian klannya di tangan kerajaan Noxus. Meskipun diperbudak dan dipaksa menja<PERSON> kehidupan sebagai gladiator, tekadnya yang tak tergoyahkan membuat dia...", "allytips": ["Menggunakan Pulverize memungkinkan kamu mengatur posisi yang lebih baik untuk Headbutt.", "Move Speed sangat penting untuk Alistar. Pertimbangkan dengan cermat boot mana yang harus dibeli.", "Menggunakan Flash memungkinkan kamu mengejutkan target dan mendorong mereka ke arah sekutu dengan kombinasi Pulverize dan <PERSON>butt."], "enemytips": ["Alistar sangat mengganggu tetapi sangat tangguh. Cobalah untuk menyerang damage dealer yang le<PERSON>h rapuh.", "Waspadai combo Pulverize-Headbutt ketika di dekat turret.", "Saat Alistar menggunakan ultimanya, lebih baik mundur dan tunggu hingga efeknya hilang sebelum menyerangnya."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "Pulverize", "description": "Alistar men<PERSON><PERSON> tanah, memberikan damage ke musuh di sekitar dan melempar mereka ke udara.", "tooltip": "Alistar menghantam tanah, <status>Knock Up</status> musuh selama {{ knockupduration }} detik dan men<PERSON><PERSON>an <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Headbutt", "name": "Headbutt", "description": "Alistar menyeruduk target den<PERSON> k<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> damage dan men<PERSON><PERSON><PERSON>nock Back pada target.", "tooltip": "Alistar men<PERSON><PERSON><PERSON> musuh, men<PERSON><PERSON><PERSON> <status>K<PERSON></status> <status>Back</status> pada mereka dan men<PERSON><PERSON>an <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ damage }}-> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AlistarE", "name": "Trample", "description": "Alistar menginjak-injak unit musuh di sekitar, mengabaikan tabrakan unit dan mendapatkan stack jika dia menghasilkan damage pada champion musuh. Saat stack penuh, basic attack Alistar berikutnya terhadap champion musuh akan menghasilkan magic damage tambahan dan menerapkan efek stun.", "tooltip": "Alistar mulai menginjak-<PERSON><PERSON> tanah, men<PERSON>di Ghost dan men<PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> dalam kurun waktu {{ e3 }} detik ke musuh di sekitar. Tiap denyut yang menghasilkan damage ke champion akan memberikan stack.<br /><br />Pada {{ e5 }} stack, Serangan Alistar berikutnya terhadap champion akan menerapkan efek <status>Stun</status> selama {{ e6 }} detik dan menghasilkan <magicDamage>{{ attackbonusdamage }} magic damage</magicDamage> tambahan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ e1 }}-> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FerociousHowl", "name": "Unbreakable Will", "description": "Alistar meraung liar, men<PERSON><PERSON><PERSON><PERSON> semua efek crowd control pada dirinya, la<PERSON> men<PERSON>i physical dan magical damage selama durasi.", "tooltip": "Alistar segera melakukan cleanse pada semua efek <status>Disable</status> dan menerima {{ rdamagereduction }}% pengurangan damage selama {{ rduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Pengurangan Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ rdamagereduction }}%-> {{ rdamagereductionNL }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Triumphant <PERSON>", "description": "Alistar men-charge raungannya dengan menerapkan stun atau melempar champion musuh di sekitar atau tiap kali musuh di sekitar mati. Saat charge penuh, dia akan heal dirinya sendiri dan semua champion sekutu di sekitar.", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}