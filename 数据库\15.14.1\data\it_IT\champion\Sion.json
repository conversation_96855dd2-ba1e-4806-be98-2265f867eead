{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sion": {"id": "Sion", "key": "14", "name": "Sion", "title": "Il colosso non-morto", "image": {"full": "Sion.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "14000", "num": 0, "name": "default", "chromas": false}, {"id": "14001", "num": 1, "name": "Sion Hextech", "chromas": false}, {"id": "14002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "14003", "num": 3, "name": "Sion Taglialegna", "chromas": false}, {"id": "14004", "num": 4, "name": "Sion Signore della Guerra", "chromas": false}, {"id": "14005", "num": 5, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "14014", "num": 14, "name": "Sion Distruttore del mondo", "chromas": true}, {"id": "14022", "num": 22, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "14030", "num": 30, "name": "Sion Mezzogiorno di Fuoco", "chromas": true}, {"id": "14040", "num": 40, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "14049", "num": 49, "name": "Sion Ordalia Gloriosa", "chromas": false}], "lore": "Un eroe di guerra di un'epoca passata, <PERSON>on era riverito a Noxus per aver strangolato a mani nude un re di Demacia, ma gli è stato negato il riposo eterno ed è stato resuscitato per servire il suo impero anche nella morte. Il fatto che massacri indiscriminatamente chiunque gli si pari davanti, a prescindere dalla sua fazione, dimostra che ha perso del tutto la sua umanità. <PERSON><PERSON> cos<PERSON>, con una rozza armatura inchiodata alla carne putrefatta, Sion continua a lanciarsi in battaglia senza freni, lottando per ricordare la sua natura mentre brandisce la possente ascia.", "blurb": "Un eroe di guerra di un'epoca passata, <PERSON>on era riverito a Noxus per aver strangolato a mani nude un re di Demacia, ma gli è stato negato il riposo eterno ed è stato resuscitato per servire il suo impero anche nella morte. Il fatto che massacri...", "allytips": ["Hai a disposizione soltanto una ridottissima possibilità di cambiare traiettoria durante Carica inarrestabile, dunque cerca di usarlo negli spazi diritti.", "Urlo del massacratore è un'ottima abilità per prepararti a mettere a segno un Decimare.", "Il buff Fornace delle anime mostra quanta forza dello scudo è rimasta. Usa questa informazione per calcolare il tempo dell'esplosione con un tempismo perfetto."], "enemytips": ["Anche se Sion dovesse comunque mettere a segno un Decimare, fargli rilasciare la carica in anticipo ridurrà il suo impatto.", "Sfrutta il tempo a disposizione dopo che Sion è morto per riposizionarti e prepararti per il suo ritorno."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 655, "hpperlevel": 87, "mp": 400, "mpperlevel": 52, "movespeed": 345, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 1.3, "attackspeed": 0.679}, "spells": [{"id": "SionQ", "name": "Decimare", "description": "Sion carica un potente attacco in un'area di fronte a lui che danneggia tutti i nemici al rilascio. Se carica per un tempo sufficiente, i nemici colpiti verranno lanciati in aria e storditi.", "tooltip": "<charge>Inizio carica</charge>: Sion carica un colpo pesante fino a 2 secondi.<br /><br /><release><PERSON><PERSON><PERSON><PERSON></release>: <PERSON><PERSON> abbatte la sua ascia, <status>rallentando</status> per un breve periodo i nemici e infliggendo da <physicalDamage>{{ mindamagetotal }} a {{ maxdamagetotal }} danni fisici</physicalDamage>, in base al tempo di carica. Se ha caricato per almeno 1 secondo, i nemici vengono <status>lanciati in aria</status> e <status>storditi</status> da {{ basestuntime }} a 2,25 secondi, in base al tempo di carica.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "Rapporto attacco fisico", "Rapporto attacco fisico", "Ricarica"], "effect": ["{{ lowdamage }} -> {{ lowdamageNL }}", "{{ highdamage }} -> {{ highdamageNL }}", "{{ adratiomin*100.000000 }}% -> {{ adratiominnl*100.000000 }}%", "{{ adratiomax*100.000000 }}% -> {{ adratiomaxnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [7.5, 7.5, 7.5, 7.5, 7.5], [60, 60, 60, 60, 60], [150, 150, 150, 150, 150], [-0.8, -0.8, -0.8, -0.8, -0.8]], "effectBurn": [null, "0", "0", "0", "0", "2.5", "0", "7.5", "60", "150", "-0.8"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "SionQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SionW", "name": "Fornace delle anime", "description": "Sion si protegge e può riattivare lo scudo dopo 3 secondi per infliggere danni magici ai nemici vicini. Quando Sion uccide nemici, ottiene passivamente salute massima.", "tooltip": "<spellPassive>Passiva</spellPassive>: <PERSON><PERSON> ottiene <scaleHealth>{{ hpperkill }} di salute massima</scaleHealth> per ogni unità uccisa o {{ hpperchampkill }} per le eliminazioni di campioni, minion grandi e mostri grandi.<br /><br /><spellActive>Attiva</spellActive>: <PERSON>on ottiene uno <shield>scudo da {{ totalshield }}</shield> per 6 secondi. Dopo {{ e7 }} secondi, se lo scudo è ancora attivo, <PERSON><PERSON> può <recast>rilanciare</recast> questa abilità per farlo esplodere, infliggendo <magicDamage>{{ totaldamage }} più un {{ e4 }}% di salute massima in danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "<PERSON><PERSON>", "Rapporto massimo salute scudo", "Costo in mana", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ shieldpercenthealthtooltip*100.000000 }}% -> {{ shieldpercenthealthtooltipnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [65, 70, 75, 80, 85], "costBurn": "65/70/75/80/85", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [40, 65, 90, 115, 140], [8, 10, 12, 14, 16], [14, 14, 14, 14, 14], [4, 4, 4, 4, 4], [15, 15, 15, 15, 15], [3, 3, 3, 3, 3], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "40/65/90/115/140", "8/10/12/14/16", "14", "4", "15", "3", "15", "6", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "SionW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SionE", "name": "Urlo del massacratore", "description": "Sion lancia un'onda d'urto a corto raggio che danneggia, rallenta e riduce l'armatura del primo nemico colpito. Se l'onda d'urto colpisce un minion o un mostro, questo verrà respinto, da<PERSON><PERSON><PERSON><PERSON>, rallentando e riducendo l'armatura di tutti i nemici che trapassa.", "tooltip": "Sion lancia un'onda d'urto, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>, <status>rallentando</status> i nemici colpiti di un {{ slowamount }}% per {{ slowduration }} secondi e rimuovendo un <scaleArmor>{{ armorshred }}% di armatura</scaleArmor> per {{ armorshredduration }} secondi. Le unità non campioni colpite vengono <status>respinte</status>. I nemici colpiti da un'unità <status>respinta</status> subiscono gli stessi danni ed effetti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [40, 45, 50, 55, 60], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "40/45/50/55/60", "5", "40/45/50/55/60", "20", "30", "4", "2.5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SionE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SionR", "name": "Carica inarrestabile", "description": "Sion carica in una direzione, diventando più veloce col tempo. Può modificare leggermente traiettoria spostando il puntatore. Quando colpisce un nemico, lo danneggia e lo lancia in aria a seconda della distanza percorsa caricando.", "tooltip": "Sion carica in una direzione per 8 secondi diventando inarrestabile, dirigendosi verso il puntatore del mouse. Sion si ferma se entra a contatto con un campione nemico o un muro, o se <recast>rilancia</recast> questa abilità.  <br /><br />Al termine della carica, Sion infligge da <physicalDamage>{{ mindamagetotal }} a {{ maxdamagetotal }} danni fisici</physicalDamage> in base alla distanza percorsa. I nemici vicino a Sion vengono <status>storditi</status> da {{ minstunduration }} a {{ maxstunduration }} secondi, in base alla distanza percorsa. I nemici in un'area più ampia vengono <status>rallentati</status> di un {{ slowamount }}% per 3 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 100, 60], "cooldownBurn": "140/100/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "SionR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Gloria nella morte", "description": "Sion torna temporaneamente in vita dopo essere stato ucciso, ma la sua salute da rianimato decade rapidamente. I suoi attacchi diventano molto rapidi, lo curano e infliggono danni bonus in base alla salute massima del bersaglio.", "image": {"full": "Sion_Passive1.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}