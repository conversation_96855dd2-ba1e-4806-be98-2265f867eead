{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Thresh": {"id": "<PERSON><PERSON><PERSON>", "key": "412", "name": "<PERSON><PERSON><PERSON>", "title": "Il carceriere", "image": {"full": "Thresh.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "412000", "num": 0, "name": "default", "chromas": false}, {"id": "412001", "num": 1, "name": "<PERSON><PERSON><PERSON> Profondo", "chromas": false}, {"id": "412002", "num": 2, "name": "<PERSON><PERSON><PERSON> 2013", "chromas": true}, {"id": "412003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "412004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "412005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "412006", "num": 6, "name": "<PERSON><PERSON><PERSON> Fu<PERSON>o", "chromas": true}, {"id": "412013", "num": 13, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "412014", "num": 14, "name": "<PERSON><PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "412015", "num": 15, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "412017", "num": 17, "name": "<PERSON><PERSON><PERSON>e", "chromas": true}, {"id": "412027", "num": 27, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "412028", "num": 28, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "412038", "num": 38, "name": "<PERSON><PERSON><PERSON> (edizione prestigio 2022)", "chromas": false}, {"id": "412039", "num": 39, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "412049", "num": 49, "name": "<PERSON><PERSON><PERSON> dell'Inverno", "chromas": true}, {"id": "412059", "num": 59, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> e astuto, Thr<PERSON> è uno spettro inquieto e ambizioso delle Isole Ombra. Un tempo custode di innumerevoli segreti arcani, fu distrutto da un potere più grande della vita e della morte e ora si alimenta tormentando e uccidendo il prossimo con spietatezza e creatività. Le sue vittime soffrono ben oltre la morte, poiché Thresh tormenta le loro anime imprigionandole nella sua empia lanterna per torturarle in eterno.", "blurb": "Sad<PERSON> e astuto, <PERSON><PERSON><PERSON> è uno spettro inquieto e ambizioso delle Isole Ombra. Un tempo custode di innumerevoli segreti arcani, fu distrutto da un potere più grande della vita e della morte e ora si alimenta tormentando e uccidendo il prossimo con...", "allytips": ["Quando si usa la lanterna di Thresh, la comunicazione è essenziale. Comunica ai tuoi compagni come intendi usarla.", "Condanna a morte e Sferzata possono essere combinate in entrambi gli ordini per ottenere potenti combo.", "T<PERSON><PERSON> pu<PERSON> raccogliere anime senza bisogno di uccidere le unità in prima persona. Pianifica la tua posizione nella mappa in modo da trovarti vicino al massimo numero di morti possibili."], "enemytips": ["Condanna a morte di Thresh ha un lungo tempo di lancio. Attendi l'inizio del lancio per le tue manovre evasive.", "Rompere intenzionalmente un muro della Scatola può consentire a un alleato vulnerabile di fuggire senza danni.", "Thresh fa affidamento sulla raccolta di anime per incrementare la sua difesa ed il suo attacco. Attaccalo quando si muove in direzione delle anime per raccoglierle."], "tags": ["Support", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 6, "magic": 6, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 120, "mp": 274, "mpperlevel": 44, "movespeed": 330, "armor": 33, "armorperlevel": 0, "spellblock": 30, "spellblockperlevel": 1.55, "attackrange": 450, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "ThreshQ", "name": "<PERSON>danna a morte", "description": "Thresh incatena un nemico e lo tira verso di lui. Attivare l'abilità una seconda volta porta Thresh verso il nemico.", "tooltip": "Thresh lancia la sua falce, <status>stordendo</status> la prima unità colpita e <status>attirandola</status> verso Thresh per {{ tauntlength }} secondi. La falce infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e conferisce <keywordStealth>Visione magica</keywordStealth> per la durata.<br /><br />Thresh pu<PERSON> <recast>rilanciare</recast> questa abilità per avvicinarsi al nemico.<br /><br />Se questa abilità colpisce, la sua ricarica è ridotta di {{ hitbonuscooldown }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [19, 16.5, 14, 11.5, 9], "cooldownBurn": "19/16.5/14/11.5/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [100, 100, 100, 100, 100], [1.5, 1.5, 1.5, 1.5, 1.5], [75, 75, 75, 75, 75], [12, 12, 12, 12, 12], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100", "1.5", "75", "12", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "ThreshQ.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ThreshW", "name": "Passaggio oscuro", "description": "<PERSON><PERSON><PERSON> lancia una lanterna che protegge i campioni alleati vicini dai danni. Gli alleati possono cliccare sulla lanterna per scattare da Thresh.", "tooltip": "Thresh lancia la sua lanterna, permettendo a un alleato di cliccare su di essa per scattare verso Thresh.<br /><br />La lanterna conferisce anche <shield>uno scudo da {{ totalshield }}</shield> per {{ shieldduration }} secondi a Thresh e al primo campione alleato che entra in contatto con essa.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ baseshieldvalue }} -> {{ baseshieldvalueNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [6, 6, 6, 6, 6], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1", "2", "0", "6", "4", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "ThreshW.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ThreshE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Gli attacchi di Thresh diventano più forti, infliggendo più danni quanto più attende tra un attacco e l'altro. All'attivazione, Thresh esegue una spazzata con la sua catena, respingendo i nemici colpiti nella direzione del colpo.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi di Thresh infliggono danni in più in base a quanto tempo è passato dal suo ultimo attacco. Infliggono tra <magicDamage>{{ pattackdamagemin }}</magicDamage> e <magicDamage>{{ pattackdamagemax }} danni magici</magicDamage>.<br /><br /><spellActive>Attiva:</spellActive> Thresh schiocca le sue catene, <status>attirando</status> o <status>spingendo</status> i nemici nella direzione del colpo. I nemici colpiti vengono <status>rallentati</status> del {{ e2 }}% per {{ e4 }} secondo/i, subendo anche <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> passivi", "<PERSON><PERSON>", "Rallentamento", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ passiveadratio }}% -> {{ passiveadratioNL }}%", "{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12.25, 11.5, 10.75, 10], "cooldownBurn": "13/12.25/11.5/10.75/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 120, 165, 210, 255], [20, 25, 30, 35, 40], [80, 110, 140, 170, 200], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [1.7, 1.7, 1.7, 1.7, 1.7], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/120/165/210/255", "20/25/30/35/40", "80/110/140/170/200", "1", "1.5", "1.7", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "ThreshE.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "La scatola", "description": "Una prigione che rallenta e infligge danni se viene oltrepassata.", "tooltip": "Thresh crea una prigione di energia spettrale, <status>rallentando</status> i campioni del {{ e3 }}% per {{ e2 }} secondi e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>. Le pareti si infrangono dopo una collisione. All'abbattimento della prima le altre non infliggono danni e <status>rallentano</status> per metà della durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [250, 400, 550], [2, 2, 2], [99, 99, 99], [4, 4, 4], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "250/400/550", "2", "99", "4", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "ThreshRPenta.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dannazione", "description": "Thr<PERSON> pu<PERSON> raccogliere le anime dei nemici che muoiono vicino a lui, ottenendo permanentemente armatura e potere magico.", "image": {"full": "Thresh_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}