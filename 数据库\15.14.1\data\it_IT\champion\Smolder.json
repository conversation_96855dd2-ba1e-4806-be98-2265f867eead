{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "<PERSON><PERSON>lder", "title": "il principino ardente", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cele<PERSON>", "chromas": true}], "lore": "Nascosto tra le scogliere scoscese della frontiera noxiana, sotto lo sguardo vigile della mamma, un giovane drago sta imparando cosa significa essere l'erede della stirpe dei draghi imperiali di Camavor. Smolder è un giocherellone ansioso di crescere e cerca ogni scusa per allenare le sue crescenti capacità. Benché sia ancora alle prime armi, le sue abilità non sono da sottovalutare, poiché è in grado di dare facilmente alle fiamme tutto ciò che brucia.", "blurb": "Nascosto tra le scogliere scoscese della frontiera noxiana, sotto lo sguardo vigile della mamma, un giovane drago sta imparando cosa significa essere l'erede della stirpe dei draghi imperiali di Camavor. Smolder è un giocherellone ansioso di crescere e...", "allytips": ["Smolder è vulnerabile a inizio partita. Concentratevi sulle cariche passive e cercate di sopravvivere per farlo diventare un drago più potente nelle fasi avanzate!", "Smolder si affida alla squadra per stare al sicuro. Cercate degli alleati che possano aiutarvi contro le minacce nemiche.", "Smolder può infliggere danni ingenti nelle aree occupate dai nemici. Cercate occasioni d'attacco dove i nemici si raggruppano."], "enemytips": ["Smolder si affida alla sua squadra per stare al sicuro. Attaccatelo quando la squadra non può salvarlo.", "<PERSON><PERSON> S<PERSON> evitate di raggrupparvi!", "Smolder è molto vulnerabile nelle fasi iniziali. Provate a sfruttare i suoi punti deboli prima che impari come essere un drago!", "Il volo di Smolder può essere interrotto da effetti di controllo intensi e soffre il rallentamento."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "Super soffio bruciante", "description": "Smolder sputa fuoco contro un nemico. Con l'aumento delle cariche, questa abilità diventa più potente.", "tooltip": "Smolder sputa fiamme, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_qdamageincrease }} danni magici</magicDamage>. Se il bersaglio muore, Smolder recupera <scaleMana>{{ manarestore }} mana</scaleMana>, una volta per ogni lancio.<br /><br />In base alle cariche di <spellName>Allenamento draconico</spellName>, questa abilità si evolve ottenendo gli effetti seguenti:<li><keywordMajor>{{ stacktier1 }} Cariche</keywordMajor>: infligge danni a tutti i nemici attorno al bersaglio.<li><keywordMajor>{{ stacktier2 }} Cariche</keywordMajor>: manda <spellName>{{ tier2_numberofblowback }}</spellName> esplosione/i oltre il bersaglio, infliggendo un {{ tier2_blowbackpercentagedamage }}% dei danni di questa abilità.<li><keywordMajor>{{ stacktier3 }} Cariche</keywordMajor>: brucia il bersaglio infliggendo danni puri per un <trueDamage>{{ tier3_burn }} della salute massima</trueDamage> nell'arco di {{ tier3_dotlength }} secondi. I campioni nemici che scendono sotto un <trueDamage>{{ tier3_executethreshold }}</trueDamage> di salute totale mentre bruciano vengono uccisi immediatamente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderW", "name": "Ecciù!", "description": "Smolder si lascia sfuggire un adorabile starnuto fiammeggiante che esplode quando colpisce i campioni nemici.", "tooltip": "A Smolder scappa un adorabile starnuto fiammeggiante, che infligge <physicalDamage>{{ initialdamage }} danni fisici</physicalDamage> e <status>rallenta</status> di un {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />Colpendo i campioni provoca un'esplosione, infliggendo <physicalDamage>{{ explosiondamage }} danni fisici</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_wdamageincrease }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> da esplosione", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderE", "name": "Guardate, sto volando!", "description": "Smolder spicca il volo ignorando il terreno e bombardando il nemico con la salute più bassa.", "tooltip": "Smolder spicca il volo, ottenendo <speed>{{ movespeed*100 }}% velocità di movimento</speed> e ignorando il terreno per {{ duration }} secondi.<br /><br />Durante il volo, Smolder bombarda il nemico con la salute più bassa <spellName>{{ totalnumberofattacks }}</spellName> (arrotondamento per difetto) volte per <physicalDamage>{{ damageperhit }} danni fisici</physicalDamage> + <magicDamage>{{ spell.smolderp:ebonusdamage }} danni magici</magicDamage> per colpo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderR", "name": "MAAAAMMAAAA!", "description": "Smolder chiede aiuto alla sua mamma, che sputa fuoco dall'alto, infliggendo danni aggiuntivi e rallentando i nemici al centro del fuoco.", "tooltip": "La mamma di Smolder soffia fuoco dall'alto, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>. I nemici al centro subiscono invece <physicalDamage>{{ tooltiponly_totalsweetspotdamage }} danni fisici</physicalDamage> e vengono <status>rallentati</status> di un {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />Se colpisce il figlio, la mamma di Smolder lo cura di <healing>{{ momhealcalc }} salute</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Allenamento draconico", "description": "Colpire campioni con le abilità e uccidere nemici con Super soffio bruciante conferisce una carica di Allenamento draconico. Le cariche aumentano i danni delle abilità base di Smolder.", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}