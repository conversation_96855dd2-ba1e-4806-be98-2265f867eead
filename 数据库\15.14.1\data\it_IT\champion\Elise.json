{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Elise": {"id": "<PERSON>", "key": "60", "name": "<PERSON>", "title": "la regina dei ragni", "image": {"full": "Elise.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "60000", "num": 0, "name": "default", "chromas": false}, {"id": "60001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "60002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "60003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "60004", "num": 4, "name": "Elise SKT T1", "chromas": false}, {"id": "60005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "60006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "60015", "num": 15, "name": "<PERSON>", "chromas": true}, {"id": "60024", "num": 24, "name": "<PERSON>", "chromas": true}, {"id": "60034", "num": 34, "name": "<PERSON>era", "chromas": false}], "lore": "Elise è una letale predatrice che vive in un palazzo sbarrato e senza luce, nel profondo della città più vecchia di Noxus. Un tempo era una mortale a capo di una potente casata, ma il morso di un malvagio semidio la trasformò in qualcosa di bello, immortale e inumano: una creatura aracnide, che attira le ignare prede nella sua ragnatela. Per mantenere la sua eterna giovinezza, Elise preferisce nutrirsi degli innocenti e di chi non ha fede. Solo in pochi sanno resistere alla sua seduzione.", "blurb": "Elise è una letale predatrice che vive in un palazzo sbarrato e senza luce, nel profondo della città più vecchia di Noxus. Un tempo era una mortale a capo di una potente casata, ma il morso di un malvagio semidio la trasformò in qualcosa di bello...", "allytips": ["La forma aracnide è più efficace nel finire i nemici con salute bassa, mentre la neurotossina della forma umana causa più danni ai nemici in salute. ", "In forma aracnide, i Ragnetti attaccheranno i bersagli su cui Elise ha inflitto il morso velenoso. ", "La forma aracnide di Elise e le sue abilità in forma aracnide non consumano mana e possono essere utili quando stai cercando di conservare mana."], "enemytips": ["La forma aracnide di Elise è più pericolosa quando la tua salute è bassa, mentre la sua forma umana è più potente quando la saluta è alta.", "Calata muoverà Elise solo verticalmente, a meno che possa discendere su un nemico.", "Calata ha un tempo di ricarica lungo ed Elise è vulnerabile dopo che l'ha usata."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 5, "magic": 7, "difficulty": 9}, "stats": {"hp": 620, "hpperlevel": 109, "mp": 324, "mpperlevel": 50, "movespeed": 330, "armor": 30, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "EliseHumanQ", "name": "Neurotossina/morso velenoso", "description": "Forma umana: infligge danni in base a quanto è alta la salute del nemico.<br><br>Forma aracnide: balza sul nemico e infligge danni in base a quanto è bassa la salute del nemico.", "tooltip": "<keywordMajor><PERSON>a umana</keywordMajor>: <PERSON> in<PERSON> una neurotossina che infligge <magicDamage>{{ basedamage }} (+{{ humanpercenthealth }} salute attuale) danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Limite danni ai mostri", "Costo in mana (Neurotossina)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ spiderbasedamage }} -> {{ spiderbasedamageNL }}", "{{ monstercapdamage }} -> {{ monstercapdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "EliseHumanQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EliseHumanW", "name": "Larva instabile/furia chitinosa", "description": "Forma umana: rilascia un Ragnetto velenoso che esplode nei pressi di un nemico.<br><br>Forma aracnide: Elise e i suoi Ragnetti guadagnano velocità d'attacco.", "tooltip": "<keywordMajor><PERSON>a umana</keywordMajor>: <PERSON> e<PERSON> un Ragnetto che raggiunge un luogo designato ed esplode quando raggiunge un nemico o dopo 3 secondi, infliggendo <magicDamage>{{ spell.elisehumanw:totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [65, 75, 85, 95, 105], [275, 275, 275, 275, 275], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4", "0", "0", "3", "65/75/85/95/105", "275", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "EliseHumanW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EliseHumanE", "name": "Bozzolo/calata", "description": "Forma umana: stordisce la prima unità colpita e la rivela se non è invisibile.<br><br>Forma aracnide: Elise e i suoi Ragnetti si librano in aria e poi si calano sul bersaglio nemico. Dopo aver raggiunto il bersaglio, i danni bonus e la guarigione di Elise in Forma aracnide aumentano.", "tooltip": "<keywordMajor><PERSON>a umana</keywordMajor>: <PERSON> sca<PERSON> un bozzolo che <status>stordisce</status> e rivela il primo nemico colpito per {{ stunduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> stordimento:", "Ricarica (Bozzolo)", "Ricarica (Calata)", "Danni e guarigione aumentati"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e3 }} -> {{ e3NL }}", "{{ e6 }}% -> {{ e6NL }}%"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [12, 11.5, 11, 10.5, 10], [15, 20, 25, 30, 35], [22, 21, 20, 19, 18], [2, 2, 2, 2, 2], [1.6, 1.8, 2, 2.2, 2.4], [40, 55, 70, 85, 100], [250, 250, 250, 250, 250], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "12/11.5/11/10.5/10", "15/20/25/30/35", "22/21/20/19/18", "2", "1.6/1.8/2/2.2/2.4", "40/55/70/85/100", "250", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EliseHumanE.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EliseR", "name": "Forma aracnide", "description": "Si trasforma in un minaccioso ragno, perdendo gittata d'attacco ma ottenendo velocità di movimento, nuove abilità e uno sciame di Ragnetti che attacca i suoi nemici.", "tooltip": "<keywordMajor>Forma umana</keywordMajor>: <PERSON> si trasforma in un ragno minaccioso, i suoi attacchi non colpiscono più a distanza, può accedere alle abilità della <keywordMajor>Forma aracnide</keywordMajor> ed evoca tutti i <keywordMajor><PERSON><PERSON><PERSON></keywordMajor> inattivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> morso Forma aracnide", "<PERSON>ni <PERSON>", "Numero mass<PERSON>", "Armatura <PERSON>", "Resistenza magica <PERSON>tti"], "effect": ["{{ passivebonusdamage }} -> {{ passivebonusdamageNL }}", "{{ spiderlingbasedamage }} -> {{ spiderlingbasedamageNL }}", "{{ spiderlingsstored }} -> {{ spiderlingsstoredNL }}", "{{ e6 }} -> {{ e6NL }}", "{{ e7 }} -> {{ e7NL }}"]}, "maxrank": 4, "cooldown": [3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [12, 22, 32, 42], [25, 25, 25, 25], [25, 25, 25, 25], [2, 3, 4, 5], [30, 50, 70, 90], [50, 70, 90, 110], [6, 8, 10, 12], [0.08, 0.08, 0.08, 0.08], [0, 0, 0, 0]], "effectBurn": [null, "0", "12/22/32/42", "25", "25", "2/3/4/5", "30/50/70/90", "50/70/90/110", "6/8/10/12", "0.08", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "EliseR.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Regina dei ragni", "description": "Forma umana: quando <PERSON> colpisce un nemico con le sue abilità, ottiene un Ragnetto.<br><br>Forma aracnide: gli attacchi base infliggono danni magici bonus e ripristinano salute a <PERSON>.", "image": {"full": "ElisePassive.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}