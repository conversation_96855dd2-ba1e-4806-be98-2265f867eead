{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akshan": {"id": "<PERSON><PERSON><PERSON>", "key": "166", "name": "<PERSON><PERSON><PERSON>", "title": "der rebellische Wächter", "image": {"full": "Akshan.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "166000", "num": 0, "name": "default", "chromas": false}, {"id": "166001", "num": 1, "name": "Cyberpop-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "166010", "num": 10, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "166020", "num": 20, "name": "<PERSON><PERSON>", "chromas": false}], "lore": "<PERSON><PERSON><PERSON> l<PERSON>t sich selten von Gefahren aus der Ruhe bringen und bekämpft das Böse mit umwerfendem Charisma, gerechter Vergeltung und einem auffälligen Mangel an Hemden. Sein besonderes Talent ist es, im Verborgenen zu agieren und unentdeckt von seinen Feinden erst dort aufzutauchen, wo sie ihn am wenigsten erwarten. Mit einem ausgeprägten Sinn für Gerechtigkeit und einer legendären Wiederbelebungswaffe verpasst er Runeterras vielen Halunken einen Denkzettel und folgt dabei seinem eigenen Kodex: „Sei kein Mistkerl.“", "blurb": "<PERSON><PERSON><PERSON> l<PERSON>t sich selten von Gefahren aus der Ruhe bringen und bekämpft das Böse mit umwerfendem Charisma, gerechter Vergeltung und einem auffälligen Mangel an Hemden. Sein besonderes Talent ist es, im Verborgenen zu agieren und unentdeckt von seinen...", "allytips": ["<PERSON><PERSON><PERSON> l<PERSON>t sich selten von Gefahren aus der Ruhe bringen und bekämpft das Böse mit umwerfendem Charisma, gerechter Vergeltung und einem auffälligen Mangel an Hemden. Sein besonderes Talent ist es, im Verborgenen zu agieren und unentdeckt von seinen Feinden erst dort aufzutauchen, wo sie ihn am wenigsten erwarten. Mit einem ausgeprägten Sinn für Gerechtigkeit und einer legendären Wiederbelebungswaffe verpasst er Runeterras vielen Halunken einen Denkzettel und folgt dabei seinem eigenen Kodex: „Sei kein Mistkerl.“"], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> wirft einen Bumerang, der auf dem Hin- und Rückweg Schaden verursacht. Immer wenn der Bumerang einen Gegner trifft, erhöht sich seine Reichwei<PERSON>.", "tooltip": "<PERSON><PERSON><PERSON> wirft einen Bumerang, der <physicalDamage>{{ finaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht. Immer wenn der Bumerang einen Gegner trifft, erhöht sich seine Reichweite.<br /><br /><PERSON><PERSON><PERSON> gewähren Akshan <speed>{{ totalhaste }}&nbsp;Lauftempo</speed>, das über {{ hasteduration }}&nbsp;Sekunde hinweg abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Vasallenschaden (%)", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ secondarytargetdamage*100.000000 }}&nbsp;% -> {{ secondarytargetdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.25, 6.5, 5.75, 5], "cooldownBurn": "8/7.25/6.5/5.75/5", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "AkshanQ.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanW", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> markiert passiv gegnerische Champions als Halunken, wenn sie seine Verbündeten töten. <PERSON><PERSON> <PERSON><PERSON><PERSON> einen Halunken tötet, werden alle Verbündeten, die von ihm getötet wurden, wied<PERSON><PERSON><PERSON><PERSON>. Außerdem erhält er zusätzliches Gold und entfernt alle Markierungen.<br><br>Bei Aktivierung camoufliert sich Akshan und erhält Lauftempo und Manaregeneration, wenn er sich auf Halunken zubewegt. <PERSON><PERSON>han verliert die Camouflage rasch, wenn er sich nicht im hohen Gras oder in der Nähe von Terrain befindet.", "tooltip": "{{ Spell_AkshanW_Tooltip_{{ gamemodeinteger }} }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "Lauftempo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ msvalue }} -> {{ msvalueNL }}"]}, "maxrank": 5, "cooldown": [18, 14, 10, 6, 2], "cooldownBurn": "18/14/10/6/2", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "AkshanW.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanE", "name": "Hero<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> feuert einen Greifhaken in das Terrain und schwingt sich dann mit dem Seil nach vorn. Währenddessen schießt er wiederholt auf den nächstbefindlichen Gegner. Er kann vorzeitig abspringen oder heruntergestoßen werden, wenn er mit Champions oder Terrain kollidiert.", "tooltip": "<spellActive>1.&nbsp;Aktivierung:</spellActive> <PERSON><PERSON><PERSON> feuert einen Greifhaken ab, der im ersten getroffenen Terrain stecken bleibt.<br /><br /><spellActive>2.&nbsp;Aktivierung:</spellActive> <PERSON><PERSON><PERSON> schwingt sich am Terrain entlang und schießt wiederholt auf den nächstbefindlichen Gegner, wobei er pro Schuss <physicalDamage>{{ asmoddamagetodeal }}&nbsp;normalen Schaden</physicalDamage> verursacht.<br /><br /><spellActive>3.&nbsp;Aktivierung:</spellActive> A<PERSON>han springt vom Seil ab und feuert einen letzten Schuss ab.<br /><br />Wenn er mit einem gegnerischen Champion oder mit Terrain kollidiert, wird sein Schwung vorzeitig abgebrochen.<br /><br />Champion-Kills/Unterstützungen setzen die Abklingzeit dieser Fähigkeit zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AkshanE.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanR", "name": "Wohlverdiente Strafe", "description": "<PERSON><PERSON><PERSON> nimmt einen gegnerischen Champion ins Visier und beginnt damit, <PERSON><PERSON><PERSON> zu laden. Bei der Ausführung feuert Akshan alle geladenen Kugeln ab und fügt dem ersten getroffenen Champion, Vasallen oder Gebäude Schaden basierend auf dem fehlenden Leben zu.", "tooltip": "<PERSON><PERSON><PERSON> nimmt einen Champion ins Visier und überlädt seine Waffe bis zu {{ channelduration }}&nbsp;<PERSON><PERSON><PERSON> lang, um bis zu {{ numberofbullets }}&nbsp;<PERSON>geln zu laden.<br /><br /><recast>Reaktivierung:</recast> <PERSON><PERSON><PERSON> feuert die geladenen Kugeln ab, wobei jede dem ersten getroffenen Gegner oder Gebäude mindestens <physicalDamage>{{ damageperbulletwithcrit }}&nbsp;normalen Schaden</physicalDamage> zufügt (wird erhöht auf bis zu <physicalDamage>{{ maxdamageperbullet }}&nbsp;normalen Schaden</physicalDamage> basierend auf dem fehlenden Leben).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Maximale Kugeln", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ numberofbullets }} -> {{ numberofbulletsNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "AkshanR.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> ha<PERSON>", "description": "Jeder 3. <PERSON><PERSON><PERSON> mit Akshans Angriffen oder Fähigkeiten verursacht zusätzlichen Schaden und gewährt ihm einen Schild, falls das Ziel ein Champion war.<br><br><PERSON><PERSON><PERSON> ang<PERSON>, feuert er einen zusätzlichen Angriff ab, der verringerten Schaden verursacht. Wenn er den zusätzlichen Angriff abbricht, erhält er stattdessen Lauftempo.", "image": {"full": "akshan_p.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}