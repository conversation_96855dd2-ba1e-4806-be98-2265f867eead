{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Chogath": {"id": "<PERSON><PERSON><PERSON>", "key": "31", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "the Terror of the Void", "image": {"full": "Chogath.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "31000", "num": 0, "name": "default", "chromas": false}, {"id": "31001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "31002", "num": 2, "name": "Gentleman <PERSON><PERSON>", "chromas": false}, {"id": "31003", "num": 3, "name": "Loch Ness <PERSON>ath", "chromas": false}, {"id": "31004", "num": 4, "name": "Jurassic Cho'Gath", "chromas": false}, {"id": "31005", "num": 5, "name": "Battlecast Prime <PERSON>", "chromas": true}, {"id": "31006", "num": 6, "name": "Prehistoric <PERSON>", "chromas": false}, {"id": "31007", "num": 7, "name": "Dark Star Cho'Gath", "chromas": false}, {"id": "31014", "num": 14, "name": "Shan <PERSON> Scrolls Cho'Gath", "chromas": true}, {"id": "31023", "num": 23, "name": "Broken Covenant Cho'Gath", "chromas": true}, {"id": "31032", "num": 32, "name": "Toy Terror Cho'Gath", "chromas": true}], "lore": "Sejak pertama kali <PERSON> muncul dan terpapar sinar matahari Runeterra, monster ini didorong oleh rasa lapar paling dahsyat dan tak terpuaskan. Ekspresi sempurna dari hasrat Void untuk melahap semua kehidup<PERSON>, sifat biologis Cho'Gath yang rumit mengubah materi menjadi pertumbuhan tubuh baru dengan cepat. Massa dan kepadatan ototnya meningkat pesat, dan karapas luarnya jadi mengeras layaknya berlian. Ketika tumbuh besar tidak sesuai dengan kebutuhan spawn Void, dia memuntahkan kelebihan materi menjadi duri setajam silet, membuat mangsanya yang tertusuk siap untuk disantap nanti.", "blurb": "Sejak pertama kali <PERSON> muncul dan terpapar sinar matah<PERSON>, monster ini didorong oleh rasa lapar paling dahsyat dan tak terpuaskan. Ekspresi sempurna dari hasrat Void untuk melahap semua keh<PERSON>, sifat biologis Cho'Gath yang rumit...", "allytips": ["Coba sejajar<PERSON> serangan Vorpal Spikes agar bisa membunuh minion sekaligus menyerang champion musuh secara be<PERSON>.", "<PERSON>ka sulit melahap champion, coba lahap minion dulu sampai ukuran<PERSON> lebih besar.", "Menggunakan Rupture pada creep dikombinasikan dengan Carnivore bagus untuk mendapatkan Health dan Mana."], "enemytips": ["Membeli beberapa item HP mengurangi kesempatan Cho'Gath membunuhmu dengan cepat.", "Fokus untuk mencegah <PERSON>G<PERSON> mencapai ukuran maksimumnya.", "Rupture memiliki awan asap yang menandakan area target serangannya. Cobalah mengawasinya untuk mencegah Cho'Gath melakukan combo Ability."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 5}, "stats": {"hp": 644, "hpperlevel": 94, "mp": 270, "mpperlevel": 60, "movespeed": 345, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.2, "attackspeedperlevel": 1.44, "attackspeed": 0.658}, "spells": [{"id": "Rupture", "name": "Rupture", "description": "Menghancurkan tanah di lokasi target, melontarkan musuh ke udara, men<PERSON><PERSON><PERSON><PERSON> damage dan menerapkan efek slow pada mereka.", "tooltip": "Cho'Gath menghancurkan tanah, menerapkan efek <status>Knock Up</status> pada musuh selama {{ e5 }} detik, men<PERSON><PERSON><PERSON>an <magicDamage>{{ totaldamagetooltip }} magic damage</magicDamage>, dan menerapkan efek <status>Slow</status> pada mereka sebesar {{ e2 }}% selama {{ e3 }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [60, 60, 60, 60, 60], [1.5, 1.5, 1.5, 1.5, 1.5], [0.625, 0.625, 0.625, 0.625, 0.625], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "60", "1.5", "0.63", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "Rupture.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FeralScream", "name": "Feral Scream", "description": "<PERSON><PERSON><PERSON><PERSON> berteriak mengerikan ke arah musuh dalam kerucut, mengh<PERSON>lkan magic damage dan menerapkan Silence pada musuh selama beberapa detik.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>, men<PERSON><PERSON><PERSON> <status>Silence</status> pada musuh selama {{ e2 }} detik dan men<PERSON><PERSON>an <magicDamage>{{ totaldamagetooltip }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ e2 }}-> {{ e2NL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1.6, 1.7, 1.8, 1.9, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1.6/1.7/1.8/1.9/2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "FeralScream.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VorpalSpikes", "name": "Vorpal Spikes", "description": "<PERSON><PERSON><PERSON> melu<PERSON>kan spike mematikan, memberi damage dan slow ke semua unit musuh di depannya.", "tooltip": "3 Serangan <PERSON>'<PERSON>ath berikutnya meluncurkan spike yang men<PERSON><PERSON> <magicDamage>{{ flatdamagecalc }} magic damage plus {{ maxhealthpercentcalc }} dari Health maksimum target</magicDamage> dan menerapkan <status>Slow</status> sebesar {{ slowamountpercentage }}%, yang berkurang dalam {{ slowduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage Health Maksimum", "Slow", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ percenthealthdamage }}%-> {{ percenthealthdamageNL }}%", "{{ slowamountpercentage }}%-> {{ slowamountpercentageNL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [40, 40, 40, 40, 40], "rangeBurn": "40", "image": {"full": "VorpalSpikes.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Feast", "name": "Feast", "description": "Devour salah satu unit musuh, <PERSON><PERSON><PERSON><PERSON><PERSON> true damage dalam jumlah besar. Jika target te<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> akan bertum<PERSON>h dan mendapatkan Health maksimum.", "tooltip": "Cho'Gath dengan rakus memakan musuh, <PERSON><PERSON><PERSON><PERSON><PERSON> <trueDamage>{{ rdamage }} true damage</trueDamage> ke champion atau <trueDamage>{{ rmonsterdamage }}</trueDamage> minion, atau monster jungle. <PERSON>ka berhasil kill target, Cho'<PERSON>ath mendapatkan stack, yang membuatnya membesar dan mendapatkan <healing>{{ rhealthperstack }} Health maksimum</healing>. Hanya {{ rminionmaxstacks }} stack yang bisa didapatkan dari minion dan monster jungle non-epik. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Champion", "Health per Stack", "Attack Range per Stack", "Cooldown"], "effect": ["{{ rbasedamage }}-> {{ rbasedamageNL }}", "{{ rhealthperstack }}-> {{ rhealthperstackNL }}", "{{ attackrangeperstack }}-> {{ attackrangeperstackNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 70, 60], "cooldownBurn": "80/70/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [175, 175, 175], "rangeBurn": "175", "image": {"full": "Feast.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Carnivore", "description": "Setiap kali <PERSON> membunuh satu unit, dia akan memulihkan Health dan Mana. <PERSON><PERSON> yang dipulihkan meningkat seiring dengan level Cho'Gath.", "image": {"full": "GreenTerror_TailSpike.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}