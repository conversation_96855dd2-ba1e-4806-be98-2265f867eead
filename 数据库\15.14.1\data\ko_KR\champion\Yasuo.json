{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yasuo": {"id": "<PERSON><PERSON><PERSON>", "key": "157", "name": "야스오", "title": "용서받지 못한 자", "image": {"full": "Yasuo.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "157000", "num": 0, "name": "default", "chromas": false}, {"id": "157001", "num": 1, "name": "하이 눈 야스오", "chromas": true}, {"id": "157002", "num": 2, "name": "프로젝트: 야스오", "chromas": false}, {"id": "157003", "num": 3, "name": "핏빛달 야스오", "chromas": false}, {"id": "157009", "num": 9, "name": "어둠의 인도자 야스오", "chromas": true}, {"id": "157010", "num": 10, "name": "오디세이 야스오", "chromas": true}, {"id": "157017", "num": 17, "name": "중간보스 야스오", "chromas": true}, {"id": "157018", "num": 18, "name": "True Damage 야스오", "chromas": true}, {"id": "157035", "num": 35, "name": "프레스티지 True Damage 야스오", "chromas": false}, {"id": "157036", "num": 36, "name": "영혼의 꽃 야스오", "chromas": true}, {"id": "157045", "num": 45, "name": "뱃사람 야스오", "chromas": true}, {"id": "157054", "num": 54, "name": "진리용 야스오", "chromas": false}, {"id": "157055", "num": 55, "name": "몽상용 야스오", "chromas": false}, {"id": "157056", "num": 56, "name": "먹그림자 야스오", "chromas": true}, {"id": "157057", "num": 57, "name": "프레스티지 먹그림자 야스오", "chromas": false}, {"id": "157068", "num": 68, "name": "선지자 야스오", "chromas": true}, {"id": "157077", "num": 77, "name": "전투 늑대 야스오", "chromas": true}, {"id": "157087", "num": 87, "name": "최초의 어둠의 인도자 야스오", "chromas": false}], "lore": "굳은 결의를 품은 아이오니아의 검객 야스오는 날렵한 검술과 바람을 자유로이 다루는 능력으로 적을 쓰러뜨린다. 젊은 시절, 자부심으로 가득 찼던 야스오는 스승을 살해한 누명을 쓰게 되고, 결백을 증명할 길이 없는 상황에서 급기야는 자신을 보호하기 위해 친형까지 죽음으로 이끌게 된다. 스승을 살해한 진범이 결국 밝혀졌지만, 지금도 야스오는 자신의 검을 인도하는 바람에만 의존한 채 고향 아이오니아를 배회하고 있다. 과거의 자신을 아직 용서하지 못한 채로.", "blurb": "굳은 결의를 품은 아이오니아의 검객 야스오는 날렵한 검술과 바람을 자유로이 다루는 능력으로 적을 쓰러뜨린다. 젊은 시절, 자부심으로 가득 찼던 야스오는 스승을 살해한 누명을 쓰게 되고, 결백을 증명할 길이 없는 상황에서 급기야는 자신을 보호하기 위해 친형까지 죽음으로 이끌게 된다. 스승을 살해한 진범이 결국 밝혀졌지만, 지금도 야스오는 자신의 검을 인도하는 바람에만 의존한 채 고향 아이오니아를 배회하고 있다. 과거의 자신을 아직 용서하지 못한 채로.", "allytips": ["적이 도망갈 때는 미니언을 뚫고 돌진해서 질풍검 효과로 추격하세요. 적을 대상으로 해서 돌진한 다음 미니언을 활용하여 퇴로를 확보하는 것도 좋습니다.", "18레벨에서 55%의 공격 속도 아이템을 갖추면 강철 폭풍의 공격 속도를 최대로 올릴 수 있습니다.", "최후의 숨결은 공중에 뜬 대상이라면 누구에게든 시전할 수 있습니다. 아군이 띄우는 적을 놓치지 마세요."], "enemytips": ["강철 폭풍의 폭은 아주 좁습니다. 가능하면 옆으로 피하세요.", "야스오가 강철 폭풍을 연이어 두 번 맞히면, 다음에는 회오리가 발사됩니다. 해당 스킬의 음향 효과가 들리면 바로 피할 수 있도록 준비하세요.", "야스오는 회오리 바람을 발사한 직후에 가장 취약해집니다. 이 때를 노려 교전을 벌이세요.", "야스오의 결의 보호막은 지속시간이 2초에 불과합니다. 피해를 입히고 2초 동안 기다린 후 교전을 개시하세요."], "tags": ["Fighter", "Assassin"], "partype": "기류", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 10}, "stats": {"hp": 590, "hpperlevel": 110, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.9, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.697}, "spells": [{"id": "YasuoQ1Wrapper", "name": "강철 폭풍", "description": "전방으로 내질러 일직선 상의 모든 적에게 피해를 입힙니다.<br><br>적중 시, 몇 초간 폭풍 구름 효과가 1회 중첩됩니다. 2회 중첩되면 강철 폭풍 스킬이 회오리바람을 발사하여 <font color='#6655CC'>공중으로</font> 띄워올립니다.<br><br>강철 폭풍은 기본 공격으로 간주되어 같은 효과가 적용됩니다.", "tooltip": "야스오가 검을 내질러 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 적중 시 {{ gatheringstormduration }}초간 1회 중첩됩니다. 2회 중첩 시 이 스킬을 다시 사용하면 회오리바람을 날려 동일한 피해를 입히고 {{ knockupdurationtooltiponly }}초 동안 <status>띄워 올립니다</status>.<br /><br />돌진 도중 이 스킬을 사용할 경우 원형으로 타격합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "YasuoQ1Wrapper.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YasuoW", "name": "바람 장막", "description": "4초간 모든 적의 투사체를 막아주는 움직이는 벽을 생성합니다.", "tooltip": "4초간 모든 적의 투사체를 막아주는 바람의 벽을 생성합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["폭", "재사용 대기시간"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [25, 23, 21, 19, 17], "cooldownBurn": "25/23/21/19/17", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [15, 20, 25, 30, 35], [60, 90, 120, 150, 180], [3, 6, 9, 12, 15], [300, 350, 400, 450, 500], [320, 390, 460, 530, 600], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "15/20/25/30/35", "60/90/120/150/180", "3/6/9/12/15", "300/350/400/450/500", "320/390/460/530/600", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "YasuoW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YasuoE", "name": "질풍검", "description": "대상 적을 뚫고 돌진하여 마법 피해를 입힙니다. 이후 사용할 때마다 돌진의 피해량이 증가하며 최대치에 도달하면 더 이상 증가하지 않습니다.<br><br>몇 초 내에는 동일한 대상에게 재사용할 수 없습니다.<br><br><font color='#99FF99'>돌진 도중 사용할 경우, 강철 폭풍이 원형으로 타격합니다.</font>", "tooltip": "대상을 뚫고 돌진하여 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 이후 사용할 때마다 {{ stackduration }}초간 돌진의 추가 피해량이 <magicDamage>{{ bonusdamageperstack }}</magicDamage>씩 상승하며, 이 효과는 최대 {{ maxstacks }}회 중첩됩니다.<br /><br />이 스킬은 공격 대상별로 {{ e2 }}초의 재사용 대기시간이 적용됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "유닛당 재사용 대기시간", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.4, 0.3, 0.2, 0.1], "cooldownBurn": "0.5/0.4/0.3/0.2/0.1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [10, 9, 8, 7, 6], [0.5, 0.4, 0.3, 0.2, 0.1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "10/9/8/7/6", "0.5/0.4/0.3/0.2/0.1", "0", "0", "750", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "YasuoE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YasuoR", "name": "최후의 숨결", "description": "<factionIonia1>공중에 뜬</factionIonia1> 적 챔피언에게 점멸하여 물리 피해를 입히고, 해당 지역에서 <factionIonia1>공중에 떠 있는</factionIonia1> 모든 적을 공중에 붙들어둡니다. 기류가 최대치로 차는 대신, 폭풍 구름의 중첩이 모두 초기화됩니다.<br><br>이후 몇 초 동안 야스오의 치명타에 높은 방어구 관통력이 추가로 적용됩니다.", "tooltip": "야스오가 적 챔피언에게 순간이동하여 <status>공중</status>에 띄워 붙들어 두며 <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입히고 주변 모든 적을 {{ rknockupduration }}초 더 <status>공중</status>에 붙들어 둡니다. <keywordMajor>기류</keywordMajor>가 최대치로 차는 대신, <spellName>강철 폭풍</spellName> 중첩을 모두 잃습니다.<br /><br />이후 야스오의 치명타 공격이 {{ rbuffduration }}초 동안 대상 <scaleArmor>추가 방어력의 {{ rpercentarmorpen }}%</scaleArmor>를 무시합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [70, 50, 30], "cooldownBurn": "70/50/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [1400, 1400, 1400], "rangeBurn": "1400", "image": {"full": "YasuoR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "낭인의 길", "description": "야스오의 치명타 확률이 증가합니다. 또한, 야스오는 이동할 때마다 보호막이 충전되며, 챔피언이나 몬스터로부터 피해를 입으면 보호막이 발동됩니다.", "image": {"full": "Yasuo_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}