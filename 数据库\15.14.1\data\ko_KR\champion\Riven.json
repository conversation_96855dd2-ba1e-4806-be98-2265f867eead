{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Riven": {"id": "Riven", "key": "92", "name": "리븐", "title": "추방자", "image": {"full": "Riven.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "92000", "num": 0, "name": "default", "chromas": false}, {"id": "92001", "num": 1, "name": "구원받은 리븐", "chromas": false}, {"id": "92002", "num": 2, "name": "핏빛 친위대 리븐", "chromas": false}, {"id": "92003", "num": 3, "name": "전투 토끼 리븐", "chromas": true}, {"id": "92004", "num": 4, "name": "2012 월드 챔피언십 리븐", "chromas": false}, {"id": "92005", "num": 5, "name": "화룡검 리븐", "chromas": true}, {"id": "92006", "num": 6, "name": "아케이드 리븐", "chromas": true}, {"id": "92007", "num": 7, "name": "재탄생한 2012 월드 챔피언십 리븐", "chromas": true}, {"id": "92016", "num": 16, "name": "빛의 인도자 리븐", "chromas": true}, {"id": "92018", "num": 18, "name": "펄스 건 리븐", "chromas": true}, {"id": "92020", "num": 20, "name": "불멸의 영웅 리븐", "chromas": false}, {"id": "92022", "num": 22, "name": "프레스티지 불멸의 영웅 리븐", "chromas": false}, {"id": "92023", "num": 23, "name": "영혼의 꽃 리븐", "chromas": true}, {"id": "92034", "num": 34, "name": "감시자 리븐", "chromas": true}, {"id": "92044", "num": 44, "name": "전투 토끼 프라임 리븐", "chromas": true}, {"id": "92045", "num": 45, "name": "프레스티지 불멸의 영웅 리븐 (2022)", "chromas": false}, {"id": "92055", "num": 55, "name": "깨진 언약 리븐", "chromas": true}, {"id": "92063", "num": 63, "name": "태고의 습격 리븐", "chromas": true}], "lore": "한때 녹서스 군의 소드마스터였던 리븐은 그녀가 정복하려던 땅에서 추방자로 살아가고 있다. 그녀는 확고한 믿음과 잔혹함에 가까운 능력에 힘입어 상급 군인으로 진급하고 전설의 룬 검과 군대를 포상으로 받았다. 그러나 녹서스에 대한 리븐의 믿음은 아이오니아 전선에서 시험대에 올랐고 결국 산산히 깨지고 말았다. 제국과의 모든 연결고리를 끊어버린 그녀는 산산이 조각난 세상 속 몸을 맡길 곳을 찾아 방랑하고 있다. 녹서스 제국이 재건되었다는 무성한 소문에도 불구하고...", "blurb": "한때 녹서스 군의 소드마스터였던 리븐은 그녀가 정복하려던 땅에서 추방자로 살아가고 있다. 그녀는 확고한 믿음과 잔혹함에 가까운 능력에 힘입어 상급 군인으로 진급하고 전설의 룬 검과 군대를 포상으로 받았다. 그러나 녹서스에 대한 리븐의 믿음은 아이오니아 전선에서 시험대에 올랐고 결국 산산히 깨지고 말았다. 제국과의 모든 연결고리를 끊어버린 그녀는 산산이 조각난 세상 속 몸을 맡길 곳을 찾아 방랑하고 있다. 녹서스 제국이 재건되었다는 무성한 소문에도...", "allytips": ["리븐의 부러진 날개 스킬은 마우스 커서가 올려져 있는 챔피언을 대상으로 합니다. 만약 적 챔피언 너머로 이동하고 싶다면 적 챔피언 앞쪽에 커서를 가져다 두십시오.", "리븐은 치료 효과, 방어 효과를 가진 스킬이 부족하기 때문에 반드시 스킬 연계로 순간 화력을 뽑아내야 합니다. 부러진 날개와 기 폭발 스킬을 이용하여 전투에 진입한 뒤 용맹 스킬을 사용하여 전장에서 벗어나며 피해를 방어하십시오."], "enemytips": ["리븐은 기동력이 매우 뛰어난 챔피언이지만 스킬 하나만 가지고는 멀리 움직일 수 없습니다. 스킬 연계 중 이동 불가 효과나 침묵 효과가 있는 스킬을 사용하면 리븐의 기동력을 크게 저하시킬 수 있습니다.", "리븐이 가하는 피해는 전부 물리 피해입니다. 적 리븐을 도저히 막을 수 없겠다 싶으면 방어력을 최우선으로 올리세요.", "리븐은 광역 스킬이 많으므로, 리븐이 대부분의 스킬을 사용할 수 없는 상태가 아닌 한 다 같이 들어가지 마세요."], "tags": ["Fighter", "Assassin"], "partype": "없음", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 8}, "stats": {"hp": 630, "hpperlevel": 100, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "RivenTriCleave", "name": "부러진 날개", "description": "리븐이 돌진하며 적을 베어 넘깁니다. 이 스킬은 세 번 사용할 수 있으며 세 번째로 사용할 때에는 맞은 적을 밀쳐냅니다.", "tooltip": "리븐이 전방으로 짧게 돌진하여 <physicalDamage>{{ firstslashdamage }}의 물리 피해</physicalDamage>를 입힙니다. 이 스킬은 2회 <recast>재사용</recast>할 수 있습니다. 최초 <recast>재사용</recast> 시 기존과 똑같은 효과가 적용되지만, 두 번째에는 다른 효과가 적용됩니다.<br /><br /><recast>재사용 시</recast>: 리븐이 공중으로 뛰어 오른 후 땅을 내려찍으며 <physicalDamage>{{ firstslashdamage }}의 물리 피해</physicalDamage>를 주고 주위 적들을 0.75초 동안 <status>공중으로 띄워 올립니다</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "공격력 계수"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [13, 13, 13, 13, 13], "cooldownBurn": "13", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [45, 75, 105, 135, 165], [30, 55, 80, 105, 130], [150, 225, 300, 375, 450], [2, 2, 2, 2, 2], [65, 70, 75, 80, 85], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/75/105/135/165", "30/55/80/105/130", "150/225/300/375/450", "2", "65/70/75/80/85", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "RivenTriCleave.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "RivenMartyr", "name": "기 폭발", "description": "리븐이 기를 폭발시켜 근처의 적에게 피해를 주며 기절시킵니다.", "tooltip": "리븐의 검에서 룬 에너지가 방출되어 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ e5 }}초간 적을 <status>기절</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [40, 70, 100, 130, 160], [3, 3, 3, 3, 3], [0.75, 0.75, 0.75, 0.75, 0.75], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "40/70/100/130/160", "3", "0.75", "0.75", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [260, 260, 260, 260, 260], "rangeBurn": "260", "image": {"full": "RivenMartyr.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "Riven<PERSON><PERSON><PERSON>", "name": "용맹", "description": "리븐이 짧은 거리를 나아가며 피해를 일부 흡수합니다.", "tooltip": "리븐이 재빨리 돌진한 후 1.5초 동안 지속되는 <shield>{{ totalshield }}의 보호막</shield>을 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "재사용 대기시간"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [70, 95, 120, 145, 170], [4, 4, 4, 4, 4], [800, 800, 800, 800, 800], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "70/95/120/145/170", "4", "800", "1.5", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "RivenFeint.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "RivenFengShuiEngine", "name": "추방자의 검", "description": "리븐이 자신의 옛 검에 힘을 불어넣어 추가 공격력과 공격 사거리를 얻습니다. 또한, 지속시간 동안 강력한 원거리 스킬인 바람 가르기를 한 번 사용할 수 있게 됩니다.", "tooltip": "리븐의 검이 정신력으로 충만하여 {{ duration }}초 동안 <physicalDamage>공격력이 {{ bonusad }}</physicalDamage> 상승하고 공격 스킬과 기본 공격의 사거리가 증가합니다. 활성화된 동안 <recast>재사용</recast>할 수 있습니다.<br /><br /><recast>재사용 시:</recast> 바람 가르기를 사용해 대상이 잃은 체력에 비례하여 <physicalDamage>{{ mindamage }}</physicalDamage>~<physicalDamage>{{ maxdamage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["충격파 최소 피해", "충격파 최대 피해", "재사용 대기시간"], "effect": ["{{ minbase }} -> {{ minbaseNL }}", "{{ maxbase }} -> {{ maxbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [100, 150, 200], [300, 450, 600], [20, 20, 20], [15, 15, 15], [25, 25, 25], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "100/150/200", "300/450/600", "20", "15", "25", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "RivenFengShuiEngine.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "룬 검", "description": "리븐이 스킬을 사용하면 검이 충전됩니다. 기본 공격을 가하면 충전 횟수를 소모해 추가 피해를 입힙니다.", "image": {"full": "RivenRunicBlades.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}