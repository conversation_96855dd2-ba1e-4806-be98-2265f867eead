{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gangplank": {"id": "Gangplank", "key": "41", "name": "Gangplank", "title": "the Saltwater Scourge", "image": {"full": "Gangplank.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "41000", "num": 0, "name": "default", "chromas": false}, {"id": "41001", "num": 1, "name": "Spooky Gangplank", "chromas": false}, {"id": "41002", "num": 2, "name": "Minuteman Gangplank", "chromas": false}, {"id": "41003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "41004", "num": 4, "name": "Toy Soldier Gangplank", "chromas": false}, {"id": "41005", "num": 5, "name": "Special Forces Gangplank", "chromas": false}, {"id": "41006", "num": 6, "name": "Sultan <PERSON>", "chromas": false}, {"id": "41007", "num": 7, "name": "Captain <PERSON><PERSON>", "chromas": false}, {"id": "41008", "num": 8, "name": "Dreadnova Gangplank", "chromas": true}, {"id": "41014", "num": 14, "name": "Pool Party Gangplank", "chromas": true}, {"id": "41021", "num": 21, "name": "FPX Gangplank", "chromas": true}, {"id": "41023", "num": 23, "name": "Gangplank the Betrayer", "chromas": true}, {"id": "41033", "num": 33, "name": "PROJECT: Gangplank", "chromas": true}], "lore": "Tak bisa diprediksi dan brutal, <PERSON><PERSON><PERSON>, raja gagak yang dilengserkan ditakuti di mana pun. <PERSON><PERSON>, dia memimpin kota pelabuhan Bilgewater. Meskipun pemerintahannya telah berakhir, ada yang percaya bahwa hal ini justru membuatnya makin berbahaya. Gangplank rela Bilgewater kembali bermandikan darah sebelum direbut pihak lain. Saat ini dia bertekad merebut kembali apa yang hilang dengan pistol, golok, dan tong mesiu.", "blurb": "Tak bisa diprediksi dan brutal, <PERSON><PERSON><PERSON>, raja gagak yang dilengserkan ditakuti di mana pun. <PERSON><PERSON>, dia memimpin kota pelabuhan Bilgewater. Meskipun pemerintahannya telah berak<PERSON>, ada yang percaya bahwa hal ini justru membuatnya makin berbahaya...", "allytips": ["<PERSON><PERSON>rley menerapkan efek On Hit seperti Frozen Mallet atau Black Cleaver.", "Memperhatikan musuh dengan health rendah di peta bisa member<PERSON>u kill dadakan dengan Cannon Barrage.", "Coba letakkan Cannon Barrage di jalur kabur untuk memotong jalan musuh yang melarikan diri."], "enemytips": ["<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> physical damage yang besar. Item yang member<PERSON>n <PERSON> dapat membantu jika Gangplank musuh dalam kondisi bagus.", "Setelah Gangplank mencapai level 6, hati-hati dengan ultima range global miliknya, Cannon Barrage!"], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 6, "magic": 4, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 280, "mpperlevel": 60, "movespeed": 345, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 4.2, "attackspeedperlevel": 3.2, "attackspeed": 0.658}, "spells": [{"id": "GangplankQWrapper", "name": "<PERSON><PERSON><PERSON>", "description": "Menembak target, menjarah Gold untuk tiap unit musuh yang di-kill.", "tooltip": "{{Spell_GangplankQWrapper_Tooltip_{{ gamemodeinteger }}}}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Plunder Gold", "Plunder Silver <PERSON>pent", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ e2 }}-> {{ e2NL }}", "{{ e5 }}-> {{ e5NL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4.5, 4.5, 4.5, 4.5, 4.5], "cooldownBurn": "4.5", "cost": [50, 45, 40, 35, 30], "costBurn": "50/45/40/35/30", "datavalues": {}, "effect": [null, [10, 40, 70, 100, 130], [3, 4, 5, 6, 7], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 5, 6, 7, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/40/70/100/130", "3/4/5/6/7", "0", "500", "4/5/6/7/8", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "GangplankQWrapper.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankW", "name": "<PERSON><PERSON><PERSON>", "description": "Memakan citrus untuk memulihkan efek crowd control dan memulihkan Health.", "tooltip": "Gangplank memakan buah citrus dalam jumlah besar, menghapus semua efek <status>Disable</status> dan men<PERSON> <healing>{{ basehealth }} plus {{ e2 }}% Health yang hilang</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heal", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [45, 70, 95, 120, 145], [13, 13, 13, 13, 13], [0.25, 0.25, 0.25, 0.25, 0.25], [200, 200, 200, 200, 200], [30, 40, 50, 60, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/70/95/120/145", "13", "0.25", "200", "30/40/50/60/70", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GangplankW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankE", "name": "Powder <PERSON>g", "description": "Gangplank mengungkap powder keg di lokasi target. <PERSON><PERSON> dia menyerang, powder keg akan meledak, menyebar damage serangan ke musuh di area tersebut dan menerapkan slow.", "tooltip": "Gangplank menempatkan powder keg yang dapat diserang oleh Gangplank dan champion musuh selama {{ e5 }} detik. Saat musuh menghancurkannya, powder keg telah dijinakkan. Saat Gangplank menghancurkannya, powder keg akan meledak, menerapkan <status>Slow</status> sebesar {{ finalslowamount }}% selama {{ e2 }} detik dan menghasilkan <physicalDamage>damage Serangan</physicalDamage>, mengabaikan {{ e0 }}% Armor. Champion menerima <physicalDamage>{{ e3 }} physical damage</physicalDamage> tambahan.<br /><br />Health powder keg berkurang tiap {{ f5 }} detik. Ledakan powder keg akan memicu lainnya dengan zona ledakan bertumpang tindih, tetapi tidak menghasilkan damage pada target yang sama lebih dari sekali. Ledakan powder keg yang dipicu oleh <spellName>Parrrley</spellName> akan memberikan gold bonus untuk target yang di-kill.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Bonus terhadap Champion", "Charge Maksimum", "Slow", "<PERSON><PERSON><PERSON>"], "effect": ["{{ e3 }}-> {{ e3NL }}", "{{ e1 }}-> {{ e1NL }}", "{{ barrelslow }}%-> {{ barrelslowNL }}%", "{{ ammorechargetime }}-> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [3, 3, 4, 4, 5], [2, 2, 2, 2, 2], [75, 105, 135, 165, 195], [40, 50, 60, 70, 80], [25, 25, 25, 25, 25], [2, 2, 2, 2, 2], [10, 10, 10, 10, 10], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 40, 40, 40, 40]], "effectBurn": [null, "3/3/4/4/5", "2", "75/105/135/165/195", "40/50/60/70/80", "25", "2", "10", "100", "0.5", "40"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "3", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GangplankE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "GangplankR", "name": "Cannon Barrage", "description": "Gangplank memberi sinyal ke kapalnya untuk membombardir area, menerapkan slow dan mengh<PERSON>lkan damage ke musuh.", "tooltip": "Gangplank memberi sinyal ke kapalnya untuk menembakkan {{ totalwavestooltip }} gelombang bola meriam di mana pun pada peta selama {{ zoneduration }} detik. Tiap gelombang menerapkan <status>Slow</status> sebesar {{ slowpercent }}% selama {{ slowduration }} detik dan menghasilkan <magicDamage>{{ onewavedamage }} magic damage</magicDamage>. Damage maksimum: {{ totaldamagetooltip }}<br /><br />Ability ini dapat di-upgrade di shop via <spellName>Parrrley</spellName>.<br /><br /><spellName>Fire at Will</spellName>: Menembakkan 6 gelombang bola meriam tambahan.<br /><spellName>Death's Daughter</spellName>: Menembakkan Mega-Cannonball yang menghasilkan <trueDamage>{{ deathsdaughterdamage }} true damage</trueDamage> dan menerapkan <status>Slow</status> sebesar {{ deathsdaughterslow }}% selama {{ deathsdaughterslowduration }} detik.<br /><spellName>Raise Morale</spellName>: Se<PERSON><PERSON> di dalam Cannon Barrage mendapatkan <speed>{{ raisemoralehaste }}% Move Speed</speed> selama {{ raisemoralehasteduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per Wave", "Cooldown"], "effect": ["{{ damageperwave }}-> {{ damageperwaveNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1.5, 0.5, 0.5], [0, 0, 0], [300, 300, 300], [75, 75, 75], [1, 1, 1], [40, 40, 40]], "effectBurn": [null, "0", "0", "0", "0", "1.5/0.5/0.5", "0", "300", "75", "1", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [30000, 30000, 30000], "rangeBurn": "30000", "image": {"full": "GangplankR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Trial by Fire", "description": "<PERSON><PERSON><PERSON>, se<PERSON><PERSON> melee <PERSON><PERSON><PERSON> akan membakar law<PERSON>.", "image": {"full": "Gangplank_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}