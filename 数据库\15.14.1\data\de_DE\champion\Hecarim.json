{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Hecarim": {"id": "<PERSON><PERSON><PERSON>", "key": "120", "name": "<PERSON><PERSON><PERSON>", "title": "der Schatten des Krieges", "image": {"full": "Hecarim.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "120000", "num": 0, "name": "default", "chromas": false}, {"id": "120001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120002", "num": 2, "name": "Schnitter<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120003", "num": 3, "name": "Kopfloser Hecarim", "chromas": false}, {"id": "120004", "num": 4, "name": "Arcade-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120005", "num": 5, "name": "Ahnenholz-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "120006", "num": 6, "name": "Weltenbrecher-Hecarim", "chromas": false}, {"id": "120007", "num": 7, "name": "Prächtige Nihilanze Hecarim", "chromas": false}, {"id": "120008", "num": 8, "name": "High Noon-Hecarim", "chromas": true}, {"id": "120014", "num": 14, "name": "Kosmisches Schlachtross Hecarim", "chromas": true}, {"id": "120022", "num": 22, "name": "Arkana-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "120031", "num": 31, "name": "Wintergeweihter Hecarim", "chromas": true}, {"id": "120041", "num": 41, "name": "Flammende Finsternis Hecarim", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> ist eine spektrale Verbindung aus Mann und Tier, und dazu verdammt, die Seelen der Lebenden bis in alle Ewigkeit zu jagen. Als der Schatten über die Gesegneten Inseln kam, wurde dieser stolze Ritter gemeinsam mit seiner Kavallerie und ihren Pferden von den tosenden Energien der Zerstörung ausgelöscht. I<PERSON>, wenn sich der schwarze Nebel j<PERSON>its von Runeterra ausbreiten will, führt er seinen vernichtenden Ansturm an und labt sich an der Schlacht und Zerstörung seiner Gegner, die er mit seinen gepanzerten Hufen niedertrampelt.", "blurb": "<PERSON><PERSON><PERSON> ist eine spektrale Verbindung aus Mann und Tier, und dazu verdammt, die Seelen der Lebenden bis in alle Ewigkeit zu jagen. Als der Schatten über die Gesegneten Inseln kam, wurde dieser stolze Ritter gemeinsam mit seiner Kavallerie und ihren...", "allytips": ["„Geist des Schreckens“ stellt <PERSON> wied<PERSON>, wenn nahe Gegner Schaden er<PERSON>iden, selbst wenn dieser von Verbündeten verursacht wurde. Nutze die Fähigkeit in größeren Kämpfen, um Hecarims Durchhaltevermögen zu steigern.", "„Vernichtender Ansturm“ verursacht mehr Schaden, je nachdem wie weit sich <PERSON><PERSON><PERSON> zuvor bewegt hat. Versuche eine Kombination mit „Ansturm der Schatten“ oder mit Beschwörerzaubern, etwa „Geist“ oder „Blitz“, um so viel Schaden wie möglich zu verursachen."], "enemytips": ["<PERSON><PERSON><PERSON> erh<PERSON>lt <PERSON> von <PERSON>, wenn er „Geist des Schreckens“ einset<PERSON>t, aber es mangelt ihm an Durchhaltevermögen, wesha<PERSON>b besonders Schadensspitzen effektiv gegen ihn sind.", "Hecarims ultimative Fähigkeit lässt Gegner Angst erleiden. Dein Team sollte sich verteilen, um ihn so indirekt zu schwächen."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 280, "mpperlevel": 40, "movespeed": 345, "armor": 32, "armorperlevel": 5.45, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 2.5, "attackspeed": 0.67}, "spells": [{"id": "HecarimRapidSlash", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> schlägt um sich und fügt nahen Gegnern normalen Schaden zu. Trifft Hecarim mindestens einen Gegner, verursacht er erhöhten Schaden und verringert die Abklingzeit für nachfolgendes „Toben“.", "tooltip": "<PERSON><PERSON><PERSON> schlägt zu und fügt <PERSON> in der Nähe <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zu. Wenn diese Fähigkeit trifft, erhält er eine Steigerung, die ihre Abklingzeit um {{ rampagecooldownreduction }}&nbsp;Sekunden verringert und ihren Schaden {{ e6 }}&nbsp;Sekunden lang um {{ rampagebonusdamageperc }}&nbsp;% erhöht. Maximal {{ e2 }}&nbsp;Steigerungen.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [28, 26, 24, 22, 20], "costBurn": "28/26/24/22/20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3", "1", "3", "60", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HecarimRapidSlash.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HecarimW", "name": "Geist des Schreckens", "description": "<PERSON><PERSON>im erhält Rüstung und Magieresistenz. <PERSON>carim fügt allen nahen Gegnern magischen Schaden zu und wird für einen Teil jeder Art von Schaden, den diese Gegner erleiden, geh<PERSON><PERSON>.", "tooltip": "<PERSON><PERSON><PERSON> fügt nahen <PERSON>n über {{ buffduration }}&nbsp;Sekunden hinweg <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> zu. <br /><br /><PERSON><PERSON><PERSON> erhält <passive>{{ resistamount }}</passive>&nbsp;<scaleArmor>Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR>. Er heilt sich um <healing>{{ leechamount }}&nbsp;% des Schadens</healing>, den nahe Gegner durch Hecarim erleiden, und <healing>{{ allytooltipleachvalue }}&nbsp;% des Schadens</healing>, den sie durch seine Verbündeten erleiden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzliche Resistenzen", "Maximale Heilung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ resistamount }} -> {{ resistamountNL }}", "{{ minionhealcap }} -> {{ minionhealcapNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "HecarimW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HecarimRamp", "name": "Vernichtender Ansturm", "description": "<PERSON><PERSON><PERSON> erhält erhöhtes Lauftempo und kann sich kurzzeitig durch Einheiten hindurch bewegen. Sein nächster Angriff stößt sein Ziel zurück und verursacht zusätzlichen normalen Schaden abhängig von seiner zurückgelegten Distanz seit der Aktivierung.", "tooltip": "<PERSON><PERSON><PERSON> er<PERSON>lt „Geist“ und <speed>{{ minmovespeed*100 }}&nbsp;% Lauftempo</speed>, das über {{ e5 }}&nbsp;Sekunden auf <speed>{{ maxmovespeed*100 }}&nbsp;%</speed> ansteigt. Bei seinem nächsten Angriff wird das Ziel <status>zurückgestoßen</status> und erleidet zwischen <physicalDamage>{{ mindamage }}</physicalDamage> und <physicalDamage>{{ maxdamage }}&nbsp;normalen Schaden</physicalDamage>. Wie weit das Ziel <status>zurückgestoßen</status> wird und der erlittene Schaden skalieren mit der Distanz, die während dieser Fähigkeit zurückgelegt wird.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mindestschaden", "<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ minbasedamage }} -> {{ minbasedamageNL }}", "{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [150, 150, 150, 150, 150], [350, 350, 350, 350, 350], [60, 90, 120, 150, 180], [30, 45, 60, 75, 90], [4, 4, 4, 4, 4], [0.65, 0.65, 0.65, 0.65, 0.65], [1200, 1200, 1200, 1200, 1200], [0.25, 0.25, 0.25, 0.25, 0.25], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "150", "350", "60/90/120/150/180", "30/45/60/75/90", "4", "0.65", "1200", "0.25", "2.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "HecarimRamp.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HecarimUlt", "name": "Ansturm der Schatten", "description": "<PERSON><PERSON><PERSON> beschw<PERSON>rt schemenhafte Reiter und stürmt voran, wobei er <PERSON>n in einer Linie vor ihm magischen Schaden zufügt. <PERSON><PERSON><PERSON> entfesselt anschließend eine <PERSON>, die dazu führt, dass nahe Gegner ängstlich fliehen.", "tooltip": "<PERSON><PERSON><PERSON> beschwört Spektralreiter und stürmt voran, wodurch er <magicDamage>{{ damagedone }}&nbsp;magischen Schaden</magicDamage> verursacht. Hecarim entfesselt am Ende der Aufladung eine Schockwelle, die Gegner zwischen {{ feardurationmin }} und {{ feardurationmax }}&nbsp;Sekunden lang in <status>Furcht</status> versetzt (erhöht sich mit der zurückgelegten Distanz).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.75, 0.75, 0.75], [1.5, 1.5, 1.5], [1100, 1100, 1100], [1000, 1000, 1000], [950, 950, 950], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.75", "1.5", "1100", "1000", "950", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [50000, 50000, 50000], "rangeBurn": "50000", "image": {"full": "HecarimUlt.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Kriegspfad", "description": "<PERSON><PERSON>im erhält Angriffsschaden in Höhe eines Prozentsatzes seines zusätzlichen Lauftempos.", "image": {"full": "Hecarim_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}