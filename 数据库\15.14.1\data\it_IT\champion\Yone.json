{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Yone", "title": "l'imperituro", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "<PERSON><PERSON> spirituale", "chromas": true}, {"id": "777010", "num": 10, "name": "Yone dell'Accademia di Battaglia", "chromas": true}, {"id": "777019", "num": 19, "name": "Yone Portatore dell'Alba", "chromas": true}, {"id": "777026", "num": 26, "name": "Yone Canto dell'Oceano", "chromas": true}, {"id": "777035", "num": 35, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "777045", "num": 45, "name": "Yone HEARTSTEEL", "chromas": true}, {"id": "777046", "num": 46, "name": "Yone HEARTSTEEL (edizione prestigio)", "chromas": false}, {"id": "777055", "num": 55, "name": "Yone Mezzogiorno di Fuoco", "chromas": true}, {"id": "777058", "num": 58, "name": "Yone Mezzogiorno di Fuoco Pacificatore", "chromas": false}, {"id": "777065", "num": 65, "name": "<PERSON><PERSON> mascherata", "chromas": false}], "lore": "In vita, Yone era il fratellastro di Yasuo e un insigne allievo della scuola di spada del loro villaggio. Do<PERSON> aver trovato la morte per mano di suo fratello, venne braccato da un'entità malvagia nel regno degli spiriti e per salvarsi fu costretto a ucciderla con la sua stessa spada. <PERSON><PERSON>, condannato a indossare la maschera con il volto di quel demone, Yone dà la caccia senza sosta a questi esseri per comprendere ciò che è diventato.", "blurb": "In vita, Yone era il fratellastro di Yasuo e un insigne allievo della scuola di spada del loro villaggio. Do<PERSON> aver trovato la morte per mano di suo fratello, venne braccato da un'entità malvagia nel regno degli spiriti e per salvarsi fu costretto a...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "Furia d'acciaio", "description": "<PERSON><PERSON>ce in avanti, danne<PERSON><PERSON><PERSON> tutti i nemici lungo una linea.<br><br><PERSON> colpo, conferisce una carica di Tempesta incombente per qualche secondo. A 2 cariche, con Furia d'acciaio Yone scatta in avanti con una raffica di vento che lancia i nemici <status>in aria</status>.", "tooltip": "Yone esegue un affondo in avanti, infliggendo <physicalDamage>{{ qdamage }} danni fisici</physicalDamage>.<br /><br />Sul colpo conferisce una carica per {{ buffduration }} secondi. A 2 cariche, l'abilità fa scattare Yone in avanti con una raffica di vento che <status>lancia in aria</status> i nemici per {{ q3knockupduration }} secondi e infligge <physicalDamage>{{ qdamage }} danni fisici</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YoneW", "name": "Fendente Reiatsu", "description": "Col<PERSON>ce in avanti, danne<PERSON><PERSON>do tutti i nemici in un'area a cono. Fornisce a Yone uno scudo il cui valore aumenta in base al numero dei campioni colpiti con la spazzata.<br><br>Il tempo di ricarica e di lancio di Fendente Reiatsu si riducono con la velocità d'attacco.", "tooltip": "Yone colpisce davanti a sé, infliggendo <physicalDamage>{{ basedamage*0.5 }} (+ {{ maxhealthdamage*50 }}% della salute massima del bersaglio) danni fisici</physicalDamage> e <magicDamage>{{ basedamage*0.5 }} (+ {{ maxhealthdamage*50 }}% della salute massima del bersaglio) danni magici</magicDamage>.<br /><br />Se l'abilità va a segno, Yone ottiene uno <shield>scudo da {{ wshield }}</shield> per {{ shieldduration }} secondi. Il valore dello <shield>scudo</shield> aumenta in base al numero di campioni colpiti. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Danni totali salute massima"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YoneE", "name": "Scissione spirituale", "description": "Lo spirito di Yone esce dal suo corpo, guadagnando velocit<PERSON> di movimento. Al termine di questa abilità, lo spirito di Yone torna nel suo corpo e ripete una parte dei danni inflitti in forma di spirito.", "tooltip": "Yone assume una forma spirituale per {{ returntimer }} secondi, abbandonando il suo corpo per tutta la durata e guadagnando da <speed>{{ startingms*100 }}%</speed> a <speed>{{ movementspeed*100 }}%</speed> velocità di movimento crescente. <br /><br />Allo scadere del tempo, Yone torna nel suo corpo e ripete un {{ deathmarkpercent*100 }}% di tutti i danni da attacchi e da abilità che ha inflitto ai campioni in quel periodo di tempo. È possibile <recast>rilanciare</recast> l'abilità in forma spirituale.<br /><br /><recast>Rilancio: </recast>abbandona in anticipo la forma spirituale.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YoneR", "name": "Rashomon", "description": "Yone scatta alle spalle dell'ultimo nemico in una linea sferrando un fendente tanto potente da attirare tutti i nemici colpiti verso di sé.", "tooltip": "Yone colpisce tutti i nemici lungo una linea infliggendo <physicalDamage>{{ tooltipdamage }} danni fisici</physicalDamage> e <magicDamage>{{ tooltipdamage }} danni magici</magicDamage>, scattando alle spalle dell'ultimo campione colpito e <status>lanciando in aria</status> i nemici colpiti attirandoli verso di sé.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Via dello Shinigami", "description": "<PERSON>ne infligge danni magici a ogni secondo attacco. Inoltre, la sua probabilità di colpo critico aumenta.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}