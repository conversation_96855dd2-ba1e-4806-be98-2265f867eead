{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON> Leerengänger", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "Festival-Ka<PERSON><PERSON>", "chromas": false}, {"id": "38002", "num": 2, "name": "Tiefenwesen-Ka<PERSON><PERSON>", "chromas": false}, {"id": "38003", "num": 3, "name": "<PERSON><PERSON><PERSON> vor der Leere", "chromas": false}, {"id": "38004", "num": 4, "name": "Vorboten-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38005", "num": 5, "name": "Kosmischer Sc<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38006", "num": 6, "name": "<PERSON>", "chromas": true}, {"id": "38014", "num": 14, "name": "Hextech-Ka<PERSON>din", "chromas": false}, {"id": "38015", "num": 15, "name": "Sc<PERSON>ckklingen-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38024", "num": 24, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "<PERSON><PERSON><PERSON> schlägt eine brennende Schneise durch die dunkelsten Orte dieser Welt, denn er weiß, dass seine Tage gezählt sind. Al<PERSON> und weit gereister Wüstenführer von Shurima wollte er einst unter den friedlichen Nomadenstämmen von Shurima eine Familie gründen – bis zu jenem <PERSON>, als sein Do<PERSON> von der Leere verschlungen wurde. Er schwor Rache und machte sich daran, verschiedene arkane Artefakte und verbotene Technologien miteinander zu kombinieren, um sich so auf den bevorstehenden Kampf vorzubereiten. <PERSON><PERSON><PERSON><PERSON><PERSON> begann <PERSON><PERSON><PERSON> seine Reise in die Ödlande von Icathia, bereit, sich auf der Suche nach ihrem selbsternannten Propheten Malzahar jeder der Leere entstiegenen Monstrosität zu stellen.", "blurb": "<PERSON><PERSON><PERSON> schlägt eine brennende Schneise durch die dunkelsten Orte dieser Welt, denn er weiß, dass seine Tage gezählt sind. Als Abenteurer und weit gereister Wüstenführer von Shurima wollte er einst unter den friedlichen Nomadenstämmen von Shurima eine...", "allytips": ["<PERSON><PERSON><PERSON> kann mit unterschiedlichen Gegenständen gespielt werden: Mit Mana und Fähigkeitsstärke als Zauberer oder mit verkürzten Abklingzeiten und Magieresistenzen als Anti-Zauberer.", "Ka<PERSON>dins ultimative Fähigkeit ist vielseitig einsetzbar und schneller wieder bereit als andere vergleichbare Fähigkeiten. Gebrauche sie häufig.", "Versuche den Buff des Blauen Wächters zu bekommen, um den steigenden Manakosten von „Leerenwanderer“ entgegenzuwirken."], "enemytips": ["<PERSON><PERSON><PERSON> verursacht größtenteils magischen Schaden. Falls er seine Sache (zu) gut macht, solltest du in Erwägung ziehen, Gegenstände mit Magieresistenz, wie etwa Merkurs Schuhe und Schleier der Todesfee, zu kaufen.", "Wiederholtes Einsetzen von <PERSON>s „Leerenwanderer“ erhöht dessen Manakosten. Vergiss das bei Verfolgungen nicht.", "Zur Ausführung seiner verlangsamenden „Energiewelle“ bedarf es 6 gewirkter Fähigkeiten. Benutze deine Fähigkeiten mit Bedacht, sobald er diese Fähigkeit lernt."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> erzeugt eine Kugel aus Leerenenergie, die Schaden verursacht und Kanalisierungen unterbricht. Die überschüssige Energie formiert sich um ihn und gewährt einen vorübergehenden Schild, der magischen Schaden absorbiert.", "tooltip": "<PERSON><PERSON><PERSON> feuert eine Kugel aus Leerenenergie, die <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und Kanalisierungen unterbricht. Ka<PERSON>din erhält außerdem 1,5&nbsp;Se<PERSON>nden lang einen <shield><PERSON><PERSON><PERSON></shield> in <PERSON><PERSON><PERSON> von {{ totalshield }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schildstärke", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>herBlade", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Passiv: Kassadins normale Angriffe verursachen zusätzlichen magischen Schaden. Aktiv: Ka<PERSON>dins nächster normaler Angriff verursacht erheblichen zusätzlichen magischen Schaden und stellt Mana wieder her.", "tooltip": "<spellPassive>Passiv:</spellPassive> Ka<PERSON>dins Angriffe verursachen zusätzlich <magicDamage>{{ onhitdamage }}&nbsp;magischen <PERSON>had<PERSON></magicDamage>.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON><PERSON><PERSON> lädt seine Klinge auf, wodurch sein nächster Angriff <magicDamage>{{ activedamage }}&nbsp;magischen Schaden</magicDamage> verursacht und <scaleMana>{{ e1 }}&nbsp;% seines fehlenden Manas</scaleMana> wiederherstellt (gegen Champions auf <scaleMana>{{ e4 }}&nbsp;%</scaleMana> erhöht).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden (Aktiv)", "Grundwert für Manawiederherstellung", "Champion-Manawiederherstellung"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }}&nbsp;% -> {{ e1NL }}&nbsp;%", "{{ e4 }}&nbsp;% -> {{ e4NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ForcePulse", "name": "Energiewelle", "description": "<PERSON><PERSON><PERSON> zieht Energie von Zaubern in seiner näheren Umgebung ab. Nachdem er sich aufgeladen hat, kann er mit seiner kegelförmigen Energiewelle Schaden an Gegnern vor ihm verursachen.", "tooltip": "<spellPassive>Passiv:</spellPassive> Die Abklingzeit von <spellName>Energiewelle</spellName> wird um {{ reductionperspellcast }}&nbsp;Sekunde(n) reduziert, wenn in der Nähe von Kassadin eine beliebige Fähigkeit eingesetzt wird.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON><PERSON><PERSON> entfesselt einen Leerenpuls, der <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und Gegner {{ e3 }}&nbsp;Sekunde(n) lang um {{ e2 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RiftWalk", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> teleportiert sich an einen Ort in der Nähe und verursacht bei gegnerischen Einheiten im Umkreis Schaden. Die mehrmalige Anwendung von „Leerenwanderer“ innerhalb kurzer Zeit kostet zusätzliches Mana, verursacht dafür aber zusätzlichen Schaden.", "tooltip": "<PERSON><PERSON><PERSON> teleportiert sich an einen Ort in der Nähe und verursacht <magicDamage>{{ basedamage }}&nbsp;magischen Schaden</magicDamage>.<br /><br />Jeder nachfolgende Einsatz dieser Fähigkeit innerhalb der nächsten {{ rstackduration }}&nbsp;Sekunden verdoppelt die Manakosten und verursacht zusätzlich <magicDamage>{{ bonusdamage }}&nbsp;magischen Schaden</magicDamage>. Die zusätzlichen Kosten und der zusätzliche Schaden können bis zu {{ maxstacks }}-mal gesteigert werden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden pro Steigerung", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ stackdamage }} -> {{ stackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> erleidet weniger magischen Schaden und kann sich durch Einheiten hindurchbewegen.", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}