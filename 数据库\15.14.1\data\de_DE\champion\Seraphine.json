{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Seraphine": {"id": "<PERSON><PERSON><PERSON>", "key": "147", "name": "<PERSON><PERSON><PERSON>", "title": "das verträumte Goldkehlchen", "image": {"full": "Seraphine.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "147000", "num": 0, "name": "default", "chromas": false}, {"id": "147001", "num": 1, "name": "K/DA ALL OUT-<PERSON><PERSON>ine (Indie-Künstlerin)", "chromas": false}, {"id": "147002", "num": 2, "name": "K/DA ALL OUT-<PERSON><PERSON><PERSON> (Aufgehender Stern)", "chromas": false}, {"id": "147003", "num": 3, "name": "K/DA ALL OUT-<PERSON><PERSON><PERSON> (Superstar)", "chromas": false}, {"id": "147004", "num": 4, "name": "An<PERSON><PERSON><PERSON>nix-Ser<PERSON>ine", "chromas": true}, {"id": "147014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "147015", "num": 15, "name": "Meeresrauschen-<PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "147024", "num": 24, "name": "Feenhof-Seraphine", "chromas": true}, {"id": "147034", "num": 34, "name": "Sternenwächteri<PERSON>", "chromas": true}, {"id": "147043", "num": 43, "name": "Battle Dove Seraphine", "chromas": true}, {"id": "147050", "num": 50, "name": "Teigtaschenschatz-<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> wurde in Piltover als Kind zhaunitischer Eltern geboren und kann die Seelen anderer hören – die Welt singt zu ihr und sie singt zurück. Auch wenn diese Klänge sie in ihrer Jugend überwältigt haben, sieht sie sie nun als Inspiration und verwandelt das Chaos in eine Symphonie. Sie tritt für die Schwesterstädte auf, um deren Bürger daran zu erinnern, dass sie nicht allein sind, dass sie gemeinsam stärker sind und dass ihr Potential in Ser<PERSON>ines Augen grenzenlos ist.", "blurb": "<PERSON><PERSON><PERSON> wurde in Piltover als Kind zhaunitischer Eltern geboren und kann die Seelen anderer hören – die Welt singt zu ihr und sie singt zurück. Auch wenn diese Klänge sie in ihrer Jugend überwältigt haben, sieht sie sie nun als Inspiration und...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 25, "movespeed": 330, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.669}, "spells": [{"id": "SeraphineQ", "name": "Absolutes Gehör", "description": "<PERSON><PERSON><PERSON> ve<PERSON> Flächenschaden.", "tooltip": "<PERSON><PERSON><PERSON> singt eine perfekte Note und verursacht <magicDamage>{{ explosiondamage }}&nbsp;magischen <PERSON>haden</magicDamage>, der sich gegen Champions mit dem prozentual fehlenden Leben des Ziels auf bis zu <magicDamage>{{ totalempowereddamage }}&nbsp;Schaden</magicDamage> bei unter {{ executethreshold*100 }}&nbsp;% Leben erhöht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "SeraphineQ.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SeraphineW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> gewährt nahen Verbündeten Schilde und Lauftempo. Wenn sie bereits von einem Schild geschützt ist, heilt sie nahe Verbündete zusätzlich.", "tooltip": "Ser<PERSON>ine unterstützt nahe verbündete Champions mit einem Lied, wodurch diese {{ shieldduration }}&nbsp;Sekunden lang <speed>{{ hastevalueallies }}&nbsp;Lauftempo</speed>, sie selbst <speed>{{ wmsbonustotal }}&nbsp;abfallendes Lauftempo</speed> und alle gemeinsam einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ shieldvalueseraphine }} erhalten.<br /><br />Wenn Seraphine bereits von einem <shield>Schild</shield> geschützt ist, ruft sie ihren Verbündeten zu, sich ihr anzuschließen und es wird nach einer Verzögerung von {{ whealsplitdelay }}&nbsp;Sekunde(n) pro nahen verbündeten Champion bei ihnen <healing>{{ wmissinghpheal }}&nbsp;% des fehlenden Lebens</healing> wiederhergestellt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Prozentuale Heilung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ wmissinghpbase }} -> {{ wmissinghpbaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 22, 22, 22, 22], "cooldownBurn": "22", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SeraphineW.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SeraphineE", "name": "Rhythmuswechsel", "description": "<PERSON><PERSON><PERSON> ve<PERSON> Schaden und beeinträchtigt die Bewegung von <PERSON> in einer Reihe.", "tooltip": "<PERSON><PERSON><PERSON> entfesselt eine schwere <PERSON>hallwelle, die <magicDamage>{{ finaldamage }}&nbsp;magischen <PERSON>en</magicDamage> an Gegnern in einer Reihe verursacht und sie {{ slowduration }}&nbsp;Sekunde(n) lang um {{ slowvalue }}&nbsp;% <status>verlangsamt</status>.<br /><br />Bereits <status>verlangsamte</status> Gegner werden stattdessen <status>festgehalten</status> und <status>bewegungsunfähige</status> Gegner werden <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uer", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "SeraphineE.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SeraphineR", "name": "Zugabe", "description": "<PERSON><PERSON><PERSON> fügt getroffenen Gegnern Schaden zu und bezaubert sie. Die Reichweite wird mit jedem getroffenen verbündeten oder gegnerischen Champion verlängert.", "tooltip": "Ser<PERSON>ine betritt die Bühne und entfesselt eine unwiderstehliche Anziehungskraft, die {{ rchannelduration }}&nbsp;Sekunden lang <status>bezaubert</status> und <magicDamage>{{ r1totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.<br /><br />Jeder getroffene Champion (einschließlich Verbündete) wird Teil des Auftritts und verlängert die Reichweite dieser Fähigkeit. Verbündete Champions erhalten maximal <keywordMajor>Noten</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "<PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ r1basedamage }} -> {{ r1basedamageNL }}", "{{ rchannelduration }} -> {{ rchanneldurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SeraphineR.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Bühnenpräsenz", "description": "Jede dritte Grundfähigkeit wird von <PERSON><PERSON> ein z<PERSON>tes Mal ausgeführt. Sie erhält außerdem für ihren nächsten Angriff zusätzlichen magischen Schaden und Reichweite, wenn sie Fähigkeiten in der Nähe von Verbündeten ausführt.", "image": {"full": "Seraphine_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}