{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Poppy": {"id": "<PERSON><PERSON>", "key": "78", "name": "ポッピー", "title": "大鎚の守護者", "image": {"full": "Poppy.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "78000", "num": 0, "name": "default", "chromas": false}, {"id": "78001", "num": 1, "name": "ノクサス ポッピー", "chromas": false}, {"id": "78002", "num": 2, "name": "ロリポッピー", "chromas": false}, {"id": "78003", "num": 3, "name": "鍛冶屋ポッピー", "chromas": false}, {"id": "78004", "num": 4, "name": "お人形ポッピー", "chromas": false}, {"id": "78005", "num": 5, "name": "王騎士ポッピー", "chromas": true}, {"id": "78006", "num": 6, "name": "緋色の槌兵ポッピー", "chromas": false}, {"id": "78007", "num": 7, "name": "スターガーディアン ポッピー", "chromas": false}, {"id": "78014", "num": 14, "name": "雪の仔鹿ポッピー", "chromas": false}, {"id": "78015", "num": 15, "name": "ヘクステック ポッピー", "chromas": false}, {"id": "78016", "num": 16, "name": "宇宙飛行士ポッピー", "chromas": true}, {"id": "78024", "num": 24, "name": "魅惑の魔女ポッピー", "chromas": true}, {"id": "78033", "num": 33, "name": "カフェキューティーズ ポッピー", "chromas": true}], "lore": "ルーンテラの地に勇敢なチャンピオンは数多いものの、ポッピーほど粘り強い者はそうはいない。自分の身長の二倍ほどもある伝説のハンマー、オーロンを携えた不屈のヨードルは、もう何年もの間、彼女のハンマーの「真の持ち主」であるといわれている伝説の戦士「デマーシアの勇者」を密かに探し続けているのだ。真の持ち主が見つかるまで、彼女は使命感を持って戦闘に挑み、ハンマーを振り回して王国の敵を押し返している。", "blurb": "ルーンテラの地に勇敢なチャンピオンは数多いものの、ポッピーほど粘り強い者はそうはいない。自分の身長の二倍ほどもある伝説のハンマー、オーロンを携えた不屈のヨードルは、もう何年もの間、彼女のハンマーの「真の持ち主」であるといわれている伝説の戦士「デマーシアの勇者」を密かに探し続けているのだ。真の持ち主が見つかるまで、彼女は使命感を持って戦闘に挑み、ハンマーを振り回して王国の敵を押し返している。", "allytips": ["「鋼鉄の大使」は壁の近くに落ちることが多い。「ヒロイックチャージ」でこの特性を活かそう。", "「守護者の鉄鎚」は直ぐに発動して敵に放つことができるので、対戦でうまく利用しよう。"], "enemytips": ["「ステッドファスト」で近接する敵のダッシュ行動を妨害できる。", "ハンマーを振り回し始めると「守護者の鉄鎚」がチャージされているので気をつけよう。", "シールドを踏むことで、回収を阻止できるぞ。"], "tags": ["Tank", "Fighter"], "partype": "マナ", "info": {"attack": 6, "defense": 7, "magic": 2, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 110, "mp": 280, "mpperlevel": 40, "movespeed": 345, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "PoppyQ", "name": "ハンマーショック", "description": "ハンマーを振り下ろしてダメージを与え、敵をスロウ状態にした後時間を置いて爆発する効果範囲を作り出す。", "tooltip": "ハンマーを打ち下ろして<physicalDamage>{{ basedamage }}</physicalDamage><physicalDamage>(+最大体力の{{ healthdamagepercent }}%)の物理ダメージ</physicalDamage>を与え、足場が不安定なエリアを作り出す。<br /><br />不安定なエリアは敵に{{ e3 }}%の<status>スロウ効果</status>を与える。このエリアは{{ e4 }}秒後に爆発して、再度<physicalDamage>{{ basedamage }}</physicalDamage><physicalDamage>(+最大体力の{{ healthdamagepercent }}%)の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン", "@AbilityResourceName@コスト", "モンスターへのダメージ上限"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e6 }} -> {{ e6NL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [30, 55, 80, 105, 130], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 25, 30, 35, 40], [1, 1, 1, 1, 1], [9, 9, 9, 9, 9], [75, 105, 135, 165, 195], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/55/80/105/130", "0.5", "20/25/30/35/40", "1", "9", "75/105/135/165/195", "100", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "PoppyQ.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PoppyW", "name": "ステッドファスト", "description": "自動効果で物理防御と魔法防御が増加する。このボーナスは体力が低下するとさらに増加する。発動すると移動速度が増加し、自身の周囲にいる敵のダッシュ行動を阻止する。ダッシュを中断させられた敵はスロウ状態および釘付け状態になる。", "tooltip": "<spellPassive>自動効果:</spellPassive> <scaleArmor>物理防御が{{ bonusarmor }}</scaleArmor>、<scaleMR>魔法防御が{{ bonusmr }}</scaleMR>増加する。自身の体力が{{ passiveempoweredhealthpercent*100 }}%を下回ると、この増加量は2倍になる。<br /><br /><spellActive>発動効果:</spellActive> {{ e1 }}秒間、<speed>移動速度が{{ e2 }}%</speed>増加して、周囲に敵のダッシュを停止させるフィールドを発生させる。このフィールドで停止した敵には{{ groundingduration }}秒間{{ slowamount*-100 }}%の<status>釘付け効果</status>と<status>スロウ効果</status>を付与し、<magicDamage>{{ interruptdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [40, 40, 40, 40, 40], [10, 10, 10, 10, 10], [0.5, 0.5, 0.5, 0.5, 0.5], [70, 110, 150, 190, 230], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "40", "10", "0.5", "70/110/150/190/230", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PoppyW.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PoppyE", "name": "ヒロイックチャージ", "description": "対象に向かってダッシュし、突き飛ばす。対象が壁にぶつかった場合、スタン状態になる。", "tooltip": "敵に向かって突撃し、<physicalDamage>{{ tackledamage }}の物理ダメージ</physicalDamage>を与えて突き飛ばす。地形にぶつけた場合、敵を{{ e3 }}秒間<status>スタン</status>させて、<physicalDamage>{{ tackledamage }}の物理ダメージ</physicalDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "壁でのダメージ", "スタン効果時間:", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [40, 60, 80, 100, 120], [40, 60, 80, 100, 120], [1.6, 1.7, 1.8, 1.9, 2], [1800, 1800, 1800, 1800, 1800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/60/80/100/120", "40/60/80/100/120", "1.6/1.7/1.8/1.9/2", "1800", "400", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "PoppyE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PoppyR", "name": "守護者の鉄鎚", "description": "ハンマーに力を溜め、敵を遥か彼方に殴り飛ばす。", "tooltip": "<charge>詠唱開始:</charge> 最大{{ channelmaxduration }}秒間ハンマーに力をチャージし、自身が{{ selfslow }}%の<status>スロウ効果</status>を受ける。<br /><br /><release>解放:</release> ハンマーを地面に叩きつけて衝撃波を発生させ、最初に命中したチャンピオンとその周囲の敵に<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与え、敵のネクサスの方向に<status>ノック</status><status>バック</status>させる。敵は飛ばされている間、対象指定不可になる。<status>ノックバック</status>させる距離はチャージ時間に応じて増加する。<br /><br />チャージせずに攻撃した場合は、<physicalDamage>{{ halfdamage }}の物理ダメージ</physicalDamage>を与えて、{{ knockupdurationsnap }}秒間<status>ノックアップ</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "PoppyR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "鋼鉄の大使", "description": "対象にバックラーを投げつける。対象に命中し跳ね返って落ちたバックラーを拾うことで、一時的にシールドを得る。", "image": {"full": "Poppy_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}