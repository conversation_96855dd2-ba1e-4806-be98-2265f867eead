{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "<PERSON><PERSON><PERSON>", "title": "la mummia triste", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32005", "num": 5, "name": "<PERSON><PERSON><PERSON>-<PERSON> del Ballo", "chromas": false}, {"id": "32006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32008", "num": 8, "name": "Amumu Festa a Sorpresa", "chromas": true}, {"id": "32017", "num": 17, "name": "Amumu Infernale", "chromas": true}, {"id": "32023", "num": 23, "name": "Amumu Hextech", "chromas": false}, {"id": "32024", "num": 24, "name": "Amumu Principe delle Zucche", "chromas": true}, {"id": "32034", "num": 34, "name": "Amumu di Porcellana", "chromas": true}, {"id": "32044", "num": 44, "name": "<PERSON><PERSON><PERSON> s<PERSON>", "chromas": true}, {"id": "32053", "num": 53, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "La leggenda racconta che Amumu, un'anima solitaria e malinconica originaria dell'antica Shurima, vaghi per il mondo alla ricerca di un amico. Vittima di un'antica maledizione, è condannato a vivere il resto della sua vita in solitudine. Il suo contatto porta morte e il suo affetto distruzione. Coloro i quali sostengono di averlo visto, descrivono Amumu come un cadavere vivente, piccolo e avvolto da bende. Amumu ha ispirato storie, canzoni e miti raccontati e tramandati di generazione in generazione, tanto da non riuscire più a separare ciò che è reale da ciò che non lo è.", "blurb": "La leggenda racconta che Amumu, un'anima solitaria e malinconica originaria dell'antica Shurima, vaghi per il mondo alla ricerca di un amico. Vittima di un'antica maledizione, è condannato a vivere il resto della sua vita in solitudine. Il suo contatto...", "allytips": ["Amumu dipende molto dai suoi compagni di squadra: prova a stare in corsia con i tuoi amici per avere la massima efficacia.", "La riduzione ricarica è molto forte su Amumu, ma è difficile da acquisire con i soli oggetti. Prendi il buff del Guardiano di quarzo quando ti è possibile per guadagnare la riduzione ricarica senza sacrificare altre statistiche.", "Disperazione è molto forte contro gli altri tank, quindi cerca sempre di trovarti nel raggio degli avversari con la salute al massimo."], "enemytips": ["Cerca di non stare troppo vicino ai tuoi alleati quando Amumu ha la sua abilità suprema pronta.", "Per Amumu è più difficile iniziare un combattimento con Lancio della bendatura se ti muovi in maniera irregolare o ti nascondi dietro le ondate di minion.", "Disperazione di Amumu rende rischioso comprare solo oggetti che danno salute."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "<PERSON><PERSON><PERSON> bendatura", "description": "Amumu lancia una benda appiccicosa a un bersaglio, stordendolo e dannegg<PERSON><PERSON>lo mentre si avvicina a esso.", "tooltip": "Amumu lancia una benda, tirandosi verso il primo nemico colpito, <status>storden<PERSON><PERSON></status> per {{ e2 }} secondo e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />Questa abilità ha 2 cariche.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Tempo di ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "Disperazione", "description": "Sopra<PERSON><PERSON><PERSON> dall'angos<PERSON>, i nemici nelle vicinanze perdono una percentuale della loro salute massima ogni secondo e vedono ripristinate le loro <font color='#9b0f5f'>maledizioni</font>.", "tooltip": "<toggle>Attiva/disattiva:</toggle> Amumu inizia a piangere, infliggendo <magicDamage>{{ basedamage }} più un {{ totalhealthdamage }}% della sua salute massima in danni magici</magicDamage> ai nemici vicini al secondo e ripristinando <keywordMajor>Maledizione</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni % salute"], "effect": ["{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }} al secondo", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }} al secondo"}, {"id": "Tantrum", "name": "Collera", "description": "Riduce permanentemente i danni fisici subiti da Amumu. <PERSON>umu può scatenare la sua rabbia infliggendo danni a tutti i nemici circostanti. Ogni volta che Amumu viene colpito, il tempo di ricarica di Collera si riduce.", "tooltip": "<spellPassive>Passiva:</spellPassive> Amumu subisce {{ damagereduction }} danni fisici in meno. In<PERSON><PERSON>, quando Amumu viene colpito da un attacco, la ricarica di quest'abilità si riduce di {{ e3 }} secondi.<br /><br /><spellActive>Attiva:</spellActive> Amumu si infuria, infliggendo <magicDamage>{{ tantrumdamage }} danni magici</magicDamage> ai nemici vicini.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "<PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "Maledizione della mummia triste", "description": "Amumu avvolge i nemici nelle vicinanze con le sue bende, applicando la sua <keywordMajor>maledi<PERSON></keywordMajor>, danne<PERSON><PERSON><PERSON><PERSON> e stordendoli.", "tooltip": "Amumu scaglia le sue bende contro i nemici, <status>stordendoli</status> per {{ rduration }} secondi, infliggendo <magicDamage>{{ rcalculateddamage }} danni magici</magicDamage> e applicando <keywordMajor>Maledizione</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamage }} -> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Gli attacchi base di Amumu <font color='#9b0f5f'>maledicono</font> i suoi nemici per 3 secondi, facendo subire loro danni puri bonus da tutti i danni magici in arrivo.", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}