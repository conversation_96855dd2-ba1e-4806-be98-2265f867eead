{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Olaf": {"id": "<PERSON>", "key": "2", "name": "<PERSON>", "title": "<PERSON> Berserker", "image": {"full": "Olaf.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "2000", "num": 0, "name": "default", "chromas": false}, {"id": "2001", "num": 1, "name": "Verlassener Olaf", "chromas": false}, {"id": "2002", "num": 2, "name": "<PERSON><PERSON><PERSON> Olaf", "chromas": false}, {"id": "2003", "num": 3, "name": "Brolaf", "chromas": true}, {"id": "2004", "num": 4, "name": "Pentakill-Olaf", "chromas": false}, {"id": "2005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "2006", "num": 6, "name": "Metzger-<PERSON>", "chromas": false}, {"id": "2015", "num": 15, "name": "SKT T1-Olaf", "chromas": false}, {"id": "2016", "num": 16, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "2025", "num": 25, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "2035", "num": 35, "name": "Pentakill III: Lost Chapter-<PERSON>", "chromas": true}, {"id": "2044", "num": 44, "name": "Infernalischer Olaf", "chromas": true}], "lore": "<PERSON> ist mit seiner Axt eine unaufhaltsame Kraft der Zerstörung und möchte nichts lieber, als in einem glorreichen Kampf zu sterben. <PERSON><PERSON>, der von der rauen freljorder Halbinsel Lokfar stammt, wurde einst ein friedvoller Tod prophezeit, was seiner Auffassung nach das Schicksal eines Feiglings und in seiner Kultur eine Beleidigung darstellt. Auf der Suche nach dem Tod und von der Wut getrieben, tobte er über das Land und tötete viele große Krieger und legendäre Bestien, um einen Gegner zu finden, der ihn aufhalten könnte. Heute ist er ein brutaler Vollstrecker für die Winterklauen und sucht sein Ende in den großen Kriegen, die bevorstehen.", "blurb": "<PERSON> ist mit seiner Axt eine unaufhaltsame Kraft der Zerstörung und möchte nichts lieber, als in einem glorreichen Kampf zu sterben. <PERSON><PERSON>, der von der rauen freljorder Halbinsel Lokfar stammt, wurde einst ein friedvoller Tod prophezeit, was seiner...", "allytips": ["<PERSON>i niedrigem Le<PERSON> kann Olaf „Berserk<PERSON>-Wut<PERSON>, „Boshafte Schläge“ und „Ragnarök“ kombinieren, um trügerisch stark zu erscheinen.", "Die durch „Boshafte Schläge“ gewährte zusätzliche Heilung verstärkt sowohl deinen Lebensraub aus allen Quellen als auch Heilungen von Verbündeten."], "enemytips": ["<PERSON> wird mit dem Verlust von Leben gefährlicher. Mach ihn erst am Ende des Kampfes kampfunfähig, um ihm den Rest zu geben.", "<PERSON><PERSON> <PERSON> in der Anfangsphase des Spiels davon a<PERSON><PERSON><PERSON><PERSON>, seine A<PERSON>t aufzuheben, minimiert dies seinen verursachten Schaden.", "<PERSON><PERSON><PERSON> seiner Immunität gegenüber kampfunfähig machenden Fähigkeiten ist Olafs Verteidigung durch den Schaden während „Ragnarök“ verringert. Falls du Olaf während „Ragnarök“ nicht entkommen kannst, versuche, deinen und den Schaden deiner Teamkollegen auf ihn zu konzentrieren."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 119, "mp": 316, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.7, "attackspeedperlevel": 2.7, "attackspeed": 0.694}, "spells": [{"id": "OlafAxeThrowCast", "name": "Sog", "description": "<PERSON> s<PERSON>rt eine Axt in den Boden eines Zielbereichs, die getroffenen Gegnern Schaden zufügt und ihre Rüstung und ihr Lauftempo verringert. Wen<PERSON> <PERSON> die Axt aufhebt, wird die Abklingzeit der Fähigkeit zurückgesetzt.", "tooltip": "<PERSON> wirft eine Axt, die <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und das Ziel bis zu {{ e3 }}&nbsp;Sekunden lang (abhä<PERSON><PERSON> von der zurückgelegten Entfernung) um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>. Getroffene Champions verlieren {{ debuffduration }}&nbsp;Sekunden lang <scaleArmor>{{ shredamount*100 }}&nbsp;% Rüstung</scaleArmor>.<br /><br /><PERSON><PERSON> <PERSON> die Axt aufhebt, wird die Abklingzeit der Fähigkeit auf {{ tooltipcdrefund }}&nbsp;Sekunden verringert oder vollständig zurückerstattet, wenn {{ tooltipcdrefund }}&nbsp;Sekunden verstrichen sind.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "<PERSON><PERSON><PERSON> an <PERSON>", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ monsterdamage }} -> {{ monsterdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [30, 30, 30, 30, 30], [2.5, 2.5, 2.5, 2.5, 2.5], [1.5, 1.5, 1.5, 1.5, 1.5], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "30", "2.5", "1.5", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "OlafAxeThrowCast.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OlafFrenziedStrikes", "name": "<PERSON><PERSON><PERSON><PERSON> zusammenbeißen", "description": "<PERSON><PERSON> Angriffstempo ist erhöht und er erhält einen Schild.", "tooltip": "<PERSON> erhält {{ duration }}&nbsp;Sekunden lang <attackSpeed>{{ attackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed> und {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> {{ baseshield }} plus {{ shieldpercmissinghp*100 }}&nbsp;% des fehlenden Lebens (bis zu einem Maximum von {{ maxshieldcalc }} bei weniger als {{ thresholdformax*100 }}&nbsp;% Leben).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Grundwert für den Schild", "Abklingzeit"], "effect": ["{{ attackspeed*100.000000 }}&nbsp;% -> {{ attackspeednl*100.000000 }}&nbsp;%", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "OlafFrenziedStrikes.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OlafRecklessStrike", "name": "Rücksichtslose<PERSON>", "description": "<PERSON> greift mit solcher Gewalt an, dass seine Angriffe an seinem Ziel und an ihm selbst absoluten Schaden verursachen. Er erhält das dafür aufgewendete Leben zurück, falls er sein Ziel zerstört.", "tooltip": "<PERSON> schwingt seine Äxte mit gewaltiger Kraft und verursacht <trueDamage>{{ totaldamage }}&nbsp;absoluten Schaden</trueDamage>. <PERSON><PERSON> der Geg<PERSON> stirbt, werden die Kosten zurückerstattet.<br /><br />Angriffe verringern die Abklingzeit dieser Fähigkeit um 1&nbsp;Sekunde (erhöht auf 2&nbsp;<PERSON><PERSON><PERSON>, wenn Olaf Monster angreift).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "30", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Kostet &nbsp;Leben", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "OlafRecklessStrike.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Kostet &nbsp;Leben"}, {"id": "Olaf<PERSON><PERSON><PERSON><PERSON>", "name": "Ragnarök", "description": "Olaf erhält passiv erhöhte Rüstung und Magieresistenz. Er kann diese Fähigkeit aktivieren, um gegen kampfunfähig machende Effekte immun zu werden, solange er angreift.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON> er<PERSON> <scaleArmor>{{ resists }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ resists }}&nbsp;Magieresistenz</scaleMR>.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON> entfernt alle <status>bewegungsunfähig</status> und <status>kampfunfähig</status> machenden Effekte, die auf ihn wirken und wird {{ duration }}&nbsp;Sekunden lang immun gegen sie. Während die Fähigkeit aktiv ist, erhält Olaf <scaleAD>{{ ad }}&nbsp;Angriffsschaden</scaleAD>. Trifft er einen Champion mit einem Angriff oder mit <spellName>Rücksichtsloser Schwung</spellName>, verlängert sich die Dauer um {{ durationextension }}&nbsp;Sekunden.<br /><br /><PERSON> erh<PERSON>lt außerdem {{ hasteduration }}&nbsp;Seku<PERSON>(n) lang <speed>{{ haste*100 }}&nbsp;% Lauftempo</speed>, wenn er sich auf gegnerische Champions zubewegt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rüstung/Magieresistenz", "Angriffsschaden", "Lauftempo", "Abklingzeit"], "effect": ["{{ resists }} -> {{ resistsNL }}", "{{ flatad }} -> {{ flatadNL }}", "{{ haste*100.000000 }} -> {{ hastenl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "OlafRagnarok.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> erhält abhä<PERSON><PERSON> von seinem fehlenden Leben Angriffstempo und Lebensraub.", "image": {"full": "Olaf_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}