{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nocturne": {"id": "Nocturne", "key": "56", "name": "ノクターン", "title": "終わりなき悪夢", "image": {"full": "Nocturne.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "56000", "num": 0, "name": "default", "chromas": false}, {"id": "56001", "num": 1, "name": "凍てつく恐怖ノクターン", "chromas": false}, {"id": "56002", "num": 2, "name": "ヴォイド ノクターン", "chromas": false}, {"id": "56003", "num": 3, "name": "破壊の化身ノクターン", "chromas": false}, {"id": "56004", "num": 4, "name": "怨霊ノクターン", "chromas": false}, {"id": "56005", "num": 5, "name": "無限の闇ノクターン", "chromas": false}, {"id": "56006", "num": 6, "name": "呪われし亡霊ノクターン", "chromas": false}, {"id": "56007", "num": 7, "name": "古の神ノクターン", "chromas": true}, {"id": "56016", "num": 16, "name": "ヘクステック ノクターン", "chromas": false}, {"id": "56017", "num": 17, "name": "破られし盟約ノクターン", "chromas": true}, {"id": "56026", "num": 26, "name": "荘厳の天球ノクターン", "chromas": true}], "lore": "知覚を持つあらゆる生物が見る悪夢が融合してできたノクターンとして知られるこの恐ろしい存在は、純悪の根源的な力である。混沌として明確な姿は持たず、顔の無い影の中に冷たい目が浮かんでおり、複数の不気味な刃を持っている。霊的領域から脱したノクターンは、目覚め始めた世界に降り立ち、真の闇にしか存在しない恐怖を糧にして生きている。", "blurb": "知覚を持つあらゆる生物が見る悪夢が融合してできたノクターンとして知られるこの恐ろしい存在は、純悪の根源的な力である。混沌として明確な姿は持たず、顔の無い影の中に冷たい目が浮かんでおり、複数の不気味な刃を持っている。霊的領域から脱したノクターンは、目覚め始めた世界に降り立ち、真の闇にしか存在しない恐怖を糧にして生きている。", "allytips": ["「パラノイア」は「闇の手」が使用できず、加速出来ない時の危機回避用スキルとして利用することもできる。", "「闇の手」は攻撃に限らず、敵との間合いを一気に詰めたり、状況によっては死を回避する切り札にもなり得る。", "「漆黒の帳」と「パラノイア」のダッシュ攻撃のコンビネーションは極めて強力。スペルシールドで防御し、敵が動揺した隙に追い撃ちをかけよう。"], "enemytips": ["「パラノイア」が発動されたら、味方チャンピオンのそばを離れないこと。数の多さが突撃してくるノクターンへの抑止力となる。", "ノクターンの「底知れぬ恐怖」は、ノクターンから離れて効果範囲を抜けることで解除できるので、移動スキルを使って振り切ろう。"], "tags": ["Fighter", "Assassin"], "partype": "マナ", "info": {"attack": 9, "defense": 5, "magic": 2, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 109, "mp": 275, "mpperlevel": 35, "movespeed": 345, "armor": 38, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2.6, "attackspeedperlevel": 2.7, "attackspeed": 0.721}, "spells": [{"id": "Nocturn<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "闇の手", "description": "指定方向に影の刃を投げてダメージを与える。刃の軌跡や命中した敵チャンピオンが残す影の上にいる間はユニットをすり抜けられ、移動速度と攻撃力が増加する。", "tooltip": "影の刃を投げて<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与え、刃の軌跡に{{ e3 }}秒間影を残す。命中した敵チャンピオンの後にも影が残る。 <br /><br />自身は影の上にいる間はゴースト化し、<speed>移動速度が{{ movespeed }}%</speed>、<physicalDamage>攻撃力が{{ bonustrailad }}</physicalDamage>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "移動速度", "増加攻撃力", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed }}% -> {{ movespeedNL }}%", "{{ bonustrailad }} -> {{ bonustrailadNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [15, 20, 25, 30, 35], [65, 110, 155, 200, 245], [5, 5, 5, 5, 5], [20, 30, 40, 50, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "15/20/25/30/35", "65/110/155/200/245", "5", "20/30/40/50/60", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1125, 1125, 1125, 1125, 1125], "rangeBurn": "1125", "image": {"full": "NocturneDuskbringer.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NocturneShroudofDarkness", "name": "漆黒の帳", "description": "自動効果: 刃に念が送られ、攻撃速度が増加する。<br><br>発動効果: 魔法のバリアを張って敵のスキルを一度だけ無効化する。無効化成功後は自動効果による攻撃速度の増加率が倍になる。", "tooltip": "<spellPassive>自動効果:</spellPassive> <attackSpeed>攻撃速度が{{ e1 }}%</attackSpeed>増加する。<br /><br /><spellActive>発動効果:</spellActive> 1.5秒間、次の敵のスキルを無効化する影のバリアを展開する。無効化に成功すると{{ e4 }}秒間、このスキルの自動効果が<attackSpeed>{{ e1 }}%の攻撃速度</attackSpeed>に増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加攻撃速度", "クールダウン"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [60, 70, 80, 90, 100], [0.3, 0.05, 0.05, 0.05, 0.05], [1.5, 1.5, 1.5, 1.5, 1.5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/70/80/90/100", "0.3/0.05/0.05/0.05/0.05", "1.5", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "NocturneShroudofDarkness.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NocturneUnspeakableHorror", "name": "底知れぬ恐怖", "description": "対象におぞましい悪夢を見せ、毎秒ダメージを与える。効果終了までに範囲内から抜け出せなかった対象にはフィアー効果を与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> <status>フィアー効果</status>を受けた敵に向かう際に<speed>移動速度が{{ tooltipfearms*100 }}%</speed>増加する。<br /><br /><spellActive>発動効果:</spellActive> 自身を対象と繋ぐことで悪夢を見せ、{{ e3 }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。繋がりが最後まで解けなかった場合は、対象に{{ e2 }}秒間<status>フィアー効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "フィアー効果時間", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [1.25, 1.5, 1.75, 2, 2.25], [2, 2, 2, 2, 2], [465, 465, 465, 465, 465], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "1.25/1.5/1.75/2/2.25", "2", "465", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "NocturneUnspeakableHorror.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NocturneParanoia", "name": "パラノイア", "description": "すべての敵チャンピオンの視界が悪化し、自分以外の視界を失う。効果中、指定した近くの敵チャンピオンに突撃して攻撃できる。", "tooltip": "{{ paranoiaduration }}秒間マップを暗くして、すべての敵チャンピオンの視界の範囲を狭め、自分以外の視界を失わせる。効果時間中にこのスキルを<recast>再発動</recast>すると、敵チャンピオンに突撃して<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "射程", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 3250, 4000], "rangeBurn": "2500/3250/4000", "image": {"full": "NocturneParanoia.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "夢幻斬", "description": "数秒ごとに次の攻撃が範囲攻撃になり、周囲の敵に追加物理ダメージを与えて自身の体力を回復する。<br><br>通常攻撃するたびにクールダウンが短縮する。", "image": {"full": "Nocturne_UmbraBlades.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}