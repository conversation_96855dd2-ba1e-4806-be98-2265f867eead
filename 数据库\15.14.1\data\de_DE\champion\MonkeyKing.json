{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MonkeyKing": {"id": "MonkeyKing", "key": "62", "name": "Wukong", "title": "der Affenkönig", "image": {"full": "MonkeyKing.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "62000", "num": 0, "name": "default", "chromas": false}, {"id": "62001", "num": 1, "name": "Vulkanischer Wukong", "chromas": false}, {"id": "62002", "num": 2, "name": "General <PERSON>", "chromas": false}, {"id": "62003", "num": 3, "name": "Jadedrachen-Wukong", "chromas": true}, {"id": "62004", "num": 4, "name": "Unterwelt-Wukong", "chromas": false}, {"id": "62005", "num": 5, "name": "Strahlender Wukong", "chromas": false}, {"id": "62006", "num": 6, "name": "Weiße Wolkenlanze Wukong", "chromas": false}, {"id": "62007", "num": 7, "name": "Academia Certaminis-Wukong", "chromas": true}, {"id": "62016", "num": 16, "name": "Ahnenholz-Wukong", "chromas": true}], "lore": "Wukong ist ein vastayanisches Schlitzohr, das sich auf seine Stärke, Beweglichkeit und Intelligenz verlässt, um seine Gegner zu verwirren und die Oberhand zu gewinnen. Nachdem Wukong mit Master Yi einen Freund fürs Leben gefunden hatte, wurde er der letzte Schüler der uralten Kampfkunst, die als Wuju bekannt ist. Mit seinem verzauberten Stab hat Wukong es sich zum Ziel gesetzt, Ionia vor dem Untergang zu bewahren.", "blurb": "Wukong ist ein vastayanisches Schlitzohr, das sich auf seine Stärke, Beweglichkeit und Intelligenz verlässt, um seine Gegner zu verwirren und die Oberhand zu gewinnen. Nachdem Wukong mit Master Yi einen Freund fürs Leben gefunden hatte, wurde er der...", "allytips": ["„Trugbild“ und „Nimbusschlag“ ergänzen sich gut, um schnell zuzuschlagen und sich zurückzuziehen, noch bevor der Gegner reagieren kann.", "<PERSON><PERSON><PERSON> „Trugbild“ nahe hohem G<PERSON>, um deinen Gegner völlig zu verwirren."], "enemytips": ["Wukong wird häufig „Trugbild“ nach „Nimbusschlag“ einsetzen. Warte mit deinen Fähigkeiten einen Augenblick, um sicherzugehen, dass du den echten Wukong triffst.", "Wu<PERSON><PERSON> wird schwerer zu töten, wenn er von seinen Gegnern umgeben ist. Versuche ihn zu isolieren, um dies zu deinem Vorteil zu nutzen."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 330, "mpperlevel": 65, "movespeed": 340, "armor": 31, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.69}, "spells": [{"id": "MonkeyKingDoubleAttack", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wukongs nächster Angriff erhält Angriffsreichweite, verursacht zusätzlichen Schaden und verringert ein paar Sekunden lang die Rüstung seines Ziels.", "tooltip": "Der nächste Angriff von Wukong und seinem <keywordMajor>Klon</keywordMajor> hat {{ attackrangebonus }}&nbsp;zusätzliche Reichweite, verursacht zusätzlich <physicalDamage>{{ bonusdamagett }}&nbsp;normalen Schaden</physicalDamage> und entfernt {{ shredduration }}&nbsp;Sekunden lang <scaleArmor>{{ armorshredpercent*100 }}&nbsp;% Rüstung</scaleArmor> des Ziels.<br /><br />Die Abklingzeit dieser Fähigkeit wird jedes Mal um {{ cooldowndecrease }}&nbsp;<PERSON><PERSON><PERSON> verringert, wenn Wukong oder sein <keywordMajor><PERSON>lon</keywordMajor> einen Gegner mit einem Angriff oder einer Fähigkeit treffen.<br /><br /><rules>Diese Fähigkeit löst Zaubereffekte aus, wenn sie Schaden verursacht.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Rüstungsverringerung (%)", "Reichweite", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ armorshredpercent*100.000000 }}&nbsp;% -> {{ armorshredpercentnl*100.000000 }}&nbsp;%", "{{ attackrangebonus }} -> {{ attackrangebonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 275, 300, 325, 350], "rangeBurn": "250/275/300/325/350", "image": {"full": "MonkeyKingDoubleAttack.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingDecoy", "name": "<PERSON><PERSON><PERSON><PERSON> Krieger", "description": "Wukong wird <font color='#91d7ee'>unsichtbar</font>, springt in eine Richtung und hinterlässt einen Klon, der Gegner in der Nähe angreift.", "tooltip": "Wukong springt, wird für {{ stealthduration }}&nbsp;Sekunde <keywordStealth>unsichtbar</keywordStealth> und hinterlässt {{ cloneduration }}&nbsp;Sekunden lang einen stationären <keywordMajor><PERSON>lon</keywordMajor>.<br /><br />Der <keywordMajor>Klon</keywordMajor> gre<PERSON> in der Nähe an, denen Wukong vor Kurzem Schaden zugefügt hat, ahmt seine ultimative Fähigkeit nach und verursacht dadurch {{ clonedamagemod*100 }}&nbsp;% normalen Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schadensanteil", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ clonedamagemod*100.000000 }}&nbsp;% -> {{ clonedamagemodnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "MonkeyKingDecoy.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingNimbus", "name": "Nimbusschlag", "description": "Wukong stürmt auf einen anvisierten Gegner zu und entsendet Abbilder von sich selbst, um Gegner in der Nähe des Ziels anzugreifen. Getroffene Gegner erleiden Schaden.", "tooltip": "Wukong springt zu einem Gegner und schickt <keywordMajor><PERSON><PERSON></keywordMajor> los, die den Sprung zu maximal {{ extratargets }} zusätzlichen Gegnern in der Nähe nachahmen. Jeder getroffene Gegner erleidet <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>. Er und sein <keywordMajor>Klon</keywordMajor> erhalten {{ attackspeedduration }}&nbsp;Sekunden lang <attackSpeed>{{ attackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Angriffstempo", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}&nbsp;% -> {{ attackspeednl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.25, 8.5, 7.75, 7], "cooldownBurn": "10/9.25/8.5/7.75/7", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MonkeyKingNimbus.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingSpinToWin", "name": "Zyklon", "description": "Wukong verlängert seinen Stab und wirbelt ihn wiederholt herum. Dabei erhält er Lauftempo.<br><br>Getroffene Gegner erleiden Schaden und werden hochgeschleudert.", "tooltip": "Wukong erhält <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed> und wirbelt seinen Stab herum. Dabei <status>schleudert</status> er Gegner in der Nähe {{ knockupduration }}&nbsp;Sekunden lang hoch und verursacht über {{ spinduration }}&nbsp;Sekunden <physicalDamage>normalen Schaden</physicalDamage> in H<PERSON><PERSON> von {{ totaldamagett }} plus {{ percenthpdamagett }} des maximalen Lebens.<br /><br />Diese Fähigkeit kann innerhalb von {{ recastwindow }}&nbsp;Sekunden ein zweites Mal aktiviert werden, bevor die Abklingzeit ausgelöst wird.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden abhängig vom maximalen Leben ", "Abklingzeit"], "effect": ["{{ basepercentmaxhpdmgpersec*200.000000 }}&nbsp;% -> {{ basepercentmaxhpdmgpersecnl*200.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [315, 315, 315], "rangeBurn": "315", "image": {"full": "MonkeyKingSpinToWin.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Wukong erhält Rüstung und regeneriert sein maximales Leben, während er gegen Champions und Monster kämpft.", "image": {"full": "MonkeyKingStoneSkin.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}