{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "<PERSON>rma", "title": "die Erleuchtete", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "43002", "num": 2, "name": "Sakura-<PERSON><PERSON>", "chromas": false}, {"id": "43003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "43004", "num": 4, "name": "Lotusorden-<PERSON><PERSON>", "chromas": false}, {"id": "43005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "43006", "num": 6, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": false}, {"id": "43007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "43008", "num": 8, "name": "Sternenvernichter-Karma", "chromas": true}, {"id": "43019", "num": 19, "name": "Rächende Dämmerung Karma", "chromas": false}, {"id": "43026", "num": 26, "name": "Odyssee-<PERSON><PERSON>", "chromas": false}, {"id": "43027", "num": 27, "name": "Schwarznebel-Karma", "chromas": true}, {"id": "43044", "num": 44, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "43054", "num": 54, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "43061", "num": 61, "name": "Infernalische Karma", "chromas": false}, {"id": "43070", "num": 70, "name": "<PERSON><PERSON>blumen<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "<PERSON>ine Sterbliche personifiziert die spirituellen Traditionen von <PERSON> besser als Karma. Sie ist die lebende Verkörperung einer uralten, unzählige Male reinkarnierten Seele, die ihre gesammelten Erinnerungen in all ihre neuen Leben übernimmt. <PERSON>ht, die sie dara<PERSON> zieht, können nur wenige begreifen. In Zeiten großer Gefahr hat sie ihr Bestes getan, ihr Volk zu leiten, doch sie weiß, dass Frieden und Harmonie einen hohen Preis einfordern – sowohl von ihr als auch von dem <PERSON>, das sie liebt.", "blurb": "<PERSON>ine Sterbliche personifiziert die spirituellen Traditionen von Ion<PERSON> besser als Karma. Sie ist die lebende Verkörperung einer uralten, unzählige Male reinkarnierten Seele, die ihre gesammelten Erinnerungen in all ihre neuen Leben übernimmt. Die Macht...", "allytips": ["„Entflammen“ belohnt eine aggressive Spielweise. Versuche Fähigkeiten und normale Angriffe auf deinen Gegner zu feuern, um die Abklingzeit von „Mantra“ zu verringern und bleibe in der Offensive.", "Wenn du „Konzentrierte Entschlossenheit“ einsetzt, solltest du deine Widersacher mit „Inneres Feuer“ verlangsamen oder dir selbst mit „Inspirieren“ mehr Tempo verleihen, falls du Probleme bekommst, an einem Ziel dranzubleiben.", "Gehe nicht zu sparsam mit „Mantra“ um. „Entflammen“ ist am stärksten in Teamkämpfen, wodurch du „Mantra“ ganz leicht mehrmals wieder aufladen kannst."], "enemytips": ["Karmas passive Fähigkeit verringert durch die Treffer gegnerischer Champions die Abklingzeit von „Mantra“. Lasse nicht zu, dass sie Gratistreffer erzielt.", "<PERSON><PERSON> „Seelenflackern“ explodiert mit zusätzlichem Schaden im platzierten Gebiet. <PERSON>l<PERSON><PERSON><PERSON> schnell, um hohen Schaden zu vermeiden.", "„Konzentrierte Entschlossenheit“ ist ein mächtiges Werkzeug zur Kampfunterbrechung. <PERSON><PERSON> Distanz auf, damit du nicht festgehalten wirst, und versuche danach anzugreifen."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "Inneres Feuer", "description": "Karma sendet einen Ball aus Geistenergie aus, der explodiert und Schaden verursacht, sobald er auf eine gegnerische Einheit trifft.<br><br>Mantra-Bonus: Zusätzlich zur Explosion erhöht „Mantra“ die zerstörerische Kraft von „Inneres Feuer“ und schafft einen Kataklysmus, der nach einer kurzen Verzögerung Schaden verursacht.", "tooltip": "Karma feuert einen Energiestoß ab, der dem ersten getroffen Ziel und umstehenden Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt und sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*-100 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSpiritBind", "name": "Konzentrierte Entschlossenheit", "description": "Karma stellt eine Verbindung zwischen sich und einem anvisierten Gegner her, die Schaden verursacht und diesen aufdeckt. Sollte die Verbindung nicht unterbrochen werden, wird der Gegner festgehalten und erleidet erneut Schaden.<br><br>Mantra-Bonus: Karma verstärkt die Verbindung, wodurch sie sich selbst heilt und die Dauer des Festhaltens ausweitet.", "tooltip": "<PERSON><PERSON> verbindet sich mit einem Champion o<PERSON>, fügt ihm <magicDamage>{{ initialdamage }}&nbsp;magischen Schaden</magicDamage> zu und deckt ihn {{ tetherduration }}&nbsp;Sekunden lang auf. Wenn die Verbindung nicht getrennt wird, erleidet das Ziel erneut <magicDamage>{{ initialdamage }}&nbsp;magischen Schaden</magicDamage> und wird {{ rootduration }}&nbsp;Sekunden lang <status>festgehalten</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Festhaltedauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSolKimShield", "name": "Inspirieren", "description": "<PERSON><PERSON> besch<PERSON>rt einen schützenden Schild, der eingehenden Schaden absorbiert und das Lauftempo geschützter Verbündeter erhöht.<br><br>Mantra-Bonus: Energie strömt aus ihrem Ziel und stärkt den ursprünglichen Schild. Verbündete Champions in der Nähe erhalten „Inspirieren“.", "tooltip": "<PERSON><PERSON> gewährt einem verbündeten Champion {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ totalshield }} und {{ movespeedduration }}&nbsp;Sekunden lang <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Mantra", "description": "Karma verstärkt ihre nächste gewirkte Fähigkeit mit einem zusätzlichen Effekt. „Mantra“ ist bereits auf Stufe 1 verfügbar und muss nicht erst erlernt werden.", "tooltip": "Karma verstärkt ihre nächste Fähigkeit, die sie innerhalb von 8&nbsp;Sekunden einsetzt.<br /><li><spellName>Inneres Feuer</spellName>: Verursacht zusätzlich <magicDamage>{{ rqimpactdamage }}&nbsp;magischen Schaden</magicDamage> und hinterlässt einen Flammenkreis, der Gegner <status>verlangsamt</status> und ihnen zusätzlich <magicDamage>{{ rqfielddamage }}&nbsp;magischen Schaden</magicDamage> zufügt.<li><spellName>Konzentrierte Entschlossenheit</spellName>: Karma stellt am Anfang und Ende der Verbindung <healing>{{ rwhealamount }}&nbsp;fehlendes Leben</healing> wieder her und kann Gegner {{ rwbonusroot }}&nbsp;Sekunden länger <status>festhalten</status>.<li><spellName>Inspirieren</spellName>: Karma<PERSON> <shield>Schild</shield> auf einen Verbündeten ist um {{ rebonusshield }} erhöht und Verbündete in dessen Nähe erhalten einen <shield>Schild</shield> in Höhe von {{ rebonusshieldarea }} und <speed>{{ removespeed*100 }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Seelenflackern: Einschlag", "Seelenflackern: Kreisschaden", "Erneuerung: Erhöhung der Festhaltedauer", "Trotz<PERSON><PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ qbonusdamage }} -> {{ qbonusdamageNL }}", "{{ qdetonationdamage }} -> {{ qdetonationdamageNL }}", "{{ rwbonusroot }} -> {{ rwbonusrootNL }}", "{{ ebonusshield }} -> {{ ebonusshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Entflammen", "description": "<PERSON><PERSON><PERSON>fähigkeiten verringern die Abklingzeit von „Mantra“.", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}