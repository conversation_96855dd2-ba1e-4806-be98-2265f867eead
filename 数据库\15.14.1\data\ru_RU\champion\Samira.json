{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Samira": {"id": "<PERSON><PERSON>", "key": "360", "name": "Самира", "title": "Пустынная роза", "image": {"full": "Samira.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "360000", "num": 0, "name": "default", "chromas": false}, {"id": "360001", "num": 1, "name": "Самира из Пси-отряда", "chromas": true}, {"id": "360010", "num": 10, "name": "Самира из Галактики грува", "chromas": true}, {"id": "360020", "num": 20, "name": "Ковбой Самира", "chromas": true}, {"id": "360030", "num": 30, "name": "Боевая душа Самира", "chromas": false}, {"id": "360033", "num": 33, "name": "Самира на маскараде Черной Розы", "chromas": false}], "lore": "Самира смотрит в лицо смерти с непоколебимой уверенностью и всегда ищет самые рискованные предприятия. В детстве бежав из разрушенной войной Шуримы, Самира нашла свое призвание в Ноксусе. Теперь эта стильная и бесшабашная наемница берется за самые опасные и ответственные задания. Самира орудует пистолетами с черным порохом и клинком, изготовленным по особым чертежам. Она устраняет все преграды на своем пути с неподражаемым шиком и никогда не дрогнет перед лицом смертельной опасности.", "blurb": "Самира смотрит в лицо смерти с непоколебимой уверенностью и всегда ищет самые рискованные предприятия. В детстве бежав из разрушенной войной Шуримы, Самира нашла свое призвание в Ноксусе. Теперь эта стильная и бесшабашная наемница берется за самые...", "allytips": [], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "Мана", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 108, "mp": 349, "mpperlevel": 38, "movespeed": 335, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "SamiraQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Самира совершает выстрел или удар мечом, нанося урон. При применении во время Неистового рывка Самира поражает всех врагов на своем пути.", "tooltip": "Самира совершает выстрел, нанося <physicalDamage>{{ damagecalc }} физического урона</physicalDamage> первому пораженному врагу.<br /><br />Если это умение направлено во врага вблизи, вместо выстрела Самира совершает удар мечом, нанося <physicalDamage>{{ damagecalc }} физического урона</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Коэффициент общей силы атаки"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ qadratio*100.000000 }}% -> {{ qadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "SamiraQ.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SamiraW", "name": "Взмах клинка", "description": "Самира вращается с мечом, нанося урон врагам и уничтожая вражеские снаряды.", "tooltip": "Самира вращается с мечом в течение {{ slashduration }} сек., дважды нанося врагам <physicalDamage>{{ damagecalc }} физического урона</physicalDamage> и уничтожая любые вражеские снаряды, которые к ней подлетают.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [30, 28, 26, 24, 22], "cooldownBurn": "30/28/26/24/22", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "SamiraW.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SamiraE", "name": "Неистовый рывок", "description": "Самира совершает рывок сквозь врага (включая строения), нанося урон всем противникам на пути и увеличивая скорость атаки. Убийства вражеских чемпионов сбрасывают перезарядку этого умения.", "tooltip": "Самира совершает рывок сквозь врага (включая строения), нанося <magicDamage>{{ dashdamage }} магического урона</magicDamage> всем противникам на пути и увеличивая свою <attackSpeed>скорость атаки на {{ bonusattackspeed*100 }}%</attackSpeed> на {{ attackspeedduration }} сек.<br /><br />Если вражеский чемпион погибает в течение 3 сек. после того, как Самира нанесла ему урон, перезарядка этого умения сбрасывается.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Скорость атаки"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ bonusattackspeed*100.000000 }}% -> {{ bonusattackspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraE.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SamiraR", "name": "Адское пекло", "description": "Самира безостановочно палит из своих пистолетов, расстреливая всех врагов вокруг.", "tooltip": "Самира может применить это умение, только если ее оценка <keywordMajor>стиля</keywordMajor> – S. Это умение поглощает весь заработанный <keywordMajor>стиль</keywordMajor>.<br /><br />Самира осыпает градом пуль всех врагов вокруг до 10 раз в течение 2 сек. Каждый выстрел наносит <physicalDamage>{{ damagecalc }} физического урона</physicalDamage>, и на него действует вампиризм с эффективностью {{ lifestealmod*100 }}%. Кроме того, каждый выстрел может быть критическим.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraR.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Безрассудный порыв", "description": "Попадая автоатаками или умениями, отличными от тех, которыми Самира попала перед этим, она выстраивает комбинацию. Автоатаки Самиры в ближнем бою наносят дополнительный магический урон. Когда Самира применяет автоатаки к врагам, на которых действуют эффекты <status>обездвиживания</status>, она совершает к ним рывок на расстояние дальности атаки. Если цель <status>подброшена</status>, продолжительность <status>подбрасывания</status> немного увеличивается.", "image": {"full": "SamiraP.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}