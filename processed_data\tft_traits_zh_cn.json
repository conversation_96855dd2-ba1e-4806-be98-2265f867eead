{"type": "traits", "version": "15.14.1", "language": "zh_CN", "count": 150, "data": {"TFT13_Augment_FirelightCrest": {"apiName": "TFT13_Augment_FirelightCrest", "name": "野火帮之徽", "desc": "获得1个【野火帮纹章】和1个【泽丽】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Hoverboard_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Hoverboard"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_WatcherCrest": {"apiName": "TFT13_Augment_WatcherCrest", "name": "监察之徽", "desc": "获得1个【监察纹章】和1个【范德尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Watcher_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Watcher"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_SniperCrest": {"apiName": "TFT13_Augment_SniperCrest", "name": "狙神之徽", "desc": "获得1个【狙神纹章】和1个【泽丽】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sniper_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT13_Sniper"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_CabalCrest": {"apiName": "TFT13_Augment_CabalCrest", "name": "黑色玫瑰之徽", "desc": "获得1个【黑色玫瑰纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Cabal_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Cabal"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_ExperimentCrest": {"apiName": "TFT13_Augment_ExperimentCrest", "name": "试验品之徽", "desc": "获得1个【试验品纹章】和1个【厄加特】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Experiment_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Experiment"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_ChembaronCrest": {"apiName": "TFT13_Augment_ChembaronCrest", "name": "炼金男爵之徽", "desc": "获得1个【炼金男爵纹章】和1个【烈娜塔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Crime_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Crime"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_SentinelCrest": {"apiName": "TFT13_Augment_SentinelCrest", "name": "哨兵之徽", "desc": "获得1个【哨兵纹章】和1个【芮尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sentinel_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Titan"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_RebelCrest": {"apiName": "TFT13_Augment_RebelCrest", "name": "蓝发小队之徽", "desc": "获得1个【蓝发小队纹章】和1个【阿卡丽】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Rebel_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Rebel"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_EnforcerCrest": {"apiName": "TFT13_Augment_EnforcerCrest", "name": "执法官之徽", "desc": "获得1个【执法官纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Squad_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Squad"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_FamilyCrest": {"apiName": "TFT13_Augment_FamilyCrest", "name": "家人之徽", "desc": "获得1个【家人纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Family_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Family"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_AmbusherCrest": {"apiName": "TFT13_Augment_AmbusherCrest", "name": "伏击专家之徽", "desc": "获得1个【伏击专家纹章】和1个【卡蜜尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Ambusher_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Ambusher"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_QuickstrikerCrest": {"apiName": "TFT13_Augment_QuickstrikerCrest", "name": "迅击战士之徽", "desc": "获得1个【迅击战士纹章】和1个【阿卡丽】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Quickstriker_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Challenger"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_VisionaryCrest": {"apiName": "TFT13_Augment_VisionaryCrest", "name": "先知之徽", "desc": "获得1个【先知纹章】和1个【烈娜塔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Visionary_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Invoker"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_BruiserCrest": {"apiName": "TFT13_Augment_BruiserCrest", "name": "格斗家之徽", "desc": "获得1个【格斗家纹章】和1个【瑟提】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Bruiser_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Bruiser"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_SorcererCrest": {"apiName": "TFT13_Augment_SorcererCrest", "name": "法师之徽", "desc": "获得1个【法师纹章】和1个【弗拉基米尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sorcerer_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT13_Sorcerer"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_PitFighterCrest": {"apiName": "TFT13_Augment_PitFighterCrest", "name": "搏击手之徽", "desc": "获得1个【搏击手纹章】和1个【厄加特】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_PitFighter_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Pugilist"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_ConquerorCrest": {"apiName": "TFT13_Augment_ConquerorCrest", "name": "铁血征服者之徽", "desc": "获得1个【铁血征服者纹章】和1个【芮尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Conqueror_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Warband"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_AcademyCrest": {"apiName": "TFT13_Augment_AcademyCrest", "name": "皮城学院之徽", "desc": "获得1个【皮城学院纹章】和1个【蕾欧娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Academy_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Academy"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_ArtilleristCrest": {"apiName": "TFT13_Augment_ArtilleristCrest", "name": "炮手之徽", "desc": "获得1个【炮手纹章】和1个【崔丝塔娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Artillerist_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Martialist"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_DominatorCrest": {"apiName": "TFT13_Augment_DominatorCrest", "name": "统领之徽", "desc": "获得1个【统领纹章】和1个【布里茨】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Dominator_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Infused"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_AutomataCrest": {"apiName": "TFT13_Augment_AutomataCrest", "name": "海克斯机械之徽", "desc": "获得1个【海克斯机械纹章】和1个【魔腾】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Automata_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Hextech"], "effects": {}, "composition": [], "from": null}, "TFT13_Augment_ScrapCrest": {"apiName": "TFT13_Augment_ScrapCrest", "name": "极客之徽", "desc": "获得1个【极客纹章】和1个【吉格斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Scrap_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT13_Scrap"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_ScholarCrest": {"apiName": "TFT12_Augment_ScholarCrest", "name": "学者之徽", "desc": "获得1个【学者纹章】和1个【阿狸】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Scholar1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Scholar"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_SugarcraftCrest": {"apiName": "TFT12_Augment_SugarcraftCrest", "name": "咖啡甜心之徽", "desc": "获得1个【咖啡甜心纹章】和1个【兰博】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sugarcraft1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Sugarcraft"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_ChronoCrest": {"apiName": "TFT12_Augment_ChronoCrest", "name": "时间学派之徽", "desc": "获得1个【时间学派纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Time1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Chrono"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_PortalCrest": {"apiName": "TFT12_Augment_PortalCrest", "name": "次元术士之徽", "desc": "获得1个【次元术士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Portal1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Portal"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_FrostCrest": {"apiName": "TFT12_Augment_FrostCrest", "name": "冰霜之徽", "desc": "获得1个【冰霜纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Winter1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Frost"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_WitchcraftCrest": {"apiName": "TFT12_Augment_WitchcraftCrest", "name": "诅咒女巫之徽", "desc": "获得1个【诅咒女巫纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Witch1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Witchcraft"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_HunterCrest": {"apiName": "TFT12_Augment_HunterCrest", "name": "猎手之徽", "desc": "获得1个【猎手纹章】和1个【克格莫】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Hunter1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Hunter"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_MultistrikerCrest": {"apiName": "TFT12_Augment_MultistrikerCrest", "name": "魔战士之徽", "desc": "获得1个【魔战士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Weaponmaster1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Multistriker"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_PyroCrest": {"apiName": "TFT12_Augment_PyroCrest", "name": "炎魔之徽", "desc": "获得1个【炎魔纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Pyrokinesis1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Pyro"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_ShapeshifterCrest": {"apiName": "TFT12_Augment_ShapeshifterCrest", "name": "换形师之徽", "desc": "获得1个【换形师纹章】和1个【希瓦娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Shapeshifter1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Shapeshifter"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_BastionCrest": {"apiName": "TFT12_Augment_BastionCrest", "name": "堡垒卫士之徽", "desc": "获得1个【堡垒卫士纹章】和1个【努努】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Bastion1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Bastion"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_HoneymancyCrest": {"apiName": "TFT12_Augment_HoneymancyCrest", "name": "小蜜蜂之徽", "desc": "获得1个【小蜜蜂纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Bee1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_<PERSON><PERSON>cy"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_VanguardCrest": {"apiName": "TFT12_Augment_VanguardCrest", "name": "重装战士之徽", "desc": "获得1个【重装战士纹章】和1个【兰博】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Vanguard1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Vanguard"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_WarriorCrest": {"apiName": "TFT12_Augment_WarriorCrest", "name": "狂暴战士之徽", "desc": "获得1个【狂暴战士纹章】和1个【阿卡丽】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Warrior1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Warrior"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_FaerieCrest": {"apiName": "TFT12_Augment_FaerieCrest", "name": "花仙子之徽", "desc": "获得1个【花仙子纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Faerie1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Faerie"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_MageCrest": {"apiName": "TFT12_Augment_MageCrest", "name": "法师之徽", "desc": "获得1个【法师纹章】和1个【加里奥】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Mage1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Mage"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_EldritchCrest": {"apiName": "TFT12_Augment_EldritchCrest", "name": "魔神使者之徽", "desc": "获得1个【魔神使者纹章】和1个【尼菈】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Eldritch1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_<PERSON><PERSON><PERSON>"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_PreserverCrest": {"apiName": "TFT12_Augment_PreserverCrest", "name": "复苏者之徽", "desc": "获得1个【复苏者纹章】和1个【基兰】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Preserver1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Preserver"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_ArcanaCrest": {"apiName": "TFT12_Augment_ArcanaCrest", "name": "命运之子之徽", "desc": "获得1个【命运之子纹章】、1个【阿狸】和1个【赫卡里姆】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Arcana_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Arcana"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_BlasterCrest": {"apiName": "TFT12_Augment_BlasterCrest", "name": "强袭枪手之徽", "desc": "获得1个【强袭枪手纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Artillerist1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Blaster"], "effects": {}, "composition": [], "from": null}, "TFT12_Augment_IncantorCrest": {"apiName": "TFT12_Augment_IncantorCrest", "name": "术师之徽", "desc": "获得1个【术师纹章】和1个【辛德拉】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Incantor1_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT12_Incantor"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_SpellweaverCrest": {"apiName": "TFT5_Augment_SpellweaverCrest", "name": "法师之徽", "desc": "获得1个【法师纹章】和1个【布兰德】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Spellweaver_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Spellweaver"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_BrawlerCrest": {"apiName": "TFT5_Augment_BrawlerCrest", "name": "斗士之徽", "desc": "获得1个【斗士纹章】和1个【瑟提】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Brawler_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Brawler"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_CannoneerCrest": {"apiName": "TFT5_Augment_CannoneerCrest", "name": "强袭炮手之徽", "desc": "获得1个【强袭炮手纹章】和1个【崔丝塔娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Cannoneer_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Cannoneer"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_SkirmisherCrest": {"apiName": "TFT5_Augment_SkirmisherCrest", "name": "神盾战士之徽", "desc": "获得1个【神盾战士纹章】和1个【奥拉夫】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Skirmisher_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Skirmisher"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_AbominationCrest": {"apiName": "TFT5_Augment_AbominationCrest", "name": "丧尸之徽", "desc": "获得1个【丧尸纹章】和1个【布兰德】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Abomination_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Abomination"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_MysticCrest": {"apiName": "TFT5_Augment_MysticCrest", "name": "秘术师之徽", "desc": "获得1个【秘术师纹章】和1个【璐璐】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Mystic_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Mystic"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_RedeemedCrest": {"apiName": "TFT5_Augment_RedeemedCrest", "name": "圣光卫士之徽", "desc": "获得1个【圣光卫士纹章】和1个【辛德拉】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Redeemed_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Redeemed"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_IroncladCrest": {"apiName": "TFT5_Augment_IroncladCrest", "name": "铁甲卫士之徽", "desc": "获得1个【铁甲卫士纹章】和1个【诺提勒斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Ironclad_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Ironclad"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_RenewerCrest": {"apiName": "TFT5_Augment_RenewerCrest", "name": "复苏者之徽", "desc": "获得1个【复苏者纹章】和1个【索拉卡】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Renewer_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Renewer"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_DawnbringerCrest": {"apiName": "TFT5_Augment_DawnbringerCrest", "name": "黎明使者之徽", "desc": "获得1个【黎明使者纹章】和1个【古拉加斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Dawnbringer_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Dawnbringer"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_HellionCrest": {"apiName": "TFT5_Augment_HellionCrest", "name": "小恶魔之徽", "desc": "获得1个【小恶魔纹章】和1个【凯南】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Hellion_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Hellion"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_LegionnaireCrest": {"apiName": "TFT5_Augment_LegionnaireCrest", "name": "征服者之徽", "desc": "获得1个【征服者纹章】和1个【艾瑞莉娅】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Legionnaire_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Legionnaire"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_AssassinCrest": {"apiName": "TFT5_Augment_AssassinCrest", "name": "刺客之徽", "desc": "获得1个【刺客纹章】和1个【卡兹克】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Assassin_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Assassin"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_RevenantCrest": {"apiName": "TFT5_Augment_RevenantCrest", "name": "复生亡魂之徽", "desc": "获得1个【复生亡魂纹章】和1个【魔腾】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Revenant_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Revenant"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_InvokerCrest": {"apiName": "TFT5_Augment_InvokerCrest", "name": "神谕者之徽", "desc": "获得1个【神谕者纹章】和1个【辛德拉】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Invoker_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Invoker"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_CavalierCrest": {"apiName": "TFT5_Augment_CavalierCrest", "name": "重骑兵之徽", "desc": "获得1个【重骑兵纹章】和1个【赫卡里姆】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Cavalier_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Cavalier"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_SentinelCrest": {"apiName": "TFT5_Augment_SentinelCrest", "name": "光明哨兵之徽", "desc": "获得1个【光明哨兵纹章】和1个【赛娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sentinel_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT5_Sentinel"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_NightbringerCrest": {"apiName": "TFT5_Augment_NightbringerCrest", "name": "黑夜使者之徽", "desc": "获得1个【黑夜使者纹章】和1个【瑟庄妮】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Nightbringer_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Nightbringer"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_DraconicCrest": {"apiName": "TFT5_Augment_DraconicCrest", "name": "龙族之徽", "desc": "获得1个【龙族纹章】和1个【婕拉】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Draconic_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Draconic"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_KnightCrest": {"apiName": "TFT5_Augment_KnightCrest", "name": "骑士之徽", "desc": "获得1个【骑士纹章】和1个【锤石】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/C<PERSON>_Knight_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Knight"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_RangerCrest": {"apiName": "TFT5_Augment_RangerCrest", "name": "游侠之徽", "desc": "获得1个【游侠纹章】和1个【韦鲁斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/C<PERSON>_Ranger_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Ranger"], "effects": {}, "composition": [], "from": null}, "TFT5_Augment_ForgottenCrest": {"apiName": "TFT5_Augment_ForgottenCrest", "name": "破败军团之徽", "desc": "获得1个【破败军团纹章】和1个【薇恩】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Forgotten_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT5_Forgotten"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_DivineCrest": {"apiName": "TFT4_Augment_DivineCrest", "name": "天神之徽", "desc": "获得1个【天神纹章】 和1个【贾克斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Divine_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Divine"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_SlayerCrest": {"apiName": "TFT4_Augment_SlayerCrest", "name": "战神之徽", "desc": "获得1个【战神纹章】和1个【劫】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Slayer_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Slayer"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_SyphonerCrest": {"apiName": "TFT4_Augment_SyphonerCrest", "name": "摄魂使之徽", "desc": "获得1个【摄魂使纹章】和1个【弗拉基米尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Syphoner_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Syphoner"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_BrawlerCrest": {"apiName": "TFT4_Augment_BrawlerCrest", "name": "斗士之徽", "desc": "获得1个【斗士纹章】和1个【蔚】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Brawler_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Brawler"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_MageCrest": {"apiName": "TFT4_Augment_MageCrest", "name": "魔法师之徽", "desc": "获得1个【魔法师纹章】和1个【璐璐】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Mage_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Mage"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_DuelistCrest": {"apiName": "TFT4_Augment_DuelistCrest", "name": "决斗大师之徽", "desc": "获得1个【决斗大师纹章】 和1个【贾克斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Duelist_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Duelist"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_ElderwoodCrest": {"apiName": "TFT4_Augment_ElderwoodCrest", "name": "永恒之森之徽", "desc": "获得1个【永恒之森纹章】和1个【洛】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Elderwood_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Elderwood"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_WarlordCrest": {"apiName": "TFT4_Augment_WarlordCrest", "name": "三国猛将之徽", "desc": "获得1个【三国猛将纹章】和1个【蔚】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Warlord_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Warlord"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_VanguardCrest": {"apiName": "TFT4_Augment_VanguardCrest", "name": "重装战士之徽", "desc": "获得1个【重装战士纹章】和1个【诺提勒斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Vanguard_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Vanguard"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_AssassinCrest": {"apiName": "TFT4_Augment_AssassinCrest", "name": "刺客之徽", "desc": "获得1个【刺客纹章】和1个【派克】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Assassin_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Assassin"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_SpiritCrest": {"apiName": "TFT4_Augment_SpiritCrest", "name": "灵魂莲华明昼之徽", "desc": "获得1个【灵魂莲华明昼纹章】和1个【提莫】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Spirit_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Spirit"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_EnlightenedCrest": {"apiName": "TFT4_Augment_EnlightenedCrest", "name": "玉剑仙之徽", "desc": "获得1个【玉剑仙纹章】和1个【迦娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Enlightened_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Enlightened"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_CultistCrest": {"apiName": "TFT4_Augment_CultistCrest", "name": "腥红之月之徽", "desc": "获得1个【腥红之月纹章】和1个【派克】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Cultist_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Cultist"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_KeeperCrest": {"apiName": "TFT4_Augment_KeeperCrest", "name": "神盾使之徽", "desc": "获得1个【神盾使纹章】和1个【嘉文四世】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Keeper_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Keeper"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_DragonsoulCrest": {"apiName": "TFT4_Augment_DragonsoulCrest", "name": "龙魂之徽", "desc": "获得1个【龙魂纹章】和1个【布隆】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Dragonsoul_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Dragonsoul"], "effects": {}, "composition": [], "from": null}, "TFT4_Augment_FortuneCrest": {"apiName": "TFT4_Augment_FortuneCrest", "name": "福星之徽", "desc": "获得1个【福星纹章】和1个【安妮】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Fortune_II.TFT_Set4_5_Revival.tex", "id": null, "associatedTraits": ["TFT4_Fortune"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_BehemothCrest": {"apiName": "TFT11_Augment_BehemothCrest", "name": "擎天卫之徽", "desc": "获得1个【擎天卫纹章】、1个【慎】和1个【锤石】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Behemoth_II.tex", "id": null, "associatedTraits": ["TFT11_Behemoth"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_ExaltedCrest": {"apiName": "TFT11_Augment_ExaltedCrest", "name": "尊者之徽", "desc": "获得1个【尊者纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Exalted_II.tex", "id": null, "associatedTraits": ["TFT11_Exalted"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_PorcelainCrest": {"apiName": "TFT11_Augment_PorcelainCrest", "name": "青花瓷之徽", "desc": "获得1个【青花瓷纹章】和1个【阿木木】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Porcelain_II.tex", "id": null, "associatedTraits": ["TFT11_Porcelain"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_MythicCrest": {"apiName": "TFT11_Augment_MythicCrest", "name": "山海绘卷之徽", "desc": "获得1个【山海绘卷纹章】和1个【妮蔻】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Mythic_II.tex", "id": null, "associatedTraits": ["TFT11_Mythic"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_StoryweaverCrest": {"apiName": "TFT11_Augment_StoryweaverCrest", "name": "剪纸仙灵之徽", "desc": "获得1个【剪纸仙灵纹章】和1个【盖伦】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Storyweaver_II.tex", "id": null, "associatedTraits": ["TFT11_Storyweaver"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_HeavenlyCrest": {"apiName": "TFT11_Augment_HeavenlyCrest", "name": "天将之徽", "desc": "获得1个【天将纹章】和1个【妮蔻】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Heavenly_II.tex", "id": null, "associatedTraits": ["TFT11_Heavenly"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_DuelistCrest": {"apiName": "TFT11_Augment_DuelistCrest", "name": "决斗大师之徽", "desc": "获得1个【决斗大师纹章】 和1个【奇亚娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Duelist_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT11_Duelist"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_InkshadowCrest": {"apiName": "TFT11_Augment_InkshadowCrest", "name": "墨之影之徽", "desc": "获得1个【墨之影纹章】和1个【赛娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Inkshadow_II.tex", "id": null, "associatedTraits": ["TFT11_InkShadow"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_BrawlerCrest": {"apiName": "TFT11_Augment_BrawlerCrest", "name": "斗士之徽", "desc": "获得1个【斗士纹章】 和1个【塔姆】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Bruiser_II.TFT_Set13.tex", "id": null, "associatedTraits": ["TFT11_Bruiser"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_FortuneCrest": {"apiName": "TFT11_Augment_FortuneCrest", "name": "吉星之徽", "desc": "获得1个【吉星纹章】和1个【崔丝塔娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Fortune_II.tex", "id": null, "associatedTraits": ["TFT11_Fortune"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_UmbralCrest": {"apiName": "TFT11_Augment_UmbralCrest", "name": "夜幽之徽", "desc": "获得1个【夜幽纹章】和1个【约里克】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Umbral_II.tex", "id": null, "associatedTraits": ["TFT11_Umbral"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_DryadCrest": {"apiName": "TFT11_Augment_DryadCrest", "name": "永恒之森之徽", "desc": "获得1个【永恒之森纹章】和1个【纳尔】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Dryad_II.tex", "id": null, "associatedTraits": ["TFT11_Dryad"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_DragonlordCrest": {"apiName": "TFT11_Augment_DragonlordCrest", "name": "天龙之子之徽", "desc": "获得1个【天龙之子纹章】、1个【迦娜】和1个【黛安娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Dragonlord_II.tex", "id": null, "associatedTraits": ["TFT11_Dragonlord"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_GhostlyCrest": {"apiName": "TFT11_Augment_GhostlyCrest", "name": "幽魂之徽", "desc": "获得1个【幽魂纹章】和1个【慎】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Ghostly_II.tex", "id": null, "associatedTraits": ["TFT11_Ghostly"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_ArcanistCrest": {"apiName": "TFT11_Augment_ArcanistCrest", "name": "法师之徽", "desc": "获得1个【法师纹章】和1个【佐伊】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Arcanist_II.tex", "id": null, "associatedTraits": ["TFT11_Arcanist"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_SageCrest": {"apiName": "TFT11_Augment_SageCrest", "name": "圣贤之徽", "desc": "获得1个【圣贤纹章】、1个【婕拉】和1个【黛安娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sage_II.tex", "id": null, "associatedTraits": ["TFT11_Sage"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_AltruistCrest": {"apiName": "TFT11_Augment_AltruistCrest", "name": "武仙子之徽", "desc": "获得1个【武仙子纹章】和1个【锐雯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Altruist_II.tex", "id": null, "associatedTraits": ["TFT11_Altruist"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_FatedCrest": {"apiName": "TFT11_Augment_FatedCrest", "name": "灵魂莲华之徽", "desc": "获得1个【灵魂莲华纹章】和1个【千珏】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Fated_II.tex", "id": null, "associatedTraits": ["TFT11_Fated"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_ReaperCrest": {"apiName": "TFT11_Augment_ReaperCrest", "name": "死神之徽", "desc": "获得1个【死神纹章】和1个【千珏】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Reaper_II.tex", "id": null, "associatedTraits": ["TFT11_Reaper"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_SniperCrest": {"apiName": "TFT11_Augment_SniperCrest", "name": "狙神之徽", "desc": "获得1个【狙神纹章】 和1个【厄斐琉斯】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sniper_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT11_Sniper"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_WardenCrest": {"apiName": "TFT11_Augment_WardenCrest", "name": "护卫之徽", "desc": "获得1个【护卫纹章】、1个【贾克斯】和1个【阿木木】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Warden_II.tex", "id": null, "associatedTraits": ["TFT11_Warden"], "effects": {}, "composition": [], "from": null}, "TFT11_Augment_InvokerCrest": {"apiName": "TFT11_Augment_InvokerCrest", "name": "神谕者之徽", "desc": "获得1个【神谕者纹章】和1个【迦娜】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Invoker_II.TFT_Set5_5_Revival.tex", "id": null, "associatedTraits": ["TFT11_Invoker"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_ProtectorCrest": {"apiName": "TFT15_Augment_ProtectorCrest", "name": "圣盾使之徽", "desc": "获得1个【圣盾使纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Protector_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Protector"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_BastionCrest": {"apiName": "TFT15_Augment_BastionCrest", "name": "护卫之徽", "desc": "获得1个【护卫纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Bastion_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Bastion"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_RingKingCrest": {"apiName": "TFT15_Augment_RingKingCrest", "name": "假面摔角手之徽", "desc": "获得1个【假面摔角手纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Luchador_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Luc<PERSON>or"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_StarGuardianCrest": {"apiName": "TFT15_Augment_StarGuardianCrest", "name": "星之守护者之徽", "desc": "获得1个【星之守护者纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_StarGuardian_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_<PERSON><PERSON><PERSON><PERSON>"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_JuggernautCrest": {"apiName": "TFT15_Augment_JuggernautCrest", "name": "主宰之徽", "desc": "获得1个【主宰纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Juggernaut_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Juggernaut"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_CrystalRoseCrest": {"apiName": "TFT15_Augment_CrystalRoseCrest", "name": "水晶玫瑰之徽", "desc": "获得1个【水晶玫瑰纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_CrystalGambit_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_GemForce"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_SpellslingerCrest": {"apiName": "TFT15_Augment_SpellslingerCrest", "name": "法师之徽", "desc": "获得1个【法师纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sorcerer_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Spellslinger"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_ChallengerCrest": {"apiName": "TFT15_Augment_ChallengerCrest", "name": "决斗大师之徽", "desc": "获得1个【决斗大师纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Duelist_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Duelist"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_EmpyreanCrest": {"apiName": "TFT15_Augment_EmpyreanCrest", "name": "至高天之徽", "desc": "获得1个【至高天纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Wraith_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Empyrean"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_EdgelordCrest": {"apiName": "TFT15_Augment_EdgelordCrest", "name": "刀锋领主之徽", "desc": "获得1个【刀锋领主纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Edgelord_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Edgelord"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_HeavyweightCrest": {"apiName": "TFT15_Augment_HeavyweightCrest", "name": "重量级斗士之徽", "desc": "获得1个【重量级斗士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Heavyweight_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Heavyweight"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_ProdigyCrest": {"apiName": "TFT15_Augment_ProdigyCrest", "name": "天才之徽", "desc": "获得1个【天才纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Prodigy_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Prodigy"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_BattleAcademiaCrest": {"apiName": "TFT15_Augment_BattleAcademiaCrest", "name": "战斗学院之徽", "desc": "获得1个【战斗学院纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_BattleAcademia_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_BattleAcademia"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_SentaiCrest": {"apiName": "TFT15_Augment_SentaiCrest", "name": "超级战队之徽", "desc": "获得1个【超级战队纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Missing-T2.tex", "id": null, "associatedTraits": ["TFT15_SentaiRanger"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_StrategistCrest": {"apiName": "TFT15_Augment_StrategistCrest", "name": "司令之徽", "desc": "获得1个【司令纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/StrategistCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT15_Strategist"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_DestroyerCrest": {"apiName": "TFT15_Augment_DestroyerCrest", "name": "裁决使者之徽", "desc": "获得1个【裁决使者纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Executioner_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Destroyer"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_SniperCrest": {"apiName": "TFT15_Augment_SniperCrest", "name": "狙神之徽", "desc": "获得1个【狙神纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Sniper_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_Sniper"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_SoulFighterCrest": {"apiName": "TFT15_Augment_SoulFighterCrest", "name": "斗魂战士之徽", "desc": "获得1个【斗魂战士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_SoulFighter_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_SoulFighter"], "effects": {}, "composition": [], "from": null}, "TFT15_Augment_SupremeCellsCrest": {"apiName": "TFT15_Augment_SupremeCellsCrest", "name": "兵王之徽", "desc": "获得1个【兵王纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_SupremeCells_II.TFT_Set15.tex", "id": null, "associatedTraits": ["TFT15_SupremeCells"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_ArmorcladCrest": {"apiName": "TFT14_Augment_ArmorcladCrest", "name": "堡垒卫士之徽", "desc": "获得1个【堡垒卫士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/ArmorcladCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Armorclad"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_TechieCrest": {"apiName": "TFT14_Augment_TechieCrest", "name": "高级工程师之徽", "desc": "获得1个【高级工程师纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/TechieCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Techie"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_InvokerCrest": {"apiName": "TFT14_Augment_InvokerCrest", "name": "人造人之徽", "desc": "获得1个【人造人纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/InvokerCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Thirsty"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_DuelistCrest": {"apiName": "TFT14_Augment_DuelistCrest", "name": "迅捷射手之徽", "desc": "获得1个【迅捷射手纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/DuelistCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Swift"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_AnimaSquadCrest": {"apiName": "TFT14_Augment_AnimaSquadCrest", "name": "幻灵战队之徽", "desc": "获得1个【幻灵战队纹章 】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/AnimaSquadCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_AnimaSquad"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_ExecutionerCrest": {"apiName": "TFT14_Augment_ExecutionerCrest", "name": "裁决使之徽", "desc": "获得1个【裁决使纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/ExecutionerCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Cutter"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_StreetDemonCrest": {"apiName": "TFT14_Augment_StreetDemonCrest", "name": "街头恶魔之徽", "desc": "获得1个【街头恶魔纹章】。", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/Traits/Spatula/Set14/TFT14_Emblem_StreetDemon.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_StreetDemon"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_BruiserCrest": {"apiName": "TFT14_Augment_BruiserCrest", "name": "斗士之徽", "desc": "获得1个【斗士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/BruiserCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Bruiser"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_GoldenOxCrest": {"apiName": "TFT14_Augment_GoldenOxCrest", "name": "福牛守护者之徽", "desc": "获得1个【福牛守护者纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/GoldenOxCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Immortal"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_DivinicorpCrest": {"apiName": "TFT14_Augment_DivinicorpCrest", "name": "圣灵使者之徽", "desc": "获得1个【圣灵使者纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/DivinicorpCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Divinicorp"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_CryptoCrest": {"apiName": "TFT14_Augment_CryptoCrest", "name": "执事之徽", "desc": "获得1个【执事纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/CryptoCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Suits"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_ExotechCrest": {"apiName": "TFT14_Augment_ExotechCrest", "name": "源计划之徽", "desc": "获得1个【源计划纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/ExotechCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_EdgeRunner"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_SlayerCrest": {"apiName": "TFT14_Augment_SlayerCrest", "name": "杀手之徽", "desc": "获得1个【杀手纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/SlayerCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Strong"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_StrategistCrest": {"apiName": "TFT14_Augment_StrategistCrest", "name": "战略分析师之徽", "desc": "获得1个【战略分析师纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/StrategistCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Controller"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_MarksmanCrest": {"apiName": "TFT14_Augment_MarksmanCrest", "name": "强袭射手之徽", "desc": "获得1个【强袭射手纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/MarksmanCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_<PERSON>man"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_VanguardCrest": {"apiName": "TFT14_Augment_VanguardCrest", "name": "重装战士之徽", "desc": "获得1个【重装战士纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/Crest_Vanguard_II.TFT_Set12.tex", "id": null, "associatedTraits": ["TFT14_Vanguard"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_MobCrest": {"apiName": "TFT14_Augment_MobCrest", "name": "辛迪加之徽", "desc": "获得1个【辛迪加纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/MobCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_Mob"], "effects": {}, "composition": [], "from": null}, "TFT14_Augment_BoomBotsCrest": {"apiName": "TFT14_Augment_BoomBotsCrest", "name": "战地机甲之徽", "desc": "获得1个【战地机甲纹章】。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/BoomBotsCrest_II.TFT_Set14.tex", "id": null, "associatedTraits": ["TFT14_BallisTek"], "effects": {}, "composition": [], "from": null}, "TFT_Item_CrestOfCinders": {"apiName": "TFT_Item_CrestOfCinders", "name": "余烬之冠", "desc": "在战斗开始时，携带者和同一排格内的所有友军获得【余烬之冠】。带有【余烬之冠】的弈子们会强化其攻击来灼烧目标%最大生命值的真实伤害并施加33%重伤。[唯一 - 每位英雄仅能装备1件]", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/TFT9_SupportItems/Crest_Of_Cinders.tex", "id": null, "associatedTraits": [], "effects": {"BurnAmount": 0.07999999821186066, "BurnDuration": 8.0, "Health": 250.0, "HexRange": 1.0, "MaxHPHeal": 2.0, "{ff22d532}": 2.0}, "composition": [], "from": null}, "TFT_Item_Artifact_LightshieldCrest": {"apiName": "TFT_Item_Artifact_LightshieldCrest", "name": "光盾徽章", "desc": "每秒，为生命值百分比最低的那个友军提供护盾，护盾值相当于携带者%的护甲与魔抗总和，持续秒。在阵亡时，将这个护盾效果提供给所有友军。", "icon": "ASSETS/Maps/TFT/Icons/Items/Hexcore/TFT_Item_Artifact_LightshieldCrest.TFT_Set13.tex", "id": null, "associatedTraits": [], "effects": {"Armor": 55.0, "MagicResist": 55.0, "PercentOfResists": 70.0, "ShieldDuration": 5.0, "TriggerRate": 3.0}, "composition": [], "from": null}, "TFT7_Augment_TomeOfTraits2": {"apiName": "TFT7_Augment_TomeOfTraits2", "name": "古代档案 II", "desc": "提供个【纹章之书】和金币。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/AncientArchives3.tex", "id": null, "associatedTraits": [], "effects": {"Gold": 5.0, "NumTomes": 2.0}, "composition": [], "from": null}, "TFT6_Augment_TomeOfTraits1": {"apiName": "TFT6_Augment_TomeOfTraits1", "name": "古代档案 I", "desc": "提供个【纹章之书】和金币。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/AncientArchives2.tex", "id": null, "associatedTraits": [], "effects": {"Gold": 3.0, "NumTomes": 1.0}, "composition": [], "from": null}, "TFT6_Augment_Traitless2": {"apiName": "TFT6_Augment_Traitless2", "name": "卓尔不群", "desc": "你的弈子们只要不具有任何已激活的羁绊，就会获得-生命值和-%攻击速度（基于当前阶段）。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/BuiltDifferent2.tex", "id": null, "associatedTraits": [], "effects": {"AS1": 45.0, "AS2": 55.0, "AS3": 65.0, "AS4": 75.0, "HP1": 300.0, "HP2": 400.0, "HP3": 500.0, "HP4": 600.0}, "composition": [], "from": null}, "TFT6_Augment_Traitless3": {"apiName": "TFT6_Augment_Traitless3", "name": "卓尔不群 III", "desc": "你的弈子们只要不具有任何已激活的羁绊，就会获得-生命值和-%攻击速度（基于当前阶段）。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/BuiltDifferent3.tex", "id": null, "associatedTraits": [], "effects": {"AS1": 50.0, "AS2": 55.0, "AS3": 60.0, "AS4": 70.0, "HP1": 300.0, "HP2": 400.0, "HP3": 500.0, "HP4": 600.0}, "composition": [], "from": null}, "TFT6_Augment_Traitless1": {"apiName": "TFT6_Augment_Traitless1", "name": "卓尔不群 I", "desc": "你的弈子们只要不具有任何已激活的羁绊，就会获得-生命值和-%攻击速度（基于当前阶段）。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/BuiltDifferent1.tex", "id": null, "associatedTraits": [], "effects": {"AS1": 25.0, "AS2": 35.0, "AS3": 45.0, "AS4": 55.0, "HP1": 200.0, "HP2": 225.0, "HP3": 250.0, "HP4": 300.0}, "composition": [], "from": null}, "TFT_Augment_TraitTracker": {"apiName": "TFT_Augment_TraitTracker", "name": "羁绊追踪者", "desc": "你在首次激活个非唯一羁绊时，获得个纹章。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/TraitTracker_II.TFT_Set12.tex", "id": null, "associatedTraits": [], "effects": {"NumEmblems": 5.0, "NumTraits": 8.0}, "composition": [], "from": null}, "TFT6_Augment_Legend_TomeOfTraits1": {"apiName": "TFT6_Augment_Legend_TomeOfTraits1", "name": "古代档案 I", "desc": "提供个【纹章之书】和金币。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/AncientArchives2.tex", "id": null, "associatedTraits": [], "effects": {"Gold": 3.0, "NumTomes": 1.0}, "composition": [], "from": null}, "TFT7_Augment_Legend_TomeOfTraits2": {"apiName": "TFT7_Augment_Legend_TomeOfTraits2", "name": "古代档案 II", "desc": "提供个【纹章之书】和金币。", "icon": "ASSETS/Maps/TFT/Icons/Augments/Hexcore/AncientArchives3.tex", "id": null, "associatedTraits": [], "effects": {"Gold": 5.0, "NumTomes": 2.0}, "composition": [], "from": null}}}