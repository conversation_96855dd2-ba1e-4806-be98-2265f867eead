{"type": "traits", "version": "15.14.1", "set": "15", "language": "zh_CN", "count": 151, "data": {"TFT15_Destroyer": {"apiName": "TFT15_Destroyer", "name": "裁决使者", "desc": "【裁决使者】获得暴击几率和暴击伤害。其技能可以暴击。() % %i:scaleCrit%；% %i:scaleCritMult%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_Destroyer.TFT_Set15.tex", "effects": [{"maxUnits": 2, "minUnits": 2, "style": 1, "variables": {"CritAmpPercent": 10.0, "CritChanceAmpPercent": 25.0}}, {"maxUnits": 3, "minUnits": 3, "style": 3, "variables": {"CritAmpPercent": 12.0, "CritChanceAmpPercent": 35.0}}, {"maxUnits": 4, "minUnits": 4, "style": 3, "variables": {"CritAmpPercent": 18.0, "CritChanceAmpPercent": 50.0}}, {"maxUnits": 25000, "minUnits": 5, "style": 5, "variables": {"CritAmpPercent": 28.0, "CritChanceAmpPercent": 55.0}}]}, "TFT15_MechanicTrait_StarStudent": {"apiName": "TFT15_MechanicTrait_StarStudent", "name": "明星学生", "desc": "() 获得生命值和+%潜能%i:set14AmpIcon%() 每回合获得经验值，并且每个存活下来的【明星学生】提供额外的经验值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"BaseHealth": 200.0, "PctAdditionalPotential": 40}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"BaseHealth": 200.0, "BaseXP": 1, "PctAdditionalPotential": 40, "{fa5c946a}": 1}}]}, "TFT15_MechanicTrait_InfernalSpeed": {"apiName": "TFT15_MechanicTrait_InfernalSpeed", "name": "炼狱速度", "desc": "获得%攻击速度。参与击杀时，生成一个【炼狱熔渣】。每个【炼狱熔渣】为己方弈子提供%额外%i:scaleAS%。(炼狱熔渣数：&nbsp;；团队&nbsp;%i:scaleAS%：&nbsp;%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BaseAS": 10.0, "CinderAS": 2.0, "NumCinders": 8.0}}]}, "TFT15_MechanicTrait_Unstoppable": {"apiName": "TFT15_MechanicTrait_Unstoppable", "name": "不可阻挡", "desc": "获得生命值，并免疫控制效果。朝新目标冲锋，晕眩途经的敌人秒。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Health": 300, "StunDuration": 1.0}}]}, "TFT15_DragonFist": {"apiName": "TFT15_DragonFist", "name": "龙的传人", "desc": "在你登场【李青】时，可在【决斗大师】形态、【裁决使者】形态和【主宰】形态之间选择一个！每个形态都有一个独特技能，并且会为【李青】提供相关的羁绊。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_DragonFist.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 4, "variables": {"{4ac719b4}": 8.0}}]}, "TFT15_MechanicTrait_MagicExpert": {"apiName": "TFT15_MechanicTrait_MagicExpert", "name": "魔法专家", "desc": "获得%法术加成。所有其它法术加成来源额外提供%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"BaseAP": 10.0, "PctBonusAP": 30.0}}]}, "TFT15_MechanicTrait_Mastermind": {"apiName": "TFT15_MechanicTrait_Mastermind", "name": "首脑", "desc": "战斗开始时：获得法力值，并给予这个弈子前面一条直线的所有友军法力值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AllyMana": 25, "SelfishMana": 50}}]}, "TFT15_Edgelord": {"apiName": "TFT15_Edgelord", "name": "刀锋领主", "desc": "【刀锋领主】获得全能吸血和物理加成。在攻击生命值低于%的敌人时，还会获得%攻击速度。() % %i:scaleSV%，% %i:scaleAD%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_10_Edgelord.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"AD": 0.15000000596046448, "BonusAS": 0.4000000059604645, "HealthThreshold": 0.5, "Omnivamp": 0.10000000149011612}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"AD": 0.3499999940395355, "BonusAS": 0.4000000059604645, "HealthThreshold": 0.5, "Omnivamp": 0.11999999731779099}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"AD": 0.5, "BonusAS": 0.4000000059604645, "HealthThreshold": 0.5, "Omnivamp": 0.15000000596046448}}]}, "TFT15_GemForce": {"apiName": "TFT15_GemForce", "name": "水晶玫瑰", "desc": "在玩家对战期间的击杀和失败会赚取【水晶能量】。每过场玩家对战，可选择将【水晶能量】转化为奖励或者“双倍投入”。在“双倍投入”处于激活状态时，失败会提供额外的%【水晶能量】，但胜利会损失%【水晶能量】并立刻提现。【水晶能量】的获取量会在第3阶段和第4阶段提升。() 每次击杀；每次失败() 以及每次击杀，获得额外战利品并重随一个新的击杀数目。(击杀数：&nbsp;)() %奖励。【水晶玫瑰】弈子们获得 %i:scaleHealth%和% %i:scaleDA%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_GemForce.TFT_Set15.tex", "effects": [{"maxUnits": 4, "minUnits": 3, "style": 1, "variables": {"7PeiceMult": 1.5, "7PieceAmp": 0.25, "7PieceHealth": 300.0, "GPPerDeathPVE": 2.0, "GPPerRoundPVE": 10.0, "NumCombats": 4, "NumCombatsPVE": 4, "PercentIncreaseGPPVE": 1.0, "PercentIncreasedGP": 1.25, "PercentLossPVE": 0.5, "PercentLost": 0.5, "{0c71c65f}": 1.0, "{4d6b4df4}": 2, "{83265d73}": 20, "{ce703dbd}": 1.149999976158142, "{d45997e0}": 1.350000023841858}}, {"maxUnits": 6, "minUnits": 5, "style": 5, "variables": {"7PeiceMult": 1.5, "7PieceAmp": 0.25, "7PieceHealth": 300.0, "GPPerDeathPVE": 3.0, "GPPerRoundPVE": 15.0, "NumCombats": 4, "NumCombatsPVE": 4, "PercentIncreaseGPPVE": 1.0, "PercentIncreasedGP": 1.25, "PercentLossPVE": 0.5, "PercentLost": 0.5, "{0c71c65f}": 1.0, "{4d6b4df4}": 2, "{83265d73}": 20, "{ce703dbd}": 1.149999976158142, "{d45997e0}": 1.350000023841858}}, {"maxUnits": 9, "minUnits": 7, "style": 5, "variables": {"7PeiceMult": 1.5, "7PieceAmp": 0.25, "7PieceHealth": 300.0, "NumCombats": 4, "NumCombatsPVE": 4, "PercentIncreaseGPPVE": 1.0, "PercentIncreasedGP": 1.25, "PercentLossPVE": 0.5, "PercentLost": 0.5, "{0c71c65f}": 1.0, "{4d6b4df4}": 2, "{83265d73}": 20, "{ce703dbd}": 1.149999976158142, "{d45997e0}": 1.350000023841858}}, {"maxUnits": 25000, "minUnits": 10, "style": 6, "variables": {"7PeiceMult": 1.5, "7PieceAmp": 0.25, "7PieceHealth": 300.0, "DamageReduction": 0.9900000095367432, "NumCombats": 4, "NumCombatsPVE": 4, "PercentIncreaseGPPVE": 1.0, "PercentIncreasedGP": 1.25, "PercentLossPVE": 0.5, "PercentLost": 0.5, "{0c71c65f}": 1.0, "{4d6b4df4}": 2, "{83265d73}": 20, "{ad97aeef}": 500.0, "{ce703dbd}": 1.149999976158142, "{d45997e0}": 1.350000023841858}}]}, "TFT15_MechanicTrait_BestDefense": {"apiName": "TFT15_MechanicTrait_BestDefense", "name": "最好的防御", "desc": "当提供或收到护盾时，下次攻击增加相当于%该护盾值的额外物理伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentDamage": 1.5}}]}, "TFT15_MechanicTrait_Health": {"apiName": "TFT15_MechanicTrait_Health", "name": "最大活力", "desc": "战斗开始时，获得永久生命值。该弈子每存活秒，获得额外的永久生命值。(当前：&nbsp;%i:scaleHealth%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Amount": 10.0, "Interval": 10.0, "PerCombatAmount": 35.0}}]}, "TFT15_MechanicTrait_BuiltDifferent": {"apiName": "TFT15_MechanicTrait_BuiltDifferent", "name": "平平无奇", "desc": "如果这个弈子的各个羁绊都不处于激活状态，就会获得-生命值和-%攻击速度(基于当前阶段)。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AS1": 20.0, "AS2": 35.0, "AS3": 50.0, "AS4": 65.0, "HP1": 300.0, "HP2": 400.0, "HP3": 600.0, "HP4": 800.0, "Multiplier": 0.20000000298023224}}]}, "TFT15_MechanicTrait_TitanForm": {"apiName": "TFT15_MechanicTrait_TitanForm", "name": "巨像", "desc": "占用2个弈子栏位。获得生命值、伤害减免、和伤害增幅。()  %i:scaleHealth%；% %i:scaleDR%；% %i:scaleDA%。()  %i:scaleHealth%；% %i:scaleDR%；% %i:scaleDA%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"BonusDA": 0.10000000149011612, "BonusDR": 0.20000000298023224, "BonusHealth": 1000.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"BonusDA": 0.30000001192092896, "BonusDR": 0.25, "BonusHealth": 1400.0}}]}, "TFT15_MechanicTrait_RobustRanger": {"apiName": "TFT15_MechanicTrait_RobustRanger", "name": "机器连者", "desc": "获得%【超级机甲】的物理加成和法术加成，和其%生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"HealthPct": 10.0, "StatsPct": 35.0}}]}, "TFT15_MechanicTrait_Attack": {"apiName": "TFT15_MechanicTrait_Attack", "name": "最大攻击", "desc": "获得%物理加成，并且每2次参与击杀永久获得%。(当前：%&nbsp;%i:scaleAD%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Amount": 0.009999999776482582, "Base": 0.11999999731779099}}]}, "TFT15_MechanicTrait_AllOut": {"apiName": "TFT15_MechanicTrait_AllOut", "name": "傲岸雄姿", "desc": "战斗开始时进入全盛姿态，获得%伤害减免和%物理加成。全盛姿态下不再衰减生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AD": 0.5, "Durability": 0.20000000298023224}}]}, "TFT15_MechanicTrait_GoldenEdge": {"apiName": "TFT15_MechanicTrait_GoldenEdge", "name": "金色锋刃", "desc": "攻击次提供金币。这个金币值会在它每次激活时提升1。(攻击次数： / )", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"NumAttacksPerProc": 85.0}}]}, "TFT15_MechanicTrait_MetabolicExplosion": {"apiName": "TFT15_MechanicTrait_MetabolicExplosion", "name": "勃勃生机", "desc": "每秒治疗%最大生命值。从所有来源获得%治疗提升。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"IncreasedHealing": 10.0, "Interval": 3.0, "PctRegen": 6.5}}]}, "TFT15_MechanicTrait_Unflinching": {"apiName": "TFT15_MechanicTrait_Unflinching", "name": "不可屈服", "desc": "在战斗的最初秒获得%全能吸血和控制免疫。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Duration": 15.0, "OmnivampPct": 15.0}}]}, "TFT15_MechanicTrait_StretchyArms": {"apiName": "TFT15_MechanicTrait_StretchyArms", "name": "延展手臂", "desc": "获得%攻击速度。参与击杀后，获得+1攻击距离。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BaseAS": 0.10000000149011612}}]}, "TFT15_MechanicTrait_ExilesEdge": {"apiName": "TFT15_MechanicTrait_ExilesEdge", "name": "血亲兄弟", "desc": "【永恩】和【亚索】造成的伤害会使敌人流血，在秒里持续造成%的造成伤害值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BleedDuration": 2.0, "BleedPct": 20.0}}]}, "TFT15_MechanicTrait_HerosArc": {"apiName": "TFT15_MechanicTrait_HerosArc", "name": "英雄弧光", "desc": "每玩家等级获得%伤害增幅。在10级时，获得额外的%伤害增幅。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DA": 0.04500000178813934, "DamageIncrease": 0.3499999940395355}}]}, "TFT15_MechanicTrait_PackTactics": {"apiName": "TFT15_MechanicTrait_PackTactics", "name": "狗海战术", "desc": "获得个狗群伙伴，狗群伙伴可造成%&nbsp;(%i:TFTBaseAD%)物理伤害。当【纳亚菲利】施放技能时，狗群伙伴们会冲刺至格内生命值最低的那个敌人。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DamagePercent": 0.15000000596046448, "NumHexes": 2, "NumPackmates": 2}}]}, "TFT15_MechanicTrait_RisingChaos": {"apiName": "TFT15_MechanicTrait_RisingChaos", "name": "混沌渐起", "desc": "施放时，发射一个额外法球来对一个随机敌人造成%伤害。后续施放增加一颗额外的法球。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentDamage": 0.05999999865889549}}]}, "TFT15_MechanicTrait_MidasTouch": {"apiName": "TFT15_MechanicTrait_MidasTouch", "name": "点金手", "desc": "普攻和技能将处决生命值低于%的敌人。击杀有%几率掉落1金币。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentChanceGold": 25.0, "PercentHealthThreshold": 10.0}}]}, "TFT15_Sniper": {"apiName": "TFT15_Sniper", "name": "狙神", "desc": "【狙神】们获得伤害增幅，在对抗更远目标时获得提升。() %&nbsp;%i:scaleDA%；+%&nbsp;%i:scaleDA%每格() %&nbsp;%i:scaleDA%；+%&nbsp;%i:scaleDA%每格() %&nbsp;%i:scaleDA%；+%&nbsp;%i:scaleDA%每格() %&nbsp;%i:scaleDA%；+%&nbsp;%i:scaleDA%每格", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_6_Sniper.tex", "effects": [{"maxUnits": 2, "minUnits": 2, "style": 1, "variables": {"BonusHexRangeIncrease": null, "PerHexIncrease": 3.0, "PercentDamageIncrease": 13.0}}, {"maxUnits": 3, "minUnits": 3, "style": 3, "variables": {"BonusHexRangeIncrease": null, "PerHexIncrease": 5.0, "PercentDamageIncrease": 16.0}}, {"maxUnits": 4, "minUnits": 4, "style": 3, "variables": {"BonusHexRangeIncrease": null, "PerHexIncrease": 7.0, "PercentDamageIncrease": 22.0}}, {"maxUnits": 25000, "minUnits": 5, "style": 5, "variables": {"BonusHexRangeIncrease": null, "PerHexIncrease": 10.0, "PercentDamageIncrease": 25.0}}]}, "TFT15_StarGuardian": {"apiName": "TFT15_<PERSON><PERSON><PERSON><PERSON>", "name": "星之守护者", "desc": "【星之守护者】们各有一个独特团队加成，会提供给所有【星之守护者】。每个已登场的【星之守护者】都会提升这个加成！棱彩条件：登场(8)，然后花费法力值。棱彩条件：花费法力值 ()群。星。觉。醒。【芮尔】：获得护盾值【芮尔】：获得护盾值【辛德拉】：每秒获得法术加成【辛德拉】：获得法术加成【霞】：每第3次攻击造成 (每个阶段获得提升)魔法伤害【霞】： 攻击附带魔法伤害【阿狸】：施放后，在秒里持续获得法力值【阿狸】：施放时，获得法力值【妮蔻】： +%治疗和护盾效果【妮蔻】：提升治疗和护盾效果【波比】：在40%生命值，治疗% %i:scaleHealth%。【波比】： 在低生命值时获得治疗【金克丝】： +%攻击速度，参与击杀后+% %i:scaleAS%，3秒里持续衰减。【金克丝】：获得攻击速度【萨勒芬妮】获得 %i:scaleArmor%%i:scaleMR%%i:scaleHealth%和% %i:scaleAD%%i:scaleAS%%i:scaleCrit%%i:scaleCritMult%【萨勒芬妮】：获得每种属性纹章：将各个加成提升%！纹章：提升其它各个加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_8_StarGuardian.tex", "effects": [{"maxUnits": 2, "minUnits": 2, "style": 1, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.0, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 3, "minUnits": 3, "style": 3, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.100000023841858, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 4, "minUnits": 4, "style": 3, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.2000000476837158, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 5, "minUnits": 5, "style": 3, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.2999999523162842, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 6, "minUnits": 6, "style": 5, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.4500000476837158, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 7, "minUnits": 7, "style": 5, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.5299999713897705, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 8, "minUnits": 8, "style": 5, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.600000023841858, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 8, "minUnits": 8, "style": 6, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.600000023841858, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 9, "minUnits": 9, "style": 5, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.7000000476837158, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 9, "minUnits": 9, "style": 6, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 1.7000000476837158, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 25000, "minUnits": 10, "style": 5, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 2.0, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}, {"maxUnits": 25000, "minUnits": 10, "style": 6, "variables": {"AhriDuration": 2.0, "EmblemBonus": 0.10000000149011612, "ManaToPrismatic": 20000.0, "Multiplier": 2.0, "SyndraTimer": 3.0, "{02914815}": 0.10000000149011612, "{05ce3a8e}": 3.0, "{1bb9c993}": 60.0, "{39509739}": 0.05000000074505806, "{40cdc45f}": 2.0, "{53e50c19}": 0.07000000029802322, "{5416a34e}": 50, "{664e2968}": null, "{7dbabf3e}": 200, "{89a442d6}": null, "{98cfb4a4}": 750.0, "{99ff11e5}": 5.0, "{9db0387d}": 0.25, "{a085bd04}": 500, "{ca26eef7}": 0.15000000596046448}}]}, "TFT15_MechanicTrait_Mage": {"apiName": "TFT15_MechanicTrait_Mage", "name": "魔术师", "desc": "双重施放技能，但造成的总伤害降低%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DAMultiplier": 0.25}}]}, "TFT15_MechanicTrait_SolarBreath": {"apiName": "TFT15_MechanicTrait_SolarBreath", "name": "阳炎吐息", "desc": "每秒，对相距最近且未被灼烧的那个敌人施加持续秒的灼烧和重伤效果。对被灼烧敌人造成%额外伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"DamageAmp": 0.10000000149011612, "Duration": 10, "HexRange": 12, "ICD": 2.0}}]}, "TFT15_MechanicTrait_Precision": {"apiName": "TFT15_MechanicTrait_Precision", "name": "精准", "desc": "设置攻击速度至。将%攻击速度转化为%物理加成。攻击造成额外的%伤害，并提供额外的法力值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ADScale": 0.00800000037997961, "ASScale": 0.009999999776482582, "AttackAmount": 1.2999999523162842, "ForcedAttackSpeed": 0.699999988079071, "Mana": 10.0}}]}, "TFT15_MechanicTrait_IceBender": {"apiName": "TFT15_MechanicTrait_IceBender", "name": "冰霜统御", "desc": "战斗开始时：【瑞兹】在战斗的最初秒处于冰封状态。他的技能造成持续秒的%冰冷效果，并且对目标邻格的敌人们造成%额外伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Chill": 20, "ChillDuration": 4.0, "Duration": 3.0, "PctSpellDamage": 0.20000000298023224}}]}, "TFT15_MechanicTrait_100PushUps": {"apiName": "TFT15_MechanicTrait_100PushUps", "name": "100个俯卧撑", "desc": "获得%伤害减免。你每花费金币刷新次时，获得%永久伤害增幅。(当前：%&nbsp;%i:scaleDA%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DAPerReroll": 0.009999999776482582, "DR": 0.15000000596046448, "NumRerolls": 3.0, "{0f631073}": null, "{7720bbfe}": null}}]}, "TFT15_MechanicTrait_HeartOfGold": {"apiName": "TFT15_MechanicTrait_HeartOfGold", "name": "黄金之心", "desc": "如果该弈子在玩家对战回合结束后仍然存活，获得金币。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"NumGold": 2.0}}]}, "TFT15_MechanicTrait_KaijuSize": {"apiName": "TFT15_MechanicTrait_KaijuSize", "name": "哥斯拉坦克", "desc": "获得%最大生命值并且体型变大。在秒后，将所有敌人晕眩1.5秒。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Countdown": 15.0, "HealthRatio": 0.20000000298023224}}]}, "TFT15_MechanicTrait_FighterSpirit": {"apiName": "TFT15_MechanicTrait_FighterSpirit", "name": "战士之魄", "desc": "() 每登场一个战士或刺客，就会获得 生命值、%物理加成和%法术加成。 () 提升至 %i:scaleHealth%、% %i:scaleAD%、和% %i:scaleAP%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ADAP": 5.0, "Health": 75.0}}]}, "TFT15_MechanicTrait_AcePilot": {"apiName": "TFT15_MechanicTrait_AcePilot", "name": "太空王牌", "desc": "这个弈子造成的伤害会贡献额外%进度至【奥德赛星舰】的导弹发射。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"percent": 0.75}}]}, "TFT15_MechanicTrait_Weights": {"apiName": "TFT15_MechanicTrait_Weights", "name": "负重", "desc": "获得【负重】。在1以上【负重】时，移动和攻击减慢%。玩家对战回合胜利时降低【负重】，失败时降低2【负重】。在0【负重】时，移动得更快，并获得%攻击速度和%伤害减免。(剩余负重：)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AS": 0.6000000238418579, "Durability": 0.18000000715255737, "NumWeights": 10, "PercentReduction": 0.5, "WeightPerCombat": 1}}]}, "TFT15_Heavyweight": {"apiName": "TFT15_Heavyweight", "name": "重量级斗士", "desc": "己方弈子获得生命值。【重量级斗士】获得额外生命值，以及相当于其一部分生命值的物理加成。() % %i:scaleHealth% | % %i:scaleHealth%的%i:scaleAD%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_Heavyweight.TFT_Set15.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"BonusPercentHealth": 0.20000000298023224, "HealthPercentToAD": 0.20000000298023224, "TeamBonusHealth": 100.0}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"BonusPercentHealth": 0.4000000059604645, "HealthPercentToAD": 0.4000000059604645, "TeamBonusHealth": 100.0}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"BonusPercentHealth": 0.550000011920929, "HealthPercentToAD": 0.6000000238418579, "TeamBonusHealth": 100.0}}]}, "TFT15_MechanicTrait_SpikyShell": {"apiName": "TFT15_MechanicTrait_SpikyShell", "name": "锥刺护甲", "desc": "获得护甲。攻击该弈子的敌人受到(+x阶段数)物理伤害。(每个攻击者有秒冷却时间)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Armor": 40.0, "Damage": 25.0, "DamagePerStage": 5.0, "ICD": 1.0}}]}, "TFT15_MechanicTrait_Hyperactive": {"apiName": "TFT15_MechanicTrait_Hyperactive", "name": "嗨到不行", "desc": "参与击杀会提供在秒内急速衰减的%攻击速度。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AttackSpeed": 90.0, "DecayDuration": 3.0}}]}, "TFT15_TheCrew": {"apiName": "TFT15_TheCrew", "name": "奥德赛", "desc": "每登场一个【奥德赛】成员，【奥德赛】弈子们就会获得%生命值和攻击速度。每个3星【奥德赛】弈子都会提供一个额外加成。(1x %i:3StarEnabled%%i:3StarDisabled%)：每次付费刷新+经验值，并且【奥德赛】弈子们的出现概率绝对不会随着玩家等级的提升而下降。(2x %i:3StarEnabled%%i:3StarDisabled%)：每回合+1次免费刷新。(3x %i:3StarEnabled%%i:3StarDisabled%)：造成伤害即可发射一颗导弹，造成。(4x %i:3StarEnabled%%i:3StarDisabled%)：每玩家等级会使导弹的发射频率增加%。(5x %i:3StarEnabled%%i:3StarDisabled%)：发射【行星爆裂弹】。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_StarCrew.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}, {"maxUnits": 25000, "minUnits": 2, "style": 3, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}, {"maxUnits": 25000, "minUnits": 2, "style": 3, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}, {"maxUnits": 25000, "minUnits": 2, "style": 5, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}, {"maxUnits": 25000, "minUnits": 2, "style": 5, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}, {"maxUnits": 25000, "minUnits": 2, "style": 6, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"BaseDamageThreshold": 280.0, "BaseRocketDamage": 105.0, "RocketRatePerLevel": 0.02500000037252903, "StatBonus": 0.05000000074505806, "XPPerReroll": 1}}]}, "TFT15_MechanicTrait_MechPilot": {"apiName": "TFT15_MechanicTrait_MechPilot", "name": "机甲驾驶员", "desc": "() 驾驶【超级机甲】，为其提供一部分你的属性：% %i:scaleHealth%；% %i:scaleAD%%i:scaleAP%%i:scaleAS%。在%生命值时脱离。() 【超级机甲】受益于驾驶员的羁绊。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"HealthShare": 0.5, "MechHealth": 0.5, "OtherStatShare": 1.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"HealthShare": 0.5, "MechHealth": 0.5, "OtherStatShare": 1.0}}]}, "TFT15_MechanicTrait_CycloneRush": {"apiName": "TFT15_MechanicTrait_CycloneRush", "name": "飓风闪击", "desc": "获得攻击速度。战斗开始时，使对位的那个敌人失能秒。() % %i:scaleAS%。() % %i:scaleAS%。使两个敌人失能。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"ASBoost": 0.15000000596046448, "Duration": 3.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"ASBoost": 0.25, "Duration": 3.0}}]}, "TFT15_MechanicTrait_SkyPiercer": {"apiName": "TFT15_MechanicTrait_SkyPiercer", "name": "突破天际", "desc": "伤害对敌人施加持续秒的30%魔抗击碎与护甲击碎。对被施加了魔抗击碎与护甲击碎的敌人们造成%额外伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"DamageAmp": 0.07999999821186066, "Duration": 6}}]}, "TFT15_MechanicTrait_GatherForce": {"apiName": "TFT15_MechanicTrait_GatherForce", "name": "聚集元气", "desc": "施放技能时，获得可叠加的物理加成，相当于%法力花费。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ADPctFromMana": 40.0}}]}, "TFT15_MechanicTrait_KillerInstinct": {"apiName": "TFT15_MechanicTrait_KillerInstinct", "name": "杀手本能", "desc": "技能转而瞄准距离内生命值最低的那个敌人。获得法力回复。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ManaRegen": 3.0}}]}, "TFT15_MechanicTrait_Atomic": {"apiName": "TFT15_MechanicTrait_Atomic", "name": "原子能", "desc": "战斗开始时储存％最大生命值，并且每秒多储存%。阵亡时，基于已储存的生命值造成一个大范围的魔法伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentMaxHealthInitial": 10.0, "PercentMaxHealthPerSecond": 2.0, "{b2f29c6b}": 15.0, "{e29ace6a}": 1.75, "{ebd239ac}": 4.0}}]}, "TFT15_MechanicTrait_Selfish": {"apiName": "TFT15_MechanicTrait_Selfish", "name": "自私自利", "desc": "获得%伤害减免。治疗相当于所有其他友军%造成伤害值的生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DR": 10.0, "HealPct": 5.0}}]}, "TFT15_MechanicTrait_SoleFighter": {"apiName": "TFT15_MechanicTrait_SoleFighter", "name": "单打独斗", "desc": "如果该弈子是唯一登场的【斗魂战士】，其获得【(4) 斗魂战士】的加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {}}]}, "TFT15_MechanicTrait_StandStrong": {"apiName": "TFT15_MechanicTrait_StandStrong", "name": "孤身站场", "desc": "如果战斗开始时该弈子所在排无友军，获得%生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"HealthRatio": 0.3499999940395355}}]}, "TFT15_SupremeCells": {"apiName": "TFT15_SupremeCells", "name": "兵王", "desc": "上一场战斗中造成最多伤害的【兵王】变为【最强兵王】。【最强兵王】阵亡时，当前伤害最高的【兵王】将成为【最强兵王】。【兵王】获得伤害增幅。【最强兵王】会获得更多伤害增幅并处决生命值低于%&nbsp;的敌人。() % %i:scaleDA% | % %i:scaleDA%() % %i:scaleDA% | % %i:scaleDA%() % %i:scaleDA% | % %i:scaleDA%。获得第二个【最强兵王】", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SupremeCells.TFT_Set15.tex", "effects": [{"maxUnits": 2, "minUnits": 2, "style": 1, "variables": {"DamageAmp": 8.0, "OverlordDamageAmp": 12.0, "OverlordExecute": 0.10000000149011612, "{86bfdc37}": 1}}, {"maxUnits": 3, "minUnits": 3, "style": 3, "variables": {"DamageAmp": 18.0, "OverlordDamageAmp": 30.0, "OverlordExecute": 0.10000000149011612, "{86bfdc37}": 1}}, {"maxUnits": 25000, "minUnits": 4, "style": 5, "variables": {"DamageAmp": 28.0, "OverlordDamageAmp": 50.0, "OverlordExecute": 0.10000000149011612, "{86bfdc37}": 2}}]}, "TFT15_MechanicTrait_RoundTwo": {"apiName": "TFT15_MechanicTrait_RoundTwo", "name": "二周目", "desc": "在阵亡时，生成一个装备着同样装备的训练假人。其拥有该弈子%的生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentHealth": 85.0}}]}, "TFT15_MechanicTrait_Over9000": {"apiName": "TFT15_MechanicTrait_Over9000", "name": "战斗力超过9000", "desc": "在备战环节开始时，获得一个随机的永久属性加成。当前： +&nbsp;%i:scaleAD%，+&nbsp;%i:scaleAP%，+%&nbsp;%i:scaleAS%, +&nbsp;%i:scaleArmor%，+&nbsp;%i:scaleMR%，+&nbsp;%i:scaleHealth%，+&nbsp;%i:TFTManaRegen%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"AD": 0.03999999910593033, "AP": 4.0, "AS": 0.03999999910593033, "Armor": 4.0, "Health": 40.0, "MR": 4.0, "Mana": 1.0}}]}, "TFT15_MechanicTrait_Speed": {"apiName": "TFT15_MechanicTrait_Speed", "name": "最大速度", "desc": "获得%攻击速度，并且每3次参与击杀永久获得%。(当前：%&nbsp;%i:scaleAS%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Amount": 0.009999999776482582, "Base": 0.07999999821186066}}]}, "TFT15_MechanicTrait_KeenEye": {"apiName": "TFT15_MechanicTrait_KeenEye", "name": "锐利的眼", "desc": "攻击和技能无视敌人%的魔抗。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentIgnore": 40.0}}]}, "TFT15_SoulFighter": {"apiName": "TFT15_SoulFighter", "name": "斗魂战士", "desc": "【斗魂战士】们获得额外生命值，并且每秒获得物理加成和法术加成，至多至层。在最大层数时，造成额外真实伤害。棱彩条件：登场(8)，然后赢得场战斗。棱彩条件：在战斗中击败个玩家()最！大！斗！魂！能！量！() &nbsp;%i:scaleHealth%，%&nbsp;%i:scaleAD%%i:scaleAP%，+%伤害() &nbsp;%i:scaleHealth%，%&nbsp;%i:scaleAD%%i:scaleAP%，+%伤害() &nbsp;%i:scaleHealth%，%&nbsp;%i:scaleAD%%i:scaleAP%，+%伤害() &nbsp;%i:scaleHealth%，%&nbsp;%i:scaleAD%%i:scaleAP%，+%伤害", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SoulFighter.TFT_Set15.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"BonusTrueDamage": 0.10000000149011612, "FlatHealth": 120.0, "MaxStacks": 8.0, "PlayerDamageToPrismatic": 10, "StatsPerSecond": 0.009999999776482582}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"BonusTrueDamage": 0.1599999964237213, "FlatHealth": 240.0, "MaxStacks": 8.0, "PlayerDamageToPrismatic": 10, "StatsPerSecond": 0.019999999552965164}}, {"maxUnits": 7, "minUnits": 6, "style": 3, "variables": {"BonusTrueDamage": 0.2199999988079071, "FlatHealth": 450.0, "MaxStacks": 8.0, "PlayerDamageToPrismatic": 10, "StatsPerSecond": 0.029999999329447746}}, {"maxUnits": 25000, "minUnits": 8, "style": 5, "variables": {"BonusTrueDamage": 0.30000001192092896, "FlatHealth": 700.0, "MaxStacks": 8.0, "PlayerDamageToPrismatic": 10, "StatsPerSecond": 0.03999999910593033}}, {"maxUnits": 25000, "minUnits": 8, "style": 6, "variables": {"BonusTrueDamage": 0.30000001192092896, "FlatHealth": 700.0, "MaxStacks": 8.0, "PlayerDamageToPrismatic": 10, "StatsPerSecond": 0.03999999910593033}}]}, "TFT15_MechanicTrait_Bladenado": {"apiName": "TFT15_MechanicTrait_Bladenado", "name": "剑刃风暴", "desc": "普攻对处于目标的攻击距离内的敌人们造成%攻击力的物理伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentDamage": 0.20000000298023224}}]}, "TFT15_MechanicTrait_WonderTwins": {"apiName": "TFT15_MechanicTrait_WonderTwins", "name": "神奇双子", "desc": "() 如果你登场了2个或以上的相同弈子，他们都会获得%的一种属性(基于角色定位)。如果你将其升到3星，获得一个2星的相同弈子。() %额外属性！", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"Stats": 40.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"NumItems": null, "Stats": 60.0}}]}, "TFT15_MechanicTrait_MaxCap": {"apiName": "TFT15_MechanicTrait_MaxCap", "name": "帽子戏法", "desc": "获得%物理加成和法术加成。 每次参与击杀时，戴上一顶帽子，每顶帽子额外提供%。阵亡时，失去你的%帽子。(当前帽子数：)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ADAPPerHat": 0.5, "BaseADAP": 20.0, "PercentHatLoss": 0.5}}]}, "TFT15_MechanicTrait_Superstar": {"apiName": "TFT15_MechanicTrait_Superstar", "name": "超级巨星", "desc": "己方弈子获得%伤害增幅，己方的每个3星弈子都会使该加成提升%。该弈子获得%此加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"DABase": 4.0, "DAPer3Star": 3.0, "Multiplier": 2.0}}]}, "TFT15_MechanicTrait_Magic": {"apiName": "TFT15_MechanicTrait_Magic", "name": "最大魔力", "desc": "获得%法术加成，并且每2次参与击杀永久获得%。(当前：%&nbsp;%i:scaleAP%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Amount": 1.0, "Base": 20.0}}]}, "TFT15_MechanicTrait_FinalAscent": {"apiName": "TFT15_MechanicTrait_FinalAscent", "name": "终极飞升", "desc": "在级和级时各进行一次额外的飞升。级：每次攻击，获得%法术加成级：发射2道额外波纹，造成%伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"APPerAttack": 5.0, "Level1": 7, "Level2": 10, "NumAttacks": 3, "ReducedWaveDamage": 0.550000011920929}}]}, "TFT15_MechanicTrait_LivingWall": {"apiName": "TFT15_MechanicTrait_LivingWall", "name": "活体城墙", "desc": "获得护甲和魔抗。这个弈子每拥有 %i:scaleArmor%和%i:scaleMR%总和，在战斗开始时处在后排的友军们就会获得%攻击速度。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AS": 1.0, "PerXResists": 30.0, "Resists": 20.0}}]}, "TFT15_MechanicTrait_TimeSkip": {"apiName": "TFT15_MechanicTrait_TimeSkip", "name": "闪烁攻击", "desc": "当转换目标时，闪烁到下一个目标旁。下一次攻击造成%的额外魔法伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"PctAdditionalDamage": 175.0}}]}, "TFT15_MechanicTrait_ManaRush": {"apiName": "TFT15_MechanicTrait_ManaRush", "name": "法力倾泻", "desc": "战斗开始时拥有满额法力值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {}}]}, "TFT15_ElTigre": {"apiName": "TFT15_ElTigre", "name": "魄罗之心", "desc": "【魄罗之心】对玩家的胜利会提供个魄罗粉丝！在失败时，每个魄罗粉丝会防止玩家伤害，然后你会失去你的%粉丝。魄罗粉丝数：", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_TheChamp.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 4, "variables": {"PctPorosLost": 75.0, "PlayerDamagePrevented": 1.0, "PlayerHealthPerVictoryDU": 1, "PoroAmountForHealPVE": 20.0, "PorosPerVictory": 2.0, "PorosPerVictoryDU": 1, "TacticianHealPVE": 1.0}}]}, "TFT15_MechanicTrait_RareCandy": {"apiName": "TFT15_MechanicTrait_RareCandy", "name": "稀有点心", "desc": "你的小怪兽拥有+【小怪兽训练师】等级。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Levels": 15}}]}, "TFT15_MechanicTrait_Surge66": {"apiName": "TFT15_MechanicTrait_Surge66", "name": "电涌66", "desc": "获得%攻击速度和%法术加成。在和次攻击后，这个数额翻倍。(攻击次数：)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AP": 10.0, "AS": 15.0, "FirstThreshold": 15.0, "SecondThreshold": 66.0}}]}, "TFT15_MechanicTrait_FinalForm": {"apiName": "TFT15_MechanicTrait_FinalForm", "name": "最终形态", "desc": "在-回合之后，如果该弈子在战斗开始时已经3星，则变成4星。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"AppropriateRound": 2, "AppropriateStage": 5, "{837c081f}": 2, "{c39eebe1}": 7}}]}, "TFT15_MechanicTrait_Classy": {"apiName": "TFT15_MechanicTrait_Classy", "name": "职业风采", "desc": "这个弈子的最底部羁绊对其提供%额外效能。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"IncreasedEffectiveness": 40}}]}, "TFT15_MechanicTrait_BonusBloom": {"apiName": "TFT15_MechanicTrait_BonusBloom", "name": "额外绽放", "desc": "获得一株额外的植物。植物们造成的伤害降低%，并且生命值降低%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PctOfDamage": 0.20000000298023224, "PctOfHealth": 0.30000001192092896}}]}, "TFT15_MechanicTrait_SeriousSlam": {"apiName": "TFT15_MechanicTrait_SeriousSlam", "name": "起飞咯", "desc": "对每个敌人的首次攻击造成% (%i:TFTBaseAD%)物理伤害，并将其晕眩秒。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AttackScale": 1.75, "KnockupDuration": 1.0, "{c37970cf}": null}}]}, "TFT15_Spellslinger": {"apiName": "TFT15_Spellslinger", "name": "法师", "desc": "【法师】们获得额外法术加成。在一个敌人被一个【法师】伤害后阵亡时，该敌人会对其他敌人们造成该敌人一部分最大生命值的伤害。()  %i:scaleAP%；%最大%i:scaleHealth%()  %i:scaleAP%；%最大%i:scaleHealth%()  %i:scaleAP%；%最大%i:scaleHealth% 至个敌人", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_Sorcerer.TFT_Set15.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"AbilityPower": 20, "HealthPct": 0.07999999821186066, "TargetNum": 1, "{a34f32df}": 3}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"AbilityPower": 50, "HealthPct": 0.10000000149011612, "TargetNum": 1, "{a34f32df}": 3}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"AbilityPower": 90, "HealthPct": 0.11999999731779099, "TargetNum": 2, "{a34f32df}": 3}}]}, "TFT15_MechanicTrait_Needlework": {"apiName": "TFT15_MechanicTrait_Needlework", "name": "引针簇射", "desc": "每第一次和第二次施放技能时，发射额外的根缝衣针，造成%伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BonusNeedles": 2, "ReducedDamage": 0.5}}]}, "TFT15_MechanicTrait_BloodFury": {"apiName": "TFT15_MechanicTrait_BloodFury", "name": "渴血英雄", "desc": "获得生命值和%物理加成 。每回合，你可以支付玩家生命值来换取 %i:scaleHealth%和 %i:scaleAD%。(总计： %i:scaleHealth%  %i:scaleAD%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_7_Tempest.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ADPerSacrifice": 0.029999999329447746, "BaseAD": 0.07999999821186066, "BaseAP": null, "BaseHealth": 100, "HealthPerSacrifice": 75.0, "PlayerHealthPerSacrifice": 3.0, "{cc84e244}": 1.5}}]}, "TFT15_MechanicTrait_BulletHell": {"apiName": "TFT15_MechanicTrait_BulletHell", "name": "纯粹弹幕地狱", "desc": "技能发射%额外飞弹。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"IncreasedProjectiles": 35, "{28c023cc}": 100}}]}, "TFT15_MechanicTrait_Finalist": {"apiName": "TFT15_MechanicTrait_Finalist", "name": "决赛圈选手", "desc": "获得%伤害增幅和%伤害减免，每有一名已被淘汰的玩家，提升% %i:scaleDA%和% %i:scaleDR%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"IncreasedDA": 0.019999999552965164, "IncreasedDR": 0.019999999552965164, "InitialDA": 0.11999999731779099, "InitialDR": 0.07999999821186066}}]}, "TFT15_MechanicTrait_AdaptiveSkin": {"apiName": "TFT15_MechanicTrait_AdaptiveSkin", "name": "适应性皮肤", "desc": "在受到伤害时，基于伤害类型获得护甲或魔抗，至多至总双抗。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ResistsPerDamageInstanceMagic": 6.0, "ResistsPerDamageInstancePhysical": 1.0, "TotalResists": 55.0}}]}, "TFT15_MechanicTrait_Hemorrhage": {"apiName": "TFT15_MechanicTrait_Hemorrhage", "name": "出血", "desc": "技能造成在秒里持续的%额外真实伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BleedDuration": 4.0, "PercentBleed": 60.0}}]}, "TFT15_MechanicTrait_FairyTail": {"apiName": "TFT15_MechanicTrait_FairyTail", "name": "妖精的尾巴", "desc": "施放技能时，生成2个妖精伙伴，它们每个都会在接下来的秒内对敌人造成-(基于阶段数)魔法伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"APDamage1": 80.0, "APDamage2": 100.0, "APDamage3": 140.0, "APDamage4": 200.0, "Duration": 8.0}}]}, "TFT15_MonsterTrainer": {"apiName": "TFT15_MonsterTrainer", "name": "小怪兽训练师", "desc": "为【璐璐】选择一只小怪兽，让她召唤它替自己出战！它在每次战斗后都会获得经验值，并且每次参与击杀都会获得额外经验值。当你的小怪兽升级时，它获得%生命值。在级和级时，它的技能会进化！小怪兽：级到下一个等级的经验值：/", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_MonsterTrainer.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 4, "variables": {"FirstEvolveLevel": 15, "HealthGainPerLevel": 0.009999999776482582, "SecondEvolveLevel": 30, "{74bcb19e}": 50, "{b4bcb37d}": 0.029999999329447746, "{c0bcc661}": 3}}]}, "TFT15_Juggernaut": {"apiName": "TFT15_Juggernaut", "name": "主宰", "desc": "【主宰】们获得伤害减免，这个加成会在生命值高于%时获得提升。当一个【主宰】弈子阵亡时，其他【主宰】回复其%最大生命值。() % 或 %&nbsp;%i:scaleDR%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_9_Juggernaut.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"BaseDR": 0.15000000596046448, "HealRatio": 0.10000000149011612, "HealthBreakpoint": 0.5, "IncreasedDR": 0.25}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"BaseDR": 0.20000000298023224, "HealRatio": 0.10000000149011612, "HealthBreakpoint": 0.5, "IncreasedDR": 0.30000001192092896}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"BaseDR": 0.25, "HealRatio": 0.10000000149011612, "HealthBreakpoint": 0.5, "IncreasedDR": 0.3499999940395355}}]}, "TFT15_Rosemother": {"apiName": "TFT15_Rosemother", "name": "荆棘之兴", "desc": "基于【婕拉】的星级，获得//个可放置的植物。前两排的植物会成长为坚韧的【缠绕之根】，而后两排的植物会长成【致命棘刺】。【荆棘之兴】植物能从【婕拉】的法术加成和攻击速度中获益。当【婕拉】施放技能时，她的植物会回复 %生命值并获得持续至战斗结束的%攻击速度。如果植物已阵亡，那么她会转而将其复活。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_Rosemother.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 4, "variables": {"1Star": 1.0, "2Star": 1.0, "3Star": 8.0, "BonusHealthAndAS": 0.3499999940395355}}]}, "TFT15_MechanicTrait_CriticalThreat": {"apiName": "TFT15_MechanicTrait_CriticalThreat", "name": "暴击威胁", "desc": "技能可以造成暴击。每秒，获得%暴击几率。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"CriticalStrikeChance": 5.0, "Interval": 3.0}}]}, "TFT15_MechanicTrait_Demolitionist": {"apiName": "TFT15_MechanicTrait_Demolitionist", "name": "爆破专家", "desc": "技能伤害造成秒晕眩。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"StunDuration": 1}}]}, "TFT15_MechanicTrait_ChaosStorm": {"apiName": "TFT15_MechanicTrait_ChaosStorm", "name": "风暴统御", "desc": "() 召唤一个风暴，风暴会回响相当于%造成伤害值的魔法伤害。 () 两个弈子都会贡献伤害。风暴更加大型。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"PercentDamageEcho": 0.25, "{44d79b6f}": 8, "{e202a897}": 1, "{e9e2b861}": 2}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"PercentDamageEcho": 0.25, "{44d79b6f}": 10, "{e202a897}": 1, "{e9e2b861}": 2}}]}, "TFT15_MechanicTrait_Mechablade": {"apiName": "TFT15_MechanicTrait_Mechablade", "name": "机甲神兵", "desc": "获得相当于%最大生命值的攻击速度，攻击会提供额外法力值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BonusMana": 2.0, "PercentHealthAsAS": 0.029999999329447746}}]}, "TFT15_MechanicTrait_Doomsayer": {"apiName": "TFT15_MechanicTrait_Doomsayer", "name": "末日预言者", "desc": "在秒后，如果仍然存活，对所有敌人造成魔法伤害，相当于本场战斗承受伤害的%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Countdown": 15.0, "PercentOfDamageTaken": 0.4000000059604645}}]}, "TFT15_MechanicTrait_ShadowJutsu": {"apiName": "TFT15_MechanicTrait_ShadowJutsu", "name": "影分身", "desc": "创造一个该弈子的完美复制品(携带同样装备)，可造成%伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DamagePercent": 0.25}}]}, "TFT15_MechanicTrait_DreadNote": {"apiName": "TFT15_MechanicTrait_DreadNote", "name": "惧亡笔记", "desc": "() 如果在和秒时存活，处决生命值最高的敌人() 改为在和秒。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"FirstDeath": 18.0, "SecondDeath": 30.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"FirstDeath": 15.0, "SecondDeath": 25.0}}]}, "TFT15_MechanicTrait_Pursuit": {"apiName": "TFT15_MechanicTrait_Pursuit", "name": "追击", "desc": "【卢锡安】的技能进行冲刺，发射+颗额外子弹，并提供法术加成，持续到战斗环节结束。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"APGain": 5, "NumAdditionalShots": 1}}]}, "TFT15_MechanicTrait_Corrupted": {"apiName": "TFT15_MechanicTrait_Corrupted", "name": "堕落使者", "desc": "以休眠状态开始战斗。每当弈子阵亡时，获得%伤害增幅和生命值。在%生命值时，苏醒并朝着敌人们闪烁，对他们造成秒晕眩。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DAPerSoul": 0.04500000178813934, "HealthPerSoul": 80, "HealthThreshold": 0.6000000238418579, "StunDuration": 1.5}}]}, "TFT15_MechanicTrait_WarmingUp": {"apiName": "TFT15_MechanicTrait_WarmingUp", "name": "正在热身", "desc": "战斗开始时或在这个果实激活时获得护甲和魔抗。每秒，将双抗转化为%物理加成。(至多至)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Conversion": 3.0, "Resists": 48.0}}]}, "TFT15_MechanicTrait_Corrosive": {"apiName": "TFT15_MechanicTrait_Corrosive", "name": "腐蚀", "desc": "对2格内的敌人们施加%魔抗击碎和护甲击碎。所受的来自带有魔抗击碎和护甲击碎的敌人的伤害会降低%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DamageReduction": 10.0, "Shred": 30}}]}, "TFT15_MechanicTrait_SoulChipper": {"apiName": "TFT15_MechanicTrait_SoulChipper", "name": "灵魂切削者", "desc": "在造成魔法伤害时使魔抗降低，持续到战斗环节结束。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"MagicResistReduce": 4.0}}]}, "TFT15_MechanicTrait_Thrillseeker": {"apiName": "TFT15_MechanicTrait_Thrillseeker", "name": "刺激追寻者", "desc": "() 在击杀时，回复%最大生命值，并获得持续秒的%攻击速度。() 击杀时% %i:scaleAS%，并处决生命值低于%的敌人。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"AS": 0.4000000059604645, "Duration": 2, "HealPercent": 15.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"AS": 0.4000000059604645, "Duration": 2, "ExecuteThreshold": 0.10000000149011612, "HealPercent": 15.0}}]}, "TFT15_MechanicTrait_Untouchable": {"apiName": "TFT15_MechanicTrait_Untouchable", "name": "不可触及", "desc": "在至少有个友军存活时处于不可被选取状态。如果该弈子在战斗结束时仍然满生命值，获得永久的%伤害增幅。(当前伤害增幅：% %i:scaleDA%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DamageAmpScaling": 0.019999999552965164, "NumAllies": 4}}]}, "TFT15_MechanicTrait_Efficient": {"apiName": "TFT15_MechanicTrait_Efficient", "name": "高效率", "desc": "技能施放所需的法力消耗降低。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Reduction": 20}}]}, "TFT15_Captain": {"apiName": "TFT15_Captain", "name": "卡牌大师", "desc": "【崔斯特】升级【奥德赛星舰】以造成其%伤害的真实伤害，并且每回合抽取各种提供随机奖励的【悬赏卡牌】。【悬赏卡牌】会随着玩家对战回合数变得更好(已登场的回合数：)当前【悬赏卡牌】：", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_RogueCaptain.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 4, "variables": {"Bounty_CasterEmpower_DA": 0.019999999552965164, "Bounty_FreeRerolls_NumRerolls": 2, "Bounty_GainAttackSpeed_AS": 0.15000000596046448, "Bounty_GainGoldLarge_MinGold": 12, "Bounty_GainGold_MinGold": 2, "Bounty_GainXP_Amount": 12, "Bounty_Omnivamp_Omnivamp": 0.15000000596046448, "Bounty_PermanentAP_AP": 18, "Bounty_TankEmpower_MaxHealth": 40, "Bounty_TeamShield_Duration": 8, "Bounty_TeamShield_Shield": 160, "Bounty_TheCrew_GainHealth_PermanentADAP": 5.0, "Bounty_TheCrew_GainHealth_PermanentHealth": 60, "Bounty_TheCrew_GoldPerStar_GoldAmount": 1, "Bounty_TheCrew_RocketUpgrade_TrueDamagePercent": 0.15000000596046448, "Bounty_ThinkFast_Cadence": 3, "Bounty_ThinkFast_Gold": 5, "{0443d547}": 1, "{0d20aaf6}": 1, "{1376c2a6}": 12, "{27dcf059}": 6, "{38d1c1ce}": 0.10000000149011612, "{e74d3d55}": 2}}]}, "TFT15_MechanicTrait_Annihilation": {"apiName": "TFT15_MechanicTrait_Annihilation", "name": "灭绝", "desc": "获得%伤害增幅，在每场战斗首次参与击杀后，提升至%伤害增幅。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BaseDA": 12.0, "IncreasedDA": 28.0}}]}, "TFT15_MechanicTrait_Resistant": {"apiName": "TFT15_MechanicTrait_Resistant", "name": "抵抗", "desc": "使即将到来的每段伤害降低。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"FlatDR": 30.0, "Resists": null}}]}, "TFT15_OldMentor": {"apiName": "TFT15_OldMentor", "name": "大宗师", "desc": "这个羁绊仅会在你恰好拥有1个或4个各不相同的【大宗师】弈子时激活。(1) 友军们获得该加成(4) 【大宗师】们获得所有加成并且升级其技能。其它友军不获得此羁绊的任何加成。【可酷伯】：%伤害减免【乌迪尔】：%物理加成和法术加成【亚索】：%攻击速度【瑞兹】：攻击获得额外法力值", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_OldMentor.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"ADAP": 0.07999999821186066, "AS": 0.10000000149011612, "Durability": 0.05999999865889549, "Mana": 2.0}}, {"maxUnits": 3, "minUnits": 2, "style": 3, "variables": {"ADAP": 0.07999999821186066, "AS": 0.10000000149011612, "Durability": 0.05999999865889549, "Mana": 2.0}}, {"maxUnits": 25000, "minUnits": 4, "style": 5, "variables": {"ADAP": 0.07999999821186066, "AS": 0.10000000149011612, "Durability": 0.05999999865889549, "Mana": 2.0}}]}, "TFT15_MechanicTrait_TinyTerror": {"apiName": "TFT15_MechanicTrait_TinyTerror", "name": "小讨厌鬼", "desc": "获得%攻击速度。在%生命值时，将此加成翻倍，持续到战斗环节结束。然后，缩小体型秒，闪避所有攻击。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AS": 0.11999999731779099, "Duration": 4.0, "Threshold": 0.5}}]}, "TFT15_MechanicTrait_Trickster": {"apiName": "TFT15_MechanicTrait_Trickster", "name": "替身忍术", "desc": "() 在低生命值时获得%伤害增幅，生成一个训练假人，并冲刺至相距最远的那个敌人。() % %i:scaleDA%；训练假人获得 %i:scaleHealth%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"DA": 0.07999999821186066, "DummyHealth": 250.0, "HealthThreshold": 0.5}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"DA": 0.11999999731779099, "DummyHealth": 1250.0, "HealthThreshold": 0.5}}]}, "TFT15_MechanicTrait_Caretaker": {"apiName": "TFT15_MechanicTrait_Caretaker", "name": "游神", "desc": "施放技能时，生命值百分比最低的那个友军获得持续秒的&nbsp;(%i:scaleAP%)护盾值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Duration": 6.0, "ShieldAmount": 120.0}}]}, "TFT15_MechanicTrait_MindBattery": {"apiName": "TFT15_MechanicTrait_MindBattery", "name": "思维电池", "desc": "每场战斗的第一次技能施放提供法力回复和%法术加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AP": 30.0, "ManaRegen": 5.0}}]}, "TFT15_MechanicTrait_Kahunahuna": {"apiName": "TFT15_MechanicTrait_Kahunahuna", "name": "秘传奥义", "desc": "每次攻击，造成&nbsp;(%i:scaleAD%)真实伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"ADDamage": 215.0, "NumAttacks": 6.0}}]}, "TFT15_Duelist": {"apiName": "TFT15_Duelist", "name": "决斗大师", "desc": "【决斗大师】们每次攻击都会获得攻击速度，最多叠加次。() %&nbsp;%i:scaleAS%() %&nbsp;%i:scaleAS%() %&nbsp;%i:scaleAS%；【决斗大师】们获得%&nbsp;%i:scaleDR%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_8_Duelist.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"AttackSpeedPercent": 0.03999999910593033, "DamageReduction": null, "MaxStacks": 12.0}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"AttackSpeedPercent": 0.07000000029802322, "DamageReduction": null, "MaxStacks": 12.0}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"AttackSpeedPercent": 0.10000000149011612, "DamageReduction": 12.0, "MaxStacks": 12.0}}]}, "TFT15_MechanicTrait_AttackExpert": {"apiName": "TFT15_MechanicTrait_AttackExpert", "name": "攻击专家", "desc": "获得%物理加成。所有其它物理加成来源额外提供%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"BaseAD": 0.05000000074505806, "PctBonusAD": 30.0}}]}, "TFT15_Strategist": {"apiName": "TFT15_Strategist", "name": "司令", "desc": "战斗开始时：前2排的友军获得持续秒的护盾值。后2排的友军获得伤害增幅。【司令】们获得三倍。() 护盾值；% %i:scaleDA%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_9_Strategist.tex", "effects": [{"maxUnits": 2, "minUnits": 2, "style": 1, "variables": {"DamageAmp": 4, "Shield": 150, "ShieldDuration": 15.0}}, {"maxUnits": 3, "minUnits": 3, "style": 3, "variables": {"DamageAmp": 6, "Shield": 225, "ShieldDuration": 15.0}}, {"maxUnits": 4, "minUnits": 4, "style": 5, "variables": {"DamageAmp": 10, "Shield": 350, "ShieldDuration": 15.0}}, {"maxUnits": 25000, "minUnits": 5, "style": 5, "variables": {"DamageAmp": 14, "Shield": 450, "ShieldDuration": 15.0}}]}, "TFT15_Bastion": {"apiName": "TFT15_Bastion", "name": "护卫", "desc": "己方弈子获得护甲和魔法抗性。【护卫】获得更多，并且这个数值在战斗的最初秒内翻倍。()  %i:scaleArmor%%i:scaleMR%()  %i:scaleArmor%%i:scaleMR%()  %i:scaleArmor%%i:scaleMR%；非【护卫】弈子获得额外的 %i:scaleArmor%%i:scaleMR%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_9_Bastion.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"BonusArmor": 18.0, "BonusMR": 18.0, "Duration": 10.0, "EnhancedTeamwideArmor": 25.0, "StatMultiplier": 2.0, "TeamwideResists": 10.0}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"BonusArmor": 36.0, "BonusMR": 36.0, "Duration": 10.0, "EnhancedTeamwideArmor": 25.0, "StatMultiplier": 2.0, "TeamwideResists": 10.0}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"BonusArmor": 70.0, "BonusMR": 70.0, "Duration": 10.0, "EnhancedTeamwideArmor": 25.0, "StatMultiplier": 2.0, "TeamwideResists": 10.0}}]}, "TFT15_MechanicTrait_StarSailor": {"apiName": "TFT15_MechanicTrait_StarSailor", "name": "星之水手战士", "desc": "() 该弈子从【星之守护者】中获得%额外加成。() 以及会对另一个【星之水手战士】治疗相当于%造成伤害的生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"HealingPercent": null, "StatIncrease": 0.25}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"HealingPercent": 0.15000000596046448, "StatIncrease": 0.25}}]}, "TFT15_MechanicTrait_NotDoneYet": {"apiName": "TFT15_MechanicTrait_NotDoneYet", "name": "决不罢休", "desc": "一旦该弈子已经阵亡次，就会获得%生命值、%全能吸血、和%伤害增幅。(阵亡达成次数：)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"NumDeathsStage2": 8, "NumDeathsStage3": 6, "NumDeathsStage4": 4, "Omnivamp": 10.0, "PctHealth": 25.0, "Stats": 25.0}}]}, "TFT15_MechanicTrait_RampingRage": {"apiName": "TFT15_MechanicTrait_RampingRage", "name": "狂暴渐长", "desc": "每次攻击提供%可叠加的攻击速度。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ASperAttack": 0.029999999329447746}}]}, "TFT15_MechanicTrait_PowerFont": {"apiName": "TFT15_MechanicTrait_PowerFont", "name": "能量源泉", "desc": "战斗开始时，以及每秒，获得法力回复。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Cadence": 3.0, "Mana": 1}}]}, "TFT15_MechanicTrait_FinalBoss": {"apiName": "TFT15_MechanicTrait_FinalBoss", "name": "最终BOSS", "desc": "获得%全能吸血。每当一个友军阵亡时，获得%可叠加的伤害增幅。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DamageAmp": 0.03999999910593033, "Omnivamp": 0.10000000149011612}}]}, "TFT15_MechanicTrait_DarkAmulet": {"apiName": "TFT15_MechanicTrait_DarkAmulet", "name": "黑暗护符", "desc": "回复相当于%造成魔法伤害的生命值。在施放技能时，受到相当于%最大生命值的真实伤害，但获得可叠加的法术加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"HealPct": 15.0, "HealthLossPct": 8.0, "StackingAP": 12.0}}]}, "TFT15_Empyrean": {"apiName": "TFT15_Empyrean", "name": "至高天", "desc": "每秒，【暗影国度】会打击个相距最近的敌人，造成魔法伤害，伤害总和相当于【至高天】弈子们从上次触发起造成的一部分伤害。你的生命值最低的【至高天】弈子会获得治疗，数额相当于由【至高天】弈子们和【暗影国度】造成的%伤害值。() %伤害。() %伤害。() %伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_Empyrean.TFT_Set15.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"DamagePercent": 0.18000000715255737, "HealPercent": 0.18000000715255737, "NumEnemy": 3, "RealmTimer": 5.0}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"DamagePercent": 0.33000001311302185, "HealPercent": 0.18000000715255737, "NumEnemy": 3, "RealmTimer": 5.0}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"DamagePercent": 0.5, "HealPercent": 0.18000000715255737, "NumEnemy": 3, "RealmTimer": 5.0}}]}, "TFT15_MechanicTrait_Supremacy": {"apiName": "TFT15_MechanicTrait_Supremacy", "name": "至尊兵王", "desc": "当这个弈子是【最强兵王】时，会转而处决生命值低于%的敌人。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ExecutePct": 0.18000000715255737}}]}, "TFT15_Prodigy": {"apiName": "TFT15_Prodigy", "name": "天才", "desc": "己方获得法力回复。【天才】们获得更多。()  %i:TFTManaRegen% |  %i:TFTManaRegen%()  %i:TFTManaRegen% |  %i:TFTManaRegen%()  %i:TFTManaRegen% |  %i:TFTManaRegen%()  %i:TFTManaRegen% |  %i:TFTManaRegen% | 【天才】们的技能为一名友军治疗相当于%造成伤害的生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_Prodigy.TFT_Set15.tex", "effects": [{"maxUnits": 2, "minUnits": 2, "style": 1, "variables": {"ProdigyBonusMana": 3.0, "TeamBonusMana": 1.0}}, {"maxUnits": 3, "minUnits": 3, "style": 3, "variables": {"ProdigyBonusMana": 5.0, "TeamBonusMana": 1.0}}, {"maxUnits": 4, "minUnits": 4, "style": 3, "variables": {"ProdigyBonusMana": 7.0, "TeamBonusMana": 2.0}}, {"maxUnits": 25000, "minUnits": 5, "style": 5, "variables": {"AllyHealing": 0.10000000149011612, "ProdigyBonusMana": 9.0, "TeamBonusMana": 3.0}}]}, "TFT15_MechanicTrait_Singularity": {"apiName": "TFT15_MechanicTrait_Singularity", "name": "奇点", "desc": "变为一个半径格的奇点，每秒造成% (%i:scaleHealth%)魔法伤害。当敌人在奇点中阵亡时，获得永久生命值。(总计： %i:scaleHealth%)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"NumHexes": 2, "PercentHealth": 0.009999999776482582, "PermanentHealth": 8}}]}, "TFT15_MechanicTrait_TeamCaptain": {"apiName": "TFT15_MechanicTrait_TeamCaptain", "name": "群星领袖", "desc": "() 该弈子每星级将其【星之守护者】团队加成提升%。() 两个弈子每星级都会将各自的【星之守护者】团队加成提升%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"StatIncrease": 0.15000000596046448}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"StatIncrease": 0.20000000298023224}}]}, "TFT15_MechanicTrait_BestestBoy": {"apiName": "TFT15_MechanicTrait_BestestBoy", "name": "最棒棒的崽", "desc": "每秒，【璐璐】向她的小怪兽投喂一个零食：治疗【拉莫斯】% %i:scaleHealth%、提供【斯莫德】%物理加成、或提供【克格莫】%法术加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"AD": 0.11999999731779099, "AP": 12.0, "Cadence": 5.0, "PercentHeal": 0.12999999523162842}}]}, "TFT15_MechanicTrait_SuperGenius": {"apiName": "TFT15_MechanicTrait_SuperGenius", "name": "超级天才", "desc": "每秒，获得相当于你法力回复的法术加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Cadence": 1.5}}]}, "TFT15_MechanicTrait_DoomBarrage": {"apiName": "TFT15_MechanicTrait_DoomBarrage", "name": "末日弹幕", "desc": "【韦鲁斯】技能期间，箭矢的法力消耗降低。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ManaReduction": 2}}]}, "TFT15_Protector": {"apiName": "TFT15_Protector", "name": "圣盾使", "desc": "单位们在拥有护盾时获得%伤害减免。每场战斗一次：【圣盾使】们在降至%生命值时，会为自身和相距最近的那名友军提供基于自身最大生命值的护盾值。护盾可叠加。() % %i:scaleHealth%护盾值", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_6_Protector.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"DamageReductionPct": 0.05000000074505806, "HealthThreshold": 0.5, "ShieldPercentAmount": 0.20000000298023224}}, {"maxUnits": 5, "minUnits": 4, "style": 3, "variables": {"DamageReductionPct": 0.05000000074505806, "HealthThreshold": 0.5, "ShieldPercentAmount": 0.4000000059604645}}, {"maxUnits": 25000, "minUnits": 6, "style": 5, "variables": {"DamageReductionPct": 0.05000000074505806, "HealthThreshold": 0.5, "ShieldPercentAmount": 0.6000000238418579}}]}, "TFT15_MechanicTrait_FrostTouch": {"apiName": "TFT15_MechanicTrait_FrostTouch", "name": "霜冻之触", "desc": "() 攻击有%几率晕眩目标秒。() 这些攻击每阶段还会造成额外魔法伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"DamagePerStage": 55, "PercentageChanceToTrigger": 15, "StunDuration": 1.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"DamagePerStage": 55, "PercentageChanceToTrigger": 25, "StunDuration": 1.0}}]}, "TFT15_MechanicTrait_SpiritSword": {"apiName": "TFT15_MechanicTrait_SpiritSword", "name": "英灵剑", "desc": "获得%暴击几率。攻击在暴击时造成%额外魔法伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BonusMagicDamage": 40.0, "CritChance": 0.20000000298023224}}]}, "TFT15_MechanicTrait_InnerFire": {"apiName": "TFT15_MechanicTrait_InnerFire", "name": "心灵烈焰", "desc": "每秒，对相距最近且未被灼烧的那个敌人施加持续秒的灼烧效果和重伤效果。所受的来自被灼烧敌人的伤害会降低%。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"DamageReduction": 0.15000000596046448, "Duration": 10, "ICD": 2.0}}]}, "TFT15_MechanicTrait_BodyChange": {"apiName": "TFT15_MechanicTrait_BodyChange", "name": "身体变换", "desc": "战斗开始时，变形为相距最近的那个友军，为该友军和这个弈子提供%生命值的护盾值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PctHealth": 0.30000001192092896}}]}, "TFT15_MechanicTrait_UltraStance": {"apiName": "TFT15_MechanicTrait_UltraStance", "name": "极致形态", "desc": "获得生命值并提供额外的+至所选姿态羁绊。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BonusHealth": 100, "BonusTraitAmount": 1}}]}, "TFT15_MechanicTrait_PureHeart": {"apiName": "TFT15_MechanicTrait_PureHeart", "name": "纯洁心灵", "desc": "每秒，治疗该弈子和2格内的友军各自的%已损失生命值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"HealTickRate": 4.0, "MissingHealthHeal": 0.18000000715255737}}]}, "TFT15_BattleAcademia": {"apiName": "TFT15_BattleAcademia", "name": "战斗学院", "desc": "【战斗学院】弈子们升级其技能并获得【潜能】%i:set14AmpIcon%。【潜能】可改进其技能。棱彩条件：登场(7)，然后通过使用装备来赚取点数。棱彩条件：在战斗中已使用的每件成装提供1点数。在点数时毕业。(/)真实潜能已解锁：+ %i:set14AmpIcon%()  %i:set14AmpIcon%()  %i:set14AmpIcon%()  %i:set14AmpIcon%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_BattleClub.TFT_Set15.tex", "effects": [{"maxUnits": 4, "minUnits": 3, "style": 1, "variables": {"DA": 0.10000000149011612, "DR": 0.05000000074505806, "NumPotential": 3, "PrismaticItemRequirement": 175, "UltimateNumPotential": 10}}, {"maxUnits": 6, "minUnits": 5, "style": 3, "variables": {"DA": 0.25, "DR": 0.10000000149011612, "NumPotential": 5, "PrismaticItemRequirement": 175, "UltimateNumPotential": 10}}, {"maxUnits": 25000, "minUnits": 7, "style": 5, "variables": {"DA": 0.5, "DR": 0.25, "NumPotential": 7, "PrismaticItemRequirement": 175, "UltimateNumPotential": 10}}, {"maxUnits": 25000, "minUnits": 7, "style": 6, "variables": {"DA": 0.5, "DR": 0.25, "NumPotential": 7, "PrismaticItemRequirement": 175, "UltimateNumPotential": 10}}]}, "TFT15_MechanicTrait_RoboRumble": {"apiName": "TFT15_MechanicTrait_RoboRumble", "name": "假面骑士", "desc": "当你同时登场【超级战队】和【假面摔角手】时，【超级机甲】获得生命值并成为【假面摔角手】(提供+1【假面摔角手】羁绊) 。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Health": 100}}]}, "TFT15_MechanicTrait_CrimsonVeil": {"apiName": "TFT15_MechanicTrait_CrimsonVeil", "name": "血色面纱", "desc": "获得%全能吸血。全能吸血造成的过量治疗会转化为护盾。(最大：%生命值)", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"MaxPercentHealthShield": 40.0, "Omnivamp": 20.0}}]}, "TFT15_MechanicTrait_FusionDance": {"apiName": "TFT15_MechanicTrait_FusionDance", "name": "融合舞", "desc": "战斗开始时，和相距最近的友军融合，从而获得其%生命值、物理加成和法术加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"StatShare": 0.44999998807907104}}]}, "TFT15_MechanicTrait_Rogue": {"apiName": "TFT15_MechanicTrait_Rogue", "name": "刺杀", "desc": "() 战斗开始时：跳跃至敌方后排，在战斗的最初秒造成的伤害降低%。() 两个弈子都跳跃。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 4, "variables": {"Duration": 8, "ReducedDamage": 0.20000000298023224}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"Duration": 8.0, "ReducedDamage": 0.15000000596046448}}]}, "TFT15_MechanicTrait_Lovers": {"apiName": "TFT15_MechanicTrait_Lovers", "name": "粉丝福利", "desc": "【霞】和【洛】获得基于彼此星级的额外属性。() % %i:scaleAD%%i:scaleAS% |  %i:scaleHealth%， %i:scaleArmor%%i:scaleMR%() % %i:scaleAD%%i:scaleAS% |  %i:scaleHealth%， %i:scaleArmor%%i:scaleMR%", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 1, "minUnits": 1, "style": 1, "variables": {"AD": 10.0, "AS": 10.0, "Health": 180.0, "Resists": 12.0}}, {"maxUnits": 25000, "minUnits": 2, "style": 1, "variables": {"AD": 12.0, "AS": 12.0, "Health": 250.0, "Resists": 15.0}}]}, "TFT15_MechanicTrait_CRMechCashout": {"apiName": "TFT15_MechanicTrait_CRMechCashout", "name": "水晶核心", "desc": "【超级机甲】将%承受伤害转化为物理加成和法术加成，并获得护甲和魔抗。【超级机甲】的技能会将【水晶玫瑰】弈子视作【超级战队】。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Absorption": 0.07999999821186066, "Resists": 100}}]}, "TFT15_MechanicTrait_Socialite": {"apiName": "TFT15_MechanicTrait_Socialite", "name": "社交名流", "desc": "战斗开始时：标记邻近格子回合。处于标记过的格子中的友军们获得%伤害增幅和生命值。这个弈子获得%该加成。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DamageAmp": 0.11999999731779099, "Health": 200, "ManaPerSecond": null, "NumRounds": 3, "PercentIncrease": 2.0}}]}, "TFT15_MechanicTrait_Momentum": {"apiName": "TFT15_MechanicTrait_Momentum", "name": "动力十足", "desc": "() 获得%攻击速度，并大幅提高移动速度。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"BonusAS": 0.6000000238418579, "BonusMS": 0.5}}]}, "TFT15_MechanicTrait_StrongSpark": {"apiName": "TFT15_MechanicTrait_StrongSpark", "name": "极限火花", "desc": "每秒，震击相距最近的那个敌人以造成该敌人%最大生命值的魔法伤害。每当一个敌人被晕眩时(任何来源)，对该敌人进行震击。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"Cadence": 3.0, "Damage": 0.09000000357627869}}]}, "TFT15_MechanicTrait_EssenceShare": {"apiName": "TFT15_MechanicTrait_EssenceShare", "name": "有蓝同享", "desc": "施放技能时，相距最近的个友军获得%的消耗法力值。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ManaPct": 0.25, "NumAllies": 2.0}}]}, "TFT15_MechanicTrait_Bludgeoner": {"apiName": "TFT15_MechanicTrait_Bludgeoner", "name": "钝器能手", "desc": "攻击和技能无视敌人%的护甲。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"PercentIgnore": 35.0}}]}, "TFT15_MechanicTrait_ArtisticKO": {"apiName": "TFT15_MechanicTrait_ArtisticKO", "name": "艺术KO", "desc": "每第4次攻击造成%额外伤害。过量击杀伤害会弹射至相距最近的那个敌人。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"IncreasedDamage": 0.4000000059604645, "NumEnemies": 1}}]}, "TFT15_MechanicTrait_Doublestrike": {"apiName": "TFT15_MechanicTrait_Doublestrike", "name": "双重打击", "desc": "普攻有%几率触发一次额外普攻。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"ProcChance": 25.0}}]}, "TFT15_Luchador": {"apiName": "TFT15_Luc<PERSON>or", "name": "假面摔角手", "desc": "【假面摔角手】们获得额外物理加成。在%生命值时，【假面摔角手】会净化负面效果，获得治疗，并且跳回战斗中，将半径1格内的敌人们晕眩秒。()  %i:scaleAD%；% %i:scaleHealth%治疗()  %i:scaleAD%；% %i:scaleHealth%治疗", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_RingKings.TFT_Set15.tex", "effects": [{"maxUnits": 3, "minUnits": 2, "style": 1, "variables": {"ADBonus": 0.15000000596046448, "HealRatio": 0.20000000298023224, "HealthRatio": 0.5, "ShieldDuration": 5.0, "StunDuration": 1.0}}, {"maxUnits": 25000, "minUnits": 4, "style": 5, "variables": {"ADBonus": 0.4000000059604645, "HealRatio": 0.5, "HealthRatio": 0.5, "ShieldDuration": 5.0, "StunDuration": 1.0}}]}, "TFT15_MechanicTrait_OnTheEdge": {"apiName": "TFT15_MechanicTrait_OnTheEdge", "name": "背水佬", "desc": "战斗开始时具有%生命值，但获得%伤害增幅。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"DA": 0.3499999940395355, "HealthPercent": 0.6499999761581421}}]}, "TFT15_MechanicTrait_StandUnited": {"apiName": "TFT15_MechanicTrait_StandUnited", "name": "秘奥义！慈悲度魂落", "desc": "【慎】的技能同时为生命值最低的友军提供其护盾值%的护盾，并且每次施法时获得可叠加的魔抗。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"MagicResist": 10, "ShieldPercent": 0.4000000059604645}}]}, "TFT15_MechanicTrait_Desperado": {"apiName": "TFT15_MechanicTrait_Desperado", "name": "亡命之徒", "desc": "在每次攻击后，向相距最近的敌人发射5枚飞弹，造成相当于%攻击力的物理伤害。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_SecretTechnique.TFT_Set15.tex", "effects": [{"maxUnits": 25000, "minUnits": 1, "style": 1, "variables": {"NumAttacks": 12.0, "PercentBADDamage": 120.0}}]}, "TFT15_SentaiRanger": {"apiName": "TFT15_SentaiRanger", "name": "超级战队", "desc": "获得【超级机甲】。【超级战队】弈子们在造成伤害时会为它提供治疗，数额为%造成伤害。解锁【究极机甲】() 【超级机甲】登场() 超级战刃协议：1级() 超级战刃协议：2级每个【超级战队】弈子的星级都会提升【超级机甲】的强度。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_RoboRangers.TFT_Set15.tex", "effects": [{"maxUnits": 4, "minUnits": 3, "style": 1, "variables": {"AutoAttackDamage": 65.0, "GalioHealRatio": 0.10000000149011612, "NumItems": 1, "{0d36fce1}": 1.0, "{2b3aecac}": 0.1599999964237213, "{35bd6e0d}": 599, "{3f8b87fb}": 5.0, "{4c8d22b6}": 50, "{4e59afce}": 0.05000000074505806, "{54e3a48d}": null, "{9456c371}": null, "{d13df441}": 2, "{dc8a3667}": 0.15000000596046448}}, {"maxUnits": 6, "minUnits": 5, "style": 3, "variables": {"AutoAttackDamage": 100.0, "GalioHealRatio": 0.10000000149011612, "Health": 1300.0, "NumItems": 2, "{0d36fce1}": 2.0, "{2b3aecac}": 0.1599999964237213, "{35bd6e0d}": 699, "{3f8b87fb}": 5.0, "{4c8d22b6}": 75, "{4e59afce}": 0.05000000074505806, "{54e3a48d}": null, "{9456c371}": null, "{d13df441}": 2, "{dc8a3667}": 0.15000000596046448}}, {"maxUnits": 25000, "minUnits": 7, "style": 5, "variables": {"AutoAttackDamage": 110.0, "GalioHealRatio": 0.10000000149011612, "Health": 2000.0, "NumItems": 3, "{0d36fce1}": 3.0, "{2b3aecac}": 0.1599999964237213, "{35bd6e0d}": 849, "{3f8b87fb}": 5.0, "{4c8d22b6}": 85, "{4e59afce}": 0.05000000074505806, "{54e3a48d}": 20.0, "{9456c371}": 60.0, "{d13df441}": 2, "{dc8a3667}": 0.15000000596046448}}, {"maxUnits": 25000, "minUnits": 7, "style": 6, "variables": {"AutoAttackDamage": 120.0, "GalioHealRatio": 0.10000000149011612, "Health": 2000.0, "NumItems": 3, "{0d36fce1}": 4.0, "{2b3aecac}": 0.1599999964237213, "{35bd6e0d}": 849, "{3f8b87fb}": 5.0, "{4c8d22b6}": 90, "{4e59afce}": 0.05000000074505806, "{54e3a48d}": 20.0, "{9456c371}": 60.0, "{d13df441}": 2, "{dc8a3667}": 0.15000000596046448}}]}}}