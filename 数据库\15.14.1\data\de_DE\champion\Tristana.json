{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tristana": {"id": "<PERSON><PERSON>", "key": "18", "name": "<PERSON><PERSON>", "title": "die Yordle-<PERSON><PERSON><PERSON><PERSON>in", "image": {"full": "Tristana.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "18000", "num": 0, "name": "default", "chromas": false}, {"id": "18001", "num": 1, "name": "Riot-<PERSON><PERSON>", "chromas": false}, {"id": "18002", "num": 2, "name": "Elfen-Tristana", "chromas": false}, {"id": "18003", "num": 3, "name": "Feuerwehr-Tristana", "chromas": false}, {"id": "18004", "num": 4, "name": "Guerilla-Tristana", "chromas": false}, {"id": "18005", "num": 5, "name": "Seeräuber-Tristana", "chromas": false}, {"id": "18006", "num": 6, "name": "Ra<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": true}, {"id": "18010", "num": 10, "name": "Drachenzähmer-Tristana", "chromas": true}, {"id": "18011", "num": 11, "name": "Hexerei-Tristana", "chromas": false}, {"id": "18012", "num": 12, "name": "Omegatrupp-Tristana", "chromas": true}, {"id": "18024", "num": 24, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "18033", "num": 33, "name": "Pingu-Cosplay-Tristana", "chromas": true}, {"id": "18040", "num": 40, "name": "Hextech-Tristana", "chromas": false}, {"id": "18041", "num": 41, "name": "Feuerwerks-Tristana", "chromas": true}, {"id": "18051", "num": 51, "name": "Seelenblumen-Tristana", "chromas": true}, {"id": "18061", "num": 61, "name": "Feenhof-Tristana", "chromas": true}], "lore": "Wohingegen viele andere Yordle ihre Zeit mit Entdeckungen, Erfindungen oder einfach Unfug verbringen, fühlte sich Tristana schon immer von den Abenteuern großer Krieger inspiriert. Sie hatte schon viel über Runeterra mit seinen Fraktionen und Kriegen gehört und glaubte, dass jemand wie sie auch zur Legende werden könnte. Mit ihrer treuen Kanone Boomer machte sie sich in die Welt auf und stürzt sich nun mit unerschütterlichem Mut und Optimismus in die Schlacht.", "blurb": "Wohingegen viele andere Yordle ihre Zeit mit Entdeckungen, Erfindungen oder einfach Unfug verbringen, fühlte sich Tristana schon immer von den Abenteuern großer Krieger inspiriert. Sie hatte schon viel über Runeterra mit seinen Fraktionen und Kriegen...", "allytips": ["Mit ihrer riesigen Waffe kann Tristana aus großer Entfernung auf Ziele schießen. <PERSON><PERSON><PERSON> dies aus, um zu ver<PERSON>dern, dass dich deine Gegner zwischen die Finger bekommen.", "<PERSON><PERSON> „Raketensprung“ ein, nachdem du deine „Sprengladung“ an einem Gegner aufgeladen hast, um ihn mit großem Schadensausstoß den Rest zu geben.", "Verwende „Schnellfeuer“, um mehr „Sprengladung“ auf gegnerischen Champions anzusammeln."], "enemytips": ["<PERSON><PERSON>, dass Tristana in einem Kampf „Schnellfeuer“ aktiviert, dann betäube sie und entferne dich von ihr, bis die Wirkungsdauer der Fähigkeit abgelaufen ist.", "Halte Abstand zu den Vasallen auf deiner Lane, um weniger Kollateralschaden durch die „Sprengladung“ zu erleiden."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 5, "difficulty": 4}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 300, "mpperlevel": 32, "movespeed": 325, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.5, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 1.5, "attackspeed": 0.656}, "spells": [{"id": "TristanaQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> feuert ihre Waffe schnell ab, um ihr Angriffstempo für kurze Zeit zu erhöhen.", "tooltip": "Tristana ist bereit zum Angriff und erhält {{ buffduration }}&nbsp;Sekunden lang <attackSpeed>{{ attackspeedmod*100 }}&nbsp;% Angriffstempo</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ attackspeedmod*100.000000 }}&nbsp;% -> {{ attackspeedmodnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TristanaQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TristanaW", "name": "Raketensprung", "description": "<PERSON>a feuert auf den Boden, um sich an einen entfernten Ort zu schleudern, wo sie bei der Landung den nahen Einheiten Schaden zufügt und Einheiten in der Umgebung kurzzeitig verlangsamt.", "tooltip": "Tristana katapultiert sich selbst an einen Zielort. Bei ihrer Landung verursacht sie <magicDamage>{{ landingdamage }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> {{ slowduration }}&nbsp;Sekunden lang um {{ slowmod*-100 }}&nbsp;%.<br /><br />Champion-Kills/Unterstützungen und <spellName>Sprengladung</spellName>-Explosionen auf Champions mit maximalen Steigerungen setzen die Abklingzeit dieser Fähigkeit zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TristanaW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TristanaE", "name": "Sprengladung", "description": "Wenn Tristana eine Einheit tötet, zerspringen ihre Kanonenkugeln in Schrapnelle und verursachen Schaden an nahen Gegnern. Kann aktiviert werden, um ein Ziel mit einer Bombe zu versehen, die nach kurzer Zeit explodiert und an umliegenden Einheiten Schaden verursacht.", "tooltip": "<spellPassive>Passiv: </spellPassive><PERSON><PERSON>, die Gegner töten, fügen umstehenden Gegnern <magicDamage>{{ passivedamage }}&nbsp;magischen Schaden</magicDamage> zu.<br /><br /><spellActive>Aktiv:</spellActive> Tristana platziert eine Bombe auf einem Gegner oder Turm, die nach {{ activeduration }}&nbsp;Sekunden umstehenden Gegnern <physicalDamage>{{ activedamage }}&nbsp;normalen Schaden</physicalDamage> zufügt. Der Schaden erhöht sich um {{ critchanceamp*100 }}&nbsp;% Chance auf kritische Treffer und um {{ activeperstackamp*100 }}&nbsp;%, wenn Tristana mit einem Angriff oder einer Fähigkeit trifft (bis zu 4&nbsp;Steigerungen).<br /><br />Bei {{ activemaxstacks }}&nbsp;Steigerungen explodiert die Bombe sofort (maximal <physicalDamage>{{ activemaxdamage }}&nbsp;normaler <PERSON></physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Explosionsschaden (Passiv)", "Grundladungsschaden", "Angriffsschadenskalierung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ activebadratio*100.000000 }}&nbsp;% -> {{ activebadrationl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TristanaR", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> l<PERSON>dt ihre Waffe mit einer gewaltigen Kanonenkugel und feuert sie auf eine gegnerische Einheit ab. Diese verursacht magischen Schaden und stößt das Ziel zurück. Wenn das Ziel eine Sprengladung-Bombe trägt, wird der Detonationsradius der Bombe verdoppelt.", "tooltip": "Tristana feuert eine gewaltige Kanonenkugel ab, die dem Ziel <magicDamage>{{ damagecalc }} magischen <PERSON></magicDamage> zufügt und es gemeinsam mit umstehenden Gegnern <status>zurückstößt</status> und für {{ stunduration }} Sekunden <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Rückstoßdistanz", "Betäubungsdauer:"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ knockbackdistance }} -> {{ knockbackdistanceNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Präzision", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> die Reichweite von <PERSON>, wenn sie eine <PERSON>ufe aufste<PERSON>t.", "image": {"full": "Tristana_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}