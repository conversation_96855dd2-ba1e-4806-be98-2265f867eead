{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Neeko": {"id": "<PERSON><PERSON><PERSON>", "key": "518", "name": "ニーコ", "title": "不思議のカメレオン", "image": {"full": "Neeko.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "518000", "num": 0, "name": "default", "chromas": false}, {"id": "518001", "num": 1, "name": "冬の奇跡ニーコ", "chromas": true}, {"id": "518010", "num": 10, "name": "スターガーディアン ニーコ", "chromas": false}, {"id": "518011", "num": 11, "name": "プレステージ スターガーディアン ニーコ", "chromas": false}, {"id": "518012", "num": 12, "name": "山海絵巻伝ニーコ", "chromas": true}, {"id": "518021", "num": 21, "name": "プレステージ スターガーディアン ニーコ(2022)", "chromas": false}, {"id": "518022", "num": 22, "name": "魅惑の魔女ニーコ", "chromas": true}, {"id": "518031", "num": 31, "name": "ストリートデーモン ニーコ", "chromas": true}, {"id": "518040", "num": 40, "name": "コスプレイヤー ニーコ", "chromas": true}], "lore": "遥か昔に途絶えたヴァスタヤ部族の末裔であるニーコは、他者の風貌を拝借してどんな集団にでも溶け込むことができる。彼女は相手の感情を吸収し、即座に敵か味方かを判別することができるのだ。ニーコがどこにいるのか、あるいはニーコの正体が何者なのか、確信を持てる者はいない。だが彼女に害をなそうという者は、やがてその真の力を目の当たりにするだろう。そして原初の霊的魔法の威力を身をもって知ることになるのだ。", "blurb": "遥か昔に途絶えたヴァスタヤ部族の末裔であるニーコは、他者の風貌を拝借してどんな集団にでも溶け込むことができる。彼女は相手の感情を吸収し、即座に敵か味方かを判別することができるのだ。ニーコがどこにいるのか、あるいはニーコの正体が何者なのか、確信を持てる者はいない。だが彼女に害をなそうという者は、やがてその真の力を目の当たりにするだろう。そして原初の霊的魔法の威力を身をもって知ることになるのだ。", "allytips": ["オプションメニューで固有スキルにホットキーを設定できる。デフォルトではShift+F1～F5だ。", "「駆け巡る色彩」は無駄使いを避けよう。意味もなく使っていると敵を警戒させてしまう。"], "enemytips": ["ニーコと相対した際にミニオンの背後に立つのは危険だ。「からまれ！」が強化されてしまう。", "ニーコが擬態中の場合、「ポップブロッサム」の警告のビジュアルは見えない。"], "tags": ["Mage", "Support"], "partype": "マナ", "info": {"attack": 1, "defense": 1, "magic": 9, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 104, "mp": 450, "mpperlevel": 30, "movespeed": 340, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "NeekoQ", "name": "弾ける花弁", "description": "魔法ダメージを与える種を投げる。種はチャンピオンに当たるか敵ユニットをキルすると再度爆発する。", "tooltip": "<magicDamage>{{ explosiondamage }}の魔法ダメージ</magicDamage>を与える種を投げる。これで敵をキルするか、チャンピオンまたは大型モンスターに当てると、再度爆発して<magicDamage>{{ seconddamage }}の魔法ダメージ</magicDamage>を与える。追加の爆発は最大2回まで。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "爆発ダメージ", "@AbilityResourceName@コスト", "モンスターへの追加ダメージ", "クールダウン"], "effect": ["{{ zonedamage }} -> {{ zonedamageNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ monsterbonus }} -> {{ monsterbonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NeekoQ.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NeekoW", "name": "シェイプスプリッター", "description": "自動効果により通常攻撃3回ごとに追加魔法ダメージを与え、少しの間、移動速度が増加する。発動すると指定方向にクローンを送り出し、再発動するとクローンの進行方向を変えられる。", "tooltip": "<passive>自動効果:</passive> 通常攻撃3回ごとに追加で<magicDamage>{{ passivebonusdamagecalc }}の魔法ダメージ</magicDamage>を与えて、{{ passivehasteduration }}秒間<speed>移動速度が{{ passivehaste }}%</speed>増加する。<br /><br /><active>発動効果:</active> {{ stealthduration }}秒間<keywordStealth>インビジブル</keywordStealth>状態になって、{{ cloneduration }}秒間持続するクローンを送り出す。自身とクローンは{{ hasteduration }}秒間、<speed>移動速度が{{ haste }}%</speed>増加する。<br /><br /><rules>クローンは「ペット移動のクリック」のホットキーを押すか、このスキルを<recast>再発動</recast>することで操作できる。<br />スキル、エモート、リコールを使用すると、クローンもそれを真似る。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果ダメージ", "発動効果による増加移動速度", "自動効果による増加移動速度", "クールダウン"], "effect": ["{{ passivedamage }} -> {{ passivedamageNL }}", "{{ haste }}% -> {{ hasteNL }}%", "{{ passivehaste }}% -> {{ passivehasteNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NeekoW.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "NeekoE", "name": "からまれ！", "description": "輪を飛ばして当たった敵すべてにダメージとスネア効果を与える。輪は敵をキルするかチャンピオンに触れると大きくなり、速度とスネア効果時間が増加する。", "tooltip": "<magicDamage>{{ basedamage }}の魔法ダメージ</magicDamage>と{{ minrootduration }}秒間の<status>スネア効果</status>を与える輪を飛ばす。<br /><br />敵に当たると輪が強化されて、サイズが拡大して速度が上がり、<status>スネア効果</status>が{{ maxrootduration }}秒間になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "最小スネア効果時間", "強化スネア効果時間", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ minrootduration }} -> {{ minrootdurationNL }}", "{{ maxrootduration }} -> {{ maxrootdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "NeekoE.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NeekoR", "name": "ポップブロッサム", "description": "少しの間準備してから宙に舞い上がり、周囲のすべての敵をノックアップさせる。さらに着地時に周囲の敵にダメージを与えてスタンさせる。擬態中は密かに準備を行える。", "tooltip": "少ししてから宙に舞い上がり、周囲のすべての敵を{{ delayuntilexplosion }}秒間<status>ノックアップ</status>させる。その後着地して、周囲のすべての敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ stunduration }}秒間<status>スタン</status>させる。<br /><br /><rules>擬態中はこのスキルを密かに準備できる。このスキルを発動してから{{ delaybeforepassiveremoval }}秒後に擬態は解除される。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "NeekoR.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "駆け巡る色彩", "description": "味方チャンピオンまたはマップ上の他のユニットの姿に化けることができる。行動妨害効果を受けるか、ダメージスキルを詠唱するか、チャンピオン以外のユニットに擬態した状態で敵タワーにダメージを与えるか、擬態したユニットの体力バーと同量のダメージを受けると擬態が解除される。", "image": {"full": "Neeko_P.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}