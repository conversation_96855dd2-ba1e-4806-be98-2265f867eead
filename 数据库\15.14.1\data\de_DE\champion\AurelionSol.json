{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"AurelionSol": {"id": "AurelionSol", "key": "136", "name": "Aurelion Sol", "title": "der Sternenschmied", "image": {"full": "AurelionSol.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "136000", "num": 0, "name": "default", "chromas": false}, {"id": "136001", "num": 1, "name": "Aschfahler Fürst Aurelion Sol", "chromas": false}, {"id": "136002", "num": 2, "name": "Mecha-Aurelion Sol", "chromas": true}, {"id": "136011", "num": 11, "name": "Sturmdrache Aurelion Sol", "chromas": false}, {"id": "136021", "num": 21, "name": "Tintenschatten-Aurelion Sol", "chromas": false}, {"id": "136031", "num": 31, "name": "Porzellan-Aurelion Sol", "chromas": false}], "lore": "Einst schmückte Aurelion Sol die weite Leere des Kosmos mit himmlischen Wundern, die er selbst erschuf. Jetzt muss er seine ehrfurchtgebietende Macht dem Willen eines Weltraumreiches unterwerfen, das ihn in die Knechtschaft gelockt hat. Aurelion Sol sehnt seine vergangenen Tage als Sternenschmied wieder herbei und würde die Himmelskörper selbst vom Firmament reißen, wenn er so seine Freiheit wiedererlangen könnte.", "blurb": "Einst schmückte Aurelion Sol die weite Leere des Kosmos mit himmlischen Wundern, die er selbst erschuf. Jetzt muss er seine ehrfurchtgebietende Macht dem Willen eines Weltraumreiches unterwerfen, das ihn in die Knechtschaft gelockt hat. Aurelion Sol...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 90, "mp": 530, "mpperlevel": 40, "movespeed": 335, "armor": 22, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "AurelionSolQ", "name": "Gebündeltes Licht", "description": "Aurelion Sol kanalisiert einige Sekunden lang seinen Drachenatem, verursacht Schaden am ersten getroffenen Gegner und verringerten Schaden an Gegnern in der Nähe. Jede Sekunde, in der sein Atem direkt auf einen Gegner gebündelt wird, verursacht zusätzlichen Schaden, der durch die Menge des gesammelten Sternenstaubs erhöht wird. Diese Fähigkeit sammelt Sternenstaub, wenn das Ziel ein Champion ist.", "tooltip": "Aurelion Sol speit bis zu {{ maxchannelduration }}&nbsp;Sekunden lang Sternenfeuer, das dem ersten getroffenen Gegner <magicDamage>{{ damagepersecond }}&nbsp;magischen Schaden</magicDamage> pro Sekunde zufügt und umstehenden Gegnern {{ aoemodifier*100 }}&nbsp;% dieses Schadens.<br /><br />Jede volle Sekunde von Sternenfeuer auf den gleichen Gegner verursacht eine Explosion von <magicDamage>{{ burstdamage }}&nbsp;magischen Schaden</magicDamage> plus <magicDamage>magischen Schaden</magicDamage> in H<PERSON><PERSON> von {{ burstbonustruedamagetochamps }} des maximalen Lebens und absorbiert <span class=\"color3458eb\">{{ qmassstolen }}&nbsp;Sternenstaub</span>, falls das Ziel ein Champion ist.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "Schaden pro Sekunde", "Explosionsschaden", "Maximale Kanalisierungsdauer"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ rankdamagepersecond }} -> {{ rankdamagepersecondNL }}", "{{ rankburstdamage }} -> {{ rankburstdamageNL }}", "{{ maxchannelduration }} -> {{ maxchanneldurationNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana pro Sekunde", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolQ.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ manacostpersecond }}&nbsp;Mana pro Sekunde"}, {"id": "AurelionSolW", "name": "Astralflug", "description": "Aurelion Sol fliegt in Zielrichtung über Terrain. In diesem Zustand kann er andere Fähigkeiten einsetzen. „Gebündeltes Licht“ hat keine Abklingzeit oder maximale Kanalisierungszeit mehr und verursacht beim Fliegen erhöhten Schaden.<br><br>Die verbleibende Abklingzeit von „Astralflug“ wird verringert, wenn ein gegnerischer Champion stirbt, nachdem er kürzlich durch Aurelion Sol Schaden erlitten hat.<br><br>Sternenstaub erhöht die maximale Reichweite von „Astralflug“.", "tooltip": "Aurelion Sol fliegt in eine Richtung. Während er fliegt, hat <spellName>Gebündeltes Licht</spellName> keine Abklingzeit, keine maximale Kanalisierungsdauer, und der gleichbleibende Schaden ist um {{ truedamagebonus*100 }}&nbsp;% erhöht.<br /><br />Für Kills/Unterstützungen bei Champions innerhalb von {{ resetwindow }}&nbsp;Sekunden, nachdem sie Schaden erlitten haben, werden {{ tooltiptakedowncooldownmultiplier }}&nbsp;% der Abklingzeit dieser Fähigkeit erstattet.<br /><br /><recast>Reaktivierung:</recast> Beendet den Flug vorzeitig.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzlicher magischer Sc<PERSON> (%)", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ truedamagebonus*100.000000 }}&nbsp;% -> {{ truedamagebonusnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ cd }} -> {{ cdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "AurelionSolW.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolE", "name": "Singularität", "description": "Aurelion Sol beschwört ein schwarzes Loch, das Gegnern Schaden zufügt und sie langsam in seine Mitte zieht. Diese Fähigkeit gewährt jedes Mal Sternenstaub, wenn ein Gegner innerhalb des schwarzen Lochs stirbt und für jede Sekunde, die ein gegnerischer Champion darin gefangen ist. Das Zentrum des schwarzen Lochs exekutiert Gegner, die über weniger als einen bestimmten Prozentsatz ihres maximalen Lebens verfügen. Sternenstaub vergrößert den Bereich der Singularität und erhöht die Exekutionsschwelle.", "tooltip": "Aurelion Sol beschwört ein schwarzes Loch, das <magicDamage>{{ damagepersecond }}&nbsp;magischen <PERSON>en</magicDamage> pro Sekunde verursacht und Gegner {{ duration }}&nbsp;Sekunden lang in sein Zentrum <status>zieht</status>. <PERSON><PERSON><PERSON> im Zentrum, die über weniger als <scaleHealth>{{ currentexecutionthreshold }}&nbsp;% ihres maximalen Lebens</scaleHealth> verfügen, sterben sofort.<br /><br />Das schwarze Loch absorbiert <span class=\"color3458eb\">Sternenstaub</span>, wenn ein Gegner darin stirbt und jede Sekunde, in der sich ein gegnerischer Champion darin befindet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolE.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolR", "name": "Sternenfall/Einsturz des Himmels", "description": "Sternenfall: Aurelion Sol lässt einen Stern auf die Erde stürzen. Der Einschlag verursacht magischen Schaden und betäubt G<PERSON>ner, zus<PERSON>zlich gewährt er Sternenstaub für jeden getroffenen gegnerischen Champion. Wenn er genug Sternenstaub sammelt, wird Aurelion Sols nächster „Sternenfall“ zu „Einsturz des Himmels“.<br><br>Einsturz des Himmels: Aurelion Sol zieht einen riesigen Stern vom Himmel herab. Die Einschlagzone sowie der Schaden sind erhöht und Gegner werden hochgeschleudert anstatt betäubt. Vom Rand der Einschlagzone breitet sich dann eine Schockwelle aus, die getroffenen Gegnern Schaden zufügt und sie verlangsamt. Sternenstaub vergrößert den Wirkungsbereich von „Sternenfall“ und „Einsturz des Himmels“.", "tooltip": "Aurelion Sol zieht einen Stern vom Himmel und lässt ihn auf die Erde stürzen, wodurch er <magicDamage>{{ maxdamagetooltip }}&nbsp;magischen Schaden</magicDamage> verursacht, Gegner {{ stunduration }}&nbsp;Sekunde lang <status>betäubt</status> und für jeden getroffenen Champion <span class=\"color3458eb\">{{ massstolen }}&nbsp;Sternenstaub</span> absorbiert.<br /><br />Wenn er <span class=\"color3458eb\">{{ calamitystacks }}&nbsp;Sternenstaub</span> gesammelt hat, wird der nächste <spellName>Sternenfall</spellName> zu <spellName>Einsturz des Himmels</spellName>.<br /><br /><spellName>Einsturz des Himmels</spellName>: Aurelion Sol zieht den Zorn eines ganzen Sternbilds vom Kosmos herab, wodurch er <magicDamage>{{ r2damage }}&nbsp;magischen Schaden</magicDamage> in einem größeren Bereich verursacht, getroffene Gegner {{ stunduration }}&nbsp;Sekunde lang <status>hochschleudert</status> und eine gigantische Schockwelle entfesselt, die Champions und epischen Monstern <magicDamage>{{ shockwavedamage }}&nbsp;magischen Schaden</magicDamage> zufügt sowie alle getroffenen Gegner 1&nbsp;Sekunde lang um {{ shockwaveslow*100 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Verstärkter Schaden", "<PERSON><PERSON><PERSON> der Schockwelle"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*1.250000 }} -> {{ basedamagenl*1.250000 }}", "{{ basedamage*0.900000 }} -> {{ basedamagenl*0.900000 }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AurelionSolR.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Kosmischer Schöpfer", "description": "Aurelion Sols schadensverursachende Fähigkeiten zerlegen Gegner in Steigerungen von <font color='#3458eb'>Sternenstaub</font>, die jede seiner Fähigkeiten dauerhaft verbessern. ", "image": {"full": "AurelionSolP.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}