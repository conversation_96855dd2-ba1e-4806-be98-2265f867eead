{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zeri": {"id": "<PERSON><PERSON>", "key": "221", "name": "<PERSON><PERSON>", "title": "Der Funke von <PERSON>", "image": {"full": "Zeri.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "221000", "num": 0, "name": "default", "chromas": false}, {"id": "221001", "num": 1, "name": "Verblühte Rose Zeri", "chromas": true}, {"id": "221010", "num": 10, "name": "Meeresrauschen-Zeri", "chromas": true}, {"id": "221019", "num": 19, "name": "Reise der Unsterblichen-Zeri", "chromas": true}, {"id": "221028", "num": 28, "name": "Schreckensnacht-Zeri", "chromas": true}, {"id": "221029", "num": 29, "name": "Schreckensnacht-Zeri (Prestige)", "chromas": false}], "lore": "<PERSON><PERSON>, eine eigenwillige und aufgeweckte junge Frau aus der Arbeiterklasse von <PERSON>, kanalisiert ihre elektrische Magie, um sich selbst und ihre selbstgebaute Waffe aufzuladen. Ihre Kräfte sind so unberechenbar wie ihre Emotionen und sie versprüht mit jedem Funken Lebensfreude. Zeri steckt voller Hilfsbereitschaft und bringt die Liebe für ihre Familie und ihre Heimat in jeden Kampf mit. Ihr Eifer kann sie zwar manchmal in Bedrängnis bringen, doch Zeri glaubt an eine unumstößliche Wahrheit: Setze dich für deine Gemeinschaft ein und sie wird sich für dich einsetzen.", "blurb": "<PERSON><PERSON>, eine eigenwillige und aufgeweckte junge Frau aus der Arbeiterklasse von <PERSON>, kanalisiert ihre elektrische Magie, um sich selbst und ihre selbstgebaute Waffe aufzuladen. Ihre Kräfte sind so unberechenbar wie ihre Emotionen und sie versprüht mit...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 250, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "ZeriQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "„Feuerstoß“ schießt eine Salve von 7 Schüssen, die dem ersten getroffenen Gegner Angriffsschaden zufügen. Diese Fähigkeit gilt als Angriff.", "tooltip": "<PERSON>eri feuert eine Salve von {{ numberofmissiles }}&nbsp;<PERSON><PERSON><PERSON><PERSON>, die dem ersten getroffenen Gegner <physicalDamage>{{ activedamagethatcancrit }}&nbsp;normalen Schaden</physicalDamage> zufügen. Diese Fähigkeit gilt als Angriff. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit Gesamtangriffsschaden"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ activeadratio*100.000000 }}&nbsp;% -> {{ activeadrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "ZeriQ.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "ZeriW", "name": "Ultraschock-Laser", "description": "Zeri feuert einen elektrischen Impuls ab, der den ersten getroffenen Gegner verlangsamt und ihm Schaden zufügt. Wenn der Schuss auf eine Mauer trifft, verlängert er sich zu einem Laser mit hoher Reichweite.", "tooltip": "Zeri feuert einen elektrischen Impuls ab, der <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und den ersten getroffenen Gegner {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercent*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br /><PERSON><PERSON> der Schuss auf Terrain trifft, verlängert er sich zu einem Laser, der diese Effekte in einem Bereich auslöst und Champions und Monster kritisch trifft.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent*100.000000 }}&nbsp;% -> {{ slowpercentnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "ZeriW.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZeriE", "name": "Überspannung", "description": "Zeri springt ein Stück nach vorn und verstärkt ihre nächsten 3 Schüsse von „Feuerstoß“, wodurch sie getroffene Gegner durchdringen. Wenn Zeri auf Terrain trifft, schwingt sie sich darüber hinweg.", "tooltip": "Zeri springt ein Stück nach vorn. Wenn sie auf Terrain trifft, schwingt sie sich darüber hinweg und erhöht dadurch die Reichweite ihres Sprungs. Für die nächsten {{ buffduration }}&nbsp;Sekunden durchdringen ihre Schüsse mit <spellName>Feuerstoß</spellName> das Ziel und fügen allen Gegnern nach dem ersten Treffer {{ pendamagepercent*100 }}&nbsp;% magischen Schaden zu. Das erste getroffene Ziel erleidet zusätzlich <magicDamage>{{ bonusdamagetotal }}&nbsp;magischen Schaden</magicDamage>. <br /><br />Die Abklingzeit dieser Fähigkeit wird um {{ cdreductionperhit }}&nbsp;Sekunden verkürzt, wenn sie einen gegnerischen Champion mit Angriffen oder Fähigkeiten trifft. Kritische Treffer verringern die Abklingzeit stattdessen um {{ critcdreductionperhit }}&nbsp;Sekunden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> (%)", "Grundschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ pendamagepercent*100.000000 }}&nbsp;% -> {{ pendamagepercentnl*100.000000 }}&nbsp;%", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [90, 85, 80, 75, 70], "costBurn": "90/85/80/75/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "ZeriE.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZeriR", "name": "Blitzeinschlag", "description": "Zeri entfesselt eine Elektrizitätsnova und überl<PERSON>dt sich selbst, wodurch sie erhöhten Schaden und Lauftempo-Steigerungen erhält. Die Überladung erneuert sich und wird jedes <PERSON>, wenn sie einen gegnerischen Champion trifft. Während der Überladung wird „Feuerstoß“ zu einem schnelleren Dreifachschuss, der Kettenblitze auf Gegner überspringen lässt.", "tooltip": "Zeri entfesselt eine Elektrizitätsnova, die Gegnern in der Nähe <magicDamage>{{ totalactivedamage }}&nbsp;magischen Schaden</magicDamage> zufügt. Zeri erhält {{ rduration }}&nbsp;Sekunden lang <attackSpeed>{{ baseaspercent*100 }}&nbsp;% Angriffstempo</attackSpeed> und <speed>{{ basebonusms*100 }}&nbsp;% Lauftempo</speed>. Wenn sie einen gegnerischen Champion mit einem Angriff oder einer Fähigkeit trifft, wird die Dauer der Fähigkeit verlängert. Außerdem erhält sie {{ maxhyperchargeduration }}&nbsp;Sekunden lang eine Steigerung von „Überladung“. Kritische Treffer fügen 2&nbsp;zusätzliche Steigerungen hinzu. Jede Steigerung gewährt <speed>{{ mspercent*100 }}&nbsp;% Lauftempo</speed>.<br /><br />W<PERSON><PERSON><PERSON> dieser Zeit wird <spellName>Feuerstoß</spellName> zu einem schnelleren Dreifachschuss, der Gegnern in der Nähe nacheinander <physicalDamage>{{ chainphysicaldamage }}&nbsp;normalen Schaden</physicalDamage> zufügt.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ activedamage }} -> {{ activedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 75, 70], "cooldownBurn": "80/75/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ZeriR.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Lebende Batterie", "description": "Z<PERSON>s Angriffe verursachen magischen Schaden und gelten als Fähigkeiten. Wenn sich Z<PERSON> bewegt und „<PERSON>uerstoß“ ausführt, wird ihr Energiespeicher aufgeladen. Bei voller Aufladung verursacht ihr nächster Angriff zusätzlichen Schaden.", "image": {"full": "ZeriP.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}