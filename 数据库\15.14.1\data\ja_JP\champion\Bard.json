{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "バード", "title": "流離いの庇護者", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "古の賢樹バード", "chromas": false}, {"id": "432005", "num": 5, "name": "雪遊びバード", "chromas": true}, {"id": "432006", "num": 6, "name": "吟遊詩人バード", "chromas": false}, {"id": "432008", "num": 8, "name": "宇宙飛行士バード", "chromas": true}, {"id": "432017", "num": 17, "name": "カフェキューティーズ バード", "chromas": true}, {"id": "432026", "num": 26, "name": "山海絵巻伝バード", "chromas": true}, {"id": "432035", "num": 35, "name": "T1 バード", "chromas": true}, {"id": "432037", "num": 37, "name": "精霊の花祭りバード", "chromas": true}], "lore": "星の向こうからやってきた旅人であるバードは幸運の使者であり、生命が混沌の無関心に耐えられる平衡を保つために戦っている。ルーンテラにはこの世のものとは思えない彼の不思議な性質を伝える歌が数多く残っているが、誰もがこの宇宙の放浪者は強力な魔法の力を宿す遺物を求めてやってきたのだと考えている。自身の手伝いをする陽気な精霊のミィプたちに囲まれたバードの行動には、悪意など一切感じられない。彼は大いなる善のために活動している…彼ならではの不思議なやり方で。", "blurb": "星の向こうからやってきた旅人であるバードは幸運の使者であり、生命が混沌の無関心に耐えられる平衡を保つために戦っている。ルーンテラにはこの世のものとは思えない彼の不思議な性質を伝える歌が数多く残っているが、誰もがこの宇宙の放浪者は強力な魔法の力を宿す遺物を求めてやってきたのだと考えている。自身の手伝いをする陽気な精霊のミィプたちに囲まれたバードの行動には、悪意など一切感じられない。彼は大いなる善のために活動している…彼ならではの不思議なやり方で。", "allytips": ["「チャイム」を集めて「ミィプ」を強化することも重要だが、レーンにいる味方にも気を配ること！ 「精霊の旅路」で他の味方と共に颯爽と駆けつけることで、戦況を一気に好転させることができる。", "「回復の遺物」は十分チャージされてから使うと、より多くの体力を回復できる。", "「精霊の旅路」は敵も利用でき、バードのアルティメットスキルは味方に対しても同様の効果があることをお忘れなく！"], "enemytips": ["バードが出現させる「精霊の旅路」は、どちらのチームのチャンピオンも通り抜けることができる。安全だと判断したら、バードを追ってゲートをくぐり抜けよう。", "バードの「回復の遺物」は触れれば破壊できるので、できれば敵に利用される前に踏み潰してしまおう。", "バードのアルティメットスキル「運命の調律」は、敵も味方もモンスターもタワーも平等に効果を受ける。わざと範囲内に飛び込んで危機を回避することも可能だ！"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "宇宙の法則", "description": "魔法のエネルギーを発射し、最初に命中した敵にスロウを与える。貫通したエネルギーが壁に当たった場合は最初の対象が、別の敵ユニットに当たった場合は両方の対象がスタン状態になる。", "tooltip": "エネルギー弾を発射し、最初に命中した2体の敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。1体目には{{ slowduration }}秒間{{ slowamountpercentage }}%の<status>スロウ効果</status>を付与する。<br /><br />エネルギー弾が2体目の敵にも命中するか、壁に当たった場合、スキルが命中したすべての敵を{{ stunduration }}秒間<status>スタン</status>させる。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果時間", "スタン効果時間:", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "BardW", "name": "回復の遺物", "description": "体力を回復する遺物を出現させる。遺物は短時間徐々に効果が増大し、最初に触れた味方の体力を回復して移動速度を増加させると消滅する。", "tooltip": "体力を回復する遺物を出現させる。遺物は最初に触れた味方の<speed>移動速度を{{ calc_movespeed }}</speed>増加させて({{ movespeed_duration }}秒かけて元に戻る)、最低でも<healing>体力を{{ initialheal }}</healing>回復する。遺物は出現から{{ chargeuptime }}秒経過すると効果が増加し、最大の<healing>体力を{{ maxheal }}</healing>回復する。<br /><br />遺物は同時に{{ maxpacks }}個まで出現させておくことができる。敵チャンピオンが触れた場合、遺物は破壊される。<br /><br />このスキルは{{ ammo_limit }}回までチャージできる。<br /><br />現在有効な遺物: {{ f1 }} / {{ f2 }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本体力回復量", "最大体力回復量", "移動速度"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}% -> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "BardE", "name": "精霊の旅路", "description": "付近の地形に一方通行の魔法のトンネルを出現させる。敵も味方も同じようにトンネルを通り抜けられるが、レベルに応じて味方が通り抜ける速度は増加する。", "tooltip": "{{ e1 }}秒間、地形を通過することができる一方通行のポータルを開く。入り口に接近することで、どのチャンピオンでもポータルに入ることができる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "BardR", "name": "運命の調律", "description": "精霊の魔力を放つ。魔力は放物線を描いて飛んでいき、着弾と同時に範囲内のすべてのユニットとタワーをしばらく停止させる。", "tooltip": "魔法の防護エネルギーを一定範囲に向けて放ち、当たったすべてのユニットと建造物を{{ rstasisduration }}秒間、固有時停止状態にする。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "旅人の呼び声", "description": "<font color='#FF9900'>ミィプ:</font> ミィプと呼ばれる精霊を呼び寄せて通常攻撃を支援させ、追加魔法ダメージを与える。一定数の<font color='#cccc00'>チャイム</font>を集めると、ミィプの攻撃が範囲攻撃になり、命中した敵にスロウ効果も与えるようになる。<br><br><font color='#FF9900'>いにしえの鐘:</font> 古代の<font color='#cccc00'>チャイム</font>がランダムに出現し、回収するたびに経験値を獲得して、マナが回復するほか、非戦闘時の移動速度が増加する。", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}