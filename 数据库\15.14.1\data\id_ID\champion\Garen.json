{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Garen": {"id": "<PERSON><PERSON><PERSON>", "key": "86", "name": "<PERSON><PERSON><PERSON>", "title": "The Might of Demacia", "image": {"full": "Garen.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "86000", "num": 0, "name": "default", "chromas": true}, {"id": "86001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86002", "num": 2, "name": "Desert Trooper <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86003", "num": 3, "name": "Commando Garen", "chromas": false}, {"id": "86004", "num": 4, "name": "Dreadknight Garen", "chromas": false}, {"id": "86005", "num": 5, "name": "Rugged <PERSON>n", "chromas": false}, {"id": "86006", "num": 6, "name": "Steel Legion Garen", "chromas": false}, {"id": "86010", "num": 10, "name": "Rogue Admiral <PERSON><PERSON>", "chromas": false}, {"id": "86011", "num": 11, "name": "Warring Kingdoms Garen", "chromas": true}, {"id": "86013", "num": 13, "name": "God-<PERSON> <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86014", "num": 14, "name": "De<PERSON>cia <PERSON>", "chromas": true}, {"id": "86022", "num": 22, "name": "Mecha <PERSON>", "chromas": false}, {"id": "86023", "num": 23, "name": "Prestige Mecha Kingdoms Garen", "chromas": false}, {"id": "86024", "num": 24, "name": "Battle Academia Garen", "chromas": true}, {"id": "86033", "num": 33, "name": "My<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "86044", "num": 44, "name": "Fallen God-King <PERSON><PERSON>n", "chromas": false}], "lore": "Warrior pemberani dan mulia, <PERSON>aren bertarung sebagai salah satu anggota Dauntless Vanguard. Dia terkenal di antara rekan-rekannya dan dihormati musuh. Sebagai keturunan keluarga Crownguard yang terpandang, dia dipercaya untuk menjaga Demacia dan cita-citanya. Dipersenjatai armor dengan magic resistant dan pedang yang sangat besar, Garen siap menghadapi mage dan sorcerer di medan pertempuran dengan putaran baja keadilan.", "blurb": "Warrior pemberani dan mulia, <PERSON><PERSON><PERSON> bertarung sebagai salah satu anggota Dauntless Vanguard. Dia terkenal di antara rekan-rekannya dan dihormati musuh. Sebagai keturunan keluarga Crownguard yang terpandang, dia dipercaya untuk menjaga Demacia dan...", "allytips": ["Regenerasi Garen meningkat drastis jika dia bisa menghindari damage selama beber<PERSON> detik.", "Judgment men<PERSON><PERSON><PERSON>an damage maksimum saat hanya mengenai satu target. Untuk trading yang efektif, coba posisikan agar hanya champion musuh yang kena.", "Garen hanya dibatasi oleh cooldown, jadi membuat item seperti Black Cleaver sangat efektif untuknya."], "enemytips": ["Stack item armor untuk <PERSON><PERSON>an jumlah physical damage yang <PERSON>aren berikan.", "Coba untuk kabur dari Garen saat <PERSON> semakin rendah, karena dia bisa mematikanmu dengan cepat menggunakan Demacian Justice.", "hati-hati jika menyerang Garen di semak. Sering kali kita terkena damage penuh dari Judgment.", "Judgment memberikan damage maksimum saat hanya mengenai satu target. <PERSON>ka tidak mungkin untuk menjauh dari <PERSON>, menyingkirlah melewati minion sekutu untuk mengurangi damage yang diterima."], "tags": ["Fighter", "Tank"], "partype": "Tidak ada", "info": {"attack": 7, "defense": 7, "magic": 1, "difficulty": 5}, "stats": {"hp": 690, "hpperlevel": 98, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 38, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.5, "attackspeedperlevel": 3.65, "attackspeed": 0.625}, "spells": [{"id": "GarenQ", "name": "Decisive Strike", "description": "<PERSON><PERSON><PERSON> menda<PERSON> burst Move Speed sehingga semua efek slow pada dirinya akan dihilangkan. Serangan berikutnya akan menghantam bagian vital musuh, men<PERSON><PERSON><PERSON><PERSON> damage bonus dan menerapkan efek silence pada musuh.", "tooltip": "Garen menghapus semua efek <status>Slow</status> pada dirinya dan mendapatkan <speed>{{ movementspeedamount*100 }}% Move Speed</speed> selama {{ movementspeedduration }} detik.<br /><br />Serangan berikutnya akan menerapkan <status>Silence</status> selama {{ silenceduration }} detik dan mengh<PERSON>lkan <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON> Move Speed"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ movementspeedduration }}-> {{ movementspeeddurationNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "GarenQ.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "GarenW", "name": "Courage", "description": "Garen meningkatkan Armor dan Magic Resist secara pasif dengan membunuh musuh. Dia juga dapat mengaktifkan ability ini untuk mendapatkan shield dan tenacity selama beberapa saat, diikuti dengan lebih sedikit jumlah pengurangan damage untuk durasi yang lebih lama.", "tooltip": "<spellPassive>Pasif:</spellPassive> Garen memiliki <scaleArmor>{{ resistsfortooltip }} Armor bonus</scaleArmor> dan <scaleMR>{{ resistsfortooltip }} Magic Resist bonus</scaleMR>. Membunuh unit akan memberikan <attention>{{ resistgainonkilltooltip }} resist</attention> secara permanen, hingga maksimum <attention>{{ resistmax }}</attention>.<br /><br /><spellActive>Aktif:</spellActive> Garen meningkatkan courage selama {{ drduration }} detik, mengurangi damage yang akan datang sebesar {{ drpercent*100 }}%. Dia juga mendapatkan <shield>{{ totalshield }} Shield</shield> dan <slow>{{ upfronttenacity*100 }}% Tenacity</slow> selama {{ upfrontduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Jumlah Shield", "Pengurangan Damage", "Cooldown"], "effect": ["{{ baseshield }}-> {{ baseshieldNL }}", "{{ drpercent*100.000000 }}%-> {{ drpercentnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GarenW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "GarenE", "name": "Judgment", "description": "Garen memutar pedang dengan cepat di sekeliling tubuhnya, <PERSON><PERSON><PERSON><PERSON><PERSON> physical damage pada musuh di sekitar.", "tooltip": "Garen memutar pedang dengan cepat selama {{ duration }} detik, men<PERSON><PERSON><PERSON>an <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> {{ f1 }} kali sepanjang durasi. Musuh terdekat menerima <physicalDamage>{{ nearestenemybonus*100 }}% peningkatan damage</physicalDamage>. Champion yang terkena {{ stackstoshred }} serangan akan kehilangan <scaleArmor>{{ shredamount*100 }}% Armor</scaleArmor> selama {{ shredduration }} detik.<br /><br /><recast>Recast</recast>: Garen mengakhiri Ability ini lebih awal.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Scaling Attack Damage Per Spin", "Cooldown"], "effect": ["{{ basedamagepertick }}-> {{ basedamagepertickNL }}", "{{ adratiopertick*100.000000 }}%-> {{ adratioperticknl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "GarenE.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "GarenR", "name": "De<PERSON>cian Justice", "description": "<PERSON><PERSON><PERSON> memanggil the Might of Demacia demi mengeksekusi champion musuh.", "tooltip": "Garen memanggil the Might of Demacia untuk menghabi<PERSON> musuhnya, <PERSON><PERSON><PERSON><PERSON><PERSON> <trueDamage>{{ basedamage }} plus {{ executedamage*100 }}% true damage dari Health yang hilang</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "Persentase Damage Health yang Hilang"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ executedamage*100.000000 }}%-> {{ executedamagenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "GarenR.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}], "passive": {"name": "Perseverance", "description": "Jika Garen tidak terkena damage atau ability musuh selama beberapa saat, ia akan memulihkan sejumlah persentase Health total miliknya tiap detik.", "image": {"full": "Garen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}