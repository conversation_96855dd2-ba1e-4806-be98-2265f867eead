{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Azir": {"id": "<PERSON><PERSON><PERSON>", "key": "268", "name": "<PERSON><PERSON><PERSON>", "title": "the Emperor of the Sands", "image": {"full": "Azir.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "268000", "num": 0, "name": "default", "chromas": false}, {"id": "268001", "num": 1, "name": "Galactic Azir", "chromas": false}, {"id": "268002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "268003", "num": 3, "name": "SKT T1 Azir", "chromas": false}, {"id": "268004", "num": 4, "name": "Warring Kingdoms Azir", "chromas": false}, {"id": "268005", "num": 5, "name": "Elderwood Azir", "chromas": true}, {"id": "268014", "num": 14, "name": "Worlds 2022 Azir", "chromas": true}, {"id": "268019", "num": 19, "name": "Attorney <PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> was a mortal emperor of Shurima in a far distant age, a proud man who stood at the cusp of immortality. His hubris saw him betrayed and murdered at the moment of his greatest triumph, but now, millennia later, he has been reborn as an Ascended being of immense power. With his buried city risen from the sand, <PERSON><PERSON><PERSON> seeks to restore Shurima to its former glory.", "blurb": "<PERSON><PERSON><PERSON> was a mortal emperor of Shurima in a far distant age, a proud man who stood at the cusp of immortality. His hubris saw him betrayed and murdered at the moment of his greatest triumph, but now, millennia later, he has been reborn as an Ascended...", "allytips": ["Be careful about putting down both of the soldiers stored by <PERSON><PERSON>! Unless you're going all-in, always have a soldier in your back pocket if you need to escape over a wall or if you misposition your other soldier and need damage now.", "In the lane, try to position your soldiers between the enemy's minions and the enemy champion. This way you can use them both to last hit and to project threat onto your lane opponent.", "Think of <PERSON>'s Divide as a defensive ability first and foremost. Use it when the enemy engages on you or your allies. Remember that your team can walk through the soldiers summoned by Emperor's Divide and use this to your advantage when enemy melee champions engage on you.", "Be a bird!"], "enemytips": ["<PERSON><PERSON><PERSON> relies on his soldiers to deal damage and can only move them so often. Try to capitalize on windows of time when his soldiers are stationary.", "<PERSON><PERSON><PERSON> has immense damage over a prolonged amount of time but lacks the upfront burst of other mages. Try to burst him out before he can turn a confrontation around.", "Try to think of Sand Soldiers as fire. Don't stand in the fire."], "tags": ["Mage", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 3, "magic": 8, "difficulty": 9}, "stats": {"hp": 575, "hpperlevel": 119, "mp": 320, "mpperlevel": 40, "movespeed": 330, "armor": 25, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.5, "attackspeedperlevel": 5.5, "attackspeed": 0.625}, "spells": [{"id": "AzirQWrapper", "name": "Conquering Sands", "description": "<PERSON><PERSON><PERSON> sends all Sand Soldiers towards a location. Sand Soldiers deal magic damage to enemies they pass through and apply a slow for 1 second.", "tooltip": "<PERSON><PERSON><PERSON> orders all <keywordMajor>Sand Soldiers</keywordMajor> to an area, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies they pass through and <status>Slowing</status> them by {{ e2 }}% for 1 second.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [60, 80, 100, 120, 140], [25, 25, 25, 25, 25], [0, 0, 0, 0, 0], [70, 70, 70, 70, 70], [1600, 1600, 1600, 1600, 1600], [200, 200, 200, 200, 200], [325, 325, 325, 325, 325], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/80/100/120/140", "25", "0", "70", "1600", "200", "325", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [740, 740, 740, 740, 740], "rangeBurn": "740", "image": {"full": "AzirQWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AzirW", "name": "Arise!", "description": "<PERSON><PERSON><PERSON> summons a Sand Soldier to attack nearby targets for him, replacing his basic attack against targets within the soldier's range. Their attacks deal magic damage to enemies in a line. Arise! also passively grants attack speed to <PERSON><PERSON><PERSON> and his Sand Soldiers.", "tooltip": "<PERSON><PERSON><PERSON> summons a <keywordMajor><PERSON></keywordMajor> for {{ e1 }} seconds. When <PERSON><PERSON><PERSON> Attacks an enemy near a <keywordMajor>Sand Soldier</keywordMajor>, he instead orders the soldiers to stab, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> in their direction.<br /><br />This Ability has {{ maxammo }} charges.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Solder Attack Damage", "Total AP Ratio", "Recharge Time", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio }} -> {{ apratioNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [25, 25, 25, 25, 25], [0.5, 0.5, 0.5, 0.5, 0.5]], "effectBurn": [null, "10", "0", "0", "0", "0", "3", "0", "5", "25", "0.5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "AzirW.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AzirEWrapper", "name": "Shifting Sands", "description": "<PERSON><PERSON><PERSON> shields himself briefly and dashes to one of his Sand Soldiers, damaging enemies. If he hits an enemy champion, he instantly readies a new Sand Soldier for deployment and halts his dash.", "tooltip": "<PERSON><PERSON><PERSON> grants himself <shield>{{ totalshield }} Shield</shield> for {{ e6 }} seconds and dashes to one of his <keywordMajor>Sand Soldiers</keywordMajor>, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies he passes through.<br /><br />If <PERSON><PERSON><PERSON> hits an enemy champion, he stops and gains a <keywordMajor>Sand Soldier</keywordMajor> charge.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Shield Amount", "Damage", "Cooldown"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [1700, 1700, 1700, 1700, 1700], [0, 0, 0, 0, 0], [60, 100, 140, 180, 220], [70, 110, 150, 190, 230], [0.3, 0.5, 0.7, 0.9, 1.1], [1.5, 1.5, 1.5, 1.5, 1.5], [1100, 1100, 1100, 1100, 1100], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [25, 25, 25, 25, 25]], "effectBurn": [null, "1700", "0", "60/100/140/180/220", "70/110/150/190/230", "0.3/0.5/0.7/0.9/1.1", "1.5", "1100", "0", "5", "25"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "AzirEWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AzirR", "name": "Emperor's Divide", "description": "<PERSON><PERSON><PERSON> summons a wall of soldiers which charge forward, knocking back and damaging enemies.", "tooltip": "<PERSON><PERSON><PERSON> summons a wall of armored soldiers that charges forward, <status>Knocking Back</status> enemies and dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>. The soldiers remain, blocking enemy paths for {{ e4 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Number of Soldiers", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ numberofsoldiers }} -> {{ numberofsoldiersNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [200, 400, 600], [0, 0, 0], [6, 7, 8], [5, 5, 5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "200/400/600", "0", "6/7/8", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "AzirR.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>'s Legacy", "description": "<PERSON><PERSON><PERSON> can summon the Disc of the Sun from the ruins of allied or enemy turrets.", "image": {"full": "Azir_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}