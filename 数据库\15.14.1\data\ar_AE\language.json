{"type": "language", "version": "15.14.1", "data": {"Back": "Back", "Continue": "Continue", "Language": "Language", "ItemInfo": "Item Info", "NextRank_": "Next Rank:", "Rank_": "Rank:", "PlayingAs": "Playing As", "PlayingAgainst": "Playing Against", "CD_": "CD:", "Range": "Range", "Range_": "Range:", "Details_": "Details:", "PrimaryRole": "Primary Role", "mobileCompanion": "Companion", "mobileForum": "Forum", "mobileFriends": "Friends", "mobilePleaseWait": "Please wait...", "mobileNews": "News", "modeClassic": "Classic", "modeOdin": "Definitely Not Dominion", "modeAram": "ARAM", "modeTutorial": "Tutorial", "modeOneforall": "FRONTEND_oneforall_game_mode_name", "modeFirstblood": "FRONTEND_firstblood_game_mode_name", "mode6v6": "FRONTEND_6v6_game_mode_name", "modeCof": "FRONTEND_cof_game_mode_name", "Map1": "Summ<PERSON><PERSON>'s Rift", "Map8": "The Crystal Scar", "Map10": "The Twisted Treeline", "Map12": "Howling Abyss", "categoryChampion": "Champions", "categoryItem": "Items", "categoryMastery": "Masteries", "categoryRune": "Runes", "categorySummoner": "Summoner Spells", "Gold": "Gold", "Level": "Level", "Abilities": "Abilities", "ChampionInfo": "Champion Info", "Lore": "Lore", "Stats": "Stats", "Tips": "Tips", "statAbility": "Ability", "statAttack": "Attack", "statDefense": "Defense", "statDifficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statUtility": "Utility", "Assassin": "Assassin", "Fighter": "Fighter", "Marksman": "Marksman", "Mage": "Mage", "Support": "Support", "Tank": "Tank", "spells_Self": "Self", "spells_target_0": "Self", "spells_target_1": "Target", "spells_target_2": "Area", "spells_target_3": "Cone", "spells_target_4": "Self Area", "spells_target_5": "Variable", "spells_target_6": "Location", "spells_target_7": "Direction", "spells_target_8": "Vector Direction", "spells_target_100": "Global", "AllItems": "All Items", "Armor": "Armor", "Attack": "Attack", "AttackSpeed": "Attack Speed", "Consumable": "Consumable", "CooldownReduction": "Cooldown Reduction", "CriticalStrike": "Critical Strike", "Damage": "Damage", "Defense": "Defense", "Health": "Health", "HealthRegen": "Health Regen", "LifeSteal": "Life Steal", "Magic": "Magic", "Mana": "<PERSON><PERSON>", "ManaRegen": "<PERSON><PERSON>", "Movement": "Movement", "SpellBlock": "Magic Resist", "SpellDamage": "Ability Power", "Boots": "Boots", "NonbootsMovement": "Other Movement Items", "Tenacity": "Tenacity", "SpellVamp": "Spell Vamp", "GoldPer": "Gold Income", "Slow": "Slow", "Aura": "<PERSON>ra", "Active": "Active", "MagicPenetration": "Magic Penetration", "ArmorPenetration": "Armor Penetration", "colloq_Armor": ";armour", "colloq_Attack": ";", "colloq_AttackSpeed": ";as", "colloq_Consumables": ";", "colloq_CriticalStrike": ";", "colloq_Damage": ";ad", "colloq_Defense": ";", "colloq_Health": ";hp", "colloq_HealthRegen": ";hpregen;hp5", "colloq_LifeSteal": ";lifesteal", "colloq_Magic": ";", "colloq_Mana": ";mp", "colloq_ManaRegen": ";mpregen;mp5", "colloq_Movement": ";movespeed", "colloq_SpellBlock": ";mr", "colloq_SpellDamage": ";ap", "colloq_Consumable": ";", "colloq_Boots": ";", "colloq_NonbootsMovement": ";", "colloq_CooldownReduction": ";cdr", "colloq_Tenacity": ";", "colloq_SpellVamp": ";spellvamp", "colloq_GoldPer": ";gp10", "colloq_Slow": ";", "colloq_Aura": ";", "colloq_Active": ";", "colloq_MagicPenetration": ";", "colloq_ArmorPenetration": ";", "RecommendedItems": "Recommended Items", "recommended_starting": "Starting Items", "recommended_essential": "Essential Items", "recommended_offensive": "Offensive Items", "recommended_defensive": "Defensive Items", "recommended_consumables": "Consumables", "Require_": "Requires:", "Cost_": "Cost:", "OriginalCost_": "Original Cost:", "SellsFor_": "Sells for:", "UpgradeCost_": "Upgrade Cost:", "Builds_": "Builds Into:", "ButtonBuy": "BUY", "ButtonSell": "SELL", "SpecialRecipeSmall": "*", "SpecialRecipeLarge": "Special", "FlatArmorMod": "Armor", "FlatAttackSpeedMod": "Attack Speed", "FlatBlockMod": "Block", "FlatCritChanceMod": "<PERSON><PERSON>", "FlatCritDamageMod": "Crit Dmg", "FlatEnergyPoolMod": "Energy", "FlatEnergyRegenMod": "Energy Regen / 5", "FlatEXPBonus": "Exp Bonus", "FlatHPPoolMod": "Health", "FlatHPRegenMod": "Health Regen / 5", "FlatMagicDamageMod": "Ability Power", "FlatMovementSpeedMod": "Move Speed", "FlatMPPoolMod": "<PERSON><PERSON>", "FlatMPRegenMod": "Man<PERSON>en / 5", "FlatPhysicalDamageMod": "Physical Dmg", "FlatSpellBlockMod": "Magic Resist", "PercentArmorMod": "Armor %", "PercentAttackSpeedMod": "Attack Speed %", "PercentBlockMod": "Block %", "PercentCritChanceMod": "Crit Chance %", "PercentCritDamageMod": "Crit Dmg %", "PercentDodgeMod": "Dodge %", "PercentEXPBonus": "Exp Bonus %", "PercentHPPoolMod": "Max Health %", "PercentHPRegenMod": "Health % / 5", "PercentMagicDamageMod": "Max Ability Power %", "PercentMovementSpeedMod": "Move Speed %", "PercentMPPoolMod": "Max Mana %", "PercentMPRegenMod": "Mana % / 5", "PercentPhysicalDamageMod": "Physical Dmg %", "PercentSpellBlockMod": "Magic Resist %", "rFlatArmorModPerLevel": "Armor at level 18", "rFlatArmorPenetrationMod": "Armor Pen.", "rFlatArmorPenetrationModPerLevel": "Armor Pen. at level 18", "rFlatCritChanceModPerLevel": "<PERSON><PERSON> at level 18", "rFlatCritDamageModPerLevel": "Crit Dmg at level 18", "rFlatDodgeMod": "Dodge", "rFlatDodgeModPerLevel": "Dodge at level 18", "rFlatEnergyModPerLevel": "Energy at level 18", "rFlatEnergyRegenModPerLevel": "Energy Regen / 5 at level 18", "rFlatGoldPer10Mod": "Gold per 10", "rFlatHPModPerLevel": "Health at level 18", "rFlatHPRegenModPerLevel": "Health Regen / 5 at level 18", "rFlatMagicDamageModPerLevel": "Ability Power at level 18", "rFlatMagicPenetrationMod": "Magic Pen.", "rFlatMagicPenetrationModPerLevel": "Magic Pen. at level 18", "rFlatMovementSpeedModPerLevel": "Move Speet at level 18", "rFlatMPModPerLevel": "Mana at level 18", "rFlatMPRegenModPerLevel": "Man<PERSON>en / 5 at level 18", "rFlatPhysicalDamageModPerLevel": "Physical Dmg at level 18", "rFlatSpellBlockModPerLevel": "Magic Resist at level 18", "rFlatTimeDeadMod": "Time Dead", "rFlatTimeDeadModPerLevel": "Time Dead at level 18", "rPercentArmorPenetrationMod": "Armor Pen. %", "rPercentArmorPenetrationModPerLevel": "Armor Pen. % at level 18", "rPercentAttackSpeedModPerLevel": "Attack Speed % at level 18", "rPercentCooldownMod": "Cooldown %", "rPercentCooldownModPerLevel": "Cooldown % at level 18", "rPercentMagicPenetrationMod": "Magic Pen. %", "rPercentMagicPenetrationModPerLevel": "Magic Pen. % at level 18", "rPercentMovementSpeedModPerLevel": "Move Speed % at level 18", "rPercentTimeDeadMod": "Time Dead %", "rPercentTimeDeadModPerLevel": "Time Dead % / lvl", "PercentLifeStealMod": "Lifesteal Bonus %", "PercentSpellVampMod": "Spellvamp Bonus %", "masteryFerocity": "Ferocity", "masteryCunning": "<PERSON><PERSON><PERSON>", "masteryResolve": "Resolve", "native_﻿ar": "العربية", "native_bg": "български език", "native_cs": "čeština", "native_de": "De<PERSON>ch", "native_el": "ελληνικά", "native_en": "English", "native_es": "español", "native_fr": "français", "native_hu": "magyar", "native_id": "Bahasa Indonesia", "native_it": "Italiano", "native_ja": "日本語", "native_ko": "조선말", "native_nl": "Nederlands", "native_pl": "język polski", "native_pt": "português", "native_ro": "română", "native_ru": "русский язык", "native_th": "ภาษาไทย", "native_tr": "Türkçe", "native_vi": "Tiếng <PERSON>", "native_zh": "汉语", "native_zh_CN": "简体字", "native_zh_MY": "简体字", "native_zh_TW": "繁體字"}, "tree": {"searchKeyIgnore": "", "searchKeyRemap": []}}