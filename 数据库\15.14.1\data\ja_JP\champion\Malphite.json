{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Malphite": {"id": "Malphite", "key": "54", "name": "マルファイト", "title": "モノリスの欠片", "image": {"full": "Malphite.png", "sprite": "champion2.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "54000", "num": 0, "name": "default", "chromas": false}, {"id": "54001", "num": 1, "name": "三ツ葉マルファイト", "chromas": false}, {"id": "54002", "num": 2, "name": "サンゴ礁マルファイト", "chromas": false}, {"id": "54003", "num": 3, "name": "大理石マルファイト", "chromas": false}, {"id": "54004", "num": 4, "name": "黒曜石マルファイト", "chromas": false}, {"id": "54005", "num": 5, "name": "氷河石マルファイト", "chromas": false}, {"id": "54006", "num": 6, "name": "メカファイト", "chromas": true}, {"id": "54007", "num": 7, "name": "鉄騎マルファイト", "chromas": false}, {"id": "54016", "num": 16, "name": "オデッセイ マルファイト", "chromas": true}, {"id": "54023", "num": 23, "name": "ダークスター マルファイト", "chromas": false}, {"id": "54024", "num": 24, "name": "プレステージ ダークスター マルファイト", "chromas": false}, {"id": "54025", "num": 25, "name": "FPX マルファイト", "chromas": true}, {"id": "54027", "num": 27, "name": "古の神マルファイト", "chromas": true}, {"id": "54037", "num": 37, "name": "月の守護者マルファイト", "chromas": true}, {"id": "54048", "num": 48, "name": "プールパーティ マルファイト", "chromas": true}], "lore": "マルファイトは混沌とした世界に祝福の秩序をもたらそうと苦闘する、生きた岩石の巨大な生物だ。モノリスとして知られる異世界のオベリスクに奉仕するかけらとして生まれた彼は、自らの強大な元素の力を使って先祖を何とか守ろうとしたが、その願いは果たせなかった。その後に続いた爆発で唯一の生き残りとなったマルファイトは、今ではルーンテラの移り気で柔らかい者たちの間で暮らしながら、種族の最後の生き残りにふさわしい新たな役割を見つけようとしている。", "blurb": "マルファイトは混沌とした世界に祝福の秩序をもたらそうと苦闘する、生きた岩石の巨大な生物だ。モノリスとして知られる異世界のオベリスクに奉仕するかけらとして生まれた彼は、自らの強大な元素の力を使って先祖を何とか守ろうとしたが、その願いは果たせなかった。その後に続いた爆発で唯一の生き残りとなったマルファイトは、今ではルーンテラの移り気で柔らかい者たちの間で暮らしながら、種族の最後の生き残りにふさわしい新たな役割を見つけようとしている。", "allytips": ["物理防御を強化すると「グラナイトシールド」の強度を有効に使うことができる。「ブルータルストライク」は自動効果で自身の物理防御を一定の割合分、増加させてくれるので意識しておこう。", "マルファイトの「ブルータルストライク」と「グラウンドスラム」は、物理防御が上がるほど威力が強化される。とはいえ、場合によっては魔法防御を強化する必要も出てくるだろう。そんなときは「イージスの盾」や「マーキュリーブーツ」「バンシーヴェール」などのアイテムがオススメだ。状況に応じたアイテムを選ぼう。"], "enemytips": ["マルファイトと戦う際、通常攻撃を主体にする遠隔チャンピオンは味方より前に出ないようにしよう。「グラウンドスラム」を受けると攻撃速度が下がり、敵に与えるダメージ量が大幅に減ってしまう。", "マルファイトは、ジャングルが得意な数少ないタンク系チャンピオンの1人だ。マルファイトが「スマイト」を持っていたら注意しよう。"], "tags": ["Tank", "Mage"], "partype": "マナ", "info": {"attack": 5, "defense": 9, "magic": 7, "difficulty": 2}, "stats": {"hp": 665, "hpperlevel": 104, "mp": 280, "mpperlevel": 60, "movespeed": 335, "armor": 37, "armorperlevel": 4.95, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 7.3, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3.4, "attackspeed": 0.736}, "spells": [{"id": "SeismicShard", "name": "サイズミックシャード", "description": "指定した敵に向かって岩の円盤を転がし、衝突時にダメージを与えて3秒間移動速度を奪う。", "tooltip": "指定した敵に向けて岩の円盤を転がし、<magicDamage>{{ qdamagecalc }}の魔法ダメージ</magicDamage>を与える。対象は{{ slowduration }}秒間、{{ e2 }}%の<status>スロウ効果</status>を受ける。さらに、この<status>スロウ効果</status>で低下させた分だけ、自身の<speed>移動速度</speed>が{{ slowduration }}秒間増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "ダメージ", "スロウ効果"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "20/25/30/35/40", "3", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "SeismicShard.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Obdu<PERSON>", "name": "サンダークラップ", "description": "大きな力で攻撃してソニックブームを発生させる。その後数秒間、通常攻撃で自身の前方に余波が発生する。", "tooltip": "<spellPassive>自動効果: </spellPassive>自身の<scaleArmor>物理防御が{{ bonusarmorpassive*100 }}%(%i:scaleArmor%{{ f1 }})</scaleArmor>増加する。<spellName>「グラナイトシールド」</spellName>が有効な間は、この効果が<scaleArmor>{{ bonusarmorpassive*300 }}% (%i:scaleArmor%{{ f2 }})</scaleArmor>になる。<br /><br /><spellPassive>発動効果: </spellPassive>次の通常攻撃が追加で<physicalDamage>{{ totalbonusdamage }}の物理ダメージ</physicalDamage>を与え、さらに余波が発生して自身の向いている方向に<physicalDamage>{{ thunderclapsplash }}の物理ダメージ</physicalDamage>を発生させる。この通常攻撃時の余波は発動から{{ thunderclapbuffduration }}秒間発生し続ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "物理防御", "ダメージ", "アフターショックの範囲ダメージ", "クールダウン"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ bonusarmorpassive*100.000000 }}% -> {{ bonusarmorpassivenl*100.000000 }}%", "{{ thunderclapbasedamage }} -> {{ thunderclapbasedamageNL }}", "{{ thunderclapsplashdamage }} -> {{ thunderclapsplashdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Obduracy.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Landslide", "name": "グラウンドスラム", "description": "地面を強打して衝撃波を起こし、自身の物理防御に応じた魔法ダメージを与える。衝撃波に当たった敵は、攻撃速度が短時間低下する。", "tooltip": "地面をたたきつけて衝撃波を起こし、<magicDamage>{{ edamagecalc }}の魔法ダメージ</magicDamage>を与えて、{{ duration }}秒間<attackSpeed>攻撃速度を{{ asreduction }}%</attackSpeed>低下させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "攻撃速度低下率"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ asreduction }}% -> {{ asreductionNL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Landslide.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UFSlash", "name": "アンストッパブル・フォース", "description": "指定地点に勢いよく跳躍し、敵ユニットにダメージを与えてノックアップさせる。", "tooltip": "アンストッパブル状態となり、地滑りのごとき猛烈な勢いで突進する。停止時に敵を{{ knockupduration }}秒間<status>ノックアップ</status>させて、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [1.5, 1.75, 2], [200, 300, 400], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "1.5/1.75/2", "200/300/400", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "UFSlash.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "グラナイトシールド", "description": "自身の最大体力の10%までのダメージを吸収する岩のシールドを生成する。このシールドは数秒間攻撃を受けないと再生する。", "image": {"full": "Malphite_GraniteShield.png", "sprite": "passive2.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}