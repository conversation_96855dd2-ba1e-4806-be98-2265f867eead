{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "Dr. <PERSON>", "title": "the Madman of Zaun", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "Toxic Dr<PERSON>", "chromas": false}, {"id": "36002", "num": 2, "name": "Mr. <PERSON>", "chromas": false}, {"id": "36003", "num": 3, "name": "Corporate Mundo", "chromas": true}, {"id": "36004", "num": 4, "name": "Mundo Mundo", "chromas": false}, {"id": "36005", "num": 5, "name": "Executioner <PERSON><PERSON>", "chromas": false}, {"id": "36006", "num": 6, "name": "Rageborn Mundo", "chromas": false}, {"id": "36007", "num": 7, "name": "TPA Mundo", "chromas": false}, {"id": "36008", "num": 8, "name": "Pool Party Mundo", "chromas": false}, {"id": "36009", "num": 9, "name": "El Macho Mundo", "chromas": false}, {"id": "36010", "num": 10, "name": "Frozen Prince <PERSON>", "chromas": true}, {"id": "36021", "num": 21, "name": "Street Demons Dr. <PERSON>", "chromas": true}], "lore": "<PERSON><PERSON>, penuh na<PERSON>u membunuh, dan be<PERSON><PERSON><PERSON> ungu mengerikan, Dr. <PERSON><PERSON> telah lama menakut-nakuti warga <PERSON>aun di malam hari. Sebelum menasbihkan diri sebagai dokter, dia dahulu adalah penghuni rumah sakit jiwa paling terkenal di Zaun. <PERSON><PERSON><PERSON> \"menyembuhkan\" se<PERSON><PERSON><PERSON> sta<PERSON>, Dr. <PERSON><PERSON> mendirikan tempat praktik di bangsal kosong tempat dia dahulu dirawat dan mulai menirukan prosedur tak etis yang dahulu sering dia alami sendiri. Dengan lemari penuh obat dan tanpa pengetahuan medis sama sekali, dia kini jadi makin mengerikan dengan tiap suntikan yang dia lakukan. Dia juga menakut-nakuti \"pasien\" malang yang tidak sengaja berada di dekat kantornya.", "blurb": "<PERSON><PERSON>, penuh na<PERSON>u <PERSON>, dan be<PERSON><PERSON><PERSON> un<PERSON> men<PERSON>, Dr. <PERSON><PERSON> telah lama menakut-nakuti warga <PERSON> di malam hari. Sebelum menasbihkan diri sebagai dokter, dia da<PERSON>u adalah penghuni rumah sakit jiwa paling terkenal di Zaun. Setelah \"menyembuhkan\"...", "allytips": ["Sadism yang tepat waktu bisa memancing champion musuh menyer<PERSON><PERSON> bahkan saat mereka tak punya cukup damage untuk menghabisimu.", "Spirit Visage akan men<PERSON> heal yang diberikan dari ultimamu dan menu<PERSON>an cooldown semua ability-mu.", "Cleaver sangat berguna untuk membunuh monster netral. Dar<PERSON><PERSON> kembali ke base, lebih baik farming monster netral sampai ultimamu bisa memberimu heal."], "enemytips": ["<PERSON><PERSON><PERSON> berk<PERSON>si dengan teman satu tim menggunakan Ability ber-damage tinggi setelah Dr. <PERSON>ndo menggunakan ultimanya. <PERSON><PERSON>, jika kamu tidak bisa membunuhnya dalam waktu cepat, dia akan heal saat memberi damage.", "<PERSON><PERSON> cast Ignite saat Dr. <PERSON><PERSON>kan Sadism untuk membatalkan banyak porsi heal-nya."], "tags": ["Tank", "Fighter"], "partype": "Tidak ada", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "Infected Bonesaw", "description": "Dr. <PERSON><PERSON> melem<PERSON> Infected <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> damage pada musuh pertama yang terkena berdasarkan Health saat ini dan menerapkan slow padanya.", "tooltip": "Dr. <PERSON><PERSON> melem<PERSON><PERSON> bonesaw, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ currenthealthdamage*100 }}% magic damage dari Health saat ini</magicDamage> ke musuh pertama yang terkena dan menerapkan <status>Slow</status> sebesar {{ slowamount*100 }}% selama {{ slowduration }} detik.<br /><br />Jika bonesaw mengenai champion atau monster, Dr. <PERSON><PERSON> akan memuli<PERSON>kan <healing>{{ healthrestoreonhitchampionmonster }} Health</healing>. <PERSON>ka mengenai non-champion atau non-monster, Dr. <PERSON>ndo akan memulihkan <healing>{{ healthrestoreonhitminion }} Health</healing> sebagai gantinya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Health Damage Saat Ini", "Damage Minimum", "Batas Damage Monster", "Biaya Health"], "effect": ["{{ currenthealthdamage*100.000000 }}%-> {{ currenthealthdamagenl*100.000000 }}%", "{{ minimumdamage }}-> {{ minimumdamageNL }}", "{{ maximummonsterdamage }}-> {{ maximummonsterdamageNL }}", "{{ healthcost }}-> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Health", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ healthcost }} Health"}, {"id": "DrMundoW", "name": "Heart Zapper", "description": "Dr. <PERSON><PERSON> electrocute pada dir<PERSON><PERSON> sendiri, <PERSON><PERSON><PERSON><PERSON><PERSON> persistent damage ke musuh di sekitar dan menyimpan sebagian damage yang diterima. <PERSON> akhir durasi atau saat Recast, Dr. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burst damage ke musuh di sekitar. <PERSON><PERSON> burst mengenai musuh, dia melakukan heal 1 persen dari damage yang tersimpan.", "tooltip": "Dr. <PERSON><PERSON> mengisi defibrilator, men<PERSON><PERSON><PERSON>an <magicDamage>{{ damagepertick*4 }} magic damage</magicDamage> tiap detik hingga {{ duration }} detik ke musuh di sekitar. <PERSON><PERSON> itu, dia juga menyimpan {{ grayhealthstorageinitial }} damage yang diterima pada {{ grayhealthinitialduration }} detik pertama dan {{ grayhealthstorage*100 }}% selama sisa durasi saat health abu-abu dan dapat melakukan <recast>Recast</recast>.<br /><br /><recast>Recast:</recast> Meledakkan defibrilator, mengh<PERSON>lkan <magicDamage>{{ totaldamage }} magic damage</magicDamage> ke musuh di sekitar. Jika mengenai setidaknya 1 champion, Dr. Mundo memulihkan <healing>{{ grayhealthbigmod*100 }}% gray health</healing>. Jika tidak, dia akan memulihkan <healing>{{ grayhealthsmallmod*100 }}% gray health</healing> sebagai gantinya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage per Tick", "Damage Recast", "Cooldown"], "effect": ["{{ damagepertick }}-> {{ damagepertickNL }}", "{{ recastbasedamage }}-> {{ recastbasedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% Health Saat Ini", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ currenthealthcost*100 }}% Health Saat Ini"}, {"id": "DrMundoE", "name": "Blunt Force Trauma", "description": "Pasif - Dr. <PERSON><PERSON> Attack Damage bonus, meningkat berdasarkan Health maksimumnya.<br><br>Aktif - Dr. <PERSON><PERSON> membanting tas \"medis\" ke musuh, men<PERSON><PERSON><PERSON><PERSON> tambahan damage berdasarkan Health yang hilang. <PERSON><PERSON> musuh tewas, mereka terlempar jauh, men<PERSON><PERSON><PERSON><PERSON> damage ke musuh yang mereka lewati.", "tooltip": "<spellPassive>Pasif:</spellPassive> Dr. <PERSON><PERSON> menda<PERSON> <physicalDamage>{{ passivebonusad }} Attack Damage</physicalDamage>.<br /><br /><spellActive>Aktif:</spellActive> Dr. <PERSON>ndo mengayunkan tas \"medis\" dengan kasar, membuat Serangan berikut<PERSON> menghasilkan <physicalDamage>{{ additionaldamage }} physical damage</physicalDamage> tambahan, meningkat hingga {{ maxdamageamptooltip }} berdasarkan Health yang hilang. Jika musuh terbunuh, Mundo melempar mereka jauh-jauh, mengh<PERSON>lkan <physicalDamage>{{ additionaldamage }} physical damage</physicalDamage> ke musuh yang dilewati.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "<PERSON><PERSON>", "Biaya Health", "Health Menjadi Attack Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ flathealthcost }}-> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}%-> {{ healthtoadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Health", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ flathealthcost }} Health"}, {"id": "DrMundoR", "name": "Maximum Dosage", "description": "Dr. <PERSON><PERSON> memompa bahan kimia ke dirinya sendiri, se<PERSON><PERSON> heal 1 persen dari Health yang hilang. <PERSON>a kemudian mendapatkan Move Speed dan meregenerasi sebagian Health maksimum dengan durasi yang lama.", "tooltip": "Dr. <PERSON>ndo memompa bahan kimia ke dirinya sendiri, mendapatkan <healing>{{ missinghealthheal*100 }}% Health yang hilang sebagai Health maksimum</healing>, <speed>{{ speedboostamount*100 }}% Move Speed</speed>, dan regenerasi <healing>{{ maxhealthhot*100 }}% Health maksimum</healing> selama {{ duration }} detik.<br /><br />Pada Rank 3, kedua efek heal akan meningkat dengan tambahan {{ bonuspernearbychampion*100 }}% per champion musuh di sekitar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Health Bonus", "Move Speed", "% Health Maksimum"], "effect": ["{{ missinghealthheal*100.000000 }}%-> {{ missinghealthhealnl*100.000000 }}%", "{{ speedboostamount*100.000000 }}%-> {{ speedboostamountnl*100.000000 }}%", "{{ maxhealthhot*100.000000 }}%-> {{ maxhealthhotnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}], "passive": {"name": "Goes Where He Pleases", "description": "Dr. <PERSON><PERSON> menahan efek Immobilize pertama yang mengenai dirinya, kehilangan Health dan menjatuhkan tabung kimia di sekitar. Dr. <PERSON><PERSON> bi<PERSON> mengambilnya dengan ber<PERSON><PERSON> melew<PERSON>, memulihkan Health dan mengurangi cooldown Ability ini.<br><br>Dr. <PERSON><PERSON> juga meningkatkan regenerasi Health secara signifikan.<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}