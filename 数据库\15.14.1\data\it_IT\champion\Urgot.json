{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Urgot": {"id": "Urgot", "key": "6", "name": "Urgot", "title": "il dreadnought", "image": {"full": "Urgot.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "6000", "num": 0, "name": "default", "chromas": false}, {"id": "6001", "num": 1, "name": "Granchiobot Grande Nemico", "chromas": false}, {"id": "6002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "6003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "6009", "num": 9, "name": "Urgot Mezzogiorno di Fuoco", "chromas": true}, {"id": "6015", "num": 15, "name": "Urgot Cosplayer di Guardiano in Pigiama", "chromas": true}, {"id": "6023", "num": 23, "name": "Urgot Notte Inquietante", "chromas": true}, {"id": "6032", "num": 32, "name": "Urgot l'Intasatore", "chromas": true}], "lore": "Un tempo un potente boia di Noxus, U<PERSON>t venne tradito dall'impero per il quale aveva ucciso così tanta gente. Imprigionato in catene di ferro, venne costretto a imparare il vero significato della forza nel Fosso, una miniera carceraria nelle profondità di Zaun. Riemerso grazie a un disastro che portò il caos in città, la sua ombra ora si staglia decisa sul sottobosco criminale. È deciso a purgare la sua nuova casa dai non meritevoli con dolorose prove di valore, legando le sue vittime alle stesse catene che una volta lo avevano reso schiavo.", "blurb": "Un tempo un potente boia di Noxus, <PERSON><PERSON>t venne tradito dall'impero per il quale aveva ucciso così tanta gente. Imprigionato in catene di ferro, venne costretto a imparare il vero significato della forza nel Fosso, una miniera carceraria nelle profondità...", "allytips": ["Fai attenzione alla ricarica delle singole zampe, perché infliggono una porzione consistente dei tuoi danni", "Colpisci con Carica corrosiva o Sdegno per agganciare un bersaglio con Epurazione. È un ottimo modo per attivare più zampe in rapida successione.", "Risparmia Tanatofobia per i nemici che sai già essere troppo deboli per sopravvivere. È l'abilità perfetta per eliminare i nemici in fuga."], "enemytips": ["Urgot si affida molto ai colpi che infligge ai suoi nemici dalle zampe, che hanno una loro ricarica e scattano quando attacca nella stessa direzione. Non farti colpire da più zampe.", "<PERSON>rgot può infliggere e assorbire danni ingenti con Epurazione, ma si rallenta mentre spara.", "Se vieni colpito da Tanatofobia, fai del tuo meglio per non scendere sotto la soglia dell'esecuzione (25% della tua salute massima) fino alla fine dell'effetto."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 102, "mp": 340, "mpperlevel": 45, "movespeed": 330, "armor": 36, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 350, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 4, "attackspeedperlevel": 3.75, "attackspeed": 0.625}, "spells": [{"id": "UrgotQ", "name": "Carica corrosiva", "description": "Spara una carica esplosiva nella posizione bersaglio, infliggendo danni fisici e rallentando i nemici coinvolti nell'esplosione.", "tooltip": "Urgot spara una carica esplosiva, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e <status>rallentando</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "UrgotQ.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotW", "name": "Epurazione", "description": "Urgot rallenta e scarica la sua arma sui nemici vicini. Dà la priorità ai campioni nemici recentemente colpiti da Urgot con altre abilità e attiva Fiamme echeggianti.", "tooltip": "<spellPassive>Passiva:</spellPassive> le altre abilità di Urgot marchiano l'ultimo campione colpito per 5 secondi.<br /><br /><spellActive>Attiva:</spellActive> Urgot inizia a sparare con la sua pistola a catena contro il nemico più vicino, dando la priorità a quelli marchiati. In questo modo, li attacca {{ e8 }} volte al secondo, infliggendo <physicalDamage>{{ damagepershot }} danni fisici</physicalDamage> per colpo. Urgot può muoversi mentre spara e ottiene il {{ e2 }}% di resistenza al <status>rallentamento</status>, ma perde <speed>{{ e5 }} velocità di movimento</speed>.<br /><br />Al livello massimo, questa abilità ha una durata illimitata e può essere <toggle>attivata o disattivata</toggle>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Attacco fisico per colpo", "Costo in @AbilityResourceName@"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e7 }}% -> {{ e7NL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 9, 6, 3, 0], "cooldownBurn": "12/9/6/3/0", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [40, 40, 40, 40, 40], [4, 4, 4, 4, 25000], [0, 0, 0, 0, 0], [125, 125, 125, 125, 125], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 23.5, 27, 30.5, 34], [3, 3, 3, 3, 3], [12, 12, 12, 12, 12], [50, 50, 50, 50, 50]], "effectBurn": [null, "30/60/90/120/150", "40", "4/4/4/4/25000", "0", "125", "0.5", "20/23.5/27/30.5/34", "3", "12", "50"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "UrgotW.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotE", "name": "Sdegno", "description": "Urgot carica in una direzione, riparandosi e travolgendo i nemici non campioni. Se afferra un campione nemico, si ferma e lo lancia via.", "tooltip": "Urgot carica in avanti e ottiene uno <shield>scudo da {{ etotalshieldhealth }}</shield> per {{ eshieldduration }} secondi. Il primo campione colpito è <status>stordito</status> per {{ stunduration }} secondi e scagliato alle spalle di Urgot. Tutti i nemici con cui Urgot si scontra subiscono <physicalDamage>{{ edamage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ ebasedamage }} -> {{ ebasedamageNL }}", "{{ eshieldbasehealth }} -> {{ eshieldbasehealthNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "UrgotE.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotR", "name": "Tanatofobia", "description": "Urgot spara una chem-trivella che impala il primo campione colpito. Se il campione finisce sotto una data soglia di salute, Urgot lo giudica debole e lo può giustiziare.", "tooltip": "Urgot spara una chem-trivella che impala il primo campione nemico colpito, infliggendogli <physicalDamage>{{ rcalculateddamage }} danni fisici</physicalDamage> e <status>rallentandolo</status> per {{ rslowduration }} secondi dell'1% per ogni 1% della sua salute mancante, fino a un massimo del {{ rmovespeedmod }}%.<br /><br />Se la vittima impalata scende sotto il {{ rhealththreshold }}% di salute, Urgot può <recast>rilanciare</recast> questa abilità, <status>sopprimendo</status> la vittima e trascinandola verso la di sé. Quando raggiunge Urgot, il bersaglio viene ucciso e tutti i nemici vicini restano <status>impauriti</status> per {{ rfearduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "UrgotR.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Gli attacchi base di Urgot ed Epurazione attivano scariche di fuoco dalle sue zampe, infliggendo danni fisici.", "image": {"full": "Urgot_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}