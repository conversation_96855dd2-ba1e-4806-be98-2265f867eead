{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Camille": {"id": "<PERSON>", "key": "164", "name": "<PERSON>", "title": "der stählerne Schatten", "image": {"full": "Camille.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "164000", "num": 0, "name": "default", "chromas": false}, {"id": "164001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "164002", "num": 2, "name": "Hexenzirkel-Camille", "chromas": true}, {"id": "164010", "num": 10, "name": "iG-Camille", "chromas": false}, {"id": "164011", "num": 11, "name": "Arkana-Camille", "chromas": true}, {"id": "164021", "num": 21, "name": "Angriff<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "164031", "num": 31, "name": "Wintergeweihte Camille", "chromas": true}, {"id": "164032", "num": 32, "name": "Wintergeweihte Camille (Prestige)", "chromas": false}], "lore": "Camille ist bis an die Zähne bewaffnet und kann so außerhalb des Gesetzes agieren, wenn sie als Leiterin des Geheimdienstes des Ferros-Klans für den reibungslosen Ablauf der Maschinerie von Piltover sowie für eine friedliche Koexistenz mit ihrem zhaunitischen Unterbau sorgt. Selbst flexibel und präzise, sieht sie schlampige Techniken als Schande an, die bestraft werden müssen. Bei einem Verstand so scharf wie die Klingen an ihrem Körper wirft Camilles Streben nach Überlegenheit durch Hextech-Augmentierungen die Frage auf, was nun eigentlich überwiegt: Frau oder Maschine?", "blurb": "Camille ist bis an die Zähne bewaffnet und kann so außerhalb des Gesetzes agieren, wenn sie als Leiterin des Geheimdienstes des Ferros-Klans für den reibungslosen Ablauf der Maschinerie von Piltover sowie für eine friedliche Koexistenz mit ihrem...", "allytips": ["<PERSON><PERSON>, bis das gegnerische Team durch einen Teamkampf abgelenkt ist, und verwende dann „Greifhaken“, um verwundbare Ziele zu eliminieren.", "Benutze die Massenkontrolle deiner Fähigkeiten, um Gegner mit beiden „Präzisionsprotokoll“-Angriff<PERSON> zu treffen."], "enemytips": ["<PERSON><PERSON> wirkt nur gegen einen Schadenstyp. Greife sie also an, wenn sie gerade verwundbar gegen deinen Schaden ist.", "„Hextech-Ultimatum“ hat nur eine sehr begrenzte Reichweite. <PERSON><PERSON><PERSON> da<PERSON>, bevor sie dir zu nahe kommt."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 99, "mp": 339, "mpperlevel": 52, "movespeed": 340, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.8, "mpregen": 8.15, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.5, "attackspeed": 0.644}, "spells": [{"id": "CamilleQ", "name": "Präzisionsprotokoll", "description": "<PERSON><PERSON> nächster Angriff verursacht zusätzlichen Schaden und erhöht ihr Lauftempo. Diese Fähigkeit kann binnen kurzer Zeit erneut ausgeführt werden und verursacht stark erhöhten zusätzlichen Schaden, wenn <PERSON> zwischen den beiden Angriffen Zeit verstreichen lässt.", "tooltip": "<PERSON><PERSON> <PERSON><PERSON>chster <PERSON> verursacht zusätzlich <physicalDamage>{{ bonusdamage }}&nbsp;normalen Schaden</physicalDamage> und erhöht ihr <speed>Lauftempo</speed> {{ msduration }}&nbsp;Sekunden lang um {{ msbonus*100 }}&nbsp;%. Diese Fähigkeit kann während der nächsten {{ qtotalrecasttime }}&nbsp;Sekunden <recast>reaktiviert</recast> werden.<br /><br />Wenn die <recast>Reaktivierung</recast> mindestens {{ qrampuptime }}&nbsp;Sekunde(n) nach dem ersten Angriff trifft, wird der zusätzliche Schaden auf <physicalDamage>{{ empoweredbonusdamage }}</physicalDamage> erhöht und {{ damageconversionpercentage }}&nbsp;% des Angriffsschadens werden in <trueDamage>absoluten Schaden</trueDamage> umgewandelt.<br /><br /><rules>Die<PERSON>higkeit löst Zaubereffekte aus, wenn sie Schaden verursacht.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Skalierung mit Gesamtangriffsschaden", "Lauftempo", "Abklingzeit"], "effect": ["{{ tadratio*100.000000 }}&nbsp;% -> {{ tadrationl*100.000000 }}&nbsp;%", "{{ msbonus*100.000000 }}&nbsp;% -> {{ msbonusnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "CamilleQ.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleW", "name": "Taktischer Klingenfeger", "description": "Nach einer Verzögerung verursacht Camille in einem kegelförmigen Gebiet Schaden. G<PERSON>ner in der äußeren Hälfte werden verlangsamt, erleiden zusätzlichen Schaden und heilen Camille.", "tooltip": "Camile holt aus und attackiert dann, wobei sie <physicalDamage>{{ basedamagetotal }}&nbsp;normalen Schaden</physicalDamage> verursacht.<br /><br />Von der äußeren Hälfte getroffene Gegner werden um {{ slowpercentage }}&nbsp;% <status>verlangsamt</status>, wobei dieser Effekt über {{ slowduration }}&nbsp;Sekunden hinweg abfällt. Zusätzlich erleiden sie <physicalDamage>{{ outeredgetooltip }}&nbsp;normalen Schaden</physicalDamage>. <PERSON> stellt <healing>{{ outerconehealingratio }}&nbsp;% des zusätzlich verursachten Schadens an Champions als Leben wieder her</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>", "Schaden abhängig vom maximalen Leben ", "Abklingzeit"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ outerconemaxhpdamage*100.000000 }}&nbsp;% -> {{ outerconemaxhpdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 15.5, 14, 12.5, 11], "cooldownBurn": "17/15.5/14/12.5/11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "CamilleW.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> zieht sich an eine <PERSON>, stößt sich ab und schleudert beim Landen gegnerische Champions in die Luft.", "tooltip": "<PERSON> schi<PERSON>ßt einen Greifhaken ab, der sich am Terrain festhakt. Sie kann sich innerhalb von 1&nbsp;Sekunde zu ihm ziehen und diese Fähigkeit <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> <PERSON> stößt sich von der Mauer ab und kollidiert mit dem ersten getroffenen gegnerischen Champion. Bei der Landung fügt sie nahen Gegnern <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und <status>betäubt</status> gegnerische Champions {{ knockupduration }}&nbsp;Sekunden lang. Sprünge zu gegnerischen Champions haben doppelte Reichweite und gewähren beim Aufprall {{ asduration }}&nbsp;Sekunden lang <attackSpeed>{{ asbuff*100 }}&nbsp;% Angriffstempo</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Angriffstempo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ asbuff*100.000000 }}&nbsp;% -> {{ asbuffnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CamilleE.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleR", "name": "Das Hextech-Ultimatum", "description": "<PERSON> springt zu einem Champion und hält ihn in einem Gebiet gefangen. Außerdem verursacht sie zusätzlichen magischen Schaden mit ihren normalen Angriffen.", "tooltip": "<PERSON> kann kurzzeitig nicht anvisiert werden, springt auf einen gegnerischen Champion, unterbricht Kanalisierungen und sperrt ihn {{ rduration }}&nbsp;Sekunden lang in ein Areal ein, aus dem er nicht entkommen kann. Andere Gegner in der Nähe werden <status>zurückgestoßen</status>. Eure Angriffe gegen den eingeschlossenen Gegner verursachen zusätzlichen magischen Schaden in Höhe von <magicDamage>{{ rpercentcurrenthpdamage }}&nbsp;% des aktuellen Lebens.</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Aktueller Schaden abhä<PERSON><PERSON> von <PERSON>", "<PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rpercentcurrenthpdamage }}&nbsp;% -> {{ rpercentcurrenthpdamageNL }}&nbsp;%", "{{ rduration }} -> {{ rdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475], "rangeBurn": "475", "image": {"full": "CamilleR.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Adaptive Verteidigung", "description": "Normale Angriffe gegen Champions gewähren für kurze Zeit einen Schild gegen den Schadenstyp eines Angriffs (normal oder magisch) in Höhe eines Prozentsatzes des maximalen Lebens von Camille.", "image": {"full": "Camille_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}