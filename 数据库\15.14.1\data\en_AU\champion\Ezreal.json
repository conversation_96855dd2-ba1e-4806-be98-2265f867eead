{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ezreal": {"id": "Ezreal", "key": "81", "name": "Ezreal", "title": "the Prodigal Explorer", "image": {"full": "Ezreal.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "81000", "num": 0, "name": "default", "chromas": false}, {"id": "81001", "num": 1, "name": "Nottingham Ezreal", "chromas": false}, {"id": "81002", "num": 2, "name": "Striker <PERSON>", "chromas": false}, {"id": "81003", "num": 3, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "81004", "num": 4, "name": "Explorer <PERSON>", "chromas": false}, {"id": "81005", "num": 5, "name": "Pulsefire Ezreal", "chromas": false}, {"id": "81006", "num": 6, "name": "TPA Ezreal", "chromas": false}, {"id": "81007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "81008", "num": 8, "name": "Ace of Spades Ezreal", "chromas": false}, {"id": "81009", "num": 9, "name": "Arcade Ezreal", "chromas": false}, {"id": "81018", "num": 18, "name": "Star Guardian Ezreal", "chromas": false}, {"id": "81019", "num": 19, "name": "SSG Ezreal", "chromas": false}, {"id": "81020", "num": 20, "name": "Pajama Guardian Ezreal", "chromas": false}, {"id": "81021", "num": 21, "name": "Battle Academia Ezreal", "chromas": true}, {"id": "81022", "num": 22, "name": "PsyOps Ezreal", "chromas": false}, {"id": "81023", "num": 23, "name": "Prestige PsyOps Ezreal", "chromas": false}, {"id": "81025", "num": 25, "name": "Porcelain Protector Ezreal", "chromas": false}, {"id": "81033", "num": 33, "name": "Faerie Court Ezreal", "chromas": false}, {"id": "81043", "num": 43, "name": "HEARTSTEEL Ezreal", "chromas": false}, {"id": "81044", "num": 44, "name": "Heavenscale Ezreal", "chromas": true}, {"id": "81054", "num": 54, "name": "Prestige Heavenscale Ezreal", "chromas": false}, {"id": "81065", "num": 65, "name": "Masque of the Black Rose Ezreal", "chromas": false}], "lore": "A dashing adventurer, unknowingly gifted in the magical arts, <PERSON><PERSON><PERSON> raids long-lost catacombs, tangles with ancient curses, and overcomes seemingly impossible odds with ease. His courage and bravado knowing no bounds, he prefers to improvise his way out of any situation, relying partially on his wits, but mostly on his mystical Shuriman gauntlet, which he uses to unleash devastating arcane blasts. One thing is for sure—whenever <PERSON><PERSON><PERSON> is around, trouble isn't too far behind. Or ahead. Probably everywhere.", "blurb": "A dashing adventurer, unknowingly gifted in the magical arts, <PERSON><PERSON><PERSON> raids long-lost catacombs, tangles with ancient curses, and overcomes seemingly impossible odds with ease. His courage and bravado knowing no bounds, he prefers to improvise his way...", "allytips": ["Use Arcane Shift to help line up your other skill shots.", "You can play <PERSON>z<PERSON> either as a Attack Damage carry or Ability Power carry depending on how you build him.", "You can line up Trueshot Barrage to hit multiple minion waves or even Monsters."], "enemytips": ["<PERSON><PERSON><PERSON> is a very fragile champion so take the fight to him.", "<PERSON><PERSON><PERSON> is completely skill shot based, so make sure to keep minions in between you.", "Mystic Shot applies on-hit effects including the Crest of Cinders."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 102, "mp": 375, "mpperlevel": 70, "movespeed": 325, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.65, "mpregen": 8.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "EzrealQ", "name": "Mystic Shot", "description": "<PERSON><PERSON><PERSON> fires a damaging bolt of energy which reduces all of his cooldowns slightly if it strikes an enemy unit.", "tooltip": "<PERSON><PERSON><PERSON> fires a bolt of energy, dealing <physicalDamage>{{ damage }} physical damage</physicalDamage> to the first enemy hit and reducing his Ability Cooldowns by {{ cdrefund }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5.25, 5, 4.75, 4.5], "cooldownBurn": "5.5/5.25/5/4.75/4.5", "cost": [28, 31, 34, 37, 40], "costBurn": "28/31/34/37/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealQ.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealW", "name": "Essence Flux", "description": "<PERSON><PERSON><PERSON> fires an orb that sticks to the first champion or objective hit. If <PERSON><PERSON><PERSON> hits an enemy with the orb, it detonates and deals damage.", "tooltip": "<PERSON><PERSON><PERSON> fires a magical orb that sticks to the first champion, structure, or epic jungle monster hit for {{ detonationtimeout }} seconds. If <PERSON><PERSON><PERSON> hits that target with an Attack or Ability, it detonates, dealing <magicDamage>{{ damage }} magic damage</magicDamage>. Detonating with an Ability refunds the Mana cost of that Ability plus <scaleMana>{{ manareturn }} Mana</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Total AP Ratio"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealW.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealE", "name": "Arcane Shift", "description": "Ezreal teleports to a target nearby location and fires a homing bolt which strikes the nearest enemy unit. Prioritizes enemies stuck with Essence Flux.", "tooltip": "Ezreal teleports then fires a bolt at the nearest enemy, dealing <magicDamage>{{ damage }} magic damage</magicDamage>. The bolt prioritizes enemies affected by <spellName>Essence Flux</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [26, 23, 20, 17, 14], "cooldownBurn": "26/23/20/17/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "EzrealE.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealR", "name": "Trueshot Barrage", "description": "Ez<PERSON> winds up before firing a powerful barrage of energy that deals massive damage to each unit it passes through (damage is reduced for minions and non-epic monsters).", "tooltip": "Ez<PERSON> fires a massive energy arc that deals <magicDamage>{{ damage }} magic damage</magicDamage>. The arc deals {{ damagereductionwaveclear.0*100 }}% damage to minions and non-epic jungle monsters.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EzrealR.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Rising Spell Force", "description": "<PERSON><PERSON><PERSON> gains increasing Attack Speed each time he successfully hits a spell, stacking up to 5 times.", "image": {"full": "Ezreal_RisingSpellForce.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}