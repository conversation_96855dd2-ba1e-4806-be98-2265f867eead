{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Brand": {"id": "Brand", "key": "63", "name": "Brand", "title": "the Burning Vengeance", "image": {"full": "Brand.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "63000", "num": 0, "name": "default", "chromas": false}, {"id": "63001", "num": 1, "name": "Apocalyptic Brand", "chromas": false}, {"id": "63002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "63003", "num": 3, "name": "Cryocore Brand", "chromas": false}, {"id": "63004", "num": 4, "name": "Zombie Brand", "chromas": false}, {"id": "63005", "num": 5, "name": "Spirit Fire Brand", "chromas": false}, {"id": "63006", "num": 6, "name": "Battle Boss Brand", "chromas": false}, {"id": "63007", "num": 7, "name": "Arclight Brand", "chromas": true}, {"id": "63008", "num": 8, "name": "Eternal Dragon Brand", "chromas": true}, {"id": "63021", "num": 21, "name": "Debonair Brand", "chromas": true}, {"id": "63022", "num": 22, "name": "Prestige Debonair Brand", "chromas": false}, {"id": "63033", "num": 33, "name": "Street Demons Brand", "chromas": true}, {"id": "63042", "num": 42, "name": "Empyrean Brand", "chromas": true}], "lore": "Once a tribesman of the icy Freljord named <PERSON><PERSON>, the creature known as <PERSON> is a lesson in the temptation of greater power. Seeking one of the legendary World Runes, <PERSON><PERSON> betrayed his companions and seized it for himself—and, in an instant, the man was no more. His soul burned away, his body a vessel of living flame, <PERSON> now roams Valoran in search of other Runes, swearing revenge for wrongs he could never possibly have suffered in a dozen mortal lifetimes.", "blurb": "Once a tribesman of the icy Freljord named <PERSON><PERSON>, the creature known as <PERSON> is a lesson in the temptation of greater power. Seeking one of the legendary World Runes, <PERSON><PERSON> betrayed his companions and seized it for himself—and, in an instant, the...", "allytips": ["You can deter enemies from standing near their minions by setting them ablaze, due to Conflagration.", "You can use <PERSON>'s abilities in a variety of combinations to maximize his damage in different situations.", "Pyroclasm bounces randomly between enemies, so try to cast it on a small group of enemies if you want to hit the same target multiple times."], "enemytips": ["<PERSON> must land an ability before his combo is able to get started. Dodging his <PERSON><PERSON> or <PERSON>llar of Flame will disrupt his rhythm.", "Try to move away from allies when you see Pyroclasm being cast. The initial missile speed is slow, which should give your team time to react.", "<PERSON>'s passive allows him to excel against teams that cluster together. Be sure to split up against him."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 4}, "stats": {"hp": 570, "hpperlevel": 105, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.681}, "spells": [{"id": "BrandQ", "name": "<PERSON><PERSON>", "description": "<PERSON> launches a ball of fire forward that deals magic damage. If the target is ablaze, <PERSON><PERSON> will stun the target for @StunDuration@ seconds.", "tooltip": "<PERSON> launches a fireball that deals <magicDamage>{{ totaldamage }} magic damage</magicDamage> to the first enemy hit.<br /><br />If the target is <keywordMajor>Ablaze</keywordMajor>, they will be <status>Stunned</status> for {{ stunduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [70, 100, 130, 160, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/100/130/160/190", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "BrandQ.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandW", "name": "Pillar of Flame", "description": "After a short delay, <PERSON> creates a Pillar of Flame at a target area, dealing magic damage to enemy units within the area. Units that are ablaze take an additional 25% damage.", "tooltip": "<PERSON> creates a pillar of pure fire, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Units that are <keywordMajor>Ablaze</keywordMajor> take <magicDamage>{{ empowereddamage }} damage</magicDamage> instead.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [20, 40, 60, 80, 100], [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "20/40/60/80/100", "0.25", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BrandW.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandE", "name": "Conflagration", "description": "<PERSON> conjures a powerful blast at his target that spreads to nearby enemies, dealing magic damage. If the target is ablaze, Conflagration's spread is doubled.", "tooltip": "<PERSON> conjures a powerful blast at his target, dealing <magicDamage>{{ edamagecalc }} magic damage</magicDamage> to surrounding units.<br /><br />If the target is <keywordMajor>Ablaze</keywordMajor>, the spread range is doubled.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [375, 375, 375, 375, 375], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "375", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "BrandE.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandR", "name": "Pyroclasm", "description": "<PERSON> unleashes a devastating torrent of fire that bounces up to 5 times off of <PERSON> and nearby enemies, dealing magic damage to enemies each time bounce. <PERSON><PERSON>ces prioritize stacking <PERSON> to max on <PERSON>. If a target is ablaze, Pyroclasm will briefly slow them.", "tooltip": "<PERSON> unleashes a devastating torrent of fire that can bounce to <PERSON> or another enemy up to 5 times, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies each bounce. Bounces prioritize stacking <keywordMajor>Blaze</keywordMajor> to max on champions.<br /><br />If the target is <keywordMajor>Ablaze</keywordMajor>, they are briefly <status>Slowed</status> by {{ slowamount }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage per Bounce", "Slow", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "BrandR.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Blaze", "description": "<PERSON>'s spells light his targets ablaze, dealing damage over 4 seconds, stacking up to 3 times. If <PERSON> kills an enemy while it is ablaze he regains mana. When <PERSON> reaches max stacks on a Champion or large monster, it becomes unstable. It detonates in 2 seconds, applying spell effects and dealing massive damage in an area around the victim.", "image": {"full": "BrandP.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}