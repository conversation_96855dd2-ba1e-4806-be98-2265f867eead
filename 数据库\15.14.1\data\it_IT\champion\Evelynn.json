{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Evelynn": {"id": "<PERSON><PERSON>", "key": "28", "name": "<PERSON><PERSON>", "title": "L'abbraccio agonizzante", "image": {"full": "Evelynn.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "28000", "num": 0, "name": "default", "chromas": false}, {"id": "28001", "num": 1, "name": "<PERSON><PERSON> dell'Ombra", "chromas": false}, {"id": "28002", "num": 2, "name": "<PERSON><PERSON>sque<PERSON>", "chromas": false}, {"id": "28003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "28004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "28005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "28006", "num": 6, "name": "Evelynn K/DA", "chromas": false}, {"id": "28007", "num": 7, "name": "Evelynn K/DA (edizione prestigio)", "chromas": false}, {"id": "28008", "num": 8, "name": "<PERSON><PERSON> al dolcetto", "chromas": true}, {"id": "28015", "num": 15, "name": "Evelynn K/DA ALL OUT", "chromas": true}, {"id": "28024", "num": 24, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "28031", "num": 31, "name": "Evelynn K/DA (edizione prestigio 2022)", "chromas": false}, {"id": "28032", "num": 32, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "28042", "num": 42, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "28052", "num": 52, "name": "<PERSON>n <PERSON> Fuoco", "chromas": true}, {"id": "28053", "num": 53, "name": "<PERSON>n <PERSON> Fuoco (edizione prestigio)", "chromas": false}, {"id": "28064", "num": 64, "name": "Evelynn Portatrice della Notte", "chromas": false}], "lore": "Negli angoli più oscuri di Runeterra, il demone Evelynn cerca la sua prossima vittima. Attira le sue prede con l'aspetto di una sensuale donna umana, ma nel momento in cui una persona soccombe al suo fascino lei scatena la sua vera forma, provocando inimmaginabili tormenti sulla sua vittima e traendo piacere dal suo dolore. Per il demone, queste relazioni sono poco più che avventure di una notte. Per il resto di Runeterra sono macabri racconti sulla passione senza controllo, terribile memento di quanto costi veramente il desiderio più libertino.", "blurb": "<PERSON>egli angoli più oscuri di Runeterra, il demone Evelynn cerca la sua prossima vittima. Attira le sue prede con l'aspetto di una sensuale donna umana, ma nel momento in cui una persona soccombe al suo fascino lei scatena la sua vera forma, provocando...", "allytips": ["Il tempo di innesco di Fascino può sembrare lungo, ma i vantaggi dell'incanto e della diminuzione della resistenza magica sono grandi, quindi vale la pena di aspettare.", "Quando è Mimetizzata, fai attenzione quando stai per essere rilevato dai campioni nemici. Si capisce dagli occhi gialli e rossi vicino ai campioni nemici.", "Se hai poca salute, puoi sfruttare la guarigione di Ombra del demone e Mimesi per tornare a combattere e sorprendere i nemici."], "enemytips": ["Acquistare lumi della visione ti aiuta a rilevare la posizione di Evelynn e prepararti alle sue imboscate.", "Buona parte del pericolo di Evelynn è rappresentato dal suo incanto, Fascino. Proteggi gli alleati marchiati da Fascino e, se sei marchiato, accertati che ci siano alleati tra te e la posizione da cui Evelynn potrebbe attaccarti.", "Se sospetti che Evelynn stia per tendere un'imboscata a un tuo compagno, faglielo sapere segnalandolo sulla minimappa e scrivendo in chat."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 642, "hpperlevel": 98, "mp": 315, "mpperlevel": 42, "movespeed": 335, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 8.11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.1, "attackspeed": 0.667}, "spells": [{"id": "Evelynn<PERSON>", "name": "Aculeo dell'odio", "description": "<PERSON><PERSON> sferra un colpo di frusta e infligge danni alla prima unità colpita. Poi spara più volte una serie di aculei ai nemici vicini.", "tooltip": "<PERSON><PERSON> col<PERSON> con la sua frusta infliggendo <magicDamage>{{ missiledamage }} danni magici</magicDamage> al primo nemico colpito e fa in modo che i suoi prossimi 3 attacchi o abilità contro quella unità infliggano <magicDamage>{{ totalbonusdamage }} danni magici</magicDamage> aggiuntivi. <PERSON><PERSON> pu<PERSON> <recast>rilanciare</recast> questa abilità fino a {{ qstackcount }} volte.<br /><br /><recast>Rilancio:</recast> <PERSON>n spara degli aculei attraverso il nemico più vicino, infliggendo <magicDamage>{{ missiledamage }} danni magici</magicDamage> a tutti i nemici colpiti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> e Aculeo", "<PERSON><PERSON> bonus", "Costo in @AbilityResourceName@"], "effect": ["{{ hatespikebasedamage }} -> {{ hatespikebasedamageNL }}", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [30, 30, 30, 30, 30], [60, 60, 60, 60, 60], [15, 25, 35, 45, 55], [25, 30, 35, 40, 45], [6, 6, 6, 6, 6], [30, 30, 30, 30, 30], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [-0.25, -0.25, -0.25, -0.25, -0.25]], "effectBurn": [null, "0", "30", "60", "15/25/35/45/55", "25/30/35/40/45", "6", "30", "50", "4", "-0.25"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EvelynnQ.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnW", "name": "Fascino", "description": "<PERSON>n maledice il suo bersaglio e il suo prossimo attacco o abilità, dopo un ritardo, incanta il bersaglio e riduce la sua resistenza magica.", "tooltip": "<PERSON><PERSON> marchia un campione o un mostro per 5 secondi. Se <PERSON><PERSON> colpisce il bersaglio con un attacco base o un'abilità, rimuove il marchio, rimborsa il suo costo e <status>rallenta</status> il bersaglio di un {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />Se il marchio dura almeno 2,5 secondi, rimuoverlo provoca effetti aggiuntivi:<li>Contro i campioni: li <status>incanta</status> per {{ charmduration }} secondo/i e riduce la <scaleMR>resistenza magica di un {{ mrshred*100 }}%</scaleMR> per {{ shredduration }} secondi.<li>Contro i mostri: li <status>incanta</status> per {{ monstercharm }} secondi e infligge loro <magicDamage>{{ monsterdamagetotaltooltip }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> amorosa", "Durata dell'ammaliamento sui mostri", "Resistenza magica", "<PERSON><PERSON> ai mostri", "Ricarica", "Gitt<PERSON>"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ monstercharm }} -> {{ monstercharmNL }}", "{{ effect9amount*100.000000 }}% -> {{ effect9amountnl*100.000000 }}%", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [1.25, 1.5, 1.75, 2, 2.25], [-0.45, -0.45, -0.45, -0.45, -0.45], [15, 14, 13, 12, 11], [5, 5, 5, 5, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [250, 300, 350, 400, 450], [0.75, 0.75, 0.75, 0.75, 0.75], [0.35, 0.375, 0.4, 0.425, 0.45], [4, 4, 4, 4, 4]], "effectBurn": [null, "2", "1.25/1.5/1.75/2/2.25", "-0.45", "15/14/13/12/11", "5", "1.5", "250/300/350/400/450", "0.75", "0.35/0.375/0.4/0.425/0.45", "4"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1300, 1400, 1500, 1600], "rangeBurn": "1200/1300/1400/1500/1600", "image": {"full": "EvelynnW.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> sferra un colpo al bersaglio e infligge danni. Ottiene velocità di movimento per un breve periodo.", "tooltip": "<PERSON><PERSON> frusta un nemico infliggendo <magicDamage>{{ basedamage }} (+ {{ percenthealthbasetooltip }} salute massima) danni magici</magicDamage>. <PERSON><PERSON> ottiene <speed>{{ speedamount*100 }}% velocità di movimento</speed> per {{ speedduration }} secondi.<br /><br /><PERSON><PERSON><PERSON> in <keywordMajor>Ombra del demone</keywordMajor> azzera la ricarica di questa abilità e la potenzia. Quando questa abilità è potenziata, <PERSON>n scatta invece verso il bersaglio e infligge <magicDamage>{{ empowereddamage }} (+ {{ percenthealthempoweredtooltip }} salute massima) danni magici</magicDamage> al suo bersaglio e a ogni nemico che attraversa.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Velocità di movimento", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ empowered<PERSON><PERSON> }} -> {{ empowereddamageNL }}", "{{ speedamount*100.000000 }}% -> {{ speedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.3, 0.35, 0.4, 0.45, 0.5], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [4, 4, 4, 4, 4], [450, 450, 450, 450, 450], [0.8, 0.85, 0.9, 0.95, 1], [2, 2, 2, 2, 2], [1.3, 1.35, 1.4, 1.45, 1.5]], "effectBurn": [null, "0", "0", "0.3/0.35/0.4/0.45/0.5", "2", "3", "4", "450", "0.8/0.85/0.9/0.95/1", "2", "1.3/1.35/1.4/1.45/1.5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [210, 210, 210, 210, 210], "rangeBurn": "210", "image": {"full": "EvelynnE.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnR", "name": "Ultima carezza", "description": "Evelynn diventa momentaneamente non bersagliabile e decima l'area davanti a sé per poi teletrasportarsi all'indietro per una lunga distanza.", "tooltip": "<PERSON><PERSON> scatena la sua energia demoniaca, infliggendo danni ingenti, diventando non bersagliabile e teletrasportandosi all'indietro. Infligge <magicDamage>{{ damage }} danni magici</magicDamage>, che aumentano fino a <magicDamage>{{ critdamage }}</magicDamage> per i nemici sotto il <healing>30% di salute</healing>. <PERSON>nc<PERSON>, imposta Ombra del demone con un tempo di ricarica di 1,25 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect1amount*2.400000 }} -> {{ effect1amountnl*2.400000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 250, 375], [1.4, 1.4, 1.4], [2.5, 2.5, 2.5], [150, 225, 300], [3, 3, 3], [5, 4, 3], [0.3, 0.3, 0.3], [700, 700, 700], [30, 45, 60], [0, 0, 0]], "effectBurn": [null, "125/250/375", "1.4", "2.5", "150/225/300", "3", "5/4/3", "0.3", "700", "30/45/60", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EvelynnR.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ombra del demone", "description": "Quando è fuori dal combattimento, <PERSON><PERSON> entra in Ombra del demone. Ombra del demone cura Evelynn quando ha poca salute e conferisce Mimesi dopo il livello 6.", "image": {"full": "Evelynn_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}