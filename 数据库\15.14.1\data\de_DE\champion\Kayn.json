{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kayn": {"id": "<PERSON><PERSON>", "key": "141", "name": "<PERSON><PERSON>", "title": "der Schatten des Todes", "image": {"full": "Kayn.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "141000", "num": 0, "name": "default", "chromas": false}, {"id": "141001", "num": 1, "name": "Seelenjäger-Kayn", "chromas": false}, {"id": "141002", "num": 2, "name": "Odyssee-Kayn", "chromas": true}, {"id": "141008", "num": 8, "name": "Flammende Finsternis Kayn", "chromas": true}, {"id": "141009", "num": 9, "name": "Flammende Finsternis Kayn (Prestige)", "chromas": false}, {"id": "141015", "num": 15, "name": "Sc<PERSON><PERSON>mond-Kay<PERSON>", "chromas": true}, {"id": "141020", "num": 20, "name": "HEARTSTEEL-<PERSON><PERSON>", "chromas": true}, {"id": "141026", "num": 26, "name": "Academia Certaminis-Kayn", "chromas": true}], "lore": "<PERSON>eda Kayns Leistungen im Bereich der Schattenmagie suchen ihresgleichen und so kämpft er beständig, um seine wahre Bestimmung zu erfüllen – eines Tages den Orden der Schatten in eine neue Ära unter ionischer Herrschaft zu führen. Obwohl er weiß, das<PERSON> Rha<PERSON>, eine lebende Waffe der Düsteren, langsam aber stetig seinen Geist und seinen Körper korrumpiert, führt er sie doch unbeirrt. Am Ende scheint es nur zwei Möglichkeiten zu geben: Entweder beugt sich die Waffe Kayns Willen … oder Kayn wird von der bösartigen Klinge vollständig verzehrt, was den Weg für die völlige Zerstörung Runeterras ebnet.", "blurb": "<PERSON><PERSON> Kayns Leistungen im Bereich der Schattenmagie suchen ihresgleichen und so kämpft er beständig, um seine wahre Bestimmung zu erfüllen – eines Tages den Orden der Schatten in eine neue Ära unter ionischer Herrschaft zu führen. Obwohl er weiß, dass...", "allytips": ["Denke an die Teamkomposition sowohl deines als auch des gegnerischen Teams, bevor du dich für eine Form entscheidest.", "<PERSON><PERSON>, dass nahe Gegner sehen können, in welcher Mauer du dich befindest."], "enemytips": ["Um „Finstere Heimsuchung“ einsetzen zu können, muss <PERSON><PERSON> seinem <PERSON> zu<PERSON>t Schaden zufügen. Versuche „Klingenschlag“ auszuweichen, um ihn daran zu hindern, „Finstere Heimsuchung“ auf größere Entfernung einsetzen zu können.", "<PERSON><PERSON> sich in Terrain in deiner Nähe aufhält, wird dir am Rand dieses Terrains eine visuelle Warnung angezeigt.", "<PERSON><PERSON><PERSON> zu, während er „Schattenschritt“ benutz<PERSON>, um dessen Dauer zu verkürzen, oder benutze schwere Massenkontrolle (Betäubung, Bezaubern, Hochschleudern usw.), um „Schattenschritt“ sofort zu beenden."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 6, "magic": 1, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 103, "mp": 410, "mpperlevel": 50, "movespeed": 340, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.7, "attackspeed": 0.669}, "spells": [{"id": "KaynQ", "name": "Sensenwirbel", "description": "<PERSON>n springt nach vorne und schlägt dann zu. <PERSON><PERSON> verursacht Schaden.", "tooltip": "<PERSON>n springt nach vorn, lässt seine Kriegssense wirbeln und fügt <PERSON>, die er auf seinem Weg trifft, <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu. Im Anschluss erleiden Gegner in der Nähe denselben Schaden erneut.<br /><br /><keywordMajor>Der Düstere:</keywordMajor> Kayn verursacht stattdessen <physicalDamage>normalen Schaden</physicalDamage> in Höhe von {{ darkinflatdamage }} plus {{ darkinpercentdamage }} des maximalen Lebens.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [75, 100, 125, 150, 175], [0.65, 0.65, 0.65, 0.65, 0.65], [5, 5, 5, 5, 5], [3.5, 3.5, 3.5, 3.5, 3.5], [200, 250, 300, 350, 400], [40, 40, 40, 40, 40], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/100/125/150/175", "0.65", "5", "3.5", "200/250/300/350/400", "40", "300", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "KaynQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaynW", "name": "Klingenschlag", "description": "<PERSON><PERSON> verursacht Schaden in einer Linie und verlangsamt seine Ziele.", "tooltip": "<PERSON><PERSON> schlägt mit seiner Kriegssense zu, verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> und <status>verlangsamt</status> getroffene Gegner um {{ e3 }}&nbsp;%. Der Effekt fällt über {{ e5 }}&nbsp;Sekunden hinweg ab.<br /><br /><keywordMajor>Schattenassassine:</keywordMajor> Während Kayn diese Fähigkeit einsetzt, kann er sich frei bewegen und profitiert von einer erhöhten Reichweite.<br /><br /><keywordMajor>Der Düstere:</keywordMajor> <status>Schleudert</status> getroffene Gegner zusätzlich {{ e2 }}&nbsp;Sekunde(n) lang hoch.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [85, 130, 175, 220, 265], [1, 1, 1, 1, 1], [90, 90, 90, 90, 90], [160, 160, 160, 160, 160], [1.5, 1.5, 1.5, 1.5, 1.5], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/130/175/220/265", "1", "90", "160", "1.5", "900", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KaynW.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaynE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>n kann sich durch <PERSON><PERSON> bewegen.", "tooltip": "Kayn erhält <speed>{{ e1 }}&nbsp;% <PERSON>ft<PERSON><PERSON></speed>, „<PERSON><PERSON>st“ und kann sich {{ e2 }}&nbsp;Sekunden lang durch Terrain bewegen. Wenn er zum ersten Mal Terrain betritt, stellt er <healing>{{ totalhealing }}&nbsp;<PERSON><PERSON></healing> wieder her.<br /><br />Ist <PERSON> <status>bewegungsunfähig</status> oder hält er sich über {{ e3 }}&nbsp;Sekunden in Folge außerhalb des Terrains auf, endet diese Fähigkeit frühzeitig.<br /><br /><keywordMajor>Schattenassassine:</keywordMajor> Kayn erhält <speed>{{ e5 }}&nbsp;% Lauftempo</speed>, Immunität gegen <status>Verlangsamung</status> und die Abklingzeit verringert sich um {{ assassincdreduction }}&nbsp;Sekunden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON>", "Heilung"], "effect": ["{{ f15 }} -> {{ f16 }}", "{{ e2 }} -> {{ e2NL }}", "{{ e7 }} -> {{ e7NL }}"]}, "maxrank": 5, "cooldown": [21, 19, 17, 15, 13], "cooldownBurn": "21/19/17/15/13", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [40, 40, 40, 40, 40], [7, 7.5, 8, 8.5, 9], [1.5, 1.5, 1.5, 1.5, 1.5], [1250, 1250, 1250, 1250, 1250], [70, 70, 70, 70, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [90, 100, 110, 120, 130], [0.45, 0.45, 0.45, 0.45, 0.45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40", "7/7.5/8/8.5/9", "1.5", "1250", "70", "1.5", "90/100/110/120/130", "0.45", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "KaynE.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaynR", "name": "Finstere Heimsuchung", "description": "<PERSON>n versteckt sich im Körper eines Gegners und verursacht massiven Schaden, wenn er hervorbricht.", "tooltip": "<spellPassive>Passiv:</spellPassive> Champions, denen <PERSON> zufügt, werden 3,15&nbsp;Sekunden lang markiert.<br /><br />Kayn befällt einen markierten Gegner und kann nicht anvisiert werden. Nach {{ infestduration }}&nbsp;Sekunden oder nach <recast>Reaktivierung</recast> der Fähigkeit bricht Kayn hervor und fügt dem <PERSON>egner <physicalDamage>{{ damage }} normalen Schaden</physicalDamage> zu.<br /><br /><keywordMajor>Schattenassassine:</keywordMajor> Erhöht die Reichweite dieser Fähigkeit, die Entfernung, in der Kayn herausbricht, und setzt die Abklingzeit von <spellName>Kriegssense der Düsteren</spellName> nach dem Herausbrechen zurück.<br /><br /><keywordMajor>Der Düstere:</keywordMajor> <PERSON><PERSON><PERSON> statt<PERSON> <physicalDamage>normalen Schaden in H<PERSON>he von {{ slayerdamage }} des maximalen Lebens</physicalDamage> zu und stellt <healing>{{ healvalue }}&nbsp;Leben</healing> wieder her ({{ slayerhealpercent*100 }}&nbsp;% des Schadens).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [0, 0, 0], [2.5, 2.5, 2.5], [300, 300, 300], [15, 15, 15], [13, 13, 13], [70, 70, 70], [750, 750, 750], [550, 550, 550]], "effectBurn": [null, "150/250/350", "0.5", "0", "2.5", "300", "15", "13", "70", "750", "550"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "KaynR.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Kriegssense der Düsteren", "description": "Kayn führt eine uralte Waffe und ringt mit Rhaast, dem Düsteren in ihr, um Kontrolle. Entweder siegt der <font color='#fe5c50'>Düstere</font> oder Kayn meistert Rhaast und wird der <font color='#8484fb'>Schattenassassine</font>.<br><br><font color='#fe5c50'>Der Düstere:</font> Heilt dich um einen Prozentanteil des Schadens, den deine Fähigkeiten Champions zufügen.<br><br><font color='#8484fb'>Schattenassassine:</font> Während der ersten paar Sekunden eines Kampfes mit einem gegnerischen Champion verursachst du zusätzlichen Schaden.", "image": {"full": "Kayn_Passive_Primary.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}