{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Milio": {"id": "<PERSON><PERSON>", "key": "902", "name": "<PERSON><PERSON>", "title": "The Gentle Flame", "image": {"full": "Milio.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "902000", "num": 0, "name": "default", "chromas": false}, {"id": "902001", "num": 1, "name": "Faerie Court Milio", "chromas": true}, {"id": "902011", "num": 11, "name": "<PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> is a warmhearted boy from Ixtal who has, despite his young age, mastered the fire axiom and discovered something new: soothing fire. With this newfound power, <PERSON><PERSON> plans to help his family escape their exile by joining the Yun Tal—just like his grandmother once did. Having traveled through the Ixtal jungles to the capital of Ixaocan, <PERSON><PERSON> now prepares to face the Vidalion and join the Yun Tal, unaware of the trials—and dangers—that await him.", "blurb": "<PERSON><PERSON> is a warmhearted boy from Ixtal who has, despite his young age, mastered the fire axiom and discovered something new: soothing fire. With this newfound power, <PERSON><PERSON> plans to help his family escape their exile by joining the Yun Tal—just like his...", "allytips": ["<PERSON><PERSON> requires allies near him to make the most out of his tools.", "<PERSON><PERSON>'s dash speeds increase with his movement speed. Use the extra speed to surprise your enemies!", "Danger is as fun as you let it be."], "enemytips": ["<PERSON><PERSON>'s movement abilities forecast their destination. Try to use this to your advantage.", "Champions with crowd control that can be applied quickly excel against Mi<PERSON>.", "Catching <PERSON><PERSON> with no allies around severely inhibits his mobility. Try to find him alone."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "MilioQ", "name": "Ultra Mega Fire Kick", "description": "Kick a ball that knocks back an enemy. The ball launches upward on hit and falls toward the enemy, damaging and slowing enemies in the area upon impact.", "tooltip": "<PERSON><PERSON> kicks a fireball, <status>Knocking Back</status> the first enemy hit. If it hits, the ball bounces past them and explodes, dealing <magicDamage>{{ damage }} magic damage</magicDamage> to surrounding enemies and <status>Slowing</status> them by {{ slowamountpercent }} for {{ slowduration }} seconds.<br /><br />Hitting at least one enemy champion with <spellName>Ultra Mega Fire Kick</spellName> refunds {{ refundratio*100 }}% of it's Mana cost.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "<PERSON><PERSON>"], "effect": ["{{ falldamage }} -> {{ falldamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "MilioQ.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioW", "name": "Cozy Campfire", "description": "Create an empowering zone that heals allies and increases attack range to those inside. The zone follows the ally nearest to the cast point.", "tooltip": "<PERSON><PERSON> creates a hearth that follows ally champions for {{ zoneduration }} seconds. Ally champions nearby gain {{ rangepercent }} Attack Range and restore <healing>{{ healingovertime }} Health</healing> over the duration. The hearth also applies <spellName>Fired Up!</spellName> every {{ healfrequencyseconds }} seconds.<br /><br /><recast>Recast:</recast> Change the hearth's follow ally.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Range", "Healing", "Cooldown", "<PERSON><PERSON>"], "effect": ["{{ rangepctincrease*100.000000 }}% -> {{ rangepctincreasenl*100.000000 }}%", "{{ totalhealingovertime }} -> {{ totalhealingovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [29, 27, 25, 23, 21], "cooldownBurn": "29/27/25/23/21", "cost": [90, 100, 110, 120, 130], "costBurn": "90/100/110/120/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "MilioW.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioE", "name": "Warm Hugs", "description": "<PERSON><PERSON> tosses a shield to an ally, temporarily increasing their movement speed. This ability has 2 charges.", "tooltip": "<PERSON><PERSON> wraps an ally champion in protective flames, granting <shield>{{ shieldcalc }} Shield</shield> and <speed>{{ movespeedamount*100 }}% Move Speed</speed> for {{ movespeedduration }} seconds.<br /><br />This ability has 2 charges and the effects stack on repeat targets.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Shield Amount", "Move Speed", "<PERSON><PERSON>", "Recharge Time"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MilioE.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioR", "name": "Breath of Life", "description": "<PERSON><PERSON> unleashes a wave of soothing flames that heal and remove crowd control effects from allies in range.", "tooltip": "<PERSON><PERSON> unleashes a wave of soothing flames on nearby ally champions, cleansing <status>Disabling</status> and <status>Immobilizing</status> effects, restoring <healing>{{ healcalc }} Health</healing>, and granting {{ tenacityamount*100 }}% Tenacity for {{ tenacityduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Healing", "Cooldown"], "effect": ["{{ healbase }} -> {{ healbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 145, 130], "cooldownBurn": "160/145/130", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MilioR.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Fired Up!", "description": "<PERSON><PERSON>'s abilities enchant allies on touch, making their next damage deal a burst of extra damage and burn the target.", "image": {"full": "Milio_P.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}