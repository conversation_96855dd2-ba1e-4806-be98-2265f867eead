{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Singed": {"id": "Singed", "key": "27", "name": "Singed", "title": "der verrückte Chemiker", "image": {"full": "Singed.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "27000", "num": 0, "name": "default", "chromas": false}, {"id": "27001", "num": 1, "name": "Riot-Singed", "chromas": false}, {"id": "27002", "num": 2, "name": "Hextech-Singed", "chromas": false}, {"id": "27003", "num": 3, "name": "Surfer-<PERSON>ed", "chromas": false}, {"id": "27004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Singed", "chromas": false}, {"id": "27005", "num": 5, "name": "Verstärkter Singed", "chromas": false}, {"id": "27006", "num": 6, "name": "Schneetag-Sing<PERSON>", "chromas": false}, {"id": "27007", "num": 7, "name": "SSW-Singed", "chromas": false}, {"id": "27008", "num": 8, "name": "Seuchendoktor Singed", "chromas": false}, {"id": "27009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "27010", "num": 10, "name": "Widerstands-Singed", "chromas": true}, {"id": "27019", "num": 19, "name": "Astronauten-Singed", "chromas": true}, {"id": "27028", "num": 28, "name": "Schimmerlabor-<PERSON><PERSON> (Arcane)", "chromas": false}], "lore": "Singed ist ein brillanter zhaunitischer Alchemist von zweifelhafter Moral, bei dessen Experimenten sich selbst den abgebrühtesten Verbrechern der Magen umdreht. Er verkauft seine Fähigkeiten an den, der am meisten dafür bietet, und schert sich wenig darum, wie seine giftigen Mixturen verwendet werden. Das daraus resultierende Chaos ist wiederum ein Experiment für sich. Sein berüchtigtstes Werk ist der „Schimmer“, der es den Chem-Baronen ermöglichte, <PERSON><PERSON><PERSON> in ihren persönlichen Spielplatz zu verwandeln. Aber vom Wahnsinn getrieben arbeitet Singed stets an etwas Neuem, wobei jedes seiner Vorhaben noch verdorbener ist als das letzte …", "blurb": "Singed ist ein brillanter zhaunitischer Alchemist von zweifelhafter Moral, bei dessen Experimenten sich selbst den abgebrühtesten Verbrechern der Magen umdreht. Er verkauft seine Fähigkeiten an den, der am meisten dafür bietet, und schert sich wenig...", "allytips": ["Die „Giftspur“ ist sehr effektiv dabei, Vasallen auszuschalten und Gegner zu beharken, wodurch Singed das Kräfteverhältnis in seiner gesamten Lane verschieben kann.", "<PERSON>utze „Irrsinnstrank“, um deine Gegner zu einer Verfolgung in der „Giftspur“ zu bewegen.", "<PERSON> Werfen von <PERSON>n in einen eigenen Turm kann ihnen schweren Schaden zufügen."], "enemytips": ["Halte Abstand, um nicht in Singeds Verbündete geschleudert zu werden.", "Singed muss nah an dein Team gelangen, um effektiv zu sein. <PERSON><PERSON><PERSON> dies aus, um ihn mit Massenkontrolleffekten in <PERSON><PERSON><PERSON> zu halten, während du seine Verbündeten angreifst.", "Pass auf, wenn du Singed jagst. Er ist sehr schwer kleinzukriegen und kann eine Giftspur hinterlassen, um während deiner Verfolgung Schaden an dir zu verursachen."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 7, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 96, "mp": 330, "mpperlevel": 45, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9.5, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.9, "attackspeed": 0.7}, "spells": [{"id": "PoisonTrail", "name": "Giftspur", "description": "Singed hinterlässt eine Giftspur, die Gegnern, die damit in Berührung kommen, <PERSON><PERSON><PERSON> zufügt.", "tooltip": "<toggle>Aktivierbar:</toggle> Singed hinterlässt eine Giftspur, die <magicDamage>{{ damagepersecond }}&nbsp;magischen <PERSON></magicDamage> pro Sekunde verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [13, 13, 13, 13, 13], "costBurn": "13", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana pro Sekunde", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "PoisonTrail.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}&nbsp;Mana pro Sekunde"}, {"id": "MegaAdhesive", "name": "Mega-Kleber", "description": "Wirft ein Fläschchen mit Mega-Kleber auf den Boden. Alle Gegner, die damit in Berührung kommen, werden verlangsamt und gehemmt.", "tooltip": "Singed schleudert ein Fläschchen mit einer klebrigen Flüssigkeit, die Gegner im Wirkbereich {{ wduration }}&nbsp;Sekunden lang <status>hemmt</status> und um {{ slowpercent }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Verlangsamung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ slowpercent }}&nbsp;% -> {{ slowpercentNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "MegaAdhesive.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Fling", "name": "Schleudern", "description": "Fügt einem gegnerischen Ziel Schaden zu und schleudert es durch die Luft hinter Singed. Falls das Ziel, das Singed schleudert, in seinem Mega-Kleber landet, wird es zudem festgehalten.", "tooltip": "Singed wirft einen Gegner über seine Schulter und fügt ihm <magicDamage>magischen Schaden</magicDamage> in <PERSON>ö<PERSON> von {{ basedamage }} plus {{ e3 }}&nbsp;% seines maximalen Lebens zu.<br /><br />Falls Singed sein Ziel in seinen <spellName>Mega-Kleber</spellName> wirft, wird es zudem {{ e2 }}&nbsp;Sekunden lang <status>festgehalten</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden abhängig vom maximalen Leben ", "Festhaltedauer", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }}&nbsp;% -> {{ e3NL }}&nbsp;%", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [50, 60, 70, 80, 90], [1, 1.25, 1.5, 1.75, 2], [6, 6.5, 7, 7.5, 8], [420, 420, 420, 420, 420], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/60/70/80/90", "1/1.25/1.5/1.75/2", "6/6.5/7/7.5/8", "420", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "Fling.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "InsanityPotion", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Singed trinkt ein starkes chemisches Gebräu, das ihm erhöhte Kampfwerte verleiht. Außerdem belegt seine „Giftspur“ Gegner mit „Klaffenden Wunden“.", "tooltip": "Singed trinkt ein mächtiges Gebräu aus Chemikalien, das ihm {{ duration }}&nbsp;Sekunden lang {{ statamount }}&nbsp;<scaleAP>Fähigkeitsstärke</scaleAP>, <scaleArmor>Rüstung</scaleArmor>, <scaleMR>Magieresistenz</scaleMR>, <speed>Lauftempo</speed>, <healing>Lebensregeneration</healing> und <scaleMana>Manaregeneration</scaleMana> gewährt. Während des Effekts fügt Singeds <spellName>Giftspur</spellName> Gegnern außerdem {{ grievousduration }}&nbsp;Sekunden lang {{ grievousamount*100 }}&nbsp;% Klaffende Wunden zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzliche Werte"], "effect": ["{{ statamount }} -> {{ statamountNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "InsanityPotion.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Widerwärtiger Windschatten", "description": "Singed agiert im Windschatten von Champions in der Nähe und erhält über kurze Zeit einen Lauftemposchub, wenn er diese passiert.", "image": {"full": "Singed_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}