{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sona": {"id": "<PERSON><PERSON>", "key": "37", "name": "ソナ", "title": "沈黙の弦奏師", "image": {"full": "Sona.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "37000", "num": 0, "name": "default", "chromas": false}, {"id": "37001", "num": 1, "name": "音楽の女神ソナ", "chromas": false}, {"id": "37002", "num": 2, "name": "Pentakill ソナ", "chromas": false}, {"id": "37003", "num": 3, "name": "聖夜の調べソナ", "chromas": false}, {"id": "37004", "num": 4, "name": "古琴の演奏ソナ", "chromas": true}, {"id": "37005", "num": 5, "name": "アーケード ソナ", "chromas": true}, {"id": "37006", "num": 6, "name": "DJ ソナ", "chromas": false}, {"id": "37007", "num": 7, "name": "スイートハート ソナ", "chromas": false}, {"id": "37009", "num": 9, "name": "オデッセイ ソナ", "chromas": true}, {"id": "37017", "num": 17, "name": "PsyOps ソナ", "chromas": true}, {"id": "37026", "num": 26, "name": "Pentakill III: ロストチャプター ソナ", "chromas": true}, {"id": "37035", "num": 35, "name": "スターガーディアン ソナ", "chromas": true}, {"id": "37045", "num": 45, "name": "不滅の旅路ソナ", "chromas": true}, {"id": "37046", "num": 46, "name": "プレステージ不滅の旅路ソナ", "chromas": false}, {"id": "37056", "num": 56, "name": "勝利の栄光ソナ", "chromas": true}], "lore": "ソナはデマーシアで随一の弦楽器エトワールの演奏家であり、その優雅な和音と活気あふれる旋律によってのみ自らの意思を伝えることができる。上品な態度で貴族たちの間で人気だが、彼女のうっとりするようなメロディーには実際に魔力が宿っており、デマーシアのタブーに触れるのではないかと考える者たちもいる。よそ者には何も聞こえないが親しい仲間だけはその旋律を理解することが可能で、彼女が爪弾くハーモニーは傷ついた味方を癒し、敵には予想もしないやり方でダメージを与える。", "blurb": "ソナはデマーシアで随一の弦楽器エトワールの演奏家であり、その優雅な和音と活気あふれる旋律によってのみ自らの意思を伝えることができる。上品な態度で貴族たちの間で人気だが、彼女のうっとりするようなメロディーには実際に魔力が宿っており、デマーシアのタブーに触れるのではないかと考える者たちもいる。よそ者には何も聞こえないが親しい仲間だけはその旋律を理解することが可能で、彼女が爪弾くハーモニーは傷ついた味方を癒し、敵には予想もしないやり方でダメージを与える。", "allytips": ["オーラはできるだけ多くの味方チャンピオンに付与する事が望ましいが、ソナ自身が敵に捕まってしまっては元も子もない。集団戦では前に出ないようにしよう。", "ソナは決して打たれ強いチャンピオンではないが「パーセヴァランス」をタイミングよく使用することでより多くのダメージに耐えられるようになる。", "「クレッシェンド」は戦局を左右する重要なスキルだ、使いどころをよく考えよう！"], "enemytips": ["彼女のアルティメットスキル「クレッシェンド」でチームの全員が踊らされないように固まらないこと。", "ソナは非常に防御力が低いが、放っておくと味方をどんどん回復させてしまうので、優先して倒そう。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 5, "defense": 2, "magic": 8, "difficulty": 4}, "stats": {"hp": 550, "hpperlevel": 91, "mp": 340, "mpperlevel": 45, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.3, "attackspeed": 0.644}, "spells": [{"id": "SonaQ", "name": "ヒム・オブ・ヴァロー", "description": "ソナが「ヒム・オブ・ヴァロー」を演奏し、音波を放って周囲の敵2体(チャンピオンか中立モンスターを優先)に魔法ダメージを与え、さらに一時的にオーラをまとって、接触した味方の次の攻撃に追加ダメージを付与する。", "tooltip": "周囲の敵2体(チャンピオンを優先)に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、その後新たな<keywordMajor>メロディー</keywordMajor>を開始する。ダメージを与えたチャンピオンの数だけ<keywordMajor>アッチェレランド</keywordMajor>のスタックを獲得する。<br /><br /><keywordMajor>メロディー:</keywordMajor> {{ auraduration }}秒間オーラを獲得する。オーラは味方チャンピオンを強化し、{{ onhitduration }}秒内の次の通常攻撃が追加で<magicDamage>{{ totalonhitdamage }}の魔法ダメージ</magicDamage>%i:OnHit%を与えるようになる。<br /><br /><keywordMajor>パワーコード - スタッカート:</keywordMajor> 「パワーコード」が追加ダメージを与える(<magicDamage>合計{{ totalstaccatodamage }}の魔法ダメージ</magicDamage>)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ(発動効果)", "ダメージ(メロディー)", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "SonaQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SonaW", "name": "パーセヴァランス", "description": "ソナが「パーセヴァランス」を演奏し、癒しの旋律によって自身と周囲の味方の体力を回復する。さらにしばらくオーラをまとい、接触した味方に一時的なシールドを与える。", "tooltip": "<spellPassive>発動効果:</spellPassive> 自身および近くにいる味方チャンピオン1体の<healing>体力を{{ totalheal }}</healing>回復する(現在体力が最も少ない味方を優先)。その後、新たな<keywordMajor>メロディー</keywordMajor>を始める。<br /><br /><keywordMajor>メロディー:</keywordMajor> {{ auraduration }}秒間オーラを獲得する。オーラは味方チャンピオンに{{ shieldduration }}秒間、<shield>耐久値{{ totalshield }}のシールド</shield>を付与する。<br /><br />ダメージを受けた味方を回復するか、シールドで他の味方のダメージを{{ accelerandoshieldbreakpoint }}以上防ぐたびに、<keywordMajor>アッチェレランド</keywordMajor>のスタックを1獲得する。<br /><br /><keywordMajor>パワーコード - ディミヌエンド:</keywordMajor> 「パワーコード」が追加で、対象の与える物理および魔法ダメージを{{ diminuendoduration }}秒間{{ totaldiminuendoweakenpercent }}減少させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量(発動効果)", "シールド量(メロディー)", "@AbilityResourceName@コスト"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SonaW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SonaE", "name": "セレリティ", "description": "ソナが「セレリティ」を演奏して周囲の味方の移動速度を増加させる。さらに一時的にオーラをまとって、接触した味方チャンピオンの移動速度を増加させる。", "tooltip": "<spellPassive>発動効果:</spellPassive> 新たな<keywordMajor>メロディー</keywordMajor>を始め、{{ selfmovementspeeddurationmin }}秒間<speed>移動速度が{{ totalselfmovementspeed }}</speed>増加する。ダメージを受けなければ、この効果時間は最大{{ selfmovementspeeddurationmax }}秒間まで延長される。<br /><br /><keywordMajor>メロディー:</keywordMajor> {{ auraduration }}秒間オーラを獲得する。オーラは味方チャンピオンに{{ allymovementspeedduration }}秒間、<speed>{{ totalallymovementspeed }}の移動速度</speed>を付与する。<br /><br /><keywordMajor>パワーコード - テンポ:</keywordMajor> 「パワーコード」が追加で、対象に{{ tempoduration }}秒間{{ totaltempomovespeedslow }}の<status>スロウ効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度"], "effect": ["{{ allybasemovementspeed*100.000000 }}% -> {{ allybasemovementspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "SonaE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SonaR", "name": "クレッシェンド", "description": "ソナが究極の和音を演奏し、触れた敵チャンピオンに魔法ダメージを与え、強制的に踊らせてスタン効果を付与する。", "tooltip": "魅惑的な和音を演奏し、敵を{{ stunduration }}秒間<status>スタン</status>させ、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "SonaR.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "パワーコード", "description": "<passive>アッチェレランド</passive>: 通常スキルを使用し条件を満たすたびに、恒久的に通常スキルヘイストを獲得する。獲得可能な上限を超えると、スキルの使用成功時にアルティメットの残りクールダウン時間が短縮されるようになる。<br><br><passive>パワーコード</passive>: スキルを数回発動するたびに、次の通常攻撃で追加魔法ダメージを与え、さらに最後に発動した通常スキルに応じた追加効果が発生する。", "image": {"full": "Sona_Passive_Charged.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}