{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Talon": {"id": "Talon", "key": "91", "name": "Тал<PERSON>н", "title": "Тень клинка", "image": {"full": "Talon.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "91000", "num": 0, "name": "default", "chromas": false}, {"id": "91001", "num": 1, "name": "Ренегат Талон", "chromas": false}, {"id": "91002", "num": 2, "name": "Багряный Талон", "chromas": false}, {"id": "91003", "num": 3, "name": "Талон Драконий Клинок", "chromas": true}, {"id": "91004", "num": 4, "name": "SSW Талон", "chromas": false}, {"id": "91005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>авая Луна", "chromas": false}, {"id": "91012", "num": 12, "name": "Несокрушимый меч Талон", "chromas": true}, {"id": "91020", "num": 20, "name": "Талон из Темной рощи", "chromas": true}, {"id": "91029", "num": 29, "name": "Увядшая роза Талон", "chromas": true}, {"id": "91038", "num": 38, "name": "Ковбой Талон", "chromas": true}, {"id": "91039", "num": 39, "name": "Ковб<PERSON>й Талон (престижный)", "chromas": false}, {"id": "91049", "num": 49, "name": "Первобытный охотник Талон", "chromas": true}, {"id": "91059", "num": 59, "name": "Великий уравнитель Талон", "chromas": false}], "lore": "Талон – это клинок во тьме, безжалостный убийца, который бьет без предупреждения и способен скрыться до того, как поднимется тревога. Он заработал себе опасную репутацию на жестоких улицах Ноксуса, где ему приходилось сражаться, убивать и воровать, чтобы выжить. Печально известная семья дю Кото взяла его под свое крыло, и теперь он сеет смерть по приказу империи, убивая вражеских лидеров, капитанов и героев... а также ноксианцев, которым хватило глупости заслужить презрение своих господ.", "blurb": "Талон – это клинок во тьме, безжалостный убийца, который бьет без предупреждения и способен скрыться до того, как поднимется тревога. Он заработал себе опасную репутацию на жестоких улицах Ноксуса, где ему приходилось сражаться, убивать и воровать...", "allytips": ["Используйте Путь убийцы, чтобы напасть на врагов с тыла или эффективно использовать Ноксианскую дипломатию.", "Атака из тени хорошо подходит для выхода из боя, но также может быть использована для нападения на группу.", "Выбирайте свою цель до начала боя. Фокусируя все умения Талона на одну цель, можно добиться успеха, но если Талон начинает метаться между несколькими врагами, он становится беспомощным."], "enemytips": ["Все атаки Талона наносят физический урон. Покупайте броню раньше обычного, чтобы противостоять его взрывному урону.", "Талон очень часто использует Атаку из тени, чтобы выйти из боя. Пока умение на перезарядке, он особенно уязвим.", "Талон способен с легкостью перемещаться между линиями. Старайтесь не упускать его из виду или агрессивно продвигать линию, чтобы он не мог воспользоваться этим преимуществом."], "tags": ["Assassin"], "partype": "Мана", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 658, "hpperlevel": 109, "mp": 400, "mpperlevel": 37, "movespeed": 335, "armor": 30, "armorperlevel": 4.7, "spellblock": 36, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 7.6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.9, "attackspeed": 0.625}, "spells": [{"id": "TalonQ", "name": "Ноксианская дипломатия", "description": "Талон поражает выбранного бойца. Если удар прошел в ближнем бою, эта атака наносит критический урон. Если Талон находился слишком далеко для атаки, то он прыгает к цели и атакует ее. Если это умение убивает цель, то Талон восстанавливает себе небольшое количество здоровья и сокращает перезарядку этого умения.", "tooltip": "Талон прыгает к цели и наносит <physicalDamage>{{ leapdamage }} физического урона</physicalDamage>. При применении вблизи Талон совершает критический удар, нанося <physicalDamage>{{ criticaldamage }} физического урона</physicalDamage>.<br /><br />Если это умение убивает цель, Талон восстанавливает <healing>{{ totalhealing }} здоровья</healing> и сокращает его перезарядку на {{ cooldownrefund*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "TalonQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TalonW", "name": "Раздирание", "description": "Талон мечет веером клинки, которые затем возвращаются к нему. По пути туда и обратно клинки наносят врагам физический урон. На пути обратно клинки наносят дополнительный урон и замедляют пораженные цели.", "tooltip": "Талон мечет веером клинки, нанося врагам <physicalDamage>{{ totalinitialdamage }} физического урона</physicalDamage>. После этого клинки возвращаются к Талону, нанося врагам <physicalDamage>{{ totalreturndamage }} физического урона</physicalDamage> и <status>замедляя</status> их на {{ movespeedslow*100 }}% на {{ slowduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Стоимость – @AbilityResourceName@", "Начальный урон", "Урон на пути обратно", "Замедление", "Перезарядка"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ initialbasedamage }} -> {{ initialbasedamageNL }}", "{{ returnbasedamage }} -> {{ returnbasedamageNL }}", "{{ movespeedslow*100.000000 }}% -> {{ movespeedslownl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TalonW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TalonE", "name": "Путь убийцы", "description": "Талон перепрыгивает через ближайшее строение или препятствие. У этого умения короткая перезарядка, но Талон не может часто перепрыгивать через одно и то же препятствие.", "tooltip": "Талон перепрыгивает через ближайший элемент ландшафта или строение. Талон не может перепрыгнуть через одно и то же препятствие чаще чем раз в {{ wallcd }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка препятствия"], "effect": ["{{ wallcd }} -> {{ wallcdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [625, 625, 625, 625, 625], [1250, 1250, 1250, 1250, 1250], [2, 2, 2, 2, 2], [160, 135, 110, 85, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "625", "1250", "2", "160/135/110/85/60", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "TalonE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "TalonR", "name": "Атака из тени", "description": "Талон мечет клинки по кругу и становится невидимым, увеличивая свою скорость передвижения. Когда он выходит из невидимости, клинки слетаются к нему. При движении клинков в каждом направлении пораженным врагам наносится физический урон.", "tooltip": "Талон швыряет по кругу клинки, нанося врагам <physicalDamage>{{ damage }} физического урона</physicalDamage>, увеличивая свою <speed>скорость передвижения на {{ movespeed*100 }}%</speed> и становясь <keywordStealth>невидимым</keywordStealth> на {{ duration }} сек. Когда он выходит из <keywordStealth>невидимости</keywordStealth>, клинки возвращаются к нему, снова нанося <physicalDamage>{{ damage }} физического урона</physicalDamage>.<br /><br />Если Талон преждевременно выходит из <keywordStealth>невидимости</keywordStealth> с помощью автоатаки или умения <spellName>Ноксианская дипломатия</spellName>, клинки слетаются к его цели.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Скорость передвижения", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TalonR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Острие клинка", "description": "Умения Талона ранят чемпионов и больших монстров, суммируясь до 3 раз. Когда Талон совершает автоатаку по цели с 3 ранениями, она начинает кровоточить, получая большой периодический урон.", "image": {"full": "TalonP.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}