{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tristana": {"id": "<PERSON><PERSON>", "key": "18", "name": "トリスターナ", "title": "ヨードルの主砲", "image": {"full": "Tristana.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "18000", "num": 0, "name": "default", "chromas": false}, {"id": "18001", "num": 1, "name": "Riotガール トリスターナ", "chromas": false}, {"id": "18002", "num": 2, "name": "可愛いエルフ トリスターナ", "chromas": false}, {"id": "18003", "num": 3, "name": "消防士トリスターナ", "chromas": false}, {"id": "18004", "num": 4, "name": "ゲリラ兵トリスターナ", "chromas": false}, {"id": "18005", "num": 5, "name": "女海賊トリスターナ", "chromas": false}, {"id": "18006", "num": 6, "name": "ロケットガール トリスターナ", "chromas": true}, {"id": "18010", "num": 10, "name": "龍使いトリスターナ", "chromas": true}, {"id": "18011", "num": 11, "name": "魅惑の魔女トリスターナ", "chromas": false}, {"id": "18012", "num": 12, "name": "オメガ小隊トリスターナ", "chromas": true}, {"id": "18024", "num": 24, "name": "ちび魔トリスターナ", "chromas": true}, {"id": "18033", "num": 33, "name": "ペング コスプレ トリスターナ", "chromas": true}, {"id": "18040", "num": 40, "name": "ヘクステック トリスターナ", "chromas": false}, {"id": "18041", "num": 41, "name": "爆発花火トリスターナ", "chromas": true}, {"id": "18051", "num": 51, "name": "精霊の花祭りトリスターナ", "chromas": true}, {"id": "18061", "num": 61, "name": "妖精の王宮トリスターナ", "chromas": true}], "lore": "他のヨードルたちは自らのエネルギーを発見や発明、またはただのいたずらに注いでいるが、トリスターナはいつだって偉大な戦士の冒険に憧れていた。彼女はルーンテラの様々な派閥や戦争の話を聞き、自分たちヨードルだって、そこで価値のある伝説を残せるはずだと考えた。信頼する愛砲「ブーマー」とともに初めてこの世界に足を踏み入れた彼女は、断固たる勇気と楽観主義を胸に戦闘に飛び込んでいく。", "blurb": "他のヨードルたちは自らのエネルギーを発見や発明、またはただのいたずらに注いでいるが、トリスターナはいつだって偉大な戦士の冒険に憧れていた。彼女はルーンテラの様々な派閥や戦争の話を聞き、自分たちヨードルだって、そこで価値のある伝説を残せるはずだと考えた。信頼する愛砲「ブーマー」とともに初めてこの世界に足を踏み入れた彼女は、断固たる勇気と楽観主義を胸に戦闘に飛び込んでいく。", "allytips": ["トリスターナの射程はレベルに応じてかなり長くなる。この長所をうまく生かせば、敵からの攻撃を受けずに済む。", "敵チャンピオンに付着している「ヨードルグレネード」のスタックが溜まっていたら「ロケットジャンプ」で仕掛けるチャンス。", "「ラピッドファイア」は、敵チャンピオンに付着している「ヨードルグレネード」のスタックを増加させるのに重宝するスキルだ。"], "enemytips": ["トリスターナが「ラピッドファイア」を発動させたら、攻撃を封じるか射程距離から撤退し、効果時間が終了するのを待とう。", "レーンでは「ヨードルグレネード」の巻き添えを極力食らわずに済むように、瀕死の敵ミニオンからは離れよう。"], "tags": ["Marksman", "Assassin"], "partype": "マナ", "info": {"attack": 9, "defense": 3, "magic": 5, "difficulty": 4}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 300, "mpperlevel": 32, "movespeed": 325, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.5, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 1.5, "attackspeed": 0.656}, "spells": [{"id": "TristanaQ", "name": "ラピッドファイア", "description": "高速で連射を行い、攻撃速度が数秒間増加する。", "tooltip": "フルオート射撃になり、{{ buffduration }}秒間<attackSpeed>攻撃速度が{{ attackspeedmod*100 }}%</attackSpeed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃速度", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TristanaQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TristanaW", "name": "ロケットジャンプ", "description": "地面を砲撃した反動で指定地点へジャンプし、着地と同時に周辺のユニット全員にダメージを与え、短時間スロウ効果を付与する。", "tooltip": "自身を飛ばして、着地時に<magicDamage>{{ landingdamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowmod*-100 }}%の<status>スロウ効果</status>を付与する。<br /><br />チャンピオンからキルまたはアシストを獲得するか、チャンピオンに付着した<spellName>「ヨードルグレネード」</spellName>を最大スタックにして爆発させると、このスキルのクールダウンが解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TristanaW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TristanaE", "name": "ヨードルグレネード", "description": "自動効果: ユニットを倒すと砲弾が炸裂して金属片が飛び散り、周辺の敵ユニットにダメージを与える。<br><br>発動効果: 対象にグレネードを付着させる。グレネードは数秒後に起爆し、対象と周囲のユニットにダメージを与える。", "tooltip": "<spellPassive>自動効果: </spellPassive>通常攻撃で敵を倒すと周囲の敵に<magicDamage>{{ passivedamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><spellActive>発動効果:</spellActive> 敵またはタワーにグレネードを付着させ、{{ activeduration }}秒後に周囲の敵に<physicalDamage>{{ activedamage }}の物理ダメージ</physicalDamage>を与える。このダメージはクリティカル率の{{ critchanceamp*100 }}%分増加し、また通常攻撃かスキルを命中させるたびに{{ activeperstackamp*100 }}%増加する(最大4スタック)。<br /><br />{{ activemaxstacks }}スタックした瞬間にグレネードは起爆する(最大<physicalDamage>{{ activemaxdamage }}の物理ダメージ</physicalDamage>)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果の爆発ダメージ", "グレネード基本ダメージ", "攻撃力反映率", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ activebadratio*100.000000 }}% -> {{ activebadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TristanaR", "name": "バスターショット", "description": "敵ユニット1体へ向けて巨大な砲弾を発射し、魔法ダメージを与えてノックバックさせる。対象に「ヨードルグレネード」が付着していた場合、爆発半径が2倍になる。", "tooltip": "巨大な砲弾を発射して、対象に<magicDamage>{{ damagecalc }}の魔法ダメージ</magicDamage>を与え、周囲の敵と一緒に<status>ノックバック</status>させ、{{ stunduration }}秒間<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "ノックバック距離", "スタン効果時間:"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ knockbackdistance }} -> {{ knockbackdistanceNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ドロー＆グロー", "description": "レベルが上がるごとに射程が増加する。", "image": {"full": "Tristana_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}