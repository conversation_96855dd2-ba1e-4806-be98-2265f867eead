{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gragas": {"id": "Gragas", "key": "79", "name": "Gragas", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Gragas.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "79000", "num": 0, "name": "default", "chromas": false}, {"id": "79001", "num": 1, "name": "Gragas scafandru", "chromas": false}, {"id": "79002", "num": 2, "name": "Gragas de la țară ", "chromas": false}, {"id": "79003", "num": 3, "name": "<PERSON>ș G<PERSON>  ", "chromas": false}, {"id": "79004", "num": 4, "name": "Gragas rafinatul", "chromas": false}, {"id": "79005", "num": 5, "name": "<PERSON><PERSON><PERSON> vandal<PERSON>", "chromas": false}, {"id": "79006", "num": 6, "name": "Gragas Oktoberfest", "chromas": false}, {"id": "79007", "num": 7, "name": "Gragas super suporter", "chromas": false}, {"id": "79008", "num": 8, "name": "Gragas Fnatic ", "chromas": false}, {"id": "79009", "num": 9, "name": "Gragas, călugărul berar", "chromas": false}, {"id": "79010", "num": 10, "name": "Gragas operațiuni arctice", "chromas": false}, {"id": "79011", "num": 11, "name": "<PERSON><PERSON>s gardian", "chromas": true}, {"id": "79020", "num": 20, "name": "Gragas distracție în spațiu", "chromas": true}, {"id": "79029", "num": 29, "name": "Gragas din Vestul Sălbatic", "chromas": true}, {"id": "79039", "num": 39, "name": "Gragas fan muzică", "chromas": true}], "lore": "Gragas este un berar enorm și g<PERSON><PERSON>, deopotrivă vesel și impunător, care e mereu în căutarea unor noi moduri de a-i înveseli pe toți cei din jur. Nu se știe de unde vine, dar caută ingrediente în ținuturile sălbatice ale Freljordului, care să-l ajute să-și perfecționeze cel mai recent amestec. E impulsiv, încăpățânat și renumit pentru bătăile pe care le începe în taverne, încheindu-se deseori cu chefuri îndelungate și proprietăți vandalizate. <PERSON><PERSON>d apare, sigur va fi rost de distracție și de distrugere – în ordinea asta.", "blurb": "Gragas este un berar enorm și g<PERSON><PERSON>, deopotrivă vesel și impunător, care e mereu în căutarea unor noi moduri de a-i înveseli pe toți cei din jur. Nu se știe de unde vine, dar caută ingrediente în ținuturile sălbatice ale Freljordului, care să-l ajute...", "allytips": ["Reducerea daunelor oferită de ''Beție turbată'' este aplicată când începi să bei. Încearcă să o folosești când vezi că urmează să suferi daune.", "Încearcă să arunci inamicii în turnurile tale cu ''Butoi exploziv''.", "Încearcă să combini ''Izbitură'' cu ''Butoi exploziv'' pentru a servi echipei tale inamicii gata de ucis."], "enemytips": ["Gragas își poate arunca toți dușmanii în spate cu abilitatea sa supremă. Ai grijă să nu ricoșezi în el sau, și mai rău, într-un turn inamic.", "''Izbitură'' are un timp de reactivare foarte scăzut, iar Gragas devine astfel greu de urmărit. Nu forța nota când încerci să-l ajungi din urmă."], "tags": ["Fighter", "Mage"], "partype": "Mană", "info": {"attack": 4, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 115, "mp": 400, "mpperlevel": 47, "movespeed": 330, "armor": 38, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.05, "attackspeed": 0.675}, "spells": [{"id": "GragasQ", "name": "Rostogolirea <PERSON>ului", "description": "Gragas își rostogolește butoiul până în locația-țintă. Acesta va exploda fie după 4 secunde, fie la reactivare. Puterea exploziei crește în timp. Inamicilor loviți de explozie le scade viteza de mișcare.", "tooltip": "Gragas rostogolește un butoi care explodează după {{ e4 }} secunde, provocând între <magicDamage>{{ mindamage }} daune magice</magicDamage> și <magicDamage>{{ maxdamage }} daune magice</magicDamage> și <status>încetinind</status> inamicii cu între {{ e2 }} și {{ effect2amount*1.5 }}% timp de {{ e3 }} secunde. Daunele și <status>încetinirea</status> cresc în funcție de timpul scurs până la explozia butoiului. <br /><br />Gragas poate <recast>refolosi</recast> abilitatea pentru a detona butoiul mai devreme.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Timp de reactivare"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [40, 45, 50, 55, 60], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [2, 2, 2, 2, 2], [150, 150, 150, 150, 150], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "40/45/50/55/60", "2", "4", "2", "150", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "GragasQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasW", "name": "Be<PERSON><PERSON> turbat<PERSON>", "description": "Gragas își degustă cea mai recentă băutură timp de 1 secundă. <PERSON><PERSON><PERSON>, devine găl<PERSON>gios și agresiv, provocându-le daune magice tuturor inamicilor din apropiere cu următorul atac de bază și reducând daunele primite.", "tooltip": "Gragas își degustă băutura, reducând daunele primite cu {{ damagereduction }} timp de {{ defenseduration }} secunde și îmbunătățind următorul său atac, care le va provoca țintei și inamicilor din jur <magicDamage>daune magice suplimentare în valoare de {{ totaldamage }}</magicDamage> plus <magicDamage>{{ maxhppercentdamage }}% din viața maximă</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Reducere daune", "Daune"], "effect": ["{{ basedamagereduction }}% -> {{ basedamagereductionNL }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "GragasW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasE", "name": "Izbitură", "description": "Gragas dă năvală înainte și se izbește de prima unitate inamică din cale, provocându-le daune tuturor unităților inamice din apropiere și amețindu-le.", "tooltip": "Gragas dă năvală înainte, izbindu-se de primul inamic, <status>aruncând în sus</status> inamicii din apropiere timp de {{ stunduration }} secundă și provocându-le <magicDamage>{{ totaldamage }} daune magice</magicDamage>.<br /><br />Timpul de reactivare al acestei abilități este redus cu {{ cooldownrefund*100 }}% dacă Gragas se lovește de un inamic.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GragasE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasR", "name": "Butoi exploziv", "description": "Gragas azvârle butoiul în locația-țintă, provocând daune și proiectând în spate inamicii prinși în zona exploziei.", "tooltip": "Gragas î<PERSON> but<PERSON>, provocând <magicDamage>{{ damagedone }} daune magice</magicDamage> și <status>aruncând în spate</status> inamicii din zona de impact.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GragasR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Happy Hour", "description": "Gragas se vindecă periodic după ce folosește o abilitate.", "image": {"full": "GragasPassiveHeal.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}