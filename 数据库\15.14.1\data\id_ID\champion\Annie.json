{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Annie": {"id": "<PERSON>", "key": "1", "name": "<PERSON>", "title": "the Dark Child", "image": {"full": "Annie.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "1000", "num": 0, "name": "default", "chromas": false}, {"id": "1001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "1002", "num": 2, "name": "Red Riding Annie", "chromas": false}, {"id": "1003", "num": 3, "name": "<PERSON> in Wonderland", "chromas": false}, {"id": "1004", "num": 4, "name": "Prom Queen <PERSON>", "chromas": false}, {"id": "1005", "num": 5, "name": "Frostfire Annie", "chromas": false}, {"id": "1006", "num": 6, "name": "Reverse Annie", "chromas": false}, {"id": "1007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "1008", "num": 8, "name": "Panda <PERSON>", "chromas": false}, {"id": "1009", "num": 9, "name": "Sweetheart Annie", "chromas": false}, {"id": "1010", "num": 10, "name": "Hextech Annie", "chromas": false}, {"id": "1011", "num": 11, "name": "Super Galaxy Annie", "chromas": false}, {"id": "1012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "1013", "num": 13, "name": "Lunar Beast Annie", "chromas": true}, {"id": "1022", "num": 22, "name": "Cafe Cuties Annie", "chromas": false}, {"id": "1031", "num": 31, "name": "Fright Night Annie", "chromas": false}, {"id": "1040", "num": 40, "name": "Winter<PERSON>sed Annie", "chromas": false}, {"id": "1050", "num": 50, "name": "Battle Princess Annie", "chromas": false}], "lore": "Annie adalah mage cilik dengan kekuatan api yang besar. Dia melucuti senjata sebelum waktunya meskipun berbahaya. Bahkan dalam bayang-bayang pegunungan utara Noxus pun, dia tetaplah pengecualian yang ajaib. Ketertarikannya pada api muncul pada awal kehidupannya lewat ledakan emosi tak terduga, meski pada akhirnya dia belajar mengendalikan “trik main-main” ini. Dia suka memanggil boneka beruang kesayangannya, <PERSON><PERSON><PERSON>, sebagai pelindung yang berapi-api. Terbuai selamanya dalam masa kecil yang penuh kepolosan, <PERSON> men<PERSON> di hutan gelap, selalu mencari teman untuk bermain bersama.", "blurb": "Annie adalah mage cilik dengan kekuatan api yang besar. Dia melucuti senjata sebelum waktunya meskipun berbahaya. Bahkan dalam bayang-bayang pegunungan utara Noxus pun, dia tetaplah pengecualian yang ajaib. Ketertarikannya pada api muncul pada awal...", "allytips": ["Menyimpan stun untuk digunakan dengan ultima bisa mengubah jalannya pertempuran tim.", "Melancarkan serangan terakhir ke minion dengan Disintegrate memudahkan Annie farming sangat baik di early game.", "Molten Shield adalah spell yang tepat digunakan untuk menyiapkan stun Annie, jadi kadang menguntungkan untuk ambil minimal 1 rank di early game."], "enemytips": ["<PERSON> meman<PERSON> be<PERSON>, <PERSON><PERSON><PERSON>, memberikan efek Burn pada unit musuh di sekitarnya. Coba jaga jarakmu darinya setelah dia dipanggil.", "Summoner Smite bisa digunakan untuk membantu melumpuhkan Tibbers.", "Hati-hati dengan kekuatan berputar warna putih di sekitar Annie. Artinya dia sudah siap melancarkan Stun."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 10, "difficulty": 6}, "stats": {"hp": 560, "hpperlevel": 96, "mp": 418, "mpperlevel": 25, "movespeed": 335, "armor": 23, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 625, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 2.65, "attackspeedperlevel": 1.36, "attackspeed": 0.61}, "spells": [{"id": "AnnieQ", "name": "Disintegrate", "description": "<PERSON> me<PERSON><PERSON>an bola api yang be<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> damage dan menge<PERSON>likan biaya Mana jika bola itu menghancurkan target.", "tooltip": "<PERSON> me<PERSON>an bola api, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage>. <PERSON><PERSON> target mati, <PERSON> men<PERSON>an biaya <PERSON>a dan mengu<PERSON>i Cooldown sebanyak 50%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "AnnieQ.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AnnieW", "name": "Incinerate", "description": "Annie me<PERSON><PERSON><PERSON> serangan api yang <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> damage ke semua musuh di area itu.", "tooltip": "<PERSON> melu<PERSON>kan gelombang api, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieW.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON>", "name": "Molten Shield", "description": "Memberikan shield ke <PERSON> atau sekutu, burst Move Speed, dan damage ke musuh yang menyerangnya dengan serangan atau spell.", "tooltip": "<PERSON> memberikan <shield>{{ shieldblocktotal }} Shield</shield> ke champion sekutu selama {{ shieldduration }} detik dan <speed>{{ movespeedcalc }} Move Speed yang berkurang</speed> selama {{ movementspeedduration }} detik. Selama shield masih aktif, musuh yang menghantam sekutu di dalam shield dengan Serangan atau Ability akan menerima <magicDamage>{{ damagereturn }} magic damage</magicDamage> satu kali per shield.<br /><br />Tibbers selalu mendapatkan efek dari <spellName>Molten Shield</spellName> saat dipanggil.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Health Shield", "Pemantulan Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ shieldamount }}-> {{ shieldamountNL }}", "{{ damagereflection }}-> {{ damagereflectionNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AnnieE.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AnnieR", "name": "Summon: <PERSON><PERSON><PERSON>", "description": "Annie membang<PERSON><PERSON><PERSON> beru<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> damage ke unit di area itu. Tibbers bisa menyerang dan juga menerapkan burn ke musuh yang berdiri di dekatnya.", "tooltip": "<spellPassive>Pasif:</spellPassive> <PERSON> {{ rpercentpenbuff*100 }}% Magic Penetration.<br /><br /><PERSON> me<PERSON> beru<PERSON>, <PERSON>ib<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ initialburstdamage }} magic damage</magicDamage>. <PERSON><PERSON><PERSON> {{ tibberslifetime }} detik berikut<PERSON>, Tibbers memberikan efek burn ke musuh di sekitar sebesar <magicDamage>{{ tibbersauradamage }} magic damage per detik</magicDamage>.<br /><br />Tibbers mengamuk saat dipanggil, jika <PERSON> memberikan stun ke champion musuh, dan jika Annie mati. Saat mengamuk, Tibbers mendapatkan <attackSpeed>275% Attack Speed</attackSpeed> dan <speed>100% Move Speed</speed> yang berkurang dalam kurun waktu 3 detik.<br /><br /><recast>Recast:</recast> Mengutus Tibbers secara manual.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Awal", "Damage Aura", "Attack Damage", "Move Speed Bonus", "Cooldown", "Magic Penetration"], "effect": ["{{ initialdamage }}-> {{ initialdamageNL }}", "{{ auradamage }}-> {{ auradamageNL }}", "{{ tibbersattackdamage }}-> {{ tibbersattackdamageNL }}", "{{ tibbersbonusms }}-> {{ tibbersbonusmsNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ rpercentpenbuff*100.000000 }}%-> {{ rpercentpenbuffnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieR.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pyromania", "description": "<PERSON><PERSON><PERSON> cast 4 spell, spell of<PERSON>if <PERSON> be<PERSON> akan menera<PERSON>kan stun ke target.<br><br>Annie memulai game dan muncul kembali dengan Pyromania tersedia.", "image": {"full": "Annie_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}