{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Garen": {"id": "<PERSON><PERSON><PERSON>", "key": "86", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Мощь Демасии", "image": {"full": "Garen.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "86000", "num": 0, "name": "default", "chromas": true}, {"id": "86001", "num": 1, "name": "Кроваво-красный Гарен", "chromas": false}, {"id": "86002", "num": 2, "name": "Пустынный воин Гарен", "chromas": false}, {"id": "86003", "num": 3, "name": "Коммандо Гарен", "chromas": false}, {"id": "86004", "num": 4, "name": "Темный рыцарь Гарен", "chromas": false}, {"id": "86005", "num": 5, "name": "Суровый Гарен", "chromas": false}, {"id": "86006", "num": 6, "name": "Гарен из Стального легиона", "chromas": false}, {"id": "86010", "num": 10, "name": "Пиратский адмирал Гарен", "chromas": false}, {"id": "86011", "num": 11, "name": "Гарен из Воюющих царств", "chromas": true}, {"id": "86013", "num": 13, "name": "Король-бог Гарен", "chromas": false}, {"id": "86014", "num": 14, "name": "Гарен из полиции Демасии", "chromas": true}, {"id": "86022", "num": 22, "name": "Гарен из Механических царств", "chromas": false}, {"id": "86023", "num": 23, "name": "Гарен из Механических царств (престижный)", "chromas": false}, {"id": "86024", "num": 24, "name": "Гарен из Боевой академии", "chromas": true}, {"id": "86033", "num": 33, "name": "Прядильщик историй Гарен", "chromas": true}, {"id": "86044", "num": 44, "name": "Па<PERSON><PERSON>ий король-бог Гарен", "chromas": false}], "lore": "Гордый и благородный воин Гарен сражается в рядах Бесстрашного авангарда. Его уважают не только товарищи, но и враги, не в последнюю очередь благодаря происхождению. Гарен – отпрыск влиятельной семьи Краунгардов, которая на протяжении поколений защищает Демасию и ее идеалы. Его доспехи защищают от магии, а меч готов сокрушить магов и колдунов на поле брани во имя высшей справедливости.", "blurb": "Гордый и благородный воин Гарен сражается в рядах Бесстрашного авангарда. Его уважают не только товарищи, но и враги, не в последнюю очередь благодаря происхождению. Гарен – отпрыск влиятельной семьи Краунгардов, которая на протяжении поколений защищает...", "allytips": ["Гарен начинает быстро восстанавливать здоровье, если не получает урона в течение нескольких секунд.", "Вердикт наносит максимальный урон, когда поражает одиночную цель. Старайтесь занять такую позицию, чтобы поразить умением только вражеского чемпиона.", "Умения Гарена ограничены только временем перезарядки, поэтому предметы вроде Черной секиры весьма полезны."], "enemytips": ["Увеличивайте вашу броню, чтобы снизить физический урон от атак Гарена.", "Убегайте от Гарена, когда у вас мало здоровья, иначе он быстро казнит вас с помощью Правосудия Демасии.", "Остерегайтесь атаковать Гарена в кустах. Это может привести к получению полного урона от Вердикта.", "Вердикт наносит максимальный урон, когда поражает одиночную цель. Если вы не можете выйти из зоны его действия, постарайтесь скрыться в толпе союзных миньонов, чтобы уменьшить получаемый урон."], "tags": ["Fighter", "Tank"], "partype": "Нет", "info": {"attack": 7, "defense": 7, "magic": 1, "difficulty": 5}, "stats": {"hp": 690, "hpperlevel": 98, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 38, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.5, "attackspeedperlevel": 3.65, "attackspeed": 0.625}, "spells": [{"id": "GarenQ", "name": "Решающий удар", "description": "Гарен избавляется ото всех замедляющих эффектов, и его скорость передвижения увеличивается. Его следующая автоатака поражает слабое место цели, заставляя ее замолчать и нанося ей дополнительный урон.", "tooltip": "Гарен снимает с себя все эффекты <status>замедления</status> и получает <speed>{{ movementspeedamount*100 }}% скорости передвижения</speed> на {{ movementspeedduration }} сек.<br /><br />Его следующая автоатака накладывает на цель <status>безмолвие</status> на {{ silenceduration }} сек. и наносит ей <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Продолжительность ускорения"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedduration }} -> {{ movementspeeddurationNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "GarenQ.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "GarenW", "name": "Храбрость", "description": "Броня и сопротивление магии Гарена увеличиваются, когда он убивает врагов. При активации умения он получает щит и значительный бонус к стойкости на короткий период. В течение оставшегося времени действия Гарен получает немного меньше урона.", "tooltip": "<spellPassive>Пассивно:</spellPassive> Гарен имеет<scaleArmor>{{ resistsfortooltip }} дополнительной брони</scaleArmor> и <scaleMR>{{ resistsfortooltip }} дополнительного сопротивления магии</scaleMR>. Каждое убийство бойца навсегда дает защитные показатели в размере <attention>{{ resistgainonkilltooltip }}</attention> (максимальный бонус – <attention>{{ resistmax }}</attention>).<br /><br /><spellActive>Активно:</spellActive> Гарен становится храбрее на {{ drduration }} сек., уменьшая получаемый урон на {{ drpercent*100 }}%. Кроме того, он получает <shield>щит прочностью {{ totalshield }}</shield> и <slow>{{ upfronttenacity*100 }}% стойкости</slow> на {{ upfrontduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Прочность щита", "Уменьшение урона", "Перезарядка"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GarenW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "GarenE", "name": "Вердикт", "description": "Гарен быстро вращается на месте с мечом в руках, нанося физический урон врагам поблизости.", "tooltip": "Гарен быстро вращает меч вокруг себя в течение {{ duration }} сек., нанося окружающим врагам <physicalDamage>{{ totaldamage }} ед. физического урона</physicalDamage> {{ f1 }} р. Ближайший враг получает на <physicalDamage>{{ nearestenemybonus*100 }}% больше урона</physicalDamage>. Чемпионы, пораженные хотя бы {{ stackstoshred }} ударами, теряют <scaleArmor>{{ shredamount*100 }}% брони</scaleArmor> на {{ shredduration }} сек.<br /><br /><recast>Повторное применение</recast>: действие этого умения прекращается преждевременно.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон за вращение", "Коэффициент силы атаки за вращение", "Перезарядка"], "effect": ["{{ basedamagepertick }} -> {{ basedamagepertickNL }}", "{{ adratiopertick*100.000000 }}% -> {{ adratioperticknl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "GarenE.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "GarenR", "name": "Правосудие Демасии", "description": "Гарен призывает мощь Демасии, чтобы казнить вражеского чемпиона.", "tooltip": "Гарен призывает мощь Демасии, чтобы сразить вражеского чемпиона, нанося <trueDamage>чистый урон в размере {{ basedamage }} плюс {{ executedamage*100 }}% от недостающего здоровья цели</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Урон", "Коэффициент урона от недостающего здоровья"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ executedamage*100.000000 }}% -> {{ executedamagenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "GarenR.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Упорство", "description": "Если Гарен не получает урона и не подвергается действию вражеских умений в течение некоторого времени, он начинает восстанавливать часть от своего общего запаса здоровья каждую секунду.", "image": {"full": "Garen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}