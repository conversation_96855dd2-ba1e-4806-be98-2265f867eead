{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Briar": {"id": "<PERSON><PERSON><PERSON>", "key": "233", "name": "<PERSON><PERSON><PERSON>", "title": "the Restrained Hunger", "image": {"full": "Briar.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "233000", "num": 0, "name": "default", "chromas": false}, {"id": "233001", "num": 1, "name": "Street Demons Briar", "chromas": true}, {"id": "233010", "num": 10, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON><PERSON> yang gagal dari <PERSON>, rasa haus darah B<PERSON>r yang liar perlu diredam pil khusus agar pikiran kacaunya terkendali. <PERSON><PERSON><PERSON> bertahun-tahun dikurung, senjata hidup ini terbebas dari kurungannya dan melepaskan diri ke dunia. Kini dia tidak dikendalikan siapa-siapa, dan hanya mengikuti rasa hausnya akan pengetahuan dan darah. Dia menikmati kebebasannya, meski tak mudah untuk mengendalikan kegilaannya.", "blurb": "<PERSON><PERSON><PERSON><PERSON> yang gagal dari <PERSON>, rasa haus darah <PERSON><PERSON>r yang liar perlu diredam pil khusus agar pikiran kacaunya terkendali. <PERSON><PERSON><PERSON> bertahun-tahun dikurung, senjata hidup ini terbebas dari kurungannya dan melepaskan diri ke dunia. Kini dia tidak...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Fury", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 95, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 30, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 0, "hpregenperlevel": 0, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "BriarQ", "name": "<PERSON>", "description": "Briar melompat ke unit dan menyerang musuh dengan The Heel Wheel (of Pain), menerapkan stun serta menghancurkan Armor mereka.", "tooltip": "Briar melompat ke target, men<PERSON><PERSON>kan <status>Stun</status> selama {{ stunduration }} detik, men<PERSON><PERSON><PERSON>an <magicDamage>{{ totaldamage }} magic damage</magicDamage> dan mengurangi {{ shredpercent*100 }}% <scaleArmor>Armor</scaleArmor> serta <scaleMR>Magic Resist</scaleMR> selama {{ shredduration }} detik.<br /><br /><rules>Briar akan berhenti memprioritaskan champion jika dia cast Ability ini pada minion atau monster selama <keywordMajor>Blood Frenzy</keywordMajor>.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON>or dan <PERSON> Resist Shred", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ shredpercent*100.000000 }}%-> {{ shredpercentnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% dari Health saat ini", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "BriarQ.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Menggunakan {{ spell.briarp:currenthealthpercentcost*100 }}% dari Health saat ini"}, {"id": "BriarW", "name": "Blood Frenzy / Snack Attack", "description": "Briar melompat ke depan dan menghancurkan pillory-nya, memasuki Blood Frenzy yang membuatnya tanpa henti mengejar musuh terdekat (memprioritaskan champion). <PERSON><PERSON> kondisi ini, dia mendapatkan peningkatan Attack Speed dan Move Speed, lalu serang<PERSON><PERSON> menghasilkan damage dalam 1 area di sekitar targetnya.<br><br>Briar bisa mengaktifkan ulang Ability ini saat dalam kondisi Frenzy untuk MENGGIGIT targetnya pada serangan berikutnya, mengh<PERSON>lkan damage tambahan berdasarkan Health yang hilang, dan memberikan heal pada Briar berdasarkan damage yang dia hasilkan.", "tooltip": "Briar melompat dan memasuki <keywordMajor>Blood Frenzy</keywordMajor>, taunt musuh di sekitar selama {{ berserkduration }} detik, memprioritaskan Champion. Saat dalam <keywordMajor>Blood Frenzy</keywordMajor> dia mendapatkan <attackSpeed>{{ berserkas*100 }}% Attack Speed</attackSpeed> dan <speed>{{ berserkms*100 }}% Move Speed</speed>, lalu serang<PERSON>ya menghasilkan <physicalDamage>{{ totalaoedamage }} physical damage</physicalDamage> pada musuh di sekitar targetnya.<br /><br />Briar bisa <recast>Recast</recast> Ability ini untuk memperkuat Serangan berikutnya. Seranga<PERSON><PERSON> menghasilkan <physicalDamage>{{ totalattackbonusdamage }} + {{ totalattackpercentmissinghealth }}% physical damage dari Health yang hilang</physicalDamage> dan <healing>heal Briar sebanyak {{ attackmaxhpheal }} + {{ attackhealpercent*100 }}% dari damage yang dihasilkan</healing>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Speed", "Move Speed", "Rasio AD Damage Area", "Damage", "Heal", "Cooldown"], "effect": ["{{ berserkas*100.000000 }}%-> {{ berserkasnl*100.000000 }}%", "{{ berserkms*100.000000 }}%-> {{ berserkmsnl*100.000000 }}%", "{{ aoeattackdamagepercent*100.000000 }}%-> {{ aoeattackdamagepercentnl*100.000000 }}%", "{{ attackbonusdamage }}-> {{ attackbonusdamageNL }}", "{{ attackhealpercent*100.000000 }}%-> {{ attackhealpercentnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% dari Health saat ini", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "BriarW.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Menggunakan {{ spell.briarp:currenthealthpercentcost*100 }}% dari Health saat ini"}, {"id": "BriarE", "name": "Chilling Scream", "description": "Briar memus<PERSON><PERSON> kembali pikirannya, menghilangkan Blood Frenzy, dan channeling energi menjadi teriakan dahsyat yang memberikan damage dan membuat musuh slow. Saat melakukan charge, dia menerima damage yang berkurang dan melakukan heal sebagian dari Health maksimumnya. Teriakan yang di-charge penuh membuat musuh terkena knock back, men<PERSON><PERSON><PERSON><PERSON> damage tambahan dan memberi stun pada mereka yang menabrak dinding.", "tooltip": "<charge><PERSON><PERSON> Charging:</charge> Briar mengh<PERSON> <keywordMajor>Blood Frenzy</keywordMajor> dan mengumpulkan energi, mendapatkan {{ drpercent }}% pengurangan damage dan memulihkan <healing>{{ percentmaxhpheal }} Health</healing> selama 1 detik.<br /><br /><release>Release:</release> Briar melepaskan teriakan yang menghasilkan hingga <magicDamage>{{ damage }} magic damage</magicDamage> berdasarkan waktu charge, dan menerapkan <status>Slow</status> sebanyak {{ slowpercent*100 }}% selama {{ slowduration }} detik. Saat di-charge penuh, teriakan itu <status>Knock Back</status> musuh, menghasilkan <magicDamage>{{ wallhitdamage }} magic damage</magicDamage> pada yang menabrak dinding dan menerapkan <status>Stun</status> selama {{ wallstunduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage <PERSON>", "<PERSON><PERSON>"], "effect": ["{{ maxbasedamage }}-> {{ maxbasedamageNL }}", "{{ wallhitbasedamage }}-> {{ wallhitbasedamageNL }}", "{{ healhppercent*100.000000 }}%-> {{ healhppercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% dari Health saat ini", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "BriarE.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Menggunakan {{ spell.briarp:currenthealthpercentcost*100 }}% dari Health saat ini"}, {"id": "BriarR", "name": "Certain Death", "description": "Briar men<PERSON>ang batu permata hemolith pillory-<PERSON><PERSON>, menandai champion pertama yang terkena sebagai buruannya. <PERSON>a lalu bergegas <PERSON>, membuat musuh lain di sekitarnya takut begitu dia tiba di target, dan memasuki status hemomania sepenuhnya. Dia akan mengejar buruannya hingga mati, mendapatkan benefit dari Blood Frenzy serta tambahan <PERSON>, Magic Resistance, Life Steal, dan <PERSON>.", "tooltip": "Briar men<PERSON>ang batu permata hemolith pillory-nya dan terbang ke lokasi champion pertama yang terkena, men<PERSON><PERSON><PERSON> sebagai buruannya. Saat mendarat, dia men<PERSON><PERSON>lkan <magicDamage>{{ damage }} magic damage</magicDamage> ke semua unit di sekitar dan menyebabkan musuh yang bukan buruannya <status>Flee</status> selama {{ fearduration }} detik. Lalu dia memasuki <keywordMajor>Blood Frenzy</keywordMajor> yang diperkuat dan akan mengejar buruannya hingga mati. Selama periode ini, dia mendapatkan {{ totalresists }} <scaleArmor>Armor</scaleArmor> dan <scaleMR>Magic Resist</scaleMR>, {{ lifestealpercent*100 }}% Life Steal, dan <speed>{{ extramovespeedpercent*100 }}% Move Speed</speed> tambahan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Life Steal", "Move Speed", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ lifestealpercent*100.000000 }}%-> {{ lifestealpercentnl*100.000000 }}%", "{{ extramovespeedpercent*100.000000 }}%-> {{ extramovespeedpercentnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% dari Health saat ini", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "BriarR.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Menggunakan {{ spell.briarp:currenthealthpercentcost*100 }}% dari Health saat ini"}], "passive": {"name": "Crimson Curse", "description": "Serangan dan Ability Briar men<PERSON><PERSON><PERSON> stack Bleed, yang <PERSON><PERSON><PERSON> heal se<PERSON>ai damage yang dihasilkan. <PERSON><PERSON><PERSON> lapar, dia mendapatkan peningkatan heal berdasarkan Health yang hilang, tetapi tidak memiliki Regenerasi Health bawaan.", "image": {"full": "BriarP.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}