{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Naafiri": {"id": "<PERSON><PERSON><PERSON>", "key": "950", "name": "ナフィーリ", "title": "百刃の猟犬", "image": {"full": "Naafiri.png", "sprite": "champion2.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "950000", "num": 0, "name": "default", "chromas": false}, {"id": "950001", "num": 1, "name": "ソウルファイター ナフィーリ", "chromas": true}, {"id": "950011", "num": 11, "name": "PROJECT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "950020", "num": 20, "name": "ホットドッグ ナフィーリ", "chromas": true}], "lore": "シュリーマの砂漠に、遠吠えが重なり響き渡る。群れを成し、この不毛の地で獲物を狩ろうと争う獰猛な捕食者、デューンハウンドの咆哮だ。その中に、ひと際目立つ群れがある。彼らは猟犬としての本能だけでなく、ダーキンの古の力に駆り立てられているのだ。", "blurb": "シュリーマの砂漠に、遠吠えが重なり響き渡る。群れを成し、この不毛の地で獲物を狩ろうと争う獰猛な捕食者、デューンハウンドの咆哮だ。その中に、ひと際目立つ群れがある。彼らは猟犬としての本能だけでなく、ダーキンの古の力に駆り立てられているのだ。", "allytips": ["シュリーマの砂漠に、遠吠えが重なり響き渡る。群れを成し、この不毛の地で獲物を狩ろうと争う獰猛な捕食者、デューンハウンドの咆哮だ。その中に、ひと際目立つ群れがある。彼らは猟犬としての本能だけでなく、ダーキンの古の力に駆り立てられているのだ。"], "enemytips": [], "tags": ["Assassin", "Fighter"], "partype": "マナ", "info": {"attack": 9, "defense": 5, "magic": 0, "difficulty": 2}, "stats": {"hp": 610, "hpperlevel": 105, "mp": 400, "mpperlevel": 55, "movespeed": 340, "armor": 28, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2, "attackspeedperlevel": 2.1, "attackspeed": 0.663}, "spells": [{"id": "Na<PERSON><PERSON><PERSON>", "name": "ダーキンダガー", "description": "最大2本の短剣を放つ。短剣はそれぞれ出血を付与し、対象がすでに出血中の場合は、それぞれ追加ダメージを与える。<br><br>「群れの同胞」はこのスキルが最初に命中したチャンピオンまたはモンスターに飛びかかって攻撃する。<br>", "tooltip": "ダーキンの力が込められた刃を放ち、<physicalDamage>{{ spell.naafiriq:totaldamagefirstcast }}物理ダメージ</physicalDamage>を与えて対象を出血状態にし、{{ spell.naafiriq:bleedduration }}秒間、<physicalDamage>{{ spell.naafiriq:totalbleeddamage }}物理ダメージ</physicalDamage>を与える。<br /><br />このスキルは<recast>再発動</recast>が可能。命中した敵がすでにこのスキルによって出血状態だった場合、残りの出血ダメージに加えて、<physicalDamage>{{ spell.naafiriq:totalmindamagesecondcast }}</physicalDamage>と、減少体力に応じた<physicalDamage>{{ spell.naafiriq:totalmaxdamagesecondcast }}物理ダメージ</physicalDamage>を与える。対象がチャンピオンまたは大型モンスターだった場合、ナフィーリの<healing>体力が{{ spell.naafiriq:totalhealsecondcast }}回復</healing>する。<br /><br /><keywordMajor>「群れの同胞」</keywordMajor> は最初に命中したチャンピオンまたはモンスターに飛びかかり、{{ spell.naafirip:packmatetauntduration }}秒間攻撃し続ける。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "発動2回目のダメージ", "出血ダメージ", "体力回復量", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ basedamagefirstcast }} -> {{ basedamagefirstcastNL }}", "{{ basedamagesecondcast }} -> {{ basedamagesecondcastNL }}", "{{ bleedbasedamage }} -> {{ bleedbasedamageNL }}", "{{ basehealsecondcast }} -> {{ basehealsecondcastNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NaafiriQ.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NaafiriR", "name": "群れの呼び声", "description": "対象指定不可状態になって群れを強化し、追加の群れの同胞を出現させて、移動速度と攻撃力が増加する。<br>", "tooltip": "{{ untargetableduration }}秒間、対象指定不可状態になって狩りに備え、<keywordMajor>{{ packmatestoadd }}体の追加の「群れの同胞」</keywordMajor>を出現させて、{{ duration }}秒間、<physicalDamage>攻撃力が{{ bonusad }}</physicalDamage>、<speed>移動速度が{{ movespeedamount*100 }}%</speed>増加する。<br /><br /><keywordMajor>「群れの同胞」</keywordMajor>が対象指定不可になり、ナフィーリのもとに呼び戻される。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "クールダウン"], "effect": ["{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24, 22, 20, 18], "cooldownBurn": "26/24/22/20/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "NaafiriR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NaafiriE", "name": "獰猛なる刃", "description": "ダッシュして、自身の周囲にいる敵にダメージを与える。その際、「群れの同胞」を呼び寄せて、体力を全回復させる。", "tooltip": "前方にダッシュして<physicalDamage>{{ totaldamagefirstslash }}の物理ダメージ</physicalDamage>を与える。その後、刃を炸裂させて<physicalDamage>{{ totaldamagesecondslash }}の物理ダメージ</physicalDamage>を与える。<br /><br /><keywordMajor>「群れの同胞」</keywordMajor>は対象指定不可になり、ナフィーリのもとに呼び戻され、<healing>体力が100%回復</healing>する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ(1回目の斬りつけ)", "ダメージ(2回目の斬りつけ)", "クールダウン"], "effect": ["{{ basedamagefirstslash }} -> {{ basedamagefirstslashNL }}", "{{ basedamagesecondhit }} -> {{ basedamagesecondhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "NaafiriE.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NaafiriW", "name": "猟犬の追跡", "description": "ナフィーリと「群れの同胞」がチャンピオンに向かってダッシュしてダメージを与える。キルまたはアシストを獲得すると自身の周囲の敵が可視化され、このスキルを再発動可能になる。2回目の使用時はシールドを獲得する。", "tooltip": "敵チャンピオンに向かってダッシュして<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与え、少しの間<status>スロウ効果</status>を与える。<keywordMajor>「群れの同胞」</keywordMajor>は対象指定不可になり、ナフィーリと共にダッシュして、<keywordMajor>「群れの同胞」</keywordMajor>1体ごとに<physicalDamage>{{ packmatedamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />{{ takedownwindow }}秒以内にキルまたはアシストを獲得すると自身の周囲の敵が可視化され、このスキルの再発動が可能になる。2回目の使用時は{{ shieldduration }}秒間、<shield>耐久値{{ shieldtotal }}のシールド</shield>を獲得する。<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "シールド量", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldsize }} -> {{ shieldsizeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "NaafiriW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "我らは一つ", "description": "「群れの同胞」を出現させる。「群れの同胞」はナフィーリの通常攻撃およびスキルの対象を攻撃する。", "image": {"full": "Icons_Naafiri_P.png", "sprite": "passive2.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}