{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Draven": {"id": "Draven", "key": "119", "name": "Draven", "title": "mărețul torționar ", "image": {"full": "Draven.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "119000", "num": 0, "name": "default", "chromas": false}, {"id": "119001", "num": 1, "name": "Draven, tâl<PERSON>ul de suflete", "chromas": false}, {"id": "119002", "num": 2, "name": "Draven gladiator", "chromas": false}, {"id": "119003", "num": 3, "name": "Draven comentator", "chromas": true}, {"id": "119004", "num": 4, "name": "Draven la piscină", "chromas": false}, {"id": "119005", "num": 5, "name": "Draven, ucigașul de fiare", "chromas": false}, {"id": "119006", "num": 6, "name": "Draven Draven", "chromas": false}, {"id": "119012", "num": 12, "name": "<PERSON><PERSON> Draven", "chromas": false}, {"id": "119013", "num": 13, "name": "Draven al regatelor Mecha", "chromas": true}, {"id": "119020", "num": 20, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "119029", "num": 29, "name": "Draven la patru ace", "chromas": true}, {"id": "119039", "num": 39, "name": "Draven noapte de groază", "chromas": true}, {"id": "119048", "num": 48, "name": "Draven La Ilusión", "chromas": true}, {"id": "119058", "num": 58, "name": "Draven, confruntare supremă", "chromas": true}], "lore": "În Noxus există numeroși războinici numiți ''răzbunători'', ce se înfruntă în arene sângeroase, testându-și puterile, dar niciunul n-a fost vreodată la fel de iubit ca Draven. A fost soldat în tinerețe, dar a descoperit că mulțimile îi apreciază într-un mod unic tendințele teatrale și îndemânarea de neegalat în mânuirea securilor sale rotitoare. Este atât de îndrăgostit de spectacolul propriei sale perfecțiuni ostentative, încât a jurat să învingă pe oricine e nevoie ca să se asigure că numele său va fi amintit pentru totdeauna în imperiu.", "blurb": "În Noxus există numeroși războinici numiți ''răzbunători'', ce se înfruntă în arene sângeroase, testându-și puterile, dar niciunul n-a fost vreodată la fel de iubit ca Draven. A fost soldat în tinerețe, dar a descoperit că mulțimile îi apreciază într-un...", "allytips": ["<PERSON>că Draven nu se miș<PERSON><PERSON>, ''Securea rotitoare'' va cădea lângă poziția lui curentă, fie direct peste el, fie puțin mai la dreapta sau la stânga.", "Dacă Draven se mișcă după atac, ''Securea rotitoare'' va cădea în direcția în care se îndreaptă. Folosește acest lucru pentru a controla direcția ''Securii rotitoare''."], "enemytips": ["Lansează-ți loviturile de îndemânare spre poziția de aterizare a ''Securilor rotitoare'' ale lui <PERSON>.", "Perturbă-l pe Draven pentru a-l face să-și scape securile. Dacă reușești să faci asta, puterea lui va scădea drastic."], "tags": ["Marksman"], "partype": "Mană", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 8}, "stats": {"hp": 675, "hpperlevel": 104, "mp": 361, "mpperlevel": 39, "movespeed": 330, "armor": 29, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.7, "mpregen": 8.05, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.6, "attackspeedperlevel": 2.7, "attackspeed": 0.679}, "spells": [{"id": "DravenSpinning", "name": "Securea rotitoare", "description": "Următorul atac al lui Draven va provoca daune fizice bonus. Securea va ricoșa din țintă și va fi aruncată în sus. <PERSON><PERSON><PERSON> o prinde, Draven pregătește automat altă ''Secure rotitoare''. Draven poate avea două ''Securi rotitoare'' în același timp.", "tooltip": "Draven pregătește o <keywordMajor>''Secure rotitoare''</keywordMajor>, iar următorul său atac provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> bonus, după care aceasta ricoșează în aer. Da<PERSON><PERSON> o prinde, Draven pregătește altă <keywordMajor>''Secure rotitoare''</keywordMajor>.<br /><br />Draven poate avea două <keywordMajor>''Securi rotitoare''</keywordMajor> în același timp.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Bonus procentual daune din atac", "Timp de reactivare"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [100, 100, 100, 100, 100], [75, 85, 95, 105, 115], [30, 35, 40, 45, 50], [5.75, 5.75, 5.75, 5.75, 5.75], [40, 45, 50, 55, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100", "75/85/95/105/115", "30/35/40/45/50", "5.75", "40/45/50/55/60", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DravenSpinning.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenFury", "name": "Atac sângeros", "description": "Draven primește un bonus la viteza de mișcare și la viteza de atac. Bonusul la viteza de mișcare scade rapid de-a lungul duratei buff-ului. Prinderea ''Securii rotitoare'' va reseta timpul de reactivare al ''Atacului sângeros''.", "tooltip": "Draven primește efectul ''Fantomă'', <speed>{{ e2 }}% vitez<PERSON> de <PERSON></speed>, valoare ce scade de-a lungul a {{ e3 }} secunde și <attackSpeed>{{ e4 }}% vitez<PERSON> de atac</attackSpeed> timp de {{ e5 }} secunde.<br /><br />Când Draven prinde o <keywordMajor>''Secure rotitoare''</keywordMajor>, timpul de reactivare al acestei abilități se reîmprospătează.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Viteză de atac", "Cost de @AbilityResourceName@", "Viteză de mișcare"], "effect": ["{{ e4 }}% -> {{ e4NL }}%", "{{ cost }} -> {{ costNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [4, 5, 6, 7, 8], [50, 55, 60, 65, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [-0.062, -0.069, -0.075, -0.081, -0.087], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/5/6/7/8", "50/55/60/65/70", "1.5", "20/25/30/35/40", "3", "-0.062/-0.069/-0.075/-0.081/-0.087", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "DravenFury.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenDoubleShot", "name": "La o parte!", "description": "Draven î<PERSON>i aruncă securile, provocându-le daune fizice țintelor lovite și aruncându-le din calea lui. Țintele lovite sunt încetinite.", "tooltip": "Draven aruncă o secure laterală care le provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> inamicilor, îi <status>aruncă în spate</status> și îi <status>încetineș<PERSON></status> cu {{ e2 }}% timp de {{ e3 }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Timp de reactivare"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [20, 25, 30, 35, 40], [2, 2, 2, 2, 2], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/110/145/180/215", "20/25/30/35/40", "2", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "DravenDoubleShot.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenRCast", "name": "Răzbunarea călăului", "description": "Draven aruncă două securi masive și le provoacă daune fizice tuturor unităților lovite. După ce lovesc un campion inamic, securile își inversează direcția lent și apoi se întorc la Draven. Draven poate reactiva această abilitate și când securile lui se deplasează, pentru a le face să se întoarcă mai devreme. Provoacă daune mai mici pentru fiecare unitate lovită și se resetează când securile își inversează direcția. Execută inamicii care au mai puțină viață decât numărul de cumuluri de ''Adorație'' ale lui Draven.", "tooltip": "Draven aruncă două securi masive care provoacă <physicalDamage>{{ rcalculateddamage }} daune fizice</physicalDamage>. Când lovesc un campion sau dacă abilitatea e <recast>refolosită</recast>, acestea își schimbă direcția și se întorc spre Draven. Securile provoacă cu {{ rdamagereductionperhit*100 }}% mai puține daune pentru fiecare inamic lovit, până la un minimum de {{ rmindamagepercent }}%.<br /><br />Da<PERSON><PERSON> <keywordMajor>''Răzbunarea călăului''</keywordMajor> ar lăsa un campion inamic cu mai puțină viață decât {{ rpassivestackscoefficient*100 }}% din numărul actual de cumuluri de <keywordMajor>''Liga lui Draven''</keywordMajor> ale lui Draven ({{ rpassivetruedamage }}), acesta va fi executat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare", "Raport daune bonus din atac"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rcoefficient*100.000000 }}% -> {{ rcoefficientnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20000, 20000, 20000], "rangeBurn": "20000", "image": {"full": "DravenRCast.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Liga lui Draven", "description": "Draven primește ''Adorația'' fanilor săi când prinde o ''Secure rotitoare'', ucide un minion sau un monstru sau distruge un turn. Când ucide campioni in<PERSON>ci, Draven primește aur bonus în funcție de numărul cumulurilor de ''Adorație''.", "image": {"full": "Draven_passive.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}