{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiddlesticks": {"id": "Fiddlesticks", "key": "9", "name": "Fiddlesticks", "title": "l'antico terrore", "image": {"full": "Fiddlesticks.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "9000", "num": 0, "name": "default", "chromas": false}, {"id": "9001", "num": 1, "name": "Fiddlesticks Spettrale", "chromas": false}, {"id": "9002", "num": 2, "name": "Fiddlesticks Union Jack", "chromas": false}, {"id": "9003", "num": 3, "name": "Fiddlesticks Bandito", "chromas": true}, {"id": "9004", "num": 4, "name": "Fiddlesticks Testa da Zucca", "chromas": false}, {"id": "9005", "num": 5, "name": "Per la Barba di Fiddle", "chromas": false}, {"id": "9006", "num": 6, "name": "Fiddlesticks Festa a Sorpresa", "chromas": true}, {"id": "9007", "num": 7, "name": "Fiddlesticks Do<PERSON>tto o <PERSON>tto", "chromas": false}, {"id": "9008", "num": 8, "name": "Fiddlesticks Asceso", "chromas": false}, {"id": "9009", "num": 9, "name": "Fiddlesticks Pretoriano", "chromas": true}, {"id": "9027", "num": 27, "name": "Fiddlesticks <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "9037", "num": 37, "name": "Fiddlesticks Luna di Sangue", "chromas": true}], "lore": "Qualcosa si è risvegliato su Runeterra. Qualcosa di antico. Qualcosa di terribile. L'orrore senza tempo conosciuto come Fiddlesticks vaga ai margini delle terre degli uomini, attratto da zone dense di paranoia in cui cibarsi di vittime terrorizzate. Armato di una falce scheggiata, questa creatura cadente e macilenta miete la paura stessa, devastando la mente di chiunque abbia la sfortuna di sopravvivere alla sua fame. Fate attenzione al gracchiare dei corvi o al sibilo di una sagoma che sembra <i>quasi</i> umana... Fiddlesticks è tornato.", "blurb": "Qualcosa si è risvegliato su Runeterra. Qualcosa di antico. Qualcosa di terribile. L'orrore senza tempo conosciuto come Fiddlesticks vaga ai margini delle terre degli uomini, attratto da zone dense di paranoia in cui cibarsi di vittime terrorizzate...", "allytips": [], "enemytips": [], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 9, "difficulty": 9}, "stats": {"hp": 650, "hpperlevel": 106, "mp": 500, "mpperlevel": 28, "movespeed": 335, "armor": 34, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 480, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.65, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "FiddleSticksQ", "name": "<PERSON><PERSON>", "description": "Se Fiddlesticks danneggia un nemico con le abilità mentre non è visibile o bersaglia un nemico con l'attivazione di Terrore, impaurisce l'unità bersaglio, facendola scappare dalla paura per un determinato periodo di tempo.", "tooltip": "<spellPassive>Passiva:</spellPassive> quando non è visibile ed è fuori dal combattimento oppure si finge un'<keywordMajor>Effige</keywordMajor>, danneggiare un nemico con un'abilità lo <status>impaurisce</status> per {{ fearduration }} secondo/i.<br /><br /><spellActive>Attiva</spellActive>: <status>impaurisce</status> un bersaglio per {{ fearduration }} secondo/i e infligge <magicDamage>{{ totalpercenthealthdamage }} della salute attuale in danni magici</magicDamage>. Se il bersaglio è stato <status>impaurito</status> di recente da Fiddlesticks, infligge invece <magicDamage>{{ totalpercenthealthdamagefeared }} della salute attuale in danni magici</magicDamage>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "% salute attuale"], "effect": ["{{ fearduration }} -> {{ feardurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 14.5, 14, 13.5, 13], "cooldownBurn": "15/14.5/14/13.5/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "FiddleSticksQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksW", "name": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "description": "Fiddlesticks assorbe salute dai nemici vicini, infliggendo danni bonus da esecuzione alla fine della durata.", "tooltip": "Fiddlesticks canalizza e assorbe le anime dei nemici vicini, infliggendo <magicDamage>{{ draindamagecalc }} danni magici</magicDamage> al secondo per 2 secondi, più <magicDamage>{{ percentfortooltip }}% della salute mancante in danni magici</magicDamage> al termine della canalizzazione. Fiddlesticks recupera <healing>{{ vamppercentage }}% dei danni come salute</healing>.<br /><br />Se Fiddlesticks finisce di canalizzare senza interruzione, la ricarica rimanente si riduce del 60%.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo", "<PERSON><PERSON> salute mancante", "Percentuale guarita", "Ricarica"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ percentfortooltip }}% -> {{ percentfortooltipNL }}%", "{{ vamppercentage }}% -> {{ vamppercentageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "FiddleSticksW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksE", "name": "Mietitura", "description": "Fiddlesticks colpisce un'area con la sua falce, rallentando tutti i nemici colpiti e silenziando quelli che si trovano al centro dell'area.", "tooltip": "Fiddlesticks scatena magia oscura, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage> e <status>rallentando</status> i nemici del {{ slowamount*-100 }}% per {{ silenceduration }} secondi. I nemici al centro dell'area sono anche <status>silenziati</status> per la durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "FiddleSticksE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksR", "name": "Tempesta di corvi", "description": "Uno stormo di corvi vola intorno a Fiddlesticks, infliggendo danni al secondo a tutte le unità nemiche nell'area.", "tooltip": "Fiddlesticks canalizza per {{ channeltime }} secondi, poi si teletrasporta e scatena uno stormo di corvi, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> nell'arco di {{ duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo", "Ricarica"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 225, 325], [5, 5, 5], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "125/225/325", "5", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "FiddleSticksR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Un innocuo spaventapasseri", "description": "Il trinket di Fiddlesticks viene sostituito dalle Effigi dello spavent<PERSON>sseri.", "image": {"full": "FiddlesticksP.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}