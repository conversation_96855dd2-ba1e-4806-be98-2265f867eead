{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Trundle": {"id": "Trundle", "key": "48", "name": "Trundle", "title": "<PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Trundle.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "48000", "num": 0, "name": "default", "chromas": false}, {"id": "48001", "num": 1, "name": "Trundle Mały Pałkarz", "chromas": false}, {"id": "48002", "num": 2, "name": "Trundle Złomiarz", "chromas": false}, {"id": "48003", "num": 3, "name": "Tradycyjn<PERSON> Trundle", "chromas": false}, {"id": "48004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "48005", "num": 5, "name": "Destruktor <PERSON> Trundle", "chromas": false}, {"id": "48006", "num": 6, "name": "Pogromca Smoków Trundle", "chromas": true}, {"id": "48012", "num": 12, "name": "Trundle Postrach <PERSON>", "chromas": true}, {"id": "48021", "num": 21, "name": "<PERSON> Esportu Trundle", "chromas": true}], "lore": "Trundle to duży i przebiegły troll o prawdziwie podłych tendencjach, zmusi wszystko do kapitulacji — nawet sam Freljord. Zawzięcie broni swojego terytorium, więc dopadnie każdego głupca, który na nie wkroczy. Potem, z pomocą swojej maczugi z Prawdziwego Lodu, zamraża przeciwników do szpiku kości i nabija ich na ostre, lodowe kolumny, śmiejąc się, kiedy ich krew barwi śnieg.", "blurb": "Trundle to duży i przebiegły troll o prawdziwie podłych tendencjach, zmusi wszystko do kapitulacji — nawet sam Freljord. Zawzięcie broni swojego terytorium, więc dopadnie każdego głupca, który na nie wkroczy. Potem, z pomocą swojej maczugi z Prawdziwego...", "allytips": ["Trundle świetnie walczy na Zamarzniętym Terytorium. Postaraj się zwabić tu przeciwników.", "Użyj Zniewolenia do osłabienia potężnego obrońcy lub ustalenia celu, na którym może skupić się cała drużyna.", "Chaps obniża obrażenia fizyczne, zadawane przez wrogów; spróbuj wykorzystać je przeciwko przeciwnikom zadającym duże obrażenia."], "enemytips": ["Trundle jest <PERSON><PERSON>, jeś<PERSON> przebywa w jednym miejscu. Postaraj się odciągnąć go od Zamarzniętego Terytorium.", "Postaraj się jak najszybciej uciec z zasięgu Kolumny Lodu, g<PERSON><PERSON>noś<PERSON> ta znacząco cię spowalnia."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 6, "magic": 2, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 110, "mp": 340, "mpperlevel": 45, "movespeed": 350, "armor": 37, "armorperlevel": 3.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 2.9, "attackspeed": 0.67}, "spells": [{"id": "TrundleTrollSmash", "name": "Chaps", "description": "Trundle gryzie przeciwnika, z<PERSON><PERSON><PERSON><PERSON> obrażenia, spowalniając i wysysając część jego obrażeń od ataku.", "tooltip": "Ko<PERSON><PERSON>y atak Trundle'a zadaje <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> i na krótko <status>spowalnia</status> cel o {{ slowamount*100 }}%. Po chwili Trundle zyskuje <physicalDamage>{{ bonusad }} pkt. obrażeń od ataku</physicalDamage> na {{ sapdebuffduration }} sek., a trafiony wróg traci <physicalDamage>{{ sappedad*-1 }} pkt. obrażeń od ataku</physicalDamage> na ten sam czas.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Współczynnik obrażeń od ataku", "Obrażenia od ataku", "Usunięto obrażenia od ataku"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ adscaling*100.000000 }}% -> {{ adscalingnl*100.000000 }}%", "{{ bonusad }} -> {{ bonusadNL }}", "{{ sappedad*-1.000000 }} -> {{ sappedadnl*-1.000000 }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "TrundleTrollSmash.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "trundledesecrate", "name": "Zamarznięte Terytorium", "description": "Trundle zmienia wybrany obszar w swoje terytorium, zyskując na nim prędkość ataku, ruchu oraz premię do leczenia.", "tooltip": "Trundle zamraża obszar na {{ e4 }} sek. Kiedy znajduje się w środku, zyskuje <speed>{{ e1 }}% prędko<PERSON>ci ruchu</speed>, <attackSpeed>{{ e2 }}% prędkości ataku</attackSpeed> i o {{ e3 }}% zwiększone leczenie.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "Czas odnowienia"], "effect": ["{{ effect1amount*100.000000 }}% -> {{ effect1amountnl*100.000000 }}%", "{{ effect2amount*100.000000 }}% -> {{ effect2amountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [20, 28, 36, 44, 52], [30, 50, 70, 90, 110], [25, 25, 25, 25, 25], [8, 8, 8, 8, 8], [775, 775, 775, 775, 775], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/28/36/44/52", "30/50/70/90/110", "25", "8", "775", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "trundledesecrate.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TrundleCircle", "name": "<PERSON><PERSON><PERSON>", "description": "Trundle tworzy w wybranym miejscu kolumnę lodu, która stanowi barierę i spowalnia jednostki dookoła.", "tooltip": "Trundle tworzy utrzymującą się przez {{ e1 }} sek. kolumnę lodu, która nieznacz<PERSON> <status>odr<PERSON>ca</status> wrogów znajdujących się bezpośrednio na jej obszarze i <status>spowalnia</status> wrogów w pobliżu o {{ e2 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Spowolnienie"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [21, 19.5, 18, 16.5, 15], "cooldownBurn": "21/19.5/18/16.5/15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [34, 38, 42, 46, 50], [360, 360, 360, 360, 360], [225, 225, 225, 225, 225], [150, 150, 150, 150, 150], [225, 225, 225, 225, 225], [400, 400, 400, 400, 400], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "34/38/42/46/50", "360", "225", "150", "225", "400", "60", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "TrundleCircle.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TrundlePain", "name": "Zniewolenie", "description": "Trundle wykrada przeciwnikowi cześć zdrowia, pancerza i odporności na magię. W ciągu następnych 4 sekund ilość skradzionego zdrowia, pancerza i odporności na magię jest podwajana.", "tooltip": "Trundle wysysa energię życiową z wrogiego bohatera, zadając mu <magicDamage>obrażenia magiczne równe {{ totalpercenthpdamage }} jego maks. zdrowia</magicDamage> i kradnąc <scaleArmor>{{ armormrshred*100 }}% jego pancerza</scaleArmor> oraz <scaleMR>odporności na magię</scaleMR> na {{ actualdurationofdrainbuff }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Wysysane zdrowie", "Czas odnowienia"], "effect": ["{{ percenthpdamage*100.000000 }}% -> {{ percenthpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "TrundlePain.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Królewski Trybut", "description": "<PERSON>edy koło Trundle'a ginie wroga jednostka, bohater leczy się o pewien procent jej maksymalnego zdrowia.", "image": {"full": "Trundle_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}