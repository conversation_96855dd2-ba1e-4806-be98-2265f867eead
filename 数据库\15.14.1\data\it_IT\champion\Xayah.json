{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Xayah": {"id": "<PERSON><PERSON><PERSON>", "key": "498", "name": "<PERSON><PERSON><PERSON>", "title": "la ribelle", "image": {"full": "Xayah.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "498000", "num": 0, "name": "default", "chromas": false}, {"id": "498001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "498002", "num": 2, "name": "<PERSON><PERSON><PERSON>uore", "chromas": false}, {"id": "498003", "num": 3, "name": "Xayah SSG", "chromas": false}, {"id": "498004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498008", "num": 8, "name": "<PERSON>ayah del Bosco Antico", "chromas": true}, {"id": "498017", "num": 17, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498026", "num": 26, "name": "<PERSON><PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "498028", "num": 28, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498037", "num": 37, "name": "<PERSON><PERSON><PERSON> violato", "chromas": true}, {"id": "498038", "num": 38, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "498047", "num": 47, "name": "<PERSON><PERSON><PERSON> Battaglia", "chromas": true}, {"id": "498057", "num": 57, "name": "Xayah dell'Accademia di Battaglia", "chromas": true}], "lore": "Letale e precisa, <PERSON><PERSON><PERSON> è una rivoluzionaria vastayana che lotta per salvare il suo popolo. Con l'astuzia, la velocità e le sue affilatissime piume, stronca chiunque cerchi di ostacolarla. <PERSON><PERSON><PERSON> lotta al fianco del suo compagno e amante, <PERSON><PERSON>, per proteggere la sua tribù in declino e riportare la sua razza alla sua idea di antica gloria.", "blurb": "Letale e precisa, <PERSON><PERSON><PERSON> è una rivoluzionaria vastayana che lotta per salvare il suo popolo. Con l'astuzia, la velocità e le sue affilatissime piume, stronca chiunque cerchi di ostacolarla. <PERSON><PERSON>h lotta al fianco del suo compagno e amante, <PERSON><PERSON>, per...", "allytips": ["Gli attacchi e le abilità di Xayah lasciano a terra delle piume che può successivamente richiamare per infliggere ingenti danni ed effetti di controllo ad area.", "<PERSON><PERSON><PERSON> può utilizzare Tumulto di piume per schivare quasi tutte le abilità creando al contempo tantissime piume. Cerca di usare gli aspetti offensivi e difensivi di quest'abilità."], "enemytips": ["Incantalame di Xayah immobilizza solo i bersagli colpiti da 3 o più piume di ritorno.", "I combattimenti lunghi nella stessa area di Xayah le permettono di lasciare un sacco di piume. Cerca di spostarti spesso!", "Accertati di essere pronto, quando vuoi ucciderla. La non bersagliabilità di Tumulto di piume può rovesciare le sorti di un'imboscata in favore di Xayah."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 6, "magic": 1, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 340, "mpperlevel": 40, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.25, "hpregenperlevel": 0.75, "mpregen": 8.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.9, "attackspeed": 0.658}, "spells": [{"id": "XayahQ", "name": "<PERSON><PERSON><PERSON> daga", "description": "<PERSON><PERSON>h lancia due daghe che infliggono danni e lasciano piume che può richiamare.", "tooltip": "<PERSON><PERSON><PERSON> lancia due pugnali che infliggono <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e lasciano due <keywordMajor>piume</keywordMajor>. I bersagli colpiti dopo il primo subiscono <physicalDamage>{{ multihitdamage }} danni</physicalDamage> da ogni pugnale.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [45, 60, 75, 90, 105], [0.5, 0.5, 0.5, 0.5, 0.5], [0.334, 0.334, 0.334, 0.334, 0.334], [0.584, 0.584, 0.584, 0.584, 0.584], [3500, 3500, 3500, 3500, 3500], [3500, 3500, 3500, 3500, 3500], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/60/75/90/105", "0.5", "0.33", "0.58", "3500", "3500", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "XayahQ.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XayahW", "name": "Piumaggio letale", "description": "<PERSON><PERSON><PERSON> crea una tempesta di lame che aumenta la sua velocità d'attacco base e i danni, oltre a conferirle velocità di movimento se attacca un campione.", "tooltip": "Xayah crea una tempesta di lame per {{ e2 }} secondi, che le conferisce <attackSpeed>{{ e1 }}% velocità d'attacco</attackSpeed> e permette ai suoi attacchi di lanciare una seconda lama che infligge {{ bonusdamagepercent }}% danni.<br /><br />Quando questa lama colpisce un campione, <PERSON><PERSON><PERSON> ottiene <speed>{{ e3 }}% velocità di movimento</speed> per {{ e4 }} secondi.<br /><br />Se Rakan è vicino, anche lui riceve gli effetti di quest'abilità, ma ottiene <speed>velocità di movimento</speed> quando <i>Xayah</i> colpisce un bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [4, 4, 4, 4, 4], [30, 30, 30, 30, 30], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 20, 20, 20, 20], [1000, 1000, 1000, 1000, 1000], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "4", "30", "1.5", "20", "1000", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XayahW.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XayahE", "name": "Incantalame", "description": "<PERSON><PERSON><PERSON> richiama a sé le piume che ha lasciato, infliggendo danni e immobilizzando i nemici.", "tooltip": "<PERSON><PERSON><PERSON> richiama tutte le sue <keywordMajor>piume</keywordMajor>, infliggendo <physicalDamage>{{ featherdamage }} danni fisici</physicalDamage> per ciascuna di esse. Se {{ featherthreshold }} o più <keywordMajor>piume</keywordMajor> colpiscono un nemico, lo <status>immobil<PERSON>zano</status> per {{ rootduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "XayahE.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XayahR", "name": "Tumulto di piume", "description": "<PERSON><PERSON><PERSON> salta e diventa non bersagliabile, per poi lanciare una raffica di daghe le quali lasciano piume che può richiamare.", "tooltip": "<PERSON><PERSON><PERSON> balza in aria e diventa non bersagliabile e spettrale per 1,5 secondi, prima di far piovere daghe che infliggono <physicalDamage>{{ damage }} danni fisici</physicalDamage> e lasciano una scia di <keywordMajor>piume</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "XayahR.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> netti", "description": "Dopo avere usato un'abilità, i prossimi attacchi base di Xayah colpiranno tutti i bersagli sul loro percorso, lasciando una <font color='#C200E1'>piuma</font>.", "image": {"full": "XayahPassive.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}