{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ahri": {"id": "<PERSON><PERSON>", "key": "103", "name": "<PERSON><PERSON>", "title": "die neunschwänzige Füchsin", "image": {"full": "Ahri.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "103000", "num": 0, "name": "default", "chromas": false}, {"id": "103001", "num": 1, "name": "Dynastie-<PERSON><PERSON>", "chromas": true}, {"id": "103002", "num": 2, "name": "Mitternacht-<PERSON><PERSON>", "chromas": true}, {"id": "103003", "num": 3, "name": "Fuchsfeuer-<PERSON><PERSON>", "chromas": true}, {"id": "103004", "num": 4, "name": "Popstar-<PERSON><PERSON>", "chromas": true}, {"id": "103005", "num": 5, "name": "Herausforderer-<PERSON><PERSON>", "chromas": true}, {"id": "103006", "num": 6, "name": "Akademie-Ahri", "chromas": true}, {"id": "103007", "num": 7, "name": "Arcade-<PERSON><PERSON>", "chromas": true}, {"id": "103014", "num": 14, "name": "Sternenwächterin Ahri", "chromas": true}, {"id": "103015", "num": 15, "name": "K/DA-<PERSON><PERSON>", "chromas": true}, {"id": "103016", "num": 16, "name": "K/DA-<PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "103017", "num": 17, "name": "Ahnenholz<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "103027", "num": 27, "name": "<PERSON><PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "103028", "num": 28, "name": "K/DA ALL OUT-Ahri", "chromas": true}, {"id": "103042", "num": 42, "name": "Hexenzirkel-<PERSON><PERSON>", "chromas": true}, {"id": "103065", "num": 65, "name": "K/DA-<PERSON><PERSON> (Prestige 2022)", "chromas": false}, {"id": "103066", "num": 66, "name": "Arkana<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "103076", "num": 76, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "103085", "num": 85, "name": "Emporgestiegene Legende Ahri", "chromas": false}, {"id": "103086", "num": 86, "name": "Verewigte Legende Ahri", "chromas": false}], "lore": "Ahri ist eine von Natur aus mit der Magie des Geisterreichs verbundene, fuchsähnliche Vastaya, die die Emotionen ihrer Beute manipulieren und deren Essenz verzehren kann. Eine verschlungene Seele lässt Erinnerungen des Opfers in ihrem Geiste aufflackern und gewährt ihr Einsicht. Einst war Ahri ein mächtiges, aber unberech<PERSON><PERSON><PERSON>, nun bereist sie die Welt auf der Suche nach Relikten ihrer Vorfahren, während sie ihre gestohlenen Erinnerungen durch eigene ersetzen möchte.", "blurb": "<PERSON><PERSON> ist eine von Natur aus mit der Magie des Geisterreichs verbundene, fuchsähnliche Vastaya, die die Emotionen ihrer Beute manipulieren und deren Essenz verzehren kann. Eine verschlungene Seele lässt Erinnerungen des Opfers in ihrem Geiste aufflackern...", "allytips": ["<PERSON><PERSON><PERSON> „Bezaubern“, um Kombinationen vorzubereiten. Dadurch wird es viel leichter, mit „Kugel der Täuschung“ oder „Fuchsfeuer“ zu treffen.", "Eröffne Teamkämpfe mit „Bezaubern“ und verfolge Flüchtende mit „Geisterhast“.", "„Geisterhast“ erlaubt es Ahri, ihre Fähigkeiten einzusetzen, indem es den Weg für „Bezaubern“ frei macht, <PERSON><PERSON><PERSON><PERSON> mit „Kugel der Täuschung“ vereinfacht und Ahri nahe genug an einen Gegner für „Fuchsfeuer“ bringt."], "enemytips": ["<PERSON><PERSON>ögen sinkt gewaltig, wenn ihre ultimative Fähigkeit, „Geisterhast“ nicht verfügbar ist.", "<PERSON><PERSON><PERSON> hinter <PERSON>, damit sie nicht zu leicht mit „Bezaubern“ treffen kann, was ihr Schadenspotential deutlich verringert."], "tags": ["Mage", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 418, "mpperlevel": 25, "movespeed": 330, "armor": 21, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.2, "attackspeed": 0.668}, "spells": [{"id": "<PERSON><PERSON><PERSON>", "name": "Kugel der Täuschung", "description": "<PERSON><PERSON> sendet ihre Kugel aus und holt sie wieder zurück, wobei sie auf dem Hinweg magischen Schaden und auf dem Rückweg absoluten Schaden verursacht. ", "tooltip": "<PERSON><PERSON> sendet ihre Kugel aus und holt sie wieder zurück, wobei sie auf dem Hinweg <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und auf dem Rückweg <trueDamage>{{ totaldamage }}&nbsp;absoluten Schaden</trueDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [55, 65, 75, 85, 95], "costBurn": "55/65/75/85/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [970, 970, 970, 970, 970], "rangeBurn": "970", "image": {"full": "AhriQ.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AhriW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ri erhält einen kurzen Lauftemposchub und entfesselt drei <PERSON>uer, die nahe Gegner suchen und angreifen.", "tooltip": "<PERSON><PERSON> entfesselt 3&nbsp;<PERSON><PERSON><PERSON><PERSON>, die nahe Gegner suchen und <magicDamage>{{ singlefiredamage }}&nbsp;magischen Schaden</magicDamage> verursachen. Nach dem ersten Fuchsfeuer verursacht jedes weitere <magicDamage>{{ multifiredamage }}&nbsp;Schaden</magicDamage>. Zusätzlich erhält sie <speed>{{ movementspeed*100 }}&nbsp;% Lauftempo</speed>, das im Verlauf von {{ movementspeedduration }}&nbsp;Sekunden abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "AhriW.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AhriE", "name": "Bezaubern", "description": "<PERSON><PERSON> wirft einen <PERSON>, der einem getroffenen Gegner Schaden zufügt, ihn bezaubert und jegliche Bewegungsfähigkeiten abbricht. Bezauberte Gegner torkeln wehrlos auf Ahri zu.", "tooltip": "<PERSON><PERSON> wirft einen <PERSON>, der den ersten getroffenen Gegner {{ charmduration }}&nbsp;Sekunden lang <status>bezaubert</status> und ihm <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>haden</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ charmduration }} -> {{ charmdurationNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "AhriE.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AhriR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ahri springt nach vorn und feuert Essenzgeschosse ab, die nahen Gegnern Schaden zufügen. „Geisterhast“ kann bis zu 3-mal aktiviert werden, bevor sie abklingt. Ahri erhält zusätzliche Reaktivierungen, wenn sie an einem Championkill beteiligt ist.", "tooltip": "<PERSON><PERSON> springt flink in Zielrichtung und feuert {{ rmaxtargetspercast }}&nbsp;Essenzgeschosse auf nahe Gegner ab. Champions werden dabei priorisiert. Diese Geschosse verursachen jeweils <magicDamage>{{ rcalculateddamage }}&nbsp;magischen Schaden</magicDamage>. <spellName>Geisterhast</spellName> kann bis zu 2-mal innerhalb von {{ rrecastwindow }}&nbsp;Sekunden <recast>reaktiviert</recast> werden.<br /><br />Wird während dieser Zeit die Essenz eines Champions mit <spellName>Essenzdieb</spellName> verbraucht, wird das Zeitfenster für Reaktivierungen um {{ pdurationextension }}&nbsp;Sekunden verlängert und eine zusätzliche Aktivierung von <spellName>Geisterhast</spellName> gewährt (maximal {{ rmaxcasts }} werden gespeichert).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "AhriR.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Nachdem Ahri 9&nbsp;Vasallen oder Monster getötet hat, heilt sie sich.<br>Nachdem Ahri am Kill eines gegnerischen Champions beteiligt war, heilt sie sich um einen höheren Betrag.", "image": {"full": "Ahri_SoulEater2.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}