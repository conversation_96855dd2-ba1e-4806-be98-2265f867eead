{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zac": {"id": "<PERSON><PERSON>", "key": "154", "name": "ザック", "title": "親愛なる秘密兵器", "image": {"full": "Zac.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "154000", "num": 0, "name": "default", "chromas": true}, {"id": "154001", "num": 1, "name": "特殊兵器ザック", "chromas": false}, {"id": "154002", "num": 2, "name": "プールパーティ ザック", "chromas": false}, {"id": "154006", "num": 6, "name": "SKT T1 ザック", "chromas": false}, {"id": "154007", "num": 7, "name": "バトルキャスト ザック", "chromas": true}, {"id": "154014", "num": 14, "name": "荘厳の天球ザック", "chromas": true}, {"id": "154024", "num": 24, "name": "ザクザクザック", "chromas": true}], "lore": "ケミテック層を流れ、ゾウンの汚水地区の奥深く、ぽつんと隔離された大空洞に溜まった有毒液の池。その澱みの中で、ザックは覚醒した。そんな哀れな出自でありながらも、ザックは原始的な粘体生物から思考する存在へと成長を遂げ、都市のパイプの中に潜んで暮らし、時には助けが必要な者を助けるために飛び出したり、ゾウンの破損したインフラを修理するのである。", "blurb": "ケミテック層を流れ、ゾウンの汚水地区の奥深く、ぽつんと隔離された大空洞に溜まった有毒液の池。その澱みの中で、ザックは覚醒した。そんな哀れな出自でありながらも、ザックは原始的な粘体生物から思考する存在へと成長を遂げ、都市のパイプの中に潜んで暮らし、時には助けが必要な者を助けるために飛び出したり、ゾウンの破損したインフラを修理するのである。", "allytips": ["ザックが生き残る上でスキルで飛び散るスライムの回収は極めて重要だ。", "「はぐれスライム」が使える場合は、たとえやられるとしても飛び散ったスライムが殺されにくい場所を選んで戦おう。", "戦場の霧の中で「ブッ飛びスライム」をチャージすれば、敵に十分な反応時間を与えることなく攻撃できる。"], "enemytips": ["飛び散ったスライムを回収されると、ザックは体力を回復してしまう。地面に落ちているスライムは片っ端から踏み潰してしまおう。", "「はぐれスライム」発動後、散らばるスライムをすべて排除すると、ザックの復活を阻止できる。", "ザックが「ブッ飛びスライム」のチャージを開始したら、サイレンス、スタン、ノックアップなどの効果を与えることで妨害できる。"], "tags": ["Tank", "Fighter"], "partype": "なし", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 8}, "stats": {"hp": 685, "hpperlevel": 109, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.6, "attackspeed": 0.736}, "spells": [{"id": "ZacQ", "name": "スライムパンチ", "description": "片手を伸ばして敵ユニットを掴む。別の敵ユニットを攻撃すると両者を互いにぶつけ合わせる。", "tooltip": "腕を伸ばして最初に当たった敵に貼り付け、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて、少しの間だけ<status>スロウ効果</status>を付与する。次に行う通常攻撃は射程が増加し、同様のダメージと<status>スロウ効果</status>を与える。 <br /><br />この通常攻撃で最初とは<i></i>異なる敵を攻撃すると、両者をお互いの方向に<status>ノックアップ</status>する。両者がぶつかった場合、両者およびその周囲の敵は<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を受け、少しの間だけ<status>スロウ効果</status>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [8, 8, 8, 8, 8], [800, 800, 800, 800, 800], [-0.4, -0.4, -0.4, -0.4, -0.4], [0.5, 0.5, 0.5, 0.5, 0.5], [2.5, 2.5, 2.5, 2.5, 2.5], [900, 900, 900, 900, 900], [700, 700, 700, 700, 700], [300, 300, 300, 300, 300], [300, 300, 300, 300, 300]], "effectBurn": [null, "40/55/70/85/100", "8", "800", "-0.4", "0.5", "2.5", "900", "700", "300", "300"], "vars": [], "costType": "%({{ healthcosttooltip }})を消費", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZacQ.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "発動時に現在体力の{{ e2 }}%({{ healthcosttooltip }})を消費"}, {"id": "ZacW", "name": "スライムクラッシュ", "description": "体を爆発させることで近くの敵に向かって突進し、最大体力の一定割合を魔法ダメージとして与える。", "tooltip": "体の一部を破裂させ、周囲の敵に<magicDamage>{{ basedamage }}(+最大体力の{{ displaypercentdamage }})の魔法ダメージ</magicDamage>を与える。<br /><br /><keywordMajor>スライム</keywordMajor>を1体拾うごとに、このスキルのクールダウンが1秒短縮する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "最大体力ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealthdamage*100.000000 }}% -> {{ basemaxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 50, 60, 70, 80], [4, 4, 4, 4, 4], [0.04, 0.05, 0.06, 0.07, 0.08], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/50/60/70/80", "4", "0.04/0.05/0.06/0.07/0.08", "1", "200", "0", "0", "0", "0", "0"], "vars": [], "costType": "%({{ tooltiphealthcost }})を消費", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "ZacW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "発動時に現在体力の{{ e2 }}%({{ tooltiphealthcost }})を消費"}, {"id": "ZacE", "name": "ブッ飛びスライム", "description": "ザックが両手をついて反発力を溜め込み、対象に勢いよく飛びかかる。", "tooltip": "<charge>チャージ開始:</charge> {{ e4 }}秒間、体を伸ばして跳躍の準備をする。<br /><br /><release>解放:</release> 跳躍して落下地点にいた敵を最大{{ maxstun }}秒間(チャージ時間に応じて)<status>ノックアップ</status>させ、<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与える。当たった敵チャンピオンの数に応じて、発生する<keywordMajor>スライム</keywordMajor>の数が増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "射程", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [4, 4, 4, 4, 4], [1200, 1350, 1500, 1650, 1800], [0.9, 1, 1.1, 1.2, 1.3], [0.5, 0.5, 0.5, 0.5, 0.5], [500, 500, 500, 500, 500], [1350, 1350, 1350, 1350, 1350], [0.6, 0.6, 0.6, 0.6, 0.6], [265, 265, 265, 265, 265], [1, 1, 1, 1, 1]], "effectBurn": [null, "60/105/150/195/240", "4", "1200/1350/1500/1650/1800", "0.9/1/1.1/1.2/1.3", "0.5", "500", "1350", "0.6", "265", "1"], "vars": [], "costType": "%({{ healthcosttooltip }})を消費", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "発動時に現在体力の{{ e2 }}%({{ healthcosttooltip }})を消費"}, {"id": "ZacR", "name": "レッツバウンス！", "description": "4回バウンドし、命中した敵をノックアップさせてスロウ状態にする。", "tooltip": "{{ bounces }}回バウンドする。最初のバウンドでは当たった敵を<status>ノックバック</status>させ、<magicDamage>{{ damageperbounce }}の魔法ダメージ</magicDamage>を与える。その後のバウンドでは<magicDamage>{{ damagepersubsequentbounce }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />バウンド中は<speed>移動速度が{{ endingms*100 }}%</speed>まで徐々に増加し、<spellName>「スライムクラッシュ」</spellName>が使用可能になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本バウンドダメージ", "クールダウン"], "effect": ["{{ basedamagebounce }} -> {{ basedamagebounceNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [1.1, 1.1, 1.1], [700, 850, 1000], [-0.3, -0.4, -0.5], [2.5, 2.5, 2.5], [275, 275, 275], [300, 300, 300], [1, 1, 1], [0.25, 0.25, 0.25], [0.1, 0.1, 0.1]], "effectBurn": [null, "150/250/350", "1.1", "700/850/1000", "-0.3/-0.4/-0.5", "2.5", "275", "300", "1", "0.25", "0.1"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "はぐれスライム", "description": "スキルによる攻撃を命中させるたびスライムが飛び散り、回収すると体力が回復する。致命的なダメージを受けた場合、ザックは4体のスライムに分裂する。その後数秒かけて再融合し、この時に生き残っていたスライムの体力に応じた体力で復活する。各スライムはザックの最大体力、物理防御、魔法防御の数パーセントに相当するステータスを持つ。ザックは一度分裂すると、ふたたび分裂するために5分間のクールダウンが必要になる。", "image": {"full": "ZacPassive.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}