{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiddlesticks": {"id": "Fiddlesticks", "key": "9", "name": "フィドルスティックス", "title": "古の恐怖", "image": {"full": "Fiddlesticks.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "9000", "num": 0, "name": "default", "chromas": false}, {"id": "9001", "num": 1, "name": "幽霊のフィドルスティックス", "chromas": false}, {"id": "9002", "num": 2, "name": "ダークブルー フィドルスティックス", "chromas": false}, {"id": "9003", "num": 3, "name": "荒野の盗賊フィドルスティックス", "chromas": true}, {"id": "9004", "num": 4, "name": "カボチャヘッド フィドルスティックス", "chromas": false}, {"id": "9005", "num": 5, "name": "黒ひげフィドル", "chromas": false}, {"id": "9006", "num": 6, "name": "楽しいパーティ フィドルスティックス", "chromas": true}, {"id": "9007", "num": 7, "name": "闇の飴細工フィドルスティックス", "chromas": false}, {"id": "9008", "num": 8, "name": "砂漠の夜明けフィドルスティックス", "chromas": false}, {"id": "9009", "num": 9, "name": "プレトリアン フィドルスティックス", "chromas": true}, {"id": "9027", "num": 27, "name": "スターネメシス フィドルスティックス", "chromas": true}, {"id": "9037", "num": 37, "name": "ブラッドムーン フィドルスティックス", "chromas": true}], "lore": "ルーンテラの中で何かが目覚めた──古代の恐ろしい何かが。フィドルスティックスとして知られる永遠の恐怖は文明の僻地をうろつき、疑心暗鬼にさいなまれた地域に引き寄せられると、恐怖に怯える犠牲者たちを貪る。ギザギザの刃を持つ鎌をその手に、か細いガラクタを寄せ集めてできたクリーチャーは恐怖そのものを刈り取る。そして、生き延びることの方が不運だと思わせるほどに、精神を粉々に砕く。カラスの鳴き声や、人間のような姿をした何かの囁きが聞こえたら注意しなければいけない…フィドルスティックスが戻ってきたのだから。", "blurb": "ルーンテラの中で何かが目覚めた──古代の恐ろしい何かが。フィドルスティックスとして知られる永遠の恐怖は文明の僻地をうろつき、疑心暗鬼にさいなまれた地域に引き寄せられると、恐怖に怯える犠牲者たちを貪る。ギザギザの刃を持つ鎌をその手に、か細いガラクタを寄せ集めてできたクリーチャーは恐怖そのものを刈り取る。そして、生き延びることの方が不運だと思わせるほどに、精神を粉々に砕く。カラスの鳴き声や、人間のような姿をした何かの囁きが聞こえたら注意しなければいけない…フィドルスティックスが戻ってきたのだから。", "allytips": [], "enemytips": [], "tags": ["Mage", "Support"], "partype": "マナ", "info": {"attack": 2, "defense": 3, "magic": 9, "difficulty": 9}, "stats": {"hp": 650, "hpperlevel": 106, "mp": 500, "mpperlevel": 28, "movespeed": 335, "armor": 34, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 480, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.65, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "FiddleSticksQ", "name": "テラー", "description": "敵に見られていない状態からスキルで敵にダメージを与えるか、「テラー」の発動効果の対象に敵を指定すると、対象にフィアー効果を与えて一定時間逃走させる。", "tooltip": "<spellPassive>自動効果:</spellPassive> 非戦闘時で敵に見られていない状態または<keywordMajor>「身代わり人形」</keywordMajor>のふりをしているときにスキルで敵にダメージを与えると、{{ fearduration }}秒間<status>フィアー効果</status>を与える。<br /><br /><spellActive>発動効果:</spellActive> 敵に{{ fearduration }}秒間<status>フィアー効果</status>を与えて、<magicDamage>現在体力の{{ totalpercenthealthdamage }}の魔法ダメージ</magicDamage>を与える。対象が直前にフィドルスティックスから<status>フィアー効果</status>を受けていた場合は、代わりに<magicDamage>現在体力の{{ totalpercenthealthdamagefeared }}の魔法ダメージ</magicDamage>を与える。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["効果時間", "クールダウン", "現在体力%"], "effect": ["{{ fearduration }} -> {{ feardurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 14.5, 14, 13.5, 13], "cooldownBurn": "15/14.5/14/13.5/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "FiddleSticksQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FiddleSticksW", "name": "豊かな収穫", "description": "周囲の敵から体力を奪い、効果時間終了時に対象の減少体力に応じた追加ダメージを与える。", "tooltip": "詠唱して周囲の敵の魂を吸収し、2秒間、毎秒<magicDamage>{{ draindamagecalc }}の魔法ダメージ</magicDamage>を与え、最後の瞬間には<magicDamage>減少体力の{{ percentfortooltip }}%の魔法ダメージ</magicDamage>を追加で与える。自身は<healing>ダメージの{{ vamppercentage }}%にあたる体力</healing>を回復する。<br /><br />詠唱を中断されずに終えると、残りクールダウンが60%短縮される。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ", "減少体力ダメージ", "割合回復", "クールダウン"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ percentfortooltip }}% -> {{ percentfortooltipNL }}%", "{{ vamppercentage }}% -> {{ vamppercentageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "FiddleSticksW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FiddleSticksE", "name": "刈り取り", "description": "一定範囲を鎌で斬りつけて、命中したすべての敵にスロウ効果を与える。また、範囲の中心にいた敵にはサイレンス効果を与える。", "tooltip": "闇の魔法を解き放ち、<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与え、{{ silenceduration }}秒間{{ slowamount*-100 }}%の<status>スロウ効果</status>を付与する。範囲の中心にいる敵には同じ時間<status>サイレンス効果</status>も与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "FiddleSticksE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FiddleSticksR", "name": "クロウストーム", "description": "自身の周囲に凶暴なカラスの群れを集め、効果範囲内の敵ユニット全員に毎秒ダメージを与える。", "tooltip": "{{ channeltime }}秒間の詠唱後にテレポートして凶暴なカラスの群れを解き放ち、{{ duration }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ", "クールダウン"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 225, 325], [5, 5, 5], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "125/225/325", "5", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "FiddleSticksR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "無害なカカシ", "description": "トリンケットが「身代わり人形」に置き換わる。", "image": {"full": "FiddlesticksP.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}