{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Skarner": {"id": "<PERSON><PERSON><PERSON>", "key": "72", "name": "<PERSON><PERSON><PERSON>", "title": "il Sovrano primordiale", "image": {"full": "Skarner.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "72000", "num": 0, "name": "default", "chromas": false}, {"id": "72001", "num": 1, "name": "Skarner Flagello delle Sabbie", "chromas": false}, {"id": "72002", "num": 2, "name": "Skarner Terra Runica", "chromas": false}, {"id": "72003", "num": 3, "name": "Skarner <PERSON><PERSON> da Guerra Alpha", "chromas": false}, {"id": "72004", "num": 4, "name": "Skarner Guardiano delle Sabbie", "chromas": false}, {"id": "72005", "num": 5, "name": "Skarner Aculeo Cosmico", "chromas": true}], "lore": "L'antico Brackern colossale Skarner è venerato a Ixtal come uno dei membri fondatori della casta regnante, gli <PERSON>, e ha il compito di tenere la regione al sicuro dal resto del mondo, preservandone l'isolamento. Skarner dimora in una sala nelle profondità di Ixaocan, da dove riesce a percepire le vibrazioni della terra e a rilevare eventuali minacce. Mentre sempre più membri degli Yun Tal iniziano a mettere in discussione l'isolamento autoimposto di Ixtal, Skarner diventa sempre più paranoico e farà di tutto per tenere Ixtal e il suo popolo al sicuro... a qualsiasi costo.", "blurb": "L'antico Brackern colossale Skarner è venerato a Ixtal come uno dei membri fondatori della casta regnante, gli <PERSON>, e ha il compito di tenere la regione al sicuro dal resto del mondo, preservandone l'isolamento. Skarner dimora in una sala nelle...", "allytips": ["Gli attacchi base applicano Tremore. Resta vicino al bersaglio e applicalo ancora per ottimizzare i danni.", "Conquistare le Guglie di cristallo prima di cercare di prendere gli obiettivi neutrali o di ingaggiare in combattimenti a squadre aumenta sensibilmente le prestazioni di Skarner negli scontri.", "Impalamento è estremamente potente quando lo usi per posizionare un nemico in modo che gli alleati lo possano attaccare."], "enemytips": ["Carica di Ixtal può essere fermata da qualsiasi stordimento, immobilizzazione o lancio in aria. Conserva una ricarica cruciale in caso di imboscata!", "Impalamento può essere schivato. Usa Flash o altre abilità per spostarti fuori portata.", "<PERSON>a lunga, i danni continui di Skarner possono essere fatali. Non puoi permetterti di ignorarlo."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 8, "magic": 5, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 320, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.2, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SkarnerQ", "name": "Terra frantumata/Sisma", "description": "Skarner strappa da terra un masso che potenzia i suoi attacchi e può essere lanciato come un potente proiettile.", "tooltip": "Skarner strappa dal terreno un masso, potenziando i suoi prossimi 3 attacchi con <attackSpeed>{{ attackspeed*100 }}% velocità d'attacco</attackSpeed> e infliggendo <physicalDamage>{{ abilitydamage }} danni fisici</physicalDamage> ai nemici circostanti. Il suo ultimo attacco infligge un <physicalDamage>{{ maxhppercent*100 }}% della salute massima del bersaglio in danni fisici</physicalDamage> aggiuntivi e <status>rallenta</status> i nemici colpiti di un {{ slowpercent*100 }}% per {{ slowduration }} secondo/i.<br /><br /><recast>Rilancio:</recast> Skarner interrompe l'abilità e lancia il masso, infliggendo <physicalDamage>{{ spell.skarnerq:abilitydamage }} + un {{ spell.skarnerq:maxhppercent*100 }}% della salute massima del bersaglio in danni fisici</physicalDamage>, <status>rallentando</status> inoltre il primo nemico colpito (e tutti i nemici circostanti) di un {{ spell.skarnerq:slowpercent*100 }}% per {{ spell.skarnerq:slowduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità d'attacco", "Limite most<PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ monsterdamagecap }} -> {{ monsterdamagecapNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.5, 10, 8.5, 7], "cooldownBurn": "13/11.5/10/8.5/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SkarnerQ.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SkarnerW", "name": "Bastione sismico", "description": "<PERSON><PERSON>ner ottiene uno scudo e crea un sisma, la cui onda d'urto danneggia e rallenta i nemici.", "tooltip": "Skarner ottiene <shield>uno scudo da {{ initialshield }}</shield> per {{ shieldduration }} secondo/i e scatena un sisma che infligge <magicDamage>{{ damage }} danni magici</magicDamage> e <status>rallenta</status> i nemici nelle vicinanze di un {{ sloweffect*-100 }}% per {{ slowduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SkarnerW.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SkarnerE", "name": "Carica di Ixtal", "description": "Skarner carica in avanti e passa attraverso il terreno. Se entra in collisione con un campione o un mostro grande, li fa schiantare contro il prossimo muro colpito, infliggendogli danni e stordendoli.", "tooltip": "Skarner carica in avanti, girando nella direzione scelta e ignorando il terreno. Se travolge un campione o un mostro grande, Skarner lo trascina per il resto della carica.<br /><br />Entrando in collisione con un muro mentre trascina un nemico gli infligge <physicalDamage>{{ pindamage }} danni fisici</physicalDamage> e lo <status>stordisce</status> per {{ stunduration }} secondo/i.<br /><br />Skarner può <recast>rilanciare</recast> questa abilità per interrompere la carica in anticipo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ pinbasedamage }} -> {{ pinbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1700, 1700, 1700, 1700, 1700], "rangeBurn": "1700", "image": {"full": "SkarnerE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SkarnerR", "name": "Impalamento", "description": "Skarner attacca in avanti con le sue code e sopprime i campioni nemici. Una volta soppresse, le vittime vengono trascinate da Skarner quando si sposta.", "tooltip": "Skarner attacca in avanti con le sue code, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage> e <status>Sopprimendo</status> i primi 3 campioni colpiti per {{ duration }} secondo/i. I bersagli colpiti vengono trascinati da Skarner per tutta la durata dell'abilità.<br /><br />Se Skarner colpisce almeno un campione ottiene <speed>{{ speedboostamount*100 }}% velocità di movimento</speed> per {{ speedboostduration }} secondi.<br /><br />Se <spellName>Terra frantumata</spellName> è attiva, Skarner lancia prima <spellName>Sisma</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "SkarnerR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Trama delle vibrazioni", "description": "<PERSON><PERSON> at<PERSON>karner, Terra frantumata, Sisma e Impalamento applicano Tremore. Al massimo delle cariche di Tremore, i nemici subiscono danni magici in base alla loro salute massima nell'arco della sua durata.", "image": {"full": "Skarner_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}