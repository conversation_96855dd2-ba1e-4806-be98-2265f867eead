{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Olaf": {"id": "<PERSON>", "key": "2", "name": "<PERSON>", "title": "Nájezdník", "image": {"full": "Olaf.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "2000", "num": 0, "name": "default", "chromas": false}, {"id": "2001", "num": 1, "name": "Zavržený Olaf", "chromas": false}, {"id": "2002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "2003", "num": 3, "name": "Brolaf", "chromas": true}, {"id": "2004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "2005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "2006", "num": 6, "name": "Řezník Olaf", "chromas": false}, {"id": "2015", "num": 15, "name": "SKT T1 Olaf", "chromas": false}, {"id": "2016", "num": 16, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "2025", "num": 25, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Olaf", "chromas": true}, {"id": "2035", "num": 35, "name": "<PERSON>takill III: Lost Chapter", "chromas": true}, {"id": "2044", "num": 44, "name": "Pekel<PERSON><PERSON> Olaf", "chromas": true}], "lore": "<PERSON>, <PERSON>o ne<PERSON>ý živel zk<PERSON><PERSON> se<PERSON>, to<PERSON><PERSON><PERSON> jen po jedin<PERSON> – zem<PERSON><PERSON>t ve velkolepém boji. Pochází z nemilosrdného freljordského poloostrovu Lokfar. Kdysi se dozvěděl o proroctví, podle kterého měl pokojně skonat... jen<PERSON>e to byl osud zba<PERSON> a mezi jeho lidem obrovská urážka. Pln hněvu se tedy vypravil hledat smrt, zu<PERSON><PERSON><PERSON> bojoval po celé zemi, skolil zástupy slavných vá<PERSON>čn<PERSON>ů a legend<PERSON>rn<PERSON>ch bestií, ale stále nena<PERSON> protivníka, k<PERSON><PERSON> by jej do<PERSON><PERSON><PERSON> zastavi<PERSON>. Nyní vstoupil do řad Zimního spáru a doufá, že najde svůj konec v blížící se válce.", "blurb": "<PERSON>, <PERSON>o ne<PERSON>ý živel z<PERSON><PERSON><PERSON>, to<PERSON><PERSON><PERSON> jen po jedin<PERSON> – zemřít ve velkolepém boji. Pochází z nemilosrdného freljordského poloostrovu Lokfar. Kdysi se dozvěděl o proroctví, podle kterého měl pokojně skonat... jenže to byl osud...", "allytips": ["<PERSON><PERSON><PERSON> má <PERSON> jen m<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bě<PERSON>lý hněv, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a Ragnarok a získat tak nečekanou sílu.", "<PERSON><PERSON><PERSON> l<PERSON>, k<PERSON><PERSON> p<PERSON> Zák<PERSON><PERSON><PERSON>, násobí tvé léčení útoky ze všech zdrojů a také vylepšuje léčení od spojenců."], "enemytips": ["<PERSON><PERSON><PERSON> má <PERSON> m<PERSON>, tím je nebez<PERSON>č<PERSON>ějš<PERSON>. Šetři si tedy omezující efekty až na samotný závěr boje, b<PERSON><PERSON> je <PERSON>.", "Zkus Olafovi zabránit v tom, aby se dostal ke své sekyře. Výrazně se tak sníží jeho efektivita ve fázi boje v lajnách.", "Olaf má sníženou obranu před poškozením během Ragnaroku, i když je imunní vůči omezujícím efektům. Pokud od Olafa během Ragnaroku nemů<PERSON>š utéct, zkus se svými parťáky soustředit poškození na něj."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 119, "mp": 316, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.7, "attackspeedperlevel": 2.7, "attackspeed": 0.694}, "spells": [{"id": "OlafAxeThrowCast", "name": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "description": "<PERSON> hodí na označenou pozici sekeru. <PERSON><PERSON><PERSON>, k<PERSON><PERSON>, z<PERSON><PERSON><PERSON><PERSON><PERSON> poš<PERSON>í a sníží jim hodnoty brnění a rychlosti pohybu. <PERSON><PERSON><PERSON> sekeru opě<PERSON>, přebíjecí doba této schopnosti se resetuje.", "tooltip": "<PERSON> ho<PERSON> se<PERSON>, <PERSON><PERSON><PERSON> z<PERSON>enému nepříteli z<PERSON>ůsobí <physicalDamage>{{ totaldamage }} bod<PERSON> fyzického poš<PERSON>í</physicalDamage> a <status>zpomal<PERSON></status> ho o {{ slowamount*100 }}&nbsp;% a<PERSON> na {{ e3 }}&nbsp;sek. (v závislosti na uražené vzdálenosti). Zasažení šampioni přijdou na {{ debuffduration }}&nbsp;sek. o <scaleArmor>{{ shredamount*100 }}&nbsp;% brnění</scaleArmor>.<br /><br />Pokud <PERSON>, zkrát<PERSON> se přebíjecí doba této schopnosti na {{ tooltipcdrefund }}&nbsp;sek., nebo se zcela navrátí, pokud už uběhlo {{ tooltipcdrefund }}&nbsp;sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození", "<PERSON><PERSON><PERSON>", "Poškození příšerám", "Mana pro seslání"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }} % -> {{ slowamountnl*100.000000 }} %", "{{ monsterdamage }} -> {{ monsterdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [30, 30, 30, 30, 30], [2.5, 2.5, 2.5, 2.5, 2.5], [1.5, 1.5, 1.5, 1.5, 1.5], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "30", "2.5", "1.5", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "OlafAxeThrowCast.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "OlafFrenziedStrikes", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Olafovi se zvýší rychlost útoků a získá štít.", "tooltip": "<PERSON> na {{ duration }}&nbsp;sek. <attackSpeed>{{ attackspeed*100 }}&nbsp;% k rych<PERSON><PERSON></attackSpeed> a <shield>št<PERSON>t o síle {{ baseshield }} bod<PERSON> plus {{ shieldpercmissinghp*100 }}&nbsp;% chyběj<PERSON><PERSON><PERSON><PERSON> (maximální síla <PERSON> je {{ maxshieldcalc }} bodů při méně než {{ thresholdformax*100 }}&nbsp;% zdraví)</shield>, k<PERSON><PERSON> p<PERSON> {{ shieldduration }}&nbsp;sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rychlost útoků", "Základ<PERSON><PERSON>", "Přebíjecí doba"], "effect": ["{{ attackspeed*100.000000 }} % -> {{ attackspeednl*100.000000 }} %", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "OlafFrenziedStrikes.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "OlafRecklessStrike", "name": "Zběsilá rána", "description": "<PERSON> ta<PERSON>, že cíli i sobě způsobuje přímé p<PERSON>š<PERSON>í. Pokud svůj cíl z<PERSON>, vr<PERSON><PERSON><PERSON> se mu cena zdraví.", "tooltip": "<PERSON> máchne svými sekerami a způsobí <trueDamage>{{ totaldamage }} bod<PERSON> př<PERSON><PERSON><PERSON></trueDamage>. Pokud nepřítel zemře, vr<PERSON>t<PERSON> se schopnosti cena spotřebovaná na její se<PERSON>lání.<br /><br />Útoky zkracují přebíjecí dobu této schopnosti o 1&nbsp;sek., při <PERSON> na příš<PERSON> se tato hodnota zvyšuje na 2&nbsp;sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození", "Přebíjecí doba"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "30", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "zdraví", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "OlafRecklessStrike.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "zdraví"}, {"id": "Olaf<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON> p<PERSON> z<PERSON>ává bonus k brnění a odolnosti vůči magii. Aktivací této schopnosti se stane imunním vůči omezujícím efektům, dokud bude sám útočit.", "tooltip": "<spellPassive>Pasivn<PERSON> efekt:</spellPassive> <PERSON> <scaleArmor>{{ resists }} k brněn<PERSON></scaleArmor> a <scaleMR>{{ resists }} k odolnosti vůči magii</scaleMR>.<br /><br /><spellActive>Aktivace:</spellActive> <PERSON> se zbaví všech <status>znehybňují<PERSON></status> a <status>omezujících</status> efektů a po dobu {{ duration }}&nbsp;sek. bude vůči nim zcela imunní. K<PERSON>ž je schopnost aktivní, získáv<PERSON> Olaf <scaleAD>{{ ad }} k útočnému poškození</scaleAD>. Při zasažení šampiona útokem nebo <spellName>Zběsilou ránou</spellName> se prodlouží trvání o {{ durationextension }}&nbsp;sek.<br /><br /><PERSON> nav<PERSON> na {{ hasteduration }}&nbsp;sek. z<PERSON> <speed>{{ haste*100 }}&nbsp;% k rychlosti pohybu</speed> při přesunu směrem k nepřátelským šampionům.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Brnění a odolnost vůči magii", "Útočné <PERSON>", "Rychlost pohybu", "Přebíjecí doba"], "effect": ["{{ resists }} -> {{ resistsNL }}", "{{ flatad }} -> {{ flatadNL }}", "{{ haste*100.000000 }} -> {{ hastenl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "OlafRagnarok.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Zběsilý hněv", "description": "<PERSON> bonus k rychlosti útoků a léčení útoky, jehož výše se odvíjí od jeho chybějícího zdraví.", "image": {"full": "Olaf_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}