{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Draven": {"id": "Draven", "key": "119", "name": "Draven", "title": "il glorioso carnefice", "image": {"full": "Draven.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "119000", "num": 0, "name": "default", "chromas": false}, {"id": "119001", "num": 1, "name": "Draven Predatore di Anime", "chromas": false}, {"id": "119002", "num": 2, "name": "Draven Gladiatore", "chromas": false}, {"id": "119003", "num": 3, "name": "Draven Prima Serata", "chromas": true}, {"id": "119004", "num": 4, "name": "Draven Festa in Piscina", "chromas": false}, {"id": "119005", "num": 5, "name": "Draven Cacciatore di Bestie", "chromas": false}, {"id": "119006", "num": 6, "name": "Draven Draven", "chromas": false}, {"id": "119012", "num": 12, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "119013", "num": 13, "name": "Draven dei Regni Mecha", "chromas": true}, {"id": "119020", "num": 20, "name": "Draven in Rovina", "chromas": true}, {"id": "119029", "num": 29, "name": "Dr<PERSON>", "chromas": true}, {"id": "119039", "num": 39, "name": "Draven Notte Inquietante", "chromas": true}, {"id": "119048", "num": 48, "name": "Draven la Ilusión", "chromas": true}, {"id": "119058", "num": 58, "name": "Draven Ordalia Gloriosa", "chromas": true}], "lore": "A Noxus, i gladiatori si affrontano nelle arene, versando sangue e mettendo in mostra la forza dei più audaci. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, è mai stato celebrato come Draven. Draven, un ex soldato, ha scoperto che le folle apprezzavano il suo gusto drammatico, per non parlare dell'impareggiabile abilità con cui brandisce le sue asce rotanti. Assuefatto allo spettacolo della sua stessa perfezione, Draven ha giurato di sconfiggere tutti i nemici necessari affinché il suo nome venga ricordato per sempre nell'impero.", "blurb": "A Noxus, i gladiatori si affrontano nelle arene, versando sangue e mettendo in mostra la forza dei più audaci. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, è mai stato celebrato come Draven. <PERSON><PERSON>, un ex soldato, ha scoperto che le folle apprezzavano il suo gusto drammatico, per...", "allytips": ["Se Draven non si muove, <PERSON><PERSON> rotante atterrerà vicino a dove si trova. Ricadrà direttamente su di lui, o appena a destra o a sinistra.", "Se Draven si muove dopo aver attaccato, Ascia rotante si orienterà verso la direzione del suo movimento. Sfrutta la cosa per controllare il movimento dell'Ascia rotante."], "enemytips": ["Lancia i colpi mirati dove dovrebbero atterrare le Asce rotanti di Draven.", "Disturba Draven con l'intenzione di fargli cadere le asce a terra. Se ci riesci, la sua potenza cala drasticamente."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 8}, "stats": {"hp": 675, "hpperlevel": 104, "mp": 361, "mpperlevel": 39, "movespeed": 330, "armor": 29, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.7, "mpregen": 8.05, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.6, "attackspeedperlevel": 2.7, "attackspeed": 0.679}, "spells": [{"id": "DravenSpinning", "name": "<PERSON><PERSON> rotante", "description": "Il prossimo attacco di Draven infligge danni fisici bonus. L'ascia rimbalza in aria contro il bersaglio. Se Draven la prende, prepara automaticamente un altro attacco con Ascia rotante. Draven può avere due Asce rotanti contemporaneamente.", "tooltip": "Draven prepara un'<keywordMajor><PERSON>cia rotante</keywordMajor>, che permette al suo attacco successivo di infliggere <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> aggiuntivi e rimbalza in aria. Se Draven la prende, prepara un altro attacco con <keywordMajor>Ascia rotante</keywordMajor>.<br /><br />Draven può avere due <keywordMajor>Asce rotanti</keywordMajor> contemporaneamente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Percentuale bonus attacco fisico", "Ricarica"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [100, 100, 100, 100, 100], [75, 85, 95, 105, 115], [30, 35, 40, 45, 50], [5.75, 5.75, 5.75, 5.75, 5.75], [40, 45, 50, 55, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100", "75/85/95/105/115", "30/35/40/45/50", "5.75", "40/45/50/55/60", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DravenSpinning.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenFury", "name": "<PERSON>atto sangui<PERSON>", "description": "Draven ottiene un aumento di velocità di movimento e attacco. Il bonus alla velocità di movimento diminuisce rapidamente per tutta la durata dell'effetto. Prendere un'ascia rotante azzera il tempo di ricarica di Scatto sanguigno.", "tooltip": "Draven ottiene <PERSON>, <speed>{{ e2 }}% velocità di movimento</speed> che decresce nell'arco di {{ e3 }} secondi e <attackSpeed>{{ e4 }}% velocità d'attacco</attackSpeed> per {{ e5 }} secondi.<br /><br />Quando Draven afferra un'<keywordMajor><PERSON><PERSON> rotante</keywordMajor>, il tempo di ricarica di questa abilità si azzera.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Costo in @AbilityResourceName@", "Velocità di movimento"], "effect": ["{{ e4 }}% -> {{ e4NL }}%", "{{ cost }} -> {{ costNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [4, 5, 6, 7, 8], [50, 55, 60, 65, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [-0.062, -0.069, -0.075, -0.081, -0.087], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/5/6/7/8", "50/55/60/65/70", "1.5", "20/25/30/35/40", "3", "-0.062/-0.069/-0.075/-0.081/-0.087", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "DravenFury.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenDoubleShot", "name": "<PERSON><PERSON> da <PERSON>e", "description": "Draven lancia le sue asce, infliggendo danni fisici ai bersagli colpiti e sbalzandoli di lato. I bersagli colpiti vengono rallentati.", "tooltip": "Draven lancia lateralmente un'ascia che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>, <status>respinge</status> i nemici e li <status>rallenta</status> del {{ e2 }}% per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [20, 25, 30, 35, 40], [2, 2, 2, 2, 2], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/110/145/180/215", "20/25/30/35/40", "2", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "DravenDoubleShot.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenRCast", "name": "Morte turbinante", "description": "Draven scaglia due enormi asce che infliggono danni fisici a ogni unità colpita. Morte turbinante inverte lentamente direzione e ritorna verso Draven dopo aver colpito un campione nemico. Draven può attivare questa abilità mentre le asce sono in volo per farle tornare prima. Infliggono danni in meno per ogni unità colpita e questa penalità si azzera quando le asce invertono la direzione. Giustizia i nemici con una salute inferiore al numero di cariche di Adorazione di Draven.", "tooltip": "Draven scaglia due enormi asce che infliggono <physicalDamage>{{ rcalculateddamage }} danni fisici</physicalDamage>. Quando colpisce un campione o <recast>rilancia</recast> l'abilità, le asce cambiano direzione e tornano da lui. Le asce infliggono {{ rdamagereductionperhit*100 }}% danni in meno per ogni nemico colpito, fino a un minimo del {{ rmindamagepercent }}%.<br /><br />Se <keywordMajor>Morte turbinante</keywordMajor> lasciasse un campione nemico con meno salute del {{ rpassivestackscoefficient*100 }}% delle attuali cariche di <keywordMajor>League of Draven</keywordMajor> ({{ rpassivetruedamage }}), Draven lo giustizia.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Rapporto attacco fisico bonus"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rcoefficient*100.000000 }}% -> {{ rcoefficientnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20000, 20000, 20000], "rangeBurn": "20000", "image": {"full": "DravenRCast.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "League of Draven", "description": "Draven riceve l'Adorazione dei suoi fan ogni volta che afferra un'Ascia rotante o elimina un minion, un mostro o una torre. Uccidere campioni nemici gli conferisce oro bonus a seconda della quantità di Adorazione posseduta.", "image": {"full": "Draven_passive.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}