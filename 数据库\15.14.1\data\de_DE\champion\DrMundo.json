{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "Dr. <PERSON>", "title": "<PERSON> <PERSON><PERSON>", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "<PERSON><PERSON> Dr<PERSON>", "chromas": false}, {"id": "36002", "num": 2, "name": "Mr. <PERSON>", "chromas": false}, {"id": "36003", "num": 3, "name": "Big Boss Mundo", "chromas": true}, {"id": "36004", "num": 4, "name": "Mundo Mundo", "chromas": false}, {"id": "36005", "num": 5, "name": "<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "36006", "num": 6, "name": "Zorngeborener Mundo", "chromas": false}, {"id": "36007", "num": 7, "name": "TPA-Mundo", "chromas": false}, {"id": "36008", "num": 8, "name": "Poolparty-Mundo", "chromas": false}, {"id": "36009", "num": 9, "name": "El Macho Mundo", "chromas": false}, {"id": "36010", "num": 10, "name": "Frostprinz Mundo", "chromas": true}, {"id": "36021", "num": 21, "name": "Straßendämonen-<PERSON><PERSON>", "chromas": true}], "lore": "Der komplett wahnsinnige, m<PERSON><PERSON><PERSON><PERSON> veranlagte und erschreckend violette Dr. Mundo sorgt bei vielen Bewohnern Zhauns für Albträume. Der ehemalige Patient des berüchtigtsten Irrenhauses von <PERSON> ist heute ein selbsternannter Arzt. Nachdem Dr. Mundo alle Mitarbeiter des Irrenhauses „geheilt“ hatte, richtete er sich in einem leeren Flügel eine Praxis ein und begann damit, die höchst unethischen Prozeduren vorzunehmen, die er immer wieder am eigenen Leib zu spüren bekommen hatte. Er verfügt über einen vollen Medizinschrank und absolut gar kein medizinisches Wissen. Dabei macht er sich selbst mit jeder Injektion noch furchteinflößender, was auch die glücklosen „Patienten“ zu spüren bekommen, die sich in der Nähe seiner Praxis aufhalten.", "blurb": "Der komplett wahnsinnige, mörde<PERSON>ch veranlagte und erschreckend violette Dr. Mundo sorgt bei vielen Bewohnern Zhauns für Albträume. Der ehemalige Patient des berüchtigtsten Irrenhauses von <PERSON> ist heute ein selbsternannter Arzt. Nachdem Dr. Mundo...", "allytips": ["Die Benutzung von „Sadismus“ im richtigen Moment kann dazu führen, dass sich Gegner überschätzen, angreifen und dann trotzdem verlieren, weil sie nicht genügend Schaden verursachen.", "„Geistessicht“ erhöht die Heilung deiner ultimativen Fähigkeit und verringert die Abklingzeiten all deiner Fähigkeiten", "Beile sind sehr hilfreich beim Töten neutraler Monster. Anstatt zur Basis zurückzukehren, kannst du also neutrale Monster töten, bis deine ultimative Fähigkeit dich heilen kann."], "enemytips": ["Stimme dich mit deinen Verbündeten ab und versucht, gemeinsam Fähigkeiten mit hohem Schaden gegen Dr. Mu<PERSON>zu<PERSON>zen, nachdem er seine ultimative Fähigkeit eingesetzt hat. Wenn du ihn nicht schnell genug tötest, heilt er sich wieder.", "Versuche „Entzünden“ zu wirken, wenn Dr. <PERSON><PERSON> „Sadismus“ ben<PERSON>t, um einen Großteil seiner Heilung wirkungslos zu machen."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "Rostige Knochensäge", "description": "Dr. <PERSON><PERSON> wirft eine rostige Knochensäge, die dem ersten getroffenen Gegner Schaden basierend auf seinem aktuellen Leben zufügt und ihn verlangsamt.", "tooltip": "Dr. <PERSON>ndo schleudert seine Knochensäge und fügt dem ersten getroffenen Gegner <magicDamage>magischen Schaden</magicDamage> in <PERSON><PERSON><PERSON> von {{ currenthealthdamage*100 }}&nbsp;% seines aktuellen Lebens zu. Außerdem wird das Ziel {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br />Wenn die Knochensäge einen Champion oder ein Monster trifft, stellt Mundo <healing>{{ healthrestoreonhitchampionmonster }}&nbsp;Leben</healing> wieder her. Trifft sie eine Einheit, die kein Champion oder Monster ist, stellt Dr. Mundo stattdessen <healing>{{ healthrestoreonhitminion }}&nbsp;Leben</healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Aktueller Schaden abhä<PERSON><PERSON> von <PERSON>", "Mindestschaden", "Grenze für Monsterschaden", "Lebenskosten"], "effect": ["{{ currenthealthdamage*100.000000 }}&nbsp;% -> {{ currenthealthdamagenl*100.000000 }}&nbsp;%", "{{ minimumdamage }} -> {{ minimumdamageNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}", "{{ healthcost }} -> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Leben", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ healthcost }}&nbsp;Leben"}, {"id": "DrMundoW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Dr. <PERSON><PERSON> setzt sich selbst unter Strom. <PERSON>bei fügt er <PERSON>egnern in der Nähe kontinuierlich Schaden zu und speichert einen Teil des Schadens, den er erleidet. Am Ende der Dauer oder bei Reaktivierung löst Dr. Mundo eine Explosion aus, die Gegnern in der Nähe hohen Schaden über kurze Zeit zufügt. Wenn die Explosion einen Gegner trifft, heilt er einen Prozentsatz des gespeicherten Schadens.", "tooltip": "Dr. <PERSON>ndo lädt einen Defibrillator auf, der Gegnern in der Nähe bis zu {{ duration }}&nbsp;Sekunden lang jede Sekunde <magicDamage>{{ damagepertick*4 }}&nbsp;magischen <PERSON>haden</magicDamage> zufügt. Außerdem speichert er während der ersten {{ grayhealthinitialduration }}&nbsp;Sekunden {{ grayhealthstorageinitial }} des erlittenen Schadens und während der restlichen Dauer {{ grayhealthstorage*100 }}&nbsp;% des erlittenen Schadens als graues Leben und kann die Fähigkeit <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> Der Defibrillator explodiert und fügt Gegnern in der Nähe <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu. Wird mindestens ein Champion getroffen, stellt Dr. Mundo <healing>{{ grayhealthbigmod*100 }}&nbsp;% des grauen Lebens</healing> wieder her, ansonsten stellt er stattdessen <healing>{{ grayhealthsmallmod*100 }}&nbsp;% des grauen Lebens</healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Zeiteinheit", "Schaden der Reaktivierung", "Abklingzeit"], "effect": ["{{ damagepertick }} -> {{ damagepertickNL }}", "{{ recastbasedamage }} -> {{ recastbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ currenthealthcost*100 }}&nbsp;% des aktuellen Lebens"}, {"id": "DrMundoE", "name": "Stumpfe Gewaltanwendung", "description": "Passiv – Dr. <PERSON><PERSON> erhält basierend auf seinem maximalen Leben zusätzlichen Angriffsschaden.<br><br>Aktiv – Dr. <PERSON><PERSON> rammt seine „Medizintasche“ in einen Gegner und fügt ihm zusätzlichen Schaden basierend auf seinem fehlenden Leben zu. Wenn der Gegner dadurch stirbt, wird er weggeschleudert und fügt allen <PERSON>n, die er auf seinem Weg trifft, <PERSON><PERSON><PERSON> zu.", "tooltip": "<spellPassive>Passiv:</spellPassive> Dr. <PERSON><PERSON> erhält <physicalDamage>{{ passivebonusad }}&nbsp;Angriffsschaden</physicalDamage>.<br /><br /><spellActive>Aktiv:</spellActive> Dr. <PERSON><PERSON> schwingt seine „Medizintasche“, wodurch sein nächster Angriff zusätzlich <physicalDamage>{{ additionaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht (wird abhängig von seinem fehlenden Leben um bis zu {{ maxdamageamptooltip }} erhöht). Wenn der Gegner stirbt, schleudert Mundo ihn beiseite und fügt getroffenen Gegnern <physicalDamage>{{ additionaldamage }}&nbsp;normalen Schaden</physicalDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden", "Lebenskosten", "Leben in Angriffsschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ flathealthcost }} -> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}&nbsp;% -> {{ healthtoadrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Leben", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ flathealthcost }}&nbsp;Le<PERSON>"}, {"id": "DrMundoR", "name": "<PERSON><PERSON> (Am)Pulle", "description": "Dr. <PERSON><PERSON> pumpt sich mit Chemikalien voll und heilt sich sofort um einen Prozentsatz seines fehlenden Lebens. Anschließend erhält er Lauftempo und regeneriert über längere Zeit einen Teil seines maximalen Lebens.", "tooltip": "Dr. <PERSON><PERSON> pumpt sich mit Chemikalien voll und erhält maximales Leben in <PERSON><PERSON><PERSON> von <healing>{{ missinghealthheal*100 }}&nbsp;% seines fehlenden Lebens</healing> sowie <speed>{{ speedboostamount*100 }}&nbsp;% Lauftempo</speed> und stellt über {{ duration }}&nbsp;Sekunden hinweg <healing>{{ maxhealthhot*100 }}&nbsp;% seines maximalen Lebens</healing> wieder her.<br /><br />Auf Rang 3 werden beide heilenden Effekte für jeden gegnerischen Champion in der Nähe um zusätzliche {{ bonuspernearbychampion*100 }}&nbsp;% erhöht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzliches Leben", "Lauftempo", "<PERSON><PERSON> (%)"], "effect": ["{{ missinghealthheal*100.000000 }}&nbsp;% -> {{ missinghealthhealnl*100.000000 }}&nbsp;%", "{{ speedboostamount*100.000000 }}&nbsp;% -> {{ speedboostamountnl*100.000000 }}&nbsp;%", "{{ maxhealthhot*100.000000 }}&nbsp;% -> {{ maxhealthhotnl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "<PERSON><PERSON><PERSON>, wo er will", "description": "Dr. <PERSON><PERSON> widersteht dem ersten bewegungsunfähig machenden Effekt, der ihn trifft. Stattdessen verliert er Leben und lässt in der Nähe einen Kanister mit Chemikalien fallen. Dr. <PERSON>ndo kann über den Kanister laufen, um ihn aufzuheben, Le<PERSON> wiederherzustellen und die Abklingzeit dieser Fähigkeit zu verringern.<br><br>Dr. <PERSON>ndo verfügt außerdem über eine stark erhöhte Lebensregeneration.<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}