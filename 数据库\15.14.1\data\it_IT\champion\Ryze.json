{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ryze": {"id": "Ryze", "key": "13", "name": "Ryze", "title": "il mago delle rune", "image": {"full": "Ryze.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "13000", "num": 0, "name": "default", "chromas": false}, {"id": "13001", "num": 1, "name": "<PERSON><PERSON><PERSON> G<PERSON>", "chromas": false}, {"id": "13002", "num": 2, "name": "Ryze Tribale", "chromas": false}, {"id": "13003", "num": 3, "name": "Zio Ryze", "chromas": false}, {"id": "13004", "num": 4, "name": "<PERSON><PERSON><PERSON> Trionfante", "chromas": false}, {"id": "13005", "num": 5, "name": "Professor <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "13006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "13007", "num": 7, "name": "<PERSON><PERSON><PERSON> C<PERSON>allo <PERSON>", "chromas": false}, {"id": "13008", "num": 8, "name": "Ryze Pirata", "chromas": false}, {"id": "13009", "num": 9, "name": "Ryze Barbabianca", "chromas": false}, {"id": "13010", "num": 10, "name": "Ryze SKT T1", "chromas": true}, {"id": "13011", "num": 11, "name": "Ryze Mondiali 2019", "chromas": true}, {"id": "13013", "num": 13, "name": "Ryze Guardiano delle Sabbie", "chromas": true}, {"id": "13020", "num": 20, "name": "Ryze Arcano Maggiore", "chromas": true}, {"id": "13029", "num": 29, "name": "Ryze Luna di Sangue", "chromas": true}], "lore": "Considerato come uno dei più abili maghi di Runeterra, <PERSON><PERSON>ze è un anziano e ostinato arcimago che porta un enorme peso sulle spalle. Dotato di un'ampia formazione e un immenso potere arcano, si dedica senza sosta alla ricerca di Rune Terrene, frammenti di magia pura che un tempo plasmarono il mondo dal nulla. Deve entrare in possesso di questi artefatti prima che finiscano nelle mani sbagliate, perché conosce gli orrori che potrebbero scatenarsi a Runeterra.", "blurb": "Considerato come uno dei più abili maghi di Runeterra, Ryze è un anziano e ostinato arcimago che porta un enorme peso sulle spalle. Dotato di un'ampia formazione e un immenso potere arcano, si dedica senza sosta alla ricerca di Rune Terrene, frammenti...", "allytips": ["Usa la passiva di Sovraccarico per ottimizzare i danni massimi e la velocità massima.", "La breve ricarica di Flusso incantato permette la diffusione di Flusso su molti nemici.", "Ryze può muoversi e lanciare altre abilità mentre Portale dei reami è in carica, senza annullare il portale."], "enemytips": ["Ryze è particolarmente pericoloso per i nemici marchiati con Flusso.", "Usa il tempo di preparazione di Portale dei reami per capire come gestire quello che potrebbe uscire dal portale.", "<PERSON>li effetti di controllo ai danni di Ryze durante la preparazione di Portale dei reami annullano il portale."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 645, "hpperlevel": 124, "mp": 300, "mpperlevel": 70, "movespeed": 340, "armor": 22, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "RyzeQWrapper", "name": "Sovraccarico", "description": "Passivamente, le altre abilità base di Ryze azzerano la ricarica Sovraccarico e caricano una runa. Quando Ryze lancia Sovraccarico con 2 rune cariche, ottiene un breve aumento della velocità di movimento.<br><br><PERSON> lancio, R<PERSON>ze scaglia una carica di pura energia in linea retta, infliggendo danni al primo nemico colpito. Se il bersaglio ha Flusso, Sovraccarico infligge danni extra e rimbalza verso i nemici nelle vicinanze marchiati con <PERSON>lus<PERSON>.", "tooltip": "<spellPassive>Passiva: </spellPassive><spellName>Prigione runica</spellName> e <spellName>Flusso incantato</spellName> azzerano la ricarica di questa abilità e caricano una runa per {{ runeduration }} secondi, fino a un massimo di {{ maximumrunes }} rune.<br /><br /><spellActive>Attiva:</spellActive> Ryze scatena un'esplosione che infligge <magicDamage>{{ qdamagecalc }} danni magici</magicDamage> al primo nemico colpito. Se il bersaglio ha <keywordMajor>Flusso</keywordMajor>, questo viene consumato; l'abilità quindi infligge {{ spell.ryzer:overloaddamagebonus }}% danni aggiuntivi e rimbalza sui nemici vicini che hanno <keywordMajor>Flusso</keywordMajor>.<br /><br />Ryze scarica anche tutte le rune, conferendo a Ryze <speed>{{ movementspeedamount }}% velocità di movimento</speed> per {{ movementspeedduration }} secondi se erano state caricate {{ maximumrunes }} rune.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedamount }}% -> {{ movementspeedamountNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 38, 36, 34, 32], "costBurn": "40/38/36/34/32", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [25, 40, 55, 70, 85], [50, 75, 100, 125, 150], [25, 28, 31, 24, 37], [2, 2, 2, 2, 2], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0.015, 0.015, 0.015, 0.015, 0.015], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "25/40/55/70/85", "50/75/100/125/150", "25/28/31/24/37", "2", "2", "3", "0.01", "2", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RyzeQWrapper.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeW", "name": "Prigione runica", "description": "Ryze intrappola un bersaglio in una gabbia di rune, che infligge danni e rallenta il movimento. Se il bersaglio ha Flusso, viene immobilizzato.", "tooltip": "Ryze infligge <magicDamage>{{ wdamagecalc }} danni magici</magicDamage> e <status>rallenta</status> del {{ slowamount*100 }}% per {{ ccduration }} secondi. Se il bersaglio ha <keywordMajor>Flusso</keywordMajor>, questo viene consumato e l'abilità <status>immobilizza</status> invece di <status>rallentare</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeE", "name": "<PERSON><PERSON>so incantato", "description": "Ryze scaglia una sfera di pura energia magica che danneggia un nemico e applica un debuff a tutti i nemici nelle vicinanze. Le abilità di Ryze hanno effetti aggiuntivi contro i nemici con il debuff.", "tooltip": "Ryze scaglia una sfera che infligge <magicDamage>{{ edamagecalc }} danni magici</magicDamage> e applica <keywordMajor>Flusso</keywordMajor> per {{ debuffduration }} secondi al bersaglio e ai nemici vicini. I nemici già colpiti da <keywordMajor>Flusso</keywordMajor> lo faranno espandere.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.25, 3, 2.75, 2.5], "cooldownBurn": "3.5/3.25/3/2.75/2.5", "cost": [35, 45, 55, 65, 75], "costBurn": "35/45/55/65/75", "datavalues": {}, "effect": [null, [80, 90, 100, 110, 120], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.1, 0.1, 0.1, 0.1, 0.1], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [4, 4, 4, 4, 4], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/90/100/110/120", "40/50/60/70/80", "100", "0.1", "1.5", "1.5", "4", "1", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeR", "name": "Portale dei reami", "description": "Passivamente, Sovraccarico infligge ancora più danni ai bersagli con Flusso.<br><br><PERSON>nc<PERSON>, <PERSON><PERSON>ze crea un portale verso una posizione vicina. <PERSON><PERSON> qualche secondo, gli alleati vicini al portale vengono teletrasportati nella posizione.", "tooltip": "<spellPassive>Passiva:</spellPassive> i danni bonus di <spellName>Sovraccarico</spellName> contro i bersagli con <keywordMajor>Flusso</keywordMajor> aumentano del {{ overloaddamagebonus }}%.<br /><br /><spellActive>Attiva:</spellActive> Ryze apre un portale verso un'altra posizione. Dopo {{ chargetimetooltip }} secondi, tutti gli alleati vicini al portale vengono teletrasportati in quel punto.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Aumento danni Sovraccarico"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ overloaddamagebonus }}% -> {{ overloaddamagebonusNL }}%"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "RyzeR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON> arcana", "description": "<mainText>Le abilità di Ryze infliggono danni magici aggiuntivi in base al suo mana bonus e ottiene un aumento al mana massimo basato su una percentuale del potere magico.</mainText>", "image": {"full": "Ryze_P.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}