{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tryndamere": {"id": "Tryndamere", "key": "23", "name": "Tryndamere", "title": "the Barbarian King", "image": {"full": "Tryndamere.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "23000", "num": 0, "name": "default", "chromas": false}, {"id": "23001", "num": 1, "name": "Highland Tryndamere", "chromas": false}, {"id": "23002", "num": 2, "name": "King <PERSON><PERSON>", "chromas": false}, {"id": "23003", "num": 3, "name": "Viking Tryndamere", "chromas": false}, {"id": "23004", "num": 4, "name": "Demonblade Tryndamere", "chromas": false}, {"id": "23005", "num": 5, "name": "Sultan Tryndamere", "chromas": false}, {"id": "23006", "num": 6, "name": "Warring Kingdoms Tryndamere", "chromas": false}, {"id": "23007", "num": 7, "name": "Nightmare Tryndamere", "chromas": false}, {"id": "23008", "num": 8, "name": "Beast Hunter Tryndamere", "chromas": false}, {"id": "23009", "num": 9, "name": "Chemtech Tryndamere", "chromas": false}, {"id": "23010", "num": 10, "name": "Blood Moon Tryndamere", "chromas": true}, {"id": "23018", "num": 18, "name": "Nightbringer Tryndamere", "chromas": true}, {"id": "23027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Fueled by unbridled fury and rage, <PERSON><PERSON><PERSON> once carved his way through the Freljord, openly challenging the greatest warriors of the north to prepare himself for even darker days ahead. The wrathful barbarian has long sought revenge for the annihilation of his clan, though more recently he has found companionship with <PERSON>, the Avarosan warmother, and a home with her people. His almost inhuman strength and fortitude is legendary, and has delivered him and his new allies countless victories against the greatest of odds.", "blurb": "Fueled by unbridled fury and rage, <PERSON><PERSON><PERSON> once carved his way through the Freljord, openly challenging the greatest warriors of the north to prepare himself for even darker days ahead. The wrathful barbarian has long sought revenge for the...", "allytips": ["Delaying the activation of Undying Rage is a very effective way to make an enemy champion over-commit to trying to kill you.", "Bloodlust is an excellent way to heal Tryndamere. Try not to let it fade off before you activate it.", "If the enemy is stacking Armor, try building items like Last Whisper or <PERSON><PERSON><PERSON>'s Ghostblade."], "enemytips": ["Try harassing <PERSON><PERSON><PERSON> early so he can't kill minions and heal with <PERSON><PERSON><PERSON>.", "Remember, <PERSON><PERSON><PERSON> can only slow you if you are facing away from him.", "Most of <PERSON><PERSON><PERSON>'s damage is physical. If he's getting too strong, consider buying a Thornmail."], "tags": ["Fighter", "Assassin"], "partype": "Fury", "info": {"attack": 10, "defense": 5, "magic": 2, "difficulty": 5}, "stats": {"hp": 696, "hpperlevel": 108, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.9, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 4, "attackspeedperlevel": 3.4, "attackspeed": 0.67}, "spells": [{"id": "TryndamereQ", "name": "Bloodlust", "description": "<PERSON><PERSON><PERSON> thrives on the thrills of combat, increasing his Attack Damage as he is more and more wounded. He can cast <PERSON><PERSON><PERSON> to consume his <PERSON> and heal himself.", "tooltip": "<spellPassive>Passive:</spellPassive> Tryndamere thirsts for blood, gaining <scaleAD>{{ flatad }} Attack Damage</scaleAD> plus <scaleAD>{{ adperonepercentmissinghp }}</scaleAD> per 1% missing Health.<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON><PERSON> consumes his <keywordMajor>Fury</keywordMajor>, restoring <healing>{{ baseheal }} plus {{ healperfury }} Health per Fury</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Damage", "Attack Damage per Health % Missing", "Healing", "Heal <PERSON>"], "effect": ["{{ flatad }} -> {{ flatadNL }}", "{{ adperonepercentmissinghp }} -> {{ adperonepercentmissinghpNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ bonushealperfury }} -> {{ bonushealperfuryNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "TryndamereQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "TryndamereW", "name": "<PERSON><PERSON>", "description": "Tryndamere lets out an insulting cry, decreasing surrounding champions' Attack Damage. Enemies with their backs turned to Tryndamere also have their Move Speed reduced.", "tooltip": "Tryndamere hurls insults, reducing champions' Attack Damage by {{ adreduction*-1 }} for {{ reductionduration }} seconds. Enemy champions with their backs towards Tryndamere are <status>Slowed</status> by {{ slowpotency*-100 }}% for the same duration.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Physical Damage Reduction", "Slow"], "effect": ["{{ adreduction*-1.000000 }} -> {{ adreductionnl*-1.000000 }}", "{{ slowpotency*-100.000000 }}% -> {{ slowpotencynl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "TryndamereW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "TryndamereE", "name": "Spinning Slash", "description": "<PERSON><PERSON><PERSON> slices toward a target unit, dealing damage to enemies in his path.", "tooltip": "<PERSON><PERSON><PERSON> spins through his enemies, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> and generating <keywordMajor>{{ nonchampfurygain }} Fury</keywordMajor> per enemy hit, increased to <keywordMajor>{{ champfurygain }} Fury</keywordMajor> against enemy champions.<br /><br />This Ability's Cooldown is reduced by {{ nonchampcdrefund }} second when <PERSON><PERSON><PERSON> critically strikes, and reduced by {{ champcdrefund }} seconds when critically striking a champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TryndamereE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "UndyingRage", "name": "Undying Rage", "description": "<PERSON><PERSON><PERSON>'s lust for battle becomes so strong that he is unable to die, no matter how wounded he becomes.", "tooltip": "Tryndamere becomes completely immune to death for {{ e3 }} seconds, refusing to be reduced below {{ e2 }} Health, and instantly gains <keywordMajor>{{ e1 }} Fury</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Minimum Health", "<PERSON> Gained"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [50, 75, 100], [30, 50, 70], [5, 5, 5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "50/75/100", "30/50/70", "5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "UndyingRage.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "No Cost"}], "passive": {"name": "Battle Fury", "description": "<PERSON><PERSON><PERSON> gains <PERSON> for each attack, critical strike, and killing blow he makes. <PERSON> passively increases his Critical Strike Chance and can be consumed with his Bloodlust spell.", "image": {"full": "Tryndamere_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}