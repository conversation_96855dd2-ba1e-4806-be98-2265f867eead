{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ryze": {"id": "Ryze", "key": "13", "name": "Ryze", "title": "the Rune Mage", "image": {"full": "Ryze.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "13000", "num": 0, "name": "default", "chromas": false}, {"id": "13001", "num": 1, "name": "Young Ryze", "chromas": false}, {"id": "13002", "num": 2, "name": "Tribal Ryze", "chromas": false}, {"id": "13003", "num": 3, "name": "Uncle <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "13004", "num": 4, "name": "Triumphant <PERSON>", "chromas": false}, {"id": "13005", "num": 5, "name": "Professor <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "13006", "num": 6, "name": "Zombie Ryze", "chromas": false}, {"id": "13007", "num": 7, "name": "Dark Crystal Ryze", "chromas": false}, {"id": "13008", "num": 8, "name": "Pirate Ryze", "chromas": false}, {"id": "13009", "num": 9, "name": "<PERSON><PERSON><PERSON>beard", "chromas": false}, {"id": "13010", "num": 10, "name": "SKT T1 Ryze", "chromas": true}, {"id": "13011", "num": 11, "name": "Worlds 2019 Ryze", "chromas": true}, {"id": "13013", "num": 13, "name": "Guardian of the Sands Ryze", "chromas": true}, {"id": "13020", "num": 20, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "13029", "num": 29, "name": "Blood Moon Ryze", "chromas": true}], "lore": "Widely considered one of the most adept sorcerers on Runeterra, <PERSON><PERSON><PERSON> is an ancient, hard-bitten archmage with an impossibly heavy burden to bear. Armed with immense arcane power and a boundless constitution, he tirelessly hunts for World Runes—fragments of the raw magic that once shaped the world from nothingness. He must retrieve these artifacts before they fall into the wrong hands, for <PERSON><PERSON><PERSON> understands the horrors they could unleash on Runeterra.", "blurb": "Widely considered one of the most adept sorcerers on Runeterra, <PERSON><PERSON><PERSON> is an ancient, hard-bitten archmage with an impossibly heavy burden to bear. Armed with immense arcane power and a boundless constitution, he tirelessly hunts for World Runes—fragments...", "allytips": ["Use Overload's passive to optimize for maximum damage or maximum speed.", "Spell Flux's short cooldown allows it to be used to spread Flux to many enemies.", "<PERSON><PERSON><PERSON> can move and cast other spells while Realm Warp is charging without cancelling the portal."], "enemytips": ["Ryze is especially dangerous to opponents that are marked with Flux.", "Use Realm Warp's windup time to figure out how to deal with what may be coming out of the portal.", "Crowd controlling Ryze during Realm Warp's windup will cancel the portal."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 645, "hpperlevel": 124, "mp": 300, "mpperlevel": 70, "movespeed": 340, "armor": 22, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "RyzeQWrapper", "name": "Overload", "description": "Passively, <PERSON><PERSON><PERSON>'s other basic abilities reset the cooldown of Overload and charge a rune. When <PERSON><PERSON><PERSON> casts Overload with 2 runes charged, he gains a brief burst of Move Speed.<br><br>On cast, <PERSON><PERSON><PERSON> throws a charge of pure energy in a line, dealing damage to the first enemy struck. If the target has Flux on it, Overload deals extra damage and bounces to nearby enemies with Flux.", "tooltip": "<spellPassive>Passive:</spellPassive> <spellName>Rune Prison</spellName> and <spellName>Spell Flux</spellName> refresh this Ability's Cooldown and charge a rune for {{ runeduration }} seconds (max {{ maximumrunes }} runes).<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON><PERSON> unleashes a blast, dealing <magicDamage>{{ qdamagecalc }} magic damage</magicDamage> to the first enemy hit. If the target has <keywordMajor>Flux</keywordMajor> on it, it is consumed, causing this Ability to deal {{ spell.ryzer:overloaddamagebonus }}% increased damage and bounces to nearby enemies with <keywordMajor>Flux</keywordMajor>.<br /><br /><PERSON><PERSON><PERSON> also discharges all runes, granting <PERSON>yze <speed>{{ movementspeedamount }}% Move Speed</speed> for {{ movementspeedduration }} seconds if {{ maximumrunes }} runes were charged.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Move Speed", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedamount }}% -> {{ movementspeedamountNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 38, 36, 34, 32], "costBurn": "40/38/36/34/32", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [25, 40, 55, 70, 85], [50, 75, 100, 125, 150], [25, 28, 31, 24, 37], [2, 2, 2, 2, 2], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0.015, 0.015, 0.015, 0.015, 0.015], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "25/40/55/70/85", "50/75/100/125/150", "25/28/31/24/37", "2", "2", "3", "0.01", "2", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RyzeQWrapper.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeW", "name": "Rune Prison", "description": "<PERSON><PERSON><PERSON> traps a target in a cage of runes, damaging them and slowing their movement. If the target has Flux on it, they are instead rooted.", "tooltip": "Ryze deals <magicDamage>{{ wdamagecalc }} magic damage</magicDamage> and <status>Slows</status> by {{ slowamount*100 }}% for {{ ccduration }} seconds. If the target has <keywordMajor>Flux</keywordMajor>, it is consumed and this Ability <status>Roots</status> instead of <status>Slowing</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeE", "name": "Spell Flux", "description": "<PERSON><PERSON><PERSON> releases an orb of pure magical power that damages an enemy and debuffs all nearby enemies. <PERSON><PERSON><PERSON>'s spells have additional effects against the debuffed enemy.", "tooltip": "<PERSON><PERSON><PERSON> fires an orb, dealing <magicDamage>{{ edamagecalc }} magic damage</magicDamage> and applies <keywordMajor>Flux</keywordMajor> for {{ debuffduration }} seconds to the target and nearby enemies. Enemies already afflicted with <keywordMajor>Flux</keywordMajor> will cause it to spread further.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.25, 3, 2.75, 2.5], "cooldownBurn": "3.5/3.25/3/2.75/2.5", "cost": [35, 45, 55, 65, 75], "costBurn": "35/45/55/65/75", "datavalues": {}, "effect": [null, [80, 90, 100, 110, 120], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.1, 0.1, 0.1, 0.1, 0.1], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [4, 4, 4, 4, 4], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/90/100/110/120", "40/50/60/70/80", "100", "0.1", "1.5", "1.5", "4", "1", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeR", "name": "Realm Warp", "description": "Passively, Overload deals even more damage against targets with Flux.<br><br>On cast, <PERSON><PERSON><PERSON> creates a portal to a nearby location. After a few seconds, allies standing near the portal are teleported to the target location.", "tooltip": "<spellPassive>Passive:</spellPassive> <spellName>Overload</spellName> damage bonus against targets with <keywordMajor>Flux</keywordMajor> increased to {{ overloaddamagebonus }}%.<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON><PERSON> opens a portal to another location. After {{ chargetimetooltip }} seconds, all allies near the portal are teleported to that location.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Overload Damage Increase"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ overloaddamagebonus }}% -> {{ overloaddamagebonusNL }}%"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "RyzeR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Arcane Mastery", "description": "<mainText><PERSON><PERSON><PERSON>'s spells deal extra damage based on his Bonus Mana, and he gains a percentage increase to his maximum Mana based on his Ability Power.</mainText>", "image": {"full": "Ryze_P.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}