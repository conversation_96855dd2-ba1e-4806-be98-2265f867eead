{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Volibear": {"id": "Volibear", "key": "106", "name": "Volibear", "title": "Bezlitosny Grom", "image": {"full": "Volibear.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "106000", "num": 0, "name": "default", "chromas": false}, {"id": "106001", "num": 1, "name": "Volibear Gromowładny", "chromas": false}, {"id": "106002", "num": 2, "name": "Volibear Burza Północy", "chromas": false}, {"id": "106003", "num": 3, "name": "Strażnik Run Volibear", "chromas": false}, {"id": "106004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "106005", "num": 5, "name": "El Rayo Volibear", "chromas": false}, {"id": "106006", "num": 6, "name": "Tysiąckroć Przebity Niedźwiedź", "chromas": false}, {"id": "106007", "num": 7, "name": "Dwoisty Smok Volibear", "chromas": true}, {"id": "106009", "num": 9, "name": "Dwoisty Smok Volibear (Prestiżowy)", "chromas": false}, {"id": "106019", "num": 19, "name": "<PERSON>ibear <PERSON>nej Sep<PERSON>", "chromas": false}], "lore": "Ci, kt<PERSON><PERSON><PERSON> wciąż czczą Volibeara, u<PERSON><PERSON><PERSON><PERSON> go za uosobienie burzy. Ten niszczycielski, dziki i niewzruszony stwór istniał, zanim śmiertelnicy postawili stopę we freljordzkiej tundrze i z dzikością broni krainy, którą stworzył wraz ze swoimi półboskimi pobratymcami. Pielęgnując w sobie głęboką nienawiść do cywilizacji i słabości, jaką ta za sobą pociągnęła, Volibear walczy o powrót do dawnych zwyczajów — do czasu, kiedy kraina nie była okiełznana, a rozlew krwi nie był niczym ograniczony — i ochoczo stawia czoła wszystkim swoim oponentom przy pomocy szponów, kłów i piorunującej dominacji.", "blurb": "Ci, którzy wciąż czcz<PERSON> Volibeara, uz<PERSON><PERSON><PERSON> go za uosobienie burzy. Ten niszczycielski, dziki i niewzruszony stwór istniał, zanim śmiertelnicy postawili stopę we freljordzkiej tundrze i z dzikością broni krainy, którą stworzył wraz ze swoimi półboskimi...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 7, "magic": 4, "difficulty": 3}, "stats": {"hp": 650, "hpperlevel": 104, "mp": 350, "mpperlevel": 70, "movespeed": 340, "armor": 31, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.75, "mpregen": 6.25, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "VolibearQ", "name": "Grzmi<PERSON>ce Uderzenie", "description": "Volibear zyskuje pręd<PERSON> ruchu, gdy porusza się w kierunku wrogów, oraz <status>og<PERSON><PERSON><PERSON></status> i zadaje obrażenia pierwszemu wrogowi, którego zaatakuje.", "tooltip": "Volibear zyskuje <speed>{{ minspeedcalc }} pr<PERSON>dkości ruchu</speed> (podwojonych do <speed>{{ maxspeedcalc }}</speed>, gdy porusza się w kierunku wrogich bohaterów) na {{ duration }} sek. Kiedy ta umiejętność jest aktywna, nast<PERSON><PERSON>ny atak Volibeara zadaje <physicalDamage>{{ calculateddamage }} pkt. obrażeń fizycznych</physicalDamage> i <status>ogłusza</status> cel na {{ stunduration }} sek.<br /><br />Volibear wpada w szał, jeśli wróg <status>unieruchomi</status> go, zanim <status>ogłuszy</status> cel, co powoduje wcześniejsze zakończenie tej umiejętności i zresetowanie jej czasu odnowienia.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ minspeed*100.000000 }}% -> {{ minspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VolibearQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VolibearW", "name": "Wściekłe Okaleczenie", "description": "Volibear zadaje wrogowi obrażenia, nakładając efekty przy trafieniu i oznaczając go.  Ponowne użycie tej umiejętności na tym samym celu zada mu dodatkowe obrażenia i uleczy Volibeara.", "tooltip": "Volibear okalecza wroga swoją łapą, zada<PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> i oznaczając cel na {{ markduration }} sek.<br /><br />Jeśli ta umiejętność zostanie użyta na oznaczonym celu, jej obrażenia zostaną zwiększone do <physicalDamage>{{ empowereddamage }} pkt.</physicalDamage>, a Volibear przywróci sobie <healing>{{ baseheal }} pkt. zdrowia plus {{ percentmissinghealthhealingratio }} swojego brakującego zdrowia</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Podstawowe leczenie", "Procent <PERSON>cego zdrowia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ healpercent*100.000000 }}% -> {{ healpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "VolibearW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VolibearE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Volibear przyzywa w wybrane miej<PERSON>ce piorun, z<PERSON><PERSON><PERSON><PERSON> obrażenia, spowalniając wrogów i zapewniając sobie tarczę, jeśli znajduje się w obszarze wybuchu.", "tooltip": "Volibear przyzywa chmurę burzow<PERSON>, kt<PERSON>ra wystrzeliwu<PERSON> piorun, zada<PERSON><PERSON><PERSON> <magicDamage>{{ totaldamagetooltip }} pkt. obrażeń magicznych plus {{ percentdamage*100 }}% maksymalnego zdrowia celu</magicDamage> i <status>spowal<PERSON>jąc</status> wrog<PERSON> o {{ slowamount*100 }}% na {{ slowduration }} sek.<br /><br />Je<PERSON><PERSON> znajduje się w obszarze wybuchu, zyskuje <shield>tarc<PERSON><PERSON> o wartości {{ shieldapratiotooltip }} pkt. plus {{ shieldamount*100 }}% swojego maksymalnego zdrowia</shield> na {{ shieldduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Obrażenia procentowe"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percentdamage*100.000000 }}% -> {{ percentdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VolibearE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VolibearR", "name": "Zwiastun Burzy", "description": "Volibear doskakuje w wybrane mi<PERSON>, spowalniając wrogów znajdujących się pod nim i zadając im obrażenia, oraz zyskuje dodatkowe zdrowie.  Wrogie wieże w pobliżu miejsca jego lądowania zostają tymczasowo wyłączone.", "tooltip": "Volibear przeobraża się i skacze w wybrane miej<PERSON>, zyskując <healing>{{ healthamount }} pkt. zdrowia</healing> i zasięg zwiększony o {{ bonusattackrange }} jedn. na {{ transformduration }} sek.<br /><br />Po wylądowaniu Volibear uderza w ziemię, <status>wył<PERSON><PERSON>ając</status> wieże w pobliżu na {{ towerdisableduration }} sek. i zadając im <physicalDamage>{{ towerdamagetooltip }} pkt. obrażeń fizycznych</physicalDamage>. Na pobliskich wrogów nałożone zostaje {{ slowamount*100 }}% <status>spowolnienie</status>, zanikające w ciągu 1 sek. Wrogowie znajdujący się bezpośrednio pod Volibearem otrzymują <physicalDamage>{{ sweetspotdamagetooltip }} pkt. obraż<PERSON>ń fizycznych</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Dodatkowe zdrowie", "<PERSON>zas działania wyłączenia wieży", "Czas odnowienia"], "effect": ["{{ sweetspotdamage }} -> {{ sweetspotdamageNL }}", "{{ healthamount }} -> {{ healthamountNL }}", "{{ towerdisableduration }} -> {{ towerdisabledurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 135, 110], "cooldownBurn": "160/135/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [75, 115, 155], [9, 9, 9], [12, 12, 12], [8, 8, 8], [0.08, 0.08, 0.08], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "75/115/155", "9", "12", "8", "0.08", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "VolibearR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Bezlitosny Grom", "description": "Ataki i umiejętności Volibeara dają mu prędko<PERSON>ć ataku i ostatecznie sprawiają, że jego ataki zadają dodatkowe obrażenia magiczne pobliskim wrogom.", "image": {"full": "Volibear_Icon_P.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}