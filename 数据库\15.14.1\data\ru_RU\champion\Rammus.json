{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rammus": {"id": "<PERSON><PERSON>", "key": "33", "name": "Раммус", "title": "Броненосец", "image": {"full": "Rammus.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "33000", "num": 0, "name": "default", "chromas": false}, {"id": "33001", "num": 1, "name": "Король Раммус", "chromas": false}, {"id": "33002", "num": 2, "name": "Хромированный Раммус", "chromas": false}, {"id": "33003", "num": 3, "name": "Вулканический Раммус", "chromas": false}, {"id": "33004", "num": 4, "name": "Раммус из Фрельйорда", "chromas": false}, {"id": "33005", "num": 5, "name": "Ниндзя Раммус", "chromas": false}, {"id": "33006", "num": 6, "name": "Цельнометаллический Раммус", "chromas": false}, {"id": "33007", "num": 7, "name": "Страж песков Раммус", "chromas": false}, {"id": "33008", "num": 8, "name": "Защитник Раммус", "chromas": true}, {"id": "33016", "num": 16, "name": "Хекстековый Раммус", "chromas": false}, {"id": "33017", "num": 17, "name": "Астронавт Раммус", "chromas": true}, {"id": "33026", "num": 26, "name": "Дуриановый защитник Раммус", "chromas": true}], "lore": "Многие его боготворят, некоторые – не воспринимают всерьез, но для всех он остается тайной. Раммус – удивительное и загадочное существо с шипастым панцирем. Многие гадают о его происхождении: то ли он полубог, то ли священный оракул, то ли просто чудовище, созданное чьим-то волшебством. Какова бы ни была истина, Раммус держит ее при себе. И в скитаниях по пустыне его никому не остановить.", "blurb": "Многие его боготворят, некоторые – не воспринимают всерьез, но для всех он остается тайной. Раммус – удивительное и загадочное существо с шипастым панцирем. Многие гадают о его происхождении: то ли он полубог, то ли священный оракул, то ли просто...", "allytips": ["Катящийся шар можно использовать как эффективное средство для побега.", "Использование насмешки над чемпионом под вашей башней может стать причиной атаки башней врага.", "Дрожь земли и Защитную позу можно использовать в конце игры для уничтожения башен. Если вы погрязли в командной борьбе, важно вломиться и атаковать здания."], "enemytips": ["Обратите особое внимание, когда закончит свое действие Защитная поза. Раммус намного слабее обычных танков вне этого состояния.", "Раммус часто собирает большое количество брони, из-за чего становится особо уязвимым для магических атак вне состояния Защитной позы."], "tags": ["Tank"], "partype": "Мана", "info": {"attack": 4, "defense": 10, "magic": 5, "difficulty": 5}, "stats": {"hp": 675, "hpperlevel": 100, "mp": 310, "mpperlevel": 33, "movespeed": 335, "armor": 35, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.55, "mpregen": 7.85, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.215, "attackspeed": 0.7}, "spells": [{"id": "PowerBall", "name": "Катящийся шар", "description": "Раммус сворачивается в клубок и катится на противников, нанося урон и замедляя всех окружающих врагов после столкновения.", "tooltip": "Раммус сворачивается в клубок и получает прибавку к <speed>скорости передвижения в размере {{ minimummovespeed }}</speed>, которая увеличивается до <speed>{{ maximummovespeed }}</speed> в течение {{ rollduration }} сек. При столкновении с врагом Раммус останавливается и наносит <magicDamage>{{ powerballdamage }} ед. магического урона</magicDamage>, а также <status>отбрасывает</status> и <status>замедляет</status> противников поблизости на {{ slowpercent }}% на {{ slowduration }} сек.<br /><br /><recast>Повторное применение</recast>: действие этого умения прекращается преждевременно.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Перезарядка"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1000, 1000, 1000, 1000, 1000]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerBall.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "DefensiveBall<PERSON>url", "name": "Защитная поза", "description": "Раммус встает в защитную стойку, значительно увеличивая свою броню и сопротивление магии, и возвращает урон врагам, которые применяют к нему автоатаки.", "tooltip": "Раммус встает в защитную стойку на {{ buffduration }} сек., получая <scaleArmor>{{ bonusarmortooltip }} брони</scaleArmor> и <scaleMR>{{ bonusmrtooltip }} сопротивления магии</scaleMR>, а также наносит <magicDamage>{{ returndamagecalc }} ед. магического урона</magicDamage> врагам, которые атакуют его.<br /><br /><recast>Повторное применение</recast>: действие этого умения прекращается преждевременно.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовое увеличение брони", "Увеличение сопротивления магии", "Процент увеличения брони", "Процент увеличения сопротивления магии"], "effect": ["{{ flatbonusarmor }} -> {{ flatbonusarmorNL }}", "{{ flatbonusmr }} -> {{ flatbonusmrNL }}", "{{ bonusarmorpercent*100.000000 }}% -> {{ bonusarmorpercentnl*100.000000 }}%", "{{ bonusmrpercent*100.000000 }}% -> {{ bonusmrpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DefensiveBallCurl.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "PuncturingTaunt", "name": "Неистовая колкость", "description": "Раммус насмехается над вражеским чемпионом или нейтральным монстром, в результате чего тот безрассудно атакует Раммуса.", "tooltip": "Раммус <status>провоцирует</status> вражеского чемпиона или монстра в течение {{ duration }} сек. Монстры получают <magicDamage>{{ monsterdamagecalc }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Продолжительность", "Урон монстрам"], "effect": ["{{ duration }} -> {{ durationNL }}", "{{ monsterdamage }} -> {{ monsterdamageNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "PuncturingTaunt.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "Tremors2", "name": "Мощное приземление", "description": "Раммус подлетает в воздух, а затем приземляется в выбранном месте, нанося пораженным врагам магический урон и замедляя их. При применении во время действия Катящегося шара Раммус также подбрасывает врагов в центре области поражения.", "tooltip": "Раммус подлетает в воздух, а затем приземляется в выбранном месте, нанося пораженным врагам <magicDamage>{{ initialdamagecalc }} ед. магического урона</magicDamage> и <status>замедляя</status> их на {{ slowamount*100 }}% на {{ slowduration }} сек. Если это умение применяется во время действия <spellName>Катящегося шара</spellName>, враги в центре области дополнительно получают <magicDamage>{{ spell.powerball:powerballdamage }} ед. магического урона</magicDamage> и <status>подбрасываются</status> на {{ knockupduration }} сек.<br /><br />После приземления Раммус создает в области поражения ударные волны ({{ numberofpulses }}) в течение {{ buffduration }} сек., которые <status>замедляют</status> цели повторно.<br /><br />Дальность этого умения зависит от <speed>скорости передвижения</speed> Раммуса.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Перезарядка"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "Tremors2.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Шипастый панцирь", "description": "Раммус получает дополнительную силу атаки, которая зависит от брони и сопротивления магии.", "image": {"full": "Armordillo_ScavengeArmor.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}