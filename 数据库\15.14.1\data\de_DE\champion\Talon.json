{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Talon": {"id": "Talon", "key": "91", "name": "Talon", "title": "der Klingenschatten", "image": {"full": "Talon.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "91000", "num": 0, "name": "default", "chromas": false}, {"id": "91001", "num": 1, "name": "Renegaten-Talon", "chromas": false}, {"id": "91002", "num": 2, "name": "Purpurelite-Talon", "chromas": false}, {"id": "91003", "num": 3, "name": "Drachenklingen-Talon", "chromas": true}, {"id": "91004", "num": 4, "name": "SSW-Talon", "chromas": false}, {"id": "91005", "num": 5, "name": "Blutmond-Talon", "chromas": false}, {"id": "91012", "num": 12, "name": "Beständiges Schwert Talon", "chromas": true}, {"id": "91020", "num": 20, "name": "Talon <PERSON>", "chromas": true}, {"id": "91029", "num": 29, "name": "Verblühte Rose Talon", "chromas": true}, {"id": "91038", "num": 38, "name": "High Noon-Talon", "chromas": true}, {"id": "91039", "num": 39, "name": "High Noon-Talon (Prestige)", "chromas": false}, {"id": "91049", "num": 49, "name": "Krallen des Tigers-Talon", "chromas": true}, {"id": "91059", "num": 59, "name": "Große Abrechnung-Talon", "chromas": false}], "lore": "Talon ist das Messer in der Dunkelheit, ein erbarmungsloser Killer, der ohne Vorwarnung zuschlägt und ohne Aufsehen zu erregen wieder verschwindet. Er hat sich in den brutalen Straßen von Noxus einen gefährlichen Ruf erworben, denn er war gezwungen, für sein Überleben zu kämpfen, zu töten und zu stehlen. Er wurde von der berüchtigten Familie Du Couteau adoptiert, betreibt nun sein tödliches Handwerk auf Befehl des Reichs und tötet gegnerische Anführer, Hauptmänner und Helden … sowie jeden <PERSON>, der töricht genug ist, die Verachtung seines Meisters auf sich zu ziehen.", "blurb": "Talon ist das Messer in der Dunkelheit, ein erbarmungsloser Killer, der ohne Vorwarnung zuschlägt und ohne Aufsehen zu erregen wieder verschwindet. Er hat sich in den brutalen Straßen von Noxus einen gefährlichen Ruf erworben, denn er war gezwungen, für...", "allytips": ["Verwende „Weg des Assassinen“, um dich hinter deinem Gegner in Position zu bringen, bevor du mit dem Nahkampfangriff von „Noxianische Diplomatie“ zuschlägst.", "„Schattenangriff“ ist ein mächtiges Fluchtwerkzeug, kann aber auch beim Angriff auf eine Gruppe eingesetzt werden.", "<PERSON><PERSON>, dir <PERSON><PERSON><PERSON> vor einem Kamp<PERSON>. <PERSON><PERSON> kann von V<PERSON> sein, alle von Talons Fähigkeiten auf ein einzelnes Ziel zu konzentrieren, sie auf<PERSON><PERSON><PERSON>n ist jedoch selten sinnvoll."], "enemytips": ["Talon setzt nur auf normalen Schaden. Konzentriere dich früh auf Rüstung, um seinen Schaden zu senken.", "Talon verlässt sich sehr auf seinen „Schattenangriff“, um aus einem Kampf zu entkommen. Ohne ihn ist er deutlich verwundbarer.", "<PERSON> kann besser als jeder andere auf der Karte umherstreifen. <PERSON><PERSON> sic<PERSON>, dass du weißt, wo er ist, oder zwinge ihn durch Druck in der Lane zurück auf seine Position."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 658, "hpperlevel": 109, "mp": 400, "mpperlevel": 37, "movespeed": 335, "armor": 30, "armorperlevel": 4.7, "spellblock": 36, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 7.6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.9, "attackspeed": 0.625}, "spells": [{"id": "TalonQ", "name": "Noxianische Diplomatie", "description": "Talon sticht auf die Zieleinheit ein. Wenn sie in Nahkampfreichweite ist, fügt der Angriff kritischen Schaden zu. Wenn sie außerhalb seiner Nahkampfreichweite ist, springt <PERSON> zu seinem Ziel und sticht dann zu. Talon erhält etwas Leben und Abklingzeit zurück, wenn diese Fähigkeit ein Ziel tötet.", "tooltip": "Talon springt auf ein Z<PERSON> und fügt ihm <physicalDamage>{{ leapdamage }}&nbsp;normalen Schaden</physicalDamage> zu. Wenn diese Fähigkeit in Nahkampfreichweite eingesetzt wird, trifft sie stattdessen kritisch und verursacht <physicalDamage>{{ criticaldamage }}&nbsp;normalen Schaden</physicalDamage>.<br /><br />Wenn diese Fähigkeit das Ziel tötet, stellt Talon <healing>{{ totalhealing }}&nbsp;Leben</healing> wieder her und {{ cooldownrefund*100 }}&nbsp;% seiner Abklingzeit werden zurückerstattet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "TalonQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TalonW", "name": "Klingensalve", "description": "<PERSON> schleudert eine Reihe Kling<PERSON>, die anschließend zu ihm zurückkehren. <PERSON><PERSON>, wenn eine der Klingen einen Gegner durch<PERSON>t, erleidet dieser normalen Schaden. Die zurückkehrenden Klingen verursachen zusätzlichen Schaden und verlangsamen getroffene Einheiten.", "tooltip": "<PERSON> schleudert eine Salve aus Klingen, die <physicalDamage>{{ totalinitialdamage }}&nbsp;normalen Schaden</physicalDamage> verursachen. Dann kehren die Klingen zu ihm zurück, verursachen <physicalDamage>{{ totalreturndamage }}&nbsp;normalen Schaden</physicalDamage> und <status>verlangsamen</status> {{ slowduration }}&nbsp;Sekunde(n) lang um {{ movespeedslow*100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "Anfänglicher Schaden", "<PERSON><PERSON><PERSON> (Rückweg)", "Verlangsamung", "Abklingzeit"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ initialbasedamage }} -> {{ initialbasedamageNL }}", "{{ returnbasedamage }} -> {{ returnbasedamageNL }}", "{{ movespeedslow*100.000000 }}&nbsp;% -> {{ movespeedslownl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TalonW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TalonE", "name": "<PERSON>g des Assassinen", "description": "Talon springt über Terrain und Gebäude (sofern die Distanz nicht zu groß ist). Die Fähigkeit selbst hat eine niedrige Abklingzeit, das Terrain hingegen eine hohe.", "tooltip": "Talon springt über das nächstgelegene Terrain oder Gebäude. Talon kann nicht mehr als einmal alle {{ wallcd }}&nbsp;Sekunden über das gleiche Terrain springen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit des Terrains"], "effect": ["{{ wallcd }} -> {{ wallcdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [625, 625, 625, 625, 625], [1250, 1250, 1250, 1250, 1250], [2, 2, 2, 2, 2], [160, 135, 110, 85, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "625", "1250", "2", "160/135/110/85/60", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "TalonE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "TalonR", "name": "Schattenangriff", "description": "<PERSON> schleudert einen Ring aus Klingen, wird unsichtbar und erhält zusätzliches Lauftempo. Wenn Talon wieder sichtbar wird, treffen die Klingen an seiner Position aufeinander. Immer wenn die Klingen sich bewegen, verursacht „Schattenangriff“ normalen Schaden an Gegnern, die von mindestens einer Klinge getroffen wurden.", "tooltip": "Talon wird {{ duration }}&nbsp;Sekunden lang <keywordStealth>unsichtbar</keywordStealth>, erhält <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed> und schleudert einen Ring aus Klingen, die <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> verursachen. Wenn die <keywordStealth>Unsichtbarkeit</keywordStealth> endet, kehren die Klingen zu Talon zurück und verursachen erneut <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage>.<br /><br /><PERSON><PERSON> <PERSON> die <keywordStealth>Unsichtbarkeit</keywordStealth> mit einem Angriff oder mit <spellName>Noxianische Diplomatie</spellName> abbricht, kehren die Klingen stattdessen zum Ziel zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Lauftempo", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed*100.000000 }}&nbsp;% -> {{ movespeednl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TalonR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Klingenspitze", "description": "Talons Fähigkeiten fügen Champions und großen Monstern Wunden zu, die sich bis zu 3 Mal steigern. <PERSON>n Talon einen Champion mit 3 Wunden angreift, erle<PERSON><PERSON> dieser eine Blutung, die schweren Schaden über Zeit verursacht.", "image": {"full": "TalonP.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}