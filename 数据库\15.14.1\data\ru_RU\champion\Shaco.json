{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shaco": {"id": "Shaco", "key": "35", "name": "Шако", "title": "Демонический шут", "image": {"full": "Shaco.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "35000", "num": 0, "name": "default", "chromas": false}, {"id": "35001", "num": 1, "name": "Безумный шляпник Шако", "chromas": false}, {"id": "35002", "num": 2, "name": "Королевский Шако", "chromas": false}, {"id": "35003", "num": 3, "name": "Шаку<PERSON>чик", "chromas": false}, {"id": "35004", "num": 4, "name": "Механический Шако", "chromas": false}, {"id": "35005", "num": 5, "name": "Психопат Шако", "chromas": false}, {"id": "35006", "num": 6, "name": "Шако в маске", "chromas": false}, {"id": "35007", "num": 7, "name": "Д<PERSON><PERSON><PERSON><PERSON><PERSON> Шако", "chromas": false}, {"id": "35008", "num": 8, "name": "Шако Темная Звезда", "chromas": true}, {"id": "35015", "num": 15, "name": "Адепт тайной магии Шако", "chromas": true}, {"id": "35023", "num": 23, "name": "Городской кошмар Шако", "chromas": true}, {"id": "35033", "num": 33, "name": "Избранник зимы Шако", "chromas": true}, {"id": "35043", "num": 43, "name": "Боевая душа Шако", "chromas": true}, {"id": "35044", "num": 44, "name": "Боевая душа Шако (престижный)", "chromas": false}, {"id": "35054", "num": 54, "name": "Шако из Мира ужасов", "chromas": true}, {"id": "35064", "num": 64, "name": "Кот в коробочке Шако", "chromas": true}], "lore": "Зачарованная марионетка Шако была создана, чтобы служить игрушкой для одинокого принца, но теперь наслаждается убийствами и хаосом. Потерявший любимого хозяина и оскверненный темной магией, этот некогда добрый клоун упивается страданиями несчастных душ, которых мучает. Шако убивает жертв с помощью игрушек и несложных фокусов, считая свои кровавые ''игры'' забавными. И если кто-то услышит глубокой ночью мрачное хихиканье – значит, скоро Демонический шут придет с ним поиграть.", "blurb": "Зачарованная марионетка Шако была создана, чтобы служить игрушкой для одинокого принца, но теперь наслаждается убийствами и хаосом. Потерявший любимого хозяина и оскверненный темной магией, этот некогда добрый клоун упивается страданиями несчастных душ...", "allytips": ["Использование Обмана через преграду поможет вам совершить чистый побег.", "Пробуйте получить предметы с эффектами при попадании. Они также действуют на вашего Галлюциногенного клона.", "Урон от Удара в спину можно увеличить предметами, повышающими критический урон, например, Гранью Бесконечности."], "enemytips": ["Если Шако показывает себя хорошо в ранней игре, то стоит потратить деньги на установку Скрытых тотемов вокруг вражеского леса.", "Если Шако использует свое умение Обман в начале боя, то он нескоро сможет использовать его снова, чтобы сбежать. Сфокусируйте силы вашей команды для того, чтобы быстро убить его."], "tags": ["Assassin"], "partype": "Мана", "info": {"attack": 8, "defense": 4, "magic": 6, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 297, "mpperlevel": 40, "movespeed": 345, "armor": 30, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.694}, "spells": [{"id": "Deceive", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Шако становится невидимым и телепортируется в указанную точку.<br><br>Его первая атака во время действия невидимости усиливается, нанося дополнительный урон. При применении со спины эта атака становится критической.", "tooltip": "Шако телепортируется и становится <keywordStealth>невидимым</keywordStealth> на {{ stealthduration }} сек. Применение умений <spellName>Шут из коробки</spellName> и <spellName>Галлюцинация</spellName> не прерывает действие <keywordStealth>невидимости</keywordStealth>.<br /><br />Следующая автоатака Шако во время действия <keywordStealth>невидимости</keywordStealth> дополнительно наносит <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>. Если цель повернута к Шако спиной, эта автоатака становится критической и наносит {{ qcritdamagemod }} урона.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Продолжительность действия невидимости", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Deceive.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "JackInTheBox", "name": "Шут из коробки", "description": "Шако устанавливает в указанном месте скрытого Шута из коробки, который пугает и затем атакует ближайших врагов.", "tooltip": "Шако создает ловушку, которая становится невидимой через {{ e5 }} сек. и существует {{ trapduration }} сек. Она активируется, когда ее раскрывают или когда к ней приближается враг. При этом она <status>пугает</status> врагов поблизости: вражеских чемпионов на {{ fearduration }} сек., а миньонов и лесных монстров на {{ minionfearduration }} сек.<br /><br />После активации ловушка стреляет во всех окружающих врагов в течение 5 сек., нанося <magicDamage>{{ aoedamage }} магического урона</magicDamage> (<magicDamage>{{ stdamage }} урона</magicDamage>, если цель одна).<br /><br />Атаки Шута из коробки дополнительно наносят <magicDamage>{{ monsterbonusdamage }}</magicDamage> урона монстрам.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Длительность страха", "Дополнительный урон монстрам", "Стоимость – @AbilityResourceName@"], "effect": ["{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ fearduration }} -> {{ feardurationNL }}", "{{ monsterbonusdamage }} -> {{ monsterbonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 65, 60, 55, 50], "costBurn": "70/65/60/55/50", "datavalues": {}, "effect": [null, [35, 50, 65, 80, 95], [200, 300, 400, 500, 600], [0.5, 0.75, 1, 1.25, 1.5], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/50/65/80/95", "200/300/400/500/600", "0.5/0.75/1/1.25/1.5", "40", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "JackInTheBox.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TwoShivPoison", "name": "Отравленные заточки", "description": "Шако отравляет врагов при автоатаках, уменьшая их скорость передвижения. При активации умения Шако мечет нож, нанося врагу урон и отравляя его. Брошенные ножи наносят дополнительный урон, если у цели осталось менее 30% здоровья.", "tooltip": "<spellPassive>Пассивно:</spellPassive> когда это умение не находится на перезарядке, автоатаки Шако <status>замедляют</status> цель на {{ slowamount*-100 }}% на {{ slowdurationpassive }} сек.<br /><br /><spellActive>Активно:</spellActive> Шако мечет заточку, которая наносит врагу <magicDamage>{{ totaldamage }} магического урона</magicDamage> и <status>замедляет</status> его на {{ slowamount*-100 }}% на {{ slowdurationactive }} сек. Если у цели осталось менее {{ executehealththreshold*100 }}% здоровья, заточка вместо этого наносит <magicDamage>{{ totalexecutedamage }} урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "TwoShivPoison.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "HallucinateFull", "name": "Галлюцинация", "description": "Шако создает рядом с собой свою иллюзию, которая может атаковать ближайших противников (наносит меньше урона башням). После смерти она взрывается, оставляя после себя три Шута из коробки и нанося урон ближайшим врагам.", "tooltip": "Шако ненадолго исчезает, после чего появляется вместе со своим клоном. Клон существует {{ clonelifetime }} сек., а после смерти взрывается, нанося врагам <magicDamage>{{ explosiontotaldamage }} магического урона</magicDamage> и оставляя после себя три маленьких <spellName>Шута из коробки</spellName>, которые срабатывают мгновенно. Клон наносит {{ cloneaadamagepercent*100 }}% от урона Шако и получает на {{ cloneincomingdamagepercent*100 }}% больше урона.<br /><br />Маленькие <spellName>Шуты из коробки</spellName> наносят <magicDamage>{{ aoedamage }} магического урона</magicDamage> (<magicDamage>{{ stdamage }} урона</magicDamage>, если враг один) и <status>пугают</status> врагов на {{ boxfearduration }} сек.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон при смерти", "Урон от коробки", "Перезарядка"], "effect": ["{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "HallucinateFull.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Удар в спину", "description": "При применении со спины автоатаки и Отравленные заточки Шако наносят дополнительный урон.", "image": {"full": "Jester_CarefulStrikes.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}