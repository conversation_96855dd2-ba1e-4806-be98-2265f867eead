{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akshan": {"id": "<PERSON><PERSON><PERSON>", "key": "166", "name": "<PERSON><PERSON><PERSON>", "title": "la Sentinella ribelle", "image": {"full": "Akshan.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "166000", "num": 0, "name": "default", "chromas": false}, {"id": "166001", "num": 1, "name": "Cyber-<PERSON>", "chromas": true}, {"id": "166010", "num": 10, "name": "<PERSON><PERSON><PERSON> Cristallo", "chromas": true}, {"id": "166020", "num": 20, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "<PERSON><PERSON><PERSON><PERSON><PERSON> del pericolo, <PERSON><PERSON><PERSON> lotta contro il male con carisma e sete di vendetta, rigorosamente a petto nudo. È abilissimo nell'arte del combattimento furtivo, evita gli sguardi nemici e riappare all'improvviso. Ha uno spiccato senso della giustizia e una leggendaria arma capace di sconfiggere la morte. <PERSON><PERSON><PERSON> raddrizza i torti di Runeterra vivendo secondo il suo codice morale: \"Non fare il bastardo.''", "blurb": "<PERSON><PERSON><PERSON><PERSON><PERSON> del pericolo, <PERSON><PERSON><PERSON> lotta contro il male con carisma e sete di vendetta, rigorosamente a petto nudo. È abilissimo nell'arte del combattimento furtivo, evita gli sguardi nemici e riappare all'improvviso. Ha uno spiccato senso della giustizia...", "allytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON> del pericolo, <PERSON><PERSON><PERSON> lotta contro il male con carisma e sete di vendetta, rigorosamente a petto nudo. È abilissimo nell'arte del combattimento furtivo, evita gli sguardi nemici e riappare all'improvviso. Ha uno spiccato senso della giustizia e una leggendaria arma capace di sconfiggere la morte. <PERSON><PERSON><PERSON> raddrizza i torti di Runeterra vivendo secondo il suo codice morale: \"Non fare il bastardo.''"], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "Vendettarang", "description": "<PERSON><PERSON><PERSON> scaglia un boomerang che infligge danni all'andata e al ritorno, aumentando la sua gittata ogni volta che colpisce un nemico.", "tooltip": "<PERSON><PERSON><PERSON> scaglia un boomerang che infligge <physicalDamage>{{ finaldamage }} danni fisici</physicalDamage>, aumentando la sua gittata ogni volta che colpisce un nemico.<br /><br />I colpi ai campioni forniscono ad <PERSON>han <speed>{{ totalhaste }} velocità di movimento</speed> che decresce nell'arco di {{ hasteduration }} secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON>ni minion %", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ secondarytargetdamage*100.000000 }}% -> {{ secondarytargetdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.25, 6.5, 5.75, 5], "cooldownBurn": "8/7.25/6.5/5.75/5", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "AkshanQ.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanW", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON>han marchia passivamente i campioni nemici come Canaglie quando uccidono i campioni alleati. Quando Akshan uccide una Canaglia, resuscita gli alleati uccisi, ottiene oro bonus e rimuove tutti i marchi.<br><br>Quando questa abilità viene attivata, <PERSON><PERSON><PERSON> entra in mimesi e ottiene velocità di movimento e rigenerazione Mana mentre si muove verso le Canaglie. Akshan perde rapidamente la mimesi mentre non è nell'erba alta o vicino ai muri.", "tooltip": "{{ Spell_AkshanW_Tooltip_{{ gamemodeinteger }} }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "Velocità di movimento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ msvalue }} -> {{ msvalueNL }}"]}, "maxrank": 5, "cooldown": [18, 14, 10, 6, 2], "cooldownBurn": "18/14/10/6/2", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "AkshanW.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanE", "name": "Intervento eroico", "description": "<PERSON><PERSON><PERSON> scaglia un rampino sul terreno, poi volteggia attorno ad esso mentre spara ripetutamente al nemico più vicino. La rotazione può essere fermata in anticipo, e si interrompe automaticamente se <PERSON><PERSON><PERSON> colpisce un campione o il terreno.", "tooltip": "<spellActive>Primo lancio:</spellActive> A<PERSON>han spara un rampino che si aggancia al primo muro colpito.<br /><br /><spellActive>Secondo lancio:</spellActive> <PERSON><PERSON>han ruota attorno al muro, sparando ripetutamente al nemico più vicino e infliggendogli <physicalDamage>{{ asmoddamagetodeal }} danni fisici</physicalDamage> per colpo.<br /><br /><spellActive>Terzo lancio:</spellActive> A<PERSON>han lascia la fune e sferra un ultimo attacco.<br /><br />Se si scontra con un campione nemico o con il terreno, la rotazione termina in anticipo.<br /><br />L'eliminazione di campioni nemici riduce la ricarica dell'abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AkshanE.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanR", "name": "Condanna inesorabile", "description": "<PERSON><PERSON><PERSON> aggancia un campione nemico e inizia ad accumulare proiettili. <PERSON>uan<PERSON> rila<PERSON>, spara tutti i proiettili accumulati, infliggendo danni in base alla salute mancante al primo campione, minion o struttura che colpisce.", "tooltip": "<PERSON><PERSON><PERSON> aggancia un campione e inizia a sovraccaricare la sua arma per {{ channelduration }} secondi, accumulando fino a {{ numberofbullets }} proiettili.<br /><br /><recast>R<PERSON><PERSON><PERSON>:</recast> <PERSON><PERSON><PERSON> spara tutti i proiettili accumulati, ciascuno dei quali infligge almeno <physicalDamage>{{ damageperbulletwithcrit }} danni fisici</physicalDamage> al primo nemico o alla prima struttura colpiti, aumentati a <physicalDamage>{{ maxdamageperbullet }} danni fisici</physicalDamage> in base alla loro salute mancante.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON><PERSON><PERSON> massimi", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ numberofbullets }} -> {{ numberofbulletsNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "AkshanR.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Combattimento sporco", "description": "Ogni tre colpi con attacchi base e abilità, <PERSON><PERSON><PERSON> infligge danni e guadagna uno scudo se il bersaglio era un campione.<br><br><PERSON><PERSON><PERSON> <PERSON><PERSON> attacca, spara un attacco aggiuntivo che infligge danni ridotti. Se interrompe l'attacco aggiuntivo, ottiene invece velocità di movimento.", "image": {"full": "akshan_p.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}