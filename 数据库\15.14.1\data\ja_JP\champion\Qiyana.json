{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Qiyana": {"id": "<PERSON><PERSON>", "key": "246", "name": "キヤナ", "title": "エレメントの女帝", "image": {"full": "Qiyana.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "246000", "num": 0, "name": "default", "chromas": false}, {"id": "246001", "num": 1, "name": "バトルボス キヤナ", "chromas": true}, {"id": "246002", "num": 2, "name": "True Damage キヤナ", "chromas": false}, {"id": "246010", "num": 10, "name": "プレステージ True Damage キヤナ", "chromas": false}, {"id": "246011", "num": 11, "name": "バトルクイーン キヤナ", "chromas": true}, {"id": "246020", "num": 20, "name": "雷刃キヤナ", "chromas": true}, {"id": "246021", "num": 21, "name": "プレステージ True Damage キヤナ(2022)", "chromas": false}, {"id": "246030", "num": 30, "name": "月の女帝キヤナ", "chromas": true}, {"id": "246040", "num": 40, "name": "ラ・イルシオン キヤナ", "chromas": true}, {"id": "246050", "num": 50, "name": "プレステージ バトルアカデミア キヤナ", "chromas": false}], "lore": "ジャングルに囲まれた都市イシャオカンで、キヤナは玉座ユン・タルを目指して無情な策略を企てている。両親の王位を継承する権利からは遠い末娘として生まれたキヤナだが、過剰なまでの自信とエレメント魔法の無類の力をもって、己の道を邪魔する者に立ち向かう。意のままに大地を操ることができる彼女は、イシャオカン史上最高のエレメント魔法の使い手であると自負している──そしてそんな自分はひとつの都市のみならず、帝国を支配するにふさわしい者であると。", "blurb": "ジャングルに囲まれた都市イシャオカンで、キヤナは玉座ユン・タルを目指して無情な策略を企てている。両親の王位を継承する権利からは遠い末娘として生まれたキヤナだが、過剰なまでの自信とエレメント魔法の無類の力をもって、己の道を邪魔する者に立ち向かう。意のままに大地を操ることができる彼女は、イシャオカン史上最高のエレメント魔法の使い手であると自負している──そしてそんな自分はひとつの都市のみならず、帝国を支配するにふさわしい者であると。", "allytips": [], "enemytips": [], "tags": ["Assassin"], "partype": "マナ", "info": {"attack": 0, "defense": 2, "magic": 4, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 124, "mp": 375, "mpperlevel": 60, "movespeed": 335, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.1, "attackspeed": 0.688}, "spells": [{"id": "QiyanaQ", "name": "エレメントの怒り/イシュタルの切先", "description": "武器を振り、保持しているエレメントに応じた追加効果の付いたダメージを与える。", "tooltip": "<keywordMajor>「エンチャントメント」</keywordMajor>を保有していない場合は斬撃を繰り出し、狭い範囲内の敵に<physicalDamage>{{ vanilladamage }}の物理ダメージ</physicalDamage>を与える。保有している場合は、このスキルの射程が増加して、<keywordMajor>「エンチャントメント」</keywordMajor>の種類に応じた追加効果を獲得する:<li><keywordMajor>氷のエンチャントメント</keywordMajor>: 短時間の<status>スネア効果</status>を付与し、その後{{ slowduration }}秒間、{{ slowpotency*-100 }}%の<status>スロウ効果</status>を付与する。<li><keywordMajor>岩のエンチャントメント</keywordMajor>: 体力が{{ critthreshold*100 }}%未満のユニットに<physicalDamage>{{ tremordamage }}の物理ダメージ</physicalDamage>を追加で与える。<li><keywordMajor>草木のエンチャントメント</keywordMajor>: 自身を<keywordStealth>インビジブル</keywordStealth>状態にして<speed>移動速度を{{ haste*100 }}%</speed>増加させる道が発生する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ vanillabase }} -> {{ vanillabaseNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "QiyanaQ.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "QiyanaW", "name": "大地の力", "description": "目標地点までダッシュし、武器にエレメントの力を付与する。武器にエレメントの力がついている間は、通常攻撃とスキルに追加ダメージが付与される。", "tooltip": "<spellPassive>自動効果:</spellPassive> 武器が<keywordMajor>「エンチャントメント」</keywordMajor>を獲得していると、<attackSpeed>攻撃速度が{{ attackspeed*100 }}%</attackSpeed>増加し、通常攻撃が<magicDamage>{{ onhitdamage }}の魔法ダメージ</magicDamage>を追加で与える。また非戦闘時、近くに<keywordMajor>「エンチャントメント」</keywordMajor>に使ったものと同種の地形が存在する場合は、<speed>移動速度が{{ passivems*100 }}%</speed>増加する。<br /><br /><spellActive>発動効果:</spellActive> 近くの茂み、壁、川までダッシュして、それに対応した<keywordMajor>「エンチャントメント」</keywordMajor>を武器に付与する。これを行うと<spellName>「エレメントの怒り/イシュタルの切先」</spellName>のクールダウンが解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["通常攻撃時ダメージ", "移動速度", "増加攻撃速度", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ passivems*100.000000 }}% -> {{ passivemsnl*100.000000 }}%", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "QiyanaW.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "QiyanaE", "name": "俊烈", "description": "敵に向かってダッシュし、ダメージを与える。", "tooltip": "敵を通り過ぎるようにダッシュし、<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "クールダウン", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "QiyanaE.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "QiyanaR", "name": "天賦絢爛", "description": "エレメントに命中すると爆発する衝撃波を放ち、周囲の敵をスタンさせながらダメージを与える。", "tooltip": "衝撃波を発生させる。衝撃波は敵を<status>ノックバック</status>させて、地形に当たると爆発する。爆発はその地形全体に沿って進み、0.5 - {{ stunduration }}秒間の<status>スタン効果</status>と<physicalDamage>{{ damage }}</physicalDamage><physicalDamage>(+最大体力の{{ missinghealthdamagerock }})の物理ダメージ</physicalDamage>を与える。<status>スタン効果</status>の時間は、爆発の移動距離に応じて低下する。<br /><br />少ししてから、衝撃波が通った場所にあった川と茂みも爆発し、同じダメージと<status>スタン効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "QiyanaR.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "女帝の威風", "description": "それぞれの敵に対する最初の通常攻撃またはスキルに追加ダメージが付与される。", "image": {"full": "Qiyana_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}