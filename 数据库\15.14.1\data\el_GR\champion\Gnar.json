{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gnar": {"id": "<PERSON><PERSON>", "key": "150", "name": "Γκν<PERSON>ρ", "title": "ο <PERSON><PERSON><PERSON><PERSON>νος <PERSON>", "image": {"full": "Gnar.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "150000", "num": 0, "name": "default", "chromas": false}, {"id": "150001", "num": 1, "name": "Γκν<PERSON>ρ<PERSON>σ<PERSON><PERSON><PERSON>ος", "chromas": true}, {"id": "150002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "150003", "num": 3, "name": "Χειμων<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "150004", "num": 4, "name": "Ελ Λεόν Γκνα<PERSON>", "chromas": false}, {"id": "150013", "num": 13, "name": "Υπεργαλαξιακός Γκναρ", "chromas": false}, {"id": "150014", "num": 14, "name": "SSG Γκναρ", "chromas": false}, {"id": "150015", "num": 15, "name": "Αστρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "150022", "num": 22, "name": "Γκναρ του Αρχαίου Δάσους", "chromas": true}, {"id": "150031", "num": 31, "name": "Γκν<PERSON>ρ η Οφθαλμαπάτη", "chromas": true}], "lore": "Ο Γκναρ είναι ένα πρωτόγονο Γιορντλ που συμπεριφέρεται σαν μωρό παιδί και μπορεί να βάλει τα κλάματα με μηδαμινή αφορμή. Η διαφορά είναι ότι, όταν συμβαίνει αυτό, μεταμορφώνεται σε ένα πελώριο κτήνος που καταστρέφει τα πάντα στο πέρασμά του. Αυτό το περίεργο πλάσμα έμεινε παγιδευμένο σε Αληθινό Πάγο για ολόκληρες χιλιετίες, αλλά τώρα κατάφερε να δραπετεύσει και είναι ελεύθερο να χοροπηδήσει όπου θέλει σε έναν σύγχρονο κόσμο, που στα μάτια του φαντάζει εξωτικός και γεμάτος θαύματα. Ο Γκναρ ενθουσιάζεται με κάθε νέα απειλή που ανακαλύπτει και πετάει ό,τι βρει στους αντιπάλους του, από το κοκάλινο μπούμερανγκ που έχει πάντα επάνω του, μέχρι το πλησιέστερο κτίριο.", "blurb": "Ο Γκναρ είναι ένα πρωτόγονο Γιορντλ που συμπεριφέρεται σαν μωρό παιδί και μπορεί να βάλει τα κλάματα με μηδαμινή αφορμή. Η διαφορά είναι ότι, όταν συμβαίνει αυτό, μεταμορφώνεται σε ένα πελώριο κτήνος που καταστρέφει τα πάντα στο πέρασμά του. Αυτό το...", "allytips": ["Η διαχείριση της Οργής σας είναι πολύ σημαντική. Προσπαθήστε να διατηρήσετε τις μεταμορφώσεις σας μέχρι τη λήξη της διάρκειάς τους, για να επωφελείστε στο έπακρο και από τις δύο μορφές.", "Τοποθετηθείτε κοντά σε τοίχους, ώστε να προσελκύσετε τους αντιπάλους σας και να τους ακινητοποιήσετε με την Υπέρτατη ικανότητά σας.", "Γνωρίστε τις δυνάμεις σας! Ο Μίνι-Γκναρ είναι γρήγορος, ευάλωτος και προκαλεί υψηλή ζημιά με μεγάλη διάρκεια. Ο Μέγα-Γκναρ είναι αργός και σκληρός και προκαλεί υψηλή ζημιά ανά χτύπημα."], "enemytips": ["Ο Γκναρ δεν μπορεί να αποκτήσει Οργή για 15 δευτερόλεπτα, αφού μεταμορφωθεί από μεγάλο σε μικρό. Εκμεταλλευτείτε αυτήν την ευκαιρία για να επιτεθείτε στην ομάδα του.", "Τα γραφικά και η γραμμή πόρων του Γκναρ αλλάζουν καθώς πλησιάζει η μεταμόρφωσή του. ", "Η υπέρτατη ικανότητα του Γκναρ σας ακινητοποιεί, αν σας ρίξει πάνω σε τοίχο. Προσέξτε όταν παλεύετε μαζί του και βρίσκεστε κοντά σε τοίχο."], "tags": ["Fighter", "Tank"], "partype": "Οργή", "info": {"attack": 6, "defense": 5, "magic": 5, "difficulty": 8}, "stats": {"hp": 540, "hpperlevel": 79, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 32, "armorperlevel": 3.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 175, "hpregen": 4.5, "hpregenperlevel": 1.25, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.2, "attackspeedperlevel": 6, "attackspeed": 0.625}, "spells": [{"id": "GnarQ", "name": "Ρίψη Μπούμερανγκ / Ρίψη Βράχου", "description": "Ο Γκναρ πετάει ένα μπούμερανγκ που προκαλεί ζημιά και επιβραδύνει τους εχθρούς που πετυχαίνει, προτού επιστρέψει και πάλι σε αυτόν. Αν πιάσει το μπούμερανγκ, ο Χρόνος Επαναφόρτισής του μειώνεται.<br><br>Ο Μεγα-Γκναρ πετάει βράχους που σταματούν μόλις χτυπήσουν την πρώτη μονάδα που βρίσκουν, προκαλώντας ζημιά και επιβράδυνση σε οτιδήποτε βρίσκεται στη γύρω περιοχή. Αν μαζέψει τον βράχο μειώνεται ο Χρόνος Επαναφόρτισης.", "tooltip": "<keywordMajor>Μινι-Γκ<PERSON>αρ:</keywordMajor> Ο Γκναρ πετάει ένα μπούμερανγκ που προκαλεί <physicalDamage>{{ spell.gnarq:minitotaldamage }} Σωματική Ζημιά</physicalDamage> και <status>Επιβραδύνει</status> τους εχθρούς κατά {{ spell.gnarq:slowamount*100 }}% για {{ spell.gnarq:slowduration }} δευτ. Αφού χτυπήσει έναν εχθρό, το μπούμερανγκ επιστρέφει, προκαλώντας μειωμένη ζημιά στους στόχους που θα πετύχει στη συνέχεια. Κάθε εχθρός μπορεί να χτυπηθεί μόνο μία φορά. Αν πιάσει το μπούμερανγκ, ο Χρόνος Επαναφόρτισής του μειώνεται κατά {{ spell.gnarq:minicdrefund*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά Μπούμερανγκ", "Ζημιά Βράχου", "Επιβράδυνση", "Επιβράδυνση από Ρίψη Βράχου", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ megabasedamage }} -> {{ megabasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ megaslowamount*100.000000 }}% -> {{ megaslowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 17.5, 15, 12.5, 10], "cooldownBurn": "20/17.5/15/12.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "GnarQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "GnarW", "name": "Ενθουσιασμός / Βαρύ Χτύπημα", "description": "Οι επιθέσεις και τα ξόρκια τονώνουν τον Γκναρ, προ<PERSON><PERSON>λώντας επιπλέον ζημιά και δίνοντάς του μεγαλύτερη Ταχύτητα Κίνησης.<br><br>Ο Μέγα-Γκναρ είναι πολύ οργισμένος και δεν διαθέτει μεγάλη ευκινησία. Όμως μπορεί να ανασηκωθεί στα πίσω πόδια του και να συνθλίψει ολόκληρη την περιοχή που βρίσκεται μπροστά του, ακινητοποιώντας του αντιπάλους του.", "tooltip": "<keywordMajor>Παθητική του Μινι-Γκναρ:</keywordMajor> Κάθε τρίτη επίθεση ή Ικανότητα στον ίδιο εχθρό προκαλεί επιπλέον <magicDamage>{{ spell.gnarw:minitotaldamage }} συν Μαγική Ζημιά ίση με το {{ spell.gnarw:minipercenthpdamage*100 }}% της μέγιστης Ζωής</magicDamage> και δίνει <speed>{{ spell.gnarr:rhypermovementspeedpercent }}% Ταχύτητα Κίνησης</speed> που μειώνεται μέσα σε {{ minihasteduration }} δευτερόλεπτα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά Ενθουσιασμού", "% Ζημιάς Ενθουσιασμού επί της Ζωής", "Ζημιά Βαριού Χτυπήματος"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ minipercenthpdamage*100.000000 }}% -> {{ minipercenthpdamagenl*100.000000 }}%", "{{ megabasedamage }} -> {{ megabasedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GnarW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "GnarE", "name": "Πηδηματάκι / Λιάνισμα", "description": "Ο Γκναρ κάνει άλμα σε μια περιοχή και αναπηδά πάνω στον επικεφαλής οποιασδήποτε μονάδας στην οποία προσγειώνεται, για να βρεθεί ακόμα πιο μακριά.<br><br> Ο Μεγα-Γκναρ είναι πολύ μεγάλος για να αναπηδήσει, οπότε προσγειώνεται με σεισμική ισχύ και προκαλεί ζημιά στη γύρω περιοχή.", "tooltip": "<keywordMajor>Μινι-Γκναρ:</keywordMajor> Ο Γκναρ πηδά και αποκτά <attackSpeed>{{ spell.gnare:minibas*100 }}% Ταχύτητα Επίθεσης</attackSpeed> για {{ spell.gnare:miniasduration }} δευτ. Αν ο Γκναρ προσγειωθεί επάνω σε μονάδα, θα αναπηδήσει και θα φτάσει πιο μακριά. Η αναπήδηση πάνω σε έναν αντίπαλο προκαλεί <physicalDamage>{{ spell.gnare:minitotaldamage }} Σωματική Ζημιά</physicalDamage> και <status>Επιβράδυνση</status> για λίγο κατά {{ spell.gnare:movespeedmod*-100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά από Αλματάκι", "Ζημιά από Λιάνισμα", "Μπόν<PERSON>υς Ταχύτητα Επίθεσης", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ minidamage }} -> {{ minidamageNL }}", "{{ megadamage }} -> {{ megadamageNL }}", "{{ minibas*100.000000 }}% -> {{ minibasnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "GnarE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "GnarR", "name": "ΓΚΝΑΡ!", "description": "Ο Μέγα-Γ<PERSON><PERSON><PERSON><PERSON> πετάει οτιδήποτε βρεθεί γύρω του προς μια επιλεγμένη κατεύθυνση, προκαλώντας ζημιά και επιβράδυνση.  Οι αντίπαλοι που πέφτουν επάνω σε τοίχους ακινητοποιούνται και δέχονται επιπλέον ζημιά.", "tooltip": "<keywordMajor>Παθητική του Μινι-Γκναρ:</keywordMajor> Αυξάνει την <speed>Ταχύτητα Κίνησης</speed> του <spellName>Ενθουσιασμού</spellName>.<br /><br /><keywordMajor>Μεγα-Γκναρ:</keywordMajor> Ο Γκναρ απωθεί τους κοντινούς εχθρούς, προκαλώντας <physicalDamage>{{ damage }} Σωματική Ζημιά</physicalDamage>, <status>Απωθώντας</status> και <status>Επιβραδύνοντάς</status> τους κατά {{ rslowpercent }}% για {{ rccduration }} δευτ. Οι εχθροί που πέφτουν επάνω σε τοίχο δέχονται <physicalDamage>{{ walldamage }} Σωματική Ζημιά</physicalDamage> και <status>Ακινητοποιούνται</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκε<PERSON>α Επιβράδυνσης/Ακινητοποίησης", "Ταχύτητα <PERSON>ίνησης του Ενθουσιασμού", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rccduration }} -> {{ rccdurationNL }}", "{{ rhypermovementspeedpercent }}% -> {{ rhypermovementspeedpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [90, 60, 30], "cooldownBurn": "90/60/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [590, 590, 590], "rangeBurn": "590", "image": {"full": "GnarR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}], "passive": {"name": "Γονίδιο της Οργής", "description": "Όταν βρίσκεται σε κατάσταση μάχης, ο Γκ<PERSON><PERSON>ρ αποκτά Οργή. Μόλις ο Γκναρ φτάσει στο μέγιστο επίπεδο Οργής, η επόμενη ικανότητά του θα τον μεταμορφώσει σε Μεγα-Γκναρ, δίνοντάς του μεγαλύτερες δυνατότητες επιβίωσης και πρόσβαση σε νέες ικανότητες.", "image": {"full": "Gnar_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}