{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "Σμόλντερ", "title": "ο Φλογερός Νεοσσός", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "Σμόλντερ των Ουρανών", "chromas": true}], "lore": "Ο νεαρός δράκος, κρυμ<PERSON><PERSON>νος στους απόκρημνους γκρεμούς των συνόρων της Νόξους, κάτω από τα άγρυπνα μάτια της μητέρας του, μαθαίνει τι σημαίνει να είναι ο κληρονόμος της γενιάς των αυτοκρατορικών δράκων του Κάμαβορ. Ο ζωηρός Σμόλντερ βιάζεται να μεγαλώσει και εκμεταλλεύεται κάθε ευκαιρία να εξασκήσει τις αναπτυσσόμενες ικανότητές του. Παρόλο που είναι ακόμα νεοσσός, οι ικανότητες του δεν είναι διόλου ευκαταφρόνητες, καθώς βάζει φωτιά σε οτιδήποτε μπορεί να καεί.", "blurb": "Ο νεαρός δράκος, κρυμ<PERSON><PERSON><PERSON>ος στους απόκρημνους γκρεμούς των συνόρων της Νόξους, κάτω από τα άγρυπνα μάτια της μητέρας του, μαθαίνει τι σημαίνει να είναι ο κληρονόμος της γενιάς των αυτοκρατορικών δράκων του Κάμαβορ. Ο ζωηρός Σμόλντερ βιάζεται να μεγαλώσει...", "allytips": ["Ο Σμόλντερ είναι ευάλωτος στην αρχή του παιχνιδιού. Εστιάστε στις στοίβες της παθητικής και προσπαθήστε να μείνετε ζωντανοί, για να γίνετε ένας πανίσχυρος δράκος αργότερα!", "Ο Σμόλντερ βασίζεται στην ομάδα του για να μένει ασφαλής. Προσπαθήστε να βρείτε συμμάχους που μπορούν να σας βοηθήσουν ενάντια στις απειλές των εχθρών.", "Ο Σμόλντερ μπορεί να προκαλέσει πολλή ζημιά σε Πεδίο Επιρροής. Αναζητήστε ευκαιρίες για επίθεση, όπου οι εχθροί είναι συσπειρωμένοι."], "enemytips": ["Ο Σμόλντερ βασίζεται στην ομάδα του για ασφάλεια. Επιτεθείτε του όταν η ομάδα του δεν μπορεί να τον σώσει.", "Μην συνωστ<PERSON><PERSON><PERSON>στε όταν αντιμετωπίζετε τον Σμόλντερ!", "Ο Σμόλντερ είναι πολύ ευάλωτος στα αρχικά στάδια του παιχνιδιού. Προσπαθήστε να εκμεταλλευτείτε την αδυναμία του πριν μάθει πώς να γίνει καλύτερος δράκος!", "Η πτήση του Σμόλντερ μπορεί να διακοπεί από ισχυρή Καταστολή Πλήθους και επηρεάζεται από Επιβραδύνσεις."], "tags": ["Marksman", "Mage"], "partype": "Μάνα", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "Σούπερ Καυτή Ανάσα", "description": "Ο Σμόλντερ εκτοξεύει φωτιά σε έναν εχθρό. Όσο αποκτά περισσότερες στοίβες, η ικανότητα γίνεται πιο ισχυρή.", "tooltip": "Ο Σμόλντερ ξερνάει φλόγες, προκαλώντας <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_qdamageincrease }} Μαγική Ζημιά</magicDamage>. Αν ο στόχος πεθάνει, ο Σμόλντερ ανακτά <scaleMana>{{ manarestore }} Μάνα</scaleMana>, μία φορά ανά χρήση.<br /><br />Ανάλογα με τις στοίβες της <spellName>Εξάσκησης Δράκου</spellName>, αυτή η ικανότητα εξελίσσεται και αποκτά τις εξής επιδράσεις:<li><keywordMajor>{{ stacktier1 }} Στοίβες</keywordMajor>: Προκαλεί ζημιά σε όλους τους εχθρούς που βρίσκονται γύρω από τον στόχο.<li><keywordMajor>{{ stacktier2 }} Στοίβες</keywordMajor>: Στέλνει <spellName>{{ tier2_numberofblowback }}</spellName> εκρήξεις πέρα από τον στόχο που προκαλούν το {{ tier2_blowbackpercentagedamage }}% της ζημιάς αυτής της Ικανότητας.<li><keywordMajor>{{ stacktier3 }} Στοίβες</keywordMajor>: Καίει τον στόχο προκαλώντας Πραγματική Ζημιά ίση με το <trueDamage>{{ tier3_burn }} της μέγιστης Ζωής</trueDamage> μέσα σε {{ tier3_dotlength }} δευτ. Οι αντίπαλοι Ήρωες που πέφτουν κάτω από <trueDamage>{{ tier3_executethreshold }}</trueDamage> της συνολικής Ζωής ενώ καίγονται, σκοτώνονται απευθείας.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderW", "name": "Αψού!", "description": "Ο Σμόλντερ φτερνίζεται με αξιολάτρευτο τρόπο και εκτοξεύει φλόγες που εκρήγνυντα<PERSON> όταν πετυχαίνουν αντίπαλους Ήρωες.", "tooltip": "Ο Σμόλντερ φτερνίζεται με αξιολάτρευτο τρόπο και εκτοξεύει φλόγες που προκαλούν <physicalDamage>{{ initialdamage }} Σωματική Ζημιά</physicalDamage> και <status>Επιβραδύνουν</status> κατά {{ slowamount*100 }}% για {{ slowduration }} δευτερόλεπτα.<br /><br />Όταν πετυχαίνει εχθρούς, προκαλείται μια έκρηξη, η οποία προκαλεί <physicalDamage>{{ explosiondamage }} Σωματική Ζημιά</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_wdamageincrease }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Ζημιά Έκρηξης", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderE", "name": "Φτερούγισμα", "description": "Ο Σμόλντερ απογ<PERSON>ιώνετα<PERSON>, αγ<PERSON><PERSON><PERSON><PERSON>τας τα εμπόδια, και βομβαρδίζει τον εχθρό με τη χαμηλότερη Ζωή.", "tooltip": "Ο Σμόλντερ απογειώνεται, απ<PERSON><PERSON><PERSON>ώντας <speed>{{ movespeed*100 }}% Ταχύτητα Κίνησης</speed> και αγνοώντας τα εμπόδια για {{ duration }} δευτ.<br /><br />Όσο πετάει, ο Σμόλντερ βομβαρδίζει τον εχθρό με τη χαμηλότερη Ζωή <spellName>{{ totalnumberofattacks }}</spellName> φορές (με στρογγυλοποίηση προς τα κάτω) προκαλώντας <physicalDamage>{{ damageperhit }} Σωματική Ζημιά</physicalDamage> + <magicDamage>{{ spell.smolderp:ebonusdamage }} Μαγική Ζημιά</magicDamage> ανά χτύπημα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Βασική ζημιά"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderR", "name": "ΜΑΜΑΑΑΑ!", "description": "Ο Σμόλντερ φωνάζει τη μαμά του που εκτοξεύει φωτιά από τον ουρανό, προκαλώντας επιπλέον ζημιά και επιβραδύνοντας τους εχθρούς που βρίσκονται στο επίκεντρο της φωτιάς της.", "tooltip": "Η μαμά του Σμόλντερ εκτοξεύει φωτιά από τον ουρανό, προκαλώντας <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage>. Οι εχθροί που βρίσκονται στο κέντρο δέχονται <physicalDamage>{{ tooltiponly_totalsweetspotdamage }} Σωματική Ζημιά</physicalDamage> και <status>Επιβραδύνονται</status> κατά {{ slowamount*100 }}% για {{ slowduration }} δευτερόλεπτα.<br /><br />Η μαμά του Σμόλντερ θεραπεύει τον γιο της για <healing>{{ momhealcalc }} Ζωή</healing> αν τον πετύχει.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Θεραπεία", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Εξάσκηση Δράκου", "description": "Όταν χτυπά Ήρωες με Ικανότητες και εκτελεί εχθρούς με τη Σούπερ Καυτή Ανάσα, ο Σμόλντερ αποκτά μια στοίβα Εξάσκηση Δράκου. Οι στοίβες αυξάνουν τη ζημιά των βασικών Ικανοτήτων του Σμόλντερ.", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}