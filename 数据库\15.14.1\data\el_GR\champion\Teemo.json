{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Teemo": {"id": "<PERSON><PERSON><PERSON>", "key": "17", "name": "Τίμο", "title": "ο Αεικίνητος Ανιχνευτής", "image": {"full": "Teemo.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "17000", "num": 0, "name": "default", "chromas": false}, {"id": "17001", "num": 1, "name": "Τίμο το Χαρούμενο Ξωτικό", "chromas": false}, {"id": "17002", "num": 2, "name": "Ανιχνευτής Τίμο", "chromas": false}, {"id": "17003", "num": 3, "name": "Τίμο το Κουνάβι", "chromas": false}, {"id": "17004", "num": 4, "name": "Αστρον<PERSON><PERSON><PERSON>ης <PERSON>ί<PERSON>ο", "chromas": true}, {"id": "17005", "num": 5, "name": "Τίμο το <PERSON>ν<PERSON>λ<PERSON>ι", "chromas": true}, {"id": "17006", "num": 6, "name": "Σούπερ Τίμο", "chromas": false}, {"id": "17007", "num": 7, "name": "Πάντα Τίμο", "chromas": false}, {"id": "17008", "num": 8, "name": "Τίμο Ομάδας Ωμέγα", "chromas": true}, {"id": "17014", "num": 14, "name": "Τίμο Διαβολάκος", "chromas": true}, {"id": "17018", "num": 18, "name": "Τίμο η Μέλισσα", "chromas": true}, {"id": "17025", "num": 25, "name": "Άνθος του Πνεύματος Τίμο", "chromas": false}, {"id": "17027", "num": 27, "name": "Άνθος του Πνεύματος Τίμο - Έκδοση Κύρους", "chromas": false}, {"id": "17037", "num": 37, "name": "Στρακαστρούκα Τίμο", "chromas": true}, {"id": "17047", "num": 47, "name": "Διαστημικός Ρυθμός Τίμο", "chromas": true}], "lore": "Απτ<PERSON><PERSON>τ<PERSON> από οποιοδήποτε εμπόδιο, <PERSON><PERSON><PERSON> απειλητικό και επικίνδυνο κι αν είναι, ο Τίμο περιπλανιέται στον κόσμο γεμάτος κέφι και με ασυγκράτητο ενθουσιασμό. Είναι ένα Γιορντλ με αταλάντευτη αίσθηση της ηθικής και ακολουθεί περήφανα τον Κώδικα των Ανιχνευτών του Μπαντλ, κάποιες φορές με τόση προθυμία μάλιστα που δεν αντιλαμβάνεται τις συνέπειες των πράξεών του. Αν και κάποιοι αμφισβητούν την ύπαρξη των Ανιχνευτών, ένα είναι σίγουρο: η επιμονή του Τίμο δεν είναι παίξε-γέλασε.", "blurb": "Απτ<PERSON><PERSON>τ<PERSON> από οποιοδήποτε εμπόδιο, <PERSON><PERSON><PERSON> απειλητικό και επικίνδυνο κι αν είναι, ο Τίμο περιπλανιέται στον κόσμο γεμάτος κέφι και με ασυγκράτητο ενθουσιασμό. Είναι ένα Γιορντλ με αταλάντευτη αίσθηση της ηθικής και ακολουθεί περήφανα τον Κώδικα των...", "allytips": ["Τα μανιτάρια του Τίμο μπορούν να χρησιμοποιηθούν αποτελεσματικά σε ορδές πλασμάτων.", "Τοποθετήστε τα μανιτάρια σε θέσεις-κλειδιά του χάρτη, όπως δίπλα στο Δράκο ή στο Βαρών<PERSON>, για να μάθετε πότε θα επιχειρήσουν οι αντίπαλοί σας να τους σκοτώσουν."], "enemytips": ["Η Τοξική Βολή του Τίμο τιμωρεί τους διστακτικούς παίκτες που δέχονται χτυπήματα και υποχωρούν, οπότε αν δεν είστε έτοιμοι να εμπλακείτε κανονικά, μείνετε μακριά.", "Επενδύστε σε έναν Φακό του Μάντη (Φυλαχτό), για να καταστρέψετε τα μανιτάρια σε ορισμένες σημαντικές θέσεις."], "tags": ["Marksman", "Mage"], "partype": "Μάνα", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 6}, "stats": {"hp": 615, "hpperlevel": 104, "mp": 334, "mpperlevel": 25, "movespeed": 330, "armor": 24, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 9.6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3, "attackspeedperlevel": 3.38, "attackspeed": 0.69}, "spells": [{"id": "TeemoQ", "name": "Βελάκι Τύφλωσης", "description": "Εμποδίζει την όραση ενός εχθρού με ένα πανίσχυρο δηλητήριο, προκαλώντας ζημιά στη στοχευμένη μονάδα και τυφλώνοντάς την όσο διαρκεί.", "tooltip": "Ο Τίμο ρίχνει ένα βελάκι, προ<PERSON><PERSON><PERSON>ώντας <status>Τύφλωση</status> στον στόχο για {{ blindduration }} δευτ. και <magicDamage>{{ calculateddamage }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Κόστος @AbilityResourceName@", "Διάρκεια", "Ζημιά"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ blindduration }} -> {{ blinddurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoQ.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TeemoW", "name": "Γρήγορη Κίνηση", "description": "Ο Τίμο τρέχει γύρω-γύρω αυξάνοντας παθητικά την Ταχύτητα Κίνησής του, έως ότου δεχθεί χτύπημα από έναν αντίπαλο Ήρωα ή Πύργο. Ο Τίμο μπορεί να τρέξει για να αποκτήσει μπόνους Ταχύτητα Κίνησης, η οποία για μικρό χρονικό διάστημα δεν σταματά αν δεχτεί χτύπημα.", "tooltip": "<spellPassive>Παθητική:</spellPassive> Ο Τίμο αποκτά <speed>Ταχύτητα Κίνησης {{ passivemovespeedbonus*100 }}%</speed>, αρκεί να μην έχει δεχτεί ζημιά από Ήρωα ή Πύργο τα τελευταία {{ passivecooldownondamagetaken }} δευτ.<br /><br /><spellActive>Ενεργή:</spellActive> Ο Τίμο επιταχύνει, αποκτώντας <speed>Ταχύτητα Κίνησης {{ activemovespeedbonus*100 }}%</speed>, η οποία δεν χάνεται όταν δέχεται χτύπημα, για {{ activemovespeedbuffduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ταχύτητα Κίνησης Παθητικής", "Ταχύτητα <PERSON>ίνησης Ενεργής"], "effect": ["{{ passivemovespeedbonus*100.000000 }}% -> {{ passivemovespeedbonusnl*100.000000 }}%", "{{ activemovespeedbonus*100.000000 }}% -> {{ activemovespeedbonusnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TeemoW.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TeemoE", "name": "Τοξική Βολή", "description": "Κάθε επίθεση του Τίμο δηλητηριάζει τον στόχο και προκαλεί ζημιά κατά την πρόσκρουση και κάθε δευτερόλεπτο για τα επόμενα 4 δευτερόλεπτα.", "tooltip": "<spellPassive>Παθητική:</spellPassive> Οι επιθέσεις του Τίμο δηλητηριάζουν τον στόχο, προκαλώντας επιπλέον <magicDamage>{{ impactcalculateddamage }} Μαγική Ζημιά</magicDamage> συν <magicDamage>{{ totaldotdamage }} Μαγική Ζημιά</magicDamage> μέσα σε {{ poisonduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά σύγκρουσης", "Ζημιά ανά δευτερόλεπτο"], "effect": ["{{ impactbasedamage }} -> {{ impactbasedamageNL }}", "{{ tickbasedamage }} -> {{ tickbasedamageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Παθητική", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoE.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Παθητική"}, {"id": "TeemoR", "name": "Δηλητηριώ<PERSON>ης <PERSON>αγίδα", "description": "Ο Τίμο εκτοξεύει μια εκρηκτική δηλητηριώδη παγίδα χρησιμοποιώντας ένα από τα μανιτάρια που έχει στο σακίδιο του. Αν κάποιος εχθρός πατήσει στην παγίδα, αυτή απελευθερώνει ένα δηλητηριώδες νέφος που επιβραδύνει τους εχθρούς και προκαλεί ζημιά με την πάροδο του χρόνου. Αν ο Τίμο πετάξει ένα μανιτάρι επάνω σε ένα άλλο, αυτό θα αναπηδήσει και θα αποκτήσει μεγαλύτερη εμβέλεια.", "tooltip": "Ο Τίμο πετάει μια μανιταροπαγίδα που ανατιν<PERSON><PERSON><PERSON>ται όταν κάποιος πατήσει επάνω της. Οι παγίδες <status>Επιβραδύνουν</status> κατά {{ slowamount }}% και προκαλούν <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> μέσα σε {{ debuffduration }} δευτ. Σε αυτό το χρονικό διάστημα αποκαλύπτονται και οι εχθροί.<br /><br />Οι παγίδες διαρκούν {{ mushroomduration }} λεπτά και είναι σε Απόκρυψη. Αν πετάξει ένα μανιτάρι επάνω σε ένα άλλο μανιτάρι, αυτό θα αναπηδήσει προτού προσγειωθεί στη θέση του. Αυτή η ικανότητα έχει {{ maxammo }} φορτίσεις (ανανέωση κάθε {{ ammorechargetime }} δευτ.)<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Επιβράδυνση", "Εμβέλεια ρίψης", "Μέγιστη απόσταση αναπήδησης", "Μέγιστος αριθμός παγίδων", "Κ<PERSON>σ<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ maxbouncedistance }} -> {{ maxbouncedistanceNL }}", "{{ maxammo }} -> {{ maxammoNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 3, "cooldown": [0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [75, 55, 35], "costBurn": "75/55/35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "3", "range": [600, 750, 900], "rangeBurn": "600/750/900", "image": {"full": "TeemoR.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ανταρτο<PERSON><PERSON>λεμος", "description": "Αν ο Τίμο σταθεί ακίνητος και δεν κάνει τίποτα για ένα σύντομο χρονικό διάστημα, γίνεται Αόρατος για απεριόριστο χρονικό διάστημα. Αν είναι μέσα σε βλάστηση, ο Τίμο μπορεί να γίνει και να παραμείνει Αόρατος ενώ κινείται. Μόλις πάψει να είναι Αόρατος, ο Τίμο κερδίζει το Στοιχείο του Αιφνιδιασμού, αυξάνοντας την Ταχύτητα Επίθεσής του για μερικά δευτερόλεπτα.", "image": {"full": "TeemoPassive.ASU_Teemo.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}