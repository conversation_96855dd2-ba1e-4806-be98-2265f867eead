{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "브라움", "title": "프렐요드의 심장", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "용 사냥꾼 브라움", "chromas": true}, {"id": "201002", "num": 2, "name": "프로레슬러 브라움", "chromas": false}, {"id": "201003", "num": 3, "name": "용맹한 브라움", "chromas": false}, {"id": "201010", "num": 10, "name": "산타 브라움", "chromas": false}, {"id": "201011", "num": 11, "name": "범죄 도시 브라움", "chromas": true}, {"id": "201024", "num": 24, "name": "달콤 가득 브라움", "chromas": true}, {"id": "201033", "num": 33, "name": "수영장 파티 브라움", "chromas": true}, {"id": "201042", "num": 42, "name": "바비큐 마스터 브라움", "chromas": true}], "lore": "축복받은 거대 이두박근과 그보다도 더 크고 호방한 마음을 지닌 브라움은 프렐요드 주민들이 사랑해 마지않는 영웅이다. 프로스텔드 북방에서 연회가 열리면 빠지지 않는 순서가 바로 브라움의 팔힘에 건배를 하는 것이다. 전설에 따르면 브라움은 단 하룻밤에 오크나무 숲 전체를 베어버렸고, 산 하나를 주먹으로 부수어 돌무더기로 만들어 버렸다고 한다. 마법이 깃든 동굴 문짝을 방패 삼아 들고, 근육만큼이나 커다란 콧수염 아래 사람 좋은 미소를 활짝 띤 브라움은 얼어붙은 북방을 배회한다. 도움이 필요한 이들의 진정한 친구가 되기 위해.", "blurb": "축복받은 거대 이두박근과 그보다도 더 크고 호방한 마음을 지닌 브라움은 프렐요드 주민들이 사랑해 마지않는 영웅이다. 프로스텔드 북방에서 연회가 열리면 빠지지 않는 순서가 바로 브라움의 팔힘에 건배를 하는 것이다. 전설에 따르면 브라움은 단 하룻밤에 오크나무 숲 전체를 베어버렸고, 산 하나를 주먹으로 부수어 돌무더기로 만들어 버렸다고 한다. 마법이 깃든 동굴 문짝을 방패 삼아 들고, 근육만큼이나 커다란 콧수염 아래 사람 좋은 미소를 활짝 띤 브라움은...", "allytips": ["아군과 협동하여 뇌진탕 펀치 중첩을 쌓는 게 좋습니다. 아군에게 표식이 생긴 대상을 기본 공격하라고 알려주세요.", "불굴 스킬을 사용하면 방어력이 약한 아군 앞으로 도약하여 투사체 공격을 막아줄 수 있습니다.", "빙하 균열은 강력한 둔화 구역을 남깁니다. 팀간 전투 시 적들을 갈라놓고 다가오는 속도를 늦출 수 있는 위치에 사용하세요."], "enemytips": ["브라움은 동상 스킬 혹은 기본 공격을 맞혀야만 뇌진탕 펀치 중첩을 시작할 수 있습니다. 일단 뇌진탕 표식이 생긴 후 3번 더 맞으면 기절하게 되므로 빠르게 사거리 밖으로 나가세요.", "브라움의 궁극기는 시전 시간이 길기 때문에 그 동안 피할 수 있습니다. 얼어붙은 지면 궤적 위를 통과하면 둔화가 적용되니, 지나가지 않아도 되도록 미리 좋은 위치를 잡아 두세요.", "불굴 덕에 브라움은 한 방향 방어력이 강합니다. 지속시간이 끝날 때까지 기다리거나 사거리 밖에서 공격하세요."], "tags": ["Tank", "Support"], "partype": "마나", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "동상", "description": "브라움이 방패에서 빙결을 뿜어내 둔화를 적용하고 마법 피해를 입힙니다.<br><br><font color='#FFF673'>뇌진탕 펀치</font> 중첩이 1회 쌓입니다.", "tooltip": "브라움이 방패에서 빙결을 뿜어내어 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 처음 맞는 적에게 입히고, 대상 적에게 {{ e2 }}%의 <status>둔화</status>를 겁니다. 둔화 효과는 {{ e5 }}초간 점차 감소합니다.<br /><br />이 스킬로 <keywordMajor>뇌진탕 펀치</keywordMajor> 중첩이 1회 쌓입니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "BraumW", "name": "내가 지킨다", "description": "브라움이 대상 아군 챔피언이나 미니언에게 도약합니다. 대상에게 다다르면 브라움과 아군은 몇 초 동안 방어력과 마법 저항력이 오릅니다.", "tooltip": "브라움이 아군 챔피언이나 미니언에게 도약합니다. 대상에게 다다르면 대상은 {{ e1 }}초 동안 <scaleArmor>방어력이 {{ grantedallyarmor }}</scaleArmor>, <scaleMR>마법 저항력이 {{ grantedallymr }}</scaleMR> 증가합니다. 브라움 역시 같은 시간 동안 <scaleArmor>방어력이 {{ grantedbraumarmor }}</scaleArmor>, <scaleMR>마법 저항력이 {{ grantedbraummr }}</scaleMR> 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 방어력/마법 저항력", "재사용 대기시간"], "effect": ["{{ baseresists }} -> {{ baseresistsNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "BraumE", "name": "불굴", "description": "브라움이 몇 초 동안 한 방향으로 방패를 들어 올려, 날아오는 투사체를 대신 맞고 파괴합니다. 첫 번째 공격은 아무 피해도 주지 않고, 이후 해당 방향에서 오는 공격은 피해량이 줄어듭니다.", "tooltip": "브라움이 {{ e2 }}초 동안 방패를 들어 올려 선택한 방향에서 날아오는 적의 투사체를 가로막아 자신이 대신 맞고서 소멸시킵니다. 브라움이 막는 첫 번째 투사체는 피해를 입히지 않으며 이후 막는 투사체는 피해량이 {{ e3 }}% 감소합니다.<br /><br />방패를 들어 올리는 동안 브라움의 <speed>이동 속도가 {{ e4 }}%</speed> 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["지속시간", "피해량 감소", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "BraumRWrapper", "name": "빙하 균열", "description": "브라움이 지면을 내리쳐 부근과 전방 직선상의 적들을 띄워올립니다. 이 직선을 따라 균열이 가며 적들을 둔화시킵니다.", "tooltip": "브라움이 지면을 내리쳐 전방에 균열을 내며 균열에 있는 적과 브라움 근처에 있는 적을 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 첫 번째로 맞은 대상은 브라움과의 거리에 비례하여 {{ minknockup }}~{{ maxknockup }}초 동안 <status>공중으로 띄워 올리고</status> 다른 적들은 {{ minknockup }}초 동안 <status>공중으로 띄워 올립니다</status>.<br /><br />균열은 {{ slowzoneduration }}초 동안 {{ movespeedmod }}%만큼 <status>둔화</status>시키는 구역을 생성합니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "띄워올리기 지속시간", "둔화", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ firstknockupduration }} -> {{ firstknockupdurationNL }}", "{{ movespeedmod }}% -> {{ movespeedmodNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "뇌진탕 펀치", "description": "브라움의 기본 공격은 뇌진탕 펀치를 적용시킵니다. 첫 번째 중첩이 적용된 후에는 <font color='#FFF673'>아군</font>의 기본 공격 역시 뇌진탕 펀치 중첩을 적용합니다. <br><br>중첩이 4번 쌓이면 대상은 기절하며 마법 피해를 입습니다. 다음 몇 초 동안은 중첩이 새로 쌓이지 않는 대신 브라움의 공격으로부터 추가 마법 피해를 입습니다.", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}