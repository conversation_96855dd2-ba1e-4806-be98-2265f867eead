{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kled": {"id": "<PERSON><PERSON>", "key": "240", "name": "<PERSON><PERSON>", "title": "l'isterico cavaliere", "image": {"full": "Kled.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "240000", "num": 0, "name": "default", "chromas": false}, {"id": "240001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "240002", "num": 2, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "240009", "num": 9, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "240018", "num": 18, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON><PERSON> tanto impavido quanto burbero, Kled è uno yordle che incarna il furioso coraggio di Noxus. È un'icona per i soldati dell'impero, ma gli ufficiali non si fidano di lui e la nobiltà lo detesta. Molti sostengono che Kled abbia combattuto in ogni campagna affrontata dalle legioni, che abbia ''guadagnato'' tutti i titoli militari e che non si sia mai ritirato da una battaglia. La veridicità dei dettagli è senza dubbio discutibile, ma una parte della sua leggenda è innegabile: a cavallo del suo poco fidato destriero, Kled combatte per proteggere ciò che gli appartiene... e per prendersi tutto quello che può.", "blurb": "<PERSON><PERSON><PERSON><PERSON> tanto impavido quanto burbero, Kled è uno yordle che incarna il furioso coraggio di Noxus. È un'icona per i soldati dell'impero, ma gli ufficiali non si fidano di lui e la nobiltà lo detesta. Molti sostengono che Kled abbia combattuto in ogni...", "allytips": ["Kled genera un po' di coraggio uccidendo i minion, ma ne ottiene molto di più combattendo i campioni.", "L'ultimo colpo di Tendenze omicide infligge più danni dei primi tre: cerca di metterlo a segno!", "Caricaaaaaaaa!!! può essere lanciata a grande distanza. Cerca di prevedere dove sarà la squadra nemica quando la raggiungerai."], "enemytips": ["Kled genera coraggio dannegg<PERSON>do i nemici con la sua Pistola tascabile e con gli attacchi base, uccidendo minion e attaccando strutture e mostri epici.", "Fai attenzione alla barra del coraggio di Kled, quando è appiedato. Se raggiunge il 100%, torna in sella e ottiene una notevole quantità di salute.", "Kled è molto più pericoloso quando le sue Tendenze omicide sono pronte."], "tags": ["Fighter"], "partype": "Coraggio", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 7}, "stats": {"hp": 410, "hpperlevel": 84, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "<PERSON>led<PERSON>", "name": "Tagliola con corda", "description": "Kled lancia una tagliola che infligge danni e si aggancia a un campione nemico. Se rimane agganciato per un breve periodo, il bersaglio subisce danni fisici e viene tirato verso Kled.<br><br><PERSON>uando è appiedato, l'abilità viene sostituita da Pistola tascabile, un colpo a distanza che spinge Kled indietro e ripristina coraggio.", "tooltip": "<keywordMajor>In sella:</keywordMajor> Kled lancia una tagliola che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e si aggancia al primo campione nemico o mostro grande colpito.<br /><br />Se Kled rimane vicino a un nemico agganciato per {{ tetherpoptime }} secondi, fa scattare la trappola, infliggendo <physicalDamage>{{ totalyankdamage }} danni fisici</physicalDamage>, <status>tirando</status> il nemico e <status>rallentandolo</status> di un {{ slowamount*-100 }}% per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> la<PERSON>", "<PERSON><PERSON> strappo", "Rallentamento", "Ricarica"], "effect": ["{{ firsthitbasedamage }} -> {{ firsthitbasedamageNL }}", "{{ firsthitbasedamage*2.000000 }} -> {{ firsthitbasedamagenl*2.000000 }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KledQ.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "KledW", "name": "Tendenze omicide", "description": "Kled ottiene un grande bonus alla velocità d'attacco per quattro attacchi. Il quarto attacco infligge più danni.", "tooltip": "<spellPassive>Passiva</spellPassive>: il successivo attacco base di Kled conferisce <attackSpeed>{{ attackspeed*100 }}% velocità d'attacco</attackSpeed> per 4 attacchi base o per {{ activeduration }} secondi.<br /><br />Il quarto colpo infligge <physicalDamage>{{ baseflatdamage }} più {{ percentdamage }} della salute massima in danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ baseflatdamage }} -> {{ baseflatdamageNL }}", "{{ 4hitmaxhealthdamage }}% -> {{ 4hitmaxhealthdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "KledW.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "<PERSON>led<PERSON>", "name": "Giostrare", "description": "Kled scatta, infliggendo danni fisici e ottenendo un rapido aumento di velocità. Kled può riattivare questa abilità per scattare indietro, attraverso il bersaglio iniziale, infliggendo gli stessi danni.", "tooltip": "Kled scatta, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> ai nemici che attraversa, tirando verso di sé i minion e i mostri piccoli.<br /><br />Se questa abilità colpisce un campione nemico o un mostro grande della giungla, Kled ottiene <speed>{{ movespeed*100 }}% velocità di movimento</speed> per {{ movespeedduration }} secondo/i, e può <recast>rilanciare</recast> l'abilità entro {{ recastwindow }} secondi per scattare indietro attraverso lo stesso bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KledE.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "KledR", "name": "Caricaaaaaaaa!!!", "description": "<PERSON>led e Skaarl caricano verso una posizione, lasciando una scia che conferisce velocità e ottenendo uno scudo. Skaarl aggancia e carica il primo campione nemico incontrato.", "tooltip": "Kled carica verso una posizione, lasciando una scia che conferisce agli alleati <speed>velocità di movimento</speed> crescente. Mentre carica e per 2 secondi dopo aver caricato, Kled guadagna uno <shield>scudo da {{ maximumshield }}</shield>. <PERSON><PERSON><PERSON><PERSON> colpisce il primo campione nemico che incontra, infliggendo da <magicDamage>{{ minimumdamagetooltip }}</magicDamage> a <magicDamage>{{ maximumchargedamage }} della salute massima in danni magici</magicDamage> (in base alla distanza percorsa) e <status>respingendo</status> per un breve periodo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Salute scudo massima", "Gitt<PERSON>", "Ricarica"], "effect": ["{{ percenthpbase*3.000000 }}% -> {{ percenthpbasenl*3.000000 }}%", "{{ shieldcapbase }} -> {{ shieldcapbaseNL }}", "{{ tooltiprange }} -> {{ tooltiprangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 125, 110], "cooldownBurn": "140/125/110", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [3500, 4000, 4500], "rangeBurn": "3500/4000/4500", "image": {"full": "KledR.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>, la lucertola codarda", "description": "Kled cavalca il suo fidato destriero, <PERSON><PERSON><PERSON><PERSON>, che subisce i danni al suo posto. Quando la salute di S<PERSON>arl si esaurisce, Kled scende.<br><br>Quando è appiedato, le abilità di Kled cambiano e infligge meno danni ai campioni. Kled può ripristinare il coraggio di Skaarl combattendo contro i nemici. Al massimo del coraggio, Kled risale in sella con una porzione della salute di Skaarl.", "image": {"full": "Kled_P.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}