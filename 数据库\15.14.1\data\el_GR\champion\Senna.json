{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Senna": {"id": "<PERSON><PERSON>", "key": "235", "name": "Σέννα", "title": "η Λύτρωση των Ψυχών", "image": {"full": "Senna.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "235000", "num": 0, "name": "default", "chromas": false}, {"id": "235001", "num": 1, "name": "True Damage Σέννα", "chromas": true}, {"id": "235009", "num": 9, "name": "True Damage Σέννα - Έκδοση Κύρους", "chromas": false}, {"id": "235010", "num": 10, "name": "Σέννα της Άγριας Δύσης", "chromas": true}, {"id": "235016", "num": 16, "name": "PROJECT: Σέννα", "chromas": true}, {"id": "235026", "num": 26, "name": "Σέννα της Σεληνιακής Έκλειψης", "chromas": true}, {"id": "235027", "num": 27, "name": "Σέννα της Σεληνιακής Έκλειψης - Έκδοση Κύρους", "chromas": false}, {"id": "235036", "num": 36, "name": "Μαγευτική Σέννα", "chromas": true}, {"id": "235046", "num": 46, "name": "Φύλακας των Άστρων Σέννα", "chromas": true}, {"id": "235056", "num": 56, "name": "Ευλογημένη από τον Χειμώνα Σέννα", "chromas": false}, {"id": "235063", "num": 63, "name": "<PERSON>ασ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> της Δικαιο<PERSON>ύνης <PERSON>α", "chromas": false}], "lore": "Καταραμένη από παιδί να καταδιώκεται από την υπερφυσική Μαύρη Ομίχλη, η Σέννα έγινε μέλος ενός ιερού τάγματος που είναι γνωστό ως Φρουροί του Φωτός και πολέμησε με λύσσα τη μοίρα της —με μόνο αποτέλεσμα τον θάνατό της και τη φυλάκιση της ψυχής της μέσα στο φανάρι του άσπλαχνου φαντάσματος Θρες. Όμως, η Σέννα αρνήθηκε να εγκαταλείψει τις ελπίδες της, και μέσα στο φανάρι έμαθε να χρησιμοποιεί την Ομίχλη. Έχει πλέον επιστρέψει ως μια νέα μορφή ζωής, αλλαγμένη για πάντα. Τώρα, οπλισμένη και με σκοτάδι εκτός από φως, η Σέννα θέλει να αποτελειώσει τη Μαύρη Ομίχλη στρέφοντάς την ενάντια στον ίδιο της τον εαυτό —με κάθε βολή από το αρχαίο της όπλο, λυτρώνει τις ψυχές που χάθηκαν μες στην Ομίχλη.", "blurb": "Καταραμένη από παιδί να καταδιώκεται από την υπερφυσική Μαύρη Ομίχλη, η Σέννα έγινε μέλος ενός ιερού τάγματος που είναι γνωστό ως Φρουροί του Φωτός και πολέμησε με λύσσα τη μοίρα της —με μόνο αποτέλεσμα τον θάνατό της και τη φυλάκιση της ψυχής της μέσα...", "allytips": [], "enemytips": [], "tags": ["Support", "Marksman"], "partype": "Μάνα", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 530, "hpperlevel": 89, "mp": 350, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 0, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SennaQ", "name": "Διαπεραστι<PERSON><PERSON>τά<PERSON>ι", "description": "Από τις διπλές κάννες του Κανονιού-κειμηλίου της, η Σέννα εξαπολύει μια ενιαία ακτίνα από φως και σκοτάδι που διαπερνά τον στόχο της, θεραπεύοντας τους συμμάχους και προκαλώντας ζημιά στους εχθρούς.", "tooltip": "Η Σέννα εξαπολύει μια ακτίνα διαπεραστικής σκιάς που περνάει μέσα από έναν σύμμαχο ή εχθρό, η οποία προκαλεί <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> σε εχθρούς και τους <status>Επιβραδύνει</status> κατά {{ totalslow }} για {{ slowduration }} δευτ. Αναπληρώνει <healing>{{ totalheal }} Ζωή</healing> σε σύμμαχους Ήρωες. <br /><br />Οι επιθέσεις μειώνουν τον Χρόνο Επαναφόρτισης αυτής της ικανότητας κατά {{ cdreductiononhit }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Θεραπεία", "Διάρκεια Επιβράδυνσης", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SennaQ.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaW", "name": "Τελευ<PERSON><PERSON><PERSON><PERSON>λισμός", "description": "Η Σέννα εξαπολύει ένα κύμα Μαύρης Ομίχλης. Αν πετύχει κάποιον εχθρό, τον τυλίγει με μανία, ριζ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς αυτόν και όλες τις κοντινές εχθρικές μονάδες μετά από μια μικρή καθυστέρηση.", "tooltip": "Η Σέννα απελευθερώνει τη Μαύρη Ομίχλη, προκαλώντας <physicalDamage>{{ damage }} Σωματική Ζημιά</physicalDamage> στον πρώτο εχθρό που θα χτυπήσει. Μετά από καθυστέρηση {{ delaytime }} δευτ., ο στόχος και άλλοι εχθροί που βρίσκονται κοντά του <status>Ριζώνονται</status> για {{ rootduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκεια Ριζώματος", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "SennaW.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaE", "name": "Κατάρα της Μ<PERSON>ύ<PERSON>ης <PERSON>ς", "description": "Η Σέννα αντλεί την ενέργεια της Ομίχλης που έχει αποθηκεύσει στο όπλο της δημιουργώντας μια θύελλα γύρω της, αποδέχεται το σκοτάδι και γίνεται φάντασμα. Οι σύμμαχοι που βρίσκονται εντός της θύελλας καμουφλάρονται και παίρνουν κι αυτοί τη μορφή του φαντάσματος, καθώς η Ομίχλη τους καλύπτει. Τα φαντάσματα έχουν αυξημένη Ταχύτητα Κίνησης, δεν μπορούν να επιλεγούν και κρύβουν την ταυτότητά τους.", "tooltip": "Η Σέννα διαλύεται σε ένα σύννεφο Μαύρης Ομίχλης για {{ buffduration }} δευτ. και μεταμορφώνεται σε Φάντασμα. Οι σύμμαχοι Ήρωες που βρίσκονται μέσα στην Ομίχλη είναι <keywordStealth>Καμουφλαρισμένοι</keywordStealth> και γίνονται Φαντάσματα όταν βγαίνουν έξω. Τα Φαντάσματα αποκτούν <speed>{{ totalms }} Ταχύτητα Κίνησης</speed>, δεν μπορούν να επιλεγούν και κρύβουν την ταυτότητά τους, όσο δεν υπάρχουν κοντά τους αντίπαλοι Ήρωες.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Διάρκεια", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ buffduration }} -> {{ buffdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24.5, 23, 21.5, 20], "cooldownBurn": "26/24.5/23/21.5/20", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SennaE.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SennaR", "name": "Ανατέλλου<PERSON><PERSON> Σκιά", "description": "Η Σέννα αντλεί δύναμη από τα κειμήλια των πεσμένων Φρουρών και χωρίζει το κανόνι της σε ένα ιερό πυροβόλο φωτός και σκιάς. Στη συνέχεια εξαπολύει μια ακτίνα που καλύπτει όλο τον χάρτη και προστατεύει τους συμμάχους της από ζημιά, ενώ κάνει ζημιά στους εχθρούς που βρίσκονται στο κέντρο.", "tooltip": "Η Σέννα εξαπολύει μια ακτίνα φωτός που προκαλεί <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> σε όλους τους αντίπαλους Ήρωες στην περιοχή. Οι σύμμαχοι Ήρωες που πετυχαίνει σε μια ευρύτερη περιοχή αποκτούν <shield>{{ totalshield }} Ασπίδα</shield> για {{ shieldduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Αντοχή Ασπίδας", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ shield }} -> {{ shieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SennaR.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Συγχώρεση", "description": "Όταν πεθαίνουν μονάδες κοντά στη Σέννα, οι ψυχές τους παγιδεύονται περιοδικά από τη Μαύρη Ομίχλη. Η Σέννα μπορεί να επιτεθεί σε αυτές τις ψυχές για να τις ελευθερώσει, απορροφώντας την Ομίχλη που τις κρατούσε αιχμάλωτες. Η Ομίχλη τροφοδοτεί το Κανόνι-κειμήλιο της Σέννα με αυξημένη Ζημιά Επίθεσης, Εμβέλεια Επίθεσης και Πιθανότητα Καίριου Χτυπήματος. <br><br>Οι επιθέσεις από το Κανόνι-κειμήλιο της Σέννα αργούν περισσότερο να πυροδοτηθούν, κ<PERSON><PERSON><PERSON><PERSON><PERSON> μπόνους ζημιά και της δίνουν ένα μέρος της Ταχύτητας Κίνησης του στόχου για σύντομο χρονικό διάστημα.", "image": {"full": "Senna_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}