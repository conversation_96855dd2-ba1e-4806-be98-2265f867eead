{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nasus": {"id": "<PERSON><PERSON>", "key": "75", "name": "Насус", "title": "Храни<PERSON><PERSON><PERSON>ь песков", "image": {"full": "Nasus.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "75000", "num": 0, "name": "default", "chromas": false}, {"id": "75001", "num": 1, "name": "Галактический Насус", "chromas": false}, {"id": "75002", "num": 2, "name": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> Насус", "chromas": false}, {"id": "75003", "num": 3, "name": "Темный рыцарь Насус", "chromas": true}, {"id": "75004", "num": 4, "name": "Riot K-9 Насус", "chromas": false}, {"id": "75005", "num": 5, "name": "Инфернальный Насус", "chromas": false}, {"id": "75006", "num": 6, "name": "Эрцгерцог Насус", "chromas": false}, {"id": "75010", "num": 10, "name": "Разрушитель миров Насус", "chromas": false}, {"id": "75011", "num": 11, "name": "Лунный страж Насус", "chromas": true}, {"id": "75016", "num": 16, "name": "Модифицированный Насус", "chromas": true}, {"id": "75025", "num": 25, "name": "Насус из Галактики грува", "chromas": true}, {"id": "75035", "num": 35, "name": "Бронированный титан Насус", "chromas": true}, {"id": "75045", "num": 45, "name": "Предвестник ночи Насус", "chromas": true}, {"id": "75054", "num": 54, "name": "Творец судеб Насус", "chromas": true}], "lore": "Насус – существо с головой шакала родом из древней Шуримы. Жителями пустыни он почитался как полубог. Насус был одним из вознесшихся, он обладал острейшим умом, был хранителем знаний и несравненным стратегом, мудрость которого на протяжении многих веков вела древнюю империю к величию. После гибели Шуримы Насус ушел в добровольное отшельничество и со временем стал для всех не более чем легендой. Но когда древняя столица Шуримы возродилась, он вернулся, чтобы не допустить нового падения.", "blurb": "Насус – существо с головой шакала родом из древней Шуримы. Жителями пустыни он почитался как полубог. Насус был одним из вознесшихся, он обладал острейшим умом, был хранителем знаний и несравненным стратегом, мудрость которого на протяжении многих веков...", "allytips": ["Грамотное добивание с помощью Вытягивающего удара значительно упрощает позднюю игру.", "На одиночной линии Призрачный огонь прекрасно очищает целые волны. Тем не менее на линии с союзником этим умением можно слишком сильно оттеснить миньонов к башне врага. Необходим баланс между добиванием с помощью Вытягивающего удара и уничтожением миньонов на площади.", "Если у вас нет никакой защиты, даже абсолютное умение вам не поможет. Даже если вы собираетесь наносить урон, а не танковать, защита практически необходима."], "enemytips": ["Насус сильнее, чем многие чемпионы Лиги, пока он видоизменен своим абсолютным умением. Вступайте с ним в бой, только если у вас есть явные преимущества.", "Максимальный уровень Иссушения является очень эффективным средством борьбы с AD-чемпионами, поэтому старайтесь не попасться на него, когда вы одни.", "Насус склонен становиться крайне мощным. Не пытайтесь вступать с ним в бой, когда у него полный уровень здоровья. "], "tags": ["Fighter", "Tank"], "partype": "Мана", "info": {"attack": 7, "defense": 5, "magic": 6, "difficulty": 6}, "stats": {"hp": 631, "hpperlevel": 104, "mp": 326, "mpperlevel": 62, "movespeed": 350, "armor": 34, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.45, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 67, "attackdamageperlevel": 4, "attackspeedperlevel": 3.48, "attackspeed": 0.638}, "spells": [{"id": "NasusQ", "name": "Вытягивающий удар", "description": "Следующая автоатака Насуса наносит дополнительный урон. Если при этом он убивает цель, этот урон навсегда увеличивается.", "tooltip": "Следующая автоатака Насуса наносит <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>. Если она убивает врага, Насус навсегда увеличивает урон этого умения на {{ basicstacks }} (на {{ bigstacks }}, если цель – чемпион, большой миньон или большой лесной монстр).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон", "Перезарядка"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7.5, 6.5, 5.5, 4.5, 3.5], "cooldownBurn": "7.5/6.5/5.5/4.5/3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [255, 255, 255, 255, 255], "rangeBurn": "255", "image": {"full": "NasusQ.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "NasusW", "name": "Иссушение", "description": "Насус старит вражеского чемпиона, постепенно уменьшая его скорость передвижения и скорость атаки.", "tooltip": "Насус старит чемпиона, <status>замедляя</status> его на {{ slowbase }}% (эффективность увеличивается до {{ maxslowtooltiponly }}% в течение {{ duration }} сек.). Кроме того, старение уменьшает <attackSpeed>скорость атаки</attackSpeed> цели на {{ attackspeedslowmult*100 }}% от <status>замедления</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Максимальное замедление", "Перезарядка"], "effect": ["{{ maxslowtooltiponly }}% -> {{ maxslowtooltiponlyNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "NasusW.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "NasusE", "name": "Призрачный огонь", "description": "Насус обрушивает призрачный огонь на выбранную область, нанося урон и уменьшая броню врагов, находящихся в зоне поражения.", "tooltip": "Насус разжигает в выбранной области призрачный огонь, нанося врагам <magicDamage>{{ initialdamage }} магического урона</magicDamage>. Враги в этой области теряют <scaleArmor>{{ e2 }}% брони</scaleArmor> и получают <magicDamage>{{ totaldotdamage }} магического урона</magicDamage> в течение {{ e3 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Начальный урон", "Урон в секунду", "Уменьшение брони", "Стоимость – @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [10, 16, 22, 28, 34], [30, 35, 40, 45, 50], [5, 5, 5, 5, 5], [50, 80, 110, 140, 170], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/16/22/28/34", "30/35/40/45/50", "5", "50/80/110/140/170", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NasusE.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "NasusR", "name": "Ярость песков", "description": "Насус призывает сильнейшую песчаную бурю, обрушивающуюся на ближайших противников. Пока буря бушует, его запас здоровья и дальность атаки увеличены. Кроме того, Насус наносит окружающим врагам урон, быстрее перезаряжает Вытягивающий удар и получает дополнительные броню и сопротивление магии.", "tooltip": "Насус окружает себя песчаной бурей на 15 сек., увеличивая <healing>максимальный запас здоровья на {{ bonushealth }}</healing>, а также <scaleArmor>броню</scaleArmor> и <scaleMR>сопротивление магии</scaleMR> на {{ initialresistgain }}.<br /><br />Пока бушует буря, враги поблизости каждую секунду получают <magicDamage>магический урон в размере {{ damagecalc }} от своего максимального запаса здоровья</magicDamage>. Кроме того, во время действия умения перезарядка <spellName>Вытягивающего удара</spellName> сокращена на {{ qcdr*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Дополнительное здоровье", "Максимальный запас здоровья", "Дополнительные броня и сопротивление магии", "Перезарядка"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ aoedamagepercent*100.000000 }}% -> {{ aoedamagepercentnl*100.000000 }}%", "{{ initialresistgain }} -> {{ initialresistgainNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NasusR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Пожира<PERSON>е<PERSON>ь душ", "description": "Насус вытягивает духовную энергию врагов, увеличивая свой вампиризм.", "image": {"full": "Nasus_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}