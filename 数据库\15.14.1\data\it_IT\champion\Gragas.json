{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gragas": {"id": "Gragas", "key": "79", "name": "Gragas", "title": "il provocatore delle masse", "image": {"full": "Gragas.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "79000", "num": 0, "name": "default", "chromas": false}, {"id": "79001", "num": 1, "name": "Gragas Sommozzatore", "chromas": false}, {"id": "79002", "num": 2, "name": "Gragas Campagnolo", "chromas": false}, {"id": "79003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "79004", "num": 4, "name": "Gragas Nobiluomo", "chromas": false}, {"id": "79005", "num": 5, "name": "Gragas Vandalo", "chromas": false}, {"id": "79006", "num": 6, "name": "Gragas Oktoberfest", "chromas": false}, {"id": "79007", "num": 7, "name": "Gragas Tifoso Sfegatato ", "chromas": false}, {"id": "79008", "num": 8, "name": "Gragas Fnatic", "chromas": false}, {"id": "79009", "num": 9, "name": "Gragas Fracassabotti", "chromas": false}, {"id": "79010", "num": 10, "name": "Gragas Operazioni Artiche", "chromas": false}, {"id": "79011", "num": 11, "name": "Gragas Inquisitore", "chromas": true}, {"id": "79020", "num": 20, "name": "Gragas Ritmo Spaziale", "chromas": true}, {"id": "79029", "num": 29, "name": "Gragas Mezzogiorno di Fuoco", "chromas": true}, {"id": "79039", "num": 39, "name": "Gragas Fan della Musica", "chromas": true}], "lore": "Gragas è un rissoso mastro birraio, tanto gioviale quanto imponente, sempre alla ricerca di nuovi modi per sollevare lo spirito di chi lo circonda. Ha origini sconosciute e cerca ingredienti tra le immacolate lande del Freljord, per perfezionare la sua ultima creazione. È impulsivo e caparbio ed è noto per le sue risse, che tendono a finire con feste che durano tutta la notte e danni ingenti alle proprietà della zona. Un'apparizione di Gragas porta sempre allegria e distruzione... in quest'ordine.", "blurb": "Gragas è un rissoso mastro birraio, tanto gioviale quanto imponente, sempre alla ricerca di nuovi modi per sollevare lo spirito di chi lo circonda. Ha origini sconosciute e cerca ingredienti tra le immacolate lande del Freljord, per perfezionare la sua...", "allytips": ["La riduzione danni di Furia alcolica viene applicata quando si inizia a bere. Cerca di usarla quando prevedi di subire dei danni.", "Cerca di spingere i nemici verso la tua torre con Barile esplosivo.", "Prova a realizzare delle combo con Urto devastante e Barile esplosivo per preparare un'uccisione per la tua squadra."], "enemytips": ["Gragas può sbaragliare tutti con la sua abilità suprema. Fai attenzione a non essere lanciato verso di lui o, peggio ancora, verso una torre nemica.", "Urto devastante ha un tempo di ricarica molto basso, il che rende difficile inseguire G<PERSON>. Non estenderti troppo per inseguirlo."], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 115, "mp": 400, "mpperlevel": 47, "movespeed": 330, "armor": 38, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.05, "attackspeed": 0.675}, "spells": [{"id": "GragasQ", "name": "<PERSON><PERSON>ola<PERSON>", "description": "Gragas fa rotolare il suo barile in una posizione, dove può essere attivato per esplodere, o dove esploderà autonomamente dopo 4 secondi. La potenza dell'esplosione aumenta col tempo. I nemici colpiti dall'esplosione vengono rallentati nella velocità di movimento.", "tooltip": "Gragas fa rotolare un barile che esplode dopo {{ e4 }} secondi, infliggendo da <magicDamage>{{ mindamage }} danni magici</magicDamage> a <magicDamage>{{ maxdamage }} danni magici</magicDamage> e <status>rallentando</status> da {{ e2 }} a {{ effect2amount*1.5 }}% per {{ e3 }} secondi. I danni e il <status>rallentamento</status> aumentano in base al tempo in cui il barile ha rotolato prima di esplodere. <br /><br />Gragas può <recast>rilanciare</recast> questa abilità per far esplodere il barile in anticipo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [40, 45, 50, 55, 60], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [2, 2, 2, 2, 2], [150, 150, 150, 150, 150], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "40/45/50/55/60", "2", "4", "2", "150", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "GragasQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasW", "name": "Furia alcolica", "description": "Gragas assaggia il suo ultimo intruglio per 1 secondo. Quando ha finito diventa turbolento e belligerante: infligge danni magici a tutti i nemici nelle vicinanze con il suo prossimo attacco base e subisce meno danni.", "tooltip": "Gragas assaggia il suo intruglio, riducendo i danni subiti di {{ damagereduction }} per {{ defenseduration }} secondi e potenziando il suo attacco successivo di un ulteriore <magicDamage>{{ totaldamage }}</magicDamage> più <magicDamage>{{ maxhppercentdamage }}% della salute massima in danni magici</magicDamage> al bersaglio e ai nemici vicini.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Riduzione danni", "<PERSON><PERSON>"], "effect": ["{{ basedamagereduction }}% -> {{ basedamagereductionNL }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "GragasW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasE", "name": "<PERSON>rto devastante", "description": "Gragas carica verso una posizione bersaglio e si scontra con il primo nemico che trova, infliggendo danni a tutte le unità nelle vicinanze e stordendole.", "tooltip": "G<PERSON>s carica, andand<PERSON> a scontrarsi con il primo nemico, <status>lanciando in aria</status> gli avversari vicini per {{ stunduration }} secondo e infliggendo loro <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />La ricarica di questa abilità si riduce del {{ cooldownrefund*100 }}% se Gragas si scontra con un nemico.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GragasE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasR", "name": "Barile esplosivo", "description": "Gragas lancia il suo barile, infliggendo danni e respingendo i nemici colpiti dall'esplosione.", "tooltip": "Gragas lancia il suo barile, infliggendo <magicDamage>{{ damagedone }} danni magici</magicDamage> e <status>respingendo</status> i nemici dalla zona di impatto.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GragasR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Happy Hour", "description": "Gragas si cura periodicamente dopo aver usato un'abilità.", "image": {"full": "GragasPassiveHeal.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}