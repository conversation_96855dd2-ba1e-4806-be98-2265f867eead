{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Urgot": {"id": "Urgot", "key": "6", "name": "Urgot", "title": "Motor Agonii", "image": {"full": "Urgot.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "6000", "num": 0, "name": "default", "chromas": false}, {"id": "6001", "num": 1, "name": "Wielki Wrogi <PERSON>", "chromas": false}, {"id": "6002", "num": 2, "name": "Urgot <PERSON>", "chromas": false}, {"id": "6003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "6009", "num": 9, "name": "Urgot w Samo Południe", "chromas": true}, {"id": "6015", "num": 15, "name": "Urgot w Przebraniu Czarodzieja Piżamy", "chromas": true}, {"id": "6023", "num": 23, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "6032", "num": 32, "name": "Urgot Król Sedesów", "chromas": true}], "lore": "Dawno temu potężny noxiański kat o imieniu Urgot został zdradzony przez imperium, w którego służbie odebrał tak wiele żyć. Skuty żelaznymi kajdanami zmuszony był poznać prawdziwą siłę w <PERSON><PERSON>, więziennej kopalni głęboko pod Zaun. Uwolniony w wyniku katastrofy, która sprowadziła chaos na całe miasto, stanowi teraz cień ciążący nad przestępczym półświatkiem. Dążąc do oczyszczenia swojego nowego domu z tych, którzy według niego nie zasługują na życie, unosi swoje ofiary na tych samych łańcuchach, które niegdyś pętały jego ciało, skazując je na niewyobrażalne cierpienie.", "blurb": "Dawno temu potężny noxiański kat o imieniu Urgot został zdradzony przez imperium, w którego służbie odebrał tak wiele żyć. Skuty żelaznymi kajdanami zmuszony był poznać prawdziwą siłę w <PERSON>, więziennej kopalni głęboko pod Zaun. Uwolniony w wyniku...", "allytips": ["Pilnuj czasów odnowienia wszystkich nóg, g<PERSON>ż zadają one znaczną częś<PERSON> twoich ogólnych obrażeń.", "Atak<PERSON>j Pociskiem Żrącym lub Pogardą, by <PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON> z Czystką. To <PERSON><PERSON><PERSON><PERSON>, aby <PERSON><PERSON><PERSON><PERSON><PERSON> kilka nóg jedna po drugiej.", "Oszczędź Strach ponad Śmierć na przeciwników, którzy na pewno go nie przeżyją. Umiejętność ta świetnie nadaje się do zdejmowania uciekających wrogów."], "enemytips": ["Urgot mocno polega na rażeniu przeciwników swoimi nogami, które mają własny czas odnowienia i strzelają jedynie wtedy, gdy bohater atakuje w tym samym kierunku. Unikaj trafienia wystrzałami z różnych nóg.", "Urgot dzięki Czystce może zadawać i pochłaniać olbrzymie obrażenia, ale podczas strzelania jest spowolniony.", "<PERSON><PERSON><PERSON> trafi cię <PERSON> p<PERSON>, postaraj się uniknąć spadnięcia poniżej progu wykończenia (25% twojego maksymalnego zdrowia), aż efekt wygaśnie."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 102, "mp": 340, "mpperlevel": 45, "movespeed": 330, "armor": 36, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 350, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 4, "attackspeedperlevel": 3.75, "attackspeed": 0.625}, "spells": [{"id": "UrgotQ", "name": "Pocisk Żrący", "description": "Wystrzeliwuje ładunek wybuchowy w wybrane miejsce, zadając obrażenia fizyczne wrogom trafionym wybuchem i spowalniając ich.", "tooltip": "Urgot wystrzeliwuje ładunek wybuchowy, zadając <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> i <status>spowalniaj<PERSON>c</status> o {{ slowamount*100 }}% na {{ slowduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Obrażenia", "Spowolnienie"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "UrgotQ.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "UrgotW", "name": "Czystka", "description": "Urgot prowadzi ostrz<PERSON>ł pobliskich wrogów, jed<PERSON>cz<PERSON>śnie będąc spowolnionym. W pierwszej kolejności strzela do wrogich bohaterów, których niedawno trafił innymi umiejętnościami, a ponadto aktywuje Wtórny Ogień.", "tooltip": "<spellPassive>Biernie:</spellPassive> Inne umiejętności Urgota oznaczają ostatniego trafionego bohatera na 5 sek.<br /><br /><spellActive>Użycie:</spellActive> Urgot zaczyna strzelać ze swojego łańcuchowego karabinu w stronę najbliższego wroga, priorytetowo traktując oznaczonych wrogów. W ciągu sekundy wyprowadza następującą liczbę ataków: {{ e8 }}, zadając każdym strzałem <physicalDamage>{{ damagepershot }} pkt. obrażeń fizycznych</physicalDamage>. Podczas strzelania Urgot może się poruszać i zyskuje {{ e2 }}% odporności na <status>spowolnienia</status>, ale traci <speed>{{ e5 }} jedn. prędkości ruchu</speed>.<br /><br />Na maks. poziomie umiejętność ta nie ma czasu działania oraz może zostać <toggle>włączona i wyłączona</toggle>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Obrażenia od ataku na strzał", "Koszt (@AbilityResourceName@)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e7 }}% -> {{ e7NL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 9, 6, 3, 0], "cooldownBurn": "12/9/6/3/0", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [40, 40, 40, 40, 40], [4, 4, 4, 4, 25000], [0, 0, 0, 0, 0], [125, 125, 125, 125, 125], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 23.5, 27, 30.5, 34], [3, 3, 3, 3, 3], [12, 12, 12, 12, 12], [50, 50, 50, 50, 50]], "effectBurn": [null, "30/60/90/120/150", "40", "4/4/4/4/25000", "0", "125", "0.5", "20/23.5/27/30.5/34", "3", "12", "50"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "UrgotW.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "UrgotE", "name": "<PERSON><PERSON><PERSON>", "description": "Urgot szarżuje w wybranym kierunku i osłania się tarczą, tratując wrogów niebędących bohaterami. Je<PERSON>eli złapie wrogiego bohatera, zatrzyma się i przerzuci go za siebie.", "tooltip": "Urgot szarżuje do przodu, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <shield>{{ etotalshieldhealth }} pkt. tarczy</shield> na {{ eshieldduration }} sek. <PERSON><PERSON><PERSON> trafiony bohater zostanie <status>ogłuszony</status> na {{ stunduration }} sek. i przerzucony za Urgota. Wszyscy wrogowie, z którymi zderzy się Urgot, otrzymają <physicalDamage>{{ edamage }} pkt. obrażeń fizycznych</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczy", "Koszt (@AbilityResourceName@)", "Czas odnowienia"], "effect": ["{{ ebasedamage }} -> {{ ebasedamageNL }}", "{{ eshieldbasehealth }} -> {{ eshieldbasehealthNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "UrgotE.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "UrgotR", "name": "<PERSON><PERSON><PERSON> p<PERSON>", "description": "Urgot wystrzeliwuje chemtechowe wiertło, które przebija pierwszego trafionego wrogiego bohatera. Jeśli jego zdrowie spadnie poniżej określonego poziomu, Urgot uzna go za słabego i wykona na nim egzekucję.", "tooltip": "Urgot wystrzeliwuje chemtechowe wiertło, które przebija pierwszego trafionego bohatera, zadając mu <physicalDamage>{{ rcalculateddamage }} pkt. obrażeń fizycznych</physicalDamage> i <status>spowalniając</status> go o 1% na {{ rslowduration }} sek. za każdy 1% brakującego zdrowia (maks. {{ rmovespeedmod }}%).<br /><br />Jeśli poziom zdrowia przebitej ofiary spadnie poniżej {{ rhealththreshold }}%, Urgot może <recast>ponownie użyć</recast> tej umiej<PERSON>, by <status>przyg<PERSON><PERSON><PERSON><PERSON><PERSON></status> ofiarę i przyciągnąć ją w swoją stronę. Po dotarciu do Urgota cel zostaje zabity, a pobliscy wrogowie <status>prz<PERSON><PERSON><PERSON>i</status> na {{ rfearduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "UrgotR.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Wtórny Ogień", "description": "Podstawowe ataki Urgota oraz Czystka uruchamiają wybuchy w jego nogach, które zadają obrażenia fizyczne.", "image": {"full": "Urgot_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}