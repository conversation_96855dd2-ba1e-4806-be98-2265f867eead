{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "Смолдер", "title": "Жгучий дракончик", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "Небесный змей Смолдер", "chromas": true}], "lore": "Найдя себе дом среди скалистых ноксианских утесов, юный Смолдер учится быть достойным наследником имперских драконов Камавора под присмотром своей мамы. Он любит играть и хочет поскорее вырасти, поэтому радуется любому поводу применить свои стремительно развивающиеся способности. Конечно, Смолдер еще совсем ребенок, но если он чихнет огнем, мало никому не покажется! Этот малыш способен поджечь все, что горит.", "blurb": "Найдя себе дом среди скалистых ноксианских утесов, юный Смолдер учится быть достойным наследником имперских драконов Камавора под присмотром своей мамы. Он любит играть и хочет поскорее вырасти, поэтому радуется любому поводу применить свои стремительно...", "allytips": ["Смолдер крайне уязвим на ранней стадии. Чтобы стать сильным драконом, играйте осторожно и накапливайте заряды пассивного умения!", "Смолдер зависим от своей команды. Старайтесь держаться союзников, которые защитят вас от врагов.", "Смолдер наносит большой урон по площади. Атакуйте, когда враги стоят близко друг к другу."], "enemytips": ["Смолдер зависим от своей команды. Атакуйте его, когда рядом с ним нет союзников.", "Во время сражений со Смолдером не стойте все в одном месте!", "Смолдер крайне уязвим на ранней стадии. Постарайтесь воспользоваться его беспомощностью, пока он не научился быть драконом!", "Полет Смолдера можно прервать при помощи жесткого контроля. Кроме того, его можно замедлить соответствующими эффектами."], "tags": ["Marksman", "Mage"], "partype": "Мана", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "Супержаркое дыхание", "description": "Смолдер дышит пламенем на врага. Чем больше зарядов он накапливает, тем сильнее становится умение.", "tooltip": "Смолдер дышит пламенем на врага, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> и <magicDamage>{{ spell.smolderp:passive_qdamageincrease }} магического урона</magicDamage>. Если цель погибает, Смолдер восстанавливает <scaleMana>{{ manarestore }} маны</scaleMana> (не более одного раза за применение умения).<br /><br />Это умение усиливается в зависимости от количества зарядов <spellName>Драконьей тренировки</spellName>:<li><keywordMajor>{{ stacktier1 }}</keywordMajor>: Смолдер наносит урон всем врагам вокруг цели.<li><keywordMajor>{{ stacktier2 }}</keywordMajor>: за целью происходят взрывы (<spellName>{{ tier2_numberofblowback }}</spellName>), которые наносят {{ tier2_blowbackpercentagedamage }}% урона этого умения.<li><keywordMajor>{{ stacktier3 }}</keywordMajor>: Смолдер поджигает цель, нанося ей <trueDamage>чистый урон в размере {{ tier3_burn }} от максимального запаса здоровья</trueDamage> в течение {{ tier3_dotlength }} сек. Если у подожженного вражеского чемпиона остается менее <trueDamage>{{ tier3_executethreshold }}</trueDamage> здоровья, он мгновенно погибает.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SmolderW", "name": "Апчхи!", "description": "Смолдер мило чихает пламенем, которое взрывается при поражении вражеских чемпионов.", "tooltip": "Смолдер мило чихает пламенем, нанося пораженным врагам <physicalDamage>{{ initialdamage }} физического урона</physicalDamage> и <status>замедляя</status> их на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br />При попадании во вражеского чемпиона вокруг него происходит взрыв, который наносит врагам <physicalDamage>{{ explosiondamage }} физического урона</physicalDamage> и <magicDamage>{{ spell.smolderp:passive_wdamageincrease }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Урон от взрыва", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SmolderE", "name": "Хлоп-хлоп-хлоп", "description": "Смолдер взмывает в воздух, перелетая через элементы ландшафта. Во время полета он атакует врага с самым низким уровнем здоровья.", "tooltip": "Смолдер взлетает, увеличивая свою <speed>скорость передвижения на {{ movespeed*100 }}%</speed> и игнорируя элементы ландшафта в течение {{ duration }} сек.<br /><br />Во время полета Смолдер обстреливает врага с самым низким уровнем здоровья сгустками огня. Количество сгустков: <spellName>{{ totalnumberofattacks }}</spellName> (округляется в меньшую сторону). Каждое попадание наносит <physicalDamage>{{ damageperhit }} физического урона</physicalDamage> и <magicDamage>{{ spell.smolderp:ebonusdamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Базовый урон"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SmolderR", "name": "МА-А-АМ!", "description": "Смолдер зовет свою маму, и она дышит огнем с небес. Враги в центре области поражения получают дополнительный урон и замедляются.", "tooltip": "Мама Смолдера дышит огнем с небес, нанося врагам <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>. Враги в центре области поражения вместо этого получают <physicalDamage>{{ tooltiponly_totalsweetspotdamage }} физического урона</physicalDamage> и <status>замедляются</status> на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br />Задев Смолдера, огонь восстанавливает ему <healing>{{ momhealcalc }} здоровья</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Лечение", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Драконья тренировка", "description": "Когда Смолдер поражает чемпионов умениями или убивает врагов Супержарким дыханием, он получает заряд Драконьей тренировки. Заряды увеличивают урон базовых умений Смолдера.", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}