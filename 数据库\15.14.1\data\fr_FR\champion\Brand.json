{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Brand": {"id": "Brand", "key": "63", "name": "Brand", "title": "Vengeur flamboyant", "image": {"full": "Brand.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "63000", "num": 0, "name": "default", "chromas": false}, {"id": "63001", "num": 1, "name": "Brand apocalyptique", "chromas": false}, {"id": "63002", "num": 2, "name": "Brand <PERSON>dale", "chromas": false}, {"id": "63003", "num": 3, "name": "Cryo Brand", "chromas": false}, {"id": "63004", "num": 4, "name": "Brand zombie", "chromas": false}, {"id": "63005", "num": 5, "name": "Brand feu spirituel", "chromas": false}, {"id": "63006", "num": 6, "name": "Brand boss de combat", "chromas": false}, {"id": "63007", "num": 7, "name": "Brand aux flammes pures", "chromas": true}, {"id": "63008", "num": 8, "name": "Brand dragon éternel", "chromas": true}, {"id": "63021", "num": 21, "name": "Brand séducteur", "chromas": true}, {"id": "63022", "num": 22, "name": "Brand séducteur prestige", "chromas": false}, {"id": "63033", "num": 33, "name": "Brand ville démoniaque", "chromas": true}, {"id": "63042", "num": 42, "name": "Brand empyréen", "chromas": true}], "lore": "Autrefois membre d'une tribu de Freljord, <PERSON><PERSON> est devenu l'être que l'on connaît sous le nom de Brand à force de convoiter des pouvoirs toujours plus grands. Alors qu'il recherchait l'une des légendaires Runes telluriques, <PERSON>gan trahit ses compagnons et s'en empara ; en un instant, son âme fut consumée par un brasier qui le changea à jamais. Désormais animé d'un feu inextinguible, <PERSON> parcourt <PERSON>oran en quête d'autres runes, jurant vengeance pour des trahisons qu'aucun mortel n'aurait pu subir au cours d'une dizaine de vies.", "blurb": "Autrefois membre d'une tribu de Freljord, <PERSON><PERSON> est devenu l'être que l'on connaît sous le nom de Brand à force de convoiter des pouvoirs toujours plus grands. Alors qu'il recherchait l'une des légendaires Runes telluriques, <PERSON>gan trahit ses...", "allytips": ["Vous pouvez empêcher vos ennemis de rester à proximité de leurs sbires en enflammant ces derniers avec Conflagration.", "Les compétences de Brand peuvent servir à effectuer différentes combos pour maximiser les dégâts selon la situation.", "Pyrolyse rebondit aléatoirement sur les ennemis : utilisez-le de préférence sur de petits groupes pour que le sort rebondisse plusieurs fois sur la même cible."], "enemytips": ["Brand doit utiliser une compétence pour initier son combo. Si vous esquivez Brûlure ou Colonne de flammes, vous interromprez sa routine.", "Pensez à vous éloigner de vos alliés lorsque vous le voyez incanter Pyrolyse. La vitesse du projectile est assez lente au début, ce qui laisse le temps de réagir.", "La compétence passive de Brand lui permet d'exceller contre les équipes regroupées. Ne restez pas tous au même endroit."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 4}, "stats": {"hp": 570, "hpperlevel": 105, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.681}, "spells": [{"id": "BrandQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Brand lance une boule de feu devant lui, infligeant des dégâts magiques. Si la cible est en flammes, Brûlure l'étourdit pendant @StunDuration@ sec.", "tooltip": "Brand lance une boule de feu qui inflige <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage> au premier ennemi touché.<br /><br />Si la cible est <keywordMajor>en flammes</keywordMajor>, elle est <status>étourdie</status> pendant {{ stunduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [70, 100, 130, 160, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/100/130/160/190", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "BrandQ.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandW", "name": "<PERSON><PERSON><PERSON> de flammes", "description": "Après un court délai, Brand crée une colonne de flammes dans la zone ciblée, infligeant des dégâts magiques aux unités ennemies qui s'y trouvent. Les unités qui sont en flammes subissent 25% de dégâts supplémentaires.", "tooltip": "Brand crée un pilier de feu, infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>.<br /><br />Les unités <keywordMajor>en flammes</keywordMajor> subissent <magicDamage>{{ empowereddamage }} pts de dégâts</magicDamage> à la place.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [20, 40, 60, 80, 100], [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "20/40/60/80/100", "0.25", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BrandW.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandE", "name": "Conflagration", "description": "Brand provoque sur sa cible une puissante explosion qui se propage vers les ennemis proches, infligeant des dégâts magiques. Si la cible est en flammes, la portée de la dispersion de Conflagration est doublée.", "tooltip": "Brand provoque une puissante explosion sur sa cible, infligeant <magicDamage>{{ edamagecalc }} pts de dégâts magiques</magicDamage> aux unités proches.<br /><br />Si la cible est <keywordMajor>en flammes</keywordMajor>, la portée de la dispersion est doublée.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [375, 375, 375, 375, 375], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "375", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "BrandE.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandR", "name": "Pyrolyse", "description": "Brand libère un torrent de flammes qui rebondit jusqu'à 5 fois sur lui et les ennemis proches, infligeant des dégâts magiques aux ennemis touchés. Les rebonds visent en priorité les champions affectés par Flammes mais qui n'ont pas atteint le nombre d'effets max. Si une cible est en flammes, Pyrolyse la ralentit brièvement.", "tooltip": "Brand libère un torrent de flammes qui rebondit jusqu'à 5 fois sur lui ou un ennemi, infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage> aux ennemis touchés. Les rebonds visent en priorité les champions affectés par <keywordMajor>Flammes</keywordMajor> mais qui n'ont pas atteint le nombre d'effets max.<br /><br />Si la cible est <keywordMajor>en flammes</keywordMajor>, elle est brièvement <status>ralentie</status> de {{ slowamount }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts par rebond", "Ralentissement", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "BrandR.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Les compétences de Brand enflamment ses cibles, leur infligeant des dégâts en 4 sec (cumulable 3 fois). Si Brand tue un ennemi en flammes, il récupère du mana. Quand <PERSON>lam<PERSON> atteint son maximum d'effets cumulés sur un champion ou un grand monstre, Flammes devient instable et explose au bout de 2 sec, infligeant les effets de sort et d'énormes dégâts autour de la victime.", "image": {"full": "BrandP.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}