{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sett": {"id": "<PERSON><PERSON>", "key": "875", "name": "<PERSON><PERSON>", "title": "<PERSON>", "image": {"full": "Sett.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "875000", "num": 0, "name": "default", "chromas": false}, {"id": "875001", "num": 1, "name": "Mechapilot <PERSON>", "chromas": true}, {"id": "875008", "num": 8, "name": "Obsidiandrache Sett", "chromas": true}, {"id": "875009", "num": 9, "name": "Obsidiandrache Sett (Prestige)", "chromas": false}, {"id": "875010", "num": 10, "name": "Poolparty-Sett", "chromas": true}, {"id": "875019", "num": 19, "name": "Feuerwerks-Sett", "chromas": true}, {"id": "875038", "num": 38, "name": "Seelenblumen-Sett", "chromas": true}, {"id": "875045", "num": 45, "name": "Soul Fighter Sett", "chromas": true}, {"id": "875056", "num": 56, "name": "HEARTSTEEL-<PERSON>t", "chromas": true}, {"id": "875066", "num": 66, "name": "Lichtschlange-Sett", "chromas": false}], "lore": "<PERSON><PERSON> <PERSON><PERSON><PERSON> von <PERSON>, krimine<PERSON> Unterwelt erlangte Sett nach dem Krieg gegen Noxus Berühmtheit. Er fing als einfacher Herausforderer in den Kampfarenen von Navori an, aber wurde schnell für seine brutale Stärke bekannt und wie er fast pausenlos jegliche Schläge einfach wegsteckte. Nachdem er sich bei den einheimischen Kämpfern einen Namen gemacht hatte, schnappte sich Sett den Platz an der Spitze und übernahm die Kontrolle über die Kampfarenen, in denen er einst gekämpft hatte.", "blurb": "<PERSON><PERSON> <PERSON><PERSON><PERSON> von <PERSON>, krimine<PERSON> Unterwelt erlangte Sett nach dem Krieg gegen Noxus Berühmtheit. Er fing als einfacher Herausforderer in den Kampfarenen von Navori an, aber wurde schnell für seine brutale Stärke bekannt und wie er fast...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "Streitlust", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 670, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "SettQ", "name": "<PERSON><PERSON> eige<PERSON>", "description": "Setts nächste beide Angriffe verursachen zusätzlichen Schaden, der auf dem maximalen Leben seines Ziels basiert. Außerdem erhält Sett Lauftempo, wenn er sich auf gegnerische Champions zubewegt.", "tooltip": "Sett ist auf Krawall gebürstet und erhält {{ msduration }}&nbsp;Sekunden lang <speed>{{ msamount*100 }}&nbsp;% Lauftempo</speed>, wenn er sich auf gegnerische Champions zubewegt.<br /><br />Setts nächste beide Angriffe verursachen zusätzlich <physicalDamage>normalen Schaden</physicalDamage> in <PERSON><PERSON><PERSON> von {{ basedamage }} plus {{ maxhealthdamagecalc }} des maximalen Lebens.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden", "Maximales Leben (%) pro 100 Angriffsschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthtadratiotooltip }}&nbsp;% -> {{ maxhealthtadratiotooltipNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "SettQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "SettW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sett speichert erlittenen Schaden als „Streitlust“. Bei Aktivierung wird die gesammelte Streitlust verbraucht, Sett erhält einen Schild und er schlägt im Zielbereich mit den Fäusten zu. Im Zentrum verursacht er absoluten Schaden, an den Seiten normalen Schaden.", "tooltip": "<spellPassive>Passiv:</spellPassive> Sett speichert {{ damagestored*100 }}&nbsp;% des erlittenen Schadens als <keywordMajor>Streitlust</keywordMajor>, bis zu einem Maximum von <keywordMajor>{{ maxgrit }}</keywordMajor>. Die <keywordMajor>Streitlust</keywordMajor> fällt {{ adrenalinestoragewindow }}&nbsp;Sekunden nach dem Erleiden von Schaden rasch ab.<br /><br /><spellActive>Aktiv:</spellActive> Sett verbraucht seine gesamte <keywordMajor>Streitlust</keywordMajor> und erhält einen <shield>Schild</shield> in Höhe von {{ shieldconversion*100 }}&nbsp;% der verbrauchten Streitlust, der über {{ shieldmaxduration }}&nbsp;Sekunden abfällt. Dann holt Sett zu einem gewaltigen Schlag aus, der Gegnern in der Mitte <trueDamage>absoluten Schaden</trueDamage> in <PERSON>öhe von {{ damagecalc }} plus {{ damageconversion }} der verbrauchten Streitlust zufügt (max. <trueDamage>{{ f1 }}&nbsp;Schaden</trueDamage>). Gegner, die sich nicht in der Mitte befinden, erleiden stattdessen <physicalDamage>normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SettW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "SettE", "name": "<PERSON><PERSON> die Zwölf", "description": "Sett zieht alle Gegner zu sich heran, die auf beiden Seiten neben ihm stehen, fügt ihnen Schaden zu und betäubt sie. Falls sich nur auf einer Seite von ihm G<PERSON>ner befinden, werden sie verlangsamt anstatt betäubt.", "tooltip": "Sett schleudert Gegner links und rechts von ihm gege<PERSON>nder, fügt ihnen dabei <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage> zu und <status>verlangsamt</status> sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%. Schnappt sich Sett mindestens einen Gegner auf jeder Se<PERSON>, werden alle Gegner {{ stunduration }}&nbsp;Sekunde lang <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "SettE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "SettR", "name": "Das K.-o.-Kriterium", "description": "Sett packt einen gegnerischen Champion, springt mit ihm nach vorn und schleudert ihn dann zu Boden. Dabei verursacht er Schaden und verlangsamt alle Gegner in der Nähe der Einschlagstelle.", "tooltip": "Sett packt einen gegnerischen Champion, zieht ihn mit nach vorn und <status>unterdrückt</status> ihn. Dann schmettert er ihn zu Boden, wodurch umstehende Gegner <physicalDamage>normalen Schaden</physicalDamage> in <PERSON><PERSON><PERSON> von {{ damagecalc }} plus {{ maxhealthdamage*100 }}&nbsp;% des zusätzlichen Lebens des gepackten Champions erleiden und {{ slowduration }}&nbsp;Sekunde(n) lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status> werden. Gegner erleiden weniger Schaden, je weiter sie von Setts Landung entfernt sind.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Zusätzlicher Schaden abhängig vom Leben", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}&nbsp;% -> {{ maxhealthdamagenl*100.000000 }}&nbsp;%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "SettR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Streitlustig", "description": "Sett schlägt bei seinen normalen Angriffen abwechselnd mit der linken und der rechten Faust zu. Sein rechter Faustschlag ist etwas kräftiger und schneller. Sett hasst es außerdem, zu verlieren. Er erhält zusätzliche Lebensregeneration, die auf seinem fehlenden Leben basiert.", "image": {"full": "Sett_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}