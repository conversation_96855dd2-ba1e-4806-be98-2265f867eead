{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Illaoi": {"id": "<PERSON><PERSON><PERSON>", "key": "420", "name": "イラオイ", "title": "海の女司祭", "image": {"full": "Illaoi.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "420000", "num": 0, "name": "default", "chromas": false}, {"id": "420001", "num": 1, "name": "ヴォイドの遣いイラオイ", "chromas": false}, {"id": "420002", "num": 2, "name": "レジスタンス イラオイ", "chromas": true}, {"id": "420010", "num": 10, "name": "宇宙の司祭イラオイ", "chromas": true}, {"id": "420018", "num": 18, "name": "スノームーン イラオイ", "chromas": true}, {"id": "420027", "num": 27, "name": "バトルベア イラオイ", "chromas": true}], "lore": "イラオイは頑強な巨体の持ち主だが、その不屈の信仰心はそれ以上に大きい。「大いなるクラーケン」の預言者である彼女は、巨大な黄金の偶像を使って敵の魂を肉体から引き剥がし、現実の知覚を打ち砕く。「ナーガケイボロスの真実の担い手」に挑むものは、すぐさまイラオイは一人で戦っているのではないということを思い知るだろう。そう──サーペントアイルの名状しがたい神が彼女とともにあり、戦うのだということを。", "blurb": "イラオイは頑強な巨体の持ち主だが、その不屈の信仰心はそれ以上に大きい。「大いなるクラーケン」の預言者である彼女は、巨大な黄金の偶像を使って敵の魂を肉体から引き剥がし、現実の知覚を打ち砕く。「ナーガケイボロスの真実の担い手」に挑むものは、すぐさまイラオイは一人で戦っているのではないということを思い知るだろう。そう──サーペントアイルの名状しがたい神が彼女とともにあり、戦うのだということを。", "allytips": ["触手は重要な戦力の一つだ。常に共に戦えるようにしよう。", "魂は引き出された対象の現在体力をコピーする。", "「器」", "を作ることが目的なら、先に相手の体力を減らしておくことで、魂をキルしやすくするとよい。", "「信仰震」は迎撃にも追撃にも使うことができる。しかし触手は移動できないので、自分から攻めに行く時は位置どりに気をつけよう。"], "enemytips": ["できる限り小まめに触手をキルすることで、イラオイとの戦いは楽になるはずだ。", "魂を引き出された時は、可能なら反撃しよう。イラオイにダメージを与えることで、魂の持続時間は短くなる。", "「信仰震」によって発生する触手の数を抑えるため、味方と密集することは避けよう。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 4}, "stats": {"hp": 656, "hpperlevel": 115, "mp": 350, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9.5, "hpregenperlevel": 0.8, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "IllaoiQ", "name": "触手の鉄槌", "description": "触手の与ダメージが増加する。発動すると、触手を叩きつけて物理ダメージを与える。", "tooltip": "<passive>自動効果:</passive> <keywordMajor>「叩きつけ」</keywordMajor>のダメージが<physicalDamage>{{ tentacledamageamp*100 }}%</physicalDamage>増加する(現在<physicalDamage>{{ f1 }}の物理ダメージ</physicalDamage>)。<br /><br /><active>発動効果:</active> 偶像を振って、触手が前方に<keywordMajor>「叩きつけ」</keywordMajor>を行う。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「叩きつけ」追加ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ tentacledamageamp*100.000000 }}% -> {{ tentacledamageampnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [200, 200, 200, 200, 200], [800, 800, 800, 800, 800], [10, 8, 6, 4, 2], [5, 5, 5, 5, 5], [0.1, 0.15, 0.2, 0.25, 0.3], [1.2, 1.2, 1.2, 1.2, 1.2], [-0.3, -0.35, -0.4, -0.45, -0.5], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "200", "800", "10/8/6/4/2", "5", "0.1/0.15/0.2/0.25/0.3", "1.2", "-0.3/-0.35/-0.4/-0.45/-0.5", "1.5", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "IllaoiQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "IllaoiW", "name": "過酷なる教訓", "description": "次の通常攻撃で対象に飛びかかって偶像で殴りつけ、物理ダメージを与える。周囲の触手にも対象を攻撃させる。", "tooltip": "次の通常攻撃で対象に飛びかかり、追加で<physicalDamage>最大体力の{{ healthpercenttotaltooltip }}の物理ダメージ</physicalDamage>を与える。攻撃が命中すると、付近にある触手もまた対象へ向かって<keywordMajor>「叩きつけ」</keywordMajor>を行う。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "最小ダメージ"], "effect": ["{{ effect1amount*100.000000 }}% -> {{ effect1amountnl*100.000000 }}%", "{{ wmindamage }} -> {{ wmindamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0.03, 0.035, 0.04, 0.045, 0.05], [2, 2, 2, 2, 2], [300, 300, 300, 300, 300], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.03/0.035/0.04/0.045/0.05", "2", "300", "6", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "IllaoiW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "IllaoiE", "name": "魂の試練", "description": "偶像から触手を伸ばし、敵の肉体から魂を引きずり出して、自身の前に立たせる。魂は受けたダメージから一定の割合を、本体に反映させる。魂がキルされるか本体が遠く離れた場合、対象は<font color='#669900'>「器」</font>となり、その周囲に触手が発生するようになる。", "tooltip": "指定方向に偶像から触手をのばし、最初に触れた敵チャンピオンから{{ spiritduration }}秒間、魂を引きずり出す。この魂を攻撃すると、魂が受けたダメージのうち{{ echopercenttooltiponly }}が本体に反映される。<br /><br />魂が倒されるか対象が魂から遠く離れた場合、対象は{{ vesselduration }}秒間マークされ、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を受ける。マークされた敵は<scaleLevel>{{ f1 }}</scaleLevel>秒ごとに触手を発生させる。<br /><br />触手は{{ timebetweenvesseltentacleslams }}秒ごとに自動的に魂とマークされた敵に<keywordMajor>「叩きつけ」</keywordMajor>を行う。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ反映率", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [10, 10, 10, 10, 10], [7, 7, 7, 7, 7], [1500, 1500, 1500, 1500, 1500], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [80, 80, 80, 80, 80], [8, 8, 8, 8, 8], [4, 3, 0, 0, 0]], "effectBurn": [null, "25/30/35/40/45", "10", "7", "1500", "1.5", "3", "0", "80", "8", "4/3/0/0/0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "IllaoiE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "IllaoiR", "name": "信仰震", "description": "偶像を地面に叩きつけて衝撃波を生み出し、周囲の敵に物理ダメージを与える。命中した敵チャンピオン1体ごとに、触手が1本発生する。", "tooltip": "偶像を地面にたたきつけ、周囲の敵ユニットに<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与え、命中した敵チャンピオン1体ごとに触手が1本発生する。<br /><br />このスキルを発動してから{{ duration }}秒間、触手は対象指定できなくなり、<keywordMajor>「叩きつけ」</keywordMajor>速度が50%増加する。また発動中は<spellName>「過酷なる教訓」</spellName>のクールダウンが2秒になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 95, 70], "cooldownBurn": "120/95/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "IllaoiR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "旧神の預言者", "description": "イラオイと、イラオイに<font color='#669900'>「器」</font>にされた者は、周囲の地形に一定時間ごとに触手を発生させる。触手は魂、<font color='#669900'>「器」</font>、イラオイの「過酷なる教訓」をくらった者を攻撃する。触手は命中すると敵に物理ダメージを与え、敵チャンピオンにダメージを与えた場合はイラオイを回復する。", "image": {"full": "Illaoi_P.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}