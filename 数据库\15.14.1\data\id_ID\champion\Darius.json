{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Darius": {"id": "<PERSON>", "key": "122", "name": "<PERSON>", "title": "the Hand of Noxus", "image": {"full": "Darius.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "122000", "num": 0, "name": "default", "chromas": true}, {"id": "122001", "num": 1, "name": "Lord <PERSON>", "chromas": false}, {"id": "122002", "num": 2, "name": "Bioforge Darius", "chromas": false}, {"id": "122003", "num": 3, "name": "Woad King <PERSON>", "chromas": false}, {"id": "122004", "num": 4, "name": "Dunkmaster <PERSON>", "chromas": true}, {"id": "122008", "num": 8, "name": "Academy Darius", "chromas": false}, {"id": "122014", "num": 14, "name": "Dread<PERSON> Darius", "chromas": false}, {"id": "122015", "num": 15, "name": "God-King <PERSON>", "chromas": false}, {"id": "122016", "num": 16, "name": "High Noon Darius", "chromas": true}, {"id": "122024", "num": 24, "name": "Lunar Beast Darius", "chromas": true}, {"id": "122033", "num": 33, "name": "Crime City Nightmare Darius", "chromas": false}, {"id": "122043", "num": 43, "name": "Spirit Blossom Darius", "chromas": false}, {"id": "122054", "num": 54, "name": "Porc<PERSON><PERSON> Darius", "chromas": false}, {"id": "122064", "num": 64, "name": "Divine God-King <PERSON>", "chromas": false}, {"id": "122065", "num": 65, "name": "Prestige Triumphant General <PERSON>", "chromas": false}], "lore": "Tak ada simbol kekuatan Noxus yang mele<PERSON><PERSON>, komandan yang paling ditakuti dan paling berp<PERSON><PERSON><PERSON> di Noxus. Berasal dari golongan rendah sampai mendapatkan gelar the Hand of Noxus, dia menebas habis musuh kerajaannya. Tak sedikit di antaranya adalah sesama bangsa Noxus. Dia tak pernah meragukan tujuannya dan tak pernah ragu mengangkat kapaknya. Siapa pun yang menentang komandan Legiun Trifarix ini tak akan mendapatkan ampunan.", "blurb": "Tak ada simbol kekuatan Noxus yang mele<PERSON><PERSON>, koman<PERSON> yang paling ditakuti dan paling berpengalaman di Noxus. Berasal dari golongan rendah sampai mendapatkan gelar the Hand of Noxus, dia menebas habis musuh kerajaannya. Tak sedikit di antaranya...", "allytips": ["Decimate adalah ability yang kuat untuk menekan musuh. Serang musuh dari jarak maksimum untuk efek terbaik.", "Makin banyak serangan yang kena sebelumnya, makin besar damage Noxian Guillotine. Gunakan Noxian Might untuk men<PERSON><PERSON><PERSON><PERSON> damage maksimum.", "<PERSON> sangat diuntungkan dari ketahanan yang diperkuat. Makin lama pertarungan berl<PERSON>, dia makin kuat."], "enemytips": ["Saat tarikan kapak Darius sedang cooldown, dia rentan terhadap serangan gangguan.", "Ability Darius untuk kabur dari pertempuran terbatas. <PERSON><PERSON> kamu unggul darinya saat itu, serang dia."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 652, "hpperlevel": 114, "mp": 263, "mpperlevel": 58, "movespeed": 340, "armor": 37, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 10, "hpregenperlevel": 0.95, "mpregen": 6.6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 5, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "DariusCleave", "name": "Decimate", "description": "<PERSON> mengum<PERSON>lkan tenaga dan mengayunkan kapaknya dalam lingkaran besar. <PERSON><PERSON>h yang terkena bilah kapaknya menerima damage lebih besar dibandingkan yang terkena gagangnya. <PERSON> mendapat heal berdasarkan champion musuh dan monster besar yang dihantam oleh bilah kapaknya.", "tooltip": "<PERSON> mengangkat lalu mengayunkan kapaknya, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ bladedamage }} physical damage</physicalDamage> dengan tepiannya dan <physicalDamage>{{ handledamage }} damage</physicalDamage> dengan gagangnya. Musuh yang dihantam oleh gagangnya tidak menerima stack <keywordMajor>Hemorrhage</keywordMajor>.<br /><br /><PERSON> memulihkan <healing>{{ e5 }}% Health yang hilang</healing> per champion musuh dan monster jungle besar yang dihantam oleh tepian kapaknya, hingga maksimum <healing>{{ e7 }}%</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Rasio Total AD", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e2 }}-> {{ e2NL }}", "{{ e1 }}%-> {{ e1NL }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [100, 110, 120, 130, 140], [50, 80, 110, 140, 170], [99, 99, 99, 99, 99], [0.1, 0.1, 0.1, 0.1, 0.1], [17, 17, 17, 17, 17], [35, 35, 35, 35, 35], [51, 51, 51, 51, 51], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/110/120/130/140", "50/80/110/140/170", "99", "0.1", "17", "35", "51", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "DariusCleave.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusNoxianTacticsONH", "name": "Crippling Strike", "description": "Serangan Darius berikutnya mengenai arteri vital musuh. <PERSON><PERSON>, <PERSON> <PERSON> mereka berk<PERSON>.", "tooltip": "Seranga<PERSON> <PERSON> berik<PERSON><PERSON> menghasilkan <physicalDamage>{{ empoweredattackdamage }} physical damage</physicalDamage> dan menera<PERSON>kan <status>Slow</status> sebanyak {{ e2 }}% selama {{ e5 }} detik.<br /><br />Ability ini mengembalikan biaya Mana dan mengurangi Cooldown sebanyak {{ e3 }}% jika target terbunuh.<br /><br /><rules>Ability ini memicu efek spell saat menghasilkan damage.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rasio Total AD"], "effect": ["{{ effect4amount*100.000000 }}-> {{ effect4amountnl*100.000000 }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [90, 90, 90, 90, 90], [50, 50, 50, 50, 50], [1.4, 1.45, 1.5, 1.55, 1.6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "90", "50", "1.4/1.45/1.5/1.55/1.6", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DariusNoxianTacticsONH.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusAxeGrabCone", "name": "Apprehend", "description": "<PERSON> men<PERSON> ka<PERSON>, se<PERSON><PERSON> pasif men<PERSON> physical damage-nya mengabaikan sekian persen Armor targetnya. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON> menya<PERSON> bersih musuh dengan kait kapaknya dan menarik mereka ke arahnya.", "tooltip": "<spellPassive>Pasif:</spellPassive> <PERSON> men<PERSON> {{ e1 }}% Armor Penetration.<br /><br /><spellActive>Aktif:</spellActive> Darius mengait dengan kapaknya, melakukan <status>Pull</status>, dan <status>Knock Up</status> serta <status>Slow</status> sebanyak {{ e2 }}% selama {{ e3 }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Persentase Armor Penetration", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cost }}-> {{ costNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 23.5, 21, 18.5, 16], "cooldownBurn": "26/23.5/21/18.5/16", "cost": [70, 60, 50, 40, 30], "costBurn": "70/60/50/40/30", "datavalues": {}, "effect": [null, [20, 25, 30, 35, 40], [40, 40, 40, 40, 40], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/25/30/35/40", "40", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [535, 535, 535, 535, 535], "rangeBurn": "535", "image": {"full": "DariusAxeGrabCone.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusExecute", "name": "Noxian G<PERSON>ine", "description": "<PERSON> melompat ke arah champion musuh dan melepaskan serangan mematikan, yang mengh<PERSON>lkan true damage. Damage ini bertambah dengan setiap stack Hemorrhage pada target. <PERSON><PERSON> berhasil membunuh musuh dengan Noxian Guillotine, cooldown-nya akan di-refresh dalam waktu singkat.", "tooltip": "<PERSON> melompat ke arah champion musuh dan melepaskan serangan mematikan, yang men<PERSON><PERSON>an <trueDamage>{{ damage }} true damage</trueDamage>. Untuk setiap <keywordMajor>Hemorrhage</keywordMajor> pada target, Ability ini menghasilkan tambahan {{ rdamagepercentperhemostack*100 }}% damage, hingga maksimum <trueDamage>{{ maximumdamage }} damage</trueDamage>.<br /><br /><PERSON>ka target terbunuh, <PERSON> bisa <recast>Recast</recast> Ability ini satu kali dalam kurun waktu {{ rrecastduration }} detik. Di rank 3, Ability ini tidak punya biaya Mana dan kill akan me-refresh Cooldown sepenuhnya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ rbasedamage }}-> {{ rbasedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 0], "costBurn": "100/100/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [460, 460, 460], "rangeBurn": "460", "image": {"full": "DariusExecute.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Hemorrhage", "description": "Serangan dan Ability Darius ya<PERSON> damage mengakibatkan musuh Bleed akibat physical damage selama 5 detik, bisa stack hingga 5 kali. <PERSON> murka dan mendapatkan Attack Damage sangat besar saat targetnya mencapai stack maksimum.", "image": {"full": "<PERSON>_<PERSON><PERSON>_Hemorrhage.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}