{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Varus": {"id": "Varus", "key": "110", "name": "Varus", "title": "Strzała Odkupienia", "image": {"full": "Varus.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "110000", "num": 0, "name": "default", "chromas": false}, {"id": "110001", "num": 1, "name": "Varus z Kryształem Rozkładu", "chromas": false}, {"id": "110002", "num": 2, "name": "Świetlisty Varus", "chromas": false}, {"id": "110003", "num": 3, "name": "Arktyczny Varus", "chromas": false}, {"id": "110004", "num": 4, "name": "Łamacz Serc Varus", "chromas": false}, {"id": "110005", "num": 5, "name": "Varus Chyża Strzała", "chromas": false}, {"id": "110006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "110007", "num": 7, "name": "Zdobywca Varus", "chromas": true}, {"id": "110009", "num": 9, "name": "Piekielny Varus", "chromas": true}, {"id": "110016", "num": 16, "name": "PROJEKT: Varus", "chromas": true}, {"id": "110017", "num": 17, "name": "Kosmiczny Łowca Varus", "chromas": true}, {"id": "110034", "num": 34, "name": "Varus w Samo Południe", "chromas": true}, {"id": "110044", "num": 44, "name": "Varus Śnieżnego Księżyca", "chromas": true}, {"id": "110053", "num": 53, "name": "Varus Empireum", "chromas": true}, {"id": "110060", "num": 60, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, jeden ze starożytnych Darkinów, był śmiertelnie niebezpiecznym zabójcą, kt<PERSON><PERSON> u<PERSON><PERSON><PERSON> gn<PERSON><PERSON><PERSON> ofiary, do<PERSON>row<PERSON><PERSON>jąc je do granic szaleństwa przed wykończeniem ich za pomocą strzał. Został uwięziony pod koniec Wielkiej Wojny Darkinów, ale wiele wieków później udało mu się uciec w odmienionym ciele dwóch ioniańskich łowców, którzy bezwiednie go wyzwolili i zostali przeklęci, by ni<PERSON><PERSON><PERSON>, kt<PERSON><PERSON> zawierał jego esencję. Varus poluje teraz na tych, którzy go uwięzili, aby dokonać na nich brutalnej zemsty, jednakże powiązane z nim dusze śmiertelników przeciwstawiają mu się na każdym kroku.", "blurb": "<PERSON><PERSON><PERSON>, jeden ze starożytnych Darkinów, był śmiertelnie niebezpiecznym zabójcą, k<PERSON><PERSON><PERSON> u<PERSON><PERSON> g<PERSON><PERSON><PERSON>iary, doprowadzając je do granic szaleństwa przed wykończeniem ich za pomocą strzał. Został uwięziony pod koniec Wielkiej Wojny Darkinów, ale wiele...", "allytips": ["Szybkie wydanie punktu na Kołczan Rozkładu pozwala nękać wrogich bohaterów i zabijać stwory.", "W trakcie krótkich walk często lepiej szyb<PERSON> wystr<PERSON>ić Przebijającą Strzałę, niż łado<PERSON> ją w pełni.", "Wykorzystaj ogromny zasięg Przebijającej Strzały, aby zaatakować wrogów przed walką lub gdy usiłują uciec."], "enemytips": ["<PERSON><PERSON><PERSON>ł ci<PERSON>, umiejętności Varusa zadadzą ci dodatkowe obrażenia.", "Ilekroć zabije wroga lub zdobędzie asystę, Varus zyskuje tymczasowo premię do prędkości ataku, przez co staje się niebezpieczny.", "Jeśli dotrze do ciebie macka superumiejęt<PERSON>ści Varusa, Ł<PERSON><PERSON><PERSON><PERSON>, nie moż<PERSON>z się ruszy<PERSON>. Jednak uciekając od niej, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>e umrze."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 3, "magic": 4, "difficulty": 2}, "stats": {"hp": 600, "hpperlevel": 105, "mp": 320, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.4, "attackspeedperlevel": 3.5, "attackspeed": 0.658}, "spells": [{"id": "VarusQ", "name": "Przebijająca Strzała", "description": "Varus przygotowuje się i wykonuje potężny strzał, zyskując tym większą premię do zasięgu i obrażeń, im dłużej przygotowywał się do strzału.", "tooltip": "<attention>Ładowanie:</attention> Varus naciąga cięciwę przed oddaniem kolejnego strzału, <status>spowal<PERSON>jąc</status> się o {{ e7 }}%. Je<PERSON><PERSON> Varus nie wystrzeli przez {{ e5 }} sek., anuluje umiejętność i odzyska {{ e4 }}% jej kosztu many.<br /><br /><attention>Wypuszczenie:</attention> Varus wypuszcza strzałę, zadając <physicalDamage>{{ totaldamagemintooltip }} pkt. obrażeń fizycznych</physicalDamage>, zmniejsz<PERSON>ch o {{ e3 }}% za każdego trafionego wroga (min. {{ e9 }}%). Efekty obrażeń i detonacji <keywordMajor>Rozkładu</keywordMajor> są zwiększone do maks. {{ maxchargeamp*100 }}%, zależnie od czasu ładowania (maks. <physicalDamage>{{ totaldamagemax }} pkt.</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Maksymalne obrażenia", "Skalowanie z dodatkowymi obrażeniami od ataku", "Czas odnowienia", "Koszt many"], "effect": ["{{ basedamagemax }} -> {{ basedamagemaxNL }}", "{{ tadratiomax }} -> {{ tadratiomaxNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [3, 3, 3, 3, 3], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [33, 33, 33, 33, 33], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "15", "50", "4", "3", "20", "0", "33", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VarusW", "name": "Kołcz<PERSON> Rozkładu", "description": "Biernie: Podstawowe ataki Varusa zadają dodatkowe obrażenia magiczne i nakładają Rozkład. Inne umiejętności Varusa detonują Rozkład, zadając obrażenia magiczne zależne od maksymalnego zdrowia celu. Użycie: Varus wzmacnia swoją następną Przebijającą Strzałę.", "tooltip": "<spellPassive>Biernie: </spellPassive> Ataki Varusa zadają dodatkowo <magicDamage>{{ onhitdamage }} pkt. obrażeń magicznych</magicDamage> i nakładają ładunki <keywordMajor>Rozkła<PERSON></keywordMajor> na {{ e3 }} sek. (maks. liczba ładunków: {{ e4 }}).<br /><br />Inne umiejętności Varusa detonują <keywordMajor>Rozkład</keywordMajor>, za każdy ładunek zadając <magicDamage>obrażenia magiczne równe {{ percenthpperstack }} maks. zdrowia celu</magicDamage> (nie więcej niż <magicDamage>{{ maxpercenthpperstack }} maks. zdrowia celu</magicDamage>). Detonacja <keywordMajor>Rozkładu</keywordMajor> na bohaterach lub potężnych potworach skraca czasy odnowienia podstawowych umiejętności Varusa o {{ cdrperblightstack*100 }}% ich maks. czasu odnowienia za każdy ładunek.<br /><br /><spellActive>Użycie:</spellActive> Następna <spellName>Przebijająca Strzała</spellName> Varusa zadaje dodatkowo <magicDamage>obrażenia magiczne równe {{ qempowerpercenthp }} brakującego zdrowia celu</magicDamage>, zwiększone do <magicDamage>obrażeń magicznych równych maks. {{ maxqempowerpercenthp }} brakującego zdrowia</magicDamage> zależnie od czasu ładowania.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia przy trafieniu", "Obrażenia od maksymalnego zdrowia", "Obrażenia od brakującego zdrowia"], "effect": ["{{ varuswonhitdamage }} -> {{ varuswonhitdamageNL }}", "{{ basepercenthpperstack*100.000000 }}% -> {{ basepercenthpperstacknl*100.000000 }}%", "{{ wqhealthdamage*100.000000 }}% -> {{ wqhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [40, 40, 40, 40, 40], "cooldownBurn": "40", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0.03, 0.035, 0.04, 0.045, 0.05], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [120, 120, 120, 120, 120], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0.03/0.035/0.04/0.045/0.05", "6", "3", "120", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VarusW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "VarusE", "name": "Grad Strzał", "description": "Varus wystrzeliwuje grad strzał, które zadają obrażenia fizyczne i plugawią dany obszar. Splugawiona ziemia spowalnia wrogów oraz osłabia ich efekty samoleczenia i regenerację.", "tooltip": "Varus wystrzeliwuje grad strzał, kt<PERSON><PERSON> zadaj<PERSON> <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> i plugawią dany obszar na {{ e3 }} sek., <status>spowal<PERSON><PERSON><PERSON>c</status> wrogów o {{ slowpercent*-100 }}% i nakładając na nich Głębokie Rany o wartości {{ grievousamount*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent*-100.000000 }}% -> {{ slowpercentnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [60, 100, 140, 180, 220], [-0.3, -0.35, -0.4, -0.45, -0.5], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/100/140/180/220", "-0.3/-0.35/-0.4/-0.45/-0.5", "4", "0", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VarusR", "name": "Łańcuch Zepsucia", "description": "Varus wysy<PERSON> mac<PERSON> z<PERSON>, która unieruchamia pierwszego trafionego wrogiego bohatera i rozprzestrzenia się na pobliskich, niezainfekowanych bohaterów, unieruchamiając ich.", "tooltip": "Varus wysyła mackę spaczenia, <status>unieruchamiając</status> pierwszego trafionego bohatera na {{ e2 }} sek. i zadając <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>. Podczas działania tej umiejętności na <status>unieruchomionych</status> wrogów zostaje nałożona następująca liczba ładunków <keywordMajor>Rozkładu</keywordMajor>: {{ e4 }}.<br /><br />Spaczenie rozprzestrzenia się z celu na niezainfekowanych wrogich bohaterów. Jeśli ich dosięgnie, otr<PERSON><PERSON><PERSON>ą tyle samo obrażeń i zostaną <status>unieruchomieni</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [2, 2, 2], [650, 650, 650], [3, 3, 3], [0.5, 0.5, 0.5], [600, 600, 600], [1.75, 1.75, 1.75], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "2", "650", "3", "0.5", "600", "1.75", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "VarusR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Uosobienie Zemsty", "description": "Przy zabójstwach i asystach Varus zyskuje tymczasowo premię do obrażeń od ataku i mocy umiejętności. Jest ona wi<PERSON><PERSON>, je<PERSON><PERSON> ofiarą jest bohater.", "image": {"full": "VarusPassive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}