{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rengar": {"id": "<PERSON><PERSON>", "key": "107", "name": "<PERSON><PERSON>", "title": "il cacciatore orgoglioso", "image": {"full": "Rengar.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "107000", "num": 0, "name": "default", "chromas": false}, {"id": "107001", "num": 1, "name": "<PERSON><PERSON> Cac<PERSON>tore di Teste", "chromas": true}, {"id": "107002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "107003", "num": 3, "name": "Rengar SSW", "chromas": false}, {"id": "107008", "num": 8, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "107015", "num": 15, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "107023", "num": 23, "name": "Rengar <PERSON>o delle Sabbie", "chromas": true}, {"id": "107030", "num": 30, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "107040", "num": 40, "name": "<PERSON><PERSON> della strada", "chromas": true}], "lore": "Rengar è un pericoloso cacciatore vastayano che vive per il brivido di trovare e uccidere le creature più pericolose. Gira il mondo in cerca delle belve più feroci, in particolare di Kha'Zix, la creatura del Vuoto che gli ha cavato un occhio. Rengar dà la caccia alle sue prede non per il cibo o per la gloria, ma per la bellezza della caccia stessa.", "blurb": "Rengar è un pericoloso cacciatore vastayano che vive per il brivido di trovare e uccidere le creature più pericolose. Gira il mondo in cerca delle belve più feroci, in particolare di Kha'Zix, la creatura del Vuoto che gli ha cavato un occhio. Rengar dà...", "allytips": ["Usa l'abilità suprema di Rengar per trovare e uccidere i bersagli ad alta priorità durante le schermaglie e gli scontri a squadre.", "La maggior parte del potere di Rengar deriva dall'uso delle abilità potenziate al momento giusto, quindi scegli bene!", "Assicurati di passare attraverso l'erba alta quando stai inseguendo un nemico per approfittare dell'abilità passiva di Rengar."], "enemytips": ["Rengar guadagna un'abilità potenziata quando la sua barra delle risorse è piena. Cerca di confrontarlo quando ha ancora la barra vuota.", "L'abilità passiva di Rengar gli permette di saltare fuori dall'erba alta; evita di combatterlo vicino all'erba alta.", "L'abilità suprema di Rengar lo mimetizza e piazza un indicatore sopra al nemico più vicino."], "tags": ["Assassin", "Fighter"], "partype": "Ferocia", "info": {"attack": 7, "defense": 4, "magic": 2, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 4, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.667}, "spells": [{"id": "RengarQ", "name": "Barbarie", "description": "Al prossimo attacco Rengar infilza brutalmente il bersaglio infliggendogli danni bonus.<br><br><PERSON><PERSON><PERSON>: infligge danni aumentati e dona velocità d'attacco.", "tooltip": "I prossimi 2 attacchi di Rengar ottengono un <attackSpeed>{{ e5 }}% velocità d'attacco</attackSpeed>. Il primo attacco infligge un colpo critico da <physicalDamage>{{ F4 }} (%i:scaleAD%%i:scaleCrit%) danni fisici</physicalDamage>.<br /><br /><keywordMajor>Ferocia massima:</keywordMajor> il primo attacco infligge un colpo critico da <physicalDamage>{{ F5 }} (%i:scaleLevel%%i:scaleAD%%i:scaleCrit%) danni fisici</physicalDamage> e conferisce a Rengar <attackSpeed>{{ empoweredqas }} velocità d'attacco</attackSpeed> per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico totale", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [100, 105, 110, 115, 120], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [40, 40, 40, 40, 40], [3, 3, 3, 3, 3], [0.2, 0.3, 0.4, 0.5, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/60/90/120/150", "100/105/110/115/120", "5", "5", "40", "3", "0.2/0.3/0.4/0.5/0.6", "0", "0", "0"], "vars": [], "costType": "Genera 1 di Ferocia", "maxammo": "1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RengarQ.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Genera 1 di Ferocia"}, {"id": "RengarW", "name": "Ruggito di battaglia", "description": "Rengar lancia un ruggito di battaglia, infliggendo danni ai nemici e guarendo parte degli ultimi danni subiti.<br><br><PERSON><PERSON><PERSON>: inoltre, interrompe gli effetti di controllo.", "tooltip": "<PERSON><PERSON> ruggisce, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici vicini e ripristinando un <healing>{{ damagepercentagehealed }}%</healing> dei danni che ha subito negli ultimi {{ e3 }} secondi come salute.<br /><br /><keywordMajor>Ferocia massima:</keywordMajor> infligge <magicDamage>{{ totaldamageempowered }} danni magici</magicDamage> e purifica Rengar dagli effetti di controllo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50", "1.5", "1.5", "100", "0", "0", "0", "0", "0"], "vars": [], "costType": "Genera 1 di Ferocia", "maxammo": "1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RengarW.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Genera 1 di Ferocia"}, {"id": "RengarE", "name": "Colpo di bola", "description": "Rengar lancia una bola, rallentando il primo bersaglio colpito per un breve periodo.<br><br><PERSON><PERSON><PERSON>: immobilizza il bersaglio.", "tooltip": "Rengar lancia una bola, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al primo nemico colpito, rivelandolo e <status>rallentandolo</status> di un {{ e2 }}% per {{ e3 }} secondi.<br /><br /><keywordMajor>Ferocia massima:</keywordMajor> infligge <physicalDamage>{{ totalempowereddamage }} danni fisici</physicalDamage> e <status>immobilizza</status> per {{ e4 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [55, 100, 145, 190, 235], [30, 45, 60, 75, 90], [1.75, 1.75, 1.75, 1.75, 1.75], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "55/100/145/190/235", "30/45/60/75/90", "1.75", "1.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Genera 1 di Ferocia", "maxammo": "1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RengarE.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Genera 1 di Ferocia"}, {"id": "RengarR", "name": "<PERSON><PERSON><PERSON><PERSON>ccia", "description": "L'istinto da predatore di Rengar prende il sopravvento, <font color='#cd90ee'>mi<PERSON><PERSON><PERSON><PERSON><PERSON></font> e rivelando il campione nemico più vicino entro un ampio raggio intorno a lui. <PERSON><PERSON><PERSON> Brivido della caccia, <PERSON><PERSON> ottiene velocità di movimento e, anche se non è nell'erba alta, può balzare sul nemico individuato riducendone l'armatura.", "tooltip": "<spellPassive>Passiva:</spellPassive> Ren<PERSON> esegue un attacco in salto durante <keywordStealth><PERSON><PERSON><PERSON></keywordStealth>.<br /><br /><spellActive>Attiva:</spellActive> <PERSON><PERSON> ottiene <speed>{{ stealthms }}% velocità di movimento</speed> e <keywordStealth>Visione magica</keywordStealth> in una piccola area intorno al campione nemico più vicino per {{ stealthduration }} secondi.<br /><br />Dopo {{ fadetime }} secondi, <PERSON><PERSON> entra in <keywordStealth>Mimesi</keywordStealth> e può balzare su un nemico senza essere nell'erba alta. Balzare addosso al campione più vicino infligge ulteriori <physicalDamage>{{ bonusdamage }} danni fisici</physicalDamage>, danneggia <scaleArmor>{{ armorshred }} armatura</scaleArmor> per {{ armorshredduration }} secondi e conclude questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Riduzione armatura", "<PERSON><PERSON>", "Velocità di movimento", "Portata visuale", "Ricarica"], "effect": ["{{ armorshred }} -> {{ armorshredNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ stealthms }}% -> {{ stealthmsNL }}%", "{{ selfvisionrange }} -> {{ selfvisionrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [2500, 3000, 3500], "rangeBurn": "2500/3000/3500", "image": {"full": "RengarR.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Predatore elusivo", "description": "Quando si trova nell'erba alta, <PERSON><PERSON> balza sul suo bersaglio con l'attacco base.<br><br>Rengar genera Ferocia ogni volta che lancia un'abilità. Con la Ferocia al massimo, la sua prossima abilità è potenziata.<br><br>Uccidere campioni nemici conferisce trofei per la <font color='#BBFFFF'>Collana di denti</font> di <PERSON>, donandogli attacco fisico bonus.", "image": {"full": "Rengar_P.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}