{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Volibear": {"id": "Volibear", "key": "106", "name": "ボリベア", "title": "無慈悲の嵐", "image": {"full": "Volibear.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "106000", "num": 0, "name": "default", "chromas": false}, {"id": "106001", "num": 1, "name": "雷帝ボリベア", "chromas": false}, {"id": "106002", "num": 2, "name": "北方戦線の雷嵐ボリベア", "chromas": false}, {"id": "106003", "num": 3, "name": "ルーンガード ボリベア", "chromas": false}, {"id": "106004", "num": 4, "name": "ボリベア警部", "chromas": false}, {"id": "106005", "num": 5, "name": "エル・ラジョ ボリベア", "chromas": false}, {"id": "106006", "num": 6, "name": "千刺の熊", "chromas": false}, {"id": "106007", "num": 7, "name": "双嵐龍ボリベア", "chromas": true}, {"id": "106009", "num": 9, "name": "プレステージ双嵐龍ボリベア", "chromas": false}, {"id": "106019", "num": 19, "name": "墨影のボリベア", "chromas": false}], "lore": "彼を崇敬する者にとって、ボリベアは嵐を体現した存在だ。破壊的で、野蛮で、頑固なまでに意思が固く、彼は定命の者たちがフレヨルドのツンドラに足を踏み入れる前からそこに存在しており、彼と半神の同族たちが創り出したその土地を獰猛に守ろうとしている。ボリベアは文明とそれがもたらす弱さに激しい憎悪を募らせており、この土地を野生のままで自由に血が流されていた昔の姿に戻すために、敵対する者すべてに自身の爪と牙と容赦なき雷鳴を向けて戦いを挑む。", "blurb": "彼を崇敬する者にとって、ボリベアは嵐を体現した存在だ。破壊的で、野蛮で、頑固なまでに意思が固く、彼は定命の者たちがフレヨルドのツンドラに足を踏み入れる前からそこに存在しており、彼と半神の同族たちが創り出したその土地を獰猛に守ろうとしている。ボリベアは文明とそれがもたらす弱さに激しい憎悪を募らせており、この土地を野生のままで自由に血が流されていた昔の姿に戻すために、敵対する者すべてに自身の爪と牙と容赦なき雷鳴を向けて戦いを挑む。", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 7, "defense": 7, "magic": 4, "difficulty": 3}, "stats": {"hp": 650, "hpperlevel": 104, "mp": 350, "mpperlevel": 70, "movespeed": 340, "armor": 31, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.75, "mpregen": 6.25, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "VolibearQ", "name": "稲妻の猛攻", "description": "敵に向かう際の移動速度が増加し、発動後最初に通常攻撃を行った対象に<status>スタン効果</status>とダメージを与える。", "tooltip": "{{ duration }}秒間、<speed>移動速度が{{ minspeedcalc }}</speed>増加する。敵チャンピオンに向かう場合は倍になって<speed>{{ maxspeedcalc }}</speed>増加する。発動後最初に行う通常攻撃が対象に<physicalDamage>{{ calculateddamage }}の物理ダメージ</physicalDamage>を与え、{{ stunduration }}秒間<status>スタン</status>させる。<br /><br />対象を<status>スタン</status>させる前に敵から<status>移動不能効果</status>を受けると激怒し、スキルは早めに終了するがクールダウンが解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "移動速度", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ minspeed*100.000000 }}% -> {{ minspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VolibearQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VolibearW", "name": "激昂の斬撃", "description": "敵にダメージと通常攻撃時効果を与えてマークする。同じ対象にもう一度発動すると追加ダメージを与えて自身の体力を回復する。", "tooltip": "敵に咬みついて<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与えて{{ markduration }}秒間マークする。<br /><br />このスキルをマークした対象に使用するとダメージが<physicalDamage>{{ empowereddamage }}</physicalDamage>に増加し、自身は<healing>{{ baseheal }}(+減少体力の{{ percentmissinghealthhealingratio }})の体力</healing>を回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "基本体力回復量", "減少体力割合", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ healpercent*100.000000 }}% -> {{ healpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "VolibearW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VolibearE", "name": "天破の一撃", "description": "指定地点に雷を落として周囲の敵にダメージとスロウ効果を与え、自身が範囲内にいた場合はシールドを獲得する。", "tooltip": "雷を落とす雷雲を召喚する。雷は<magicDamage>{{ totaldamagetooltip }}(+最大体力の{{ percentdamage*100 }}%)の魔法ダメージ</magicDamage>を与えて、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />自身が効果範囲内にいた場合は、{{ shieldduration }}秒間、<shield>{{ shieldapratiotooltip }}(+最大体力の{{ shieldamount*100 }}%)のシールド</shield>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "割合ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percentdamage*100.000000 }}% -> {{ percentdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VolibearE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VolibearR", "name": "嵐を起こす者", "description": "指定地点に飛びかかって踏みつけた敵にスロウ効果とダメージを与え、自身は体力が増加する。着地地点の近くにある敵のタワーは一時的に無効化される。", "tooltip": "変身して跳躍し、{{ transformduration }}秒間、<healing>体力が{{ healthamount }}</healing>、 射程距離が{{ bonusattackrange }}増加する。<br /><br />着地時に大地がひび割れて周囲のタワーを{{ towerdisableduration }}秒間<status>無効化</status>し、<physicalDamage>{{ towerdamagetooltip }}の物理ダメージ</physicalDamage>を与える。周囲の敵には{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。このスロウ効果は1秒かけて元に戻る。自身の真下にいた敵には<physicalDamage>{{ sweetspotdamagetooltip }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "増加体力", "タワー無効化時間", "クールダウン"], "effect": ["{{ sweetspotdamage }} -> {{ sweetspotdamageNL }}", "{{ healthamount }} -> {{ healthamountNL }}", "{{ towerdisableduration }} -> {{ towerdisabledurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 135, 110], "cooldownBurn": "160/135/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [75, 115, 155], [9, 9, 9], [12, 12, 12], [8, 8, 8], [0.08, 0.08, 0.08], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "75/115/155", "9", "12", "8", "0.08", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "VolibearR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "無慈悲の嵐", "description": "通常攻撃およびスキルの使用で攻撃速度が増加していき、最終的に通常攻撃が周囲の敵に追加魔法ダメージを与えるようになる。", "image": {"full": "Volibear_Icon_P.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}