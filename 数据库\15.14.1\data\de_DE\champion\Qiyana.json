{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Qiyana": {"id": "<PERSON><PERSON>", "key": "246", "name": "<PERSON><PERSON>", "title": "Gebieterin der Elemente", "image": {"full": "Qiyana.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "246000", "num": 0, "name": "default", "chromas": false}, {"id": "246001", "num": 1, "name": "Schlachtboss-Qiyana", "chromas": true}, {"id": "246002", "num": 2, "name": "True Damage Qiyana", "chromas": false}, {"id": "246010", "num": 10, "name": "True Damage Qiyana (Prestige)", "chromas": false}, {"id": "246011", "num": 11, "name": "Kriegerkönigin <PERSON>", "chromas": true}, {"id": "246020", "num": 20, "name": "Sc<PERSON>ckklingen-<PERSON><PERSON>", "chromas": true}, {"id": "246021", "num": 21, "name": "True Damage Qiyana (Prestige 2022)", "chromas": false}, {"id": "246030", "num": 30, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "246040", "num": 40, "name": "La Ilusión Qiyana", "chromas": true}, {"id": "246050", "num": 50, "name": "Academia Certaminis-Qiyana (Prestige)", "chromas": false}], "lore": "<PERSON><PERSON><PERSON> von Ixaocan schmiedet Qiyana einen skrupellosen Plan, um den Thron der Yun Tal zu besteigen. Die Letzte in der Thronfolge tritt jedem, der sich ihr in den Weg stellt, mit dreistem Selbstbewusstsein und beispielloser Beherrschung der Elementarmagie entgegen. <PERSON><PERSON> hält sich für das größte Elementartalent in der Geschichte von Ixaocan. Da sogar das Land selbst jedem ihrer Befehle gehorcht, ist sie der Ansicht, dass ihr nicht nur die Herrschaft über eine Stadt, sondern über ein ganzes Kaiserreich zusteht.", "blurb": "<PERSON><PERSON> von Ixa<PERSON> schmiedet Qiyana einen skrupellosen Plan, um den Thron der Yun Tal zu besteigen. Die Letzte in der Thronfolge tritt jedem, der sich ihr in den Weg stellt, mit dreistem Selbstbewusstsein und beispielloser Beherrschung der...", "allytips": [], "enemytips": [], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 2, "magic": 4, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 124, "mp": 375, "mpperlevel": 60, "movespeed": 335, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.1, "attackspeed": 0.688}, "spells": [{"id": "QiyanaQ", "name": "<PERSON>ement<PERSON><PERSON> / <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> schwingt ihre Waffe, veru<PERSON>cht Schaden und löst abhäng<PERSON> von ihrem Element einen zusätzlichen Effekt aus.", "tooltip": "<PERSON><PERSON> keine <keywordMajor>Verzauberung</keywordMajor> hat, schwingt sie ihre Ringklinge und fügt <PERSON> in einem kleinen Bereich <physicalDamage>{{ vanilladamage }}&nbsp;normalen Schaden</physicalDamage> zu. Hat sie eine Verzauberung, erhält die Fähigkeit zusätzliche Reichweite sowie einen zusätzlichen Effekt, der von der Art der <keywordMajor>Verzauberung</keywordMajor> abhängig ist:<li><keywordMajor>Eis-Verzauberung</keywordMajor>: Getroffene Gegner werden kurz <status>festgehalten</status> und dann {{ slowduration }}&nbsp;Sekunde lang um {{ slowpotency*-100 }}&nbsp;% <status>verlangsamt</status>.<li><keywordMajor>Stein-Verzauberung</keywordMajor>: <PERSON>ügt <PERSON> mit weniger als {{ critthreshold*100 }}&nbsp;% Leben zusätzlich <physicalDamage>{{ tremordamage }}&nbsp;normalen Schaden</physicalDamage> zu.<li><keywordMajor>Wildnis-Verzauberung</keywordMajor>: Erschafft ein Laubdach, das Qiyana <keywordStealth>unsichtbar</keywordStealth> macht und ihr <speed>{{ haste*100 }}&nbsp;% Lauftempo</speed> gewährt.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ vanillabase }} -> {{ vanillabaseNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "QiyanaQ.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QiyanaW", "name": "Terraformung", "description": "<PERSON><PERSON> springt zum Zielort und speichert ein Element in ihrer Waffe. Während ihre Waffe verzaubert ist, verursachen ihre Angriffe und Fähigkeiten zusätzlichen Schaden. ", "tooltip": "<spellPassive>Passiv:</spellPassive> Während Qiyana<PERSON>fe <keywordMajor>ve<PERSON><PERSON><PERSON></keywordMajor> ist, erhält sie <attackSpeed>{{ attackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed> und ihre Angriffe verursachen zusätzlich <magicDamage>{{ onhitdamage }}&nbsp;magischen Schaden</magicDamage>. Sie erhält außerdem <speed>{{ passivems*100 }}&nbsp;% Lauftempo</speed>, wenn sie sich außerhalb des Kampfes in der Nähe des entsprechenden Terraintyps befindet.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON><PERSON> springt zu einem nahen hohen Gras, Terrain oder Fluss und <keywordMajor>verz<PERSON>bert</keywordMajor> ihre Waffe mit diesem bestimmten Terraintyp. Dadurch wird die Abklingzeit von <spellName>Elementarer Zorn / <PERSON>ling<PERSON> von I<PERSON>tal</spellName> zurückgesetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Trefferschaden", "Lauftempo", "Zusätzliches Angriffstempo", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ passivems*100.000000 }}&nbsp;% -> {{ passivemsnl*100.000000 }}&nbsp;%", "{{ attackspeed*100.000000 }}&nbsp;% -> {{ attackspeednl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "QiyanaW.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QiyanaE", "name": "Unbescheidenheit", "description": "<PERSON><PERSON> springt zu einem Gegner und fügt ihm Schaden zu.", "tooltip": "<PERSON><PERSON> springt durch einen Gegner hindurch und fügt ihm <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "QiyanaE.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QiyanaR", "name": "Elementares Supertalent", "description": "<PERSON><PERSON> l<PERSON> eine <PERSON> aus, die jedes getroffene Element explodieren lässt, wodurch nahe Gegner betäubt werden und Schaden erleiden.", "tooltip": "<PERSON><PERSON> löst eine <PERSON> aus, die Gegner <status>zurückstößt</status> und bei Kontakt mit Terrain explodiert. Die Explosion zieht dann am Rand des gesamten Terrains entlang, <status>betäubt</status> Gegner zwischen 0,5 und {{ stunduration }}&nbsp;Sekunde(n) lang und fügt ihnen <physicalDamage>normalen Schaden</physicalDamage> in Höhe von <physicalDamage>{{ damage }} plus {{ missinghealthdamagerock }} ihres maximalen Lebens</physicalDamage> zu. Die Dauer der <status>Betäubung</status> verringert sich mit der zurückgelegten Distanz der Schockwelle.<br /><br />Der Fluss oder hohes Gras, das von der Schockwelle durchlaufen wurde, explodiert nach kurzer Verzögerung ebenfalls und verursacht den gleichen Schaden und die gleiche <status>Betäubung</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "QiyanaR.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Kaiserliches Vorrecht", "description": "<PERSON><PERSON>s erster normaler Angriff oder ihre erste Grundfähigkeit gegen jeden Gegner verursacht zusätzlichen Schaden.", "image": {"full": "Qiyana_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}