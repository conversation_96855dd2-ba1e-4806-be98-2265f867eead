{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiora": {"id": "<PERSON><PERSON>", "key": "114", "name": "<PERSON><PERSON>", "title": "the Grand Duelist", "image": {"full": "Fiora.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "114000", "num": 0, "name": "default", "chromas": false}, {"id": "114001", "num": 1, "name": "Royal Guard Fiora", "chromas": false}, {"id": "114002", "num": 2, "name": "Nightraven <PERSON>", "chromas": false}, {"id": "114003", "num": 3, "name": "Headmistress <PERSON><PERSON>", "chromas": true}, {"id": "114004", "num": 4, "name": "PROJECT: <PERSON><PERSON>", "chromas": true}, {"id": "114005", "num": 5, "name": "Pool Party Fiora", "chromas": true}, {"id": "114022", "num": 22, "name": "Soaring Sword Fiora", "chromas": false}, {"id": "114023", "num": 23, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "114031", "num": 31, "name": "iG Fiora", "chromas": true}, {"id": "114041", "num": 41, "name": "Pulsefire Fiora", "chromas": false}, {"id": "114050", "num": 50, "name": "Lunar Beast Fiora", "chromas": false}, {"id": "114051", "num": 51, "name": "Prestige Lunar Beast Fiora", "chromas": false}, {"id": "114060", "num": 60, "name": "Bewitching <PERSON><PERSON>", "chromas": false}, {"id": "114069", "num": 69, "name": "Faerie Court Fiora", "chromas": true}, {"id": "114080", "num": 80, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "114089", "num": 89, "name": "Battle Queen Fiora", "chromas": true}], "lore": "<PERSON><PERSON>, duelist paling di<PERSON><PERSON><PERSON> di seluruh Valoran ini terkenal dengan sikapnya yang kasar dan pikiran liciknya, serta kecepatan rapier bluesteel-nya. <PERSON><PERSON> di <PERSON> di <PERSON>era<PERSON>, <PERSON><PERSON> mengambil alih kendali keluarga dari ayahnya, set<PERSON>h terjadi skandal yang hampir menghancurkan keluarganya. Reputasi keluarga Laurent telah hancur, tapi Fiora berusaha keras memulihkan kehormatan keluarganya dan mengembalikan mereka ke tempat yang selayaknya, di antara para pembesar dan pemuka Demacia.", "blurb": "<PERSON><PERSON>, duelist paling di<PERSON><PERSON><PERSON> di seluruh Valoran ini terkenal dengan sikapnya yang kasar dan pikiran liciknya, serta kecepatan rapier bluesteel-nya. <PERSON><PERSON> di <PERSON>, <PERSON><PERSON> mengambil alih kendali keluarga dari ayahnya, setelah...", "allytips": ["Berkat Duelist's Dance, <PERSON>ora sangat unggul dalam trading cepat. Gunakan boost Move Speed setelah mengenai Vital untuk kabur tanpa luka atau menyiapkan serangan berikutnya.", "Grand Challenge memungkinkan Fiora mengalahkan bahkan lawan paling tangguh dan pulih jika berhasil. <PERSON><PERSON> jangan ragu menyerang barisan depan musuh."], "enemytips": ["Duelist's Dance member<PERSON> tahu kamu dari mana <PERSON> akan <PERSON>, jadi be<PERSON> untuk membalas se<PERSON>.", "Waspadai ketika kamu cast efek crowd control Immobilize pada Fiora.  Jika Riposte siap digunakan, dia bisa memanfaatkan kekuatannya untuk menyerangmu."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 4, "magic": 2, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 345, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.69}, "spells": [{"id": "FioraQ", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> me<PERSON>n lunge ke arah tertentu dan menusuk musuh di sekitar, <PERSON><PERSON><PERSON><PERSON><PERSON> physical damage dan men<PERSON><PERSON><PERSON> efek on-hit.", "tooltip": "<PERSON>ora melakukan lunge ke arah tertentu dan menusuk musuh, ward, atau bangunan terdekat, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>. Serangan akan memprioritaskan <keywordMajor>Vital</keywordMajor> dan musuh yang akan di-kill.<br /><br /><PERSON><PERSON>ora menyerang musuh, Cooldown Ability ini akan dikurangi sebesar {{ cdrefundpercent*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON>sio <PERSON> Bonus", "Cooldown"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ badratio*100.000000 }}%-> {{ badrationl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.25, 9.5, 7.75, 6], "cooldownBurn": "13/11.25/9.5/7.75/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FioraQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FioraW", "name": "Riposte", "description": "Fiora menangkis semua damage dan efek crowd control yang masuk selama beberapa saat, lalu menusuk ke arah tertentu. Tusukan ini akan memberikan efek slow pada champion musuh pertama yang kena, atau menerapkan stun jika Fiora menangkis efek immobilize dengan ability ini.", "tooltip": "<PERSON>ora menangkis semua damage, crowd control, dan efek negatif yang akan datang selama {{ parryduration }} detik, lalu menusuk. <PERSON><PERSON><PERSON> menghasilkan <magicDamage>{{ stabdamage }} magic damage</magicDamage> ke champion pertama yang kena dan menerapkan <status>Slow</status> <speed>Move Speed</speed> sebesar {{ msslowpercent*-100 }}%<attackSpeed> dan Attack Speed sebesar {{ attackslowpercent*-100 }}% </attackSpeed> selama {{ ccduration }} detik. Jika Fiora menangkis efek <status>Immobilize</status>, musuh yang tertusuk akan terkena <status>Stun</status> alih-alih terkena <status>Slow</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "FioraW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FioraE", "name": "Bladework", "description": "Attack speed Fiora ditingkatkan untuk dua serangan berikutnya. Serangan pertama akan memberikan efek slow pada target dan serangan kedua akan men<PERSON><PERSON><PERSON> critical strike.", "tooltip": "Fiora mendapatkan <attackSpeed>{{ aspercent*100 }}% Attack Speed</attackSpeed> untuk dua Serangan berikutnya. Serangan pertama menerapkan <status>Slow</status> sebesar {{ slowpercent*-100 }}% selama {{ slowduration }} detik. Serangan kedua akan selalu men<PERSON> critical strike dengan <physicalDamage>{{ attacktwopercenttad*100 }}% damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Critical Strike Damage", "Cooldown", "Attack Speed"], "effect": ["{{ attacktwopercenttad*100.000000 }}%-> {{ attacktwopercenttadnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ aspercent*100.000000 }}%-> {{ aspercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "FioraE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FioraR", "name": "Grand Challenge", "description": "Fiora mengungkap keempat Vital pada champion musuh dan mendapatkan Move Speed saat berada di dekatnya. Jika Fiora menghantam keempat Vital atau jika target mati setelah ia menghantam setidaknya 1 Vital, Fiora dan sekutunya di area tersebut akan menerima heal selama beberapa detik berikutnya.", "tooltip": "<spellPassive>Pasif:</spellPassive> <spellName>Duelist's Dance</spellName> <speed>Move Speed</speed> bonus meningkat sebesar {{ percentms*100 }}%.<br /><br /><spellActive>Aktif:</spellActive> Fiora mengungkap keempat <keywordMajor>Vital</keywordMajor> pada champion untuk maksimum <trueDamage>{{ spell.fiorapassive:rdamagetotal }} true damage Health maksimum</trueDamage> dan mendapatkan <spellName>Duelist's Dance</spellName> <speed>Move Speed</speed> bonus saat berada di dekat target.<br /><br />Jika Fiora menyerang semua <keywordMajor>Vital</keywordMajor> dalam waktu {{ markduration }} detik, atau jika target mati setelah dia mengenai setidaknya satu Vital, Fiora akan memulihkan <healing>{{ healpersecondcalc }} Health per detik</healing> untuk champion sekutu di sekitar selama {{ healduration }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Move Speed Duelist", "<PERSON><PERSON> <PERSON>"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ percentms*100.000000 }}%-> {{ percentmsnl*100.000000 }}%", "{{ healpersecond }}-> {{ healpersecondNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "FioraR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Duelist's Dance", "description": "<PERSON>ora telah mengung<PERSON> <keywordMajor>Vital</keywordMajor> pada Champion ini. <PERSON>ka dia mengenai <keywordMajor>Vital</keywordMajor>, <PERSON>ora <healing>memulihkan Health</healing> dan mendapatkan <speed>Move Speed</speed>.", "image": {"full": "Fiora_P.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}