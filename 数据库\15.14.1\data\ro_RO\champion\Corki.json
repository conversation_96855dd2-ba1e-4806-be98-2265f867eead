{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Corki": {"id": "<PERSON><PERSON>", "key": "42", "name": "<PERSON><PERSON>", "title": "bombardierul curajos", "image": {"full": "Corki.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "42000", "num": 0, "name": "default", "chromas": false}, {"id": "42001", "num": 1, "name": "<PERSON>i <PERSON>", "chromas": false}, {"id": "42002", "num": 2, "name": "<PERSON><PERSON> tobogan <PERSON> g<PERSON>", "chromas": false}, {"id": "42003", "num": 3, "name": "<PERSON><PERSON>, baron<PERSON> r<PERSON>ș<PERSON>", "chromas": false}, {"id": "42004", "num": 4, "name": "<PERSON><PERSON> Hot Rod", "chromas": false}, {"id": "42005", "num": 5, "name": "<PERSON><PERSON> c<PERSON>lare pe urf", "chromas": false}, {"id": "42006", "num": 6, "name": "<PERSON><PERSON> aripi de dragon", "chromas": true}, {"id": "42007", "num": 7, "name": "<PERSON><PERSON> ", "chromas": false}, {"id": "42008", "num": 8, "name": "Corki Arcade", "chromas": true}, {"id": "42018", "num": 18, "name": "<PERSON><PERSON> corgi", "chromas": true}, {"id": "42026", "num": 26, "name": "<PERSON><PERSON> astronaut", "chromas": true}], "lore": "Corki este un pilot yordle care iubește două lucruri mai presus de orice: zborul și mustața sa extraordinară... dar nu neapărat în ordinea asta. După ce a plecat din Orașul Bandle, s-a stabilit în Piltover și s-a îndrăgostit de minunatele mașinării pe care le-a găsit acolo. S-a implicat puternic în dezvoltarea dispozitivelor zburătoare, conducând forța de apărare aeriană Zburătorii Veseli, formată numai din veterani căliți în luptă. Corki patrulează înălțimile din jurul orașului de care s-a atașat, își păstrează calmul când e atacat și n-a întâlnit niciodată vreo problemă care să nu poată fi rezolvată cu o ploaie zdravănă de proiectile.", "blurb": "Corki este un pilot yordle care iubește două lucruri mai presus de orice: zborul și mustața sa extraordinară... dar nu neapărat în ordinea asta. După ce a plecat din Orașul Bandle, s-a stabilit în Piltover și s-a îndrăgostit de minunatele mașinării pe...", "allytips": ["''Bomba cu fosfor'' poate fi folosită la dezvăluirea unităților inamice care s-ar putea ascunde în tufișurile din apropiere.", "''Walkirie'' poate fi folosită și în scop defensiv, așa că încearcă să o lansezi când vrei să scapi repede.", "Corki poate continua să atace cât își folo<PERSON> ''Mitraliera''. Creșterea ''Mitralierei'' până la nivelul maxim e esențială pentru un joc desăvârșit cu Corki."], "enemytips": ["Ai grijă la ''Ploaia de proiectile'' a lui <PERSON>. Abilitatea provoacă daune AoE, ceea ce înseamnă că te poate lovi chiar și dacă stai în spatele minionilor.", "Corki e vulnerabil după ce folosește ''Walkirie'' sau ''Livrare specială'', deci e o idee bună să încerci să-l ucizi după ce le folosește pentru a intra în luptă."], "tags": ["Marksman", "Mage"], "partype": "Mană", "info": {"attack": 8, "defense": 3, "magic": 6, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 100, "mp": 350, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2, "attackspeedperlevel": 2.8, "attackspeed": 0.644}, "spells": [{"id": "PhosphorusBomb", "name": "Bombă cu fosfor", "description": "Corki lansează o bombă fulgerătoare într-o locație-țintă, provocându-le daune magice inamicilor din zonă. În plus, acest atac dezvăluie pentru un timp unitățile din zonă.", "tooltip": "<PERSON><PERSON> aruncă o bombă care provoacă <magicDamage>{{ totaldamage }} daune magice</magicDamage>. Bomba dezvăluie zona și campionii pe care-i lovește timp de {{ revealduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "PhosphorusBomb.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CarpetBomb", "name": "Walkirie", "description": "Corki parcurge în zbor o distanță scurtă și aruncă bombe ce continuă să ardă după ce ajung la sol, provocându-le daune inamicilor prinși în zona de acțiune.", "tooltip": "Corki parcurge în zbor un traseu căruia îi dă foc timp de {{ trailduration }} secunde. Inamicii care sunt prinși în flăcări suferă până la <magicDamage>{{ maximumdamage }} daune magice</magicDamage> pe durata efectului.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune în timp", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "CarpetBomb.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GGun", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Mitraliera lui Corki trage rapid într-o zonă în formă de con din fața lui, provocând daune și reducând armura și rezistența la magie ale inamicilor.", "tooltip": "<PERSON>i trage cu o mitralieră în față, provocând <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> de-a lungul a {{ sprayduration }} secunde și reducând cu până la <scaleArmor>{{ shredmax*-1 }} armura</scaleArmor> și <scaleMR>rezistența la magie</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Reducere atribute defensive", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredmax*-1.000000 }} -> {{ shredmaxnl*-1.000000 }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GGun.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissileBarrage", "name": "Ploaia de proiectile", "description": "Corki lansează spre locația-țintă un proiectil care explodează la impact, provocându-le daune inamicilor din zonă. Corki stochează rachete în timp, până la un număr maxim. Fiecare al treilea proiectil lansat va fi ''Unul mare'' și va provoca daune suplimentare.", "tooltip": "Corki lansează un proiectil care explodează la contactul cu primul inamic lovit, provocându-le <physicalDamage>{{ rsmallmissiledamage }} daune fizice</physicalDamage> inamicilor din jur. Fiecare al treilea proiectil provoacă în schimb <physicalDamage>{{ rbigmissiledamage }} daune fizice</physicalDamage>.<br /><br />Această abilitate are până la {{ maxammotooltip }} cumuluri. La impact, atacurile de bază împotriva campionilor reduc perioada de încărcare a cumulurilor cu <attention>{{ attackrefund }}</attention> secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [2, 2, 2], "cooldownBurn": "2", "cost": [35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "4", "range": [1225, 1225, 1225], "rangeBurn": "1225", "image": {"full": "MissileBarrage.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Muniție hextech", "description": "Un procent din daunele de bază din atac ale lui <PERSON>i sunt provocate sub formă de <trueDamage>daune reale</trueDamage> bonus.", "image": {"full": "Corki_RapidReload.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}