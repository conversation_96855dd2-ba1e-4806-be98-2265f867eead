{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "잭스", "title": "무기의 달인", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "아이스하키 잭스", "chromas": false}, {"id": "24002", "num": 2, "name": "파괴단 잭스", "chromas": false}, {"id": "24003", "num": 3, "name": "낚시꾼 잭스", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX 잭스", "chromas": false}, {"id": "24005", "num": 5, "name": "잭시무스", "chromas": false}, {"id": "24006", "num": 6, "name": "소림사 잭스", "chromas": false}, {"id": "24007", "num": 7, "name": "응징자 잭스", "chromas": true}, {"id": "24008", "num": 8, "name": "SKT T1 잭스", "chromas": false}, {"id": "24012", "num": 12, "name": "심판자 잭스", "chromas": false}, {"id": "24013", "num": 13, "name": "신의 지팡이 잭스", "chromas": false}, {"id": "24014", "num": 14, "name": "메카 삼국 잭스", "chromas": true}, {"id": "24020", "num": 20, "name": "정복자 잭스", "chromas": false}, {"id": "24021", "num": 21, "name": "프레스티지 정복자 잭스", "chromas": false}, {"id": "24022", "num": 22, "name": "창공 잭스", "chromas": true}, {"id": "24032", "num": 32, "name": "네오 PAX 잭스", "chromas": false}, {"id": "24033", "num": 33, "name": "프로젝트: 잭스", "chromas": true}], "lore": "독특한 무기와 날카로운 독설 모두 따라올 자가 없는 잭스는 이케시아에서 마지막으로 알려진 무기의 달인이다. 풀려난 공허의 잔해로 조국이 기울자, 잭스와 그 무리는 조국의 남은 부분이나마 수호하기로 맹세했다. 마법이 강해지고 잠재하는 위협이 다시금 태동하자, 잭스는 발로란을 떠돌며 만나는 모든 전사에게 이케시아의 마지막 불빛을 휘두른다. 그와 견줄 수 있을 정도로 강한지 시험하기 위해.", "blurb": "독특한 무기와 날카로운 독설 모두 따라올 자가 없는 잭스는 이케시아에서 마지막으로 알려진 무기의 달인이다. 풀려난 공허의 잔해로 조국이 기울자, 잭스와 그 무리는 조국의 남은 부분이나마 수호하기로 맹세했다. 마법이 강해지고 잠재하는 위협이 다시금 태동하자, 잭스는 발로란을 떠돌며 만나는 모든 전사에게 이케시아의 마지막 불빛을 휘두른다. 그와 견줄 수 있을 정도로 강한지 시험하기 위해.", "allytips": ["잭스는 와드를 포함한 아군 유닛에 도약 공격을 사용할 수 있습니다. 이를 활용하여 전투에서 탈출하십시오.", "잭스는 구인수의 격노검이나 마법공학 총검처럼 주문력과 공격력을 겸비한 아이템을 착용할 시 큰 이득을 볼 수 있습니다."], "enemytips": ["잭스와 정면으로 맞서기 보다는 치고 빠지는 작전을 사용하는 편이 좋습니다. 잭스의 연속 공격을 차단하면 공격력이 현저하게 떨어집니다.", "잭스는 짧은 시간동안 모든 공격을 회피할 수 있습니다. 회피 후에는 근처에 있는 모든 적을 기절시킵니다. 회피 효과가 사라질 때까지 기다리세요."], "tags": ["Fighter"], "partype": "마나", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "도약 공격", "description": "잭스가 목표 유닛을 향해 뛰어오릅니다. 대상이 적일 경우 무기로 내려칩니다.", "tooltip": "잭스가 아군 또는 적 유닛, 와드를 향해 도약합니다. 적인 경우 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "JaxW", "name": "무기 강화", "description": "잭스가 무기에 힘을 모아 다음 번 적을 공격할 때 강력한 추가 피해를 입힙니다.", "tooltip": "잭스가 무기에 힘을 모아 다음 기본 공격이나 <spellName>도약 공격</spellName> 시 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "JaxE", "name": "반격", "description": "잭스가 뛰어난 전투 실력으로 짧은 시간동안 적의 공격을 모두 피할 수 있게 됩니다. 공격을 피한 뒤에는 재빨리 반격해 주변의 적을 모두 기절시킵니다.", "tooltip": "잭스가 {{ dodgeduration }}초간 방어 태세에 들어가 기본 공격을 회피하고, 광역 스킬로부터 받는 피해가 {{ aoedamagereduction }}% 감소합니다. {{ dodgeduration }}초가 지나거나 스킬을 <recast>재사용</recast>하면 근처 적들에게 <magicDamage>{{ totaldamage }}+최대 체력의 {{ percenthealthdamage }}%에 해당하는 마법 피해</magicDamage>를 입히고 {{ stunduration }}초 동안 <status>기절</status>시킵니다. <br /><br />회피한 기본 공격 1회당 피해량이 {{ percentincreasedperdodge*100 }}%씩 최대 <magicDamage>{{ maxdamage }}+최대 체력의 {{ maxpercenthealthdamage }}%</magicDamage>까지 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "JaxR", "name": "무기의 달인", "description": "매 세 번째 연속 공격마다 추가 마법 피해를 줍니다. 또 잭스는 이 스킬을 통해 자신의 주변에 피해를 입히고 결의를 다져, 잠깐 동안 방어력과 마법 저항력이 상승합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> {{ passivefallofftime }}초 안에 세 번째 공격을 할 때마다 <magicDamage>{{ onhitdamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.<br /><br /><spellActive>사용 시:</spellActive> 잭스가 가로등을 내리쳐 주변 적에게 <magicDamage>{{ swingdamagetotal }}의 마법 피해</magicDamage>를 입힙니다. 챔피언을 맞히면 <scaleArmor>방어력이 {{ basearmor }}</scaleArmor>, <scaleMR>마법 저항력이 {{ basemr }}</scaleMR> 증가하며 다음 {{ duration }}초 안에 맞힌 챔피언 하나당 <scaleArmor>방어력이 {{ bonusarmor }}</scaleArmor>, <scaleMR>마법 저항력이 {{ bonusmr }}</scaleMR>씩 추가로 증가합니다. 이때 세 번째가 아닌 두 번째 기본 공격마다 <magicDamage>마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 지속 효과 피해량", "사용 시 피해량", "기본 방어력", "기본 마법 저항력", "추가 챔피언당 방어력", "추가 챔피언당 마법 저항력", "재사용 대기시간"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ swingdamagebase }} -> {{ swingdamagebaseNL }}", "{{ baseresists }} -> {{ baseresistsNL }}", "{{ baseresists*0.600000 }} -> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }} -> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }} -> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "가차없는 맹공", "description": "잭스가 연속해서 기본 공격을 가하면 공격 속도가 상승합니다.", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}