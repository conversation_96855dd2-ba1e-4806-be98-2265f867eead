{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TahmKench": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "223", "name": "<PERSON><PERSON>", "title": "Der König des Flusses", "image": {"full": "TahmKench.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "223000", "num": 0, "name": "default", "chromas": false}, {"id": "223001", "num": 1, "name": "Meisterkoch Tahm <PERSON>", "chromas": false}, {"id": "223002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "223003", "num": 3, "name": "Münzkaiser <PERSON>", "chromas": true}, {"id": "223011", "num": 11, "name": "Arkana<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "223020", "num": 20, "name": "High Noon-<PERSON><PERSON>", "chromas": false}, {"id": "223030", "num": 30, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Der Dämon Tahm Kench war im Laufe der Zeit schon unter vielen Namen bekannt, reist auf Runeterras Wasserwegen und nährt seinen unstillbaren Hunger mit dem Elend der anderen. Obwohl er außerordentlich charmant und stolz er<PERSON><PERSON>, stolziert er durch die reale Welt wie ein Landstreicher auf der Suche nach nichtsahnenden Opfern. Seine peitschende Zunge kann selbst einen schwer gepanzerten Krieger betäuben und eine Landung in seinem knurrenden Magen kommt dem Sturz in einen Abgrund gleich, aus dem es kein Entrinnen gibt.", "blurb": "Der Dämon Tahm Kench war im Laufe der Zeit schon unter vielen Namen bekannt, reist auf Runeterras Wasserwegen und nährt seinen unstillbaren Hunger mit dem Elend der anderen. Obwohl er außerordentlich charmant und stolz er<PERSON>int, stolziert er durch die...", "allytips": ["Als Supporter besteht deine wichtigste Aufgabe darin, leicht verwundbare Verbündete zu beschützen. Habe immer die Reichweite und Abklingzeit von „Verschlingen“ im Blick und positioniere dich entsprechend!", "<PERSON><PERSON><PERSON><PERSON> dir gut, wann du die aktive Wirkung von „Dicke Haut“ einsetzt. Manchmal ist es gut, schon früh den Schild zu nutzen, um weiteren Schaden zu vermeiden, doch manchmal ist auch die Heilung sinnvoller."], "enemytips": ["<PERSON><PERSON> <PERSON>, dass <PERSON> den Schild von „Dicke Haut“ ben<PERSON><PERSON>, denk da<PERSON>, dass er gerade auf eine ganze Menge Heilung verzichtet hat. Außerdem sammelt er kein neues graues <PERSON>, bis „<PERSON>e Haut“ wieder verfügbar ist. Nutze das zu deinem Vorteil!", "Achte auf Kenchs „Tauchgang in den Abgrund“ – du kannst die Kanalisierung durch Massenkontrolleffekte abbrechen."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 325, "mpperlevel": 50, "movespeed": 335, "armor": 39, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.2, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "TahmKenchQ", "name": "Zungenpeitsche", "description": "<PERSON><PERSON> schlägt mit seiner Zunge zu, wodurch er der ersten getroffenen Einheit Schaden zufügt und sie verlangsamt. Trifft er einen gegnerischen Champion, heilt er sich selbst.<br><br>Belegt gegnerische Champions mit einer Steigerung von <spellName>Erlesener Geschmack</spellName>. Ist der Champion bereits mit 3&nbsp;Steigerungen von <spellName>Erlesener Geschmack</spellName> belegt, wird er betäubt und die Steigerungen werden verbraucht.", "tooltip": "Fügt dem ersten getroffenen Gegner <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu und <status>verlangsamt</status> ihn {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%. <br /><br />Wenn ein Champion getroffen wird, heilt sich Tahm Kench um <healing>{{ baseheal }} +{{ percenthealthhealing*100 }}&nbsp;% seines fehlenden Lebens</healing> und belegt ihn mit einer Steigerung von <spellName>Erlesener Geschmack</spellName>, die zusätzlich <magicDamage>{{ spell.tahmkenchpassive:totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Ist dieser Champion bereits mit 3&nbsp;Steigerungen von <spellName>Erlesener Geschmack</spellName> belegt, wird er zudem {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status> und die Steigerungen werden verbraucht.<br /><br />Aktiviere <span class=\"color0bf7de\">Verschlingen</span>, während die Zunge in der Luft ist, um etwas weiter entfernte gegnerische Champions, die mit 3&nbsp;Steigerungen von <spellName>Erlesener Geschmack</spellName> belegt sind, bei einem Treffer zu verschlingen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Heilung", "Prozentuale Heilung abhängig vom fehlenden Leben", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ percenthealthhealing*100.000000 }}&nbsp;% -> {{ percenthealthhealingnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 46, 42, 38, 34], "costBurn": "50/46/42/38/34", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TahmKenchQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TahmKenchW", "name": "Tauchgang in den Abgrund", "description": "<PERSON><PERSON> taucht ab und am Zielort wieder auf. Bei seiner Ankunft fügt er allen G<PERSON>nern in einem Bereich Schaden zu und schleudert sie hoch.", "tooltip": "Tahm Kench taucht ab und am Zielort wieder auf. Bei seiner Ankunft verursacht er <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und <status>schleudert</status> alle Gegner in einem Bereich {{ knockupduration }}&nbsp;Sekunde lang hoch. Trifft er mindestens einen gegnerischen Champion, werden {{ champrefund*100 }}&nbsp;% der Abklingzeit und <scaleMana>Manakosten</scaleMana> zurückerstattet. <br /><br /><span class=\"color0bf7de\">Verschlungene</span> Verbündete werden von ihm transportiert (aber sie können sich jederzeit ausspucken lassen).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Reichweite", "Kosten (@AbilityResourceName@)", "Rückerstattung der Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cost }} -> {{ costNL }}", "{{ champrefund*100.000000 }}&nbsp;% -> {{ champrefundnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 75, 90, 105, 120], "costBurn": "60/75/90/105/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1050, 1100, 1150, 1200], "rangeBurn": "1000/1050/1100/1150/1200", "image": {"full": "TahmKenchW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TahmKenchE", "name": "<PERSON><PERSON>", "description": "<passive>Passiv:</passive> <PERSON><PERSON> speichert einen Prozentsatz des erlittenen Schadens und heilt sich abh<PERSON><PERSON><PERSON> davon, während er sich außerhalb des Kampfes befindet.<br><br><active>Aktiv:</active> Wandelt jeglichen gespeicherten Schaden in einen temporären Schild um.", "tooltip": "<passive>Passiv:</passive> {{ greyhealthratio*100 }}&nbsp;% des <PERSON>ens, den <PERSON>hm <PERSON> erle<PERSON>, wird von se<PERSON> <spellName><PERSON><PERSON>ut</spellName> gespeichert (wird erhöht auf {{ greyhealthratioenhanced*100 }}&nbsp;%, wenn mindestens {{ enhancedthreshold }}&nbsp;gegnerische Champions in der Nähe sind). <PERSON><PERSON> {{ ooctimer }}&nbsp;Sekunden lang keinen Schaden erlitten hat, wird <spellName><PERSON><PERSON> Haut</spellName> rasch verbraucht, um ihn um {{ greyhealthhealingratio }} ihres Werts zu heilen.<br /><br /><active>Aktiv:</active> Jegliche gespeicherte <spellName>Dicke Haut</spellName> wird in einen <shield>Schild</shield> umgewandelt, der {{ shieldduration }}&nbsp;Sekunden lang bestehen bleibt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% Sc<PERSON>en an „Dicke Haut“", "% Schaden an „Dicke Haut“ (verstärkt)"], "effect": ["{{ greyhealthratio*100.000000 }}&nbsp;% -> {{ greyhealthrationl*100.000000 }}&nbsp;%", "{{ greyhealthratioenhanced*100.000000 }}&nbsp;% -> {{ greyhealthratioenhancednl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2400, 2400, 2400, 2400, 2400], "rangeBurn": "2400", "image": {"full": "TahmKenchE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TahmKenchRWrapper", "name": "Verschlingen", "description": "<PERSON><PERSON> verschlingt ein paar Sekunden lang einen Champion. Ist der Champion e<PERSON>, fügt er ihm magischen Schaden zu. Ist der Champion ein Verbündeter, schirmt er ihn ab.", "tooltip": "Tahm Kench verschlingt ein paar Sekunden lang einen Champion. Er kann die Fähigkeit <recast>reaktivieren</recast>, um den Champion auszuspucken.<br /><br /><specialRules>Gegnerische Champions:</specialRules> Erfordert 3&nbsp;Steigerungen von <spellName>Erlesener Geschmack</spellName>. Sie werden bis zu {{ enemyduration }}&nbsp;Sekunden lang verschlungen und erleiden <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ basedamage }} (+{{ percenthpdamage }} ihres max. Lebens). Tahm Kench wird während des Effekts um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status> und <keywordName>gehemmt</keywordName>.<br /><br /><specialRules>Verbündete Champions:</specialRules> Werden bis zu {{ allyduration }}&nbsp;Sekunden lang verschlungen und erhalten nach dem Ausspucken einen <shield><PERSON>hild</shield> in Hö<PERSON> von {{ totalshield }}, der danach langsam abfällt. Verbündete können sich selbst vorzeitig ausspucken lassen. Tahm Kench ist während dieses Effekts <status>gehemmt</status>, kann aber <keywordName>Tauchgang in den Abgrund</keywordName> ausführen. Zudem erhält er {{ allyduration }}&nbsp;Sekunden lang <speed>{{ allyspeedamount*100 }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schildstärke", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ datacooldown }} -> {{ datacooldownNL }}"]}, "maxrank": 3, "cooldown": [0, 0, 0], "cooldownBurn": "0", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "TahmKenchRWrapper.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ datamanacost }}&nbsp;Mana"}], "passive": {"name": "Erlesener Geschmack", "description": "<PERSON><PERSON>ch legt das ganze Gewicht seines immensen Körpers in seine Angriffe, wodurch er abhängig von seinem Gesamtleben zusätzlichen Schaden erhält. Treffer gegen gegnerische Champions bauen Steigerungen von <spellName>Erlesener Geschmack</spellName> auf. Bei 3 Steigerungen kann er <spellName>Verschlingen</spellName> gegen sie einsetzen.", "image": {"full": "TahmKenchP.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}