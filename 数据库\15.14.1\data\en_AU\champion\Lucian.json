{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lucian": {"id": "Lucian", "key": "236", "name": "Lucian", "title": "the Purifier", "image": {"full": "Lucian.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "236000", "num": 0, "name": "default", "chromas": true}, {"id": "236001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "236002", "num": 2, "name": "Striker <PERSON>", "chromas": false}, {"id": "236006", "num": 6, "name": "PROJECT: Lucian", "chromas": true}, {"id": "236007", "num": 7, "name": "Heartseek<PERSON>", "chromas": false}, {"id": "236008", "num": 8, "name": "High Noon Lucian", "chromas": true}, {"id": "236009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "236018", "num": 18, "name": "Pulsefire Lucian", "chromas": false}, {"id": "236019", "num": 19, "name": "Prestige Pulsefire Lucian", "chromas": false}, {"id": "236025", "num": 25, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "236031", "num": 31, "name": "Arcana <PERSON>", "chromas": true}, {"id": "236040", "num": 40, "name": "Strike Paladin <PERSON>", "chromas": true}, {"id": "236052", "num": 52, "name": "Winterblessed Lucian", "chromas": true}, {"id": "236062", "num": 62, "name": "Masked Justice <PERSON>", "chromas": true}], "lore": "<PERSON>, a Sentinel of Light, is a grim hunter of wraiths and specters, pursuing them relentlessly and annihilating them with his twin relic pistols. After the specter <PERSON><PERSON><PERSON> slew his wife, <PERSON> embarked on the path of vengeance—but even with her return to life, his rage is undiminished. Merciless and single-minded, <PERSON> will stop at nothing to protect the living from the long-dead horrors of the Black Mist.", "blurb": "<PERSON>, a Sentinel of Light, is a grim hunter of wraiths and specters, pursuing them relentlessly and annihilating them with his twin relic pistols. After the specter <PERSON><PERSON><PERSON> slew his wife, <PERSON> embarked on the path of vengeance—but even with her...", "allytips": ["For optimal burst, try to combine Relentless Pursuit into Piercing Light.", "Ardent <PERSON> actually explodes in a star pattern. Try to line it up so the spokes hit enemy champions.", "Once you have chosen an angle for The Culling, you cannot change it. Pick your moment well!", "Because of <PERSON><PERSON>, <PERSON> benefits more from Attack Damage than he does from Attack Speed."], "enemytips": ["<PERSON> has strong burst, but little sustained damage.", "<PERSON> cannot change the aim of The Culling. Take advantage of this by avoiding the angle of the bullets.", "Piercing Light does not give <PERSON> extra Attack Range. He still needs to find a target in range to line up his shot. Avoid Piercing Light by anticipating the angle <PERSON> will choose."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 641, "hpperlevel": 100, "mp": 320, "mpperlevel": 43, "movespeed": 335, "armor": 28, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.9, "attackspeedperlevel": 3.3, "attackspeed": 0.638}, "spells": [{"id": "LucianQ", "name": "Piercing Light", "description": "<PERSON> shoots a bolt of piercing light through a target.", "tooltip": "<PERSON> shoots a bolt of piercing light, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Attack Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [48, 56, 64, 72, 80], "costBurn": "48/56/64/72/80", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [60, 75, 90, 105, 120], [1000, 1000, 1000, 1000, 1000], [0.41, 0.41, 0.41, 0.41, 0.41], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "60/75/90/105/120", "1000", "0.41", "100", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "LucianQ.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianW", "name": "Ardent Blaze", "description": "<PERSON> shoots a missile that explodes in a star shape, marking and briefly revealing enemies. <PERSON> gains Move Speed for attacking marked enemies.", "tooltip": "<PERSON> fires a shot that explodes at the end of its range or on the first enemy hit, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>, briefly revealing enemies and marking them for 6 seconds.<br /><br />When <PERSON> or an ally damages a marked enemy, <PERSON> gains <speed>{{ e2 }} Move Speed</speed> for 1 second. Allies triggering this effect will also grant <PERSON> <attention>Vigilance</attention>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Move Speed", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [60, 65, 70, 75, 80], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [700, 700, 700, 700, 700]], "effectBurn": [null, "75/110/145/180/215", "60/65/70/75/80", "900", "0", "1", "200", "1", "6", "1", "700"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "LucianW.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianE", "name": "Relentless Pursuit", "description": "<PERSON> quickly dashes a short distance. <PERSON><PERSON> attacks reduce Relentless Pursuit's cooldown.", "tooltip": "<PERSON> dashes.<br /><br />Cooldown is reduced by {{ e1 }} second whenever <PERSON> hits an enemy with <spellName><PERSON><PERSON></spellName> ({{ e2 }} seconds for champions). {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [425, 425, 425, 425, 425], [200, 200, 200, 200, 200], [1350, 1350, 1350, 1350, 1350], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1", "2", "425", "200", "1350", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [445, 445, 445, 445, 445], "rangeBurn": "445", "image": {"full": "LucianE.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianR", "name": "The Culling", "description": "<PERSON> unleashes a torrent of shots from his weapons.", "tooltip": "<PERSON> rapidly fires <keywordMajor>{{ totalnumshots }}</keyword<PERSON>ajor> shots in a direction for {{ duration }} seconds or until he <recast>Recasts</recast>. Each shot deals <physicalDamage>{{ damageperbullet }} physical damage</physicalDamage> to the first enemy hit.<br /><br />While firing, <PERSON> may use <spellName>Relentless Pursuit</spellName>.<br /><br />Total Damage: <physicalDamage>{{ totaldamage }} physical damage</physicalDamage><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1400, 1400, 1400], "rangeBurn": "1400", "image": {"full": "LucianR.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Whenever <PERSON> uses an ability, his next attack becomes a double-shot. When <PERSON> is healed or shielded by an ally, or when a nearby enemy Champion is immobilized, his next 2 basic attacks will deal bonus magic damage.", "image": {"full": "Lucian_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}