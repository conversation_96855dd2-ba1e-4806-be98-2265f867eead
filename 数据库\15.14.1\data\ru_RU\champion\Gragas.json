{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gragas": {"id": "Gragas", "key": "79", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Бедокур", "image": {"full": "Gragas.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "79000", "num": 0, "name": "default", "chromas": false}, {"id": "79001", "num": 1, "name": "Подводник Грагас", "chromas": false}, {"id": "79002", "num": 2, "name": "Фермер Граг<PERSON><PERSON>", "chromas": false}, {"id": "79003", "num": 3, "name": "Санта-Граг<PERSON>с", "chromas": false}, {"id": "79004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, эсквайр", "chromas": false}, {"id": "79005", "num": 5, "name": "Ванд<PERSON><PERSON> Гр<PERSON>г<PERSON><PERSON>", "chromas": false}, {"id": "79006", "num": 6, "name": "Грагас на Октоберфесте", "chromas": false}, {"id": "79007", "num": 7, "name": "<PERSON>а<PERSON><PERSON> Граг<PERSON><PERSON>", "chromas": false}, {"id": "79008", "num": 8, "name": "<PERSON>natic <PERSON>рага<PERSON>", "chromas": false}, {"id": "79009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Дебошир", "chromas": false}, {"id": "79010", "num": 10, "name": "Арктический Грагас", "chromas": false}, {"id": "79011", "num": 11, "name": "Хран<PERSON><PERSON><PERSON><PERSON><PERSON> Грагас", "chromas": true}, {"id": "79020", "num": 20, "name": "Грага<PERSON> из Галактики грува", "chromas": true}, {"id": "79029", "num": 29, "name": "Ковбой Грагас", "chromas": true}, {"id": "79039", "num": 39, "name": "Мело<PERSON>ан Грагас", "chromas": true}], "lore": "Грозный, но веселый Грагас – известный дебошир-пивовар, который всегда пытается порадовать других. Его происхождение неизвестно, но сейчас он ищет редкие ингредиенты в девственной тундре Фрельйорда, пытаясь довести до ума свой новый рецепт. Импульсивный и упорный Грагас славен тем, что часто затевает драки, которые обычно заканчиваются ночными пирушками с порчей имущества. Любое появление Грагаса обязательно сопровождается весельем и разрушениями – именно в таком порядке.", "blurb": "Грозный, но веселый Грагас – известный дебошир-пивовар, который всегда пытается порадовать других. Его происхождение неизвестно, но сейчас он ищет редкие ингредиенты в девственной тундре Фрельйорда, пытаясь довести до ума свой новый рецепт. Импульсивный...", "allytips": ["Пьяный Угар уменьшает урон, когда Грагас начинает пить. Старайтесь активировать умение, когда вы предвидите урон.", "Старайтесь подловить ваших врагов и бросить их под союзную башню Взрывным бочонком.", "Старайтесь совмещать Таран и Взрывной бочонок, чтобы помогать своей команде убивать врагов."], "enemytips": ["Абсолютное умение Грагаса способно разметать всю вашу команду. Будьте осторожны и следите за тем, чтобы вас не отбросило к нему или вражеской башне.", "Таран очень быстро перезаряжается, затрудняя преследование Грагаса. Не попадите в затруднительное положение, погнавшись за ним."], "tags": ["Fighter", "Mage"], "partype": "Мана", "info": {"attack": 4, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 115, "mp": 400, "mpperlevel": 47, "movespeed": 330, "armor": 38, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.05, "attackspeed": 0.675}, "spells": [{"id": "GragasQ", "name": "Бочка", "description": "Грагас катит бочку в указанную точку, а затем может взорвать ее, или же она сама взрывается по прошествии 4 секунд. Сила взрыва со временем увеличивается. Враги, пораженные взрывом, замедляются.", "tooltip": "Грагас выпускает бочку, которая взрывается через {{ e4 }} сек., нанося <magicDamage>от {{ mindamage }} до {{ maxdamage }} магического урона</magicDamage> и <status>замедляя</status> врагов на {{ e2 }}-{{ effect2amount*1.5 }}% на {{ e3 }} сек. Урон и <status>замедление</status> зависят от того, сколько пролежала бочка. <br /><br />Грагас может <recast>повторно применить</recast> это умение, чтобы взорвать бочку преждевременно.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [40, 45, 50, 55, 60], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [2, 2, 2, 2, 2], [150, 150, 150, 150, 150], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "40/45/50/55/60", "2", "4", "2", "150", "70", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "GragasQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "GragasW", "name": "Пьян<PERSON>й угар", "description": "Грагас пробует свой новый напиток в течение 1 секунды, на некоторое время уменьшая получаемый урон. После этого он впадает в буйство, и его следующая автоатака наносит магический урон по площади.", "tooltip": "Грагас пробует свой напиток, уменьшая получаемый урон на {{ damagereduction }} на {{ defenseduration }} сек., а его следующая автоатака дополнительно наносит цели и окружающим врагам <magicDamage>магический урон в размере {{ totaldamage }} ед.</magicDamage> плюс <magicDamage>{{ maxhppercentdamage }}% от максимального запаса здоровья</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Уменьшение урона", "Урон"], "effect": ["{{ basedamagereduction }}% -> {{ basedamagereductionNL }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "GragasW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "GragasE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Грагас совершает рывок в указанную точку. Первый противник, с которым он сталкивается на своем пути, и все враги вокруг него получают урон и оглушаются.", "tooltip": "Грагас совершает рывок вперед, останавливаясь при столкновении с первым врагом на пути. При этом он <status>подбрасывает</status> врагов поблизости на {{ stunduration }} сек. и наносит им <magicDamage>{{ totaldamage }} магического урона</magicDamage>.<br /><br />При столкновении с врагом время перезарядки этого умения сокращается на {{ cooldownrefund*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GragasE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "GragasR", "name": "Взрывной бочонок", "description": "Грагас бросает бочку в указанное место, нанося урон всем врагам в зоне поражения и отбрасывая их.", "tooltip": "Грагас бросает бочку, нанося <magicDamage>{{ damagedone }} магического урона</magicDamage> и <status>отбрасывая</status> врагов от точки ее приземления.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GragasR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Пойло со скидкой", "description": "Грагас периодически лечится при использовании умений.", "image": {"full": "GragasPassiveHeal.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}