{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Janna": {"id": "<PERSON><PERSON>", "key": "40", "name": "<PERSON><PERSON>", "title": "the Storm's Fury", "image": {"full": "Janna.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "40000", "num": 0, "name": "default", "chromas": false}, {"id": "40001", "num": 1, "name": "Tempest Janna", "chromas": false}, {"id": "40002", "num": 2, "name": "Hextech Janna", "chromas": false}, {"id": "40003", "num": 3, "name": "Frost Queen <PERSON>", "chromas": false}, {"id": "40004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40005", "num": 5, "name": "Forecast <PERSON><PERSON>", "chromas": false}, {"id": "40006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "40007", "num": 7, "name": "Star Guardian Janna", "chromas": false}, {"id": "40008", "num": 8, "name": "Sacred Sword Janna", "chromas": true}, {"id": "40013", "num": 13, "name": "Bewitching <PERSON><PERSON>", "chromas": true}, {"id": "40020", "num": 20, "name": "Guardian of the Sands Janna", "chromas": true}, {"id": "40027", "num": 27, "name": "Battle Queen <PERSON><PERSON>", "chromas": true}, {"id": "40036", "num": 36, "name": "<PERSON>", "chromas": true}, {"id": "40045", "num": 45, "name": "Cyber Hal<PERSON>", "chromas": true}, {"id": "40046", "num": 46, "name": "Prestige Cyber Halo Janna", "chromas": false}, {"id": "40056", "num": 56, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "40066", "num": 66, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Dipersenjatai kekuatan badai angin Runeterra, <PERSON><PERSON> spirit elemen angin misterius yang melindungi orang-orang buang<PERSON>. Konon dia tercipta berkat permohonan para pelaut Runeterra. <PERSON>reka berdoa meminta angin baik saat mengarungi perairan berbahaya dan melewati badai ganas. <PERSON>tuan dan perlindungannya pun dipanggil ke Zaun. <PERSON>na, <PERSON><PERSON> menjadi mercusuar harapan bagi mereka yang membutuhkan. Tak ada yang tahu di mana atau kapan dia akan muncul, tetapi dia sering hadir untuk membantu.", "blurb": "Dipersenjatai kekuatan badai angin Runeterra, <PERSON><PERSON> spirit elemen angin misterius yang melindungi orang-orang buangan <PERSON>. Konon dia tercipta berkat permohonan para pelaut Runeterra. <PERSON><PERSON>a berdoa meminta angin baik saat mengarungi perairan...", "allytips": ["Eye of the Storm bisa digunakan pada turret sekutu.", "Menembakkan Howling Gale dengan cepat tanpa charge bisa digunakan untuk melumpuhkan tim lawan.", "<PERSON><PERSON>ur waktu ultima Janna bisa mendorong musuh menjauh dari sekutu yang terluka atau bahkan memisahkan musuh."], "enemytips": ["Gunakan Ability interrupt pada saat yang tepat untuk menghentikan ultima Janna.", "Dengar<PERSON> baik-baik suara <PERSON>ling Gale karena itu bisa menjadi tanda serangan dari semak atau di luar layar.", "Kekuatan tertinggi <PERSON>na muncul saat memberi buff pada sekutu.  <PERSON>ka kamu bisa menyerang sekutunya, itu akan melemahkan Ability-nya untuk melawanmu."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 50, "movespeed": 325, "armor": 28, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON>G<PERSON>", "name": "Howling <PERSON>", "description": "Dengan mengubah tekanan dan suhu di se<PERSON><PERSON><PERSON>, <PERSON><PERSON> mampu menciptakan badai kecil yang berta<PERSON>h besar seiring waktu. Spell ini bisa diaktifkan kembali untuk melepaskan badai. <PERSON><PERSON> dile<PERSON>, badai akan terbang ke arah yang ditentukan dan mengh<PERSON>lkan damage serta mengempaskan musuh yang ada di jalurnya.", "tooltip": "<PERSON><PERSON> memanggil tornado yang makin kuat selama {{ maxduration }} detik kemudian menerjang di sepanjang jalurnya. <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ minimumdamage }}–{{ maxdamage }} magic damage</magicDamage> dan <status>Knock Up</status> selama {{ baseknockup }}–{{ maxknockup }} detik. Jarak, damage, dan durasi <status>Knock Up</status> bertambah mengikuti besarnya tornado. Janna bisa <recast>Recast</recast> untuk mengirimkan tornado lebih awal.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage per Detik Saat Charge", "@AbilityResourceName@ Cost"], "effect": ["{{ mindamage }}-> {{ mindamageNL }}", "{{ bonusdamage }}-> {{ bonusdamageNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "HowlingGale.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SowTheWind", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> memanggil elemental udara yang meningkatkan Move Speed secara pasif dan membuat dia bisa menembus unit. Dia juga bisa mengaktifkan ability ini untuk memberikan damage dan slow musuh.", "tooltip": "<spellPassive>Pasif:</spellPassive> <PERSON><PERSON> <speed>{{ totalms }} Move Speed</speed> dan menja<PERSON>.<br /><br /><spellActive>Aktif:</spellActive> Elemental <PERSON><PERSON> men<PERSON> musuh, memberikan <status>Slow</status> sebesar {{ totalslow }} selama {{ slowduration }} detik, dan men<PERSON>an <magicDamage>{{ totaldamage }} + {{ spell.tailwindself:bonusdamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Move Speed Pasif", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ slowpercent }}%-> {{ slowpercentNL }}%", "{{ mspercent*100.000000 }}%-> {{ mspercentnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4294967295, 4294967295, 4294967295, 4294967295, 4294967295], "rangeBurn": "4294967295", "image": {"full": "SowTheWind.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EyeOfTheStorm", "name": "Eye of the Storm", "description": "<PERSON><PERSON> men<PERSON>n angin kencang yang memberi shield pada champion se<PERSON><PERSON> atau turret dari damage yang datang dan meningkatkan Attack Damage mereka.", "tooltip": "Janna memberi champion seku<PERSON> atau turret <shield>{{ totalshield }} Shield</shield> selama {{ shieldduration }} detik. Saat terkena shield, mereka mendapatkan <scaleAD>{{ totalad }} Attack Damage</scaleAD>.<br /><br />Janna me-refund {{ ecdrefundforcc*100 }}% cooldown kapan pun dia mengganggu gerakan champion musuh dengan Ability.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Health Shield", "Attack Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ baseshield }}-> {{ baseshieldNL }}", "{{ bonusad }}-> {{ bonusadNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EyeOfTheStorm.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ReapTheWhirlwind", "name": "Monsoon", "description": "<PERSON><PERSON>uti dirinya dengan badai magis yang melempar mundur musuh. <PERSON><PERSON><PERSON> badai mulai reda, angin semilir memberi heal pada sekutu di sekitar saat ability masih ini aktif.", "tooltip": "<PERSON><PERSON> memanggil badai monsoon magis, menerapkan efek <status>Knock Back</status> ke musuh lalu memberi heal pada sekutu di sekitar sebesar <healing>{{ totalheal }} Health</healing> selama {{ e3 }} detik. <PERSON>ak atau menggunakan Ability lain akan mengakhiri monsoon lebih awal.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> <PERSON>", "Cooldown"], "effect": ["{{ healbasepersecond }}-> {{ healbasepersecondNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [300, 450, 600], [3, 3, 3], [700, 700, 700], [875, 875, 875], [875, 875, 875], [1200, 1200, 1200], [10, 10, 10], [0.5, 0.5, 0.5], [0, 0, 0]], "effectBurn": [null, "0", "300/450/600", "3", "700", "875", "875", "1200", "10", "0.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725], "rangeBurn": "725", "image": {"full": "ReapTheWhirlwind.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tailwind", "description": "<PERSON><PERSON><PERSON> men<PERSON> Move Speed dan bergerak ke arahnya.<br><br><PERSON><PERSON> sebagian Move Speed bonus sebagai magic damage bonus on-hit dan den<PERSON>.", "image": {"full": "Janna_Tailwind.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}