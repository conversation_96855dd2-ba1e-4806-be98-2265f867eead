{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "<PERSON><PERSON><PERSON>", "title": "călătorul din Vid", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "<PERSON><PERSON>din festiv", "chromas": false}, {"id": "38002", "num": 2, "name": "<PERSON><PERSON><PERSON> al adâncurilor", "chromas": false}, {"id": "38003", "num": 3, "name": "<PERSON><PERSON><PERSON>a Vidulu<PERSON>", "chromas": false}, {"id": "38004", "num": 4, "name": "<PERSON><PERSON><PERSON>, vestitorul", "chromas": false}, {"id": "38005", "num": 5, "name": "<PERSON><PERSON><PERSON> asasin intergalactic", "chromas": false}, {"id": "38006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "38014", "num": 14, "name": "<PERSON><PERSON><PERSON> hextech", "chromas": false}, {"id": "38015", "num": 15, "name": "<PERSON><PERSON><PERSON>, la<PERSON><PERSON> fulger<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38024", "num": 24, "name": "<PERSON><PERSON><PERSON> dragomant", "chromas": false}], "lore": "<PERSON>și știe că zilele îi sunt numărate, <PERSON><PERSON><PERSON> alege să-și petreacă ultima parte a vieții lăsându-și amprenta distrugătoare asupra celor mai întunecate colțuri ale lumii. A fost odată un ghid și explorator shuriman cu experiență, care a ales să-și întemeieze o familie alături de triburile pașnice din sud. Dar apoi satul său a fost înghițit de Vid. <PERSON>r<PERSON><PERSON> să se răzbune, <PERSON><PERSON><PERSON> a combinat puterea unor artifacte mistice și a unor tehnologii interzise pentru a se pregăti de luptă. În cele din urmă, a pornit către pustiurile unde a fost odată Icathia, pregătit să înfrunte orice creație monstruoasă a Vidului pentru a-l găsi pe așa-zisul profet Malzahar.", "blurb": "<PERSON>și știe că zilele îi sunt numărate, <PERSON><PERSON><PERSON> alege să-și petreacă ultima parte a vieții lăsându-și amprenta distrugătoare asupra celor mai întunecate colțuri ale lumii. A fost odată un ghid și explorator shuriman cu experiență, care a ales să-și...", "allytips": ["Kassadin are multe opțiuni legate de obiecte; poate juca rolul de mag, cu ajutorul manei și al puterii abilităților sau poate fi un anti-mag datorită reducerii timpilor de reactivare și rezistenței la magie.", "Abilitatea supremă a lui <PERSON> poate avea multiple utilizări și are un timp de reactivare mai scurt decât multe altele, deci folosește-o cât mai des.", "Încearcă să obții buff-ul Colosul albastru pentru a compensa consumul de mană în creștere aferent ''Pașilor riftului''."], "enemytips": ["<PERSON><PERSON><PERSON> provoacă mai ales daune magice. Dacă se descurcă bine, cumpără obiecte pentru rezistență la magie, precum ''Pașii lui Mercur'' și ''<PERSON><PERSON><PERSON><PERSON> urs<PERSON>i''.", "<PERSON><PERSON><PERSON> f<PERSON> ''Pașii riftului'' succ<PERSON><PERSON>, consumă tot mai multă mană. <PERSON><PERSON> cont de asta când îl urmărești.", "Pentru ca abilitatea de încetinire a l<PERSON>, ''Impuls de forță'', s<PERSON> poată fi folosită, sunt necesare 6 vrăji. Dacă investește în această abilitate, folosește-ți cu grijă propriile abilități dacă îl ai drept adversar pe culoar."], "tags": ["Assassin", "Mage"], "partype": "Mană", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "Sfera Vidului", "description": "<PERSON><PERSON><PERSON> lansează spre țintă o sferă de energie a Vidului, provocându-i daune și întrerupându-i pregătirea vrăjilor. Energia în exces se adună în jurul lui, oferindu-i temporar un scut care absoarbe daune magice.", "tooltip": "<PERSON><PERSON><PERSON> lansează o sferă de energie a Vidului, provocând <magicDamage>{{ totaldamage }} daune magice</magicDamage> și întrerupând pregătirea vrăjilor. El primește și un <shield>scut anti-magie în valoare de {{ totalshield }}</shield> timp de 1,5 secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Valoarea scutului", "Cost de @AbilityResourceName@", "Timp de reactivare"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>herBlade", "name": "Spada infernului", "description": "Pasivă: atac<PERSON>le de bază ale lui <PERSON><PERSON>din provoacă daune magice bonus. Activă: următorul atac de bază al lui Ka<PERSON>din provoacă daune magice bonus și reface mană.", "tooltip": "<spellPassive>Pasiv<PERSON>:</spellPassive> atacurile lui <PERSON><PERSON><PERSON> provoacă încă <magicDamage>{{ onhitdamage }} daune magice</magicDamage>.<br /><br /><spellActive>Activă:</spellActive> <PERSON><PERSON><PERSON> își încarc<PERSON> spada, iar următorul său atac va provoca <magicDamage>{{ activedamage }} daune magice</magicDamage> și îi va reface <scaleMana>{{ e1 }}% din mana lipsă</scaleMana>, valoare ce crește la <scaleMana>{{ e4 }}%</scaleMana> împotriva campionilor.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune activă", "<PERSON><PERSON><PERSON> man<PERSON> de baz<PERSON>", "<PERSON><PERSON><PERSON> mană campion"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ e4 }}% -> {{ e4NL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ForcePulse", "name": "Impuls de forță", "description": "<PERSON><PERSON><PERSON> se hrănește cu energie din vrăjile folosite în apropierea lui. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> poate folosi ''Impulsul de forță'' pentru a provoca daune și a încetini inamicii dintr-o zonă în formă de con din fața lui.", "tooltip": "<spellPassive>Pasiv<PERSON>:</spellPassive> <spellName>Timpul de reactivare al ''Impuls de forță''</spellName> este redus cu {{ reductionperspellcast }} sec. de fiecare dată când se folosește o abilitate în apropierea lui <PERSON>.<br /><br /><spellActive>Activă:</spellActive> <PERSON><PERSON><PERSON> dezlănțuie un impuls din vid, provocând <magicDamage>{{ totaldamage }} daune magice</magicDamage> și <status>încetinind</status> inamicii cu {{ e2 }}% timp de {{ e3 }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RiftWalk", "name": "Pașii riftului", "description": "<PERSON><PERSON>din se teleportează într-o locație apropiată, provocând daune unităților inamice din zonă. Folosirea ''Pașilor riftului'' de mai multe ori într-o perioadă scurtă de timp costă mană suplimentară, însă provoacă și daune în plus.", "tooltip": "<PERSON><PERSON><PERSON> se deplasează instantaneu într-o locație apropiată, provocând <magicDamage>{{ basedamage }} daune magice</magicDamage>.<br /><br />Fiecare folosire ulterioară a abilității în decurs de {{ rstackduration }} secunde dublează costul de mană și provoacă <magicDamage>{{ bonusdamage }} daune magice</magicDamage> suplimentare. Creșterea costului și daunelor se poate cumula de până la {{ maxstacks }} ori.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Daune per cumul", "Timp de reactivare"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ stackdamage }} -> {{ stackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Piatra Vidului", "description": "<PERSON><PERSON><PERSON> suferă daune magice reduse și ignoră coliziunea dintre unități.", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}