{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Samira": {"id": "<PERSON><PERSON>", "key": "360", "name": "サミーラ", "title": "砂漠の薔薇", "image": {"full": "Samira.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "360000", "num": 0, "name": "default", "chromas": false}, {"id": "360001", "num": 1, "name": "PsyOps サミーラ", "chromas": true}, {"id": "360010", "num": 10, "name": "スペースグルーヴ サミーラ", "chromas": true}, {"id": "360020", "num": 20, "name": "荒野のサミーラ", "chromas": true}, {"id": "360030", "num": 30, "name": "ソウルファイター サミーラ", "chromas": false}, {"id": "360033", "num": 33, "name": "黒薔薇の仮面サミーラ", "chromas": false}], "lore": "サミーラはゆるぎない自信を浮かべた目で死を見つめ、行く先々でスリルを探し求める。幼少期にシュリーマの家が破壊された後、サミーラはノクサスで天職を見つけた。そこで彼女は危険な任務を請け負い、クールなスタイルの命知らずとしての評判を築いた。黒色火薬の拳銃と特注の剣を携え、サミーラは立ちはだかる者は誰であろうと排除し、生きるか死ぬかの状況を切り抜ける。", "blurb": "サミーラはゆるぎない自信を浮かべた目で死を見つめ、行く先々でスリルを探し求める。幼少期にシュリーマの家が破壊された後、サミーラはノクサスで天職を見つけた。そこで彼女は危険な任務を請け負い、クールなスタイルの命知らずとしての評判を築いた。黒色火薬の拳銃と特注の剣を携え、サミーラは立ちはだかる者は誰であろうと排除し、生きるか死ぬかの状況を切り抜ける。", "allytips": [], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "マナ", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 108, "mp": 349, "mpperlevel": 38, "movespeed": 335, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "SamiraQ", "name": "フレア", "description": "銃を発砲するか、剣を振ってダメージを与える。「ワイルドラッシュ」中に使用した場合は、ダッシュ後に通り道にいたすべての敵を攻撃する。", "tooltip": "銃を撃って最初に命中した敵に<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与える。<br /><br />このスキルを近接攻撃射程内の敵に使用した場合は、代わりに剣で斬りつけて<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与える。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "合計攻撃力反映率"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ qadratio*100.000000 }}% -> {{ qadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "SamiraQ.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SamiraW", "name": "ブレードワール", "description": "周囲を斬りつけて敵にダメージを与え、敵の飛翔物を破壊する。", "tooltip": "{{ slashduration }}秒間周囲を斬りつけ、各命中で敵に<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を2回与え、範囲内に入ってきた敵の飛翔物を破壊する。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [30, 28, 26, 24, 22], "cooldownBurn": "30/28/26/24/22", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "SamiraW.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SamiraE", "name": "ワイルドラッシュ", "description": "敵(建造物を含む)を通り抜けるようにダッシュし、接触した敵を斬りつけて、攻撃速度が増加する。敵チャンピオンをキルすると、このスキルのクールダウンが解消される。", "tooltip": "敵(建造物を含む)を通り抜けるようにダッシュし、接触した敵を斬りつけ<magicDamage>{{ dashdamage }}の魔法ダメージ</magicDamage>を与えて、{{ attackspeedduration }}秒間<attackSpeed>攻撃速度が{{ bonusattackspeed*100 }}%</attackSpeed>増加する。 <br /><br />サミーラがダメージを与えてから3秒以内にその敵チャンピオンが倒されると、このスキルのクールダウンが解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "攻撃速度"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ bonusattackspeed*100.000000 }}% -> {{ bonusattackspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraE.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SamiraR", "name": "インフェルノトリガー", "description": "銃から弾丸を高速で連射し、周囲のすべての敵に攻撃を行う。", "tooltip": "このスキルは発動時に<keywordMajor>「スタイル」</keywordMajor>のグレードが「S」でなければ使用できない。このスキルを使用すると<keywordMajor>「スタイル」</keywordMajor>のグレードがリセットされる。<br /><br />銃から弾丸を高速で連射し、周囲の敵に2秒かけて10回発砲する。1回の発砲で<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与えて、{{ lifestealmod*100 }}%の効果でライフスティールを適用する。また、この発砲はそれぞれクリティカル判定を持つ。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraR.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "デアデビルインパルス", "description": "直前に命中したものとは異なる通常攻撃またはスキルを命中させることでコンボが増加していく。近接攻撃射程の通常攻撃は追加魔法ダメージを与える。<status>移動不能効果</status>を受けた敵に通常攻撃を行うと、自身の射程内までダッシュする。敵が<status>ノックアップ</status>していた場合は、少しの間だけ<status>ノックアップ</status>させたままにする。", "image": {"full": "SamiraP.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}