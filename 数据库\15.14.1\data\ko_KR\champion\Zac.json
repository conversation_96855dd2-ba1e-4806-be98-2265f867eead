{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zac": {"id": "<PERSON><PERSON>", "key": "154", "name": "자크", "title": "비밀 병기", "image": {"full": "Zac.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "154000", "num": 0, "name": "default", "chromas": true}, {"id": "154001", "num": 1, "name": "특수 병기 자크", "chromas": false}, {"id": "154002", "num": 2, "name": "수영장 파티 자크", "chromas": false}, {"id": "154006", "num": 6, "name": "SKT T1 자크", "chromas": false}, {"id": "154007", "num": 7, "name": "전투 기계 자크", "chromas": true}, {"id": "154014", "num": 14, "name": "창공 자크", "chromas": true}, {"id": "154024", "num": 24, "name": "딥 소스 자크", "chromas": true}], "lore": "자크는 화학공학 지층을 따라 흐른 독성물질이 자운의 지하동굴 깊은 곳에 위치한 웅덩이에 모여 만들어진 생명체이다. 이처럼 변변치 못한 태생에도 불구하고 자크는 원시적인 진흙의 상태에서 지성을 갖춘 존재로 성장했다. 그는 자운의 배관 속에 살면서 도움이 필요한 이들을 돕거나 자운의 망가진 기반시설을 재건하기 위해 이따금 모습을 드러낸다.", "blurb": "자크는 화학공학 지층을 따라 흐른 독성물질이 자운의 지하동굴 깊은 곳에 위치한 웅덩이에 모여 만들어진 생명체이다. 이처럼 변변치 못한 태생에도 불구하고 자크는 원시적인 진흙의 상태에서 지성을 갖춘 존재로 성장했다. 그는 자운의 배관 속에 살면서 도움이 필요한 이들을 돕거나 자운의 망가진 기반시설을 재건하기 위해 이따금 모습을 드러낸다.", "allytips": ["전장에서 살아남으려면 반드시 떨어져나간 조각을 흡수해야 합니다.", "세포 분열을 사용할 수 있을 때는 적이 갈라진 몸 조각을 처치하기 어려운 곳으로 가서 죽는 게 유리합니다.", "적이 대응할 틈이 없도록, 새총 발사는 전장의 안개 속에 숨어서 충전하세요."], "enemytips": ["자크는 몸에서 떨어져나간 조각을 흡수해 체력을 회복합니다. 이 조각을 밟아 으깨세요.", "자크가 죽었을 때 갈라져나간 조각들을 각각 처치하여 부활하지 못하게 막으세요.", "자크가 새총 발사를 충전 중일 때는 침묵, 기절, 속박, 띄우기로 끊을 수 있습니다."], "tags": ["Tank", "Fighter"], "partype": "없음", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 8}, "stats": {"hp": 685, "hpperlevel": 109, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.6, "attackspeed": 0.736}, "spells": [{"id": "ZacQ", "name": "탄성 주먹", "description": "자크가 팔을 쭉 뻗어 적을 붙잡습니다. 다른 적을 공격하면 둘을 서로에게 던져 충돌하게 합니다.", "tooltip": "자크가 팔을 뻗어 처음 맞힌 적을 붙잡고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히며 잠깐 동안 <status>둔화</status>시킵니다. 자크의 다음 기본 공격은 사거리가 증가하며 동일한 피해를 입히고 <status>둔화</status>시킵니다. <br /><br />자크가 <i>다른</i> 적에게 기본 공격을 가하면 둘을 <status>공중으로 띄워 올려</status> 서로에게 던집니다. 충돌 시 해당 적과 주변 적은 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입으며 잠깐 동안 <status>둔화</status>합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [8, 8, 8, 8, 8], [800, 800, 800, 800, 800], [-0.4, -0.4, -0.4, -0.4, -0.4], [0.5, 0.5, 0.5, 0.5, 0.5], [2.5, 2.5, 2.5, 2.5, 2.5], [900, 900, 900, 900, 900], [700, 700, 700, 700, 700], [300, 300, 300, 300, 300], [300, 300, 300, 300, 300]], "effectBurn": [null, "40/55/70/85/100", "8", "800", "-0.4", "0.5", "2.5", "900", "700", "300", "300"], "vars": [], "costType": "% ({{ healthcosttooltip }}) 소모", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZacQ.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "현재 체력의 {{ e2 }}% ({{ health<PERSON><PERSON><PERSON>ip }}) 소모"}, {"id": "ZacW", "name": "불안정 물질", "description": "자크의 몸이 폭발하며 주위 적들에게 대상 최대 체력의 일정 비율에 해당하는 마법 피해를 입힙니다.", "tooltip": "자크의 몸이 터져서 주위에 있는 적 모두에게 <magicDamage>{{ basedamage }}+최대 체력의 {{ displaypercentdamage }}에 해당하는 마법 피해</magicDamage>를 입힙니다.<br /><br /><keywordMajor>조각</keywordMajor>을 흡수하면 이 스킬의 재사용 대기시간이 1초 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "적 최대 체력 비례 피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealthdamage*100.000000 }}% -> {{ basemaxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 50, 60, 70, 80], [4, 4, 4, 4, 4], [0.04, 0.05, 0.06, 0.07, 0.08], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/50/60/70/80", "4", "0.04/0.05/0.06/0.07/0.08", "1", "200", "0", "0", "0", "0", "0"], "vars": [], "costType": "% ({{ tooltiphealthcost }}) 소모", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "ZacW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "현재 체력의 {{ e2 }}% ({{ tooltiphealthcost }}) 소모"}, {"id": "ZacE", "name": "새총 발사", "description": "자크가 지면에 두 팔을 붙이고 뒤로 늘어난 다음, 전방으로 몸을 날립니다.", "tooltip": "<charge>충전 시작:</charge> 자크가 {{ e4 }}초 동안 자신의 몸을 팽팽히 당겨 돌진할 준비를 합니다.<br /><br /><release>발사:</release> 자크가 자신의 몸을 날려 착지하는 곳에 있는 적을 충전 시간에 비례해 최대 {{ maxstun }}초 동안 <status>공중으로 띄워 올리고</status> <magicDamage>{{ damage }}의 마법 피해</magicDamage>를 입힙니다. 적 챔피언을 하나 맞힐 때마다 <keywordMajor>조각</keywordMajor>이 하나씩 생성됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "사거리", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [4, 4, 4, 4, 4], [1200, 1350, 1500, 1650, 1800], [0.9, 1, 1.1, 1.2, 1.3], [0.5, 0.5, 0.5, 0.5, 0.5], [500, 500, 500, 500, 500], [1350, 1350, 1350, 1350, 1350], [0.6, 0.6, 0.6, 0.6, 0.6], [265, 265, 265, 265, 265], [1, 1, 1, 1, 1]], "effectBurn": [null, "60/105/150/195/240", "4", "1200/1350/1500/1650/1800", "0.9/1/1.1/1.2/1.3", "0.5", "500", "1350", "0.6", "265", "1"], "vars": [], "costType": "% ({{ healthcosttooltip }}) 소모", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "현재 체력의 {{ e2 }}% ({{ health<PERSON><PERSON><PERSON>ip }}) 소모"}, {"id": "ZacR", "name": "바운스!", "description": "자크가 4회 튀어 올라 적중당한 적을 공중에 띄우고 둔화시킵니다.", "tooltip": "자크가 {{ bounces }}회 튀어 오릅니다. 자크에게 처음 맞은 적은 뒤로 <status>밀리며</status> <magicDamage>{{ damageperbounce }}의 마법 피해</magicDamage>를 입습니다. 이후에는 맞을 때마다 <magicDamage>{{ damagepersubsequentbounce }}의 마법 피해</magicDamage>를 입고 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>합니다.<br /><br />자크는 <speed>이동 속도가 {{ endingms*100 }}%</speed>까지 점점 증가하며 튀어 오르는 동안 <spellName>불안정 물질</spellName> 스킬을 사용할 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 반동 피해량", "재사용 대기시간"], "effect": ["{{ basedamagebounce }} -> {{ basedamagebounceNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [1.1, 1.1, 1.1], [700, 850, 1000], [-0.3, -0.4, -0.5], [2.5, 2.5, 2.5], [275, 275, 275], [300, 300, 300], [1, 1, 1], [0.25, 0.25, 0.25], [0.1, 0.1, 0.1]], "effectBurn": [null, "150/250/350", "1.1", "700/850/1000", "-0.3/-0.4/-0.5", "2.5", "275", "300", "1", "0.25", "0.1"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "세포 분열", "description": "자크는 스킬로 적을 맞힐 때마다 몸에서 조각이 떨어져 나갑니다. 이 조각들을 다시 흡수하면 체력을 회복할 수 있습니다. 자크는 치명상을 입으면 4조각으로 갈라졌다가 다시 합쳐지려고 모입니다. 몸 조각 중 하나라도 생존할 경우, 살아남은 조각의 체력에 비례한 체력을 가지고 부활합니다. 각각의 조각은 자크의 최대 체력, 방어력과 마법 저항력의 일부를 보유합니다. 이 스킬의 재사용 대기시간은 5분입니다.", "image": {"full": "ZacPassive.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}