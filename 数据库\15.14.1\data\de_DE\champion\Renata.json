{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "Renata <PERSON>", "title": "die Chem-Baronin", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "Admiral <PERSON><PERSON>", "chromas": true}, {"id": "888010", "num": 10, "name": "Schreckensnacht-<PERSON>ata <PERSON>", "chromas": true}, {"id": "888020", "num": 20, "name": "La Ilusión Renata Glasc", "chromas": true}, {"id": "888021", "num": 21, "name": "La Ilusión Renata Glasc (Prestige)", "chromas": false}, {"id": "888031", "num": 31, "name": "Maske der Schwarzen Rose Renata Glasc", "chromas": false}], "lore": "Renata Glasc erhob sich wie ein Phönix aus der Asche des Hauses, in dem sie aufgewachsen war, und hatte nichts als ihren Namen und die alchemistischen Forschungen ihrer Eltern. In den folgenden Jahrzehnten wurde sie zu Zhauns wohlhabendster Chem-Baronin und einflussreicher Unternehmerin, die die Interessen ihrer Partner und Gegenspieler mit ihren eigenen verknüpft hat. So erlangte sie große <PERSON>ht. Wer mit ihr arbeitet, wird über alle Maßen entlohnt. Wer gegen sie arbeitet, bereut es bitter. Am Ende aber landen sie sowieso alle auf ihre Seite.", "blurb": "Renata Glasc erhob sich wie ein Phönix aus der Asche des Hauses, in dem sie aufgewachsen war, und hatte nichts als ihren Namen und die alchemistischen Forschungen ihrer Eltern. In den folgenden Jahrzehnten wurde sie zu Zhauns wohlhabendster Chem-Baronin...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "Handschlag", "description": "Renata feuert ein Geschoss ab, das den ersten getroffenen Gegner festhält. Sie kann die Fähigkeit erneut einsetzen, um die Einheit in eine Richtung zu schleudern.", "tooltip": "Renata feuert ein Geschoss aus ihrem Arm ab, das den ersten getroffenen Gegner {{ rootduration }}&nbsp;Sekunde(n) lang <status>festhält</status> und ihm <magicDamage>{{ totaldamage }}</magicDamage>&nbsp;<magicDamage>magischen Schaden</magicDamage> zufügt.<br /><br /><recast>Reaktivierung:</recast> Renata <status>zieht</status> den Gegner in eine Richtung, verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> an <PERSON><PERSON><PERSON><PERSON>, die von der geworfenen Einheit getroffen werden, und <status>betäubt</status> sie {{ stunduration }}&nbsp;Sekunden lang, falls der geworfene Gegner ein Champion ist.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataW", "name": "Rettungsschirm", "description": "Renata verstärkt einen verbündeten Champion mit einem Buff, der seinen Tod hi<PERSON>t und ihn davor bewahren kann, wenn er an einem Kill beteiligt ist.", "tooltip": "Renata verstärkt einen verbündeten Champion, der daraufhin <attackSpeed>{{ ascalc }}&nbsp;Angriffstempo</attackSpeed> und <speed>{{ mscalc }}&nbsp;Lauftempo</speed> in Richtun<PERSON> von Gegnern erhält (erhöht sich über {{ duration }}&nbsp;Sekunden hinweg auf bis zu <attackSpeed>{{ finalascalc }}&nbsp;Angriffstempo</attackSpeed> und <speed>{{ finalmscalc }}&nbsp;Lauftempo</speed>). Bei einer Killbeteiligung wird die Dauer des Buffs zurückgesetzt.<br /><br />Wenn der Verbündete sterben würde, wird stattdessen sein gesamtes Leben wiederhergestellt, bevor es über 3&nbsp;Sekunden hinweg abfällt.<br /><br />Wenn der Verbündete während dieser Zeit an einem Kill beteiligt ist, wird er auf <healing>{{ triumphpercent }}&nbsp;% seines maximalen Lebens</healing> gese<PERSON><PERSON>, das nicht weiter abfällt.<br /><br /><rules>Der Tod des Champions kann durch Heilung oder ähnliche Effekte hinausgezögert werden, er wird jedoch nur durch eine Killbeteiligung verhindert. Champions können ihren Tod nur einmal hinauszögern.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Lauftempo", "Abklingzeit"], "effect": ["{{ bonusattackspeed }}&nbsp;% -> {{ bonusattackspeedNL }}&nbsp;%", "{{ bonusmovespeed }}&nbsp;% -> {{ bonusmovespeedNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataE", "name": "Loyalitätsprogramm", "description": "Renata feuert zwei Chemtech-Geschosse ab, die Verbündeten einen Schild gewähren und getroffenen Gegnern Schaden zufügen und sie verlangsamen.", "tooltip": "Renata feuert zwei Chemtech-Geschosse ab, die umstehenden und getroffenen Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügen und sie {{ slowduration }}&nbsp;Sekunden lang um 30&nbsp;% <status>verlangsamen</status>. Getroffene Verbündete erhalten {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ shieldcalc }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Schildstärke", "Kosten (@AbilityResourceName@)"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataR", "name": "Feindliche Übernahme", "description": "Renata setzt eine Chemikalienwolke frei, die alle getroffenen Gegner in Raserei versetzt.", "tooltip": "Renata setzt eine Chemikalienwolke frei, die alle getroffenen Gegner {{ berserkduration }}&nbsp;Sekunden lang <status>in Raserei</status> versetzt. Sie greifen dann die nächstbefindliche Einheit an, wobei ihre eigenen Verbündeten priorisiert werden.<br /><br />Im Zustand der <status>Raserei</status> erhalten die Gegner <attackSpeed>{{ bonusattackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Investition", "description": "Renatas Angriffe verursachen zusätzlichen Schaden und markieren Gegner. Wenn Renatas Verbündete markierten Gegnern Schaden zufügen, verursachen sie zusätzlichen Schaden.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}