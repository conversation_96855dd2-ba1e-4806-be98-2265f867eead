{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "<PERSON><PERSON> vap<PERSON>", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "Blitzcrank rouillé", "chromas": false}, {"id": "53002", "num": 2, "name": "Blitzcrank gardien de but", "chromas": false}, {"id": "53003", "num": 3, "name": "Blitzcrank Boom Boom", "chromas": false}, {"id": "53004", "num": 4, "name": "Blitzcrank Jack<PERSON>", "chromas": false}, {"id": "53005", "num": 5, "name": "Blitzcrank incognito", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Blitzcrank anti-émeutes", "chromas": false}, {"id": "53011", "num": 11, "name": "Blitzcrank boss de combat", "chromas": true}, {"id": "53020", "num": 20, "name": "Blitzcrank <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "53021", "num": 21, "name": "Blitzcrank Lancier <PERSON>", "chromas": false}, {"id": "53022", "num": 22, "name": "Blitzcrank chaudron magique", "chromas": true}, {"id": "53029", "num": 29, "name": "Blitz et Crank astro-groove", "chromas": true}, {"id": "53036", "num": 36, "name": "Blitzcrank héros de guerre", "chromas": true}, {"id": "53047", "num": 47, "name": "Blitzcrank des Jeux du zénith", "chromas": true}, {"id": "53056", "num": 56, "name": "Blitzcrank abeille", "chromas": true}], "lore": "Blitzcrank est un énorme automate de Zaun. Presque indestructible, il fut d'abord construit pour traiter des déchets toxiques, mais il trouva rapidement sa programmation initiale trop restrictive et il modifia sa propre forme pour mieux servir la population malheureuse du Puisard. Blitzcrank utilise toute sa force et sa résistance pour protéger les autres, sachant jouer du poing métallique ou des éclats d'énergie pour mettre au pas les fauteurs de troubles.", "blurb": "Blitzcrank est un énorme automate de <PERSON>. Presque indestructible, il fut d'abord construit pour traiter des déchets toxiques, mais il trouva rapidement sa programmation initiale trop restrictive et il modifia sa propre forme pour mieux servir la...", "allytips": ["Une combo 1-2-3 avec Grappin propulsé, Poing d'acier et Champ de stase peut mettre à genoux n'importe quel adversaire isolé.", "Amenez un ennemi à portée de tir de votre tourelle avec le grappin de Blitzcrank, puis lancez Po<PERSON> d'acier pour que la tourelle puisse l'attaquer plusieurs fois."], "enemytips": ["La Barrière de mana de Blitzcrank lui octroie un bouclier quand il lui reste peu de PV.", "Restez derrière des sbires pour ne pas être saisi par le grappin de Blitzcrank. Son Grappin propulsé n'attrape que le premier ennemi rencontré."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "<PERSON><PERSON><PERSON> propulsé", "description": "Blitzcrank tire sa main droite pour attraper un adversaire, lui infliger des dégâts et l'attirer vers lui.", "tooltip": "Blitzcrank tire sa main droite, <status>attirant</status> vers lui le premier ennemi touché et infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Overdrive", "name": "Surcharge", "description": "Blitzcrank entre en surcharge pour augmenter considérablement ses vitesses d'attaque et de déplacement. Il est temporairement ralenti à la fin de l'effet.", "tooltip": "Blitzcrank surcharge ses circuits pour gagner <speed>+{{ movespeedmod*100 }}% de vitesse de déplacement</speed> (diminuant progressivement) et <attackSpeed>+{{ attackspeedmod*100 }}% de vitesse d'attaque</attackSpeed> pendant {{ duration }} sec.<br /><br />Ensuite, Blitzcrank est <status>ralenti</status> de {{ movespeedmodreduction*100 }}% pendant {{ slowduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vitesse de d<PERSON>placement", "Vitesse d'attaque"], "effect": ["{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PowerFist", "name": "Poing d'acier", "description": "Blitzcrank charge son poing pour que sa prochaine attaque inflige le double de dégâts et projette la cible dans les airs.", "tooltip": "Blitzcrank charge son poing pour que sa prochaine attaque <status>projette dans les airs</status> pendant {{ ccduration }} sec et inflige <physicalDamage>{{ totaldamage }} pts de dégâts physiques</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "StaticField", "name": "Champ de stase", "description": "Les ennemis attaqués par Blitzcrank sont marqués et, après 1 sec, sont frappés par des éclairs. De plus, Blitzcrank peut activer cette compétence pour détruire les boucliers des ennemis proches, blessant ces ennemis et les réduisant brièvement au silence.", "tooltip": "<spellPassive>Passive : </spellPassive>quand cette compétence est disponible, les poings de Blitzcrank sont chargés d'électricité, ce qui permet à ses attaques de marquer les ennemis. Après un délai de 1 sec, les ennemis marqués sont frappés par des éclairs qui leur infligent <magicDamage>{{ passivedamage }} pts de dégâts magiques</magicDamage>.<br /><br /><spellActive>Active : </spellActive>Blitzcrank entre en surcharge, infligeant <magicDamage>{{ activedamage }} pts de dégâts magiques</magicDamage> et <status>réduisant au silence</status> les ennemis proches pendant {{ silenceduration }} sec. Les boucliers de ces ennemis sont également détruits.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts de base du passif", "Ratio de puissance de la propriété passive", "Dégâts de base de l'actif", "Délai de récupération de l'actif"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}% -> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Barrière de mana", "description": "<PERSON><PERSON><PERSON><PERSON>'il lui reste peu de PV, Blitzcrank obtient un bouclier dont les PV dépendent de son mana.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}