{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Janna": {"id": "<PERSON><PERSON>", "key": "40", "name": "<PERSON><PERSON>", "title": "la furia della tempesta", "image": {"full": "Janna.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "40000", "num": 0, "name": "default", "chromas": false}, {"id": "40001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40006", "num": 6, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40007", "num": 7, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40008", "num": 8, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "40013", "num": 13, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "40020", "num": 20, "name": "<PERSON><PERSON> delle Sabbie", "chromas": true}, {"id": "40027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "40036", "num": 36, "name": "<PERSON><PERSON>ristallo", "chromas": true}, {"id": "40045", "num": 45, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "40046", "num": 46, "name": "<PERSON><PERSON>ibernet<PERSON> (edizione prestigio)", "chromas": false}, {"id": "40056", "num": 56, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "40066", "num": 66, "name": "<PERSON>na Portat<PERSON> dell'Alba", "chromas": false}], "lore": "Armata con il potere dei venti di Runeterra, Jan<PERSON> è un misterioso spirito elementale del vento che protegge i diseredati di Zaun. Alcuni credono che sia nata grazie alle preghiere dei marinai di Runeterra, che chiedevano venti favorevoli nelle insidiose acque e nelle tempeste più impetuose. Il suo favore e la sua protezione sono poi stati invocati nelle profondità di Zaun, dove Janna è diventata un simbolo di speranza per i bisognosi. Nessuno sa dove o quando apparirà, ma solitamente lo fa per aiutare.", "blurb": "Armata con il potere dei venti di Runeterra, <PERSON><PERSON> è un misterioso spirito elementale del vento che protegge i diseredati di Zaun. Alcuni credono che sia nata grazie alle preghiere dei marinai di Runeterra, che chiedevano venti favorevoli nelle...", "allytips": ["Occhio del ciclone può essere usato sulle torri alleate.", "Usare velocemente Tempesta ululante senza caricarla è utile per lanciare impedimenti contro la squadra avversaria.", "Lanciare con precisione l'abilità suprema di Janna fa allontanare i nemici da un alleato ferito o li fa addirittura dividere."], "enemytips": ["Conserva un'abilità di interruzione per quando Janna usa la sua abilità suprema.", "Fai attenzione al suono della carica di Tempesta ululante, nel caso in cui Janna cerchi di colpirti da fuori lo schermo o dall'erba alta.", "<PERSON><PERSON> è più forte quando sta potenziando un altro alleato. Se riesci ad aggredire il suo alleato, indebolirai la sua forza."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 50, "movespeed": 325, "armor": 28, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON>G<PERSON>", "name": "<PERSON><PERSON><PERSON> ululante", "description": "Creando un cambiamento nella pressione e nella temperatura, <PERSON><PERSON> in grado di creare un piccolo tornado che cresce col tempo. Può attivare di nuovo l'abilità per rilasciare il tornado. Al rilascio, il tornado vola verso la direzione in cui è stato lanciato, infliggendo danni e lanciando in aria i nemici sulla sua traiettoria.", "tooltip": "<PERSON><PERSON> evoca un tornado che aumenta di forza nell'arco di {{ maxduration }} secondi per poi iniziare ad avanzare in linea retta. Infligge <magicDamage>{{ minimumdamage }} - {{ maxdamage }} danni magici</magicDamage> e <status>lancia in aria</status> le unità colpite per {{ baseknockup }} - {{ maxknockup }} secondi. Distan<PERSON>, danni e durata del <status>lancio in aria</status> aumentano in base a quanta forza ha accumulato il tornado. <PERSON><PERSON> pu<PERSON> <recast>rilanciare</recast> quest'abilità per far muovere il tornado in anticipo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> al secondo caricati", "Costo in @AbilityResourceName@"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "HowlingGale.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SowTheWind", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> e<PERSON>a un elementale dell'aria che aumenta passivamente la sua velocità di movimento e le permette di passare attraverso le unità. Può anche attivare l'abilità per infliggere danni e rallentare la velocità di movimento del nemico.", "tooltip": "<spellPassive>Passiva:</spellPassive> <PERSON><PERSON> <speed>{{ totalms }} velocità di movimento</speed> e Spettralità.<br /><br /><spellActive>Attiva:</spellActive> l'elementale di Janna colpisce un nemico, <status>rallentandolo</status> del {{ totalslow }} per {{ slowduration }} secondi e infliggendo <magicDamage>{{ totaldamage }} + {{ spell.tailwindself:bonusdamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Velocità di movimento passiva", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ mspercent*100.000000 }}% -> {{ mspercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4294967295, 4294967295, 4294967295, 4294967295, 4294967295], "rangeBurn": "4294967295", "image": {"full": "SowTheWind.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EyeOfTheStorm", "name": "Occhio del ciclone", "description": "<PERSON><PERSON> e<PERSON>a una tempesta difensiva che protegge un campione o una torre alleata dai danni, aumentando il suo attacco fisico.", "tooltip": "<PERSON><PERSON> conferisce a un campione o a una torre alleata uno <shield>scudo da {{ totalshield }}</shield> per {{ shieldduration }} secondi. Quando lo scudo è attivo, ottiene <scaleAD>{{ totalad }} attacco fisico</scaleAD>.<br /><br /><PERSON>na rimborsa un {{ ecdrefundforcc*100 }}% della ricarica ogni volta che ostacola i movimenti di un campione nemico con un'abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute scudo", "Attacco fisico", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ bonusad }} -> {{ bonusadNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EyeOfTheStorm.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ReapTheWhirlwind", "name": "Mon<PERSON>e", "description": "<PERSON>na si circonda di una tempesta magica che respinge i nemici. <PERSON><PERSON> che la tempesta si è calmata, un vento rassicurante cura gli alleati nelle vicinanze finché l'abilità è attiva.", "tooltip": "<PERSON><PERSON> evoca un monsone magico, che <status>respinge</status> i nemici e guarisce gli alleati vicini di <healing>{{ totalheal }} salute</healing> nell'arco di {{ e3 }} secondi. Muoversi o usare un'abilità annulla il monsone in anticipo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione al secondo", "Ricarica"], "effect": ["{{ healbasepersecond }} -> {{ healbasepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [300, 450, 600], [3, 3, 3], [700, 700, 700], [875, 875, 875], [875, 875, 875], [1200, 1200, 1200], [10, 10, 10], [0.5, 0.5, 0.5], [0, 0, 0]], "effectBurn": [null, "0", "300/450/600", "3", "700", "875", "875", "1200", "10", "0.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725], "rangeBurn": "725", "image": {"full": "ReapTheWhirlwind.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Vento in poppa", "description": "<PERSON>li alleati di Janna ottengono velocità di movimento spostandosi verso di lei.<br><br><PERSON><PERSON> infligge una parte della velocità di movimento bonus come danni magici bonus sul colpo e con Zefiro.", "image": {"full": "Janna_Tailwind.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}