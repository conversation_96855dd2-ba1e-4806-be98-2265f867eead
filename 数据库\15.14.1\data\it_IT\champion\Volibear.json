{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Volibear": {"id": "Volibear", "key": "106", "name": "Volibear", "title": "la Tempesta implacabile", "image": {"full": "Volibear.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "106000", "num": 0, "name": "default", "chromas": false}, {"id": "106001", "num": 1, "name": "Volibear Signore del Tuono", "chromas": false}, {"id": "106002", "num": 2, "name": "Volibear Tempesta del Nord", "chromas": false}, {"id": "106003", "num": 3, "name": "Volibear Guardia Runica", "chromas": false}, {"id": "106004", "num": 4, "name": "Capitano Volibear", "chromas": false}, {"id": "106005", "num": 5, "name": "Volibear El Rayo", "chromas": false}, {"id": "106006", "num": 6, "name": "L'Orso dalle Mille Ferite", "chromas": false}, {"id": "106007", "num": 7, "name": "Volibear Drago del Dualismo", "chromas": true}, {"id": "106009", "num": 9, "name": "Volibear Drago del Dualismo (edizione prestigio)", "chromas": false}, {"id": "106019", "num": 19, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Per coloro che ancora lo venerano, il Volibear è l'avatar della tempesta. Distruttivo, selvaggio, testardo e risoluto, esiste da prima che i mortali camminassero nella tundra del Freljord e protegge con ferocia le terre create da lui e dai suoi fratelli semidei. Spinto da un profondo odio per la civilizzazione e le debolezze che questa porta con sé, combatte per ristabilire le antiche tradizioni, quando la terra selvaggia era percorsa da fiumi di sangue, e combatte chiunque si opponga a lui con zanne, artigli e tuoni possenti.", "blurb": "Per coloro che ancora lo venerano, il Volibear è l'avatar della tempesta. Distruttivo, selvaggio, testardo e risoluto, esiste da prima che i mortali camminassero nella tundra del Freljord e protegge con ferocia le terre create da lui e dai suoi fratelli...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 7, "magic": 4, "difficulty": 3}, "stats": {"hp": 650, "hpperlevel": 104, "mp": 350, "mpperlevel": 70, "movespeed": 340, "armor": 31, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.75, "mpregen": 6.25, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "VolibearQ", "name": "<PERSON><PERSON>to tonante", "description": "Volibear ottiene velocità e si scaglia contro i nemici, <status>stordendo</status> e infliggendo danni al primo bersaglio che attacca.", "tooltip": "Volibear guadagna <speed>{{ minspeedcalc }} velocità di movimento</speed>, raddoppiata a <speed>{{ maxspeedcalc }}</speed> verso i campioni nemici, per i prossimi {{ duration }} secondi. Mentre l'abilità è attiva, il prossimo attacco di Volibear infligge <physicalDamage>{{ calculateddamage }} danni fisici</physicalDamage> e <status>stordisce</status> il bersaglio per {{ stunduration }} secondo.<br /><br />Volibear diventa furioso se un nemico lo <status>immobilizza</status> prima che possa <status>stordire</status> un bersaglio: in questo caso l'abilità si interrompe, ma la ricarica è azzerata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ minspeed*100.000000 }}% -> {{ minspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VolibearQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VolibearW", "name": "<PERSON><PERSON>", "description": "Volibear infligge danni a un nemico, applicando effetti sul colpo e march<PERSON>.  Lanciando di nuovo questa abilità sullo stesso bersaglio, Volibear infligge danni bonus e si cura.", "tooltip": "Volibear dilania un nemico infliggendogli <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e marchiandolo per {{ markduration }} secondi.<br /><br />Se questa abilità viene usata su un bersaglio marchiato, i suoi danni aumentano a <physicalDamage>{{ empowereddamage }}</physicalDamage> e Volibear recupera <healing>{{ baseheal }} più {{ percentmissinghealthhealingratio }} della sua salute mancante</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione base", "Percentuale salute mancante", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ healpercent*100.000000 }}% -> {{ healpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "VolibearW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VolibearE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Volibear evoca sulla posizione bersaglio un fulmine che infligge danni e rallenta i nemici, oltre a ottenere uno scudo se si trova nel raggio dell'esplosione.", "tooltip": "Volibear evoca una nube che scaglia un fulmine infliggendo <magicDamage>{{ totaldamagetooltip }} (+ {{ percentdamage*100 }}% della salute massima del bersaglio) danni magici</magicDamage> e <status>rallentando</status> i nemici del {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />Se Volibear si trova all'interno dell'esplosione, ottiene uno <shield>scudo da {{ shieldapratiotooltip }} (+ {{ shieldamount*100 }}% della propria salute massima)</shield> per {{ shieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percentdamage*100.000000 }}% -> {{ percentdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VolibearE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VolibearR", "name": "Incarna della tempesta", "description": "Volibear balza verso una posizione bersaglio, rallentando e dannegg<PERSON>do i nemici sotto di lui mentre guadagna salute bonus. Le torri nemiche vicine al punto d'atterraggio vengono temporaneamente disattivate.", "tooltip": "Volibear si trasforma e balza, guadagnan<PERSON> <healing>{{ healthamount }} salute</healing> e {{ bonusattackrange }} gittata d'attacco per i prossimi {{ transformduration }} secondi.<br /><br />All'atterraggio Volibear frantuma il terreno, <status>disattivando</status> le torri vicine per {{ towerdisableduration }} secondi e infliggendo loro <physicalDamage>{{ towerdamagetooltip }} danni fisici</physicalDamage>. I nemici vicini subiscono un <status>rallentamento</status> del {{ slowamount*100 }}% che decade in 1 secondo. I nemici che si trovano sotto Volibear subiscono <physicalDamage>{{ sweetspotdamagetooltip }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Salute bonus", "Durata disattivazione torre", "Ricarica"], "effect": ["{{ sweetspotdamage }} -> {{ sweetspotdamageNL }}", "{{ healthamount }} -> {{ healthamountNL }}", "{{ towerdisableduration }} -> {{ towerdisabledurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 135, 110], "cooldownBurn": "160/135/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [75, 115, 155], [9, 9, 9], [12, 12, 12], [8, 8, 8], [0.08, 0.08, 0.08], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "75/115/155", "9", "12", "8", "0.08", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "VolibearR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "La Tempesta implacabile", "description": "Gli attacchi e le abilità di Volibear conferiscono velocità d'attacco, e col tempo permettono ai suoi attacchi di infliggere danni magici bonus ai nemici vicini.", "image": {"full": "Volibear_Icon_P.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}