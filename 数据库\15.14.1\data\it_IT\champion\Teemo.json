{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Teemo": {"id": "<PERSON><PERSON><PERSON>", "key": "17", "name": "<PERSON><PERSON><PERSON>", "title": "il rapido scout", "image": {"full": "Teemo.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "17000", "num": 0, "name": "default", "chromas": false}, {"id": "17001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "17005", "num": 5, "name": "Teemo Coda a Batuffoli", "chromas": true}, {"id": "17006", "num": 6, "name": "Super Teemo", "chromas": false}, {"id": "17007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17008", "num": 8, "name": "<PERSON><PERSON><PERSON>ra <PERSON>", "chromas": true}, {"id": "17014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "17018", "num": 18, "name": "L'Ape Teemo", "chromas": true}, {"id": "17025", "num": 25, "name": "Teemo Fiore spirituale", "chromas": false}, {"id": "17027", "num": 27, "name": "Teemo Fiore spirituale (edizione prestigio)", "chromas": false}, {"id": "17037", "num": 37, "name": "Teemo Fuoco d'Artificio", "chromas": true}, {"id": "17047", "num": 47, "name": "<PERSON>emo Ritmo <PERSON>", "chromas": true}], "lore": "Teemo esplora il mondo con entusiasmo e allegria, senza aver paura persino degli ostacoli più pericolosi e minacciosi. Uno yordle con un senso della morale incrollabile, fiero sostenitore del codice degli scout di Bandle, che a volte non realizza la portata più ampia delle sue azioni, per colpa dell'entusiasmo. Alcuni dicono che l'esistenza degli scout è discutibile, ma una cosa è certa: la determinazione di Teemo non va presa alla leggera.", "blurb": "Teemo esplora il mondo con entusiasmo e allegria, senza aver paura persino degli ostacoli più pericolosi e minacciosi. Uno yordle con un senso della morale incrollabile, fiero sostenitore del codice degli scout di Bandle, che a volte non realizza la...", "allytips": ["I funghi di Teemo possono essere usati per finire ondate di minion in maniera efficace.", "Piazza i tuoi funghi in luoghi chiave della mappa, come vicino al Drago o al Barone Nashor, per sapere quando i tuoi nemici cercano di ucciderli."], "enemytips": ["Il Colpo tossico di Teemo punisce i giocatori che, una volta colpiti, tornano indietro. Quindi stai a distanza di sicurezza finché non sei pronto a ingaggiare.", "Usare Lente dell'oracolo (Trinket) potrebbe essere importante per distruggere i funghi nei luoghi chiave."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 6}, "stats": {"hp": 615, "hpperlevel": 104, "mp": 334, "mpperlevel": 25, "movespeed": 330, "armor": 24, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 9.6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3, "attackspeedperlevel": 3.38, "attackspeed": 0.69}, "spells": [{"id": "TeemoQ", "name": "<PERSON><PERSON> a<PERSON>", "description": "Oscura la visione di un nemico con un potente veleno, infliggendo danni al bersaglio e facendo in modo che i suoi attacchi non vadano a segno per la durata dell'effetto.", "tooltip": "Teemo lancia un dardo, <status>accecando</status> il bersaglio per {{ blindduration }} secondi e infliggendo <magicDamage>{{ calculateddamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON>", "<PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ blindduration }} -> {{ blinddurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoQ.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TeemoW", "name": "Rapidità", "description": "Teemo va in giro per il campo di battaglia, e aumenta passivamente la sua velocità di movimento finché non è colpito da un campione nemico o una torre. Teemo può scattare per guadagnare un bonus alla velocità di movimento per un breve periodo di tempo, che non viene annullato se viene colpito.", "tooltip": "<spellPassive>Passiva:</spellPassive> <PERSON><PERSON><PERSON> ottiene <speed>{{ passivemovespeedbonus*100 }}% velocità di movimento</speed> se non ha subito danni da un campione o una torre nemica negli ultimi {{ passivecooldownondamagetaken }} secondi.<br /><br /><spellActive>Attiva:</spellActive> <PERSON>emo scatta, ottenendo <speed>{{ activemovespeedbonus*100 }}% velocità di movimento</speed> per {{ activemovespeedbuffduration }} secondi, che non perde se viene colpito.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento passiva", "Velocità di movimento attiva"], "effect": ["{{ passivemovespeedbonus*100.000000 }}% -> {{ passivemovespeedbonusnl*100.000000 }}%", "{{ activemovespeedbonus*100.000000 }}% -> {{ activemovespeedbonusnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TeemoW.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TeemoE", "name": "Colpo tossico", "description": "<PERSON><PERSON><PERSON> degli attacchi di Teemo avvelena il bersaglio, infliggendo danni all'impatto e poi a ogni secondo, per 4 secondi.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi di Teemo applicano un veleno, che infligge <magicDamage>{{ impactcalculateddamage }} danni magici</magicDamage> aggiuntivi più <magicDamage>{{ totaldotdamage }} danni magici</magicDamage> nell'arco di {{ poisonduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> impatto", "<PERSON><PERSON> al secondo"], "effect": ["{{ impactbasedamage }} -> {{ impactbasedamageNL }}", "{{ tickbasedamage }} -> {{ tickbasedamageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiva", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoE.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Passiva"}, {"id": "TeemoR", "name": "Trappola tossica", "description": "Teemo lancia una trappola esplosiva e velenosa, usando uno dei funghi che ha raccolto. Se un nemico attiva la trappola si sprigiona una nube velenosa, che rallenta i nemici e li danneggia nel tempo. Se Teemo lancia un fungo su un altro fungo, questo rimbalzerà ottenendo una gittata maggiore.", "tooltip": "Teemo lancia una trappola di funghi che esplode quando viene calpestata. Le trappole <status>rallentano</status> del {{ slowamount }}% e infliggono <magicDamage>{{ totaldamage }} danni magici</magicDamage> nell'arco di {{ debuffduration }} secondi. I nemici vengono rivelati per la stessa durata.<br /><br />Le trappole durano {{ mushroomduration }} minuti e sono invisibili. Un fungo lanciato su di un altro rimbalza prima di atterrare nella sua posizione. Questa abilità ha {{ maxammo }} cariche ({{ ammorechargetime }} secondi di ricarica).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Gitt<PERSON> lancio", "Distanza di rimbalzo massima", "Trappole massime", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ maxbouncedistance }} -> {{ maxbouncedistanceNL }}", "{{ maxammo }} -> {{ maxammoNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 3, "cooldown": [0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [75, 55, 35], "costBurn": "75/55/35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "3", "range": [600, 750, 900], "rangeBurn": "600/750/900", "image": {"full": "TeemoR.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Guerriglia", "description": "Se Teemo sta fermo e non agisce per un breve periodo di tempo, diventa invisibile. Se si trova nell'erba alta, Te<PERSON><PERSON> può attivare e mantenere l'Invisibilità mentre si muove. <PERSON><PERSON> che è tornato visibile, <PERSON>emo guadagna Elemento di sorpresa, aumentando la velocità d'attacco per alcuni secondi.", "image": {"full": "TeemoPassive.ASU_Teemo.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}