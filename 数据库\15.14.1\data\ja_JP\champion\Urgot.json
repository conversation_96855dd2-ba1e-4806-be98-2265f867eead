{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Urgot": {"id": "Urgot", "key": "6", "name": "アーゴット", "title": "ドレッドノート", "image": {"full": "Urgot.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "6000", "num": 0, "name": "default", "chromas": false}, {"id": "6001", "num": 1, "name": "なんて巨大なカニゴット", "chromas": false}, {"id": "6002", "num": 2, "name": "虐殺者アーゴット", "chromas": false}, {"id": "6003", "num": 3, "name": "バトルキャスト アーゴット", "chromas": true}, {"id": "6009", "num": 9, "name": "荒野のアーゴット", "chromas": true}, {"id": "6015", "num": 15, "name": "パジャマガーディアン コスプレ アーゴット", "chromas": true}, {"id": "6023", "num": 23, "name": "恐怖の夜アーゴット", "chromas": true}, {"id": "6032", "num": 32, "name": "くらしの安心 アーゴット", "chromas": true}], "lore": "かつてノクサスの処刑人として数多くの死体を積み上げたアーゴットは、自らが仕えた帝国に裏切られた。ゾウンの地下深くにある監獄鉱山・ドレッジに送られ、鉄の鎖に繋がれた彼は、そこで強さの本当の意味をその身に刻まれることになる。その後、街中に大混乱をもたらした災害に乗じて脱出した彼は、今や闇社会に強烈な影を落とす存在となった。かつて自らをも繋いでいた鎖に縛られる者たちを扇動しながら、自分の新たな“ホーム”から価値なき連中を間引き、苦痛の試練を与えているのだ。", "blurb": "かつてノクサスの処刑人として数多くの死体を積み上げたアーゴットは、自らが仕えた帝国に裏切られた。ゾウンの地下深くにある監獄鉱山・ドレッジに送られ、鉄の鎖に繋がれた彼は、そこで強さの本当の意味をその身に刻まれることになる。その後、街中に大混乱をもたらした災害に乗じて脱出した彼は、今や闇社会に強烈な影を落とす存在となった。かつて自らをも繋いでいた鎖に縛られる者たちを扇動しながら、自分の新たな“ホーム”から価値なき連中を間引き、苦痛の試練を与えているのだ。", "allytips": ["脚それぞれのクールダウンに注意しておこう。与えるダメージの多くはこれに頼ることになる。", "「コラプトシェル」や「ディスデイン」で対象をロックオンしてから「パージ」を使おう。複数の脚による攻撃を連続で発動させるのにうってつけだ。", "「デスグラインダー」は体力が低下している逃げ足の速い敵に使うのが効果的だ。"], "enemytips": ["アーゴットは脚から放たれる炎による攻撃に大きく依存している。脚それぞれにクールダウンがあり、攻撃可能な脚の方向に通常攻撃をする必要がある。複数の脚の攻撃を受けないようにしよう。", "アーゴットは「パージ」で大量のダメージを防ぎつつ、大量のダメージも与えられるが、射撃中は移動速度が低下する。", "「デスグラインダー」を当てられたら、処刑されないように、時間切れまで何とかして最大体力の25%以上を維持しよう。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 102, "mp": 340, "mpperlevel": 45, "movespeed": 330, "armor": 36, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 350, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 4, "attackspeedperlevel": 3.75, "attackspeed": 0.625}, "spells": [{"id": "UrgotQ", "name": "コラプトシェル", "description": "指定地点に榴弾を発射する。周囲の敵に物理ダメージを与えて、移動速度を低下させる。", "tooltip": "榴弾を発射して<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ", "スロウ効果"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "UrgotQ.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UrgotW", "name": "パージ", "description": "最も近くにいる敵に速射攻撃を行う。その間は移動速度が低下する。直前に他のスキルで攻撃した敵チャンピオンを優先して攻撃し、「エコーフレイム」を発動する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 自身の他のスキルが、最後に命中したチャンピオンを5秒間マークする。<br /><br /><spellActive>発動効果:</spellActive> 最も近くにいる敵(マークした敵を優先)にチェーンガンを発砲し、1秒間に{{ e8 }}回通常攻撃を行い、1回ごとに<physicalDamage>{{ damagepershot }}の物理ダメージ</physicalDamage>を与える。発砲中も移動可能で、{{ e2 }}%の<status>スロウ</status>耐性を獲得するが、<speed>移動速度は{{ e5 }}</speed>低下する。<br /><br />スキルレベルが最大になると、このスキルが無限に持続して、<toggle>発動/非発動</toggle>を切り替えられるようになる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "一発あたりの攻撃力反映率", "@AbilityResourceName@コスト"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e7 }}% -> {{ e7NL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 9, 6, 3, 0], "cooldownBurn": "12/9/6/3/0", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [40, 40, 40, 40, 40], [4, 4, 4, 4, 25000], [0, 0, 0, 0, 0], [125, 125, 125, 125, 125], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 23.5, 27, 30.5, 34], [3, 3, 3, 3, 3], [12, 12, 12, 12, 12], [50, 50, 50, 50, 50]], "effectBurn": [null, "30/60/90/120/150", "40", "4/4/4/4/25000", "0", "125", "0.5", "20/23.5/27/30.5/34", "3", "12", "50"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "UrgotW.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UrgotE", "name": "ディスデイン", "description": "指定方向に突撃しながらシールドを展開し、チャンピオン以外の敵ユニットを横に弾き飛ばす。敵チャンピオンを捕まえるとその場で止まり、そのチャンピオンを後方に投げ飛ばす。", "tooltip": "前方に突撃して、{{ eshieldduration }}秒間<shield>耐久値{{ etotalshieldhealth }}のシールド</shield>を獲得し、最初に当たったチャンピオンを{{ stunduration }}秒間<status>スタン</status>させて自身の背後に投げる。突撃中にぶつかったすべての敵は、<physicalDamage>{{ edamage }}の物理ダメージ</physicalDamage>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "シールド量", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ ebasedamage }} -> {{ ebasedamageNL }}", "{{ eshieldbasehealth }} -> {{ eshieldbasehealthNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "UrgotE.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UrgotR", "name": "デスグラインダー", "description": "ケミドリルを発射する。ドリルは最初に当たった敵チャンピオンに突き刺さる。その敵チャンピオンの体力が一定未満になると、アーゴットが弱者とみなして処刑する。", "tooltip": "ケミドリルを発射する。ドリルは最初に当たったチャンピオンに突き刺さり、<physicalDamage>{{ rcalculateddamage }}の物理ダメージ</physicalDamage>を与えて、{{ rslowduration }}秒間、減少体力1%につき1%の<status>スロウ効果</status>を与える(最大{{ rmovespeedmod }}%)。<br /><br />突き刺した対象の体力が{{ rhealththreshold }}%未満になると、このスキルを<recast>再発動</recast>できるようになり、対象に<status>サプレッション効果</status>を与えて自身のところまで引き寄せる。引き寄せ終えると対象をキルし、周囲の敵に{{ rfearduration }}秒間の<status>フィアー効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "UrgotR.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "エコーフレイム", "description": "通常攻撃時と「パージ」発動時にその方向にある脚から散発的に炎が放射され、物理ダメージを与える。", "image": {"full": "Urgot_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}