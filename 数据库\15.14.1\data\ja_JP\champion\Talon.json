{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Talon": {"id": "Talon", "key": "91", "name": "タロン", "title": "将軍の懐刀", "image": {"full": "Talon.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "91000", "num": 0, "name": "default", "chromas": false}, {"id": "91001", "num": 1, "name": "ストリート タロン", "chromas": false}, {"id": "91002", "num": 2, "name": "真紅の精鋭タロン", "chromas": false}, {"id": "91003", "num": 3, "name": "龍剣タロン", "chromas": true}, {"id": "91004", "num": 4, "name": "SSW タロン", "chromas": false}, {"id": "91005", "num": 5, "name": "ブラッドムーン タロン", "chromas": false}, {"id": "91012", "num": 12, "name": "不朽の剣タロン", "chromas": true}, {"id": "91020", "num": 20, "name": "タロン ブラックウッド", "chromas": true}, {"id": "91029", "num": 29, "name": "枯朽の薔薇タロン", "chromas": true}, {"id": "91038", "num": 38, "name": "荒野のタロン", "chromas": true}, {"id": "91039", "num": 39, "name": "プレステージ荒野のタロン", "chromas": false}, {"id": "91049", "num": 49, "name": "原始の襲撃タロン", "chromas": true}, {"id": "91059", "num": 59, "name": "最後の清算タロン", "chromas": false}], "lore": "タロンは闇を駆ける刃であり、誰にも悟られることなく攻撃し、気付かれる前に脱出することができる非情な殺し屋だ。暴力があふれる危険なノクサスの裏路地で育った彼は、生きるために戦い、殺し、盗みを余儀なくされて、優れたナイフの使い手として知られるようになった。現在は悪名高きデュ・クートウ家の養子となり、帝国の指揮のもとに殺しの仕事を請け負い、敵側の指導者、隊長、英雄…さらには支配者の軽蔑を勝った愚かなノクサス人たちまでも暗殺し続けている。", "blurb": "タロンは闇を駆ける刃であり、誰にも悟られることなく攻撃し、気付かれる前に脱出することができる非情な殺し屋だ。暴力があふれる危険なノクサスの裏路地で育った彼は、生きるために戦い、殺し、盗みを余儀なくされて、優れたナイフの使い手として知られるようになった。現在は悪名高きデュ・クートウ家の養子となり、帝国の指揮のもとに殺しの仕事を請け負い、敵側の指導者、隊長、英雄…さらには支配者の軽蔑を勝った愚かなノクサス人たちまでも暗殺し続けている。", "allytips": ["「ノクサスの刃」の近接攻撃を当てるために、「暗殺者の跳躍」で敵の背後を取ろう。", "「シャドウアサルト」は逃走手段としてきわめて有効だが、複数の敵を一度に攻撃する手段としても効果的だ。", "戦闘に入る前に、対象をよく見極めよう。すべてのスキルを1体の対象に集中させるのが効果的な場合が多いので、複数の敵に分散させてしまわないように注意。"], "enemytips": ["タロンの攻撃はすべて物理ダメージだ。早めに物理防御を強化して、強力な瞬間ダメージに備えよう。", "タロンは戦闘から逃走するとき「シャドウアサルト」に頼りがちだ。このスキルのクールダウン中は、かなり倒しやすくなる。", "タロンは高いローム能力を持っている。彼の居場所に常に注意を払っておくか、積極的にレーンをプッシュしてレーンから動けなくしてやろう。"], "tags": ["Assassin"], "partype": "マナ", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 658, "hpperlevel": 109, "mp": 400, "mpperlevel": 37, "movespeed": 335, "armor": 30, "armorperlevel": 4.7, "spellblock": 36, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 7.6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.9, "attackspeed": 0.625}, "spells": [{"id": "TalonQ", "name": "ノクサスの刃", "description": "タロンが対象を突き刺す。近接攻撃の射程内であれば、この攻撃はクリティカルダメージを与える。近接攻撃の射程外であれば、対象までジャンプしてから突き刺す。このスキルで対象を倒すと体力の一部が回復し、クールダウンの一部が解消する。", "tooltip": "対象に向かってジャンプして<physicalDamage>{{ leapdamage }}の物理ダメージ</physicalDamage>を与える。近接攻撃の射程で使用した場合はクリティカルになり、代わりに<physicalDamage>{{ criticaldamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />このスキルで対象を倒すと<healing>体力を{{ totalhealing }}</healing>回復して、このスキルのクールダウンが{{ cooldownrefund*100 }}%解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "TalonQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TalonW", "name": "飛燕手裏剣", "description": "ブーメランのように戻ってくる刃を同時に複数投げる。刃は往路と復路で敵を貫通するたびに物理ダメージを与える。復路で刃が敵ユニットに命中すると、追加ダメージと短時間のスロウ効果を与える。", "tooltip": "複数の刃を投げ、<physicalDamage>{{ totalinitialdamage }}の物理ダメージ</physicalDamage>を与える。その後、刃は自身のところに戻ってきて<physicalDamage>{{ totalreturndamage }}の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間{{ movespeedslow*100 }}%の<status>スロウ効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "初期ダメージ", "復路ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ initialbasedamage }} -> {{ initialbasedamageNL }}", "{{ returnbasedamage }} -> {{ returnbasedamageNL }}", "{{ movespeedslow*100.000000 }}% -> {{ movespeedslownl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TalonW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TalonE", "name": "暗殺者の跳躍", "description": "タロンはどんな地形や建造物も最大距離まで飛び越えられる。このスキルのクールダウンは短いが、飛び越えた地形や建造物に対しては長いクールダウンに入る。", "tooltip": "最も近くにある地形や建造物を飛び越える。同一の地形や建造物は再び飛び越えられるようになるまで{{ wallcd }}秒間待つ必要がある。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["地形毎のクールダウン"], "effect": ["{{ wallcd }} -> {{ wallcdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [625, 625, 625, 625, 625], [1250, 1250, 1250, 1250, 1250], [2, 2, 2, 2, 2], [160, 135, 110, 85, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "625", "1250", "2", "160/135/110/85/60", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "TalonE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "TalonR", "name": "シャドウアサルト", "description": "複数の刃を全方向に投げ、インビジブル状態になって移動速度が増加する。インビジブル状態が解除されると、投げた刃がタロンのいる地点に一斉に戻ってくる。飛んでいった時と戻ってきた時のそれぞれで、刃が命中した敵に物理ダメージを与える。", "tooltip": "周囲に複数の刃を投げて<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。さらに<speed>移動速度が{{ movespeed*100 }}%</speed>増加して、{{ duration }}秒間<keywordStealth>インビジブル</keywordStealth>状態になる。<keywordStealth>インビジブル</keywordStealth>状態が終了すると投げた刃が自身のところに戻り、再び<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。<br /><br />通常攻撃か<spellName>「ノクサスの刃」</spellName>で<keywordStealth>インビジブル</keywordStealth>状態をキャンセルすると、刃はその対象がいる位置に集まる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "移動速度", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TalonR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "血塗られし慈悲", "description": "タロンのスキルは敵チャンピオンまたは大型モンスターに3回までスタックする「傷」を付与する。「傷」のスタックが3つになった敵チャンピオンに通常攻撃を行うと、そのチャンピオンは出血して継続的に大ダメージを受ける。", "image": {"full": "TalonP.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}