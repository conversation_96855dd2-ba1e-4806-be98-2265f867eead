{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kalista": {"id": "Kalista", "key": "429", "name": "칼리스타", "title": "복수의 화신", "image": {"full": "Kalista.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "429000", "num": 0, "name": "default", "chromas": false}, {"id": "429001", "num": 1, "name": "핏빛달 칼리스타", "chromas": false}, {"id": "429002", "num": 2, "name": "2015 월드 챔피언십 칼리스타", "chromas": true}, {"id": "429003", "num": 3, "name": "SKT T1 칼리스타", "chromas": false}, {"id": "429005", "num": 5, "name": "습격자 칼리스타", "chromas": true}, {"id": "429014", "num": 14, "name": "요정 왕국 칼리스타", "chromas": true}, {"id": "429024", "num": 24, "name": "빛의 인도자 칼리스타", "chromas": true}], "lore": "칼리스타, 영원불멸하는 복수의 화신이자 분노에 찬 응징의 혼. 그림자 군도에서 소환되는 칼리스타는 배신자와 반역자들에겐 악몽이 그 자체로 갑옷을 두르고 현실화한 듯한 존재이다. 배신당한 이들이 피맺힌 외침으로 복수를 갈망할 때, 영혼도 기꺼이 내놓겠다는 자들에겐 칼리스타의 응답이 들린다. 그 분노의 표적이 된 자는 결코 무사할 수 없다. 칼리스타는 무자비한 추격자이며, 복수의 계약을 끝낼 수 있는 것은 영혼을 꿰뚫는 창날의 차가운 불꽃뿐이기 때문이다.", "blurb": "칼리스타, 영원불멸하는 복수의 화신이자 분노에 찬 응징의 혼. 그림자 군도에서 소환되는 칼리스타는 배신자와 반역자들에겐 악몽이 그 자체로 갑옷을 두르고 현실화한 듯한 존재이다. 배신당한 이들이 피맺힌 외침으로 복수를 갈망할 때, 영혼도 기꺼이 내놓겠다는 자들에겐 칼리스타의 응답이 들린다. 그 분노의 표적이 된 자는 결코 무사할 수 없다. 칼리스타는 무자비한 추격자이며, 복수의 계약을 끝낼 수 있는 것은 영혼을 꿰뚫는 창날의 차가운 불꽃뿐이기 때문이다.", "allytips": ["뽑아 찢기는 대상을 처치할 때 재사용 대기시간이 초기화되므로 최후의 일격으로 사용하면 좋습니다.", "전투 태세 명령을 발동하려고 이동 명령을 한 번 했을 때는 칼리스타의 기본 공격 대상이 취소되지 않습니다.", "기본 지속 효과에 의해 칼리스타의 이동 속도는 공격 속도에 비례하여 상승합니다."], "enemytips": ["칼리스타의 기동력은 공격에 의해 좌우됩니다. 즉, 공격 사거리 밖에 있을 때는 기동력이 낮으며, 공격 속도가 느려지면 교전 시 이동할 수 있는 범위가 줄어듭니다.", "칼리스타는 기본 공격 준비 동작을 취소할 수 없습니다. 기동력은 뛰어나지만, 이 때문에 공격을 언제 시작할지 제대로 짚으면 스킬을 적중시킬 수 있습니다.", "수풀 등을 활용해서 칼리스타의 시야에서 벗어나면 기본 공격이 빗나가, 창이 바닥으로 떨어집니다."], "tags": ["Marksman"], "partype": "마나", "info": {"attack": 8, "defense": 2, "magic": 4, "difficulty": 7}, "stats": {"hp": 560, "hpperlevel": 114, "mp": 300, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 6.3, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 4, "attackspeedperlevel": 4.5, "attackspeed": 0.694}, "spells": [{"id": "KalistaMysticShot", "name": "꿰뚫는 창", "description": "칼리스타가 빠르게 날아가는 창을 던집니다. 적이 처치될 경우 관통해 날아갑니다.", "tooltip": "칼리스타가 창을 던져 처음 적중한 대상에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 대상을 처치하면 창이 계속 뻗어나가 다음으로 적중한 대상에게 <spellName>뽑아 찢기</spellName>의 중첩을 적용합니다.<br /><br />칼리스타는 이 스킬을 사용한 후 <spellName>전투 태세</spellName> 효과로 도약할 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["소모값 @AbilityResourceName@", "피해량"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "KalistaMysticShot.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KalistaW", "name": "감시하는 혼", "description": "칼리스타와 계약을 맺은 아군이 같은 대상을 공격하면 추가 피해를 입힙니다. <br><br>사용 시 혼이 일정한 경로를 순찰하며 자기 앞의 지역을 드러냅니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 칼리스타와 <keywordMajor>계약자</keywordMajor>가 같은 대상을 기본 공격하면 칼리스타가 <magicDamage>최대 체력의 {{ maxhealthdamage*100 }}%에 해당하는 마법 피해</magicDamage>를 입힙니다. 대상 하나당 재사용 대기시간은 {{ pertargetcooldown }}초이며 챔피언이 아닌 대상에게는 최대 {{ maximummonsterdamage }}의 피해를 입힙니다.<br /><br /><spellPassive>사용 시: </spellPassive>칼리스타가 혼을 하나 보내 지정 영역을 정찰하게 합니다. 혼은 세 번 왕복하고 사라지며, 발각된 챔피언은 4초 동안 모습이 드러납니다. 충전 횟수는 2회이며 {{ ammorechargetooltip }}초마다 1회 충전됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["적 최대 체력 비례 피해량", "혼 충전 시간", "몬스터 대상 최대 피해량"], "effect": ["{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ ammorechargetooltip }} -> {{ ammorechargetooltipNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}"]}, "maxrank": 5, "cooldown": [30, 30, 30, 30, 30], "cooldownBurn": "30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 80, 80, 80, 80], [0, 0, 0, 0, 0], [125, 150, 175, 200, 225], [90, 80, 70, 60, 50], [0, 0, 0, 0, 0], [75, 100, 125, 150, 175], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80", "0", "125/150/175/200/225", "90/80/70/60/50", "0", "75/100/125/150/175", "10", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "2", "range": [5000, 5000, 5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "KalistaW.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "KalistaExpungeWrapper", "name": "뽑아 찢기", "description": "공격 시 대상을 창으로 꿰뚫습니다. 재시전하면 대상에 박힌 창을 뜯어내서 둔화시키며, 주는 피해량이 증가합니다.", "tooltip": "<spellPassive>기본 지속 효과: </spellPassive>칼리스타의 창은 대상의 몸에 4초 동안 유지되며 무제한으로 중첩됩니다.<br /><br /><spellActive>사용 시:</spellActive> 칼리스타가 근처 적에게 박힌 창을 뜯어내며 <physicalDamage>{{ normaldamage }}</physicalDamage>+두 번째 창부터 창 하나당 <physicalDamage>{{ additionaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 적중당한 적은 {{ slowduration }}초 동안 <attention>{{ totalslowamount }}</attention> <status>둔화</status>됩니다.<br /><br />이 스킬로 대상을 처치하면 재사용 대기시간이 초기화되고 <scaleMana>마나를 {{ manarefund }}</scaleMana> 돌려받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "중첩 당 피해량", "중첩 당 공격력 계수", "이동속도 감소량", "돌려받는 마나", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ additionalbasedamage }} -> {{ additionalbasedamageNL }}", "{{ additionaladratio*100.000000 }}% -> {{ additionaladrationl*100.000000 }}%", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ manarefund }} -> {{ manarefundNL }}", "{{ fakedcooldown }} -> {{ fakedcooldownNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "2", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaExpungeWrapper.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KalistaRx", "name": "운명의 부름", "description": "칼리스타가 계약을 맺은 아군을 자신에게로 순간 이동시킵니다. 계약자는 지정한 위치로 돌진하여 적 챔피언을 뒤로 밀어내는 스킬을 사용할 수 있습니다.", "tooltip": "칼리스타가 <keywordMajor>계약자</keywordMajor>를 옆으로 끌어와 최대 4초간 경직 상태로 만듭니다. <keywordMajor>계약자</keywordMajor>는 마우스를 클릭하여 지정한 위치로 날아갈 수 있습니다. 챔피언과 부딪치면 멈추며, 주변 적들을 <status>뒤로 밀어냅니다</status>. <keywordMajor>계약자</keywordMajor>는 챔피언과 부딪치면 최대 공격 사거리만큼 밀려납니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "띄워올리기 지속시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [1, 1.5, 2], [40, 60, 80], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "1/1.5/2", "40/60/80", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaRx.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "전투 태세", "description": "칼리스타가 기본 공격이나 꿰뚫는 창의 준비 동작을 하는 동안 이동 명령을 하면, 칼리스타가 기본 공격과 함께 해당 위치로 도약합니다.", "image": {"full": "Kalista_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}