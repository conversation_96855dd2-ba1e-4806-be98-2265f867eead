{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "스몰더", "title": "꼬마 불꽃용", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "천상비늘 스몰더", "chromas": true}], "lore": "녹서스 변경 부근의 깎아지른 절벽 지대에서 한 어린 용이 어미 용의 냉엄한 시선 아래 카마보르 황실의 용 혈통을 이을 후계자 수업을 받고 있다. 천성 자체가 쾌활한 스몰더는 하루라도 빨리 어른이 될 날을 꿈꾸면서, 오늘도 몰라보게 성장하는 제 능력을 시험할 구실을 찾아다니는 중이다. 아직 좀 어리긴 하지만, 스몰더는 어떤 것이든지 불에 타기만 한다면 가볍게 태워버릴 수 있는 능력을 지니고 있다.", "blurb": "녹서스 변경 부근의 깎아지른 절벽 지대에서 한 어린 용이 어미 용의 냉엄한 시선 아래 카마보르 황실의 용 혈통을 이을 후계자 수업을 받고 있다. 천성 자체가 쾌활한 스몰더는 하루라도 빨리 어른이 될 날을 꿈꾸면서, 오늘도 몰라보게 성장하는 제 능력을 시험할 구실을 찾아다니는 중이다. 아직 좀 어리긴 하지만, 스몰더는 어떤 것이든지 불에 타기만 한다면 가볍게 태워버릴 수 있는 능력을 지니고 있다.", "allytips": ["스몰더는 게임 초반에 취약한 편입니다. 기본 지속 효과 중첩을 쌓으며 살아남는 데 집중하세요. 그러다 보면 어느새 강력한 용이 되어 있을 것입니다!", "스몰더는 아군의 보호에 의존합니다. 적에게 맞서는 데 힘을 보태줄 아군과 함께 행동하세요.", "스몰더는 적에게 막대한 광역 피해를 입힐 수 있습니다. 적이 뭉쳐있을 때를 노려 공격하세요."], "enemytips": ["스몰더는 아군의 보호에 의존합니다. 주변에 지켜줄 동료가 없을 때를 노려 공격하세요.", "스몰더가 앞에 있을 땐 뭉치지 않도록 조심하세요!", "스몰더는 게임 초반에 굉장히 취약합니다. 스몰더가 용으로 성장하기 전에 이 약점을 노려 이득을 얻으세요!", "스몰더의 비행은 강력한 군중 제어 스킬로 방해할 수 있으며, 둔화에 영향을 받습니다."], "tags": ["Marksman", "Mage"], "partype": "마나", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "초강력 화염 숨결", "description": "스몰더가 적에게 불을 내뿜습니다. 이 스킬은 스몰더가 중첩을 쌓을수록 더 강력해집니다.", "tooltip": "스몰더가 불꽃을 내뿜어 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>+<magicDamage>{{ spell.smolderp:passive_qdamageincrease }}의 마법 피해</magicDamage>를 입힙니다. 대상이 사망하면 스몰더가 스킬 사용 한 번당 <scaleMana>{{ manarestore }}의 마나</scaleMana>를 돌려받습니다.<br /><br />이 스킬은 <spellName>용 훈련</spellName> 중첩 수에 따라 진화하며 다음과 같은 효과를 얻습니다.<li><keywordMajor>{{ stacktier1 }}회 중첩</keywordMajor>: 대상 주변의 모든 적에게 피해를 입힙니다.<li><keywordMajor>{{ stacktier2 }}회 중첩</keywordMajor>: 이 스킬 피해량의 {{ tier2_blowbackpercentagedamage }}%만큼 피해를 입히는 폭발 <spellName>{{ tier2_numberofblowback }}</spellName>개를 대상 너머로 날립니다.<li><keywordMajor>{{ stacktier3 }}회 중첩</keywordMajor>: 대상을 불태우며 {{ tier3_dotlength }}초에 걸쳐 <trueDamage>최대 체력의 {{ tier3_burn }}에 해당하는 고정 피해</trueDamage>를 입힙니다. 불타는 동안 총 체력이 <trueDamage>{{ tier3_executethreshold }}</trueDamage> 밑으로 떨어지는 적 챔피언은 즉시 처치됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SmolderW", "name": "에취!", "description": "스몰더가 적 챔피언에게 적중 시 폭발하는 앙증맞은 불꽃 재채기를 내뿜습니다.", "tooltip": "스몰더가 앙증맞은 불꽃 재채기를 내뿜어 <physicalDamage>{{ initialdamage }}의 물리 피해</physicalDamage>를 입히고 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>시킵니다.<br /><br />챔피언에게 적중하면 폭발이 일어나며 <physicalDamage>{{ explosiondamage }}의 물리 피해</physicalDamage>+<magicDamage>{{ spell.smolderp:passive_wdamageincrease }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "폭발 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SmolderE", "name": "펄럭펄럭", "description": "스몰더가 지형을 무시하고 날며 체력이 가장 낮은 적을 폭격합니다.", "tooltip": "스몰더가 하늘을 날며 {{ duration }}초 동안 <speed>이동 속도가 {{ movespeed*100 }}%</speed> 증가하고 지형을 무시합니다.<br /><br />스몰더는 비행하는 동안 체력이 가장 낮은 적을 <spellName>{{ totalnumberofattacks }}</spellName>(내림 적용)회 폭격해 공격 적중 시 <physicalDamage>{{ damageperhit }}의 물리 피해</physicalDamage>+<magicDamage>{{ spell.smolderp:ebonusdamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "기본 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SmolderR", "name": "엄마아아아!", "description": "스몰더의 울음에 어미가 나타나 위에서 불을 내뿜어 추가 피해를 입히고 화염 중심에 있는 적을 둔화시킵니다.", "tooltip": "스몰더의 어미가 위에서 불을 내뿜어 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 중심에 있는 적은 그 대신 <physicalDamage>{{ tooltiponly_totalsweetspotdamage }}의 물리 피해</physicalDamage>를 입고 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>됩니다.<br /><br />스몰더의 어미가 스몰더를 맞히면 스몰더가 <healing>체력을 {{ momhealcalc }}</healing> 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "회복", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "용 훈련", "description": "스킬로 챔피언을 맞히고 초강력 화염 숨결 스킬로 적을 처치하면 용 훈련 중첩을 1회 얻습니다. 중첩이 쌓이면 스몰더 기본 스킬의 피해량이 증가합니다.", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}