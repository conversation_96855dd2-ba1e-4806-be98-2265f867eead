{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ornn": {"id": "<PERSON><PERSON>", "key": "516", "name": "<PERSON><PERSON>", "title": "der Gott der Vulkanschmiede", "image": {"full": "Ornn.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "516000", "num": 0, "name": "default", "chromas": false}, {"id": "516001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "516002", "num": 2, "name": "Ahnenholz-Ornn", "chromas": true}, {"id": "516011", "num": 11, "name": "Weltraum-Groove-Ornn", "chromas": true}, {"id": "516020", "num": 20, "name": "Tschu-Tschu-Ornn", "chromas": true}], "lore": "Ornn stammt aus Freljord und ist der Geist der Schmiede- und Handwerkskunst. Er werkelt zurückgezogen in einer gewaltigen Schmiede, die er in die Lavahöhlen unter einem Vulkan geschlagen hat, der als die „Wiege des Feuers“ bekannt ist. Dort rührt er in blubbernden Kesseln geschmolzene Felsen, veredelt Erze und schmiedet Kunstwerke von unübertroffener Qualität. Wann immer andere Götter – besonders Volibear – auf der Erde umherwandern und sich in die Angelegenheiten der Menschen einmischen, steigt Ornn empor, um diese ungestümen Wesen daran zu erinnern, wo ihr Platz ist – entweder mithilfe seines treuen Hammers oder mit der flammenden Kraft der Berge.", "blurb": "Ornn stammt aus Freljord und ist der Geist der Schmiede- und Handwerkskunst. Er werkelt zurückgezogen in einer gewaltigen Schmiede, die er in die Lavahöhlen unter einem Vulkan geschlagen hat, der als die „Wiege des Feuers“ bekannt ist. Dort rührt er in...", "allytips": ["Wenn du die Herstellungspfade von Gegenständen lernst, kannst du dich schnell für eine Aufwertung in der Lane entscheiden.", "<PERSON>t „Magmaspalte“ lassen sich Bereiche erzeugen, in denen du die Gegner in Schach halten kannst.", "Die Reihenfolge deiner Fähigkeiten spielt eine Rolle! Versuche, deine Gegner zur richtigen Zeit „mürbe“ zu machen."], "enemytips": ["<PERSON><PERSON><PERSON>, dich von <PERSON>n fernzu<PERSON>en. Ornn verliert an Stärke, wenn er dich nicht betäuben kann.", "Du kannst Ornn an der Herstellung von Gegenständen in der Lane hindern, indem du ihn fortlaufend angreifst."], "tags": ["Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 660, "hpperlevel": 109, "mp": 341, "mpperlevel": 65, "movespeed": 335, "armor": 33, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "OrnnQ", "name": "Magmaspalte", "description": "Ornn schlägt auf den Boden und lässt einen Riss entstehen, der Schaden verursacht und getroffene Gegner verlangsamt. Nach einer kurzen Verzögerung entsteht am Zielort eine Magmasäule.", "tooltip": "Ornn schlägt auf den Boden und erzeugt einen <PERSON>iss, der <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und Gegner {{ e6 }}&nbsp;Sekunden lang um {{ e5 }}&nbsp;% <status>verlangsamt</status>. Am Ende des Risses formt sich {{ e3 }}&nbsp;Sekunden lang eine Felssäule. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [20, 45, 70, 95, 120], [1, 1, 1, 1, 1], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/45/70/95/120", "1", "4", "0", "40", "2", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "OrnnQ.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrnnW", "name": "<PERSON><PERSON>bal<PERSON>", "description": "Ornn bewegt sich vorwärts und spuckt Feuer. <PERSON><PERSON><PERSON>, die vom letzten Flammenstoß getroffen werden, werden mürbe gemacht.", "tooltip": "Ornn stampft unaufhaltsam geradeaus und spuckt dabei <PERSON>uer, wobei er {{ breathduration }}&nbsp;Sekunden lang <magicDamage>magischen Schaden</magicDamage> in Hö<PERSON> von {{ maxpercenthpperticktooltip }}&nbsp;% des fehlenden Lebens verursacht. <PERSON><PERSON><PERSON>, die vom letzten Flammenstoß getroffen werden, werden {{ brittleduration }}&nbsp;Sekunden lang <keywordMajor>mürbe</keywordMajor>.<br /><br /><status>Bewegungsunfähig</status> machende Effekte, die auf <keywordMajor>mürbe</keywordMajor> Ziele einwirken, halten 30&nbsp;% länger an und verursachen zusätzlich <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ brittlepercentmaxhpcalc }} des maximalen Lebens. Ornns Angriffe gegen <keywordMajor>mürbe</keywordMajor> Ziel<PERSON> <status>stoßen</status> diese <status>zurück</status> und fügen ihnen zusätzlichen Schaden zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden in Höhe eines Prozentsatzes des Lebens", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ maxpercenthpperticktooltip }}&nbsp;% -> {{ maxpercenthpperticktooltipNL }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "OrnnW.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrnnE", "name": "Sengender Ansturm", "description": "Ornn stürmt nach vorne und verursacht Schaden an allen <PERSON>, die er dabei überrennt. Wenn Ornn mit Terrain kollidiert, erzeugt der Aufprall eine Sc<PERSON>ckwelle um ihn herum, die Schaden verursacht und Gegner in die Luft schleudert.", "tooltip": "Ornn stürmt los und verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>. Wenn Ornn mit Terrain zusammenstöß<PERSON>, erzeugt er eine <PERSON>welle, die Gegner {{ knockupduration }}&nbsp;Sekunden lang <status>hochschleudert</status> und allen, die nicht vom Ansturm getroffen wurden, denselben Schaden zufügt.<br /><br />Ornns Ansturm zerstört Magmasäulen und Terrain, das von Gegnern erschaffen wurde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Sprintschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "OrnnE.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrnnR", "name": "<PERSON><PERSON> Schmiedefeuers", "description": "Ornn beschwört einen riesigen Elementar an einem Zielort, der sich daraufhin mit zunehmender Geschwindigkeit auf ihn zubewegt. <PERSON><PERSON><PERSON>, die vom Elementar überrannt werden, erle<PERSON> Schaden, werden verlangsamt und zusätzlich mürbe gemacht. Ornn kann die Fähigkeit erneut einsetzen, um zum Elementar zu stürmen und ihn in die entgegengesetzte Richtung umzuleiten. <PERSON><PERSON> Gegner, die der Elementar daraufhin trifft, werden in die Luft geschleudert, erleiden denselben Schaden und werden erneut mürbe gemacht.", "tooltip": "Ornn beschwört einen riesigen Feuerelementar, der auf ihn zustürmt und <magicDamage>{{ rdamagecalc }}&nbsp;magischen Schaden</magicDamage> verursacht. Getroffene Gegner werden außerdem <keywordMajor>mürbe</keywordMajor> gemacht und {{ brittledurationtooltiponly }}&nbsp;Sekunde(n) lang um {{ rslowpercentbasepremath }}&nbsp;% <status>verlangsamt</status>.<br /><br />Ornn kann die Fähigkeit <recast>reaktivieren</recast>, um mit einem Kopfstoß nach vorn zu springen. Wenn er dabei den Elementar trifft, ändert er dessen Richtung und verstärkt ihn, wodurch er den ersten Champion {{ rstunduration }}&nbsp;Sekunde(n) und alle weiteren Champions {{ minstun }}&nbsp;Sekunde(n) lang <status>hochschleudert</status>. Der Elementar verursacht dabei <magicDamage>{{ rdamagecalc }}&nbsp;magischen Schaden</magicDamage> und macht Gegner erneut <keywordMajor>mürbe</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rslowpercentbasepremath }} -> {{ rslowpercentbasepremathNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "OrnnR.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Leben<PERSON>hm<PERSON>e", "description": "Ornn erhält zusätzliche Rüstung und Magieresistenz aus allen Quellen.<br><br>Ornn kann Gold ausgeben und überall auf der Karte Gegenstände schmieden. Verbrauchsgegenstände sind davon ausgenommen.<br><br><PERSON><PERSON><PERSON><PERSON><PERSON> kann Ornn für sich selbst und seine Verbündeten Meisterwerke herstellen.", "image": {"full": "OrnnP.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}