{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ekko": {"id": "<PERSON><PERSON><PERSON>", "key": "245", "name": "<PERSON><PERSON><PERSON>", "title": "the Boy Who Shattered Time", "image": {"full": "Ekko.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "245000", "num": 0, "name": "default", "chromas": false}, {"id": "245001", "num": 1, "name": "Sandstorm Ekko", "chromas": true}, {"id": "245002", "num": 2, "name": "Academy Ekko", "chromas": false}, {"id": "245003", "num": 3, "name": "PROJECT: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "245011", "num": 11, "name": "SKT T1 Ekko", "chromas": false}, {"id": "245012", "num": 12, "name": "Trick or Treat <PERSON>", "chromas": true}, {"id": "245019", "num": 19, "name": "True Damage Ekko", "chromas": true}, {"id": "245028", "num": 28, "name": "Pulsefire Ekko", "chromas": true}, {"id": "245036", "num": 36, "name": "Arcane Firelight Ekko", "chromas": true}, {"id": "245045", "num": 45, "name": "Star Guardian Ekko", "chromas": true}, {"id": "245046", "num": 46, "name": "Prestige Star Guardian Ekko", "chromas": false}, {"id": "245056", "num": 56, "name": "Breakout True Damage Ekko", "chromas": false}, {"id": "245057", "num": 57, "name": "Arcane Last Stand Ekko", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, si anak ajaib dari jalanan <PERSON>aun yang keras, yang bisa memanipulasi waktu untuk mengubah situasi apa pun demi menguntungkan dirinya. Dia menggunakan penemuannya sendiri, Z-Drive, untuk menjelajahi kemungkinan realitas, menciptakan momen sempurna untuk melakukan hal yang mustahil pertama kalinya, setiap saat. Meski Ekko menikmati kebebasan ini, jika ada temannya terancam, dia dan Firelight akan melakukan apa pun untuk melindungi mereka.", "blurb": "<PERSON><PERSON><PERSON>, si anak ajaib dari jalanan <PERSON>aun yang keras, yang bisa memanipulasi waktu untuk mengubah situasi apa pun demi menguntungkan dirinya. Dia menggunakan penemuannya sendiri, Z-Drive, untuk menjelajahi kemungkinan realitas, menciptakan momen sempurna...", "allytips": ["Chronobreak adalah alat kabur yang efektif, tetapi juga bisa sangat kuat apabila digunakan untuk menyerang. Jangan remehkan potensi damage-nya.", "<PERSON>ka bisa mengaktifkan Z-Drive Resonance pada champion mus<PERSON>, ambil risiko untuk melakukannya. Bonus Move Speed membuatmu mudah kabur.", "Dash dari Phase Dive sangat bagus untuk menyiapkan ability <PERSON><PERSON><PERSON> la<PERSON>. Gunakan untuk melakukan double hit den<PERSON> Timewinder atau posisi yang tepat untuk meledakkan Parallel Convergence."], "enemytips": ["<PERSON><PERSON><PERSON> sangat lemah saat ultimanya belum siap digunakan. Perhatikan jejak yang dia tinggalkan untuk melihat apakah Chronobreak sudah tersedia.", "Stun zona Ekko butuh 3 detik untuk aktif. <PERSON><PERSON> gambar yang dia buat saat cast dan coba tebak posisi zona itu.", "<PERSON><PERSON><PERSON> kedua <PERSON><PERSON><PERSON> le<PERSON><PERSON> be<PERSON>, jadi hindari saja."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 280, "mpperlevel": 70, "movespeed": 340, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.688}, "spells": [{"id": "EkkoQ", "name": "Timewinder", "description": "Ekko melempar granat temporal yang meluas menjadi bidang distorsi waktu saat mengenai champion musuh, menerapkan slow dan memberi damage pada siapa pun yang terperangkap di dalamnya. Granat akan rewind ke Ek<PERSON> setelah beberapa saat, <PERSON><PERSON><PERSON><PERSON><PERSON> damage saat kembali.", "tooltip": "<PERSON><PERSON><PERSON> melem<PERSON>an perangkat, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ initialdamage }} magic damage</magicDamage>. Saat mengenai champion atau mencapai akhir range serangan, perangkat akan meluas menjadi area yang menerapkan <status>Slow</status> pada musuh di dalamnya sebesar {{ e2 }}%. <PERSON><PERSON><PERSON> meluas, <PERSON>k<PERSON> me-recall perangkat, men<PERSON><PERSON><PERSON>an <magicDamage>{{ recalldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage <PERSON>ar", "Slow", "Damage Kembali"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ outgoingdamage }}-> {{ outgoingdamageNL }}", "{{ effect2amount*-100.000000 }}%-> {{ effect2amountnl*-100.000000 }}%", "{{ returndamage }}-> {{ returndamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 85, 100, 115, 130], [40, 45, 50, 55, 60], [40, 65, 90, 115, 140], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [165, 165, 165, 165, 165], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/85/100/115/130", "40/45/50/55/60", "40/65/90/115/140", "100", "0", "165", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EkkoQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoW", "name": "Parallel Convergence", "description": "Basic attack <PERSON><PERSON><PERSON> magic damage bonus pada musuh dengan health rendah. Dia bisa cast Parallel Convergence untuk membelah timeline, menciptakan anomali setelah beberapa detik, menerapkan slow pada musuh yang terjebak di dalamnya. Jika Ekko memasuki anomali tersebut, dia mendapatkan shield dan menerapkan stun pada musuh dengan menghentikan mereka tepat waktu.", "tooltip": "<spellPassive>Pasif:</spellPassive> Serangan Ekko terhadap musuh dengan Health di bawah 30% menghasilkan <magicDamage>{{ missinghealthpercent }} magic damage dari Health yang hilang</magicDamage>.<br /><br /><spellActive>Aktif:</spellActive> <PERSON><PERSON><PERSON> beberapa saat, <PERSON>kko meluncurkan kubah waktu selama 1,5 detik yang menerapkan <status>Slow</status> ke musuh di dalamnya sebesar {{ e0 }}%. Jika Ekko memasuki kubah dan meledakkannya, menerapkan <status>Stun</status> selama {{ e2 }} detik dan mendapatkan <shield>{{ totalshield }} Shield</shield>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Jumlah Shield", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e4 }}-> {{ e4NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [375, 375, 375, 375, 375], [2.25, 2.25, 2.25, 2.25, 2.25], [3, 3, 3, 3, 3], [100, 120, 140, 160, 180], [150, 150, 150, 150, 150], [15, 15, 15, 15, 15], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [40, 40, 40, 40, 40]], "effectBurn": [null, "375", "2.25", "3", "100/120/140/160/180", "150", "15", "1.5", "3", "2", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1600, 1600, 1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "EkkoW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoE", "name": "Phase Dive", "description": "Ekko me<PERSON> ke satu arah dan mengaktifkan Z-Drive. Serangan berikutnya akan menghasilkan damage bonus dan melakukan warp realitas, membuatnya teleport ke posisi target.", "tooltip": "<PERSON><PERSON><PERSON> me<PERSON> dash dan memperkuat Serangan berikutnya untuk mendapatkan range bonus, teleport ke target, dan men<PERSON><PERSON><PERSON> tambahan <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [350, 350, 350, 350, 350], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "350", "3", "300", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "EkkoE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoR", "name": "Chronobreak", "description": "<PERSON><PERSON><PERSON> timeline, menjadi tak bisa ditarget dan melakukan rewind ke titik waktu yang lebih menguntungkan. Dia kembali ke mana pun dia berada beberapa detik yang lalu, dan menerima heal untuk sebagian damage yang diterima selama durasi. <PERSON><PERSON><PERSON> di dekat zona awal akan menerima damage luar biasa.", "tooltip": "<PERSON><PERSON><PERSON> memutar kembali waktu, memasuki Stasis sembari melakukan teleport ke tempatnya 4 detik yang lalu, dan men<PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> ke musuh di sekitar. <PERSON><PERSON> itu, <PERSON><PERSON><PERSON> juga memulihkan <healing>{{ totalbaseheal }} Health</healing>, meningkat sebesar {{ percenthealampperpercentmissinghealth }}% untuk tiap 1% Health yang hilang selama 4 detik terakhir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Heal", "Cooldown"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ flatheal }}-> {{ flathealNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 80, 50], "cooldownBurn": "110/80/50", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850], "rangeBurn": "850", "image": {"full": "EkkoR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Z-Drive Resonance", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> atau spell ketiga yang menghasilkan damage pada target yang sama akan menghasilkan magic damage bonus, dan memberi <PERSON><PERSON><PERSON> tambahan kecepatan jika target ad<PERSON><PERSON> champion.<br><br>", "image": {"full": "Ekko_P.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}