{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "Alistar", "title": "Minotaure", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "Alistar noir", "chromas": false}, {"id": "12002", "num": 2, "name": "Alistar doré", "chromas": false}, {"id": "12003", "num": 3, "name": "Alistar matador", "chromas": false}, {"id": "12004", "num": 4, "name": "Alistar longhorn", "chromas": false}, {"id": "12005", "num": 5, "name": "Alistar déchaî<PERSON>", "chromas": false}, {"id": "12006", "num": 6, "name": "Alistar infernal", "chromas": false}, {"id": "12007", "num": 7, "name": "Alistar libéro", "chromas": false}, {"id": "12008", "num": 8, "name": "Alistar maraudeur", "chromas": false}, {"id": "12009", "num": 9, "name": "SKT T1 Alistar", "chromas": false}, {"id": "12010", "num": 10, "name": "<PERSON>star meuh-meuh", "chromas": true}, {"id": "12019", "num": 19, "name": "Alistar Hextech", "chromas": false}, {"id": "12020", "num": 20, "name": "Alistar conquérant", "chromas": true}, {"id": "12022", "num": 22, "name": "Alistar des glaces noires", "chromas": true}, {"id": "12029", "num": 29, "name": "Alistar bête lunaire", "chromas": true}], "lore": "Alistar est un guerrier redoutable cherchant à se venger de l'empire noxien qui a détruit son clan. Bien qu'il ait été réduit en esclavage et forcé de vivre une vie de gladiateur, sa volonté de fer lui a permis de ne pas succomber à la folie bestiale qui le menaçait. Maintenant qu'il a retrouvé la liberté, il combat au nom des faibles et des opprimés. Ses seules armes sont ses cornes, ses sabots et ses poings.", "blurb": "Alistar est un guerrier redoutable cherchant à se venger de l'empire noxien qui a détruit son clan. Bien qu'il ait été réduit en esclavage et forcé de vivre une vie de gladiateur, sa volonté de fer lui a permis de ne pas succomber à la folie bestiale...", "allytips": ["Atomisation peut vous aider à mieux vous placer pour un Coup de tête.", "La vitesse de déplacement est très importante pour Alistar. Achetez les bonnes bottes.", "Saut éclair vous permet de surprendre votre cible ; repoussez-la ensuite vers vos alliés avec Atomisation et Coup de tête."], "enemytips": ["Alistar peut être dangereux, mais il est solide et il est préférable de l'ignorer au profit de cibles plus fragiles.", "Attention à la combinaison Atomisation-Coup de tête à proximité des tourelles.", "Quand Alistar utilise son ultime, il est préférable de s'éloigner et d'attendre que l'effet se dissipe avant de l'attaquer."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "Atomisation", "description": "Alistar frappe le sol, infligeant des dégâts magiques aux ennemis proches et les projetant dans les airs.", "tooltip": "Alistar frappe le sol, <status>projetant dans les airs</status> les ennemis pendant {{ knockupduration }} sec et infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Headbutt", "name": "<PERSON><PERSON> <PERSON>", "description": "Alistar charge une cible, lui inflige des dégâts et la fait tomber à la renverse.", "tooltip": "Alistar fonce dans un ennemi, le <status>repoussant</status> et lui infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AlistarE", "name": "Piétinement", "description": "Alistar piétine les unités ennemies proches, ignorant les collisions avec les unités et gagnant un effet s'il blesse un champion ennemi. Au maximum d'effets cumulés, la prochaine attaque de base d'Alistar contre un champion ennemi infligera des dégâts magiques supplémentaires et étourdira la cible.", "tooltip": "Alistar piétine le sol, devient fantomatique et inflige <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage> aux ennemis proches en {{ e3 }} sec. Chaque pulsation qui blesse au moins un champion octroie un effet.<br /><br />À {{ e5 }} effets cumulés, la prochaine attaque d'Alistar contre un champion <status>étourdit</status> la cible pendant {{ e6 }} sec et inflige <magicDamage>{{ attackbonusdamage }} pts de dégâts magiques</magicDamage> supplémentaires.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FerociousHowl", "name": "<PERSON>ont<PERSON>er", "description": "Alistar lâche un puissant rugissement, dissipant tous les effets de contrôle de foule qui l'affectent et réduisant les dégâts physiques et magiques subis pendant la durée d'effet.", "tooltip": "Alistar purge immédiatement toutes les <status>entraves</status> qui l'affectent et réduit les dégâts qu'il subit de {{ rdamagereduction }}% pendant {{ rduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Réduction des dégâts"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamagereduction }}% -> {{ rdamagereductionNL }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Alistar charge son cri en étourdissant ou en déplaçant des champions ennemis ou quand des ennemis proches meurent. Quand le cri est pleinement chargé, Alistar se soigne et soigne tous les champions alliés proches.", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}