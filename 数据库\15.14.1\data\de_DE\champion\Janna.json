{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Janna": {"id": "<PERSON><PERSON>", "key": "40", "name": "<PERSON><PERSON>", "title": "die Wut des Sturms", "image": {"full": "Janna.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "40000", "num": 0, "name": "default", "chromas": false}, {"id": "40001", "num": 1, "name": "Or<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "40002", "num": 2, "name": "Hextech-Janna", "chromas": false}, {"id": "40003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "40004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "40005", "num": 5, "name": "Wetterfee-<PERSON><PERSON>", "chromas": false}, {"id": "40006", "num": 6, "name": "Fnatic-<PERSON><PERSON>", "chromas": false}, {"id": "40007", "num": 7, "name": "Sternenwächt<PERSON><PERSON>", "chromas": false}, {"id": "40008", "num": 8, "name": "Heiliges Schwert Janna", "chromas": true}, {"id": "40013", "num": 13, "name": "Hexerei<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "40020", "num": 20, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "40027", "num": 27, "name": "Kriegerköni<PERSON>", "chromas": true}, {"id": "40036", "num": 36, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "40045", "num": 45, "name": "Cyber-Halo<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "40046", "num": 46, "name": "Cyber-Hal<PERSON><PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "40056", "num": 56, "name": "Himmelsschuppen-<PERSON><PERSON>", "chromas": true}, {"id": "40066", "num": 66, "name": "Rächende Dämmerung Janna", "chromas": false}], "lore": "<PERSON><PERSON> hat die mächtigen Winde Runeterras auf ihrer Seite, ist ein mysteriöser Windgeist und die Beschützerin der Armen von Z<PERSON>un. <PERSON><PERSON> glauben, dass sie von den Seemännern Runeterras ins Leben gerufen wurde, die um günstigen Wind baten, als sie durch tückische Gewässer navigieren und rauen Stürmen trotzen mussten. <PERSON><PERSON><PERSON> und ihr Schutz werden von vielen in den Tiefen von <PERSON>, wo <PERSON><PERSON> zu einem strahlenden Licht der Hoffnung geworden ist. <PERSON><PERSON><PERSON> weiß genau, wo oder wann sie erscheint, aber in den meisten Fällen will sie helfen.", "blurb": "<PERSON><PERSON> hat die mächtigen Winde Runeterras auf ihrer Seite, ist ein mysteriöser Windgeist und die Beschützerin der Armen von Z<PERSON>un. <PERSON><PERSON> glauben, dass sie von den Seemännern Runeterras ins Leben gerufen wurde, die um günstigen Wind baten, als sie durch...", "allytips": ["Das „Auge des Sturms“ kann auf verbündete Türme angewendet werden.", "Die Verwendung von „Heulender Sturm“, ohne vollständige Aufladung, kann benutzt werden, um das gegnerische Team auszuschalten.", "Der Einsatz von Jannas ultimativer Fähigkeit kann Gegner von einem verwundeten Verbündeten wegstoßen oder sogar G<PERSON>ner voneinander trennen."], "enemytips": ["Falls Janna ihre ultimative Fähigkeit einsetzt, kannst du sie mit einer unterbrechenden Fähigkeit stören.", "Achte auf das Geräusch von „Heulender Sturm“, falls <PERSON><PERSON> ve<PERSON>, dich aus dem Nebel des Krieges oder aus einem Gebüsch heraus zu treffen.", "<PERSON><PERSON> ist am stärksten, wenn sie anderen Verbündeten Buffs gewährt. Wenn du ihre Verbündeten drangsalierst, kann sie dich nicht mehr so gut angreifen."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 50, "movespeed": 325, "armor": 28, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON>G<PERSON>", "name": "<PERSON><PERSON><PERSON> Sturm", "description": "Indem Jan<PERSON> und Temperatur an einem Ort ä<PERSON>t, kann sie einen kleinen Sturm erzeugen, der mit der Zeit an Größe zunimmt. Wenn sie den Zauber dann noch einmal aktiviert, setzt sie den Sturm frei. <PERSON><PERSON> fliegt der Sturm in die Richtung, in die er gewirkt wurde. Dabei schleudert er alle Gegner in seinem Weg in die Luft und fügt ihnen Schaden zu.", "tooltip": "<PERSON><PERSON> be<PERSON>rt einen Tornado, der sich über {{ maxduration }}&nbsp;Sekunden hinweg aufbaut und sich dann in Zielrichtung bewegt. Er verursacht <magicDamage>{{ minimumdamage }}–{{ maxdamage }}&nbsp;magischen Schaden</magicDamage> und getroffene Ziele werden {{ baseknockup }}–{{ maxknockup }}&nbsp;Sekunden lang <status>hochgeschleudert</status>. Die Reichweite, der Schaden und die Dauer des <status>Hochschleuderns</status> erhöhen sich, je länger sich der Tornado aufbauen konnte. <PERSON><PERSON> kann diese Fähigkeit <recast>reaktivieren</recast>, um den Tornado vorzeitig auszusenden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden pro aufgebauter Sekunde", "Kosten (@AbilityResourceName@)"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "HowlingGale.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SowTheWind", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> be<PERSON> einen Luftelementar, der ihr Lauftempo passiv erhöht und es ihr ermöglicht, sich durch Einheiten hindurchzubewegen. Sie kann diese Fähigkeit auch aktivieren, um Schaden zu verursachen und das Lauftempo von Gegnern zu verringern.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON><PERSON> <speed>{{ totalms }}&nbsp;Lauftempo</speed> und „Geist“.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON>nas Elementar attackiert e<PERSON> G<PERSON>, <status>verlangsamt</status> ihn {{ slowduration }}&nbsp;Sekunden lang um {{ totalslow }} und fügt ihm <magicDamage>{{ totaldamage }} + {{ spell.tailwindself:bonusdamage }}&nbsp;magischen <PERSON>haden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Passives Lauftempo", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent }}&nbsp;% -> {{ slowpercentNL }}&nbsp;%", "{{ mspercent*100.000000 }}&nbsp;% -> {{ mspercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4294967295, 4294967295, 4294967295, 4294967295, 4294967295], "rangeBurn": "4294967295", "image": {"full": "SowTheWind.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EyeOfTheStorm", "name": "Auge des Sturms", "description": "<PERSON><PERSON> eine defensive Bö<PERSON> herau<PERSON>, die einen verbündeten Champion oder Turm vor Schaden schützt und dessen Angriffsschaden erhöht.", "tooltip": "<PERSON><PERSON> gew<PERSON>hrt einem verbündeten Champion oder Turm {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON><PERSON><PERSON></shield> in <PERSON><PERSON><PERSON> von {{ totalshield }}. Während der Schild aktiv ist, gewährt er <scaleAD>{{ totalad }}&nbsp;Angriffsschaden</scaleAD>.<br /><br /><PERSON><PERSON> er<PERSON> {{ ecdrefundforcc*100 }}&nbsp;% der Abklingzeit zurück, wenn sie mit einer Fähigkeit die Bewegungen eines gegnerischen Champions einschränkt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Angriffsschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ bonusad }} -> {{ bonusadNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EyeOfTheStorm.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ReapTheWhirlwind", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> umgibt sich mit einem magischen Sturm, der Gegner zurückwirft. Nach dem Abklingen des Sturms werden Verbündete in der Nähe durch lindernde Winde geheilt, solange die Fähigkeit aktiv ist.", "tooltip": "<PERSON><PERSON> be<PERSON>rt einen magischen Monsun, der Gegner in der Nähe <status>zurückstößt</status> und danach Verbündete in der Nähe im Verlauf von {{ e3 }}&nbsp;Sekunden um <healing>{{ totalheal }}&nbsp;<PERSON><PERSON></healing> heilt. Der Monsun endet vorzeitig, wenn sich Janna bewegt oder eine Fähigkeit einsetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilen pro Sekunde", "Abklingzeit"], "effect": ["{{ healbasepersecond }} -> {{ healbasepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [300, 450, 600], [3, 3, 3], [700, 700, 700], [875, 875, 875], [875, 875, 875], [1200, 1200, 1200], [10, 10, 10], [0.5, 0.5, 0.5], [0, 0, 0]], "effectBurn": [null, "0", "300/450/600", "3", "700", "875", "875", "1200", "10", "0.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725], "rangeBurn": "725", "image": {"full": "ReapTheWhirlwind.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Rückenwind", "description": "<PERSON><PERSON>te erhalten Lauftempo, wenn sie sich auf sie zu bewegen.<br><br><PERSON><PERSON> ve<PERSON> bei <PERSON> und mit „Zephir“ zusätzlichen magischen Schaden in Höhe eines Teils des zusätzlichen Lauftempos.", "image": {"full": "Janna_Tailwind.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}