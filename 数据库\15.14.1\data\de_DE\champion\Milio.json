{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Milio": {"id": "<PERSON><PERSON>", "key": "902", "name": "<PERSON><PERSON>", "title": "Die schützende Flamme", "image": {"full": "Milio.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "902000", "num": 0, "name": "default", "chromas": false}, {"id": "902001", "num": 1, "name": "Feenhof-Milio", "chromas": true}, {"id": "902011", "num": 11, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> ist ein warmherziger Junge aus Ixtal, der trotz seines jungen Alters das Feueraxiom gemeistert und etwas Neues entdeckt hat: beruhi<PERSON><PERSON> Feuer. Mit seiner neuen Kraft will <PERSON><PERSON> seiner Familie da<PERSON> helfen, aus der Verbannung zurückzukehren, indem er sich den Yun Tal anschließt – genauso wie seine Großmutter damals. Nachdem er durch die Dschungel von Ixtal bis zur Hauptstadt Ixaocan gereist ist, bereitet sich Milio nun darauf vor, sich dem Vidalion zu stellen und den Yun Tal anzuschließen, ohne sich der ihm bevorstehenden Prüfungen – und Gefahren – bewusst zu sein.", "blurb": "<PERSON><PERSON> ist ein warmherziger Junge aus Ixtal, der trotz seines jungen Alters das Feueraxiom gemeistert und etwas Neues entdeckt hat: beru<PERSON><PERSON><PERSON> Feuer. Mit seiner neuen Kraft will <PERSON><PERSON> seiner Familie <PERSON> he<PERSON>, aus der Verbannung zurückzukehren...", "allytips": ["<PERSON><PERSON> bra<PERSON> Verbündete in seiner Nähe, um das Beste aus seinen Fähigkeiten herauszuholen.", "<PERSON><PERSON><PERSON> erhöht die Geschwindigkeit seiner Sprünge. Du kannst die zusätzliche Geschwindigkeit nutzen, um den Gegner zu überraschen!", "<PERSON><PERSON><PERSON>r kann spaßig sein. Das hängt nur von dir ab."], "enemytips": ["<PERSON><PERSON><PERSON>fähigkeiten lassen seinen Zielort erkennen. Das kannst du zu deinem Vorteil nutzen.", "Champions mit schnell einsetzbarer Massenkontrolle können <PERSON> in die Knie zwingen.", "<PERSON><PERSON><PERSON> du au<PERSON>lio, wenn keine seiner Verbündeten in der Nähe sind, ist seine Mobilität stark eingeschränkt. Du solltest also versuchen, ihn alleine zu fassen zu bekommen."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "MilioQ", "name": "Ultra-Mega-Feuerkick", "description": "<PERSON>t einen Ball, der Gegner zurückstößt. Bei einem Treffer fliegt der <PERSON> in die Luft und stürzt anschließend in Richtung des Gegners, woraufhin er im Aufprallbereich Schaden verursacht und Gegner verlangsamt.", "tooltip": "<PERSON><PERSON> kickt einen Feuerball, der den ersten getroffenen Gegner <status>zurückstößt</status>. Bei einem Treffer springt der Ball hinter ihn und explodiert. Dabei verursacht er <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> an umstehenden Gegnern und <status>verlangsamt</status> sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowamountpercent }}.<br /><br />Wird mindestens ein gegnerischer Champion von <spellName>Ultra-Mega-Feuerkick</spellName> getroffen, werden {{ refundratio*100 }}&nbsp;% der Manakosten der Fähigkeit wiederhergestellt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Manakosten"], "effect": ["{{ falldamage }} -> {{ falldamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "MilioQ.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioW", "name": "Lebendiges Lagerfeuer", "description": "Du erschaffst einen verstärkenden Bereich, in dem Verbündete geheilt werden und ihre Angriffsreichweite erhöht wird, solange sie sich in ihm befinden. Der Bereich folgt dem Verbündeten, der am nächsten zum Wirkungsort war.", "tooltip": "<PERSON><PERSON> erschafft eine Feuerstelle, die verbündeten Champions {{ zoneduration }}&nbsp;Sekunden lang folgt. Verbündete Champions in der Nähe erhalten {{ rangepercent }}&nbsp;Angriffsreichweite und stellen über die Dauer hinweg <healing>{{ healingovertime }}&nbsp;<PERSON><PERSON></healing> wieder her. Außerdem löst die Feuerstelle alle {{ healfrequencyseconds }}&nbsp;Sekunden <spellName>Feuer und Flamme!</spellName> aus.<br /><br /><recast>Reaktivierung:</recast> Wechselt den Verbündeten, dem die Feuerstelle folgt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Reichweite", "Heilung", "Abklingzeit", "Manakosten"], "effect": ["{{ rangepctincrease*100.000000 }}&nbsp;% -> {{ rangepctincreasenl*100.000000 }}&nbsp;%", "{{ totalhealingovertime }} -> {{ totalhealingovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [29, 27, 25, 23, 21], "cooldownBurn": "29/27/25/23/21", "cost": [90, 100, 110, 120, 130], "costBurn": "90/100/110/120/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "MilioW.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioE", "name": "Warme Umarmung", "description": "<PERSON><PERSON> g<PERSON>t einem Verbündeten einen Schild, wodurch er vorübergehend dessen Lauftempo erhöht. Diese Fähigkeit hat 2 Aufladungen.", "tooltip": "<PERSON><PERSON>t einen verbündeten Champion in schützende Flammen, um ihm einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ shieldcalc }} sowie {{ movespeedduration }}&nbsp;Sekunden lang <speed>{{ movespeedamount*100 }}&nbsp;% Lauftempo</speed> zu gewähren.<br /><br />Die Fähigkeit hat 2 Aufladungen und ihre Effekte steigern sich bei Anwendung auf das gleiche Ziel.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Lauftempo", "Manakosten", "Wiederaufladungsrate"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ movespeedamount*100.000000 }}&nbsp;% -> {{ movespeedamountnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MilioE.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioR", "name": "Atem des Lebens", "description": "<PERSON><PERSON> eine Welle aus schützenden Flammen, die Verbündete in Reichweite heilen und Massenkontrolleffekte von ihnen entfernen.", "tooltip": "<PERSON><PERSON> ent<PERSON>t eine Welle von beruhigenden Flammen auf verbündete Champions in der Nähe, die <status>kampfunfähig machende</status> und <status>bewegungsunfähig machende</status> Effekte entfernt, <healing>{{ healcalc }}&nbsp;Leben</healing> wied<PERSON><PERSON>tellt und {{ tenacityduration }}&nbsp;Sekunden lang {{ tenacityamount*100 }}&nbsp;% Zähigkeit gewährt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung", "Abklingzeit"], "effect": ["{{ healbase }} -> {{ healbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 145, 130], "cooldownBurn": "160/145/130", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MilioR.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> und Flamme!", "description": "<PERSON><PERSON><PERSON>gkeiten verzaubern Verbündete bei Berührung, wodurch sie mit dem nächsten schadensverursachenden Angriff zusätzlichen Schaden verursachen und das Ziel verbrennen.", "image": {"full": "Milio_P.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}