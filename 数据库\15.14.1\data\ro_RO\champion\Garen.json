{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Garen": {"id": "<PERSON><PERSON><PERSON>", "key": "86", "name": "<PERSON><PERSON><PERSON>", "title": "măreția Demaciei", "image": {"full": "Garen.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "86000", "num": 0, "name": "default", "chromas": true}, {"id": "86001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86002", "num": 2, "name": "<PERSON><PERSON><PERSON>, soldat al deșerturilor", "chromas": false}, {"id": "86003", "num": 3, "name": "Garen commando", "chromas": false}, {"id": "86004", "num": 4, "name": "Gare<PERSON>, cavalerul groazei", "chromas": false}, {"id": "86005", "num": 5, "name": "Garen veteran", "chromas": false}, {"id": "86006", "num": 6, "name": "Garen din Legiunea de Oțel", "chromas": false}, {"id": "86010", "num": 10, "name": "<PERSON><PERSON><PERSON>, amiralul răzvrătit", "chromas": false}, {"id": "86011", "num": 11, "name": "Garen al regatelor războinice", "chromas": true}, {"id": "86013", "num": 13, "name": "Garen, zeul-rege", "chromas": false}, {"id": "86014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "86022", "num": 22, "name": "Garen al regatelor Mecha", "chromas": false}, {"id": "86023", "num": 23, "name": "Garen al regatelor Mecha (Prestigiu)", "chromas": false}, {"id": "86024", "num": 24, "name": "Garen de la Academia de Luptă", "chromas": true}, {"id": "86033", "num": 33, "name": "Garen creator de mituri", "chromas": true}, {"id": "86044", "num": 44, "name": "Gare<PERSON>, zeul-rege decăzut", "chromas": false}], "lore": "Garen este un luptător mândru și nobil, ce face parte din Avangarda Neînfricată. Camarazii săi îl admiră, iar inamicii îl respectă – poate și din cauză că face parte din prestigioasa familie Crownguard, casa însărcinată cu apărarea Demaciei și a idealurilor sale. Protejat de o armură rezistentă la magie și înarmat cu un paloș impunător, Garen e pregătit să înfrunte toți magii și vrăjitorii de pe câmpul de luptă, ca o adevărată tornadă de oțel a justiției.", "blurb": "Garen este un luptător mândru și nobil, ce face parte din Avangarda Neînfricată. Camarazii săi îl admiră, iar inamicii îl respectă – poate și din cauză că face parte din prestigioasa familie Crownguard, casa însărcinată cu apărarea Demaciei și a...", "allytips": ["Regenerarea lui Garen crește considerabil dacă poate evita daunele inamicilor timp de câteva secunde.", "''Judecata'' provoacă daune maxime doar atunci când lovește o singură țintă. Dacă vrei să faci schimburi avantajoa<PERSON>, încearcă să te poziționezi astfel încât să lovești doar campionul inamic.", "Singura limită a lui Garen sunt timpii săi de reactivare, iar obiectele precum ''Satârul negru'' îl fac foarte eficient."], "enemytips": ["Cumpără obiecte de armură pentru a reduce daunele fizice pe care le provoacă Garen.", "Încearcă să fugi de el când îți scade viața, fiindcă te poate executa pe loc cu ajutorul ''Dreptății demaciene''.", "Fii precaut dacă vrei să-l ataci atunci când se află în tufișuri. Urmăritorii lui suferă adesea întreaga forță a ''Judecății''.", "''Judecata'' provoacă daune maxime doar atunci când lovește o singură țintă. Dacă nu este posibil să ieși din raza sa de acțiune, intră în valul de minioni pentru a reduce daunele suferite."], "tags": ["Fighter", "Tank"], "partype": "Inexistentă", "info": {"attack": 7, "defense": 7, "magic": 1, "difficulty": 5}, "stats": {"hp": 690, "hpperlevel": 98, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 38, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.5, "attackspeedperlevel": 3.65, "attackspeed": 0.625}, "spells": [{"id": "GarenQ", "name": "Lovitură decisivă", "description": "Garen primește un bonus scurt la viteza de mișcare și scapă de toate efectele de încetinire care îl afectează. Următorul atac lovește o zonă vitală a inamicului, provoacă daune bonus și amuțește ținta.", "tooltip": "Garen elimină toate efectele de <status>încetinire</status> care îl afectează și primește <speed>{{ movementspeedamount*100 }}% viteză de mișcare</speed> timp de {{ movementspeedduration }} sec.<br /><br />Următorul său atac <status>amuțeș<PERSON></status> ținta timp de {{ silenceduration }} secunde și îi provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Durată viteză de <PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedduration }} -> {{ movementspeeddurationNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "GarenQ.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "GarenW", "name": "<PERSON><PERSON><PERSON>", "description": "Garen își îmbunătățește în mod pasiv armura și rezistența la magie atunci când ucide adversari. În plus, poate activa această abilitate pentru a primi un bonus la tenacitate și un scut; după aceea, pe o perioadă mai lungă, daunele sunt reduse mai puțin.", "tooltip": "<spellPassive>Pasiv<PERSON>:</spellPassive> Garen are <scaleArmor>{{ resistsfortooltip }} armur<PERSON> bonus</scaleArmor> și <scaleMR>{{ resistsfortooltip }} rezistență la magie bonus</scaleMR>. Când ucide o unitate, primește permanent <attention>{{ resistgainonkilltooltip }} rezistențe</attention>, până la maximum <attention>{{ resistmax }}</attention>.<br /><br /><spellActive>Activă:</spellActive> Garen își ia inima-n dinți timp de {{ drduration }} secunde, iar daunele pe care le suferă sunt reduse cu {{ drpercent*100 }}%. Primește și un <shield>scut în valoare de {{ totalshield }}</shield> și <slow>{{ upfronttenacity*100 }}% tenacitate</slow> timp de {{ upfrontduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Valoarea scutului", "Reducere daune", "Timp de reactivare"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GarenW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "GarenE", "name": "Judecată", "description": "Garen î<PERSON>i rotește rapid sabia în jurul corpului, provocându-le daune inamicilor din apropiere.", "tooltip": "Garen își rotește rapid sabia timp de {{ duration }} secunde, provocând <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> de {{ f1 }} ori de-a lungul duratei. Cel mai apropiat inamic suferă cu <physicalDamage>{{ nearestenemybonus*100 }}% mai multe daune</physicalDamage>. Campionii loviți de {{ stackstoshred }} lovituri pierd <scaleArmor>{{ shredamount*100 }}% din armură</scaleArmor> timp de {{ shredduration }} secunde.<br /><br /><recast>Refolosire</recast>: Garen încheie această abilitate mai devreme{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune de bază per rotire", "Creștere daune din atac per rotire", "Timp de reactivare"], "effect": ["{{ basedamagepertick }} -> {{ basedamagepertickNL }}", "{{ adratiopertick*100.000000 }}% -> {{ adratioperticknl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "GarenE.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "GarenR", "name": "Dreptate demaciană", "description": "Garen invocă măreția Demaciei pentru a încerca să execute un campion inamic.", "tooltip": "Garen invocă măreția Demaciei pentru a-și ucide inamicul, provocându-i <trueDamage>daune reale în valoare de {{ basedamage }} plus {{ executedamage*100 }}% din viața lipsă</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune", "Pro<PERSON> daune via<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ executedamage*100.000000 }}% -> {{ executedamagenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "GarenR.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}], "passive": {"name": "Perseverență", "description": "Dacă Garen nu a suferit recent daune și nu a fost lovit recent de abilitățile inamicilor, își regenerează o parte din viața totală în fiecare secundă.", "image": {"full": "Garen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}