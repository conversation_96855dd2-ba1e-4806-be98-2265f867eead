{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Garen": {"id": "<PERSON><PERSON><PERSON>", "key": "86", "name": "Γκ<PERSON><PERSON><PERSON>ν", "title": "η Δύναμη της Ντεμάσια", "image": {"full": "Garen.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "86000", "num": 0, "name": "default", "chromas": true}, {"id": "86001", "num": 1, "name": "Αιματ<PERSON><PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86002", "num": 2, "name": "Γκ<PERSON><PERSON><PERSON><PERSON> ο Πεζοναύτης της Ερήμου", "chromas": false}, {"id": "86003", "num": 3, "name": "Καταδρομ<PERSON><PERSON>ς <PERSON>ν", "chromas": false}, {"id": "86004", "num": 4, "name": "Γ<PERSON><PERSON><PERSON><PERSON><PERSON> ο Ιππότης του Τρόμου", "chromas": false}, {"id": "86005", "num": 5, "name": "Σκληρ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86006", "num": 6, "name": "Γ<PERSON><PERSON><PERSON><PERSON>ν της <PERSON><PERSON><PERSON><PERSON><PERSON>ι<PERSON>ης <PERSON>ς", "chromas": false}, {"id": "86010", "num": 10, "name": "Αποστ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86011", "num": 11, "name": "Γ<PERSON><PERSON><PERSON><PERSON>ν των Επτά Βασιλείων", "chromas": true}, {"id": "86013", "num": 13, "name": "Θεϊκός Βασιλιάς <PERSON>εν", "chromas": false}, {"id": "86014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "86022", "num": 22, "name": "Γκάρεν Βασιλείων Mecha", "chromas": false}, {"id": "86023", "num": 23, "name": "Γκά<PERSON><PERSON>ν Βασιλείων Mecha - Έκδοση Κύρους", "chromas": false}, {"id": "86024", "num": 24, "name": "Γ<PERSON><PERSON><PERSON><PERSON>ν της Ακαδημ<PERSON>ας <PERSON>ς", "chromas": true}, {"id": "86033", "num": 33, "name": "Μυθο<PERSON><PERSON><PERSON><PERSON><PERSON>ης <PERSON>ν", "chromas": true}, {"id": "86044", "num": 44, "name": "Έκπτωτος Θεϊκός Βασιλιάς Γκ<PERSON>εν", "chromas": false}], "lore": "Ένας υπερήφανος και ευγενής πολεμιστής, ο Γκάρεν είναι το πιο διακεκριμένο μέλος της Ατρόμητης Φρουράς. Οι φίλοι του τον λατρεύουν και οι εχθροί του τον σέβονται, τόσο για τις ικανότητές του στο πεδίο της μάχης, όσο και ως γόνο του Οίκου των Στεμματοφυλάκων, μιας οικογένειας που υπερασπίζεται διαχρονικά την Ντεμάσια και τα ιδανικά της. Θωρακισμένος με τη πανοπλία του που αντιστέκεται στη μαγεία και με το τεράστιο σπαθί του στα χέρια, ο Γκάρεν είναι ο φόβος και ο τρόμος των μάγων που επιβουλεύονται τη Ντεμάσια, ένας πραγματικός ατσάλινος κυκλώνας που ξεχύνεται στο πεδίο της μάχης.", "blurb": "Ένας υπερήφανος και ευγενής πολεμιστής, ο Γκάρεν είναι το πιο διακεκριμένο μέλος της Ατρόμητης Φρουράς. Οι φίλοι του τον λατρεύουν και οι εχθροί του τον σέβονται, τόσο για τις ικανότητές του στο πεδίο της μάχης, όσο και ως γόνο του Οίκου των...", "allytips": ["Η αναπλήρωση του Γκάρεν αυξάνεται θεαματικά αν δεν δεχτεί ζημιά για μερικά δευτερόλεπτα.", "Η Κρίση προκαλεί μέγιστη ζημιά όταν πετυχαίνει έναν μόνο στόχο. Για αποτελεσματικές αψιμαχίες, προσπαθήστε να τοποθετηθείτε με τρόπο που να δέχεται χτύπημα μόνο ο αντίπαλος Ήρωας.", "Το μόνο πράγμα που επηρεάζει τον Γκάρεν είναι ο Χρόνος Επαναφόρτισης των ικανοτήτων του."], "enemytips": ["Μαζέψτε αντικείμενα πανοπλίας για να μειώσετε το μεγάλο ποσό Σωματικής Ζημιάς που προκαλεί ο Γκάρεν.", "Τρέξτε μακριά από τον Γκάρεν όταν μειωθεί πολύ η Ζωή σας, γιατί μπορεί να σας εκτελέσει στιγμιαία με την Ντεμασιανή Δικαιοσύνη.", "Προσέξτε όταν επιτίθεστε στον Γκάρεν όταν βρίσκεται σε θάμνους. Συχνά θα οδηγήσει σε πρόκληση μέγιστης ζημιάς από την Κρίση.", " Η Κρίση προκαλεί μέγιστη ζημιά όταν πετυχαίνει έναν μόνο στόχο. Αν δεν είναι εφικτό να ξεφύγετε από την εμβέλειά της, κινηθείτε μέσα από συμμάχους υπηρέτες για να μειωθεί η ζημιά που θα δεχθείτε."], "tags": ["Fighter", "Tank"], "partype": "Καθόλου", "info": {"attack": 7, "defense": 7, "magic": 1, "difficulty": 5}, "stats": {"hp": 690, "hpperlevel": 98, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 38, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.5, "attackspeedperlevel": 3.65, "attackspeed": 0.625}, "spells": [{"id": "GarenQ", "name": "Αποφα<PERSON>ιστι<PERSON><PERSON>ύπημα", "description": "Η Ταχύτητα Κίνησης του Γκάρεν αυξάνεται στιγμιαία αφαιρώντας όλες τις επιβραδύνσεις που τον επηρεάζουν. Η επόμενη επίθεσή του χτυπά τον αντίπαλο σε ζωτικό σημείο, προκαλώντας μπόνους ζημιά και αναγκάζοντάς τον να σιγήσει.", "tooltip": "Ο Γκάρεν αφαιρεί όλες τις επιδράσεις <status>Επιβράδυνσης</status> που τον επηρεάζουν και αποκτά <speed>Ταχύτητα Κίνησης {{ movementspeedamount*100 }}%</speed> για {{ movementspeedduration }} δευτ.<br /><br />Η επόμενη επίθεσή του προκαλεί <status>Σιωπή</status> για {{ silenceduration }} δευτ. και κάνει <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκε<PERSON><PERSON> Ταχύτητας <PERSON>ησης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedduration }} -> {{ movementspeeddurationNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "GarenQ.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "GarenW", "name": "Κουράγιο", "description": "Ο Γκάρεν αυξάνει παθητικά τη Θωράκιση και την Αντίσταση Μαγείας του σκοτώνοντας εχθρούς. Μπορεί επίσης να ενεργοποιήσει αυτή την ικανότητα για να αποκτήσει μια ασπίδα και Εμμονή για σύντομο χρονικό διάστημα, καθώς και μικρότερη ποσότητα Μείωσης Ζημιάς για μεγαλύτερο χρονικό διάστημα.", "tooltip": "<spellPassive>Παθητική:</spellPassive> Ο Γκάρεν αποκτά <scaleArmor>{{ resistsfortooltip }} μπόνους Θωράκιση</scaleArmor> και <scaleMR>{{ resistsfortooltip }} μπόνους Αντίσταση Μαγείας</scaleMR>. Η εκτέλεση μονάδων δίνει μόνιμα <attention>{{ resistgainonkilltooltip }} αντιστάσεις</attention>, με μέγιστο όριο <attention>{{ resistmax }}</attention>.<br /><br /><spellActive>Ενεργή:</spellActive> Ο Γκάρεν μαζεύει το κουράγιο του για {{ drduration }} δευτ. και μειώνει την εισερχόμενη ζημιά κατά {{ drpercent*100 }}%. Επίσης, αποκτά <shield>{{ totalshield }} Ασπίδα</shield> και <slow>{{ upfronttenacity*100 }}% Εμμονή</slow> για {{ upfrontduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Αντοχή Ασπίδας", "Μείωση ζημιάς", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GarenW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "GarenE", "name": "Κρίση", "description": "Ο Γκάρεν περιστρέφει γρήγορα το σπαθί του γύρω από το σώμα του και προκαλεί Σωματική Ζημιά στους κοντινούς εχθρούς.", "tooltip": "Ο Γκάρεν περιστρέφει γρήγορα το σπαθί του για {{ duration }} δευτ., προκαλώντας <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> {{ f1 }} φορές κατά τη διάρκεια της ικανότητας. Ο πλησιέστερος αντίπαλος δέχεται <physicalDamage>{{ nearestenemybonus*100 }}% αυξημένη ζημιά</physicalDamage>. Οι Ήρωες που δέχονται {{ stackstoshred }} χτυπήματα χάνουν <scaleArmor>{{ shredamount*100 }}% Θωράκιση</scaleArmor> για {{ shredduration }} δευτ.<br /><br /><recast>Νέα χρήση</recast>: Ο Γκάρεν λήγει την Ικανότητα νωρίτερα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική Ζημιά ανά Περιστροφή", "Αναλογική αύξηση Ζημιάς Επίθεσης ανά περιστροφή", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamagepertick }} -> {{ basedamagepertickNL }}", "{{ adratiopertick*100.000000 }}% -> {{ adratioperticknl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "GarenE.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "GarenR", "name": "Ντεμασιανή Δικαιο<PERSON>ύνη", "description": "Ο Γκάρεν καλεί τη δύναμη της Ντεμάσια σε μια προσπάθεια να εκτελέσει έναν αντίπαλο Ήρωα.", "tooltip": "Ο Γκάρεν καλεί τη δύναμη της Ντεμάσια για να εκτελέσει έναν αντίπαλο Ήρωα, προκαλώντας <trueDamage>Πραγματική Ζημιά ίση με {{ basedamage }} συν το {{ executedamage*100 }}% της Ζωής που του λείπει</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Ζημιά", "Ποσοστό ζημιάς βάσει Ζωής που λείπει"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ executedamage*100.000000 }}% -> {{ executedamagenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "GarenR.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}], "passive": {"name": "Επιμονή", "description": "Αν ο Γκάρεν δεν έχει χτυπηθεί πρόσφατα από ζημιά ή εχθρικές ικανότητες, αναπληρώνει ένα ποσοστό της συνολικής Ζωής του κάθε δευτερόλεπτο.", "image": {"full": "Garen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}