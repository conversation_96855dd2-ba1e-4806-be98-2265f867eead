{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "カーサス", "title": "死を歌う者", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "ファントム カーサス", "chromas": false}, {"id": "30002", "num": 2, "name": "自由のカーサス", "chromas": false}, {"id": "30003", "num": 3, "name": "デスゲイト カーサス", "chromas": true}, {"id": "30004", "num": 4, "name": "Pentakill カーサス", "chromas": false}, {"id": "30005", "num": 5, "name": "Fnatic カーサス", "chromas": false}, {"id": "30009", "num": 9, "name": "破滅の光カーサス", "chromas": false}, {"id": "30010", "num": 10, "name": "地獄の業火カーサス", "chromas": true}, {"id": "30017", "num": 17, "name": "Pentakill III: ロストチャプター カーサス", "chromas": true}, {"id": "30026", "num": 26, "name": "古の賢樹カーサス", "chromas": true}], "lore": "忘却の使徒、不死の亡霊カーサスは、呪いの歌を口にしながらその不吉な姿を露わにする。命ある者は不死がもたらす永遠を恐れるが、カーサスの目に映るのはそこに内包された美しさと純粋さ、すわなち生と死の完全なる統合のみだ。シャドウアイルから現れ出でるカーサスは、不死の使徒として生ける者に死という愉悦を与える。", "blurb": "忘却の使徒、不死の亡霊カーサスは、呪いの歌を口にしながらその不吉な姿を露わにする。命ある者は不死がもたらす永遠を恐れるが、カーサスの目に映るのはそこに内包された美しさと純粋さ、すわなち生と死の完全なる統合のみだ。シャドウアイルから現れ出でるカーサスは、不死の使徒として生ける者に死という愉悦を与える。", "allytips": ["「鎮魂歌」で異なるレーンの敵を倒す場合は、味方にタイミングを指示してもらうか、常にマップを見る癖をつける必要がある。", "「根絶やし」はミニオンを倒したり敵チャンピオンを牽制するのはもちろん、一瞬だが視界も取れる強力なスキルだ。"], "enemytips": ["カーサスは死亡後も一定時間その場に残り、スキルを発動できる。「冒涜」の発動効果も死亡時に起動されるのでカーサスの骸には近づかないこと。", "カーサスのアルティメットスキル「鎮魂歌」に耐えられるだけの体力は常に確保しておこう。", "そのためには回復アイテムや本拠地に帰還する等の手間を惜しんではいけない。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "根絶やし", "description": "指定地点を時間差で爆発させ、周囲の敵にダメージを与える。命中した敵が1体の場合は与えるダメージが増加する。 ", "tooltip": "魔法のエネルギーを爆発させて<magicDamage>{{ qdamage }}の魔法ダメージ</magicDamage>を与える。爆発が当たった敵が1体のみだった場合は、代わりに<magicDamage>{{ qsingletargetdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "嘆きの壁", "description": "エネルギーを搾取する霊的な壁を出現させ、そこを通り抜けた敵の移動速度と魔法防御を一定時間低下させる。", "tooltip": "{{ e4 }}秒間持続する壁を出現させる。この壁を通過した敵は{{ e5 }}秒間、<scaleMR>魔法防御が{{ e1 }}%</scaleMR>低下し、{{ e3 }}%の<status>スロウ効果</status>を受ける。この効果は効果時間をかけて減衰していく。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["壁の幅", "スロウ効果"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "冒涜", "description": "自動効果として、カーサスが倒した敵から生気を奪いマナを回復する。<br>発動すると自身の周囲に倒した獲物の魂を召喚し、範囲内の敵にダメージを与えるがマナを著しく消耗する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 敵を倒すと<scaleMana>マナが{{ e2 }}</scaleMana>回復する。<br /><br /><toggle>発動中:</toggle> 壊死のオーラを展開し、周囲の敵に毎秒<magicDamage>{{ totaldps }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ", "マナ回復", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "毎秒{{ cost }}マナ"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "鎮魂歌", "description": "3秒間の詠唱後、マップ中の敵ユニットチャンピオンにダメージを与える。", "tooltip": "3秒間詠唱してから、すべての敵チャンピオンに距離に関係なく<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "怨嗟の叫び", "description": "カーサスは死亡すると同時に霊体化し、その場でスキルを発動できるようになる。", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}