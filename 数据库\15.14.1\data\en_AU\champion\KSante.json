{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"KSante": {"id": "KSante", "key": "897", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "the Pride of Nazumah", "image": {"full": "KSante.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "897000", "num": 0, "name": "default", "chromas": false}, {"id": "897001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "897002", "num": 2, "name": "Prestige Empyrean K'Sante", "chromas": false}, {"id": "897008", "num": 8, "name": "HEARTSTEEL K'Sante", "chromas": true}], "lore": "Defiant and courageous, <PERSON><PERSON><PERSON><PERSON> battles colossal beasts and ruthless Ascended to protect his home of Nazumah, a coveted oasis amid the sands of Shurima. But after a falling-out with his former partner, <PERSON><PERSON><PERSON><PERSON> realizes that in order to become a warrior worthy of leading his city, he must temper his single-minded drive for success. Only then can he avoid falling prey to his own pride and find the wisdom he needs to defeat the vicious monsters threatening his people.", "blurb": "Defiant and courageous, <PERSON><PERSON><PERSON><PERSON> battles colossal beasts and ruthless Ascended to protect his home of Nazumah, a coveted oasis amid the sands of Shurima. But after a falling-out with his former partner, <PERSON><PERSON><PERSON><PERSON> realizes that in order to become a warrior...", "allytips": ["Use Sonic Wave before Dragon's Rage so you can chase the target with Resonating Strike.", "Take advantage of Flurry by weaving in basic attacks between spell casts - this maximizes damage output and minimizes Energy loss.", "Self-casting Safeguard and using Iron Will are powerful tools for killing neutral monsters (in the jungle)."], "enemytips": ["Stay spread out to minimize the impact of <PERSON>'s ultimate, Dragon's Rage.", "<PERSON> has powerful tools to combat physical damage in Iron Will and Cripple but he is still vulnerable to magic damage.", "<PERSON> relies heavily on following up with his abilities. Use disables to prevent him from chaining together his abilities and attacks."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 8, "magic": 7, "difficulty": 9}, "stats": {"hp": 625, "hpperlevel": 120, "mp": 320, "mpperlevel": 60, "movespeed": 330, "armor": 36, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 2.1, "attackrange": 150, "hpregen": 9.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.688}, "spells": [{"id": "KSanteQ", "name": "Ntofo Strikes", "description": "<PERSON><PERSON><PERSON><PERSON> strikes, damaging and slowing enemies in a short line.<br><br>On hit, grants a stack of Ntofo Strikes. At 2 stacks, <PERSON><PERSON><PERSON><PERSON> fires a shockwave that pulls enemies in.<br><br>When All Out, cooldown is reduced.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> slams his weapon, dealing <physicalDamage>{{ basedamage }} physical damage</physicalDamage> and <status>Slowing</status> enemies by {{ slowpercent*100 }}% for {{ slowduration }}s. If he hits, he gains a stack of Ntofo Strikes for {{ recastwindow }} seconds. At 2 stacks, he instead fires a shockwave that <status>Stuns</status> and <status>Pulls</status> enemies for {{ stunduration }} second.<br /><br /><keywordMajor>All Out</keywordMajor>: Cooldown is reduced by {{ rcooldownreduction.0*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ flatdamage }} -> {{ flatdamageNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "KSanteQ.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteW", "name": "Path Maker", "description": "<PERSON><PERSON><PERSON><PERSON> charges up, taking reduced damage, then dashes, knocking back and stunning enemies.<br><br>When All Out, deals increased damage and no longer knocks back or stuns.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> raises his weapons defensively for {{ mindurationtooltip }}-{{ maxduration.1 }}s, becoming Unstoppable and reducing incoming damage by {{ damagereduction*100 }}%. Then, he rams forward, dealing <physicalDamage>{{ basedamage }} + {{ totalmaxhealthdamage }} max Health physical damage</physicalDamage>. Enemies hit are <status>Knocked Back</status> and <status>Stunned</status> for {{ minknockbackduration }}-{{ maxknockbackduration }}s (based on charge time).<br /><br /><keywordMajor>All Out:</keywordMajor> Cooldown is refreshed. Deals an additional {{ rdamageincreasemin*100 }}-{{ rdamageincreasemax*100 }}% damage as <trueDamage>true damage</trueDamage> (based on charge time), damage reduction is increased to {{ rdamagereduction*100 }}% and dash speed is increased, but no longer <status>Knocks Back</status> or <status>Stuns</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KSanteW.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteE", "name": "Footwork", "description": "<PERSON><PERSON><PERSON><PERSON> dashes and gains a shield. If targeting an ally, <PERSON><PERSON><PERSON><PERSON> dashes to the ally with increased range and both gain a shield.<br><br>When All Out, cooldown is reduced and speed is increased.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> dashes, gaining <shield>{{ totalshield }} Shield</shield> for {{ shieldduration }} seconds. If dashing to an ally, the distance of the dash is significantly increased and the ally is also <shield>Shielded</shield>.<br /><br /><keywordMajor>All Out</keywordMajor>: Cooldown is decreased by {{ cooldownmodao*100 }}% and dash speed is increased.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Shield", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ shieldbaseamountfast }} -> {{ shieldbaseamountfastNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "KSanteE.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteR", "name": "All Out", "description": "<PERSON><PERSON><PERSON><PERSON> knocks an enemy back, launching them through any wall in their path. Then, <PERSON><PERSON><PERSON><PERSON> goes All Out and dashes after them, gaining increased damage, healing, and transformed abilities at the cost of reduced defenses.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> shatters his ntofos to <status>Knock Back</status> an enemy champion, dealing <physicalDamage>{{ basedamage }} physical damage</physicalDamage>, then dashes behind them and goes <keywordMajor>All Out</keywordMajor> for {{ alloutduration }}s. If the enemy would hit a wall, they are <status>Knocked Back</status> through the wall and <PERSON><PERSON><PERSON><PERSON> strikes them again for <physicalDamage>{{ totaldamageslamdown }} physical damage</physicalDamage>.<br /><br />While <keywordMajor>All Out</keywordMajor>, <PERSON><PERSON><PERSON><PERSON>'s Abilities are upgraded and he gains <attackSpeed>{{ attackspeed*100 }}% Attack Speed</attackSpeed>, {{ armorpenpercent*100 }}% Bonus Armor Penetration, and <omnivamp>{{ omnivamp*100 }}% Omnivamp</omnivamp>, but loses <healing>{{ healthlost*100 }}% max Health</healing>, <scaleArmor>{{ defenseslost*100 }}% Bonus Armor</scaleArmor>, and <scaleMR>{{ defenseslost*100 }}% Bonus Magic Resistance</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Follow-Up Damage", "Cooldown", "Attack Speed"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slamdownstrikedamage }} -> {{ slamdownstrikedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "KSanteR.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dauntless Instinct", "description": "<PERSON><PERSON><PERSON><PERSON>'s Abilities mark targets to take more damage on his next Attack.<br><br>When All Out, <PERSON><PERSON><PERSON><PERSON> deals more damage with all Attacks and Abilities.", "image": {"full": "Icons_KSante_P.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}