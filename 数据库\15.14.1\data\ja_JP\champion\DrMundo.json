{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "ドクター・ムンド", "title": "ゾウンの狂人", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "ポイズン ムンド", "chromas": false}, {"id": "36002", "num": 2, "name": "筋肉モリモリ ムンドマン", "chromas": false}, {"id": "36003", "num": 3, "name": "企業戦士ムンド", "chromas": true}, {"id": "36004", "num": 4, "name": "ムンド・ムンド", "chromas": false}, {"id": "36005", "num": 5, "name": "処刑人ムンド", "chromas": false}, {"id": "36006", "num": 6, "name": "オーク ムンド", "chromas": false}, {"id": "36007", "num": 7, "name": "TPA ムンド", "chromas": false}, {"id": "36008", "num": 8, "name": "プールパーティ ムンド", "chromas": false}, {"id": "36009", "num": 9, "name": "エル・マチョ ムンド", "chromas": false}, {"id": "36010", "num": 10, "name": "凍れる王子ムンド", "chromas": true}, {"id": "36021", "num": 21, "name": "ストリートデーモン ドクター・ムンド", "chromas": true}], "lore": "完全に正気を失った、おぞましい紫色の悲しき殺人鬼。ゾウン市民の多くが闇の深い夜に外を出歩かないのは、ドクター・ムンドがいるためだ。今では“医者”を名乗っているが、以前はゾウンでも特に評判の悪い、とある医療院の患者だった。そこの職員を一人残らず“治療”したのち、ドクター・ムンドはかつて自身が処置を受けていた無人の病棟に診察室を構え、その身で何度も味わってきた非人道的な医療行為を、見よう見まねで行うようになった。棚に入った大量の薬物と、素人以下の医療知識を手に、ドクター・ムンドは今、注射を打つことで自身をさらなる怪物へと変え、不幸にも診察室の近くへ迷い込んだ“患者たち”を、恐怖で震え上がらせている。", "blurb": "完全に正気を失った、おぞましい紫色の悲しき殺人鬼。ゾウン市民の多くが闇の深い夜に外を出歩かないのは、ドクター・ムンドがいるためだ。今では“医者”を名乗っているが、以前はゾウンでも特に評判の悪い、とある医療院の患者だった。そこの職員を一人残らず“治療”したのち、ドクター・ムンドはかつて自身が処置を受けていた無人の病棟に診察室を構え、その身で何度も味わってきた非人道的な医療行為を、見よう見まねで行うようになった。棚に入った大量の薬物と、素人以下の医療知識を手に、ドクター・ムンドは今、注射を打つことで自身を...", "allytips": ["体力が減った状態でタイミングよく「サディズム」を発動し、体力を回復しながら敵の重要な役割のチャンピオンに向かっていこう。敵の意識をドクター・ムンドに引きつけることが仕事だ。", "「スピリットビサージュ」を装備すると、アルティメットスキルの体力回復効果が増加し、全スキルのクールダウンが短縮される。とても相性が良い。", "「ホウチョウ」は、中立モンスターを効率よく倒すのに向いている。体力が残り少なくなったら、本拠地に戻らずに中立モンスターを狩りながら、アルティメットスキルで体力を維持しよう。"], "enemytips": ["ドクター・ムンドが「サディズム」を発動したら、味方と協力して高ダメージのスキルで一気に連続攻撃しよう。ただし、素早く倒さないと体力が回復してしまうので注意すること。", "ドクター・ムンドが「サディズム」を使ったら、サモナースペル「イグナイト」を使って大幅にその体力回復効果を削ろう。"], "tags": ["Tank", "Fighter"], "partype": "なし", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "骨切りノコギリ", "description": "骨切りノコギリを投げ、最初に命中した敵に対象の現在体力に応じたダメージを与えて、スロウ効果を付与する。", "tooltip": "ノコギリを投げ、最初に命中した敵に<magicDamage>現在体力の{{ currenthealthdamage*100 }}%にあたる魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />ノコギリがチャンピオンまたはモンスターに命中した場合は<healing>体力が{{ healthrestoreonhitchampionmonster }}</healing>回復する。チャンピオンまたはモンスター以外に命中した場合は、代わりに<healing>体力が{{ healthrestoreonhitminion }}</healing>回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["現在体力ダメージ", "最小ダメージ", "モンスターへのダメージ上限", "体力コスト"], "effect": ["{{ currenthealthdamage*100.000000 }}% -> {{ currenthealthdamagenl*100.000000 }}%", "{{ minimumdamage }} -> {{ minimumdamageNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}", "{{ healthcost }} -> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "の体力", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ healthcost }}の体力"}, {"id": "DrMundoW", "name": "心臓ビリビリ", "description": "自身を感電させて、周囲の敵に継続的にダメージを与え、受けたダメージの一部を蓄える。効果時間の最後か再発動時に、周囲の敵に大ダメージを与える。これが敵に命中した場合は、それまでに蓄えていたダメージの一定割合を体力として回復する。", "tooltip": "除細動器をチャージして、最大{{ duration }}秒間、周囲の敵に毎秒<magicDamage>{{ damagepertick*4 }}の魔法ダメージ</magicDamage>を与える。さらに、最初の{{ grayhealthinitialduration }}秒間は受けたダメージの{{ grayhealthstorageinitial }}を、残りの時間は受けたダメージの{{ grayhealthstorage*100 }}%を灰色の体力として蓄え、<recast>再発動</recast>可能になる。<br /><br /><recast>再発動:</recast> 除細動器を爆発させて、周囲の敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。チャンピオンに命中した場合は、<healing>灰色の体力の{{ grayhealthbigmod*100 }}%</healing>を回復する。チャンピオンに命中しなかった場合は、代わりに<healing>灰色の体力の{{ grayhealthsmallmod*100 }}%</healing>を回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ヒットごとのダメージ", "再発動時ダメージ", "クールダウン"], "effect": ["{{ damagepertick }} -> {{ damagepertickNL }}", "{{ recastbasedamage }} -> {{ recastbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "%", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "現在体力の{{ currenthealthcost*100 }}%"}, {"id": "DrMundoE", "name": "野蛮な痛み", "description": "自動効果 - 自身の最大体力に応じて増加する、増加攻撃力を獲得する。<br><br>発動効果 - “往診用”バッグを敵に叩きつけ、自身の減少体力に応じた追加ダメージを与える。対象をキルした場合はその敵を弾き飛ばし、接触した敵にダメージを与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> <physicalDamage>攻撃力が{{ passivebonusad }}</physicalDamage>増加する。<br /><br /><spellActive>発動効果:</spellActive> “往診用”バッグを激しく叩きつけ、次の通常攻撃が<physicalDamage>{{ additionaldamage }}の物理ダメージ</physicalDamage>を追加で与える。このダメージは自身の減少体力に応じて最大{{ maxdamageamptooltip }}まで増加する。対象をキルした場合はその敵を弾き飛ばし、接触した敵に<physicalDamage>{{ additionaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "基本ダメージ", "体力コスト", "体力から攻撃力への変換"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ flathealthcost }} -> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}% -> {{ healthtoadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "の体力", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ flathealthcost }}の体力"}, {"id": "DrMundoR", "name": "マキシマム投与", "description": "自身に薬品を注入し、減少体力の一定割合を瞬時に回復する。さらに移動速度が増加し、長い時間をかけて最大体力の一部を自動回復する。", "tooltip": "自身に薬品を注入して、<healing>減少体力の{{ missinghealthheal*100 }}%を最大体力として</healing>獲得し、<speed>移動速度が{{ speedboostamount*100 }}%</speed>増加する。また、{{ duration }}秒かけて<healing>最大体力の{{ maxhealthhot*100 }}%</healing>を自動回復する。<br /><br />スキルレベル3になると、周囲の敵チャンピオン1体ごとに両方の体力回復効果が{{ bonuspernearbychampion*100 }}%増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加体力", "移動速度", "最大体力 %"], "effect": ["{{ missinghealthheal*100.000000 }}% -> {{ missinghealthhealnl*100.000000 }}%", "{{ speedboostamount*100.000000 }}% -> {{ speedboostamountnl*100.000000 }}%", "{{ maxhealthhot*100.000000 }}% -> {{ maxhealthhotnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "気ままな往診", "description": "最初に受ける移動不能効果を無効化する。その際、代わりに体力を失い、近くに薬品の入った容器を落とす。落とした容器の上を歩いて回収すると体力が回復し、このスキルのクールダウンが短縮される。<br><br>また、ドクター・ムンドは極めて高い体力自動回復能力を持っている。<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}