{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "Jax", "title": "der Großmeister der Waffen", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "Eishockey-Jax", "chromas": false}, {"id": "24002", "num": 2, "name": "<PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "24003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX-<PERSON>", "chromas": false}, {"id": "24005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24006", "num": 6, "name": "<PERSON>mp<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "24007", "num": 7, "name": "Nemesis-Jax", "chromas": true}, {"id": "24008", "num": 8, "name": "SKT T1-Jax", "chromas": false}, {"id": "24012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "24013", "num": 13, "name": "Götterstab-Jax", "chromas": false}, {"id": "24014", "num": 14, "name": "Mecha<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "24020", "num": 20, "name": "<PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "24021", "num": 21, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "24022", "num": 22, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "24032", "num": 32, "name": "Neo-PAX-Jax", "chromas": false}, {"id": "24033", "num": 33, "name": "PROJEKT: <PERSON>", "chromas": true}], "lore": "Jax ist in seinem Umgang mit einzigartigen Waffen sowie bissigem Sarkasmus unerreicht und außerdem der letzte bekannte Waffenmeister von Icathia. Nachdem die Bewohner seiner Heimat in einem Anflug von Selbstüberschätzung die Leere entfesselt und ihr Land damit zerstört hatten, schworen sich Jax und die anderen seiner Art, die Überreste zu schützen. Jetzt erhebt sich erneut die Magie in dieser Welt und so auch die schlummernde Gefahr, so dass Jax mit dem letzten Licht Icathias Valoran durchstreift. Alle Krieger, die er auf seinem Weg trifft, unterzieht er einem Test, um zu sehen, ob sie stark genug sind, neben ihm zu kämpfen.", "blurb": "Jax ist in seinem Umgang mit einzigartigen Waffen sowie bissigem Sarkasmus unerreicht und außerdem der letzte bekannte Waffenmeister von Icathia. Nachdem die Bewohner seiner Heimat in einem Anflug von Selbstüberschätzung die Leere entfesselt und ihr...", "allytips": ["Jax kann mit „Sprungschlag“ zu verbündeten Einheiten, wozu auch platzierte Augen zählen, springen. So lässt sich eine möglicherweise nötige Flucht planen.", "Jax profitiert besonders von Gegenständen mit Fähigkeitsstärke und Angriffsschaden, etwa „Guinsoos <PERSON>“ und „Hextech-Gunblade“."], "enemytips": ["<PERSON><PERSON><PERSON>, ihn nur mit kurzen Attacken anzugreifen, anstatt ihn in einen dauerhaften Nahkampf zu verwickeln. <PERSON><PERSON><PERSON> man ihn daran, fort<PERSON><PERSON><PERSON> anzu<PERSON><PERSON><PERSON><PERSON>, kann man sein Schadenspotential deutlich verringern.", "Jax' kann allen Angriffen kurzzeitig ausweichen und er betäubt danach alle nahen G<PERSON>ner, weshalb es sich auszahlen kann, hier gegebenenfalls etwas länger zu warten."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "Sprungschlag", "description": "Jax springt zu einer Einheit. Ist dies ein <PERSON>, schlägt er diesen mit seiner Waffe.", "tooltip": "Jax springt zu einer freundlichen oder gegnerischen Einheit oder einem Auge und verursacht dabei <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>, falls es sich um einen Gegner handelt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxW", "name": "Verstärkung", "description": "<PERSON> lädt seine Waffe mit Energie auf, wodurch sein nächster Angriff zusätzlichen Schaden verursachen wird.", "tooltip": "Jax lädt seine Waffe mit Energie auf. Sein nächster Angriff oder <spellName>Sprungschlag</spellName> verursacht zusätzlich <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxE", "name": "Gegenschlag", "description": "Jax' Kampfgeschick erlaubt es ihm, kurzzeitig allen Angriffen auszuweichen und dann mit einem schnellen Gegenangriff umstehende Gegner zu betäuben.", "tooltip": "Jax nimmt bis zu {{ dodgeduration }}&nbsp;Sekunden lang eine defensive Haltung ein, wodurch er allen eingehenden Angriffen ausweicht und durch Fähigkeiten mit Flächenwirkung {{ aoedamagereduction }}&nbsp;% weniger Schaden erleidet. Nach {{ dodgeduration }}&nbsp;Sekunden oder wenn die Fähigkeit <recast>reaktiviert</recast> wird, fügt <PERSON> in der Nähe <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ totaldamage }} + {{ percenthealthdamage }}&nbsp;% des maximalen Lebens zu und <status>betäubt</status> sie {{ stunduration }}&nbsp;Sekunden lang. <br /><br />Der Schaden erhöht sich um {{ percentincreasedperdodge*100 }}&nbsp;% pro ausgewichenem Angriff, bis zu einem Maximum von <magicDamage>{{ maxdamage }} + {{ maxpercenthealthdamage }}&nbsp;% des maximalen Lebens</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxR", "name": "Großmeister der Waffen", "description": "Jeder dritte aufeinanderfolgende Angriff verursacht zusätzlichen magischen Schaden. Jax kann diese Fähigkeit zudem aktivieren, um Schaden um sich herum zu verursachen und seine Entschlossenheit zu stärken, wodurch er kurzzeitig seine Rüstung und seine Magieresistenz erhöht.", "tooltip": "<spellPassive>Passiv:</spellPassive> Jeder dritte Angriff innerhalb von {{ passivefallofftime }}&nbsp;Sekunden verursacht zusätzlich <magicDamage>{{ onhitdamage }}&nbsp;magischen <PERSON>haden</magicDamage>.<br /><br /><spellActive>Aktiv:</spellActive> Jax schmettert seine Laterne zu Boden und fügt <PERSON> in der Nähe <magicDamage>{{ swingdamagetotal }}&nbsp;magischen Schaden</magicDamage> zu. Wenn er einen Champion trifft, erhält er {{ duration }}&nbsp;Sekunden lang <scaleArmor>{{ basearmor }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ basemr }}&nbsp;Magieresistenz</scaleMR> sowie zusätzlich <scaleArmor>{{ bonusarmor }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ bonusmr }}&nbsp;Magieresistenz</scaleMR> pro getroffenem Champion. Während dieses Zeitfensters verursacht jeder zweite (statt jeder dritte) Angriff zusätzlichen <magicDamage>magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Sc<PERSON>en (Passiv)", "Schaden (Aktiv)", "Grundwert für Rüstung", "Grundwert für Magieresistenz", "Rüstung pro zusätzlichem Champion", "Magieresistenz pro zusätzlichem Champion", "Abklingzeit"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ swingdamagebase }} -> {{ swingdamagebaseNL }}", "{{ baseresists }} -> {{ baseresistsNL }}", "{{ baseresists*0.600000 }} -> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }} -> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }} -> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Unerbittlicher Angriff", "description": "Jax' aufeinander folgende normale Angriffe erhöhen sein Angriffstempo immer weiter.", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}