{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tristana": {"id": "<PERSON><PERSON>", "key": "18", "name": "Тристана", "title": "Йордлский канонир", "image": {"full": "Tristana.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "18000", "num": 0, "name": "default", "chromas": false}, {"id": "18001", "num": 1, "name": "Riot Тристана", "chromas": false}, {"id": "18002", "num": 2, "name": "Усердный эльф Тристана", "chromas": false}, {"id": "18003", "num": 3, "name": "Пожарный Тристана", "chromas": false}, {"id": "18004", "num": 4, "name": "Партизанка Тристана", "chromas": false}, {"id": "18005", "num": 5, "name": "Морская разбойница Тристана", "chromas": false}, {"id": "18006", "num": 6, "name": "Девушка-ракета Тристана", "chromas": true}, {"id": "18010", "num": 10, "name": "Укротительница драконов Тристана", "chromas": true}, {"id": "18011", "num": 11, "name": "Ведьма Тристана", "chromas": false}, {"id": "18012", "num": 12, "name": "Тристана из отряда ''Омега''", "chromas": true}, {"id": "18024", "num": 24, "name": "Демоненок Тристана", "chromas": true}, {"id": "18033", "num": 33, "name": "Тристана в костюме перьерыцаря", "chromas": true}, {"id": "18040", "num": 40, "name": "Хекстековая Тристана", "chromas": false}, {"id": "18041", "num": 41, "name": "Пиротехник Тристана", "chromas": true}, {"id": "18051", "num": 51, "name": "Дух цветения Тристана", "chromas": true}, {"id": "18061", "num": 61, "name": "Тристана из Королевства фей", "chromas": true}], "lore": "Многие йордлы направляют свою энергию на открытия, изобретения или обычное озорство, но Тристану всегда вдохновляли свершения великих воинов. Она много слышала о Рунтерре, ее народах и войнах и считает, что ее собственная раса тоже достойна быть воспетой в легендах. Отправляясь навстречу приключениям в первый раз, она взяла с собой верную пушку Бахалку и теперь рвется в бой с непоколебимым мужеством и оптимизмом.", "blurb": "Многие йордлы направляют свою энергию на открытия, изобретения или обычное озорство, но Тристану всегда вдохновляли свершения великих воинов. Она много слышала о Рунтерре, ее народах и войнах и считает, что ее собственная раса тоже достойна быть...", "allytips": ["Огромная пушка позволяет Тристане расстреливать врагов с большого расстояния. Используйте это, чтобы они не могли даже прикоснуться к вам.", "Используйте Ракетный прыжок после того, как несколько раз поразите врага Разрывным снарядом, чтобы прикончить его, нанеся огромный урон.", "Используйте Беглый огонь, чтобы накапливать заряды Разрывного снаряда на вражеских чемпионах."], "enemytips": ["Если вы видите, что Тристана активирует в бою Беглый огонь, оглушите ее и постарайтесь отойти до тех пор, пока время действия эффекта не завершится.", "Стойте в стороне от ваших миньонов, будучи на линии, чтобы получать меньше сопутствующего урона от Разрывного снаряда."], "tags": ["Marksman", "Assassin"], "partype": "Мана", "info": {"attack": 9, "defense": 3, "magic": 5, "difficulty": 4}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 300, "mpperlevel": 32, "movespeed": 325, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.5, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 1.5, "attackspeed": 0.656}, "spells": [{"id": "TristanaQ", "name": "Беглый огонь", "description": "Тристана готовится открыть беглый огонь, увеличивая свою скорость атаки на короткое время.", "tooltip": "Тристана увеличивает свою <attackSpeed>скорость атаки на {{ attackspeedmod*100 }}%</attackSpeed> на {{ buffduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Скорость атаки", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TristanaQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TristanaW", "name": "Ракетный прыжок", "description": "Тристана делает выстрел в землю и подлетает в воздух, чтобы приземлиться в указанное место, нанеся урон противникам в зоне приземления и на некоторое время замедлив их.", "tooltip": "Тристана запускает себя в воздух, а после приземления наносит врагам <magicDamage>{{ landingdamage }} магического урона</magicDamage> и <status>замедляет</status> их на {{ slowmod*-100 }}% на {{ slowduration }} сек.<br /><br />Перезарядка этого умения сбрасывается, когда Тристана участвует в убийстве чемпиона или взрывает бомбу от умения <spellName>Разрывной снаряд</spellName> на чемпионе с максимумом зарядов.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TristanaW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TristanaE", "name": "Разрывной снаряд", "description": "Когда Тристана убивает противника, ее пушечные ядра взрываются шрапнелью, нанося урон окружающим врагам. При активации умения Тристана прикрепляет к цели бомбу, которая взрывается через короткий промежуток времени, нанося урон окружающим врагам.", "tooltip": "<spellPassive>Пассивно:</spellPassive> автоатаки Тристаны, убивающие цель, наносят <magicDamage>{{ passivedamage }} магического урона</magicDamage> окружающим врагам.<br /><br /><spellActive>Активно:</spellActive> Тристана бросает на врага или башню бомбу, которая наносит <physicalDamage>{{ activedamage }} физического урона</physicalDamage> окружающим врагам через {{ activeduration }} сек. Этот урон увеличивается на {{ critchanceamp*100 }}% от шанса критического удара и на {{ activeperstackamp*100 }}% каждый раз, когда Тристана поражает цель автоатакой или умением (максимум зарядов - 4).<br /><br />Если накапливается {{ activemaxstacks }} заряда, бомба взрывается немедленно (умение наносит не более <physicalDamage>{{ activemaxdamage }} физического урона</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон при взрыве противника", "Урон от разрывного заряда", "Коэффициент урона от силы атаки", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ activebadratio*100.000000 }}% -> {{ activebadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TristanaR", "name": "Вышибной заряд", "description": "Тристана выпускает в противника огромное пушечное ядро, которое наносит магический урон, отбрасывает цель назад и удваивает радиус поражения при взрыве Разрывного снаряда.", "tooltip": "Тристана выстреливает большим пушечным ядром, нанося цели <magicDamage>{{ damagecalc }} магического урона</magicDamage>, а также <status>отбрасывая</status> и <status>оглушая</status> ее и окружающих врагов на {{ stunduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Дальность отбрасывания", "Продолжительность оглушения:"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ knockbackdistance }} -> {{ knockbackdistanceNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Прицеливание", "description": "С каждым уровнем Тристана увеличивает дальность атаки.", "image": {"full": "Tristana_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}