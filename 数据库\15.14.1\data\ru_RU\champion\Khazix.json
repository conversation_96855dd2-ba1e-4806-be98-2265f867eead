{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Khazix": {"id": "Khazix", "key": "121", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Хищник из Бездны", "image": {"full": "Khazix.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "121000", "num": 0, "name": "default", "chromas": false}, {"id": "121001", "num": 1, "name": "Механический Ка'Зикс", "chromas": true}, {"id": "121002", "num": 2, "name": "Страж песков Ка'Зикс", "chromas": false}, {"id": "121003", "num": 3, "name": "Ка'Зикс Цветок Смерти", "chromas": false}, {"id": "121004", "num": 4, "name": "Ка'Зикс Темная Звезда", "chromas": false}, {"id": "121011", "num": 11, "name": "Ка'Зикс Чемпионата мира 2018", "chromas": true}, {"id": "121060", "num": 60, "name": "Ка'Зикс из Одиссеи", "chromas": false}, {"id": "121069", "num": 69, "name": "Лунный страж Ка'Зикс", "chromas": false}, {"id": "121079", "num": 79, "name": "Кристаллический мучитель Ка'Зикс", "chromas": false}], "lore": "Бездна растет и приспосабливается, и ни одно из ее бесчисленных порождений не подтверждает это лучше, чем Ка'Зикс. Этот ужасный мутант – результат эволюции, он рожден, чтобы выживать и истреблять сильнейших. Если ему это не удается, он развивает свои способности и находит более эффективные способы убивать добычу. Изначально Ка'Зикс был безмозглым зверем, однако со временем он усовершенствовал не только свое тело, но и интеллект. Теперь это существо тщательно планирует охоту и умело использует страх, который вызывает у своих жертв.", "blurb": "Бездна растет и приспосабливается, и ни одно из ее бесчисленных порождений не подтверждает это лучше, чем Ка'Зикс. Этот ужасный мутант – результат эволюции, он рожден, чтобы выживать и истреблять сильнейших. Если ему это не удается, он развивает свои...", "allytips": ["Враги считаются изолированными, если вблизи нет союзников. Вкус страха наносит таким целям больше урона.", "Невидимая угроза активируется, когда чемпиона Ка'Зикса не видит вражеская команда. Восстанавливается при помощи кустов или Атаки из бездны. Не забывайте применять Невидимую угрозу, автоатакуя вражеских чемпионов. ", "У Ка'Зикса большая свобода выбора, где и когда сражаться. Выбирайте сражения тщательно, чтобы добиться успеха."], "enemytips": ["Вкус страха наносит дополнительный урон изолированным целям. Получите преимущество, сражаясь вблизи союзных миньонов, чемпионов или башен.", "У Прыжка и Атаки из бездны долгая перезарядка. Ка'Зикс очень уязвим, когда они не доступны."], "tags": ["Assassin"], "partype": "Мана", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 6}, "stats": {"hp": 643, "hpperlevel": 99, "mp": 327, "mpperlevel": 40, "movespeed": 350, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.59, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.668}, "spells": [{"id": "KhazixQ", "name": "Вкус страха", "description": "Наносит физический урон цели. Урон увеличивается против <font color='#FFF673'>изолированных</font> врагов. Если развиты <font color='#00DD33'>Когти Жнеца</font>, перезарядка умения сокращается при атаке <font color='#FFF673'>изолированной</font> цели. Дальность атаки Ка'Зикса и дальность поражения Вкуса страха увеличены.", "tooltip": "Ка'Зикс рассекает врага поблизости, нанося <physicalDamage>{{ spell.khazixq:basedamage }} физического урона</physicalDamage>. Если цель <keywordMajor>изолирована</keywordMajor>, урон увеличивается до <physicalDamage>{{ spell.khazixq:isodamage }}</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [80, 105, 130, 155, 180], [110, 110, 110, 110, 110], [50, 50, 50, 50, 50], [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/105/130/155/180", "110", "50", "45", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "KhazixQ.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KhazixW", "name": "Шипы Бездны", "description": "Ка'Зикс стреляет шипами, которые взрываются при попадании, нанося физический урон всем пораженным врагам. Если взрыв задевает самого Ка'Зикса, он восстанавливает себе здоровье. Если развиты <font color='#00DD33'>Шипованные наросты</font>, Ка'Зикс стреляет тремя группами шипов веером, при этом замедляя пораженных врагов и раскрывая вражеских чемпионов на 2 сек. Замедление усиливается против <font color='#FFF673'>изолированных</font> целей.", "tooltip": "Ка'Зикс выпускает шип, нанося <physicalDamage>{{ basedamage }} физического урона</physicalDamage> первому пораженному врагу в небольшой области. Если Ка'Зикс находится в этой области, он восстанавливает себе <healing>{{ healamount }} здоровья</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Лечение", "Стоимость – @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [55, 75, 95, 115, 135], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "55/75/95/115/135", "40", "2", "0", "2", "60", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KhazixW.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KhazixE", "name": "Прыжок", "description": "Ка'Зикс прыгает в указанную область, нанося физический урон всем врагам вокруг при приземлении. Если у него развиты <font color='#00DD33'>Крылья</font>, дальность прыжка увеличивается на 200, и время перезарядки умения обнуляется при убийстве вражеского чемпиона или содействии.", "tooltip": "Ка'Зикс прыгает в выбранное место, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> при приземлении.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [30, 35, 40, 45, 50], [10, 10, 10, 10, 10], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "30/35/40/45/50", "10", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KhazixE.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KhazixR", "name": "Атака из Бездны", "description": "Каждый новый уровень Атаки из Бездны позволяет Ка'Зиксу развить одно из своих умений, придав ему дополнительный уникальный эффект. При активации умения Ка'Зикс становится <font color='#91d7ee'>невидимым</font>, создавая Невидимую угрозу, и его скорость передвижения увеличивается. Если развит <font color='#00DD33'>Адаптивный камуфляж</font>, продолжительность действия <font color='#91d7ee'>невидимости</font> Атаки из Бездны увеличивается, и Ка'Зикс может применить умение еще раз.", "tooltip": "<spellActive>Активно:</spellActive> Ка'Зикс становится <keywordStealth>невидимым</keywordStealth> на {{ stealthduration }} сек. и увеличивает свою <speed>скорость передвижения на {{ bonusmovementspeedpercent*100 }}%</speed>. Ка'Зикс может один раз <recast>повторно применить</recast> это умение в течение {{ recastwindow }} сек.<br /><br /><spellPassive>Пассивно:</spellPassive> при повышении уровня этого умения Ка'Зикс получает возможность <evolve>развить</evolve> одно из своих умений, получив дополнительные эффекты.<li><spellName>Вкус страха:</spellName> дальность действия этого умения и автоатак увеличена, а при применении к <keywordMajor>изолированным</keywordMajor> целям его перезарядка сокращается на {{ spell.khazixq:effect4amount }}%.<li><spellName>Шипы Бездны:</spellName> Ка'Зикс выпускает 3 шипа, которые <status>замедляют</status> на {{ spell.khazixw:effect3amount }}%. Сила замедления против <keywordMajor>изолированных</keywordMajor> целей увеличена.<li><spellName>Прыжок:</spellName> дальность увеличена, а при участии в убийстве чемпиона перезарядка этого умения сбрасывается.<li><spellName>Атака из Бездны:</spellName> продолжительность <keywordStealth>невидимости</keywordStealth> увеличена на {{ evolvedstealthduration }} сек., и Ка'Зикс может второй раз <recast>повторно применить</recast> это умение.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Возможности развития", "Перезарядка"], "effect": ["{{ evolutionsavailable }} -> {{ evolutionsavailableNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "KhazixR.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Невидимая угроза", "description": "Ближайшие враги, <font color='#FFF673'>изолированные</font> от своих союзников, помечаются. Умения Ка'Зикса усиливаются при охоте на <font color='#FFF673'>изолированных</font> врагов.<br><br>Когда вражеская команда не видит Ка'Зикса, он создает Невидимую угрозу и при следующей автоатаке против вражеского чемпиона дополнительно наносит ему магический урон и уменьшает его скорость передвижения на несколько секунд.", "image": {"full": "Khazix_P.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}