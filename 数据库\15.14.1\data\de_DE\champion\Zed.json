{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zed": {"id": "<PERSON><PERSON>", "key": "238", "name": "<PERSON><PERSON>", "title": "der Meister der Schatten", "image": {"full": "Zed.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "238000", "num": 0, "name": "default", "chromas": false}, {"id": "238001", "num": 1, "name": "Schockklingen-Zed", "chromas": true}, {"id": "238002", "num": 2, "name": "SKT T1-Zed", "chromas": true}, {"id": "238003", "num": 3, "name": "PROJEKT: <PERSON><PERSON>", "chromas": true}, {"id": "238010", "num": 10, "name": "WM 2016-Zed", "chromas": true}, {"id": "238011", "num": 11, "name": "Todesschwur-Zed", "chromas": false}, {"id": "238013", "num": 13, "name": "Galaktischer Zerstörer Zed", "chromas": true}, {"id": "238015", "num": 15, "name": "PsyOps-Zed", "chromas": true}, {"id": "238030", "num": 30, "name": "PROJEKT: <PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "238031", "num": 31, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "238038", "num": 38, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "238049", "num": 49, "name": "Reise der Unsterblichen-Zed", "chromas": true}, {"id": "238058", "num": 58, "name": "Blutmond-Zed", "chromas": true}, {"id": "238068", "num": 68, "name": "Galaktischer Quanten-Zerstörer Zed", "chromas": false}, {"id": "238069", "num": 69, "name": "Seelenblumen-Zed (Prestige)", "chromas": true}], "lore": "Zed ist gänzlich skrupellos sowie ohne Sinn für Gnade und außerdem der Anführer des Ordens der Schatten, einer Organisation, die er zur Militarisierung von Ionias <PERSON>- und Magiekünsten gründete, um die noxianischen Invasoren zurückzuschlagen. Während des Krieges führte ihn seine Verzweiflung zu den Geheimnissen der Schattenform – einer heimtückischen Geistermagie, so gefährlich und verdorben wie mächtig. Zed hat all die verbotenen Techniken erlernt, um jeden zu zerstören, der eine Gefahr für seine Nation oder seinen neuen Orden darstellt.", "blurb": "Zed ist gänzlich skrupellos sowie ohne Sinn für Gnade und außerdem der Anführer des Ordens der Schatten, einer Organisation, die er zur Militarisierung von Ionias <PERSON>- und Magiekünsten gründete, um die noxianischen Invasoren zurückzuschlagen. Während...", "allytips": ["Du kannst den Schadensausstoß von „Zeichen des Todes“ maximieren, indem du Energie und Abklingzeiten aufsparst, bis du deine ultimative Fähigkeit einsetzt.", "<PERSON>n du „Lebender Schatten“ schnell doppelt ank<PERSON>st, wird <PERSON><PERSON> so<PERSON> zu seinem Schatten teleportiert, wodurch du schnell flüchten kannst.", "<PERSON><PERSON> du „Lebender Schatten“ vor „Zeichen des Todes“ einsetzt, kann sich Zed sicher aus dem Kampf entfernen."], "enemytips": ["Zed erhält einen zusätzlichen Effekt durch Gegenstände mit Angriffsschaden, deshalb ist Rüstung äußerst effektiv gegen ihn.", "Zed ist am verwundbarsten, nachdem er „Lebender Schatten“ eingesetzt hat, da sein <PERSON>, seine Verlangsamung und seine Beweglichkeit davon abhängen.", "Zeds „Schattenschlag“ verlangsamt dich nur, wenn dich sein Schatten damit trifft."], "tags": ["Assassin"], "partype": "Energie", "info": {"attack": 9, "defense": 2, "magic": 1, "difficulty": 7}, "stats": {"hp": 654, "hpperlevel": 99, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 4.7, "spellblock": 29, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.65, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3.4, "attackspeedperlevel": 3.3, "attackspeed": 0.651}, "spells": [{"id": "ZedQ", "name": "Klingen-Shuriken", "description": "<PERSON><PERSON> und seine Schatten werfen ihre Shuriken.<br><br><PERSON>er Shuriken verursacht Schaden an allen getroffenen Gegnern.", "tooltip": "Z<PERSON> und seine <keywordMajor><PERSON><PERSON><PERSON></keywordMajor> werfen ihre Shuriken und verursachen jeweils <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> am ersten getroffenen Gegner und <physicalDamage>{{ passthroughdamage }}&nbsp;normalen Schaden</physicalDamage> an allen weiteren getroffenen Gegnern.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [75, 70, 65, 60, 55], "costBurn": "75/70/65/60/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZedQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZedW", "name": "<PERSON><PERSON><PERSON>", "description": "<font color='#FF9900'>Passiv: </font><PERSON><PERSON> und seine Schatten ein Ziel mit der gleichen Fähigkeit treffen, erhält Zed Energie. Dies kann nur einmal pro Fähigkeit passieren.<br><br><font color='#FF9900'>Aktiv: </font><PERSON>eds Schatten springt vorwärts und bleibt einige Sekunden lang vor Ort. Wenn er „Lebender Schatten“ reaktiviert, tauscht er die Position mit seinem Schatten.", "tooltip": "<spellPassive>Passiv:</spellPassive> Zed erhält jedes <PERSON> <keywordMajor>{{ e3 }}&nbsp;Energie</keywordMajor>, wenn er und seine <keywordMajor><PERSON><PERSON>ten</keywordMajor> einen Gegner mit derselben Fähigkeit treffen.<br /><br /><spellActive>Aktiv:</spellActive> Zeds <keywordMajor>Schatten</keywordMajor> springt nach vorne und bleibt {{ e5 }}&nbsp;Sekunden lang bestehen. Wenn er diese Fähigkeit <recast>reaktiviert</recast>, tauscht Zed die Position mit diesem <keywordMajor>Schatten</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zurückerstattete Energie", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [5.25, 5.25, 5.25, 5.25, 5.25], [0.2, 0.2, 0.2, 0.2, 0.2], [30, 35, 40, 45, 50], [5.25, 5.25, 5.25, 5.25, 5.25], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5.25", "0.2", "30/35/40/45/50", "5.25", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "ZedW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZedE", "name": "Schattenschlag", "description": "<PERSON><PERSON> und seine Schatten schlagen zu und verursachen Schaden an Gegnern in der Nähe. <PERSON><PERSON><PERSON>, die vom Schlag eines Schattens getroffen wurden, werden verlangsamt.", "tooltip": "Zed und seine <keywordMajor><PERSON><PERSON><PERSON></keywordMajor> schlagen zu und verursachen dabei <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> an nahen <PERSON>nern.<br /><br />Für jeden getroffenen gegnerischen Champion wird die Abklingzeit von <spellName>Lebender Schatten</spellName> um {{ shadowhitcdr }}&nbsp;Sekunden verringert.<br /><br />Von <keywordMajor>Schatten</keywordMajor> getroffene Gegner werden {{ slowduration }}&nbsp;Sekunden lang um {{ movespeedmod*-100 }}&nbsp;% <status>verlangsamt</status>. <PERSON><PERSON><PERSON>, die von mehreren Schlägen getroffen werden, werden um {{ movespeedmodbonus*-100 }}&nbsp;% <status>verlangsamt</status>, erleiden aber keinen zusätzlichen Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeedmod*-100.000000 }}&nbsp;% -> {{ movespeedmodnl*-100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5, 4.5, 4, 3.5, 3], "cooldownBurn": "5/4.5/4/3.5/3", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [290, 290, 290, 290, 290], "rangeBurn": "290", "image": {"full": "ZedE.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZedR", "name": "Zeichen des Todes", "description": "Zed kann nicht anvisiert werden und springt zu einem gegnerischen Champion, was diesen markiert. Nach 3 Sekunden wird das Zeichen ausgelöst und verursacht erneut einen Teil des Schadens, der dem Ziel während der Markierung von Zed zugefügt wurde.", "tooltip": "Zed kann nicht anvisiert werden, springt zu einem gegnerischen Champion und versieht ihn mit einem Zeichen. Nach {{ rdeathmarkduration }}&nbsp;Sekunden explodiert das Zeichen, verursacht <physicalDamage>{{ rcalculateddamage }}&nbsp;normalen Schaden</physicalDamage> und fügt erneut {{ rdamageamp*100 }}&nbsp;% des Schadens zu, den das Ziel durch Zed erlitten hat, während das Zeichen aktiv war.<br /><br />Der Sprung hinterlässt {{ rshadowdurationdisplayed }}&nbsp;Sekunden lang einen <keywordMajor>Schatten</keywordMajor>. Zed kann diese Fähigkeit <recast>reaktivieren</recast>, um die Position mit diesem <keywordMajor>Schatten</keywordMajor> zu tauschen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Explosionsschaden – Zeichen", "Abklingzeit"], "effect": ["{{ rdamageamp*100.000000 }}&nbsp;% -> {{ rdamageampnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "ZedR.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "<PERSON><PERSON> <PERSON>", "description": "Zeds normale Angriffe verursachen zusätzlichen magischen Schaden, wenn das Ziel über wenig Leben verfügt. Dieser Effekt kann gegen denselben gegnerischen Champion nur alle paar Sekunden ausgelöst werden.", "image": {"full": "ZedP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}