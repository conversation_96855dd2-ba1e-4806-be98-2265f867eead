{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "レナータ・グラスク", "title": "ケミテック長者", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "提督グラスク", "chromas": true}, {"id": "888010", "num": 10, "name": "恐怖の夜レナータ・グラスク", "chromas": true}, {"id": "888020", "num": 20, "name": "ラ・イルシオン レナータ・グラスク", "chromas": true}, {"id": "888021", "num": 21, "name": "プレステージ ラ・イルシオン レナータ・グラスク", "chromas": false}, {"id": "888031", "num": 31, "name": "黒薔薇の仮面レナータ・グラスク", "chromas": false}], "lore": "レナータ・グラスクは幼少期を過ごした家の焼け跡から、その名前と、両親が遺した錬金術の研究資料だけを手に再び立ち上がった。あれから数十年が過ぎた今、彼女はゾウンで最も裕福なケミ長者となった。ライバルを次々と傘下に引き入れて勢力を広げ、実業界の大物へと成り上がったのだ。レナータと手を組めば巨額の報酬が約束されるが、逆に歯向かえば後悔だけの一生が待っている。いずれにせよ、最後には誰もがレナータの側につくのだ。", "blurb": "レナータ・グラスクは幼少期を過ごした家の焼け跡から、その名前と、両親が遺した錬金術の研究資料だけを手に再び立ち上がった。あれから数十年が過ぎた今、彼女はゾウンで最も裕福なケミ長者となった。ライバルを次々と傘下に引き入れて勢力を広げ、実業界の大物へと成り上がったのだ。レナータと手を組めば巨額の報酬が約束されるが、逆に歯向かえば後悔だけの一生が待っている。いずれにせよ、最後には誰もがレナータの側につくのだ。", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "手荒い挨拶", "description": "ミサイルを発射して、最初に命中した敵にスネア効果を与える。その後、再発動してそのユニットを指定方向に投げ飛ばすことができる。", "tooltip": "腕からミサイルを発射して、最初に命中した敵に{{ rootduration }}秒間の<status>スネア効果</status>を付与し、<magicDamage>{{ totaldamage }}</magicDamage>の<magicDamage>魔法ダメージ</magicDamage>を与える。<br /><br /><recast>再発動:</recast> その敵を指定方向に<status>引き寄せて</status>投げ飛ばす。投げた対象をぶつけた敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。投げられた敵がチャンピオンだった場合は、{{ stunduration }}秒間の<status>スタン効果</status>も付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RenataW", "name": "緊急支援", "description": "味方チャンピオンにバフを付与して戦闘能力を強化し、そのデスを遅らせる。味方はキルかアシストを奪えば、デスを回避できる。", "tooltip": "味方チャンピオンにバフを付与し、<attackSpeed>攻撃速度を{{ ascalc }}</attackSpeed>、敵に向かう際の<speed>移動速度を{{ mscalc }}</speed>増加させる。この効果は{{ duration }}秒かけて徐々に増加し、最大で<attackSpeed>攻撃速度は{{ finalascalc }}</attackSpeed>、<speed>移動速度は{{ finalmscalc }}</speed>まで増加する。キルまたはアシストを奪うと、このバフの効果時間がリフレッシュされる。<br /><br />この味方は倒されると体力が最大まで回復する。この体力はその後3秒かけて徐々に減衰していく。<br /><br />体力が減衰している間に、この味方がキルまたはアシストを獲得すると、その体力が<healing>最大体力の{{ triumphpercent }}%</healing>になって、体力の減衰が止まる。<br /><br /><rules>体力が徐々に減衰している間、回復などの効果によってデスを先延ばしにすることはできるが、そのチャンピオンがキルまたはアシストを獲得しなければ、デスを完全に防ぐことはできない。また、チャンピオンは一度しかデスを先延ばしにできない。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃速度", "移動速度", "クールダウン"], "effect": ["{{ bonusattackspeed }}% -> {{ bonusattackspeedNL }}%", "{{ bonusmovespeed }}% -> {{ bonusmovespeedNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RenataE", "name": "ロイヤリティープログラム", "description": "2発のケミテックミサイルを発射する。ミサイルに触れた味方にはシールドを付与し、敵にはダメージとスロウ効果を与える。", "tooltip": "2発のケミテックミサイルを発射する。ミサイルは周囲の敵および触れた敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて{{ slowduration }}秒間、30%の<status>スロウ効果</status>を付与する。また、触れた味方には{{ shieldduration }}秒間、<shield>耐久値{{ shieldcalc }}のシールド</shield>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "シールド量", "@AbilityResourceName@コスト"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RenataR", "name": "敵対的買収", "description": "化学物質の波を送り出し、触れた敵をバーサーク状態にする。", "tooltip": "化学物質の波を送り出し、{{ berserkduration }}秒間敵を<status>バーサーク状態</status>にして、一番近くのユニットに通常攻撃させる。この際、敵は敵自身の味方を優先して通常攻撃を行う。<br /><br /><status>バーサーク状態</status>の間、敵は<attackSpeed>攻撃速度が{{ bonusattackspeed*100 }}%</attackSpeed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "効果時間"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "レバレッジ", "description": "通常攻撃が追加ダメージを与えて敵をマークする。味方はマークされた敵にダメージを与えると追加ダメージを与える。", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}