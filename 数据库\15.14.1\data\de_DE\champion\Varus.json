{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Varus": {"id": "Varus", "key": "110", "name": "Varus", "title": "der Pfeil der Vergeltung", "image": {"full": "Varus.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "110000", "num": 0, "name": "default", "chromas": false}, {"id": "110001", "num": 1, "name": "Verderbniskristall-Varus", "chromas": false}, {"id": "110002", "num": 2, "name": "Lichtbogen-Varus", "chromas": false}, {"id": "110003", "num": 3, "name": "Schneeschlag-Varus", "chromas": false}, {"id": "110004", "num": 4, "name": "Herzsucher-Varus", "chromas": false}, {"id": "110005", "num": 5, "name": "Varus <PERSON>", "chromas": false}, {"id": "110006", "num": 6, "name": "Sternenvernichter-Varus", "chromas": true}, {"id": "110007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "110009", "num": 9, "name": "Infernalischer Varus", "chromas": true}, {"id": "110016", "num": 16, "name": "PROJEKT: Varus", "chromas": true}, {"id": "110017", "num": 17, "name": "Kosmischer Jäger Varus", "chromas": true}, {"id": "110034", "num": 34, "name": "High Noon-Varus", "chromas": true}, {"id": "110044", "num": 44, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "110053", "num": 53, "name": "Elysischer Varus", "chromas": true}, {"id": "110060", "num": 60, "name": "Seelenblumen-Varus", "chromas": true}], "lore": "Varus war ein tödlicher Vollstrecker, ein Abkömmling der uralten Düsteren. Er trieb seine <PERSON> gerne in den Wahnsinn, um sie dann mit einem letzten Pfeil hinzurichten. Er kam zum Ende des Großen Düsteren Krieges in Gefangenschaft, der er nach Jahrhunderten entkommen konnte, indem er sich der verwobenen Körper zweier ionischen Jäger bemächtigte. Sie hatten Varus in ihrer Unwissenheit befreit und tragen jetzt den Bogen, der sein Wesen beherbergt. Varus sucht nun nach denjenigen, die ihn einst in Ketten legten, um seinen brutalen Rachedurst zu stillen. Die sterblichen Seelen in seinem Inneren kämpfen jedoch mit jedem Schritt gegen sein Vorhaben an.", "blurb": "Varus war ein tödlicher Vollstrecker, ein Abkömmling der uralten Düsteren. Er trieb seine Fe<PERSON> gerne in den Wahnsinn, um sie dann mit einem letzten Pfeil hinzurichten. Er kam zum Ende des Großen Düsteren Krieges in Gefangenschaft, der er nach...", "allytips": ["Schaltest du „Verdorbener Köcher“ fr<PERSON>h frei, kann dir das dabei helfen, gegnerischen Champions zuzusetzen oder Todesstöße gegen Vasallen zu erzielen.", "Bei Kämpfen auf kurze Distanz kann es manchmal besser sein, „Durchdringender Pfeil“ schnell einzusetzen, anstatt diesen erst komplett zu laden.", "Versuche die sehr große Reichweite von „Durchdringender Pfeil“ auszunutzen, um gegnerische Champions zu treffen, solange diese noch außer Reichweite sind oder wenn sie sich bereits zurückziehen."], "enemytips": ["Stehst du unter dem Einfluss von Varu<PERSON>' Verderbnis, so erleidest du zusätzlichen Schaden.", "Kann Varus einen Todesstoß erzielen oder hat er bei einem Kill geholfen, dann erhält er kurzzeitig zusätzliches Angriffstempo, was ihn noch viel gefährlicher macht.", "Du wirst an Ort und Stelle festgehalten, falls dich eine der Ranken von Varus' ultimativer Fähigkeit, „<PERSON>tte der Verderbnis“, erreicht. Du kannst dem aber entgehen, wenn du der Ranke lange genug fernbleiben kannst."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 3, "magic": 4, "difficulty": 2}, "stats": {"hp": 600, "hpperlevel": 105, "mp": 320, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.4, "attackspeedperlevel": 3.5, "attackspeed": 0.658}, "spells": [{"id": "VarusQ", "name": "Durchdringender Pfeil", "description": "Varus spannt seinen Bogen und feuert dann einen kräftigen Schuss, dessen Reichweite und Schaden mit der Vorbereitungszeit steigt.", "tooltip": "<attention>Aufladungsbeginn:</attention> Varus bereitet seinen nächsten Schuss vor und <status>verlangsamt</status> sich selbst um {{ e7 }}&nbsp;%. <PERSON><PERSON> der <PERSON> nach {{ e5 }}&nbsp;Sekunden nicht abgefeuert wird, bricht Varus die Fähigkeit ab und erhält {{ e4 }}&nbsp;% seiner Manakosten zurück.<br /><br /><attention>Aktivierung:</attention> Varus schießt den Pfeil ab und verursacht <physicalDamage>{{ totaldamagemintooltip }}&nbsp;normalen Schaden</physicalDamage>. Der Schaden wird pro getroffenem Gegner um {{ e3 }}&nbsp;% verringert (mindestens {{ e9 }}&nbsp;%). Der Schaden und die Explosionseffekte von <keywordMajor>Verderbnis</keywordMajor> erhöhen sich abhäng<PERSON> von der Aufladungszeit auf bis zu {{ maxchargeamp*100 }}&nbsp;% (maximal <physicalDamage>{{ totaldamagemax }}</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzlicher Angriffsschaden – Skalierung", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamagemax }} -> {{ basedamagemaxNL }}", "{{ tadratiomax }} -> {{ tadratiomaxNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [3, 3, 3, 3, 3], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [33, 33, 33, 33, 33], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "15", "50", "4", "3", "20", "0", "33", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VarusW", "name": "Verdorbener Köcher", "description": "Passiv: Varus' normale Angriffe verursachen zusätzlichen magischen Schaden und bewirken „Verderbnis“. Varus' andere Fähigkeiten bringen „Verderbnis“ zur Explosion und verursachen magischen Schaden abhängig vom maximalen Leben des Ziels. Aktiv: Varus verstärkt seinen nächsten „Durchdringenden Pfeil“.", "tooltip": "<spellPassive>Passiv: </spellPassive>Varus' Angriffe verursachen zusätzlich <magicDamage>{{ onhitdamage }}&nbsp;magischen Schaden</magicDamage> und fügen {{ e3 }}&nbsp;Sekunden lang eine Steigerung von <keywordMajor><PERSON>rbnis</keywordMajor> hinzu (max. {{ e4 }}&nbsp;Steigerungen).<br /><br />Varus' andere Fähigkeiten lassen die <keywordMajor>Verderbnis</keywordMajor>-Steigerungen explodieren und verursachen pro Steigerung <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ percenthpperstack }} des max. Lebens (max. <magicDamage>{{ maxpercenthpperstack }} des max. Lebens</magicDamage>). Wenn Varus' Grundfähigkeiten die <keywordMajor><PERSON>rbnis</keywordMajor>-Steigerungen auf Champions und epischen Monstern explodieren lassen, verringern sich dadurch auch ihre Abklingzeiten um {{ cdrperblightstack*100 }}&nbsp;% ihres Maximalwerts pro Steigerung.<br /><br /><spellActive>Aktiv:</spellActive> Varus' nächster <spellName>Durchdringender Pfeil</spellName> verursacht abhängig von der Aufladungszeit zusätzlichen <magicDamage>magischen Schaden</magicDamage> in Höhe von <magicDamage>{{ qempowerpercenthp }} bis {{ maxqempowerpercenthp }}</magicDamage> des fehlenden Lebens des Ziels.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> bei <PERSON>riff", "Schaden abhängig vom maximalen Leben ", "Schaden abhängig vom fehlenden Leben"], "effect": ["{{ varuswonhitdamage }} -> {{ varuswonhitdamageNL }}", "{{ basepercenthpperstack*100.000000 }}&nbsp;% -> {{ basepercenthpperstacknl*100.000000 }}&nbsp;%", "{{ wqhealthdamage*100.000000 }}&nbsp;% -> {{ wqhealthdamagenl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [40, 40, 40, 40, 40], "cooldownBurn": "40", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0.03, 0.035, 0.04, 0.045, 0.05], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [120, 120, 120, 120, 120], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0.03/0.035/0.04/0.045/0.05", "6", "3", "120", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VarusW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "VarusE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Varus feuert einen <PERSON>, der normalen Schaden verursacht und den Boden entweiht. Der entweihte Boden verringert das Lauftempo von Geg<PERSON>n und verringert deren Heilung und Regeneration.", "tooltip": "Varus feuert einen Pfeilhagel ab, der <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und den Boden {{ e3 }}&nbsp;Sekunden lang entweiht, woraufhin seine Gegner um {{ slowpercent*-100 }}&nbsp;% <status>verlangsamt</status> und mit {{ grievousamount*100 }}&nbsp;% Klaffende Wunden belegt werden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent*-100.000000 }}&nbsp;% -> {{ slowpercentnl*-100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [60, 100, 140, 180, 220], [-0.3, -0.35, -0.4, -0.45, -0.5], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/100/140/180/220", "-0.3/-0.35/-0.4/-0.45/-0.5", "4", "0", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VarusR", "name": "<PERSON><PERSON>", "description": "Varus schleudert eine Schaden verursachende Ranke der Verderbnis, die den ersten gegnerischen Champion, den sie trifft, bewegungsunfähig macht, und sich anschließend auf nicht infizierte Champions in der Nähe ausbreitet, die bei Berührung ebenfalls bewegungsunfähig werden.", "tooltip": "Varus schleudert eine Ranke der Verderbnis, die <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und den ersten getroffenen gegnerischen Champion {{ e2 }}&nbsp;Sekunden lang <status>festhält</status>. <status>Festgehaltene</status> Gegner erhalten über die Dauer {{ e4 }}&nbsp;Steigerungen von <keywordMajor>Verderbnis</keywordMajor>.<br /><br />Die Verderbnis breitet sich von ihrem Ziel auf nicht-infizierte gegnerische Champions aus. Wenn sie sie erreicht, erleiden sie denselben Schaden und werden <status>festgehalten</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [2, 2, 2], [650, 650, 650], [3, 3, 3], [0.5, 0.5, 0.5], [600, 600, 600], [1.75, 1.75, 1.75], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "2", "650", "3", "0.5", "600", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "VarusR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Lebende Vergeltung", "description": "Varus erhält bei Kills und Unterstützungen vorübergehend Angriffsschaden und Fähigkeitsstärke. Der Bonus ist größer, wenn das Ziel ein gegnerischer Champion war.", "image": {"full": "VarusPassive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}