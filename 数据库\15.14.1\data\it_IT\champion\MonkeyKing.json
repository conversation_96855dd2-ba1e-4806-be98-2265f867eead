{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MonkeyKing": {"id": "MonkeyKing", "key": "62", "name": "Wukong", "title": "il re delle scimmie", "image": {"full": "MonkeyKing.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "62000", "num": 0, "name": "default", "chromas": false}, {"id": "62001", "num": 1, "name": "Wukong Vulcanico", "chromas": false}, {"id": "62002", "num": 2, "name": "Wukong Generale", "chromas": false}, {"id": "62003", "num": 3, "name": "Wukong Drago di Giada", "chromas": true}, {"id": "62004", "num": 4, "name": "Wukong dell'Oltretomba", "chromas": false}, {"id": "62005", "num": 5, "name": "Wukong Splendente", "chromas": false}, {"id": "62006", "num": 6, "name": "Wukong Lanciere delle Nuvole", "chromas": false}, {"id": "62007", "num": 7, "name": "Wukong dell'Accademia di Battaglia", "chromas": true}, {"id": "62016", "num": 16, "name": "Wukong del Bosco Antico", "chromas": true}], "lore": "Wukong è un astuto vastaya che usa la forza, l'agilità e l'intelligenza per confondere il nemico e averne la meglio. Do<PERSON> aver trovato un amico per la vita nel guerriero noto come <PERSON> Yi, Wukong è diventato l'ultimo studente dell'antica arte marziale nota come Wuju. Armato con un bastone incantato, cerca di impedire che Ionia cada in rovina.", "blurb": "Wukong è un astuto vastaya che usa la forza, l'agilità e l'intelligenza per confondere il nemico e averne la meglio. Dopo aver trovato un amico per la vita nel guerriero noto come Master Yi, Wukong è diventato l'ultimo studente dell'antica arte marziale...", "allytips": ["Esca e Colpo della nuvola funzionano bene insieme per colpire velocemente il nemico e scappare prima che possa reagire.", "Prova a usare Esca vicino all'erba alta per fare in modo che il nemico si preoccupi inutilmente del tuo movimento."], "enemytips": ["Wukong userà spesso Esca dopo Colpo della nuvola. Aspetta a lanciare le tue abilità in modo da assicurarti di colpire il vero Wukong.", "Wukong diventa più forte quando è circondato dai nemici. Cerca di isolarlo per avere un vantaggio."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 330, "mpperlevel": 65, "movespeed": 340, "armor": 31, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.69}, "spells": [{"id": "MonkeyKingDoubleAttack", "name": "<PERSON><PERSON> de<PERSON>", "description": "Il prossimo attacco di Wukong ottiene gittata, infligge danni bonus e riduce l'armatura del nemico per alcuni secondi.", "tooltip": "Il prossimo attacco base di Wukong e del suo <keywordMajor>clone</keywordMajor> guadagna {{ attackrangebonus }} gittata extra, infligge <physicalDamage>{{ bonusdamagett }} danni fisici</physicalDamage> aggiuntivi e riduce del <scaleArmor>{{ armorshredpercent*100 }}% l'armatura</scaleArmor> del bersaglio per {{ shredduration }} secondi.<br /><br />La ricarica dell'abilità si riduce di {{ cooldowndecrease }} secondi ogni volta che Wukong o un suo <keywordMajor>clone</keywordMajor> colpiscono un nemico con un attacco o un'abilità.<br /><br /><rules>Questa abilità attiva gli effetti sull'incantesimo quando infligge danni.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Riduzione armatura %", "Gitt<PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ armorshredpercent*100.000000 }}% -> {{ armorshredpercentnl*100.000000 }}%", "{{ attackrangebonus }} -> {{ attackrangebonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 275, 300, 325, 350], "rangeBurn": "250/275/300/325/350", "image": {"full": "MonkeyKingDoubleAttack.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingDecoy", "name": "<PERSON><PERSON><PERSON><PERSON> insidioso", "description": "Wukong diventa <font color='#91d7ee'>invisibile</font> e scatta in una direzione, lasciando al suo posto un clone che attacca i nemici vicini.", "tooltip": "Wukong scatta e diventa <keywordStealth>invisibile</keywordStealth> per {{ stealthduration }} secondo/i, lasciandosi alle spalle un <keywordMajor>clone</keywordMajor> stazionario per {{ cloneduration }} secondi.<br /><br />Il <keywordMajor>clone</keywordMajor> attacca i nemici vicini che Wukong ha danneggiato di recente e imita la sua suprema, infliggendo il {{ clonedamagemod*100 }}% dei danni normali.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON> danni", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ clonedamagemod*100.000000 }}% -> {{ clonedamagemodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "MonkeyKingDecoy.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingNimbus", "name": "<PERSON><PERSON> nuvola", "description": "Wukong scatta verso il bersaglio nemico e manda immagini di se stesso ad attaccare i nemici vicino al suo bersaglio, infliggendo danni a ogni nemico colpito.", "tooltip": "Wukong si lancia verso un nemico e invia <keywordMajor>cloni</keywordMajor> che imitano lo scatto fino ad altri {{ extratargets }} avversari nelle vicinanze. Ogni nemico colpito subisce <magicDamage>{{ totaldamage }} danni magici</magicDamage>. <PERSON>i e il suo <keywordMajor>clone</keywordMajor> ottengono un <attackSpeed>{{ attackspeed*100 }}% di velocità d'attacco</attackSpeed> per {{ attackspeedduration }} secondi.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità d'attacco", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.25, 8.5, 7.75, 7], "cooldownBurn": "10/9.25/8.5/7.75/7", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MonkeyKingNimbus.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingSpinToWin", "name": "C<PERSON>lone", "description": "Wukong allunga il suo bastone e lo ruota ripetutamente attorno, ottenendo velocità di movimento.<br><br>I nemici colpiti subiscono danni e vengono lanciati in aria.", "tooltip": "Wukong guadagna <speed>{{ movespeed*100 }}% velocità di movimento</speed> e inizia a roteare il bastone, <status>lanciando in aria</status> i nemici vicini per {{ knockupduration }} secondi e infliggendo loro <physicalDamage>{{ totaldamagett }} (+{{ percenthpdamagett }} salute massima del bersaglio) danni fisici</physicalDamage> in {{ spinduration }} secondi.<br /><br />Questa abilità può essere lanciata una seconda volta entro {{ recastwindow }} secondi prima di andare in ricarica.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> salute massima", "Ricarica"], "effect": ["{{ basepercentmaxhpdmgpersec*200.000000 }}% -> {{ basepercentmaxhpdmgpersecnl*200.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [315, 315, 315], "rangeBurn": "315", "image": {"full": "MonkeyKingSpinToWin.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pelle di pietra", "description": "Wukong ottiene armatura che si accumula e rigenerazione salute massima mentre combatte contro campioni e mostri.", "image": {"full": "MonkeyKingStoneSkin.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}