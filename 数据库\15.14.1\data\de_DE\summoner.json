{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Barriere", "description": "Du erhältst kurzzeitig einen Schild.", "tooltip": "Du erhältst {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON><PERSON><PERSON>, der {{ shieldstrength }}&nbsp;<PERSON><PERSON><PERSON> absorbiert</shield>.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerBoost": {"id": "SummonerBoost", "name": "Läuterung", "description": "Entfernt alle kampfunfähig machenden Effekte (außer „Unterdrückung“ und „In der Luft“) und Beschwörerzauber-Debuffs von deinem Champion und gewährt Zähigkeit.", "tooltip": "Entfernt sämtliche Massenkontrolleffekte (mit Ausnahme von <keyword>In der Luft</keyword> und <keyword>Unterdrückung</keyword>) und Beschwörerzauber-Debuffs von dir und gewährt {{ tenacityduration }}&nbsp;Sekunden lang {{ tenacityvalue*100 }}&nbsp;% Zähigkeit.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "Blitz", "description": "Teleportiert deinen Champion über eine kurze Distanz in Richtung deines Mauszeigers.", "tooltip": "Teleportiert deinen Champion über eine kurze Distanz in Richtung deines Mauszeigers.<br /><br />Kann eine volle Runde lang nicht erneut ausgeführt werden <rules>(eine Runde besteht aus einer Kaufphase und einer Kampfphase).</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "<PERSON><PERSON><PERSON>", "description": "Du erhältst einen kurzen Lauftemposchub, der erhöht wird, während du von gegnerischen Champions wegläufst.", "tooltip": "<keywordMajor>Platz für aktive Fähigkeit:</keywordMajor> Augmentierungen, die einen Beschwörerzauber gewähren, nehmen diesen Platz ein.<br /><br />Du erhältst {{ duration }}&nbsp;Sekunden lang <moveSpeed>{{ basems*100 }}&nbsp;% Lauftempo</moveSpeed> (wird für jeden Gegner hinter dir um {{ bonusmsperenemybehind*100 }}&nbsp;% erhöht).", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerDot": {"id": "SummonerDot", "name": "Entzünden", "description": "Fügt dem anvisierten gegnerischen Champion über die Dauer hinweg absoluten Schaden zu und verringert jegliche Heileffekte.", "tooltip": "Fügt dem anvisierten gegnerischen Champion über 5&nbsp;Sekunden hinweg <trueDamage>{{ tooltiptruedamagecalculation }}&nbsp;absoluten Schaden</trueDamage> zu und belegt ihn für die Dauer mit <keyword>{{ grievousamount*100 }}&nbsp;% Klaffenden Wunden</keyword>.<br /><br /><keyword>Wunden</keyword>: Verringert die Effektivität von Heil- und Regenerationseffekten.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Verlangsamt den anvisierten gegnerischen Champion und verringert dessen verursachten Schaden.", "tooltip": "<keyword>Verlangsamt</keyword> den anvisierten gegnerischen Champion {{ debuffduration }}&nbsp;Sekunden lang um {{ slow }}&nbsp;% und verringert dessen verursachten Schaden um {{ damagereduction }}&nbsp;%.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerFlash": {"id": "SummonerFlash", "name": "Blitz", "description": "Teleportiert dich über eine kurze Distanz in Richtung deines Mauszeigers.", "tooltip": "Teleportiert dich über eine kurze Distanz in Richtung deines Mauszeigers.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerHaste": {"id": "SummonerHaste", "name": "<PERSON><PERSON><PERSON>", "description": "Du erhältst Lauftempo und ignorierst während der Wirkdauer Kollisionen mit Einheiten.", "tooltip": "Du erhältst {{ duration }}&nbsp;Sekunden lang <speed>{{ movespeedmod }}&nbsp;Lauftempo</speed> und <keyword>Geist</keyword>.<br /><br /><keyword>Geist</keyword>: Du ignorierst Zusammenstöße mit anderen Einheiten.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerHeal": {"id": "SummonerHeal", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> Leben wieder her und gewährt dir und einem anvisierten verbündeten Champion Lauftempo.", "tooltip": "Stellt <healing>{{ totalheal }}&nbsp;<PERSON><PERSON></healing> wieder her und gewährt dir und einem anvisierten verbündeten Champion {{ movespeedduration }}&nbsp;Sekunde lang <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed>.<br /><br /><rules>Falls diese Fähigkeit kein Ziel anvisiert, wird sie auf den am schwersten verwundeten verbündeten Champion in Reichweite angewendet.<br />Die Heilung ist halbiert bei Einheiten, die vor kurzem von dem Beschwörerzauber „Heilen“ betroffen waren.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "Klarheit", "description": "<PERSON><PERSON><PERSON> dein Mana und das Mana des verbündeten Champions wieder her.", "tooltip": "Regeneriert {{ e1 }}&nbsp;% des max. Manas deines Champions und {{ e2 }}&nbsp;% des maximalen Manas naher Verbündeter.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "Zum Kö<PERSON>g!", "description": "<PERSON><PERSON><PERSON> dich schnell an die Seite des Porokönigs.", "tooltip": "<span class=\"colorFFE076\">Passiv:</span> <PERSON><PERSON> du einen gegnerischen Champion mit einem Poro triffst, erhält dein Team 1&nbsp;Porozeichen. Bei 10&nbsp;Porozeichen beschwört dein Team den Porokönig, der an eurer Seite kämpft. Solange der Porokönig aktiv ist, erhält keines der Teams weitere Porozeichen.<br /><br /><span class=\"colorFFE076\">Aktiv:</span> Begib dich schnell an die Seite des Porokönigs. Kann nur ausgeführt werden, solange der Porokönig an deiner Seite deines Teams kämpft. <br /><br /><i><span class=\"colorFDD017\">„Poros berühren das Herz. Alle anderen sind nur zum Spaß hier.“</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "Poroschleudern", "description": "Schleudere einen Poro auf deine Gegner. Sollte er treffen, kannst du anschließend schnell zu deinem Ziel kommen.", "tooltip": "Schleudert einen Poro über eine lange Strecke, der {{ f2 }}&nbsp;absoluten Schaden an der 1.&nbsp;getroffenen gegnerischen Einheit verursacht und <span class=\"coloree91d7\">absolute Sicht</span> auf das getroffene Ziel gewährt.<br /><br />Diese Fähigkeit kann 3&nbsp;Sekunden lang erneut ausgeführt werden, um zum getroffenen Ziel zu springen. Dadurch wird zusätzlich {{ f2 }}&nbsp;absoluter Schaden verursacht und die Abklingzeit des nächsten „Poroschleudern“ um {{ e4 }}&nbsp;Sekunden verringert.<br /><br /><PERSON><PERSON> können weder von Zauberschilden noch Windmauern aufgehalten werden, da sie Tiere sind und keine Zauber!<br /><br /><i><span class=\"colorFDD017\">„Poros sind ein Musterbeispiel für Aerodynamik in Runeterra.“</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerSmite": {"id": "SummonerSmite", "name": "Zerschmettern", "description": "<PERSON><PERSON>gt einem Monster oder Vasallen absoluten Schaden zu.", "tooltip": "<PERSON>ügt anvisierten großen Monstern oder einem Lane-Vasallen <trueDamage>{{ smitebasedamage }}&nbsp;absoluten Schaden</trueDamage> zu.<br /><br /><PERSON>ü<PERSON>-Begleitern <trueDamage>{{ firstpvpdamage }}&nbsp;absoluten Schaden</trueDamage> zu.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "<PERSON><PERSON>", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "<PERSON><PERSON><PERSON>", "description": "Wirft in gerader Linie einen Schneeball auf deine Gegner. Wird ein Gegner getroffen, wird dieser markiert, und du erhältst absolute Sicht. Dein Champion kann sich daraufhin zügig zum markierten Ziel begeben.", "tooltip": "Wirft über weite Entfernung einen Schneeball, der der ersten getroffenen gegnerischen Einheit {{ tooltipdamagetotal }}&nbsp;absoluten Schaden zufügt und <span class=\"coloree91d7\">absolute Sicht</span> auf das Ziel gewährt. Wird ein Gegner getroffen, kann diese Fähigkeit binnen {{ e3 }}&nbsp;Sekunden erneut ausgeführt werden, um zur markierten Einheit zu sprinten und ihr zusätzlich {{ tooltipdamagetotal }}&nbsp;absoluten Schaden zuzufügen. Der Sprint zum Ziel verringert die Abklingzeit von „Markieren“ um {{ e4 }}&nbsp;%.<br /><br /><span class=\"colorFFFF00\">Schneebälle werden nicht von Zauberschilden oder Geschosse abwehrenden Effekten aufgehalten.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "<PERSON><PERSON><PERSON>", "description": "Wirft in gerader Linie einen Schneeball auf deine Gegner. Wird ein Gegner getroffen, wird dieser markiert, und du erhältst absolute Sicht. Dein Champion kann sich daraufhin zügig zum markierten Ziel begeben.", "tooltip": "Wirft über weite Entfernung einen Schneeball, der der ersten getroffenen gegnerischen Einheit {{ tooltipdamagetotal }}&nbsp;absoluten Schaden zufügt und <span class=\"coloree91d7\">absolute Sicht</span> auf das Ziel gewährt. Wird ein Gegner getroffen, kann diese Fähigkeit binnen {{ e3 }}&nbsp;Sekunden erneut ausgeführt werden, um zur markierten Einheit zu sprinten und ihr zusätzlich {{ tooltipdamagetotal }}&nbsp;absoluten Schaden zuzufügen. Der Sprint zum Ziel verringert die Abklingzeit von „Markieren“ um {{ e4 }}&nbsp;%.<br /><br /><span class=\"colorFFFF00\">Schneebälle werden nicht von Zauberschilden oder Geschosse abwehrenden Effekten aufgehalten.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "Teleportation", "description": "Nach kurzer Kanalisierung kannst du nicht länger anvisiert werden und begibst dich zu einer verbündeten Einheit. Wird zu „Unbegrenzte Teleportation“ aufgewertet, wodurch du dich wesentlich schneller fortbewegen kannst. ", "tooltip": "Du kannst nach einer Kanalisierungszeit von {{ channelduration }}&nbsp;Sekunden <keyword>nicht anvisiert werden</keyword> und bewegst dich zu einem verbündeten Gebäude, einem verbündeten Vasallen oder Auge. <br /><br />Wird nach {{ upgrademinute }} <PERSON><PERSON><PERSON> zu „Unbegrenzte Teleportation“ aufgewertet, wodurch dein Reisetempo erheblich erhöht wird.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "Leerer Platz", "description": "Dieser Platz wird durch die ultimative Fähigkeit eines anderen Champions ersetzt, die zu Spielbeginn gewählt wurde. Du hast 30 Sekunden Zeit für deine Wahl. Sei vorbereitet!", "tooltip": "Wird von deinem gewählten ultimativen Beschwörerzauber eingenommen.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "Platzhalter und „Angriff-Zerschmettern“", "description": "Dieser Platz wird durch die ultimative Fähigkeit eines anderen Champions ersetzt und du erhältst „Angriff-Zerschmettern“. Du hast 30 Sekunden Zeit für deine Wahl. Sei vorbereitet!", "tooltip": "Wird von deinem ultimativen Beschwörerzauber eingenommen.<br /><br /><PERSON>t „Angriff-Zerschmettern“. „Angriff-Zerschmettern“ exekutiert verbündete Buff-Monster, epische Monster und Kluftkrabbler, wenn du sie angreifst.<br /><br /><attention>„Angriff-Zerschmettern“ hat keine Abklingzeit.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}