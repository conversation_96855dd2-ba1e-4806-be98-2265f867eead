{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Singed": {"id": "Singed", "key": "27", "name": "Singed", "title": "il folle chimico", "image": {"full": "Singed.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "27000", "num": 0, "name": "default", "chromas": false}, {"id": "27001", "num": 1, "name": "Riot Singed", "chromas": false}, {"id": "27002", "num": 2, "name": "Singed Hextech", "chromas": false}, {"id": "27003", "num": 3, "name": "<PERSON>ed <PERSON>", "chromas": false}, {"id": "27004", "num": 4, "name": "Singed <PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "27005", "num": 5, "name": "<PERSON>ed <PERSON>", "chromas": false}, {"id": "27006", "num": 6, "name": "Singed della Nevicata", "chromas": false}, {"id": "27007", "num": 7, "name": "Singed SSW", "chromas": false}, {"id": "27008", "num": 8, "name": "Singed <PERSON> oscura", "chromas": false}, {"id": "27009", "num": 9, "name": "Singed Apicultore", "chromas": false}, {"id": "27010", "num": 10, "name": "Singed della Resistenza", "chromas": true}, {"id": "27019", "num": 19, "name": "Singed <PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "27028", "num": 28, "name": "Singed Arcane: Laboratorio di Shimmer", "chromas": false}], "lore": "Singed è un brillante alchimista dalla dubbia moralità, i cui esperimenti farebbero rivoltare lo stomaco persino ai criminali più spietati. Mette le sue abilità al servizio del miglior offerente e non si cura affatto del modo in cui i suoi miscugli nocivi vengono utilizzati, vedendo anzi nel caos solo l'ennesimo esperimento. Il suo più celebre composto è lo \"Shimmer\", che ha permesso ai baroni chimici di trasformare Zaun nel loro parco giochi personale. Tuttavia, Singed è costantemente al lavoro su composti nuovi e sempre più immorali...", "blurb": "Singed è un brillante alchimista dalla dubbia moralità, i cui esperimenti farebbero rivoltare lo stomaco persino ai criminali più spietati. Mette le sue abilità al servizio del miglior offerente e non si cura affatto del modo in cui i suoi miscugli...", "allytips": ["Scia velenosa è utile sia per finire i minion sia per aggredire gli avversari, e permette a Singed di dominare qualunque corsia nella quale si sia posizionato.", "Usa la Pozione della pazzia per ingannare i tuoi nemici e farti inseguire nella tua Scia velenosa.", "Scagliare i nemici verso la tua torre significa causargli ingenti quantità di danni."], "enemytips": ["Tieniti a distanza per evitare di essere Scagliato indietro verso gli alleati di Singed.", "Singed deve correre vicino alla tua squadra per essere efficace. Prova a prendere l'iniziativa usando controlli su di lui mentre attacchi i suoi alleati.", "Fai attenzione quando insegui Singed. È difficile da abbattere e può lasciare dietro di sé la sua Scia velenosa per danneggiarti mentre lo stai inseguendo."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 7, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 96, "mp": 330, "mpperlevel": 45, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9.5, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.9, "attackspeed": 0.7}, "spells": [{"id": "PoisonTrail", "name": "<PERSON><PERSON> velenosa", "description": "<PERSON>cia una scia di veleno dietro a <PERSON>ed, che infligge danni ai nemici sulla traiettoria.", "tooltip": "<toggle>Attiva/disattiva:</toggle> Singed rilascia una scia di veleno che infligge <magicDamage>{{ damagepersecond }} danni magici</magicDamage> al secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [13, 13, 13, 13, 13], "costBurn": "13", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana al secondo", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "PoisonTrail.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} mana al secondo"}, {"id": "MegaAdhesive", "name": "Mega adesivo", "description": "Lancia una fiala di mega adesivo sul terreno, rallentando e ancorando i nemici che ci camminano sopra.", "tooltip": "Singed lancia un barile di liquido appiccicoso, <status>ancorando</status> e <status>rallentando</status> i nemici nell'area di un {{ slowpercent }}% per {{ wduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "MegaAdhesive.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Fling", "name": "Scagliamento", "description": "Danneggia l'unità nemica bersaglio e la lancia in aria dietro a Singed. Se il bersaglio di Singed atterra nel suo Mega adesivo viene anche immobilizzato.", "tooltip": "Singed scaglia il nemico alle sue spalle, infliggendo <magicDamage>{{ basedamage }} più  {{ e3 }}% della salute massima del bersaglio in danni magici</magicDamage>.<br /><br />Se lancia un bersaglio nel suo <spellName>Mega adesivo</spellName>, il nemico resta <status>immobilizzato</status> per {{ e2 }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> salute massima", "Durata immobilizzazione", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [50, 60, 70, 80, 90], [1, 1.25, 1.5, 1.75, 2], [6, 6.5, 7, 7.5, 8], [420, 420, 420, 420, 420], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/60/70/80/90", "1/1.25/1.5/1.75/2", "6/6.5/7/7.5/8", "420", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "Fling.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "InsanityPotion", "name": "Pozione della pazzia", "description": "Singed beve un potente intruglio di sostanze chimiche, che migliora notevolmente le sue statistiche di attacco e permette a Scia velenosa di applicare Ferita grave.", "tooltip": "Singed beve un potente intruglio di sostanze chimiche, che gli conferisce {{ statamount }} <scaleAP>potere magico</scaleAP>, <scaleArmor>armatura</scaleArmor>, <scaleMR>resistenza magica</scaleMR>, <speed>velocità di movimento</speed>, <healing>rigenerazione salute</healing> e <scaleMana>rigenerazione mana</scaleMana> per {{ duration }} secondi. Durante questo effetto, <spellName>Scia velenosa</spellName> di Singed applica anche un {{ grievousamount*100 }}% di Ferita grave per {{ grievousduration }} secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Statistiche bonus"], "effect": ["{{ statamount }} -> {{ statamountNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "InsanityPotion.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Scia tossica", "description": "Singed sfreccia sfruttando i campioni vicini, ottenendo un aumento momentaneo della velocità di movimento quando li supera.", "image": {"full": "Singed_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}