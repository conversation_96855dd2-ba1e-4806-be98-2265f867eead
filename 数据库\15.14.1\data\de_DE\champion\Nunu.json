{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nunu": {"id": "<PERSON><PERSON><PERSON>", "key": "20", "name": "Nunu & Willump", "title": "der Junge und sein Yeti", "image": {"full": "Nunu.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "20000", "num": 0, "name": "default", "chromas": false}, {"id": "20001", "num": 1, "name": "Nunu & Sasquatch-Willump", "chromas": false}, {"id": "20002", "num": 2, "name": "Elfen-Nunu & Rentier-Willump", "chromas": false}, {"id": "20003", "num": 3, "name": "Nunu & Spielzeug-Willump", "chromas": false}, {"id": "20004", "num": 4, "name": "Nunu & Willump-Bot", "chromas": false}, {"id": "20005", "num": 5, "name": "Metall-Nunu & Zerstörer-Willump", "chromas": false}, {"id": "20006", "num": 6, "name": "TPA-Nunu & TPA-Willump", "chromas": false}, {"id": "20007", "num": 7, "name": "Untoter Nunu & Zombie-Willump", "chromas": false}, {"id": "20008", "num": 8, "name": "Nunu & Papierkunst-Willump", "chromas": true}, {"id": "20016", "num": 16, "name": "Weltraum-Groove-Nunu & Willump", "chromas": true}, {"id": "20026", "num": 26, "name": "Nunu & Willi", "chromas": true}, {"id": "20035", "num": 35, "name": "Kosmische Paladine Nunu & Willump", "chromas": true}, {"id": "20044", "num": 44, "name": "Schreckensnacht-Nunu & Willump", "chromas": true}], "lore": "Vor langer Zeit gab es einen Jungen, der seinen Heldenmut beweisen wollte, indem er ein fürchterliches Monster erschlug. Als er es jedoch fand, er<PERSON><PERSON> er, dass es nur ein einsamer magischer Yet<PERSON> war, der sich nach einem Freund sehn<PERSON>. Nunu und Willump sind durch eine uralte Macht sowie ihre gemeinsame Leidenschaft für Schneebälle verbunden und streifen nun gemeinsam durch Freljord. Wo immer sie auch hingehen, hauchen sie ihren fantastischen Abenteuern Leben ein. Sie hoffen, dass sie irgendwo dort draußen Nunus Mutter finden werden. Wenn sie es schaffen können, sie zu retten, dann werden sie letztlich vielleicht doch zu <PERSON> …", "blurb": "Vor langer Zeit gab es einen Jungen, der seinen Heldenmut beweisen wollte, indem er ein fürchterliches Monster erschlug. Als er es jedoch fand, er<PERSON><PERSON> er, dass es nur ein einsamer magischer Yet<PERSON> war, der sich nach einem Freund sehnte. Nunu und Willump...", "allytips": ["„Konsumieren“ erlaubt es Nunu, auch gegen Fernkämpfer zu bestehen.", "<PERSON><PERSON> „Kältesturz“ vorzeitig ab, um zumindest einen Teil des Gesamtschadens zu verursachen, sollte dein Gegner versuchen, zu fliehen.", "Oft ist es <PERSON>, „Kältesturz“ solange herauszuzögern, bis die erste Runde von Kampfunfähigkeits-Fertigkeiten durchgelaufen ist. Versuch, dich zurückzuhalten, bevor du dich in einen größeren Kampf stürzt."], "enemytips": ["Unterbrichst du die Aufladung von „Kältesturz“, dann senkst du den Schaden, den dein Team erleiden wird.", "<PERSON><PERSON> <PERSON><PERSON>litz“ entkommst du dem „Kältesturz“ ohne Frostbeulen.", "Der größte Schneeball aller Zeiten rollt sehr schnell, lässt sich aber nicht so leicht umlenken, also renne nicht in gerader Linie vor ihm weg.  Schlage stattdessen Haken."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 90, "mp": 280, "mpperlevel": 42, "movespeed": 345, "armor": 29, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "NunuQ", "name": "Konsumieren", "description": "<PERSON><PERSON> beißt ein Stück aus einem Vasallen, Monster oder gegnerischen Champion heraus, verursacht Schaden und heilt sich.", "tooltip": "Auf Nunus Kommando beißt Willump einen Gegner und fügt ihm dadurch <trueDamage>{{ monsterminiondamage }}&nbsp;absoluten Schaden</trueDamage> zu. Ist der Gegner ein Vasall oder Dschungelmonster, stellt der Biss <healing>{{ monsterhealing }}&nbsp;<PERSON><PERSON></healing> wieder her. Ist der Gegner ein Champion, verursacht der Biss stattdessen <magicDamage>{{ totalchampiondamage }}&nbsp;magischen Schaden</magicDamage> und stellt <healing>{{ championhealing }}&nbsp;Leben</healing> wieder her.<br /><br />Die <healing>Heilung</healing> wird um {{ lowhealthhealingscalar*100 }}&nbsp;% erhöht, wenn Nunu und Willump über weniger als {{ lowhealththreshhold*100 }}&nbsp;% Leben verfügen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> an <PERSON>", "<PERSON><PERSON><PERSON> an Champions", "Heilung", "Abklingzeit"], "effect": ["{{ monsterminiondamage }} -> {{ monsterminiondamageNL }}", "{{ championdamage }} -> {{ championdamageNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "NunuQ.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}&nbsp;Mana"}, {"id": "NunuW", "name": "Der größte Schneeball aller Zeiten!", "description": "Willump rollt einen Schneeball vor sich her, der immer größer und schneller wird.  Der Schneeball verursacht Schaden an Gegnern und schleudert sie in die Luft.", "tooltip": "Nunu und Willump rollen einen Schneeball vor sich her, der mit der Zeit an Größe und Geschwindigkeit zunimmt. Sie werden langsamer, während sie den Schneeball rollen, können aber das Tempo in Kurven erhöhen, wenn sie in der Kurve bleiben.<br /><br />Der Schneeball verursacht zwischen <magicDamage>{{ noimpactsnowballdamage }}</magicDamage> und <magicDamage>{{ maximumsnowballdamage }}&nbsp;magischen Schaden</magicDamage> und <status>schleudert</status> das Ziel zwischen {{ baseknockupduration }} und {{ maximumstunduration }}&nbsp;Sekunden lang hoch, wenn er mit einem Champion, einem großen Monster oder einer Mauer zusammenstößt. Diese Werte skalieren mit der zurückgelegten Distanz.<br /><br />Nunu und Willump können die Fähigkeiten <recast>reaktivieren</recast>, um den Schneeball vorzeitig loszulassen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "NunuW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuE", "name": "Schneeballsalve", "description": "<PERSON><PERSON>u wirft mehrere Schneebälle, die an Gegnern Schaden verursachen.  Wenn die Fähigkeit endet, hält Willump Champions oder große Monster, die von einem Schneeball getroffen wurden, an Ort und Stelle fest.", "tooltip": "Nunu wirft 3&nbsp;<PERSON>hneeb<PERSON><PERSON> und verursacht <magicDamage>{{ totalsnowballdamage }}&nbsp;magischen <PERSON>haden</magicDamage> pro Schneeball. <PERSON><PERSON><PERSON>, die von allen 3&nbsp;Schneebä<PERSON> getroffen wurden, werden zudem {{ slowduration }}&nbsp;Sekunde lang um {{ slowamount*-100 }}&nbsp;% <status>verlangsamt</status>. Nunu kann diese Fähigkeit bis zu 2-mal <recast>reaktivieren</recast>.<br /><br />Nach {{ totalspellduration }}&nbsp;Sekunden werden alle Gegner in der Nähe, die von den Schneebällen <status>verlangsamt</status> wurden, {{ rootduration }}&nbsp;Sekunden lang <status>festgehalten</status> und erleiden dadurch zusätzlich <magicDamage>{{ totalrootdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Manakosten", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ slowamount*-100.000000 }}&nbsp;% -> {{ slowamountnl*-100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "NunuE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuR", "name": "Kältesturz", "description": "<PERSON><PERSON><PERSON> und <PERSON> lassen einen mächtigen Schneesturm in einem Bereich entstehen, der Gegner verlangsamt und am Ende massiven Schaden verursacht.", "tooltip": "Nunu und Willump kanalisieren {{ channelduration }}&nbsp;Sekunden lang einen mächtigen Schneesturm. Gegner im Schneesturm werden anfänglich um {{ slowstartamount*-100 }}&nbsp;% <status>verlangsamt</status>, was sich während seiner Dauer auf {{ maxslowamount*-100 }}&nbsp;% erhöht. Außerdem erhalten Nunu und Willump während des Schneesturms einen <shield>Schild</shield> in Höhe von {{ totalshieldamount }}, der anschließend über {{ shielddecayduration }}&nbsp;Sekunden hinweg abfällt.<br /><br />Wenn der Schneesturm endet, explodiert er und verursacht abhängig von der Kanalisierungszeit bis zu <magicDamage>{{ maximumdamage }}&nbsp;magischen Schaden</magicDamage>. Nunu und Willump können die Fähigkeit <recast>reaktivieren</recast>, um den Schneesturm vorzeitig zu beenden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schildstärke", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "NunuR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Der Ruf Freljords", "description": "Nunu erhöht das Angriffs- und Lauftempo von Willump und einem nahen Verbündeten und Willumps normale Angriffe fügen allen Gegnern um das Ziel herum Schaden zu.", "image": {"full": "NunuPassive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}