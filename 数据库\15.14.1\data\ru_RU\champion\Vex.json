{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vex": {"id": "Vex", "key": "711", "name": "<PERSON>е<PERSON><PERSON>", "title": "Мрачнистка", "image": {"full": "Vex.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "711000", "num": 0, "name": "default", "chromas": false}, {"id": "711001", "num": 1, "name": "Предвестница зари Векс", "chromas": true}, {"id": "711010", "num": 10, "name": "Эмпирейка Векс", "chromas": true}, {"id": "711020", "num": 20, "name": "Звездочет Векс", "chromas": true}], "lore": "В темном сердце Сумрачных островов, где клубится призрачный туман, бродит одинокий йордл. Хмарь и безысходность ее вполне устраивают. Пользуясь неисчерпаемыми запасами подросткового уныния и поддержкой могущественной тени, Векс создала собственный мрачный мирок и старается держаться подальше от безудержного веселья, которым лучатся окружающие ее цивилы. И пусть в жизни ее мало что интересует, она не упускает возможности приглушить яркие краски, а тех, кто захочет ей помешать, ждет знакомство с магией уныния.", "blurb": "В темном сердце Сумрачных островов, где клубится призрачный туман, бродит одинокий йордл. Хмарь и безысходность ее вполне устраивают. Пользуясь неисчерпаемыми запасами подросткового уныния и поддержкой могущественной тени, Векс создала собственный...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "Мана", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 490, "mpperlevel": 32, "movespeed": 335, "armor": 23, "armorperlevel": 4.45, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 2.75, "attackspeedperlevel": 1, "attackspeed": 0.669}, "spells": [{"id": "VexQ", "name": "Черная полоса", "description": "Векс выпускает наносящий урон снаряд, который ускоряется посреди полета.", "tooltip": "Векс выпускает волну тумана, которая наносит <magicDamage>{{ qdamagecalc }} магического урона</magicDamage>. После задержки волна уменьшается и ускоряется.<br /><br />Поглощает метки <keywordMajor>Безысходности</keywordMajor> с пораженных врагов.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Стоимость – @AbilityResourceName@", "Перезарядка", "Урон"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VexQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "VexW", "name": "Личное пространство", "description": "Векс получает щит и наносит урон врагам поблизости.", "tooltip": "Векс получает <shield>щит прочностью {{ shieldcalc }}</shield> на {{ shieldduration }} сек. и выпускает ударную волну, которая наносит <magicDamage>{{ wdamagecalc }} магического урона</magicDamage>.<br /><br />Поглощает метки <keywordMajor>Безысходности</keywordMajor> с пораженных врагов.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Прочность щита", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldamount }} -> {{ shieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "VexW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "VexE", "name": "Неизбежная тьма", "description": "Векс создает область, которая наносит урон и замедляет врагов. Кроме того, она помечает их Безысходностью.", "tooltip": "Тень летит в выбранное место, постепенно увеличиваясь. По прибытии она наносит пораженным врагам <magicDamage>{{ edamagecalc }} магического урона</magicDamage> и <status>замедляет</status> их на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br />При убийстве врага при помощи этого умения перезарядка <keywordMajor>Тлена и безысходности</keywordMajor> сокращается на {{ gloomcdnonchamptooltip*100 }}%.<br /><br />Накладывает <keywordMajor>Безысходность</keywordMajor> на пораженных врагов.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Общий коэффициент силы умений"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ apratio*100.000000 }} -> {{ aprationl*100.000000 }}"]}, "maxrank": 5, "cooldown": [13, 13, 13, 13, 13], "cooldownBurn": "13", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "VexE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "VexR", "name": "Стремительная тень", "description": "Векс выпускает снаряд, который помечает вражеского чемпиона. При повторном применении умения Векс совершает рывок к цели и наносит урон.", "tooltip": "Тень восторженно летит вперед, нанося <magicDamage>{{ spell.vexr:rdamagecalc }} магического урона</magicDamage> и помечая первого пораженного вражеского чемпиона на 4 сек.<br /><br /><recast>Повторное применение</recast>: Векс совершает рывок к помеченному чемпиону, нанося <magicDamage>{{ spell.vexr:recastdamagecalc }} магического урона</magicDamage> по прибытии.<br /><br />Если помеченный чемпион погибает в течение {{ spell.vexr:takedownwindow }} сек. после получения урона от этого умения, его перезарядка временно сбрасывается.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Урон", "Урон при повторном применении", "Дальность"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ recastdamage }} -> {{ recastdamageNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "VexR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Тлен и безысходность", "description": "Векс периодически усиливается, и ее следующее базовое умение пугает врагов и прерывает рывки. Когда враги рядом с Векс совершают рывки, она помечает их, а при снятии метки наносит дополнительный урон и сокращает перезарядку своего усиления.", "image": {"full": "Icons_Vex_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}