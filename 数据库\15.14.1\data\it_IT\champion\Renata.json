{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "Renata <PERSON>", "title": "la Baronessa chimica", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "888010", "num": 10, "name": "Renata Glasc Notte Inquietante", "chromas": true}, {"id": "888020", "num": 20, "name": "Renata Glasc la Ilusión", "chromas": true}, {"id": "888021", "num": 21, "name": "Renata Glasc la Ilusión (edizione prestigio)", "chromas": false}, {"id": "888031", "num": 31, "name": "Renata Glasc Ballo della Rosa Nera", "chromas": false}], "lore": "Renata Glasc è rinata dalle ceneri della sua casa d'infanzia, partendo solo dal suo nome e dalla ricerca alchemica dei suoi genitori. Nei decenni successivi è diventata la baronessa chimica più ricca di <PERSON>aun, una magnate degli affari che ha plasmato il suo potere intrecciando gli interessi altrui alle sue imprese. Chi lavora con lei viene ricompensato generosamente. Chi si oppone se ne pente amaramente. <PERSON><PERSON>, prima o poi, saranno al suo servizio.", "blurb": "Renata Glasc è rinata dalle ceneri della sua casa d'infanzia, partendo solo dal suo nome e dalla ricerca alchemica dei suoi genitori. Nei decenni successivi è diventata la baronessa chimica più ricca di <PERSON>aun, una magnate degli affari che ha plasmato il...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "Stretta di mano", "description": "Renata lancia un proiettile che immobilizza il primo nemico colpito e può rilanciare l'abilità per mandarlo in una direzione.", "tooltip": "Renata lancia un proiettile dal suo braccio che <status>immobilizza</status> il primo nemico colpito per {{ rootduration }} secondo/i e infligge <magicDamage>{{ totaldamage }}</magicDamage> <magicDamage>danni magici</magicDamage>.<br /><br /><recast>Rilancio:</recast> Renata <status>attira</status> il nemico in una direzione, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici che l'unità incontra e <status>stordendoli</status> per {{ stunduration }} secondi se l'unità lanciata è un campione.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataW", "name": "Salvataggio", "description": "Renata potenzia un campione alleato per migliorarlo in combattimento, ritardandone la morte e possibilmente salvandolo se ottiene un'eliminazione.", "tooltip": "Renata infonde un campione alleato, dandogli <attackSpeed>{{ ascalc }} velocità d'attacco</attackSpeed> e <speed>{{ mscalc }} velocità di movimento</speed> verso i nemici, che aumentano a <attackSpeed>{{ finalascalc }} velocità d'attacco</attackSpeed> e <speed>{{ finalmscalc }} velocità di movimento</speed> per {{ duration }} secondi. Le eliminazioni fanno ripartire la durata del buff.<br /><br />Se l'alleato muore, torna al massimo della salute, che poi decade nell'arco di 3 secondi.<br /><br />Se l'unità ottiene un'eliminazione durante il decadimento, raggiungerà un <healing>{{ triumphpercent }}% della sua salute massima</healing> e interromperà il decadimento.<br /><br /><rules>La morte del campione può essere ritardata da effetti curativi o simili durante il decadimento, ma non può essere impedita a meno che il campione non ottenga un'eliminazione. I campioni possono ritardare la propria morte solo una volta.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Velocità di movimento", "Ricarica"], "effect": ["{{ bonusattackspeed }}% -> {{ bonusattackspeedNL }}%", "{{ bonusmovespeed }}% -> {{ bonusmovespeedNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataE", "name": "Programma di fedeltà", "description": "Renata spara una coppia di missili chemtech, proteggendo gli alleati e danneggiando e rallentando i nemici colpiti.", "tooltip": "Renata spara una coppia di missili chemtech, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallentando</status> i nemici circostanti e quelli che vengono colpiti del 30% per {{ slowduration }} secondi. Gli alleati colpiti ottengono uno <shield>scudo da {{ shieldcalc }}</shield> per {{ shieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Quantità scudo", "Costo in @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataR", "name": "Acquisizione forzata", "description": "Renata lancia un'onda di sostanze chimiche, facendo in modo che tutti i nemici colpiti vadano in berserk.", "tooltip": "Renata lancia un'onda di sostanze chimiche che <status>mandano in berserk</status> i nemici, facendo in modo che attacchino il bersaglio più vicino per {{ berserkduration }} secondi, dando priorità ai loro alleati.<br /><br />Mentre sono in <status>berserk</status>, i nemici ottengono <attackSpeed>{{ bonusattackspeed*100 }}% velocità d'attacco</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Influenza", "description": "Gli attacchi di Renata infliggono danni bonus e marchiano i nemici. Gli alleati di Renata possono danneggiare i nemici marchiati per infliggere danni bonus.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}