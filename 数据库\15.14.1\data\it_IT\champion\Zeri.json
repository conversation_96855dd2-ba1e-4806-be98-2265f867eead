{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zeri": {"id": "<PERSON><PERSON>", "key": "221", "name": "<PERSON><PERSON>", "title": "La scintilla di Zaun", "image": {"full": "Zeri.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "221000", "num": 0, "name": "default", "chromas": false}, {"id": "221001", "num": 1, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "221010", "num": 10, "name": "Zeri Canto dell'Oceano", "chromas": true}, {"id": "221019", "num": 19, "name": "Zeri Viaggio Immortale", "chromas": true}, {"id": "221028", "num": 28, "name": "Zeri Notte Inquietante", "chromas": true}, {"id": "221029", "num": 29, "name": "Zeri Notte Inquietante (edizione prestigio)", "chromas": false}], "lore": "<PERSON><PERSON>, una giovane e caparbia donna della classe operaia zaunita, canalizza la sua magia elettrica per caricare se stessa e la sua arma. I suoi poteri sono instabili come le sue emozioni e le sue scintille riflettono il suo approccio fulmineo alla vita. Zeri ha una grande compassione per il prossimo e porta in ogni battaglia l'amore della sua famiglia. A volte la sua voglia di aiutare provoca qualche pasticcio, ma Zeri è convinta di una cosa: chi lotta per la sua comunità avrà sempre la comunità al suo fianco.", "blurb": "<PERSON><PERSON>, una giovane e caparbia donna della classe operaia zaunita, canalizza la sua magia elettrica per caricare se stessa e la sua arma. I suoi poteri sono instabili come le sue emozioni e le sue scintille riflettono il suo approccio fulmineo alla vita...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 250, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "ZeriQ", "name": "Fuoco a raffica", "description": "Fuoco a raffica spara una raffica di 7 proiettili che infliggono attacco fisico al primo nemico colpito. Quest'abilità è considerata un attacco.", "tooltip": "Zeri spara una raffica di {{ numberofmissiles }} proiettili che infliggono <physicalDamage>{{ activedamagethatcancrit }} danni fisici</physicalDamage> al primo nemico colpito. Quest'abilità è considerata un attacco. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico totale"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ activeadratio*100.000000 }}% -> {{ activeadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "ZeriQ.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "ZeriW", "name": "Laser ultrashock", "description": "<PERSON>eri spara un impulso elettrico che rallenta e danneggia il primo nemico colpito. Se l'impulso colpisce un muro, si espande in un laser a lungo raggio.", "tooltip": "Zeri spara un impulso elettrico che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e <status>rallenta</status> di un {{ slowpercent*100 }}% per {{ slowduration }} secondi il primo nemico colpito.<br /><br />Se l'impulso colpisce il terreno, si espande in un laser che applica gli effetti in un'area e infligge colpi critici a campioni e mostri.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "ZeriW.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZeriE", "name": "Scarica di scintille", "description": "<PERSON><PERSON> scatta per una breve distanza ed energizza i 3 colpi successivi di Fuoco a raffica, che ora sono in grado di trapassare i nemici. Supera e scavalca qualsiasi ostacolo che tocca.", "tooltip": "Zeri scatta per una breve distanza e scavalca ogni ostacolo che tocca, aumentando notevolmente la gittata dello scatto. Per i prossimi {{ buffduration }} secondi i colpi di <spellName>Fuoco a raffica</spellName> perforano i nemici, infliggendo {{ pendamagepercent*100 }}% danni ai nemici dopo il primo e altri <magicDamage>{{ bonusdamagetotal }} danni magici</magicDamage> sul colpo al primo bersaglio colpito. <br /><br />Colpire un campione nemico con un attacco o un'abilità riduce il tempo di ricarica di questa abilità di {{ cdreductionperhit }} secondi. I colpi critici riducono i tempi di ricarica di {{ critcdreductionperhit }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Danni base", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ pendamagepercent*100.000000 }}% -> {{ pendamagepercentnl*100.000000 }}%", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [90, 85, 80, 75, 70], "costBurn": "90/85/80/75/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "ZeriE.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZeriR", "name": "<PERSON><PERSON><PERSON> elett<PERSON>", "description": "Zeri scarica un'esplosione di elettricità e si sovraccarica, ottenendo danni aumentati e accumulando velocità di movimento che si ripristina e diventa più forte ogni volta che colpisce un campione nemico. Quando Zeri è in sovraccarico, Fuoco a raffica diventa un rapido colpo triplo che fa rimbalzare fulmini tra i nemici.", "tooltip": "Zeri scarica un'esplosione di elettricità, infliggendo <magicDamage>{{ totalactivedamage }} danni magici</magicDamage> ai nemici vicini. Se colpisce un campione nemico, Zeri ottiene <attackSpeed>{{ baseaspercent*100 }}% velocità d'attacco</attackSpeed> e <speed>{{ basebonusms*100 }}% velocità di movimento</speed> per {{ rduration }} secondi. Colpire un campione nemico con un attacco o un'abilità estende la durata dell'abilità e aggiunge una carica di Sovraccarico per {{ maxhyperchargeduration }} secondi. I colpi critici aggiungono 2 cariche aggiuntive. Ogni carica conferisce <speed>{{ mspercent*100 }}% velocità di movimento</speed>.<br /><br />Durante questo periodo, <spellName>Fuoco a raffica</spellName> diventa un colpo triplo più rapido che concatena <physicalDamage>{{ chainphysicaldamage }} danni fisici</physicalDamage> ai nemici vicini.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ activedamage }} -> {{ activedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 75, 70], "cooldownBurn": "80/75/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ZeriR.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dinamo", "description": "Gli attacchi di Zeri infliggono danni magici e vengono considerati abilità. Il movimento e il lancio di Fuoco a raffica accumulano energia nello Zaino scintilla di Zeri. Al massimo delle cariche, il suo prossimo attacco infligge danni bonus.", "image": {"full": "ZeriP.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}