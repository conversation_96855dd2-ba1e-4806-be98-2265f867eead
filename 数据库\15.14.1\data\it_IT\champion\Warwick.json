{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "Warwick", "title": "la furia scatenata di Zaun", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "<PERSON>", "chromas": true}, {"id": "19002", "num": 2, "name": "<PERSON><PERSON> il Lamantino", "chromas": false}, {"id": "19003", "num": 3, "name": "Grosso Warwick Cattivo", "chromas": false}, {"id": "19004", "num": 4, "name": "Warwick C<PERSON> Tundra", "chromas": false}, {"id": "19005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "19006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "19007", "num": 7, "name": "<PERSON>", "chromas": false}, {"id": "19008", "num": 8, "name": "<PERSON>", "chromas": false}, {"id": "19009", "num": 9, "name": "Urfwick", "chromas": false}, {"id": "19010", "num": 10, "name": "<PERSON>", "chromas": true}, {"id": "19016", "num": 16, "name": "PROGETTO: Warwick", "chromas": true}, {"id": "19035", "num": 35, "name": "Warwick Dio Antico", "chromas": false}, {"id": "19045", "num": 45, "name": "Warwick Favore dell'Inverno", "chromas": false}, {"id": "19046", "num": 46, "name": "Warwick Favore dell'Inverno (edizione prestigio)", "chromas": false}, {"id": "19056", "num": 56, "name": "Warwick <PERSON>: <PERSON><PERSON>", "chromas": false}], "lore": "Warwick è un mostro che va a caccia nei grigi vicoli di Zaun. Trasformato da esperimenti strazianti, il suo corpo è fuso con un intricato sistema di camere e pompe, delle macchine che riempiono le sue vene di furia alchemica. Emerge dall'ombra per dare la caccia ai criminali che terrorizzano le profondità della città. Warwick è attratto dal sangue, perde il controllo quando ne sente l'odore... e nessun ferito può sfuggirgli.", "blurb": "Warwick è un mostro che va a caccia nei grigi vicoli di Zaun. Trasformato da esperimenti strazianti, il suo corpo è fuso con un intricato sistema di camere e pompe, delle macchine che riempiono le sue vene di furia alchemica. Emerge dall'ombra per dare...", "allytips": ["Se<PERSON>i le scie di Caccia di sangue per trovare i nemici con poca salute.", "La distanza di Balzo dell'infinito cresce con la velocità di movimento che ottieni, compresi buff e incantesimi dell'evocatore.", "Fauci della bestia permette di seguire i nemici che fuggono, scattano o si teletrasportano, se tieni premuto il tasto."], "enemytips": ["Gli attacchi di Warwick lo curano quando ha poca salute. Risparmia gli impedimenti per finirlo.", "Warwick è più potente contro i nemici che hanno poca salute. Gestisci la tua salute per tenerlo a bada.", "La suprema di Warwick ha una portata di lancio che aumenta con la sua velocità di movimento."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "Fauci della bestia", "description": "<PERSON> balza in avanti e morde il suo bersaglio, infliggendo danni in base alla sua salute massima e curandosi per i danni inflitti.", "tooltip": "<tap>Premi:</tap> <PERSON> balza in avanti e morde il nemico, infliggendo <magicDamage>{{ basebitedamage }} più un {{ targetpercenthpdamage }}% della sua salute massima in danni</magicDamage> e <healing>curando un {{ e3 }}% dei danni inflitti</healing>.<br /><br /><hold>Tieni premuto:</hold> Warwick balza e serra la mascella attorno al bersaglio, saltando alle sue spalle. Finch<PERSON> attanaglia il bersaglio, <PERSON> segue tutti i suoi movimenti. Dopo aver mollato la presa, infligge la stessa quantità di danni e recupera la stessa quantità di salute.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "Danni % salute", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e3 }}% -> {{ e3NL }}%", "{{ targetpercenthpdamage }}% -> {{ targetpercenthpdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickW", "name": "Caccia di sangue", "description": "Warwick percepisce i nemici con meno del 50% di salute e ottiene velocità d'attacco contro di essi e velocità di movimento nella loro direzione. Quando sono sotto il 25% di salute, entra in frenesia e i bonus si triplicano.", "tooltip": "<spellPassive>Passiva:</spellPassive> Warwick può percepire i campioni con meno del 50% di salute e ottiene <speed>{{ passivemsbonus }}% velocità di movimento</speed> per muoversi verso di loro. Le abilità e gli attacchi contro i nemici con meno del 50% di salute conferiscono <speed>{{ passiveasbonus }}% velocità d'attacco</speed>. Tali effetti aumentano del 200% contro i nemici con meno del 25% di salute. <br /><br /><spellActive>Attiva:</spellActive> Warwick può percepire brevemente tutti i nemici e ottiene l'effetto passivo di questa abilità contro il campione più vicino per 8 secondi, a prescindere dalla salute. Se non viene trovato alcun campione, la ricarica dell'abilità si riduce del 30%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Velocità d'attacco", "Ricarica"], "effect": ["{{ passivemsbonus }}% -> {{ passivemsbonusNL }}%", "{{ passiveasbonus }}% -> {{ passiveasbonusNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickE", "name": "Ululato primordiale", "description": "Warwick ottiene riduzione danni per 2,5 secondi. <PERSON>a fine, o se viene riattivata l'abilità, ulula, facendo fuggire i nemici nelle vicinanze per 1 secondo.", "tooltip": "<PERSON> ottiene {{ e1 }}% riduzione danni per 2,5 secondi. Al termine di questo e<PERSON>tto, <PERSON> ulula, <status>impaurendo</status> i nemici vicini per {{ e3 }} secondo/i. <PERSON> pu<PERSON> <recast>rilanciare</recast> questa abilità per farla terminare in anticipo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Riduzione danni", "Ricarica"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickR", "name": "Balzo dell'infinito", "description": "Warwick balza in una direzione (con una portata che aumenta con la velocità di movimento) e immobilizza il primo campione con il quale si scontra per 1,5 secondi.", "tooltip": "Warwick balza coprendo un'enorme distanza che aumenta in base alla sua <speed>velocità di movimento</speed>, <status>sopprimendo</status> il primo campione con cui si scontra mentre canalizza per {{ rduration }} secondi. Attacca quel campione 3 volte nell'arco della durata, infliggendo <magicDamage>{{ damagecumulative }} danni magici</magicDamage>. <PERSON> guarisce per il <healing>100% di tutti i danni inflitti</healing> durante la canalizzazione.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Sete eterna", "description": "Gli attacchi base di Warwick infliggono danni magici bonus. Se <PERSON> ha meno del 50% di salute, si cura per la stessa quantità. Se <PERSON> ha meno del 25% di salute, la guarigione triplica.", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}