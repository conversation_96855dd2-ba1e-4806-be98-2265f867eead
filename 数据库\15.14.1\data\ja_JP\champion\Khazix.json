{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Khazix": {"id": "Khazix", "key": "121", "name": "カ＝ジックス", "title": "ヴォイドの捕食者", "image": {"full": "Khazix.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "121000", "num": 0, "name": "default", "chromas": false}, {"id": "121001", "num": 1, "name": "メカ＝ジックス", "chromas": true}, {"id": "121002", "num": 2, "name": "砂漠の守護者カ＝ジックス", "chromas": false}, {"id": "121003", "num": 3, "name": "死の華カ＝ジックス", "chromas": false}, {"id": "121004", "num": 4, "name": "ダークスター カ＝ジックス", "chromas": false}, {"id": "121011", "num": 11, "name": "Worlds 2018 カ＝ジックス", "chromas": true}, {"id": "121060", "num": 60, "name": "オデッセイ カ＝ジックス", "chromas": false}, {"id": "121069", "num": 69, "name": "月の守護者カ＝ジックス", "chromas": false}, {"id": "121079", "num": 79, "name": "クリスタリス インドゥミタス カ＝ジックス", "chromas": false}], "lore": "ヴォイドは成長し、適応する──無数に存在するヴォイドの生物の中でカ＝ジックスほどこの真実を体現しているものは存在しない。この恐怖のミュータントの原動力となっているのは進化であり、最強の生き物を倒して生き延びることを目的として生まれてきた。獲物を倒せなければ、新たに成長してより効果的な方法を身に付ける。カ＝ジックスはもともと心を持たない獣だったが、今では形態とともに知性が発達して獲物を狙う際に計画を立てるようになり、自身が獲物の心に植え付ける恐怖すら利用するようになっている。", "blurb": "ヴォイドは成長し、適応する──無数に存在するヴォイドの生物の中でカ＝ジックスほどこの真実を体現しているものは存在しない。この恐怖のミュータントの原動力となっているのは進化であり、最強の生き物を倒して生き延びることを目的として生まれてきた。獲物を倒せなければ、新たに成長してより効果的な方法を身に付ける。カ＝ジックスはもともと心を持たない獣だったが、今では形態とともに知性が発達して獲物を狙う際に計画を立てるようになり、自身が獲物の心に植え付ける恐怖すら利用するようになっている。", "allytips": ["周囲に味方がいない状態の敵ユニットは孤立状態となり「甘美なる恐怖」のダメージが大幅に増加する。", "「見えざる脅威」は、カ＝ジックスの姿が敵の視界から消えると使用できるようになる。再度使用するには、茂みに入ったり「捕食の本能」を発動すればよい。敵チャンピオンと交戦する時は、通常攻撃で「見えざる脅威」を発動させるのを忘れないように。", "カ＝ジックスは自分の望んだ時に、望んだ場所で戦闘に持ち込むことができる。勝てる相手を選んで、確実に仕留めていこう。"], "enemytips": ["「甘美なる恐怖」は、孤立した対象に追加ダメージを与える。味方ミニオンやチャンピオン、タワーのそばを離れずにいれば、有利に戦える。", "「リープ」と「捕食の本能」はクールダウンが長く、カ＝ジックスはこの間、極めて無防備になる。"], "tags": ["Assassin"], "partype": "マナ", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 6}, "stats": {"hp": 643, "hpperlevel": 99, "mp": 327, "mpperlevel": 40, "movespeed": 350, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.59, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.668}, "spells": [{"id": "KhazixQ", "name": "甘美なる恐怖", "description": "対象に物理ダメージを与える。<font color='#FFF673'>「孤立」</font>している対象にはダメージが増加する。<font color='#00DD33'>「死鎌の進化」</font>が完了している場合は、<font color='#FFF673'>「孤立」</font>している対象に使用するとクールダウンの一定割合が戻される。また、通常攻撃と「甘美なる恐怖」の射程が延びる。", "tooltip": "近くの敵1体を斬りつけて<physicalDamage>{{ spell.khazixq:basedamage }}の物理ダメージ</physicalDamage>を与える。対象が他の敵から<keywordMajor>「孤立」</keywordMajor>している場合は、代わりに<physicalDamage>{{ spell.khazixq:isodamage }}のダメージ</physicalDamage>を与える。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [80, 105, 130, 155, 180], [110, 110, 110, 110, 110], [50, 50, 50, 50, 50], [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/105/130/155/180", "110", "50", "45", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "KhazixQ.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KhazixW", "name": "ヴォイドの刺棘", "description": "爆発する棘を発射して、命中した敵ユニットに物理ダメージを与える。カ＝ジックスが爆発範囲内にいた場合は体力が回復する。<font color='#00DD33'>「刺棘の進化」</font>が完了している場合は、扇状に棘の塊が3つ発射され、命中した敵ユニットにはスロウ効果を与え、敵チャンピオンに命中した場合は2秒間可視状態になる。<font color='#FFF673'>「孤立」</font>した対象にはスロウ効果が強化される。", "tooltip": "棘を発射して、最初に命中した対象と狭い範囲内の敵に<physicalDamage>{{ basedamage }}の物理ダメージ</physicalDamage>を与える。自身が範囲内にいた場合は<healing>体力を{{ healamount }}</healing>回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "体力回復量", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [55, 75, 95, 115, 135], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "55/75/95/115/135", "40", "2", "0", "2", "60", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KhazixW.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KhazixE", "name": "リープ", "description": "カ＝ジックスが指定地点へと跳躍し、着地と同時に物理ダメージを与える。<font color='#00DD33'>「翅の進化」</font>を選択した場合は、「リープ」の飛距離が200延びて、敵チャンピオンのキルまたはアシストでクールダウンが解消する。", "tooltip": "跳躍して着地時に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [30, 35, 40, 45, 50], [10, 10, 10, 10, 10], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "30/35/40/45/50", "10", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KhazixE.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KhazixR", "name": "捕食の本能", "description": "自動効果: スキルレベルが上がるたびにスキルを1つ進化させ、特殊な効果を追加できる。<br>発動効果: カ＝ジックスが<font color='#91d7ee'>インビジブル</font>状態になり、「見えざる脅威」が発動して移動速度が増加する。<font color='#00DD33'>「適応擬態の進化」</font>が完了している場合は、「捕食の本能」の<font color='#91d7ee'>インビジブル</font>の効果時間と使用回数が増加する。", "tooltip": "<spellActive>発動効果:</spellActive> {{ stealthduration }}秒間<keywordStealth>インビジブル</keywordStealth>状態になり、<speed>移動速度が{{ bonusmovementspeedpercent*100 }}%</speed>増加する。{{ recastwindow }}秒以内なら一度だけこのスキルを<recast>再発動</recast>できる。<br /><br /><spellPassive>自動効果:</spellPassive> このスキルのレベルを上げると、スキルの1つを<evolve>「進化」</evolve>させて、追加効果を付与できる。<li><spellName>甘美なる恐怖:</spellName> スキルと通常攻撃の射程が増加し、対象が<keywordMajor>「孤立」</keywordMajor>している場合はクールダウンが{{ spell.khazixq:effect4amount }}%短縮される。<li><spellName>ヴォイドの刺棘:</spellName> 3つの棘を発射して{{ spell.khazixw:effect3amount }}%の<status>スロウ効果</status>を与え、<keywordMajor>「孤立」</keywordMajor>した対象には効果が増加する。<li><spellName>リープ:</spellName> 射程が増加し、チャンピオンからキルまたはアシストを奪うとクールダウンが解消される。<li><spellName>捕食の本能:</spellName> <keywordStealth>インビジブル</keywordStealth>状態の効果時間が{{ evolvedstealthduration }}秒になり、2回<recast>再発動</recast>可能になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["進化可能スキル数", "クールダウン"], "effect": ["{{ evolutionsavailable }} -> {{ evolutionsavailableNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "KhazixR.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "見えざる脅威", "description": "味方から<font color='#FFF673'>「孤立」</font>している周囲の敵ユニットをマークする。カ＝ジックスのスキルは<font color='#FFF673'>「孤立」</font>している対象が相手だと変化する。<br><br>敵の視界から消えるとカ＝ジックスは「見えざる脅威」を獲得し、次の通常攻撃で敵チャンピオンに追加魔法ダメージを与え、移動速度を数秒間、低下させる。", "image": {"full": "Khazix_P.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}