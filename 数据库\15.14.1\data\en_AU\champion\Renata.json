{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "Renata <PERSON>", "title": "the Chem-Baroness", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "Admiral <PERSON><PERSON>", "chromas": true}, {"id": "888010", "num": 10, "name": "Fright Night Renata Glasc", "chromas": true}, {"id": "888020", "num": 20, "name": "La Ilusión Renata Glasc", "chromas": true}, {"id": "888021", "num": 21, "name": "Prestige La Ilusión Renata Glasc", "chromas": false}, {"id": "888031", "num": 31, "name": "Masque of the Black Rose Renata Glasc", "chromas": false}], "lore": "<PERSON><PERSON> rose from the ashes of her childhood home with nothing but her name and her parents' alchemical research. In the decades since, she has become <PERSON><PERSON><PERSON>'s wealthiest chem-baron, a business magnate who built her power by tying everyone's interests to her own. Work with her, and be rewarded beyond measure. Work against her, and live to regret it. But everyone comes to her side, eventually.", "blurb": "<PERSON><PERSON> rose from the ashes of her childhood home with nothing but her name and her parents' alchemical research. In the decades since, she has become <PERSON><PERSON><PERSON>'s wealthiest chem-baron, a business magnate who built her power by tying everyone's...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "Handshake", "description": "Renata sends out a missile rooting the first enemy hit, and can recast the ability to throw the unit in a direction.", "tooltip": "<PERSON><PERSON> sends out a missile from her arm, <status>Rooting</status> the first enemy hit for {{ rootduration }} second and dealing <magicDamage>{{ totaldamage }}</magicDamage> <magicDamage>magic damage</magicDamage>.<br /><br /><recast>Recast:</recast> Ren<PERSON> <status>Pulls</status> the enemy in a direction, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies they are thrown into and <status>Stunning</status> for {{ stunduration }} seconds if the thrown enemy is a champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataW", "name": "Bailout", "description": "<PERSON><PERSON> buffs an allied champion to fight harder, delaying their death and potentially saving them if they get a takedown.", "tooltip": "Ren<PERSON> infuses an ally champion, granting <attackSpeed>{{ ascalc }} Attack Speed</attackSpeed> and <speed>{{ mscalc }} Move Speed</speed> towards enemies, increasing to <attackSpeed>{{ finalascalc }} Attack Speed</attackSpeed> and <speed>{{ finalmscalc }} Move Speed</speed> over {{ duration }} seconds. Takedowns refresh the duration of the buff.<br /><br />If the ally would die, they instead return to full Health, which then decays over 3 seconds.<br /><br />If they get a Takedown while decaying, they will be set to <healing>{{ triumphpercent }}% max Health</healing> and stop decaying.<br /><br /><rules>The Champion's death can be delayed by healing or similar effects while in the decaying state, but cannot be prevented unless the Champion gets a Takedown. Champions can only delay their death once.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Speed", "Move Speed", "Cooldown"], "effect": ["{{ bonusattackspeed }}% -> {{ bonusattackspeedNL }}%", "{{ bonusmovespeed }}% -> {{ bonusmovespeedNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataE", "name": "Loyalty Program", "description": "Renata sends out a pair of chemtech missiles, shielding allies and damaging and slowing enemies hit.", "tooltip": "Renata sends out a pair of chemtech missiles, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> and <status>Slowing</status> surrounding enemies and enemies hit by 30% for {{ slowduration }} seconds. Allies hit gain <shield>{{ shieldcalc }} Shield</shield> for {{ shieldduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "Shield Amount", "@AbilityResourceName@ Cost"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataR", "name": "Hostile Takeover", "description": "Renata sends out a wave of chemicals, causing any enemies hit to go Berserk.", "tooltip": "Renata sends out a wave of chemicals, <status>Berserking</status> enemies for {{ berserkduration }} seconds, causing them to Attack the nearest unit, prioritizing their allies.<br /><br />While <status>Berserk</status>, enemies gain <attackSpeed>{{ bonusattackspeed*100 }}% Attack Speed</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Duration"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Leverage", "description": "<PERSON><PERSON>'s Attacks deal bonus damage and mark enemies. <PERSON><PERSON>'s allies can damage marked enemies to deal bonus damage.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}