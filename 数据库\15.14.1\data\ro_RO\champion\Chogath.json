{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Chogath": {"id": "<PERSON><PERSON><PERSON>", "key": "31", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "teroarea din Vid", "image": {"full": "Chogath.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "31000", "num": 0, "name": "default", "chromas": false}, {"id": "31001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "31002", "num": 2, "name": "<PERSON><PERSON><PERSON> ", "chromas": false}, {"id": "31003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON> din Loch Ness", "chromas": false}, {"id": "31004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON> jurasic", "chromas": false}, {"id": "31005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON> ma<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "31006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON> preistoric", "chromas": false}, {"id": "31007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON>, steaua întunecată", "chromas": false}, {"id": "31014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON> din Pergamentele Shan Hai", "chromas": true}, {"id": "31023", "num": 23, "name": "<PERSON><PERSON><PERSON><PERSON> leg<PERSON><PERSON><PERSON><PERSON> rupt", "chromas": true}, {"id": "31032", "num": 32, "name": "<PERSON>'<PERSON><PERSON> teroare de j<PERSON>", "chromas": true}], "lore": "Din clipa în care Cho'Gath a ajuns pentru prima oară sub lumina aspră a soarelui Runeterrei, a fost mânat doar de o foame pură, ce nu poate fi potolită niciodată. Biologia lui complexă este o manifestare perfectă a dorinței Vidului de a înghiți întreaga viață din univers. Orice înghite îl face să crească rapid, augmentându-i masa musculară și întărindu-i carapacea exterioară care se aseamănă unui diamant organic. Atunci când creșterea nu corespunde nevoilor sale, Cho'Gath vomită materialul în exces sub forma unor spini ascuțiți ca niște lame, străpungându-și prada și pregătind-o pentru ospățul de mai târziu.", "blurb": "Din clipa în care Cho'Gath a ajuns pentru prima oară sub lumina aspră a soarelui Runeterrei, a fost mânat doar de o foame pură, ce nu poate fi potolită niciodată. Biologia lui complexă este o manifestare perfectă a dorinței Vidului de a înghiți întreaga...", "allytips": ["Încearcă să-ți aliniezi atacurile cu ''Țepușe vorpale'' astfel încât să ucidă minionii și să-i hărțuiască pe campionii inamici din spatele lor în același timp.", "Dacă ai probleme la devorarea campionilor, încearcă să mănânci minioni până mai crești.", "Ucide minioni cu ''Fisura'' și ''Carnivor'' ca să câștigi viață și mană."], "enemytips": ["Cumpără câteva obiecte de viață pentru a scădea șansele ca Cho'Gath să te omoare rapid.", "Nu-l lăsa pe <PERSON> să ajungă la forma lui finală.", "''Fisura'' e precedată de un cerc care indică zona pe care o va afecta. Observă unde se formează cercul pentru a nu-l lăsa pe Cho'Gath să te prindă cu un combo de abilități."], "tags": ["Tank", "Mage"], "partype": "Mană", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 5}, "stats": {"hp": 644, "hpperlevel": 94, "mp": 270, "mpperlevel": 60, "movespeed": 345, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.2, "attackspeedperlevel": 1.44, "attackspeed": 0.658}, "spells": [{"id": "Rupture", "name": "Fisur<PERSON>", "description": "Fisurează pământul în locația-țintă și aruncă unitățile inamice în sus, provocându-le daune și încetinindu-le.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> fi<PERSON><PERSON> pământul, <status>aruncând în sus</status> inamicii timp de {{ e5 }} sec., provocându-le <magicDamage>{{ totaldamagetooltip }} daune magice</magicDamage> și <status>încetinindu-i</status> cu {{ e2 }}% timp de {{ e3 }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [60, 60, 60, 60, 60], [1.5, 1.5, 1.5, 1.5, 1.5], [0.625, 0.625, 0.625, 0.625, 0.625], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "60", "1.5", "0.63", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "Rupture.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FeralScream", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Cho'Gath scoate un țipăt înfiorător care lovește toți campionii dintr-o zonă în formă de con, provocându-le daune magice și amuțindu-i timp de câteva secunde.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON>, <status>amuț<PERSON></status> inamicii timp de {{ e2 }} secunde și provocându-le <magicDamage>{{ totaldamagetooltip }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare", "Durat<PERSON> amuț<PERSON>", "Cost de @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1.6, 1.7, 1.8, 1.9, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1.6/1.7/1.8/1.9/2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "FeralScream.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VorpalSpikes", "name": "Țepușe vorpale", "description": "Atacurile lui <PERSON>Gath lansează țepușe letale care le provoacă daune tuturor unităților inamice aflate în fața sa și le încetinește.", "tooltip": "Următoarele 3 atacuri ale lui Cho'Gath lansează țepușe care le provoacă țintelor <magicDamage>daune magice în valoare de {{ flatdamagecalc }} plus {{ maxhealthpercentcalc }} din viața maximă a țintei</magicDamage> și le <status>încetinesc</status> cu {{ slowamountpercentage }}%, valoare ce scade de-a lungul a {{ slowduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Daune din viața maximă", "Încetinire", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthealthdamage }}% -> {{ percenthealthdamageNL }}%", "{{ slowamountpercentage }}% -> {{ slowamountpercentageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [40, 40, 40, 40, 40], "rangeBurn": "40", "image": {"full": "VorpalSpikes.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Feast", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Devorează o unitate inamică, provocându-i o cantitate mare de daune reale. Dacă ținta este ucisă, Cho'Gath crește în dimensiune și primește un bonus la viața maximă.", "tooltip": "Cho'Gath devorează un inamic, provocându-le <trueDamage>{{ rdamage }} daune reale</trueDamage> campionilor sau <trueDamage>{{ rmonsterdamage }}</trueDamage> minionilor și monștrilor din junglă. Dac<PERSON> abilitatea ucide <PERSON>, Cho'Gath primește un cumul, care îl face să crească în mărime și îi oferă <healing>{{ rhealthperstack }} viață maximă</healing>. Abilitatea poate oferi cel mult {{ rminionmaxstacks }} cumuluri împotriva minionilor și monștrilor non-epici din junglă. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune împotriva campionilor", "Viață per cumul", "Raz<PERSON> de atac per cumul", "Timp de reactivare"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rhealthperstack }} -> {{ rhealthperstackNL }}", "{{ attackrangeperstack }} -> {{ attackrangeperstackNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 70, 60], "cooldownBurn": "80/70/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [175, 175, 175], "rangeBurn": "175", "image": {"full": "Feast.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Carnivor", "description": "<PERSON><PERSON><PERSON> ucide o unitate, recuperează viață și mană. Valoarea recuperată crește odată cu nivelul lui <PERSON>'<PERSON>ath.", "image": {"full": "GreenTerror_TailSpike.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}