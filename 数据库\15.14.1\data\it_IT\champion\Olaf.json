{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Olaf": {"id": "<PERSON>", "key": "2", "name": "<PERSON>", "title": "il berserker", "image": {"full": "Olaf.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "2000", "num": 0, "name": "default", "chromas": false}, {"id": "2001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "2002", "num": 2, "name": "<PERSON>cial<PERSON>", "chromas": false}, {"id": "2003", "num": 3, "name": "Brolaf", "chromas": true}, {"id": "2004", "num": 4, "name": "<PERSON>kill", "chromas": false}, {"id": "2005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "2006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "2015", "num": 15, "name": "Olaf SKT T1", "chromas": false}, {"id": "2016", "num": 16, "name": "<PERSON>", "chromas": true}, {"id": "2025", "num": 25, "name": "<PERSON>", "chromas": true}, {"id": "2035", "num": 35, "name": "<PERSON> III: Lost Chapter", "chromas": true}, {"id": "2044", "num": 44, "name": "Olaf <PERSON>", "chromas": true}], "lore": "<PERSON> è una forza distruttiva inarrestabile che non desidera altro che una morte gloriosa in combattimento. Nato nella brutale penisola freljordiana di Lokfar, ricevette una profezia per la quale sarebbe morto pacificamente. Una fine da codardi, un'onta, presso la sua gente. Per questo è partito in cerca della sua morte, alimentato dalla rabbia, pronto a massacrare guerrieri e belve leggendarie alla ricerca di un avversario in grado di fermarlo. Ora è un brutale combattente dell'Artiglio d'Inverno e attende la sua fine nelle grandi guerre che si preparano.", "blurb": "<PERSON> è una forza distruttiva inarrestabile che non desidera altro che una morte gloriosa in combattimento. Nato nella brutale penisola freljordiana di Lokfar, ricevette una profezia per la quale sarebbe morto pacificamente. Una fine da codardi, un'onta...", "allytips": ["<PERSON> può combinare <PERSON><PERSON>rserk<PERSON>, <PERSON><PERSON> fero<PERSON> e <PERSON>rok per diventare inaspettatamente forte.", "Il bonus curativo di Colpi feroci amplifica il tuo rubavita da tutte le fonti, insieme alle guarigioni dagli alleati."], "enemytips": ["<PERSON> diventa sempre più pericoloso man mano che perde salute. Conserva i tuoi impedimenti per finirlo.", "Impedire a Olaf di recuperare la sua ascia minimizza la quantità di aggressioni nei tuoi confronti durante la fase in corsia.", "<PERSON>, pur essendo immune agli impedimenti, ha le difese dai danni ridotte, durant<PERSON>. Se non riesci a fuggire mentre Olaf esegue Ragnarok, prova a concentrare i danni su di lui insieme alla tua squadra."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 119, "mp": 316, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.7, "attackspeedperlevel": 2.7, "attackspeed": 0.694}, "spells": [{"id": "OlafAxeThrowCast", "name": "<PERSON>cia turbinante", "description": "<PERSON> lancia un'ascia nel suolo in un punto bersaglio, infliggendo danni ai nemici colpiti lungo il suo percorso e riducendone armatura e velocità di movimento. Se <PERSON> raccoglie l'ascia, la ricarica dell'abilità viene azzerata.", "tooltip": "<PERSON> lancia un'ascia, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e <status>rallentando</status> i nemici di un {{ slowamount*100 }}% fino a {{ e3 }} secondi (in base alla distanza percorsa). I campioni colpiti perdono <scaleArmor>{{ shredamount*100 }}% di armatura</scaleArmor> per {{ debuffduration }} secondi.<br /><br />Se <PERSON> raccoglie l'ascia, la ricarica di questa abilità si riduce a {{ tooltipcdrefund }} secondi o viene del tutto rimborsata se sono passati {{ tooltipcdrefund }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "<PERSON><PERSON> ai mostri", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ monsterdamage }} -> {{ monsterdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [30, 30, 30, 30, 30], [2.5, 2.5, 2.5, 2.5, 2.5], [1.5, 1.5, 1.5, 1.5, 1.5], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "30", "2.5", "1.5", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "OlafAxeThrowCast.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OlafFrenziedStrikes", "name": "<PERSON><PERSON> s<PERSON>", "description": "<PERSON> ottiene uno scudo e la sua velocità d'attacco è aumentata.", "tooltip": "<PERSON> o<PERSON> <attackSpeed>{{ attackspeed*100 }}% velocità d'attacco</attackSpeed> per {{ duration }} secondi e <shield>uno scudo da {{ baseshield }} più un {{ shieldpercmissinghp*100 }}% della salute mancante (fino a uno scudo massimo di {{ maxshieldcalc }} con meno di {{ thresholdformax*100 }}% salute)</shield> per {{ shieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Scudo base", "Ricarica"], "effect": ["{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "OlafFrenziedStrikes.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OlafRecklessStrike", "name": "Attacco temerario", "description": "Olaf attacca con così tanta forza che infligge danni puri al suo bersaglio e a sé stesso, recuperando la salute perduta se distrugge il bersaglio.", "tooltip": "<PERSON> brand<PERSON>ce ferocemente le sue asce, infliggendo <trueDamage>{{ totaldamage }} danni puri</trueDamage>. Se il nemico muore, il costo di questa abilità viene rimborsato.<br /><br />Gli attacchi riducono la ricarica di questa abilità di 1 secondo. I secondi diventano 2 attaccando i mostri.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "30", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Costa  salute", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "OlafRecklessStrike.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Costa  salute"}, {"id": "Olaf<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "Olaf ottiene passivamente un aumento di armatura e resistenza magica. <PERSON><PERSON>ò attivare questa abilità per diventare immune agli effetti di controllo finché continua ad attaccare.", "tooltip": "<spellPassive>Passiva:</spellPassive> <PERSON> <scaleArmor>{{ resists }} armatura</scaleArmor> e <scaleMR>{{ resists }} resistenza magica</scaleMR>.<br /><br /><spellActive>Attiva: </spellActive><PERSON> rimuove tutti gli effetti di <status>immobilizzazione</status> e <status>impedimento</status> su di sé e ne diventa immune per {{ duration }} secondi. Mentre è attiva, Olaf ottiene <scaleAD>{{ ad }} attacco fisico</scaleAD>. Colpire un campione con un attacco o <spellName>Attacco temerario</spellName> aumenta la durata di {{ durationextension }} secondi.<br /><br /><PERSON><PERSON><PERSON>, <PERSON> ottiene <speed>{{ haste*100 }}% velocità di movimento</speed> verso i campioni nemici per {{ hasteduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Resistenza magica e armatura", "Attacco fisico", "Velocità di movimento", "Ricarica"], "effect": ["{{ resists }} -> {{ resistsNL }}", "{{ flatad }} -> {{ flatadNL }}", "{{ haste*100.000000 }} -> {{ hastenl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "OlafRagnarok.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> <PERSON>", "description": "<PERSON> ottiene velocità d'attacco e rubavita in base alla sua salute mancante.", "image": {"full": "Olaf_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}