{"type": "language", "version": "15.14.1", "data": {"Back": "Înapoi", "Continue": "Con<PERSON><PERSON><PERSON>", "Language": "Limbă", "ItemInfo": "Info obiect", "NextRank_": "Următoarea poziţie:", "Rank_": "Poziţie:", "PlayingAs": "Joacă în calitate de", "PlayingAgainst": "<PERSON><PERSON><PERSON>", "CD_": "CD:", "Range": "Rază de acţiune", "Range_": "Rază de acţiune:", "Details_": "Detalii:", "PrimaryRole": "<PERSON><PERSON><PERSON>", "mobileCompanion": "<PERSON><PERSON><PERSON><PERSON>", "mobileForum": "Forum", "mobileFriends": "<PERSON><PERSON><PERSON><PERSON>", "mobilePleaseWait": "Te rugăm să aştepţi...", "mobileNews": "Ş<PERSON>ri", "modeClassic": "Clasic", "modeOdin": "Cu siguranță nu Dominion", "modeAram": "ARAM", "modeTutorial": "Tutorial", "modeOneforall": "FRONTEND_oneforall_game_mode_name", "modeFirstblood": "FRONTEND_firstblood_game_mode_name", "mode6v6": "FRONTEND_6v6_game_mode_name", "modeCof": "FRONTEND_cof_game_mode_name", "Map1": "Riftul Invocatorului", "Map8": "Cicatricea de Cristal", "Map10": "Pădurea Torsionată", "Map12": "Abisul Urletelor", "categoryChampion": "Campioni", "categoryItem": "obiecte", "categoryMastery": "M<PERSON><PERSON><PERSON><PERSON>", "categoryRune": "<PERSON><PERSON>", "categorySummoner": "Vrăji de invocator", "Gold": "<PERSON><PERSON>", "Level": "<PERSON><PERSON>", "Abilities": "Abilităţi", "ChampionInfo": "Informaţii despre campion", "Lore": "Poveste", "Stats": "Statistici", "Tips": "<PERSON><PERSON><PERSON><PERSON>", "statAbility": "<PERSON><PERSON><PERSON><PERSON>", "statAttack": "Atac", "statDefense": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statDifficulty": "Dificultate", "statUtility": "Utilitate", "Assassin": "<PERSON><PERSON><PERSON>", "Fighter": "<PERSON><PERSON><PERSON><PERSON>", "Marksman": "Țintaș", "Mage": "Mag", "Support": "Suport", "Tank": "Tan<PERSON>", "spells_Self": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_0": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_1": "Ţintă", "spells_target_2": "Arie", "spells_target_3": "Con", "spells_target_4": "Arie proprie", "spells_target_5": "Variabilă", "spells_target_6": "Locaţie", "spells_target_7": "Direcţie", "spells_target_8": "Direcţie vector", "spells_target_100": "Global", "AllItems": "Toate obiectele", "Armor": "<PERSON><PERSON><PERSON>", "Attack": "Atac", "AttackSpeed": "Viteza de atac", "Consumable": "Consumabile", "CooldownReduction": "Timp de reactivare", "CriticalStrike": "Lovitură critică", "Damage": "Daune", "Defense": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Health": "Viaţă", "HealthRegen": "Regenerare viaţă", "LifeSteal": "<PERSON>rt <PERSON>", "Magic": "<PERSON><PERSON>", "Mana": "Mană", "ManaRegen": "Regenerare mană", "Movement": "Mişcare", "SpellBlock": "Rezistență la magie: ", "SpellDamage": "Puterea abilităţilor", "Boots": "<PERSON><PERSON><PERSON>", "NonbootsMovement": "Alte obiecte de mişcare", "Tenacity": "Tenacitate", "SpellVamp": "Vampirism magic", "GoldPer": "Venit în aur", "Slow": "Încetinire", "Aura": "<PERSON><PERSON><PERSON>", "Active": "Activă", "MagicPenetration": "Penetrare magică", "ArmorPenetration": "<PERSON>et<PERSON><PERSON>", "colloq_Armor": ";armur<PERSON>", "colloq_Attack": ";", "colloq_AttackSpeed": ";as", "colloq_Consumables": ";", "colloq_CriticalStrike": ";", "colloq_Damage": ";", "colloq_Defense": ";", "colloq_Health": ";", "colloq_HealthRegen": ";", "colloq_LifeSteal": ";furt de via<PERSON>", "colloq_Magic": ";", "colloq_Mana": ";", "colloq_ManaRegen": ";", "colloq_Movement": ";movespeed", "colloq_SpellBlock": ";mr", "colloq_SpellDamage": ";ap", "colloq_Consumable": ";", "colloq_Boots": ";", "colloq_NonbootsMovement": ";", "colloq_CooldownReduction": ";cdr", "colloq_Tenacity": ";", "colloq_SpellVamp": ";vampirism magic", "colloq_GoldPer": ";gp10", "colloq_Slow": ";", "colloq_Aura": ";", "colloq_Active": ";", "colloq_MagicPenetration": ";", "colloq_ArmorPenetration": ";", "RecommendedItems": "obiecte recomandate", "recommended_starting": "Obiecte de început", "recommended_essential": "Obiecte esenţiale", "recommended_offensive": "Obiecte de atac", "recommended_defensive": "Obiecte de apărare", "recommended_consumables": "Consumabile", "Require_": "Necesită:", "Cost_": "Cost:", "OriginalCost_": "Cost original:", "SellsFor_": "Preț de vânzare: ", "UpgradeCost_": "Cost îmbună<PERSON><PERSON><PERSON><PERSON>:", "Builds_": "Se construieşte în:", "ButtonBuy": "CUMPĂRĂ", "ButtonSell": "VINDE", "SpecialRecipeSmall": "*", "SpecialRecipeLarge": "Special", "FlatArmorMod": "<PERSON><PERSON><PERSON>", "FlatAttackSpeedMod": "Viteza de atac", "FlatBlockMod": "Blocare", "FlatCritChanceMod": "Şansă crit.", "FlatCritDamageMod": "Daune crit.", "FlatEnergyPoolMod": "Energie", "FlatEnergyRegenMod": "Regenerare energie / 5", "FlatEXPBonus": "Bonus exp.", "FlatHPPoolMod": "Viaţă", "FlatHPRegenMod": "Regenerare viaţă / 5", "FlatMagicDamageMod": "Puterea abilităţilor", "FlatMovementSpeedMod": "Viteză de mișcare", "FlatMPPoolMod": "Mană", "FlatMPRegenMod": "Regenerare mană / 5", "FlatPhysicalDamageMod": "Daune fizice", "FlatSpellBlockMod": "Rezistență la magie", "PercentArmorMod": "% Armură", "PercentAttackSpeedMod": "% Viteză de atac", "PercentBlockMod": "% Blocare", "PercentCritChanceMod": "% Şansă crit.", "PercentCritDamageMod": "% Daune crit.", "PercentDodgeMod": "% Eschivă", "PercentEXPBonus": "% Bonus exp.", "PercentHPPoolMod": "% Viaţă maximă", "PercentHPRegenMod": "% viaţă / 5", "PercentMagicDamageMod": "% Puterea abilităţilor max", "PercentMovementSpeedMod": "% Viteză de mișcare", "PercentMPPoolMod": "% Mană max", "PercentMPRegenMod": "% Mană / 5", "PercentPhysicalDamageMod": "% Daune fizice", "PercentSpellBlockMod": "% Rezistență la magie", "rFlatArmorModPerLevel": "Armură la nivelul 18", "rFlatArmorPenetrationMod": "<PERSON><PERSON>", "rFlatArmorPenetrationModPerLevel": "Penetrare armură la nivelul 18", "rFlatCritChanceModPerLevel": "Şansă crit la nivelul 18", "rFlatCritDamageModPerLevel": "Daune critice la nivelul 18", "rFlatDodgeMod": "Eschivă", "rFlatDodgeModPerLevel": "Eschivă la nivelul 18", "rFlatEnergyModPerLevel": "Energie la nivelul 18", "rFlatEnergyRegenModPerLevel": "Regenerare energie / 5 la nivelul 18", "rFlatGoldPer10Mod": "Aur pe 10", "rFlatHPModPerLevel": "Viaţă la nivelul 18", "rFlatHPRegenModPerLevel": "Regenerare viaţă / 5 la nivelul 18", "rFlatMagicDamageModPerLevel": "Puterea abilităţilor la nivelul 18", "rFlatMagicPenetrationMod": "Pen. magie", "rFlatMagicPenetrationModPerLevel": "Penetrare magică la nivelul 18", "rFlatMovementSpeedModPerLevel": "Viteză de mișcare la nivelul 18", "rFlatMPModPerLevel": "Mană la nivelul 18", "rFlatMPRegenModPerLevel": "Regenerare mană / 5 la nivelul 18", "rFlatPhysicalDamageModPerLevel": "Daune fizice la nivelul 18", "rFlatSpellBlockModPerLevel": "Rezistență magie la nivelul 18", "rFlatTimeDeadMod": "<PERSON><PERSON><PERSON> moar<PERSON>", "rFlatTimeDeadModPerLevel": "Durată moarte la nivelul 18", "rPercentArmorPenetrationMod": "% pen. armură", "rPercentArmorPenetrationModPerLevel": "% penetrare armură la nivelul 18", "rPercentAttackSpeedModPerLevel": "Viteză atac la nivelul 18", "rPercentCooldownMod": "% Timp de reactivare", "rPercentCooldownModPerLevel": "% Timp de reactivare la nivelul 18", "rPercentMagicPenetrationMod": "Pen. magie %", "rPercentMagicPenetrationModPerLevel": "Penetrare magică % la nivelul 18", "rPercentMovementSpeedModPerLevel": "% Viteză de mișcare la nivelul 18", "rPercentTimeDeadMod": "% Durat<PERSON> moarte", "rPercentTimeDeadModPerLevel": "% Durată moarte / niv.", "PercentLifeStealMod": "% Bonus de furt de viaţă", "PercentSpellVampMod": "% Bonus de vampirism magic", "masteryFerocity": "Ferocitate", "masteryCunning": "Șiretenie", "masteryResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "native_﻿ar": "العربية", "native_bg": "български език", "native_cs": "čeština", "native_de": "De<PERSON>ch", "native_el": "ελληνικά", "native_en": "English", "native_es": "español", "native_fr": "français", "native_hu": "magyar", "native_id": "Bahasa Indonesia", "native_it": "Italiano", "native_ja": "日本語", "native_ko": "조선말", "native_nl": "Nederlands", "native_pl": "język polski", "native_pt": "português", "native_ro": "română", "native_ru": "русский язык", "native_th": "ภาษาไทย", "native_tr": "Türkçe", "native_vi": "Tiếng <PERSON>", "native_zh": "汉语", "native_zh_CN": "简体字", "native_zh_MY": "简体字", "native_zh_TW": "繁體字"}, "tree": {"searchKeyIgnore": "", "searchKeyRemap": []}}