{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rammus": {"id": "<PERSON><PERSON>", "key": "33", "name": "Ράμους", "title": "ο Θωρακισμένος", "image": {"full": "Rammus.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "33000", "num": 0, "name": "default", "chromas": false}, {"id": "33001", "num": 1, "name": "Βασιλιάς Ράμους", "chromas": false}, {"id": "33002", "num": 2, "name": "Επιχρωμιωμένος Ράμους", "chromas": false}, {"id": "33003", "num": 3, "name": "Ράμους από Λάβα", "chromas": false}, {"id": "33004", "num": 4, "name": "Ράμους του Φρέλιορντ", "chromas": false}, {"id": "33005", "num": 5, "name": "Νίντζα Ράμους", "chromas": false}, {"id": "33006", "num": 6, "name": "Μεταλλικός Ράμους", "chromas": false}, {"id": "33007", "num": 7, "name": "Φύλακας της Άμμου Ράμους", "chromas": false}, {"id": "33008", "num": 8, "name": "Λίμπερο Ράμους", "chromas": true}, {"id": "33016", "num": 16, "name": "Μαγοτεχνικ<PERSON>ς Ράμους", "chromas": false}, {"id": "33017", "num": 17, "name": "Αστροναύτης Ράμους", "chromas": true}, {"id": "33026", "num": 26, "name": "Ράμους Υπερασπιστής Ντούριαν", "chromas": true}], "lore": "Για πολλούς είναι αντικείμενο λατρείας. Άλλοι τον περιφρονούν. Το παράξενο ον που είναι γνωστό ως Ράμους παραμένει ένας άλυτος γρίφος. Υπάρχουν πολλές αντικρουόμενες θεωρίες για την προέλευση του Ράμους και του κελύφους με καρφιά που τον προστατεύει: ορισμένοι υποστηρίζουν ότι είναι ημίθεος, άλλοι ιερός μάντης, ενώ κάποιοι θεωρούν ότι πρόκειται για ένα ζώο που μεταλλάχτηκε με μαγικό τρόπο. Όποια και αν είναι η αλήθεια, ο Ράμους δεν μιλά ποτέ για τον εαυτό του και δεν υπολογίζει κανέναν στις περιπλανήσεις του στην έρημο της Σουρίμα.", "blurb": "Για πολλούς είναι αντικείμενο λατρείας. Άλλοι τον περιφρονούν. Το παράξενο ον που είναι γνωστό ως Ράμους παραμένει ένας άλυτος γρίφος. Υπάρχουν πολλές αντικρουόμενες θεωρίες για την προέλευση του Ράμους και του κελύφους με καρφιά που τον προστατεύει:...", "allytips": ["Η ικανότητα Μπάλα Επίθεσης μπορεί να χρησιμοποιηθεί ως αποτελεσματικός μηχανισμός απόδρασης.", "Η χρήση προσβολών σε έναν Ήρωα δίπλα από τον Πύργο σας μπορεί να προκαλέσει την επίθεση του Πύργου στους αντιπάλους σας.", "Οι ικανότητες Δονήσεις και Αμυντική Συσπείρωση, μπορούν να χρησιμοποιηθούν αργότερα στο παιχνίδι για την καταστροφή πύργων. Αν βρίσκεστε σε αδιέξοδο κατά τη διάρκεια μιας ομαδικής μάχης, είναι συχνά χρήσιμο να απομακρύνεστε και να επιτίθεστε στα κτίρια."], "enemytips": ["Δώστε ιδιαίτερη προσοχή όταν η Αμυντική Συσπείρωσή του απενεργοποιηθεί. Ο Ράμους έχει αρκετά χαμηλότερα στατιστικά από ένα κανονικό Τανκ όταν έχει βγει από την στάση αυτή.", "Ο Ράμους συχνά συγκεντρώνει υψηλή Θωράκιση, κάτι που τον αφήνει ευάλωτο σε ζημιές από μάγους όταν δεν βρίσκεται στην Αμυντική Συσπείρωση."], "tags": ["Tank"], "partype": "Μάνα", "info": {"attack": 4, "defense": 10, "magic": 5, "difficulty": 5}, "stats": {"hp": 675, "hpperlevel": 100, "mp": 310, "mpperlevel": 33, "movespeed": 335, "armor": 35, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.55, "mpregen": 7.85, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.215, "attackspeed": 0.7}, "spells": [{"id": "PowerBall", "name": "Μπάλα Επίθεσης", "description": "Ο Ράμους κινείται γρήγορα με τη μορφή μπάλας προς τους εχθρούς του και τους προκαλεί ζημιά, ενώ επιβραδύνει τους στόχους που επηρεάζονται από τη σύγκρουση.", "tooltip": "Ο Ράμους γίνεται μπάλα, αποκτώντας <speed>{{ minimummovespeed }} Ταχύτητα Κίνησής</speed>, αναπτύσσοντας ταχύτητα έως <speed>{{ maximummovespeed }} Ταχύτητα Κίνησης</speed> σταδιακά σε {{ rollduration }} δευτ. Ο Ράμους σταματά όταν συγκρουστεί με έναν εχθρό, προκαλώντας <magicDamage>{{ powerballdamage }} Μαγική Ζημιά</magicDamage>, <status>Απωθώντας</status> και <status>Επιβραδύνοντας</status> τους κοντινούς εχθρούς κατά {{ slowpercent }}% για {{ slowduration }} δευτ.<br /><br /><recast>Νέα χρήση</recast>: Ο Ράμους διακόπτει την ικανότητά του νωρίτερα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Επιβράδυνση", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1000, 1000, 1000, 1000, 1000]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerBall.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DefensiveBall<PERSON>url", "name": "Αμυντική Συσπείρωση", "description": "Ο Ράμους κινείτ<PERSON><PERSON> αμυντικ<PERSON>, αυ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σημαντικ<PERSON> τη Θωράκιση και την Αντίσταση Μαγείας του, και επιστρέφοντα<PERSON> ζημιά στους αντιπάλους που του επιτίθενται.", "tooltip": "Ο Ράμους παίρνει αμυντική στάση για {{ buffduration }} δευτ., αποκτά <scaleArmor>{{ bonusarmortooltip }} Θωράκιση</scaleArmor> και <scaleMR>{{ bonusmrtooltip }} Αντίσταση Μαγείας</scaleMR> και προκαλεί <magicDamage>{{ returndamagecalc }} Μαγική Ζημιά</magicDamage> στους εχθρούς που του επιτίθενται.<br /><br /><recast>Νέα χρήση</recast>: Ο Ράμους διακόπτει την ικανότητά του νωρίτερα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Επιπ<PERSON><PERSON><PERSON><PERSON> Θωράκιση", "Επιπλέον Αντίσταση Μαγείας", "Μπ<PERSON>ν<PERSON><PERSON><PERSON> πο<PERSON><PERSON><PERSON><PERSON><PERSON>ωράκισης", "Μπ<PERSON>ν<PERSON><PERSON><PERSON> ποσο<PERSON>τ<PERSON> Αντίστασης Μαγείας"], "effect": ["{{ flatbonusarmor }} -> {{ flatbonusarmorNL }}", "{{ flatbonusmr }} -> {{ flatbonusmrNL }}", "{{ bonusarmorpercent*100.000000 }}% -> {{ bonusarmorpercentnl*100.000000 }}%", "{{ bonusmrpercent*100.000000 }}% -> {{ bonusmrpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DefensiveBallCurl.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PuncturingTaunt", "name": "Μανιασμένη Πρόκληση", "description": "Ο Ράμους προκαλε<PERSON> έναν αντίπαλο Ήρωα ή ένα τέρας και αναγκάζει τον στόχο του να του επιτεθεί χωρίς να υπολογίζει τίποτε άλλο.", "tooltip": "Ο Ράμους <status>Προ<PERSON><PERSON>λε<PERSON></status> έναν αντίπαλο Ήρωα ή ένα τέρας για {{ duration }} δευτ. Τα τέρατα δέχονται <magicDamage>{{ monsterdamagecalc }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Διάρκεια", "Ζημιά σε τέρατα"], "effect": ["{{ duration }} -> {{ durationNL }}", "{{ monsterdamage }} -> {{ monsterdamageNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "PuncturingTaunt.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Tremors2", "name": "Ιπτά<PERSON><PERSON><PERSON><PERSON>νημα", "description": "Ο Ράμους πηδά στον αέρα και προσγειώνεται με δύναμη στην επιλεγμένη περιοχή, προκαλώντας Μαγική Ζημιά και επιβραδύνοντας τους εχθρούς. Εάν γίνει χρήση όσο ο Ράμους είναι σε Μπάλα Επίθεσης, πετάει τους εχθρούς στον αέρα και κοντά στο κέντρο.", "tooltip": "Ο Ράμους πηδά στον αέρα και προσγειώνεται με δύναμη σε μια περιοχή, προκαλώντας <magicDamage>{{ initialdamagecalc }} Μαγική Ζημιά</magicDamage> και <status>Επιβραδύνοντας</status> τους αντιπάλους κατά {{ slowamount*100 }}% για {{ slowduration }} δευτ. Αν χρησιμοποιηθεί κατά τη διάρκεια της <spellName>Μπάλας Επίθεσης</spellName>, οι εχθροί που βρίσκονται στο κέντρο δέχονται επιπλέον <magicDamage>{{ spell.powerball:powerballdamage }} Μαγική Ζημιά</magicDamage> και <status>Εκτοξεύονται στον Αέρα</status> για {{ knockupduration }} δευτ.<br /><br />Στη συνέχεια, ο Ράμους προκαλεί {{ numberofpulses }} μετασεισμούς στην περιοχή μέσα σε {{ buffduration }} δευτ., επαναλαμβάνοντας την <status>Επιβράδυνση</status>.<br /><br />Η εμβέλεια αυτής της ικανότητας αυξάνεται ανάλογα με την <speed>Ταχύτητα Κίνησης</speed> του Ράμους.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Επιβράδυνση", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "Tremors2.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Κέ<PERSON><PERSON><PERSON>ος με Καρφιά", "description": "Ο Ράμους αποκτά μπόνους αναλογική αύξηση της Ζημιάς Επίθεσής του, βάσει της Θωράκισης και της Αντίστασης Μαγείας του.", "image": {"full": "Armordillo_ScavengeArmor.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}