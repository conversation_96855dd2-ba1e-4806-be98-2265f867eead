{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zac": {"id": "<PERSON><PERSON>", "key": "154", "name": "<PERSON><PERSON>", "title": "die Geheimwaffe", "image": {"full": "Zac.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "154000", "num": 0, "name": "default", "chromas": true}, {"id": "154001", "num": 1, "name": "Spezialwaffe Zac", "chromas": false}, {"id": "154002", "num": 2, "name": "Poolparty-Zac", "chromas": false}, {"id": "154006", "num": 6, "name": "SKT T1-Zac", "chromas": false}, {"id": "154007", "num": 7, "name": "Stahlkrieger-Zac", "chromas": true}, {"id": "154014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "154024", "num": 24, "name": "Pikanter Dip-Zac", "chromas": true}], "lore": "Zac ist durch eine giftige Substanz entstanden, die aus einem undichten Chemtech-Tank herausgetropft war und in einer abgeschiedenen Höhle in Zhauns Grube eine Lache gebildet hatte. Trot<PERSON> dieser bescheidenen Herkunft hat sich Zac prächtig entwickelt – von der Ursuppe zu einem denkenden Wesen, das in den Rohren der Stadt haust und diese gelegentlich verlässt, um den Hilflosen beizustehen oder die marode Infrastruktur von Zhaun wiederaufzubauen.", "blurb": "Zac ist durch eine giftige Substanz entstanden, die aus einem undichten Chemtech-Tank herausgetropft war und in einer abgeschiedenen Höhle in Zhauns Grube eine Lache gebildet hatte. T<PERSON><PERSON> dieser bescheidenen Herkunft hat sich Zac prächtig entwickelt –...", "allytips": ["Glibbertröpfchen einzusammeln ist für dein Überleben sehr wichtig.", "<PERSON>n „Zellteilung“ bereit ist, versuche an einer Stelle zu sterben, an der es für das gegnerische Team schwierig ist, deine Tröpfchen zu töten.", "Wenn du „Elastische Schleuder“ aus dem Nebel des Krieges heraus auflädst, haben deine Widersacher weniger Zeit zu reagieren."], "enemytips": ["<PERSON><PERSON> heilt sich mit <PERSON><PERSON><PERSON> des Glibbers, der von ihm abfällt. Du kannst die Glibberstückchen zerstören, indem du auf sie drauf trittst.", "<PERSON>öte alle Trö<PERSON><PERSON> von <PERSON>, wenn er auseinanderfällt, damit er sich nicht neu bilden kann.", "Verstummungen, Betäubungen, Festhalteeffekte und Hochschlagen unterbrechen Zac, wenn er „Elastische Schleuder“ auflädt."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 8}, "stats": {"hp": 685, "hpperlevel": 109, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.6, "attackspeed": 0.736}, "spells": [{"id": "ZacQ", "name": "Dehnschlag", "description": "<PERSON><PERSON> dehnt einen Arm und greift sich einen Gegner. Wenn er daraufhin einen anderen Gegner angreift, werden beide Ziele gegeneinander geworfen.", "tooltip": "<PERSON><PERSON> streckt seinen Arm nach dem ersten getroffenen Gegner aus, um ihm <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zuzufügen und ihn kurzzeitig zu <status>verlangsamen</status>. Zacs nächster Angriff hat erhöhte Reichweite und löst den gleichen Schaden und die gleiche <status>Verlangsamung</status> aus. <br /><br />Trifft Zac einen <i>anderen</i> Gegner mit diesem Angriff, <status>schleudert</status> er beide Gegner aufeinander zu. Wenn sie zusammenstoßen, erleiden sie und angrenzende Gegner <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und werden kurzzeitig <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [8, 8, 8, 8, 8], [800, 800, 800, 800, 800], [-0.4, -0.4, -0.4, -0.4, -0.4], [0.5, 0.5, 0.5, 0.5, 0.5], [2.5, 2.5, 2.5, 2.5, 2.5], [900, 900, 900, 900, 900], [700, 700, 700, 700, 700], [300, 300, 300, 300, 300], [300, 300, 300, 300, 300]], "effectBurn": [null, "40/55/70/85/100", "8", "800", "-0.4", "0.5", "2.5", "900", "700", "300", "300"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens ({{ healthcosttooltip }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZacQ.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "Kostet {{ e2 }}&nbsp;% des aktuellen Lebens ({{ healthcosttooltip }})"}, {"id": "ZacW", "name": "Instabile Materie", "description": "Zac explodiert und fügt nahen Gegnern einen Prozentsatz ihres maximalen Lebens als magischen Schaden zu.", "tooltip": "<PERSON><PERSON><PERSON> explodiert und fügt nahen <PERSON> <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ basedamage }} + {{ displaypercentdamage }} des maximalen Lebens zu.<br /><br />Wenn er <keywordMajor>G<PERSON>bber</keywordMajor> absorbiert, verringert sich die Abklingzeit dieser Fähigkeit um 1&nbsp;Sekunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Schaden abhängig vom maximalen Leben "], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealthdamage*100.000000 }}&nbsp;% -> {{ basemaxhealthdamagenl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 50, 60, 70, 80], [4, 4, 4, 4, 4], [0.04, 0.05, 0.06, 0.07, 0.08], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/50/60/70/80", "4", "0.04/0.05/0.06/0.07/0.08", "1", "200", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens ({{ tooltiphealthcost }})", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "ZacW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "Kostet {{ e2 }}&nbsp;% des aktuellen Lebens ({{ tooltiphealthcost }})"}, {"id": "ZacE", "name": "Elastische Schleuder", "description": "<PERSON><PERSON> verankert seine Arme im Boden und spannt sich nach hinten, um sich dann nach vorn zu schleudern.", "tooltip": "<charge>Aufladungsbeginn:</charge> <PERSON><PERSON> zieht sich zusammen und lädt über {{ e4 }}&nbsp;Sekunde(n) einen Sprung auf.<br /><br /><release>Auslösen:</release> Zac saust durch die Luft, <status>schleudert</status> <PERSON><PERSON><PERSON> dort, wo er landet, {{ maxstun }}&nbsp;Sekunde(n) (abhä<PERSON><PERSON> von der Aufladedauer) in die Luft und verursacht <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage>. Zac bildet einen zusätzlichen Klumpen <keywordMajor>Glibber</keywordMajor> für jeden getroffenen gegnerischen Champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Reichweite", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [4, 4, 4, 4, 4], [1200, 1350, 1500, 1650, 1800], [0.9, 1, 1.1, 1.2, 1.3], [0.5, 0.5, 0.5, 0.5, 0.5], [500, 500, 500, 500, 500], [1350, 1350, 1350, 1350, 1350], [0.6, 0.6, 0.6, 0.6, 0.6], [265, 265, 265, 265, 265], [1, 1, 1, 1, 1]], "effectBurn": [null, "60/105/150/195/240", "4", "1200/1350/1500/1650/1800", "0.9/1/1.1/1.2/1.3", "0.5", "500", "1350", "0.6", "265", "1"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens ({{ healthcosttooltip }})", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "Kostet {{ e2 }}&nbsp;% des aktuellen Lebens ({{ healthcosttooltip }})"}, {"id": "ZacR", "name": "<PERSON>sen wir los!", "description": "Zac hüpft 4&nbsp;<PERSON>, schleudert getroffene Gegner in die Luft und verlangsamt sie.", "tooltip": "<PERSON><PERSON> springt {{ bounces }}-mal. Der erste Sprung, der jeden Gegner trifft, <status>stößt sie zurück</status> und verursacht <magicDamage>{{ damageperbounce }}&nbsp;magischen Schaden</magicDamage>. Nachfolgende Sprünge verursachen <magicDamage>{{ damagepersubsequentbounce }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamen</status> Gegner {{ slowduration }}&nbsp;Sekunde lang um {{ slowamount*100 }}&nbsp;%.<br /><br />Zac erhält mit der Zeit bis zu <speed>{{ endingms*100 }}&nbsp;% Lauftempo</speed> und kann <spellName>Instabile Materie</spellName> im Sprung aktivieren.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden&nbsp;– <PERSON><PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamagebounce }} -> {{ basedamagebounceNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [1.1, 1.1, 1.1], [700, 850, 1000], [-0.3, -0.4, -0.5], [2.5, 2.5, 2.5], [275, 275, 275], [300, 300, 300], [1, 1, 1], [0.25, 0.25, 0.25], [0.1, 0.1, 0.1]], "effectBurn": [null, "150/250/350", "1.1", "700/850/1000", "-0.3/-0.4/-0.5", "2.5", "275", "300", "1", "0.25", "0.1"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Zellteilung", "description": "<PERSON><PERSON>, wenn Z<PERSON> einen Gegner mit einer Fähigkeit trifft, verliert er einen Klumpen von sich, der wieder aufgenommen werden kann, um Leben wiederherzustellen. Wenn er tödlichen Schaden erle<PERSON>t, zer<PERSON><PERSON><PERSON><PERSON> Zac in 4 Klumpen, die versuchen sich wieder zu vereinen. Sollten Klumpen übrig bleiben, wird er abhängig vom Leben der überlebenden Klumpen wiederbelebt. Jeder Klumpen besitzt einen Prozentsatz von Zacs maximalem Leben, Rüstung und Magieresistenz. Diese Fähigkeit hat eine Abklingzeit von 5 Minuten.", "image": {"full": "ZacPassive.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}