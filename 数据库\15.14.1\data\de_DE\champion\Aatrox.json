{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aatrox": {"id": "Aatrox", "key": "266", "name": "Aatrox", "title": "Die Klinge der Düsteren", "image": {"full": "Aatrox.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "266000", "num": 0, "name": "default", "chromas": false}, {"id": "266001", "num": 1, "name": "Justikar-Aatrox", "chromas": false}, {"id": "266002", "num": 2, "name": "Mecha-Aatrox", "chromas": true}, {"id": "266003", "num": 3, "name": "Seejäger-Aatrox", "chromas": false}, {"id": "266007", "num": 7, "name": "Blutmond-Aatrox", "chromas": false}, {"id": "266008", "num": 8, "name": "Blutmond-Aatrox (Prestige)", "chromas": false}, {"id": "266009", "num": 9, "name": "Siegreicher Aatrox", "chromas": true}, {"id": "266011", "num": 11, "name": "Odyssee-Aatrox", "chromas": true}, {"id": "266020", "num": 20, "name": "Blutmond-Aatrox (Prestige 2022)", "chromas": false}, {"id": "266021", "num": 21, "name": "Mondfinsternis-Aatrox", "chromas": true}, {"id": "266030", "num": 30, "name": "DRX-Aatrox", "chromas": true}, {"id": "266031", "num": 31, "name": "DRX-Aatrox (Prestige)", "chromas": false}, {"id": "266033", "num": 33, "name": "Primordianischer Aatrox", "chromas": true}], "lore": "Einst waren Aatrox und seine Brüder ehrenhafte Verteidiger Shurimas gegen die Leere, die zu einer noch größeren Bedrohung für Runeterra wurden und schließlich nur durch hinterlistige, sterbliche Zauberei besiegt werden konnten. Aber nach Jahrhunderten der Gefangenschaft war Aatrox der Erste, der es erneut in die Freiheit schaffte, indem er diejenigen verdarb und verwandelte, die unbedacht genug waren, die magische Waffe zu benutzen, die seine Essenz enthielt. Jetzt bedient er sich eines gestohlenen Körpers, ein groteskes Abbild seiner ehemaligen Gestalt, und will die apokalyptische Rache nehmen, auf die er seit Ewigkeiten sinnt.", "blurb": "Einst waren Aatrox und seine Brüder ehrenhafte Verteidiger Shurimas gegen die Leere, die zu einer noch größeren Bedrohung für Runeterra wurden und schließlich nur durch hinterlistige, sterbliche Zauberei besiegt werden konnten. Aber nach Jahrhunderten...", "allytips": ["<PERSON><PERSON>e „Düstersprung“, während du „Die Klinge der Düsteren“ wirkst, um deine Chance auf Treffer zu erhöhen.", "Die Massenkontrolle deiner Verbündeten oder deine eigenen „Infernalischen Ketten“ können es dir erleichtern, „Die Klinge der Düsteren“ einzusetzen.", "Verwende „Weltenvernichter“, wenn du dir sicher bist, dass du einen Kampf erzwingen kannst."], "enemytips": ["Der Wirkungsbereich von Aatrox' Angriffen ist schon früh sichtbar, so dass du ihnen gut ausweichen kannst.", "Aatrox' „Infernalischen Ketten“ kann man leichter entkommen, wenn man zur Seite oder direkt auf Aatrox zuläuft.", "<PERSON><PERSON>d, wenn Aatrox seine Ult einsetzt, damit er sich nicht wiederbeleben kann."], "tags": ["Fighter"], "partype": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 38, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.651}, "spells": [{"id": "AatroxQ", "name": "Die Klinge der Düsteren", "description": "Aatrox schwingt sein Großschwert und verursacht normalen Schaden. Er kann bis zu dreimal zuschlagen und jeder Sc<PERSON>ag hat einen anderen Wirkungsbereich.", "tooltip": "Aatrox schwingt sein Großschwert und verursacht <physicalDamage>{{ qdamage }}&nbsp;normalen Schaden</physicalDamage>. <PERSON><PERSON> von der Schneide getroffen werden, werden sie kurzzeitig <status>hochgeschleudert</status> und erleiden stattdessen <physicalDamage>{{ qedgedamage }}&nbsp;normalen Schaden</physicalDamage>. Diese Fähigkeit kann zweimal <recast>reaktiviert</recast> werden. Mit jeder Reaktivierung ändert sich die Form und der Schaden erhöht sich um 25&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Skalierung mit Gesamtangriffsschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ qtotaladratio*100.000000 }}&nbsp;% -> {{ qtotaladrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "AatroxW", "name": "Infernalische Ketten", "description": "Aatrox schlägt auf den Boden und verursacht Schaden am ersten getroffenen Gegner. Champions und große Monster müssen den Einschlagsbereich schnell verlassen oder werden wieder in die Mitte gezogen und erleiden erneut Schaden.", "tooltip": "Aatrox schleudert eine <PERSON>, die den ersten getroffenen Gegner {{ wslowduration }}&nbsp;Sekunden lang um {{ wslowpercentage*-100 }}&nbsp;% <status>verlangsamt</status> und <physicalDamage>{{ wdamage }}&nbsp;normalen Schaden</physicalDamage> verursacht. Champions und große Dschungelmonster müssen den Einschlagsbereich innerhalb von {{ wslowduration }}&nbsp;Sekunden verlassen, sonst werden sie wieder in die Mitte <status>gezogen</status> und erleiden erneut Schaden in derselben Höhe.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wbasedamage }} -> {{ wbasedamageNL }}", "{{ wslowpercentage*-100.000000 }}&nbsp;% -> {{ wslowpercentagenl*-100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AatroxW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "AatroxE", "name": "Düstersprung", "description": "Aatrox heilt sich passiv, indem er gegnerischen Champions Schaden zufügt. Bei Aktivierung springt er in eine Richtung.", "tooltip": "<spellPassive>Passiv:</spellPassive> Aatrox heilt sich um <lifeSteal>{{ totalevamp }}</lifeSteal> des <PERSON>ens, den er Champions zufügt.<br /><br /><spellActive>Aktiv:</spellActive> Aatrox springt. Er kann diese Fähigkeit einsetzen, während er seine anderen Fähigkeiten vorbereitet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxE.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "AatroxR", "name": "Weltenvernichter", "description": "Aatrox entfesselt seine dämonische Form, wodurch er gegnerische Vasallen in der Nähe in Furcht versetzt und sich sein Angriffsschaden, seine Heilung sowie sein Lauftempo erhöhen. Wenn er an einem Kill beteiligt ist, wird dieser Effekt verlängert.", "tooltip": "Aatrox enthüllt seine wahre dämonische Form, versetzt nahe Vasallen {{ rminionfearduration }}&nbsp;Sekunden lang in <status>Furcht</status> und erhält <speed>{{ rmovementspeedbonus*100 }}&nbsp;% Lauftempo</speed>, das im Verlauf von {{ rduration }}&nbsp;Sekunden abfällt. Außerdem erhält er <scaleAD>{{ rtotaladamp*100 }}&nbsp;% Angriffsschaden</scaleAD> und erhöht seine <healing>Selbstheilung während der Dauer um {{ rhealingamp*100 }}&nbsp;%</healing>.<br /><br />Champion-Kills verlängern die Dauer dieses Effekts um {{ rextension }}&nbsp;Sekunden und erneuern den <speed>Lauftempo</speed>-Effekt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Erhöhung des Gesamtangriffsschaden", "Erhöhung der Heilung", "Lauftempo", "Abklingzeit"], "effect": ["{{ rtotaladamp*100.000000 }}&nbsp;% -> {{ rtotaladampnl*100.000000 }}&nbsp;%", "{{ rhealingamp*100.000000 }}&nbsp;% -> {{ rhealingampnl*100.000000 }}&nbsp;%", "{{ rmovementspeedbonus*100.000000 }}&nbsp;% -> {{ rmovementspeedbonusnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxR.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Todesbringer", "description": "Gelegentlich verursacht Aatrox' nächster normaler Angriff zusätzlichen <physicalDamage>normalen Schaden</physicalDamage> und heilt ihn abhängig vom maximalen Leben des Ziels. ", "image": {"full": "Aatrox_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}