{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Swain": {"id": "Swain", "key": "50", "name": "Swain", "title": "Wielki Generał Noxusu", "image": {"full": "Swain.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "50000", "num": 0, "name": "default", "chromas": false}, {"id": "50001", "num": 1, "name": "<PERSON><PERSON> z <PERSON>u <PERSON>nego", "chromas": false}, {"id": "50002", "num": 2, "name": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "50003", "num": 3, "name": "Swain <PERSON><PERSON>", "chromas": false}, {"id": "50004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "50011", "num": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Swain", "chromas": false}, {"id": "50012", "num": 12, "name": "Kryształowa Róża Swain", "chromas": true}, {"id": "50021", "num": 21, "name": "Swain <PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "50032", "num": 32, "name": "Swain <PERSON><PERSON><PERSON><PERSON> przez W<PERSON>", "chromas": true}, {"id": "50033", "num": 33, "name": "Swain <PERSON><PERSON><PERSON><PERSON> (Prestiżowy)", "chromas": false}], "lore": "Jericho Swain jest wizjon<PERSON><PERSON> przyw<PERSON><PERSON><PERSON>, podbojo<PERSON>go narodu, kt<PERSON><PERSON> uznaje tylko siłę. <PERSON><PERSON> że podczas wojen z Ionią doznał poważnego uszczerbku na zdrowiu, zarówno fizycznym — jego lewa ręka została odcięta — jak i psychicznym, udało mu się przejąć władzę nad imperium dzięki bezwzględnej determinacji... i nowej, demonicznej dłoni. D<PERSON>ś Swain wydaje rozkazy z pierwszej linii, maszerując naprzeciw nadchodzącej ciemności, kt<PERSON><PERSON><PERSON> tylko on może zobaczyć w kr<PERSON>tkich, pourywanych wizjach zbieranych przez mroczne kruki z ciał poległych wokół niego. W wirze ofiar i tajemnic największym misterium jest fakt, że prawdziwy wróg siedzi w nim samym.", "blurb": "Jericho Swain jest w<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON>, pod<PERSON><PERSON><PERSON><PERSON> narod<PERSON>, kt<PERSON><PERSON> uznaje tylko siłę. <PERSON><PERSON>e podczas wojen z Ionią doznał poważnego uszczerbku na zdrowiu, zarówno fizycznym — jego lewa ręka została odcięta — jak i psychicznym, udało mu się...", "allytips": ["Je<PERSON><PERSON> masz problemy z unieruchomieniem wrogów Nigdyruchem, spróbuj użyć go na przeciwnikach, kiedy stoją blisko swoich stworów, tak żeby wybuch ich zaskoczył.", "Graj<PERSON><PERSON> w alei, spróbuj używać przebijających obrażeń Ręki Śmierci, aby z bezpiecznej odległości zadawać obrażenia wrogom.", "Trafienie przeciwnika Wizją Imperium nie jest łatwe. Aby temu <PERSON>, rozglądaj się na mapie za potyczkami, w których wrogowie nie będą na nią uważać lub będą pod wpływem efektu kontroli tłumu.", "Demoniczne Wzejście czyni Swaina trudnym do zabicia, ale łatwo jest wtedy od niego uciec. Je<PERSON>li mobilność przeciwników jest przyt<PERSON><PERSON>, spr<PERSON><PERSON><PERSON> przed<PERSON>, kt<PERSON>re ich spowolnią, co sprawi, że pozostaną twoim z<PERSON>gu."], "enemytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bierna Swaina jest bar<PERSON><PERSON> m<PERSON>, jeśli trafiono cię efektem unieruchamiającym. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> w pobliżu wrogów, którzy posiadają takie efekty.", "Duża mobilność świetnie sprawdza się przeciwko umiejętnościom Swaina — <PERSON><PERSON><PERSON>mierci zadaje tym więcej o<PERSON>ń, im Swain jest bliżej, Wizja Imperium ma bardzo długie opóźnienie, a Nigdyruch musi zacząć do niego powracać, aby s<PERSON><PERSON><PERSON> zagrożenie.", "Zakup przedmiotu nakładającego efekt Głębokich Ran ułatwi zlikwidowanie Swaina, gdy używa Demonicznego Wzejścia."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 595, "hpperlevel": 99, "mp": 400, "mpperlevel": 29, "movespeed": 330, "armor": 25, "armorperlevel": 4.7, "spellblock": 31, "spellblockperlevel": 1.55, "attackrange": 525, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 10, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "Swain<PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "Swain wystrzeliwuje kilka pocisków nieziemskiej mocy, które przebijają się przez wrogów. Wrogowie otrzymują więcej obrażeń za każdy pocisk, który ich trafi.", "tooltip": "Swain wys<PERSON><PERSON><PERSON><PERSON>je 5 pocisków nieziemskiej mocy, zadając <magicDamage>{{ initialdamage }} pkt. obrażeń magicznych</magicDamage> plus <magicDamage>{{ extraboltdamage }} pkt. obrażeń magicznych</magicDamage> za każdy kolejny trafiony pocisk (maks. <magicDamage>{{ maxdamage }} pkt. obrażeń magicznych</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od pocisku", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "SwainQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "<PERSON>wain<PERSON>", "name": "Wizja Imperium", "description": "Swain otwiera oko demona, które zadaje obrażenia i spowalnia wrogów. Trafieni bohaterowi zostają ujawnieni i dają Swainowi Skrawek Duszy.", "tooltip": "Swain otwiera <PERSON>z<PERSON> oko, od<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wybrany obszar na 1,5 sek. Nastę<PERSON><PERSON> zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia</status> o {{ slow*-100 }}% na {{ slowduration }} sek.<br /><br />Trafieni bohaterowie zapewniają Swainowi <span class=\"size18 colorFF3F3F\">Skrawek Duszy</span> i zostają ujawnieni na {{ revealduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "<PERSON><PERSON><PERSON><PERSON>", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [5500, 6000, 6500, 7000, 7500], "rangeBurn": "5500/6000/6500/7000/7500", "image": {"full": "SwainW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "Swain<PERSON>", "name": "Nigdyru<PERSON>", "description": "Swain wystrzeliwuje do przodu falę demonicznej mocy. Następnie wraca ona do Swaina i unieruchamia trafionych wrogów. Wówczas może on przyciągnąć wszystkich usidlonych bohaterów bliżej. Ta umiejętność ma krótszy czas odnowienia podczas Demonicznego Wzejścia.", "tooltip": "Swain posyła falę <PERSON>j mocy, która do niego wraca i eksploduje przy trafieniu pierwszego wroga, zadaj<PERSON>c <magicDamage>{{ secondarydamage }} pkt. obrażeń magicznych</magicDamage> i <status>unieruchamiając</status> wrogów na obszarze na {{ rootduration }} sek.<br /><br /><status>Unieruchomienie</status> wrogiego bohatera pozwala Swainowi ponownie aktywować tę umiejętność, aby prz<PERSON><PERSON><PERSON><PERSON> wszystkich wrogich bohaterów <status>unieruchomionych</status> przez <spellName>Nigdyruch</spellName> i uzyskać <span class=\"size18 colorFF3F3F\">Skrawek Duszy</span> za każdego z nich.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ e2damage }} -> {{ e2damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "SwainE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SwainR", "name": "Demoniczne Wzejście", "description": "Swain przeobraża się w demona i wysysa zdrowie pobliskich wrogich bohaterów, stworów i neutralnych potworów. Swain może r<PERSON><PERSON>ć Rozbłysk Demona, aby zdziesiątkować i spowolnić pobliskich wrogów przy pomocy eksplozji płomieni duszy. Ta forma jest nieograniczona czasowo, dopóki Swain wysysa wrogich bohaterów.", "tooltip": "Swain uwalnia demona, z<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damagecalc }} pkt. obrażeń magicznych</magicDamage> i wysysając <healing>{{ healingcalc }} pkt. zdrowia</healing> na sekundę wrogom w zasięgu. Jego demoniczna energia wyczerpuje się z czasem, ale można ją ładować w ni<PERSON><PERSON>, wys<PERSON><PERSON><PERSON><PERSON> wrogich bohaterów, a udział w zabójstwie zregeneruje ją do maksimum.<br /><br />Po {{ demonflarecastdelay }} sek. i co każde kolejne {{ demonflarecooldowntooltip }} sek. po przemianie Swain może rzucić <spellName>Rozbłysk Demona</spellName>, który zadaje <magicDamage>{{ demonflaredamagetotal }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia</status> wrogów o {{ demonflareslowamount*100 }}%, zani<PERSON><PERSON><PERSON><PERSON> w ciągu {{ demonflareslowduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia wysysania", "Leczenie wysysania", "Obrażenia Rozbłysku Demona"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ healpersecond }} -> {{ healpersecondNL }}", "{{ demonflaredamagebase }} -> {{ demonflaredamagebaseNL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "SwainR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Krwiożercze Stado", "description": "<PERSON><PERSON><PERSON> <i>Skrawki Duszy</i>, które go leczą i na stałe zwiększają jego maksymalne zdrowie.", "image": {"full": "Swain_P.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}