{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rammus": {"id": "<PERSON><PERSON>", "key": "33", "name": "ラムス", "title": "アーマージロ", "image": {"full": "Rammus.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "33000", "num": 0, "name": "default", "chromas": false}, {"id": "33001", "num": 1, "name": "キング ラムス", "chromas": false}, {"id": "33002", "num": 2, "name": "クロム ラムス", "chromas": false}, {"id": "33003", "num": 3, "name": "マグマ ラムス", "chromas": false}, {"id": "33004", "num": 4, "name": "フレヨルド ラムス", "chromas": false}, {"id": "33005", "num": 5, "name": "忍者ラムス", "chromas": false}, {"id": "33006", "num": 6, "name": "フルメタル ラムス", "chromas": false}, {"id": "33007", "num": 7, "name": "砂漠の守護者ラムス", "chromas": false}, {"id": "33008", "num": 8, "name": "スイーパー ラムス", "chromas": true}, {"id": "33016", "num": 16, "name": "ヘクステック ラムス", "chromas": false}, {"id": "33017", "num": 17, "name": "宇宙飛行士ラムス", "chromas": true}, {"id": "33026", "num": 26, "name": "ドリアン戦士ラムス", "chromas": true}], "lore": "ラムス──謎に包まれたこの生き物を、聖なる存在と崇拝してやまない者は多い。一方で、ただの動物に過ぎないと見る者もいる。その素性は誰にも分からない。人々は棘の付いた甲羅を持つラムスについて様々な説を打ち立てる。ラムスの姿が確認された場所では、半神だ、いや神聖なる神の遣いだ、魔術で姿を変えられたただの獣だ、などと論争が巻き起こる。真実がどうであれラムスは沈黙を守り、独り砂漠を彷徨って、他者と関わろうとはしない。", "blurb": "ラムス──謎に包まれたこの生き物を、聖なる存在と崇拝してやまない者は多い。一方で、ただの動物に過ぎないと見る者もいる。その素性は誰にも分からない。人々は棘の付いた甲羅を持つラムスについて様々な説を打ち立てる。ラムスの姿が確認された場所では、半神だ、いや神聖なる神の遣いだ、魔術で姿を変えられたただの獣だ、などと論争が巻き起こる。真実がどうであれラムスは沈黙を守り、独り砂漠を彷徨って、他者と関わろうとはしない。", "allytips": ["「ころころ」は逃走手段としても効果的。", "味方タワーの近くで敵チャンピオンに「ちくちく」を使うと、敵にタワーの攻撃を浴びせることができる。", "「ドスンドスン」と「かたくなる」は、ゲーム終盤でタワーを破壊するのに重宝する。集団戦で苦戦するのならば、戦闘から脱してオブジェクトを攻めるのも有効。"], "enemytips": ["「かたくなる」を発動していない時を狙って攻撃しよう。通常時のラムスは、他のタンク系チャンピオンに比べて防御力が低い。", "物理防御を増加させるアイテムを装備すると攻撃力も増加するが「かたくなる」発動時以外は魔法防御力は増加しないので気をつけよう。"], "tags": ["Tank"], "partype": "マナ", "info": {"attack": 4, "defense": 10, "magic": 5, "difficulty": 5}, "stats": {"hp": 675, "hpperlevel": 100, "mp": 310, "mpperlevel": 33, "movespeed": 335, "armor": 35, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.55, "mpregen": 7.85, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.215, "attackspeed": 0.7}, "spells": [{"id": "PowerBall", "name": "ころころ", "description": "体を丸めて高速回転し、移動速度が増加する。衝突した敵に突進してダメージを与え、スロウ効果を付与する。", "tooltip": "丸くなって<speed>移動速度が{{ minimummovespeed }}</speed>増加し、さらに{{ rollduration }}秒かけて<speed>移動速度が最大{{ maximummovespeed }}</speed>まで増加する。敵にぶつかると停止し、<magicDamage>{{ powerballdamage }}の魔法ダメージ</magicDamage>を与えて<status>ノックバック</status>させ、周囲の敵に{{ slowduration }}秒間、{{ slowpercent }}%の<status>スロウ効果</status>を付与する。<br /><br /><recast>再発動</recast>: このスキルを早めに終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1000, 1000, 1000, 1000, 1000]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerBall.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "DefensiveBall<PERSON>url", "name": "かたくなる", "description": "防御体勢を取って物理防御と魔法防御を大幅に増加させ、通常攻撃を行ってきた相手にダメージを跳ね返す。", "tooltip": "{{ buffduration }}秒間、防御体勢を取り、<scaleArmor>物理防御が{{ bonusarmortooltip }}</scaleArmor>、<scaleMR>魔法防御が{{ bonusmrtooltip }}</scaleMR>増加し、通常攻撃を行ってきた相手に<magicDamage>{{ returndamagecalc }}の魔法ダメージ</magicDamage>を跳ね返す。<br /><br /><recast>再発動</recast>: このスキルを早めに終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加物理防御", "増加魔法防御", "物理防御増加割合", "魔法防御増加割合"], "effect": ["{{ flatbonusarmor }} -> {{ flatbonusarmorNL }}", "{{ flatbonusmr }} -> {{ flatbonusmrNL }}", "{{ bonusarmorpercent*100.000000 }}% -> {{ bonusarmorpercentnl*100.000000 }}%", "{{ bonusmrpercent*100.000000 }}% -> {{ bonusmrpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DefensiveBallCurl.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PuncturingTaunt", "name": "ぴりぴり", "description": "敵チャンピオンまたは中立モンスターをタウントし、固い甲羅に無謀な攻撃をさせる。", "tooltip": "敵チャンピオンまたはモンスターを{{ duration }}秒間<status>タウント</status>する。モンスターには<magicDamage>{{ monsterdamagecalc }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["効果時間", "モンスターに対するダメージ"], "effect": ["{{ duration }} -> {{ durationNL }}", "{{ monsterdamage }} -> {{ monsterdamageNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "PuncturingTaunt.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Tremors2", "name": "どーんどーん", "description": "ジャンプしてから指定地点に勢いよく着地し、敵に魔法ダメージとスロウ効果を与える。「ころころ」発動中に使用した場合、範囲の中心付近にいる敵にはノックアップも与える。", "tooltip": "ジャンプしてから勢いよく着地して、一定範囲に<magicDamage>{{ initialdamagecalc }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間、{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。<spellName>「ころころ」</spellName>中に使用すると、範囲の中心にいた敵に追加で<magicDamage>{{ spell.powerball:powerballdamage }}の魔法ダメージ</magicDamage>を与え、{{ knockupduration }}秒間、<status>ノックアップ</status>する。<br /><br />その後、{{ buffduration }}秒かけてその範囲に{{ numberofpulses }}回余震を発生させ、繰り返し<status>スロウ効果</status>を与える。<br /><br />このスキルの射程は自身の<speed>移動速度</speed>に応じて増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "Tremors2.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "トゲトゲ", "description": "自身の物理防御と魔法防御に応じて攻撃力が増加する。", "image": {"full": "Armordillo_ScavengeArmor.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}