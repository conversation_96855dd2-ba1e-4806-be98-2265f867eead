{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Yone", "title": "der unvergessene Krieger", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "Seelenblumen-<PERSON>ne", "chromas": true}, {"id": "777010", "num": 10, "name": "Academia Certaminis-Yone", "chromas": true}, {"id": "777019", "num": 19, "name": "Rächende Dämmerung Yone", "chromas": true}, {"id": "777026", "num": 26, "name": "Meeresrauschen-Yone", "chromas": true}, {"id": "777035", "num": 35, "name": "Tintenschatten-<PERSON>ne", "chromas": true}, {"id": "777045", "num": 45, "name": "HEARTSTEEL-<PERSON>ne", "chromas": true}, {"id": "777046", "num": 46, "name": "HEARTSTEEL-<PERSON>ne (Prestige)", "chromas": false}, {"id": "777055", "num": 55, "name": "High Noon-Yone", "chromas": true}, {"id": "777058", "num": 58, "name": "Friedensstifter High Noon-Yone", "chromas": false}, {"id": "777065", "num": 65, "name": "Maskierte Gerechtigkeit Yone", "chromas": false}], "lore": "Zu seinen Lebzeiten war er Yone – <PERSON><PERSON><PERSON> und renommierter Schüler an der Schwertkampfschule seines Dorfes. Nachdem er durch die Hand seines Bruders den Tod fand, wurde er von einer bösartigen Kreatur des Geisterreichs verfolgt und er musste sie mit ihrem eigenem Schwert niederstrecken. Seitdem ist Yone dazu verflucht, ihre dämonische Maske über seinem Gesicht zu tragen. Nun macht er Jagd auf diese Kreaturen, um herauszufinden, was aus ihm geworden ist.", "blurb": "Zu seinen Lebzeiten war er Yone – <PERSON><PERSON><PERSON> und renommierter Schüler an der Schwertkampfschule seines Dorfes. Nachdem er durch die Hand seines Bruders den Tod fand, wurde er von einer bösartigen Kreatur des Geisterreichs verfolgt und er musste...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Fluss", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "<PERSON><PERSON>b<PERSON><PERSON> Stahl", "description": "Greift nach vorn an und fügt allen <PERSON>nern in einer Linie Schaden zu.<br><br>Bei einem Treffer wird einige Sekunden lang eine Steigerung von „Aufziehender Sturm“ gewährt. Bei 2&nbsp;Steigerungen lässt die nächste Aktivierung von „Sterblicher Stahl“ Yone mit einem Windstoß nach vorn springen, wodur<PERSON> Gegner <status>in die Luft</status> geschleudert werden.", "tooltip": "<PERSON><PERSON> schl<PERSON>gt gera<PERSON> zu und verursacht <physicalDamage>{{ qdamage }}&nbsp;normalen Schaden</physicalDamage>.<br /><br />Bei einem Treffer wird {{ buffduration }}&nbsp;Sekunde(n) lang eine Steigerung gewährt. Bei 2&nbsp;Steigerungen lässt diese Fähigkeit Yone mit einem Windstoß nach vorn springen, wodurch Gegner {{ q3knockupduration }}&nbsp;Sekunde(n) lang <status>hochgeschleudert</status> werden und <physicalDamage>{{ qdamage }}&nbsp;normalen Schaden</physicalDamage> erleiden. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "YoneW", "name": "Bloßgelegte Seele", "description": "Yone greift nach vorn an und fügt allen Gegnern in einem kegelförmigen Bereich Schaden zu. Gewährt Yone einen Schild, der sich mit der Anzahl von getroffenen Champions erhöht.<br><br>Die Abklingzeit und Ausführungsdauer von „Bloßgelegte Seele“ skalieren mit dem Angriffstempo.", "tooltip": "Yone greift nach vorn an und verursacht <physicalDamage>{{ basedamage*0.5 }} + {{ maxhealthdamage*50 }}&nbsp;% des maximalen Lebens als normalen Schaden</physicalDamage> und <magicDamage>{{ basedamage*0.5 }} + {{ maxhealthdamage*50 }}&nbsp;% des maximalen Lebens als magischen Schaden</magicDamage>.<br /><br />Wenn Yone trifft, erhält er {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON>öhe von {{ wshield }}. Die Stärke des <shield>Schilds</shield> ist pro getroffenem Champion erhöht. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Gesamtschaden basierend auf maximalem Leben"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}&nbsp;% -> {{ maxhealthdamagenl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "YoneE", "name": "Entfesselter Geist", "description": "Yones Geist verlässt seinen Körper, w<PERSON><PERSON><PERSON> sich sein Lauftempo erhöht. Wenn die Fähigkeit endet, kehrt Yones Geist wieder in seinen Körper zurück und ein Teil des Schadens, den er als Geist verursacht hat, wird erneut zugefügt.", "tooltip": "Yone nimmt {{ returntimer }}&nbsp;Sekunden lang eine Geistform an und verlässt seinen Körper, wodurch er <speed>{{ startingms*100 }} %</speed> bis <speed>{{ movementspeed*100 }} % ansteigendes Lauftempo</speed> erhält.<br /><br />Wenn die Geistform endet, kehrt Yone in seinen Körper zurück und teilt erneut {{ deathmarkpercent*100 }}&nbsp;% des gesamten Angriffs- und Fähigkeitsschadens aus, den er während dieser Zeit an Champions verursacht hat. Er kann die Fähigkeit in seiner Geistform <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung: </recast>Beendet die Geistform frühzeitig.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ deathmarkpercent*100.000000 }}&nbsp;% -> {{ deathmarkpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "YoneR", "name": "Besiegeltes Schicksal", "description": "Yone teleportiert sich hinter den letzten Champion in einer Reihe und greift mit einem enormen Schlag an, wodurch er alle getroffenen Gegner zu sich heranzieht.", "tooltip": "Yone fügt allen getroffenen Gegnern in einer Reihe <physicalDamage>{{ tooltipdamage }}&nbsp;normalen Schaden</physicalDamage> und <magicDamage>{{ tooltipdamage }}&nbsp;magischen Schaden</magicDamage> zu. Dabei teleportiert er sich hinter den letzten getroffenen Champion, <status>schleudert</status> seine Opfer hoch, und zieht sie zu sich heran.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "<PERSON>g des Jägers", "description": "Yone verursacht bei jedem zweiten Angriff magischen Schaden. Außerdem ist seine kritische Trefferchance erhöht.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}