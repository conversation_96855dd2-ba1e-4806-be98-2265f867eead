{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Skarner": {"id": "<PERSON><PERSON><PERSON>", "key": "72", "name": "스카너", "title": "태고의 군주", "image": {"full": "Skarner.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "72000", "num": 0, "name": "default", "chromas": false}, {"id": "72001", "num": 1, "name": "사막의 재앙 스카너", "chromas": false}, {"id": "72002", "num": 2, "name": "대지룬 스카너", "chromas": false}, {"id": "72003", "num": 3, "name": "전투 기계 알파 스카너", "chromas": false}, {"id": "72004", "num": 4, "name": "사막의 수호자 스카너", "chromas": false}, {"id": "72005", "num": 5, "name": "우주 전갈 스카너", "chromas": true}], "lore": "거대한 고대 브래컨, 스카너는 지배 계급인 윤 탈의 창립 일원으로 이쉬탈에서 추앙받는다. 자신의 나라를 외부 세계로부터 안전하게 지키기 위해 헌신하는 스카너는 이샤오칸 아래 내실에서 살아가며 대지의 진동에 귀를 기울이고 잠재적 위협을 감지한다. 이쉬탈의 고립에 의문을 품는 윤 탈의 구성원이 많아지면서 스카너의 편집증은 갈수록 심해지고 있다. 이쉬탈과 이쉬탈 사람들의 안전을 지키기 위해서라면 스카너는 무엇이든 할 것이다. 어떤 대가가 따르더라도.", "blurb": "거대한 고대 브래컨, 스카너는 지배 계급인 윤 탈의 창립 일원으로 이쉬탈에서 추앙받는다. 자신의 나라를 외부 세계로부터 안전하게 지키기 위해 헌신하는 스카너는 이샤오칸 아래 내실에서 살아가며 대지의 진동에 귀를 기울이고 잠재적 위협을 감지한다. 이쉬탈의 고립에 의문을 품는 윤 탈의 구성원이 많아지면서 스카너의 편집증은 갈수록 심해지고 있다. 이쉬탈과 이쉬탈 사람들의 안전을 지키기 위해서라면 스카너는 무엇이든 할 것이다. 어떤 대가가 따르더라도.", "allytips": ["기본 공격 시 전율 효과가 적용됩니다. 최대 피해를 입히려면 대상에게 붙어 효과를 다시 적용하세요.", "중립 목표물이나 대규모 교전이 일어나기 전에 근처 수정 첨탑을 점령해 두면 스카너의 위력이 엄청나게 커집니다.", "꿰뚫기를 사용하여 아군이 공격할 수 있는 위치로 적을 옮길 수 있습니다."], "enemytips": ["이쉬탈의 격돌은 기절, 속박, 또는 띄워 올리기로 막을 수 있습니다. 스카너가 습격하기 위해 오고 있다면 군중 제어기를 아껴두세요!", "꿰뚫기는 회피할 수 있습니다. 점멸이나 다른 스킬을 사용해 범위에서 벗어나세요.", "스카너의 지속 피해를 받으면 죽을 수도 있습니다. 스카너를 과소평가하지 마세요."], "tags": ["Tank", "Fighter"], "partype": "마나", "info": {"attack": 7, "defense": 8, "magic": 5, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 320, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.2, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SkarnerQ", "name": "부서진 대지/지반 돌출", "description": "스카너가 대지에서 바위를 뜯어냅니다. 바위는 기본 공격을 강화하며 강력한 투사체로 던질 수 있습니다.", "tooltip": "스카너가 땅에서 바위를 뜯어내 다음 기본 공격 3회의 <attackSpeed>공격 속도를 {{ attackspeed*100 }}%</attackSpeed> 강화하고 주변 적에게 <physicalDamage>{{ abilitydamage }}의 물리 피해</physicalDamage>를 입힙니다. 마지막 기본 공격은 <physicalDamage>최대 체력의 {{ maxhppercent*100 }}%에 해당하는 물리 피해</physicalDamage>를 추가로 입히고 영향을 받은 적을 {{ slowduration }}초 동안 {{ slowpercent*100 }}% <status>둔화</status>시킵니다.<br /><br /><recast>재사용 시:</recast> 스카너가 이 스킬을 끝내고 바위를 던져 <physicalDamage>{{ spell.skarnerq:abilitydamage }}+최대 체력의 {{ spell.skarnerq:maxhppercent*100 }}%에 해당하는 물리 피해</physicalDamage>를 입히고 추가로 처음 적중한 적과 그 주변에 있는 적을 {{ spell.skarnerq:slowduration }}초 동안 {{ spell.skarnerq:slowpercent*100 }}% <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "공격 속도", "몬스터 대상 최대 체력 비례 피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ monsterdamagecap }} -> {{ monsterdamagecapNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.5, 10, 8.5, 7], "cooldownBurn": "13/11.5/10/8.5/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SkarnerQ.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SkarnerW", "name": "대지의 수호자", "description": "스카너가 방어막을 얻고 지진을 일으켜 그 충격파로 적에게 피해를 입히고 둔화 효과를 적용합니다.", "tooltip": "스카너가 {{ shieldduration }}초 동안 <shield>{{ initialshield }}의 피해를 흡수하는 보호막</shield>을 얻으며 지진을 일으켜 주변 적에게 <magicDamage>{{ damage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ sloweffect*-100 }}%의 <status>둔화</status> 효과를 적용합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SkarnerW.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SkarnerE", "name": "이쉬탈의 격돌", "description": "스카너가 앞으로 돌진하며 지형을 통과합니다. 챔피언이나 대형 몬스터와 충돌하면 다음에 부딪치는 벽에 내동댕이쳐 피해를 입히고 기절시킵니다.", "tooltip": "스카너가 앞으로 돌진하며 지형을 무시하고 지정한 방향으로 움직입니다. 챔피언이나 대형 몬스터와 마주치면 돌진이 끝날 때까지 끌고 다닙니다.<br /><br />끌고 온 적과 함께 벽에 부딪치면 해당 적은 <physicalDamage>{{ pindamage }}의 물리 피해</physicalDamage>를 입고 {{ stunduration }}초 동안 <status>기절</status>합니다.<br /><br />스킬을 <recast>재사용</recast>하면 돌진을 일찍 끝낼 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ pinbasedamage }} -> {{ pinbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1700, 1700, 1700, 1700, 1700], "rangeBurn": "1700", "image": {"full": "SkarnerE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SkarnerR", "name": "꿰뚫기", "description": "스카너가 꼬리를 앞으로 후려쳐 적 챔피언을 제압합니다. 제압한 적 챔피언은 스카너를 따라 끌려다닙니다.", "tooltip": "스카너가 꼬리를 앞으로 후려쳐 <magicDamage>{{ damage }}의 마법 피해</magicDamage>를 입히고 처음 적중한 챔피언 3명을 {{ duration }}초 동안 <status>제압</status>합니다. 적중한 적은 스킬의 지속시간 동안 스카너를 따라 끌려다닙니다.<br /><br />챔피언이 한 명이라도 적중하면 스카너의 <speed>이동 속도가 {{ speedboostduration }}초 동안 {{ speedboostamount*100 }}%</speed> 증가합니다.<br /><br /><spellName>부서진 대지</spellName> 활성화 시 스카너가 <spellName>지반 돌출</spellName>부터 사용합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "SkarnerR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "진동의 가닥", "description": "스카너의 기본 공격, 부서진 대지, 지반 돌출, 꿰뚫기가 전율을 적용합니다. 전율 최대 중첩 시 지속시간 동안 적들이 최대 체력 비례 마법 피해를 입습니다.", "image": {"full": "Skarner_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}