{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "ヨネ", "title": "忘られざる者", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "精霊の花祭りヨネ", "chromas": true}, {"id": "777010", "num": 10, "name": "バトルアカデミア ヨネ", "chromas": true}, {"id": "777019", "num": 19, "name": "秩序の光ヨネ", "chromas": true}, {"id": "777026", "num": 26, "name": "オーシャンソング ヨネ", "chromas": true}, {"id": "777035", "num": 35, "name": "墨影のヨネ", "chromas": true}, {"id": "777045", "num": 45, "name": "HEARTSTEEL ヨネ", "chromas": true}, {"id": "777046", "num": 46, "name": "プレステージ HEARTSTEEL ヨネ", "chromas": false}, {"id": "777055", "num": 55, "name": "荒野のヨネ", "chromas": true}, {"id": "777058", "num": 58, "name": "荒野の調停者ヨネ", "chromas": false}, {"id": "777065", "num": 65, "name": "正義の仮面ヨネ", "chromas": false}], "lore": "生前、ヨネはヤスオの腹違いの兄であり、村の剣術道場で名を知られた生徒だった。しかし弟の手で殺されたヨネは、霊界の邪悪な存在に狙われ、その悪しき者が持っていた刀を使って殺すことを余儀なくされた。そして悪魔の仮面を被る呪いにかけられたヨネは、自らが何者に変わったのかを理解するために、そうした邪悪な存在を飽くことなく狩り続けている。", "blurb": "生前、ヨネはヤスオの腹違いの兄であり、村の剣術道場で名を知られた生徒だった。しかし弟の手で殺されたヨネは、霊界の邪悪な存在に狙われ、その悪しき者が持っていた刀を使って殺すことを余儀なくされた。そして悪魔の仮面を被る呪いにかけられたヨネは、自らが何者に変わったのかを理解するために、そうした邪悪な存在を飽くことなく狩り続けている。", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "つむじ風", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "斬命刀", "description": "前方に突きを放ち、直線上の敵すべてにダメージを与える。<br><br>このスキルが命中すると「つむじ風」のスタックを数秒間得る。2スタックになると、「斬命刀」を使用した際、一陣の風をまとって前方にダッシュし、敵を<status>ノックアップ</status>させる。", "tooltip": "前方に突きを放ち、<physicalDamage>{{ qdamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />命中時に{{ buffduration }}秒間スタックを獲得する。2スタック時にこのスキルを使用すると一陣の風をまとって前方にダッシュし、{{ q3knockupduration }}秒間<status>ノックアップ</status>させて<physicalDamage>{{ qdamage }}の物理ダメージ</physicalDamage>を与える。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "YoneW", "name": "霊断刀", "description": "前方をなぎ払い、扇状の範囲内にいるすべての敵にダメージを与え、シールドを獲得する。シールド量はなぎ払いが命中したチャンピオンの数に応じて増加する。<br><br>攻撃速度が増加すると「霊断刀」のクールダウンと詠唱時間が短縮される。", "tooltip": "前方をなぎ払い、<physicalDamage>{{ basedamage*0.5 }}(+最大体力の{{ maxhealthdamage*50 }}%)の物理ダメージ</physicalDamage>と<magicDamage>{{ basedamage*0.5 }}(+最大体力の{{ maxhealthdamage*50 }}%)の魔法ダメージ</magicDamage>を与える。<br /><br />この攻撃が敵に命中すると、{{ shieldduration }}秒間<shield>耐久値{{ wshield }}のシールド</shield>を獲得する。<shield>シールド</shield>量は命中したチャンピオンの数に応じて増加する。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "最大体力に対する合計ダメージ率"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "YoneE", "name": "縛魂の解放", "description": "自身の霊魂が肉体を離れ、移動速度が増加する。スキル終了時にこの霊魂は肉体に強制的に戻り、霊魂として与えたダメージの一部をもう一度与える。", "tooltip": "{{ returntimer }}秒間霊魂形態となり、効果時間中は肉体をその場に残して、<speed>移動速度が{{ startingms*100 }}%</speed>から<speed>{{ movementspeed*100 }}%まで徐々に増加</speed>する。<br /><br />霊魂形態が終了すると肉体に戻され、効果時間中にチャンピオンに通常攻撃またはスキルで与えた全ダメージの{{ deathmarkpercent*100 }}%をもう一度与える。霊魂形態中にこのスキルを<recast>再発動</recast>できる。<br /><br /><recast>再発動: </recast>霊魂形態が早めに終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["繰り返しダメージ", "クールダウン"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "YoneR", "name": "冥封一閃", "description": "直線上の最後にいるチャンピオンの背後に強力な斬撃を与えてブリンクし、当たった敵すべてを自身の方向に引き寄せる。", "tooltip": "通り道にいた敵すべてに<physicalDamage>{{ tooltipdamage }}の物理ダメージ</physicalDamage>と<magicDamage>{{ tooltipdamage }}の魔法ダメージ</magicDamage>を与え、攻撃が当たった最後のチャンピオンの背後に瞬間移動して、命中した敵すべてを自身の方向に<status>ノックアップ</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "狩鬼道", "description": "通常攻撃2回ごとに魔法ダメージを与える。さらに、クリティカル率が増加する。", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}