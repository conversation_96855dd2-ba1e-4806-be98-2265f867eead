{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "Alistar", "title": "der Minotaurus", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "12003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12004", "num": 4, "name": "Langhorn-Alistar", "chromas": false}, {"id": "12005", "num": 5, "name": "Entfesselter Alistar", "chromas": false}, {"id": "12006", "num": 6, "name": "Infernalischer Alistar", "chromas": false}, {"id": "12007", "num": 7, "name": "Libero-Alistar", "chromas": false}, {"id": "12008", "num": 8, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12009", "num": 9, "name": "SKT T1-Alistar", "chromas": false}, {"id": "12010", "num": 10, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "12019", "num": 19, "name": "Hextech-Alistar", "chromas": false}, {"id": "12020", "num": 20, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": true}, {"id": "12022", "num": 22, "name": "Schwarzfrost-Alistar", "chromas": true}, {"id": "12029", "num": 29, "name": "Mondzodiak-Alistar", "chromas": true}], "lore": "Alistar war seit jeher ein mächtiger Krieger mit furchterregendem Ruf, doch nun will er am noxianischen Reich Rache für den Tod seines Klans üben. Obwohl er versklavt und zu einem Leben als Gladiator gezwungen wurde, bewahrte ihn sein unbe<PERSON><PERSON><PERSON> <PERSON><PERSON>, gänzlich zum Tier zu werden. <PERSON><PERSON>, da er frei ist von den Ketten seiner früheren Meister, kämpft er im Namen der Unterdrückten und Geknechteten und setzt seine Wut wie seine Hörner, <PERSON><PERSON> und Fäuste als Waffe ein.", "blurb": "Alistar war seit jeher ein mächtiger Krieger mit furchterregendem Ruf, doch nun will er am noxianischen Reich Rache für den Tod seines Klans üben. Obwohl er versklavt und zu einem Leben als Gladiator gezwungen wurde, bewahrte ihn sein unbeugsamer Wille...", "allytips": ["„Pulverisieren“ eignet sich gut dazu, eine bessere Position für „Kopfstoß“ zu erlangen.", "<PERSON>i Alistar ist das Lauftempo besonders wichtig. <PERSON><PERSON><PERSON> dies, wenn du seine Stiefel kaufst.", "„Blitz“ in Verbindung mit „Pulverisieren“ und „Kopfstoß“ kann es dir ermöglichen, deine Gegner zu überrumpeln und sie in deine Verbündeten zu stoßen."], "enemytips": ["Alistar kann sehr störend sein, aber manchmal ist es besser, sich zunächst auf einfachere Ziele zu konzentrieren.", "Nimm dich vor der Kombination „Pulverisieren“ und „Kopfstoß“ in <PERSON>cht, wenn du dich in der Nähe von Türmen bewegst.", "<PERSON>n Alistar seine ultimative Fähigkeit einsetzt, ist es meist besser, sich zurückzuziehen und zu warten, bis der Effekt beendet ist, bevor man ihn wieder angreift."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "Pulverisieren", "description": "Alistar schlägt auf den Boden, verursacht an allen nahen Gegnern Schaden und schleudert sie in die Luft.", "tooltip": "Alistar schlägt auf den Boden, <status>schleudert</status> Gegner {{ knockupduration }}&nbsp;Sekunden lang <status>hoch</status> und fügt ihnen <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Headbutt", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Alistar rammt ein Ziel mit seine<PERSON>, wobei er Schaden verursacht und das Ziel zurückstößt.", "tooltip": "Alistar stürmt auf einen Gegner zu und rammt ihn. Dabei fügt er ihm <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu und <status>stößt</status> ihn <status>zurück</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AlistarE", "name": "Trampeln", "description": "Alistar trampelt über gegnerische Einheiten, ignoriert die Kollisionsabfrage von Einheiten und erhält 1 Steigerung, wenn er einem gegnerischen Champion Schaden zufügt. Bei vollen Steigerungen verursacht Alistars nächster normaler Angriff gegen einen gegnerischen Champion zusätzlichen magischen Schaden und betäubt ihn.", "tooltip": "Alistar trampelt über den Boden, erhält „Geist“ und fügt <PERSON>n in der Nähe über {{ e3 }}&nbsp;Sekunden hinweg <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu<PERSON> <PERSON><PERSON>, das einem Champion <PERSON> zufügt, gewährt eine Steigerung.<br /><br />Bei {{ e5 }}&nbsp;Steigerungen <status>betäubt</status> Alistars nächster Angriff gegen Champions das Ziel {{ e6 }}&nbsp;Sekunde(n) lang und fügt ihm zusätzlich <magicDamage>{{ attackbonusdamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FerociousHowl", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Alistar stößt ein lautes Gebrüll aus, das alle Massenkontrolleffekte von ihm entfernt und erhaltenen normalen und magischen Schaden während der Wirkungsdauer verringert.", "tooltip": "Alistar entfernt sofort alle <status>kampfunfähig</status> machenden Effekte und erleidet {{ rduration }}&nbsp;Sekunden lang um {{ rdamagereduction }}&nbsp;% verringerten Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Schadensverringerung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamagereduction }}&nbsp;% -> {{ rdamagereductionNL }}&nbsp;%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Siegesschrei", "description": "Alistar lädt seinen Siegesschrei auf, wenn er gegnerische Champions betäubt, fortschleudert oder Gegner in der Nähe sterben. Sobald er vollständig aufgeladen ist, heilt er sich und alle nahen verbündeten Champions.", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}