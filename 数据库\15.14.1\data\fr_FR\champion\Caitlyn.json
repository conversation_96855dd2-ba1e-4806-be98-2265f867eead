{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Caitlyn": {"id": "<PERSON><PERSON><PERSON>", "key": "51", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Caitlyn.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "51000", "num": 0, "name": "default", "chromas": true}, {"id": "51001", "num": 1, "name": "<PERSON><PERSON><PERSON> r<PERSON>", "chromas": false}, {"id": "51002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51003", "num": 3, "name": "Caitlyn safari", "chromas": false}, {"id": "51004", "num": 4, "name": "<PERSON><PERSON><PERSON>que", "chromas": false}, {"id": "51005", "num": 5, "name": "Agent <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51006", "num": 6, "name": "<PERSON><PERSON><PERSON> chass<PERSON>", "chromas": false}, {"id": "51010", "num": 10, "name": "<PERSON><PERSON><PERSON> spectre lunaire", "chromas": true}, {"id": "51011", "num": 11, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "51013", "num": 13, "name": "<PERSON><PERSON><PERSON> fusil à eau", "chromas": true}, {"id": "51019", "num": 19, "name": "Caitlyn arcade", "chromas": false}, {"id": "51020", "num": 20, "name": "Caitlyn arcade prestige", "chromas": false}, {"id": "51022", "num": 22, "name": "<PERSON><PERSON>lyn de l'Académie du combat", "chromas": true}, {"id": "51028", "num": 28, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51029", "num": 29, "name": "Caitlyn arcade prestige (2022)", "chromas": false}, {"id": "51030", "num": 30, "name": "<PERSON><PERSON><PERSON> lune de neige", "chromas": true}, {"id": "51039", "num": 39, "name": "Caitlyn coup de cœur", "chromas": true}, {"id": "51048", "num": 48, "name": "DRX Caitlyn", "chromas": true}, {"id": "51050", "num": 50, "name": "<PERSON><PERSON><PERSON> commandante d'<PERSON>", "chromas": true}, {"id": "51051", "num": 51, "name": "<PERSON><PERSON><PERSON> commandante d'Arcane prestige", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> Kiramman est la plus célèbre gardienne de la paix à Piltover, mais elle est aussi la plus apte à débarrasser la ville de ses criminels les plus insaisissables. Elle fait souvent équipe avec Vi, son calme faisant contrepoids à la fougue de sa partenaire. Bien qu'elle porte un fusil Hextech unique en son genre, la meilleure arme de Caitlyn reste son intelligence ; elle invente en effet des pièges élaborés pour attraper les bandits qui auraient l'audace d'agir dans la Cité du progrès.", "blurb": "<PERSON><PERSON><PERSON> est la plus célèbre gardienne de la paix à Piltover, mais elle est aussi la plus apte à débarrasser la ville de ses criminels les plus insaisissables. Elle fait souvent équipe avec Vi, son calme faisant contrepoids à la fougue de sa...", "allytips": ["Utilisez les Piège-yordle en les déployant préventivement et en vous assurant d'en avoir un disponible pendant les combats.", "Évitez d'utiliser Tir chirurgical dans les grandes mêlées en équipe car il risque d'être bloqué par la mauvaise cible.", "<PERSON><PERSON><PERSON> de calibre 90 loin de l'adversaire pour combler l'écart ou passer par-dessus des murs."], "enemytips": ["Restez derrière les sbires alliés si Caitlyn vous harcèle avec Pacificateur de Piltover (les dégâts diminuent pour chaque cible subséquente).", "Vous pouvez intercepter le projectile de Tir chirurgical qui va frapper un allié en vous plaçant sur la trajectoire."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 6}, "stats": {"hp": 580, "hpperlevel": 107, "mp": 315, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 650, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.8, "attackspeedperlevel": 4, "attackspeed": 0.681}, "spells": [{"id": "CaitlynQ", "name": "Pacificateur de Piltover", "description": "<PERSON><PERSON><PERSON> fait monter son fusil en puissance pendant 1 seconde pour effectuer un tir perforant qui inflige des dégâts physiques (les cibles consécutives subissent moins de dégâts).", "tooltip": "<PERSON><PERSON><PERSON> tire un projectile perforant qui inflige <physicalDamage>{{ initialdamage }} pts de dégâts physiques</physicalDamage>. Après que le projectile a touché une première cible, il s'élargit et inflige <physicalDamage>{{ secondarydamage }} pts de dégâts physiques</physicalDamage>.<br /><br />Les ennemis révélés par <spellName>Piège-yordle</spellName> subissent toujours l'intégralité des dégâts.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ratio de dégâts d'attaque totaux", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ tadratio*100.000000 }}% -> {{ tadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1, 1, 1, 1, 1], [1.3, 1.45, 1.6, 1.75, 1.9], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1", "1.3/1.45/1.6/1.75/1.9", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "CaitlynQ.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynW", "name": "Piège-yordle", "description": "Caitlyn pose un piège. Quand il est déclenché, il révèle et immobilise le champion ennemi pendant 1.5 sec, et il octroie un Tir dans la tête renforcé à Caitlyn.", "tooltip": "<PERSON><PERSON><PERSON> pose un piège qui <status>immobilise</status> pendant {{ e6 }} sec le premier champion qui marche dessus. Le piège octroie une <keywordStealth>vision pure</keywordStealth> de ce champion pendant 3 sec. Les pièges durent {{ e3 }} sec et {{ e5 }} pièges peuvent être actifs simultanément. Cette compétence a {{ e5 }} charges ({{ ammorechargetime }} sec de rechargement).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts supplémentaires de Tir dans la tête contre les cibles piégées", "<PERSON><PERSON><PERSON> de rechargement", "Pièges max", "Durée du piège"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [35, 80, 125, 170, 215], [30, 35, 40, 45, 50], [3, 3, 4, 4, 5], [3, 3, 4, 4, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "35/80/125/170/215", "30/35/40/45/50", "3/3/4/4/5", "3/3/4/4/5", "1.5", "30", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CaitlynW.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynE", "name": "Filet de calibre 90", "description": "<PERSON><PERSON><PERSON> lance un filet lourd qui ralentit sa cible. Le recul projette Caitlyn en arrière.", "tooltip": "C<PERSON>lyn tire un filet, ce qui la repousse en arrière. Le filet <status>ralentit</status> la première cible touchée de {{ e3 }}% pendant {{ e2 }} sec et lui inflige <magicDamage>{{ netdamage }} pts de dégâts magiques</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1, 1, 1, 1, 1], [50, 50, 50, 50, 50], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1", "50", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "CaitlynE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynR", "name": "Tir chirurgical", "description": "<PERSON><PERSON><PERSON> prend son temps pour réaliser le tir parfait, infligeant d'importants dégâts à une cible unique et à très longue portée. Les champions ennemis peuvent intercepter la balle à la place de leur allié.", "tooltip": "C<PERSON><PERSON> prend le temps de canaliser un tir parfait, puis elle fait feu, infligeant <physicalDamage>{{ rtotaldamage }} pts de dégâts physiques</physicalDamage>. Les autres champions ennemis peuvent intercepter le tir. Cette compétence octroie une <keywordStealth>vision pure</keywordStealth> de la cible pendant la canalisation.<br /><br /><rules>Inflige jusqu'à {{ critchanceamp*100 }}% de dégâts supplémentaires selon les chances de coup critique de Caitlyn.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3500, 3500, 3500], "rangeBurn": "3500", "image": {"full": "CaitlynR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tir dans la tête", "description": "Toutes les quelques attaques de base ou quand elle vise une cible prise dans un de ses pièges ou dans son filet, <PERSON>aitlyn tire dans la tête de sa cible, infligeant des dégâts supplémentaires qui augmentent avec ses chances de coup critique. Contre les cibles prises au piège ou dans un filet, la portée du Tir dans la tête est doublée.", "image": {"full": "Caitlyn_Headshot.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}