{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Draven": {"id": "Draven", "key": "119", "name": "Draven", "title": "der ruhmreiche Scharfrichter", "image": {"full": "Draven.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "119000", "num": 0, "name": "default", "chromas": false}, {"id": "119001", "num": 1, "name": "Seelenschnitter-Draven", "chromas": false}, {"id": "119002", "num": 2, "name": "Gladiatoren-Draven", "chromas": false}, {"id": "119003", "num": 3, "name": "Kommentator-<PERSON><PERSON>", "chromas": true}, {"id": "119004", "num": 4, "name": "Poolparty-<PERSON>aven", "chromas": false}, {"id": "119005", "num": 5, "name": "Bestienjäger-Draven", "chromas": false}, {"id": "119006", "num": 6, "name": "Superdraviger <PERSON>", "chromas": false}, {"id": "119012", "num": 12, "name": "Weihnachts-Draven", "chromas": false}, {"id": "119013", "num": 13, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "119020", "num": 20, "name": "<PERSON><PERSON><PERSON><PERSON>bel-Draven", "chromas": true}, {"id": "119029", "num": 29, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "119039", "num": 39, "name": "Schreckensnacht-Draven", "chromas": true}, {"id": "119048", "num": 48, "name": "La Ilusión Draven", "chromas": true}, {"id": "119058", "num": 58, "name": "Große Abrechnung-Draven", "chromas": true}], "lore": "In Noxus stellen sich Krieger, auch bekannt al<PERSON>, <PERSON><PERSON><PERSON> in Arenen, wo sie ihr Blut vergießen und ihre Stärken testen, entgegen – doch nie wurde jemand so bejubelt wie Draven. Als ehemaliger Soldat konnte er die Mengen mit seinem Hang zur Dramatik und seinen unerreichten Kampfkünsten sowie den wirbelnden Äxten begeistern. Draven ist süchtig nach dem Spektakel seiner eigenen, dreisten Perfektion und hat sich geschworen, jeden zu besiegen, damit sein Name für immer im ganzen Reich bejubelt wird.", "blurb": "In Noxus stellen sich K<PERSON>ger, auch bekannt al<PERSON>, <PERSON><PERSON><PERSON> in Arenen, wo sie ihr Blut vergießen und ihre Stärken testen, entgegen – doch nie wurde jemand so bejubelt wie Draven. Als ehemaliger Soldat konnte er die Mengen mit seinem Hang zur...", "allytips": ["<PERSON>n sich Draven nicht bewegt, schl<PERSON>gt „Wirbelnde Axt“ in seiner Nähe auf. Sie landet direkt an seiner Position oder leicht links oder rechts.", "<PERSON>wegt sich Draven nach dem Angriff, fällt „Wirbelnde Axt“ in seine Bewegungsrichtung. <PERSON><PERSON><PERSON> dies, um zu bestimmen, wohin die Axt fallen soll."], "enemytips": ["Versuche mit Skillshots auf die Landeposition von Dravens „Wirbelnde Axt“ zu zielen.", "<PERSON><PERSON><PERSON>, damit er seine Äxte fallen lässt. Seine Stärke sinkt so erheblich."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 8}, "stats": {"hp": 675, "hpperlevel": 104, "mp": 361, "mpperlevel": 39, "movespeed": 330, "armor": 29, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.7, "mpregen": 8.05, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.6, "attackspeedperlevel": 2.7, "attackspeed": 0.679}, "spells": [{"id": "DravenSpinning", "name": "Wirbelnde Axt", "description": "Dravens nächster Angriff verursacht zusätzlichen normalen Schaden. Diese Axt prallt vom Ziel ab und fliegt hoch in die Luft. Wenn Draven sie auffängt, bereitet er automatisch eine neue „Wirbelnde Axt“ vor. Draven kann zwei „Wirbelnde Äxte“ gleichzeitig haben.", "tooltip": "Draven bereitet eine <keywordMajor>Wirbelnde Axt</keywordMajor> vor, die bei seinem nächsten Angriff zusätzlich <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und danach zurück in die Luft fliegt. Fängt Draven sie, bereitet er eine neue <keywordMajor>Wirbelnde Axt</keywordMajor> vor.<br /><br />Draven kann zwei <keywordMajor>Wirbelnde Äxte</keywordMajor> gleichzeitig haben.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzlicher prozentualer Angriffsschaden", "Abklingzeit"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [100, 100, 100, 100, 100], [75, 85, 95, 105, 115], [30, 35, 40, 45, 50], [5.75, 5.75, 5.75, 5.75, 5.75], [40, 45, 50, 55, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100", "75/85/95/105/115", "30/35/40/45/50", "5.75", "40/45/50/55/60", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DravenSpinning.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenFury", "name": "Bluthetz<PERSON>", "description": "Draven erhält erhöhtes Lauf- und Angriffstempo. Das zusätzliche Lauftempo fällt während der Dauer rapide ab. Fängt Draven eine „Wirbelnde Axt“, wird die Abklingzeit von „Bluthetze“ zurückgesetzt.", "tooltip": "Draven erhält „Geist“, <speed>{{ e2 }}&nbsp;% Lauftempo</speed>, das über {{ e3 }}&nbsp;Sekunden hinweg abfällt, und {{ e5 }}&nbsp;Sekunden lang <attackSpeed>{{ e4 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />Wenn <PERSON> e<PERSON> <keywordMajor>Wirbelnde Axt</keywordMajor> fängt, wird die Abklingzeit dieser Fähigkeit zurückgesetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Kosten (@AbilityResourceName@)", "Lauftempo"], "effect": ["{{ e4 }}&nbsp;% -> {{ e4NL }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [4, 5, 6, 7, 8], [50, 55, 60, 65, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [-0.062, -0.069, -0.075, -0.081, -0.087], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/5/6/7/8", "50/55/60/65/70", "1.5", "20/25/30/35/40", "3", "-0.062/-0.069/-0.075/-0.081/-0.087", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "DravenFury.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenDoubleShot", "name": "<PERSON>ur Seite!", "description": "Draven wirft seine Äxte, verursacht normalen Schaden an getroffenen Zielen und stößt sie zur Seite. Getroffene Ziele werden verlangsamt.", "tooltip": "Draven schleudert eine horizontale Axt, die dem Ziel <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zufügt, es <status>zurückstößt</status> und {{ e3 }}&nbsp;Sekunden lang um {{ e2 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [20, 25, 30, 35, 40], [2, 2, 2, 2, 2], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/110/145/180/215", "20/25/30/35/40", "2", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "DravenDoubleShot.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenRCast", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Draven schleudert zwei riesige Äxte, um an allen getroffenen Einheiten normalen Schaden zu verursachen. Die Äxte drehen langsam um und kehren zu Draven zurück, nachdem sie einen gegnerischen Champion getroffen haben. Draven kann die Fähigkeit außerdem während des Flugs der Äxte aktivieren, damit diese sofort zu ihm zurückkehren. Der Schaden nimmt mit jeder getroffenen Einheit ab und wird beim Richtungswechsel zurückgesetzt. Exekutiert <PERSON>, wenn ihr Leben niedriger ist als die Anzahl der Steigerungen von Dravens „Verehrung“.", "tooltip": "Draven schleudert zwei riesige Äxte, die <physicalDamage>{{ rcalculateddamage }}&nbsp;normalen Schaden</physicalDamage> verursachen. Bei einem Championtreffer oder bei <recast>Reaktivierung</recast> ändern sie ihre Richtung und kehren zu Draven zurück. Die Äxte verursachen pro getroffenen Gegner {{ rdamagereductionperhit*100 }}&nbsp;% weniger <PERSON>, bis zu einem Minimum von {{ rmindamagepercent }}&nbsp;%.<br /><br />Wenn das Leben eines gegnerischen Champions durch einen Treffer von <keywordMajor>Wirbelnder Tod</keywordMajor> geringer wäre als {{ rpassivestackscoefficient*100 }}&nbsp;% von Dravens aktuellen <keywordMajor>League of Draven</keywordMajor>-Steigerungen ({{ rpassivetruedamage }}), wird er exekutiert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Zusätzlicher Angriffsschaden – Skalierung"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rcoefficient*100.000000 }}&nbsp;% -> {{ rcoefficientnl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20000, 20000, 20000], "rangeBurn": "20000", "image": {"full": "DravenRCast.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "League of Draven", "description": "Draven verdient sich die „Verehrung“ seiner <PERSON>, wenn er eine „Wirbelnde Axt“ fängt, einen Vasallen oder ein Monster tötet oder einen Turm zerstört. Wenn er gegnerische Champions tötet, erhält Draven abhäng<PERSON> davon, wie viel „Verehrung“ er angesammelt hat, zusätzliches Gold.", "image": {"full": "Draven_passive.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}