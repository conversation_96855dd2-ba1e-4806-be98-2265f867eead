{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zoe": {"id": "<PERSON>", "key": "142", "name": "<PERSON>", "title": "Aspekt Zmierzchu", "image": {"full": "Zoe.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "142000", "num": 0, "name": "default", "chromas": false}, {"id": "142001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "142002", "num": 2, "name": "Basenowa Zoe", "chromas": true}, {"id": "142009", "num": 9, "name": "Czarod<PERSON>jka Gwiazd Zoe", "chromas": true}, {"id": "142018", "num": 18, "name": "Adeptka Arkanów Zoe", "chromas": false}, {"id": "142019", "num": 19, "name": "Adeptka Arkanów Zoe (Prestiżowa)", "chromas": false}, {"id": "142020", "num": 20, "name": "EDG Zoe", "chromas": true}, {"id": "142022", "num": 22, "name": "<PERSON>", "chromas": true}, {"id": "142033", "num": 33, "name": "<PERSON>", "chromas": true}], "lore": "Jako uosobienie psotliwości, w<PERSON><PERSON><PERSON><PERSON> i zmiany, <PERSON> jest kosmicznym posłańcem Targonu, kt<PERSON><PERSON> zwiastuje ważne wydarzenia zmieniające całe światy. <PERSON>a jej obecność zakrzywia prawa fizyki, co czasami prowadzi do kataklizmów, le<PERSON> nie jest to zamierzone działanie. Być może wyjaśnia to nonszalancję, z jaką Zoe traktuje swoje obowiązki, co daje jej mnóstwo czasu na igraszki, strojenie sobie żartów ze śmiertelników i dostarczanie sobie rozrywki na inne sposoby. Spotkanie z Zoe może być przyjemnym i pozytywnym doświadczeniem, lecz zawsze kryje się w tym coś więcej i nierzadko jest to coś bardzo niebez<PERSON>cznego.", "blurb": "Jako uoso<PERSON>nie <PERSON>li<PERSON>, w<PERSON><PERSON><PERSON><PERSON> i zmiany, Zoe jest kosmicznym posłańcem Targonu, kt<PERSON>ry zwiastuje ważne wydarzenia zmieniające całe światy. Sama jej obe<PERSON> zakrzywia prawa fizyki, co czasami prowadzi do kataklizmów, lecz nie jest to zamierzone...", "allytips": ["Gwiezdna Piłeczka Zoe zadaje tym więcej obrażeń, im dalej przeleci. Rzucanie jej za siebie przed zmianą jej kierunku może zadać olbrzymie obrażenia.", "Obudź wrogów za pomocą umiejętności zadającej największe obrażenia, bo wrogowie, kt<PERSON><PERSON><PERSON>pią, o<PERSON><PERSON><PERSON><PERSON><PERSON> podwojone obrażenia.", "<PERSON>na <PERSON>ńka Kłopotów ma wi<PERSON><PERSON><PERSON> z<PERSON>, jeś<PERSON> zostanie użyta przez ścianę. Znajdź kryjówkę, by <PERSON><PERSON><PERSON><PERSON> zabójstwo z dużej odległo<PERSON>ci."], "enemytips": ["Gwiezdna Piłeczka Zoe zadaje tym więcej obrażeń, im dalej przeleci.", "Zoe musi powrócić do miejsca, w kt<PERSON>rym była, zanim użyła Skoku przez Portal, co czyni ją podatną na kontratak.", "<PERSON>na <PERSON>ka Kłopotów ma wi<PERSON><PERSON><PERSON>, je<PERSON><PERSON> zostanie użyta przez ścianę. <PERSON>e pozwól Zoe chować się w mgle wojny, by p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jej w używaniu tego zaklę<PERSON>."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 106, "mp": 425, "mpperlevel": 25, "movespeed": 340, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "Zoe<PERSON>", "name": "Gwiezdna Piłeczka!", "description": "<PERSON> w<PERSON><PERSON> pocisk, k<PERSON><PERSON><PERSON> może przekier<PERSON> w locie. Zadaje tym więcej obrażeń, im dalej przeleci w linii prostej.", "tooltip": "<PERSON> w<PERSON>, która zadaje pierwszemu trafionemu wrogowi oraz pobliskim wrogom tym więcej obraż<PERSON>ń, im dalej doleci — od <magicDamage>{{ totaldamagetooltip }} do {{ maxdamagetooltip }} pkt. obrażeń magicznych</magicDamage>.<br /><br /><PERSON> <recast>ponownie <PERSON></recast> tej umie<PERSON>, by prz<PERSON><PERSON><PERSON><PERSON> pocisk w nowe miejsce w pobliżu siebie.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8.5, 8, 7.5, 7, 6.5], "cooldownBurn": "8.5/8/7.5/7/6.5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeQ.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZoeW", "name": "Złodziejka Czarów", "description": "Zoe może pod<PERSON> pozostałości czarów przywoływacza oraz przedmiotów użytych przez wrogów i użyć ich jeden raz. Za każdym razem, gdy używa czaru przywoływacza, wystrzeliwuje 3 pociski, które trafiają w najbliższy cel.", "tooltip": "<spellPassive>Biernie:</spellPassive> Użyte przez wrogów czary przywoływacza i przedmioty pozostawiają odłamki zaklęć. Niektóre zabite przez Zoe lub jej pobliskiego sojusznika stwory również upuszczają odłamek zaklęcia. Zoe może podnieść odłamek i jednorazowo użyć tej umiejętności.<br /><br /><spellPassive>Biernie:</spellPassive> Kiedy Zoe używa tej umiejętności lub dowolnego czaru przywoływacza, zyskuje <speed>{{ e9 }}% prędkości ruchu</speed> na {{ e0 }} sek. i rzuca 3 pociski w cel, który ostatnio zaatakowała. Każ<PERSON> z pocisków zadaje <magicDamage>{{ missiledamagetooltip }} pkt. obrażeń magicznych</magicDamage>.<br /><br /><spellActive>Użycie:</spellActive> Zoe używa umiejętności z odłamka zaklęcia, kt<PERSON><PERSON> podnio<PERSON>ła.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Całkowite obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Czas trwania przyspieszenia"], "effect": ["{{ totalbasedamage*3.000000 }} -> {{ totalbasedamagenl*3.000000 }}", "{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ movespeedduration }} -> {{ movespeeddurationNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3000, 4500, 6000, 0, 0], [0.1, 0.1, 0.1, 0.1, 0.1], [2500, 2500, 2500, 2500, 2500], [60, 60, 60, 60, 60], [20, 50, 80, 110, 140], [0.2, 0.2, 0.2, 0.2, 0.2], [0, 0, 0, 0, 0], [30, 40, 50, 60, 70], [2, 2.25, 2.5, 2.75, 3]], "effectBurn": [null, "0", "3000/4500/6000/0/0", "0.1", "2500", "60", "20/50/80/110/140", "0.2", "0", "30/40/50/60/70", "2/2.25/2.5/2.75/3"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [3000, 4500, 6000, 3000, 3000], "rangeBurn": "3000/4500/6000/3000/3000", "image": {"full": "ZoeW.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "ZoeE", "name": "Senna Bańka Kłopotów", "description": "<PERSON><PERSON><PERSON><PERSON>, że cel staje się senny, a potem zasypia. Podczas uśpienia odporność na magię celu zostaje zmniejszona. <PERSON><PERSON><PERSON> obrażenia, kt<PERSON><PERSON> sprawi<PERSON>, że cel się obudzi, zost<PERSON><PERSON> podwojone (do określonego limitu).", "tooltip": "<PERSON> kopie ba<PERSON>, k<PERSON><PERSON><PERSON> zadaje <magicDamage>{{ totaldamagetooltip }} pkt. obrażeń magicznych</magicDamage> i pozostaje na ziemi jako puła<PERSON>ka, jeśli w nic nie trafi. <PERSON><PERSON><PERSON>g bańki zostaje zwiększony, jeśli ta przeleci ponad przeszkodami terenowymi.<br /><br />Ofiara po chwili <status>zasypia</status> i jej <scaleMR>odporność na magię</scaleMR> zostaje zmniejszona o {{ percentpen*100 }}% na 2 sek. Trafione ataki i umiejętności budzą cel, ale zadają podwojone obrażenia, do maks. <trueDamage>{{ breakdamagetooltip }} pkt. obrażeń nieuchronnych</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Limit obrażeń dodatkowych", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [0.1, 0.15, 0.2, 0.25, 0.3], [5, 15, 25, 35, 45], [5, 5, 5, 5, 5], [0.1, 0.15, 0.2, 0.25, 0.3], [70, 110, 150, 190, 230], [0.45, 0.45, 0.45, 0.45, 0.45], [1.4, 1.4, 1.4, 1.4, 1.4], [2.25, 2.25, 2.25, 2.25, 2.25], [1, 1, 1, 1, 1]], "effectBurn": [null, "70/110/150/190/230", "0.1/0.15/0.2/0.25/0.3", "5/15/25/35/45", "5", "0.1/0.15/0.2/0.25/0.3", "70/110/150/190/230", "0.45", "1.4", "2.25", "1"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeE.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZoeR", "name": "Skok przez Portal", "description": "Mignij w pobliskie miejsce na 1 sekundę. Następnie mignij z powrotem.", "tooltip": "Zoe teleportuje się w pobliskie miejsce na 1 sekundę. Po chwili teleportuje się z powrotem. W tym czasie Zoe może używać umiejętności i atakować, ale nie może się ruszać.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [11, 8, 5], "cooldownBurn": "11/8/5", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [-0.3, -0.4, -0.5], [1.5, 2, 2.5], [4, 4, 4], [0.5, 0.5, 0.5], [3, 3, 3], [100, 200, 300], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "-0.3/-0.4/-0.5", "1.5/2/2.5", "4", "0.5", "3", "100/200/300", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [575, 575, 575], "rangeBurn": "575", "image": {"full": "ZoeR.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>!", "description": "Po rzuceniu zaklęcia następny podstawowy atak Zoe zada dodatkowe obrażenia magiczne.", "image": {"full": "Zoe_P.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}