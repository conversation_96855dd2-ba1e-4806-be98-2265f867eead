{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Chogath": {"id": "<PERSON><PERSON><PERSON>", "key": "31", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "der Schrecken der Leere", "image": {"full": "Chogath.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "31000", "num": 0, "name": "default", "chromas": false}, {"id": "31001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": false}, {"id": "31002", "num": 2, "name": "Gentleman-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "31003", "num": 3, "name": "Loch Ness <PERSON>ath", "chromas": false}, {"id": "31004", "num": 4, "name": "Jurassic-<PERSON><PERSON>ath", "chromas": false}, {"id": "31005", "num": 5, "name": "<PERSON>ahlkrieger-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "31006", "num": 6, "name": "Urzeit-Cho'Gath", "chromas": false}, {"id": "31007", "num": 7, "name": "Sternenvernichter-<PERSON><PERSON>Gath", "chromas": false}, {"id": "31014", "num": 14, "name": "<PERSON>", "chromas": true}, {"id": "31023", "num": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "31032", "num": 32, "name": "Spielzeugschrecken-<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> <PERSON>, als Cho'Gath erstmals das erbarmungslose Sonnenlicht von Runeterra erblickte, wurde die Bestie von einem wahren und unersättlichen Hunger getrieben. Cho'Gath verkörpert das Verlangen der Leere, alles Leben zu konsumieren, und sein komplexer Körper verwandelt alle Materie in zusätzliches Wachstum – so erhöht er seine Muskelmasse und -Dichte, oder härtet seinen Panzer wie einen organischen Diamanten. Wenn das Geschöpf der Leere nicht weiter wachsen muss, speit er das überflüssige Material als messerscharfe Stacheln wieder aus, die seine Opfer aufspießen, und er nur noch zubeissen muss.", "blurb": "<PERSON><PERSON> <PERSON>, als Cho'Gath erstmals das erbarmungslose Sonnenlicht von Runeterra erblickte, wurde die Bestie von einem wahren und unersättlichen Hunger getrieben. Cho'Gath verkörpert das Verlangen der Leere, alles Leben zu konsumieren, und sein...", "allytips": ["Versuche deine Angriffe so mit „Dornentod“ abzustim<PERSON>, dass sie Vasallen töten und gleichzeitig Champions beschäftigen.", "<PERSON><PERSON><PERSON><PERSON> dich mit Vasallen, wenn du es nicht scha<PERSON>t, <PERSON> zu verschlingen, bis du größer bist.", "Der Einsatz von „Riss“ in Kombination mit „Fleischfresser“ auf Vasallen ist eine gute Möglichkeit, <PERSON><PERSON> und Mana zurück zu gewinnen."], "enemytips": ["Durch den Kauf von <PERSON>, die dein <PERSON> erh<PERSON>, kann <PERSON> dich nicht so schnell töten.", "<PERSON><PERSON><PERSON> auf jeden <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> zu hindern, seine maximale Größe zu erreichen.", "Die Rauchwolke von „Riss“ zeigt das Gebiet an, das er trifft. Halte die Augen danach offen, um <PERSON>ath davon a<PERSON>en, seine Fähigkeiten zu kombinieren."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 5}, "stats": {"hp": 644, "hpperlevel": 94, "mp": 270, "mpperlevel": 60, "movespeed": 345, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.2, "attackspeedperlevel": 1.44, "attackspeed": 0.658}, "spells": [{"id": "Rupture", "name": "<PERSON><PERSON>", "description": "Reißt den Boden am Zielort auf, schleudert gegnerische Einheiten in die Luft, fügt ihnen Schaden zu und verlangsamt sie.", "tooltip": "<PERSON>'<PERSON>ath lässt den Boden aufbrechen, <status>schleudert</status> <PERSON><PERSON><PERSON> {{ e5 }}&nbsp;Sekunde lang in die Luft, verursacht <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> sie {{ e3 }}&nbsp;Sekunden lang um {{ e2 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [60, 60, 60, 60, 60], [1.5, 1.5, 1.5, 1.5, 1.5], [0.625, 0.625, 0.625, 0.625, 0.625], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "60", "1.5", "0.63", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "Rupture.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FeralScream", "name": "<PERSON>", "description": "<PERSON>'<PERSON><PERSON> stößt einen schrecklichen Schrei aus, der in einem Kegel magischen Schaden verursacht und Gegner für einige Sekunden verstummen lässt.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> br<PERSON>, lä<PERSON>t Gegner {{ e2 }}&nbsp;Sekunden lang <status>verstummen</status> und verursacht <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen <PERSON></magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1.6, 1.7, 1.8, 1.9, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1.6/1.7/1.8/1.9/2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "FeralScream.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VorpalSpikes", "name": "Do<PERSON><PERSON>d", "description": "<PERSON>'<PERSON><PERSON><PERSON><PERSON><PERSON> setzen tödliche Dornen frei, die allen gegnerischen Einheiten vor ihm Schaden zufügen und sie verlangsamen.", "tooltip": "Cho'Gaths nächste 3&nbsp;normale Angriffe feuern Dornen ab, die <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ flatdamagecalc }} plus {{ maxhealthpercentcalc }} des maximalen Lebens des Ziels verursachen und um {{ slowamountpercentage }}&nbsp;% <status>verlangsamen</status>. Die Verlangsamung fällt über {{ slowduration }}&nbsp;Sekunden ab.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden abhängig vom maximalen Leben ", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthealthdamage }}&nbsp;% -> {{ percenthealthdamageNL }}&nbsp;%", "{{ slowamountpercentage }}&nbsp;% -> {{ slowamountpercentageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [40, 40, 40, 40, 40], "rangeBurn": "40", "image": {"full": "VorpalSpikes.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Feast", "name": "<PERSON><PERSON><PERSON>", "description": "Verschlingt eine gegnerische Einheit und verursacht hohen absoluten Schaden. Wenn das Ziel getötet wird, wächst Cho<PERSON>G<PERSON> und sein maximales Leben erhöht sich.", "tooltip": "Cho'Gath stürzt sich gefräßig auf einen Gegner. Dabei verursacht er <trueDamage>{{ rdamage }}&nbsp;absoluten Schaden</trueDamage> an Champions und <trueDamage>{{ rmonsterdamage }}</trueDamage> an Vasallen oder Dschungelmonstern. Wenn das Ziel dadurch getötet wird, erhält Cho'Gath eine Steigerung, durch die er größer wird und die ihm <healing>{{ rhealthperstack }}&nbsp;maximales Leben</healing> gewährt. Durch Vasallen und nicht-epische Dschungelmonster können maximal {{ rminionmaxstacks }}&nbsp;Steigerungen erhalten werden. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> an Champions", "Leben pro Steigerung", "Angriffsreichweite pro Steigerung", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rhealthperstack }} -> {{ rhealthperstackNL }}", "{{ attackrangeperstack }} -> {{ attackrangeperstackNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 70, 60], "cooldownBurn": "80/70/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [175, 175, 175], "rangeBurn": "175", "image": {"full": "Feast.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> eine Einheit tötet, erhält er Leben und Mana zurück.", "image": {"full": "GreenTerror_TailSpike.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}