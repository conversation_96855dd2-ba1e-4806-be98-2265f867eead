{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rell": {"id": "<PERSON><PERSON>", "key": "526", "name": "<PERSON><PERSON>", "title": "la Vergine di ferro", "image": {"full": "Rell.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "526000", "num": 0, "name": "default", "chromas": false}, {"id": "526001", "num": 1, "name": "<PERSON><PERSON> g<PERSON>", "chromas": true}, {"id": "526010", "num": 10, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "526020", "num": 20, "name": "<PERSON><PERSON> di Fuoco", "chromas": true}, {"id": "526030", "num": 30, "name": "<PERSON><PERSON> Gloriosa", "chromas": false}], "lore": "Nata da una serie di brutali esperimenti condotti dalla <PERSON>, Rell è un'arma vivente ribelle determinata a far cadere Noxus. Orrore e dolore l'hanno accompagnata nella sua infanzia, durante la quale ha dovuto sopportare terribili procedure per perfezionare e rendere letale la sua magia di controllo del metallo... finché non ne ebbe abbastanza, e decise di fuggire uccidendo brutalmente molti dei suoi aguzzini. Ora considerata una pericolosa criminale, Rell attacca qualsiasi soldato noxiano veda mentre bracca i superstiti della sua ''accademia'', difendendo i deboli e massacrando senza pietà i suoi vecchi torturatori.", "blurb": "Nata da una serie di brutali esperimenti condotti dalla <PERSON>, <PERSON><PERSON> è un'arma vivente ribelle determinata a far cadere Noxus. Orrore e dolore l'hanno accompagnata nella sua infanzia, durante la quale ha dovuto sopportare terribili procedure per...", "allytips": [], "enemytips": [], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 620, "hpperlevel": 104, "mp": 320, "mpperlevel": 40, "movespeed": 315, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.8, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.85, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "RellQ", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> infligge danni magici alle unità in una linea, spezzando i loro scudi e stordendole. ", "tooltip": "<PERSON><PERSON> infilza con la lancia, <status>stordendo</status> i bersagli per {{ stunduration }} secondi, distruggendo tutti gli <shield>scudi</shield> e infliggendo <magicDamage>{{ damage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RellQ.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON>_Dismount", "name": "Ferromanzia: <PERSON><PERSON><PERSON>", "description": "In sella: <PERSON><PERSON> smonta da cavallo schiantandosi con la sua armatura, lanciando in alto i nemici e guadagnando un robusto scudo. Mentre è a piedi, guadagna armatura, resistenza magica, velocità d'attacco e gittata, ma è rallentata.<br><br>A piedi: Rell plasma la sua cavalcatura, guadagnando un aumento di velocità e lanciando in aria il prossimo nemico che attacca.<br><br>", "tooltip": "<spellPassive>Passiva - Celerità in sella:</spellPassive> mentre è in sella, Rell guadagna <speed>{{ spell.rellw_dismount:mountedmovespeed }} velocità di movimento</speed>.<br /><br /><spellActive>Attiva - Ferromanzia: Schianto:</spellActive> Rell salta giù da cavallo, <status>lanciando in aria</status> i nemici e infliggendo <magicDamage>{{ spell.rellw_dismount:dismountdamage }} danni magici</magicDamage>. Rell guadagna uno <shield>scudo da {{ spell.rellw_dismount:shield }}</shield> che permane finché non torna in sella.<br /><br />Rell poi assume la sua forma corazzata, guadagnando <scaleArmor>{{ spell.rellw_dismount:resistanceincrease*100 }}% armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR> aumentate, <attackSpeed>{{ spell.rellw_dismount:dismountedasboost*100 }}% velocità d'attacco</attackSpeed> e {{ spell.rellw_dismount:dismountedrangeboost }} gittata. Mentre è in questa forma corazzata, può usare <spellName>Ferromanzia: In sella</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Danni capovolgi<PERSON>", "Velocità di movimento passiva"], "effect": ["{{ crashdowndamage }} -> {{ crashdowndamageNL }}", "{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ mountupdamage }} -> {{ mountupdamageNL }}", "{{ mountedmovespeed }} -> {{ mountedmovespeedNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RellW_Dismount.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Rell<PERSON>", "name": "Massima velocità", "description": "Passiva: <PERSON><PERSON> guadagna velocità di movimento fuori dai combattimenti.<br><br>Attiva: Rell e un alleato guadagnano velocità di movimento crescente, raddoppiata se si muovono l'uno verso l'altra o verso i nemici. Il suo prossimo attacco esplode infliggendo danni magici.<br>", "tooltip": "Rell e un alleato partono all'attacco, ottenendo un <speed>{{ minms*100 }}% di velocità di movimento</speed>, che aumenta fino a un <speed>{{ maxms*100 }}%</speed> quando affrontano campioni nemici o si scontrano l'una con l'altro per {{ duration }} secondi. L'attacco successivo o <spellName><PERSON>po frantumante</spellName> di Rell esplode in un'area per <magicDamage>{{ maxhealthdamagecalc }} danni magici in base alla salute massima del bersaglio.</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ percenthealthdamage*100.000000 }}% -> {{ percenthealthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "RellE.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RellR", "name": "Tempesta magnetica", "description": "<PERSON>ll scatena la sua furia magnetica, attirando violentemente a sé i nemici vicini. A questo punto, Rell trascina costantemente verso di sé i nemici vicini per un breve periodo, infliggendo danni magici nel tempo.", "tooltip": "Rell scatena la sua furia magnetica, <status>attirando</status> violentemente a sé i nemici vicini. A questo punto, Rell <status>trascina</status> costantemente verso di sé i nemici vicini e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> nell'arco dei successivi {{ duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamagepersecond*2.000000 }} -> {{ basedamagepersecondnl*2.000000 }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "RellR.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "R<PERSON>pere gli schemi", "description": "Gli attacchi e le abilità di Rell infliggono danni magici aggiuntivi e rubano armatura e resistenza magica sul colpo.", "image": {"full": "RellP.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}