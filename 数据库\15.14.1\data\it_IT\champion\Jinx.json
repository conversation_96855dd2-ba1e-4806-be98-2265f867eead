{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jinx": {"id": "<PERSON><PERSON>", "key": "222", "name": "<PERSON><PERSON>", "title": "la mina vagante", "image": {"full": "Jinx.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "222000", "num": 0, "name": "default", "chromas": false}, {"id": "222001", "num": 1, "name": "Jinx Città del crimine", "chromas": false}, {"id": "222002", "num": 2, "name": "<PERSON>x <PERSON> d'Artificio", "chromas": true}, {"id": "222003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "222004", "num": 4, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "222012", "num": 12, "name": "Jinx <PERSON> ambiziosa", "chromas": false}, {"id": "222013", "num": 13, "name": "Jinx dell'Odissea", "chromas": true}, {"id": "222020", "num": 20, "name": "PROGETTO: <PERSON><PERSON>", "chromas": true}, {"id": "222029", "num": 29, "name": "<PERSON>x <PERSON>", "chromas": true}, {"id": "222037", "num": 37, "name": "Jinx Arcane: Nemica", "chromas": false}, {"id": "222038", "num": 38, "name": "<PERSON><PERSON> Battaglia", "chromas": true}, {"id": "222040", "num": 40, "name": "<PERSON><PERSON> Battaglia (edizione prestigio)", "chromas": false}, {"id": "222051", "num": 51, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "222060", "num": 60, "name": "Jinx <PERSON>ane: <PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "222062", "num": 62, "name": "Jinx T1", "chromas": false}], "lore": "Una squilibrata e impulsiva criminale della città sotterranea, Jin<PERSON> è tormentata dalle conseguenze del suo passato, ma questo non le impedisce di seminare il suo personale stile di caos a Piltover e Zaun. Sfrutta un arsenale di armi da lei create con efficacia devastante, tra coloratissime esplosioni e cascate di proiettili, ispirando gli emarginati a ribellarsi e resistere sulla scia della distruzione che lascia dietro di sé.", "blurb": "Una squilibrata e impulsiva criminale della città sotterranea, <PERSON><PERSON> è tormentata dalle conseguenze del suo passato, ma questo non le impedisce di seminare il suo personale stile di caos a Piltover e Zaun. Sfrutta un arsenale di armi da lei create con...", "allytips": ["I razzi non sono sempre la soluzione migliore! La minigun di Jinx è estremamente potente quando è a pieno regime. Usala ogni volta che un campione nemico si avvicina troppo.", "I razzi di Jinx infliggono il massimo dei danni a tutti i nemici coinvolti nell'esplosione. Usali sui minion in corsia per colpire i campioni nemici vicini senza diventare il bersaglio dei minion.", "Quando inizia un combattimento cerca di stare fuori dal centro dello scontro ed effettua azioni di disturbo con i razzi e Zap! Non gettarti nella mischia per sparare con la minigun finché non ti senti sicuro."], "enemytips": ["La minigun di Jinx ha bisogno di tempo per scaldarsi. Se la vedi armeggiare coi razzi, cerca di interromperla con la forza.", "La suprema di Jinx infligge meno danni al diminuire della distanza con lei.", "Le granate di Jinx hanno un tempo di ricarica elevato e sono la sua difesa principale. Se sbaglia mira, avrà difficoltà a liberarsi da un inseguitore."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 4, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 105, "mp": 260, "mpperlevel": 50, "movespeed": 325, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.75, "hpregenperlevel": 0.5, "mpregen": 6.7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.25, "attackspeedperlevel": 1.4, "attackspeed": 0.625}, "spells": [{"id": "JinxQ", "name": "Caaambio!", "description": "Jinx modifica i suoi attacchi base passando da Pow Pow, la sua minigun, a Fishbones, il suo lanciarazzi. Gli attacchi con Pow Pow conferiscono velocità d'attacco, mentre quelli con Fishbones infliggono danni ad area e hanno portata aumentata, ma assorbono mana e sono più lenti.", "tooltip": "Jinx cambia armi, alternan<PERSON>, il lanciarazzi e Pow Pow, la minigun.<br /><br />Quando usa il lanciarazzi, gli attacchi di Jinx infliggono <physicalDamage>{{ rocketdamage }} danni fisici</physicalDamage> al bersaglio e ai nemici circostanti, ottengono {{ rocketbonusrange }} gittata, hanno un costo in mana e crescono di un {{ rocketaspdpenalty*100 }}% in meno con la velocità d'attacco bonus.<br /><br />Quando usa la minigun, gli attacchi di Jinx conferiscono <attackSpeed>velocità d'attacco</attackSpeed> per {{ minigunattackspeedduration }} secondi, cumulabile fino a {{ minigunattackspeedstacks }} volte (<attackSpeed>+{{ minigunattackspeedmax }}% %i:scaleAS% massimo</attackSpeed>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bonus gittata razzo", "Velocità d'attacco minigun totale"], "effect": ["{{ rocketbonusrange }} -> {{ rocketbonusrangeNL }}", "{{ minigunattackspeedmax }}% -> {{ minigunattackspeedmaxNL }}%"]}, "maxrank": 5, "cooldown": [0.9, 0.9, 0.9, 0.9, 0.9], "cooldownBurn": "0.9", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana per razzo", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JinxQ.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} mana per razzo"}, {"id": "JinxW", "name": "Zap!", "description": "<PERSON>x <PERSON> Zapper, la sua pistola elettrica, per sparare un colpo che danneggia il primo nemico colpito, rallentandolo e rivelandolo.", "tooltip": "Jinx spara un colpo elettrico che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al primo nemico colpito, <status>rallentandolo</status> del {{ slowpercent }}% e rivelandolo per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "JinxW.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxE", "name": "<PERSON><PERSON><PERSON> fuochino!", "description": "Jinx lancia una serie di granate che esplodono dopo 5 secondi, incendiando i nemici. Le granate mordono i campioni nemici che le calpestano, immobilizzandoli.", "tooltip": "Jinx lancia 3 granate che durano {{ grenadeduration }} secondi ed esplodono a contatto con i campioni nemici, <status>immobiliz<PERSON><PERSON>li</status> per {{ rootduration }} secondi e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici vicini.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 20.5, 17, 13.5, 10], "cooldownBurn": "24/20.5/17/13.5/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "JinxE.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxR", "name": "Super mega razzo della morte!", "description": "Jinx spara un super razzo attraverso la mappa, che guadagna danni mentre viaggia. Il razzo esplode al contatto con un campione nemico, infliggendo danni a lui e ai nemici circostanti in base alla loro salute mancante.", "tooltip": "Jinx spara un razzo che esplode al contatto con il primo campione nemico colpito, infliggendo da <physicalDamage>{{ damagefloor }} a {{ damagemax }} + {{ percentdamage }}% della salute mancante in danni fisici</physicalDamage> e incrementando i danni durante il primo secondo del suo percorso. I nemici nelle vicinanze subiscono {{ aoedamagemult*100 }}% danni.<br /><br /><rules>I danni in base alla salute mancante non possono superare {{ monsterexecutemax }} contro i mostri.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "<PERSON><PERSON>uali salute mancante", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ percentdamage }}% -> {{ percentdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [85, 65, 45], "cooldownBurn": "85/65/45", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JinxR.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Che emozione!", "description": "Jinx riceve un grande aumento di velocità di movimento e velocità d'attacco quando aiuta a eliminare un campione nemico, un mostro epico della giungla o una struttura.", "image": {"full": "Jinx_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}