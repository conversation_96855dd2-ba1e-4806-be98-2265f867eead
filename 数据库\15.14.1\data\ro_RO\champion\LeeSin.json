{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"LeeSin": {"id": "<PERSON><PERSON><PERSON>", "key": "64", "name": "<PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> orb", "image": {"full": "LeeSin.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "64000", "num": 0, "name": "default", "chromas": false}, {"id": "64001", "num": 1, "name": "Lee Sin tradițional", "chromas": false}, {"id": "64002", "num": 2, "name": "Lee Sin, acolitul", "chromas": false}, {"id": "64003", "num": 3, "name": "<PERSON> puterea dragonului", "chromas": true}, {"id": "64004", "num": 4, "name": "<PERSON>", "chromas": true}, {"id": "64005", "num": 5, "name": "Lee Sin <PERSON> piscină", "chromas": false}, {"id": "64006", "num": 6, "name": "Lee Sin SKT T1", "chromas": false}, {"id": "64010", "num": 10, "name": "<PERSON>", "chromas": false}, {"id": "64011", "num": 11, "name": "<PERSON>", "chromas": false}, {"id": "64012", "num": 12, "name": "<PERSON> playmaker", "chromas": true}, {"id": "64027", "num": 27, "name": "<PERSON>, întruparea Întunericului", "chromas": false}, {"id": "64028", "num": 28, "name": "<PERSON>, întrup<PERSON>a Întunericului (Prestigiu)", "chromas": false}, {"id": "64029", "num": 29, "name": "Lee Sin FPX", "chromas": false}, {"id": "64031", "num": 31, "name": "<PERSON>, dragon<PERSON> furtunii", "chromas": false}, {"id": "64039", "num": 39, "name": "<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Întunericului (Prestigiu) – 2022", "chromas": false}, {"id": "64041", "num": 41, "name": "Lee <PERSON> de la Jocurile Zenitului", "chromas": false}, {"id": "64051", "num": 51, "name": "<PERSON> solzi divini", "chromas": false}, {"id": "64052", "num": 52, "name": "Lee <PERSON> solzi divini paradisiaci", "chromas": false}, {"id": "64068", "num": 68, "name": "<PERSON> T1", "chromas": false}, {"id": "64072", "num": 72, "name": "<PERSON> umbră de cerneală", "chromas": false}], "lore": "<PERSON>, un maestru al anticelor arte marțiale ioniene, este un luptător cu convingeri ferme, care se folosește de esența spiritului dragonului pentru a trece de orice obstacol. Cu toate că și-a pierdut vederea cu mulți ani în urmă, r<PERSON><PERSON><PERSON>inicul-călugăr și-a devotat întreaga viață întru apărarea patriei sale de către toți cei care îndrăznesc să-i amenințe echilibrul sacru. Inamicii care-l subestimează din cauza comportamentului său meditativ îi vor simți puterea pumnilor înflăcărați și necruțătoarele lovituri de picioare.", "blurb": "<PERSON>, un maestru al anticelor arte marțiale ioniene, este un luptător cu convingeri ferme, care se folosește de esența spiritului dragonului pentru a trece de orice obstacol. Cu toate că și-a pierdut vederea cu mulți ani în urmă, r<PERSON><PERSON><PERSON>inicul-călugăr...", "allytips": ["Folosește ''Unda sonică'' înainte de ''Furia dragonului'' pentru a-ți putea urmări ținta cu ''Lovitură rezonantă''.", "Profită de ''Răbufnire'' intercalând atacurile de bază cu folosirea vrăjilor – astfel crești daunele provocate și reduci la minimum pierderile de energie.", "<PERSON><PERSON>ă folo<PERSON> ''Salvare'' pe propriul campion și ''Voinț<PERSON> de fier'', vei avea două mijloace de maxim efect pentru a ucide monștrii neutri (din junglă)."], "enemytips": ["Dispersați-vă pentru a atenua impactul abilității supreme a lui <PERSON>, ''Furia dragonului''.", "Lee Sin contracarează eficient daunele fizice cu ''Voință de Fier'' și ''Vlăguire'', însă e vulnerabil la daunele provocate de vrăji.", "Lee Sin se bazează pe utilizarea cumulativă a abilităților. Folosește abilități de neutralizare pentru a nu-l lăsa să-și continue secvența de abilități și atacuri."], "tags": ["Fighter", "Assassin"], "partype": "Energie", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 645, "hpperlevel": 108, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 3, "attackspeed": 0.651}, "spells": [{"id": "LeeSinQOne", "name": "Undă sonică / Lovitură rezonantă", "description": "Undă sonică: pentru a-și depista inamicii, <PERSON> proiectează o undă sonică discordantă care-i provoacă daune fizice primului inamic pe care îl întâlnește. Dacă ''Unda sonică'' love<PERSON><PERSON> un inamic, <PERSON> poate folosi ''Lovitura rezonantă'' în decurs de 3 secunde.<br>Lovitură rezonantă: <PERSON> se năpustește asupra inamicului lovit de ''Unda sonică'' și îi provoacă daune fizice în funcție de viața lipsă a țintei.", "tooltip": "Lee Sin proiectează o undă sonică discordantă, provocându-i <physicalDamage>{{ initialdamage }} daune fizice</physicalDamage> primului inamic lovit, primind ''viziune supranaturală'' asupra acestuia și putând să <recast>refolosească</recast> abilitatea în următoarele {{ reactivatetime }} secunde.<br /><br /><recast>Refolosire:</recast> <PERSON> Sin se năpustește spre inamicul lovit de unda sonică, provocându-i între <physicalDamage>{{ recastdamage }} și {{ empowereddamage }} daune fizice</physicalDamage>, valoare ce crește în funcție de viața lipsă a țintei. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune ''Undă sonică''", "Daune de bază minime ''Lovitură rezonantă''", "Daune de bază maxime ''Lovitură rezonantă''", "Timp de reactivare"], "effect": ["{{ q1basedamage }} -> {{ q1basedamageNL }}", "{{ q2basedamage }} -> {{ q2basedamageNL }}", "{{ q2basedamage*2.000000 }} -> {{ q2basedamagenl*2.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LeeSinQOne.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinWOne", "name": "Salvare / Voință <PERSON>er", "description": "Salvare: <PERSON> sare în ajutorul unui aliat-ț<PERSON><PERSON>, pro<PERSON><PERSON><PERSON><PERSON>-se cu un scut. Da<PERSON>ă aliatul este un campion, și acesta primește un scut. După ce folosește ''Salvare'', <PERSON> poate activa ''Voința de fier''.<br>Voin<PERSON><PERSON> de fier: datorit<PERSON> pregătirii sale intense, <PERSON> este foarte priceput în luptă. <PERSON> Sin poate fura viață și beneficiază de vampirism magic.", "tooltip": "<PERSON> Sin se năpustește spre un aliat sau o gardă. Dac<PERSON> ținta este un campion, <PERSON> Sin se protejează pe el și pe acesta cu un <shield>scut în valoare de {{ shieldamount }}</shield> timp de {{ shieldduration }} secunde și reduce timpul de reactivare al acestei abilități cu {{ w1cooldownrecovered*100 }}%. <PERSON> Sin poate <recast>refolosi</recast> abilitatea în următoarele {{ w1reactivatetime }} secunde.<br /><br /><recast>Refolosire:</recast> <PERSON> primește {{ lifestealandspellvamp }}% furt de viață și vampirism magic timp de {{ lifestealandspellvamptime }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Absorbție scut la Salvare", "Furt de viață la Voință de fier / Vampirism magic %"], "effect": ["{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ lifestealandspellvamp }}% -> {{ lifestealandspellvampNL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LeeSinWOne.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinEOne", "name": "Cutremur / Vlăguire", "description": "Cutremur: <PERSON> pământul și propagă o undă de șoc care dezvăluie unitățile inamice lovite și le provoacă daune magice. Dacă abilitatea lovește un inamic, <PERSON> poate folosi ''Vlăguirea''.<br>Vlăguire: <PERSON> reduce viteza de mișcare a inamicilor loviți de ''Cutremur''. Aceasta se reface treptat de-a lungul intervalului.", "tooltip": "<PERSON> Sin izbește pământul și propagă o undă de șoc care dezvăluie unitățile inamice lovite timp de {{ slowduration }} secunde și le provoacă <magicDamage>{{ initialdamage }} daune magice</magicDamage>. Dacă abilitatea lovește un inamic, <PERSON> o poate <recast>refolosi</recast> în următoarele {{ reactivatetime }} secunde.<br /><br /><recast>Refolosire:</recast> <PERSON> <status>încetinește</status> inamicii din apropiere loviți de unda de șoc cu {{ slowamount }}%, valoare ce scade de-a lungul a {{ slowduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune ''Cutremur''", "Viteză de mișcare ''Vlăguire''"], "effect": ["{{ e1damage }} -> {{ e1damageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LeeSinEOne.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinR", "name": "Furia dragonului", "description": "Lee Sin aplică o puternică lovitură de picior circulară, proiectându-și inamicul în spate și provocând daune fizice atât celui lovit direct, cât și celor pe care-i lovește acesta în cădere. Inamicii de care se izbește cel lovit de Lee Sin sunt proiectați în aer pentru scurt timp. Cel care l-a învățat tehnica este <PERSON>, de<PERSON><PERSON> nu scoate jucătorii de pe hartă.", "tooltip": "Lee Sin execută o lovitură de picior circulară, care <status>aruncă în spate</status> un campion inamic și îi provoacă <physicalDamage>{{ damage }} daune fizice</physicalDamage>.<br /><br />Inamicii de care se izbește ținta sunt <status>aruncați în sus</status> pentru scurt timp și suferă <physicalDamage>daune fizice în valoare de {{ damage }} plus {{ percenthpcarrythrough }}% din viața bonus a țintei inițiale</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Daune bonus în funcție de viață", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthpcarrythrough }}% -> {{ percenthpcarrythroughNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 85, 60], "cooldownBurn": "110/85/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [375, 375, 375], "rangeBurn": "375", "image": {"full": "LeeSinR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Fără cost"}], "passive": {"name": "Răbufnire", "description": "După ce Lee Sin folosește o abilitate, următoarele 2 atacuri de bază ale sale primesc viteză de atac suplimentară și refac energie.", "image": {"full": "LeeSinPassive.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}