{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zac": {"id": "<PERSON><PERSON>", "key": "154", "name": "Зак", "title": "Секретное оружие", "image": {"full": "Zac.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "154000", "num": 0, "name": "default", "chromas": true}, {"id": "154001", "num": 1, "name": "Особое оружие Зак", "chromas": false}, {"id": "154002", "num": 2, "name": "Тус<PERSON>ый Зак", "chromas": false}, {"id": "154006", "num": 6, "name": "SKT T1 Зак", "chromas": false}, {"id": "154007", "num": 7, "name": "Модифицированный Зак", "chromas": true}, {"id": "154014", "num": 14, "name": "Эм<PERSON>и<PERSON><PERSON><PERSON><PERSON> Зак", "chromas": true}, {"id": "154024", "num": 24, "name": "Сырный Зак", "chromas": true}], "lore": "Зак появился на свет в результате утечки токсичных веществ, просочившихся сквозь швы химтековой трубы в уединенную пещеру в глубинах сточных колодцев Зауна. Несмотря на столь скромное происхождение, Зак сумел проделать путь от зачатка жизни в луже грязи до разумного создания. Он обитает в городской канализации, откуда выбирается время от времени, чтобы подлатать трубы и помочь тем, кто в этом нуждается.", "blurb": "Зак появился на свет в результате утечки токсичных веществ, просочившихся сквозь швы химтековой трубы в уединенную пещеру в глубинах сточных колодцев Зауна. Несмотря на столь скромное происхождение, Зак сумел проделать путь от зачатка жизни в луже грязи...", "allytips": ["Чтобы остаться в живых, очень важно вовремя подбирать куски слизи.", "Когда Цитокинез будет готов, постарайтесь умереть в таком положении, в котором вражеской команде будет сложно убить ваши капли.", "Если зарядку Эластичной рогатки производить, укрывшись за туманом войны, у противников будет меньше времени для того, чтобы среагировать."], "enemytips": ["Зак может восстанавливать здоровье с помощью слизи, которая от него отделяется. Комки слизи можно уничтожать, наступая на них.", "Чтобы предотвратить возрождение Зака, достаточно убить все капли, на которые он разделяется.", "Прервать зарядку Эластичной рогатки Зака можно с помощью эффектов молчания, оглушения, сковывания и отбрасывания."], "tags": ["Tank", "Fighter"], "partype": "Нет", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 8}, "stats": {"hp": 685, "hpperlevel": 109, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.6, "attackspeed": 0.736}, "spells": [{"id": "ZacQ", "name": "Резиновые удары", "description": "Зак растягивает руку, хватая врага. При атаке другого врага Зак бросает цели друг в друга.", "tooltip": "Зак вытягивает свою руку, цепляясь за первого врага на пути, нанося ему <magicDamage>{{ totaldamage }} магического урона</magicDamage> и ненадолго <status>замедляя</status>. Дальность следующей атаки Зака увеличена, она наносит столько же урона и тоже <status>замедляет</status>. <br /><br />Если эта атака поражает <i>другого</i> врага, Зак <status>подбрасывает</status> обе цели по направлению друг к другу. Если цели сталкиваются, они и окружающие враги получают <magicDamage>{{ totaldamage }} магического урона</magicDamage> и ненадолго <status>замедляются</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [8, 8, 8, 8, 8], [800, 800, 800, 800, 800], [-0.4, -0.4, -0.4, -0.4, -0.4], [0.5, 0.5, 0.5, 0.5, 0.5], [2.5, 2.5, 2.5, 2.5, 2.5], [900, 900, 900, 900, 900], [700, 700, 700, 700, 700], [300, 300, 300, 300, 300], [300, 300, 300, 300, 300]], "effectBurn": [null, "40/55/70/85/100", "8", "800", "-0.4", "0.5", "2.5", "900", "700", "300", "300"], "vars": [], "costType": "% текущего здоровья ({{ healthcosttooltip }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZacQ.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ e2 }}% текущего здоровья ({{ healthcosttooltip }})"}, {"id": "ZacW", "name": "Нестабильная материя", "description": "Зак взрывается по направлению к ближайших врагам, нанося им магический урон в зависимости от их максимального запаса здоровья.", "tooltip": "Тело Зака разрушается, нанося врагам поблизости <magicDamage>магический урон в размере {{ basedamage }} + {{ displaypercentdamage }} от максимального запаса здоровья</magicDamage>.<br /><br />Поглощение кусочка <keywordMajor>слизи</keywordMajor> сокращает перезарядку этого умения на 1 сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Постоянный урон", "Коэффициент урона от максимального запаса здоровья"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealthdamage*100.000000 }}% -> {{ basemaxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 50, 60, 70, 80], [4, 4, 4, 4, 4], [0.04, 0.05, 0.06, 0.07, 0.08], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/50/60/70/80", "4", "0.04/0.05/0.06/0.07/0.08", "1", "200", "0", "0", "0", "0", "0"], "vars": [], "costType": "% текущего здоровья ({{ tooltiphealthcost }})", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "ZacW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ e2 }}% текущего здоровья ({{ tooltiphealthcost }})"}, {"id": "ZacE", "name": "Эластичная рогатка", "description": "Зак цепляется руками за землю, оттягивает свое тело назад, а затем запускает себя в указанном направлении.", "tooltip": "<charge>Начало подготовки:</charge> Зак оттягивает свое тело назад, готовясь к прыжку в течение {{ e4 }} сек.<br /><br /><release>Применение:</release> Зак запускает себя и <status>подбрасывает</status> врагов в точке приземления на время вплоть до {{ maxstun }} сек. (в зависимости от времени подготовки), а также наносит им <magicDamage>{{ damage }} магического урона</magicDamage>. За каждого пораженного чемпиона от тела Зака отрываются дополнительные кусочки <keywordMajor>слизи</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Дальность", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [4, 4, 4, 4, 4], [1200, 1350, 1500, 1650, 1800], [0.9, 1, 1.1, 1.2, 1.3], [0.5, 0.5, 0.5, 0.5, 0.5], [500, 500, 500, 500, 500], [1350, 1350, 1350, 1350, 1350], [0.6, 0.6, 0.6, 0.6, 0.6], [265, 265, 265, 265, 265], [1, 1, 1, 1, 1]], "effectBurn": [null, "60/105/150/195/240", "4", "1200/1350/1500/1650/1800", "0.9/1/1.1/1.2/1.3", "0.5", "500", "1350", "0.6", "265", "1"], "vars": [], "costType": "% текущего здоровья ({{ healthcosttooltip }})", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ e2 }}% текущего здоровья ({{ healthcosttooltip }})"}, {"id": "ZacR", "name": "Попрыгаем!", "description": "Зак делает четыре отскока, подбрасывая и замедляя пораженных врагов.", "tooltip": "Зак отскакивает от земли {{ bounces }} раза. Первый для каждого врага отскок <status>отбрасывает</status> цель и наносит <magicDamage>{{ damageperbounce }} магического урона</magicDamage>. Последующие отскоки наносят <magicDamage>{{ damagepersubsequentbounce }} магического урона</magicDamage> и <status>замедляют</status> на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br />Во время действия умения Зак постепенно <speed>ускоряется на величину вплоть до {{ endingms*100 }}%</speed>, а также может применять <spellName>Нестабильную материю</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон отскока", "Перезарядка"], "effect": ["{{ basedamagebounce }} -> {{ basedamagebounceNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [1.1, 1.1, 1.1], [700, 850, 1000], [-0.3, -0.4, -0.5], [2.5, 2.5, 2.5], [275, 275, 275], [300, 300, 300], [1, 1, 1], [0.25, 0.25, 0.25], [0.1, 0.1, 0.1]], "effectBurn": [null, "150/250/350", "1.1", "700/850/1000", "-0.3/-0.4/-0.5", "2.5", "275", "300", "1", "0.25", "0.1"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Цитокинез", "description": "Каждый раз, когда Зак поражает противника умением, он отрывает от своего тела кусочек, который может поглотить обратно, восстановив часть своего здоровья. Получив смертельный урон, Зак распадается на 4 капли, которые пытаются воссоединиться. Если хотя бы одна из этих капель останется жива, Зак возродится с запасом здоровья, зависящим от оставшегося запаса здоровья уцелевших капель. Каждая капля получает долю от максимального запаса здоровья Зака, его брони и сопротивления магии. Перезарядка этого умения занимает 5 минут.", "image": {"full": "ZacPassive.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}