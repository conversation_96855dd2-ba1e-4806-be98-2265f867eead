{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Singed": {"id": "Singed", "key": "27", "name": "Singed", "title": "Šílený chemik", "image": {"full": "Singed.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "27000", "num": 0, "name": "default", "chromas": false}, {"id": "27001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Singed", "chromas": false}, {"id": "27002", "num": 2, "name": "Ma<PERSON><PERSON><PERSON><PERSON>ed", "chromas": false}, {"id": "27003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "27004", "num": 4, "name": "Šílený vě<PERSON><PERSON> Singed", "chromas": false}, {"id": "27005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "27006", "num": 6, "name": "Sně<PERSON><PERSON><PERSON>ed", "chromas": false}, {"id": "27007", "num": 7, "name": "SSW Singed", "chromas": false}, {"id": "27008", "num": 8, "name": "<PERSON><PERSON><PERSON> mor Singed", "chromas": false}, {"id": "27009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "27010", "num": 10, "name": "Rebel Singed", "chromas": true}, {"id": "27019", "num": 19, "name": "Astronaut Singed", "chromas": true}, {"id": "27028", "num": 28, "name": "Singed z laboratoře třpytu z Arcane", "chromas": false}], "lore": "Singed je geni<PERSON>lní alchymista s pochyb<PERSON><PERSON> morálk<PERSON>, z jeho<PERSON> experimentů by se <PERSON><PERSON><PERSON>alude<PERSON> i tomu nejdrsněj<PERSON><PERSON><PERSON>. Sv<PERSON> schopnosti prodává nejvyšší nabídce a nestará se o to, jak kdo používá jeho jed<PERSON> odvary, j<PERSON><PERSON><PERSON> nastalý chaos je experimentem sám o sobě. Jeho neblaze nejproslulejším dílem je „třpyt“, který umožnil chemobaronům proměnit Zaun ve své osobní hřiště – ale Singed, poh<PERSON><PERSON><PERSON><PERSON> šílenstvím, neustále pracuje na něčem novém a každý jeho podnik je zvrácenější než ten předchozí...", "blurb": "Singed je geniální alchymista s pochyb<PERSON>u morá<PERSON>, z jeho<PERSON>ů by se z<PERSON><PERSON> žaludek i tomu nejdrsněj<PERSON><PERSON><PERSON> zlo<PERSON>. Své schopnosti prodává nejvyšš<PERSON> nabídce a nestará se o to, jak kdo používá jeho jed<PERSON> odvary, j<PERSON><PERSON>ž nastalý chaos je...", "allytips": ["<PERSON>ová stopa je velice účinná při farmení a napadání nepřátel. Singed dokáže díky této schopnosti snadno získat převahu v libovolné lajně.", "<PERSON><PERSON><PERSON><PERSON><PERSON> šílenství a nalákej tak nepřátele, aby tě pronásledovali skrz Jed<PERSON> stopu.", "<PERSON><PERSON>ž nepřátele odhodíš na některou ze svých věží, m<PERSON><PERSON><PERSON><PERSON> jim tak způsobit výrazné poškození."], "enemytips": ["<PERSON><PERSON> <PERSON> od <PERSON> d<PERSON>, aby tě ne<PERSON>hl schopností Mrštit odhodit mezi ostatní nepřátele.", "Aby byl Singed v boji efektivní, musí se dostat blízko k druhému týmu. Zkus toho využít - můžeš na něj seslat nějakou schopnost s omezujícím efektem a dál útočit na jeho spojence.", "Při pronásledování Singeda si dávej dobrý pozor. Zabít jej není nijak snadné a může za sebou zanechávat Jed<PERSON>u stopu, která ti bude během nahánění způsobovat poškození."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 7, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 96, "mp": 330, "mpperlevel": 45, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9.5, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.9, "attackspeed": 0.7}, "spells": [{"id": "PoisonTrail", "name": "<PERSON><PERSON>a", "description": "Zanechává za Singedem jedovatou stopu, kter<PERSON> způsobí poškození všem nepř<PERSON>ům, kteří ji překříží.", "tooltip": "<toggle><PERSON><PERSON><PERSON><PERSON><PERSON>:</toggle> Singed za sebou zanech<PERSON>v<PERSON> jedovou stopu, k<PERSON><PERSON> z<PERSON>bu<PERSON> <magicDamage>{{ damagepersecond }} bod<PERSON> magického poškození</magicDamage> za sekundu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [13, 13, 13, 13, 13], "costBurn": "13", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " many za sekundu", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "PoisonTrail.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} many za sekundu"}, {"id": "MegaAdhesive", "name": "Megalepidlo", "description": "Vrhne na zem lahvičku megalepidla, jež <PERSON> a uzemní všechny nepřátele, kteří na něj vkročí.", "tooltip": "Singed v<PERSON><PERSON>e soudek s lepka<PERSON>u ka<PERSON>, kter<PERSON> na {{ wduration }}&nbsp;sek. <status>uzemní</status> nepřátele v zasažené oblasti a <status>zpomalí</status> je o {{ slowpercent }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Přebíjecí doba", "@AbilityResourceName@ pro seslání"], "effect": ["{{ slowpercent }} % -> {{ slowpercentNL }} %", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "MegaAdhesive.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "Fling", "name": "Přehodit", "description": "Poškodí vybranou nepřátelskou jednotku a vyhodí ji do vzduchu za Singeda. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Singed odmrští, přistane v jeho Megalepidle, bude v něm znehybněn.", "tooltip": "Singed mrští nepřítelem přes rameno a způsobí mu <magicDamage>magické poškození ve výši {{ basedamage }} bod<PERSON> plus {{ e3 }}&nbsp;% maximálního zdraví</magicDamage>.<br /><br /><PERSON><PERSON><PERSON>, k<PERSON><PERSON> Singed odmrští, přistane v oblasti zasažené schopností <spellName>Megalepidlo</spellName>, bude v ní na {{ e2 }}&nbsp;sek. <status>znehybněn</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození", "Poškození v závislosti na maximálním zdraví", "Trvání znehybnění", "Přebíjecí doba", "@AbilityResourceName@ pro seslání"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} % -> {{ e3NL }} %", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [50, 60, 70, 80, 90], [1, 1.25, 1.5, 1.75, 2], [6, 6.5, 7, 7.5, 8], [420, 420, 420, 420, 420], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/60/70/80/90", "1/1.25/1.5/1.75/2", "6/6.5/7/7.5/8", "420", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "Fling.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "InsanityPotion", "name": "Lektvar šílenství", "description": "Singed vypije mocný chemický od<PERSON>, <PERSON><PERSON><PERSON><PERSON> k bojovým atributům a jeho Jed<PERSON> stopa bude způsobovat Vážné rány.", "tooltip": "Singed vypije siln<PERSON> chem<PERSON> od<PERSON>, <PERSON><PERSON><PERSON><PERSON> na {{ duration }}&nbsp;sek. bonus +{{ statamount }} k <scaleAP>s<PERSON><PERSON></scaleAP>, <scaleArmor>brněn<PERSON></scaleArmor>, <scaleMR>odolnosti vůči magii</scaleMR>, <speed>rychlosti pohybu</speed>, <healing>regeneraci zdraví</healing> a <scaleMana>regeneraci many</scaleMana>. Po dobu trvání tohoto efektu navíc <PERSON>edova <spellName>Jedová stopa</spellName> aplikuje na {{ grievousduration }}&nbsp;sek. {{ grievousamount*100 }}% Vážné rány.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bonusové atributy"], "effect": ["{{ statamount }} -> {{ statamountNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "InsanityPotion.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "V závěsu", "description": "Singed se zachytí za šampiony ve svém okolí, a k<PERSON><PERSON> je <PERSON>, získá krátký <PERSON> k rychlosti pohybu.", "image": {"full": "Singed_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}