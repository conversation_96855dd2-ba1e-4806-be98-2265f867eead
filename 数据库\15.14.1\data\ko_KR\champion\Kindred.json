{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kindred": {"id": "Kindred", "key": "203", "name": "킨드레드", "title": "영겁의 사냥꾼", "image": {"full": "Kindred.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "203000", "num": 0, "name": "default", "chromas": false}, {"id": "203001", "num": 1, "name": "그림자 불꽃 킨드레드", "chromas": false}, {"id": "203002", "num": 2, "name": "슈퍼 갤럭시 킨드레드", "chromas": false}, {"id": "203003", "num": 3, "name": "영혼의 꽃 킨드레드", "chromas": true}, {"id": "203012", "num": 12, "name": "도자기 킨드레드", "chromas": true}, {"id": "203022", "num": 22, "name": "멍멍양 킨드레드", "chromas": true}, {"id": "203023", "num": 23, "name": "DRX 킨드레드", "chromas": true}, {"id": "203033", "num": 33, "name": "프레스티지 도자기 킨드레드", "chromas": false}, {"id": "203034", "num": 34, "name": "늑대에게 선택받은 자 킨드레드", "chromas": true}], "lore": "킨드레드는 따로지만 언제나 함께인 죽음의 양면을 지닌 존재다. 운명을 받아들인 자에게는 양의 화살로 빠른 죽음을 선사하고, 운명을 거부하고 도망치는 자에게는 늑대가 달려드는 잔혹한 최후를 안겨준다. 룬테라에서는 지역마다 킨드레드의 본성에 다른 의미를 부여했지만, 필멸의 존재라면 결국 진정한 죽음을 선택해야만 한다는 점은 같았다.", "blurb": "킨드레드는 따로지만 언제나 함께인 죽음의 양면을 지닌 존재다. 운명을 받아들인 자에게는 양의 화살로 빠른 죽음을 선사하고, 운명을 거부하고 도망치는 자에게는 늑대가 달려드는 잔혹한 최후를 안겨준다. 룬테라에서는 지역마다 킨드레드의 본성에 다른 의미를 부여했지만, 필멸의 존재라면 결국 진정한 죽음을 선택해야만 한다는 점은 같았다.", "allytips": ["정글 사냥 시 공격하는 사이사이 움직이면 몬스터의 공격을 피할 수 있을 뿐더러 늑대의 광기 스킬로 인한 치유 효과가 활성화됩니다.", "어떤 대상을 사냥할지 신중하게 선택하세요. 게임이 진행될수록 사냥에 많이 성공해야만 승리를 기약할 수 있습니다.", "대규모 교전 시엔 선두에서 들어가지 마세요. 아군이 공격을 개시하길 기다리는 편이 좋습니다."], "enemytips": ["킨드레드는 몸이 약한 편입니다. 압박을 주면 소극적인 플레이를 펼칠 수밖에 없습니다.", "사냥 효과 지우기 - 늑대가 사냥을 활성화한 정글 캠프의 몬스터를 모두 처치하면 킨드레드의 피해량이 커지는 것을 지연시킬 수 있습니다.", "킨드레드가 양의 안식처 스킬을 사용하면 영역 안으로 들어가세요. 어떤 챔피언이든 죽지 않게 됩니다."], "tags": ["Marksman"], "partype": "마나", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 4}, "stats": {"hp": 595, "hpperlevel": 104, "mp": 300, "mpperlevel": 35, "movespeed": 325, "armor": 29, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 3.25, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "KindredQ", "name": "화살 세례", "description": "킨드레드가 구르며 근처의 대상들에게 최대 세 발의 화살을 발사합니다.", "tooltip": "킨드레드가 뛰어올라 최대 3명의 적에게 화살을 발사하여 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ e8 }}초 동안 공격 속도가 <attackSpeed>{{ totalqattackspeed }}</attackSpeed>만큼 증가합니다.<br /><br /><spellName>늑대의 광기</spellName> 범위 안에 있는 동안 이 스킬의 재사용 대기시간이 {{ e4 }}초로 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "늑대의 광기 지대 내부 재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e4 }} -> {{ e4NL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [40, 65, 90, 115, 140], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 3.5, 3, 2.5, 2], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0.35, 0.35, 0.35, 0.35, 0.35], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/65/90/115/140", "0", "500", "4/3.5/3/2.5/2", "100", "12", "0.35", "4", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [340, 340, 340, 340, 340], "rangeBurn": "340", "image": {"full": "KindredQ.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KindredW", "name": "늑대의 광기", "description": "늑대가 분노하여 주변의 적들을 공격합니다. 양이 기본 지속 효과로 움직이고 공격하여 중첩을 얻습니다. 완전히 충전되면 양이 다음 번 공격할 때 체력을 회복합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 킨드레드가 이동하고 공격할 때마다 중첩이 쌓입니다. 100회 중첩 상태에서 다음 기본 공격 시 잃은 체력의 <healing>{{ attackheal }}에 해당하는 체력</healing>을 회복합니다.<br /><br /><spellActive>사용 시:</spellActive> 킨드레드가 지대를 지정하고 늑대에게 명령을 내려 양이 마지막으로 공격한 적을 물게 합니다. 늑대의 공격은 <magicDamage>{{ basewolfdamage }}</magicDamage>+현재 체력의 <magicDamage>{{ percentwolfdamage }}에 해당하는 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [40, 45, 50, 55, 60], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [8.5, 8.5, 8.5, 8.5, 8.5], [25, 30, 35, 40, 45], [800, 800, 800, 800, 800], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/45/50/55/60", "1", "1.5", "8.5", "25/30/35/40/45", "800", "0.5", "0.5", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [560, 560, 560, 560, 560], "rangeBurn": "560", "image": {"full": "KindredW.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KindredEWrapper", "name": "차오르는 공포", "description": "양이 신중하게 화살을 발사해 대상의 속도를 늦춥니다. 양이 대상을 두 번 더 공격하면 세 번째 공격 시 늑대가 적을 덮치게 만들어, 흉포한 공격으로 큰 피해를 입힙니다.", "tooltip": "킨드레드가 적을 약화시켜 {{ slowduration }}초 동안 {{ totalslow }}% <status>둔화</status>시킵니다.<br /><br />{{ totalduration }}초 내에 대상을 세 번 공격하면 늑대가 적을 덮쳐 <physicalDamage>{{ basebitedamage }}</physicalDamage>+<physicalDamage>적이 잃은 체력의 {{ percentbitedamage }}에 해당하는 물리 피해</physicalDamage>를 입힙니다.<br /><br />대상의 체력이 {{ healththreshold }} 이하일 경우 늑대가 대상을 덮쳐 <physicalDamage>{{ basebitedamage }}</physicalDamage>+<physicalDamage>대상이 잃은 체력의 {{ critdamage }}에 해당하는 물리 피해</physicalDamage>를 치명타로 적용합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "KindredEWrapper.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KindredR", "name": "양의 안식처", "description": "양은 안식처 영역 안에 있는 모든 생명체를 죽음으로부터 지켜줍니다. 효과가 끝날 때까지는 아무도 죽지 않습니다. 효과가 끝나면 유닛들이 치유됩니다.", "tooltip": "킨드레드가 {{ e2 }}초 동안 땅을 축복하여 해당 영역 안에 있는 아군, 적, 중립 몬스터를 포함한 모든 유닛이 사망하지 않습니다. 체력이 10%로 떨어지면 유닛들이 해당 영역 안에 있는 동안 피해를 받거나 치유되지 않습니다.<br /><br />축복이 끝나면 영역 안에 있는 모든 유닛이 <healing>체력을 {{ e1 }}</healing> 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [225, 300, 375], [4, 4, 4], [530, 530, 530], [400, 400, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "225/300/375", "4", "530", "400", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "KindredR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "킨드레드의 표식", "description": "킨드레드는 사냥감에게 표식을 남길 수 있습니다. 사냥에 성공하면 영구적으로 킨드레드의 기본 스킬이 강화됩니다. 사냥에 4회 성공하면 킨드레드의 기본 공격 사거리가 증가합니다.", "image": {"full": "Kindred_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}