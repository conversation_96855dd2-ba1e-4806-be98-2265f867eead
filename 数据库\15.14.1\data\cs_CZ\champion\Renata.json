{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "Renata <PERSON>", "title": "Chemobaronka", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "Admirálka <PERSON>", "chromas": true}, {"id": "888010", "num": 10, "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "chromas": true}, {"id": "888020", "num": 20, "name": "La Ilusión Renata Glasc", "chromas": true}, {"id": "888021", "num": 21, "name": "Prestižní La Ilusión Renata Glasc", "chromas": false}, {"id": "888031", "num": 31, "name": "Renata Glasc s maskou Černé růže", "chromas": false}], "lore": "Renata Glasc povstala z popela svého rodného domova a neměla nic než své příjmení a alchymistický výzkum svých rodičů. Po několika desítkách let se z ní stala nejbohatší chemobaronka v Zaunu – obchodní magnátka, která svou moc vybudovala tak, že propletla zájmy všech s těmi svými. Kdo pracuje pro ni, bude velmi štědře odměněn. Kdo pracuje proti ní, bude toho litovat. Ale nakonec na její stranu přejdou všichni.", "blurb": "Renata Glasc povstala z popela svého rodného domova a neměla nic než své příjmení a alchymistický výzkum svých rodičů. Po několika desítkách let se z ní stala nejbohatší chemobaronka v Zaunu – obchodní magnátka, která svou moc vybudovala tak, že...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "<PERSON>uku na to", "description": "Renata vystř<PERSON><PERSON> r<PERSON>, která znehybní prvního zasaženého nepřítele. Poté může schopnost seslat znovu a mrštit jednotku zvoleným směrem.", "tooltip": "Renata vystřelí z pa<PERSON>e raketu, kter<PERSON> na {{ rootduration }}&nbsp;sek. <status>znehybní</status> prvn<PERSON>ho zasaženého nepřítele a způsobí mu <magicDamage>{{ totaldamage }}</magicDamage> <magicDamage>bodů magického poš<PERSON>zení</magicDamage>.<br /><br /><recast>Opětovné seslání:</recast> Renata <status>přitáhne</status> nepřítele zvoleným směrem, způsobí tak <magicDamage>{{ totaldamage }} bodů magického poš<PERSON>zení</magicDamage> nep<PERSON><PERSON><PERSON>ům, do kterých byl protivník hozen, a jednalo-li se o šampiona, na {{ stunduration }}&nbsp;sek. je <status>omráčí</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RenataW", "name": "<PERSON><PERSON>", "description": "Renata posí<PERSON><PERSON> s<PERSON><PERSON><PERSON>, a<PERSON> b<PERSON><PERSON><PERSON>, oddálí tak jeho smrt a umožní mu se zachránit, pokud si připíše likvidaci.", "tooltip": "Renata posílí spojeneckého šampiona – poskytne mu <attackSpeed>{{ ascalc }} k rychlosti útoků</attackSpeed> a <speed>{{ mscalc }} k rychlosti pohybu</speed> směrem k nepř<PERSON><PERSON>ům, tento bonus se během {{ duration }}&nbsp;sek. zvyšuje až na <attackSpeed>{{ finalascalc }} k rychlosti útoků</attackSpeed> a <speed>{{ finalmscalc }} k rychlosti pohybu</speed>. Provedené likvidace trvání tohoto buffu obnovují.<br /><br />Pokud by mě<PERSON> spoje<PERSON><PERSON>, obnoví si místo toho plné zdraví, které po dobu 3&nbsp;sek. postupně mizí.<br /><br />Jestliže si jednotka během ubývání zdraví připíše likvidaci, nastaví se její zdraví na <healing>{{ triumphpercent }}&nbsp;% maximálního zdraví</healing> a jeho hodnota přestane klesat.<br /><br /><rules>Smrt šampiona lze během ubývání zdraví oddálit léčením a podobnými efekty, nelze jí však zabránit jinak než připsáním likvidace. Šampioni mohou pozdržet svou smrt jen jednou.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rychlost útoků", "Rychlost pohybu", "Přebíjecí doba"], "effect": ["{{ bonusattackspeed }} % -> {{ bonusattackspeedNL }} %", "{{ bonusmovespeed }} % -> {{ bonusmovespeedNL }} %", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RenataE", "name": "Věrnostní program", "description": "Renata vystřelí dvojici chemotechnických raket, k<PERSON><PERSON> poskytnou spojencům štít a zasažené nepřátele poškodí a zpomalí.", "tooltip": "Renata vystřelí dvojici chemotechnických raket, kter<PERSON> okolním a zasaženým nepřátelům způsobí <magicDamage>{{ totaldamage }} bodů magického poš<PERSON>í</magicDamage> a na {{ slowduration }}&nbsp;sek. je <status>zpomal<PERSON></status> o 30&nbsp;%. Zasažení spojenci zís<PERSON>jí na {{ shieldduration }}&nbsp;sek. <shield>štít o síle {{ shieldcalc }} bod<PERSON></shield>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození", "Přebíjecí doba", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ pro seslání"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RenataR", "name": "Násilné <PERSON>í", "description": "Renata vyšle zvoleným směrem vlnu chemikálií a všichni zasažení nepřátelé propadnou běsnění.", "tooltip": "Renata vyšle zvoleným směrem vlnu chemikálií. Zasažení nepřátelé budou po dobu {{ berserkduration }}&nbsp;sek. <status>běsnit</status> a útočit na nejbližší jednot<PERSON>, primárně na své vlastní spojence.<br /><br />Po dobu <status>běsnění</status> získávají nepřátelé <attackSpeed>{{ bonusattackspeed*100 }}&nbsp;% k rychlosti útoků</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Přebíjecí doba", "Trvání"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Vliv", "description": "Renatiny útoky způsobují bonusové poškození a označují nepřátele. Renatini spojenci mohou poškozením označených nepřátel způsobit bonusové poškození.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}