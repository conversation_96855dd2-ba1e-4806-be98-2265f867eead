{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "워윅", "title": "자운의 고삐 풀린 분노", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "회색 워윅", "chromas": true}, {"id": "19002", "num": 2, "name": "바다사자 우르프", "chromas": false}, {"id": "19003", "num": 3, "name": "크고 사나운 워윅", "chromas": false}, {"id": "19004", "num": 4, "name": "극지 사냥꾼 워윅", "chromas": false}, {"id": "19005", "num": 5, "name": "야성의 워윅", "chromas": false}, {"id": "19006", "num": 6, "name": "불송곳니 워윅", "chromas": false}, {"id": "19007", "num": 7, "name": "하이에나 워윅", "chromas": false}, {"id": "19008", "num": 8, "name": "습격자 워윅", "chromas": false}, {"id": "19009", "num": 9, "name": "우르프 워윅", "chromas": false}, {"id": "19010", "num": 10, "name": "달빛 수호자 워윅", "chromas": true}, {"id": "19016", "num": 16, "name": "프로젝트: 워윅", "chromas": true}, {"id": "19035", "num": 35, "name": "고대 신 워윅", "chromas": false}, {"id": "19045", "num": 45, "name": "겨울의 축복 워윅", "chromas": false}, {"id": "19046", "num": 46, "name": "프레스티지 겨울의 축복 워윅", "chromas": false}, {"id": "19056", "num": 56, "name": "아케인 밴더 워윅", "chromas": false}], "lore": "자운의 잿빛 골목을 무대로 사냥감을 찾는 괴수 워윅. 극한의 고통을 동반하는 각종 실험으로 완전히 변형된 그의 육체는 약품통, 펌프, 여러 기계가 결합된 복잡한 시스템과 연결되어 있으며, 정맥에는 연금술을 통해 조합한 분노가 주입된다. 어두운 그림자 속에서 느닷없이 튀어나오곤 하는 그는 사람들을 위협하는 도시의 범죄자들을 단숨에 사냥한다. 피 냄새를 맡고 나면 이성을 잃고 마는 워윅... 조금이라도 피를 흘린 자는 그 누구도 워윅의 손아귀에서 벗어날 수 없다.", "blurb": "자운의 잿빛 골목을 무대로 사냥감을 찾는 괴수 워윅. 극한의 고통을 동반하는 각종 실험으로 완전히 변형된 그의 육체는 약품통, 펌프, 여러 기계가 결합된 복잡한 시스템과 연결되어 있으며, 정맥에는 연금술을 통해 조합한 분노가 주입된다. 어두운 그림자 속에서 느닷없이 튀어나오곤 하는 그는 사람들을 위협하는 도시의 범죄자들을 단숨에 사냥한다. 피 냄새를 맡고 나면 이성을 잃고 마는 워윅... 조금이라도 피를 흘린 자는 그 누구도 워윅의 손아귀에서 벗어날...", "allytips": ["피의 사냥 흔적을 따라가면 체력이 낮은 적 챔피언을 찾을 수 있습니다.", "무한의 구속 (R) 도약 사거리는 아군이 준 이로운 효과나 소환사 주문 효과를 비롯한 모든 이동 속도 증가분에 비례합니다.", "야수의 송곳니(Q)를 계속 누르고 있으면 도망치거나 도약, 순간이동하는 적도 따라갈 수 있습니다."], "enemytips": ["워윅은 체력이 낮을 때 공격하면 체력을 회복합니다. 완벽하게 마무리하려면 군중 제어기를 남겨두세요.", "워윅은 체력이 낮은 대상 상대로 특히 강합니다. 체력 관리에 유의하세요.", "워윅 궁극기의 사거리는 이동 속도에 비례합니다."], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "야수의 송곳니", "description": "워윅이 앞으로 도약해 대상을 물고 대상 최대 체력에 비례한 피해를 입힙니다. 이때 워윅은 입힌 피해량만큼 체력을 회복합니다.", "tooltip": "<tap>짧게 누를 때:</tap> 워윅이 앞으로 도약한 후 대상을 물어 <magicDamage>{{ basebitedamage }}+최대 체력의 {{ targetpercenthpdamage }}%에 해당하는 마법 피해</magicDamage>를 입히고 <healing>입힌 피해량의 {{ e3 }}%만큼 회복</healing>합니다.<br /><br /><hold>길게 누를 때:</hold> 워윅이 도약한 후 대상을 꽉 물며 뒤로 넘어 갑니다. 꽉 문 동안 워윅은 대상이 이동할 때 같이 이동합니다. 대상을 놓은 후에는 같은 양의 피해를 입히고 체력을 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "% 체력 비례 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e3 }}% -> {{ e3NL }}%", "{{ targetpercenthpdamage }}% -> {{ targetpercenthpdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "WarwickW", "name": "피의 사냥", "description": "체력이 50% 미만인 적을 감지합니다. 이 대상에게 이동할 때는 워윅의 이동 속도가 증가하고 대상에 대한 공격 속도도 증가합니다. 적의 체력이 25% 아래로 내려가면 워윅이 광분하며 추가 효과가 세 배로 증가합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 체력이 50% 미만인 챔피언을 감지합니다. 해당 챔피언을 향해 이동할 경우 <speed>이동 속도가 {{ passivemsbonus }}%</speed> 증가합니다. 해당 챔피언에게 스킬 및 기본 공격을 가할 경우 <speed>공격 속도가 {{ passiveasbonus }}%</speed> 증가합니다. 적의 체력이 25% 이하일 경우 이 효과들은 200% 증가합니다. <br /><br /><spellActive>사용 시:</spellActive> 잠시 동안 모든 적의 위치를 감지하여 체력과 상관없이 가장 가까이에 있는 적 챔피언에게 8초 동안 이 스킬의 기본 지속 효과를 적용합니다. 발견된 챔피언이 없으면 이 스킬의 재사용 대기시간이 30% 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동 속도", "공격 속도", "재사용 대기시간"], "effect": ["{{ passivemsbonus }}% -> {{ passivemsbonusNL }}%", "{{ passiveasbonus }}% -> {{ passiveasbonusNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "WarwickE", "name": "원시의 포효", "description": "2.5초 동안 워윅이 입는 피해가 감소합니다. 지속시간이 종료되거나 스킬을 재사용하면 워윅이 포효하며 근처의 모든 적을 1초 동안 도망치게 합니다.", "tooltip": "2.5초 동안 워윅이 입는 피해가 {{ e1 }}% 감소합니다. 지속시간이 종료되면 워윅이 포효하며 근처의 모든 적을 {{ e3 }}초 동안 <status>공포</status>에 빠뜨립니다. <recast>재사용</recast>하면 스킬이 일찍 종료됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량 감소", "재사용 대기시간"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "WarwickR", "name": "무한의 구속", "description": "워윅이 지정한 방향으로 도약해 (도약 거리는 추가 이동 속도에 비례) 첫 번째로 부딪힌 챔피언을 1.5초 동안 제압합니다.", "tooltip": "워윅이 <speed>이동 속도</speed>에 비례하는 먼 거리를 도약하여 첫 번째로 부딪힌 적 챔피언을 {{ rduration }}초 동안 <status>제압</status>하며 정신을 집중합니다. 지속시간 동안 해당 챔피언을 3회 공격해 <magicDamage>{{ damagecumulative }}의 마법 피해</magicDamage>를 입힙니다. 워윅은 정신을 집중하면서 <healing>입힌 모든 피해량의 100%</healing>만큼 체력을 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "끝없는 허기", "description": "워윅이 기본 공격 시 추가 마법 피해를 입힙니다. 워윅의 체력이 50% 아래로 내려가면 추가 피해량만큼 체력을 회복합니다. 체력이 25% 아래로 내려가면 체력 회복량이 세 배로 증가합니다.", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}