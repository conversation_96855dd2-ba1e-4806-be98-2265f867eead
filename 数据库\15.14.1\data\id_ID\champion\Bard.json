{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Bard", "title": "the Wandering Caretaker", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "<PERSON><PERSON> Bard", "chromas": false}, {"id": "432005", "num": 5, "name": "Snow Day Bard", "chromas": true}, {"id": "432006", "num": 6, "name": "Bard Bard", "chromas": false}, {"id": "432008", "num": 8, "name": "Astronaut Bard", "chromas": true}, {"id": "432017", "num": 17, "name": "Cafe Cuties Bard", "chromas": true}, {"id": "432026", "num": 26, "name": "Shan <PERSON> Scrolls Bard", "chromas": true}, {"id": "432035", "num": 35, "name": "T1 Bard", "chromas": true}, {"id": "432037", "num": 37, "name": "Spirit Blossom Bard", "chromas": true}], "lore": "Pengembara dari luar gugusan bintang, Bard terpanggil untuk berjuang menjaga keseimbangan supaya kehidupan bisa bertahan dalam kekacauan. Banyak warga Runeterra menyanyikan lagu tentang kehebatannya, tetapi mereka menganggap bahwa pengembara kosmik ini mengincar artefak berkekuatan magis luar biasa. Dikelilingi paduan suara meep spirit penolong yang riang, dia mustahil dianggap jahat, karena <PERSON><PERSON> selalu melakukan kebaikan yang lebih besar ... dengan caranya yang aneh.", "blurb": "Pengembara dari luar gugusan bin<PERSON>, Bard terpanggil untuk berjuang menjaga keseimbangan supaya kehidupan bisa bertahan dalam kekacauan. Banyak warga Runeterra menyanyikan lagu tentang kehebatannya, tetapi mereka menganggap bahwa pengembara kosmik ini...", "allytips": ["<PERSON><PERSON><PERSON><PERSON>an chime untuk meningkatkan serangan meep memang penting, tetapi jangan abaikan partnermu di lane! <PERSON>ba buat kejutan dengan membawa sekutu ke jalur menggunakan Magical Journey.", "Biarkan Caretaker's Shrine terisi penuh karena member<PERSON>n heal jauh lebih besar saat maksimal.", "<PERSON><PERSON> lup<PERSON>, musuh juga bisa menggunakan portal Magical Journey, dan ultimamu juga bisa mengenai sekutu!"], "enemytips": ["<PERSON><PERSON><PERSON> juga bisa melewati pintu Magical Journey. <PERSON>mu bisa men<PERSON>, jika menurutmu aman situasinya.", "Kamu bisa menghancurkan healing shrine Bard hanya dengan melewatinya. <PERSON>an sampai sekutu mengambilnya tanpa perlawanan.", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON>, memen<PERSON><PERSON><PERSON> sekutu, musuh, monster, dan turret. Kadang dengan melompatinya akan menguntungkanmu!"], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "Cosmic Binding", "description": "Bard menembakkan misil yang akan menerapkan slow pada musuh pertama yang terkena, dan terus melaju. <PERSON><PERSON> mengenai dinding, misil akan stun target awal; jika mengenai musuh lain, misil akan stun keduanya.", "tooltip": "Bard menembakkan energy bolt, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> pada dua musuh pertama yang terkena. Target pertama yang terkena mendapat <status>Slow</status> sebanyak {{ slowamountpercentage }}% selama {{ slowduration }} dtk.<br /><br />Jika bolt mengenai musuh kedua atau dinding, musuh yang terkena mendapat <status>Stun</status> selama {{ stunduration }} dtk.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>:", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ slowduration }}-> {{ slowdurationNL }}", "{{ stunduration }}-> {{ stundurationNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Caretaker's Shrine", "description": "Mengungkap Health Shrine yang akan terisi per<PERSON>, mengh<PERSON>ng setelah member<PERSON>n heal dan mempercepat gerakan sekutu pertama yang menyentuhnya.", "tooltip": "Bard menciptakan Health Shrine yang memberikan <speed>{{ calc_movespeed }} Move Speed</speed> yang berkurang dalam kurun waktu {{ movespeed_duration }} detik dan memulihkan setidaknya <healing>{{ initialheal }} Health</healing> ke sekutu pertama yang masuk. Shrine berkembang untuk memulihkan <healing>{{ maxheal }} Health</healing> setelah muncul selama {{ chargeuptime }} detik.<br /><br />Bard bisa memiliki hingga {{ maxpacks }} Shrine sekaligus. Shrine akan hancur jika champion musuh memasukinya.<br /><br />Ability ini memiliki {{ ammo_limit }} charge.<br /><br />Shrine Aktif Saat Ini: {{ f1 }} / {{ f2 }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Move Speed"], "effect": ["{{ e5 }}-> {{ e5NL }}", "{{ e6 }}-> {{ e6NL }}", "{{ movespeed_base*100.000000 }}%-> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Magical Journey", "description": "Bard membuka portal di medan sekitar. Sekutu dan musuh bisa melakukan perjalanan satu arah melalui medan tersebut dengan masuk ke dalam portal.", "tooltip": "Bard membuka portal satu arah melalui medan selama {{ e1 }} detik. Semua champion bisa memasuki portal dengan masuk ke sana saat di dekat pintu masuk.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "Tempered Fate", "description": "Bard melepaskan spirit energy yang mengalir ke suatu lokasi, membuat semua champion, minion, monster, dan turret yang terkena memasuki kondisi stasis untuk sementara.", "tooltip": "Bard melemparkan energi protektif ajaib ke suatu area, membuat semua unit dan bangunan yang terkena memasuki kondisi Stasis selama {{ rstasisduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Traveler's Call", "description": "<font color='#FF9900'>Meep:</font> Bard menarik lesser spirit yang membantu dengan basic attack-nya untuk menghasilkan magic damage ekstra. Saat Bard mengumpulkan cukup <font color='#cccc00'>Chime</font>, Meep-nya juga akan menghasilkan damage dalam 1 area dan menerapkan slow pada musuh yang terkena.<br><br><font color='#FF9900'>Chime:</font> <font color='#cccc00'>Chime</font> kuno secara random muncul untuk Bard ambil. Chime memberikan XP, memu<PERSON><PERSON><PERSON> mana, dan memberikan Move Speed di luar kombat.", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}