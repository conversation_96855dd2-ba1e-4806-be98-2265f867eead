{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yasuo": {"id": "<PERSON><PERSON><PERSON>", "key": "157", "name": "<PERSON><PERSON><PERSON>", "title": "il reietto", "image": {"full": "Yasuo.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "157000", "num": 0, "name": "default", "chromas": false}, {"id": "157001", "num": 1, "name": "<PERSON><PERSON><PERSON> di Fuoco", "chromas": true}, {"id": "157002", "num": 2, "name": "PROGETTO: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "157003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "157009", "num": 9, "name": "<PERSON><PERSON><PERSON> della Notte", "chromas": true}, {"id": "157010", "num": 10, "name": "Yasuo dell'Odissea", "chromas": true}, {"id": "157017", "num": 17, "name": "<PERSON><PERSON><PERSON> da Battaglia", "chromas": true}, {"id": "157018", "num": 18, "name": "<PERSON><PERSON><PERSON> True Damage", "chromas": true}, {"id": "157035", "num": 35, "name": "<PERSON><PERSON>o True Damage (edizione prestigio)", "chromas": false}, {"id": "157036", "num": 36, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157045", "num": 45, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157054", "num": 54, "name": "<PERSON><PERSON><PERSON> Verità", "chromas": false}, {"id": "157055", "num": 55, "name": "Yasuo Drago dei Sogni", "chromas": false}, {"id": "157056", "num": 56, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "157057", "num": 57, "name": "<PERSON><PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "157068", "num": 68, "name": "<PERSON><PERSON><PERSON> Profezia", "chromas": true}, {"id": "157077", "num": 77, "name": "<PERSON><PERSON><PERSON> Battaglia", "chromas": true}, {"id": "157087", "num": 87, "name": "<PERSON><PERSON><PERSON> della Notte Genesi", "chromas": false}], "lore": "Un determinato abitante di Ionia, Ya<PERSON><PERSON> è un agile spadaccino addestrato a usare l'aria come arma contro i suoi avversari. In gioventù l'orgoglio lo ha condotto alla rovina facendogli perdere la sua posizione, il suo mentore e, infine, suo fratello. <PERSON>aduto in disgrazia per via di false accuse e ora braccato come un criminale, Ya<PERSON><PERSON> vaga per la sua terra natia in cerca di redenzione, con il vento a guidare la sua lama.", "blurb": "Un determinato abitante di Ionia, <PERSON><PERSON><PERSON> è un agile spadaccino addestrato a usare l'aria come arma contro i suoi avversari. In gioventù l'orgoglio lo ha condotto alla rovina facendogli perdere la sua posizione, il suo mentore e, infine, suo fratello...", "allytips": ["Scatta attraverso un minion per avere un Colpo di taglio pronto per inseguire il tuo avversario, nel caso tentasse di scappare. Scatta direttamente verso l'avversario per tenere un minion come via di fuga.", "Al livello 18, <PERSON>mpesta d'acciaio arriva al massimo della sua velocità d'attacco, con un 55% di velocità d'attacco data dagli oggetti.", "<PERSON>ltimo respiro può essere lanciato su qualunque bersaglio in aria (anche se non è stato lanciato in aria da Yasu<PERSON>, ma da un alleato)."], "enemytips": ["Tempesta d'acciaio ha un raggio di azione limitato. Una buona strategia consiste nell'evitare il colpo lateralmente. ", "Dopo aver lanciato due Tempeste di acciaio, <PERSON><PERSON><PERSON> lancerà un tornado. Prepararvi a schivare il colpo.", "<PERSON><PERSON><PERSON> è debole dopo aver lanciato un vortice di vento. Cercate di affrontarlo in questo momento.", "Lo scudo di Volontà di ferro dura 2 secondi. Se lo attaccate, lo scudo viene attivato. Aspettate che esaurisca il suo effetto e poi attaccatelo."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 10}, "stats": {"hp": 590, "hpperlevel": 110, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.9, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.697}, "spells": [{"id": "YasuoQ1Wrapper", "name": "Tempesta d'acciaio", "description": "<PERSON>atta in avanti, da<PERSON><PERSON><PERSON><PERSON> tutti i nemici lungo una linea.<br><br><PERSON> colpo, Tempesta d'acciaio conferisce una carica di Tempesta incombente per qualche secondo. A 2 cariche, Tempesta d'acciaio rilascia una raffica che lancia <font color='#6655CC'>in aria</font>.<br><br>Tempesta d'acciaio conta come un attacco base e cresce nello stesso modo.", "tooltip": "Yasuo esegue un affondo, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>. Se colpisce, ottiene una carica per {{ gatheringstormduration }} secondi. A 2 cariche, l'uso successivo di questa abilità scaglia in avanti un tornado che infligge gli stessi danni e <status>lancia in aria</status> il bersaglio per {{ knockupdurationtooltiponly }} secondo.<br /><br />Se usato mentre scatta, questa abilità colpisce in un cerchio invece che in una linea.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "YasuoQ1Wrapper.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YasuoW", "name": "<PERSON>ro di vento", "description": "Crea un muro di vento che blocca tutti i proiettili nemici per 4 secondi.", "tooltip": "<PERSON><PERSON><PERSON> crea un muro di vento che blocca tutti i proiettili nemici per 4 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON>", "Ricarica"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [25, 23, 21, 19, 17], "cooldownBurn": "25/23/21/19/17", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [15, 20, 25, 30, 35], [60, 90, 120, 150, 180], [3, 6, 9, 12, 15], [300, 350, 400, 450, 500], [320, 390, 460, 530, 600], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "15/20/25/30/35", "60/90/120/150/180", "3/6/9/12/15", "300/350/400/450/500", "320/390/460/530/600", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "YasuoW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YasuoE", "name": "<PERSON><PERSON>lio", "description": "Scatta attraverso il nemico bersaglio, infliggendo danni magici. Ogni lancio aumenta i danni dello scatto successivo, fino a una quantità massima.<br><br>L'abilità non può essere lanciata di nuovo sullo stesso nemico per qualche secondo.<br><br><font color='#99FF99'>Se lanciata durante lo scatto, Tempesta d'acciaio colpirà in cerchio.</font>", "tooltip": "<PERSON><PERSON><PERSON> scatta attraverso un bersaglio, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>. Ogni uso di questa abilità conferisce <magicDamage>{{ bonusdamageperstack }}</magicDamage> danni aggiuntivi agli usi successivi per {{ stackduration }} secondi, per un massimo di {{ maxstacks }} volte.<br /><br />Questa abilità ha una ricarica di {{ e2 }} secondo/i per bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica per unità", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.4, 0.3, 0.2, 0.1], "cooldownBurn": "0.5/0.4/0.3/0.2/0.1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [10, 9, 8, 7, 6], [0.5, 0.4, 0.3, 0.2, 0.1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "10/9/8/7/6", "0.5/0.4/0.3/0.2/0.1", "0", "0", "750", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "YasuoE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YasuoR", "name": "<PERSON><PERSON><PERSON>", "description": "Appare vicino a un campione nemico <factionIonia1>in aria</factionIonia1> infliggendo danni fisici e mantenendo <factionIonia1>in aria</factionIonia1> i nemici già in aria nell'area. Conferisce il massimo del Flusso ma azzera le cariche di Tempesta incombente.<br><br>Per un moderato periodo di tempo, i colpi critici di Yasuo ottengono una grande quantità di penetrazione armatura bonus.", "tooltip": "Yasuo si teletrasporta vicino a un campione nemico <status>in aria</status> infliggendo <physicalDamage>{{ damage }} danni fisici</physicalDamage> e mantenendo <status>in aria</status> tutti i nemici vicini già in aria per {{ rknockupduration }} secondo in più. Yasuo guadagna anche il massimo di <keywordMajor>Flusso</keywordMajor>, ma perde tutte le cariche di <spellName>Tempesta d'acciaio</spellName>.<br /><br />Successivamente i colpi critici di Yasuo ignorano <scaleArmor>{{ rpercentarmorpen }}% armatura bonus</scaleArmor> per {{ rbuffduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [70, 50, 30], "cooldownBurn": "70/50/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1400, 1400, 1400], "rangeBurn": "1400", "image": {"full": "YasuoR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Via del vagabondo", "description": "La probabilità di colpo critico di Yasuo aumenta. <PERSON><PERSON><PERSON>, quando Yasuo si muove accumula punti che lo portano a ottenere uno scudo. Lo scudo si attiva quando subisce danni da un campione o da un mostro.", "image": {"full": "Yasuo_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}