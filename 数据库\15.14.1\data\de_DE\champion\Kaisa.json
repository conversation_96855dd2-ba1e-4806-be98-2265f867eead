{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kaisa": {"id": "<PERSON><PERSON>", "key": "145", "name": "Kai'Sa", "title": "<PERSON><PERSON><PERSON> der Leere", "image": {"full": "Kaisa.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "145000", "num": 0, "name": "default", "chromas": false}, {"id": "145001", "num": 1, "name": "Ra<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "145014", "num": 14, "name": "K/DA-Kai'Sa", "chromas": false}, {"id": "145015", "num": 15, "name": "K/DA-Kai'Sa (Prestige)", "chromas": false}, {"id": "145016", "num": 16, "name": "iG-<PERSON><PERSON>Sa", "chromas": false}, {"id": "145017", "num": 17, "name": "Arcade-Kai'Sa", "chromas": false}, {"id": "145026", "num": 26, "name": "K/DA ALL OUT-Kai'Sa", "chromas": false}, {"id": "145027", "num": 27, "name": "K/DA ALL OUT-Kai'Sa (Prestige)", "chromas": false}, {"id": "145029", "num": 29, "name": "Lagunendrache Kai'Sa", "chromas": false}, {"id": "145039", "num": 39, "name": "K/DA-Kai'Sa (Prestige 2022)", "chromas": false}, {"id": "145040", "num": 40, "name": "Sternenwächterin Kai'Sa", "chromas": false}, {"id": "145048", "num": 48, "name": "Tintenschatten-Kai'Sa", "chromas": false}, {"id": "145059", "num": 59, "name": "Himmelsschuppen-Kai'Sa", "chromas": false}, {"id": "145069", "num": 69, "name": "Sternenvernichter-Kai'Sa", "chromas": false}, {"id": "145070", "num": 70, "name": "Emporgestiegene Legende Kai'Sa", "chromas": false}, {"id": "145071", "num": 71, "name": "Verewigte Legende Kai'Sa", "chromas": false}], "lore": "<PERSON>'<PERSON> musste schon als Kind mit der Leere zurechtkommen und es gelang ihr nur durch reine Zähigkeit und Willensstärke, zu überleben. Ihre Erfahrungen haben sie zu einer tödlichen Jägerin gemacht, und für manche ist sie die Vorbotin einer Zukunft, die sie lieber nicht erleben wollen. Sie ist eine unruhige Symbiose mit einem lebenden Leerenpanzer eingegangen und wird sich bald entscheiden müssen, ob sie den Sterblichen vergibt, die sie als Monster bezeichnen, und die nahende Finsternis mit ihnen zusammen bekämpft … oder einfach alles vergisst, während die Leere die Welt verschlingt, die sie alleine zurückgelassen hat.", "blurb": "<PERSON>'<PERSON> musste schon als Kind mit der Leere zurechtkommen und es gelang ihr nur durch reine Zähigkeit und Willensstärke, zu überleben. Ihre Erfahrungen haben sie zu einer tödlichen Jägerin gemacht, und für manche ist sie die Vorbotin einer Zukunft, die...", "allytips": ["<PERSON><PERSON><PERSON>, gegnerische Schützen alleine zu erwischen, damit du sie schnell mit „Icathianische Salve“ ausschalten kannst.", "Arbeite mit deinen Teamkameraden zusammen, um deinen Ult effektiv einzusetzen und den Schaden deines Passivs zu optimieren.", "<PERSON><PERSON>, dass du Gegenstände erwirbst, die mindestens 1 oder 2 deiner Fähigkeiten weiterentwickeln."], "enemytips": ["<PERSON><PERSON><PERSON> ist sehr gut darin, isolierte Gegner auszuschalten. <PERSON><PERSON><PERSON><PERSON> zu<PERSON>mmen, wenn sie gegen euch spielt.", "Kai'Sa ist sehr verwundbar gegen Magier und Schützen mit hoher Reichweite.", "Platziere stets Augen, um deine toten Winkel abzudecken und Kai<PERSON>Sa schon aus der Ferne kommen zu sehen."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 345, "mpperlevel": 40, "movespeed": 335, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.6, "attackspeedperlevel": 1.8, "attackspeed": 0.644}, "spells": [{"id": "KaisaQ", "name": "Icathianische Salve", "description": "<PERSON>'<PERSON> feuert eine Salve von Geschossen ab, die auf nahe Ziele zufliegen.<br><br><PERSON><PERSON><PERSON><PERSON><PERSON>: „Icathianische Salve“ wird aufgewertet und feuert mehr Geschosse ab.", "tooltip": "<PERSON>'<PERSON> feuert {{ e2 }}&nbsp;Geschosse ab, die sich auf Gegner in der Nähe aufteilen. Jedes Gescho<PERSON> verursacht dabei <physicalDamage>{{ totalindividualmissiledamage }}&nbsp;normalen Schaden</physicalDamage> (bis zu einem Maximum von {{ maxdamagedisplay }}). Zusätzliche Raketentreffer gegen Champions oder Monster verursachen {{ extrahitreduction*100 }}&nbsp;% Schaden.<br /><br /><keywordMajor>Entwickelt</keywordMajor>: Kai'<PERSON> feuert stattdessen {{ e7 }}&nbsp;Raketen ab.<br />Aktuell: <physicalDamage>{{ f11.1 }}/{{ e6 }} zusätzlicher Angriffsschaden</physicalDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Schaden pro Geschoss"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [6, 6, 6, 6, 6], [0.25, 0.25, 0.25, 0.25, 0.25], [2, 2, 2, 2, 2], [0.35, 0.35, 0.35, 0.35, 0.35], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/55/70/85/100", "6", "0.25", "2", "0.35", "100", "12", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KaisaQ.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaW", "name": "Leerenprojektile", "description": "<PERSON>'Sa feuert ein Ges<PERSON> mit sehr hoher Reichweite ab und markiert Gegner mit ihrem Passiv.<br><br><PERSON><PERSON><PERSON> W<PERSON><PERSON>: „Leerenprojektile“ wird aufgewertet, fügt mehr Steigerungen des Passivs zu und verringert die Abklingzeit bei einem Championtreffer.", "tooltip": "<PERSON>'Sa feuert ein Leerengeschoss ab, das ihr {{ spell.kaisapassive:pduration }}&nbsp;Sekunden lang <keywordStealth>absolute Sicht</keywordStealth> auf den ersten getroffenen Gegner gewährt, {{ e4 }}&nbsp;<keywordMajor>Plasma</keywordMajor>-Steigerungen erzeugt und <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.<br /><br /><keywordMajor>Entwickelt</keywordMajor>: Kai'Sa erzeugt stattdessen {{ e5 }}&nbsp;<keywordMajor>Plasma</keywordMajor>-Steigerungen. Wenn sie einen Champion trifft, verringert sich die Abklingzeit um {{ e3 }}&nbsp;%.<br />Aktuell: <scaleAP>{{ f2.1 }}/{{ e2 }}&nbsp;Fähigkeitsstärke</scaleAP>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Manakosten"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [30, 55, 80, 105, 130], [100, 100, 100, 100, 100], [75, 75, 75, 75, 75], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/55/80/105/130", "100", "75", "2", "3", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "KaisaW.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaE", "name": "Superenergie", "description": "Kai'Sa erhöht kurzzeitig ihr Lauftempo und daraufhin ihr Angriffstempo.<br><br><PERSON><PERSON><PERSON><PERSON>: „Superenergie“ wird aufgewertet und gewährt kurzzeitig Unsichtbarkeit.", "tooltip": "<PERSON>'<PERSON>dt ihre Leerenenergie und erhält <speed>{{ totalmovespeed }}&nbsp;Lauftempo</speed> sowie „<PERSON><PERSON><PERSON>“, während sie auflädt. Im Anschluss erhält sie {{ e2 }}&nbsp;Sekunden lang <attackSpeed>{{ e5 }}&nbsp;Angriffstempo</attackSpeed>.<br /><br />Angriffe verringern die Abklingzeit der Fähigkeit um {{ e4 }}&nbsp;Sekunden.<br /><br /><keywordMajor>Entwickelt</keywordMajor>: <PERSON>'<PERSON> wird außerdem {{ e7 }}&nbsp;Sekunden lang <keywordStealth>unsichtbar</keywordStealth>.<br />Aktuell: <attackSpeed>{{ f10.1 }}&nbsp;%/{{ e6 }}&nbsp;% zusätzliches Angriffstempo</attackSpeed>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Lauftempo", "Angriffstempo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ effect1amount*100.000000 }}&nbsp;% -> {{ effect1amountnl*100.000000 }}&nbsp;%", "{{ effect5amount*100.000000 }}&nbsp;% -> {{ effect5amountnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0.55, 0.6, 0.65, 0.7, 0.75], [4, 4, 4, 4, 4], [1.2, 1.2, 1.2, 1.2, 1.2], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.55/0.6/0.65/0.7/0.75", "4", "1.2", "0.5", "40/50/60/70/80", "100", "0.5", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "KaisaE.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaR", "name": "Killerinstinkt", "description": "<PERSON><PERSON><PERSON> springt in die Nähe eines gegnerischen Champions.", "tooltip": "<PERSON>'<PERSON> springt mit extremer Geschwindigkeit in die Nähe eines gegnerischen Champions, der von <keywordMajor>Plasma</keywordMajor> betroffen ist, und erhält einen Schild, der {{ rshieldduration }}&nbsp;Sekunden lang <shield>{{ rcalculatedshieldvalue }}&nbsp;Schaden</shield> absorbiert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Reichweite", "Abklingzeit", "Schildstärke", "Angriffsschadenskalierung"], "effect": ["{{ rrange }} -> {{ rrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rbasevalue }} -> {{ rbasevalueNL }}", "{{ rtotaladratio*100.000000 }}&nbsp;% -> {{ rtotaladrationl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "KaisaR.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Kai'Sas normale Angriffe erzeugen Steigerungen von „Plasma“ und verursachen zusätzlichen magischen Schaden, der sich stetig erhöht. Bewegungsunfähig machende Effekte von Verbündeten helfen dabei, Steigerungen von „Plasma“ zu erzeugen. Außerdem werden Kai'Sas normale Fähigkeiten zu stärkeren Versionen aufgewertet, wenn sie Gegenstände erwirbt.", "image": {"full": "Kaisa_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}