{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vayne": {"id": "<PERSON><PERSON>", "key": "67", "name": "<PERSON><PERSON>", "title": "die Jägerin der Nacht", "image": {"full": "Vayne.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "67000", "num": 0, "name": "default", "chromas": false}, {"id": "67001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "67002", "num": 2, "name": "Aristokraten-<PERSON><PERSON>", "chromas": false}, {"id": "67003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "67004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "67005", "num": 5, "name": "SKT T1-<PERSON><PERSON>", "chromas": false}, {"id": "67006", "num": 6, "name": "Lichtbogen-Vayne", "chromas": false}, {"id": "67010", "num": 10, "name": "Seelenstehler-Vayne", "chromas": true}, {"id": "67011", "num": 11, "name": "PROJEKT: <PERSON><PERSON>", "chromas": false}, {"id": "67012", "num": 12, "name": "Feuerwerks-Vayne", "chromas": true}, {"id": "67013", "num": 13, "name": "Feuerwerks-Vayne (Prestige)", "chromas": false}, {"id": "67014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "67015", "num": 15, "name": "FPX-Vayne", "chromas": true}, {"id": "67025", "num": 25, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "67032", "num": 32, "name": "Battle Bat Vayne", "chromas": true}, {"id": "67033", "num": 33, "name": "Feuerwerks-Vayne (Prestige 2022)", "chromas": false}, {"id": "67044", "num": 44, "name": "Rächende Dämmerung Vayne", "chromas": true}, {"id": "67055", "num": 55, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "67064", "num": 64, "name": "Emporgestiegene Legende Vayne", "chromas": true}], "lore": "<PERSON><PERSON> ist eine tödliche und skrupellose Monsterjägerin aus Demacia, die ihr Leben der Jagd auf den Dämon verschrieben hat, der ihre Familie ermordete. Wirk<PERSON> glücklich ist Vayne nur, wenn sie voll Rachedurst mit der Armbrust an ihrem Handgelenk die Zauberer und Kreaturen der dunklen Künste mit ihren Silberbolzen aus den Schatten heraus niedermähen kann.", "blurb": "<PERSON><PERSON> ist eine tödliche und skrupellose Monsterjägerin aus Demacia, die ihr Leben der Jagd auf den Dämon verschrieben hat, der ihre Familie ermordete. Wirk<PERSON> glücklich ist Vayne nur, wenn sie voll Rachedurst mit der Armbrust an ihrem Handgelenk...", "allytips": ["„Hechtrolle“ hat verschiedene Verwendungsmöglichkeiten, aber Vayne kann damit keine Mauern überwinden.", "„Verdammen“ kann genutzt werden, um Gegner für einen Kill festzuhalten oder einem Verfolger zu entkommen.", "<PERSON><PERSON><PERSON><PERSON> dich nicht als erstes in einen Teamkampf. Überlasse deinen Teammitgliedern die Eröffnung."], "enemytips": ["Vayne ist recht zerbrechlich und mit entsprechendem Druck muss sie sehr vorsichtig spielen.", "<PERSON>s nicht zu, dass dich Vayne an eine Wand nageln kann."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 1, "magic": 1, "difficulty": 8}, "stats": {"hp": 550, "hpperlevel": 103, "mp": 232, "mpperlevel": 35, "movespeed": 330, "armor": 23, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.35, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "VayneTumble", "name": "Hechtrolle", "description": "Vayne vollführt eine kurze Hechtrolle und bringt sich für ihren nächsten Schuss in Position. Ihr nächster Angriff verursacht zusätzlichen Schaden.", "tooltip": "<PERSON>ayne rollt sich eine kurze Strecke ab und verursacht mit ihrem nächsten Angriff zusätzlich <physicalDamage>{{ adratiobonus }}&nbsp;normalen Schaden</physicalDamage>.<br /><br /><rules>Diese Fähigkeit löst Zaubereffekte aus, wenn sie Schaden verursacht.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "% Skalierung mit Angriffsschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ totaladratio*100.000000 }}&nbsp;% -> {{ totaladrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VayneTumble.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>ayneSil<PERSON><PERSON><PERSON><PERSON>", "name": "Silberbolzen", "description": "Vayne versieht ihre Bolzen mit Spitzen aus seltenem Metall, die das Bösartige vergiften. Der dritte Angriff, ob normaler Angriff oder Fähigkeit, in Folge auf das gleiche Ziel verursacht einen prozentualen Teil des maximalen Lebens des Ziels als absoluten Schaden.", "tooltip": "<spellPassive>Passiv</spellPassive>: <PERSON><PERSON> dritte Ang<PERSON> bzw. Fähigkeit in Folge gegen dense<PERSON>ben <PERSON>, veru<PERSON><PERSON> zusätzlich <trueDamage>absoluten Schaden</trueDamage> in <PERSON><PERSON><PERSON> von {{ totaldamage }} des maximalen Lebens.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% absoluter <PERSON><PERSON><PERSON>", "Mindestschaden"], "effect": ["{{ maxhealthratio*100.000000 }}&nbsp;% -> {{ maxhealthrationl*100.000000 }}&nbsp;%", "{{ damagefloor }} -> {{ damagefloorNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiv", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VayneSilveredBolts.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Passiv"}, {"id": "VayneCondemn", "name": "Verdam<PERSON>", "description": "<PERSON>ayne zieht ihre schwere Armbrust von ihrem Rücken und feuert einen riesigen Bolzen auf ihr Ziel, der es zurückschleudert und Schaden verursacht. Kollidiert es mit der Umgebung, wird es gep<PERSON>, erleidet zusätzlichen Schaden und wird betäubt.", "tooltip": "Vayne feuert einen Bolzen ab, der das Ziel <status>zurück<PERSON><PERSON><PERSON>t</status> und ihm <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zufügt. Falls das Ziel mit Terrain kollidiert, erleidet es zusätzlich <physicalDamage>{{ empowereddamagett }}&nbsp;normalen Schaden</physicalDamage> und wird {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "VayneCondemn.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VayneInquisition", "name": "Letzte Stunde", "description": "Vayne bereitet sich auf eine epische Konfrontation vor. Sie erhält zusätzlichen Angriffsschaden, Unsichtbarkeit während der „Hechtrolle“, verringert die Abklingzeit von „Hechtrolle“ und erhöht das zusätzliches Lauftempo von „Jägerin der Nacht“.", "tooltip": "Vayne erhält {{ baseduration }}&nbsp;Sekunden lang <physicalDamage>{{ bonusattackdamage }}&nbsp;Angriffsschaden</physicalDamage> (die Dauer wird um {{ durationtoadd }}&nbsp;<PERSON>ku<PERSON> verlängert, wenn ein Champion innerhalb von {{ damagedmarkerduration }}&nbsp;<PERSON><PERSON><PERSON> stirbt, nachdem Vayne ihm Schaden zugefügt hat). Zusätzlich gilt während der Wirkdauer:<li><spellName>Jägerin der Nacht</spellName> gewährt stattdessen <speed>{{ movementspeed }}&nbsp;Lauftempo</speed>.<li><spellName>Hechtrolle</spellName> hat eine um {{ tumblecdreduction }}&nbsp;% verringerte Abklingzeit und gewährt {{ tumblestealthduration }}&nbsp;Sekunde(n) lang <keywordStealth>Unsichtbarkeit</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON>", "Zusätzlicher Angriffsschaden", "Abklingzeitverringerung – Hechtrolle"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseduration }} -> {{ basedurationNL }}", "{{ bonusattackdamage }} -> {{ bonusattackdamageNL }}", "{{ tumblecdreduction }}&nbsp;% -> {{ tumblecdreductionNL }}&nbsp;%"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "VayneInquisition.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON> der Nacht", "description": "Vayne jagt unbarmherzig Übeltäter und erhält Lauftempo, wenn sie sich auf gegnerische Champions in der Nähe zubewegt.", "image": {"full": "Vayne_NightHunter.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}