{"type": "language", "version": "15.14.1", "data": {"Back": "<PERSON><PERSON><PERSON>", "Continue": "Folytatás", "Language": "Nyelv", "ItemInfo": "Tárgyin<PERSON>", "NextRank_": "Következő szint:", "Rank_": "Szint:", "PlayingAs": "V<PERSON>lasz<PERSON><PERSON> hő<PERSON>:", "PlayingAgainst": "J<PERSON><PERSON><PERSON> ellenfele:", "CD_": "Töltési idő:", "Range": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Range_": "Hatótáv:", "Details_": "Részletek:", "PrimaryRole": "Szerep", "mobileCompanion": "Kísérő", "mobileForum": "Fórum", "mobileFriends": "Barátok", "mobilePleaseWait": "<PERSON><PERSON> t<PERSON>...", "mobileNews": "<PERSON><PERSON><PERSON>", "modeClassic": "Klasszikus", "modeOdin": "<PERSON><PERSON><PERSON><PERSON><PERSON> sem Dominion", "modeAram": "ARAM", "modeTutorial": "Edzés", "modeOneforall": "FRONTEND_oneforall_game_mode_name", "modeFirstblood": "FRONTEND_firstblood_game_mode_name", "mode6v6": "FRONTEND_6v6_game_mode_name", "modeCof": "FRONTEND_cof_game_mode_name", "Map1": "Idézők szurdoka", "Map8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Map10": "Ka<PERSON><PERSON><PERSON> re<PERSON>", "Map12": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "categoryChampion": "Hősök", "categoryItem": "<PERSON><PERSON><PERSON><PERSON>", "categoryMastery": "Mesterségek", "categoryRune": "<PERSON><PERSON><PERSON>", "categorySummoner": "Idézői varázslatok", "Gold": "<PERSON><PERSON>", "Level": "S<PERSON>t", "Abilities": "Képességek", "ChampionInfo": "Hősinformációk", "Lore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stats": "Statok", "Tips": "Tippek", "statAbility": "Képességek", "statAttack": "Sebzés", "statDefense": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statDifficulty": "Nehézség", "statUtility": "<PERSON><PERSON><PERSON><PERSON>", "Assassin": "Orgyilkos", "Fighter": "<PERSON><PERSON><PERSON>", "Marksman": "Löv<PERSON><PERSON>", "Mage": "M<PERSON><PERSON>", "Support": "T<PERSON>mo<PERSON><PERSON>", "Tank": "Tank", "spells_Self": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_0": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_1": "<PERSON><PERSON><PERSON>", "spells_target_2": "Hatóterület", "spells_target_3": "T<PERSON><PERSON><PERSON><PERSON>r", "spells_target_4": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_5": "V<PERSON>ltoz<PERSON>", "spells_target_6": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_7": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_8": "Vektor <PERSON>", "spells_target_100": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AllItems": "Összes tárgy", "Armor": "<PERSON><PERSON><PERSON><PERSON>", "Attack": "Támadás", "AttackSpeed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Consumable": "Fogyóeszköz", "CooldownReduction": "Töltési idő csökkentés", "CriticalStrike": "<PERSON><PERSON><PERSON><PERSON>", "Damage": "Sebzés", "Defense": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Health": "<PERSON><PERSON><PERSON><PERSON>", "HealthRegen": "Életerő-regenerálódás", "LifeSteal": "Életlopás", "Magic": "Varázslat", "Mana": "<PERSON><PERSON>", "ManaRegen": "Mana-regener<PERSON><PERSON><PERSON><PERSON><PERSON>", "Movement": "Mozgás", "SpellBlock": "Varázsellenállás", "SpellDamage": "Varázserő", "Boots": "Cipők", "NonbootsMovement": "<PERSON><PERSON><PERSON><PERSON>", "Tenacity": "Kitart<PERSON>", "SpellVamp": "Életvar<PERSON><PERSON><PERSON>", "GoldPer": "Aranybevétel", "Slow": "Lassítás", "Aura": "<PERSON>ra", "Active": "Aktív", "MagicPenetration": "Varázstörés", "ArmorPenetration": "Páncéltö<PERSON><PERSON>", "colloq_Armor": ";armour", "colloq_Attack": ";", "colloq_AttackSpeed": ";as;ts;támad<PERSON>i;sebesség;attack;speed;", "colloq_Consumables": ";", "colloq_CriticalStrike": ";", "colloq_Damage": ";seb;ad;sebzés;", "colloq_Defense": ";", "colloq_Health": ";hp;ép;élet;health;életerő;", "colloq_HealthRegen": ";épregen;ép5;hpregen;hp5;élet;regen;", "colloq_LifeSteal": ";lifesteal;életlopás;", "colloq_Magic": ";", "colloq_Mana": ";mp;mana;", "colloq_ManaRegen": ";mpregen;mp5;mana;regen;", "colloq_Movement": ";movespeed;mozgási;sebesség;gyorsaság;gyors", "colloq_SpellBlock": ";mr;ve;var<PERSON><PERSON><PERSON><PERSON>;var<PERSON><PERSON><PERSON>;<PERSON><PERSON>;", "colloq_SpellDamage": ";ap;ve;ability;power;var<PERSON>zserő;", "colloq_Consumable": ";", "colloq_Boots": ";", "colloq_NonbootsMovement": ";", "colloq_CooldownReduction": ";cdr;tics;tölt<PERSON>i;id<PERSON>;csökkentés;cooldown;reduction;", "colloq_Tenacity": ";", "colloq_SpellVamp": ";spellvamp;életvarázs;", "colloq_GoldPer": ";ap10;gp10;arany;gold;", "colloq_Slow": ";", "colloq_Aura": ";", "colloq_Active": ";", "colloq_MagicPenetration": ";", "colloq_ArmorPenetration": ";", "RecommendedItems": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recommended_starting": "Kezdő tárgyak", "recommended_essential": "Alapvető tárgyak", "recommended_offensive": "Támadóeszközök", "recommended_defensive": "V<PERSON>delmi eszközök", "recommended_consumables": "Fogyóeszközök", "Require_": "Előfeltételek:", "Cost_": "Költség:", "OriginalCost_": "Eredeti költség:", "SellsFor_": "Eladási ár: ", "UpgradeCost_": "Fejlesztés költsége:", "Builds_": "Beépül ezekbe:", "ButtonBuy": "VÁSÁRLÁS", "ButtonSell": "ELADÁS", "SpecialRecipeSmall": "*", "SpecialRecipeLarge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FlatArmorMod": "<PERSON><PERSON><PERSON><PERSON>", "FlatAttackSpeedMod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FlatBlockMod": "H<PERSON>rí<PERSON><PERSON>", "FlatCritChanceMod": "Krit. es<PERSON><PERSON>", "FlatCritDamageMod": "Krit. se<PERSON><PERSON>", "FlatEnergyPoolMod": "Energia", "FlatEnergyRegenMod": "Energia-regen / 5", "FlatEXPBonus": "Tapasztalati bónusz", "FlatHPPoolMod": "<PERSON><PERSON><PERSON><PERSON>", "FlatHPRegenMod": "Életerő-regen / 5", "FlatMagicDamageMod": "Varázserő", "FlatMovementSpeedMod": "Mozg<PERSON><PERSON>", "FlatMPPoolMod": "<PERSON><PERSON>", "FlatMPRegenMod": "Mana-regen / 5", "FlatPhysicalDamageMod": "Fizikai sebzés", "FlatSpellBlockMod": "Varázsellenállás", "PercentArmorMod": "Páncél %", "PercentAttackSpeedMod": "Támadási sebesség %", "PercentBlockMod": "Hárítás %", "PercentCritChanceMod": "Krit. esély %", "PercentCritDamageMod": "Krit. sebzés %", "PercentDodgeMod": "Kitérés %", "PercentEXPBonus": "Tapasztalati bónusz %", "PercentHPPoolMod": "Maximális életerő %", "PercentHPRegenMod": "Életerő % / 5", "PercentMagicDamageMod": "Maximális <PERSON>ő %", "PercentMovementSpeedMod": "Mozgási sebesség %", "PercentMPPoolMod": "Maximális mana %", "PercentMPRegenMod": "Mana % / 5", "PercentPhysicalDamageMod": "Fizikai sebzés %", "PercentSpellBlockMod": "Varázsellenállás %", "rFlatArmorModPerLevel": "Páncél a 18. szinten", "rFlatArmorPenetrationMod": "Páncéltö<PERSON><PERSON>", "rFlatArmorPenetrationModPerLevel": "Páncéltörés a 18. szinten", "rFlatCritChanceModPerLevel": "<PERSON><PERSON><PERSON> es<PERSON><PERSON> a 18. szinten", "rFlatCritDamageModPerLevel": "Krit. sebzés a 18. szinten", "rFlatDodgeMod": "<PERSON><PERSON><PERSON><PERSON>", "rFlatDodgeModPerLevel": "Kit<PERSON><PERSON><PERSON> a 18. szinten", "rFlatEnergyModPerLevel": "Energia a 18. szinten", "rFlatEnergyRegenModPerLevel": "Energia-regen / 5 a 18. szinten", "rFlatGoldPer10Mod": "Arany / 10", "rFlatHPModPerLevel": "Életerő a 18. szinten", "rFlatHPRegenModPerLevel": "Életerő-regen / 5 a 18. szinten", "rFlatMagicDamageModPerLevel": "Varázserő a 18. szinten", "rFlatMagicPenetrationMod": "Varázstörés", "rFlatMagicPenetrationModPerLevel": "Varázstörés a 18. szinten", "rFlatMovementSpeedModPerLevel": "Mozgási sebesség a 18. szinten", "rFlatMPModPerLevel": "<PERSON><PERSON> a 18. szinten", "rFlatMPRegenModPerLevel": "Mana-regen / 5 a 18. s<PERSON>ten", "rFlatPhysicalDamageModPerLevel": "Fizikai sebzés a 18. szinten", "rFlatSpellBlockModPerLevel": "Varázsellenállás a 18. szinten", "rFlatTimeDeadMod": "<PERSON><PERSON> t<PERSON>", "rFlatTimeDeadModPerLevel": "<PERSON><PERSON> töltött idő a 18. szinten", "rPercentArmorPenetrationMod": "Páncéltörés %", "rPercentArmorPenetrationModPerLevel": "Páncéltörés % a 18. szinten", "rPercentAttackSpeedModPerLevel": "Támadási sebesség % a 18. szinten", "rPercentCooldownMod": "Töltési idő %", "rPercentCooldownModPerLevel": "Töltési idő % a 18. szinten", "rPercentMagicPenetrationMod": "Varázstörés %", "rPercentMagicPenetrationModPerLevel": "Varázstörés % a 18. szinten", "rPercentMovementSpeedModPerLevel": "Mozgási sebesség % a 18. szinten", "rPercentTimeDeadMod": "Holtan töltött idő %", "rPercentTimeDeadModPerLevel": "Holtan töltött idő %/szint", "PercentLifeStealMod": "Életlopási bónusz %", "PercentSpellVampMod": "Életvarázs-bónusz %", "masteryFerocity": "Kegyetlenség", "masteryCunning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "masteryResolve": "Elszántság", "native_﻿ar": "العربية", "native_bg": "български език", "native_cs": "čeština", "native_de": "De<PERSON>ch", "native_el": "ελληνικά", "native_en": "English", "native_es": "español", "native_fr": "français", "native_hu": "magyar", "native_id": "Bahasa Indonesia", "native_it": "Italiano", "native_ja": "日本語", "native_ko": "조선말", "native_nl": "Nederlands", "native_pl": "język polski", "native_pt": "português", "native_ro": "română", "native_ru": "русский язык", "native_th": "ภาษาไทย", "native_tr": "Türkçe", "native_vi": "Tiếng <PERSON>", "native_zh": "汉语", "native_zh_CN": "简体字", "native_zh_MY": "简体字", "native_zh_TW": "繁體字"}, "tree": {"searchKeyIgnore": "", "searchKeyRemap": []}}