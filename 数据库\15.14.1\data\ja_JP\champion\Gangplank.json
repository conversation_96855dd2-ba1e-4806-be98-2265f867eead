{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gangplank": {"id": "Gangplank", "key": "41", "name": "ガングプランク", "title": "大海原の大災厄", "image": {"full": "Gangplank.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "41000", "num": 0, "name": "default", "chromas": false}, {"id": "41001", "num": 1, "name": "幽霊船長ガングプランク", "chromas": false}, {"id": "41002", "num": 2, "name": "民兵ガングプランク", "chromas": false}, {"id": "41003", "num": 3, "name": "水兵ガングプランク", "chromas": false}, {"id": "41004", "num": 4, "name": "おもちゃの兵隊ガングプランク", "chromas": false}, {"id": "41005", "num": 5, "name": "特殊部隊兵ガングプランク", "chromas": false}, {"id": "41006", "num": 6, "name": "絶対君主ガングプランク", "chromas": false}, {"id": "41007", "num": 7, "name": "船長ガングプランク", "chromas": false}, {"id": "41008", "num": 8, "name": "ドレッドノヴァ ガングプランク", "chromas": true}, {"id": "41014", "num": 14, "name": "プールパーティ ガングプランク", "chromas": true}, {"id": "41021", "num": 21, "name": "FPX ガングプランク", "chromas": true}, {"id": "41023", "num": 23, "name": "裏切りのガングプランク", "chromas": true}, {"id": "41033", "num": 33, "name": "PROJECT: Gangplank", "chromas": true}], "lore": "気まぐれにして残忍、「略奪の王」を名乗るもその玉座を追われたガングプランクの名は、七つの海に轟き恐れられていた。かつてビルジウォーターの港町を牛耳っていた彼だが、その座を奪われた今、逆に彼はさらに危険な存在になったと考える者もいる。誰かにこの街を奪われるくらいなら、ガングプランクは再びビルジウォーターを血の海にしてやるだろう。ピストル、カトラス、火薬の樽を携え、彼は何としてでも奪われたものを取り返すつもりだ。", "blurb": "気まぐれにして残忍、「略奪の王」を名乗るもその玉座を追われたガングプランクの名は、七つの海に轟き恐れられていた。かつてビルジウォーターの港町を牛耳っていた彼だが、その座を奪われた今、逆に彼はさらに危険な存在になったと考える者もいる。誰かにこの街を奪われるくらいなら、ガングプランクは再びビルジウォーターを血の海にしてやるだろう。ピストル、カトラス、火薬の樽を携え、彼は何としてでも奪われたものを取り返すつもりだ。", "allytips": ["「偽りの発砲」には「フローズンマレット」や「ブラック クリーバー」の通常攻撃時効果が付与される。", "体力の低い敵チャンピオンがいたら、マップのどこからでも「一斉砲撃」を放ち、止めを刺そう。", "「一斉砲撃」は敵の退路をふさぐように使うと効果的だ。"], "enemytips": ["「偽りの発砲」は高い物理ダメージを与えるため、対ガングプランクでは物理防御を増加させるアイテムが効果的だ。", "ガングプランクがレベル6に達したら、アルティメットスキル「一斉砲撃」に注意したい。"], "tags": ["Fighter"], "partype": "マナ", "info": {"attack": 7, "defense": 6, "magic": 4, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 280, "mpperlevel": 60, "movespeed": 345, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 4.2, "attackspeedperlevel": 3.2, "attackspeed": 0.658}, "spells": [{"id": "GangplankQWrapper", "name": "偽りの発砲", "description": "対象を撃ち抜き、そのユニットを倒すことでゴールドを奪うことができる。", "tooltip": "{{Spell_GangplankQWrapper_Tooltip_{{ gamemodeinteger }}}}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "追加ゴールド", "追加シルバーサーペント", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e5 }} -> {{ e5NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4.5, 4.5, 4.5, 4.5, 4.5], "cooldownBurn": "4.5", "cost": [50, 45, 40, 35, 30], "costBurn": "50/45/40/35/30", "datavalues": {}, "effect": [null, [10, 40, 70, 100, 130], [3, 4, 5, 6, 7], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 5, 6, 7, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/40/70/100/130", "3/4/5/6/7", "0", "500", "4/5/6/7/8", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "GangplankQWrapper.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GangplankW", "name": "壊血病治癒", "description": "オレンジをかじり、自身が受けている行動妨害効果を解消し、体力を回復する。", "tooltip": "オレンジを大量摂取して、<status>行動妨害効果</status>をすべて解除し、<healing>{{ basehealth }}(+減少体力の{{ e2 }}%)</healing>を回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [45, 70, 95, 120, 145], [13, 13, 13, 13, 13], [0.25, 0.25, 0.25, 0.25, 0.25], [200, 200, 200, 200, 200], [30, 40, 50, 60, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/70/95/120/145", "13", "0.25", "200", "30/40/50/60/70", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GangplankW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GangplankE", "name": "火薬樽", "description": "指定地点に火薬樽を設置する。火薬樽はガングプランクが攻撃すると爆発し、範囲内の敵にその攻撃と同じダメージを与え、スロウ効果を付与する。", "tooltip": "自身および敵チャンピオンが通常攻撃可能な「火薬樽」を{{ e5 }}秒間設置する。敵が破壊した場合、樽はそのまま消滅する。自身が破壊した場合、樽は爆発して{{ e2 }}秒間{{ finalslowamount }}%の<status>スロウ効果</status>を付与し、物理防御の{{ e0 }}%を無視して<physicalDamage>通常攻撃のダメージ</physicalDamage>を与える。チャンピオンに対しては、追加で<physicalDamage>{{ e3 }}の物理ダメージ</physicalDamage>を与える。<br /><br />樽の体力は{{ f5 }}秒ごとに減少していく。爆発した樽と爆発範囲が重なっている樽は誘爆するが、同じ対象には1回しかダメージを与えない。<spellName>「偽りの発砲」</spellName>による樽の爆発で対象を倒した場合、追加ゴールドを獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["チャンピオンへの追加ダメージ", "最大チャージ数", "スロウ効果", "リチャージ時間"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ barrelslow }}% -> {{ barrelslowNL }}%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [3, 3, 4, 4, 5], [2, 2, 2, 2, 2], [75, 105, 135, 165, 195], [40, 50, 60, 70, 80], [25, 25, 25, 25, 25], [2, 2, 2, 2, 2], [10, 10, 10, 10, 10], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 40, 40, 40, 40]], "effectBurn": [null, "3/3/4/4/5", "2", "75/105/135/165/195", "40/50/60/70/80", "25", "2", "10", "100", "0.5", "40"], "vars": [], "costType": "コスト無し", "maxammo": "3", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GangplankE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "GangplankR", "name": "一斉砲撃", "description": "海賊船に合図を送って指定したエリアを砲撃させ、効果範囲内の敵にスロウ効果とダメージを与える。", "tooltip": "海賊船に合図を送り、{{ zoneduration }}秒かけてマップ上の任意の場所を{{ totalwavestooltip }}回砲撃させる。それぞれの砲撃で{{ slowduration }}秒間{{ slowpercent }}%の<status>スロウ効果</status>と<magicDamage>{{ onewavedamage }}の魔法ダメージ</magicDamage>を与える。最大ダメージ: {{ totaldamagetooltip }}<br /><br />このスキルは<spellName>「偽りの発砲」</spellName>で集めたシルバーサーペントを使ってショップでアップグレードできる。<br /><br /><spellName>乱れ撃ち</spellName>: 砲撃回数が6回増加する。<br /><spellName>死の女神</spellName>: 巨大な砲弾を放ち、<trueDamage>{{ deathsdaughterdamage }}の確定ダメージ</trueDamage>を与え、{{ deathsdaughterslowduration }}秒間、{{ deathsdaughterslow }}%の<status>スロウ効果</status>を付与する。<br /><spellName>士気上昇</spellName>: {{ raisemoralehasteduration }}秒間、「一斉砲撃」の範囲内の味方の<speed>移動速度が{{ raisemoralehaste }}%</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["砲撃毎のダメージ", "クールダウン"], "effect": ["{{ damageperwave }} -> {{ damageperwaveNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1.5, 0.5, 0.5], [0, 0, 0], [300, 300, 300], [75, 75, 75], [1, 1, 1], [40, 40, 40]], "effectBurn": [null, "0", "0", "0", "0", "1.5/0.5/0.5", "0", "300", "75", "1", "40"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [30000, 30000, 30000], "rangeBurn": "30000", "image": {"full": "GangplankR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "背水の銃剣", "description": "数秒毎に近接攻撃で敵に火を点ける。", "image": {"full": "Gangplank_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}