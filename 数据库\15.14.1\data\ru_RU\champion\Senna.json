{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Senna": {"id": "<PERSON><PERSON>", "key": "235", "name": "Сенна", "title": "Освободительница", "image": {"full": "Senna.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "235000", "num": 0, "name": "default", "chromas": false}, {"id": "235001", "num": 1, "name": "Сенна из True Damage", "chromas": true}, {"id": "235009", "num": 9, "name": "Сенна из True Damage (престижный)", "chromas": false}, {"id": "235010", "num": 10, "name": "Ковбой Сенна", "chromas": true}, {"id": "235016", "num": 16, "name": "ПРОЕКТ: Сенна", "chromas": true}, {"id": "235026", "num": 26, "name": "Сенна Лунное Затмение", "chromas": true}, {"id": "235027", "num": 27, "name": "Сенна Лунное Затмение (престижный)", "chromas": false}, {"id": "235036", "num": 36, "name": "Ведьма Сенна", "chromas": true}, {"id": "235046", "num": 46, "name": "Звездная защитница Сенна", "chromas": true}, {"id": "235056", "num": 56, "name": "Избранница зимы Сенна", "chromas": false}, {"id": "235063", "num": 63, "name": "Героиня в маске Сенна", "chromas": false}], "lore": "С детства обреченная спасаться от преследующего ее черного тумана, Сенна вступила в орден Стражей света. Она яростно сражалась со своим проклятием, но погибла, а ее душу поймал в свой фонарь жестокий призрак Треш. Однако Сенна не утратила надежды и внутри фонаря научилась управлять туманом. Она сумела вернуться в мир живых, необратимо изменившись. Ее оружием стали одновременно и тьма, и свет. Сенна обращает черный туман против самого себя и каждым выстрелом из каменного ружья освобождает заключенные в нем души.", "blurb": "С детства обреченная спасаться от преследующего ее черного тумана, Сенна вступила в орден Стражей света. Она яростно сражалась со своим проклятием, но погибла, а ее душу поймал в свой фонарь жестокий призрак Треш. Однако Сенна не утратила надежды и...", "allytips": [], "enemytips": [], "tags": ["Support", "Marksman"], "partype": "Мана", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 530, "hpperlevel": 89, "mp": 350, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 0, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SennaQ", "name": "Пронзающая тьма", "description": "Сенна стреляет из двух стволов своего каменного ружья, пропуская сквозь цель луч из света и тени. При этом она лечит союзников и наносит урон врагам.", "tooltip": "Сенна выпускает заряд пронзающей тени сквозь союзника или врага, нанося пораженным противникам <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> и <status>замедляя</status> их на {{ totalslow }} на {{ slowduration }} сек. Задетые союзные чемпионы восстанавливают <healing>{{ totalheal }} здоровья</healing>. <br /><br />Автоатаки сокращают перезарядку этого умения на {{ cdreductiononhit }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Лечение", "Продолжительность замедления", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SennaQ.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SennaW", "name": "Прощальные объятия", "description": "Сенна выпускает вперед волну черного тумана. При поражении врага туман жадно вцепляется в него, спустя короткое время обездвиживая основную цель и всех вокруг нее.", "tooltip": "Сенна выпускает вперед черный туман, который наносит <physicalDamage>{{ damage }} физического урона</physicalDamage> первому пораженному врагу. Спустя {{ delaytime }} сек. эта цель и другие враги поблизости оказываются <status>обездвижены</status> на {{ rootduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Продолжительность обездвиживания", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "SennaW.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SennaE", "name": "Проклятие черного тумана", "description": "Сенна создает вокруг себя бурю из тумана, который она накопила в своем оружии, открывает себя тьме и становится призраком. Союзники, вошедшие в область действия умения, получают маскировку, а также становятся призраками, когда их укрывает туман. Скорость передвижения призраков увеличена. Они невыбираемы и скрывают свою личность от врагов.", "tooltip": "Сенна растворяется в облаке черного тумана на {{ buffduration }} сек., превращаясь в призрака. Союзные чемпионы, вошедшие в туман, получают <keywordStealth>маскировку</keywordStealth>, а после выхода тоже превращаются в призраков. Призраки получают <speed>{{ totalms }} скорости передвижения</speed> и становятся невыбираемыми. Вражеские чемпионы не видят, кем является призрак, пока не приблизятся.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Продолжительность", "Перезарядка"], "effect": ["{{ buffduration }} -> {{ buffdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24.5, 23, 21.5, 20], "cooldownBurn": "26/24.5/23/21.5/20", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SennaE.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SennaR", "name": "Тенесвет", "description": "Сенна призывает древние камни павших Стражей, расщепляя свое ружье на святые частицы тени и света. После этого она выстреливает лучом через всю карту, накладывая на союзников щит, а также нанося урон врагам в центре.", "tooltip": "Сенна выпускает луч света, который наносит <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> всем пораженным врагам. Союзные чемпионы в более широкой области получают <shield>щит прочностью {{ totalshield }}</shield> на {{ shieldduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Прочность щита", "Перезарядка"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ shield }} -> {{ shieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SennaR.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Освобождение", "description": "Когда около Сенны погибают бойцы, их душами периодически завладевает черный туман. Сенна может атаковать такие души, чтобы освободить их, при этом поглощая туман, который удерживает их в царстве мертвых. Туман усиливает каменное ружье Сенны, увеличивая ее силу атаки, дальность атаки и шанс критического удара. <br><br>Каменное ружье Сенны стреляет с задержкой, но наносит дополнительный урон и ненадолго увеличивает ее скорость передвижения в размере части от скорости передвижения цели.", "image": {"full": "Senna_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}