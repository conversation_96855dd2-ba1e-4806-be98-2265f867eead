{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "<PERSON><PERSON><PERSON>", "title": "the Void Walker", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "Festival Kassadin", "chromas": false}, {"id": "38002", "num": 2, "name": "Deep One Kassadin", "chromas": false}, {"id": "38003", "num": 3, "name": "Pre-Void Ka<PERSON>din", "chromas": false}, {"id": "38004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38005", "num": 5, "name": "Cosmic Reaver <PERSON>", "chromas": false}, {"id": "38006", "num": 6, "name": "Count <PERSON>", "chromas": true}, {"id": "38014", "num": 14, "name": "Hextech <PERSON>", "chromas": false}, {"id": "38015", "num": 15, "name": "Shockblade <PERSON>", "chromas": false}, {"id": "38024", "num": 24, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "<PERSON><PERSON>h lolos dari tempat tergelap di dunia, <PERSON><PERSON><PERSON> tahu bahwa waktunya tidak akan lama lagi. Dia adalah petualang dan pemandu Shuriman yang terkenal. Dia memilih untuk membina keluarga di tengah-tengah suku pengelana Shurima yang damai di Selatan. Sampai suatu hari desanya direnggut Void. Dia bersumpah akan membalas dendam, lalu menggabungkan berbagai artefak arcane serta teknologi terlarang untuk berjuang. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> menu<PERSON> lahan tandus <PERSON>, dan bersiap menghadapi ciptaan Void yang mengerikan dalam mencari sosok yang mengaku nabi, yaitu <PERSON>.", "blurb": "<PERSON><PERSON><PERSON> lolos dari tempat tergelap di dunia, <PERSON><PERSON><PERSON> tahu bahwa waktunya tidak akan lama lagi. Dia adalah petualang dan pemandu <PERSON>riman yang terkenal. Dia memilih untuk membina keluarga di tengah-tengah suku pengelana Shurima yang damai di Selatan...", "allytips": ["<PERSON><PERSON><PERSON> memilliki banyak pilihan build item; dia bisa menjadi caster dengan Mana dan Ability Power, atau anti-caster dengan Cooldown Reduction dan Magic Resist.", "<PERSON><PERSON><PERSON> memiliki banyak kegunaan dan cooldown-nya lebih singkat dari kebanyakan ability, jadi manfaatkan sesering mungkin.", "Usahakan mengambil buff Ancient Golem untuk mengimbangi biaya Mana Riftwalk yang terus naik."], "enemytips": ["<PERSON><PERSON><PERSON>a memberikan magic damage. <PERSON><PERSON> kond<PERSON> bagus, pertimbangkan untuk membeli item magic resist seperti Mercury's Treads da<PERSON>'s <PERSON><PERSON>.", "<PERSON>ia<PERSON> kali <PERSON> terus menggunakan Riftwalk, dia akan terus menggunakan lebih banyak Mana. Ingat ini saat kamu mengejarnya.", "Butuh 6 cast spell sampai skill Slow-nya, Force Pulse, untuk bisa di-cast. Jika dia menaikkan level Ability itu, pakai skill-mu secara bijak saat berada di lane melawan dia."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "Null Sphere", "description": "<PERSON><PERSON><PERSON> men<PERSON>kkan orb energi void ke target, men<PERSON><PERSON><PERSON><PERSON> damage dan mengganggu channel. Energi berlebih terbentuk di sekelilingnya, memberikan shield sementara yang menyerap magic damage.", "tooltip": "<PERSON><PERSON><PERSON> menembakkan orb energi void ke target, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> dan mengganggu channel. Ka<PERSON>din juga mendapatkan <shield>{{ totalshield }} Magic Shield</shield> selama 1,5 detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Jumlah Shield", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ e3 }}-> {{ e3NL }}", "{{ cost }}-> {{ costNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>herBlade", "name": "Nether Blade", "description": "Pasif: Basic attack <PERSON><PERSON><PERSON> menghasilkan magic damage bonus. Aktif: Basic attack Kassadin berikutnya menghasilkan magic damage bonus yang signifikan dan memu<PERSON><PERSON><PERSON>.", "tooltip": "<spellPassive>Pasif:</spellPassive> <PERSON><PERSON><PERSON> men<PERSON> tambahan <magicDamage>{{ onhitdamage }} magic damage</magicDamage>.<br /><br /><spellActive>Aktif:</spellActive> <PERSON><PERSON><PERSON> melakukan charge pada pedangnya, menyebabkan Serangan berikut<PERSON> menghasilkan <magicDamage>{{ activedamage }} magic damage</magicDamage> dan memulihkan <scaleMana>{{ e1 }}% Mana yang hilang</scaleMana>, meningkat hingga <scaleMana>{{ e4 }}%</scaleMana> terhadap champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Aktif", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ e3 }}-> {{ e3NL }}", "{{ e1 }}%-> {{ e1NL }}%", "{{ e4 }}%-> {{ e4NL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ForcePulse", "name": "Force Pulse", "description": "<PERSON><PERSON><PERSON> menarik energi dari spell cast di sekitarnya. Saat melakukan charge, Ka<PERSON>din bisa menggunakan Force Pulse untuk menghasilkan damage dan menerapkan slow pada musuh dalam kerucut di depannya.", "tooltip": "<spellPassive>Pasif:</spellPassive> Cooldown <spellName>Force Pulse</spellName> dikurangi sebesar {{ reductionperspellcast }} detik tiap kali Ability apa pun digunakan di dekat <PERSON>.<br /><br /><spellActive>Aktif:</spellActive> Ka<PERSON>din melepaskan void pulse, men<PERSON><PERSON><PERSON>an <magicDamage>{{ totaldamage }} magic damage</magicDamage> dan menerapkan <status>Slow</status> sebesar {{ e2 }}% selama {{ e3 }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ e2 }}%-> {{ e2NL }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RiftWalk", "name": "Riftwalk", "description": "<PERSON><PERSON><PERSON> berte<PERSON>portasi ke lokasi jarak dekat, men<PERSON><PERSON><PERSON><PERSON> damage pada unit musuh di sekitar. Beberapa Riftwalk dalam waktu singkat membutuhkan <PERSON>a tambahan, tetapi juga mengh<PERSON>lkan damage bonus.", "tooltip": "<PERSON><PERSON><PERSON> berteleportasi ke lokasi jarak dekat, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ basedamage }} magic damage</magicDamage>.<br /><br />Tiap penggunaan berikutnya dari Ability ini dalam {{ rstackduration }} detik berikutnya, menggandakan biaya Mana dan mengh<PERSON>lkan <magicDamage>{{ bonusdamage }} magic damage</magicDamage> tambahan. Kenaikan biaya dan damage bisa stack sampai {{ maxstacks }} kali.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage Per <PERSON>", "Cooldown"], "effect": ["{{ rbasedamage }}-> {{ rbasedamageNL }}", "{{ stackdamage }}-> {{ stackdamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Void Stone", "description": "<PERSON><PERSON><PERSON> menerima lebih sedikit magic damage dan mengabaikan tabrakan unit.", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}