{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "<PERSON><PERSON><PERSON>", "title": "Zaman Bekçisi", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "26002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "26003", "num": 3, "name": "<PERSON><PERSON> Çöll<PERSON>", "chromas": false}, {"id": "26004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "26005", "num": 5, "name": "Kanlı Ay Zilean", "chromas": false}, {"id": "26006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "26014", "num": 14, "name": "Kışın Seçilmişi Zilean", "chromas": true}], "lore": "Bir zamanlar güçlü bir Icathia büyücüsü o<PERSON>, vatanının Hiçlik tarafından yok edilmesine tanık olmasının ardından zamanın geçişini takıntı haline getirdi. Bu muazzam kayba üzülmek için bir an bile bulamayan büyücü, bütün olası sonuçları öğrenmek için kadim zaman büyülerine başvurdu. Teknik olarak ölümsüz hale gelen Zilean, artık geçmiş, şimdiki zaman ve gelecek arasında gidip gelerek etrafında akan zamanı eğip büküyor ve Icathia'nın yıkımını tersine çevirecek o yakalanması zor anı bulmaya çalışıyor.", "blurb": "Bir zamanlar güçlü bir Icathia büyücüsü <PERSON>, vatanının Hiçlik tarafından yok edilmesine tanık olmasının ardından zamanın geçişini takıntı haline getirdi. Bu muazzam kayba üzülmek için bir an bile bulamayan büyücü, bütün olası sonuçları öğrenmek...", "allytips": ["Saatli Bomba ve Başa Sar'ı birleştirerek iki Saatli Bomba'yı bir hedefe çabucak yerleştirebilirsin. İkinci bombayı yerleştirmek ilkini patlatır ve yakındaki tüm rakipleri sersemletir.", "Zamanı Bük, takım arkadaşlarının rakipleri katletmesine yardımcı olabilir ya da kaybedilen bir savaştan kaçmaya yarayabilir.", "Zaman Kayması takımının taşıyıcılarına saldıranları caydırmak için güçlü bir araçtır ama Zaman Kayması'nı çatışmada çok erken kullanmak rakibin erkenden hedef değiştirmesine ve yeteneğin etkisinin azalmasına neden olur."], "enemytips": ["Zilean'ın hızını yakalayabilirsen, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> darbeyi vurmadan önce ultisinin bitmesini beklemek bazen yararına olabilir.", "Takım ona odaklanırsa Zilean zayıf bir şampiyondur; ama aksi halde öldürmesi çok zordur. Onu takım olarak öldürmeye çalışın."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "Saatli Bomba", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>ye doğru fırlattığı bomba, yakınından geçen birimlere yapışır (Şampiyonlara öncelik verir). Bomba 3 saniye sonra patlayarak alan etkili hasar verir. Yerleştirilen Saatli Bomba, başka bir Saatli Bomba tarafından erken patlatılırsa, ek olarak rakipleri sersemletir.", "tooltip": "Zilean çevresindeki küçük alana giren ilk birime yapışan bir saatli bomba fırlatır. Bomba {{ e2 }} saniyenin ardından patlayarak <magicDamage>{{ totaldamage }} Büyü Hasarı</magicDamage> verir.<br /><br />Üstünde bomba olan bir birime ikinci bir bomba fırlatmak, ilk bombayı anında patlatır ve patlamanın içinde kalan rakipleri {{ e4 }} saniyeliğine <status>sersemletir</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Bedeli", "<PERSON><PERSON>", "Sersemletme <PERSON>i:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZileanW", "name": "Başa Sar", "description": "Zilean diğer temel yeteneklerinin bekleme sürelerini azaltarak kendini önündeki çatışmalara hazırlayabilir.", "tooltip": "<PERSON><PERSON>an zamanı geri sararak diğer temel yeteneklerinin bekleme sürelerini {{ e2 }} saniye azaltır.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }} <PERSON>a"}, {"id": "TimeWarp", "name": "Zamanı Bük", "description": "<PERSON><PERSON>an herhangi bir birimin etrafında zamanı bükerek kısa süreliğine rakiplerin hareket hızını azaltır veya takım arkadaşlarının hareket hızını arttırır.", "tooltip": "Zilean {{ e1 }} saniyeliğine bir rakip <PERSON>u %{{ e2 }} <status>yavaşlatır</status> veya bir takım arkadaşına <speed>%{{ e2 }} Hareket Hızı</speed> kazandırır.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Hareket Hızı"], "effect": ["%{{ e2 }} -> %{{ e2NL }}", "%{{ e2 }} -> %{{ e2NL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ChronoShift", "name": "<PERSON><PERSON>", "description": "<PERSON>ilean dost bir şampiyonun üzerine koruyucu bir zaman rünü işler ve ölümcül hasar alması halinde, zamanda geri dö<PERSON><PERSON><PERSON>.", "tooltip": "<PERSON>ilean bir takım arkadaşına {{ rduration }} saniyeliğine koruyucu bir zaman rünü verir. <PERSON><PERSON><PERSON> katledilirse, rün zamanı geri sararken ona {{ revivestateduration }} saniyeliğine zamanı durdurma etkisi uygular. Daha sonra bu takım arkadaşı dirilir ve <healing>{{ rtotalheal }} Can</healing> yeniler.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Bedeli", "İyileştirme"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Zaman <PERSON>ı", "description": "<PERSON><PERSON>an zamanı deneyim olarak biriktirir ve onu takım arkadaşlarına aktarabilir. Takım arkadaşlarından birinin seviyesini tamamlamaya yetecek kadar deneyim biriktirdiğinde, takım arkadaşına sağ tıklayarak bunu aktarabilir. <PERSON><PERSON>an da aynı miktarda deneyim kazanır.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}