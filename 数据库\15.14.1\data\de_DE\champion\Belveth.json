{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Belveth": {"id": "Belveth", "key": "200", "name": "Bel'Veth", "title": "die Kaiserin der Leere", "image": {"full": "Belveth.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "200000", "num": 0, "name": "default", "chromas": false}, {"id": "200001", "num": 1, "name": "Schlachtboss-Bel'Veth", "chromas": true}, {"id": "200010", "num": 10, "name": "Kosmische Matriarchin Bel'Veth", "chromas": true}, {"id": "200019", "num": 19, "name": "Primordianische Bel'Veth", "chromas": true}], "lore": "Bel'Veth ist eine albtraumhafte Kaiserin, die aus dem Rohmaterial einer komplett verschlungenen Stadt erschaffen wurde. Sie ist das Ende von Runeterra … und der Anfang einer ungeheuerlichen Realität nach ihren Vorstellungen. Angetrieben durch Zeitalter umfunktionierter Geschichte, Wissen und Erinnerungen aus der oberen Welt ernährt sie ein immer größer werdendes Verlangen nach neuen Erfahrungen und Emotionen und verschlingt dabei alles, was ihren Weg kreuzt. <PERSON><PERSON> kann ihre Begierde niemals von nur einer Welt gesättigt werden und so wendet sie ihren hungrigen Blick den alten Meistern der Leere zu …", "blurb": "Bel'Veth ist eine albtraumhafte Kaiserin, die aus dem Rohmaterial einer komplett verschlungenen Stadt erschaffen wurde. Sie ist das Ende von Runeterra … und der Anfang einer ungeheuerlichen Realität nach ihren Vorstellungen. Angetrieben durch Zeitalter...", "allytips": [], "enemytips": [], "tags": ["Fighter"], "partype": "", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 60, "mpperlevel": 0, "movespeed": 340, "armor": 32, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 1.5, "attackspeedperlevel": 0, "attackspeed": 0.85}, "spells": [{"id": "BelvethQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Bel'Veth bewegt sich schnell in eine gewählte Richtung und fügt allen getroffenen Gegnern auf ihrem Weg Schaden zu.", "tooltip": "Bel'Veth bewegt sich schnell nach vorn und verursacht <physicalDamage>{{ basedamage }}&nbsp;normalen Schaden</physicalDamage> an <PERSON><PERSON><PERSON><PERSON>, die sie dabei trifft.<br /><br /><PERSON><PERSON> Richtung hat eine einzigartige Abklingzeit von {{ f1 }}&nbsp;Sekunden (skaliert abhängig von ihrem <attackSpeed>Angriffstempo</attackSpeed> nach unten).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit pro Richtung", "<PERSON><PERSON><PERSON> an <PERSON>", "Vasallenschaden"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ persidecooldown }} -> {{ persidecooldownNL }}", "{{ monstermod }} -> {{ monstermodNL }}", "{{ minonmod*100.000000 }}&nbsp;% -> {{ minonmodnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethQ.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "BelvethW", "name": "Diesseits und Jenseits", "description": "Bel'<PERSON><PERSON> schlägt mit ihrem Schwanz auf den Boden, wodurch Gegner Schaden erleiden sowie hochgeschleudert und verlangsamt werden.", "tooltip": "Bel'Veth schlägt mit ihrem <PERSON>, wodurch sie <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht und Gegner {{ duration }}&nbsp;Sekunden lang <status>hochschleudert</status> und {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercent*100 }}&nbsp;% <status>verlangsamt</status>. Wenn ein Champion getroffen wird, wird die Abklingzeit von <spellName>Leerenvorstoß</spellName> in diese Richtung zurückgesetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uer", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [715, 715, 715, 715, 715], "rangeBurn": "715", "image": {"full": "BelvethW.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "BelvethE", "name": "Kaiser<PERSON><PERSON>strom", "description": "Bel'Veth kanalisiert an ihrer Position einen aggressiven Sturm um sich herum, der den Gegner mit dem niedrigsten Leben anvisiert und ihr Lebensraub und Schadensverringerung gewährt.", "tooltip": "Bel'Veth kanalisiert und schlägt um sich, erhält {{ drpercent*100 }}&nbsp;% Schadensverringerung, {{ totallifesteal }}&nbsp;Lebensraub und greift über {{ totalduration }}&nbsp;Sekunden hinweg {{ f2.0 }}-mal an (Anzahl der Angriffe erhöht sich mit dem <attackSpeed>Angriffstempo</attackSpeed>). Jeder Angriff trifft den Gegner mit dem niedrigsten Leben und verursacht <physicalDamage>{{ damageperstrike }}</physicalDamage> bis <physicalDamage>{{ maxdamageperstriketooltip }}&nbsp;normalen Schaden</physicalDamage> abhängig vom fehlenden Leben des Ziels.<br /><br />Der Einsatz einer anderen Fähigkeit oder eine <recast>Reaktivierung</recast> beendet diese Fähigkeit frühzeitig.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schadensverringerung", "Abklingzeit"], "effect": ["{{ damageperhit }} -> {{ damageperhitNL }}", "{{ drpercent*100.000000 }}&nbsp;% -> {{ drpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "BelvethE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "BelvethR", "name": "Endloses Fest<PERSON><PERSON>", "description": "Bel'Veth absorbiert Reste von Leerenkorallen und verwandelt sich in ihre wahre Gestalt. <PERSON><PERSON>ch erhöhen sich ihr maximales Leben, ihre Angriffsreichweite, ihr Angriffstempo und ihr Lauftempo außerhalb des Kampfes. Das Absorbieren der Leerenkoralle eines epischen Leerenmonsters verlängert die Dauer ihrer ultimativen Fähigkeit und verleiht ihr die Macht, einen Schwarm aus Leerenfischen zu beschwören.", "tooltip": "<spellPassive>Passiv:</spellPassive> Jeder zweite Angriff gegen das gleiche Ziel verursacht zusätzlich <trueDamage>{{ finalonhitdamage }}&nbsp;absoluten Schaden</trueDamage>, der unbegrenzt steigerbar ist. Kills/Unterstützungen gegen Champions und epische Monster hinterlassen Überreste einer Leerenkoralle.<br /><br /><spellActive>Aktiv:</spellActive> Der Verbrauch der Leerenkoralle gewährt <keywordMajor>{{ passivestacksondevour }}&nbsp;violette</keywordMajor> Steigerung und aktiviert {{ steroidduration }}&nbsp;Sekunden lang Bel'Veths wahre Gestalt. Leerenkorallen von epischen Monstern aus der Leere verlängern die Dauer auf {{ voidduration }}&nbsp;Sekunden und lassen Vasallen, die in der Nähe sterben, zu Leerenfischen werden. Während der Ausführung <status>verlangsamt</status> Bel'Veth nahe <PERSON>. Dann explodiert sie und verursacht <trueDamage>absoluten Schaden</trueDamage> in Höhe von {{ totalexplosiondamage }} + {{ missinghealthdamage*100 }}&nbsp;% des fehlenden Lebens.<br /><br />In ihrer wahren Gestalt erhält Bel'Veth <healing>{{ maxhealthondevour }}&nbsp;maximales Leben</healing>, <speed>{{ oocms }}&nbsp;Lauftempo</speed> außerhalb des Kampfes, {{ bonusaarange }}&nbsp;Angriffsreichweite, sowie <attackSpeed>{{ totalasmod*100 }}&nbsp;% Gesamtangriffstempo</attackSpeed> und <spellName>Leerenvorstoß</spellName> kann Mauern überwinden.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Trefferschaden", "Explosionsschaden", "Zusätzliches Leben", "Lauftempo", "Angriffstempo", "Leerenfische – Leben"], "effect": ["{{ onhitdamage }} -> {{ onhitdamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealth }} -> {{ basemaxhealthNL }}", "{{ oocms }} -> {{ oocmsNL }}", "{{ totalasmod*100.000000 }}&nbsp;% -> {{ totalasmodnl*100.000000 }}&nbsp;%", "{{ voidlinghpscale*100.000000 }}&nbsp;% -> {{ voidlinghpscalenl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Tod in Violett ", "description": "Bel'Veth erhält dauerhafte Angriffstempo-Steigerungen, nachdem sie große Vasallen, Monster und Champions besiegt hat. Außerdem erhält sie vorübergehend zusätzliches Angriffstempo, nachdem sie eine Fähigkeit einsetzt.", "image": {"full": "Belveth_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}