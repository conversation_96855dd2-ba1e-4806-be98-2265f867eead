{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taric": {"id": "<PERSON><PERSON>", "key": "44", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Taric.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "44000", "num": 0, "name": "default", "chromas": false}, {"id": "44001", "num": 1, "name": "Szmaragdo<PERSON>", "chromas": false}, {"id": "44002", "num": 2, "name": "Taric w Zbroi z Piątego Wieku", "chromas": false}, {"id": "44003", "num": 3, "name": " <PERSON><PERSON>", "chromas": false}, {"id": "44004", "num": 4, "name": "Basenowy Taric", "chromas": true}, {"id": "44009", "num": 9, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "44018", "num": 18, "name": "Taric na Kosmofazie", "chromas": true}, {"id": "44027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> jest Aspe<PERSON>em Protektora, ob<PERSON><PERSON><PERSON> niezwykłą mocą strażnikiem życia, mił<PERSON>ś<PERSON> i piękna na Runeterze. Zniesławiony za porzucenie obowiązków i wygnany z Demacii, s<PERSON><PERSON>j oj<PERSON>yzny, wspiął się na Górę Targon, aby z<PERSON><PERSON> odkupienie, ale zamiast tego odkrył wyższe powołanie pośród gwiazd. <PERSON><PERSON>, przepełniony mocą starożytnego Targonu, stoi na straży, aby chronić ludzi przed spaczeniem Pustki.", "blurb": "<PERSON><PERSON> jest Aspektem Protektora, ob<PERSON><PERSON><PERSON> niezwykłą mocą strażnikiem życia, miłości i piękna na Runeterze. Zniesławiony za porzucenie obowiązków i wygnany z Demacii, swo<PERSON>j ojczyzny, wspiął się na Górę Targon, aby znale<PERSON>ć odkupienie, ale zamiast tego...", "allytips": ["Efekt skracania czasów odnowienia umiejętności przez Zuchwał<PERSON> sprawia, że przedmioty takie jak <PERSON>, Lodowa Rękawica, <PERSON><PERSON>b<PERSON>ze Ducha są niebywale skuteczne na Taricu.", "Używanie Gwiezdnego Muśnięcia przy mniejszej ilości ładunków sprawia, że jego leczenie jest mniej wydajne pod względem many, ale może znacznie zwiększyć obrażenia ciągłe, które Taric zadaje za pomocą Zuchwałości.", "Zamiast zachowywać Kosmiczną Poświatę do ostatniej chwili i ryzykować, że ktoś zginie z powodu jej opóźnienia, lepsze może okazać się użycie jej zawczasu - kiedy tylko ma się pewność, że walka drużynowa się rozpocznie."], "enemytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, d<PERSON>ła z dużym opóźnieniem. Postaraj się szyb<PERSON>, czy lepiej będzie wycofać się z walki, czy s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wykończyć jego sojuszników, nim zadziała.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pozwala Taricowi skracać czas odnowienia swoich umiejętności, kiedy wykonuje podstawowe ataki na wrogach. Staraj się utrzymywać go na dystans w trakcie walk druż<PERSON>owych, a w alei atakować go, kiedy będzie próbował zbliżyć się do stworów."], "tags": ["Support", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 40, "armorperlevel": 4.3, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "TaricQ", "name": "Gwiezdne Muśnięcie", "description": "Leczy pobliskich, sojuszniczych bohaterów, bazując na liczbie zebranych ładunków. Ataki wzmocnione efektem Zuchwałości dają ładunek Gwiezdnego Muśnięcia.", "tooltip": "<spellPassive>B<PERSON>nie:</spellPassive> <PERSON><PERSON><PERSON><PERSON> (maks. {{ e6 }}) co {{ stackcooldown }} sek. i za każdym razem, gdy trafia atakiem wzmocnionym <spellName>Zuchwałością</spellName>.<br /><br /><spellActive>Użycie:</spellActive> Zużywa wszystkie ładunki, by p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pobliskim sojuszniczym bohaterom <healing>{{ healingperstack }} pkt. zdrowia</healing> za każdy ładunek (<healing>{{ maxstackhealing }} pkt.</healing> przy {{ e6 }} ładunkach).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Maksymalna liczba ładunków", "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>"], "effect": ["{{ e6 }} -> {{ e6NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [1, 1, 1, 1, 1], [0.15, 0.3, 0.45, 0.6, 0.75], [0.75, 1.5, 2.25, 3, 3.75], [25, 50, 75, 100, 125], [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "1", "0.15/0.3/0.45/0.6/0.75", "0.75/1.5/2.25/3/3.75", "25/50/75/100/125", "1/2/3/4/5", "0", "0", "0", "0"], "vars": [], "costType": " pkt. many, wszystkie ładunki", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "TaricQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. many, wszystkie ładunki"}, {"id": "TaricW", "name": "Bastion", "description": "Biernie zwiększa pancerz Tarica oraz sojusznika z efektem Bastionu.<br><br>Użycie zapewnia sojusznikowi tarczę oraz obdarowuje efektem Bastionu, kt<PERSON>ry pozostaje aktywny, dop<PERSON>ki nie oddali się on od Tarica. Ponadto czary Tarica rzucane będą także z bohatera chronionego Bastionem.", "tooltip": "<spellPassive><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bierna:</spellPassive> <PERSON><PERSON> zyskuje <scaleArmor>{{ bonusarmor }} pkt. pancerza</scaleArmor> i za pomocą tej umiejętności tworzy więź między sobą a sojusznikiem. Kiedy objęci więzią bohaterowie znajdują się niedaleko siebie, połączony z Tarikiem sojusznik zyskuje <scaleArmor>{{ bonusarmor }} pkt. pancerza</scaleArmor>. Ponadto umiejętności Tarica rzucane są także z pozycji pobliskiego sojusznika.<br /><br /><spellPassive>Użycie:</spellPassive> Taric tworzy więź z sojuszniczym bohaterem, osła<PERSON>j<PERSON><PERSON> go <shield>tarczą równą {{ e2 }}% maks. zdrowia</shield> na {{ e3 }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["B<PERSON>ny pan<PERSON>z", "Współczynnik tarczy"], "effect": ["{{ armorbonuspercentage*100.000000 }}% -> {{ armorbonuspercentagenl*100.000000 }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [7, 8, 9, 10, 11], [2.5, 2.5, 2.5, 2.5, 2.5], [1000, 1000, 1000, 1000, 1000], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "7/8/9/10/11", "2.5", "1000", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "TaricW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TaricE", "name": "Rozbłysk", "description": "<PERSON>ric przygotowuje promień gwiezdnego światła, kt<PERSON>ry po chwili zadaje wrogom obrażenia magiczne i ogłusza ich.", "tooltip": "<PERSON>ric wytwarza promień gwiezdnego światła, kt<PERSON>ry po {{ e3 }} sek. zadaje wrogom <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>og<PERSON><PERSON><PERSON></status> ich na {{ e2 }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [90, 130, 170, 210, 250], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/130/170/210/250", "1.5", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "TaricE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TaricR", "name": "Kosmiczna <PERSON>", "description": "Z pewnym opóźnieniem uwalnia kosmiczną energię na pobliskich, sojuszniczych bohaterów, sprawiając, że na krótki czas stają się niewrażliwi.", "tooltip": "Taric wzywa ochronę z niebios. Po {{ initialdelay }} sek. pobliscy sojuszniczy bohaterowie stają się niewrażliwi na {{ invulnduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "TaricR.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Rzucane zaklęcia wzmacniają dwa kolejne podstawowe ataki Tarica, w wyniku czego zadają one dodatkowe obrażenia magiczne, skracają czas odnowienia jego umiejętności i są wyprowadzane w krótkim odstępie czasu.", "image": {"full": "Taric_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}