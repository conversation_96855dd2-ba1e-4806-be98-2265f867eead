{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Udyr": {"id": "<PERSON><PERSON><PERSON>", "key": "77", "name": "<PERSON><PERSON><PERSON>", "title": "Duchowy Wędrowca", "image": {"full": "Udyr.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "77000", "num": 0, "name": "default", "chromas": false}, {"id": "77001", "num": 1, "name": "Udyr z Czarnym Pasem", "chromas": false}, {"id": "77002", "num": 2, "name": "Pierwotny Udyr", "chromas": false}, {"id": "77003", "num": 3, "name": "Udyr Strażnik Przodków", "chromas": false}, {"id": "77004", "num": 4, "name": "Na Pewno Ni<PERSON>", "chromas": false}, {"id": "77005", "num": 5, "name": "Smoczy Wieszcz Udyr", "chromas": false}, {"id": "77006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON>r, najpotężniejszy z żyjących duchowych wędrowców, obcuje ze wszystkimi widmami Freljordu — czy to poprzez empatyczne zrozumienie ich potrzeb, czy też kierowanie i przekształcanie ich eterycznej energii w swój własny, pierwotny styl walki. <PERSON><PERSON> wewnętrznej równowagi, aby jego umysł nie zagubił się wśród innych, jednak dąży również do harmonii poza granicami samego siebie — mistyczny krajobraz Freljordu może się rozwijać tylko dzięki wzrostowi, który wynika z konfliktu i walki, a Udyr wie, że aby utrzymać pokojową stagnację, trzeba pono<PERSON> ofiary.", "blurb": "<PERSON><PERSON><PERSON>, najpotężniejszy z żyjących duchowych wędrowców, obcuje ze wszystkimi widmami Frel<PERSON>u — czy to poprzez empatyczne zrozumienie ich potrzeb, czy też kierowanie i przekształcanie ich eterycznej energii w swój własny, pierwotny styl walki. Szuka...", "allytips": ["Tarcza Żółwia otrzymuje obrażenia po ich redukcji. Z tego powodu zakup przedmiotów obronnych drastycznie zwiększy twoją szansę na przeżycie.", "Udyr doskonale radzi sobie, zabijając stwory w dżungli. Wykorzystaj to do uzyskania dużej przewagi doświadczenia oraz kontroli mapy."], "enemytips": ["Udyr ma ograniczone opcje dystansowe, staraj się zachować odległ<PERSON>.", "Po użyciu <PERSON>, przebudzonej wersji umiejętności Udyr na jakiś czas nie będzie mógł przebudzić innych umiejętności."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 7, "magic": 4, "difficulty": 7}, "stats": {"hp": 664, "hpperlevel": 92, "mp": 271, "mpperlevel": 50, "movespeed": 350, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.65}, "spells": [{"id": "UdyrQ", "name": "<PERSON><PERSON><PERSON>", "description": "Udyr zyskuje prędko<PERSON><PERSON> ataku, a jego dwa następne ataki zadają dodatkowe obrażenia fizyczne. Ponowne użycie: Zyskujesz jeszcze większą prędko<PERSON><PERSON> ataku, a dwa następne ataki powodują trafienie celu błyskawicą.", "tooltip": "<spellActive>St<PERSON>:</spellActive> <PERSON><PERSON><PERSON> <attackSpeed>{{ attackspeedbase*100 }}% prędko<PERSON>ci ataku</attackSpeed>, a jego ataki zadają <physicalDamage>{{ onhitdamage }} pkt. obrażeń fizycznych</physicalDamage> %i:OnHit% <OnHit>przy trafieniu</OnHit> przez {{ attackspeeddurationbase }} sek. Ponadto dwa kolejne ataki Udyra w tym stylu zadają dodatkowo <physicalDamage>{{ maxhponhit1 }} pkt. obrażeń fizycznych zależnych od maks. zdrowia</physicalDamage> i zyskują {{ attackrange }} jedn. zasięgu.<br /><br /><keywordMajor>Przebudzenie:</keywordMajor> Zyskaj dodatkową <attackSpeed>prędko<PERSON>ć ataku</attackSpeed> (łącznie: <attackSpeed>{{ empoweredtotal<PERSON> }}</attackSpeed>) i obrażenia od maksymalnego zdrowia (łącznie: <physicalDamage>{{ q2totalonhithpdamage }}</physicalDamage>). Ponadto dwa kolejne ataki Udyra sześciokrotnie wywołują błyskawice, które zadają łącznie <magicDamage>{{ empoweredlightningbonusmax }} pkt. obrażeń magicznych zależnych od maks. zdrowia</magicDamage> odosobnionym celom (błyskawice przeskakują na pobliskie cele, gdy jest to możliwe).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "% obrażeń od maksymalnego zdrowia", "Obrażenia przy trafieniu"], "effect": ["{{ attackspeedbase*100.000000 }}% -> {{ attackspeedbasenl*100.000000 }}%", "{{ maxhponhitbase*100.000000 }}% -> {{ maxhponhitbasenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [20, 20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "UdyrW", "name": "Żelazna Opończa", "description": "Udyr zyskuje tarczę, a jego następne dwa ataki przywracają mu zdrowie. Ponowne użycie: zyskaj jeszcze większą tarczę i leczenie zależne od maks. zdrowia w ciągu kilku następnych sekund.", "tooltip": "<spellPassive><PERSON>yl <PERSON>y:</spellPassive> <PERSON><PERSON><PERSON> <shield>{{ totalshield }} pkt. tarczy</shield> na {{ shieldduration }} sek., a jego dwa kolejne ataki zyskują {{ lifesteal*100 }}% kradzieży życia i przywracają <healing>{{ lifeonhit }} pkt. zdrowia</healing>.<br /><br /><keywordMajor>Przebudzenie:</keywordMajor> Zyskaj <shield>{{ recastshield }} pkt. tarczy</shield>, odzyskaj <healing>{{ recastheal }} pkt. zdrowia</healing> w ciągu {{ shieldduration }} sek., a dwa kolejne ataki Udyra zyskają {{ lifesteal*200 }}% kradzieży życia i zregenerują <healing>{{ lifeonhitawakened }} pkt. zdrowia</healing>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczy", "Tarcza o wartości % zdrowia", "Kradzież życia"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ shieldpercenthealth*100.000000 }}% -> {{ shieldpercenthealthnl*100.000000 }}%", "{{ lifesteal*100.000000 }}% -> {{ lifestealnl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [0, 0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "UdyrW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "UdyrE", "name": "Płonący Pęd", "description": "Udyr zyskuje prędkość ruchu, a jego pierwszy atak na każdy cel spowoduje jego ogłuszenie. Ponowne użycie: zyskaj jeszcze większą prędkość ruchu i odporność na efekty unieruchomienia przez kilka sekund. ", "tooltip": "<spellActive>Styl Pędu:</spellActive> <PERSON><PERSON><PERSON> <speed>{{ movespeed*100 }}% prędkości ruchu</speed> zanikaj<PERSON>cej w ciągu {{ movespeedduration }} sek. Ponadto ataki Udyra powodują doskok do celu i <status>ogł<PERSON>zenie</status> go na {{ stunduration }} sek. (czas odnowienia: {{ icd }} sek. na cel).<br /><br /><keywordMajor>Przebudzenie:</keywordMajor> Zapewnia odporność na <status>unieruchomienie</status> i <status>osłabienie</status> oraz dodatkowe <speed>{{ movespeedbonus }} jedn. prędkości ruchu</speed> przez {{ unstoppableduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Osobny czas odnowienia dla każdej jednostki"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ icd }} -> {{ icdNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "UdyrR", "name": "Skrzydlata Burza", "description": "Udyr otacza się lodową burzą, która zadaje obrażenia i spowalnia pobliskich wrogów. Ponowne użycie: Wzmacnia i uwalnia burzę, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że śledzi ona wrogów i zadaje dodatkowe obrażenia.", "tooltip": "<spellActive>St<PERSON>:</spellActive> Przez {{ buffduration }} sek. Udyr otacza się lodową burzą, która zadaje <magicDamage>{{ stormdamage }} pkt. obrażeń magicznych</magicDamage> co sekundę pobliskim wrogom i <status>spowalnia</status> ich o {{ slowpotency*100 }}%, a jego dwa kolejne ataki w tym stylu zadają <magicDamage>{{ pulsedamage }} pkt. obrażeń magicznych</magicDamage> wrogom na terenie burzy.<br /><br /><keywordMajor>Przebudzenie:</keywordMajor> U<PERSON>ln<PERSON>j burzę, która pójdzie śladem ostatniego zaatakowanego przez Udyra wroga, zada mu dodatkowe <magicDamage>obrażenia magiczne w wysokości {{ percenthpblast }} maks. zdrowia</magicDamage> przez czas jej trwania oraz <status>spowolni</status> cel o dodatkowe {{ empoweredslow }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia na sekundę", "Spowolnienie"], "effect": ["{{ stormbasedamage }} -> {{ stormbasedamageNL }}", "{{ slowpotency*100.000000 }}% -> {{ slowpotencynl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [370, 370, 370, 370, 370, 370], "rangeBurn": "370", "image": {"full": "UdyrR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Pomost", "description": "Udyr ma cztery podstawowe umiejętności, k<PERSON><PERSON><PERSON><PERSON> może używać, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> inny styl, a ponowne rzucenie powoduje jego odnowienie wraz z superkorzyściami. Ponadto po rzuceniu umiejętności dwa następne ataki Udyra mają zwiększoną prędkość ataku.", "image": {"full": "Udyr_P.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}