{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Soraka": {"id": "<PERSON><PERSON><PERSON>", "key": "16", "name": "<PERSON><PERSON><PERSON>", "title": "Gwiezdne Dziecko", "image": {"full": "Soraka.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "16000", "num": 0, "name": "default", "chromas": false}, {"id": "16001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "16002", "num": 2, "name": "Boska Soraka", "chromas": false}, {"id": "16003", "num": 3, "name": "Gwiezdna Soraka", "chromas": false}, {"id": "16004", "num": 4, "name": "Soraka Żniwiarz <PERSON>", "chromas": false}, {"id": "16005", "num": 5, "name": "Soraka z Zakonu Banana", "chromas": false}, {"id": "16006", "num": 6, "name": "Cyber Soraka", "chromas": false}, {"id": "16007", "num": 7, "name": "Czarodziejka Gwiazd Soraka", "chromas": false}, {"id": "16008", "num": 8, "name": "Czarodziejka Piżamy Soraka", "chromas": false}, {"id": "16009", "num": 9, "name": "Sorak<PERSON> Zimo<PERSON>", "chromas": true}, {"id": "16015", "num": 15, "name": "Soraka Zwiastunka Brzasku", "chromas": false}, {"id": "16016", "num": 16, "name": "Soraka Zwiastunka Nocy", "chromas": false}, {"id": "16017", "num": 17, "name": "Czarodziejka Gwiazd Soraka (Prestiżowa)", "chromas": false}, {"id": "16018", "num": 18, "name": "Kawiarniany Cukiereczek Soraka", "chromas": true}, {"id": "16027", "num": 27, "name": "Soraka Duchowego Rozkwitu", "chromas": true}, {"id": "16037", "num": 37, "name": "Soraka Nieśmiertelnej Podróży", "chromas": true}, {"id": "16044", "num": 44, "name": "Soraka z Wróżkowego Dworu", "chromas": true}], "lore": "Wędrowniczka z astralnych wymiarów ponad Górą Targon, Soraka porzuciła nieśmiertelność na rzecz obrony ras śmiertelników przed ich własnymi, bardziej brutalnymi instynktami. Przemierza świat, by <PERSON><PERSON><PERSON><PERSON> się cnotami współczucia i litości ze wszystkim napotkanymi ludźmi, le<PERSON><PERSON><PERSON> nawet tych, którzy jej zł<PERSON>zeczą. <PERSON><PERSON><PERSON> całego zła, kt<PERSON><PERSON> widziała, <PERSON><PERSON><PERSON> dalej wierzy, że ludzie z Runeterry wciąż nie osiągnęli swojego potencjału.", "blurb": "Wędrowniczka z astralnych wymiarów ponad Górą <PERSON>, Soraka porzuciła nieśmiertelność na rzecz obrony ras śmiertelników przed ich własnymi, bardziej brutalnymi instynktami. Przemierza świat, by <PERSON><PERSON><PERSON><PERSON> się cnotami współczucia i litości ze wszystkim...", "allytips": ["Soraka jest potężnym sprzymierzeńcem w bitwie, korzystającym ze swoich czarów leczących, aby prowad<PERSON><PERSON> swoją drużynę do ataku.", "Użyj <PERSON>, aby wesprz<PERSON><PERSON> swoich sojuszników po drugiej stronie mapy i uchronić ich przed śmiercią.", "Równonoc może być użyta do utrzymywania przeciwników na dystans."], "enemytips": ["Skup uwagę na Sorace, gdy za bardzo wysunie się do przodu, aby <PERSON><PERSON><PERSON> swoich sojuszników.", "Wykorzystaj długi czas odnowienia Równonocy, jeż<PERSON> użyje jej do nękania.", "Łatwiej jest zabi<PERSON>, niż leczonego przez nią sojusznika."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 7, "difficulty": 3}, "stats": {"hp": 605, "hpperlevel": 88, "mp": 425, "mpperlevel": 40, "movespeed": 325, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2.14, "attackspeed": 0.625}, "spells": [{"id": "SorakaQ", "name": "Deszcz Gwiazd", "description": "W wybranym miejscu spada z niebios gwiazda, zadając obrażenia magiczne i spowalniając wrogów. Jeśli Deszcz Gwiazd trafi wrogiego bohatera, Soraka odzyskuje zdrowie.", "tooltip": "Soraka przywołuje gwiazdę zadającą <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>spowalniaj<PERSON><PERSON>ą</status> wrog<PERSON> o {{ movespeedslow*100 }}% na {{ slowduration }} sek. <br /><br />Trafienie wrogiego bohatera zapewni Sorace <keywordMajor>Odmłodzenie</keywordMajor>, które przywraca jej <healing>{{ totalhot }} pkt. zdrowia</healing> w ciągu {{ hotduration }} sek. i zapewnia <speed>{{ movespeedhaste*100 }}% prędkości ruchu</speed> zanikającej w tym samym czasie.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Przywrócenie zdrowia (Odmłodzenie)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basehot }} -> {{ basehotNL }}", "{{ movespeedhaste*100.000000 }}% -> {{ movespeedhastenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "SorakaQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SorakaW", "name": "<PERSON><PERSON><PERSON>", "description": "Soraka poświęca część swojego zdrowia, aby ul<PERSON><PERSON><PERSON> sojuszniczego bohatera.", "tooltip": "Soraka przywraca <healing>{{ totalheal }} pkt. zdrowia</healing> innemu sojuszniczemu bohaterowi.<br /><br /><PERSON><PERSON><PERSON> na Sorakę nałożony jest efekt <keywordMajor>Odmłodzenia</keywordMajor>, koszt zdrowia tej umiejętności zostaje zmniejszony o {{ percenthealthcostrefund*100 }}%, a cel zyskuje <keywordMajor>Odmłodzenie</keywordMajor> na {{ spell.sorakaq:hotduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Leczenie", "Czas odnowienia", "Koszt (@AbilityResourceName@)", "Zmniejszenie kosztu zdrowia"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ percenthealthcostrefund*100.000000 }}% -> {{ percenthealthcostrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% maksymalnego zdrowia, {{ cost }} pkt. many", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SorakaW.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ percenthealthcost*100 }}% maksymalnego zdrowia, {{ cost }} pkt. many"}, {"id": "SorakaE", "name": "Równonoc", "description": "Tworzy pole w wy<PERSON><PERSON><PERSON>, które ucisza wszystkich przeciwników znajdujących się w środku. G<PERSON> pole wygaśnie, wszyscy przeciwnicy w środku zostaną unieruchomieni.", "tooltip": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> g<PERSON> pole, kt<PERSON><PERSON> zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> bohaterom. Pole utrzymuje się przez {{ rootdelay }} sek., <status>u<PERSON><PERSON><PERSON><PERSON><PERSON></status> wrog<PERSON> znajdujących się na jego obszarze. Gdy pole zniknie, bohaterowie, którzy wciąż się w nim znajdują, zostan<PERSON> <status>unieruchomieni</status> na {{ rootduration }} sek. i otrzymają <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia", "Czas działania unieruchomienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "SorakaE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SorakaR", "name": "Życzenie", "description": "Soraka wypełnia sojuszników nadzieją, przywracając zdrowie sobie i wszystkim sojuszniczym bohaterom.", "tooltip": "Soraka przywo<PERSON><PERSON><PERSON> boską moc, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <healing>{{ healingcalc }} pkt. zdrowia</healing> wszystkim sojuszniczym bohaterom bez względu na ich położenie na mapie. Wartość leczenia jest zwiększona o <healing>{{ ampedhealing }} pkt.</healing> na celach, które mają mniej niż 40% zdrowia.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Leczenie", "Czas odnowienia"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [150, 135, 120], "cooldownBurn": "150/135/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SorakaR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Zbawienie", "description": "Soraka porusza się s<PERSON>j, gdy biegnie w stronę pobliskich sojuszników z niskim poziomem zdrowia.", "image": {"full": "Soraka_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}