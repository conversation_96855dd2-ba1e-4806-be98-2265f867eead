{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sett": {"id": "<PERSON><PERSON>", "key": "875", "name": "セト", "title": "ザ・ボス", "image": {"full": "Sett.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "875000", "num": 0, "name": "default", "chromas": false}, {"id": "875001", "num": 1, "name": "三国武神セト", "chromas": true}, {"id": "875008", "num": 8, "name": "黒曜龍セト", "chromas": true}, {"id": "875009", "num": 9, "name": "プレステージ黒曜龍セト", "chromas": false}, {"id": "875010", "num": 10, "name": "プールパーティ セト", "chromas": true}, {"id": "875019", "num": 19, "name": "爆発花火セト", "chromas": true}, {"id": "875038", "num": 38, "name": "精霊の花祭りセト", "chromas": true}, {"id": "875045", "num": 45, "name": "ソウルファイター セト", "chromas": true}, {"id": "875056", "num": 56, "name": "HEARTSTEEL セト", "chromas": true}, {"id": "875066", "num": 66, "name": "大蛇の耀光セト", "chromas": false}], "lore": "ノクサスとの戦争の機運が高まる中、セトはアイオニアで勢力を増しつつある裏社会の親玉として名を上げた。ナヴォリの闘技場で戦い始めたときには無名の挑戦者に過ぎなかったセトだが、恐ろしいほどの腕力と底知れぬ打たれ強さで瞬く間にその名を轟かせた。実力で参加者たちの頂点にまで上り詰めたセトは、ついにはかつて自分が戦った闘技場の支配権を握ったのだった。", "blurb": "ノクサスとの戦争の機運が高まる中、セトはアイオニアで勢力を増しつつある裏社会の親玉として名を上げた。ナヴォリの闘技場で戦い始めたときには無名の挑戦者に過ぎなかったセトだが、恐ろしいほどの腕力と底知れぬ打たれ強さで瞬く間にその名を轟かせた。実力で参加者たちの頂点にまで上り詰めたセトは、ついにはかつて自分が戦った闘技場の支配権を握ったのだった。", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "闘魂", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 670, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "SettQ", "name": "ナックルダウン", "description": "次の2回の通常攻撃が対象の最大体力に応じた追加ダメージを与える。また、敵チャンピオンに向かう際は移動速度が増加する。", "tooltip": "戦いを求めて、{{ msduration }}秒間敵チャンピオンに向かう際の<speed>移動速度が{{ msamount*100 }}%</speed>増加する。<br /><br />さらに、次の2回の通常攻撃が追加で<physicalDamage>{{ basedamage }}(+最大体力の{{ maxhealthdamagecalc }})にあたる物理ダメージ</physicalDamage>を与えるようになる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "基本ダメージ", "最大体力割合ダメージ(攻撃力100ごと)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthtadratiotooltip }}% -> {{ maxhealthtadratiotooltipNL }}%"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "SettQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "SettW", "name": "ヘイメイカー", "description": "自動効果により、受けたダメージを「闘魂」として蓄える。発動するとすべての「闘魂」を消費してシールドを獲得し、パンチして一定範囲内の中心にいる敵には確定ダメージ、端にいる敵には物理ダメージを与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> 被ダメージの{{ damagestored*100 }}%を<keywordMajor>「闘魂」</keywordMajor>として獲得する(最大<keywordMajor>{{ maxgrit }}</keywordMajor>)。<keywordMajor>「闘魂」</keywordMajor>はダメージを受けてから{{ adrenalinestoragewindow }}秒後に急速に減衰する。<br /><br /><spellActive>発動効果:</spellActive> すべての<keywordMajor>「闘魂」</keywordMajor>を消費して、<shield>消費した「闘魂」の{{ shieldconversion*100 }}%に当たるシールド</shield>を獲得する。このシールドは{{ shieldmaxduration }}秒かけて減衰する。その後、強烈なパンチをお見舞いし、中心部にいる敵に<trueDamage>{{ damagecalc }}(+消費した「闘魂」の{{ damageconversion }})の確定ダメージ</trueDamage>を与える(最大<trueDamage>{{ f1 }}ダメージ</trueDamage>)。中心部にいなかった敵には、代わりに<physicalDamage>物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "基本ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SettW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "SettE", "name": "フェイスブレイカー", "description": "自身の両側にいるすべての敵を引き寄せ、ダメージを与えてスタンさせる。敵が片側のみにいた場合はスタンの代わりにスロウ効果を与える。", "tooltip": "自身の左右にいる敵同士をぶつけて<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。左右両側でそれぞれ1体以上の敵をつかむと、すべての敵を{{ stunduration }}秒間<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "基本ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "SettE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "SettR", "name": "ショーストッパー", "description": "敵チャンピオンを担いでジャンプして移動後に地面に叩きつけ、着地時に周囲のすべての敵にダメージとスロウ効果を与える。", "tooltip": "敵チャンピオンを掴んで<status>サプレッション効果</status>を与えながら前方に運び、地面に叩きつける。叩きつけた際に周囲の敵に<physicalDamage>{{ damagecalc }}(+掴んだ敵の増加体力の{{ maxhealthdamage*100 }}%)の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間、{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。叩きつけた位置から離れているほど、敵が受けるダメージは低下する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "増加体力ダメージ", "基本ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "SettR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "ファイティングスピリット", "description": "通常攻撃で左右のパンチを交互に繰り出す。右のパンチの方が少し強く、速い。また、セトは負けず嫌いで、減少体力に応じて体力自動回復が増加する。", "image": {"full": "Sett_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}