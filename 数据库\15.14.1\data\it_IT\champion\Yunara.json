{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yunara": {"id": "<PERSON><PERSON>", "key": "804", "name": "<PERSON><PERSON>", "title": "la fede incrollabile", "image": {"full": "Yunara.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "804000", "num": 0, "name": "default", "chromas": false}, {"id": "804001", "num": 1, "name": "<PERSON><PERSON> Terme Fiore spirituale", "chromas": true}], "lore": "Incrollabile nella sua devozione verso Ionia, <PERSON><PERSON> ha trascorso secoli isolata nel regno degli spiriti, affinando le sue abilità con le Aion Er'na, reliquie Kinkou leggendarie. Nonostante tutti i sacrifici di Yunara, il suo giuramento di liberare la terra dalla mancanza di armonia e dalla discordia resta immutato, cos<PERSON> come la sua fede. Ma il mondo che la attende, e l'ombra incombente di un'antica minaccia, metteranno alla prova tutta la sua determinazione.", "blurb": "Incrollabile nella sua devozione verso Ionia, Yunara ha trascorso secoli isolata nel regno degli spiriti, affinando le sue abilità con le Aion Er'na, reliquie Kinkou leggendarie. Nonostante tutti i sacrifici di Yunara, il suo giuramento di liberare la...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 275, "mpperlevel": 45, "movespeed": 325, "armor": 25, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.65}, "spells": [{"id": "YunaraQ", "name": "Fioritura dello Spirito", "description": "Yunara ottiene velocità d'attacco, danni bonus sul colpo e i suoi attacchi si propagano ai nemici nelle vicinanze.", "tooltip": "<spellPassive>Passiva</spellPassive>: <PERSON><PERSON> infligge <magicDamage>{{ calc_passive_damage }} danni magici</magicDamage> <OnHit>%i:OnHit% sul colpo</OnHit> e gli attacchi conferiscono <evolve>{{ resource_nonchampion }} Scatenata</evolve> (<evolve>{{ resource_champion }} Scatenata</evolve> per i campioni).<br /><br /><spellPassive>Attiva</spellPassive>: <PERSON><PERSON> consuma <evolve>{{ resource_max }} Scatenata</evolve> e per {{ buff_duration }} secondi ottiene <attackSpeed>{{ calc_attack_speed }} velocità d'attacco</attackSpeed> e infligge ulteriori <magicDamage>{{ calc_damage }} danni magici</magicDamage> <OnHit>%i:OnHit% sul colpo</OnHit>. Durante questo periodo, i suoi attacchi si propagano ai nemici nelle vicinanze, infliggendo loro <physicalDamage>{{ calc_damage_spread }} danni fisici</physicalDamage>.<br /><br /><keywordMajor>Stato trascendente</keywordMajor>: Questa abilità si attiva istantaneamente per {{ spell.yunarar:buff_duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni sul colpo della passiva", "Velocità d'attacco", "Danni sul colpo dell'attiva"], "effect": ["{{ damage_passive }} -> {{ damage_passiveNL }}", "{{ attack_speed*100.000000 }}% -> {{ attack_speednl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraQ.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ basecost }} mana"}, {"id": "YunaraW", "name": "Arco del Giudizio | Arco della Rovina", "description": "Yunara scaglia un grano votivo roteante che infligge danni e rallenta i nemici. Mentre è nello stato trascendente spara invece un raggio laser che infligge danni e rallenta i nemici.", "tooltip": "Yunara scaglia un grano votivo che infligge <magicDamage>{{ calc_damage_initial }} danni magici</magicDamage> e un <status>rallentamento di {{ calc_slow }} che decade nell'arco di {{ slow_duration }} secondi</status>. Infligge <magicDamage>{{ calc_damage_per_second }} danni magici</magicDamage> aggiuntivi al secondo.<br /><br /><keywordMajor>Stato trascendente - Arco della Rovina</keywordMajor>: Yunara scaglia un grano votivo che infligge <magicDamage>{{ spell.yunarar:calc_rw_damage }} danni magici</magicDamage> e un <status>rallentamento di {{ spell.yunarar:calc_rw_slow_amount }} che decade nell'arco di {{ spell.yunarar:rw_slow_duration }} secondo/i</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> al secondo"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ damage_per_second }} -> {{ damage_per_secondNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "YunaraW.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} <PERSON>a"}, {"id": "YunaraE", "name": "Passi del Kanmei | Ombre intoccabili", "description": "Yunara ottiene velocità di movimento decrescente e diventa spettrale. Mentre è nello stato trascendente esegue invece uno scatto in una direzione.", "tooltip": "<PERSON><PERSON> ottiene <speed>{{ calc_move_speed }} velocità di movimento</speed>, che aumenta a <speed>{{ calc_move_speed_enhanced }} velocità di movimento</speed> quando si muove verso un campione nemico e decresce nell'arco di {{ buff_duration }} secondi.<br /><br /><keywordMajor>Stato trascendente - Ombre intoccabili</keywordMajor>: <PERSON><PERSON> scatta in una direzione.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "<PERSON><PERSON>"], "effect": ["{{ move_speed*100.000000 }}% -> {{ move_speednl*100.000000 }}%", "{{ buff_duration }} -> {{ buff_durationNL }}"]}, "maxrank": 5, "cooldown": [7.5, 7.5, 7.5, 7.5, 7.5], "cooldownBurn": "7.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraE.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} <PERSON>a"}, {"id": "YunaraR", "name": "Trascendere l'io", "description": "Yunara entra in uno stato trascendente che ne potenzia le abilità base.", "tooltip": "<PERSON><PERSON> entra in uno <keywordMajor>stato trascendente</keywordMajor> per {{ buff_duration }} secondi, potenziando le proprie abilità base per la durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Arco della Rovina | Danni", "Ombre intoccabili | Velocità scatto"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rw_damage_base }} -> {{ rw_damage_baseNL }}", "{{ re_dash_speed }} -> {{ re_dash_speedNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraR.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Voto alle Terre Antiche", "description": "I colpi critici di Yunara infliggono danni magici bonus.", "image": {"full": "Yunara_Passive.Yunara.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}