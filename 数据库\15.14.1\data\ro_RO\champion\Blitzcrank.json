{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "marele golem cu abur", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "Blitzcrank ruginit", "chromas": false}, {"id": "53002", "num": 2, "name": "Blitzcrank portar", "chromas": false}, {"id": "53003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "53004", "num": 4, "name": "Blitzcrank din Piltover", "chromas": false}, {"id": "53005", "num": 5, "name": "Cu siguranță nu Blitzcrank", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Riot Blitzcrank", "chromas": false}, {"id": "53011", "num": 11, "name": "Blitzcrank boss", "chromas": true}, {"id": "53020", "num": 20, "name": "<PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON> rebel", "chromas": false}, {"id": "53021", "num": 21, "name": "B<PERSON><PERSON><PERSON>, l<PERSON><PERSON>ier model", "chromas": false}, {"id": "53022", "num": 22, "name": "Blitzcrank infuzia vrăjitoarei", "chromas": true}, {"id": "53029", "num": 29, "name": "Blitz și Crank distracție în spațiu", "chromas": true}, {"id": "53036", "num": 36, "name": "Blitzcrank victorios", "chromas": true}, {"id": "53047", "num": 47, "name": "Blitzcrank de la Jocurile Zenitului", "chromas": true}, {"id": "53056", "num": 56, "name": "Blitzcrank albinuță", "chromas": true}], "lore": "Blitzcrank este un automaton zaunian enorm, aproape indestructibil, care a fost construit cu scopul de a înlătura deșeurile periculoase. În timp, îns<PERSON>, s-a simțit limitat de obiectivul lui original, așa că și-a adus singur modificări pentru a-i putea sluji mai bine pe locuitorii fragili ai Haznalei. Blitzcrank își folosește acum atât trupul robust, cât și forța, pentru a-i proteja pe ceilalți cu pumnii săi de metal și pentru a-i pedepsi pe răufăcători.", "blurb": "Blitzcrank este un automaton zaunian enorm, aproape indestructibil, care a fost construit cu scopul de a înlătura deșeurile periculoase. În timp, îns<PERSON>, s-a simțit limitat de obiectivul lui original, așa că și-a adus singur modificări pentru a-i putea...", "allytips": ["Combo-ul 1-2-3 format din ''Cârlig'', ''Pumnul energizat'' și ''Câmp static'' poate spulbera un inamic singur.", "Dacă folosești cârligul lui Blitzcrank ca să târăști un inamic în raza de acțiune a turnului tău și apoi îi aplici ''Pumnul energizat'', îți vei ajuta turnul să lovească inamicul de câteva ori."], "enemytips": ["Abilitatea pasivă a lui <PERSON>litzcrank, ''Bariera de mană'', îl protejează când are viața scăzută.", "Dacă stai în spatele minionilor, te poți feri de cârligul lui Blitzcrank. Acesta agață doar prima țintă inamică întâlnită."], "tags": ["Tank", "Support"], "partype": "Mană", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Blitzcrank își aruncă înainte pumnul drept pentru a prinde un adversar de pe traiectorie, provocându-i daune și trăgându-l spre el.", "tooltip": "Blitzcrank își aruncă înainte pumnul drept, <status>trăgând</status> spre el primul inamic lovit și provocându-i <magicDamage>{{ totaldamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Overdrive", "name": "Supraturaț<PERSON>", "description": "Blitzcrank se supraîncarcă pentru a-și mări dramatic viteza de mișcare și de atac. Este încetinit temporar după sfârșitul efectului.", "tooltip": "Blitzcrank se supraîncar<PERSON><PERSON>, primind <speed>{{ movespeedmod*100 }}% vitez<PERSON> de mișcare ce scade în timp</speed> și <attackSpeed>{{ attackspeedmod*100 }}% vitez<PERSON> de atac</attackSpeed> timp de {{ duration }} secunde.<br /><br />Apoi este <status>încetinit</status> cu {{ movespeedmodreduction*100 }}% timp de {{ slowduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Viteză de mișcare", "Viteză de atac"], "effect": ["{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PowerFist", "name": "Pumn energizat", "description": "Blitzcrank își încarcă pumnul pentru ca următorul lui atac să provoace daune duble și să arunce ținta în sus.", "tooltip": "Blitzcrank î<PERSON>i înc<PERSON> pumnul, iar următorul său atac <status>aruncă în sus</status> ținta timp de {{ ccduration }} sec. și îi provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "StaticField", "name": "Câmp static", "description": "Inamicii atacați de Blitzcrank sunt marcați și suferă daune din fulgere după 1 secundă. La activare, abilitatea le elimină scuturile inamicilor din apropiere, le provoacă daune și îi amuțește pentru scurt timp.", "tooltip": "<spellPassive>Pasivă: </spellPassive>cât timp abilitatea este disponibilă, fulgerele încarc<PERSON> pumnii lui Blitzcrank, însemnând inamicii atacați. Dup<PERSON> 1 secundă, ace<PERSON><PERSON> sunt electrocutați, suferind <magicDamage>{{ passivedamage }} daune magice</magicDamage>.<br /><br /><spellActive>Activă: </spellActive>Blitzcrank se supraîncarcă, provocându-le <magicDamage>{{ activedamage }} daune magice</magicDamage> inamicilor din apropiere și <status>amuțindu-i</status> timp de {{ silenceduration }} secunde. În plus, scuturile lor sunt distruse.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune de bază pasivă", "Raport AP pasivă", "Daune de bază activă", "Timp de reactivare activă"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}% -> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Barieră de mană", "description": "Când are viaț<PERSON> scăzută, Blitzcrank primește un scut a cărui valoare depinde de mana lui.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}