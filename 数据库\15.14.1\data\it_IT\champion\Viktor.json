{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viktor": {"id": "<PERSON>", "key": "112", "name": "<PERSON>", "title": "l'araldo dell'Arcane", "image": {"full": "Viktor.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "112000", "num": 0, "name": "default", "chromas": false}, {"id": "112001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "112002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "112003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "112004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "112005", "num": 5, "name": "Viktor OPSI", "chromas": true}, {"id": "112014", "num": 14, "name": "<PERSON> Fuoco", "chromas": true}, {"id": "112024", "num": 24, "name": "<PERSON>: <PERSON>", "chromas": false}], "lore": "Viktor ha accolto senza riserve la Gloriosa Evoluzione, trasformando biomeccanicamente il suo io passato e diventando una specie di messia per i suoi seguaci. Ha sacrificato la sua umanità seguendo la logica per cui eliminare le emozioni cancella ogni sofferenza, e ora vuole diffondere la rivelazione dell'hexcore in tutto il mondo... anche a coloro incapaci di comprenderne i benefici. <PERSON><PERSON><PERSON><PERSON>, per questo maestro dell'arcano la violenza non è che una variabile necessaria per bilanciare l'equazione suprema.", "blurb": "Viktor ha accolto senza riserve la Gloriosa Evoluzione, trasformando biomeccanicamente il suo io passato e diventando una specie di messia per i suoi seguaci. Ha sacrificato la sua umanità seguendo la logica per cui eliminare le emozioni cancella ogni...", "allytips": ["Il Raggio hextech è un colpo rapido molto potente ed è uno strumento di controllo ad area molto forte. Usalo in combinazione con Campo gravitazionale per controllare la posizione del nemico.", "Assicurati di scegliere il giusto potenziamento al momento giusto."], "enemytips": ["Fai attenzione a quanto lasci avvicinare Viktor. Il controllo di Viktor del campo di battaglia aumenta con la vicinanza al suo avversario.", "Capirai quanti potenziamenti Viktor possiede dal colore del suo bastone (viola, giallo, blu, rosso)."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 600, "hpperlevel": 100, "mp": 405, "mpperlevel": 45, "movespeed": 335, "armor": 23, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 8, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "ViktorQ", "name": "Trasferimento energetico", "description": "<PERSON> colpisce un'unità nemica con dei danni magici, che gli conferiscono uno scudo e potenziano il suo prossimo attacco base.<br><br>Potenziamento: lo scudo di Trasferimento energetico aumenta del 60% e Viktor ottiene velocità di movimento bonus dopo il lancio dell'abilità.<br>", "tooltip": "<PERSON> colpisce un nemico, infliggendo <magicDamage>{{ totalmissiledamage }} danni magici</magicDamage> e conferendo a <PERSON> uno <shield>scudo da {{ shieldlevelscaling }}</shield> per {{ buffduration }} secondi.<br /><br />Il prossimo attacco di Viktor entro 3,5 secondi infligge ulteriori <magicDamage>{{ attacktotaldmg }} danni magici</magicDamage>.<br /><br /><keywordMajor>Potenziamento:</keywordMajor> conferisce uno <shield>scudo da {{ totalaugmentedshieldvalue }}</shield> e <PERSON> <speed>{{ augmentmovespeedbonus }}% velocità di movimento</speed> per {{ buffduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON> (proiettile)", "<PERSON><PERSON> (attacco)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ aabonusdamage }} -> {{ aabonusdamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [2.5, 2.5, 2.5, 2.5, 2.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0.08, 0.08, 0.08, 0.08, 0.08], [0.18, 0.18, 0.18, 0.18, 0.18], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "2.5", "30", "0", "20/45/70/95/120", "0.08", "0.18", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViktorQ.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorW", "name": "Campo gravitazionale", "description": "Viktor evoca un pesante campo gravitazionale che rallenta i nemici nel suo raggio d'azione. I nemici che restano troppo a lungo nel campo vengono storditi.<br><br>Potenziamento: le abilità di Viktor rallenteranno i nemici.<br>", "tooltip": "<PERSON> schiera un meccanismo di imprigionamento gravitazionale per {{ fieldduration }} secondi, <status>rallentando</status> di un {{ slowpotency*-1 }}% i nemici al suo interno. I nemici che restano nel suo raggio per 1.25 secondi vengono <status>storditi</status> per {{ stunduration }} secondi.<br /><br /><keywordMajor>Passiva potenziata:</keywordMajor> le abilità di Viktor <status>rallentano</status> di un {{ augmentslow }}% per 1 secondo.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Rallentamento del movimento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ slowpotency*-1.000000 }}% -> {{ slowpotencynl*-1.000000 }}%"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ViktorW.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorE", "name": "Raggio hextech", "description": "Viktor usa il suo braccio biomeccanico per sparare un raggio hextech che taglia il terreno in una linea, infliggendo danni a qualsiasi avversario che attraversa la sua traiettoria.<br><br>Potenziamento: il raggio hextech è seguito da un'esplosione che infligge danni magici.<br>", "tooltip": "Viktor spara un raggio hextech in una direzione a scelta, infliggendo <magicDamage>{{ laserdamage }} danni magici</magicDamage> ai nemici colpiti.<br /><br /><keywordMajor>Potenziato:</keywordMajor> dopo il raggio della morte arriva una scossa di assestamento che infligge <magicDamage>{{ aftershockdamage }} danni magici</magicDamage>.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> (Raggio hextech)", "<PERSON><PERSON> (Scossa di assestamento)", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamagelaser }} -> {{ basedamagelaserNL }}", "{{ basedamageaftershock }} -> {{ basedamageaftershockNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [20, 60, 100, 140, 180], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "20/60/100/140/180", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "ViktorE.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorR", "name": "<PERSON><PERSON><PERSON> arcana", "description": "<PERSON> evoca una Tempesta arcana sul campo, infliggendo danni magici e interrompendo le canalizzazioni nemiche. La tempesta infligge periodicamente danni magici a tutti i nemici nelle vicinanze e può essere deviata da <PERSON>.<br><br>Potenziamento: la Tempesta arcana si muove il 25% più velocemente e, ogni volta che infligge danni a un campione che muore, diventa più grande e aumenta la propria durata.<br><br>", "tooltip": "Viktor evoca una Tempesta arcana in un'area per {{ stormduration }} secondi, infliggendo all'istante <magicDamage>{{ initialburstdamage }} danni magici</magicDamage> seguiti da <magicDamage>{{ subsequentburstdamage }} danni magici</magicDamage> al secondo ai nemici circostanti. La tempesta segue automaticamente i campioni che ha danneggiato di recente.<br /><br /><recast>Rila<PERSON><PERSON>:</recast> Viktor può spostare manualmente la tempesta.<br /><br /><keywordMajor>Potenziata:</keywordMajor> La tempesta si muove un {{ augmentboost*100 }}% più velocemente. Se un campione che ha subito danni dalla tempesta muore, la tempesta diventa più grande e la sua durata aumenta di {{ tooltip_durationextension }} secondi (fino a {{ maxgrowths }} volte).<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> (raffica iniziale)", "<PERSON><PERSON> (tick periodici)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ initialburstbasedamage }} -> {{ initialburstbasedamageNL }}", "{{ subsequentburstbasedamage }} -> {{ subsequentburstbasedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "ViktorR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Gloriosa Evoluzione", "description": "<PERSON> ottiene Frammenti Hextech ogni volta che uccide un nemico. <PERSON>po ogni 100 Frammenti Hextech ottenuti, <PERSON> migliora un'abilità attiva in modo permanente. Dopo aver potenziato tutte le abilità di base, può raccogliere 100 Frammenti Hextech per potenziare l'abilità suprema.", "image": {"full": "Viktor_Passive.ViktorVGU.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}