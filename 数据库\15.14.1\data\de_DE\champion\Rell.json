{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rell": {"id": "<PERSON><PERSON>", "key": "526", "name": "<PERSON><PERSON>", "title": "die eiserne Jungfrau", "image": {"full": "Rell.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "526000", "num": 0, "name": "default", "chromas": false}, {"id": "526001", "num": 1, "name": "Kriegerkönigin <PERSON>ll", "chromas": true}, {"id": "526010", "num": 10, "name": "Sternenwächt<PERSON><PERSON>", "chromas": true}, {"id": "526020", "num": 20, "name": "High Noon-<PERSON><PERSON>", "chromas": true}, {"id": "526030", "num": 30, "name": "Große Abrechnung-Rell", "chromas": false}], "lore": "Rell ist das Ergebnis grausamer Experimente der Schwarzen Rose, die sie in eine lebende Waffe verwandelt haben. Seitdem setzt sie alles daran, Noxus zu vernichten. Ihre Kindheit war von Elend und Schrecken geprägt, als sie unaussprechliche Eingriffe über sich ergehen lassen musste. Der Plan war, ihre magische Kontrolle über Metall zu perfektionieren und als Waffe einzusetzen … bis sie auf ihrer brutalen Flucht viele ihrer Peiniger tötete. Als Verbrecherin geächtet bekämpft Rell nun auf ihrer Suche nach Überlebenden aus ihrer alten „Akademie“ alle noxianischen Soldaten, die sich ihr in den Weg stellen, und beschützt die Schwachen, während sie ihre ehemaligen Aufseher ohne Gnade hinrichtet.", "blurb": "<PERSON>ll ist das Ergebnis grausamer Experimente der Schwarzen Rose, die sie in eine lebende Waffe verwandelt haben. Seitdem setzt sie alles daran, Noxus zu vernichten. Ihre Kindheit war von Elend und Schrecken geprägt, als sie unaussprechliche Eingriffe...", "allytips": [], "enemytips": [], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 620, "hpperlevel": 104, "mp": 320, "mpperlevel": 40, "movespeed": 315, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.8, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.85, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "RellQ", "name": "Vernichtender Schlag", "description": "<PERSON><PERSON> fügt <PERSON> in einer Reihe magischen Schaden zu, zerst<PERSON>rt ihre Schilde und betäubt sie. ", "tooltip": "<PERSON><PERSON> stößt mit ihrer Lanze zu, <status>betäubt</status> Ziele {{ stunduration }}&nbsp;<PERSON><PERSON><PERSON> lang, zerstört alle <shield><PERSON><PERSON><PERSON></shield> und verursacht <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RellQ.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON>_Dismount", "name": "Ferromantie: <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Beritten: <PERSON>ll springt ab und schützt sich mit ihrer Rüstung. Gegner werden hochgeschleudert und sie erhält einen starken Schild. Wenn sie zu Fuß unterwegs ist, erhält sie Rüstung, Magieresistenz, Angriffstempo und Angriffsreichweite, ist aber verlangsamt.<br><br><PERSON><PERSON>: Rell erschafft ihr Reittier, erhält einen Lauftemposchub und schleudert den nächsten Gegner, den sie angreift, in die Luft.<br><br>", "tooltip": "<spellPassive>Passiv&nbsp;– Berittene Gewandtheit:</spellPassive> Rell erhält <speed>{{ spell.rellw_dismount:mountedmovespeed }}&nbsp;Lauftempo</speed>, wenn sie mit ihrem Reittier unterwegs ist.<br /><br /><spellActive>Aktiv&nbsp;– Ferromantie: Abspringen:</spellActive> Rell springt von ihrem Reittier ab, <status>schleudert</status> Gegner hoch und fügt ihnen <magicDamage>{{ spell.rellw_dismount:dismountdamage }}&nbsp;magischen <PERSON>haden</magicDamage> zu. Rell erhält einen <shield>Schild</shield> in Höhe von {{ spell.rellw_dismount:shield }}, der bestehen bleibt, bis sie wieder auf ihr Reittier steigt.<br /><br />Dann schützt sich Rell mit ihrer Rüstung. Sie erhält dabei <scaleArmor>{{ spell.rellw_dismount:resistanceincrease*100 }}&nbsp;% zusätzliche Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR>, <attackSpeed>{{ spell.rellw_dismount:dismountedasboost*100 }}&nbsp;% Angriffstempo</attackSpeed> und {{ spell.rellw_dismount:dismountedrangeboost }}&nbsp;Angriffsreichweite. In dieser geschützten Form kann sie <spellName>Ferromantie: Aufsitzen</spellName> einsetzen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Absprungschaden", "Schildstärke", "Wurfschaden", "Passives Lauftempo"], "effect": ["{{ crashdowndamage }} -> {{ crashdowndamageNL }}", "{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ mountupdamage }} -> {{ mountupdamageNL }}", "{{ mountedmovespeed }} -> {{ mountedmovespeedNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RellW_Dismount.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Rell<PERSON>", "name": "Gefährlicher Galopp", "description": "Passiv: <PERSON><PERSON> erh<PERSON>lt Lauftempo außerhalb des Kampfes.<br><br>Aktiv: <PERSON><PERSON> und ein Verbündeter erhalten ansteigendes Lauftempo, das verdoppelt wird, wenn sie sich auf Gegner oder aufeinander zubewegen. Ihr nächster Angriff explodiert und verursacht magischen Schaden.<br>", "tooltip": "<PERSON><PERSON> und ein Verbündeter stürmen vor und erhalten <speed>{{ minms*100 }}&nbsp;% Lauftempo</speed>, das sich auf <speed>{{ maxms*100 }}&nbsp;%</speed> erhöht, wenn sie einem gegnerischen Champion oder sich selbst {{ duration }}&nbsp;Sekunden lang gegenüberstehen. Rells nächster Angriff oder <spellName>Vernichtender Schlag</spellName> explodiert in einem Bereich und verursacht magischen Schaden<magicDamage> in <PERSON>öhe von </magicDamage>{{ maxhealthdamagecalc }} des maximalen Lebens.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ percenthealthdamage*100.000000 }}&nbsp;% -> {{ percenthealthdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "RellE.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RellR", "name": "Magnetsturm", "description": "<PERSON><PERSON> entfesselt einen gewaltigen Magnetsturm, der Gegner in der Nähe ruckartig in ihre Richtung zieht. <PERSON><PERSON> zieht Rell für kurze Zeit kontinuierlich nahe Gegner zu sich heran und fügt ihnen magischen Schaden über Zeit zu.", "tooltip": "<PERSON>ll entfesselt einen gewaltigen Magnetsturm, der Gegner in der Nähe ruckartig in ihre Richtung <status>zieht</status>. Dana<PERSON> <status>zieht</status> Rell kontinuierlich nahe Gegner zu sich heran und fügt ihnen über die nächsten {{ duration }}&nbsp;Sekunden hinweg <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamagepersecond*2.000000 }} -> {{ basedamagepersecondnl*2.000000 }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "RellR.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> und <PERSON>ahl", "description": "Rells Angriffe und Fähigkeiten verursachen zusätzlichen magischen Schaden und rauben bei Treffern Rüstung und Magieresistenz.", "image": {"full": "RellP.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}