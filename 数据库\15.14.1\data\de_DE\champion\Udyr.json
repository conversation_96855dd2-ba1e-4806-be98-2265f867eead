{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Udyr": {"id": "<PERSON><PERSON><PERSON>", "key": "77", "name": "<PERSON><PERSON><PERSON>", "title": "der Geistwanderer", "image": {"full": "Udyr.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "77000", "num": 0, "name": "default", "chromas": false}, {"id": "77001", "num": 1, "name": "Schwarzgurt-Udyr", "chromas": false}, {"id": "77002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "77003", "num": 3, "name": "Geistwächter-Udyr", "chromas": false}, {"id": "77004", "num": 4, "name": "Eindeutig nicht Udyr", "chromas": false}, {"id": "77005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "77006", "num": 6, "name": "Tintenschatten-Udyr", "chromas": true}], "lore": "Udyr ist der mächtigste aller existierenden Geistwanderer und kommuniziert mit den Geistern von Freljord. Er kann sowohl ihre Bedürfnisse spüren als auch ihre ätherische Energie sammeln und in seinen eigenen instinktiven Kampfstil einfließen lassen. Er sucht nach einem inneren Gleichgewicht, damit sein Verstand nicht zwischen anderen verloren geht. Gleichzeitig strebt er auch danach, die Balance seiner Umgebung zu wahren – denn die mystische Landschaft von Freljord kann nur durch Konflikte und Kriege gedeihen. <PERSON><PERSON><PERSON> weiß, dass man Opfer bringen muss, um einer friedlichen Stagnation zuvorzukommen.", "blurb": "Udyr ist der mächtigste aller existierenden Geistwanderer und kommuniziert mit den Geistern von Freljord. Er kann sowohl ihre Bedürfnisse spüren als auch ihre ätherische Energie sammeln und in seinen eigenen instinktiven Kampfstil einfließen lassen. Er...", "allytips": ["„Macht der Schildkröte“ kommt erst nach der Schadensminderung zum Tragen. Deshalb kann der <PERSON><PERSON> von Gegenständen mit defensiven Werten deine Überlebenschance drastisch erhöhen.", "U<PERSON>r ist einer der besten Jungler im Spiel. Du solltest dir diesen Vorteil zu Nutze machen, um deinem Team mehr Erfahrung und mehr Kontrolle über die Karte zu verschaffen."], "enemytips": ["Udyrs Reichweite ist begrenzt, halte also Abstand.", "Nach dem Einsatz der stärkeren, erweckten Version einer Fähigkeit kann Udyr einige Zeit keine weiteren Fähigkeiten erwecken."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 7, "magic": 4, "difficulty": 7}, "stats": {"hp": 664, "hpperlevel": 92, "mp": 271, "mpperlevel": 50, "movespeed": 350, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.65}, "spells": [{"id": "UdyrQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Udyr erhält Angriffstempo und die nächsten beiden Angriffe verursachen zusätzlichen normalen Schaden. Reaktivierung: Udyr erhält noch mehr Angriffstempo und die nächsten beiden Angriffe treffen das Ziel mit Blitzen.", "tooltip": "<spellActive>Klauenhaltung:</spellActive> Udyr erhält <attackSpeed>{{ attackspeedbase*100 }}&nbsp;% Angriffstempo</attackSpeed> und seine Angriffe verursachen {{ attackspeeddurationbase }}&nbsp;Sekunden lang <physicalDamage>{{ onhitdamage }}&nbsp;normalen Schaden</physicalDamage> %i:OnHit% <OnHit>bei Treffern</OnHit>. Außerdem verursachen Udyrs nächste zwei Angriffe in dieser Haltung zusätzlichen <physicalDamage>normalen Schaden</physicalDamage> in Höhe von {{ maxhponhit1 }} des maximalen Lebens und erhalten {{ attackrange }}&nbsp;Reichweite.<br /><br /><keywordMajor>Erweckt:</keywordMajor> Das zusätzliche <attackSpeed>Angriffstempo</attackSpeed> wird erhöht auf <attackSpeed>{{ empoweredtotalas }}</attackSpeed> und der Schaden basierend auf maximalem Leben wird erhöht auf <physicalDamage>{{ q2totalonhithpdamage }}</physicalDamage>. Udyrs nächste zwei Angriffe treffen zudem sechsmal mit Blitzen, die isolierten Gegnern insgesamt <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ empoweredlightningbonusmax }} des maximalen Lebens zufügen (falls möglich springen Treffer auf andere Ziele in der Nähe über).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "<PERSON><PERSON><PERSON> basierend auf maximalem Leben (%)", "Trefferschaden"], "effect": ["{{ attackspeedbase*100.000000 }}&nbsp;% -> {{ attackspeedbasenl*100.000000 }}&nbsp;%", "{{ maxhponhitbase*100.000000 }}&nbsp;% -> {{ maxhponhitbasenl*100.000000 }}&nbsp;%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [20, 20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UdyrW", "name": "Eisenmantel", "description": "Udyr erhält einen Schild und die nächsten beiden Angriffe heilen ihn. Reaktivierung: Udyr erhält einen noch größeren Schild und heilt sich abhängig von seinem maximalen Leben über die nächsten paar Sekunden hinweg.", "tooltip": "<spellPassive>Schu<PERSON>haltung:</spellPassive> Udyr erhält {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ totalshield }}. Außerdem erhalten seine nächsten zwei Angriffe {{ lifesteal*100 }}&nbsp;% <PERSON><PERSON><PERSON>raub und stellen <healing>{{ lifeonhit }}&nbsp;<PERSON><PERSON></healing> wieder her.<br /><br /><keywordMajor>Erweckt:</keywordMajor> Udyr erhält einen <shield>Schild</shield> in Höhe von {{ recastshield }} und stellt über {{ shieldduration }}&nbsp;Sekunden hinweg <healing>{{ recastheal }}&nbsp;Leben</healing> wieder her. Udyrs Angriffe erhalten stattdessen {{ lifesteal*200 }}&nbsp;% Lebensraub und stellen <healing>{{ lifeonhitawakened }}&nbsp;<PERSON><PERSON></healing> wieder her.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "% <PERSON><PERSON><PERSON> vom Leben", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ shieldpercenthealth*100.000000 }}&nbsp;% -> {{ shieldpercenthealthnl*100.000000 }}&nbsp;%", "{{ lifesteal*100.000000 }}&nbsp;% -> {{ lifestealnl*100.000000 }}&nbsp;%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "UdyrW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UdyrE", "name": "Flammende Stampede", "description": "Udyr erhält Lauftempo und sein erster Angriff gegen jedes Z<PERSON> betä<PERSON>t dieses. Reaktivierung: Udyr erhält ein paar Sekunden lang noch mehr Lauftempo sowie Immunität gegen bewegungsunfähig machende Effekte. ", "tooltip": "<spellActive>Stampedenhaltung:</spellActive> Udyr erhält <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed>, das über {{ movespeedduration }}&nbsp;Sekunden hinweg abfällt. Außerdem springt Udyr bei seinen Angriffen zum Ziel und <status>betäubt</status> es {{ stunduration }}&nbsp;Sekunden lang ({{ icd }}&nbsp;Sekunden Abklingzeit pro Ziel).<br /><br /><keywordMajor>Erweckt:</keywordMajor> Gewährt {{ unstoppableduration }}&nbsp;Sekunden lang Immunität gegen <status>bewegungsunfähig</status> und <status>kampfunfähig</status> machende Effekte und <speed>{{ movespeedbonus }}&nbsp;Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "Abklingzeit pro Einheit"], "effect": ["{{ movespeed*100.000000 }}&nbsp;% -> {{ movespeednl*100.000000 }}&nbsp;%", "{{ icd }} -> {{ icdNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UdyrR", "name": "Sturm der Schwingen", "description": "<PERSON><PERSON>r umgibt sich mit einem Eissturm, der nahen Gegnern Schaden zufügt und sie verlangsamt. Reaktivierung: Der Sturm wird stärker und verfolgt <PERSON>, um ihnen zusätzlichen Schaden zuzufügen.", "tooltip": "<spellActive>Sturmhaltung:</spellActive> Udyr umgibt sich {{ buffduration }}&nbsp;Sekunden lang mit einem Eissturm, der Gegnern in der Nähe <magicDamage>{{ stormdamage }}&nbsp;magischen Schaden</magicDamage> pro Sekunde zufügt und sie um {{ slowpotency*100 }}&nbsp;% <status>verlangsamt</status>. Außerdem fügen seine nächsten zwei Angriffe in dieser Haltung Gegnern innerhalb des Sturms <magicDamage>{{ pulsedamage }}&nbsp;magischen Schaden</magicDamage> zu.<br /><br /><keywordMajor>Erweckt:</keywordMajor> Udyr sendet den Sturm aus, um den zuletzt angegriffenen Gegner zu verfolgen, ihm über die Dauer zusätzlich <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ percenthpblast }} seines maximalen Lebens zuzufügen und ihn um weitere {{ empoweredslow }} zu <status>verlangsamen</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde", "Verlangsamung"], "effect": ["{{ stormbasedamage }} -> {{ stormbasedamageNL }}", "{{ slowpotency*100.000000 }}&nbsp;% -> {{ slowpotencynl*100.000000 }}&nbsp;%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [370, 370, 370, 370, 370, 370], "rangeBurn": "370", "image": {"full": "UdyrR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Udyr verfügt über vier Grundfähigkeiten, die zwischen Haltungen wechseln. Wenn er eine Fähigkeit reaktiviert, wird sie mit Ult-Vorteilen erneuert. Nach dem Einsatz einer Fähigkeit erhöht sich außerdem das Angriffstempo von Udyrs nächsten zwei Angriffen.", "image": {"full": "Udyr_P.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}