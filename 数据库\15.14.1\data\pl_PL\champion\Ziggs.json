{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ziggs": {"id": "<PERSON><PERSON>", "key": "115", "name": "<PERSON><PERSON>", "title": "Hextechowy Saper", "image": {"full": "Ziggs.png", "sprite": "champion5.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "115000", "num": 0, "name": "default", "chromas": false}, {"id": "115001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115002", "num": 2, "name": "Major <PERSON>", "chromas": false}, {"id": "115003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115007", "num": 7, "name": "<PERSON><PERSON> z Odysei", "chromas": true}, {"id": "115014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "115023", "num": 23, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115024", "num": 24, "name": "BZZZiggs", "chromas": true}, {"id": "115033", "num": 33, "name": "<PERSON>iggs La Ilusión", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, mił<PERSON><PERSON><PERSON> dużych bomb i krótkich lontów, jest chodzącym wybuchowym kataklizmem. Będąc asystentem wynalazcy w Piltover, stał się znudzony swoim przepełnionym rutyną życiem, więc zaprzyjaźnił się z szaloną niebieskowłosą wariatką z bombami zwaną Jinx. Po szalonej nocy na mieście, <PERSON><PERSON> posłuchał się jej i przeprowadził do Zaun, gdzie teraz może swobodnie zgłębiać swoje pasje, terroryzując po równo chembaronów i zwykłych obywateli, by da<PERSON> <PERSON><PERSON> swo<PERSON>j żądzy wybuchów.", "blurb": "<PERSON><PERSON><PERSON>, mił<PERSON><PERSON><PERSON> dużych bomb i krótkich lontów, jest chodzącym wybuchowym kataklizmem. Będąc asystentem wynalazcy w Piltover, stał się znudzony swoim przepełnionym rutyną życiem, więc zaprzyjaźnił się z szaloną niebieskowłosą wariatką z bombami...", "allytips": ["Nawet jeśli znajdujesz się daleko od bitwy, moż<PERSON>z pomóc sojusznikom Megapiekielną Bombą.", "Spowolnienie wrogów Hextechowymi Minami ułatwia trafienie resztą umiejętności.", "Odbicie się siłą wybuchu Ładunku Kumulacyjnego, aby przelecieć nad przeszkodą, to dobry manewr przy pogoni lub ucieczce przed wrogami. "], "enemytips": ["Nie wchodź na miny Ziggsa! Spowolnią cię i ułatwią mu trafienie cię innymi umiejętnościami.", "Wiele umiejętności Ziggsa ma długi czas odnowienia. Postaraj się go zaatakować wkrótce po tym, jak ich użyje.", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Megapie<PERSON>lna Bomba, zadaje większe obrażenia w epicentrum eksplozji."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 4}, "stats": {"hp": 606, "hpperlevel": 106, "mp": 480, "mpperlevel": 23.5, "movespeed": 325, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2, "attackspeed": 0.656}, "spells": [{"id": "ZiggsQ", "name": "Skacząca Bomba", "description": "<PERSON>iggs rzuca skaczącą bombę, która zadaje obrażenia magiczne.", "tooltip": "<PERSON><PERSON> rzuca s<PERSON> bomb<PERSON>, kt<PERSON>ra zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Całkowite skalowanie z mocą umiejętności", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio }} -> {{ apratioNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [850, 850, 850, 850, 850], [325, 325, 325, 325, 325], [225, 225, 225, 225, 225], [180, 180, 180, 180, 180], [240, 240, 240, 240, 240], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "850", "325", "225", "180", "240", "70", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "ZiggsQ.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZiggsW", "name": "Ładunek Kumulacyjny", "description": "<PERSON>iggs ciska ładunkiem wybuchowym, kt<PERSON><PERSON> eksploduje po niedługim opóźnieniu lub po ponownym użyciu umiejętności. Eksplozja zadaje wrogom obrażenia magiczne, odr<PERSON><PERSON><PERSON><PERSON><PERSON> ich. <PERSON><PERSON> także zostaje odrzucony, jed<PERSON><PERSON> nie otrzymuje obrażeń. <br><br><PERSON><PERSON> może wykorzystać Ładunek, aby heksplodować wieże o naruszonej konstrukcji.", "tooltip": "<PERSON>iggs ciska ładunkiem wybuchowym, kt<PERSON><PERSON> eksploduje po {{ bombduration }} sek. lub po <recast>ponownym użyciu</recast>. Zadaje wrogom <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>odr<PERSON><PERSON></status> ich. <PERSON>iggs także zostaje odrzucony, jednak nie otrzymuje obrażeń.<br /><br />Ładunek Kumulacyjny automatycznie zniszczy wieże, które mają mniej niż {{ turretdestroypercent*100 }}% zdrowia.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia", "Próg zniszczenia wieży"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ turretdestroypercent*100.000000 }}% -> {{ turretdestroypercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "ZiggsW.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZiggsE", "name": "Hextechowe Miny", "description": "<PERSON>iggs podkłada miny zbliżeniowe, kt<PERSON>re wybuchają, g<PERSON> zost<PERSON> dotknięte przez wrogów, zadając obrażenia magiczne i spowalniając ich. Każda kolejna mina, której wybuch obejmie ten sam cel, zada mniejsze obrażenia.", "tooltip": "<PERSON><PERSON> rozrzuca miny z<PERSON>e, k<PERSON><PERSON><PERSON> wybuchają, g<PERSON> zost<PERSON> dotknięte przez wrogów, zada<PERSON><PERSON><PERSON> im <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>spowal<PERSON><PERSON>c</status> ich o {{ slow*-100 }}% na {{ e4 }} sek. Miny z<PERSON>aj<PERSON> po {{ e3 }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia miny", "Całkowite skalowanie z mocą umiejętności", "Spowolnienie", "Koszt (@AbilityResourceName@)"], "effect": ["{{ damagepermine }} -> {{ damagepermineNL }}", "{{ apratiopermine }} -> {{ apratiopermineNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [10, 10, 10, 10, 10], [1.5, 1.5, 1.5, 1.5, 1.5], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [135, 135, 135, 135, 135], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "10", "1.5", "0.4", "0", "135", "150", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZiggsE.png", "sprite": "spell17.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZiggsR", "name": "Megapiekielna Bomba", "description": "Ziggs ciska swoje arcydzieło, <PERSON><PERSON><PERSON><PERSON><PERSON>ę, na znaczną odległość. Wrogowie w epicentrum wybuchu otrzymują większe obrażenia od tych bardziej oddalonych.", "tooltip": "<PERSON><PERSON> ciska swoim <PERSON>, zadając <magicDamage>{{ empowereddamage }} pkt. obrażeń magicznych</magicDamage> w centrum wybuchu lub <magicDamage>{{ blastdamage }} pkt. obrażeń magicznych</magicDamage> na jego krawędzi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia w epicentrum wybuchu", "Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*0.667000 }} -> {{ basedamagenl*0.667000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 95, 70], "cooldownBurn": "120/95/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [300, 450, 600], [66.6667, 66.6667, 66.6667], [525, 525, 525], [250, 250, 250], [200, 300, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "300/450/600", "66.67", "525", "250", "200/300/400", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "ZiggsR.png", "sprite": "spell17.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Co jakiś czas następny podstawowy atak Ziggsa zadaje dodatkowe obrażenia magiczne. Czas ten zostaje skrócony, <PERSON><PERSON><PERSON><PERSON> użyje umiejętności.", "image": {"full": "ZiggsPassiveReady.png", "sprite": "passive5.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}