{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "<PERSON><PERSON><PERSON>", "title": "das Herz Freljords", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "201002", "num": 2, "name": "El Tigre Braum", "chromas": false}, {"id": "201003", "num": 3, "name": "Braum Löwenherz", "chromas": false}, {"id": "201010", "num": 10, "name": "Weihnachts-Braum", "chromas": false}, {"id": "201011", "num": 11, "name": "Gang<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "201024", "num": 24, "name": "Zuckerschock-B<PERSON>um", "chromas": true}, {"id": "201033", "num": 33, "name": "Poolparty-B<PERSON>um", "chromas": true}, {"id": "201042", "num": 42, "name": "Grill<PERSON>", "chromas": true}], "lore": "B<PERSON><PERSON>, der geliebte Held Freljords ist mit massivem Bizeps und einem noch größeren Herzen gesegnet. In allen Methallen nördlich des Frostklamms wird auf seine legendäre Stärke getrunken. Legenden über ihn besagen, dass er einen ganzen Eichenwald in einer einzigen Nacht gefällt und einen ganzen Berg zu Schutt geschlagen haben soll. Mit seinem verzauberten Schild, der einst die Tür einer uralten Schatzkammer war, durchstreift Braum den frostigen Norden und setzt dabei stets ein schnauzbärtiges Lächeln auf, das so groß wie seine Muskeln ist – er ist ein wahrer Freund, für alle die Hilfe brauchen.", "blurb": "<PERSON><PERSON><PERSON>, der geliebte Held Freljords ist mit massivem Bizeps und einem noch größeren Herzen gesegnet. In allen Methallen nördlich des Frostklamms wird auf seine legendäre Stärke getrunken. Legenden über ihn besagen, dass er einen ganzen Eichenwald in...", "allytips": ["Arbeite mit deinen Verbündeten zusammen, um Steigerungen von „Erschütternde Hiebe“ aufzubauen. <PERSON><PERSON><PERSON><PERSON> sie, markierte Ziele mit normalen Angriffen anzugreifen.", "Stelle dich vor einen verwundbaren Freund und schirme ihn mit dem Schild von „Unverwüstlich“ vor Projektilen ab.", "„Gletscherspalte“ hinterlässt ein mächtiges Verlangsamungsfeld. Positioniere es mit Bedacht, um Teamkämpfe auseinanderzuziehen und den Ansturm des Gegners zu verlangsamen."], "enemytips": ["Braum muss „<PERSON><PERSON><PERSON>“ oder einen normalen Angriff ausführen, um „Erschütternde Hiebe“ zu starten. Solltest du markiert werden, gehe aus der Kampfzone, bevor du noch 3-mal getroffen wirst. So vermeidest du eine Betäubung.", "Braums ultimative Fähigkeit hat eine lange Ausführzeit, die du zum Ausweichen nutzen kannst. Wenn du den vereisten Boden betrittst, den die Fähigkeit hinterlässt, wirst du verlangsamt. Positioniere dich also so, dass du nicht hindurchlaufen musst.", "Durch „Unverwüstlich“ verteidigt er sich in eine bestimmte Richtung. Du wartest besser ab oder umgehst die Fähigkeit."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "Winterbiss", "description": "Braum feuert von seinem Schild gefrierendes Eis ab, verlangsamt und verursacht magischen Schaden.<br><br>Erzeugt 1&nbsp;Steigerung <font color='#FFF673'>„Erschütternde Hiebe“</font>.", "tooltip": "Braum feuert von seinem Schild gefrorenes Eis ab, das dem ersten getroffenen Gegner <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>haden</magicDamage> zufügt und ihn um {{ e2 }}&nbsp;% <status>verlangsamt</status>. Dieser Effekt fällt im Verlauf von {{ e5 }}&nbsp;Sekunden ab.<br /><br />Bewirkt 1&nbsp;Steigerung <keywordMajor>Erschütternde Hiebe</keywordMajor>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumW", "name": "<PERSON><PERSON> dich hinter mich", "description": "Braum springt zu einem anvisierten verbündeten Champion oder Vasallen. Nach der Landung erhalten er und der Verbündete einige Sekunden lang Rüstung und Magieresistenz.", "tooltip": "Braum springt zu einem verbündeten Champion oder Vasallen. Bei der Landung gewährt Braum dem Ziel {{ e1 }}&nbsp;Sekunden lang <scaleArmor>{{ grantedallyarmor }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ grantedallymr }}&nbsp;Magieresistenz</scaleMR>. Braum selbst erhält währenddessen <scaleArmor>{{ grantedbraumarmor }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ grantedbraummr }}&nbsp;Magieresistenz</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ baseresists }} -> {{ baseresistsNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Braum hebt seinen Schild einige Sekunden lang in eine Richtung und fängt alle Geschosse ab, die ihn daraufhin treffen und zerstört werden. Er negiert den Schaden des ersten Angriffs vollständig und verringert den Schaden aller nachfolgenden Angriffe aus dieser Richtung.", "tooltip": "Braum hebt {{ e2 }}&nbsp;Sekunden lang seinen Schild und fängt gegnerische Geschosse aus der gewählten Richtung ab, wodurch sie Braum treffen und danach zerstört werden. Das erste von Braum geblockte Geschoss verursacht keinen Schaden, weitere Geschosse verursachen um {{ e3 }}&nbsp;% verringerten Schaden.<br /><br />Braum erhält <speed>{{ e4 }}&nbsp;% Lauftempo</speed>, während sein Schild angehoben ist.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Schadensverringerung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}&nbsp;% -> {{ e3NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumRWrapper", "name": "Gletscherspalte", "description": "Braum rammt seinen Schild in den Boden und schleudert Gegner in der Nähe hoch, die in einer Linie vor ihm stehen. Vor ihm bleibt ein Riss im Boden zurück, der Gegner verlangsamt.", "tooltip": "Braum schlägt auf den Boden und sendet einen Riss aus, der Gegner auf seinem Weg und in der Nähe von Braum <status>hochschleudert</status> und <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Das erste getroffene Ziel wird {{ minknockup }} bis {{ maxknockup }}&nbsp;Sekunden lang <status>hochgeschleudert</status> (die Dauer erhöht sich mit der Entfernung von Braum). Alle anderen getroffenen Ziele werden {{ minknockup }}&nbsp;Sekunden lang <status>hochgeschleudert</status>.<br /><br />Der Riss erzeugt außerdem einen Bereich, der {{ slowzoneduration }}&nbsp;Sekunden lang bestehen bleibt und Gegner um {{ movespeedmod }}&nbsp;% <status>verlangsamt</status>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Dauer des Hochschleuderns", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ firstknockupduration }} -> {{ firstknockupdurationNL }}", "{{ movespeedmod }}&nbsp;% -> {{ movespeedmodNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Erschütternde Hiebe", "description": "Braums normale Angriffe erzeugen „Erschütternde Hiebe“. Sobald die erste Steigerung erzeugt ist, bauen auch die normalen Angriffe <font color='#FFF673'>Verbündeter</font> Steigerungen von „Erschütternde Hiebe“ auf. <br><br>Sind 4 Steigerungen erreicht, wird das Ziel betäubt und erleidet magischen Schaden. In den nächsten Sekunden erhält es keine weiteren Steigerungen und erleidet zusätzlichen magischen Schaden durch Braums Angriffe.", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}