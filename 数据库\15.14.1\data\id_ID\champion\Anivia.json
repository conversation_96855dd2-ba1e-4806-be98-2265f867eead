{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Anivia": {"id": "An<PERSON><PERSON>", "key": "34", "name": "An<PERSON><PERSON>", "title": "the Cryophoenix", "image": {"full": "Anivia.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "34000", "num": 0, "name": "default", "chromas": false}, {"id": "34001", "num": 1, "name": "Team Spirit Anivia", "chromas": false}, {"id": "34002", "num": 2, "name": "Bird of Prey Anivia", "chromas": false}, {"id": "34003", "num": 3, "name": "Noxus Hunter Anivia", "chromas": false}, {"id": "34004", "num": 4, "name": "Hextech Anivia", "chromas": false}, {"id": "34005", "num": 5, "name": "Blackfrost Anivia", "chromas": false}, {"id": "34006", "num": 6, "name": "Prehistoric Anivia", "chromas": false}, {"id": "34007", "num": 7, "name": "Festival Queen <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "34008", "num": 8, "name": "Papercraft Anivia", "chromas": true}, {"id": "34017", "num": 17, "name": "Cosmic Flight Anivia", "chromas": true}, {"id": "34027", "num": 27, "name": "Divine Phoenix Anivia", "chromas": true}, {"id": "34037", "num": 37, "name": "Bewitching Batnivia", "chromas": true}, {"id": "34046", "num": 46, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Anivia adalah spirit bersa<PERSON><PERSON> baik hati yang bertahan menjalani siklus kehid<PERSON>, k<PERSON><PERSON>, dan kela<PERSON>an kembali yang tanpa henti, demi melindungi <PERSON>. Manusia setengah dewa yang terlahir dari es ini tak kenal ampun dan dingin. Dengan kekuatan elemen itu, dia akan menggagalkan siapa pun yang berani mengganggu tanah kelahirannya. Anivia menuntun dan melindungi suku utara yang keras. Mereka menghormatinya sebagai simbol harapan dan pertanda perubahan besar. Berjuang dengan segenap jiwa raganya, dia tahu bahwa lewat pengorbanannya, ingata<PERSON>a akan abadi, dan dia akan terlahir kembali di hari esok yang baru.", "blurb": "Anivia adalah spirit bersa<PERSON>p baik hati yang bertahan men<PERSON>lani sik<PERSON> keh<PERSON>, k<PERSON><PERSON>, dan kela<PERSON>an kembali yang tanpa henti, demi melind<PERSON><PERSON>. Man<PERSON>ia setengah dewa yang terlahir dari es ini tak kenal ampun dan dingin. Dengan kekuatan...", "allytips": ["<PERSON>gatur waktu Flash Frost dengan <PERSON>bite bisa menghasilkan kombo mematikan.", "<PERSON><PERSON><PERSON> sangat butuh Mana untuk Glacial Storm. Coba beli item dengan <PERSON>a atau ambil buff Crest of the Ancient Golem di Summoner's Rift.", "Champion musuh akan sulit membunuh telurnya di early game. Manfaatkan kesempatan ini dengan bermain agresif."], "enemytips": ["Coba lakukan gank pada Anivia saat dia melakukan laning. <PERSON><PERSON><PERSON> be<PERSON> orang, akan lebih mudah memastikan telurnya mati.", "Jika kamu memainkan champion ranged, pastikan kamu cukup jauh dari <PERSON><PERSON><PERSON> agar kamu bisa lebih mudah menghindari <PERSON>.", "Coba lawan <PERSON>. Di jungle dia bisa memblokir jalur dengan rank Crystallize yang lebih rendah."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 4, "magic": 10, "difficulty": 10}, "stats": {"hp": 550, "hpperlevel": 92, "mp": 495, "mpperlevel": 45, "movespeed": 325, "armor": 21, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.68, "attackspeed": 0.658}, "spells": [{"id": "FlashFrost", "name": "<PERSON>", "description": "Anivia menyatukan kedua sayapnya dan memanggil bola es yang terbang ke arah lawan, menerapkan chill dan memberikan damage ke siapa pun yang dilewati. Saat meledak, bola es memoderasi damage dalam radius, menerapkan stun ke siapa pun di area ini.", "tooltip": "Anivia men<PERSON>n bong<PERSON><PERSON> es besar, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totalpassthroughdamage }} magic damage</magicDamage> dan menerapkan <keywordMajor>Chill</keywordMajor> pada musuh selama {{ slowduration }} detik, menerapkan <status>Slow</status> sebesar {{ spell.glacialstorm:slowamount }}%. Di ujung range, es akan meledak, menerapkan <status>Stun</status> pada musuh selama {{ stunduration }} detik dan menghasilkan <magicDamage>{{ totalexplosiondamage }} magic damage</magicDamage>.<br /><br />Anivia bisa <recast>Recast</recast> ability ini untuk meledakkannya lebih awal saat es melayang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage Led<PERSON>n", "<PERSON><PERSON><PERSON>:", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ passthroughbasedamage }}-> {{ passthroughbasedamageNL }}", "{{ explosionbasedamage }}-> {{ explosionbasedamageNL }}", "{{ stunduration }}-> {{ stundurationNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "FlashFrost.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Crystallize", "name": "Crystallize", "description": "Anivia mengubah kelembapan di udara menjadi tembok es yang tidak bisa dilewati, menghalangi semua pergerakan. Tembok itu hanya bertahan sebentar sebelum mencair.", "tooltip": "Anivia memanggil tembok es seluas {{ e2 }} unit. Tembok itu bertahan selama {{ e1 }} detik sebelum mencair.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ e2 }}-> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [17, 17, 17, 17, 17], "cooldownBurn": "17", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [5, 5, 5, 5, 5], [400, 500, 600, 700, 800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5", "400/500/600/700/800", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "Crystallize.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Frostbite", "name": "Frostbite", "description": "<PERSON>gan kepakan say<PERSON>, <PERSON><PERSON><PERSON> damage dengan meledakkan embusan angin pembeku ke target. Jika target baru saja terkena Flash Frost atau menerima damage dari Glacial Storm sempurna, menerima damage sebesar 2 kali lipat.", "tooltip": "<PERSON><PERSON><PERSON> musuh dengan angin pembeku, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage>. Ter<PERSON>ap musuh yang terkena <keywordMajor>Chill</keywordMajor>, <PERSON><PERSON><PERSON> <magicDamage>{{ empowereddamage }} magic damage</magicDamage> sebagai gantinya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Frostbite.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GlacialStorm", "name": "Glacial Storm", "description": "Anivia memanggil badai hujan es dan melancarkan damage sekaligus menerapkan slow pada musuhnya.", "tooltip": "<toggle>Toggle</toggle>: <PERSON><PERSON><PERSON> memanggil badai hujan es yang memberikan efek <status>Slow</status> pada musuh sebesar {{ slowamount }}% dan menghasilkan <magicDamage>{{ totaldamagepersecond }} magic damage per detik</magicDamage>. Ukuran badai akan membesar dalam {{ growthtime }} detik.<br /><br />Saat terbentuk sempurna, badai akan memberikan <keywordMajor>Chill</keywordMajor>, <status>Slow</status> sebesar {{ slowpercentempoweredtt }}%, juga menghasilkan <magicDamage>{{ empowereddamagepersecondtooltiponly }} magic damage per detik</magicDamage> sebagai gantinya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per <PERSON>", "Slow", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cooldown"], "effect": ["{{ damagepersecond }}-> {{ damagepersecondNL }}", "{{ slowamount }}%-> {{ slowamountNL }}%", "{{ slowpercentempoweredtt }}%-> {{ slowpercentempoweredttNL }}%", "{{ manacostpersecond }}-> {{ manacostpersecondNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [4, 3, 2], "cooldownBurn": "4/3/2", "cost": [60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " + {{ manacostpersecond }} Mana per detik", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "GlacialStorm.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} + {{ manacostpersecond }} Mana per detik"}], "passive": {"name": "Rebirth", "description": "Setelah menerima damage fatal, <PERSON><PERSON><PERSON> kembali ke bentuk telur dan terlahir kembali dengan health penuh.", "image": {"full": "Anivia_P.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}