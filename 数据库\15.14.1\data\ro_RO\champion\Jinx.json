{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jinx": {"id": "<PERSON><PERSON>", "key": "222", "name": "<PERSON><PERSON>", "title": "dezastrul ambulant", "image": {"full": "Jinx.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "222000", "num": 0, "name": "default", "chromas": false}, {"id": "222001", "num": 1, "name": "<PERSON><PERSON> din Orașul crimelor", "chromas": false}, {"id": "222002", "num": 2, "name": "<PERSON>x cu dragonul de foc", "chromas": true}, {"id": "222003", "num": 3, "name": "Jinx ucigașă de zombi", "chromas": false}, {"id": "222004", "num": 4, "name": "<PERSON><PERSON>, Magia Stelelor", "chromas": true}, {"id": "222012", "num": 12, "name": "<PERSON><PERSON>, s<PERSON>rid<PERSON>ș ambițios", "chromas": false}, {"id": "222013", "num": 13, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "222020", "num": 20, "name": "PROIECT: Jinx", "chromas": true}, {"id": "222029", "num": 29, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "222037", "num": 37, "name": "Jinx, inamica din Arcane", "chromas": false}, {"id": "222038", "num": 38, "name": "<PERSON><PERSON>, pisicuța bătăușă", "chromas": true}, {"id": "222040", "num": 40, "name": "<PERSON><PERSON>, pisicuța bătăușă (Prestigiu)", "chromas": false}, {"id": "222051", "num": 51, "name": "<PERSON><PERSON> din Cafeneaua adorabilă", "chromas": true}, {"id": "222060", "num": 60, "name": "<PERSON><PERSON> din Arcane fracturată", "chromas": false}, {"id": "222062", "num": 62, "name": "Jinx T1", "chromas": false}], "lore": "O criminală nebună și impulsivă din orașul de jos, Jinx e chinuită de consecințele trecutului ei – dar asta n-o oprește să dezlănțuie haosul în Piltover și Zaun. Își folosește arsenalul creat pentru a provoca efecte devastatoare, de<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> torenți de explozii colorate și focuri de gloanțe, iar nebunia ei îi inspiră pe cei lipsiți de drepturi și privilegii să se revolte și să reziste oprimării.", "blurb": "O criminală nebună și impulsivă din orașul de jos, Jinx e chinuită de consecințele trecutului ei – dar asta n-o oprește să dezlănțuie haosul în Piltover și Zaun. Își folosește arsenalul creat pentru a provoca efecte devastatoare, dez<PERSON><PERSON>n<PERSON><PERSON>d torenți de...", "allytips": ["Rachetele nu sunt întotdeauna cea mai bună alegere! Mitraliera lui Jinx este incredibil de puternică atunci când ajunge la viteză maximă. Apelează la ea ori de câte ori un campion inamic se apropie prea mult.", "Rachetele lui Jinx provoacă daune complete tuturor inamicilor în momentul exploziei. Folosește rachetele asupra minionilor de pe culoar pentru a lovi campionii inamici aflați în apropierea lor fără a atrage atacurile minionilor.", "Când izbucnește o luptă, încearcă să stai la margine și să ataci de la distanță cu rachetele și ''Zap!''. Nu intra în luptă cu mitraliera decât atunci când consideri că e sigur să faci asta."], "enemytips": ["Durează ceva timp pentru ca mitraliera lui Jinx să ajungă la capacitate maximă. Dacă o vezi hăr<PERSON>uind cu rachete, încearcă să te arunci asupra ei și să-i provoci daune.", "Daunele provocate de abilitatea supremă a lui <PERSON>x sunt cu atât mai mici cu cât ești mai aproape de ea.", "Grenadele-capcană ale lui Jinx au timp de reactivare mare și sunt principala ei modalitate de a se proteja. Dacă ratează atunci când le folosește, îi va fi greu să scape dacă este atacată. "], "tags": ["Marksman"], "partype": "Mană", "info": {"attack": 9, "defense": 2, "magic": 4, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 105, "mp": 260, "mpperlevel": 50, "movespeed": 325, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.75, "hpregenperlevel": 0.5, "mpregen": 6.7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.25, "attackspeedperlevel": 1.4, "attackspeed": 0.625}, "spells": [{"id": "JinxQ", "name": "Schimbăm foaia!", "description": "Jinx își modifică atacurile de bază comutând între mitraliera ''Pac-Pac'' și lansatorul de rachete ''Bubuilă''. Atacurile cu ''Pac-Pac'' oferă viteză de atac, în timp ce atacurile cu ''B<PERSON>uilă'' provoacă daune AoE, au rază mai mare, însă consumă mană și sunt mai lente.", "tooltip": "Jinx își schimbă armele între lansatorul de rachete ''Bubuilă'' și mitraliera ''Pac-Pac''.<br /><br /><PERSON><PERSON>d folosește lansatorul de rachete, atacurile lui Jinx le provoacă <physicalDamage>{{ rocketdamage }} daune fizice</physicalDamage> țintei și inamicilor din apropiere, au raza de atac mai mare cu {{ rocketbonusrange }} unități, consumă mană și cresc cu {{ rocketaspdpenalty*100 }}% mai puțin în funcție de viteza de atac bonus.<br /><br /><PERSON><PERSON><PERSON> folosește mitraliera, atacurile lui Jinx îi oferă <attackSpeed>vitez<PERSON> de atac</attackSpeed> timp de {{ minigunattackspeedduration }} secunde, cumulându-se de până la {{ minigunattackspeedstacks }} ori (maximum <attackSpeed>+{{ minigunattackspeedmax }}% %i:scaleAS%</attackSpeed>){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rază <PERSON> pentru rachetă", "Viteză de atac totală pentru mitralieră"], "effect": ["{{ rocketbonusrange }} -> {{ rocketbonusrangeNL }}", "{{ minigunattackspeedmax }}% -> {{ minigunattackspeedmaxNL }}%"]}, "maxrank": 5, "cooldown": [0.9, 0.9, 0.9, 0.9, 0.9], "cooldownBurn": "0.9", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mană per rachetă", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JinxQ.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} mană per rachetă"}, {"id": "JinxW", "name": "Zap!", "description": "Jinx <PERSON>i folosește pistolul electric ''Zapper'' pentru a trage un proiectil care îi provoacă daune primului inamic lovit, îl încetinește și îl dezvăluie.", "tooltip": "Jinx la<PERSON>ază un șoc electric care îi provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> primului inamic lovit, <status>încetinindu-l</status> cu {{ slowpercent }}% și dezvăluindu-l timp de {{ slowduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "JinxW.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxE", "name": "Fălci de foc!", "description": "Jinx arun<PERSON>ă o serie de grenade-capcană ce explodează după 5 secunde, incendiind inamicii. ''Fălcile de foc'' vor țintui inamicii care calcă pe ele.", "tooltip": "Jinx aruncă 3 fălci care rezistă timp de {{ grenadeduration }} secunde. Acestea explodează la contactul cu campionii inamici, <status>țintuindu-i</status> timp de {{ rootduration }} secunde și provocându-le <magicDamage>{{ totaldamage }} daune magice</magicDamage> inamicilor din apropiere.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 20.5, 17, 13.5, 10], "cooldownBurn": "24/20.5/17/13.5/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "JinxE.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxR", "name": "Super mega racheta morții!", "description": "Jinx trage cu o super-rachetă care câștigă daune pe măsură ce înaintează. Racheta va exploda la impactul cu un campion inamic, provocându-i daune acestuia și inamicilor din jur, în funcție de viața lor lipsă.", "tooltip": "Jinx lansează o rachetă care explodează la contactul cu primul campion inamic lovit, provocându-i <physicalDamage>daune fizice în valoare de între {{ damagefloor }} și {{ damagemax }} + {{ percentdamage }}% din viața lipsă</physicalDamage>, a căror valoare crește de-a lungul primei secunde de zbor. Inamicii din apropiere suferă {{ aoedamagemult*100 }}% din daune.<br /><br /><rules>Daunele din viața lipsă nu pot depăși {{ monsterexecutemax }} împotriva monștrilor.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune minime", "Daune maxime", "Pro<PERSON> daune via<PERSON><PERSON>", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ percentdamage }}% -> {{ percentdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [85, 65, 45], "cooldownBurn": "85/65/45", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JinxR.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Entuziasmează-te!", "description": "Viteza de mișcare și viteza de atac ale lui Jinx cresc foarte mult de fiecare dată când ucide sau participă la uciderea unui campion sau a unui monstru epic din junglă sau când ajută la distrugerea unei structuri.", "image": {"full": "Jinx_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}