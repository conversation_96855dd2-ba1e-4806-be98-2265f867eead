{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pyke": {"id": "<PERSON><PERSON>", "key": "555", "name": "파이크", "title": "핏빛 항구의 학살자", "image": {"full": "Pyke.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "555000", "num": 0, "name": "default", "chromas": false}, {"id": "555001", "num": 1, "name": "모래유령 파이크", "chromas": true}, {"id": "555009", "num": 9, "name": "핏빛달 파이크", "chromas": true}, {"id": "555016", "num": 16, "name": "프로젝트: 파이크", "chromas": true}, {"id": "555025", "num": 25, "name": "초능력특공대 파이크", "chromas": true}, {"id": "555034", "num": 34, "name": "감시자 파이크", "chromas": true}, {"id": "555044", "num": 44, "name": "재의 기사 파이크", "chromas": true}, {"id": "555045", "num": 45, "name": "창공 파이크", "chromas": true}, {"id": "555053", "num": 53, "name": "소울 파이터 파이크", "chromas": true}, {"id": "555054", "num": 54, "name": "프레스티지 소울 파이터 파이크", "chromas": false}, {"id": "555064", "num": 64, "name": "공포의 밤 파이크", "chromas": true}, {"id": "555074", "num": 74, "name": "먹그림자 파이크", "chromas": true}], "lore": "빌지워터 학살의 부두에서 유명한 작살잡이였던 파이크는 거대한 자울치의 뱃속에서 죽음을 맞이할 운명이었지만... 살아 돌아왔다. 이제, 그는 옛 고향의 음습한 골목과 뒷길을 소리 없이 누비며, 타인을 이용하여 부를 쌓은 자들에게 그가 얻은 초자연적인 힘으로 빠르고 잔혹한 최후를 선사한다. 괴물을 사냥한다고 자부했던 도시는 이제 괴물에게 사냥당하고 있다.", "blurb": "빌지워터 학살의 부두에서 유명한 작살잡이였던 파이크는 거대한 자울치의 뱃속에서 죽음을 맞이할 운명이었지만... 살아 돌아왔다. 이제, 그는 옛 고향의 음습한 골목과 뒷길을 소리 없이 누비며, 타인을 이용하여 부를 쌓은 자들에게 그가 얻은 초자연적인 힘으로 빠르고 잔혹한 최후를 선사한다. 괴물을 사냥한다고 자부했던 도시는 이제 괴물에게 사냥당하고 있다.", "allytips": ["파이크는 체력이 약한 편이기 때문에 전투에서 잠시 동안 몸을 피해야 할 때도 있습니다. 적에게 보이지 않을 경우에는 가라앉은 자들의 축복으로 상당량의 체력을 회복할 수 있습니다.", "뼈 작살을 길게 눌러 사용하면 항상 일정한 거리만큼 적을 끌어당깁니다. 가까운 적에게 사용할 경우 파이크의 등 뒤로 적을 넘길 수 있습니다.", "뼈 작살을 짧게 눌러 사용하면 훨씬 빠르게 추가 피해를 줄 수 있습니다.", "공격 스킬의 대부분은 도주 수단이 되기도 합니다. 전투에서 빠져나올 방법을 미리 계획해 두세요."], "enemytips": ["파이크는 적 챔피언에게 피해를 입어 잃은 체력 중 상당 부분을 회복합니다. 하지만 적의 시야에 보이지 않을 때만 회복이 가능합니다.", "파이크가 유령 잠수를 사용해 가까운 곳에 숨어있을 때는 챔피언의 발밑에서 상어가 원을 그립니다.", "체력이 낮은 아군 근처에 가지 마세요. 파이크가 깊은 바다의 처형을 사용하면 다음 차례에는 자신이 처형될 수 있습니다."], "tags": ["Support", "Assassin"], "partype": "마나", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 670, "hpperlevel": 110, "mp": 415, "mpperlevel": 50, "movespeed": 330, "armor": 43, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2, "attackspeedperlevel": 2.5, "attackspeed": 0.667}, "spells": [{"id": "PykeQ", "name": "뼈 작살", "description": "파이크가 앞에 있는 적을 공격하거나 끌어당깁니다.", "tooltip": "<tap>짧게 누를 때:</tap> 파이크가 공격해 처음 적중한 적에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다. (챔피언 우선) 적중한 적은 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>됩니다.<br /><br /><hold>길게 누를 때: </hold>파이크가 작살을 던져 처음 적중한 적에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 자신 앞으로 <status>끌어당깁니다</status>. 적중한 적은 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>됩니다.<br /><br />정신 집중이 성공적으로 끝나지 않거나 스킬이 적 챔피언에게 적중하면 소모한 마나의 {{ manarefund*100 }}%를 돌려받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PykeQ.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "PykeW", "name": "유령 잠수", "description": "파이크가 위장 상태가 되며 이동 속도가 크게 증가합니다. 이동 속도는 시간이 지남에 따라 원래대로 감소합니다.", "tooltip": "파이크가 <keywordStealth>위장</keywordStealth> 상태에 돌입하고 이동 속도가 <speed>{{ movespeed }}%</speed> 증가합니다. 이동 속도는 {{ e0 }}초에 걸쳐 원래대로 돌아옵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0.3, 0.3, 0.3, 0.3, 0.3], [0.03, 0.03, 0.03, 0.03, 0.03], [80, 80, 80, 80, 80], [0.55, 0.55, 0.55, 0.55, 0.55], [8, 8, 8, 8, 8], [0.01, 0.01, 0.01, 0.01, 0.01], [1.15, 1.15, 1.15, 1.15, 1.15], [5, 5, 5, 5, 5]], "effectBurn": [null, "45", "0", "0.3", "0.03", "80", "0.55", "8", "0.01", "1.15", "5"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PykeW.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "PykeE", "name": "망자의 물살", "description": "파이크가 돌진하며 원래 있던 자리에 유령을 남깁니다. 이 유령은 파이크에게 돌아오면서 경로상의 모든 적 챔피언을 기절시킵니다.", "tooltip": "파이크가 돌진하며, 돌진을 시작했던 지점에 유령이 생성됩니다. 유령은 {{ stunduration }}초 동안 적 챔피언을 <status>기절</status>시키고 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [100, 150, 200, 250, 300], [1.25, 1.25, 1.25, 1.25, 1.25], [550, 550, 550, 550, 550], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/150/200/250/300", "1.25", "550", "1", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "PykeE.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "PykeR", "name": "깊은 바다의 처형", "description": "파이크가 체력이 낮은 적에게 순간적으로 이동하여 처형하고 스킬을 다시 사용할 수 있게 됩니다. 또한 처치에 관여한 아군에게 추가 골드를 줍니다.", "tooltip": "파이크가 X 모양의 영역 내에 있는 모든 적 챔피언에게 피해를 주며, 체력이 <scaleAD>{{ rdamage }}</scaleAD> 미만인 적에게 순간이동하여 <danger>처형</danger>합니다. 체력이 기준 이상인 챔피언과 챔피언이 아닌 대상의 경우, 해당 수치(<physicalDamage>{{ reduceddamagefinal }}</physicalDamage>)의 {{ reduceddamage*100 }}%에 해당하는 물리 피해를 입습니다. <br /><br />적 챔피언이 X 구역 안에서 처치되면 {{ rrecastduration }}초 안에 이 스킬을 <recast>재사용</recast>할 수 있습니다. 해당 챔피언을 파이크가 처치했다면 마지막으로 처치를 도운 아군에게도 챔피언 처치 골드가 주어집니다. 파이크가 아니라 아군이 처치했어도 파이크에게 챔피언 처치 골드가 주어집니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "PykeR.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "가라앉은 자들의 축복", "description": "파이크가 적에게 보이지 않는 상태가 되면 최근 적 챔피언에게 잃었던 체력의 일부를 빠르게 회복합니다. 또한, 어떤 방법으로든 파이크가 획득한 추가 최대 체력은 모두 추가 공격력으로 전환됩니다.", "image": {"full": "PykePassive.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}