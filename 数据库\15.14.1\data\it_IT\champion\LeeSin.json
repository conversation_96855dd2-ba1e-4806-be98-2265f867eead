{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"LeeSin": {"id": "<PERSON><PERSON><PERSON>", "key": "64", "name": "<PERSON>", "title": "il monaco cieco", "image": {"full": "LeeSin.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "64000", "num": 0, "name": "default", "chromas": false}, {"id": "64001", "num": 1, "name": "Lee Sin Tradizionale", "chromas": false}, {"id": "64002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "64003", "num": 3, "name": "<PERSON> del Drago", "chromas": true}, {"id": "64004", "num": 4, "name": "<PERSON>", "chromas": true}, {"id": "64005", "num": 5, "name": "Lee <PERSON>a in Piscina", "chromas": false}, {"id": "64006", "num": 6, "name": "Lee Sin SKT T1", "chromas": false}, {"id": "64010", "num": 10, "name": "<PERSON>", "chromas": false}, {"id": "64011", "num": 11, "name": "<PERSON>", "chromas": false}, {"id": "64012", "num": 12, "name": "<PERSON> Playmaker", "chromas": true}, {"id": "64027", "num": 27, "name": "Lee Sin Portatore della Notte", "chromas": false}, {"id": "64028", "num": 28, "name": "Lee Sin Portatore della Notte (edizione prestigio)", "chromas": false}, {"id": "64029", "num": 29, "name": "Lee Sin FPX", "chromas": false}, {"id": "64031", "num": 31, "name": "<PERSON> della Tempesta", "chromas": false}, {"id": "64039", "num": 39, "name": "Lee Sin Portatore della Notte (edizione prestigio 2022)", "chromas": false}, {"id": "64041", "num": 41, "name": "Lee Sin dei Giochi dello Zenith", "chromas": false}, {"id": "64051", "num": 51, "name": "<PERSON>o celeste", "chromas": false}, {"id": "64052", "num": 52, "name": "<PERSON>gito celeste divino", "chromas": false}, {"id": "64068", "num": 68, "name": "<PERSON> T1", "chromas": false}, {"id": "64072", "num": 72, "name": "<PERSON>", "chromas": false}], "lore": "Maestro nelle antiche arti marziali di Ionia, Lee Sin è un combattente irreprensibile che canalizza l'essenza dello spirito del drago per affrontare ogni sfida. Pur avendo perso la vista molti anni fa, il monaco guerriero ha dedicato la vita a proteggere la patria da chiunque osi sovvertirne i sacri equilibri. I nemici che sottovalutano il suo atteggiamento meditativo subiscono i suoi leggendari pugni e i suoi fulminei calci rotanti.", "blurb": "Maestro nelle antiche arti marziali di Ionia, Lee Sin è un combattente irreprensibile che canalizza l'essenza dello spirito del drago per affrontare ogni sfida. Pur avendo perso la vista molti anni fa, il monaco guerriero ha dedicato la vita a...", "allytips": ["Usa Onda sonica prima di impiegare Furia del dragone in modo da poter inseguire il bersaglio con Colpo risonante.", "Approfitta di Millemani, colpendo con degli attacchi base tra un lancio e l'altro: in questo modo aumenterai i danni al massimo e ridurrai il costo in energia.", "Lanciare su di sé Salvaguardia e usare poi Volontà di ferro è il modo ideale per uccidere i mostri neutrali nella giungla."], "enemytips": ["State divisi per minimizzare l'impatto dell'abilità suprema di Lee Sin, Furia del dragone.", "Lee Sin dipende molto dall'usare le proprie abilità una dietro l'altra. Lancia degli impedimenti per evitare che colleghi le sue abilità e i suoi attacchi."], "tags": ["Fighter", "Assassin"], "partype": "Energia", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 645, "hpperlevel": 108, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 3, "attackspeed": 0.651}, "spells": [{"id": "LeeSinQOne", "name": "Onda sonica/Colpo risonante", "description": "Onda sonica: <PERSON> proietta un'onda sonora dissonante per localizzare i suoi nemici, infliggendo danni fisici al primo nemico che colpisce. Se Onda sonica va a segno, <PERSON> <PERSON> può lanciare Colpo risonante nei 3 secondi successivi.<br><PERSON>po risonante: <PERSON> si lancia contro il nemico colpito da Onda sonica, infliggendo danni fisici in base alla salute mancante del bersaglio.", "tooltip": "Lee Sin produce un'onda sonora dissonante, infliggendo <physicalDamage>{{ initialdamage }} danni fisici</physicalDamage> al primo nemico che colpisce, ottenendo Visione magica e la possibilità di <recast>rilanciare</recast> questa abilità nei {{ reactivatetime }} secondi successivi.<br /><br /><recast>R<PERSON><PERSON><PERSON>:</recast> Lee Sin si scaglia contro il nemico colpito dall'onda, infliggendo da <physicalDamage>{{ recastdamage }} a {{ empowereddamage }} danni fisici</physicalDamage> che crescono in base alla salute mancante del bersaglio. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> sonica", "Danni base minimi Colpo risonante", "Danni base massimi Colpo risonante", "Ricarica"], "effect": ["{{ q1basedamage }} -> {{ q1basedamageNL }}", "{{ q2basedamage }} -> {{ q2basedamageNL }}", "{{ q2basedamage*2.000000 }} -> {{ q2basedamagenl*2.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LeeSinQOne.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinWOne", "name": "Salvaguardia/Volontà di ferro", "description": "Salvaguardia: <PERSON> scatta in direzione dell'alleato bersaglio, proteggendosi dai danni con uno scudo. Se l'alleato è un campione, riceve uno scudo. <PERSON><PERSON> aver usato Sal<PERSON>guardia, <PERSON> può lanciare Volontà di ferro.<br>Volontà di ferro: l'intenso addestramento di Lee Sin gli permette di eccellere in battaglia. <PERSON> Sin ottiene rubavita e rubavita magico.", "tooltip": "<PERSON> Sin scatta verso un alleato o un lume. Se il bersaglio è un campione, <PERSON> conferisce a lui e a se stesso uno <shield>scudo da {{ shieldamount }}</shield> per {{ shieldduration }} secondi e riduce il tempo di ricarica di questa abilità di un {{ w1cooldownrecovered*100 }}%. <PERSON> <recast>rilanciare</recast> questa abilità nei successivi {{ w1reactivatetime }} secondi.<br /><br /><recast>Rilancio:</recast> <PERSON> ottiene {{ lifestealandspellvamp }}% di rubavita e rubavita magico per {{ lifestealandspellvamptime }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Assorbimento scudo Sal<PERSON>guardia", "% di Rubavita/Rubavita magico di Volontà di ferro"], "effect": ["{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ lifestealandspellvamp }}% -> {{ lifestealandspellvampNL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LeeSinWOne.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinEOne", "name": "Tempesta/Menomazione", "description": "Tempesta: <PERSON> colpisce il terreno, scatenando un'onda d'urto che infligge danni magici e rivela le unità nemiche colpite. Se Tempesta colpisce un nemico, <PERSON> può lanciare Menomazione.<br>Menomazione: <PERSON> mutila i nemici vicini danneggiati da Tempesta, riducendone la velocità di movimento. La velocità di movimento si ripristina gradualmente durante l'effetto.", "tooltip": "<PERSON> colpisce il terreno, scatenando un'onda d'urto che infligge <magicDamage>{{ initialdamage }} danni magici</magicDamage> e rivela le unità nemiche colpite per {{ slowduration }} secondi. Se colpisce un nemico, <PERSON> <recast>rilanciare</recast> per i successivi {{ reactivatetime }} secondi.<br /><br /><recast>Rilancio:</recast> <PERSON> <status>rallenta</status> i nemici vicini colpiti dall'onda d'urto di un {{ slowamount }}% che decade in {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento Menomazione"], "effect": ["{{ e1damage }} -> {{ e1damageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LeeSinEOne.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LeeSinR", "name": "Furia del dragone", "description": "Lee Sin esegue un potente calcio circolare, respingendo il suo bersaglio e infliggendo danni a lui e a tutti i nemici con cui si scontra. I nemici con cui collide il bersaglio vengono lanciati in aria per una breve durata. Questa tecnica gli è stata insegnata da <PERSON>, ma <PERSON> non scaglia i giocatori fuori dalla mappa.", "tooltip": "Lee Sin esegue un potente calcio circolare, <status>respingendo</status> un campione nemico e infliggendo <physicalDamage>{{ damage }} danni fisici</physicalDamage>.<br /><br />I nemici colpiti dal bersaglio vengono <status>lanciati in aria</status> per una breve durata e subiscono <physicalDamage>{{ damage }} danni fisici più il {{ percenthpcarrythrough }}% della salute bonus del bersaglio come danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> salute bonus", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthpcarrythrough }}% -> {{ percenthpcarrythroughNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 85, 60], "cooldownBurn": "110/85/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [375, 375, 375], "rangeBurn": "375", "image": {"full": "LeeSinR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Do<PERSON> che Lee <PERSON> ha usato un'abilità, i suoi prossimi 2 attacchi base guadagnano velocità d'attacco e ripristinano energia.", "image": {"full": "LeeSinPassive.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}