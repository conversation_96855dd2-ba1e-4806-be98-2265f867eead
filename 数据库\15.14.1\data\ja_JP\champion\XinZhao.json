{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"XinZhao": {"id": "XinZhao", "key": "5", "name": "シン・ジャオ", "title": "デマーシアの家令長", "image": {"full": "XinZhao.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "5000", "num": 0, "name": "default", "chromas": false}, {"id": "5001", "num": 1, "name": "コマンドー シン・ジャオ", "chromas": false}, {"id": "5002", "num": 2, "name": "帝国兵士シン・ジャオ", "chromas": false}, {"id": "5003", "num": 3, "name": "剣闘士シン・ジャオ", "chromas": false}, {"id": "5004", "num": 4, "name": "有翼軽騎兵シン・ジャオ", "chromas": false}, {"id": "5005", "num": 5, "name": "三国武将シン・ジャオ", "chromas": true}, {"id": "5006", "num": 6, "name": "エージェント シン・ジャオ", "chromas": false}, {"id": "5013", "num": 13, "name": "龍殺しシン・ジャオ", "chromas": true}, {"id": "5020", "num": 20, "name": "宇宙の守護者シン・ジャオ", "chromas": true}, {"id": "5027", "num": 27, "name": "略奪者シン・ジャオ", "chromas": true}, {"id": "5036", "num": 36, "name": "爆発花火シン・ジャオ", "chromas": true}], "lore": "シン・ジャオは現王朝のライトシールド家に忠誠を誓った固い決意を持つ戦士だ。かつてはノクサスの闘技場で闘士をさせられ、そこで無数の戦闘を生き延びたが、デマーシアの軍隊によって解放されたことでこの勇敢な解放者に対して生涯の同盟を誓った。お気に入りの三叉の槍を手に、彼はいかなる不利な状況でもあらゆる敵に大胆に挑み、新たな故郷となった王国のために戦っている。", "blurb": "シン・ジャオは現王朝のライトシールド家に忠誠を誓った固い決意を持つ戦士だ。かつてはノクサスの闘技場で闘士をさせられ、そこで無数の戦闘を生き延びたが、デマーシアの軍隊によって解放されたことでこの勇敢な解放者に対して生涯の同盟を誓った。お気に入りの三叉の槍を手に、彼はいかなる不利な状況でもあらゆる敵に大胆に挑み、新たな故郷となった王国のために戦っている。", "allytips": ["シン・ジャオは強力な先鋒型チャンピオン。「兵貴神速」で戦闘の口火を切り、続けてアルティメットスキルを発動すれば、複数の敵に大ダメージを与えられる。", "アルティメットスキルを発動する際は、ノックバック効果を最大限に活かせる位置取りを心がけ敵の陣形を崩壊させよう。"], "enemytips": ["シン・ジャオは「兵貴神速」とアルティメットスキルの双方で範囲ダメージを与えられる強力なチャンプだ。シン・ジャオがアルティメットスキルを使うまで、チームは1カ所に固まらず分散しておくのが無難。", "「三槍撃」はダメージを与えると同時にその他のスキルのクールダウンも短縮できる、シン・ジャオの恐るべきダメージの生命線とも言うべきスキル。その発動を妨害できれば、シン・ジャオの戦闘力はがた落ちする。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 2}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 274, "mpperlevel": 55, "movespeed": 345, "armor": 35, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "XinZhaoQ", "name": "三槍撃", "description": "通常攻撃が3回分強化され、3回目で敵をノックアップさせる。", "tooltip": "次の3回の通常攻撃が<physicalDamage>{{ bonusdamage }}の物理ダメージ</physicalDamage>を追加で与え、他のスキルのクールダウンを1秒短縮する。また、3回目の通常攻撃は対象を{{ e2 }}秒間<status>ノックアップ</status>させる。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["追加ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [20, 35, 50, 65, 80], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/35/50/65/80", "0.75", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "XinZhaoQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "XinZhaoW", "name": "風成雷鳴", "description": "前方を槍で薙ぎ払い、次に槍を突いて敵ユニットにスロウを与え、挑戦対象としてマークする。", "tooltip": "槍で薙ぎ払って<physicalDamage>{{ slashdamage }}</physicalDamage>の物理ダメージを与え、その後の突きで<physicalDamage>{{ thrustdamage }}</physicalDamage>の物理ダメージを与える。突きが命中した敵には{{ e7 }}秒間{{ e6 }}%の<status>スロウ効果</status>を与える。<br /><br />突きが命中したチャンピオンと大型モンスターは{{ markduration }}秒間、<keywordMajor>挑戦対象</keywordMajor>としてマークされ、<keywordStealth>ステルス</keywordStealth>状態でない限り可視化される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["薙ぎ払いダメージ", "突きダメージ", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ slashbasedamage }} -> {{ slashbasedamageNL }}", "{{ thrustbasedamage }} -> {{ thrustbasedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [140, 140, 140, 140, 140], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0.5", "0.5", "0.5", "50", "1.5", "140", "200", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XinZhaoW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "XinZhaoE", "name": "兵貴神速", "description": "敵に突進して攻撃速度が増加し、範囲内にいるすべての敵にダメージと短時間のスロウ効果を与える。挑戦対象に対しては「兵貴神速」の射程が増加する。", "tooltip": "敵に突撃して、周囲の敵に<magicDamage>{{ chargedamage }}の魔法ダメージ</magicDamage>を与え、{{ e6 }}秒間{{ baseslowamount }}%の<status>スロウ効果</status>を付与する。<br /><br />また{{ e4 }}秒間、<attackSpeed>攻撃速度が{{ e3 }}%</attackSpeed>増加する。<br /><br /><keywordMajor>挑戦対象</keywordMajor>の敵に対しては<spellName>「兵貴神速」</spellName>の射程が増加する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "攻撃速度"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect3amount*100.000000 }}% -> {{ effect3amountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [-0.3, -0.3, -0.3, -0.3, -0.3], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [250, 250, 250, 250, 250], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "-0.3", "40/45/50/55/60", "5", "250", "0.5", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "XinZhaoE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "XinZhaoR", "name": "三日月槍守", "description": "直前にダメージを与えていた敵を挑戦対象にする。周囲にいる敵に対象の現在体力に応じたダメージを与え、挑戦対象以外をノックバックさせる。発生した円の外にいる敵チャンピオンからはダメージを受けなくなる。", "tooltip": "自分が最後に通常攻撃または<spellName>「兵貴神速」</spellName>でダメージを与えたチャンピオンを{{ markduration }}秒間、<keywordMajor>挑戦対象</keywordMajor>にする。<br /><br />周囲を槍で薙ぎ払って<physicalDamage>{{ totaldamage }}(+現在体力の{{ percentcurrenthealthdamage*100 }}%)の物理ダメージ</physicalDamage>を与え、<keywordMajor>挑戦対象を除く</keywordMajor>すべての敵を<status>ノックバック</status>させる。<br /><br />その後{{ missiledefensebaseduration }}秒間、薙ぎ払いの範囲外にいる敵からはダメージを受けなくなる。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "XinZhaoR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "不退転", "description": "通常攻撃が3回毎に追加ダメージを与えて自身を回復する。", "image": {"full": "XinZhaoP.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}