{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Soraka": {"id": "<PERSON><PERSON><PERSON>", "key": "16", "name": "ソラカ", "title": "星の子", "image": {"full": "Soraka.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "16000", "num": 0, "name": "default", "chromas": false}, {"id": "16001", "num": 1, "name": "森の精霊ソラカ", "chromas": false}, {"id": "16002", "num": 2, "name": "神聖なるソラカ", "chromas": false}, {"id": "16003", "num": 3, "name": "白魔道士ソラカ", "chromas": false}, {"id": "16004", "num": 4, "name": "死神ソラカ", "chromas": false}, {"id": "16005", "num": 5, "name": "バナナ団ソラカ", "chromas": false}, {"id": "16006", "num": 6, "name": "プログラム ソラカ", "chromas": false}, {"id": "16007", "num": 7, "name": "スターガーディアン ソラカ", "chromas": false}, {"id": "16008", "num": 8, "name": "パジャマガーディアン ソラカ", "chromas": false}, {"id": "16009", "num": 9, "name": "冬の奇跡ソラカ", "chromas": true}, {"id": "16015", "num": 15, "name": "秩序の光ソラカ", "chromas": false}, {"id": "16016", "num": 16, "name": "混沌の闇ソラカ", "chromas": false}, {"id": "16017", "num": 17, "name": "プレステージ スターガーディアン ソラカ", "chromas": false}, {"id": "16018", "num": 18, "name": "カフェキューティーズ ソラカ", "chromas": true}, {"id": "16027", "num": 27, "name": "精霊の花祭りソラカ", "chromas": true}, {"id": "16037", "num": 37, "name": "不滅の旅路ソラカ", "chromas": true}, {"id": "16044", "num": 44, "name": "妖精の王宮ソラカ", "chromas": true}], "lore": "霊峰ターゴンの彼方にある宇宙の次元をさまよっていたソラカは、定命の種族を彼らの暴力的な本能から守るために、自らの永遠の命を手放してやってきた。彼女は出会ったものすべてに慈悲と情けの美徳を広めようと努めており、彼女を傷つけようとするものですら治癒を施す。この世界で様々な紛争を目にしてきたにもかかわらず、彼女は今も、ルーンテラの人々には可能性が残っていると信じている。", "blurb": "霊峰ターゴンの彼方にある宇宙の次元をさまよっていたソラカは、定命の種族を彼らの暴力的な本能から守るために、自らの永遠の命を手放してやってきた。彼女は出会ったものすべてに慈悲と情けの美徳を広めようと努めており、彼女を傷つけようとするものですら治癒を施す。この世界で様々な紛争を目にしてきたにもかかわらず、彼女は今も、ルーンテラの人々には可能性が残っていると信じている。", "allytips": ["ソラカはその圧倒的な治癒力によって味方の前進を助ける、頼もしい存在だ。", "「星の静寂」は、敵を足止めするための強力な防波堤としても機能する。", "「星に願いを」を使えば、遠く離れた場所で窮地に陥っている味方も救うことができる。"], "enemytips": ["ソラカが「星のささやき」を使用するために前へ出てきたら、攻撃のチャンスだ。", "ソラカの「星の静寂」は厄介なスキルだが、クールダウンが長いという欠点を利用しよう。", "ソラカが回復するチャンピオンを狙うよりも、ソラカ自身を狙ったほうが効率的だ。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 5, "magic": 7, "difficulty": 3}, "stats": {"hp": 605, "hpperlevel": 88, "mp": 425, "mpperlevel": 40, "movespeed": 325, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2.14, "attackspeed": 0.625}, "spells": [{"id": "SorakaQ", "name": "星のささやき", "description": "指定地点に流れ星が落ち、周囲の敵ユニットに魔法ダメージとスロウ効果を与える。敵チャンピオンに「星のささやき」が命中した場合、ソラカの体力が回復する。", "tooltip": "流れ星を落として<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ movespeedslow*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />敵チャンピオンに命中すると<keywordMajor>「再生」</keywordMajor>を獲得し、{{ hotduration }}秒かけて<healing>{{ totalhot }}の体力</healing>を回復する。また、同じ時間をかけて減衰していく<speed>{{ movespeedhaste*100 }}%の移動速度</speed>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "体力回復(再生)", "移動速度", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basehot }} -> {{ basehotNL }}", "{{ movespeedhaste*100.000000 }}% -> {{ movespeedhastenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "SorakaQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SorakaW", "name": "星霊の癒し", "description": "自身の体力を消費し、指定した味方チャンピオンの体力を回復する。", "tooltip": "自分以外の味方チャンピオン1体の<healing>体力を{{ totalheal }}</healing>回復する。<br /><br />自身が<keywordMajor>「再生」</keywordMajor>の効果を受けている場合、体力コストが{{ percenthealthcostrefund*100 }}%減少し、対象に{{ spell.sorakaq:hotduration }}秒間<keywordMajor>「再生」</keywordMajor>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "クールダウン", "@AbilityResourceName@コスト", "体力コスト減少"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ percenthealthcostrefund*100.000000 }}% -> {{ percenthealthcostrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "%、{{ cost }}マナ", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SorakaW.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "最大体力の{{ percenthealthcost*100 }}%、{{ cost }}マナ"}, {"id": "SorakaE", "name": "星の静寂", "description": "指定した場所に時空の渦を生じさせ、巻き込んだ敵チャンピオンすべてにサイレンス効果を付与する。さらに渦が消滅した瞬間に渦の範囲内に居るすべての敵チャンピオンにスネア効果を付与する。", "tooltip": "星々の渦巻くフィールドを作り出し、チャンピオンに<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。このフィールドは{{ rootdelay }}秒間持続し、範囲内の敵に<status>サイレンス効果</status>を与える。フィールドが消滅した瞬間、範囲内に残っていたチャンピオンに{{ rootduration }}秒間の<status>スネア効果</status>を付与し、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "スネア効果時間", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "SorakaE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SorakaR", "name": "星に願いを", "description": "ソラカとすべての味方チャンピオンが降り注ぐ希望の光で満たされ、瞬時に体力が回復する。", "tooltip": "聖なる力を呼び覚まし、距離に関係なくすべての味方チャンピオンの<healing>体力を{{ healingcalc }}</healing>回復する。体力が40%未満の対象は、回復量が<healing>{{ ampedhealing }}</healing>に増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "クールダウン"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [150, 135, 120], "cooldownBurn": "150/135/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SorakaR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "救済の足音", "description": "近くにいる体力の少ない味方に向かって移動する際に、ソラカの移動速度が増加する。", "image": {"full": "Soraka_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}