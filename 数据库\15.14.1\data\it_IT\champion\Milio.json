{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Milio": {"id": "<PERSON><PERSON>", "key": "902", "name": "<PERSON><PERSON>", "title": "La fiamma gentile", "image": {"full": "Milio.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "902000", "num": 0, "name": "default", "chromas": false}, {"id": "902001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "902011", "num": 11, "name": "<PERSON><PERSON> pluviale", "chromas": true}], "lore": "<PERSON>lio è un amichevole ragazzo di Ixtal che, nonostante la giovane età, ha padroneggiato l'Assioma del fuoco e ha scoperto qualcosa di nuovo: il fuoco rassicurante. Grazie a questo nuovo potere, Milio vuole aiutare la sua famiglia e farle lasciare l'esilio unendosi agli Yun Tal come fece un tempo sua nonna. Do<PERSON> aver attraversato le giungle di Ixtal fino alla capitale di Ixaocan, ora Milio si prepara ad affrontare il Vidalion e a unirsi agli Yun Tal, ignaro delle prove, e soprattutto dei pericoli, che lo attendono.", "blurb": "<PERSON>lio è un amichevole ragazzo di Ixtal che, nonostante la giovane età, ha padroneggiato l'Assioma del fuoco e ha scoperto qualcosa di nuovo: il fuoco rassicurante. Grazie a questo nuovo potere, Milio vuole aiutare la sua famiglia e farle lasciare...", "allytips": ["<PERSON><PERSON> ha bisogno di alleati vicini per sfruttare al meglio i suoi strumenti.", "La velocità dello scatto di Milio aumenta con la sua velocità di movimento. Usa la velocità extra per sorprendere i tuoi nemici!", "Il pericolo è divertente, se lo accetti."], "enemytips": ["Le abilità di movimento di Milio mostrano la loro destinazione. Cerca di sfruttare questo dato a tuo vantaggio.", "I campioni con effetti di controllo veloci sono molto forti contro <PERSON>.", "Cogliere Milio senza alleati intorno inibisce fortemente la sua mobilità. Cerca di prenderlo quando è solo."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "MilioQ", "name": "Ultra mega calcio di fuoco", "description": "Calcia una palla che respinge un nemico. <PERSON><PERSON><PERSON> co<PERSON>, la palla sale verso l'alto e ricade sul bersaglio, infliggendo danni e rallentando i nemici nell'area di impatto.", "tooltip": "<PERSON><PERSON> calcia una palla di fuoco, <status>respingendo</status> il primo nemico colpito. Se colpisce, la palla rimbalza oltre ed esplode, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage> ai nemici circostanti e <status>rallentandoli</status> di {{ slowamountpercent }} per {{ slowduration }} secondi.<br /><br />Colpire almeno un campione nemico con <spellName>Ultra mega calcio di fuoco</spellName> rimborsa un {{ refundratio*100 }}% del costo in mana.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Costo in mana"], "effect": ["{{ falldamage }} -> {{ falldamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "MilioQ.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioW", "name": "<PERSON><PERSON><PERSON><PERSON> ristoratore", "description": "Crea una zona di potenziamento che cura gli alleati e aumenta la gittata d'attacco di chi si trova al suo interno. La zona segue l'alleato più vicino al punto di lancio.", "tooltip": "<PERSON><PERSON> crea un fuocherello che segue i campioni alleati per {{ zoneduration }} secondi. I campioni alleati vicini ottengono {{ rangepercent }} gittata d'attacco e ripristinano <healing>{{ healingovertime }} salute</healing> per tutta la durata. Il fuocherello applica anche <spellName>Fiammeggiante!</spellName> ogni {{ healfrequencyseconds }} secondi.<br /><br /><recast>Rilancio:</recast> cambia l'alleato seguito dal fuocherello.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Gitt<PERSON>", "Guarigione", "Ricarica", "Costo in mana"], "effect": ["{{ rangepctincrease*100.000000 }}% -> {{ rangepctincreasenl*100.000000 }}%", "{{ totalhealingovertime }} -> {{ totalhealingovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [29, 27, 25, 23, 21], "cooldownBurn": "29/27/25/23/21", "cost": [90, 100, 110, 120, 130], "costBurn": "90/100/110/120/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "MilioW.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioE", "name": "<PERSON><PERSON> abbrac<PERSON>", "description": "Milio applica uno scudo a un alleato, incrementandone temporaneamente la velocità di movimento. Questa abilità ha 2 cariche.", "tooltip": "<PERSON><PERSON> avvolge un campione alleato in fiamme protettive che conferiscono <shield>{{ shieldcalc }} scudo</shield> e <speed>{{ movespeedamount*100 }}% velocità di movimento</speed> per {{ movespeedduration }} secondi.<br /><br />Questa abilità ha 2 cariche e gli effetti si accumulano sui bersagli consecutivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Velocità di movimento", "Costo in mana", "Tempo di ricarica"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MilioE.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioR", "name": "Soffio di vita", "description": "<PERSON><PERSON> scatena un'ondata di fiamme lenitive che curano e rimuovono gli effetti di controllo dagli alleati entro la portata.", "tooltip": "<PERSON><PERSON> scatena un'ondata di fiamme lenitive sui campioni alleati vicini, purificando gli effetti di <status>impedimento</status> e <status>immobilizzazione</status>, ripristinando <healing>{{ healcalc }} salute</healing> e conferendo un {{ tenacityamount*100 }}% di Tenacia per {{ tenacityduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "Ricarica"], "effect": ["{{ healbase }} -> {{ healbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 145, 130], "cooldownBurn": "160/145/130", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MilioR.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Fiammeggiante!", "description": "Le abilità di Milio incantano gli alleati che toccano, la cui abilità o auto-attacco successivo infligge una raffica di danni aggiuntivi e brucia il bersaglio.", "image": {"full": "Milio_P.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}