{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Anivia": {"id": "An<PERSON><PERSON>", "key": "34", "name": "An<PERSON><PERSON>", "title": "der Kryophönix", "image": {"full": "Anivia.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "34000", "num": 0, "name": "default", "chromas": false}, {"id": "34001", "num": 1, "name": "Friedensphönix-Anivia", "chromas": false}, {"id": "34002", "num": 2, "name": "Raubvogel-Anivia", "chromas": false}, {"id": "34003", "num": 3, "name": "Noxusjäger-Anivia", "chromas": false}, {"id": "34004", "num": 4, "name": "Hextech-Anivia", "chromas": false}, {"id": "34005", "num": 5, "name": "Schwarzfrost-Anivia", "chromas": false}, {"id": "34006", "num": 6, "name": "Urzeit-Anivia", "chromas": false}, {"id": "34007", "num": 7, "name": "Karnevalskönigin Anivia", "chromas": false}, {"id": "34008", "num": 8, "name": "Papierkunst-Anivia", "chromas": true}, {"id": "34017", "num": 17, "name": "Kosmische Schwingen Anivia", "chromas": true}, {"id": "34027", "num": 27, "name": "Göttlicher Phönix-Anivia", "chromas": true}, {"id": "34037", "num": 37, "name": "Hexerei-Fledernivia", "chromas": true}, {"id": "34046", "num": 46, "name": "Siegreiche Anivia", "chromas": true}], "lore": "Anivia ist ein gütiger geflügelter Geist. <PERSON> Freljord zu schützen, muss sie einen endlosen Kreislauf aus Leben, Tod und Wiedergeburt durchstehen. Al<PERSON>, die aus unerbittlichem Eis und schneidenden Winden geboren wurde, kann sie die Macht dieser Elemente nutzen und jeden zerschmettern, der ihr Heimatland bedroht. Anivia beschützt die Stämme des rauen Nordens und steht ihnen mit Rat zur Seite. Von ihnen wird sie als Symbol der Hoffnung und Vorzeichen großer Veränderungen verehrt. In Kämpfen hält sie sich niemals auch nur ein bisschen zurück. <PERSON><PERSON><PERSON><PERSON><PERSON> weiß sie, dass ihr Opfer nicht in Vergessenheit geraten wird und dass ihre Wiedergeburt gewiss ist.", "blurb": "Anivia ist ein gütiger geflügelter Geist. <PERSON> Freljord zu schützen, muss sie einen endlosen Kreislauf aus Leben, Tod und Wiedergeburt durchstehen. <PERSON><PERSON>, die aus unerbittlichem Eis und schneidenden Winden geboren wurde, kann sie die <PERSON>ht dieser...", "allytips": ["„Blitzeis“ und „Frostbiss“ können eine vernichtende Kombination ergeben.", "Aniv<PERSON> ist durch „Eissturm“ besonders von Mana abhängig. Versuch daher Gegenstände mit zusätzlichem Mana zu bekommen, oder besorge dir den Buff des Blauen Wächters in der Kluft der Beschwörer.", "<PERSON>ür gegnerische Champions kann es schwierig werden, ihr Ei schon früh im Spiel zu zerstören. Durch einen anfänglichen aggressiven Spielstil lässt sich dies besonders gut ausnutzen."], "enemytips": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> auf der Lane anzugreifen. <PERSON>t mehreren Leuten ist es ein<PERSON>cher, das Ei zu z<PERSON>tören.", "<PERSON>n du einen Fernkämpfer spielst, dann bleib weit genug von Aniv<PERSON> weg, um dem „Blitzeis“ besser ausweichen zu können.", "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> auf der Lane anzugreifen. Im Dschungel kann sie die engen Wege schon mit niedrigstufigem „Kristallisieren“ versperren."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 4, "magic": 10, "difficulty": 10}, "stats": {"hp": 550, "hpperlevel": 92, "mp": 495, "mpperlevel": 45, "movespeed": 325, "armor": 21, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.68, "attackspeed": 0.658}, "spells": [{"id": "FlashFrost", "name": "Blitzeis", "description": "<PERSON><PERSON><PERSON> schl<PERSON>gt ihre Flügel zusammen und beschwört einen Eiskristall, der in Richtung eines Gegners fliegt. Der Kristall verursacht an allen Gegnern auf seinem Weg Schaden und unterkühlt sie. Wenn der Kristall explodiert, verursacht er mittleren Schaden in einem Radius und betäubt alle Gegner in dem Bereich.", "tooltip": "Anivia sendet einen massiven Eisbrocken aus, der Gegnern <magicDamage>{{ totalpassthroughdamage }}&nbsp;magischen Schaden</magicDamage> zuf<PERSON><PERSON>, sie {{ slowduration }}&nbsp;Sekunden lang <keywordMajor>unterkühlt</keywordMajor> und um {{ spell.glacialstorm:slowamount }}&nbsp;% <status>verlangsamt</status>. Wenn der Eisbrocken seine maximale Reichweite erreicht, explodiert er, wodurch Gegner {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status> werden und <magicDamage>{{ totalexplosiondamage }}&nbsp;magischen Schaden</magicDamage> erleiden.<br /><br />Anivia kann diese Fähigkeit <recast>reaktivieren</recast>, während das Eis fliegt, um es frühzeitig explodieren zu lassen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Explosionsschaden", "Betäubungsdauer:", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ passthroughbasedamage }} -> {{ passthroughbasedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "FlashFrost.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Crystallize", "name": "Kristallisieren", "description": "Anivia kondens<PERSON>t die Luftfeuchtigkeit zu einer undurchdringlichen Eiswand, um jegliche Bewegung zu blockieren. Die Wand bleibt nur kurze Zeit bestehen, bevor sie wieder schmilzt.", "tooltip": "<PERSON><PERSON><PERSON> be<PERSON>rt eine <PERSON>wan<PERSON> herau<PERSON>, die {{ e2 }}&nbsp;Einheiten breit ist. Die Wand bleibt {{ e1 }}&nbsp;<PERSON><PERSON>nden lang bestehen, bevor sie wieder schmilzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Breite"], "effect": ["{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [17, 17, 17, 17, 17], "cooldownBurn": "17", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [5, 5, 5, 5, 5], [400, 500, 600, 700, 800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5", "400/500/600/700/800", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "Crystallize.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Frostbite", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>gelschlag erzeugt einen frostigen Windstoß, der ihrem Ziel Schaden zufügt. Wenn das Ziel vor Kurzem von „Blitzeis“ getroffen wurde oder durch einen voll ausgebildeten „Eissturm“ Schaden erlitten hat, erleidet es doppelten Schaden.", "tooltip": "<PERSON><PERSON><PERSON>ügelschlag erzeugt einen frostigen Windstoß, der <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. <keywordMajor>Unterkühlten</keywordMajor> G<PERSON><PERSON>n fügt An<PERSON> statt<PERSON> <magicDamage>{{ empowereddamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Frostbite.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GlacialStorm", "name": "Eissturm", "description": "<PERSON><PERSON><PERSON> be<PERSON> und <PERSON>, die Gegnern Schaden zufügen und am Vorankommen hindern.", "tooltip": "<toggle>Aktivierbar:</toggle> Anivia beschwört einen schweren Niederschlag aus E<PERSON> und Hagel herauf, der ihre Gegner um {{ slowamount }}&nbsp;% <status>verlangsamt</status> und ihnen <magicDamage>{{ totaldamagepersecond }}&nbsp;magischen Schaden pro Sekunde</magicDamage> zufügt. Der Sturm wird über {{ growthtime }}&nbsp;Sekunden immer größer.<br /><br />Wenn der Sturm seine volle Größe erreicht, <keywordMajor>unterkühlt</keywordMajor> er, <status>verlangsamt</status> um {{ slowpercentempoweredtt }}&nbsp;% und verursacht <magicDamage>{{ empowereddamagepersecondtooltiponly }}&nbsp;magischen Schaden pro Sekunde</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde", "Verlangsamung", "Unterkühlung – Verlangsamung", "Manakosten pro Sekunde", "Abklingzeit"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%", "{{ slowpercentempoweredtt }}&nbsp;% -> {{ slowpercentempoweredttNL }}&nbsp;%", "{{ manacostpersecond }} -> {{ manacostpersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [4, 3, 2], "cooldownBurn": "4/3/2", "cost": [60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " + {{ manacostpersecond }}&nbsp;Mana pro Sekunde", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "GlacialStorm.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} + {{ manacostpersecond }}&nbsp;Mana pro Sekunde"}], "passive": {"name": "Wiedergeburt", "description": "<PERSON><PERSON><PERSON>t Anivia t<PERSON> Schaden, verwan<PERSON>t sie sich zurück in ein E<PERSON> und wird dann mit vollem Leben wiedergeboren.", "image": {"full": "Anivia_P.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}