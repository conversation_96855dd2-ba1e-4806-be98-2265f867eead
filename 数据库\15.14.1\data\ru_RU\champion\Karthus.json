{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "Кар<PERSON><PERSON><PERSON>", "title": "Певец смерти", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "Призрак Картус", "chromas": false}, {"id": "30002", "num": 2, "name": "Статуя Картуса", "chromas": false}, {"id": "30003", "num": 3, "name": "Мрачный жнец Картус", "chromas": true}, {"id": "30004", "num": 4, "name": "Картус, вокалист Pentakill", "chromas": false}, {"id": "30005", "num": 5, "name": "Fnatic Картус", "chromas": false}, {"id": "30009", "num": 9, "name": "Светоубийца Картус", "chromas": false}, {"id": "30010", "num": 10, "name": "Инфернальный Картус", "chromas": true}, {"id": "30017", "num": 17, "name": "Картус из Pentakill ''Lost Chapter''", "chromas": true}, {"id": "30026", "num": 26, "name": "Дух леса Картус", "chromas": true}], "lore": "Картус – предвестник забвения, о кошмарном появлении которого возвещают его печальные песни. Живые трепещут перед загробной жизнью, Картус же видит только красоту и чистоту ее объятий, только безупречное единство жизни и смерти. Картус, этот апостол небытия, приходит с Сумрачных островов, чтобы явить людям радость смерти.", "blurb": "Картус – предвестник забвения, о кошмарном появлении которого возвещают его печальные песни. Живые трепещут перед загробной жизнью, Картус же видит только красоту и чистоту ее объятий, только безупречное единство жизни и смерти. Картус, этот апостол...", "allytips": ["Просите ваших союзников подсказывать, когда вам стоит использовать умение Реквием, чтобы убивать на разных линиях.", "Умение Опустошение идеально подходит для получения денег от убийств миньонов и изматывания вражеских чемпионов."], "enemytips": ["Короткий промежуток времени после своей смерти Картус может использовать заклинания. Бегите прочь от его трупа, чтобы спастись.", "Удостоверьтесь, что у вас достаточно здоровья, чтобы пережить Реквием, даже если для этого потребуется чаще возвращаться на базу для лечения. "], "tags": ["Mage"], "partype": "Мана", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "Опустошение", "description": "Картус производит отложенный взрыв в указанном месте, нанося урон всем ближайшим врагам. Изолированные враги получают больше урона. ", "tooltip": "Картус создает магический взрыв, который наносит <magicDamage>{{ qdamage }} магического урона</magicDamage>. Если взрыв поражает только одного врага, он наносит <magicDamage>{{ qsingletargetdamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "Стена боли", "description": "Картус создает энергетический барьер, который замедляет проходящих сквозь него врагов и уменьшает их сопротивление магии на некоторое время.", "tooltip": "Картус создает стену на {{ e4 }} сек. Враги, проходящие сквозь стену, теряют <scaleMR>{{ e1 }}% сопротивления магии</scaleMR> на {{ e5 }} сек. и <status>замедляются</status> на {{ e3 }}% (эффект ослабевает в течение времени действия).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON> барьера", "Замедление"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Осквернение", "description": "Картус пассивно высасывает жизненную энергию из убитых им врагов, восстанавливая себе ману при каждом убийстве. Или же вместо этого он может окружить себя душами своих жертв, которые наносят урон ближайшим врагам, но быстро расходуют его ману.", "tooltip": "<spellPassive>Пассивно:</spellPassive> когда Картус убивает бойца, он восстанавливает себе <scaleMana>{{ e2 }} маны</scaleMana>.<br /><br /><toggle>Включено:</toggle> Картус создает вокруг себя ауру смерти, нанося <magicDamage>{{ totaldps }} магического урона</magicDamage> в секунду врагам поблизости.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон в секунду", "Восстановление маны", "Стоимость – @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " маны в секунду", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} маны в секунду"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "Реквием", "description": "Картус читает заклинание в течение 3 секунд, после чего наносит урон всем вражеским чемпионам.", "tooltip": "Картус читает заклинание в течение 3 сек., после чего наносит <magicDamage>{{ totaldamage }} магического урона</magicDamage> всем вражеским чемпионам независимо от расстояния.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Победа над смертью", "description": "После смерти Картус приобретает форму духа, которая позволяет ему продолжать использовать заклинания.", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}