{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Bard", "title": "ผู้ดูแลพเนจร", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "<PERSON><PERSON> Bard", "chromas": false}, {"id": "432005", "num": 5, "name": "Snow Day Bard", "chromas": true}, {"id": "432006", "num": 6, "name": "Bard Bard", "chromas": false}, {"id": "432008", "num": 8, "name": "Astronaut Bard", "chromas": true}, {"id": "432017", "num": 17, "name": "Cafe Cuties Bard", "chromas": true}, {"id": "432026", "num": 26, "name": "Shan <PERSON> Scrolls Bard", "chromas": true}, {"id": "432035", "num": 35, "name": "T1 Bard", "chromas": true}, {"id": "432037", "num": 37, "name": "Spirit Blossom Bard", "chromas": true}], "lore": "นักเดินทางผู้ซึ่งมาจากดวงดาว Bard คือตัวแทนแห่งโชคลาภผู้ซึ่งต่อสู้เพื่อรักษาความสมดุลของชีวิตทั้งปวงไม่ให้เกิดความโกลาหลเกินกว่าที่จะรับไหว ชาวรูนเทอร์รามากมายร้องเพลงเพื่อรำลึกถึงตัวตนอันพิเศษของเขา แต่พวกเขาทุกคนก็เชื่อว่าจริง ๆ แล้วนักท่องจักรวาลพเนจรผู้นี้มาหาวัตถุเวทมนตร์อันทรงพลังต่างหาก รอบกายเขามักมีวิญญาณ Meep ตัวน้อยลอยตามอยู่มากมาย จนไม่อาจคิดสงสัยได้ว่าการกระทำนั้นเป็นการประสงค์ร้าย เพราะสิ่งที่ Bard ทำนั้นล้วนแต่ทำเพื่อผู้อื่นเสมอ... แม้จะด้วยวิธีแปลก ๆ ของเขาก็ตาม", "blurb": "นักเดินทางผู้ซึ่งมาจากดวงดาว Bard คือตัวแทนแห่งโชคลาภผู้ซึ่งต่อสู้เพื่อรักษาความสมดุลของชีวิตทั้งปวงไม่ให้เกิดความโกลาหลเกินกว่าที่จะรับไหว ชาวรูนเทอร์รามากมายร้องเพลงเพื่อรำลึกถึงตัวตนอันพิเศษของเขา แต่พวกเขาทุกคนก็เชื่อว่าจริง ๆ...", "allytips": ["มันเป็นเรื่องสำคัญในการสะสม Chime เพื่อเพิ่มพลังให้กับการโจมตีของ Meep แต่อย่าลืมช่วยเหลือเพื่อนร่วมเลนของคุณ! พยายามสร้างทางเข้าเลนขนาดใหญ่ด้วย Magical Journey เพื่อนำเพื่อนร่วมทีมเข้าบุกเลนนั้น ๆ", "คุณสามารถปล่อยให้ Caretaker's Shrine สะสมพลังได้ เมื่อพวกมันสะสมพลังจนเต็ม พวกมันก็จะฟื้นฟูพลังชีวิตได้มากขึ้นด้วย", "อย่าลืมว่าศัตรูก็สามารถใช้ประตู Magical Journey ได้เช่นกัน อีกอย่างก็อย่าลืมด้วยว่าสกิลอัลติเมทของคุณโดนเพื่อนร่วมทีมได้!"], "enemytips": ["ทีมฝ่ายตรงข้าม Bard ก็สามารถเคลื่อนที่ผ่านประตู Magical Journey ได้ คุณสามารถเลือกเดินทางติดตามเขาไปได้ ถ้าคุณคิดว่าปลอดภัยที่จะทำ", "คุณสามารถทำลายเทวสถานการรักษาของ Bard ได้ง่าย ๆ โดยเดินไปชนมัน อย่าปล่อยให้ทีมของเขาเก็บพวกมันได้โดยไม่ต้องต่อสู้", "สกิลอัลติเมทของ Bard คือ Tempered Fate มันสามารถส่งผลได้ทั้งฝ่ายเดียวกันและฝ่ายตรงข้าม หรือแม้กระทั่งป้อมเองก็ได้เช่นกัน และในบางครั้ง คุณก็สามารถใช้ประโยชน์จากมันได้เช่นกัน!"], "tags": ["Support", "Mage"], "partype": "มานา", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "Cosmic Binding", "description": "Bard ยิงพลังเวทออกไป มันจะสโลว์ศัตรูตัวแรกที่โดน แล้วทะลุต่อไป และถ้าหากมันชนเข้ากับกำแพง มันจะทำการสตันเป้าหมายแรกแทน และถ้าหากมันชนเข้ากับศัตรูอีกตัว มันจะทำการสตันศัตรูทั้งสองตัว", "tooltip": "Bard ยิงลูกพลังงานออกไป สร้าง<magicDamage>ความเสียหายเวท {{ totaldamage }} หน่วย</magicDamage>ให้แก่ศัตรู 2 ตัวแรกที่โดน ศัตรูตัวแรกที่โดนจะถูก<status>สโลว์</status> {{ slowamountpercentage }}% เป็นเวลา {{ slowduration }} วินาที<br /><br />ถ้าหากลูกศรพลังงานทะลุไปโดนศัตรูหรือโดนกำแพง ศัตรูทั้งหมดที่โดนจะถูก<status>สตัน</status>เป็นเวลา {{ stunduration }} วินาที<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ความเสียหาย", "ระยะเวลาสโลว์", "ระยะเวลาสตัน:", "คูลดาวน์"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Caretaker's Shrine", "description": "แสดงเทวสถานแห่งการรักษาขึ้นมา ซึ่งมันสามารถที่จะเพิ่มพลังมากขึ้นได้ด้วยการรอครู่หนึ่ง และมันจะหายไปเมื่อฟื้นฟูพลังชีวิตและเพิ่มความเร็วเคลื่อนที่ให้เพื่อนร่วมทีมคนแรกที่โดนมัน", "tooltip": "Bard สร้างเสาพลังชีวิตที่จะมอบ<speed>ความเร็วเคลื่อนที่ {{ calc_movespeed }} </speed>ที่จะค่อย ๆ ลดลงในช่วง {{ movespeed_duration }} วินาที และฟื้นฟู<healing>พลังชีวิตอย่างน้อย {{ initialheal }} หน่วย</healing>แก่เพื่อนร่วมทีมคนแรกที่เข้ามา เสาจะเติบโตจนฟื้นฟูพลังชีวิตได้ <healing>{{ maxheal }} หน่วย</healing>หากอยู่ได้นาน {{ chargeuptime }} วินาที<br /><br />Bard สามารถสร้างเสาพลังชีวิตได้สูงสุด {{ maxpacks }} อันพร้อมกัน หากศัตรูเหยียบเสาพลังชีวิต มันจะถูกทำลาย<br /><br />สกิลนี้มี {{ ammo_limit }} ชาร์จ<br /><br />เสาที่เปิดใช้งานในปัจจุบัน: {{ f1 }} / {{ f2 }} อัน{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ฮีลพื้นฐาน", "ฮีลสุงสุด", "ความเร็วเคลื่อนที่"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}% -> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Magical Journey", "description": "Bard เปิดประตูมิติให้กับภูมิประเทศที่อยู่ใกล้เคียง ประตูมิตินี้เป็นประตูแบบเดินผ่านได้ทางเดียว โดยทั้งฝ่ายเดียวกันและฝ่ายตรงข้าม สามารถที่จะใช้ประตูนี้ได้เหมือนกัน ด้วยการเดินเข้าประตูมิติ", "tooltip": "Bard เปิดประตูมิติที่เข้าได้ทางเดียวผ่านสิ่งกีดขวางเป็นเวลา {{ e1 }} วินาที แชมเปี้ยนทุกตัวสามารถเข้าประตูได้โดยการเคลื่อนที่บนมันในขณะที่อยู่ใกล้กับทางเข้า{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["คูลดาวน์"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "Tempered Fate", "description": "Bard ส่งพลังวิญญาณลอยไปสู่พื้นที่เป้าหมาย ทำให้ยูนิตทั้งหมดในพื้นที่ แชมเปี้ยน มินเนี่ยน สัตว์ป่า และรวมถึงป้อมปราการด้วย ถูกเปลี่ยนสถานะเป็นหยุดนิ่งครู่หนึ่ง", "tooltip": "Bard ขว้างพลังเวทป้องกันไปยังพื้นที่เป้าหมาย ทำให้ทุกยูนิตและสิ่งก่อสร้างที่โดนจะเข้าสู่สภาวะหยุดนิ่งเป็นเวลา {{ rstasisduration }} วินาที{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["คูลดาวน์"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Traveler's Call", "description": "<font color='#FF9900'>Meep:</font> Bard จะมีดวงวิญญาณตัวน้อยติดตามมาที่จะช่วยเหลือเขาเมื่อทำการโจมตีปกติจะสร้างความเสียหายเวทเพิ่มเติม เมื่อ Bard สะสม <font color='#cccc00'>Chime</font> เพียงพอ Meep ของเขาจะสร้างความเสียหายในพื้นที่และสโลว์ศัตรูที่โดนด้วย<br><br><font color='#FF9900'>Chime: Chime </font>โบราณ<font color='#cccc00'> จะประกฎขึ้นมาแบบสุ่มเพื่อให้ Bard เก็บ Chime เหล่านี้จะมอบค่าประสบการณ์ ฟื้นฟูมานา และความเร็วเคลื่อนที่เมื่อไม่ได้ต่อสู้</font>", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}