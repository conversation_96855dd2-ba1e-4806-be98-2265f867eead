{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Briar": {"id": "<PERSON><PERSON><PERSON>", "key": "233", "name": "<PERSON><PERSON><PERSON>", "title": "la fame repressa", "image": {"full": "Briar.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "233000", "num": 0, "name": "default", "chromas": false}, {"id": "233001", "num": 1, "name": "<PERSON><PERSON><PERSON> <PERSON> della strada", "chromas": true}, {"id": "233010", "num": 10, "name": "B<PERSON>r <PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, esperimento fallito della Rosa Nera, ha una sete di sangue talmente insaziabile che per controllarla si è resa necessaria una speciale gogna. Dopo anni di detenzione, quest'arma vivente è fuggita dalla sua cella e si è tuffata nel mondo. Ora nessuno la controlla: segue solo la sua fame di sangue e conoscenza, e si gode i momenti in cui può scatenarsi, anche se placare la frenesia non è facile.", "blurb": "<PERSON><PERSON><PERSON>, esperimento fallito della Rosa Nera, ha una sete di sangue talmente insaziabile che per controllarla si è resa necessaria una speciale gogna. Dopo anni di detenzione, quest'arma vivente è fuggita dalla sua cella e si è tuffata nel mondo. Ora...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Furia", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 95, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 30, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 0, "hpregenperlevel": 0, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "BriarQ", "name": "Salto alla gola", "description": "Briar balza verso un'unità e colpisce i nemici con la Ruota (del dolore), stordendoli e riducendone l'armatura.", "tooltip": "Briar balza addosso a un bersaglio <status>stordendolo</status> per {{ stunduration }} secondi, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e riducendone l'<scaleArmor>armatura</scaleArmor> e la <scaleMR>resistenza magica</scaleMR> di un {{ shredpercent*100 }}% per {{ shredduration }} secondi.<br /><br /><rules>Se lancia questa abilità su un minion o un mostro durante <keywordMajor>Frenesia sanguinaria</keywordMajor>, Briar smette di dare la priorità ai campioni.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Riduzione armatura e resistenza magica", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredpercent*100.000000 }}% -> {{ shredpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% della salute attuale", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "BriarQ.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Costa {{ spell.briarp:currenthealthpercentcost*100 }}% della salute attuale"}, {"id": "BriarW", "name": "Frenesia sanguinaria / Morsi della fame", "description": "Briar balza in avanti, frantuma la sua gogna ed entra in Frenesia sanguinaria, che la costringe a inseguire senza sosta il nemico più vicino (dando la priorità ai campioni). Quando è in frenesia, ottiene velocità d'attacco e velocità di movimento aumentate, e i suoi attacchi infliggono danni ad area attorno al bersaglio.<br><br>Mentre è in frenesia, Briar può riattivare questa abilità per ADDENTARE il bersaglio con l'attacco successivo, infliggendo danni aggiuntivi in base alla sua salute mancante e curandosi in base ai danni inflitti.", "tooltip": "<PERSON><PERSON><PERSON> bal<PERSON> ed entra in <keywordMajor>Frenesia sanguinaria</keywordMajor>, auto-provocandosi verso il nemico più vicino per {{ berserkduration }} secondi, dando la priorità ai campioni. <PERSON>tre è in <keywordMajor>Frenesia sanguinaria</keywordMajor>, ottiene un <attackSpeed>{{ berserkas*100 }}% di velocità d'attacco</attackSpeed> e un <speed>{{ berserkms*100 }}% di velocità di movimento</speed>, e i suoi attacchi infliggono <physicalDamage>{{ totalaoedamage }} danni fisici</physicalDamage> ai nemici attorno al bersaglio.<br /><br /><PERSON><PERSON><PERSON> pu<PERSON> <recast>rilanciare</recast> questa abilità per potenziare il suo attacco successivo, che infligge <physicalDamage>{{ totalattackbonusdamage }} + {{ totalattackpercentmissinghealth }}% danni fisici in base alla salute mancante</physicalDamage> e <healing>la cura di un {{ attackmaxhpheal }} + {{ attackhealpercent*100 }}% dei danni inflitti</healing>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Velocità di movimento", "Rapporto attacco fisico danni ad area", "<PERSON><PERSON>", "Guarigione", "Ricarica"], "effect": ["{{ berserkas*100.000000 }}% -> {{ berserkasnl*100.000000 }}%", "{{ berserkms*100.000000 }}% -> {{ berserkmsnl*100.000000 }}%", "{{ aoeattackdamagepercent*100.000000 }}% -> {{ aoeattackdamagepercentnl*100.000000 }}%", "{{ attackbonusdamage }} -> {{ attackbonusdamageNL }}", "{{ attackhealpercent*100.000000 }}% -> {{ attackhealpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% della salute attuale", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "BriarW.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Costa {{ spell.briarp:currenthealthpercentcost*100 }}% della salute attuale"}, {"id": "BriarE", "name": "<PERSON><PERSON><PERSON>", "description": "Briar rit<PERSON>a la concentrazione, rimuovendo Frenesia sanguinaria e canalizzando energia in un potente urlo che danneggia e rallenta i nemici. Mentre carica questa abilità, subisce danni ridotti e si cura di una porzione della sua salute massima. Un urlo caricato al massimo respinge i nemici, infligge danni aggiuntivi e li stordisce se colpiscono un muro.", "tooltip": "<charge>Inizio carica:</charge> Briar rimuove <keywordMajor>Frenesia sanguinaria</keywordMajor> e accumula energia, ottenendo un {{ drpercent }}% di riduzione danni e ripristinando <healing>{{ percentmaxhpheal }} salute</healing> nell'arco di 1 secondo.<br /><br /><release>Rila<PERSON>cio:</release> Briar lancia un urlo che infligge fino a <magicDamage>{{ damage }} danni magici</magicDamage> in base al tempo di carica e <status>rallenta</status> di un {{ slowpercent*100 }}% per {{ slowduration }} secondi. Un urlo caricato al massimo <status>respinge</status> i nemici, infliggendo <magicDamage>{{ wallhitdamage }} danni magici</magicDamage> a quelli che colpiscono un muro e <status>stordendoli</status> per {{ wallstunduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> a<PERSON>iu<PERSON>", "Rapporto guarigione"], "effect": ["{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ wallhitbasedamage }} -> {{ wallhitbasedamageNL }}", "{{ healhppercent*100.000000 }}% -> {{ healhppercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% della salute attuale", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "BriarE.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Costa {{ spell.briarp:currenthealthpercentcost*100 }}% della salute attuale"}, {"id": "BriarR", "name": "Promessa di morte", "description": "Briar calcia l'emolite sulla sua gogna, march<PERSON><PERSON> come sua preda il primo campione che colpisce. Quindi vola dritta verso la sua preda, impaurendo gli altri nemici nelle vicinanze del suo punto di arrivo ed entrando in uno stato di completa emomania. Briar insegue la sua preda fino alla morte, otten<PERSON><PERSON> i vantaggi di Frenesia sanguinaria oltre ad armatura, resistenza magica, rubavita e velocità di movimento aggiuntive.", "tooltip": "Briar calcia l'emolite della gogna e vola verso la posizione del primo campione colpito, che viene marchiato come sua preda. Quando atterra, infligge <magicDamage>{{ damage }} danni magici</magicDamage> a tutto ciò che si trova nelle vicinanze e fa <status>fuggire</status> per {{ fearduration }} secondi i nemici non marchiati come preda. A questo punto, Briar entra in una <keywordMajor>Frenesia sanguinaria</keywordMajor> potenziata e insegue la sua preda fino alla morte. Durante questo periodo, ottiene {{ totalresists }} <scaleArmor>armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR>, un {{ lifestealpercent*100 }}% di rubavita e un ulteriore <speed>{{ extramovespeedpercent*100 }}% di velocità di movimento</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Velocità di movimento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ lifestealpercent*100.000000 }}% -> {{ lifestealpercentnl*100.000000 }}%", "{{ extramovespeedpercent*100.000000 }}% -> {{ extramovespeedpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% della salute attuale", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "BriarR.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Costa {{ spell.briarp:currenthealthpercentcost*100 }}% della salute attuale"}], "passive": {"name": "<PERSON><PERSON><PERSON> cremisi", "description": "Gli attacchi e le abilità di Briar applicano ai nemici un sanguinamento cumulativo che la cura di una porzione dei danni inflitti. Perennemente affamata, la guarigione che ottiene aumenta in base alla sua salute mancante, ma non ha rigenerazione di salute innata.", "image": {"full": "BriarP.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}