{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Elise": {"id": "<PERSON>", "key": "60", "name": "<PERSON>", "title": "regina-<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Elise.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "60000", "num": 0, "name": "default", "chromas": false}, {"id": "60001", "num": 1, "name": "<PERSON>, floarea mor<PERSON>ii", "chromas": false}, {"id": "60002", "num": 2, "name": "<PERSON> vict<PERSON>", "chromas": false}, {"id": "60003", "num": 3, "name": "<PERSON> lun<PERSON> sân<PERSON>ie", "chromas": false}, {"id": "60004", "num": 4, "name": "Elise SKT T1", "chromas": false}, {"id": "60005", "num": 5, "name": "Elise super galactică", "chromas": false}, {"id": "60006", "num": 6, "name": "<PERSON>", "chromas": true}, {"id": "60015", "num": 15, "name": "<PERSON> t<PERSON>", "chromas": true}, {"id": "60024", "num": 24, "name": "<PERSON> sabat", "chromas": true}, {"id": "60034", "num": 34, "name": "<PERSON>, masca T<PERSON>af<PERSON>i Negru", "chromas": false}], "lore": "Elise este un prădător nemilos care trăiește într-un palat scufundat în întuneric din cel mai vechi oraș al Noxusului. A fost odată o muritoare de viță nobilă și stăpână a unei Case puternice din Imperiu, dar mușcătura unui semizeu malefic a transformat-o într-o creatură splendidă și inumană, asemănătoare unui păianjen, care-și atrage prada neștiutoare în plasă. Elise sacrifică inocenți și necredincioși pentru a-și menține tinerețea veșnică, iar puțini sunt cei ce pot rezista farmecelor ei.", "blurb": "Elise este un prădător nemilos care trăiește într-un palat scufundat în întuneric din cel mai vechi oraș al Noxusului. A fost odată o muritoare de viță nobilă și stăpână a unei Case puternice din Imperiu, dar mușcătura unui semizeu malefic a...", "allytips": ["Forma de păianjen este cea mai eficientă pentru exterminarea inamicilor cu nivel de viață scăzut; ''Neurotoxina'' formei umane provoacă daune mai mari adversarilor cu nivel de viață ridicat. ", "Când ia forma de păianjen, puii de păianjen vor ataca ținta asupra căreia Elise folosește ''Mușcătura veninoasă''. ", "Forma de păianjen și abilitățile formei de păianjen a Elisei nu necesită mană și pot fi prioritizate când vrei să păstrezi mana."], "enemytips": ["Forma de păianjen a Elisei este mai periculoasă când ai nivelul de viață scăzut, iar forma umană este mai puternică atunci când ai nivelul de viață ridicat.", "Abilitatea ''Rapel'' o va deplasa pe Elise în sus și în jos pe verticală dacă nu poate coborî asupra unei unități inamice.", "''Rapel'' are timp de reactivare mare. <PERSON><PERSON><PERSON> folosirea acestei abilită<PERSON>i, <PERSON> este vulnerabilă."], "tags": ["Assassin", "Mage"], "partype": "Mană", "info": {"attack": 6, "defense": 5, "magic": 7, "difficulty": 9}, "stats": {"hp": 620, "hpperlevel": 109, "mp": 324, "mpperlevel": 50, "movespeed": 330, "armor": 30, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "EliseHumanQ", "name": "Neurotoxină / Mușcătura veninoasă", "description": "Formă umană: provoacă daune în funcție de cât de ridicat este nivelul de viață al țintei.<br><br>Formă de păianjen: sare spre un inamic și îi provoacă daune în funcție de cât de scăzut este nivelul de viață al acestuia.", "tooltip": "<keywordMajor><PERSON><PERSON> uman<PERSON></keywordMajor>: <PERSON> in<PERSON> o neurotoxină, provocând <magicDamage>daune magice în valoare de {{ basedamage }} plus {{ humanpercenthealth }} din viața actuală</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune ''Neurotoxină''", "Daune ''Mușcătura veninoasă''", "Limită daune împotriva monștrilor", "Cost de man<PERSON> (Neurotoxină)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ spiderbasedamage }} -> {{ spiderbasedamageNL }}", "{{ monstercapdamage }} -> {{ monstercapdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "EliseHumanQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EliseHumanW", "name": "Păianjen exploziv / Frenezia păianjenilor", "description": "Formă umană: eliberează un pui de păianjen plin de venin care explodează când se apropie de o țintă.<br><br>Formă de păianjen: Elise și puii ei de păianjen primesc viteză de atac.", "tooltip": "<keywordMajor><PERSON><PERSON> uman<PERSON></keywordMajor>: <PERSON> in<PERSON> un păianjen exploziv care se deplasează spre o locație-țintă și explodează când se apropie de un inamic sau după 3 secunde. Păianjenul provoacă <magicDamage>{{ spell.elisehumanw:totaldamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [65, 75, 85, 95, 105], [275, 275, 275, 275, 275], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4", "0", "0", "3", "65/75/85/95/105", "275", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "EliseHumanW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EliseHumanE", "name": "Cocon / Rapel", "description": "Formă umană: amețește prima unitate inamică lovită și o dezvăluie dacă nu are prezența mascată.<br><br>Formă de păianjen: Elise și puii ei de păianjen se ridică în aer, apoi cad peste inamicul-țintă. <PERSON><PERSON><PERSON> cădere, vindecarea și daunele bonus primite de Elise din ''Regina-păianjen'' cresc.", "tooltip": "<keywordMajor><PERSON><PERSON></keywordMajor>: <PERSON> un cocon, <status>ame<PERSON><PERSON></status> și dezvăluind primul inamic lovit timp de {{ stunduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Durată amețire:", "Timp de reactivare (Cocon)", "Timp de reactivare (Rapel)", "Cresc daunele și vindecarea"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e3 }} -> {{ e3NL }}", "{{ e6 }}% -> {{ e6NL }}%"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [12, 11.5, 11, 10.5, 10], [15, 20, 25, 30, 35], [22, 21, 20, 19, 18], [2, 2, 2, 2, 2], [1.6, 1.8, 2, 2.2, 2.4], [40, 55, 70, 85, 100], [250, 250, 250, 250, 250], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "12/11.5/11/10.5/10", "15/20/25/30/35", "22/21/20/19/18", "2", "1.6/1.8/2/2.2/2.4", "40/55/70/85/100", "250", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EliseHumanE.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EliseR", "name": "Formă de păianjen", "description": "<PERSON> se transformă într-un păianjen amenințător; raza de atac îi scade, iar viteza de mișcare îi crește. În plus, primește noi abilități și un roi de pui de păianjen care-i vor ataca inamicii.", "tooltip": "<keywordMajor><PERSON><PERSON></keywordMajor>: <PERSON> se <PERSON>ă într-un păianjen ameni<PERSON>, având rază de atac melee, primind acces la abilitățile <keywordMajor>formei de păianjen</keywordMajor> și invocând toți <keywordMajor>puii de păianjen</keywordMajor> disponibili.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune mușcătură ''Formă de păianjen''", "Daune bonus pui de p<PERSON><PERSON>jen", "<PERSON><PERSON><PERSON>r maxim de pui de p<PERSON>ianjen", "Armură pui de păianjen", "Rezistență la magie pui de păianjen"], "effect": ["{{ passivebonusdamage }} -> {{ passivebonusdamageNL }}", "{{ spiderlingbasedamage }} -> {{ spiderlingbasedamageNL }}", "{{ spiderlingsstored }} -> {{ spiderlingsstoredNL }}", "{{ e6 }} -> {{ e6NL }}", "{{ e7 }} -> {{ e7NL }}"]}, "maxrank": 4, "cooldown": [3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [12, 22, 32, 42], [25, 25, 25, 25], [25, 25, 25, 25], [2, 3, 4, 5], [30, 50, 70, 90], [50, 70, 90, 110], [6, 8, 10, 12], [0.08, 0.08, 0.08, 0.08], [0, 0, 0, 0]], "effectBurn": [null, "0", "12/22/32/42", "25", "25", "2/3/4/5", "30/50/70/90", "50/70/90/110", "6/8/10/12", "0.08", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "EliseR.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Fără cost"}], "passive": {"name": "Regina-p<PERSON><PERSON><PERSON><PERSON>", "description": "Formă umană: când abilităț<PERSON> ei lovesc un inamic, <PERSON> prime<PERSON>te un pui de păianjen.<br><br>Formă de păianjen: atacurile de bază provoacă daune magice bonus și îi refac viața Elisei.", "image": {"full": "ElisePassive.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}