{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "<PERSON><PERSON><PERSON>", "title": "the Sad Mummy", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32002", "num": 2, "name": "Vancouver Amumu", "chromas": false}, {"id": "32003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32004", "num": 4, "name": "Re-Gifted <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32005", "num": 5, "name": "Almost-Prom King <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32006", "num": 6, "name": "Little Knight Amumu", "chromas": false}, {"id": "32007", "num": 7, "name": "Sad Robot <PERSON>u", "chromas": false}, {"id": "32008", "num": 8, "name": "Surprise Party Amumu", "chromas": true}, {"id": "32017", "num": 17, "name": "Infernal Amumu", "chromas": true}, {"id": "32023", "num": 23, "name": "Hextech Amumu", "chromas": false}, {"id": "32024", "num": 24, "name": "Pumpkin Prince <PERSON>", "chromas": true}, {"id": "32034", "num": 34, "name": "Po<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "32044", "num": 44, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "32053", "num": 53, "name": "Dumpling <PERSON><PERSON>", "chromas": true}], "lore": "Konon Amumu adalah jiwa yang kesepian dan melankolis dari <PERSON><PERSON> kuno, yang berkeliaran di dunia untuk mencari teman. Kutukan kuno membuat dia sendirian selam<PERSON>, sent<PERSON><PERSON><PERSON> adalah kematian, ka<PERSON><PERSON> say<PERSON><PERSON> adalah kehancuran. Bagi mereka yang mengaku pernah melihatnya, dia digambarkan sebagai mayat hidup, bertubuh kerempeng dan terbungkus perban. Amumu telah menjadi sosok mitos, lagu, dan cerita rakyat yang turun-temurun, se<PERSON>ga mustahil untuk memisahkan antara kebenaran dengan khayalan.", "blurb": "<PERSON>non Amumu adalah jiwa yang kesepian dan melankolis dari <PERSON><PERSON> kuno, yang berkeliaran di dunia untuk mencari teman. Kutukan kuno membuat dia sendiri<PERSON> se<PERSON>, sent<PERSON><PERSON><PERSON> adalah kema<PERSON>, ka<PERSON>h say<PERSON> adalah kehan<PERSON>ran. Bagi mereka yang mengaku...", "allytips": ["<PERSON><PERSON>u sangat bergantung pada tim, jadi coba bersihkan jalur bersama teman untuk hasil maksimum.", "Cooldown Reduction pada Amumu sangat kuat, tetapi sulit untuk membeli itemnya. Ambil buff Golem jika memungkinkan untuk mendapatkan Cooldown Reduction tanpa mengorbankan stat.", "Despair sangat efektif melawan tank lain. <PERSON><PERSON> pastikan kamu berada dalam jangkauan musuh dengan Health tertinggi."], "enemytips": ["<PERSON><PERSON> be<PERSON>ul dengan teman satu tim saat Amumu siap menggunakan ultimanya.", "Gerakan yang tidak stabil atau berlindung di balik gelombang creep bisa membuat Amumu kesulitan memulai pertarungan dengan Bandage Toss.", "Despair Amumu menjadikan pembelian item Health lebih berisiko."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "Bandage Toss", "description": "<PERSON>umu melempar perban lengket ke arah target, menerapkan stun dan men<PERSON><PERSON>an damage pada target serta menarik dirinya ke arah target.", "tooltip": "<PERSON><PERSON><PERSON> me<PERSON> perban, menarik dirinya ke arah musuh yang pertama kali terkena, menera<PERSON><PERSON> <status>Stun</status> selama {{ e2 }} detik, dan men<PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Ability ini memiliki 2 charge.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ ammorechargetime }}-> {{ ammorechargetimeNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "Despair", "description": "<PERSON><PERSON><PERSON><PERSON>der<PERSON>, musuh di sekitar kehilangan sejumlah persentase dari Health maksimum mereka per detik dan efek <font color='#9b0f5f'>Curse</font> pada mereka di-refresh.", "tooltip": "<toggle>Toggle</toggle>: <PERSON><PERSON><PERSON> mulai men<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ basedamage }} plus {{ totalhealthdamage }}% magic damage dari Health maksimum</magicDamage> ke musuh di sekitar setiap detik dan me-refresh <keywordMajor>Curse</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% Damage Health"], "effect": ["{{ healthdamage }}%-> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }} per Detik", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }} per Detik"}, {"id": "Tantrum", "name": "Tantrum", "description": "Mengurangi physical damage yang diterima Amumu secara permanen. Amumu bisa melepaskan amukan dan men<PERSON><PERSON>lkan damage ke musuh di sekitarnya. Tiap kali Amumu terkena serangan, cooldown Tantrum berkurang.", "tooltip": "<spellPassive>Pasif:</spellPassive> Amumu men<PERSON> {{ damagereduction }} pengurangan physical damage. <PERSON><PERSON> itu, saat Amumu terkena <PERSON>, Cooldown Ability ini berkurang {{ e3 }} detik.<br /><br /><spellActive>Aktif:</spellActive> Amumu melempar tantrum, men<PERSON><PERSON><PERSON>an <magicDamage>{{ tantrumdamage }} magic damage</magicDamage> pada musuh di sekitar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage yang <PERSON>i", "Cooldown", "Damage"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ e2 }}-> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "Curse of the Sad Mummy", "description": "Amumu mengikat unit musuh di sekelilingnya dengan perban, menerapkan <keywordMajor>Curse</keywordMajor>, memberi mereka damage dan stun.", "tooltip": "<PERSON><PERSON><PERSON> mele<PERSON>an perbannya, men<PERSON><PERSON><PERSON> <status><PERSON>un</status> selama {{ rduration }} de<PERSON><PERSON>, men<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ rcalculateddamage }} magic damage</magicDamage> dan menerapkan <keywordMajor>Curse</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@ Cost", "Cooldown", "Damage"], "effect": ["{{ cost }}-> {{ costNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ rdamage }}-> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Cursed Touch", "description": "Basic attack <PERSON><PERSON><PERSON> <font color='#9b0f5f'>Curse</font> pada musuh<PERSON>, menyebabkan mereka menerima true damage bonus dari magic damage yang diterima.", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}