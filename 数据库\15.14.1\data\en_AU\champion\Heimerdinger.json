{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Heimerdinger": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "74", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "the Revered Inventor", "image": {"full": "Heimerdinger.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "74000", "num": 0, "name": "default", "chromas": false}, {"id": "74001", "num": 1, "name": "Alien Invader <PERSON>", "chromas": false}, {"id": "74002", "num": 2, "name": "Blast Zone Heimerdinger", "chromas": false}, {"id": "74003", "num": 3, "name": "Piltover Customs Heimerdinger", "chromas": false}, {"id": "74004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74006", "num": 6, "name": "Dragon Trainer <PERSON>", "chromas": false}, {"id": "74015", "num": 15, "name": "Pool <PERSON> Heimerdinger", "chromas": false}, {"id": "74024", "num": 24, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74033", "num": 33, "name": "Arcane Professor <PERSON>", "chromas": false}], "lore": "The eccentric Professor <PERSON> is one of the most innovative and esteemed inventors the world has ever known. As the longest serving member of the Council of Piltover, he saw the best and the worst of the city's unending desire for progress. Nonetheless, this brilliant scientist and teacher will always remain dedicated to using his unconventional devices to improve the lives of others.", "blurb": "The eccentric Professor <PERSON> is one of the most innovative and esteemed inventors the world has ever known. As the longest serving member of the Council of Piltover, he saw the best and the worst of the city's unending desire for...", "allytips": ["Turret placement can be a deciding factor in a battle. Against most enemies turrets are best when they can support each other, but if the enemy has a lot of area of effect damage your turrets can be destroyed quickly. Placing turrets in brush can lead to a surprise attack in your favor.", "The success of Electron Storm Grenade is very important to <PERSON><PERSON><PERSON><PERSON>'s survival. The slow and stun both are capable of keeping enemies in place long enough to punish them, but it is also the first line of defense against a surprise attack.", "Spreading out Hextech Micro-Rockets leads to more reliable damage and more efficiency against multiple targets, but focusing it creates the biggest impact."], "enemytips": ["It's better to destroy all of He<PERSON>rdinger's turrets at once with the help of creeps than to fight them one at a time.", "Be wary of <PERSON><PERSON><PERSON><PERSON>'s Upgrade!!!, as he can use it to find an answer to most trouble he gets into. Once his ultimate is down, go in for the kill!"], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 8}, "stats": {"hp": 558, "hpperlevel": 101, "mp": 385, "mpperlevel": 20, "movespeed": 340, "armor": 19, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.7, "attackspeedperlevel": 1.36, "attackspeed": 0.658}, "spells": [{"id": "<PERSON><PERSON>rdingerQ", "name": "H-28 G <PERSON> Turret", "description": "<PERSON><PERSON>rdinger lays down a rapid-fire cannon turret equipped with a secondary pass-through beam attack (turrets deal half damage to towers).", "tooltip": "<PERSON><PERSON><PERSON><PERSON> constructs a <keywordMajor>Turret</keywordMajor> that attacks nearby enemies. He<PERSON>rdi<PERSON> can have {{ maxturrets }} turrets active at once. <keywordMajor>Turrets</keywordMajor> slowly build up charge. At max charge, they fire a stronger attack.<br /><br />If <PERSON><PERSON><PERSON><PERSON> gets too far away, his <keywordMajor>Turrets</keywordMajor> will deactivate after 8 seconds.<br /><br />This Ability has {{ maxkits }} charges.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cannon Damage", "Beam Damage"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamagebeam }} -> {{ basedamagebeamNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "900", "0", "0", "0", "0", "0"], "vars": [], "costType": " Turret Kit & {{ cost }} Mana", "maxammo": "3", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HeimerdingerQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ kitcost }} Turret Kit & {{ cost }} Mana"}, {"id": "HeimerdingerW", "name": "Hextech Micro-Rockets", "description": "<PERSON><PERSON><PERSON><PERSON> fires long-range rockets that converge on his cursor.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> unleashes a barrage of {{ rockets }} rockets that deal <magicDamage>{{ damage }} magic damage</magicDamage> to the first enemy hit. Additional rocket hits deal reduced damage.<br /><br />Max Damage: <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Nearby <keywordMajor>Turrets</keywordMajor> gain 20% charge per rocket that hits a champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [12, 18, 24, 30, 36], [25, 25, 25, 25, 25], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [108, 162, 216, 270, 324], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "12/18/24/30/36", "25", "20", "30", "108/162/216/270/324", "5", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1325, 1325, 1325, 1325, 1325], "rangeBurn": "1325", "image": {"full": "HeimerdingerW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HeimerdingerE", "name": "CH-2 Electron Storm Grenade", "description": "<PERSON><PERSON><PERSON><PERSON> lobs a grenade at a location, dealing damage to enemy units, as well as stunning anyone directly hit and slowing surrounding units.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> hurls a grenade that deals <magicDamage>{{ damage }} magic damage</magicDamage> in an area and <status>Slows</status> by {{ slowpercent.0*100 }}% for {{ slowduration }} seconds. Enemies in the center are also <status>Stunned</status> for {{ stunduration }} seconds.<br /><br />Hitting a champion fully charges nearby <keywordMajor>Turrets</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [85, 85, 85, 85, 85], "costBurn": "85", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [970, 970, 970, 970, 970], "rangeBurn": "970", "image": {"full": "HeimerdingerE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HeimerdingerR", "name": "UPGRADE!!!", "description": "<PERSON><PERSON><PERSON><PERSON> invents an upgrade, causing his next spell to have increased effects. ", "tooltip": "<PERSON><PERSON><PERSON><PERSON> upgrades his next non-ultimate Ability.<br /><br /><spellName>H-28Q Apex Turret:</spellName> Place an upgraded <keywordMajor>Turret</keywordMajor> for 8 seconds that doesn't count towards <PERSON><PERSON><PERSON><PERSON>'s max turrets, deals <magicDamage>{{ qultdamage }} magic damage</magicDamage> per shot and <magicDamage>{{ qultdamagebeam }} magic damage</magicDamage> per charged shot. Its attacks deal damage in an area, <status>Slow</status> by 25% for 2 seconds, and it is immune to crowd control.<br /><br /><spellName>Hextech Rocket Swarm:</spellName> Fires 4 waves of rockets that each deal <magicDamage>{{ wultdamage }} magic damage</magicDamage>. Champions and jungle monsters hit by additional rockets take reduced damage, and minions take increased damage. Max Damage: <magicDamage>{{ wulttotaldamage }} magic damage</magicDamage>. <br /><br /><spellName>CH-3X Lightning Grenade:</spellName> Throws a bouncing grenade that discharges three times, dealing <magicDamage>{{ eultdamage }} magic damage</magicDamage>. The <status>Stun</status> and <status>Slow</status> areas are larger.<br /><br /><recast>Recast:</recast> Cancel this Ability.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Apex Turret Cannon Damage", "<PERSON><PERSON><PERSON> Damage", "Rocket Swarm Damage", "Rocket Swarm Max Damage", "Lightning Grenade Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qultbasedamage }} -> {{ qultbasedamageNL }}", "{{ qultbasedamagebeam }} -> {{ qultbasedamagebeamNL }}", "{{ wultbasedamage }} -> {{ wultbasedamageNL }}", "{{ wulttotalbasedamage }} -> {{ wulttotalbasedamageNL }}", "{{ eultbasedamage }} -> {{ eultbasedamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 140, 180], [80, 80, 80], [1.5, 1.5, 1.5], [0.12, 0.12, 0.12], [500, 690, 865], [0.45, 0.45, 0.45], [80, 100, 120], [135, 180, 225], [28, 39, 49], [150, 250, 350]], "effectBurn": [null, "100/140/180", "80", "1.5", "0.12", "500/690/865", "0.45", "80/100/120", "135/180/225", "28/39/49", "150/250/350"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "HeimerdingerR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Hextech Affinity", "description": "<PERSON>ain Move Speed while near allied towers and turrets deployed by <PERSON><PERSON><PERSON><PERSON>.", "image": {"full": "Heimerdinger_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}