{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jinx": {"id": "<PERSON><PERSON>", "key": "222", "name": "징크스", "title": "난폭한 말괄량이", "image": {"full": "Jinx.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "222000", "num": 0, "name": "default", "chromas": false}, {"id": "222001", "num": 1, "name": "범죄 도시 징크스", "chromas": false}, {"id": "222002", "num": 2, "name": "불꽃놀이 징크스", "chromas": true}, {"id": "222003", "num": 3, "name": "좀비 슬레이어 징크스", "chromas": false}, {"id": "222004", "num": 4, "name": "별 수호자 징크스", "chromas": true}, {"id": "222012", "num": 12, "name": "당돌한 엘프 징크스", "chromas": false}, {"id": "222013", "num": 13, "name": "오디세이 징크스", "chromas": true}, {"id": "222020", "num": 20, "name": "프로젝트: 징크스", "chromas": true}, {"id": "222029", "num": 29, "name": "사랑의 추적자 징크스", "chromas": true}, {"id": "222037", "num": 37, "name": "아케인 적대자 징크스", "chromas": false}, {"id": "222038", "num": 38, "name": "전투 고양이 징크스", "chromas": true}, {"id": "222040", "num": 40, "name": "프레스티지 전투 고양이 징크스", "chromas": false}, {"id": "222051", "num": 51, "name": "귀염둥이 카페 징크스", "chromas": true}, {"id": "222060", "num": 60, "name": "아케인 분열 징크스", "chromas": false}, {"id": "222062", "num": 62, "name": "T1 징크스", "chromas": false}], "lore": "지하도시 출신의 충동적이고 불안정한 범죄자 징크스는 자신의 과거가 빚어낸 결과에 시달리면서도 필트오버와 자운에 특유의 대혼란을 일으키는 것을 멈추지 않는다. 손수 만든 무기를 파괴적으로 사용해 화려한 폭발과 총격을 쏟아내며 가는 곳마다 쑥대밭을 만들어 소외된 자들을 반란과 저항으로 이끈다.", "blurb": "지하도시 출신의 충동적이고 불안정한 범죄자 징크스는 자신의 과거가 빚어낸 결과에 시달리면서도 필트오버와 자운에 특유의 대혼란을 일으키는 것을 멈추지 않는다. 손수 만든 무기를 파괴적으로 사용해 화려한 폭발과 총격을 쏟아내며 가는 곳마다 쑥대밭을 만들어 소외된 자들을 반란과 저항으로 이끈다.", "allytips": ["반드시 로켓이 더 좋은 것은 아닙니다! 징크스의 미니건은 완전히 공격 속도가 상승하면 위력이 엄청나게 커집니다. 적 챔피언이 너무 가까이 붙으면 반드시 미니건으로 전환하세요.", "징크스의 로켓은 폭발 반경 안의 모든 적에게 최대치의 피해를 입힙니다. 적 챔피언 옆에 있는 미니언들에게 사용해서 미니언의 공격 대상이 되지 않도록 하세요.", "교전이 시작되면 진형 끝에 서서 로켓 런처와 빠직! 스킬로 원거리 공격을 하세요! 안전이 확보될 때까지는 달려들어가 미니건을 난사하지 않는 게 좋습니다."], "enemytips": ["징크스의 미니건은 공격 속도가 상승할 때까지 시간이 걸립니다. 거리를 두고 로켓으로 공격한다면 덮쳐서 빠르게 녹여버리세요.", "징크스의 궁극기는 가까이 있을수록 피해량이 적습니다.", "징크스의 주력 방어 스킬인 와작와작 지뢰는 재사용 대기시간이 깁니다. 지뢰가 재사용 대기중일 때 전투가 벌어지면 징크스가 도주하기 어려운 점을 활용하세요."], "tags": ["Marksman"], "partype": "마나", "info": {"attack": 9, "defense": 2, "magic": 4, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 105, "mp": 260, "mpperlevel": 50, "movespeed": 325, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.75, "hpregenperlevel": 0.5, "mpregen": 6.7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.25, "attackspeedperlevel": 1.4, "attackspeed": 0.625}, "spells": [{"id": "JinxQ", "name": "휘릭휘릭!", "description": "징크스는 '빵야빵야' 미니건과 '생선대가리' 로켓 런처 중에서 원하는 것을 선택하여 기본 공격을 변경할 수 있습니다. 미니건으로 공격하면 공격 속도가 상승하고, 로켓 런처로 공격하면 광역 피해를 입히고 사거리가 증가하지만 마나가 소모되고 공격 속도가 느려집니다.", "tooltip": "징크스가 생선대가리 로켓 런처와 빵야빵야 미니건을 변환합니다.<br /><br />로켓 런처로 기본 공격 시 마나를 소모하여 대상과 주변 적들에게 <physicalDamage>{{ rocketdamage }}의 물리 피해</physicalDamage>를 입힙니다. 추가 공격 속도는 {{ rocketaspdpenalty*100 }}% 느려지지만 사거리는 {{ rocketbonusrange }}만큼 증가합니다.<br /><br />미니건으로 기본 공격 시 {{ minigunattackspeedduration }}초 동안 <attackSpeed>공격 속도</attackSpeed>가 상승합니다. 이 효과는 최대 {{ minigunattackspeedstacks }}번까지 중첩됩니다. (<attackSpeed>최대 %i:scaleAS%의 {{ minigunattackspeedmax }}%</attackSpeed>){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["로켓 추가 사거리", "미니건의 총 공격 속도 증가분"], "effect": ["{{ rocketbonusrange }} -> {{ rocketbonusrangeNL }}", "{{ minigunattackspeedmax }}% -> {{ minigunattackspeedmaxNL }}%"]}, "maxrank": 5, "cooldown": [0.9, 0.9, 0.9, 0.9, 0.9], "cooldownBurn": "0.9", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JinxQ.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "로켓 1발당 마나 {{ cost }}"}, {"id": "JinxW", "name": "빠직!", "description": "징크스가 빠직!으로 전기 충격파를 발사해 처음 적중한 적을 둔화시키고 위치를 드러내며 피해를 입힙니다.", "tooltip": "징크스가 전기 충격파를 발사하여 처음 맞힌 적에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ slowduration }}초 동안 {{ slowpercent }}% <status>둔화</status>시키며 위치를 드러냅니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "JinxW.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "JinxE", "name": "와작와작 뻥!", "description": "징크스가 와작와작 지뢰를 일렬로 던지면 5초 후에 폭발하며 주변 적들을 불태웁니다. 와작와작 지뢰는 적 챔피언이 밟으면 물어뜯어 제자리에 묶습니다.", "tooltip": "징크스가 {{ grenadeduration }}초간 유지되는 와작와작 지뢰 3개를 던집니다. 적 챔피언이 닿으면 {{ rootduration }}초 동안 <status>속박</status>시키고 폭발하여 주변 적들에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 20.5, 17, 13.5, 10], "cooldownBurn": "24/20.5/17/13.5/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "JinxE.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "JinxR", "name": "초강력 초토화 로켓!", "description": "징크스가 맵을 가로지르는 강력한 로켓을 발사합니다. 발사 후 비행 거리에 비례해 로켓의 피해량이 증가합니다. 로켓은 적 챔피언과 충돌하면 폭발하며, 해당 챔피언과 주위의 적에게 잃은 체력에 비례하여 피해를 입힙니다.", "tooltip": "징크스가 로켓을 발사합니다. 로켓은 발사 후 첫 1초 동안 피해량이 커지며, 적 챔피언을 맞히면 폭발하여 <physicalDamage>{{ damagefloor }}~{{ damagemax }}+대상이 잃은 체력의 {{ percentdamage }}%에 해당하는 물리 피해</physicalDamage>를 입힙니다. 주변 적들은 {{ aoedamagemult*100 }}%의 피해를 입습니다.<br /><br /><rules>몬스터를 상대로는 잃은 체력 비례 피해량이 {{ monsterexecutemax }}까지만 적용됩니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최소 피해량", "최대 피해량", "잃은 체력 비례 피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ percentdamage }}% -> {{ percentdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [85, 65, 45], "cooldownBurn": "85/65/45", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JinxR.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "신난다!", "description": "징크스는 적 챔피언이나 에픽 정글 몬스터 처치에 관여하거나 구조물 파괴를 도우면 이동 속도와 공격 속도가 대폭 상승합니다.", "image": {"full": "Jinx_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}