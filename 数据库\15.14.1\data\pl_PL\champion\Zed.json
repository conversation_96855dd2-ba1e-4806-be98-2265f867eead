{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zed": {"id": "<PERSON><PERSON>", "key": "238", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Zed.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "238000", "num": 0, "name": "default", "chromas": false}, {"id": "238001", "num": 1, "name": "Zed Burzowego Ostrza", "chromas": true}, {"id": "238002", "num": 2, "name": "SKT T1 Zed", "chromas": true}, {"id": "238003", "num": 3, "name": "PROJEKT: <PERSON><PERSON>", "chromas": true}, {"id": "238010", "num": 10, "name": "Zed Mistrzostw 2016", "chromas": true}, {"id": "238011", "num": 11, "name": "Zed Zaprzysiężony <PERSON>rci", "chromas": false}, {"id": "238013", "num": 13, "name": "Pogromca Galaktyki Zed", "chromas": true}, {"id": "238015", "num": 15, "name": "Zed z Psychoperacji", "chromas": true}, {"id": "238030", "num": 30, "name": "PROJEKT: <PERSON><PERSON> (Prestiżowy)", "chromas": false}, {"id": "238031", "num": 31, "name": "Wytworny Zed", "chromas": true}, {"id": "238038", "num": 38, "name": "Zed Empireum", "chromas": true}, {"id": "238049", "num": 49, "name": "Zed Nieśmiertelnej Podróży", "chromas": true}, {"id": "238058", "num": 58, "name": "Zed Krwawego Księżyca", "chromas": true}, {"id": "238068", "num": 68, "name": "Kwantowy Pogromca Galaktyki Zed", "chromas": false}, {"id": "238069", "num": 69, "name": "Zed Duchowego <PERSON> (Prestiżowy)", "chromas": true}], "lore": "Całkowicie bezwzględny i pozbawiony litości, Zed jest przywódcą Zakonu Cienia, c<PERSON><PERSON> organizacji, k<PERSON><PERSON><PERSON><PERSON> stwo<PERSON>, kieru<PERSON><PERSON><PERSON> się zmilitaryzowaniem sztuk walki Ionii, by w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> noxiańskich najeźdźców. Podczas wojny desperacja sprawiła, że odnalazł sekretną formę cienia — nikczemną magię ducha, równie niebezpieczną i wypaczającą, co potężną. Zed stał się mistrzem tych zakazanych technik, by <PERSON><PERSON><PERSON><PERSON><PERSON>, co mogłoby zagrażać jego narodowi lub nowemu zakonowi.", "blurb": "Całkowicie bezwzględny i pozbawiony lito<PERSON>, Zed jest przywódcą Zakonu Cienia, c<PERSON>li organizacji, k<PERSON><PERSON><PERSON><PERSON> stworz<PERSON>ł, kieru<PERSON><PERSON><PERSON> się zmilitaryzowaniem sztuk walki Ionii, by w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> noxiańskich najeźdźców. Podczas wojny desperacja sprawiła, że odnalazł...", "allytips": ["Oszczędzanie energii i czasów odnowienia zaklęć przed użyciem superumiejętności zmaksymalizuje obrażenia od Znaku Śmierci.", "Szybkie dwukrotne stuknięcie w Żywy Cień natychmiast teleportuje Zeda do jego cienia, co przydaje się w przypadku konieczności szybkiej ucieczki.", "Zastosowanie Żywego Cienia przed użyciem Znaku Śmierci stanowi dla Zeda szansę ucieczki przed walką."], "enemytips": ["Zed otrzymuje premie z przedmiotów z obrażeniami od ataku, więc pancerz jest przeciw niemu niezwykle skuteczny.", "Gdy Zed użyje Żywego Cienia staje się wrażliwy na ataki, ponieważ umiej<PERSON>t<PERSON>ć ta odpowiada za jego obrażenia, efekt spowolnienia i mobilność.", "Cięcie Cienia Zeda spowalnia tylko wtedy, gdy trafi cię nim jego cień. "], "tags": ["Assassin"], "partype": "Energia", "info": {"attack": 9, "defense": 2, "magic": 1, "difficulty": 7}, "stats": {"hp": 654, "hpperlevel": 99, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 4.7, "spellblock": 29, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.65, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3.4, "attackspeedperlevel": 3.3, "attackspeed": 0.651}, "spells": [{"id": "ZedQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Zed i jego cienie rzucają shurikenami.<br><br><PERSON><PERSON><PERSON> shuriken zadaje obrażenia wszystkim trafionym wrogom.", "tooltip": "Z<PERSON> i jego <keywordMajor>cienie</keywordMajor> r<PERSON><PERSON><PERSON><PERSON> ostrzami, a każde zadaje <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> pierwszemu przeszytemu wrogowi i <physicalDamage>{{ passthroughdamage }} pkt. obrażeń fizycznych</physicalDamage> wszystkim następnym.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [75, 70, 65, 60, 55], "costBurn": "75/70/65/60/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZedQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZedW", "name": "Żywy Cień", "description": "<font color='#FF9900'>Biernie:</font> Zed zyskuje energię, gdy on i jego cienie trafią wroga tą samą umiejętnością. Energię można zyskać jedynie raz na każdą użytą umiejętność.<br><br><font color='#FF9900'>Użycie:</font> Cień Zeda doskakuje w przód i pozostaje w miejscu przez kilka sekund. Ponowna aktywacja Żywego Cienia spowoduje, że Zed zamieni się miejscami ze swoim cieniem.", "tooltip": "<spellPassive>Biernie:</spellPassive> <PERSON><PERSON> <keywordMajor>{{ e3 }} pkt. energii</keywordMajor>, gdy on i jego <keywordMajor>cienie</keywordMajor> trafiają wroga tą samą umiejętnością.<br /><br /><spellActive>Użycie:</spellActive> <keywordMajor>Cień</keywordMajor> Zeda doskakuje w przód i pozostaje w miejs<PERSON> przez {{ e5 }} sek. <recast>Ponowne użycie</recast> tej umiejętności spowoduje, że Zed zamieni się miejscami ze swoim <keywordMajor>cieniem</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zwrot energii", "Koszt (@AbilityResourceName@)", "Czas odnowienia"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [5.25, 5.25, 5.25, 5.25, 5.25], [0.2, 0.2, 0.2, 0.2, 0.2], [30, 35, 40, 45, 50], [5.25, 5.25, 5.25, 5.25, 5.25], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5.25", "0.2", "30/35/40/45/50", "5.25", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "ZedW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZedE", "name": "Cięcie Cienia", "description": "Zed i jego cienie atakują, zadając obrażenia pobliskim wrogom. Wrogowie trafieni przez Cięcie Cienia zostają spowolnieni.", "tooltip": "Zed i jego <keywordMajor>cienie</keywordMajor> atakują, zadając <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> pobliskim wrogom.<br /><br /><PERSON><PERSON><PERSON><PERSON> Zeda skraca czas odnowienia <spellName>Żywego Cienia</spellName> o {{ shadowhitcdr }} sek. za każdego trafionego wrogiego bohatera.<br /><br />Wrogowie trafieni przez <keywordMajor>cienie</keywordMajor> zostają <status>spowolnieni</status> o {{ movespeedmod*-100 }}% na {{ slowduration }} sek. Wrogowie trafieni przez wiele cięć będą <status>spowolnieni</status> o {{ movespeedmodbonus*-100 }}%, ale nie otrzymają dodatkowych obrażeń.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeedmod*-100.000000 }}% -> {{ movespeedmodnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5, 4.5, 4, 3.5, 3], "cooldownBurn": "5/4.5/4/3.5/3", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [290, 290, 290, 290, 290], "rangeBurn": "290", "image": {"full": "ZedE.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZedR", "name": "<PERSON>nak <PERSON>", "description": "Zed staje się niemożliwy do obrania za cel i doskakuje do wrogiego bohatera, oznaczając go. Po 3 sek. znak aktywuje się, ponownie zadając obrażenia, j<PERSON><PERSON> <PERSON>adał <PERSON>elowi, gdy ten miał na sobie znak.", "tooltip": "Zed staje się niemożliwy do obrania na cel i doskakuje do wrogiego bohatera, oznaczając go. Po {{ rdeathmarkduration }} sek. znak aktywuje się, zadając <physicalDamage>{{ rcalculateddamage }} pkt. obrażeń fizycznych</physicalDamage> i ponawiając {{ rdamageamp*100 }}% wszystkich obrażeń zadanych celowi przez Zeda oraz jego cienie podczas działania znaku.<br /><br />Wykonanie doskoku pozostawia <keywordMajor>cień</keywordMajor>, który utrzymuje się przez {{ rshadowdurationdisplayed }} sek. Zed może <recast>ponownie użyć</recast> tej umiej<PERSON>, by zamieni<PERSON> się miejscami ze swoim <keywordMajor>cieniem</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia aktywacji znaku", "Czas odnowienia"], "effect": ["{{ rdamageamp*100.000000 }}% -> {{ rdamageampnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "ZedR.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Darmowa"}], "passive": {"name": "Pogarda dla Słabych", "description": "Podstawowe ataki Zeda zadają dodatkowe obrażenia magiczne wrogom z niskim poziomem zdrowia. Ten efekt może dotknąć tego samego wrogiego bohatera tylko raz na kilka sekund.", "image": {"full": "ZedP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}