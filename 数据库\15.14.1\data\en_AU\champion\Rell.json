{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rell": {"id": "<PERSON><PERSON>", "key": "526", "name": "<PERSON><PERSON>", "title": "the Iron Maiden", "image": {"full": "Rell.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "526000", "num": 0, "name": "default", "chromas": false}, {"id": "526001", "num": 1, "name": "Battle Queen <PERSON>ll", "chromas": true}, {"id": "526010", "num": 10, "name": "Star Guardian Rell", "chromas": true}, {"id": "526020", "num": 20, "name": "High Noon <PERSON>ll", "chromas": true}, {"id": "526030", "num": 30, "name": "Grand Reckoning Rell", "chromas": false}], "lore": "The product of brutal experimentation at the hands of the Black Rose, <PERSON><PERSON> is a defiant, living weapon determined to topple <PERSON>x<PERSON>. Her childhood was one of misery and horror, enduring unspeakable procedures to perfect and weaponize her magical control over metal... until she staged a violent escape, killing many of her captors in the process. Now branded as a criminal, <PERSON><PERSON> attacks Noxian soldiers on sight as she searches for survivors of her old “academy,” defending the meek while delivering violent death to her former overseers.", "blurb": "The product of brutal experimentation at the hands of the Black Rose, <PERSON><PERSON> is a defiant, living weapon determined to topple <PERSON>x<PERSON>. Her childhood was one of misery and horror, enduring unspeakable procedures to perfect and weaponize her magical control...", "allytips": [], "enemytips": [], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 620, "hpperlevel": 104, "mp": 320, "mpperlevel": 40, "movespeed": 315, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.8, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.85, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "RellQ", "name": "Shattering Strike", "description": "<PERSON><PERSON> deals magic damage to units in a line, breaking their shields and stunning them. ", "tooltip": "<PERSON><PERSON> thrusts her lance, <status>Stunning</status> targets for {{ stunduration }} seconds, destroying all <shield>Shields</shield>, and dealing <magicDamage>{{ damage }} magic damage.</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RellQ.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON>_Dismount", "name": "Ferromancy: Crash Down", "description": "Mounted: <PERSON><PERSON> Dismounts, crashing down in her armor, Knocking enemies up and gaining a large Shield. While dismounted, she gains <PERSON><PERSON>, <PERSON> Resist, Attack Speed, and Attack Range, but is Slowed.<br><br>Dismounted: <PERSON><PERSON> forms her Mount, gaining a burst of speed and Knocking Up the next enemy she attacks.<br><br>", "tooltip": "<spellPassive>Passive - Mounted Alacrity:</spellPassive> Re<PERSON> gains <speed>{{ spell.rellw_dismount:mountedmovespeed }} Move Speed</speed> while mounted.<br /><br /><spellActive>Active - Ferromancy: Crash Down:</spellActive> <PERSON><PERSON> leaps off her mount, <status>Knocking Up</status> enemies and dealing <magicDamage>{{ spell.rellw_dismount:dismountdamage }} magic damage</magicDamage>. <PERSON><PERSON> gains <shield>{{ spell.rellw_dismount:shield }} Shield</shield>, which lasts until she remounts.<br /><br /><PERSON><PERSON> then enters her armored form, gaining <scaleArmor>{{ spell.rellw_dismount:resistanceincrease*100 }}% increased Armor</scaleArmor> and <scaleMR>Magic Resist</scaleMR>, <attackSpeed>{{ spell.rellw_dismount:dismountedasboost*100 }}% Attack Speed</attackSpeed> and {{ spell.rellw_dismount:dismountedrangeboost }} Attack Range. In this armored form, she can use <spellName>Ferromancy: Mount Up</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dismount Damage", "Shield Amount", "Flip Damage", "Passive Move Speed"], "effect": ["{{ crashdowndamage }} -> {{ crashdowndamageNL }}", "{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ mountupdamage }} -> {{ mountupdamageNL }}", "{{ mountedmovespeed }} -> {{ mountedmovespeedNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RellW_Dismount.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Rell<PERSON>", "name": "Full Tilt", "description": "Passive: <PERSON><PERSON> gains out of combat Move Speed.<br><br>Active: <PERSON><PERSON> and an ally gain ramping Move Speed, doubled toward enemies and each other. Her next Attack explodes, dealing magic damage.<br>", "tooltip": "<PERSON><PERSON> and an ally charge, gaining <speed>{{ minms*100 }}% Move Speed</speed>, increased to <speed>{{ maxms*100 }}%</speed> when facing enemy champions or each other for {{ duration }} seconds. <PERSON><PERSON>'s next Attack or <spellName>Shattering Strike</spellName> explodes in an area for <magicDamage>{{ maxhealthdamagecalc }} max Health magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ percenthealthdamage*100.000000 }}% -> {{ percenthealthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "RellE.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RellR", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> explodes in a magnetic fury, violently Pulling nearby enemies toward herself. Then <PERSON><PERSON> constantly Drags nearby enemies toward herself for a short while, dealing magic damage over time.", "tooltip": "<PERSON><PERSON> explodes in a magnetic fury, violently <status>Pulling</status> nearby enemies towards herself. Then <PERSON><PERSON> constantly <status>Drags</status> nearby enemies towards herself and deals <magicDamage>{{ totaldamage }} magic damage</magicDamage> over the next {{ duration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamagepersecond*2.000000 }} -> {{ basedamagepersecondnl*2.000000 }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "RellR.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Break the Mold", "description": "<PERSON><PERSON>'s Attacks and Abilities deal additional magic damage and steal Armor and Magic Resist on hit.", "image": {"full": "RellP.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}