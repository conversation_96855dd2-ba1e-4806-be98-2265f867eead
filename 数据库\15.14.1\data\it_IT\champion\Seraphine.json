{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Seraphine": {"id": "<PERSON><PERSON><PERSON>", "key": "147", "name": "<PERSON><PERSON><PERSON>", "title": "la cantante sognatrice", "image": {"full": "Seraphine.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "147000", "num": 0, "name": "default", "chromas": false}, {"id": "147001", "num": 1, "name": "Seraphine K/DA ALL OUT Indie", "chromas": false}, {"id": "147002", "num": 2, "name": "Seraphine K/DA ALL OUT Stella nascente", "chromas": false}, {"id": "147003", "num": 3, "name": "Seraphine K/DA ALL OUT Superstar", "chromas": false}, {"id": "147004", "num": 4, "name": "<PERSON><PERSON>ine <PERSON> Aggraziata", "chromas": true}, {"id": "147014", "num": 14, "name": "<PERSON><PERSON><PERSON>'Oceano", "chromas": true}, {"id": "147015", "num": 15, "name": "<PERSON><PERSON><PERSON>'Oceano (edizione prestigio)", "chromas": false}, {"id": "147024", "num": 24, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "147034", "num": 34, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "147043", "num": 43, "name": "<PERSON><PERSON><PERSON> Battaglia", "chromas": true}, {"id": "147050", "num": 50, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Nata a Piltover da genitori di Zaun, <PERSON><PERSON><PERSON> sente le anime altrui. Il mondo canta per lei, e lei canta in risposta. Nella sua giovinezza venne travolta da quei suoni che ora invece le sono d'ispirazione, trasformando il caos in una sinfonia. Si esibisce per le città sorelle a ricordo che non sono soli, che insieme sono più forti e che, ai suoi occhi, hanno un potenziale illimitato.", "blurb": "Nata a Piltover da genitori di Zaun, <PERSON><PERSON><PERSON> sente le anime altrui. Il mondo canta per lei, e lei canta in risposta. Nella sua giovinezza venne travolta da quei suoni che ora invece le sono d'ispirazione, trasformando il caos in una sinfonia. Si...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 25, "movespeed": 330, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.669}, "spells": [{"id": "SeraphineQ", "name": "Nota acuta", "description": "<PERSON><PERSON><PERSON> infligge danni in un'area.", "tooltip": "<PERSON><PERSON><PERSON> emette una nota pura, infliggendo <magicDamage>{{ explosiondamage }} danni magici</magicDamage>, aumentati contro i campioni in base alla percentuale di salute mancante del bersaglio, fino a <magicDamage>{{ totalempowereddamage }} danni</magicDamage> sotto {{ executethreshold*100 }}% salute.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "SeraphineQ.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SeraphineW", "name": "Surround", "description": "<PERSON><PERSON><PERSON> ripara e velocizza gli alleati vicini. Se è già riparata, cura anche gli alleati vicini.", "tooltip": "Seraphine coinvolge i campioni alleati vicini in una canzone, conferendo loro <speed>{{ hastevalueallies }} velocità di movimento</speed>, a se stessa <speed>{{ wmsbonustotal }}% velocità di movimento che decade nel tempo</speed> e a entrambi uno <shield>scudo da {{ shieldvalueseraphine }}</shield> per {{ shieldduration }} secondi.<br /><br />Se Seraphine è già <shield>protetta</shield>, raduna i suoi alleati e ripristina <healing>{{ wmissinghpheal }}% della loro salute mancante</healing> per ogni campione alleato vicino dopo un ritardo di {{ whealsplitdelay }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Percentuale guarita", "Costo in @AbilityResourceName@"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ wmissinghpbase }} -> {{ wmissinghpbaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 22, 22, 22, 22], "cooldownBurn": "22", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SeraphineW.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SeraphineE", "name": "Cambio di ritmo", "description": "<PERSON><PERSON><PERSON> infligge danni e ostacola i movimenti dei nemici lungo una linea.", "tooltip": "Seraphine scatena una potente onda sonora, infliggendo <magicDamage>{{ finaldamage }} danni magici</magicDamage> ai nemici in linea retta e <status>rallentandoli</status> di un {{ slowvalue }}% per {{ slowduration }} secondi.<br /><br />I nemici già <status>rallentati</status> vengono <status>immobilizzati</status> e i nemici <status>immobilizzati</status> vengono <status>storditi</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> rallenta<PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "SeraphineE.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SeraphineR", "name": "Bis!", "description": "<PERSON><PERSON><PERSON> infligge danni e ammalia i nemici colpiti, ripristinando la gittata con ogni campione alleato o nemico colpito.", "tooltip": "Seraphine sale sul palco e proietta una forza ammaliante che <status>affascina</status> i nemici per {{ rchannelduration }} secondi e infligge <magicDamage>{{ r1totaldamage }} danni magici</magicDamage>.<br /><br />I campioni colpiti (alleati inclusi) diventano parte della performance ed estendono la portata dell'abilità. I campioni alleati ottengono il massimo delle <keywordMajor>Note</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> amorosa", "Ricarica"], "effect": ["{{ r1basedamage }} -> {{ r1basedamageNL }}", "{{ rchannelduration }} -> {{ rchanneldurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SeraphineR.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Presenza scenica", "description": "Ogni terza abilità base viene lanciata due volte da Seraphine. Inoltre, lanciare abilità vicino agli alleati le conferisce danni magici bonus e gittata al suo prossimo attacco base.", "image": {"full": "Seraphine_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}