{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akali": {"id": "Akali", "key": "84", "name": "Akali", "title": "the Rogue Assassin", "image": {"full": "Akali.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "84000", "num": 0, "name": "default", "chromas": false}, {"id": "84001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "84002", "num": 2, "name": "Infernal Akali", "chromas": false}, {"id": "84003", "num": 3, "name": "All-star <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "84004", "num": 4, "name": "Nurse <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "84005", "num": 5, "name": "Blood Moon Akali", "chromas": false}, {"id": "84006", "num": 6, "name": "Silverfang Akali", "chromas": false}, {"id": "84007", "num": 7, "name": "Headhunter Akali", "chromas": true}, {"id": "84008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "84009", "num": 9, "name": "K/DA Akali", "chromas": false}, {"id": "84013", "num": 13, "name": "Prestige K/DA Akali", "chromas": false}, {"id": "84014", "num": 14, "name": "PROJECT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "84015", "num": 15, "name": "True Damage Akali", "chromas": true}, {"id": "84032", "num": 32, "name": "K/DA ALL OUT Akali", "chromas": true}, {"id": "84050", "num": 50, "name": "Crime City Nightmare Akali", "chromas": false}, {"id": "84060", "num": 60, "name": "Prestige K/DA Akali (2022)", "chromas": false}, {"id": "84061", "num": 61, "name": "Star Guardian Akali", "chromas": false}, {"id": "84068", "num": 68, "name": "DRX Akali", "chromas": false}, {"id": "84070", "num": 70, "name": "Coven Akali", "chromas": false}, {"id": "84071", "num": 71, "name": "Prestige Coven Akali", "chromas": true}, {"id": "84082", "num": 82, "name": "Empyrean <PERSON>", "chromas": false}, {"id": "84092", "num": 92, "name": "Spirit Blossom Akali", "chromas": false}], "lore": "Meninggalkan Kinkou Order dan gelar Fist of Shadow, Akali kini menyerang sendirian, siap menjadi senjata mematikan bagi rakyatnya. Meski dia menguasai semua ilmu dari guru<PERSON>, <PERSON>, dia telah berjanji untuk menjaga Ionia dari musuh-musuhnya, nyawa demi nyawa. Akali bisa menyerang dalam diam, tetapi pesannya akan terdengar lantang dan jelas: takutlah pada assassin tak bertuan.", "blurb": "Meninggalkan Kinkou Order dan gelar <PERSON>st of Shadow, Akali kini menyerang sendirian, siap menjadi senjata mematikan bagi rakyatnya. Meski dia menguasai semua ilmu dari guru<PERSON>, <PERSON>, dia telah berjanji untuk menjaga Ionia dari musuh-musuhnya, nyawa demi...", "allytips": ["Akali jago menghabisi champion yang lemah. Biarkan tim memulai serang<PERSON>, lalu serang musuh di belakang.", "Twilight Shroud member<PERSON><PERSON> keamanan bahkan dalam situasi paling berbahaya. Gunakan waktu tersebut untuk mengumpulkan energi dan melancarkan serangan cepat."], "enemytips": ["<PERSON><PERSON>i masih bisa terkena spell AOE saat bersembunyi di dalam Twilight Shroud. <PERSON><PERSON> beg<PERSON>, p<PERSON><PERSON>ya akan terlihat sejenak.", "Five Point Strike Akali sangat efektif jika digunakan pada range dan energi maksimum. Serang dia saat energinya rendah untuk memaksimalkan peluang menang dalam pertarungan.", "Kembali ke base jika Health-mu rendah dan ultima Akali siap digunakan."], "tags": ["Assassin"], "partype": "Energy", "info": {"attack": 5, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 119, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 23, "armorperlevel": 4.7, "spellblock": 37, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.625}, "spells": [{"id": "AkaliQ", "name": "Five Point Strike", "description": "Akali melemparkan lima kunai, <PERSON><PERSON><PERSON><PERSON><PERSON> damage berdasarkan bonus Attack Damage dan Ability Power, serta menerapkan efek slow.", "tooltip": "Akali melemparkan kunai dalam bentuk busur, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damage }} magic damage</magicDamage> dan menerapkan efek <status>Slow</status> ke musuh di ujungnya sebesar {{ slowpercentage*100 }}% selama {{ slowduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cost }}-> {{ costNL }}", "{{ basedamagenamed }}-> {{ basedamagenamedNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [110, 100, 90, 80, 70], "costBurn": "110/100/90/80/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "AkaliQ.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliW", "name": "<PERSON>hroud", "description": "Akali menjatuhkan smoke bomb dan mendapatkan Move Speed sesaat. Saat di dalam asap, Akali menjadi tak terlihat dan tak bisa ditarget oleh spell dan serangan musuh. Menyerang atau menggunakan ability akan mengungkapnya sesaat.  ", "tooltip": "Akali menjatuhkan smoke bomb, menciptakan tirai asap yang bertahan {{ baseduration }} detik dan member<PERSON>nnya <speed>{{ movementspeed }}% Move Speed</speed> yang akan berkurang dalam kurun waktu {{ movementspeedduration }} detik.<br /><br />Akali meningkatkan Energy maksimumnya sebanyak {{ energyrestore }} saat tirai asap aktif. <br /><br />Saat di dalam asap, Akali menjadi <keywordStealth>Invisible</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Move Speed", "<PERSON><PERSON><PERSON>", "Cooldown"], "effect": ["{{ movementspeed }}%-> {{ movementspeedNL }}%", "{{ baseduration }}-> {{ basedurationNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [140, 140, 140, 140, 140], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [250, 250, 250, 250, 250], [60, 65, 70, 75, 80], [0.3, 0.35, 0.4, 0.45, 0.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [5, 5.5, 6, 6.5, 7], [0, 0, 0, 0, 0]], "effectBurn": [null, "140", "4", "0", "250", "60/65/70/75/80", "0.3/0.35/0.4/0.45/0.5", "1", "0", "5/5.5/6/6.5/7", "0"], "vars": [], "costType": " Energy", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AkaliW.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "Memulihkan {{ energyrestore }} Energy"}, {"id": "AkaliE", "name": "<PERSON><PERSON><PERSON>", "description": "Melompat mundur dan menembakkan shuriken ke depan, men<PERSON><PERSON>lkan magic damage. <PERSON><PERSON><PERSON> pertama atau awan asap yang terkena akan ditandai. Recast untuk melakukan dash ke target yang ditandai, mengh<PERSON><PERSON><PERSON> damage tambahan.", "tooltip": "Akali melompat mundur sambil melempar shuriken, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ e1damage }} magic damage</magicDamage> dan menandai musuh pertama atau awan asap yang terkena. Akali bisa <recast>Recast</recast> ability ini satu kali untuk dash ke target yang ditandai, men<PERSON><PERSON><PERSON>an <magicDamage>{{ e2damagecalc }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "<PERSON><PERSON>"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AkaliE.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliR", "name": "Perfect Execution", "description": "Akali melompat ke satu arah, <PERSON><PERSON><PERSON><PERSON><PERSON> damage pada musuh yang dihantam. Recast: Akali dash ke satu arah, mengeksekusi semua musuh yang diserang.", "tooltip": "Akali melompat melewati champion musuh, memberikan <magicDamage>{{ cast1damage }} magic damage</magicDamage> ke semua musuh di jalurnya. <br /><br />Akali bisa <recast>Recast</recast> setelah {{ cooldownbetweencasts }} detik untuk melakukan serangan menusuk, melakukan dash dan memberikan <magicDamage>{{ cast2damagemin }}</magicDamage> hingga <magicDamage>{{ cast2damagemax }} magic damage</magicDamage> berdasarkan Health yang hilang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage Awal", "Damage Minimum", "Damage Maksimum"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ r2basedamage }}-> {{ r2basedamageNL }}", "{{ r2basedamage*3.000000 }}-> {{ r2basedamagenl*3.000000 }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [1, 1, 1], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [675, 675, 675], "rangeBurn": "675", "image": {"full": "AkaliR.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}], "passive": {"name": "Assassin's Mark", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> spell damage pada champion memb<PERSON><PERSON> lingkaran energi yang mengelilingi mereka. <PERSON><PERSON><PERSON> dari lingkaran memperkuat Serangan Akali berikutnya dengan jangkauan dan damage bonus.", "image": {"full": "Akali_P.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}