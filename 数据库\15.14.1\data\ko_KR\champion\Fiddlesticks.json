{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiddlesticks": {"id": "Fiddlesticks", "key": "9", "name": "피들스틱", "title": "오래된 공포", "image": {"full": "Fiddlesticks.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "9000", "num": 0, "name": "default", "chromas": false}, {"id": "9001", "num": 1, "name": "유령 피들스틱", "chromas": false}, {"id": "9002", "num": 2, "name": "유니언 잭 피들스틱", "chromas": false}, {"id": "9003", "num": 3, "name": "노상강도 피들스틱", "chromas": true}, {"id": "9004", "num": 4, "name": "호박머리 피들스틱", "chromas": false}, {"id": "9005", "num": 5, "name": "해적 피들스틱", "chromas": false}, {"id": "9006", "num": 6, "name": "깜짝 파티 피들스틱", "chromas": true}, {"id": "9007", "num": 7, "name": "어둠 막대사탕 피들스틱", "chromas": false}, {"id": "9008", "num": 8, "name": "부활한 피들스틱", "chromas": false}, {"id": "9009", "num": 9, "name": "시공간 침략자 피들스틱", "chromas": true}, {"id": "9027", "num": 27, "name": "별의 숙적 피들스틱", "chromas": true}, {"id": "9037", "num": 37, "name": "핏빛달 피들스틱", "chromas": true}], "lore": "룬테라에 무시무시한 고대의 무언가가 깨어났다. 시간을 초월한 공포의 존재 피들스틱은 불안에 동요하는 인간 사회의 끝자락에 나타나 겁먹은 희생자들을 양분으로 삼는다. 앙상하고 투박한 모습의 이 생물은 삐죽삐죽한 낫을 휘두르며 공포를 거둬들이고, 살아남은 불행한 자들의 정신을 산산이 조각낸다. 까마귀 울음소리와 인간을 닮은 형상의 속삭임을 조심하라... 피들스틱이 돌아온 것이다.", "blurb": "룬테라에 무시무시한 고대의 무언가가 깨어났다. 시간을 초월한 공포의 존재 피들스틱은 불안에 동요하는 인간 사회의 끝자락에 나타나 겁먹은 희생자들을 양분으로 삼는다. 앙상하고 투박한 모습의 이 생물은 삐죽삐죽한 낫을 휘두르며 공포를 거둬들이고, 살아남은 불행한 자들의 정신을 산산이 조각낸다. 까마귀 울음소리와 인간을 닮은 형상의 속삭임을 조심하라... 피들스틱이 돌아온 것이다.", "allytips": [], "enemytips": [], "tags": ["Mage", "Support"], "partype": "마나", "info": {"attack": 2, "defense": 3, "magic": 9, "difficulty": 9}, "stats": {"hp": 650, "hpperlevel": 106, "mp": 500, "mpperlevel": 28, "movespeed": 335, "armor": 34, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 480, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.65, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "FiddleSticksQ", "name": "공포", "description": "피들스틱이 적의 시야에 보이지 않을 때 적에게 스킬로 피해를 입히거나 공포를 사용하면 대상을 공포에 질리게 하여 일정 시간 도망치게 합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 전투에서 벗어나 적의 시야에 보이지 않을 때나 <keywordMajor>허수아비</keywordMajor>인 척할 때 적에게 스킬로 피해를 입히면 대상이 {{ fearduration }}초 동안 <status>공포</status>에 질립니다.<br /><br /><spellActive>사용 시:</spellActive> {{ fearduration }}초 동안 적을 <status>공포</status>에 빠트리고 <magicDamage>현재 체력의 {{ totalpercenthealthdamage }}에 해당하는 마법 피해</magicDamage>를 입힙니다. 최근에 피들스틱에 의해 <status>공포</status>에 빠진 대상은 <magicDamage>현재 체력의 {{ totalpercenthealthdamagefeared }}에 해당하는 마법 피해</magicDamage>를 입습니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["지속시간", "재사용 대기시간", "현재 체력 %"], "effect": ["{{ fearduration }} -> {{ feardurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 14.5, 14, 13.5, 13], "cooldownBurn": "15/14.5/14/13.5/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "FiddleSticksQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FiddleSticksW", "name": "풍작", "description": "피들스틱이 주변 적의 체력을 흡수합니다. 지속시간이 끝날 때 잃은 체력에 비례한 추가 피해를 입힙니다.", "tooltip": "피들스틱이 정신을 집중해 2초에 걸쳐 주변 적들의 영혼을 흡수합니다. 그동안 초당 <magicDamage>{{ draindamagecalc }}의 마법 피해</magicDamage> 를 입히고, 지속시간이 끝날 때 <magicDamage>대상이 잃은 체력의 {{ percentfortooltip }}%에 해당하는 마법 피해</magicDamage>를 입힙니다. 피들스틱은 <healing>피해량의 {{ vamppercentage }}%에 해당하는 체력</healing>을 회복합니다.<br /><br />피들스틱이 방해 없이 끝까지 스킬을 사용하면 남은 재사용 대기시간이 60% 감소합니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["초당 피해량", "잃은 체력 비례 피해량", "회복 %", "재사용 대기시간"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ percentfortooltip }}% -> {{ percentfortooltipNL }}%", "{{ vamppercentage }}% -> {{ vamppercentageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "FiddleSticksW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FiddleSticksE", "name": "수확", "description": "피들스틱이 넓은 범위를 낫으로 베어 적중한 모든 적을 둔화시키고 가운데에 있는 적을 침묵시킵니다.", "tooltip": "피들스틱이 어둠의 마력을 방출해 <magicDamage>{{ damage }}의 마법 피해</magicDamage>를 입히고 {{ silenceduration }}초 동안 {{ slowamount*-100 }}% <status>둔화</status>시킵니다. 또한 범위 중심에 있는 적을 지속시간 동안 <status>침묵</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "FiddleSticksE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FiddleSticksR", "name": "까마귀 폭풍", "description": "피들스틱 주변을 까마귀 떼가 헤집고 다니며 매초 해당 지역에 있는 모든 적 유닛에게 피해를 입힙니다.", "tooltip": "피들스틱이 {{ channeltime }}초 동안 정신을 집중해 대상 지역으로 순간 이동한 뒤 살인 까마귀 떼를 불러내어 {{ duration }}초 동안 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["초당 피해량", "재사용 대기시간"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 225, 325], [5, 5, 5], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "125/225/325", "5", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "FiddleSticksR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "무해한 허수아비", "description": "피들스틱의 장신구는 허수아비로 대체됩니다.", "image": {"full": "FiddlesticksP.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}