{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jhin": {"id": "<PERSON><PERSON>", "key": "202", "name": "Τζιν", "title": "ο Βιρτουόζος", "image": {"full": "Jhin.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "202000", "num": 0, "name": "default", "chromas": false}, {"id": "202001", "num": 1, "name": "Τζιν της Άγριας Δύσης", "chromas": true}, {"id": "202002", "num": 2, "name": "Τζιν του Ματωμένου Φεγγαριού", "chromas": false}, {"id": "202003", "num": 3, "name": "SKT T1 Τζιν", "chromas": false}, {"id": "202004", "num": 4, "name": "PROJECT: Τζιν", "chromas": false}, {"id": "202005", "num": 5, "name": "Σκο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Κοσμικός Τζιν", "chromas": false}, {"id": "202014", "num": 14, "name": "Τζιν Πάπυροι Σαν Χάι", "chromas": true}, {"id": "202023", "num": 23, "name": "DWG Τζιν", "chromas": true}, {"id": "202025", "num": 25, "name": "Αιθέ<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "202036", "num": 36, "name": "<PERSON>υχο<PERSON><PERSON><PERSON><PERSON>ν", "chromas": false}, {"id": "202037", "num": 37, "name": "Τζιν της Σκο<PERSON><PERSON>ι<PERSON><PERSON>ς Κοσμικής Εξάλειψης", "chromas": true}, {"id": "202047", "num": 47, "name": "Μυθοπλάστης <PERSON>ζιν", "chromas": false}], "lore": "Ο Τζιν είναι ένας τελειομανής ψυχοπαθής εγκληματίας που πιστεύει ότι ο φόνος είναι μια μορφή τέχνης. Κ<PERSON><PERSON><PERSON><PERSON><PERSON> ήταν έγκλειστος στις φυλακές της Ιονίας, αλ<PERSON><PERSON> αφέθηκε ελεύθερος χάρη στην επιρροή μιας μυστικής οργάνωσης που είχε διεισδύσει στο κυβερνητικό συμβούλιο της χώρας. Τώρα, ο κατά συρροήν αυτός δολοφόνος σκοτώνει για λογαριασμό της ίδιας κλίκας που τον απελευθέρωσε. Ο Τζιν χρησιμοποιεί το όπλο του με τη μαεστρία ενός ζωγράφου, δημιουργώντας έργα καλλιτεχνικής βιαιότητας που γεμίζουν τα θύματα και τους θεατές με φρίκη. Αντλεί μια άγρια χαρά από αυτό το μακάβριο θέατρο και θεωρεί ότι είναι το ιδανικό μέσο για να στείλει στον κόσμο το πιο ισχυρό μήνυμά του: τον τρόμο.", "blurb": "Ο Τζιν είναι ένας τελειομανής ψυχοπαθής εγκληματίας που πιστεύει ότι ο φόνος είναι μια μορφή τέχνης. Κ<PERSON><PERSON><PERSON><PERSON><PERSON> ήταν έγκλειστος στις φυλακές της Ιονίας, αλ<PERSON><PERSON> αφέθηκε ελεύθερος χάρη στην επιρροή μιας μυστικής οργάνωσης που είχε διεισδύσει στο κυβερνητικό...", "allytips": ["Η Προοικονομία έχει απίστευτη εμβέλεια. Όταν προσεγγίζετε μια μάχη, ελέγξτε από πριν αν υπάρχουν εχθροί που μπορούν να ακινητοποιηθούν.", "Η υπέρτατη ικανότητά σας προκαλεί σημαντικά μειωμένη ζημιά σε εχθρούς που έχουν όλη τη Ζωή τους. Εστιάστε στους αποδυναμωμένους στόχους που προσπαθούν να ξεφύγουν.", "Μπορείτε να χρησιμοποιήσετε τις ικανότητές σας ενώ ξαναγεμίζετε το όπλο σας. Θα σας βοηθήσουν μέχρι να έχετε πάλι στη διάθεσή σας το όπλο."], "enemytips": ["Η Προοικονομία ακινητοπ<PERSON>ι<PERSON>ί μόνο όσους δέχονται χτυπήματα από τις βασικές επιθέσεις, τις παγίδες ή τους συμμάχους του Τζιν στα τελευταία 4 δευτερόλεπτα.", "Ο Τζιν τοποθετεί αόρατες παγίδες σε όλον τον χάρτη. Προσέξτε πού πατάτε!", "Οι επιθέσεις του Τζιν είναι πολύ ισχυρές, αλλά πρέπει να ξαναγεμίσει μετά την 4η βολή. Εκμεταλλευτείτε αυτό το άνοιγμα για να του ορμήσετε και να του κάνετε όση ζημιά μπορείτε."], "tags": ["Marksman", "Mage"], "partype": "Μάνα", "info": {"attack": 10, "defense": 2, "magic": 6, "difficulty": 6}, "stats": {"hp": 655, "hpperlevel": 107, "mp": 300, "mpperlevel": 50, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.4, "attackspeedperlevel": 0, "attackspeed": 0.625}, "spells": [{"id": "JhinQ", "name": "Χοροβομβίδα", "description": "Ο Τζιν εκτοξεύει μια μαγική βομβίδα σε έναν εχθρό. Μπορεί να χτυπήσει έως και τέσσερις στόχους και η ζημιά της αυξάνεται κάθε φορά που σκοτώνει κάποιον στόχο.", "tooltip": "Ο Τζιν πετάει μια βομβίδα που προκαλεί <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> και μετά αναπηδά σε έναν κοντινό εχθρό που δεν έχει χτυπηθεί ακόμα.<br /><br />Η βομβίδα μπορεί να χτυπήσει έως και {{ tooltipmaxtargetshit }} φορές. Οι εχθροί που πεθαίνουν σύντομα αφότου χτυπηθούν με τη βομβίδα, αυξάνουν τη ζημιά των επόμενων χτυπημάτων κατά {{ percentamponkill*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Συνολική Αναλογία Ζημιάς Επίθεσης", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "JhinQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinW", "name": "Προοικονομία", "description": "Ο Τζιν ανεμίζει το μπαστούνι του και ρίχνει μία βολή με απίστευτη εμβέλεια. Διαπερνά υπηρέτες και τέρατα, <PERSON><PERSON><PERSON><PERSON> σταματά στον πρώτο Ήρωα που θα χτυπήσει. Αν ο στόχος έχει χτυπηθεί πρόσφατα από τους σύμμαχους του Τζιν, τις Παγίδες-Λωτούς ή αν έχει δεχτεί ζημιά από τον Τζιν, τότε θα ακινητοποιηθεί.", "tooltip": "Ο Τζιν εκτοξεύει μια βολή μεγάλης εμβέλειας που προκαλεί <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> στον πρώτο Ήρωα που θα χτυπήσει και σε άλλους εχθρούς που θα πετύχει στην τροχιά της.<br /><br />Αν αυτή η ικανότητα χτυπήσει κάποιον Ήρωα που έχει ήδη δεχτεί ζημιά από κάποιον σύμμαχο Ήρωα τα τελευταία {{ spottingduration }} δευτ., θα τον <status>Ριζώσει</status> για {{ rootduration }} δευτ. και ο Τζιν θα αποκτήσει την Ταχύτητα Κίνησης του <spellName>Ψίθυρου</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκεια Ριζώματος", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "JhinW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinE", "name": "Καθηλω<PERSON><PERSON>ν<PERSON>ινό", "description": "Ο Τζιν τοποθετεί μια αόρατη παγίδα-λωτό που ανθίζει όταν κάποιος περάσει από πάνω της. Επιβραδύνει τους εχθρούς που βρίσκονται κοντά και μετά προκαλεί ζημιά με μια έκρηξη πριονωτών πετάλων. <br><br><font color='#FFFFFF'>Η Ομορφιά του Θανάτου -</font> Όταν ο Τζιν σκοτώνει έναν αντίπαλο Ήρωα, μια παγίδα-λωτός θα ανθίσει κοντά στο πτώμα του.", "tooltip": "<passive>Παθητική:</passive> Οι Ήρωες που σκοτώνει ο Τζιν δημιουργούν μια Παγίδα-Λωτό στη θέση τους, η οποία ανατινάζεται αμέσως.<br /><br /><active>Ενεργή:</active> Ο Τζιν τοποθετεί μια αόρατη Παγίδα-Λωτό για {{ trapduration }} λεπτά, η οποία δημιουργεί μια ζώνη που <status>Επιβραδύνει</status> κατά {{ trapslowamount*100 }}% τους εχθρούς που πατάνε μέσα της. Μετά από {{ trapdetonationtime }} δευτ., η παγίδα ανατινάζεται και προκαλεί <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage>.<br /><br />Αυτή η ικανότητα έχει 2 φορτίσεις (ανανεώνεται κάθε {{ ammorechargeratetooltip }} δευτ.){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>λήρω<PERSON>ης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [2, 2, 2, 2, 2], "cooldownBurn": "2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "JhinE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinR", "name": "Τελευταία Υπόκλιση", "description": "Ο Τζιν διοχετεύει ενέργεια και μετατρέπει τον Ψίθυρο σε ένα τεράστιο κανόνι που στηρίζεται στον ώμο. Μπορεί να ρίξει 4 ενισχυμένες βολές με τεράστια εμβέλεια, οι οποίες διαπερνούν υπηρέτες και τέρατα, αλλά σταματούν στον πρώτο Ήρωα που θα χτυπήσουν. <PERSON> <PERSON>ίθυρος σακατεύει τους εχθρούς που χτυπά, επιβραδύνοντάς τους, και προκαλεί ζημιά εκτέλεσης. Η 4η βολή είναι ειδικά κατασκευασμένη, απ<PERSON>στευτα ισχυρή και προκαλεί εγγυημένα Καίριο Χτύπημα.", "tooltip": "Ο Τζιν ετοιμάζεται και μαζεύει ενέργεια και, στη συνέχεια, αποκτά τη δυνατότητα να ρίξει 4 ενισχυμένες βολές. Κάθε βολή προκαλεί <physicalDamage>{{ damagecalc }}</physicalDamage> έως <physicalDamage>{{ maxincreasecalc }} Σωματική Ζημιά</physicalDamage> στον πρώτο Ήρωα που θα χτυπήσει ανάλογα με το ποσοστό Ζωής που λείπει και τον <status>Επιβραδύνει</status> κατά {{ slowpercent*100 }}% για {{ slowduration }} δευτ. Η τέταρτη βολή προκαλεί καίριο χτύπημα για {{ fourthshotmultiplier*100 }}% ζημιά.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JhinR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ψίθυρος", "description": "Το όπλο του Τζιν λέγεται Ψίθυρος και είναι ένα όργανο ακριβείας, ειδικά σχεδιασμένο για να προκαλεί τεράστια ζημιά. Πυροβολεί με σταθερή συχνότητα και χωράει μόνο τέσσερις βολές. Ο Τζιν ενισχύει την τελευταία σφαίρα με σκοτεινή μαγεία που του επιτρέπει να προκαλεί εγγυημένα Καίρια Χτυπήματα και να κάνει μπόνους ζημιά εκτέλεσης. Κάθε φορά που ο Ψίθυρος προκαλέσει ένα Καίριο Χτύπημα, ο Τζιν εμπνέεται και αυξάνει σημαντικά την Ταχύτητα Κίνησής του.", "image": {"full": "Jhin_P.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}