{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Urgot": {"id": "Urgot", "key": "6", "name": "Ургот", "title": "Дредноут", "image": {"full": "Urgot.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "6000", "num": 0, "name": "default", "chromas": false}, {"id": "6001", "num": 1, "name": "Гигантский вражеский Крабгот", "chromas": false}, {"id": "6002", "num": 2, "name": "Мясник Ургот", "chromas": false}, {"id": "6003", "num": 3, "name": "Модифицированный Ургот", "chromas": true}, {"id": "6009", "num": 9, "name": "Ковбой Ургот", "chromas": true}, {"id": "6015", "num": 15, "name": "Ургот в пижаме Звездного защитника", "chromas": true}, {"id": "6023", "num": 23, "name": "Ургот из Мира ужасов", "chromas": true}, {"id": "6032", "num": 32, "name": "Ургот Отец засоров", "chromas": true}], "lore": "Когда-то Ургот был Верховным Палачом Ноксуса и пользовался огромной властью. Но затем последовали предательство и опала: Ургота заковали в кандалы и отправили на каторгу в глубочайший заунский рудник – Яму. Там Ургот постиг истинную природу силы. И когда он освободился, по Зауну прокатилась волна насилия и разрушения. Теперь Ургот – внушительная фигура в преступном мире; он славится тем, что захлестывает свои жертвы цепями, в которые был раньше закован. Бывший узник превратит свой новый дом в горнило боли и очистит город от недостойных.", "blurb": "Когда-то Ургот был Верховным Палачом Ноксуса и пользовался огромной властью. Но затем последовали предательство и опала: Ургота заковали в кандалы и отправили на каторгу в глубочайший заунский рудник – Яму. Там Ургот постиг истинную природу силы. И...", "allytips": ["Внимательно следите за перезарядкой каждой лапы, поскольку они наносят значительную часть урона.", "Если вы попадете в противника Коррозийным зарядом или Презрением, он станет целью для Зачистки – это отличный способ быстро разрядить в него несколько лап.", "Приберегите Сильнее страха смерти для оппонентов, которые слишком ослаблены, чтобы пережить применение умения. Сильнее страха смерти прекрасно подойдет для добивания убегающих врагов."], "enemytips": ["Ургот в значительной степени полагается на атаки из своих лап. У них есть перезарядка, и они срабатывают, когда Ургот поражает врага, который находится напротив них. Старайтесь избегать попадания под огонь из нескольких лап.", "Ургот наносит и поглощает тонны урона с помощью Зачистки, во время стрельбы передвижение замедляется.", "Если вы стали целью ''Сильнее страха смерти'', делайте все, чтобы уровень вашего здоровья не упал ниже порога (25% от макс.), пока эффект не закончится."], "tags": ["Fighter", "Tank"], "partype": "Мана", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 102, "mp": 340, "mpperlevel": 45, "movespeed": 330, "armor": 36, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 350, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 4, "attackspeedperlevel": 3.75, "attackspeed": 0.625}, "spells": [{"id": "UrgotQ", "name": "Коррозийный заряд", "description": "Ургот выпускает в указанную точку разрывной снаряд, который наносит физический урон всем врагам в области взрыва и замедляет их.", "tooltip": "Ургот выпускает разрывной снаряд, нанося врагам <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> и <status>замедляя</status> их на {{ slowamount*100 }}% на {{ slowduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Урон", "Замедление"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "UrgotQ.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "UrgotW", "name": "Зачистка", "description": "Ургот разряжает свое оружие в ближайших врагов, при этом замедляясь. Предпочтение отдается врагам, которых Ургот недавно поразил другими умениями. Возможно срабатывание Огненного эха.", "tooltip": "<spellPassive>Пассивно:</spellPassive> другие умения Ургота помечают последнего пораженного чемпиона на 5 сек.<br /><br /><spellActive>Активно:</spellActive> Ургот начинает расстреливать ближайшего врага, отдавая приоритет помеченным целям. Он применяет к ним несколько автоатак ({{ e8 }}) в секунду, нанося <physicalDamage>{{ damagepershot }} физического урона</physicalDamage> за выстрел. Пока действует умение, Ургот может передвигаться, его устойчивость к <status>замедлению</status> увеличена на {{ e2 }}%, но <speed>скорость передвижения</speed> уменьшена на <speed>{{ e5 }}</speed>.<br /><br />На максимальном уровне умения его продолжительность становится неограниченной, и Ургот может <toggle>включать и отключать</toggle> его.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Сила атаки за выстрел", "Стоимость – @AbilityResourceName@"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e7 }}% -> {{ e7NL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 9, 6, 3, 0], "cooldownBurn": "12/9/6/3/0", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [40, 40, 40, 40, 40], [4, 4, 4, 4, 25000], [0, 0, 0, 0, 0], [125, 125, 125, 125, 125], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 23.5, 27, 30.5, 34], [3, 3, 3, 3, 3], [12, 12, 12, 12, 12], [50, 50, 50, 50, 50]], "effectBurn": [null, "30/60/90/120/150", "40", "4/4/4/4/25000", "0", "125", "0.5", "20/23.5/27/30.5/34", "3", "12", "50"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "UrgotW.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "UrgotE", "name": "Презрение", "description": "Ургот делает рывок в выбранном направлении, топча миньонов и монстров, а также накладывая на себя щит. Поймав вражеского чемпиона, он останавливается и отшвыривает его со своего пути.", "tooltip": "Ургот совершает рывок вперед, получая <shield>щит прочностью {{ etotalshieldhealth }}</shield> на {{ eshieldduration }} сек. Первый пораженный чемпион <status>оглушается</status> на {{ stunduration }} сек. и перебрасывается через Ургота. Все враги на пути Ургота получают <physicalDamage>{{ edamage }} физического урона</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Прочность щита", "Стоимость – @AbilityResourceName@", "Перезарядка"], "effect": ["{{ ebasedamage }} -> {{ ebasedamageNL }}", "{{ eshieldbasehealth }} -> {{ eshieldbasehealthNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "UrgotE.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "UrgotR", "name": "Сильнее страха смерти", "description": "Ургот активирует химтековый бур, который пронзает первого задетого вражеского чемпиона. Если здоровье этого чемпиона падает ниже определенного порога, Ургот признает его слабым и может казнить его.", "tooltip": "Ургот выпускает химтековый бур, который пронзает первого пораженного вражеского чемпиона, нанося <physicalDamage>{{ rcalculateddamage }} физического урона</physicalDamage> и <status>замедляя</status> на {{ rslowduration }} сек. на 1% за каждый 1% недостающего здоровья (максимальное замедление - {{ rmovespeedmod }}%).<br /><br />Если у пронзенной цели остается менее {{ rhealththreshold }}% здоровья, Ургот может <recast>повторно применить</recast> это умение, <status>подавив</status> цель и притянув ее к себе. Когда жертва достигает Ургота, она погибает, а окружающие враги <status>пугаются</status> на {{ rfearduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "UrgotR.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Огненное эхо", "description": "Если Ургот поражает автоатакой врага или использует против него Зачистку, из его лап периодически вырывается пламя, наносящее физический урон.", "image": {"full": "Urgot_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}