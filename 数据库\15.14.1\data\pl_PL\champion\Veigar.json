{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Veigar": {"id": "<PERSON><PERSON><PERSON>", "key": "45", "name": "<PERSON><PERSON><PERSON>", "title": "Panicz Zła", "image": {"full": "Veigar.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "45000", "num": 0, "name": "default", "chromas": false}, {"id": "45001", "num": 1, "name": "Biały Mag Veigar", "chromas": false}, {"id": "45002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "45006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45007", "num": 7, "name": "<PERSON><PERSON><PERSON> Mi<PERSON>ła<PERSON>", "chromas": false}, {"id": "45008", "num": 8, "name": "Końcowy Boss Veigar", "chromas": true}, {"id": "45009", "num": 9, "name": "Veigar z Oddziału Omega", "chromas": true}, {"id": "45013", "num": 13, "name": "Veigar z Prastarej Kniei", "chromas": true}, {"id": "45023", "num": 23, "name": "Veigar w Przebraniu Marudoroga", "chromas": true}, {"id": "45032", "num": 32, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "45041", "num": 41, "name": "Poskramiacz Potworów Veigar", "chromas": true}, {"id": "45051", "num": 51, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "45060", "num": 60, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Entuzjastyczny mistrz czarnej magii, <PERSON><PERSON><PERSON> pozna<PERSON> moce, z którymi niewielu śmiertelników chce się zaznajomić. Jako niezależny mieszkaniec Bandle City, ch<PERSON>ł wyjść poza granice yordlowej magii, wi<PERSON><PERSON>ł zaj<PERSON>ć się magicznymi wolumenami, które pozostawały ukryte przez tysiące lat. Teraz jest upartym stwo<PERSON>, z niekończącą się fascynacją na punkcie tajemnic wszechświata. Veigar jest często niedoceniany przez innych — choć sam wierzy, że jest prawdziwie zły, posiada wewnętrzny zmysł moralności, przez który inni kwestionują jego pobudki.", "blurb": "Entuzjastyczny mistrz czarnej magii, <PERSON><PERSON><PERSON> p<PERSON> moce, z którymi niewielu śmiertelników chce się zaznajomić. Jako niezależny mieszkaniec Bandle City, ch<PERSON>ł wyjść poza granice yordlowej magii, wi<PERSON><PERSON> z<PERSON>zął zajmować się magicznymi wolumenami, które...", "allytips": ["Uż<PERSON><PERSON>, a<PERSON> z<PERSON><PERSON><PERSON><PERSON><PERSON> swoje szanse trafienia Ciemną Materią.", "Veigar jest nadzwyczaj zależny od many i skrócenia czasu odnowienia. Postaraj się kupować przedmioty zapewniające te statystyki, aby z<PERSON><PERSON><PERSON><PERSON><PERSON> skuteczność swojej umiejętności pasywnej oraz Złowrogiego Uderzenia.", "Veigar jest bard<PERSON> w<PERSON>tły. Przydaje się wybrać choć jeden defensywny czar przywoływacza."], "enemytips": ["Ciemna Materia zadaje bardzo duże obrażenia ale jest możliwa do uniknięcia. Zwracaj uwagę na znak wizualny i dźwiękowy, aby wiedzieć kiedy została użyta i gdzie wyląduje.", "Horyzont Zdarzeń ogłusza wyłącznie jednostki znajdujące się na jego krawędzi. Jeżeli znajdujesz się w środku, nadal możesz się poruszać i atakować.", "Superumiej<PERSON><PERSON><PERSON><PERSON><PERSON> Veigara zadaje tym więcej obrażeń, im więcej brakuje ci punktów zdrowia."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 580, "hpperlevel": 108, "mp": 490, "mpperlevel": 26, "movespeed": 340, "armor": 18, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.24, "attackspeed": 0.625}, "spells": [{"id": "VeigarBalefulStrike", "name": "Złowrogie Uderzenie", "description": "Veigar strzela pociskiem mrocznej energii, kt<PERSON>ry zadaje obrażenia magiczne pierwszym dwóm trafionym celom. Veigar na stałe zyskuje trochę mocy umiejętności, gdy zabije w ten sposób jednostkę.", "tooltip": "Veigar wyzwala pocisk mrocznej energii, kt<PERSON><PERSON> zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> pierwszym dwóm trafionym wrogom.<br /><br /><PERSON>abicie wroga za pomocą tej umieję<PERSON> sprawi, że Veigar otrzyma {{ spell.veigarpassive:dqkillstacks }} ładunek <keywordMajor><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></keywordMajor>. Duże stwory oraz duże potwory zapewniają zamiast tego następującą liczbę ładunków: {{ spell.veigarpassive:dqkillstackslarge }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Całkowite skalowanie z mocą umiejętności", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "VeigarBalefulStrike.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VeigarDarkMatter", "name": "Ciemna Materia", "description": "Veigar przywołuje z niebios wielką masę ciemnej materii, która w momencie lądowania zadaje obrażenia magiczne w wybranym miejscu. Ładunki Niezwykle Nikczemnej Mocy skracają czas odnowienia Ciemnej Materii.", "tooltip": "Veigar przyzywa ciemną materię z nieba, zada<PERSON><PERSON>c <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>.<br /><br />Ka<PERSON>de {{ spell.veigarpassive:pstacksperdarkmattercdr }} ładunków <keywordMajor><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></keywordMajor> skraca czas odnowienia tej umiejętności o {{ spell.veigarpassive:darkmattercdrincrement*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Całkowite skalowanie z mocą umiejętności", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1.2, 1.2, 1.2, 1.2, 1.2], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1.2", "8", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "VeigarDarkMatter.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VeigarEventHorizon", "name": "Horyzont Zdarzeń", "description": "Veigar zagina krawędzie przestrzeni, <PERSON><PERSON><PERSON><PERSON> k<PERSON>, która ogł<PERSON>za wrogów, gdy spróbuj<PERSON> przez nią przej<PERSON>.", "tooltip": "Veigar zagina krawędzie przestrzeni, <PERSON><PERSON><PERSON><PERSON> klat<PERSON>, kt<PERSON><PERSON> <status>og<PERSON><PERSON><PERSON></status> wrog<PERSON> na {{ e1 }} sek., gdy spróbują przez nią przejść. Klatka działa przez 3 sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas działania ogłuszenia:", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18.5, 17, 15.5, 14], "cooldownBurn": "20/18.5/17/15.5/14", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [1.5, 1.75, 2, 2.25, 2.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5/1.75/2/2.25/2.5", "0.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "VeigarEventHorizon.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VeigarR", "name": "Przedwieczny Wybuch", "description": "Poraża wybranego wrogiego bohatera, <PERSON><PERSON><PERSON><PERSON><PERSON> du<PERSON> il<PERSON> obrażeń magicznych, kt<PERSON>re są tym wię<PERSON>, im więcej zdrowia brakuje celowi.", "tooltip": "Veigar uderza we wrogiego bohatera pierwotną magią, zada<PERSON><PERSON><PERSON> od <magicDamage>{{ mindamage }} do {{ maxdamage }} pkt. obrażeń magicznych</magicDamage>, w zależności od brakującego zdrowia celu. Umiejętność zadaje maksymalne obrażenia wrogom mającym mniej niż 33% punktów zdrowia.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Całkowite skalowanie z mocą umiejętności", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "VeigarR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Niezwykle Nikczem<PERSON> Mo<PERSON>", "description": "Veigar jest najwię<PERSON><PERSON><PERSON> złem, jakie kiedykolwiek uderzyło w serce Runeterry — i staje się coraz potężniejszy! Trafienie wrogiego bohatera zaklęciem lub wzięcie udziału w zabójstwie trwale zwiększa moc umiejętności Veigara.", "image": {"full": "VeigarEntropy.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}