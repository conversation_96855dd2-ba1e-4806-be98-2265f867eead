{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Evelynn": {"id": "<PERSON><PERSON>", "key": "28", "name": "イブリン", "title": "苦悶の抱擁", "image": {"full": "Evelynn.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "28000", "num": 0, "name": "default", "chromas": false}, {"id": "28001", "num": 1, "name": "シャドウ イブリン", "chromas": false}, {"id": "28002", "num": 2, "name": "仮面舞踏会イブリン", "chromas": false}, {"id": "28003", "num": 3, "name": "タンゴ イブリン", "chromas": false}, {"id": "28004", "num": 4, "name": "金庫破りイブリン", "chromas": false}, {"id": "28005", "num": 5, "name": "ブラッドムーン イブリン", "chromas": false}, {"id": "28006", "num": 6, "name": "K/DA イブリン", "chromas": false}, {"id": "28007", "num": 7, "name": "プレステージ K/DA イブリン", "chromas": false}, {"id": "28008", "num": 8, "name": "シュガーラッシュ イブリン", "chromas": true}, {"id": "28015", "num": 15, "name": "K/DA ALL OUT イブリン", "chromas": true}, {"id": "28024", "num": 24, "name": "盟約の魔女イブリン", "chromas": true}, {"id": "28031", "num": 31, "name": "プレステージ K/DA イブリン(2022)", "chromas": false}, {"id": "28032", "num": 32, "name": "精霊の花祭りイブリン", "chromas": true}, {"id": "28042", "num": 42, "name": "ソウルファイター イブリン", "chromas": true}, {"id": "28052", "num": 52, "name": "荒野のイブリン", "chromas": true}, {"id": "28053", "num": 53, "name": "プレステージ荒野のイブリン", "chromas": false}, {"id": "28064", "num": 64, "name": "混沌の闇イブリン", "chromas": false}], "lore": "ルーンテラ中の暗がりで悪魔イブリンは次の犠牲者を物色する。艶かしい女性の姿で獲物を誘い、相手を魅了したところで真の姿を露わす。それから彼女は犠牲者を「言葉にするのもはばかられるような拷問」にかけ、苦悶する様を糧（かて）に愉悦に浸る。この悪魔にとって、それらの色事は罪悪感の無いおふざけに過ぎない。だがルーンテラの住民の間では、情欲に溺れる者は悲惨な末路を迎えるという教訓を如実に伝えるおぞましい物語として知られている。", "blurb": "ルーンテラ中の暗がりで悪魔イブリンは次の犠牲者を物色する。艶かしい女性の姿で獲物を誘い、相手を魅了したところで真の姿を露わす。それから彼女は犠牲者を「言葉にするのもはばかられるような拷問」にかけ、苦悶する様を糧（かて）に愉悦に浸る。この悪魔にとって、それらの色事は罪悪感の無いおふざけに過ぎない。だがルーンテラの住民の間では、情欲に溺れる者は悲惨な末路を迎えるという教訓を如実に伝えるおぞましい物語として知られている。", "allytips": ["「アリュール」はチャーム効果を与えられるようになるまでの時間は長いが、魔法防御を低下させれば非常に有利になるので待って狙ってみる価値はある。", "ステルス中は近くにいる敵チャンピオンに発見されるタイミングに注意を払おう。これは近くにいる敵チャンピオンの上に黄色と赤の光る目で表示されている。", "体力が低下しても、「妖魔の影」の回復とカモフラージュを活かして戦闘に戻り、相手を驚かせてやろう。"], "enemytips": ["「ビジョンワード」を購入して設置することで、イブリンの位置を把握し奇襲に備えることができる。", "イブリンの脅威の大半は「アリュール」によるチャームだ。味方が「アリュール」でマークされたらその味方を守り、自分がマークされた時はイブリンが攻撃してきそうな方向に立ってもらい、味方に守ってもらおう。", "チームメイトがイブリンに奇襲されそうだと思ったら、ミニマップ上でピンを鳴らし、チャットでメッセージを送り、危険を知らせてあげよう。位置情報の共有が、彼女の弱点だ。"], "tags": ["Assassin", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 642, "hpperlevel": 98, "mp": 315, "mpperlevel": 42, "movespeed": 335, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 8.11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.1, "attackspeed": 0.667}, "spells": [{"id": "Evelynn<PERSON>", "name": "ヘイトスパイク", "description": "鞭を振って最初に当たった敵ユニットにダメージを与える。その後、地面から一直線に貫通するトゲを近くの敵に数回放つことができる。", "tooltip": "鞭で攻撃して最初に当たった敵に<magicDamage>{{ missiledamage }}の魔法ダメージ</magicDamage>を与え、その対象に対する次の3回の通常攻撃またはスキルが追加で<magicDamage>{{ totalbonusdamage }}の魔法ダメージ</magicDamage>を与える。このスキルは最大{{ qstackcount }}回まで<recast>再発動</recast>できる。<br /><br /><recast>再発動:</recast> 最も近くにいる敵を貫通するトゲを飛ばし、当たったすべての敵に<magicDamage>{{ missiledamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["鞭とトゲのダメージ", "追加ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ hatespikebasedamage }} -> {{ hatespikebasedamageNL }}", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [30, 30, 30, 30, 30], [60, 60, 60, 60, 60], [15, 25, 35, 45, 55], [25, 30, 35, 40, 45], [6, 6, 6, 6, 6], [30, 30, 30, 30, 30], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [-0.25, -0.25, -0.25, -0.25, -0.25]], "effectBurn": [null, "0", "30", "60", "15/25/35/45/55", "25/30/35/40/45", "6", "30", "50", "4", "-0.25"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EvelynnQ.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "EvelynnW", "name": "アリュール", "description": "対象に呪いをかける。少ししてから次に行う通常攻撃またはスキルがその敵にチャーム効果を与え、魔法防御を低下させる。", "tooltip": "チャンピオンまたはモンスターを5秒間マークする。その対象に通常攻撃またはスキルが命中するとマークを消費し、マナが還元されて、対象に{{ slowduration }}秒間、{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。<br /><br />マークが2.5秒以上持続していた場合、マークを消費して以下の追加効果を付与する:<li>対チャンピオン: {{ charmduration }}秒間の<status>チャーム効果</status>を与え、{{ shredduration }}秒間、<scaleMR>魔法防御を{{ mrshred*100 }}%</scaleMR>低下させる。<li>対モンスター: {{ monstercharm }}秒間の<status>チャーム効果</status>と、<magicDamage>{{ monsterdamagetotaltooltip }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["チャーム効果時間", "モンスターに対するチャーム効果時間", "魔法防御低下量", "モンスターに対するダメージ", "クールダウン", "射程"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ monstercharm }} -> {{ monstercharmNL }}", "{{ effect9amount*100.000000 }}% -> {{ effect9amountnl*100.000000 }}%", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [1.25, 1.5, 1.75, 2, 2.25], [-0.45, -0.45, -0.45, -0.45, -0.45], [15, 14, 13, 12, 11], [5, 5, 5, 5, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [250, 300, 350, 400, 450], [0.75, 0.75, 0.75, 0.75, 0.75], [0.35, 0.375, 0.4, 0.425, 0.45], [4, 4, 4, 4, 4]], "effectBurn": [null, "2", "1.25/1.5/1.75/2/2.25", "-0.45", "15/14/13/12/11", "5", "1.5", "250/300/350/400/450", "0.75", "0.35/0.375/0.4/0.425/0.45", "4"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1300, 1400, 1500, 1600], "rangeBurn": "1200/1300/1400/1500/1600", "image": {"full": "EvelynnW.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "EvelynnE", "name": "ウィップラッシュ", "description": "対象を鞭で打ってダメージを与え、その後少しの間だけ移動速度が増加する。", "tooltip": "敵を鞭で打って<magicDamage>{{ basedamage }}(+最大体力の{{ percenthealthbasetooltip }})の魔法ダメージ</magicDamage>を与える。{{ speedduration }}秒間、<speed>移動速度が{{ speedamount*100 }}%</speed>増加する。<br /><br /><keywordMajor>「妖魔の影」</keywordMajor>に包まれると、このスキルのクールダウンがリセットされて強化される。このスキルが強化されている間は、対象までダッシュして、対象および通り道にいたすべての敵に<magicDamage>{{ empowereddamage }}(+最大体力の{{ percenthealthempoweredtooltip }})の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "強化ダメージ", "移動速度", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ empowered<PERSON><PERSON> }} -> {{ empowereddamageNL }}", "{{ speedamount*100.000000 }}% -> {{ speedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.3, 0.35, 0.4, 0.45, 0.5], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [4, 4, 4, 4, 4], [450, 450, 450, 450, 450], [0.8, 0.85, 0.9, 0.95, 1], [2, 2, 2, 2, 2], [1.3, 1.35, 1.4, 1.45, 1.5]], "effectBurn": [null, "0", "0", "0.3/0.35/0.4/0.45/0.5", "2", "3", "4", "450", "0.8/0.85/0.9/0.95/1", "2", "1.3/1.35/1.4/1.45/1.5"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [210, 210, 210, 210, 210], "rangeBurn": "210", "image": {"full": "EvelynnE.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "EvelynnR", "name": "ラストカレス", "description": "少しの間だけ対象指定不可になり、自身の正面の範囲内にいる敵に大ダメージを与えてから後方に大きくワープする。", "tooltip": "悪魔のエネルギーを解放して大ダメージを与え、対象指定されなくなって後方にワープする。<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与え、<healing>体力が30%</healing>未満の敵に対しては与えるダメージが<magicDamage>{{ critdamage }}</magicDamage>に増加する。発動時に「妖魔の影」が1.25秒のクールダウンに入る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "強化ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect1amount*2.400000 }} -> {{ effect1amountnl*2.400000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 250, 375], [1.4, 1.4, 1.4], [2.5, 2.5, 2.5], [150, 225, 300], [3, 3, 3], [5, 4, 3], [0.3, 0.3, 0.3], [700, 700, 700], [30, 45, 60], [0, 0, 0]], "effectBurn": [null, "125/250/375", "1.4", "2.5", "150/225/300", "3", "5/4/3", "0.3", "700", "30/45/60", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EvelynnR.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "妖魔の影", "description": "戦闘中以外は「妖魔の影」に包まれ、体力が低下している場合は体力が回復する。レベル6以降は「妖魔の影」でカモフラージュも獲得する。", "image": {"full": "Evelynn_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}