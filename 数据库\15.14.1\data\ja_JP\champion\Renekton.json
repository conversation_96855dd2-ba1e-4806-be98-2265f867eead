{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renekton": {"id": "Renekton", "key": "58", "name": "レネクトン", "title": "砂漠の解体屋", "image": {"full": "Renekton.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "58000", "num": 0, "name": "default", "chromas": false}, {"id": "58001", "num": 1, "name": "銀河レネクトン", "chromas": false}, {"id": "58002", "num": 2, "name": "荒野の暴れん坊レネクトン", "chromas": false}, {"id": "58003", "num": 3, "name": "血塗られた怒りレネクトン", "chromas": false}, {"id": "58004", "num": 4, "name": "ルーン戦士レネクトン", "chromas": false}, {"id": "58005", "num": 5, "name": "灼熱の大地レネクトン", "chromas": false}, {"id": "58006", "num": 6, "name": "プールパーティ レネクトン", "chromas": false}, {"id": "58007", "num": 7, "name": "先史時代レネクトン", "chromas": false}, {"id": "58008", "num": 8, "name": "SKT T1 レネクトン", "chromas": false}, {"id": "58009", "num": 9, "name": "レネクトイ", "chromas": true}, {"id": "58017", "num": 17, "name": "ヘクステック レネクトン", "chromas": false}, {"id": "58018", "num": 18, "name": "漆黒の霧氷レネクトン", "chromas": true}, {"id": "58026", "num": 26, "name": "PROJECT: Renekton", "chromas": true}, {"id": "58033", "num": 33, "name": "秩序の光レネクトン", "chromas": true}, {"id": "58042", "num": 42, "name": "Worlds 2023 レネクトン", "chromas": true}, {"id": "58048", "num": 48, "name": "墨影のレネクトン", "chromas": true}], "lore": "威圧的な巨体に怒りをみなぎらせた超越者レネクトンは、灼熱のシュリーマに生まれ出でた。レネクトンはかつて、帝国随一と目されていた戦士であった。彼の率いる軍隊は、シュリーマを数限りない勝利に導いた。しかし、帝国は崩壊し、レネクトンは砂の下に幽閉される運命を辿る。時が流れ、世が変わりゆく間に、じわじわと彼は狂気に支配されていった。今や自由の身となったレネクトンは、兄ナサスを見つけ出し、葬り去ることに執念を燃やす。狂気の中、彼は数百年にも渡って自分を闇に封じ込めたのは、全てナサスの仕業だという妄想に憑りつかれているのだ。", "blurb": "威圧的な巨体に怒りをみなぎらせた超越者レネクトンは、灼熱のシュリーマに生まれ出でた。レネクトンはかつて、帝国随一と目されていた戦士であった。彼の率いる軍隊は、シュリーマを数限りない勝利に導いた。しかし、帝国は崩壊し、レネクトンは砂の下に幽閉される運命を辿る。時が流れ、世が変わりゆく間に、じわじわと彼は狂気に支配されていった。今や自由の身となったレネクトンは、兄ナサスを見つけ出し、葬り去ることに執念を燃やす。狂気の中、彼は数百年にも渡って自分を闇に封じ込めたのは、全てナサスの仕業だという妄想に憑りつかれ...", "allytips": ["「スライス・アンド・ダイス」は、少ないリスクで敵をけん制できる優秀なスキルだ。一撃目で対象まで斬り込み、別のスキルで追い打ちをかけ、さらに二撃目を使って安全な場所まで引き返そう。", "「ミートカット」は、周りに敵が多いほど大量の体力を回復できる。弱っていると見せかけ、敵が近寄ってきたところで発動するといいだろう。", "レネクトンにとってクールダウン短縮の効果は極めて重要だ。「フューリー」の増加が早まり、強化スキルを発動しやすくなる。"], "enemytips": ["レネクトンが攻撃してくるタイミングは「フューリー」のたまり具合によってある程度予測が可能だ。", "妨害攻撃を継続的に仕掛け、レネクトンをできるだけ戦闘から遠ざけよう。", "「フューリー」をためさせないこと。これでスキルの威力は激減する。"], "tags": ["Fighter", "Tank"], "partype": "フューリー", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 660, "hpperlevel": 111, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.15, "attackspeedperlevel": 2.75, "attackspeed": 0.665}, "spells": [{"id": "RenektonCleave", "name": "ミートカット", "description": "武器を振り回して周囲の敵に物理ダメージを与え、ダメージの数パーセントに相当する体力を回復する。「フューリー」が50以上たまっている場合は、ダメージと回復量が増える。", "tooltip": "刃を振り回して<physicalDamage>{{ basicdamage }}の物理ダメージ</physicalDamage>を与え、命中したチャンピオン以外のユニット1体ごとに<healing>{{ nonchamphealing }}の体力</healing>を回復し、チャンピオン1体ごとに<healing>{{ champhealing }}の体力</healing>を回復する。また、チャンピオン以外の対象1体あたり<keywordMajor>{{ minionfurygain }}フューリー</keywordMajor>、チャンピオン1体あたり<keywordMajor>{{ championfurygain }}フューリー</keywordMajor>を獲得する。<br /><br /><keywordMajor>フューリーボーナス</keywordMajor>: ダメージが<physicalDamage>{{ empdamage }}の物理ダメージ</physicalDamage>に増加して、チャンピオン以外のユニットからの体力回復量が<healing>{{ empnonchamphealing }}</healing>に増加し、チャンピオンからの体力回復量が<healing>{{ empchamphealing }}</healing>に増加する。<keywordMajor>フューリー</keywordMajor>は生成しない。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "チャンピオンごとの体力回復量", "非チャンピオンごとの体力回復量", "最大体力回復量"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ champheal }} -> {{ champhealNL }}", "{{ baseminionheal }} -> {{ baseminionhealNL }}", "{{ basichealcap }} -> {{ basichealcapNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RenektonCleave.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "RenektonPreExecute", "name": "メッタ斬り", "description": "敵を2回斬りつけて物理ダメージを与え、0.75秒間スタン効果を付与する。「フューリー」が50以上たまっている場合は攻撃回数が3回に増え、対象のダメージシールドを消滅させてより多くのダメージを与える。また、スタン時間が1.5秒に延びる。", "tooltip": "次の通常攻撃が2回攻撃になり、{{ stunduration }}秒間<status>スタン</status>させて、合計<physicalDamage>{{ basictotaldamage }}の物理ダメージ</physicalDamage>を与える。チャンピオンに命中すると追加で<keywordMajor>{{ bonusfuryvschamps }}フューリー</keywordMajor>を獲得する。<br /><br /><keywordMajor>フューリーボーナス</keywordMajor>: 通常攻撃の回数が3回に増え、<shield>シールド</shield>を破壊してから<physicalDamage>{{ emptotaldamage }}の物理ダメージ</physicalDamage>を与え、{{ enragedstunduration }}秒間<status>スタン効果</status>を付与する。<keywordMajor>フューリー</keywordMajor>は獲得しない。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "強化ダメージ", "クールダウン"], "effect": ["{{ basedamageperhit*2.000000 }} -> {{ basedamageperhitnl*2.000000 }}", "{{ basedamageperhit*3.000000 }} -> {{ basedamageperhitnl*3.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "RenektonPreExecute.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "RenektonSliceAndDice", "name": "スライス・アンド・ダイス", "description": "ダッシュ攻撃を繰り出し、進路上の敵にダメージを与える。強化時は与えるダメージが増え、2回目のダッシュ攻撃が命中した敵の物理防御を低下させる。", "tooltip": "ダッシュして<physicalDamage>{{ basicdamage }}の物理ダメージ</physicalDamage>を与える。命中したチャンピオン以外の対象1体あたり<keywordMajor>{{ minionragegeneration }}フューリー</keywordMajor>を獲得し、チャンピオン1体あたり<keywordMajor>{{ championragegeneration }}フューリー</keywordMajor>を獲得する。1体以上の敵に命中させると、{{ dicetimer }}秒間このスキルを一度だけ<recast>再発動</recast>できる。<br /><br /><keywordMajor>フューリーボーナス</keywordMajor>: <recast>再発動</recast>するとダッシュして、代わりに<physicalDamage>{{ empdamage }}の物理ダメージ</physicalDamage>を与え、{{ shredtimer }}秒間、<scaleArmor>物理防御を{{ enragedarmorshred }}%</scaleArmor>低下させる。<keywordMajor>フューリー</keywordMajor>は獲得しない。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "強化ダメージ", "物理防御低下率", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ enragedbasedamage }} -> {{ enragedbasedamageNL }}", "{{ enragedarmorshred }}% -> {{ enragedarmorshredNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RenektonSliceAndDice.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "RenektonReignOfTheTyrant", "name": "セベクの怒り", "description": "凶暴化して最大体力が増加し、周囲の敵にダメージを与える。凶暴化中は毎秒「フューリー」がたまっていく。", "tooltip": "{{ buffduration }}秒間、邪悪なエネルギーに包み込まれ、<healing>最大体力が{{ healthgain }}</healing>増加して、<keywordMajor>{{ furyoncast }}フューリー</keywordMajor>を得る。発動中は<magicDamage>{{ totaldamagepersecond }}の魔法ダメージ</magicDamage>を与え、毎秒<keywordMajor>{{ furypersecond }}フューリー</keywordMajor>を得る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加体力", "毎秒ダメージ", "クールダウン"], "effect": ["{{ healthgain }} -> {{ healthgainNL }}", "{{ auradamagepersecond }} -> {{ auradamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "RenektonReignOfTheTyrant.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "激情の支配", "description": "攻撃に「フューリー」を獲得する。自身の体力が低下していると「フューリー」の獲得量が増加する。「フューリー」を消費するとスキルに追加効果を付与できる。", "image": {"full": "Renekton_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}