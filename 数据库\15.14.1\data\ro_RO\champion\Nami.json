{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nami": {"id": "<PERSON><PERSON>", "key": "267", "name": "<PERSON><PERSON>", "title": "aleasa mării", "image": {"full": "Nami.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "267000", "num": 0, "name": "default", "chromas": false}, {"id": "267001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "267002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "267003", "num": 3, "name": "Urf sirenă", "chromas": false}, {"id": "267007", "num": 7, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "267008", "num": 8, "name": "Nami SKT T1", "chromas": false}, {"id": "267009", "num": 9, "name": "<PERSON><PERSON> c<PERSON>", "chromas": true}, {"id": "267015", "num": 15, "name": "<PERSON>i cu toiagul splendid", "chromas": true}, {"id": "267024", "num": 24, "name": "<PERSON><PERSON> destin <PERSON>", "chromas": true}, {"id": "267032", "num": 32, "name": "Nami vrăjitoare", "chromas": true}, {"id": "267041", "num": 41, "name": "Nami distracție în spațiu", "chromas": true}, {"id": "267042", "num": 42, "name": "Nami distracție în spațiu (Prestigiu)", "chromas": false}, {"id": "267051", "num": 51, "name": "<PERSON><PERSON> sabat", "chromas": true}, {"id": "267058", "num": 58, "name": "<PERSON><PERSON> crea<PERSON> de mituri", "chromas": true}], "lore": "Nami este o tânără și încăpățânată vastaya a mării. Când a fost încălcat acordul străvechi dintre tribul Marai și targonieni, ea a fost prima care a părăsit oceanul și s-a aventurat pe uscat. Neavând de ales, a decis să ducă singură la bun sfârșit ritualul sacru care i-ar menține poporul în siguranță. În haosul acestei noi ere, Nami își înfruntă viitorul incert cu hotărâre și curaj, folosindu-și sceptrul mareic pentru a invoca însăși puterea oceanelor.", "blurb": "Nami este o tânără și încăpățânată vastaya a mării. Când a fost încălcat acordul străvechi dintre tribul Marai și targonieni, ea a fost prima care a părăsit oceanul și s-a aventurat pe uscat. Neavând de ales, a decis să ducă singură la bun sfârșit...", "allytips": ["''Închisoarea acvatică'' are un timp de reactivare lung, așa că asigură-te că o folosești la momentul potrivit.", "Dacă folosești abilitatea ''Flux și reflux'' în timpul unei confruntări cu un campion inamic, poți întoarce lupta în favoarea ta.", "Abilitatea supremă a lui Nami poate fi foarte utilă la inițierea atacurilor asupra inamicilor aflați la distanță."], "enemytips": ["''Închisoarea acvatică'' este o abilitate foarte puternică, dar cu un timp de reactivare lung. Profită de ocazie dacă Nami o ratează.", "''Val<PERSON> mare<PERSON>'' are o rază de acțiune foarte lungă, dar se mi<PERSON><PERSON><PERSON> lent, deci fii atent când vine spre tine și ferește-te din calea lui.", "Încearcă să eviți lupta cu un adversar afectat de ''Binecuvântarea Alesei Mării''. Are o durată scurtă, deci încearcă să aștepți să treacă efectul."], "tags": ["Support", "Mage"], "partype": "Mană", "info": {"attack": 4, "defense": 3, "magic": 7, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 335, "armor": 29, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.61, "attackspeed": 0.644}, "spells": [{"id": "NamiQ", "name": "Închisoare acvatică", "description": "Trimite spre zona-țintă o bulă care provoacă daune și amețește toți inamicii la impact.", "tooltip": "<PERSON><PERSON> la<PERSON> un balon care <status>ame<PERSON><PERSON>ș<PERSON></status> inamicii timp de {{ e2 }} secunde și le provoacă <magicDamage>{{ totaldamagett }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 130, 185, 240, 295], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/130/185/240/295", "1.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "NamiQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiW", "name": "Flux și reflux", "description": "Dezlănțuie un torent de apă ce ricoșează de la campionii aliați la cei inamici, vindecând aliații și provocându-le daune inamicilor.", "tooltip": "<PERSON>i de<PERSON>n<PERSON>uie un torent de apă care ricoșează de la campionii aliați la cei inamici. Fiecare campion poate fi lovit o singură dată. Torentul lovește cel mult {{ maxtargets }} ținte.<li>Le reface <healing>{{ totalheal }} viață</healing> aliaților și ricoșează spre un campion inamic din apropiere. <li>Le provoacă <magicDamage>{{ totaldamage }} daune magice</magicDamage> inamicilor și ricoșează spre un campion aliat din apropiere.<br />Valoarea daunelor și a vindecării se modifică cu {{ bouncescaling }} după fiecare ricoșeu. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vindecare", "Daune", "Cost de @AbilityResourceName@"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "NamiW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiE", "name": "Binecuvântarea Alesei Mării", "description": "Oferă pentru o scurtă perioadă de timp putere sporită unui campion aliat. Atacurile de bază și vrăjile aliatului provoacă daune magice bonus și încetinesc ținta.", "tooltip": "<PERSON>i <PERSON>ște următoarele {{ hitcount }} atacuri și abilități ale unui campion aliat timp de {{ buffduration }} secunde. Acestea vor <status>încetini</status> țintele cu {{ totalslow }} timp de {{ slowduration }} secunde și vor provoca <magicDamage>{{ totaldamage }} daune magice</magicDamage> suplimentare. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslow }}% -> {{ baseslowNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [15, 20, 25, 30, 35], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "15/20/25/30/35", "1", "3", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NamiE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiR", "name": "<PERSON>", "description": "Invocă un val mareic uriaș care aruncă inamicii în sus, încetinindu-i și provocându-le daune. Aliații loviți primesc de două ori efectul ''Mareei dezlănțuite''.", "tooltip": "Nami invocă un val mareic, <status>aruncând în sus</status> inamicii timp de 0,5 secunde, <status>încetinindu-i</status> cu {{ e4 }}% și provocându-le <magicDamage>{{ totaldamage }} daune magice</magicDamage>. Durata <status>încetinirii</status> crește în funcție de distanța parcursă de val, până la cel mult {{ e5 }} secunde.<br /><br />Aliații loviți de val primesc un efect de două ori mai puternic din <spellName>''<PERSON><PERSON> dezl<PERSON>n<PERSON>''</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [2, 2, 2], [70, 70, 70], [4, 4, 4], [0.002, 0.002, 0.002], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.5", "2", "70", "4", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2550, 2550, 2550], "rangeBurn": "2550", "image": {"full": "NamiR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Atunci când abilitățile lui <PERSON> lovesc campioni aliați, aceștia primesc un bonus scurt la viteza de mișcare.", "image": {"full": "NamiPassive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}