{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ryze": {"id": "Ryze", "key": "13", "name": "ライズ", "title": "ルーンの魔導師", "image": {"full": "Ryze.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "13000", "num": 0, "name": "default", "chromas": false}, {"id": "13001", "num": 1, "name": "若き日のライズ", "chromas": false}, {"id": "13002", "num": 2, "name": "部族民ライズ", "chromas": false}, {"id": "13003", "num": 3, "name": "ライズおじさん", "chromas": false}, {"id": "13004", "num": 4, "name": "達成者ライズ", "chromas": false}, {"id": "13005", "num": 5, "name": "ライズ教授", "chromas": false}, {"id": "13006", "num": 6, "name": "ゾンビ ライズ", "chromas": false}, {"id": "13007", "num": 7, "name": "闇のクリスタル ライズ", "chromas": false}, {"id": "13008", "num": 8, "name": "海賊ライズ", "chromas": false}, {"id": "13009", "num": 9, "name": "白ひげライズ", "chromas": false}, {"id": "13010", "num": 10, "name": "SKT T1 ライズ", "chromas": true}, {"id": "13011", "num": 11, "name": "Worlds 2019 ライズ", "chromas": true}, {"id": "13013", "num": 13, "name": "砂漠の守護者ライズ", "chromas": true}, {"id": "13020", "num": 20, "name": "アルカナ ライズ", "chromas": true}, {"id": "13029", "num": 29, "name": "ブラッドムーン ライズ", "chromas": true}], "lore": "ライズは類まれな能力を持つルーンテラ屈指の魔術師として知られ、古くから揺るぎない信念を持って活動している。その信念の裏に、彼は耐え難いほどの重荷を背負っている。ライズは計り知れない才能と神秘の力に関する膨大な知識を駆使し、ワールドルーン──無から世界を形成したとされる原始の魔法の断片──を探すことに人生を捧げる。古代文字が刻まれたルーンは、妄用される前に回収しなければならない。ルーンテラを創生した古代文字は、ルーンテラを破壊する力をも秘めているのだ。", "blurb": "ライズは類まれな能力を持つルーンテラ屈指の魔術師として知られ、古くから揺るぎない信念を持って活動している。その信念の裏に、彼は耐え難いほどの重荷を背負っている。ライズは計り知れない才能と神秘の力に関する膨大な知識を駆使し、ワールドルーン──無から世界を形成したとされる原始の魔法の断片──を探すことに人生を捧げる。古代文字が刻まれたルーンは、妄用される前に回収しなければならない。ルーンテラを創生した古代文字は、ルーンテラを破壊する力をも秘めているのだ。", "allytips": ["「オーバーロード」の自動効果を活かしてダメージや速度を最大化させよう。", "「スペルフラックス」の短いクールダウンを活かせば多くの敵に「フラックス」を拡散できる。", "ライズは「ポータルワープ」をチャージしている最中も移動やスキルの使用が可能であり、それでポータルがキャンセルされることはない。"], "enemytips": ["「フラックス」でマークされている時にライズと勝負するのは危険だ。", "「ポータルワープ」の起動時間中にポータルから現れる敵への対処方法を考えておこう。", "「ポータルワープ」の起動時間中にライズに行動妨害効果を与えればポータルをキャンセルすることができる。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 645, "hpperlevel": 124, "mp": 300, "mpperlevel": 70, "movespeed": 340, "armor": 22, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "RyzeQWrapper", "name": "オーバーロード", "description": "自動効果: 他の通常スキルを使うと「オーバーロード」のクールダウンがリセットされ、「ルーン」がチャージされる。「ルーン」が2つチャージされた状態で「オーバーロード」を使用すると、少しの間移動速度が増加する。<br><br>発動効果: 凝縮したエネルギー弾を直線上に発射して、最初に当たった敵にダメージを与える。すでに対象に「フラックス」が付与されていた場合、「オーバーロード」は追加ダメージを与え、周囲の敵に「フラックス」が波及する。", "tooltip": "<spellPassive>自動効果:</spellPassive> <spellName>「ルーンプリズン」</spellName>と<spellName>「スペルフラックス」</spellName>はこのスキルのクールダウンを解消すると同時に、「ルーン」を{{ runeduration }}秒間チャージする(最大{{ maximumrunes }}ルーンまで)。<br /><br /><spellActive>発動効果:</spellActive> エネルギー弾を放ち、最初に命中した敵に<magicDamage>{{ qdamagecalc }}の魔法ダメージ</magicDamage>を与える。対象に<keywordMajor>「フラックス」</keywordMajor>が付与されていた場合、それを消費してこのスキルのダメージを{{ spell.ryzer:overloaddamagebonus }}%増加させ、周囲の敵に<keywordMajor>「フラックス」</keywordMajor>を波及させる。<br /><br />{{ maximumrunes }}ルーンがチャージされていた場合、全ルーンを放出して{{ movementspeedduration }}秒間、<speed>移動速度が{{ movementspeedamount }}%</speed>増加する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "移動速度", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedamount }}% -> {{ movementspeedamountNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 38, 36, 34, 32], "costBurn": "40/38/36/34/32", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [25, 40, 55, 70, 85], [50, 75, 100, 125, 150], [25, 28, 31, 24, 37], [2, 2, 2, 2, 2], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0.015, 0.015, 0.015, 0.015, 0.015], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "25/40/55/70/85", "50/75/100/125/150", "25/28/31/24/37", "2", "2", "3", "0.01", "2", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RyzeQWrapper.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RyzeW", "name": "ルーンプリズン", "description": "対象をルーンの檻に捕らえ、ダメージとスロウ効果を与える。対象に「フラックス」が付与されている場合は、スロウの代わりにスネア状態にする。", "tooltip": "<magicDamage>{{ wdamagecalc }}の魔法ダメージ</magicDamage>を与え、{{ ccduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。対象に<keywordMajor>「フラックス」</keywordMajor>が付与されている場合は、それを消費して、<status>スロウ効果</status>の代わりに<status>スネア効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RyzeE", "name": "スペルフラックス", "description": "純粋な魔力を凝縮したオーブを発射して対象にダメージを与え、対象とその周囲のすべての敵にデバフを与える。ライズのスキルはデバフを受けた敵には追加効果を与える。", "tooltip": "オーブを発射して<magicDamage>{{ edamagecalc }}の魔法ダメージ</magicDamage>を与え、対象と周囲の敵に{{ debuffduration }}秒間<keywordMajor>「フラックス」</keywordMajor>を付与する。敵がすでに<keywordMajor>「フラックス」</keywordMajor>を受けていた場合は<keywordMajor>「フラックス」</keywordMajor>がさらに拡散する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.25, 3, 2.75, 2.5], "cooldownBurn": "3.5/3.25/3/2.75/2.5", "cost": [35, 45, 55, 65, 75], "costBurn": "35/45/55/65/75", "datavalues": {}, "effect": [null, [80, 90, 100, 110, 120], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.1, 0.1, 0.1, 0.1, 0.1], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [4, 4, 4, 4, 4], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/90/100/110/120", "40/50/60/70/80", "100", "0.1", "1.5", "1.5", "4", "1", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RyzeR", "name": "ポータルワープ", "description": "自動効果: 「フラックス」が付与された対象に「オーバーロード」で与える追加ダメージが増加する。<br><br>発動効果: 近くにポータルを発生させる。数秒後、指定した場所にポータルの範囲内の味方をテレポートさせる。", "tooltip": "<spellPassive>自動効果:</spellPassive> <keywordMajor>「フラックス」</keywordMajor>が付与された対象に対する<spellName>「オーバーロード」</spellName>の追加ダメージが{{ overloaddamagebonus }}%に増加する。<br /><br /><spellActive>発動効果:</spellActive> 離れた場所にポータルを作り出す。{{ chargetimetooltip }}秒後、ポータルの近くにいるすべての味方が、その場所までテレポートする。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "「オーバーロード」のダメージ増加"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ overloaddamagebonus }}% -> {{ overloaddamagebonusNL }}%"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "RyzeR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "古代の呪術", "description": "<mainText>ライズのスキルは増加したマナに応じて追加ダメージを与え、魔力に応じて最大マナが一定割合増加する。</mainText>", "image": {"full": "Ryze_P.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}