{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Volibear": {"id": "Volibear", "key": "106", "name": "<PERSON>о<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Неутихающая Буря", "image": {"full": "Volibear.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "106000", "num": 0, "name": "default", "chromas": false}, {"id": "106001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Повелитель Грома", "chromas": false}, {"id": "106002", "num": 2, "name": "Волибир Северная Буря", "chromas": false}, {"id": "106003", "num": 3, "name": "Хранитель рун Волибир", "chromas": false}, {"id": "106004", "num": 4, "name": "Ка<PERSON>и<PERSON><PERSON><PERSON> Волиб<PERSON>р", "chromas": false}, {"id": "106005", "num": 5, "name": "Эль Райо Волибир", "chromas": false}, {"id": "106006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Пронзенный Тысячью Мечей", "chromas": false}, {"id": "106007", "num": 7, "name": "Двойственный дракон Волибир", "chromas": true}, {"id": "106009", "num": 9, "name": "Двойственный дракон Волибир (престижный)", "chromas": false}, {"id": "106019", "num": 19, "name": "Воли<PERSON><PERSON>р Чернильная Тень", "chromas": false}], "lore": "Те, кто до сих пор чтят Волибира, считают его воплощением бури. Дикий, всеразрушающий и невероятно упрямый, он пришел в тундру Фрельйорда задолго до первых смертных и теперь ревностно охраняет морозный край, созданный им вместе с другими полубогами, его братьями и сестрами. Он ненавидит цивилизацию за то, что она делает людей слабыми, и борется за возвращение старых времен, когда природа была дикой, а кровь лилась рекой. Всякого, кто осмелится ему противостоять, Волибир сметет с дороги клыками, когтями и рокочущей яростью грома.", "blurb": "Те, кто до сих пор чтят Волибира, считают его воплощением бури. Дикий, всеразрушающий и невероятно упрямый, он пришел в тундру Фрельйорда задолго до первых смертных и теперь ревностно охраняет морозный край, созданный им вместе с другими полубогами, его...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "Мана", "info": {"attack": 7, "defense": 7, "magic": 4, "difficulty": 3}, "stats": {"hp": 650, "hpperlevel": 104, "mp": 350, "mpperlevel": 70, "movespeed": 340, "armor": 31, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.75, "mpregen": 6.25, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "VolibearQ", "name": "Грозовой удар", "description": "Волибир ускоряется при движении в сторону врагов. Первый враг, которого он атакует, получает урон и <status>оглушается</status>.", "tooltip": "Волибир увеличивает свою <speed>скорость передвижения на {{ minspeedcalc }}</speed> (или на <speed>{{ maxspeedcalc }}</speed> при движении в сторону вражеских чемпионов) на {{ duration }} сек. Пока умение активно, следующая автоатака Волибира наносит <physicalDamage>{{ calculateddamage }} физического урона</physicalDamage> и <status>оглушает</status> цель на {{ stunduration }} сек.<br /><br />Волибир впадает в ярость, если враг <status>обездвиживает</status> его до того, как он успеет <status>оглушить</status> цель, - при этом действие умения заканчивается преждевременно, но его перезарядка сбрасывается.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Скорость передвижения", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ minspeed*100.000000 }}% -> {{ minspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VolibearQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "VolibearW", "name": "Растерзание", "description": "Волибир наносит урон врагу, накладывает на него эффекты при попадании и помечает. При повторном применении против той же цели умение наносит дополнительный урон и лечит Волибира.", "tooltip": "Волибир терзает врага, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> и помечая его на {{ markduration }} сек.<br /><br />При применении этого умения к помеченной цели его урон увеличивается до <physicalDamage>{{ empowereddamage }}</physicalDamage>, а Волибир восстанавливает себе <healing>здоровье в размере {{ baseheal }} плюс {{ percentmissinghealthhealingratio }} от недостающего здоровья</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Базовое лечение", "Недостающее здоровье", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ healpercent*100.000000 }}% -> {{ healpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "VolibearW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "VolibearE", "name": "Громовержец", "description": "Волибир призывает молнию в выбранном месте, которая наносит урон врагам и замедляет их. Если Волибир находится в области удара, он получает щит.", "tooltip": "Волибир призывает грозовое облако, которое выпускает молнию, нанося <magicDamage>магический урон в размере {{ totaldamagetooltip }} плюс {{ percentdamage*100 }}% от максимального запаса здоровья</magicDamage> и <status>замедляя</status> на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br />Если Волибир находится в области поражения, он получает <shield>щит прочностью {{ shieldapratiotooltip }} плюс {{ shieldamount*100 }}% от максимального запаса здоровья</shield> на {{ shieldduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Коэффициент урона"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percentdamage*100.000000 }}% -> {{ percentdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VolibearE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "VolibearR", "name": "Вестник бури", "description": "Волибир прыгает в выбранное место, нанося урон врагам под собой и замедляя их, а также увеличивая свой запас здоровья. Вражеские башни вокруг его места приземления временно перестают работать.", "tooltip": "Волибир преображается и прыгает, увеличивая <healing>запас здоровья на {{ healthamount }}</healing> и дальность атаки на {{ bonusattackrange }} на {{ transformduration }} сек.<br /><br />По приземлении Волибир <status>отключает</status> башни поблизости на {{ towerdisableduration }} сек. и наносит им <physicalDamage>{{ towerdamagetooltip }} физического урона</physicalDamage>. Враги поблизости <status>замедляются</status> на {{ slowamount*100 }}% (эффект ослабевает в течение 1 сек.). Враги прямо под Волибиром получают <physicalDamage>{{ sweetspotdamagetooltip }} физического урона</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Дополнительное здоровье", "Продолжительность отключения башен", "Перезарядка"], "effect": ["{{ sweetspotdamage }} -> {{ sweetspotdamageNL }}", "{{ healthamount }} -> {{ healthamountNL }}", "{{ towerdisableduration }} -> {{ towerdisabledurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 135, 110], "cooldownBurn": "160/135/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [75, 115, 155], [9, 9, 9], [12, 12, 12], [8, 8, 8], [0.08, 0.08, 0.08], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "75/115/155", "9", "12", "8", "0.08", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "VolibearR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Неутихающая буря", "description": "Автоатаки и умения Волибира увеличивают его скорость атаки. При накоплении максимального числа зарядов его автоатаки начинают наносить дополнительный магический урон врагам поблизости.", "image": {"full": "Volibear_Icon_P.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}