{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "문도 박사", "title": "자운의 광인", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "독극물 문도 박사", "chromas": false}, {"id": "36002", "num": 2, "name": "몸짱 문도씨", "chromas": false}, {"id": "36003", "num": 3, "name": "CEO 문도", "chromas": true}, {"id": "36004", "num": 4, "name": "분장한다 문도!", "chromas": false}, {"id": "36005", "num": 5, "name": "처형자 문도", "chromas": false}, {"id": "36006", "num": 6, "name": "전장을 누비는 문도", "chromas": false}, {"id": "36007", "num": 7, "name": "TPA 문도", "chromas": false}, {"id": "36008", "num": 8, "name": "수영장 파티 문도", "chromas": false}, {"id": "36009", "num": 9, "name": "프로레슬러 문도", "chromas": false}, {"id": "36010", "num": 10, "name": "얼음 왕자 문도", "chromas": true}, {"id": "36021", "num": 21, "name": "거리의 악마 문도 박사", "chromas": true}], "lore": "완전히 미쳐버린 비참한 살인마이자 오싹한 보라색 괴물로 변한 문도 박사는 많은 자운 시민이 특히나 어두운 밤에 외출을 피하는 이유이다. 현재 의사를 자처하는 그는 한때 자운에서 가장 악명 높은 정신 병원에 수용된 환자였다. 모든 의료진을 '치료'하고 나서, 문도 박사는 자신이 치료받았던 빈 병원에서 의사가 된 뒤 본인이 수없이 당한 몹시 부도덕한 치료법을 따라 하기 시작했다. 의약품은 많지만 의학 지식은 전혀 없는 문도 박사는 약물을 주사할 때마다 더욱더 괴물처럼 변해 그의 진료실 주변을 헤매는 불운한 '환자'들을 두려움에 빠트린다.", "blurb": "완전히 미쳐버린 비참한 살인마이자 오싹한 보라색 괴물로 변한 문도 박사는 많은 자운 시민이 특히나 어두운 밤에 외출을 피하는 이유이다. 현재 의사를 자처하는 그는 한때 자운에서 가장 악명 높은 정신 병원에 수용된 환자였다. 모든 의료진을 '치료'하고 나서, 문도 박사는 자신이 치료받았던 빈 병원에서 의사가 된 뒤 본인이 수없이 당한 몹시 부도덕한 치료법을 따라 하기 시작했다. 의약품은 많지만 의학 지식은 전혀 없는 문도 박사는 약물을 주사할 때마다...", "allytips": ["가학증을 시기 적절하게 사용하면 적 챔피언이 자신의 불리한 처지도 잊고 덤벼들게 할 수 있습니다.", "정령의 형상은 궁극기에서 얻는 치료 효과를 높이고 다른 스킬의 재사용 대기시간을 줄여줍니다.", "칠흑의 양날도끼는 중립 몬스터를 제거하는 데 효과적인 무기입니다. 본진으로 돌아가기 보다는 중립 몬스터를 제거하며 궁극기로 체력을 회복하십시오."], "enemytips": ["문도 박사가 궁극기를 사용하고 나면 그 즉시 아군들과 함께 협력하여 공격하십시오. 만에 하나 문도 박사를 신속하게 쓰러뜨리지 못하면 문도 박사의 체력이 입은 피해 이상으로 회복되기 때문입니다.", "문도 박사가 가학증 스킬을 사용하려고 할 때 소환사 주문 점화를 사용하여 체력 회복을 막으십시오."], "tags": ["Tank", "Fighter"], "partype": "없음", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "오염된 뼈톱", "description": "문도 박사가 오염된 뼈톱을 던져 처음 맞는 적에게 적 현재 체력에 비례한 피해를 입히고 둔화 효과를 적용합니다.", "tooltip": "문도 박사가 뼈톱을 던져 처음 맞는 적에게 <magicDamage>적 현재 체력의 {{ currenthealthdamage*100 }}%에 해당하는 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>시킵니다.<br /><br />뼈톱이 챔피언이나 몬스터에게 적중하면 문도 박사가 <healing>{{ healthrestoreonhitchampionmonster }}의 체력</healing>을 회복합니다. 챔피언 또는 몬스터가 아닌 대상에게 적중하면 <healing>{{ healthrestoreonhitminion }}의 체력</healing>을 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["적 현재 체력 비례 피해량", "최소 피해량", "몬스터 대상 최대 피해량", "체력 소모량"], "effect": ["{{ currenthealthdamage*100.000000 }}% -> {{ currenthealthdamagenl*100.000000 }}%", "{{ minimumdamage }} -> {{ minimumdamageNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}", "{{ healthcost }} -> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "체력 {{ healthcost }}"}, {"id": "DrMundoW", "name": "심장 전기 충격", "description": "문도 박사가 스스로를 감전시켜 주변 적에게 지속 피해를 입히고 자신이 입는 피해의 일부를 저장합니다. 지속시간이 끝나거나 다시 사용하면 주변 적에게 큰 피해를 입힙니다. 적에게 적중 시 저장한 피해의 일부를 체력으로 회복합니다.", "tooltip": "문도 박사가 제세동기를 충전하여 주변 적에게 최대 {{ duration }}초까지 초당 <magicDamage>{{ damagepertick*4 }}의 마법 피해</magicDamage>를 입힙니다. 추가로 첫 {{ grayhealthinitialduration }}초 동안에는 입는 피해의 {{ grayhealthstorageinitial }}를, 남은 지속시간에는 입는 피해의 {{ grayhealthstorage*100 }}%를 회색 체력으로 저장하고 <recast>재사용</recast>할 수 있습니다.<br /><br /><recast>재사용 시:</recast> 제세동기가 폭발하여 주변 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 챔피언에게 적중하면 문도 박사가 <healing>회색 체력의 {{ grayhealthbigmod*100 }}%</healing>를 회복하며 그렇지 않으면 <healing>회색 체력의 {{ grayhealthsmallmod*100 }}%</healing>를 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["틱당 피해량", "재사용 피해량", "재사용 대기시간"], "effect": ["{{ damagepertick }} -> {{ damagepertickNL }}", "{{ recastbasedamage }} -> {{ recastbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "%", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "현재 체력의 {{ currenthealthcost*100 }}%"}, {"id": "DrMundoE", "name": "둔기에 의한 외상", "description": "기본 지속 효과 - 문도 박사가 최대 체력에 비례하여 추가 공격력을 얻습니다.<br><br>사용 시 - 문도 박사가 적에게 왕진 가방을 내리쳐 자신이 잃은 체력에 비례한 추가 피해를 입힙니다. 이때 처치된 적은 밀려나며 지나치는 적에게 피해를 입힙니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 문도 박사가 <physicalDamage>{{ passivebonusad }}의 공격력</physicalDamage>을 얻습니다.<br /><br /><spellActive>사용 시:</spellActive> 문도 박사가 왕진 가방을 맹렬하게 휘둘러 다음 공격 시 <physicalDamage>{{ additionaldamage }}의 물리 피해</physicalDamage>를 추가로 입힙니다. 이 수치는 문도 박사가 잃은 체력에 비례하여 최대 {{ maxdamageamptooltip }}까지 증가합니다. 이때 처치된 적은 밀려나며 지나치는 적에게 <physicalDamage>{{ additionaldamage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "기본 피해량", "체력 소모량", "공격력 전환 체력"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ flathealthcost }} -> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}% -> {{ healthtoadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "체력 {{ flathealthcost }}"}, {"id": "DrMundoR", "name": "최대 투여량", "description": "문도 박사가 화학 물질을 투여하여 잃은 체력의 일부를 즉시 회복합니다. 이후 이동 속도를 얻고, 긴 지속시간에 걸쳐 최대 체력의 일부만큼 체력을 회복합니다.", "tooltip": "문도 박사가 화학 물질을 투여하여 <healing>잃은 체력의 {{ missinghealthheal*100 }}%를 최대 체력</healing>으로, <speed>{{ speedboostamount*100 }}%의 이동 속도</speed>를 얻고 {{ duration }}초에 걸쳐 <healing>최대 체력의 {{ maxhealthhot*100 }}%</healing>만큼 체력을 회복합니다.<br /><br />스킬 레벨이 3이 되면 근처에 있는 적 챔피언 하나당 두 회복 효과 모두 {{ bonuspernearbychampion*100 }}%씩 추가로 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["추가 체력", "이동 속도", "최대 체력 %"], "effect": ["{{ missinghealthheal*100.000000 }}% -> {{ missinghealthhealnl*100.000000 }}%", "{{ speedboostamount*100.000000 }}% -> {{ speedboostamountnl*100.000000 }}%", "{{ maxhealthhot*100.000000 }}% -> {{ maxhealthhotnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "가고 싶은 데로 간다", "description": "문도 박사가 처음으로 적중하는 이동 불가 효과에 저항하며, 체력을 잃고 근처에 화학 물질이 든 통을 떨어뜨립니다. 통 위로 이동하면 통을 주워 체력을 회복하고 이 스킬의 재사용 대기시간을 줄입니다.<br><br>또한 문도 박사의 체력 재생이 크게 증가합니다.<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}