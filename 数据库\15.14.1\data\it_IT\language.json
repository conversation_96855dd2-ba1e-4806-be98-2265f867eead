{"type": "language", "version": "15.14.1", "data": {"Back": "Indietro", "Continue": "Continua", "Language": "<PERSON><PERSON>", "ItemInfo": "Informazioni oggetto", "NextRank_": "<PERSON><PERSON><PERSON>:", "Rank_": "<PERSON><PERSON>:", "PlayingAs": "Come", "PlayingAgainst": "<PERSON><PERSON>", "CD_": "RIC:", "Range": "Gitt<PERSON>", "Range_": "Gittata:", "Details_": "Dettagli:", "PrimaryRole": "Ruolo principale", "mobileCompanion": "Compagno", "mobileForum": "Forum", "mobileFriends": "Am<PERSON>", "mobilePleaseWait": "Attendere...", "mobileNews": "Notizie", "modeClassic": "Classica", "modeOdin": "Sicuramente non Dominion", "modeAram": "ARAM", "modeTutorial": "Tutorial", "modeOneforall": "FRONTEND_oneforall_game_mode_name", "modeFirstblood": "FRONTEND_firstblood_game_mode_name", "mode6v6": "FRONTEND_6v6_game_mode_name", "modeCof": "FRONTEND_cof_game_mode_name", "Map1": "Landa degli evocatori", "Map8": "Valle dei cristalli", "Map10": "<PERSON><PERSON><PERSON> demoniaca", "Map12": "<PERSON><PERSON><PERSON> ulula<PERSON>", "categoryChampion": "Campioni", "categoryItem": "<PERSON><PERSON><PERSON>", "categoryMastery": "Maestrie", "categoryRune": "<PERSON><PERSON>", "categorySummoner": "Incantesimi dell'evocatore", "Gold": "Oro", "Level": "<PERSON><PERSON>", "Abilities": "Abilità", "ChampionInfo": "Informazioni Campione", "Lore": "Storia", "Stats": "Statistiche", "Tips": "<PERSON><PERSON><PERSON>", "statAbility": "Abilità", "statAttack": "Attacco", "statDefense": "Difesa", "statDifficulty": "Difficoltà", "statUtility": "Utilità", "Assassin": "Assassino", "Fighter": "Combattente", "Marksman": "Tiratore", "Mage": "Mago", "Support": "Supporto", "Tank": "Tank", "spells_Self": "Se stesso", "spells_target_0": "Se stesso", "spells_target_1": "<PERSON><PERSON><PERSON><PERSON>", "spells_target_2": "Area", "spells_target_3": "Cono", "spells_target_4": "Se stesso (area)", "spells_target_5": "Variabile", "spells_target_6": "<PERSON><PERSON>", "spells_target_7": "Direzione", "spells_target_8": "Direzione vettore", "spells_target_100": "Globale", "AllItems": "<PERSON><PERSON> gli og<PERSON>ti", "Armor": "Armatura", "Attack": "Attacco", "AttackSpeed": "Velocità d'attacco", "Consumable": "Consumabile", "CooldownReduction": "Riduzione ricarica", "CriticalStrike": "<PERSON><PERSON> critico", "Damage": "<PERSON><PERSON>", "Defense": "Difesa", "Health": "Salute", "HealthRegen": "Rigenerazione salute", "LifeSteal": "<PERSON><PERSON><PERSON><PERSON>", "Magic": "Magia", "Mana": "<PERSON><PERSON>", "ManaRegen": "Rigenerazione mana", "Movement": "Movimento", "SpellBlock": "Resistenza magica", "SpellDamage": "Potere magico", "Boots": "Stivali", "NonbootsMovement": "Altri oggetti di movimento", "Tenacity": "<PERSON><PERSON><PERSON>", "SpellVamp": "<PERSON><PERSON><PERSON><PERSON> magico", "GoldPer": "Guadagno d'oro", "Slow": "<PERSON><PERSON>", "Aura": "<PERSON>ra", "Active": "Attivo", "MagicPenetration": "Penetrazione magica", "ArmorPenetration": "Penetrazione armatura", "colloq_Armor": ";armour", "colloq_Attack": ";", "colloq_AttackSpeed": ";as", "colloq_Consumables": ";", "colloq_CriticalStrike": ";", "colloq_Damage": ";df", "colloq_Defense": ";", "colloq_Health": ";ps", "colloq_HealthRegen": ";rigenps;ps5", "colloq_LifeSteal": ";rubavita", "colloq_Magic": ";", "colloq_Mana": ";pm", "colloq_ManaRegen": ";rigenpm;pm5", "colloq_Movement": ";velocitàdimovimento", "colloq_SpellBlock": ";mr", "colloq_SpellDamage": ";ap", "colloq_Consumable": ";", "colloq_Boots": ";", "colloq_NonbootsMovement": ";", "colloq_CooldownReduction": ";cdr", "colloq_Tenacity": ";", "colloq_SpellVamp": ";spellvamp", "colloq_GoldPer": ";gp10", "colloq_Slow": ";", "colloq_Aura": ";", "colloq_Active": ";", "colloq_MagicPenetration": ";", "colloq_ArmorPenetration": ";", "RecommendedItems": "<PERSON><PERSON><PERSON> r<PERSON>", "recommended_starting": "<PERSON><PERSON>etti iniziali", "recommended_essential": "<PERSON><PERSON><PERSON> es<PERSON>", "recommended_offensive": "<PERSON>ggetti d'attacco", "recommended_defensive": "<PERSON><PERSON>etti difensivi", "recommended_consumables": "Consumabili", "Require_": "<PERSON><PERSON>:", "Cost_": "Costo:", "OriginalCost_": "Costo originale:", "SellsFor_": "Prezzo di vendita: ", "UpgradeCost_": "Costo aggiornamento:", "Builds_": "Diventa:", "ButtonBuy": "COMPRA", "ButtonSell": "VENDI", "SpecialRecipeSmall": "*", "SpecialRecipeLarge": "Speciale", "FlatArmorMod": "Armatura", "FlatAttackSpeedMod": "Velocità d'attacco", "FlatBlockMod": "Blocco", "FlatCritChanceMod": "Prob. critico", "FlatCritDamageMod": "<PERSON><PERSON> da <PERSON>o", "FlatEnergyPoolMod": "Energia", "FlatEnergyRegenMod": "Rigen. energia/5", "FlatEXPBonus": "Bonus PE", "FlatHPPoolMod": "Salute", "FlatHPRegenMod": "Rigen. salute/5", "FlatMagicDamageMod": "Potere magico", "FlatMovementSpeedMod": "Movimento", "FlatMPPoolMod": "<PERSON><PERSON>", "FlatMPRegenMod": "Rigen. mana/5", "FlatPhysicalDamageMod": "<PERSON><PERSON>", "FlatSpellBlockMod": "Resist. magica", "PercentArmorMod": "Armatura", "PercentAttackSpeedMod": "Velocità d'attacco", "PercentBlockMod": "Blocco", "PercentCritChanceMod": "Prob. critico", "PercentCritDamageMod": "<PERSON><PERSON> da <PERSON>o", "PercentDodgeMod": "<PERSON><PERSON><PERSON>", "PercentEXPBonus": "Bonus PE", "PercentHPPoolMod": "Salute massima", "PercentHPRegenMod": "Salute %/5", "PercentMagicDamageMod": "Potere magico max", "PercentMovementSpeedMod": "Movimento", "PercentMPPoolMod": "<PERSON><PERSON> massimo", "PercentMPRegenMod": "Mana %/5", "PercentPhysicalDamageMod": "<PERSON><PERSON>", "PercentSpellBlockMod": "Res. magia", "rFlatArmorModPerLevel": "Armatura al livello 18", "rFlatArmorPenetrationMod": "<PERSON><PERSON> armatura", "rFlatArmorPenetrationModPerLevel": "Pen. armatura al livello 18", "rFlatCritChanceModPerLevel": "Prob. critico al <PERSON>llo 18", "rFlatCritDamageModPerLevel": "<PERSON><PERSON> da critico al livello 18", "rFlatDodgeMod": "<PERSON><PERSON><PERSON>", "rFlatDodgeModPerLevel": "Schivata al livello 18", "rFlatEnergyModPerLevel": "Energia al livello 18", "rFlatEnergyRegenModPerLevel": "Rigen. energia/5 al livello 18", "rFlatGoldPer10Mod": "Oro ogni 10", "rFlatHPModPerLevel": "Salute al livello 18", "rFlatHPRegenModPerLevel": "Rigen. salute/5 al livello 18", "rFlatMagicDamageModPerLevel": "Potere magico al livello 18", "rFlatMagicPenetrationMod": "Pen. magica", "rFlatMagicPenetrationModPerLevel": "Pen. magica al livello 18", "rFlatMovementSpeedModPerLevel": "Movimento al livello 18", "rFlatMPModPerLevel": "Mana al livello 18", "rFlatMPRegenModPerLevel": "Rigen. mana/5 al livello 18", "rFlatPhysicalDamageModPerLevel": "<PERSON><PERSON> fi<PERSON> al livello 18", "rFlatSpellBlockModPerLevel": "Resist. magica al livello 18", "rFlatTimeDeadMod": "Tempo da morto", "rFlatTimeDeadModPerLevel": "Tempo da morto al livello 18", "rPercentArmorPenetrationMod": "<PERSON><PERSON> armatura", "rPercentArmorPenetrationModPerLevel": "Pen. armatura % al livello 18", "rPercentAttackSpeedModPerLevel": "Velocità d'attacco % al livello 18", "rPercentCooldownMod": "Riduzione ricarica", "rPercentCooldownModPerLevel": "Riduzione ricarica % al livello 18", "rPercentMagicPenetrationMod": "Pen. magica", "rPercentMagicPenetrationModPerLevel": "Pen. magica % al livello 18", "rPercentMovementSpeedModPerLevel": "Movimento % al livello 18", "rPercentTimeDeadMod": "Tempo da morto %", "rPercentTimeDeadModPerLevel": "Tempo da morto %/liv", "PercentLifeStealMod": "Bonus rubavita", "PercentSpellVampMod": "Bonus rubavita magico", "masteryFerocity": "Ferocia", "masteryCunning": "Astuzia", "masteryResolve": "<PERSON><PERSON><PERSON><PERSON>zza", "native_﻿ar": "العربية", "native_bg": "български език", "native_cs": "čeština", "native_de": "De<PERSON>ch", "native_el": "ελληνικά", "native_en": "English", "native_es": "español", "native_fr": "français", "native_hu": "magyar", "native_id": "Bahasa Indonesia", "native_it": "Italiano", "native_ja": "日本語", "native_ko": "조선말", "native_nl": "Nederlands", "native_pl": "język polski", "native_pt": "português", "native_ro": "română", "native_ru": "русский язык", "native_th": "ภาษาไทย", "native_tr": "Türkçe", "native_vi": "Tiếng <PERSON>", "native_zh": "汉语", "native_zh_CN": "简体字", "native_zh_MY": "简体字", "native_zh_TW": "繁體字"}, "tree": {"searchKeyIgnore": "", "searchKeyRemap": [{"k": "A", "v": "AÀà"}, {"k": "E", "v": "EÉéÈè"}, {"k": "I", "v": "IÍí<PERSON>"}, {"k": "O", "v": "OÓóÒò"}, {"k": "U", "v": "UÚúÙù"}]}}