{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shaco": {"id": "Shaco", "key": "35", "name": "샤코", "title": "악마 어릿광대", "image": {"full": "Shaco.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "35000", "num": 0, "name": "default", "chromas": false}, {"id": "35001", "num": 1, "name": "이상한 나라의 모자장수 샤코", "chromas": false}, {"id": "35002", "num": 2, "name": "궁전 어릿광대 샤코", "chromas": false}, {"id": "35003", "num": 3, "name": "호두까기 샤코", "chromas": false}, {"id": "35004", "num": 4, "name": "태엽장치 샤코", "chromas": false}, {"id": "35005", "num": 5, "name": "뒷골목 샤코", "chromas": false}, {"id": "35006", "num": 6, "name": "신바람 탈 샤코", "chromas": false}, {"id": "35007", "num": 7, "name": "와일드 카드 샤코", "chromas": false}, {"id": "35008", "num": 8, "name": "암흑의 별 샤코", "chromas": true}, {"id": "35015", "num": 15, "name": "비전 마법사 샤코", "chromas": true}, {"id": "35023", "num": 23, "name": "범죄 도시 악몽 샤코", "chromas": true}, {"id": "35033", "num": 33, "name": "겨울의 축복 샤코", "chromas": true}, {"id": "35043", "num": 43, "name": "소울 파이터 샤코", "chromas": true}, {"id": "35044", "num": 44, "name": "프레스티지 소울 파이터 샤코", "chromas": false}, {"id": "35054", "num": 54, "name": "공포의 밤 샤코", "chromas": true}, {"id": "35064", "num": 64, "name": "고양이 상자 샤코", "chromas": true}], "lore": "샤코는 오래 전, 외로움을 타던 어느 왕자를 위한 장난감으로 만들어졌다. 하지만 이제는 살육과 참사를 즐기는 마법 깃든 인형이다. 이전에는 친절한 마음씨를 지닌 광대였지만, 흑마법으로 타락하고 사랑하는 이를 잃은 상실감 때문에 오로지 불쌍한 인간들을 고문하는 것에서만 기쁨을 느끼게 되었다. 장난감과 단순한 속임수를 이용하여 상대를 농락하고 목숨을 빼앗은 다음, 유혈낭자한 '유희'의 결과에 만족하며 웃는다. 한밤중에 음산한 웃음소리가 들리는가? 그렇다면 이 악마의 어릿광대에게 다음 장난감으로 선택을 받은 것인지도 모른다.", "blurb": "샤코는 오래 전, 외로움을 타던 어느 왕자를 위한 장난감으로 만들어졌다. 하지만 이제는 살육과 참사를 즐기는 마법 깃든 인형이다. 이전에는 친절한 마음씨를 지닌 광대였지만, 흑마법으로 타락하고 사랑하는 이를 잃은 상실감 때문에 오로지 불쌍한 인간들을 고문하는 것에서만 기쁨을 느끼게 되었다. 장난감과 단순한 속임수를 이용하여 상대를 농락하고 목숨을 빼앗은 다음, 유혈낭자한 '유희'의 결과에 만족하며 웃는다. 한밤중에 음산한 웃음소리가 들리는가?...", "allytips": ["속임수 스킬로 지형을 넘어가면 안정적으로 탈출할 수 있습니다.", "적중 시 발동 효과를 지닌 아이템을 구매하십시오. 환각 스킬 사용 시 분신도 영향을 받습니다.", "암습 스킬의 피해량은 무한의 대검같이 치명타 피해를 올려주는 아이템의 효과를 받아 상승합니다."], "enemytips": ["샤코가 게임 초반에 강세를 보이면 샤코가 갈만한 정글 캠프에 투명 와드를 설치하는 것도 나쁘지 않습니다.", "샤코가 전투를 시작하자마자 속임수를 사용했다면 재사용 대기시간 동안 도망칠 수 없습니다. 팀원들과 힘을 합쳐 재빨리 샤코부터 해치우십시오."], "tags": ["Assassin"], "partype": "마나", "info": {"attack": 8, "defense": 4, "magic": 6, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 297, "mpperlevel": 40, "movespeed": 345, "armor": 30, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.694}, "spells": [{"id": "Deceive", "name": "속임수", "description": "샤코가 즉시 투명해지며 지정한 위치로 순간이동합니다.<br><br>투명 상태에서 가하는 첫 번째 기본 공격이 강화되어 추가 피해를 입히고 뒤에서 공격 시 치명타로 적중합니다.", "tooltip": "샤코가 근처로 순간이동해 {{ stealthduration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 됩니다. <spellName>깜짝 상자</spellName>나 <spellName>환각</spellName> 스킬을 사용해도 <keywordStealth>투명</keywordStealth> 상태는 유지됩니다.<br /><br /><keywordStealth>투명</keywordStealth> 상태에서 다음 기본 공격 시 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 추가로 입힙니다. 뒤에서 기본 공격 시 치명타가 적용되어 {{ qcritdamagemod }}의 피해를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "은신 지속시간", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Deceive.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "JackInTheBox", "name": "깜짝 상자", "description": "샤코가 깜짝 상자를 숨깁니다. 상자가 발동되면 주변의 적들을 공포 상태에 빠뜨린 다음 공격합니다.", "tooltip": "샤코가 {{ e5 }}초 뒤 시야에서 사라지는 상자를 남깁니다. 상자는 {{ trapduration }}초 동안 보이지 않습니다. 적이 가까이 다가오거나 발각되면 발동해 주변 적 챔피언을 {{ fearduration }}초 동안, 미니언과 정글 몬스터를 {{ minionfearduration }}초 동안 <status>공포</status>에 빠뜨립니다.<br /><br />상자가 발동되면 주변 모든 적을 5초 동안 공격해 <magicDamage>{{ aoedamage }}의 마법 피해</magicDamage>를 입히거나 단일 대상 공격 시 <magicDamage>{{ stdamage }}의 피해</magicDamage>를 입힙니다.<br /><br />깜짝 상자는 몬스터에게 <magicDamage>{{ monsterbonusdamage }}</magicDamage>의 추가 피해를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "공포 지속시간", "추가 몬스터 피해량", "소모값 @AbilityResourceName@"], "effect": ["{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ fearduration }} -> {{ feardurationNL }}", "{{ monsterbonusdamage }} -> {{ monsterbonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 65, 60, 55, 50], "costBurn": "70/65/60/55/50", "datavalues": {}, "effect": [null, [35, 50, 65, 80, 95], [200, 300, 400, 500, 600], [0.5, 0.75, 1, 1.25, 1.5], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/50/65/80/95", "200/300/400/500/600", "0.5/0.75/1/1.25/1.5", "40", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "JackInTheBox.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TwoShivPoison", "name": "양날 독", "description": "샤코의 단검은 공격에 맞은 대상을 중독시켜 이동 속도를 늦춥니다. 샤코는 단검을 던져 적에게 피해를 입히고 중독시킬 수 있으며 이때 대상의 체력이 30% 이하면 추가 피해를 입힙니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 이 스킬이 재사용 대기 상태가 아닐 때 샤코가 기본 공격 시 {{ slowdurationpassive }}초 동안 대상을 {{ slowamount*-100 }}% <status>둔화</status>시킵니다.<br /><br /><spellActive>사용 시:</spellActive> 샤코가 단검을 던져 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 대상을{{ slowdurationactive }}초 동안 {{ slowamount*-100 }}% <status>둔화</status>시킵니다. 대상의 체력이 {{ executehealththreshold*100 }}% 미만이면 <magicDamage>{{ totalexecutedamage }}의 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "TwoShivPoison.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "HallucinateFull", "name": "환각", "description": "샤코가 자신의 환영을 생성합니다. 적 포탑에게는 환영의 피해량이 줄어듭니다. 환영은 죽을 경우 폭발하며 세 개의 작은 깜짝 상자를 남겨 주변 적에게 피해를 입힙니다.", "tooltip": "샤코가 잠시 사라졌다가 {{ clonelifetime }}초 동안 유지되는 분신과 함께 다시 나타납니다. 분신은 처치되면 폭발하여 <magicDamage>{{ explosiontotaldamage }}의 마법 피해</magicDamage>를 입히고 즉시 발동하는 작은 <spellName>깜짝 상자</spellName> 세 개를 남깁니다. 분신은 샤코의 {{ cloneaadamagepercent*100 }}%에 해당하는 피해를 입히지만, 받는 피해량이 {{ cloneincomingdamagepercent*100 }}% 증가합니다.<br /><br />작은 <spellName>깜짝 상자</spellName>는 <magicDamage>{{ aoedamage }}의 마법 피해</magicDamage>를 입히거나 단일 적 공격 시 <magicDamage>{{ stdamage }}의 마법 피해</magicDamage>를 입히고 {{ boxfearduration }}초 동안 적을 <status>공포</status>에 빠뜨립니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["분신 사망 시 피해량", "작은 상자 피해량", "재사용 대기시간"], "effect": ["{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "HallucinateFull.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "암습", "description": "샤코의 기본 공격과 양날 독을 뒤에서 사용하면 추가 피해를 입힙니다.", "image": {"full": "Jester_CarefulStrikes.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}