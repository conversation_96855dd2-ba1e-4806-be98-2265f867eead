{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vi": {"id": "Vi", "key": "254", "name": "바이", "title": "필트오버의 집행자", "image": {"full": "Vi.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "254000", "num": 0, "name": "default", "chromas": false}, {"id": "254001", "num": 1, "name": "네온 주먹 바이", "chromas": false}, {"id": "254002", "num": 2, "name": "바이 경관", "chromas": true}, {"id": "254003", "num": 3, "name": "연미복 바이", "chromas": false}, {"id": "254004", "num": 4, "name": "데몬 바이", "chromas": false}, {"id": "254005", "num": 5, "name": "대장군 바이", "chromas": false}, {"id": "254011", "num": 11, "name": "프로젝트: 바이", "chromas": false}, {"id": "254012", "num": 12, "name": "사랑꾼 바이", "chromas": true}, {"id": "254020", "num": 20, "name": "초능력특공대 바이", "chromas": true}, {"id": "254029", "num": 29, "name": "아케인 지하도시 바이", "chromas": false}, {"id": "254030", "num": 30, "name": "가슴앓이 바이", "chromas": true}, {"id": "254039", "num": 39, "name": "태고의 습격 바이", "chromas": true}, {"id": "254048", "num": 48, "name": "아케인 싸움꾼 바이", "chromas": false}], "lore": "자운의 암흑가에서 자란 다혈질 성격의 바이는 충동적이고 성급한 데다가 권위에 대한 존중이라고는 눈곱만큼도 없다. 윗동네에서 사고를 치고 다니던 어린 시절에도 스틸워터 교도소에서 부당하게 긴 징역살이를 했을 때도 언제나 상황을 빠르게 판단하며 살아남았다. 이제 평화를 깨는 대신 유지하기 위해 필트오버의 집행자가 된 바이는 육중한 벽을 단숨에 부술 만큼 무시무시한 마법공학 건틀릿을 휘두르며 범죄자를 색출하고 있다.", "blurb": "자운의 암흑가에서 자란 다혈질 성격의 바이는 충동적이고 성급한 데다가 권위에 대한 존중이라고는 눈곱만큼도 없다. 윗동네에서 사고를 치고 다니던 어린 시절에도 스틸워터 교도소에서 부당하게 긴 징역살이를 했을 때도 언제나 상황을 빠르게 판단하며 살아남았다. 이제 평화를 깨는 대신 유지하기 위해 필트오버의 집행자가 된 바이는 육중한 벽을 단숨에 부술 만큼 무시무시한 마법공학 건틀릿을 휘두르며 범죄자를 색출하고 있다.", "allytips": ["금고 부수기가 완전히 충전되면 피해량이 두 배가 됩니다. 달아나는 적을 잡아서 최후의 일격을 날릴 때 사용하면 좋습니다.", "끈질긴 힘의 충격파에 걸리는 적은 모두 100%의 피해를 입습니다. 미니언 뒤에 적이 숨어 있을 때 사용하세요.", "정지 명령은 전투 개시에 좋은 스킬입니다. 다만 아군보다 너무 앞서 가지 않도록 주의하세요."], "enemytips": ["금고 부수기가 완전히 충전되면 피해량이 두 배가 됩니다. 바이가 충전을 시작하면 후퇴하거나 공격을 피해야만 합니다.", "바이에게 세 번 연속으로 맞으면 방어구가 갈라지고, 바이의 공격 속도가 상승합니다. 바이를 상대할 때는 싸움이 길어지지 않도록 중간에 끊어 주세요.", "바이가 궁극기를 쓸 땐 멈출 수 없으니 주의하세요. 바이가 돌진을 마칠 때까지 위치 이동 효과는 아껴두세요."], "tags": ["Fighter", "Assassin"], "partype": "마나", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 295, "mpperlevel": 65, "movespeed": 340, "armor": 30, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 10, "hpregenperlevel": 1, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "ViQ", "name": "금고 부수기", "description": "바이가 건틀릿을 충전하여, 두꺼운 금고도 뚫을 위력의 타격을 가하며 전방으로 돌진합니다. 부딪힌 적들은 뒤로 밀려나고, 찌그러뜨리기가 중첩됩니다.", "tooltip": "<charge>충전 시작:</charge> 바이가 강력한 한 방을 충전하며 {{ e4 }}% <status>둔화</status>됩니다.<br /><br /><release>돌진:</release> 바이가 전방으로 돌진하며 부딪친 모든 적에게 충전 시간에 비례해 <physicalDamage>{{ totaldamage }}~{{ maxdamagetooltip }}의 물리 피해</physicalDamage>를 입히고 <spellName>찌그러뜨리기</spellName> 효과를 적용합니다. 적 챔피언과 충돌하면 멈추면서 적을 <status>뒤로</status> <status>밀어냅니다</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최소 피해량", "최대 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ mindamage*2.500000 }} -> {{ mindamagenl*2.500000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "15", "6", "5", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ViQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ViW", "name": "찌그러뜨리기", "description": "바이의 주먹이 적의 방어구를 찌그러뜨려 추가 피해를 가하며 공격 속도가 상승합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 같은 대상에게 기본 공격을 3번 가할 때마다 대상 <physicalDamage>최대 체력의 {{ totaldamagetooltip }}에 해당하는 물리 피해</physicalDamage>를 추가로 입히고 {{ sharedbuffsduration }}초간 대상의 <scaleArmor>방어력을 {{ shredamount }}%</scaleArmor> 낮추며 바이의 <attackSpeed>공격 속도가 {{ attackspeed }}%</attackSpeed> 상승합니다. 또한 <spellName>폭발 보호막</spellName>의 재사용 대기시간이 {{ spell.vipassive:cdreductionon3hit }}초 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["적 최대 체력 비례 피해량", "공격 속도"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ attackspeed }}% -> {{ attackspeedNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "기본 지속 효과", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "기본 지속 효과"}, {"id": "ViE", "name": "끈질긴 힘", "description": "바이의 다음 공격이 대상을 관통하여 뒤에 있는 적들에게 피해를 가합니다.", "tooltip": "바이의 다음 기본 공격이 대상과 그 뒤의 적들에게 <physicalDamage>{{ totaldamagetooltip }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />이 스킬은 2회까지 충전됩니다. ({{ ammorechargetime }}초마다 충전){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "충전 시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [26, 32, 38, 44, 50], "costBurn": "26/32/38/44/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1.5", "6", "1", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "2", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ViR", "name": "정지 명령", "description": "바이가 적을 추격하며 중간에 걸리는 유닛들을 옆으로 밀어냅니다. 대상에 다다르면 공중으로 띄워올리고 뒤따라 점프하여, 바닥으로 쿵 찍어누릅니다.", "tooltip": "바이가 적 챔피언 한 명을 추격하면서 위치를 드러냅니다. 추격 중인 바이는 멈출 수 없으며, 닿는 순간 {{ rstunduration }}초 동안 <status>공중으로</status> <status>띄워 올리고</status> <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />중간에 바이와 부딪히는 적들은 옆으로 밀려나며 피해를 입고, {{ secondarytargetstunduration }}초 동안 <status>기절</status>합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "재사용 대기시간"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ViR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "폭발 보호막", "description": "바이는 시간이 흐르면 보호막이 1회 충전됩니다. 이 보호막은 적을 스킬로 적중시키면 활성화됩니다.", "image": {"full": "ViPassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}