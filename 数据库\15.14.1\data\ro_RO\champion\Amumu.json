{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON> t<PERSON>", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32004", "num": 4, "name": "Amum<PERSON>, cadoul returnat", "chromas": false}, {"id": "32005", "num": 5, "name": "<PERSON><PERSON><PERSON> aproape-rege al balului", "chromas": false}, {"id": "32006", "num": 6, "name": "<PERSON><PERSON><PERSON> micul cavaler", "chromas": false}, {"id": "32007", "num": 7, "name": "<PERSON><PERSON><PERSON> rob<PERSON><PERSON><PERSON> trist", "chromas": false}, {"id": "32008", "num": 8, "name": "<PERSON><PERSON><PERSON> petrecere surpriz<PERSON>", "chromas": true}, {"id": "32017", "num": 17, "name": "Amumu infernal", "chromas": true}, {"id": "32023", "num": 23, "name": "Amumu hextech", "chromas": false}, {"id": "32024", "num": 24, "name": "Am<PERSON>u, prințul dovleac", "chromas": true}, {"id": "32034", "num": 34, "name": "<PERSON><PERSON><PERSON> porțela<PERSON>", "chromas": true}, {"id": "32044", "num": 44, "name": "<PERSON><PERSON><PERSON> inim<PERSON> frântă", "chromas": true}, {"id": "32053", "num": 53, "name": "<PERSON><PERSON><PERSON>i drăgălași", "chromas": true}], "lore": "Legendele spun că Amumu este un suflet singuratic și melancolic din imperiul antic al Shurimei, care străbate lumea-n lung și-n lat pentru a-și găsi un prieten. Un blestem străvechi l-a condamnat să rămână pe veci singur: atingerea sa înseamnă moarte, iar afecțiunea sa, năruirea tuturor speranțelor. Cei care susțin că l-au văzut îl descriu ca pe un cadavru readus la viață, mic de statură și acoperit cu bandaje. Amumu a inspirat nenumărate mituri, cântece și povești din folclor transmise din generație în generație, astfel încât nimeni nu mai poate ști acum care este adevărul.", "blurb": "Legendele spun că Amumu este un suflet singuratic și melancolic din imperiul antic al Shurimei, care străbate lumea-n lung și-n lat pentru a-și găsi un prieten. Un blestem străvechi l-a condamnat să rămână pe veci singur: atingerea sa înseamnă moarte...", "allytips": ["<PERSON>umu depinde foarte mult de colegii de echipă, deci încearcă să mergi pe culoar cu prietenii, ca să fii cât mai eficient.", "La Amumu, reducerea timpului de reactivare este foarte puternică, dar de multe ori e dificil s-o obții. Ia buff-ul albastru ori de câte ori e posibil, pentru a câștiga o reducere a timpului de reactivare fără să-ți sacrifici atributele.", "''Disperarea'' e foarte eficientă împotriva altor campioni tanc, așa că asigură-te că te afli în raza de acțiune a adversarilor cu cel mai mare nivel de viață."], "enemytips": ["Nu te înghesui lângă alți aliați când Amumu încă are la dispoziție abilitatea sa supremă.", "Mișcările haotice sau folosirea valurilor de minioni ca paravan nu îi vor mai permite lui Amumu să folosească ''Aruncarea bandajului''.", "Abilitatea ''Disperare'' a lui <PERSON><PERSON>u face riscant<PERSON> cumpărarea obiectelor de viață."], "tags": ["Tank", "Support"], "partype": "Mană", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "Aruncarea bandajului", "description": "Amumu aruncă în direcția țintei un bandaj adeziv, apoi se trage spre ea, o amețește și îi provoacă daune.", "tooltip": "Amumu aruncă un bandaj, trăg<PERSON><PERSON>-se spre primul inamic lovit, <status>amețindu-l</status> timp de {{ e2 }} secundă și provocându-i <magicDamage>{{ totaldamage }} daune magice</magicDamage>.<br /><br />Această abilitate are 2 cumuluri.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON> <PERSON>", "Cost de @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "Disperare", "description": "Copleșiți de panică, inamicii din apropiere pierd în fiecare secundă un procent din viața maximă, iar durata <font color='#9b0f5f'>blestemelor</font> aruncate asupra lor se resetează.", "tooltip": "<toggle>La activare:</toggle> Amumu începe să plângă, provocându-le inamicilor din apropiere <magicDamage>daune magice în valoare de {{ basedamage }} plus {{ totalhealthdamage }}% din viața maximă</magicDamage> în fiecare secundă și reîmprospătând <keywordMajor>blestemul</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune în funcție de viață"], "effect": ["{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }} pe secundă", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }} pe secundă"}, {"id": "Tantrum", "name": "<PERSON><PERSON>", "description": "Reduce în mod permanent daunele fizice pe care le-ar suferi Amumu. Amumu își poate dezlănțui furia pentru a le provoca daune inamicilor din jur. De fiecare dată când Amumu e lovit, timpul de reactivare al abilității se reduce.", "tooltip": "<spellPassive>Pasiv<PERSON>:</spellPassive> Amumu suferă daune fizice reduse cu {{ damagereduction }}. În plus, când Amumu este lovit de un atac, timpul de reactivare al acestei abilități este redus cu {{ e3 }} secunde.<br /><br /><spellActive>Activă:</spellActive> Amumu se înfurie, provocându-le <magicDamage>{{ tantrumdamage }} daune magice</magicDamage> inamicilor din apropiere.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune reduse", "Timp de reactivare", "Daune"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "Blestemul mumiei triste", "description": "Amumu prinde în bandaje unitățile din apropiere, <keywordMajor>blestemându-le</keywordMajor>, provocându-le daune și amețindu-le.", "tooltip": "Am<PERSON>u <PERSON> band<PERSON>, <status>ame<PERSON><PERSON></status> inamicii timp de {{ rduration }} secunde, provocându-le <magicDamage>{{ rcalculateddamage }} daune magice</magicDamage> și <keywordMajor>blestemându-i</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cost de @AbilityResourceName@", "Timp de reactivare", "Daune"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamage }} -> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Atingere blestemată", "description": "Atacurile de bază ale lui Amumu <font color='#9b0f5f'>blestemă</font> inamicii. Atunci când li se provoacă daune magice, aceștia suferă daune reale bonus.", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}