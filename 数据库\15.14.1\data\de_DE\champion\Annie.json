{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Annie": {"id": "<PERSON>", "key": "1", "name": "<PERSON>", "title": "das Kind der Finsternis", "image": {"full": "Annie.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "1000", "num": 0, "name": "default", "chromas": false}, {"id": "1001", "num": 1, "name": "Gothic-Annie", "chromas": false}, {"id": "1002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "1003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "1004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Annie", "chromas": false}, {"id": "1005", "num": 5, "name": "<PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "1006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "1007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "1008", "num": 8, "name": "Panda-<PERSON>", "chromas": false}, {"id": "1009", "num": 9, "name": "Herzblatt-<PERSON>", "chromas": false}, {"id": "1010", "num": 10, "name": "Hextech-Annie", "chromas": false}, {"id": "1011", "num": 11, "name": "Supergalaktische Annie", "chromas": false}, {"id": "1012", "num": 12, "name": "Jubiläum-<PERSON>", "chromas": false}, {"id": "1013", "num": 13, "name": "Mondzodiak-<PERSON>", "chromas": true}, {"id": "1022", "num": 22, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "1031", "num": 31, "name": "Schreckensnacht-<PERSON>", "chromas": false}, {"id": "1040", "num": 40, "name": "Wintergeweihte Annie", "chromas": false}, {"id": "1050", "num": 50, "name": "Kriegerp<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Annie ist zwar noch ein Kind, doch beherrscht sie bereits immens mächtige Feuermagie. Se<PERSON><PERSON>t in den Schatten der Berge des nördlichen Noxus gilt sie als magischer Sonderfall. Ihre natürliche Verbundenheit zum Feuer kam durch unvorhersehbare Gefühlsausbrüche bereits früh in ihrem Leben zum Vorschein – doch irgendwann lernte sie, diese „spielerischen Tricks“ zu kontrollieren. Am liebsten verwandelt sie ihren geliebten Teddybären Tibbers in einen flammenden Beschützer. Trotz allem durchwandert Annie in ihrer ewig kindlichen Unschuld die düsteren Wälder, und sucht nach jemandem, der mit ihr spielt.", "blurb": "Annie ist zwar noch ein <PERSON>, doch beherrscht sie bereits immens mächtige Feuermagie. Selbst in den Schatten der Berge des nördlichen Noxus gilt sie als magischer Sonderfall. Ihre natürliche Verbundenheit zum Feuer kam durch unvorhersehbare...", "allytips": ["Eine Betäubung in der Hinterhand zu haben, kann in Kombination mit ihrer ultimativen Fähigkeit einen Gruppenkampf entscheidend beeinflussen.", "Das Töten von Vasallen mit „Auflösung“ ermöglicht es Annie, schon sehr früh Gold anzuhäufen.", "„Geschmolzener Schild“ kann benutzt werden, um gezielt die für eine Betäubung notwendigen Zauber zu sammeln, wodurch es manchmal lohnenswert sein kann, zumindest eine Stufe dafür zu investieren."], "enemytips": ["<PERSON><PERSON> be<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, verb<PERSON>nt gegnerische Einheiten in seiner Nähe. Vers<PERSON> dich von ihm fernzu<PERSON>en, nachdem er beschworen wurde.", "„Zerschmettern“ kann benutzt werden, um Tibbers niederzuringen.", "Achte auf eine weiße, wabernde Energie um Annie. Sie ist dann bereit, ihre Betäubung einzusetzen."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 10, "difficulty": 6}, "stats": {"hp": 560, "hpperlevel": 96, "mp": 418, "mpperlevel": 25, "movespeed": 335, "armor": 23, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 625, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 2.65, "attackspeedperlevel": 1.36, "attackspeed": 0.61}, "spells": [{"id": "AnnieQ", "name": "Auflösung", "description": "<PERSON> schle<PERSON>rt einen von Mana durchdrungenen Feuerball, der Schaden verursacht. Wenn der Treffer tödlich ist, werden die Manakosten zurückerstattet.", "tooltip": "<PERSON> schle<PERSON>rt einen Feuerball, der <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Wenn das Ziel stirbt, werden ihre Manakosten zurückerstattet und die Abklingzeit wird um 50&nbsp;% verringert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "AnnieQ.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AnnieW", "name": "Verbrennen", "description": "<PERSON> wirft einen flammenden Feuerkegel, der allen nahen Gegnern Schaden zufügt.", "tooltip": "Annie sendet eine <PERSON>uer<PERSON> aus, die <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieW.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON>", "name": "Geschmolzen<PERSON> Schild", "description": "Gewährt Annie oder einem Verbündeten einen Schild sowie einen Lauftemposchub und fügt <PERSON>, die sie mit Angriffen oder Fähigkeiten treffen, <PERSON><PERSON><PERSON> zu.", "tooltip": "<PERSON> gewährt einem verbündeten Champion {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON><PERSON><PERSON></shield> in <PERSON><PERSON><PERSON> von {{ shieldblocktotal }} und {{ movementspeedduration }}&nbsp;Sekunden lang <speed>{{ movespeedcalc }}&nbsp;abfallendes Lauftempo</speed>. Solange der Schild aktiv ist, erle<PERSON> Gegner, die den geschützten Verbündeten mit Angriffen oder Fähigkeiten treffen, <magicDamage>{{ damagereturn }}&nbsp;magischen <PERSON></magicDamage> (einmal pro Schild).<br /><br />Wenn Tibbers beschworen wird, erhält er immer die Effekte von <spellName>Geschmolzener Schild</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Reflektierter Schaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ shieldamount }} -> {{ shieldamountNL }}", "{{ damagereflection }} -> {{ damagereflectionNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AnnieE.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AnnieR", "name": "<PERSON><PERSON><PERSON> besch<PERSON>ören", "description": "<PERSON> ruft ihren Bären Tibbers ins Leben, um Einheiten in der Nähe Schaden zuzufügen. Tibbers kann angreifen und außerdem nahe Gegner verbrennen.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON> {{ rpercentpenbuff*100 }}&nbsp;% Magiedurchdringung.<br /><br /><PERSON> beschw<PERSON>rt ihren Bären Tibbers und verursacht <magicDamage>{{ initialburstdamage }}&nbsp;magischen Schaden</magicDamage>. Während der nächsten {{ tibberslifetime }}&nbsp;Sekunden verbrennt Tibbers nahe Gegner und verursacht dabei <magicDamage>{{ tibbersauradamage }}&nbsp;magischen Schaden pro Sekunde</magicDamage>.<br /><br />Wenn Tibbers beschworen ist und Annie einen gegnerischen Champion betäubt oder Annie stirbt, rastet Tibbers aus. Wenn Tibbers ausrastet, erhält er zusätzlich <attackSpeed>275&nbsp;% Angriffstempo</attackSpeed> und <speed>100&nbsp;% Lauftempo</speed>. Die<PERSON> Boni fallen über 3&nbsp;Sekunden hinweg ab.<br /><br /><recast>Reaktivierung:</recast> Du kannst Tibbers manuell Befehle geben.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "<PERSON><PERSON><PERSON> durch Aura", "Angriffsschaden", "Zusätzliches Lauftempo", "Abklingzeit", "Magiedurchdringung"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ auradamage }} -> {{ auradamageNL }}", "{{ tibbersattackdamage }} -> {{ tibbersattackdamageNL }}", "{{ tibbersbonusms }} -> {{ tibbersbonusmsNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rpercentpenbuff*100.000000 }}&nbsp;% -> {{ rpercentpenbuffnl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieR.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pyromanie", "description": "Nachdem Annie 4&nbsp;Fähigkeiten eingesetzt hat, betäubt ihre nächste offensive Fähigkeit das Ziel.<br><br><PERSON><PERSON> und wenn <PERSON> w<PERSON>, ist „Pyromanie“ verfügbar.", "image": {"full": "Annie_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}