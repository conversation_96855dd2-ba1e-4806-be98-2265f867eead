{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vayne": {"id": "<PERSON><PERSON>", "key": "67", "name": "베인", "title": "어둠 사냥꾼", "image": {"full": "Vayne.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "67000", "num": 0, "name": "default", "chromas": false}, {"id": "67001", "num": 1, "name": "수호자 베인", "chromas": false}, {"id": "67002", "num": 2, "name": "귀족 베인", "chromas": false}, {"id": "67003", "num": 3, "name": "용 사냥꾼 베인", "chromas": true}, {"id": "67004", "num": 4, "name": "사랑의 추적자 베인", "chromas": false}, {"id": "67005", "num": 5, "name": "SKT T1 베인", "chromas": false}, {"id": "67006", "num": 6, "name": "빛의 베인", "chromas": false}, {"id": "67010", "num": 10, "name": "영혼약탈자 베인", "chromas": true}, {"id": "67011", "num": 11, "name": "프로젝트: 베인", "chromas": false}, {"id": "67012", "num": 12, "name": "불꽃놀이 베인", "chromas": true}, {"id": "67013", "num": 13, "name": "프레스티지 불꽃놀이 베인", "chromas": false}, {"id": "67014", "num": 14, "name": "영혼의 꽃 베인", "chromas": true}, {"id": "67015", "num": 15, "name": "FPX 베인", "chromas": true}, {"id": "67025", "num": 25, "name": "감시자 베인", "chromas": true}, {"id": "67032", "num": 32, "name": "전투 박쥐 베인", "chromas": true}, {"id": "67033", "num": 33, "name": "프레스티지 불꽃놀이 베인 (2022)", "chromas": false}, {"id": "67044", "num": 44, "name": "빛의 인도자 베인", "chromas": true}, {"id": "67055", "num": 55, "name": "용술사 베인", "chromas": true}, {"id": "67064", "num": 64, "name": "떠오른 전설 베인", "chromas": true}], "lore": "가차 없는 괴물 사냥꾼 샤우나 베인은 가족을 몰살한 마녀를 찾아 죽이기로 맹세했다. 손목에 장착된 석궁과 복수심에 불타는 마음으로 무장한 베인이 진정 행복할 때는 오직 그림자 속에서 질풍처럼 은화살을 날려 흑마법을 연구하는 자나 흑마법이 만들어낸 창조물을 벨 때뿐이다.", "blurb": "가차 없는 괴물 사냥꾼 샤우나 베인은 가족을 몰살한 마녀를 찾아 죽이기로 맹세했다. 손목에 장착된 석궁과 복수심에 불타는 마음으로 무장한 베인이 진정 행복할 때는 오직 그림자 속에서 질풍처럼 은화살을 날려 흑마법을 연구하는 자나 흑마법이 만들어낸 창조물을 벨 때뿐이다.", "allytips": ["구르기는 다양한 용도로 사용할 수 있지만 벽을 뚫고 지날 수는 없습니다.", "선고는 대상을 벽에 밀어붙여 죽이거나 적에게서 탈출할 때 사용할 수 있습니다.", "대규모 접전에서는 다른 팀원이 먼저 전투를 시작할 때까지 기다리십시오."], "enemytips": ["베인은 체력이 약한 편이므로 몇 번 견제해 주면 조심스럽게 게임을 운용할 것입니다.", "베인을 상대로 구석에 몰리지 마십시오."], "tags": ["Marksman", "Assassin"], "partype": "마나", "info": {"attack": 10, "defense": 1, "magic": 1, "difficulty": 8}, "stats": {"hp": 550, "hpperlevel": 103, "mp": 232, "mpperlevel": 35, "movespeed": 330, "armor": 23, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.35, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "VayneTumble", "name": "구르기", "description": "베인이 구르기를 하며 다음 공격 대상을 신중하게 선택합니다. 베인의 다음 공격은 추가 피해를 입힙니다.", "tooltip": "베인이 짧은 구르기를 합니다. 이후 기본 공격을 하면 <physicalDamage>{{ adratiobonus }}의 물리 피해</physicalDamage>를 추가로 입힙니다.<br /><br /><rules>이 스킬은 피해를 입힐 때 효과가 발동합니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "공격력 %"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ totaladratio*100.000000 }}% -> {{ totaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VayneTumble.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "<PERSON>ayneSil<PERSON><PERSON><PERSON><PERSON>", "name": "은화살", "description": "베인이 사악한 존재에게만 피해를 주는 특수 금속이 달린 볼트를 사용합니다. 같은 대상에게 세 번째 공격 또는 스킬이 명중하면 대상 최대 체력에 비례한 고정 피해를 추가로 입힙니다.", "tooltip": "<spellPassive>기본 지속 효과</spellPassive>: 적에게 세 번 기본 공격 또는 스킬이 적중할 때마다 <trueDamage>대상 최대 체력의 {{ totaldamage }}에 해당하는 고정 피해</trueDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["고정 피해 %", "최소 피해량"], "effect": ["{{ maxhealthratio*100.000000 }}% -> {{ maxhealthrationl*100.000000 }}%", "{{ damagefloor }} -> {{ damagefloorNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "기본 지속 효과", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VayneSilveredBolts.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "기본 지속 효과"}, {"id": "VayneCondemn", "name": "선고", "description": "베인이 등에서 커다란 석궁을 뽑아 거대한 볼트를 발사하여 대상을 뒤로 날려보내고 대상에게 피해를 줍니다. 지형과 부딪힌 대상은 지형에 꽂힌 채 추가 피해를 받고 기절합니다.", "tooltip": "베인이 볼트를 발사하여 대상을 <status>뒤로 날려보내고</status> <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 지형에 부딪힌 대상은 <physicalDamage>{{ empowereddamagett }}의 추가 물리 피해</physicalDamage>를 입고 {{ stunduration }}초 동안 <status>기절</status>합니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "피해"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "VayneCondemn.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VayneInquisition", "name": "결전의 시간", "description": "격렬한 전투에 대비한 베인이 공격력을 높이고 구르기 시 투명 상태가 되며 구르기의 재사용 대기시간이 줄어듭니다. 어둠 사냥꾼의 이동 속도 증가량이 더 늘어납니다.", "tooltip": "베인이 {{ baseduration }}초 동안 <physicalDamage>{{ bonusattackdamage }}의 공격력</physicalDamage>을 얻습니다. 적 챔피언이 베인에게 피해를 입고 {{ damagedmarkerduration }}초 안에 죽으면 지속시간이 {{ durationtoadd }}초 늘어납니다. 또한 스킬이 지속되는 동안 <li><spellName>어둠 사냥꾼</spellName> 효과가 강화되어 <speed>이동 속도가 {{ movementspeed }}</speed> 증가합니다.<li><spellName>구르기</spellName>의 재사용 대기시간이 {{ tumblecdreduction }}% 감소하며, 구르기를 사용하면 {{ tumblestealthduration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "지속시간", "추가 공격력", "구르기 재사용 대기시간 감소"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseduration }} -> {{ basedurationNL }}", "{{ bonusattackdamage }} -> {{ bonusattackdamageNL }}", "{{ tumblecdreduction }}% -> {{ tumblecdreductionNL }}%"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "VayneInquisition.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "어둠 사냥꾼", "description": "베인이 악당을 무자비하게 사냥합니다. 근처 적 챔피언에게 접근할 때 이동 속도가 증가합니다.", "image": {"full": "Vayne_NightHunter.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}