{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Evelynn": {"id": "<PERSON><PERSON>", "key": "28", "name": "<PERSON><PERSON>", "title": "Agony's Em<PERSON>ce", "image": {"full": "Evelynn.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "28000", "num": 0, "name": "default", "chromas": false}, {"id": "28001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "28002", "num": 2, "name": "Masquerade Evelynn", "chromas": false}, {"id": "28003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "28004", "num": 4, "name": "Safecracker <PERSON>", "chromas": false}, {"id": "28005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "28006", "num": 6, "name": "K/DA Evelynn", "chromas": false}, {"id": "28007", "num": 7, "name": "Prestige K/DA Evelynn", "chromas": false}, {"id": "28008", "num": 8, "name": "<PERSON>", "chromas": true}, {"id": "28015", "num": 15, "name": "K/DA ALL OUT Evelynn", "chromas": true}, {"id": "28024", "num": 24, "name": "Coven <PERSON>", "chromas": true}, {"id": "28031", "num": 31, "name": "Prestige K/DA Evelynn (2022)", "chromas": false}, {"id": "28032", "num": 32, "name": "Spirit Blossom <PERSON><PERSON>", "chromas": true}, {"id": "28042", "num": 42, "name": "Soul Fighter Evelyn<PERSON>", "chromas": true}, {"id": "28052", "num": 52, "name": "High Noon Evelynn", "chromas": true}, {"id": "28053", "num": 53, "name": "Prestige High Noon Evelynn", "chromas": false}, {"id": "28064", "num": 64, "name": "Nightbringer <PERSON>", "chromas": false}], "lore": "Di dalam lapisan gelap Runeterra, iblis <PERSON><PERSON> mengincar korban berikutnya. Dia memikat mangsanya dengan wajah manusia cantik. Begitu korbannya terpikat oleh p<PERSON>, wujud asli <PERSON> akan terung<PERSON>. Dia lantas membuat korbannya mengalami torment, memuaskan dirinya sendiri dengan rasa sakit mereka. <PERSON>gi sang iblis, para penghubung ini adalah orang tak bersalah. Bagi warga Runeterra lainnya, kisah-kisah mengerikan itu jadi pengingat tentang betapa mengerikannya hasrat yang tak terkendali.", "blurb": "Di dalam lapisan gelap Runeterra, i<PERSON><PERSON> <PERSON><PERSON> mengincar korban berikutnya. Dia memikat mangsanya dengan wajah manusia cantik. Begitu korbannya terpikat o<PERSON>h <PERSON>, wu<PERSON>d asli <PERSON> akan terung<PERSON>. Dia lantas membuat korbannya mengalami torment...", "allytips": ["<PERSON><PERSON><PERSON> persiapan Allure mungkin terasa lama, tetapi <PERSON> dan pengurangan magic resist memberi <PERSON> keu<PERSON>an besar yang layak untuk ditunggu.", "<PERSON><PERSON> stealth, perhatikan kapan kamu (hampir) terdeteksi oleh champion musuh. Ini ditandai dengan mata kuning dan merah menyala di atas champion musuh di sekitar.", "Jika health rendah, kamu bisa manfa<PERSON> heal dari Demon Shade dan melakukan <PERSON>lage untuk kembali ke pertarungan dan mengejutkan lawan."], "enemytips": ["Membeli Vision Ward bisa membantumu mendeteksi lokasi Evelyn agar bisa menghadapi sergapannya.", "<PERSON> membuat <PERSON><PERSON> be<PERSON> ad<PERSON>, \"Allure\". <PERSON><PERSON>ngi sekutu dengan mark \"Allure\" atau, kalau kamu terkena mark, pastikan sekutu berada di antara kamu dan tempat kemungkinan <PERSON>n akan men<PERSON>.", "<PERSON>ka kamu curiga <PERSON> akan menyergap salah satu rekan timmu, beri tahu mereka dengan ping di minimap dan ketik di chat."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 642, "hpperlevel": 98, "mp": 315, "mpperlevel": 42, "movespeed": 335, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 8.11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.1, "attackspeed": 0.667}, "spells": [{"id": "Evelynn<PERSON>", "name": "Hate Spike", "description": "<PERSON><PERSON> menyerang menggunakan cambuk, <PERSON><PERSON><PERSON><PERSON><PERSON> damage ke unit pertama yang kena. <PERSON><PERSON><PERSON>, <PERSON><PERSON> bisa menembakkan sekumpulan duri ke musuh di sekitar beberapa kali.", "tooltip": "<PERSON><PERSON> men<PERSON>ang dengan <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ missiledamage }} magic damage</magicDamage> pada musuh pertama yang terkena dan membuat 3 Serangan atau Ability Evelynn berikutnya pada unit tersebut menghasilkan <magicDamage>{{ totalbonusdamage }} magic damage</magicDamage> tambahan. Evelynn bisa <recast>Recast</recast> Ability ini hingga {{ qstackcount }} kali.<br /><br /><recast>Recast:</recast> Evelynn menembakkan duri yang menembus musuh terdekat, menghasilkan <magicDamage>{{ missiledamage }} magic damage</magicDamage> pada semua musuh yang terkena.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Lasher dan Spike", "Damage Bonus", "@AbilityResourceName@ Cost"], "effect": ["{{ hatespikebasedamage }}-> {{ hatespikebasedamageNL }}", "{{ bonusdamagebase }}-> {{ bonusdamagebaseNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [30, 30, 30, 30, 30], [60, 60, 60, 60, 60], [15, 25, 35, 45, 55], [25, 30, 35, 40, 45], [6, 6, 6, 6, 6], [30, 30, 30, 30, 30], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [-0.25, -0.25, -0.25, -0.25, -0.25]], "effectBurn": [null, "0", "30", "60", "15/25/35/45/55", "25/30/35/40/45", "6", "30", "50", "4", "-0.25"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EvelynnQ.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnW", "name": "Allure", "description": "<PERSON><PERSON> menera<PERSON> curse pada target, menyebabkan serangan atau spell berik<PERSON><PERSON> setelah jeda akan menerapkan charm pada targetnya dan mengurangi magic resist mereka.", "tooltip": "Evelynn menandai champion atau monster selama 5 detik. <PERSON><PERSON> mengenai target dengan Serangan atau Ability, dia akan menghapus tanda, me-refund cost, dan menerapkan <status>Slow</status> pada target sebesar {{ slowamount*100 }}% selama {{ slowduration }} detik.<br /><br /><PERSON>ka tanda bertahan setidaknya 2,5 detik, menghapusnya akan memberikan efek tambahan:<li>Terhadap champion: <status>Charm</status> musuh selama {{ charmduration }} detik dan mengurangi <scaleMR>{{ mrshred*100 }}% Magic Resist</scaleMR> selama {{ shredduration }} detik.<li>Terhadap monster: <status>Charm</status> musuh selama {{ monstercharm }} detik dan menghasilkan <magicDamage>{{ monsterdamagetotaltooltip }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Magic Resist Shred", "Damage Monster", "Cooldown", "Range"], "effect": ["{{ e2 }}-> {{ e2NL }}", "{{ monstercharm }}-> {{ monstercharmNL }}", "{{ effect9amount*100.000000 }}%-> {{ effect9amountnl*100.000000 }}%", "{{ e7 }}-> {{ e7NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ castrange }}-> {{ castrangeNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [1.25, 1.5, 1.75, 2, 2.25], [-0.45, -0.45, -0.45, -0.45, -0.45], [15, 14, 13, 12, 11], [5, 5, 5, 5, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [250, 300, 350, 400, 450], [0.75, 0.75, 0.75, 0.75, 0.75], [0.35, 0.375, 0.4, 0.425, 0.45], [4, 4, 4, 4, 4]], "effectBurn": [null, "2", "1.25/1.5/1.75/2/2.25", "-0.45", "15/14/13/12/11", "5", "1.5", "250/300/350/400/450", "0.75", "0.35/0.375/0.4/0.425/0.45", "4"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1300, 1400, 1500, 1600], "rangeBurn": "1200/1300/1400/1500/1600", "image": {"full": "EvelynnW.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnE", "name": "Whiplash", "description": "Evelynn memecut target<PERSON> dengan <PERSON>, men<PERSON><PERSON><PERSON><PERSON> damage. Dia mendapatkan Move Speed selama beberapa saat.", "tooltip": "<PERSON><PERSON> men<PERSON> musuh, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ basedamage }} plus {{ percenthealthbasetooltip }} magic damage Health maksimum</magicDamage>. <PERSON><PERSON> mendapatkan <speed>{{ speedamount*100 }}% Move Speed</speed> selama {{ speedduration }} detik.<br /><br /><PERSON><PERSON><PERSON> <keywordMajor>Demon Shade</keywordMajor> akan me-refresh cooldown Ability dan memperkuatnya. Saat Ability diperkuat, Evelynn melakukan dash ke target dan mengh<PERSON>lkan <magicDamage>{{ empowereddamage }} plus {{ percenthealthempoweredtooltip }} magic damage Health maksimum</magicDamage> ke target dan siapa pun yang dilewatinya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage yang <PERSON>at", "Move Speed", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ empowered<PERSON><PERSON> }}-> {{ empowereddamageNL }}", "{{ speedamount*100.000000 }}%-> {{ speedamountnl*100.000000 }}%", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.3, 0.35, 0.4, 0.45, 0.5], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [4, 4, 4, 4, 4], [450, 450, 450, 450, 450], [0.8, 0.85, 0.9, 0.95, 1], [2, 2, 2, 2, 2], [1.3, 1.35, 1.4, 1.45, 1.5]], "effectBurn": [null, "0", "0", "0.3/0.35/0.4/0.45/0.5", "2", "3", "4", "450", "0.8/0.85/0.9/0.95/1", "2", "1.3/1.35/1.4/1.45/1.5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [210, 210, 210, 210, 210], "rangeBurn": "210", "image": {"full": "EvelynnE.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnR", "name": "Last Caress", "description": "Evelynn tak bisa ditarget untuk sesaat dan menghancurkan area di depannya sebelum berpindah jauh ke belakang.", "tooltip": "<PERSON>n mele<PERSON>kan energi demonic, men<PERSON><PERSON><PERSON><PERSON> damage luar biasa, menjadi tak bisa ditarget dan teleport ke belakang. <PERSON>a menghasilkan <magicDamage>{{ damage }} magic damage</magicDamage>, meningkat menjadi <magicDamage>{{ critdamage }}</magicDamage> terhadap musuh dengan Health di bawah <healing>30%</healing>. Saat cast, set cooldown Demon Shade ke 1,25 detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage yang <PERSON>at", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ effect1amount*2.400000 }}-> {{ effect1amountnl*2.400000 }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 250, 375], [1.4, 1.4, 1.4], [2.5, 2.5, 2.5], [150, 225, 300], [3, 3, 3], [5, 4, 3], [0.3, 0.3, 0.3], [700, 700, 700], [30, 45, 60], [0, 0, 0]], "effectBurn": [null, "125/250/375", "1.4", "2.5", "150/225/300", "3", "5/4/3", "0.3", "700", "30/45/60", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EvelynnR.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Demon Shade", "description": "<PERSON> luar combat, <PERSON><PERSON> memasuki <PERSON>. Demon Shade member<PERSON><PERSON> heal pada <PERSON> saat health rendah dan member<PERSON><PERSON> setelah level 6.", "image": {"full": "Evelynn_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}