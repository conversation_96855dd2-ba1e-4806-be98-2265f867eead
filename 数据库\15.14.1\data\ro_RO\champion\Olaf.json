{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Olaf": {"id": "<PERSON>", "key": "2", "name": "<PERSON>", "title": "berserker<PERSON>", "image": {"full": "Olaf.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "2000", "num": 0, "name": "default", "chromas": false}, {"id": "2001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "2002", "num": 2, "name": "<PERSON> glacial", "chromas": false}, {"id": "2003", "num": 3, "name": "Brolaf", "chromas": true}, {"id": "2004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "2005", "num": 5, "name": "<PERSON> brigand", "chromas": false}, {"id": "2006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "2015", "num": 15, "name": "Olaf SKT T1", "chromas": false}, {"id": "2016", "num": 16, "name": "<PERSON>, spa<PERSON> dragonilor", "chromas": true}, {"id": "2025", "num": 25, "name": "<PERSON>", "chromas": true}, {"id": "2035", "num": 35, "name": "<PERSON>takill III: Capitol pierdut", "chromas": true}, {"id": "2044", "num": 44, "name": "Olaf infernal", "chromas": true}], "lore": "<PERSON> este o forță distructivă de neoprit, mânuindu-și toporul cu o singură dorință: moartea într-o luptă glorioasă. Provine din peninsula Lokfar din Freljord și, demult, i s-a profețit că va muri paș<PERSON> – o soartă de laș și o mare insultă pentru cineva din poporul lui. Căutând moartea și lăsându-se mânat de furie, s-a dezlănțuit în tot nordul, ucigând nenumărați războinici și multe bestii legendare în încercarea de a găsi un adversar care să-i ofere un sfârșit onorabil. Acum luptă cu îndârjire pentru Gheara iernii și-și caută moartea glorioasă în marile războaie ce vor urma.", "blurb": "Olaf este o forță distructivă de neoprit, mânuindu-și toporul cu o singură dorință: moartea într-o luptă glorioasă. Provine din peninsula Lokfar din Freljord și, demult, i s-a profețit că va muri paș<PERSON> – o soartă de laș și o mare insultă pentru cineva...", "allytips": ["Da<PERSON>ă Olaf combină ''Berserkerul turbat'', ''Loviturile feroce'' și ''Ragnarok'' când are viața scăzută, va deveni surprinzător de puternic.", "Vindecarea bonus acordată de ''Loviturile feroce'' îți amplifică furtul de viață din toate sursele și vindecarea de la aliați."], "enemytips": ["Olaf devine și mai periculos când viața îi scade. Păstrează-ți abilitățile de neutralizare pentru a-i da lovitura de grație.", "Dacă nu îl lași pe Olaf să ajungă la securea sa, vei evita o mare parte din hărțuirea de pe culoar.", "Olaf are o defensivă redusă din cauza daunelor provocate de ''Ragnarok'', în ciuda faptului că este imun la neutralizări. Dacă nu poți scăpa de Olaf în timp ce acesta folosește ''Ragnarok'', încearcă să-ți concentrezi daunele asupra lui împreună cu coechipierii tăi."], "tags": ["Fighter", "Tank"], "partype": "Mană", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 119, "mp": 316, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.7, "attackspeedperlevel": 2.7, "attackspeed": 0.694}, "spells": [{"id": "OlafAxeThrowCast", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON> a<PERSON> o secure în locația-țintă, care le provoacă daune inamicilor prin care trece și le reduce armura și viteza de mișcare. Dac<PERSON> Olaf ridică securea, timpul de reactivare al abilității este resetat.", "tooltip": "<PERSON> a<PERSON> o secure, provoc<PERSON><PERSON><PERSON>le inamicilor <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> și <status>încetinindu-i</status> cu {{ slowamount*100 }}% timp de {{ e3 }} secunde (în funcție de distanța parcursă). Campionii loviți pierd <scaleArmor>{{ shredamount*100 }}% armură</scaleArmor> timp de {{ debuffduration }} secunde.<br /><br />Dac<PERSON> Olaf ridică securea, timpul de reactivare al acestei abilități este redus la {{ tooltipcdrefund }} secunde sau resetat complet dacă au trecut {{ tooltipcdrefund }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Daune împotriva <PERSON>ș<PERSON>lor", "Cost de man<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ monsterdamage }} -> {{ monsterdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [30, 30, 30, 30, 30], [2.5, 2.5, 2.5, 2.5, 2.5], [1.5, 1.5, 1.5, 1.5, 1.5], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "30", "2.5", "1.5", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "OlafAxeThrowCast.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OlafFrenziedStrikes", "name": "Îmb<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> mărește viteza de atac și primește un scut.", "tooltip": "<PERSON> <attackSpeed>{{ attackspeed*100 }}% vitez<PERSON> de atac</attackSpeed> timp de {{ duration }} secunde și un <shield>scut în valoare de {{ baseshield }} plus {{ shieldpercmissinghp*100 }}% din viața lipsă (până la cel mult {{ maxshieldcalc }} când are sub {{ thresholdformax*100 }}% viaț<PERSON>)</shield> timp de {{ shieldduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Viteză de atac", "Scut de bază", "Timp de reactivare"], "effect": ["{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "OlafFrenziedStrikes.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OlafRecklessStrike", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> atacă cu atâta forță încât provoacă daune reale țintei și lui îns<PERSON>ș<PERSON>, recuperându-și viața consumată dacă distruge ținta.", "tooltip": "<PERSON> rotește fioros securile, provocând <trueDamage>{{ totaldamage }} daune reale</trueDamage>. Dac<PERSON> inamicul moare, costul este rambursat.<br /><br />Atacurile reduc timpul de reactivare al acestei abilități cu 1 secundă. Valoarea crește la 2 secunde pentru atacurile împotriva monștrilor.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "30", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Costă  viață", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "OlafRecklessStrike.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Costă  viață"}, {"id": "Olaf<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "Olaf primește armură și rezistență la magie în mod pasiv. Poate activa această abilitate pentru a deveni imun la neutralizări atât timp cât continuă să atace.", "tooltip": "<spellPassive><PERSON><PERSON><PERSON><PERSON>:</spellPassive> <PERSON> <scaleArmor>{{ resists }} arm<PERSON><PERSON></scaleArmor> și <scaleMR>{{ resists }} rezistență la magie</scaleMR>.<br /><br /><spellActive>Activă: </spellActive><PERSON> se purifică de toate efectele de <status>imobilizare</status> și <status>neutralizare</status> și devine imun la ele timp de {{ duration }} secunde. Pe durata abilității, <PERSON> primeș<PERSON> <scaleAD>{{ ad }} daune din atac</scaleAD>. Dacă lovește un campion cu un atac sau cu <spellName>''Avântul nesăbuit''</spellName>, prelungește durata cu {{ durationextension }} secunde.<br /><br />În plus, <PERSON> primeș<PERSON> <speed>{{ haste*100 }}% vitez<PERSON> de mișcare</speed> spre campionii inamici timp de {{ hasteduration }} secundă.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Armură și rezistență la magie", "Daune din atac", "Viteză de mișcare", "Timp de reactivare"], "effect": ["{{ resists }} -> {{ resistsNL }}", "{{ flatad }} -> {{ flatadNL }}", "{{ haste*100.000000 }} -> {{ hastenl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "OlafRagnarok.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Berserker turbat", "description": "Olaf primește viteză de atac și furt de viață, în funcție de viața lipsă.", "image": {"full": "Olaf_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}