{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Janna": {"id": "<PERSON><PERSON>", "key": "40", "name": "잔나", "title": "폭풍의 분노", "image": {"full": "Janna.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "40000", "num": 0, "name": "default", "chromas": false}, {"id": "40001", "num": 1, "name": "폭풍의 잔나", "chromas": false}, {"id": "40002", "num": 2, "name": "마법공학 잔나", "chromas": false}, {"id": "40003", "num": 3, "name": "서리 여왕 잔나", "chromas": false}, {"id": "40004", "num": 4, "name": "승리의 잔나", "chromas": false}, {"id": "40005", "num": 5, "name": "기상캐스터 잔나", "chromas": false}, {"id": "40006", "num": 6, "name": "Fnatic 잔나", "chromas": false}, {"id": "40007", "num": 7, "name": "별 수호자 잔나", "chromas": false}, {"id": "40008", "num": 8, "name": "불멸의 영웅 잔나", "chromas": true}, {"id": "40013", "num": 13, "name": "마녀 잔나", "chromas": true}, {"id": "40020", "num": 20, "name": "사막의 수호자 잔나", "chromas": true}, {"id": "40027", "num": 27, "name": "전투 여왕 잔나", "chromas": true}, {"id": "40036", "num": 36, "name": "수정 장미 잔나", "chromas": true}, {"id": "40045", "num": 45, "name": "사이버 헤일로 잔나", "chromas": true}, {"id": "40046", "num": 46, "name": "프레스티지 사이버 헤일로 잔나", "chromas": false}, {"id": "40056", "num": 56, "name": "천상비늘 잔나", "chromas": true}, {"id": "40066", "num": 66, "name": "빛의 인도자 잔나", "chromas": false}], "lore": "룬테라의 돌풍이라는 강력한 무기를 지닌, 그러나 모든 것이 베일에 싸인 잔나. 그녀는 이제 폐허가 되어버린 자운을 지키는 바람의 정령이다. 그런 그녀를 두고 떠도는 이야기는 많다. 그중에서 가장 그럴듯한 설은 바로 수호신으로서의 잔나. 악천후 속에서 폭풍우를 뚫고 나아가며 부디 순항할 수 있기를 기원하는 룬테라 선원들의 간구 속에서 등장했다고 여기는 것이다. 사람들은 잔나가 남다른 애정으로 자운의 선원들을 보호한다고 믿었다. 잔나와 자운, 이 둘의 관계가 결코 뗄 수 없을 정도로 가까워진 데에는 바로 이러한 사연이 있다고 생각했다. 그 결과 잔나는 도움의 손길이 필요한 모든 자운 사람들에게 있어 마치 등대와 같은 역할을 하게 되었다. 그런 그녀가 언제 어디서 나타날지는 아무도 모른다. 하지만 도움이 필요한 순간에는 어김없이 나타나 구원의 손길을 내밀었다.", "blurb": "룬테라의 돌풍이라는 강력한 무기를 지닌, 그러나 모든 것이 베일에 싸인 잔나. 그녀는 이제 폐허가 되어버린 자운을 지키는 바람의 정령이다. 그런 그녀를 두고 떠도는 이야기는 많다. 그중에서 가장 그럴듯한 설은 바로 수호신으로서의 잔나. 악천후 속에서 폭풍우를 뚫고 나아가며 부디 순항할 수 있기를 기원하는 룬테라 선원들의 간구 속에서 등장했다고 여기는 것이다. 사람들은 잔나가 남다른 애정으로 자운의 선원들을 보호한다고 믿었다. 잔나와 자운, 이 둘의...", "allytips": ["폭풍의 눈은 아군 포탑에 사용할 수 있습니다.", "울부짖는 돌풍을 충전 없이 빠르게 발사하여 적을 방해할 수 있습니다.", "잔나의 궁극기를 시기 적절하게 사용하여 적들을 갈라놓거나 부상 당한 아군에게서 적을 멀리 날려버릴 수 있습니다."], "enemytips": ["잔나가 궁극기를 사용할 때까지 방해 효과가 있는 스킬을 아껴두십시오.", "잔나가 보이지 않는 곳에서 울부짖는 돌풍으로 공격할 수도 있으므로 효과음에 집중하십시오.", "잔나는 아군에게 이로운 효과를 걸어주고 있을 때 가장 강력합니다. 잔나의 아군을 공격하면 잔나의 위력 또한 감소합니다."], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 50, "movespeed": 325, "armor": 28, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON>G<PERSON>", "name": "울부짖는 돌풍", "description": "잔나가 기압과 온도를 국지적으로 변경하여 시간이 지나면 점점 커지는 작은 폭풍을 생성합니다. 스킬을 다시 사용하면 폭풍을 날려보낼 수 있습니다. 폭풍은 스킬을 사용한 방향으로 날아가며, 경로에 있는 적들에게 피해를 입히고 공중으로 띄워 올립니다.", "tooltip": "잔나가 {{ maxduration }}초에 걸쳐 점점 세진 후 경로를 따라 이동하는 회오리바람을 소환합니다. 회오리바람은 적중 시 <magicDamage>{{ minimumdamage }}~{{ maxdamage }}의 마법 피해</magicDamage>를 입히고 {{ baseknockup }}~{{ maxknockup }}초 동안 <status>공중으로 띄워 올립니다</status>. 거리, 피해량, <status>띄워 올리기</status> 지속시간은 회오리바람이 커진 정도에 비례해 증가합니다. <recast>재사용</recast>하면 회오리바람이 더 일찍 날아갑니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "충전 시간당 피해 증가량", "소모값 @AbilityResourceName@"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "HowlingGale.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SowTheWind", "name": "서풍", "description": "잔나가 기본 지속 효과로 정령을 소환하여 이동 속도를 높이고 다른 유닛 사이로 지나갈 수 있게 됩니다. 스킬을 활성화할 경우 적에게 피해를 입히고 이동 속도를 늦출 수 있습니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 잔나의 <speed>이동 속도가 {{ totalms }}</speed> 증가하며 유체화 상태가 됩니다.<br /><br /><spellActive>사용 시:</spellActive> 잔나의 원소가 적을 공격하여 {{ slowduration }}초 동안 {{ totalslow }} <status>둔화</status>시키고 <magicDamage>{{ totaldamage }}+{{ spell.tailwindself:bonusdamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "기본 지속 효과 이동 속도", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ mspercent*100.000000 }}% -> {{ mspercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [4294967295, 4294967295, 4294967295, 4294967295, 4294967295], "rangeBurn": "4294967295", "image": {"full": "SowTheWind.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "EyeOfTheStorm", "name": "폭풍의 눈", "description": "잔나가 방어용 광풍을 만들어 아군 챔피언이나 포탑을 공격에서 보호하고 공격력을 높여줍니다.", "tooltip": "잔나가 {{ shieldduration }}초 동안 아군 챔피언이나 포탑에 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 부여합니다. 대상은 보호막이 지속되는 동안 <scaleAD>{{ totalad }}의 공격력</scaleAD>을 얻습니다.<br /><br />잔나가 스킬로 적 챔피언의 이동을 방해할 때마다 재사용 대기시간의 {{ ecdrefundforcc*100 }}%를 돌려받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "공격력", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ bonusad }} -> {{ bonusadNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EyeOfTheStorm.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ReapTheWhirlwind", "name": "계절풍", "description": "잔나가 자신을 마법의 폭풍으로 둘러싸며 적을 뒤로 밀어냅니다. 폭풍이 잦아들면 능력의 효과가 끝날 때까지 부드러운 바람이 아군을 치료합니다.", "tooltip": "잔나가 마법의 계절풍을 소환하여 주변 적들을 <status>뒤로 밀어낸</status> 후 {{ e3 }}초에 걸쳐 주변 아군의 <healing>체력을 {{ totalheal }}</healing>만큼 회복시킵니다. 이동하거나 스킬을 사용하면 계절풍이 일찍 사라집니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["초당 체력 회복량", "재사용 대기시간"], "effect": ["{{ healbasepersecond }} -> {{ healbasepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [300, 450, 600], [3, 3, 3], [700, 700, 700], [875, 875, 875], [875, 875, 875], [1200, 1200, 1200], [10, 10, 10], [0.5, 0.5, 0.5], [0, 0, 0]], "effectBurn": [null, "0", "300/450/600", "3", "700", "875", "875", "1200", "10", "0.5", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [725, 725, 725], "rangeBurn": "725", "image": {"full": "ReapTheWhirlwind.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "순풍", "description": "아군이 잔나를 향해 움직일 때 이동 속도가 증가합니다.<br><br>잔나가 적중 시 및 서풍으로 추가 이동 속도의 일정 비율만큼 추가 마법 피해를 입힙니다.", "image": {"full": "Janna_Tailwind.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}