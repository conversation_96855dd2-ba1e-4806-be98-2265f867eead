{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Bard", "title": "il custode errante", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "Bard del Bosco Antico", "chromas": false}, {"id": "432005", "num": 5, "name": "Bard della Nevicata", "chromas": true}, {"id": "432006", "num": 6, "name": "Bar<PERSON>", "chromas": false}, {"id": "432008", "num": 8, "name": "Bar<PERSON> Astronaut<PERSON>", "chromas": true}, {"id": "432017", "num": 17, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "432026", "num": 26, "name": "Bard <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "432035", "num": 35, "name": "Bard T1", "chromas": true}, {"id": "432037", "num": 37, "name": "Bard <PERSON> spirituale", "chromas": true}], "lore": "Bard è un viaggiatore stellare, un agente della serendipità che lotta per mantenere l'equilibrio dove la vita può sostenere l'indifferenza del caos. Molti runeterrani cantano canzoni che si interrogano sulla sua natura straordinaria, ma sono tutti d'accordo sul fatto che il vagabondo cosmico sia attratto dagli artefatti dal grande potere magico. Circondato da un coro giubilante di spiriti Mippi, agisce in un modo che non può mai essere interpretato come maligno, perché Bard si muove sempre per un bene più grande... a modo suo.", "blurb": "Bard è un viaggiatore stellare, un agente della serendipità che lotta per mantenere l'equilibrio dove la vita può sostenere l'indifferenza del caos. Molti runeterrani cantano canzoni che si interrogano sulla sua natura straordinaria, ma sono tutti...", "allytips": ["È importante raccogliere campanelle per migliorare gli attacchi dei Mippi, ma non dimenticate il vostro compagno in corsia! Cercate di entrare con stile, portando un alleato in corsia con Viaggio magico.", "Fate caricare gli Altari del custode, perché quando sono a piena potenza curano molto di più.", "Non dimenticate che i nemici possono usare anche loro i portali di Viaggio magico, e che la suprema può colpire anche i vostri alleati!"], "enemytips": ["Anche i nemici di Bard possono usare i portali di Viaggio magico. Se credete che sia sicuro, seguitelo pure.", "È possibile distruggere gli Altari curativi di Bard camminandoci sopra. Non lasciate che i suoi alleati li sfruttino senza combattere.", "La suprema di Bard, <PERSON><PERSON> immobile, ha effetto su alleati, ne<PERSON>i, mostri e torri. A volte può valere la pena di lanciarsi nell'area d'effetto!"], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "Le<PERSON>o", "description": "Bard spara un proiettile che rallenta il primo nemico colpito, per poi proseguire. Se colpisce un muro, stordisce il primo bersaglio. Se colpisce un altro nemico, stordisce entrambi i bersagli.", "tooltip": "Bard scaglia una dardo di energia, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai primi due nemici colpiti. Il primo bersaglio colpito viene <status>rallentato</status> di un {{ slowamountpercentage }}% per {{ slowduration }} secondo/i.<br /><br />Se il dardo colpisce un altro nemico o un muro, i nemici colpiti vengono <status>storditi</status> per {{ stunduration }} secondo/i.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> rallenta<PERSON>", "<PERSON><PERSON> stordimento:", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Altare del custode", "description": "Rivela un altare della salute che si potenzia in un breve periodo, per poi sparire dopo aver curato e accelerato il primo alleato che lo tocca.", "tooltip": "Bard crea un altare della salute che fornisce <speed>{{ calc_movespeed }} velocità di movimento</speed> che decresce nell'arco di {{ movespeed_duration }} secondi e ripristina almeno <healing>{{ initialheal }} salute</healing> al primo alleato che entra nell'area. L'altare cresce e ripristina <healing>{{ maxheal }} salute</healing> dopo essere stato presente per {{ chargeuptime }} secondi.<br /><br />Bard può avere al massimo {{ maxpacks }} altari attivi contemporaneamente. Se un campione nemico accede all'altare, questo viene distrutto.<br /><br />Questa abilità ha {{ ammo_limit }} cariche.<br /><br />Altari attualmente attivi: {{ f1 }} / {{ f2 }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione base", "Guarigione massima", "Velocità di movimento"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}% -> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Viaggio magico", "description": "Bard apre un portale nel terreno vicino. Gli alleati e i nemici possono attraversarlo in un viaggio a senso unico oltre il terreno.", "tooltip": "Bard apre un portale unidirezionale nel terreno per {{ e1 }} secondi. Qualsiasi campione può attraversare il portale avvicinandosi e spostandosi su di esso.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "Destino immobile", "description": "Bard lancia una scarica di energia spirituale nella posizione bersaglio, facendo entrare tutte le unità e le torri in stasi per un breve periodo.", "tooltip": "Bard diffonde un'energia magica protettiva in un'area, mandando tutte le unità e le strutture colpite in Stasi per {{ rstasisduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Chiamata del viandante", "description": "<font color='#FF9900'>Mippi:</font> Bard attira degli spiriti minori che lo aiutano negli attacchi base infliggendo danni magici extra. Quando Bard raccoglie abbastanza <font color='#cccc00'>campanelle</font>, i suoi Mippi infliggono anche danni ad area rallentando i nemici colpiti.<br><br><font color='#FF9900'>Campanelle:</font> Bard può raccogliere le <font color='#cccc00'>campanelle</font> che appaiono casualmente sulla mappa. Le campanelle conferiscono esperienza, ripristinano mana e forniscono velocità di movimento fuori dai combattimenti.", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}