{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pantheon": {"id": "Pantheon", "key": "80", "name": "Pantheon", "title": "der unbeugs<PERSON>", "image": {"full": "Pantheon.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "80000", "num": 0, "name": "default", "chromas": false}, {"id": "80001", "num": 1, "name": "Myrmidonen-Pantheon", "chromas": false}, {"id": "80002", "num": 2, "name": "Unbarmherziger Pantheon", "chromas": false}, {"id": "80003", "num": 3, "name": "Perseus-Pantheon", "chromas": false}, {"id": "80004", "num": 4, "name": "Fullmetal-Pantheon", "chromas": false}, {"id": "80005", "num": 5, "name": "Glefen-Pantheon", "chromas": false}, {"id": "80006", "num": 6, "name": "Drachentöter-Pantheon", "chromas": true}, {"id": "80007", "num": 7, "name": "Zombieschlächter-Pantheon", "chromas": false}, {"id": "80008", "num": 8, "name": "Bäcker-Pantheon", "chromas": false}, {"id": "80016", "num": 16, "name": "Pulsfeuer-Pantheon", "chromas": true}, {"id": "80025", "num": 25, "name": "Schwarznebel-Pantheon", "chromas": true}, {"id": "80026", "num": 26, "name": "Aufgestiegener Pantheon (Prestige)", "chromas": false}, {"id": "80036", "num": 36, "name": "Aschfahler Eroberer Pantheon", "chromas": true}, {"id": "80038", "num": 38, "name": "Auserwählter des Wolfs-Pantheon", "chromas": true}], "lore": "Atreus' Körper diente einst gegen seinen Willen dem Aspekt des Krieges und überlebte, als die göttliche Macht in seinem Inneren niedergestreckt wurde. Er weigerte sich schlichtweg, einem Hieb nachzugeben, der Sterne vom Himmel fegte. Mit der Zeit lernte er, die Kraft seiner eigenen Sterblichkeit und die störrische Widerstandskraft anzunehmen, die damit einhergeht. Jetzt stellt sich Atreus als neugeborener Pantheon gegen alles Göttliche und sein unbeugsamer Wille fließt auf dem Schlachtfeld durch die Waffen des gefallenen Aspektes.", "blurb": "Atreus' Körper diente einst gegen seinen Willen dem Aspekt des Krieges und überlebte, als die göttliche Macht in seinem Inneren niedergestreckt wurde. Er weigerte sich schlichtweg, einem Hieb nachzugeben, der Sterne vom Himmel fegte. Mit der Zeit lernte...", "allytips": ["„Sterblicher Wille“ wird nach 5&nbsp;Fähigkeiten oder normalen Angriffen aktiviert&nbsp;– plane deine Kämpfe so, dass du ihn mehrmals auslöst.", "Mach die Gegner mit „Kometenspeeren“ mürbe, bevor du ins Getümmel springst.", "Wenn ein Gegner außer Reichweite des „Ansturms der Ägide“ gelangt, kannst du die Fähigkeit reaktivieren, um die Schildramme sofort einzusetzen."], "enemytips": ["Pantheons „Ansturm der Ägide“ macht ihn gegen frontalen Schaden immun. <PERSON><PERSON><PERSON>, hinter ihn zu gelangen, oder warte ab.", "Sei in der Nähe von Pantheon vorsichtig, wenn dein Leben niedrig ist&nbsp;– der Wurf mit dem „Kometenspeer“ exekutiert Gegner mit niedrigem Leben.", "„Astraler Kollisionskurs“ wird mehrere Sekunden lang angekündigt, bevor Pantheon aufschlägt. Mach dich vom Acker oder überlege dir, wie du mit ihm fertigwirst."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 109, "mp": 317, "mpperlevel": 31, "movespeed": 345, "armor": 40, "armorperlevel": 4.95, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6, "hpregenperlevel": 0.65, "mpregen": 7.35, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.95, "attackspeed": 0.658}, "spells": [{"id": "PantheonQ", "name": "Kometenspeer", "description": "Pantheon stößt oder wirft seinen Speer in die gewählte Richtung.", "tooltip": "<span class=\"colorFF8C00\">Antippen:</span> Pantheon stößt mit seinem Speer zu und fügt getroffenen Gegnern <physicalDamage>{{ tapdamagecalc }}&nbsp;normalen Schaden</physicalDamage> zu. Erstattet {{ tapcooldownrefund*100 }}&nbsp;% der Abklingzeit dieser Fähigkeit zurück.<br /><br /><span class=\"colorFF8C00\">Halten:</span> Pantheon schleudert seinen Speer und fügt dem ersten getroffenen Gegner <physicalDamage>{{ holddamagecalc }}&nbsp;normalen Schaden</physicalDamage> zu. Weitere Ziele erleiden {{ damagefalloff*100 }}&nbsp;% weniger Schaden. <br /><br />Diese Fähigkeit ist gegen Gegner mit weniger als {{ crithealththreshold*100 }}&nbsp;% Leben verstärkt und verursacht stattdessen <physicalDamage>{{ executedamagecalcmodified }}&nbsp;normalen Schaden</physicalDamage>.<br /><br /><span class=\"colorEDDA74\">Bonus durch „Sterblicher Wille“:</span> Verursacht zusätzlich <physicalDamage>{{ empowereddamagecalc }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Grundwert für Exekutionsschaden", "Abklingzeit"], "effect": ["{{ tapdamage }} -> {{ tapdamageNL }}", "{{ executebasedamage }} -> {{ executebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.25, 9.5, 8.75, 8], "cooldownBurn": "11/10.25/9.5/8.75/8", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "PantheonQ.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PantheonW", "name": "Schildsprung", "description": "Pantheon springt zu einem Ziel, fügt ihm Schaden zu und betäubt es.", "tooltip": "Pantheon springt auf sein <PERSON>, <status>betäubt</status> es {{ stunduration }}&nbsp;Sekunde lang und fügt ihm <physicalDamage>normalen Schaden in Höhe von {{ maxhealthdamagecalc }} des maximalen Lebens</physicalDamage> zu.<br /><br /><keywordMajor>Bonus durch „Sterblicher Wille“:</keywordMajor> Pantheons nächster Angriff trifft {{ empowerednumhits }}-mal und verursacht insgesamt <physicalDamage>{{ empowereddamagemultcalcmodified }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> %", "Abklingzeit"], "effect": ["{{ maxhealthdamage*100.000000 }}&nbsp;% -> {{ maxhealthdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PantheonW.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PantheonE", "name": "Ansturm der Ägide", "description": "Pantheon rammt seinen Schild in den Boden, wird gegen frontalen Schaden immun und stößt wiederholt mit seinem Speer zu.", "tooltip": "Pantheon erhebt seinen Schild und nimmt es {{ shieldduration }}&nbsp;Sekunden lang mit Gegnern aus einer gewählten Richtung auf. Dabei wird er gegen Schaden aus dieser Richtung (ausgenommen Turmschaden) immun und verursacht im Laufe der Dauer <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage>. Pantheon kanalisiert und führt dann eine Schildramme aus, die <physicalDamage>{{ shielddamagecalc }}&nbsp;normalen Schaden</physicalDamage> verursacht.<br /><br /><span class=\"colorEDDA74\">Bonus durch „Sterblicher Wille“:</span> Wenn Pantheon eine Schildramme ausführt, erhält er {{ resistsduration }}&nbsp;Sekunden lang <scaleArmor>{{ resistscalc }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ resistscalc }}&nbsp;Magieresistenz</scaleMR> und {{ speedduration }}&nbsp;Sekunden lang <speed>{{ speedamount*100 }}&nbsp;% Lauftempo</speed>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Schaden durch Schildsprung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldbasedamage }} -> {{ shieldbasedamageNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PantheonE.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PantheonR", "name": "<PERSON><PERSON><PERSON>", "description": "Pantheon geht in sich, springt in die Luft und landet als Komet am gewählten Zielort.", "tooltip": "<spellPassive>Passiv:</spellPassive> Pantheon erhält {{ armorpenetration*100 }}&nbsp;% Rüstungsdurchdringung.<br /><br /><spellActive>Aktiv:</spellActive> Pantheon sammelt seine Kräfte und springt hoch in die Luft. Er wirft seinen Speer von oben her<PERSON>, wodurch er in einem kleinen Bereich <physicalDamage>{{ spell.pantheonq:holddamagecalc }}&nbsp;normalen Schaden</physicalDamage> verursacht und Gegner {{ spearslowduration }}&nbsp;Sekunden lang um {{ spearslow*100 }}&nbsp;% <status>verlangsamt</status>. <br /><br />Dann schlägt Pantheon als Meteor im Zielbereich auf. Er fügt <PERSON>egnern in einer Reihe bis zu <magicDamage>{{ damagecalc }}&nbsp;magischen Schaden</magicDamage> zu (am Rand des Bereichs um bis zu {{ edgedamagereduction*100 }}&nbsp;% verringert).<br /><br />Nach dieser Fähigkeit ist <span class=\"colorEDDA74\">Sterblicher Wille</span> sofort bereit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Rüstungsdurchdringung"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ armorpenetration*100.000000 }}&nbsp;% -> {{ armorpenetrationnl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [180, 165, 150], "cooldownBurn": "180/165/150", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "PantheonR.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Will<PERSON>", "description": "Alle paar Fähigkeiten oder Angriffe wird Pantheons nächste Fähigkeit verstärkt.", "image": {"full": "Pantheon_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}