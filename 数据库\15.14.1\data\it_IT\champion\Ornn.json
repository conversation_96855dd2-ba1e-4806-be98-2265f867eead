{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ornn": {"id": "<PERSON><PERSON>", "key": "516", "name": "<PERSON><PERSON>", "title": "il fuoco della montagna", "image": {"full": "Ornn.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "516000", "num": 0, "name": "default", "chromas": false}, {"id": "516001", "num": 1, "name": "Ornn Signore <PERSON>ono", "chromas": false}, {"id": "516002", "num": 2, "name": "Ornn del Bosco Antico", "chromas": true}, {"id": "516011", "num": 11, "name": "<PERSON><PERSON> Rit<PERSON>", "chromas": true}, {"id": "516020", "num": 20, "name": "<PERSON><PERSON>-ciuf", "chromas": true}], "lore": "Ornn è lo spirito freljordiano della metallurgia e dell'artigianato. Lavora nella solitudine di una gigantesca fucina, ricavata dalle caverne laviche sotto il vulcano <PERSON>. È da lì che alimenta roventi calderoni di roccia fusa, raffinando i metalli e costruendo oggetti di qualità insuperabile. Quando le altre divinità, in particolare Volibear, scendono sulla terra a impicciarsi negli affari dei mortali, Ornn sorge per rimettere al loro posto quegli esseri impetuosi, con il suo fidato martello o con il potere delle montagne.", "blurb": "Ornn è lo spirito freljordiano della metallurgia e dell'artigianato. Lavora nella solitudine di una gigantesca fucina, ricavata dalle caverne laviche sotto il vulcano <PERSON>. È da lì che alimenta roventi calderoni di roccia fusa, raffinando i metalli e...", "allytips": ["Imparare le configurazioni e le ricette degli oggetti può aiutarti a scegliere velocemente i potenziamenti in corsia.", "Fenditura vulcanica può essere usata per creare zone di pericolo per i nemici.", "L'ordine delle abilità è importante! Cerca di ottimizzare l'uso di Fragilità."], "enemytips": ["Cerca di stare lontano dai muri. Ornn è molto meno forte se non può stordirti.", "Attacca Ornn per impedirgli di creare oggetti in corsia."], "tags": ["Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 660, "hpperlevel": 109, "mp": 341, "mpperlevel": 65, "movespeed": 335, "armor": 33, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "OrnnQ", "name": "Fenditura vulcanica", "description": "Ornn colpisce a terra, creando una spaccatura che infligge danni fisici e rallenta i nemici colpiti. Dopo un breve ritardo, nella posizione bersaglio si forma una colonna di magma.", "tooltip": "Ornn colpisce il terreno, creando una fenditura che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e <status>rallenta</status> del {{ e5 }}% per {{ e6 }} secondi. Al termine della fenditura si forma un pilastro di roccia per {{ e3 }} secondi. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [20, 45, 70, 95, 120], [1, 1, 1, 1, 1], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/45/70/95/120", "1", "4", "0", "40", "2", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "OrnnQ.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrnnW", "name": "So<PERSON><PERSON> del mantice", "description": "<PERSON><PERSON> a<PERSON>, sputando fuoco. I nemici colpiti dall'ultimo soffio di fiamme diventano Fragili.", "tooltip": "Ornn diventa inarrestabile e carica in avanti sputando fuoco, infliggendo <magicDamage>{{ maxpercenthpperticktooltip }}% della salute massima in danni magici</magicDamage> nell'arco di {{ breathduration }} secondi. I nemici colpiti dall'ultimo soffio di fiamme diventano <keywordMajor>Fragili</keywordMajor> per {{ brittleduration }} secondi.<br /><br />G<PERSON> effetti di <status>immobilizzazione</status> aumentano la durata del 30% contro i bersagli <keywordMajor>Fragili</keywordMajor> e gli infliggono <magicDamage>{{ brittlepercentmaxhpcalc }} della salute massima in danni magici</magicDamage> aggiuntivi. Gli attacchi di Ornn contro i bersagli <keywordMajor>Fragili</keywordMajor> li <status>respingono</status>, infliggendo loro i danni aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni % salute", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ maxpercenthp<PERSON>icktooltip }}% -> {{ maxpercenthpperticktooltipNL }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "OrnnW.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrnnE", "name": "Carica ardente", "description": "Ornn carica, infliggendo danni ai nemici che travolge. Se Ornn colpisce un terreno durante la carica, genera un'onda d'urto che danneggia e lancia in aria i nemici intorno a lui.", "tooltip": "Ornn parte alla carica, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>. Se Ornn si scontra con un muro, l'onda d'urto che ne consegue <status>lancia in aria</status> i nemici per {{ knockupduration }} secondi e applica gli stessi danni a chi non è stato colpito dalla carica.<br /><br />La carica di Ornn distrugge i pilastri di magma e i muri creati dai nemici.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> scatto", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "OrnnE.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrnnR", "name": "Richiamo del dio della forgia", "description": "Ornn evoca un possente elementale che si muove verso di lui con velocità crescente. I nemici travolti dall'elementale subiscono danni, vengono rallentati e diventano Fragili. Ornn può lanciare nuovamente l'abilità per caricare verso l'elementale, deviandolo nella direzione in cui lo colpisce, facendo in modo che i nemici colpiti dall'elementale vengano lanciati in aria, e che infligga gli stessi danni e riapplichi Fragilità.", "tooltip": "Ornn evoca un possente elementale di lava che corre verso di lui, infliggendo <magicDamage>{{ rdamagecalc }} danni magici</magicDamage>, applicando <keywordMajor>Fragilità</keywordMajor> ai nemici colpiti e <status>rallentandoli</status> di un {{ rslowpercentbasepremath }}% per {{ brittledurationtooltiponly }} secondi.<br /><br />Ornn può <recast>rilanciare</recast> questa abilità per scattare e tirare una testata. Se con lo scatto colpisce l'elementale, gli fa cambiare direzione e lo potenzia, facendogli <status>lanciare in aria</status> il primo campione colpito per {{ rstunduration }} secondo e i successivi campioni per {{ minstun }} secondi. L'elementale infligge anche <magicDamage>{{ rdamagecalc }} danni magici</magicDamage> e applica nuovamente <keywordMajor>Fragilità</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rslowpercentbasepremath }} -> {{ rslowpercentbasepremathNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "OrnnR.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Forgia incarnata", "description": "Ornn ottiene un bonus aggiuntivo di armatura e resistenza magica bonus da tutte le fonti.<br><br>Ornn può investire oro per forgiare oggetti non consumabili in qualunque punto.<br><br><PERSON><PERSON><PERSON>, può creare oggetti capolavoro per se stesso e per i suoi alleati.", "image": {"full": "OrnnP.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}