{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zeri": {"id": "<PERSON><PERSON>", "key": "221", "name": "ゼリ", "title": "ゾウンの火花", "image": {"full": "Zeri.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "221000", "num": 0, "name": "default", "chromas": false}, {"id": "221001", "num": 1, "name": "枯朽の薔薇ゼリ", "chromas": true}, {"id": "221010", "num": 10, "name": "オーシャンソング ゼリ", "chromas": true}, {"id": "221019", "num": 19, "name": "不滅の旅路ゼリ", "chromas": true}, {"id": "221028", "num": 28, "name": "恐怖の夜ゼリ", "chromas": true}, {"id": "221029", "num": 29, "name": "プレステージ恐怖の夜ゼリ", "chromas": false}], "lore": "ゾウンの労働階級に生まれたゼリは、頑固で活発な若者だ。彼女は電気の魔力を操り、自らと特製のライフルに電力を注ぎこんでいる。ゼリの不安定な力は彼女の感情を反映しており、飛び散る火花は命を救うために電光石火で飛び回る彼女のアプローチそのものだ。他者に対して深い思いやりを持つゼリは、いつも家族と故郷への愛を胸に、戦いに臨む。助けようとする熱意が裏目に出ることもあるが、ゼリは一つの真実を確信している。仲間のために立ち上がれば、彼らも共に立ち上がってくれるということを。", "blurb": "ゾウンの労働階級に生まれたゼリは、頑固で活発な若者だ。彼女は電気の魔力を操り、自らと特製のライフルに電力を注ぎこんでいる。ゼリの不安定な力は彼女の感情を反映しており、飛び散る火花は命を救うために電光石火で飛び回る彼女のアプローチそのものだ。他者に対して深い思いやりを持つゼリは、いつも家族と故郷への愛を胸に、戦いに臨む。助けようとする熱意が裏目に出ることもあるが、ゼリは一つの真実を確信している。仲間のために立ち上がれば、彼らも共に立ち上がってくれるということを。", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "マナ", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 250, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "ZeriQ", "name": "バーストファイア", "description": "「バーストファイア」は7発の弾をバースト射撃し、最初に命中した敵に攻撃力ダメージを与える。このスキルは通常攻撃として扱われる。", "tooltip": "{{ numberofmissiles }}発の弾をバースト射撃して、最初に命中した敵に<physicalDamage>{{ activedamagethatcancrit }}の物理ダメージ</physicalDamage>を与える。このスキルは通常攻撃として扱われる。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "合計攻撃力反映率"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ activeadratio*100.000000 }}% -> {{ activeadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "ZeriQ.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "ZeriW", "name": "ウルトラショックレーザー", "description": "電磁パルスを発射して、最初に命中した敵にスロウ効果とダメージを与える。パルスが壁に当たると、当たった位置から幅が広く、射程の長いレーザーを発射する。", "tooltip": "電磁パルスを発射して最初に命中した敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間、{{ slowpercent*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />パルスが壁に当たると、当たった位置から幅の広いレーザーを発射し、範囲内の敵に上記の効果を適用する。また、チャンピオンとモンスターに対しては、クリティカルを発生させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "ZeriW.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ZeriE", "name": "スパークサージ", "description": "短い距離をダッシュし、次に行う3回の「バーストファイア」が強化されて敵を貫通するようになる。地形に触れた場合は、地形を飛び越えるか地形に沿って滑る。", "tooltip": "短い距離をダッシュする。地形に触れた場合は地形を飛び越え、ダッシュ距離が大幅に増加する。その後、{{ buffduration }}秒間、<spellName>「バーストファイア」</spellName>が敵を貫通するようになり、2体目以降の敵には{{ pendamagepercent*100 }}%のダメージを与え、最初に命中した対象には通常攻撃時効果で<magicDamage>{{ bonusdamagetotal }}の魔法ダメージ</magicDamage>を追加で与える。<br /><br />通常攻撃またはスキルを敵チャンピオンに命中させると、このスキルのクールダウンが{{ cdreductionperhit }}秒短縮される。クリティカルが発生すると、代わりにクールダウンが{{ critcdreductionperhit }}秒短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["割合ダメージ", "基本ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ pendamagepercent*100.000000 }}% -> {{ pendamagepercentnl*100.000000 }}%", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [90, 85, 80, 75, 70], "costBurn": "90/85/80/75/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "ZeriE.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ZeriR", "name": "ライトニングクラッシュ", "description": "大量の電力を放出して自身をオーバーチャージし、ダメージと移動速度が増加する。この増加移動速度はスタック可能で、敵チャンピオンに攻撃が命中するたび、リフレッシュされて強化される。オーバーチャージ中は「バーストファイア」が素早い3連射になり、他の敵へと連鎖する電撃が放たれるようになる。", "tooltip": "大量の電力を放出し、周囲の敵に<magicDamage>{{ totalactivedamage }}の魔法ダメージ</magicDamage>を与える。敵チャンピオンに命中した場合は、{{ rduration }}秒間、<attackSpeed>攻撃速度が{{ baseaspercent*100 }}%</attackSpeed>、<speed>移動速度が{{ basebonusms*100 }}%</speed>増加する。敵チャンピオンに通常攻撃またはスキルが命中すると、このスキルの効果時間が{{ maxhyperchargeduration }}秒延長されて、「オーバーチャージ」のスタックが1つ増加する。クリティカルが発生すると、追加で2スタック獲得する。スタックごとに<speed>移動速度が{{ mspercent*100 }}%</speed>増加する。<br /><br />この効果時間中は<spellName>「バーストファイア」</spellName>が素早い3連射になって周囲の敵に連鎖し、<physicalDamage>{{ chainphysicaldamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ activedamage }} -> {{ activedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 75, 70], "cooldownBurn": "80/75/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ZeriR.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "生体バッテリー", "description": "ゼリの通常攻撃は魔法ダメージを与え、スキルとして扱われる。移動および「バーストファイア」の使用により、「スパークパック」にエネルギーが蓄積される。最大までチャージされると、次の通常攻撃が追加ダメージを与える。", "image": {"full": "ZeriP.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}