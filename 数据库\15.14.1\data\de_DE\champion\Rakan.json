{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rakan": {"id": "<PERSON><PERSON>", "key": "497", "name": "<PERSON><PERSON>", "title": "der Publikumsliebling", "image": {"full": "Rakan.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "497000", "num": 0, "name": "default", "chromas": false}, {"id": "497001", "num": 1, "name": "Kosmisches Sonnenfeuer Rakan", "chromas": false}, {"id": "497002", "num": 2, "name": "Herzblatt-Rakan", "chromas": false}, {"id": "497003", "num": 3, "name": "SSG-Rakan", "chromas": false}, {"id": "497004", "num": 4, "name": "iG-Rakan", "chromas": false}, {"id": "497005", "num": 5, "name": "Sternenwächter Rakan", "chromas": true}, {"id": "497009", "num": 9, "name": "Ahnenholz-Rakan", "chromas": true}, {"id": "497018", "num": 18, "name": "Arkana-Rakan", "chromas": true}, {"id": "497027", "num": 27, "name": "Gebrochener Pakt-Rakan", "chromas": true}, {"id": "497036", "num": 36, "name": "Erlöster Sternenwächter Rakan", "chromas": false}, {"id": "497037", "num": 37, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "497038", "num": 38, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "497047", "num": 47, "name": "Academia Certaminis-Rakan", "chromas": true}], "lore": "<PERSON><PERSON>, ein gleichermaßen wankelmütiger wie charmanter Geselle, ist ein berüchtigter vastayanischer Unruhestifter und der begnadetste Kriegstänzer in der Stammesgeschichte der Lhotlan. Für die Menschen des ionischen Hochlandes ist sein Name seit langer Zeit gleichbedeutend mit wilden Festen und ungebändigter Musik. Nur wenige würden vermuten, dass der energiegeladene Schausteller auch der Gefährte der Rebellin Xayah ist und sich ihrer Sache verschrieben hat.", "blurb": "<PERSON><PERSON>, ein gleichermaßen wankelmütiger wie charmanter Geselle, ist ein berüchtigter vastayanischer Unruhestifter und der begnadetste Kriegstänzer in der Stammesgeschichte der Lhotlan. Für die Menschen des ionischen Hochlandes ist sein Name seit langer...", "allytips": ["Rakan braucht Verbündete in seiner Nähe, um das Beste aus seinen Fähigkeiten herauszuholen.", "Rakans Lauftempo erhöht die Geschwindigkeit seiner Sprünge. Du kannst die zusätzliche Geschwindigkeit nutzen, um den Gegner zu überraschen!", "<PERSON><PERSON><PERSON>r kann spaßig sein. Das hängt nur von dir ab."], "enemytips": ["Rakans Bewegungsfähigkeiten lassen seinen Zielort erkennen. Das kannst du zu deinem Vorteil nutzen.", "Champions mit schnell einsetzbarer Massenkontrolle können Rakan leicht in die Knie zwingen.", "Triffs<PERSON> du auf Rakan, wenn keine seiner Verbündeten in der Nähe sind, ist seine Mobilität stark eingeschränkt. Du solltest also versuchen, ihn alleine zu fassen zu bekommen."], "tags": ["Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 315, "mpperlevel": 50, "movespeed": 335, "armor": 30, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 300, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 8.75, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.635}, "spells": [{"id": "RakanQ", "name": "Phönixfeder", "description": "<PERSON><PERSON><PERSON><PERSON>t eine magische Feder, die magischen Schaden verursacht. Wenn ein Champion oder ein episches Monster getroffen wird, erhält Rakan die Möglichkeit, seine Verbündeten zu heilen.", "tooltip": "<PERSON>kan schleudert eine magische Feder, die am ersten getroffenen Gegner <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.<br /><br />Wenn die Feder einen Champion oder ein episches Dschungelmonster trifft, stellt Rakan nach {{ healdelay }}&nbsp;Sekunden, oder sobald er einen Verbündeten berührt, <healing>{{ totalheal }}&nbsp;Leben</healing> bei sich selbst und Verbündeten in der Nähe wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RakanQ.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RakanW", "name": "Gelungener Auftritt", "description": "Springt an einen Ort und schleudert dort alle Gegner in der Nähe in die Luft.", "tooltip": "<PERSON><PERSON> springt und wirbelt dann in die Luft, wodurch er Gegner {{ knockupduration }}&nbsp;Sekunde lang <status>hochschleudert</status> und ihnen <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RakanW.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RakanE", "name": "Kriegstänzer", "description": "Fliegt zu einem verbündeten Champion und gewährt ihm einen Schild. Kann für kurze Zeit ohne Kosten erneut gewirkt werden.", "tooltip": "<PERSON><PERSON> springt zu einem verbündeten Champion und gewährt ihm {{ e3 }}&nbsp;Sekunden lang einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ totalshield }}.<br /><br /><PERSON><PERSON> kann diese Fähigkeit innerhalb von {{ e2 }}&nbsp;Sekunden einmal <recast>reaktivieren</recast>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [3, 3, 3, 3, 3], [20, 18, 16, 14, 12], [40, 45, 50, 55, 60], [700, 700, 700, 700, 700], [1000, 1000, 1000, 1000, 1000], [1150, 1150, 1150, 1150, 1150], [1250, 1250, 1250, 1250, 1250], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "5", "3", "20/18/16/14/12", "40/45/50/55/60", "700", "1000", "1150", "1250", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "RakanE.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RakanR", "name": "<PERSON><PERSON> dir den Weg frei!", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Lauftempo, beza<PERSON>rt berührte Gegner und fügt ihnen magischen Schaden zu.", "tooltip": "Rakan erhält {{ e2 }}&nbsp;Sekunden lang <speed>{{ e5 }}&nbsp;% Lauftempo</speed>. Rakan verursacht {{ e3 }}&nbsp;Sekunde(n) lang <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage> und <status>bezaubert</status> <PERSON><PERSON><PERSON>, die er zum ersten Mal berührt. Der erste berührte Champion gewährt Rakan <speed>{{ e6 }}&nbsp;% Lauftempo</speed>, das langsam wieder abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 200, 300], [4, 4, 4], [1, 1.25, 1.5], [0.25, 0.25, 0.25], [75, 75, 75], [150, 150, 150], [150, 150, 150], [130, 110, 90], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "100/200/300", "4", "1/1.25/1.5", "0.25", "75", "150", "150", "130/110/90", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [150, 150, 150], "rangeBurn": "150", "image": {"full": "RakanR.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Aufgeplustert", "description": "<PERSON><PERSON> erh<PERSON>lt in regelmäßigen Abständen einen Schild.", "image": {"full": "Rakan_P.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}