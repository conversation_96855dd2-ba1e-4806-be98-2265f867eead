{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kindred": {"id": "Kindred", "key": "203", "name": "Киндред", "title": "Вечные охотники", "image": {"full": "Kindred.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "203000", "num": 0, "name": "default", "chromas": false}, {"id": "203001", "num": 1, "name": "Огненные Киндред", "chromas": false}, {"id": "203002", "num": 2, "name": "Супергалактические Киндред", "chromas": false}, {"id": "203003", "num": 3, "name": "Духи цветения Киндред", "chromas": true}, {"id": "203012", "num": 12, "name": "Фарфоровые Киндред", "chromas": true}, {"id": "203022", "num": 22, "name": "Кот и пес Киндред", "chromas": true}, {"id": "203023", "num": 23, "name": "DRX Киндред", "chromas": true}, {"id": "203033", "num": 33, "name": "Фарфоровые Киндред (престижный)", "chromas": false}, {"id": "203034", "num": 34, "name": "Избранные Волком Киндред", "chromas": true}], "lore": "Киндред – это воплощение смерти, разделенное надвое; два духа-близнеца, которые никогда не расстаются. Стрелы Овечки быстро избавляют от тревог тех, кто смирился с судьбой. Волк же охотится на непокорных, которые пытаются убежать от неизбежного. Он прерывает их бег одним мощным ударом челюстей. Происхождение Киндред неясно, но каждый житель Рунтерры знает, что в конце концов должен будет выбрать лик своей смерти.", "blurb": "Киндред – это воплощение смерти, разделенное надвое; два духа-близнеца, которые никогда не расстаются. Стрелы Овечки быстро избавляют от тревог тех, кто смирился с судьбой. Волк же охотится на непокорных, которые пытаются убежать от неизбежного. Он...", "allytips": ["Постоянное перемещение между атаками при игре в лесу поможет избежать урона и даст дополнительное восстановление здоровья от Волчьего бешенства.", "Ответственно отнеситесь к выбору целей охоты: убийства помеченных целей - залог успеха на поздней стадии игры.", "Не бросайтесь в командный бой впереди всех - позвольте вашим сокомандникам инициировать его."], "enemytips": ["Киндред - хрупкий чемпион. Постоянное давление заставит Вечных охотников играть осторожно.", "Зачищайте лесные лагеря с помеченными монстрами. Это замедлит рост урона Киндред.", "Когда Киндред используют Милость Овечки, забирайтесь внутрь области действия умения: оно не дает умереть всем чемпионам (даже вражеским)."], "tags": ["Marksman"], "partype": "Мана", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 4}, "stats": {"hp": 595, "hpperlevel": 104, "mp": 300, "mpperlevel": 35, "movespeed": 325, "armor": 29, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 3.25, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "KindredQ", "name": "Тан<PERSON>ц стрел", "description": "Овечка делает рывок в выбранном направлении, а затем стреляет, поражая до трех врагов.", "tooltip": "Киндред совершают рывок и выпускают стрелы в 3 врагов, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> и увеличивая свою <attackSpeed>скорость атаки на {{ totalqattackspeed }}</attackSpeed> на {{ e8 }} сек.<br /><br />Пока Киндред находятся в области действия умения <spellName>Волчье бешенство</spellName>, перезарядка этого умения сокращается до {{ e4 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка в Волчьем бешенстве"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e4 }} -> {{ e4NL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [40, 65, 90, 115, 140], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 3.5, 3, 2.5, 2], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0.35, 0.35, 0.35, 0.35, 0.35], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/65/90/115/140", "0", "500", "4/3.5/3/2.5/2", "100", "12", "0.35", "4", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [340, 340, 340, 340, 340], "rangeBurn": "340", "image": {"full": "KindredQ.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KindredW", "name": "Волчье бешенство", "description": "Волк впадает в ярость и атакует врагов вокруг себя. Атакуя и передвигаясь, Овечка пассивно накапливает заряды. При накоплении максимального количества зарядов следующая атака Овечки восстанавливает здоровье.", "tooltip": "<spellPassive>Пассивно:</spellPassive> Киндред получают заряды, когда передвигаются и атакуют. После накопления 100 зарядов следующая автоатака восстанавливает Киндред до <healing>{{ attackheal }} здоровья</healing> в зависимости от их недостающего здоровья.<br /><br /><spellActive>Активно:</spellActive> Киндред очерчивают область, в которой Волк кусает последнего врага, атакованного Овечкой. Укусы Волка наносят <magicDamage>магический урон в размере {{ basewolfdamage }} плюс {{ percentwolfdamage }} от текущего здоровья</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [40, 45, 50, 55, 60], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [8.5, 8.5, 8.5, 8.5, 8.5], [25, 30, 35, 40, 45], [800, 800, 800, 800, 800], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/45/50/55/60", "1", "1.5", "8.5", "25/30/35/40/45", "800", "0.5", "0.5", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [560, 560, 560, 560, 560], "rangeBurn": "560", "image": {"full": "KindredW.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KindredEWrapper", "name": "Всепоглощающий ужас", "description": "Овечка делает точный выстрел, замедляя врага. Если после этого она дважды поражает его автоатаками, при третьей автоатаке на него набрасывается Волк, нанося огромный урон.", "tooltip": "Киндред ослабляют врага, <status>замедляя</status> его на {{ totalslow }}% на {{ slowduration }} сек.<br /><br />Во время третьей автоатаки Киндред против цели на нее набрасывается Волк, нанося <physicalDamage>физический урон</physicalDamage> <physicalDamage>в размере {{ basebitedamage }} плюс {{ percentbitedamage }} от недостающего здоровья</physicalDamage>. Если между атаками проходит {{ totalduration }} сек., эффект пропадает.<br /><br />Если у цели осталось менее {{ healththreshold }} здоровья, атака Волка становится критической и наносит <physicalDamage>физический урон</physicalDamage> <physicalDamage>в размере {{ basebitedamage }} плюс {{ critdamage }} от недостающего здоровья</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "KindredEWrapper.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KindredR", "name": "Милость Овечки", "description": "Овечка дарует всем бойцам в зоне действия умения защиту от смерти. Во время действия умения никто не может умереть. По окончании его действия всем восстанавливается здоровье.", "tooltip": "Киндред благословляют землю вокруг себя на {{ e2 }} сек., создавая область, в которой никто не может умереть (действует на всех союзных, вражеских и нейтральных бойцов). Когда уровень здоровья бойца достигает 10%, он становится невосприимчив к урону и лечению, пока не покинет область.<br /><br />По окончании действия благословения все бойцы внутри области восстанавливают <healing>{{ e1 }} здоровья</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Лечение", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [225, 300, 375], [4, 4, 4], [530, 530, 530], [400, 400, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "225/300/375", "4", "530", "400", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "KindredR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Знак Киндред", "description": "Киндред могут помечать цели для охоты. Удачная охота усиливает базовые умения Киндред до конца игры. Каждые 4 удачные попытки поохотиться увеличивают дальность автоатак Киндред.", "image": {"full": "Kindred_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}