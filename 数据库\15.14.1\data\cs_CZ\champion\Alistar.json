{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "Alistar", "title": "Minotaurus", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "12004", "num": 4, "name": "Ostř<PERSON><PERSON><PERSON> Ali<PERSON>", "chromas": false}, {"id": "12005", "num": 5, "name": "Nespoutaný Alistar", "chromas": false}, {"id": "12006", "num": 6, "name": "Pekeln<PERSON> Alistar", "chromas": false}, {"id": "12007", "num": 7, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "12008", "num": 8, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "12009", "num": 9, "name": "SKT T1 Alistar", "chromas": false}, {"id": "12010", "num": 10, "name": "Kravička Alistar", "chromas": true}, {"id": "12019", "num": 19, "name": "Magitechnický Alistar", "chromas": false}, {"id": "12020", "num": 20, "name": "Dobyvatel Alistar", "chromas": true}, {"id": "12022", "num": 22, "name": "Alistar temného <PERSON>u", "chromas": true}, {"id": "12029", "num": 29, "name": "Lunárn<PERSON>", "chromas": true}], "lore": "Alistar je mocný válečník s ob<PERSON><PERSON><PERSON> pověstí, jen<PERSON> se snaží pomstít noxijské říši za vyhlazení svého kmene. Ačkoliv byl zotročen a přinucen přijmout úděl gladi<PERSON>, díky své nezlomné vůli se z něj nakonec nestalo pouhé bojující zvíře. Časem se mu podařilo zbavit okovů svých bývalých pánů a nyní bojuje jménem všech utlačovaných a umlčovaných. Jeho hněv je stejně nebezpečnou zbraní jako jeho rohy, kopyta a pěsti.", "blurb": "Alistar je mocný válečník s ob<PERSON><PERSON><PERSON> pověstí, jen<PERSON> se snaží pomstít noxijské říši za vyhlazení svého kmene. Ačkoliv byl zotročen a přinucen přijmout úděl glad<PERSON>, díky své nezlomné vůli se z něj nakonec nestalo pouhé bojující zvíře. Časem se mu...", "allytips": ["Schopností Rozdrtit si můžeš připravit lepší pozici pro seslání Beranidla.", "Rychlost pohybu je pro Alistara velice důležitá. Pečlivě si roz<PERSON>, kter<PERSON> boty mu koupíš.", "Pomocí schopnosti Skok můžeš protivníky překvapit a kombinací kouzel Rozdrtit a Beranidlo je odrazit směrem ke svým spolubojovníkům."], "enemytips": ["Alistar ti dokáže svými omezujícími efekty velice znepříjemnit život, sám je přitom velice odolný. Zam<PERSON>ř se tedy spíš na zranitelnější <PERSON>y, kte<PERSON><PERSON> způsobují velké poš<PERSON>zení.", "V blízkosti věží si dej pozor na kombinaci schopností Rozdrtit a Beranidlo.", "<PERSON><PERSON>ž Alistar aktivuje svou ultimátku, je lep<PERSON> se stáhnout a zaútočit na něj až ve chvíli, kdy efekt vyprchá."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Alistar mohutně udeří do země, <PERSON><PERSON><PERSON><PERSON> z<PERSON>ůsobí poškození okolním nepřátelům a vyhodí je do vzduchu.", "tooltip": "Alistar udeří do země, na {{ knockupduration }}&nbsp;sek. vyhodí nepřátele <status>do vzduchu</status> a způsobí jim <magicDamage>{{ totaldamage }} bod<PERSON> magického poš<PERSON>zení</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Přebíjecí doba", "@AbilityResourceName@ pro seslání", "Poškození"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "Headbutt", "name": "Beranidlo", "description": "Alistar do cíle narazí svou hlav<PERSON>, <PERSON><PERSON><PERSON><PERSON> jej poškodí a odhodí zpět.", "tooltip": "Alistar narazí plnou silou do nepřítele, <PERSON><PERSON><PERSON><PERSON> <status>odhod<PERSON></status> <status>zpět</status> a způsobí mu <magicDamage>{{ totaldamage }} bod<PERSON> magického poš<PERSON>zení</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Přebíjecí doba", "@AbilityResourceName@ pro seslání", "Poškození"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "AlistarE", "name": "Zadupání", "description": "Alistar zadupe okolní nepřátelské jednotky do země, může procházet ostatními jednotkami a získává stupně nabití, pokud poškodí nějakého nepřátelského šampiona. Při plném nabití způsobí Alistarův příští základní útok vůči nepřátelskému šampionovi dodatečné magické poškození a omráčí ho.", "tooltip": "Alistar začne dusat, získá efekt Duch a způsobuje okolním nepřátelům <magicDamage>{{ totaldamage }} bodů magického poškození</magicDamage> roz<PERSON>ženého do {{ e3 }}&nbsp;sek. <PERSON>, kter<PERSON> způsobí poškození šampionovi, poskytne Alistarovi jeden stupeň efektu.<br /><br />Po nasčítání {{ e5 }} stupňů Alistar svým příštím útokem vedeným proti šampionovi na {{ e6 }}&nbsp;sek. <status>omráč<PERSON></status> zasažený cíl a způsobí mu dodatečných <magicDamage>{{ attackbonusdamage }} bodů magického poškození</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Přebíjecí doba", "@AbilityResourceName@ pro seslání", "Poškození"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "FerociousHowl", "name": "Nezlomná vůle", "description": "Alistar vydá divoký řev, <PERSON><PERSON><PERSON>ž se zbaví všech omezujících efektů a po dobu trvání schopnosti bude obdržovat snížené fyzické i magické poškození.", "tooltip": "Alistar se okamžitě zbaví všech <status>omezujících</status> efektů a po dobu {{ rduration }}&nbsp;sek. mu bude způsobováno o {{ rdamagereduction }}&nbsp;% ni<PERSON><PERSON><PERSON> p<PERSON>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Přebíjecí doba", "Snížení poškození"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamagereduction }} % -> {{ rdamagereductionNL }} %"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Alistar si nabíj<PERSON> řev omráčením či odstrkováním nepřátelských šampionů nebo když kolem něj zemřou nepřátelé. K<PERSON>ž řev plně nabije, vyléč<PERSON> sebe a všechny okolní spojenecké šampiony.", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}