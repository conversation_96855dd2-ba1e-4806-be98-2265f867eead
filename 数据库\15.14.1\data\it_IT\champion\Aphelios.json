{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aphelios": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "523", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "L'arma della fede", "image": {"full": "Aphelios.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "523000", "num": 0, "name": "default", "chromas": false}, {"id": "523001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON> della Notte", "chromas": true}, {"id": "523009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "523018", "num": 18, "name": "<PERSON><PERSON>lios EDG", "chromas": false}, {"id": "523020", "num": 20, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "523030", "num": 30, "name": "Aphelios HEARTSTEEL", "chromas": false}], "lore": "Emergendo ad armi spianate dall'ombra della luna, <PERSON><PERSON><PERSON><PERSON> elimina i nemici della fede immerso in un infinito silenzio: a parlare sono solo la sua mira infallibile e il rombo dei suoi cannoni. Anche se nel suo corpo scorre un potente veleno che lo ha reso muto, può contare sull'aiuto della sorella Alune. Lei lo guida da un tempio remoto, fornendogli un intero arsenale di armi create con la pietra di luna. Finché la luna brillerà alta nel cielo, A<PERSON><PERSON><PERSON> non camminerà mai da solo.", "blurb": "Emergendo ad armi spianate dall'ombra della luna, <PERSON><PERSON><PERSON><PERSON> elimina i nemici della fede immerso in un infinito silenzio: a parlare sono solo la sua mira infallibile e il rombo dei suoi cannoni. Anche se nel suo corpo scorre un potente veleno che lo ha...", "allytips": ["Ogni arma di Aphelios ha diversi punti di forza, quindi cerca di trovare la situazione migliore per sfruttarli. "], "enemytips": ["Ognuna delle armi di Aphelios ha debolezze diverse. Cerca di sfruttare quelle che più si adattano al tuo campione. Attenzione all'arma gravitazionale viola, ti può immobilizzare."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 2, "magic": 1, "difficulty": 10}, "stats": {"hp": 600, "hpperlevel": 102, "mp": 348, "mpperlevel": 42, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 6.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.3, "attackspeedperlevel": 2.1, "attackspeed": 0.665}, "spells": [{"id": "ApheliosQ_ClientTooltipWrapper", "name": "Abilità armi", "description": "Aphelios ha 5 diverse abilità attive, in base all'arma principale:<br><br>Calibrum (fucile): arma a lunga distanza che marchia il suo bersaglio per un attacco successivo a lunga gittata.<br>Severum (pistola falce): corri veloce mentre attacchi i nemici nelle vicinanze con entrambe le armi.<br>Gravitum (cannone): immobilizza tutti i nemici rallentati da quest'arma.<br>Infernum (lanciafiamme): colpisci i nemici in un'area a cono e li attacchi con la tua arma secondaria.<br>Crescendum (chakram): schiera una sentinella che spara con la tua arma secondaria.<br>", "tooltip": "{{ spellmodifierdescriptionappend }}", "leveltip": {"label": [], "effect": []}, "maxrank": 6, "cooldown": [9, 9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "ApheliosQ_ClientTooltipWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ApheliosW", "name": "Fase", "description": "<PERSON><PERSON><PERSON>s cambia la sua arma principale con quella secondaria, sostituendo attacco base e abilità attiva.", "tooltip": "Scambia l'arma principale e quella secondaria, equipaggiando <b><i><span class=\"colora64dff\">Gravitum</span></i></b>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": [], "effect": []}, "maxrank": 6, "cooldown": [0.8, 0.8, 0.8, 0.8, 0.8, 0.8], "cooldownBurn": "0.8", "cost": [0, 0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [250, 250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ApheliosW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "ApheliosE_ClientTooltipWrapper", "name": "Sistema coda armi", "description": "<PERSON><PERSON><PERSON>s non ha una terza abilità. <PERSON><PERSON> slot mostra l'arma successiva che gli darà Alune. L'ordine delle armi inizia fisso ma può cambiare nel corso della partita: quando un'arma si scarica va in coda alla fila.", "tooltip": "{{ spellmodifierdescriptionappend }}", "leveltip": {"label": [], "effect": []}, "maxrank": 6, "cooldown": [0, 0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "ApheliosE_ClientTooltipWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ApheliosR", "name": "Veg<PERSON> lunare", "description": "Spara un colpo concentrato di luce lunare che esplode sui campioni nemici. Applica gli effetti unici dell'arma principale di Aphelios.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> spara un colpo concentrato di luce lunare che esplode quando colpisce un campione, infliggendo <physicalDamage>{{ maxdamage }} danni fisici</physicalDamage> ai nemici circostanti.<br /><br /><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> attacca tutti i campioni colpiti con l'arma principale. {{ Spell_ApheliosR_WeaponMod_{{ f1 }} }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Bonus Calibrum: danni marchio", "Bonus Severum: guarigione", "Bonus Infernum: danni"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ calibrumrmarkbonusdamage }} -> {{ calibrumrmarkbonusdamageNL }}", "{{ severumrhealbonus }} -> {{ severumrhealbonusNL }}", "{{ infernumrbonusdamagebase }} -> {{ infernumrbonusdamagebaseNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "ApheliosR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Impulso e Ragione", "description": "<PERSON><PERSON><PERSON><PERSON> brandisce cinque armi dei Lunari create da sua sorella, Alune. Ne usa due per volta: una principale e una secondaria. Ogni arma ha un attacco base unico e un'abilità. Gli attacchi e le abilità consumano le munizioni di un'arma. <PERSON>uan<PERSON> finisce le munizioni, <PERSON><PERSON><PERSON><PERSON> lascia l'arma e Alune evoca la successiva delle 5. ", "image": {"full": "ApheliosP.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}