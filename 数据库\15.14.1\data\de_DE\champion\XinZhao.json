{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"XinZhao": {"id": "XinZhao", "key": "5", "name": "<PERSON><PERSON>", "title": "<PERSON> Sen<PERSON><PERSON><PERSON> von <PERSON>", "image": {"full": "XinZhao.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "5000", "num": 0, "name": "default", "chromas": false}, {"id": "5001", "num": 1, "name": "Kommando-<PERSON><PERSON>", "chromas": false}, {"id": "5002", "num": 2, "name": "Imperialer <PERSON><PERSON>", "chromas": false}, {"id": "5003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "5004", "num": 4, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": false}, {"id": "5005", "num": 5, "name": "Kriegsher<PERSON>", "chromas": true}, {"id": "5006", "num": 6, "name": "Geheimagent <PERSON><PERSON>", "chromas": false}, {"id": "5013", "num": 13, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": true}, {"id": "5020", "num": 20, "name": "Kosmischer Verteidiger <PERSON>", "chromas": true}, {"id": "5027", "num": 27, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": true}, {"id": "5036", "num": 36, "name": "Feuerwerks-Xi<PERSON> Zhao", "chromas": true}], "lore": "<PERSON>n <PERSON> ist ein entschlossener Krieger, der der Lichtschild-Dynastie gegenüber loyal ist. Einst kämpfte er in den verdammten Schlachtgruben von Noxus in unzähligen Gladiatorenkämpfen ums pure Überleben, doch als er von demacianischen Truppen befreit wurde, schwor er diesen mutigen Befreiern bei seinem Leben ewige Treue. Mit seinem geliebten dreispitzigen Speer kämpft Xin Zhao nun für seine Wahlheimat und fordert ungeachtet des Risikos jeden Gegner kühn heraus.", "blurb": "<PERSON>n <PERSON> ist ein entschlossener Krieger, der der Lichtschild-Dynastie gegenüber loyal ist. Einst kämpfte er in den verdammten Schlachtgruben von Noxus in unzähligen Gladiatorenkämpfen ums pure Überleben, doch als er von demacianischen Truppen befreit...", "allytips": ["<PERSON><PERSON> ist besonders gut darin, Kämpfe zu initiieren. Führe den Angriff an und benutze deine ultimative Fähigkeit, um so viel Schaden wie möglich zu verursachen.", "<PERSON><PERSON><PERSON>, dich in die richtige Position zu bringen, damit das Zurückstoßen durch deine ultimative Fähigkeit möglichst effektiv ausfällt."], "enemytips": ["Mit seinem Angriff und seiner ultimativen Fähigkeit, die Schaden an allen Einheiten um ihn herum verursachen, ist Xin Zhao ein guter Initiator. Dein Team sollte möglichst weit verteilt stehen, bis er seine ultimative Fähigkeit gezündet hat.", "<PERSON><PERSON> verlässt sich vor allem auf „Drei-Krallen-Schlag“ für mehr Schaden und verkürzte Abklingzeiten, weshalb es einen spürbaren Unterschied ausmacht, wenn du seine Kombination unterbrechen kannst."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 2}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 274, "mpperlevel": 55, "movespeed": 345, "armor": 35, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "XinZhaoQ", "name": "Drei-Krallen-Schlag", "description": "<PERSON><PERSON> verursacht mit seinen nächsten 3 normalen Angriffen erhöhten Schaden, wobei der 3. <PERSON><PERSON> den getroffenen Gegner in die Luft schleudert.", "tooltip": "<PERSON>n Zhaos nächste 3&nbsp;Angriffe verursachen zusätzlich <physicalDamage>{{ bonusdamage }}&nbsp;normalen Schaden</physicalDamage> und verringern die Abklingzeiten seiner anderen Fähigkeiten um 1&nbsp;Sekunde. Beim dritten Angriff wird das Ziel zudem {{ e2 }}&nbsp;Sekunden lang <status>hochgeschleudert</status>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzlicher Schaden", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [20, 35, 50, 65, 80], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/35/50/65/80", "0.75", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "XinZhaoQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XinZhaoW", "name": "<PERSON> zu Blitz", "description": "<PERSON><PERSON> schwingt seinen <PERSON>er vor sich und stößt ihn dann nach vorne. Getroffene Gegner werden verlangsamt und als herausgefordert markiert.", "tooltip": "<PERSON><PERSON> <PERSON> schwingt seinen Speer und verursacht <physicalDamage>{{ slashdamage }}</physicalDamage>&nbsp;normalen Schaden. Dann stößt er den Speer nach vorn und verursacht <physicalDamage>{{ thrustdamage }}</physicalDamage>&nbsp;Schad<PERSON>. G<PERSON><PERSON>, die vom Stoß getroffen wurden, werden {{ e7 }}&nbsp;Sekunden lang um {{ e6 }}&nbsp;% <status>verlangsamt</status>. <br /><br />Champions und große Monster, die vom Stoß getroffen wurden, werden {{ markduration }}&nbsp;Sekunden lang als <keywordMajor>herausgefordert</keywordMajor> markiert und aufgedeckt, sofern sie nicht <keywordStealth>getarnt</keywordStealth> sind.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schlagschaden", "St<PERSON>ßschaden", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ slashbasedamage }} -> {{ slashbasedamageNL }}", "{{ thrustbasedamage }} -> {{ thrustbasedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [140, 140, 140, 140, 140], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0.5", "0.5", "0.5", "50", "1.5", "140", "200", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XinZhaoW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XinZhaoE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> stürmt auf einen Gegner zu, erhöht sein Angriffstempo, fügt allen Gegnern im Bereich Schaden zu und verlangsamt sie kurzzeitig. Die Reichweite von „Kühner Angriff“ ist gegen herausgeforderte Ziele erhöht.", "tooltip": "<PERSON><PERSON> stürmt auf einen Gegner zu, fügt <PERSON> in der Nähe <magicDamage>{{ chargedamage }}&nbsp;magischen <PERSON>en</magicDamage> zu und <status>verlangsamt</status> sie {{ e6 }}&nbsp;Sekunden lang um {{ baseslowamount }}&nbsp;%.<br /><br />Außerdem erhält Xin Zhao {{ e4 }}&nbsp;Sekunden lang <attackSpeed>{{ e3 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />Die Reichweite von <spellName>Kühner Angriff</spellName> ist bei Angriffen gegen <keywordMajor>herausgeforderte</keywordMajor> Gegner erhöht. <br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Angriffstempo"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect3amount*100.000000 }}&nbsp;% -> {{ effect3amountnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [-0.3, -0.3, -0.3, -0.3, -0.3], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [250, 250, 250, 250, 250], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "-0.3", "40/45/50/55/60", "5", "250", "0.5", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "XinZhaoE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XinZhaoR", "name": "Sichel<PERSON>utz", "description": "<PERSON><PERSON> <PERSON> fordert einen G<PERSON>ner heraus, dem er vor kurzem Schaden zugefügt hat. Xin <PERSON> verursacht an Gegnern in der Nähe Schaden abhängig von ihrem aktuellen Leben und stößt nicht herausgeforderte Ziele zurück. Er wird immun gegen<PERSON>ber Schaden, den Champions außerhalb des erschaffenen Kreises verursachen.", "tooltip": "<PERSON> letzte Champion, dem <PERSON><PERSON> mit einem Angriff oder mit <spellName>Kühner Angriff</spellName> Schaden zugefügt hat, ist {{ markduration }}&nbsp;Sekunden lang <keywordMajor>herausgefordert</keywordMajor>.<br /><br /><PERSON><PERSON> schwingt seinen Speer und verursacht <physicalDamage>normalen Schaden</physicalDamage> in <PERSON><PERSON><PERSON> von {{ totaldamage }} plus {{ percentcurrenthealthdamage*100 }}&nbsp;% seines aktuellen Lebens. <PERSON><PERSON> Gegner, die <keywordMajor>nicht herausgefordert</keywordMajor> sind, werden dabei <status>zurückgestoßen</status>. <br /><br />Danach ist Xin Zhao {{ missiledefensebaseduration }}&nbsp;Sekunden lang immun gegen Schaden von Gegnern, die sich außerhalb der Reichweite seines Schwungs befinden. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "XinZhaoR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Entschlossenheit", "description": "Jeder 3.&nbsp;<PERSON>riff verursacht zusätzlichen Schaden und heilt <PERSON><PERSON>.", "image": {"full": "XinZhaoP.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}