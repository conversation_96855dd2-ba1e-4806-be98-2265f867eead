{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lux": {"id": "<PERSON><PERSON>", "key": "99", "name": "<PERSON><PERSON>", "title": "la signora della luminosità", "image": {"full": "Lux.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "99000", "num": 0, "name": "default", "chromas": false}, {"id": "99001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "99002", "num": 2, "name": "Lux <PERSON> d'Incantesimi", "chromas": false}, {"id": "99003", "num": 3, "name": "Lux Commando", "chromas": false}, {"id": "99004", "num": 4, "name": "Lux <PERSON>e", "chromas": false}, {"id": "99005", "num": 5, "name": "Lux Legione d'Acciaio", "chromas": false}, {"id": "99006", "num": 6, "name": "Lux <PERSON>a <PERSON>", "chromas": false}, {"id": "99007", "num": 7, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "99008", "num": 8, "name": "Lux Imperatrice Lunare", "chromas": true}, {"id": "99014", "num": 14, "name": "Lux <PERSON>a in Pigiama", "chromas": false}, {"id": "99015", "num": 15, "name": "Lux dell'Accademia di Battaglia", "chromas": false}, {"id": "99016", "num": 16, "name": "Lux dell'Accademia di Battaglia (edizione prestigio)", "chromas": false}, {"id": "99017", "num": 17, "name": "Lux <PERSON>curit<PERSON> Cosmica", "chromas": false}, {"id": "99018", "num": 18, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "99019", "num": 19, "name": "Lux Rit<PERSON>", "chromas": true}, {"id": "99029", "num": 29, "name": "Lux di Porcellana", "chromas": true}, {"id": "99038", "num": 38, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "99039", "num": 39, "name": "Lux dell'Accademia di Battaglia (edizione prestigio 2022)", "chromas": false}, {"id": "99040", "num": 40, "name": "Lux di Porcellana (edizione prestigio)", "chromas": false}, {"id": "99042", "num": 42, "name": "Lux <PERSON>a", "chromas": true}, {"id": "99061", "num": 61, "name": "<PERSON>x <PERSON> fatata", "chromas": true}, {"id": "99070", "num": 70, "name": "Lux Fiore spirituale (edizione prestigio)", "chromas": true}], "lore": "Lu<PERSON>nna Crownguard viene da De<PERSON>cia, un reame isolato dove le capacità magiche vengono viste con timore e sospetto. Capace di piegare la luce alla sua volontà, è cresciuta nel timore, costretta a tenere segreto il suo potere per mantenere la reputazione della sua famiglia. Nonostante questo, la resistenza e l'ottimismo di Lux l'hanno portata ad accettare le sue capacità e ora le utilizza in segreto al servizio della sua patria.", "blurb": "<PERSON><PERSON>nna Crownguard viene da Demacia, un reame isolato dove le capacità magiche vengono viste con timore e sospetto. Capace di piegare la luce alla sua volontà, è cresciuta nel timore, costretta a tenere segreto il suo potere per mantenere la reputazione...", "allytips": ["Lux ha delle abilità di controllo delle zone molto forti. Prova a piazzare Singolarità lucente in modo da prevenire l'avanzata o la ritirata di un nemico.", "Se hai difficoltà a controllare la Barriera prismatica, ricorda<PERSON> che ritorna verso di te dopo che ha raggiunto la sua gittata massima.", "Singolarità lucente è un ottimo strumento di ricognizione. Lancialo nell'erba alta prima di entrarvi dentro per controllare la presenza di eventuali nemici."], "enemytips": ["Lux ha delle forti abilità di controllo della zona. Cercate di dividervi e attaccare da diverse direzioni in modo che non possa bloccare un'area specifica.", "Quando ti stai ritirando con poca salute, preparati a schivare Scintilla finale di Lux; prima del raggio principale viene sparato un laser rosso: cerca di muoverti di lato, se possibile."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 580, "hpperlevel": 99, "mp": 480, "mpperlevel": 23.5, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3, "attackspeed": 0.669}, "spells": [{"id": "LuxLightBinding", "name": "Prigione luminosa", "description": "Lux rilascia una sfera di luce che può intrappolare e infliggere danni fino ad un massimo di due unità nemiche.", "tooltip": "Lux lancia una sfera di luce che <status>immobilizza</status> i primi due nemici per {{ e3 }} secondi e infligge <magicDamage>{{ totaldamagett }} danni magici</magicDamage> a ciascuno.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "50", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1175, 1175, 1175, 1175, 1175], "rangeBurn": "1175", "image": {"full": "LuxLightBinding.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxPrismaticWave", "name": "Barriera prismatica", "description": "Lux lancia la sua bacchetta e distorce la luce intorno a qualsiasi bersaglio amico che tocca, proteggendolo dai danni nemici.", "tooltip": "Lux lancia la sua bacchetta, che fornisce uno <shield>scudo da {{ totalshieldtt }}</shield> per {{ e3 }} secondi agli alleati che attraversa e che poi ritorna fornendo lo stesso <shield>scudo</shield> sulla traiettoria.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [2, 4, 6, 8, 10], [40, 55, 70, 85, 100], [2.5, 2.5, 2.5, 2.5, 2.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2/4/6/8/10", "40/55/70/85/100", "2.5", "100", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "LuxPrismaticWave.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxLightStrikeKugel", "name": "Singolarità lucente", "description": "Spara in un'area un'anomalia di luce distorta, che rallenta i nemici nelle vicinanze. Lux può farla detonare per danneggiare i nemici nell'area dell'effetto.", "tooltip": "Lux crea una zona di luce che <status>rallenta</status> i nemici del {{ e1 }}% e rivela l'area. Dopo {{ e3 }} secondi o al <recast>rilancio</recast> dell'abilità, la zona esplode infliggendo <magicDamage>{{ totaldamagett }} danni magici</magicDamage> e <status>rallentando</status> i nemici colpiti di {{ slowlingerduration }} secondo/i ulteriore.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento del movimento", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [65, 115, 165, 215, 265], [5, 5, 5, 5, 5], [310, 310, 310, 310, 310], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25/30/35/40/45", "65/115/165/215/265", "5", "310", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LuxLightStrikeKugel.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxR", "name": "Scintilla finale", "description": "Dopo aver raccolto le energie, Lux spara un raggio di luce che infligge danni a tutti i bersagli nell'area. In più, attiva l'abilità passiva di Lux e ricarica la durata del debuff di Illuminazione.", "tooltip": "Lux emette un accecante raggio di luce che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> a tutti i nemici lungo una linea.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 50, 40], "cooldownBurn": "60/50/40", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3340, 3340, 3340], "rangeBurn": "3340", "image": {"full": "LuxR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Illuminazione", "description": "Le abilità di Lux che infliggono danni caricano di energia il bersaglio per qualche secondo. Il prossimo attacco di Lux infiamma l'energia, infliggendo danni magici bonus (basati sul livello di Lux) al bersaglio.", "image": {"full": "LuxIlluminatingFraulein.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}