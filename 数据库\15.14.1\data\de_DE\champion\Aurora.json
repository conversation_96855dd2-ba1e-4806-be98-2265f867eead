{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aurora": {"id": "Aurora", "key": "893", "name": "Aurora", "title": "die Hexe zwischen den Welten", "image": {"full": "Aurora.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "893000", "num": 0, "name": "default", "chromas": false}, {"id": "893001", "num": 1, "name": "Battle Bunny Aurora", "chromas": true}], "lore": "Seit ihrer Geburt verfügt Aurora über die einzigartige Fähigkeit, zwischen der Welt der Geister und der Sterblichen hin und her zu reisen. Sie verließ ihr Zuhause, um mehr über die Bewohner des Geisterreichs zu lernen und ihre Forschungen voranzubringen. Da<PERSON> stieß sie auf einen abtrünnigen Hal<PERSON>t, der seinen Weg verloren hatte und vergessen wurde. Aurora erkannte seine Verzweiflung und beschloss, einen <PERSON>g zu finden, damit ihr wilder Freund seine vergessene Identität wiedererlangen konnte – eine Reise, die sie bis in die entlegensten Winkel Freljords führen sollte.", "blurb": "Seit ihrer Geburt verfügt Aurora über die einzigartige Fähigkeit, zwischen der Welt der Geister und der Sterblichen hin und her zu reisen. Sie verließ ihr Zuhause, um mehr über die Bewohner des Geisterreichs zu lernen und ihre Forschungen voranzubringen...", "allytips": [], "enemytips": [], "tags": ["Mage", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 607, "hpperlevel": 110, "mp": 475, "mpperlevel": 30, "movespeed": 335, "armor": 23, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.668}, "spells": [{"id": "AuroraQ", "name": "Hex, hex!", "description": "Aurora entsendet ein Ges<PERSON>, das jeden getroffenen Gegner verhext. Dann kann sie die Fähigkeit reaktivieren, um aktive Verhexungen zu sich zurückzuholen und unterwegs getroffenen Gegnern Schaden zuzufügen.", "tooltip": "Feuert verhexte Energie in eine Richtung, die Gegnern <magicDamage>{{ damage }}magischen Schaden</magicDamage> zufügt und sie für {{ markduration }} Sekunden verflucht.<br /><br /><recast>Reaktivierung:</recast> Beendet die Verhexung und zieht einen Teil des Geists der Gegner zurück zu Aurora, wodurch auf dem Weg getroffene Gegner abhängig von ihrem fehlenden Leben <magicDamage>{{ q2damagemax }}&nbsp;magischen Schaden</magicDamage> erleiden. Nach dem ersten Treffer wird der Schaden um 20&nbsp;% verringert.<br /><br />Wenn die Wirkung nachlässt, <recast>reaktiviert</recast> Aurora die Fähigkeit automatisch.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "AuroraQ.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuroraW", "name": "<PERSON><PERSON> den Schleier", "description": "<PERSON> springt in eine beliebige Richtung, betritt bei der Landung das Geisterreich, wird kurzzeitig unsichtbar und erhält währenddessen erhöhtes Lauftempo.", "tooltip": "<PERSON> hüpft in eine Richtung. Nach der Landung betrittst du das Geisterreich und wirst für {{ invisduration }}&nbsp;Sekunden <keywordStealth>unsichtbar</keywordStealth>. <PERSON> trit<PERSON> in den Zustand <keywordMajor>Weltenhopser</keywordMajor> ein und erhältst <speed>{{ movespeedbonus }}&nbsp;% Lauftempo</speed>.<br /><br />Erzielst du einen Kill gegen einen gegnerischen Champion, wird die Abklingzeit dieser Fähigkeit zurückgesetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON>", "Lauftempo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ invisduration }} -> {{ invisdurationNL }}", "{{ movespeedbonus }}&nbsp;% -> {{ movespeedbonusNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "AuroraW.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuroraE", "name": "Geisterstunde", "description": "Aurora vereint die beiden Welten und entfesselt eine Welle aus Geisterenergie, die Gegnern Schaden zufügt und sie verlangsamt, bevor <PERSON> zurück in Sicherheit hüpft.", "tooltip": "Aurora vereint die beiden Welten kurzzeitig und entfesselt eine Welle aus Geistmagie, die Gegnern im Bereich <magicDamage>{{ damagecalc }}&nbsp;magischen Schaden</magicDamage> zufügt und sie um {{ slowpercent*-100 }}&nbsp;% <status>verlangsamt</status>. Die Verlangsamung fällt im Verlauf von {{ slowduration }}&nbsp;Sekunden ab.<br /><br />Aurora hüpft nach dem Wirken ein kleines Stück nach hinten.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AuroraE.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuroraR", "name": "Zwischen den Welten", "description": "Aurora springt in eine beliebige Richtung und entfesselt eine <PERSON>welle, die getroffenen Gegnern Schaden zufügt und sie verlangsamt. Anschließend erzeugt sie einen Bereich, in dem Gegner verlangsamt werden, und der es Aurora ermöglicht, sich von einer Seite zur anderen zu teleportieren.", "tooltip": "Aurora springt in eine Richtung. Bei der Landung vereint sie beide Welten und setzt eine Welle aus Geistenergie frei, die Gegnern <magicDamage>{{ damagecalc }}&nbsp;magischen Schaden</magicDamage> zufügt und sie 2&nbsp;Sekunden lang um {{ slowpercent*-100 }}&nbsp;% <status>verlangsamt</status>.<br /><br />Diese Überlagerung der Welten bleibt {{ areaduration }}&nbsp;Sekunden lang aktiv, wodurch Aurora <keywordMajor>Weltenhopser</keywordMajor> erhält und sie einem Rand des Bereichs zum anderen springen kann.<br /><br /><PERSON><PERSON><PERSON>, die den Bereich verlassen oder betreten wollen, werden {{ stunduration }}&nbsp;Sekunden lang um {{ exitslowpercent*-100 }}&nbsp;% <status>verlangsamt</status>.<br /><br />Aurora kann diese Fähigkeit <recast>erneut wirken</recast>, um ihren Effekt frühzeitig zu beenden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ areaduration }} -> {{ areadurationNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "AuroraR.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Abschwörung der Geister", "description": "Auroras Fähigkeiten und Angriffe befreien Geister aus Gegnern, denen sie Schaden zufügt. Die ausgetriebenen Geister folgen Aurora überall hin und heilen sie.", "image": {"full": "AuroraPassive.Aurora.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}