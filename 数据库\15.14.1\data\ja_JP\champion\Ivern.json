{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ivern": {"id": "Ivern", "key": "427", "name": "アイバーン", "title": "豊緑の神秘", "image": {"full": "Ivern.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "427000", "num": 0, "name": "default", "chromas": false}, {"id": "427001", "num": 1, "name": "お菓子の王様アイバーン", "chromas": false}, {"id": "427002", "num": 2, "name": "ダンクマスター アイバーン", "chromas": true}, {"id": "427011", "num": 11, "name": "古の神アイバーン", "chromas": true}, {"id": "427020", "num": 20, "name": "宇宙飛行士アイバーン", "chromas": true}, {"id": "427030", "num": 30, "name": "精霊の花祭りアイバーン", "chromas": false}], "lore": "「豊緑の神秘」の名で広く知られるアイバーン・ブランブルフットは、ルーンテラの森を渡り歩きながら行く先々で生命の種を蒔く、奇妙な半人半樹である。彼は自然界の秘密に精通しており、ありとあらゆる草木、花鳥、昆虫たちと深い友情を結んでいる。アイバーンは荒野をさすらい、出会うもの皆に奇妙な知恵を授け、森に滋養を与え、時には口の軽いチョウチョを信用して秘密を教えてしまう。", "blurb": "「豊緑の神秘」の名で広く知られるアイバーン・ブランブルフットは、ルーンテラの森を渡り歩きながら行く先々で生命の種を蒔く、奇妙な半人半樹である。彼は自然界の秘密に精通しており、ありとあらゆる草木、花鳥、昆虫たちと深い友情を結んでいる。アイバーンは荒野をさすらい、出会うもの皆に奇妙な知恵を授け、森に滋養を与え、時には口の軽いチョウチョを信用して秘密を教えてしまう。", "allytips": ["「ネッコナゲ」が命中した敵へ飛び込む味方に、「タネバクダン」を使って援護しよう！", "「シゲミヅクリ」で奇襲のための場所を作り出そう！", "「デイジー」は方向指定攻撃をブロックして敵の追撃を遅らせることができる。彼女を使ってチームメイトを救い出そう！"], "enemytips": ["アイバーンは見かけによらず逃げるのが得意だ。遠くまで追いかける時は気を付けよう。", "アイバーンの茂みは持続時間が長い。奇襲に気を付けよう！", "一人でアイバーンと戦う時は、「デイジー」が使用可能かどうかに注意が必要だ！"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 450, "mpperlevel": 60, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 475, "hpregen": 7, "hpregenperlevel": 0.85, "mpregen": 6, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 3.4, "attackspeed": 0.644}, "spells": [{"id": "IvernQ", "name": "ネッコナゲ", "description": "魔法の根を飛ばし、命中した敵ユニットにダメージを与えてスネア状態にする。味方はスネア状態になった敵に向かってダッシュできる。", "tooltip": "魔法の根を飛ばし、最初に命中した敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ rootduration }}秒間の<status>スネア効果</status>を付与する。<status>スネア効果</status>を受けている敵に通常攻撃を行う味方は、攻撃射程内までダッシュする。<br /><br /><recast>再発動:</recast> <status>スネア効果</status>を与えた敵まで自身がダッシュする。<br /><br /><rules>エピック以外のモンスターに命中させると<spellName>「ネッコナゲ」</spellName>のクールダウンが50%短縮される。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["スネア効果時間", "ダメージ", "クールダウン"], "effect": ["{{ rootduration }} -> {{ rootdurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1125, 1125, 1125, 1125, 1125], "rangeBurn": "1125", "image": {"full": "IvernQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "IvernW", "name": "シゲミヅクリ", "description": "茂みの中にいると、自身および周囲の味方の通常攻撃が追加魔法ダメージを与える。このスキルを発動すると茂みを作り出せる。", "tooltip": "<spellPassive>自動効果:</spellPassive> 茂みの中にいる間と茂みを出てから{{ buffduration }}秒間は、通常攻撃が<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を追加で与える。周囲の味方はこの効果を{{ allybuffduration }}秒間獲得して、<magicDamage>{{ totalallydamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><spellActive>発動効果:</spellActive> {{ revealduration }}秒間持続する茂みを発生させ、その地点を可視化する。この茂みは味方チームが内部の視界を失うか、{{ maxbrushduration }}秒経過すると消滅する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["魔法ダメージ", "味方の魔法ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ allybasedamage }} -> {{ allybasedamageNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "3", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "IvernW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "IvernE", "name": "タネバクダン", "description": "味方にシールドを付与する。少しすると爆発して、周囲の敵にスロウ効果とダメージを与える。敵に命中しなかった場合は、シールドがリフレッシュされる。", "tooltip": "味方チャンピオンまたはデイジーに<shield>耐久値{{ totalshield }}のシールド</shield>を付与する。シールドは{{ shieldduration }}秒後に爆発して、敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />シールドがまだ残っている状態で<spellName>「タネバクダン」</spellName>が爆発し、その爆発が敵チャンピオンに命中しなかった場合、対象の味方は{{ shieldduration }}秒間、耐久値<shield>{{ totalshield }}</shield>のシールドを獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "IvernE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "IvernR", "name": "デイジー！", "description": "守護者である友達のデイジーを召喚して、一緒に戦わせる。再発動するとデイジーに攻撃または移動を指示できる。", "tooltip": "守護者である友達のデイジーを召喚して、{{ daisyduration }}秒間戦闘に参加させる。<br /><br /><spellActive>デイジー、叩きつけて！:</spellActive> デイジーが同じチャンピオンまたはエピックモンスターを3回連続で攻撃すると、衝撃波が発生する。衝撃波は命中したすべての敵に<magicDamage>{{ totalshockwavedamage }}の魔法ダメージ</magicDamage>を与え、{{ shockwaveccduration }}秒間<status>ノックアップ</status>させる。この効果は{{ shockwavecd }}秒に1回しか発生しない。<br /><br /><recast>再発動:</recast> デイジーに攻撃または移動を指示する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃力", "衝撃波ダメージ", "デイジーの増加攻撃速度", "クールダウン"], "effect": ["{{ daisyad }} -> {{ daisyadNL }}", "{{ shockwavebasedamage }} -> {{ shockwavebasedamageNL }}", "{{ daisyas }}% -> {{ daisyasNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 130, 120], "cooldownBurn": "140/130/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "IvernR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "モリノトモダチ", "description": "アイバーンはエピック以外のモンスターに対して、攻撃することも攻撃されることもない。ジャングルキャンプに時間が経つことで成長する小さな魔法の森を作ることができる。小さな森が完全に成長すると、モンスターを逃がしてゴールドと経験値を獲得できる。", "image": {"full": "IvernP.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}