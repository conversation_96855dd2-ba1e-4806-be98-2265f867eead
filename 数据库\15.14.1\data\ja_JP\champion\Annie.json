{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Annie": {"id": "<PERSON>", "key": "1", "name": "アニー", "title": "闇の申し子", "image": {"full": "Annie.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "1000", "num": 0, "name": "default", "chromas": false}, {"id": "1001", "num": 1, "name": "ゴス アニー", "chromas": false}, {"id": "1002", "num": 2, "name": "赤ずきんアニーちゃん", "chromas": false}, {"id": "1003", "num": 3, "name": "不思議な国の姫アニー", "chromas": false}, {"id": "1004", "num": 4, "name": "プロムクイーン アニー", "chromas": false}, {"id": "1005", "num": 5, "name": "氷炎のアニー", "chromas": false}, {"id": "1006", "num": 6, "name": "取りかえっこアニー", "chromas": false}, {"id": "1007", "num": 7, "name": "フランケンくまアニー", "chromas": false}, {"id": "1008", "num": 8, "name": "パンダ アニー", "chromas": false}, {"id": "1009", "num": 9, "name": "スイートハート アニー", "chromas": false}, {"id": "1010", "num": 10, "name": "ヘクステック アニー", "chromas": false}, {"id": "1011", "num": 11, "name": "超銀河アニー", "chromas": false}, {"id": "1012", "num": 12, "name": "アニーバーサリー", "chromas": false}, {"id": "1013", "num": 13, "name": "月の聖獣アニー", "chromas": true}, {"id": "1022", "num": 22, "name": "カフェキューティーズ アニー", "chromas": false}, {"id": "1031", "num": 31, "name": "恐怖の夜アニー", "chromas": false}, {"id": "1040", "num": 40, "name": "冬の祝福アニー", "chromas": false}, {"id": "1050", "num": 50, "name": "バトルプリンセス アニー", "chromas": false}], "lore": "危険極まりなく、それでいて無邪気でおませなアニーは、強力な発火能力を持つ小さな魔女だ。ノクサスの北に位置する山々の影に忍んでいてもなお、アニーは異端者として果てしない孤独の中に生きている。生まれ持った炎を操る力は、彼女が感情をほとばしらせることになった思いがけない出来事がきっかけで目覚めた。そして次第に、アニーは「火遊び」のやり方を覚えていく。 アニーのお気に入りはクマのぬいぐるみのティバーズで、呼べばすぐにそばに来て、炎で彼女を守ってくれる。永遠に無垢な子供のままであるアニーは、暗い森をさまよい続ける──いつも、一緒に遊んでくれる誰かを探しながら。", "blurb": "危険極まりなく、それでいて無邪気でおませなアニーは、強力な発火能力を持つ小さな魔女だ。ノクサスの北に位置する山々の影に忍んでいてもなお、アニーは異端者として果てしない孤独の中に生きている。生まれ持った炎を操る力は、彼女が感情をほとばしらせることになった思いがけない出来事がきっかけで目覚めた。そして次第に、アニーは「火遊び」のやり方を覚えていく。 アニーのお気に入りはクマのぬいぐるみのティバーズで、呼べばすぐにそばに来て、炎で彼女を守ってくれる。永遠に無垢な子供のままであるアニーは、暗い森をさまよい続け...", "allytips": ["アルティメットスキルでスタン攻撃を繰り出せば、集団戦の形勢を一変させることも可能。", "ゲーム序盤は「ファイアボール」でミニオンを倒すことで、効率よくゴールドを稼ぐことができる。", "「モルテンシールド」は固有スキルのチャージに使用すると効果的。序盤で1ランクだけでも習得しておくと便利だ。"], "enemytips": ["アニーが召喚するクマのティバーズは、周囲にいる敵ユニットに炎でダメージを与える。ティバーズが召喚されたら近づきすぎないよう注意。", "ティバーズを攻撃する際は、サモナースペルの「スマイト」が有効。", "アニーの周りに白いエネルギーの渦が出現したら注意。彼女の次のスキルに、スタン効果が付与される。"], "tags": ["Mage", "Support"], "partype": "マナ", "info": {"attack": 2, "defense": 3, "magic": 10, "difficulty": 6}, "stats": {"hp": 560, "hpperlevel": 96, "mp": 418, "mpperlevel": 25, "movespeed": 335, "armor": 23, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 625, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 2.65, "attackspeedperlevel": 1.36, "attackspeed": 0.61}, "spells": [{"id": "AnnieQ", "name": "ファイアボール", "description": "火の玉を放って、指定した対象にダメージを与える。このスキルで敵ユニットを倒すと、消費したマナが回復しクールダウンが短縮する。", "tooltip": "火の玉を放って<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。対象をキルすると、消費したマナコストが回復して、クールダウンが50%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "AnnieQ.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AnnieW", "name": "バーニングファイア", "description": "指定方向に扇状の炎を放ち、範囲内のすべての敵ユニットにダメージを与える。", "tooltip": "炎を放射して<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieW.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON><PERSON>", "name": "モルテンシールド", "description": "自身または味方1体にシールドを付与し、移動速度が一時的に上昇する。また、通常攻撃かスキルで自身を攻撃した敵にダメージを与える。", "tooltip": "味方チャンピオン1体に{{ shieldduration }}秒間<shield>耐久値{{ shieldblocktotal }}のシールド</shield>を付与し、{{ movementspeedduration }}秒間<speed>移動速度を{{ movespeedcalc }}</speed>増加させる。増加した移動速度は徐々に元に戻る。シールドを受けた味方は、シールドが持続している間に敵から通常攻撃またはスキルで攻撃されると、その敵に<magicDamage>{{ damagereturn }}の魔法ダメージ</magicDamage>を与える。このダメージは1回のシールドにつき1度だけ発生する。<br /><br />ティバーズは召喚時に必ず<spellName>「モルテンシールド」</spellName>の効果を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "ダメージ反射", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ shieldamount }} -> {{ shieldamountNL }}", "{{ damagereflection }} -> {{ damagereflectionNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AnnieE.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AnnieR", "name": "やっちゃえ！ティバーズ！", "description": "クマのティバーズを召喚し、範囲内の敵ユニットにダメージを与える。召喚されたティバーズは敵ユニットを攻撃し、身にまとう炎で周囲にいる敵に継続ダメージを与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> {{ rpercentpenbuff*100 }}%の魔法防御貫通を獲得する。<br /><br />クマのティバーズを召喚し、<magicDamage>{{ initialburstdamage }}の魔法ダメージ</magicDamage>を与える。その後{{ tibberslifetime }}秒間、ティバーズは周囲の敵を燃やして毎秒<magicDamage>{{ tibbersauradamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />ティバーズは召喚された時、アニーが敵チャンピオンをスタンさせた時、およびアニーが倒された時に激怒する。ティバーズは激怒すると、<attackSpeed>攻撃速度が275%</attackSpeed>、<speed>移動速度が100%</speed>増加する。この移動速度は3秒かけて元に戻る。<br /><br /><recast>再発動:</recast> ティバーズにマニュアルで指示を出す。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "炎ダメージ", "攻撃力", "増加移動速度", "クールダウン", "魔法防御貫通"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ auradamage }} -> {{ auradamageNL }}", "{{ tibbersattackdamage }} -> {{ tibbersattackdamageNL }}", "{{ tibbersbonusms }} -> {{ tibbersbonusmsNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rpercentpenbuff*100.000000 }}% -> {{ rpercentpenbuffnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieR.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "火遊びだいすき", "description": "スキルを4回使用すると、次に行う攻撃スキルが対象をスタンさせる。<br><br>試合開始時および復活時は「火遊びだいすき」が使用可能な状態でスタートする。", "image": {"full": "Annie_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}