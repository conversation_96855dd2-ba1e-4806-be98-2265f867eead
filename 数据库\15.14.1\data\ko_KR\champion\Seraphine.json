{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Seraphine": {"id": "<PERSON><PERSON><PERSON>", "key": "147", "name": "세라핀", "title": "노래하는 별", "image": {"full": "Seraphine.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "147000", "num": 0, "name": "default", "chromas": false}, {"id": "147001", "num": 1, "name": "K/DA ALL OUT 세라핀 인디", "chromas": false}, {"id": "147002", "num": 2, "name": "K/DA ALL OUT 세라핀 떠오르는 별", "chromas": false}, {"id": "147003", "num": 3, "name": "K/DA ALL OUT 세라핀 슈퍼스타", "chromas": false}, {"id": "147004", "num": 4, "name": "우아한 불사조 세라핀", "chromas": true}, {"id": "147014", "num": 14, "name": "바다의 노래 세라핀", "chromas": true}, {"id": "147015", "num": 15, "name": "프레스티지 바다의 노래 세라핀", "chromas": false}, {"id": "147024", "num": 24, "name": "요정 왕국 세라핀", "chromas": true}, {"id": "147034", "num": 34, "name": "별 수호자 세라핀", "chromas": true}, {"id": "147043", "num": 43, "name": "전투 종달새 세라핀", "chromas": true}, {"id": "147050", "num": 50, "name": "달콤 달링 세라핀", "chromas": true}], "lore": "자운 출신의 부모를 둔 세라핀은 필트오버에서 태어나 다른 사람의 영혼의 소리를 들을 수 있다. 세상이 그녀에게 노래하고 그녀 또한 답가를 불렀다. 어린 시절에는 이 소리가 그녀를 억눌렀지만 이제 그녀는 영감을 위해 소리를 이끌어내고 혼돈을 협화음으로 바꿀 수 있게 되었다. 세라핀은 두 도시를 위해 공연하며 시민들에게 그들은 혼자가 아니고 함께일 때 더 강하며, 그들의 잠재력은 무한하다는 것을 일깨워준다.", "blurb": "자운 출신의 부모를 둔 세라핀은 필트오버에서 태어나 다른 사람의 영혼의 소리를 들을 수 있다. 세상이 그녀에게 노래하고 그녀 또한 답가를 불렀다. 어린 시절에는 이 소리가 그녀를 억눌렀지만 이제 그녀는 영감을 위해 소리를 이끌어내고 혼돈을 협화음으로 바꿀 수 있게 되었다. 세라핀은 두 도시를 위해 공연하며 시민들에게 그들은 혼자가 아니고 함께일 때 더 강하며, 그들의 잠재력은 무한하다는 것을 일깨워준다.", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 25, "movespeed": 330, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.669}, "spells": [{"id": "SeraphineQ", "name": "고음", "description": "세라핀이 대상 지역에 피해를 입힙니다.", "tooltip": "세라핀이 맑은 음을 노래해 <magicDamage>{{ explosiondamage }}의 마법 피해</magicDamage>를 입힙니다. 챔피언 대상이 잃은 체력에 비례해 피해량이 증가하며 대상의 체력이 {{ executethreshold*100 }}% 이하일 때까지 최대 <magicDamage>{{ totalempowereddamage }}</magicDamage>의 피해를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최초 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "SeraphineQ.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SeraphineW", "name": "소리 장막", "description": "세라핀이 주변 아군에게 보호막과 이동 속도를 부여합니다. 세라핀에게 이미 보호막이 있으면 주변 아군의 체력을 회복시킵니다.", "tooltip": "세라핀이 노래로 주변 아군을 감싸 {{ shieldduration }}초 동안 아군의 <speed>이동 속도가 {{ hastevalueallies }}</speed> 상승하고, 자신의 <speed>이동 속도가 {{ wmsbonustotal }}</speed> 상승하며 <shield>{{ shieldvalueseraphine }}의 피해를 흡수하는 보호막</shield>을 얻습니다. 이동 속도는 점차 원래대로 돌아옵니다.<br /><br />세라핀에게 이미 <shield>보호막</shield>이 있으면 아군을 불러모아 {{ whealsplitdelay }}초 후 주변 아군 챔피언 하나당 <healing>잃은 체력의 {{ wmissinghpheal }}%</healing>만큼 체력을 회복시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "회복 %", "소모값 @AbilityResourceName@"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ wmissinghpbase }} -> {{ wmissinghpbaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 22, 22, 22, 22], "cooldownBurn": "22", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SeraphineW.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SeraphineE", "name": "비트 발사", "description": "세라핀이 일직선상에 있는 적들에게 피해를 입히고 이동을 방해합니다.", "tooltip": "세라핀이 묵직한 음파를 발사하여 일직선상에 있는 적들에게 <magicDamage>{{ finaldamage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ slowvalue }}% <status>둔화</status>시킵니다.<br /><br />이미 <status>둔화</status>된 적들은 <status>속박</status>되며, <status>이동 불가</status> 상태인 적들은 <status>기절</status>합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화 지속시간", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1300, 1300, 1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "SeraphineE.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SeraphineR", "name": "앙코르", "description": "세라핀이 적중한 적에게 피해를 입히고 매혹합니다. 아군 또는 적 챔피언에게 적중하면 사거리가 초기화됩니다.", "tooltip": "무대를 장악한 세라핀이 사로잡는 힘을 날려 {{ rchannelduration }}초 동안 적을 <status>매혹</status>하고 <magicDamage>{{ r1totaldamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />챔피언(아군, 적 무관)에게 적중 시 이 스킬의 사거리가 늘어나고 아군 챔피언은 <keywordMajor>음표</keywordMajor> 중첩을 최대로 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최초 피해량", "매혹 지속시간", "재사용 대기시간"], "effect": ["{{ r1basedamage }} -> {{ r1basedamageNL }}", "{{ rchannelduration }} -> {{ rchanneldurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SeraphineR.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "무대 장악", "description": "세라핀이 세 번째 기본 스킬을 사용할 때마다 두 번 사용됩니다. 추가로 아군 근처에서 스킬을 사용하면 다음 기본 공격이 추가 마법 피해를 입히고 사거리가 증가합니다.", "image": {"full": "Seraphine_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}