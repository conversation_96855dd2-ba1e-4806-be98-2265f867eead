{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Morgana": {"id": "<PERSON><PERSON>", "key": "25", "name": "<PERSON><PERSON>", "title": "the Fallen", "image": {"full": "Morgana.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "25000", "num": 0, "name": "default", "chromas": true}, {"id": "25001", "num": 1, "name": "Exiled <PERSON><PERSON>", "chromas": false}, {"id": "25002", "num": 2, "name": "Sinful Succulence Morgana", "chromas": false}, {"id": "25003", "num": 3, "name": "Blade Mistress <PERSON>", "chromas": false}, {"id": "25004", "num": 4, "name": "Blackthorn Morgan<PERSON>", "chromas": true}, {"id": "25005", "num": 5, "name": "Ghost Bride <PERSON>", "chromas": false}, {"id": "25006", "num": 6, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "25010", "num": 10, "name": "Lunar Wraith Morgan<PERSON>", "chromas": false}, {"id": "25011", "num": 11, "name": "Bewitching <PERSON><PERSON>", "chromas": true}, {"id": "25017", "num": 17, "name": "Majestic Empress <PERSON><PERSON>", "chromas": true}, {"id": "25026", "num": 26, "name": "Coven Morgana", "chromas": true}, {"id": "25039", "num": 39, "name": "Dawnbringer Morgana", "chromas": true}, {"id": "25041", "num": 41, "name": "Prestige Bewitching Morgana", "chromas": false}, {"id": "25050", "num": 50, "name": "Star Nemesis Morgana", "chromas": true}, {"id": "25060", "num": 60, "name": "<PERSON>", "chromas": true}, {"id": "25070", "num": 70, "name": "Porcelain Morgana", "chromas": false}, {"id": "25080", "num": 80, "name": "Spirit Blossom <PERSON><PERSON>", "chromas": false}], "lore": "Conflicted between her celestial and mortal natures, <PERSON><PERSON> bound her wings to embrace humanity, and inflicts her pain and bitterness upon the dishonest and the corrupt. She rejects laws and traditions she believes are unjust, and fights for truth from the shadows of <PERSON><PERSON><PERSON>—even as others seek to repress it—by casting shields and chains of dark fire. More than anything else, <PERSON><PERSON> truly believes that even the banished and outcast may one day rise again.", "blurb": "Conflicted between her celestial and mortal natures, <PERSON><PERSON> bound her wings to embrace humanity, and inflicts her pain and bitterness upon the dishonest and the corrupt. She rejects laws and traditions she believes are unjust, and fights for truth from...", "allytips": ["Shrewd use of Black Shield can determine the outcome of team fights.", "Items that provide survivability allow <PERSON><PERSON> to become extremely difficult to kill in conjunction with Black Shield and Soul Shackles.", "Tormented Shadow is an excellent farming tool if you're by yourself in a lane."], "enemytips": ["Tormented Shadow deals tons of damage to units missing large amounts of Health. When low on Health, be wary of Morgana's attempts to trap you within its reach.", "<PERSON><PERSON> often needs to land Dark Binding to setup her other attacks. Use your minions as shields against Dark Binding.", "Black Shield makes <PERSON><PERSON> immune to disabling effects, but can be broken with magic damage."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 6, "magic": 8, "difficulty": 1}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 340, "mpperlevel": 60, "movespeed": 335, "armor": 25, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 5.5, "hpregenperlevel": 0.4, "mpregen": 11, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.53, "attackspeed": 0.625}, "spells": [{"id": "MorganaQ", "name": "Dark Binding", "description": "<PERSON><PERSON> binds an enemy in place with dark magic, forcing them to feel the pain they've caused and dealing magic damage. ", "tooltip": "<PERSON><PERSON> hurls a blast of starfire that <status>Roots</status> the first enemy hit for {{ rootduration }} seconds and deals <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Root Duration", "@AbilityResourceName@ Cost"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "MorganaQ.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MorganaW", "name": "Tormented Shadow", "description": "<PERSON><PERSON> casts a cursed shadow on an area, damaging enemies who dare stand in her dark circle. They receive magic damage over time, which increases the lower health they are.", "tooltip": "<PERSON><PERSON> ignites the ground for {{ wduration }} seconds, dealing <magicDamage>{{ totalmindamage }} magic damage</magicDamage> per second, increased by up to <magicDamage>{{ totalmaxdamage }}</magicDamage> based on the targets' missing Health.<br /><br />This Ability's Cooldown is reduced by {{ cdrefundpercent*100 }}% every time <PERSON><PERSON> is healed by <spellName>Soul Siphon</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per Second", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [70, 85, 100, 115, 130], "costBurn": "70/85/100/115/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "MorganaW.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MorganaE", "name": "Black Shield", "description": "<PERSON><PERSON> anoints an ally with a protective barrier of starfire, which absorbs magical damage and disabling effects until it is broken.", "tooltip": "<PERSON><PERSON> grants an ally champion <shield>{{ totalshieldstrength }} Magic Shield</shield> for {{ shieldduration }} seconds. The shield prevents <status>Disabling</status> and <status>Immobilizing</status> effects until it breaks.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Shield Amount", "Cooldown"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 23.5, 21, 18.5, 16], "cooldownBurn": "26/23.5/21/18.5/16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "MorganaE.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MorganaR", "name": "Soul Shackles", "description": "<PERSON><PERSON> unleashes the full force of her Celestial power as she unbinds her wings and hovers above the ground. She lashes chains of dark pain onto nearby enemy champions, gaining Move Speed. The chains slow and deal initial damage and, after a delay, stun those who are unable to break them.", "tooltip": "<PERSON><PERSON> chains herself to nearby enemy Champions, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> and <status>Slowing</status> them by {{ slowpercent }}%. After {{ chainduration }} seconds, enemies unable to break the chains take an additional <magicDamage>{{ totaldamage }} magic damage</magicDamage> and are <status>Stunned</status> for {{ stunduration }} seconds.<br /><br />While casting this Ability, <PERSON><PERSON> gains <speed>{{ hastepercent }}% Move Speed</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Move Speed", "Stun Duration:", "Cooldown"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ hastepercent }}% -> {{ hastepercentNL }}%", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "MorganaR.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Soul Siphon", "description": "<PERSON><PERSON> drains spirit from her enemies, healing as she deals damage to champions, large minions, and medium and larger jungler monsters.", "image": {"full": "FallenAngel_Empathize.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}