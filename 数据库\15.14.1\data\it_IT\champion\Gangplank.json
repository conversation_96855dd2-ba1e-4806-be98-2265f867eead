{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gangplank": {"id": "Gangplank", "key": "41", "name": "Gangplank", "title": "il flagello dei mari", "image": {"full": "Gangplank.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "41000", "num": 0, "name": "default", "chromas": false}, {"id": "41001", "num": 1, "name": "Gangplank Spettrale", "chromas": false}, {"id": "41002", "num": 2, "name": "Gangplank Milizia", "chromas": false}, {"id": "41003", "num": 3, "name": "Gangplank Marinaio", "chromas": false}, {"id": "41004", "num": 4, "name": "Gangplank Soldatino <PERSON>", "chromas": false}, {"id": "41005", "num": 5, "name": "Gangplank Forze Speciali", "chromas": false}, {"id": "41006", "num": 6, "name": "Gangplank Sultano", "chromas": false}, {"id": "41007", "num": 7, "name": "Capitano Gangplank", "chromas": false}, {"id": "41008", "num": 8, "name": "Gangplank Nova del Terrore", "chromas": true}, {"id": "41014", "num": 14, "name": "Gangplank Festa in Piscina", "chromas": true}, {"id": "41021", "num": 21, "name": "Gangplank FPX", "chromas": true}, {"id": "41023", "num": 23, "name": "Gangplank il Traditore", "chromas": true}, {"id": "41033", "num": 33, "name": "PROGETTO: Gangplank", "chromas": true}], "lore": "Brutale e imprevedibile, il re detronizzato Gangplank è temuto in tutto il mondo. Un tempo regnava sulla città portuale di Bilgewater e, anche se la situazione è cambiata, molti credono che sia diventato ancora più pericoloso. Gangplank preferirebbe vedere Bilgewater annegare nel sangue, che lasciarla a qualcun altro. Ora, armato di pistola, sciabola e barili di polvere da sparo, è determinato a reclamare ciò che ha perso.", "blurb": "Brutale e imprevedibile, il re detronizzato Gangplank è temuto in tutto il mondo. Un tempo regnava sulla città portuale di Bilgewater e, anche se la situazione è cambiata, molti credono che sia diventato ancora più pericoloso. Gangplank preferirebbe...", "allytips": ["Parrrlé applica gli effetti sul colpo come Martello ghiacciato o Mannaia oscura.", "Se fai attenzione ai nemici con la salute bassa, dovunque siano sulla mappa, potresti sorprenderli con un'uccisione con Sbarramento d'artiglieria.", "Prova a piazzare Sbarramento d'artiglieria sulla via di fuga dei nemici per tagliargliela."], "enemytips": ["Parrrlé infligge una alta quantità di danni fisici. Gli oggetti che conferiscono armatura aiutano, se un Gangplank nemico sta andando forte.", "<PERSON>uan<PERSON> Gangplank raggiunge il livello 6, attenti alla sua suprema con portata globale, Sbarramento d'artiglieria!"], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 6, "magic": 4, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 280, "mpperlevel": 60, "movespeed": 345, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 4.2, "attackspeedperlevel": 3.2, "attackspeed": 0.658}, "spells": [{"id": "GangplankQWrapper", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Spara al bersaglio, razziando oro per ogni unità nemica uccisa.", "tooltip": "{{Spell_GangplankQWrapper_Tooltip_{{ gamemodeinteger }}}}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Saccheggio oro", "Saccheggio Serpenti d'argento", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e5 }} -> {{ e5NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4.5, 4.5, 4.5, 4.5, 4.5], "cooldownBurn": "4.5", "cost": [50, 45, 40, 35, 30], "costBurn": "50/45/40/35/30", "datavalues": {}, "effect": [null, [10, 40, 70, 100, 130], [3, 4, 5, 6, 7], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 5, 6, 7, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/40/70/100/130", "3/4/5/6/7", "0", "500", "4/5/6/7/8", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "GangplankQWrapper.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankW", "name": "R<PERSON><PERSON>vi scorbuto", "description": "Mangiare arance cura gli effetti di controllo e ripristina salute.", "tooltip": "Gangplank consuma una grande quantità di arance che rimuove qualsiasi effetto di <status>impedimento</status> e guarisce <healing>{{ basehealth }} più {{ e2 }}% della sua salute mancante</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [45, 70, 95, 120, 145], [13, 13, 13, 13, 13], [0.25, 0.25, 0.25, 0.25, 0.25], [200, 200, 200, 200, 200], [30, 40, 50, 60, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/70/95/120/145", "13", "0.25", "200", "30/40/50/60/70", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GangplankW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankE", "name": "<PERSON>le di polvere da sparo", "description": "Gangplank svela un Barile di polvere da sparo nella posizione bersaglio. Se lo attacca, esplode, diffondendo i danni dell'attacco ai nemici nell'area e rallentandoli.", "tooltip": "Gangplank piazza un barile di polvere da sparo che può essere attaccato da lui e dai campioni nemici per {{ e5 }} secondi. Se un nemico lo distrugge, il barile viene disinnescato. Se è Gangplank a distruggerlo, il barile esplode, <status>rallentando</status> i nemici di {{ finalslowamount }}% per {{ e2 }} secondi e infliggendo <physicalDamage>attacco fisico</physicalDamage>, che ignora {{ e0 }}% di armatura. I campioni subiscono <physicalDamage>{{ e3 }} danni fisici</physicalDamage> aggiuntivi.<br /><br />La salute del barile diminuisce ogni {{ f5 }} secondi. La detonazione di un barile fa scoppiare gli altri coinvolti nell'esplosione, che però non infliggono danni allo stesso bersaglio più di una volta. Le esplosioni dei barili innescate da <spellName>Parrrlé</spellName> conferiscono oro bonus per i bersagli uccisi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni bonus contro i campioni", "Cariche massime", "Rallentamento", "Tempo di ricarica"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ barrelslow }}% -> {{ barrelslowNL }}%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [3, 3, 4, 4, 5], [2, 2, 2, 2, 2], [75, 105, 135, 165, 195], [40, 50, 60, 70, 80], [25, 25, 25, 25, 25], [2, 2, 2, 2, 2], [10, 10, 10, 10, 10], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 40, 40, 40, 40]], "effectBurn": [null, "3/3/4/4/5", "2", "75/105/135/165/195", "40/50/60/70/80", "25", "2", "10", "100", "0.5", "40"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "3", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GangplankE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GangplankR", "name": "Sbarramento d'artiglieria", "description": "Gangplank ordina alla sua nave di aprire il fuoco su un'area, rallentando e danneggiando i nemici.", "tooltip": "Gangplank segnala alla sua nave di sparare {{ totalwavestooltip }} bordate di palle di cannone in un'area qualsiasi della mappa per {{ zoneduration }} secondi. Ciascuna bordata <status>rallenta</status> di un {{ slowpercent }}% per {{ slowduration }} secondi e infligge <magicDamage>{{ onewavedamage }} danni magici</magicDamage>. Danni massimi: {{ totaldamagetooltip }}<br /><br />Questa abilità può essere potenziata nell'emporio usando <spellName>Parrrlé</spellName>.<br /><br /><spellName>Fuoco a volontà</spellName>: Spara 6 ulteriori bordate di palle di cannone.<br /><spellName>Figlia della Morte</spellName>: Spara una mega palla di cannone che infligge <trueDamage>{{ deathsdaughterdamage }} danni puri</trueDamage> e {{ deathsdaughterslow }}% <status>rallentamento</status> per {{ deathsdaughterslowduration }} secondo/i.<br /><spellName>Morale alto</spellName>: Gli alleati all'interno di Sbarramento d'artiglieria ottengono <speed>{{ raisemoralehaste }}% velocità di movimento</speed> per {{ raisemoralehasteduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> per ondata", "Ricarica"], "effect": ["{{ damageperwave }} -> {{ damageperwaveNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1.5, 0.5, 0.5], [0, 0, 0], [300, 300, 300], [75, 75, 75], [1, 1, 1], [40, 40, 40]], "effectBurn": [null, "0", "0", "0", "0", "1.5/0.5/0.5", "0", "300", "75", "1", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [30000, 30000, 30000], "rangeBurn": "30000", "image": {"full": "GangplankR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Prova del fuoco", "description": "Ogni manciata di secondi, i colpi in mischia di Gangplank incendiano gli avversari.", "image": {"full": "Gangplank_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}