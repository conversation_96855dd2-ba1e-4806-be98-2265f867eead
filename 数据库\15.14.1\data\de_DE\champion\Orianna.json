{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Orianna": {"id": "<PERSON><PERSON><PERSON>", "key": "61", "name": "<PERSON><PERSON><PERSON>", "title": "die Aufziehpuppe", "image": {"full": "Orianna.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "61000", "num": 0, "name": "default", "chromas": false}, {"id": "61001", "num": 1, "name": "Gothic-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61002", "num": 2, "name": "Chaospuppe-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61003", "num": 3, "name": "Klingenwerk-Orianna", "chromas": false}, {"id": "61004", "num": 4, "name": "TPA-Orianna", "chromas": false}, {"id": "61005", "num": 5, "name": "Winterwunder-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61007", "num": 7, "name": "Sternenvernichter-<PERSON><PERSON>na", "chromas": true}, {"id": "61008", "num": 8, "name": "<PERSON>eg<PERSON><PERSON>", "chromas": true}, {"id": "61011", "num": 11, "name": "Poolparty-Orianna", "chromas": true}, {"id": "61020", "num": 20, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "61029", "num": 29, "name": "Sternenwächterin Orianna", "chromas": true}, {"id": "61038", "num": 38, "name": "T1-<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Einst war Orianna ein neugieriges Mädchen aus Fleisch und Blut, heute ist sie ein technisches Meisterwerk, das vollständig aus mechanischen Bauteilen zusammengesetzt ist. Nach einem Unfall in den unteren Sektoren von <PERSON> wurde sie schwer krank und ihr versagender Körper musste Stück für Stück durch ausgefeiltes Handwerk ersetzt werden. Mit einer außergewöhnlichen Messingkugel, die sie als Begleiter und Beschützer konstruierte, erkundet <PERSON>ianna nun die Geheimnisse und Wunder von Piltover und darüber hinaus.", "blurb": "Einst war Orianna ein neugieriges Mädchen aus Fleisch und Blut, heute ist sie ein technisches Meisterwerk, das vollständig aus mechanischen Bauteilen zusammengesetzt ist. Nach einem Unfall in den unteren Sektoren von <PERSON> wurde sie schwer krank und ihr...", "allytips": ["„Befehl: Besch<PERSON>tz<PERSON>“ kann auf Orianna selbst angewendet werden, damit die Kugel schnell zu ihr zurückkehrt. Zusammen mit „Befehl: Angriff“ lassen sich so <PERSON>ner leicht beharken.", "„Befehl: Dissonanz“ ist sehr gut für eine Flucht geeignet, wenn Orianna die Kugel besitzt. Die Kombination aus erhöhtem Lauftempo und verlangsamendem Hindernis kann sich als sehr mächtig erweisen.", "„Befehl: <PERSON><PERSON><PERSON><PERSON><PERSON>“ kann mit der richtigen Positionierung der Kugel Gegner von Orianna weg- oder auf sie zuschleudern."], "enemytips": ["<PERSON><PERSON><PERSON> kann nur auf den Bereich um ihre Kugel einwirken. <PERSON><PERSON>e dies zu deinem Vorteil.", "<PERSON><PERSON><PERSON> da<PERSON>, wenn Oriannas <PERSON> zu ihr zurückkehrt, weil sie sich zu weit entfernt hat. Dies kann zu unerwarteten Situationen führen."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 3, "magic": 9, "difficulty": 7}, "stats": {"hp": 585, "hpperlevel": 110, "mp": 418, "mpperlevel": 25, "movespeed": 325, "armor": 20, "armorperlevel": 4.2, "spellblock": 26, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 44, "attackdamageperlevel": 2.6, "attackspeedperlevel": 3.5, "attackspeed": 0.658}, "spells": [{"id": "OrianaIzunaCommand", "name": "Befehl: <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> be<PERSON>t ihrer Kugel, sich in ein Zielgebiet zu schleudern und so magischen Schaden an allen auf dem Weg getroffenen Zielen zu verursachen (verursacht weniger Schaden bei weiteren Treffern). Ihre Kugel verbleibt danach an der Zielposition.", "tooltip": "<PERSON><PERSON><PERSON> be<PERSON> ihrer <keywordMajor><PERSON><PERSON></keywordMajor>, sich an eine Position zu bewegen, wodurch umstehende Gegner und Gegner, die sie auf ihrem Weg trifft, <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage> erleiden. Alle getroffenen Gegner nach dem Ersten erleiden {{ e2 }}&nbsp;% weniger Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [6, 5.25, 4.5, 3.75, 3], "cooldownBurn": "6/5.25/4.5/3.75/3", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [30, 30, 30, 30, 30], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "30", "70", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [815, 815, 815, 815, 815], "rangeBurn": "815", "image": {"full": "OrianaIzunaCommand.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrianaDissonanceCommand", "name": "Befehl: <PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> be<PERSON> i<PERSON><PERSON>, einen Energiestoß auszusenden, der magischen Schaden verursacht. Er hinterlässt ein Feld, das Verbündete beschleunigt und Gegner verlangsamt.", "tooltip": "<PERSON><PERSON><PERSON> be<PERSON> ihrer <keywordMajor><PERSON><PERSON></keywordMajor>, einen elektrischen Impuls auszusenden, der an umstehenden Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.<br /><br />Der Impuls hinterlässt {{ fieldduration }}&nbsp;Sekunden lang ein Energiefeld, das Gegner um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status> und Verbündeten <speed>{{ hasteamount*100 }}&nbsp;% Lauftempo</speed> gew<PERSON><PERSON>t, das über {{ slowandhasteduration }}&nbsp;Sekunden abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Lauftempo", "Verlangsamung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ hasteamount*100.000000 }}&nbsp;% -> {{ hasteamountnl*100.000000 }}&nbsp;%", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [30, 35, 40, 45, 50], [30, 35, 40, 45, 50], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/105/150/195/240", "30/35/40/45/50", "30/35/40/45/50", "3", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [225, 225, 225, 225, 225], "rangeBurn": "225", "image": {"full": "OrianaDissonanceCommand.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrianaRedactCommand", "name": "Befehl: <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> be<PERSON> ihr<PERSON>, einen verbündeten Champion zu begleiten, diesen abzuschirmen und magischen Schaden an allen auf dem Weg getroffenen Gegnern zu verursachen. Die Kugel gewährt diesem Champion außerdem zusätzlich Rüstung und Magieresistenz.", "tooltip": "<spellPassive>Passiv: </spellPassive>Die <keywordMajor><PERSON><PERSON></keywordMajor> gewährt dem verbündeten Champion, den sie begleitet, <scaleArmor>{{ e2 }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ e2 }}&nbsp;Magieresistenz</scaleMR>.<br /><br /><spellActive>Aktiv: </spellActive><PERSON><PERSON><PERSON> befiehlt ihrer <keywordMajor><PERSON><PERSON></keywordMajor>, einen verbündeten Champion zu begleiten und ihm {{ e5 }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ totalshieldtooltip }} zu gewähren. G<PERSON><PERSON>, die von der <keywordMajor>Kugel</keywordMajor> auf ihrem Weg getroffen werden, erleiden <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzliche Rüstung", "Zusätzliche Magieresistenz", "Schildstärke"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [55, 90, 125, 160, 195], [6, 12, 18, 24, 30], [60, 90, 120, 150, 180], [75, 75, 75, 75, 75], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "55/90/125/160/195", "6/12/18/24/30", "60/90/120/150/180", "75", "2.5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1095, 1095, 1095, 1095, 1095], "rangeBurn": "1095", "image": {"full": "OrianaRedactCommand.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrianaDetonateCommand", "name": "Befehl: <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> be<PERSON>t ihrer Kugel eine Schockwelle auszusenden, die magischen Schaden verursacht und nach kurzer Verzögerung nahe Gegner auf die Kugel zuschleudert.", "tooltip": "<PERSON><PERSON><PERSON> be<PERSON> <keywordMajor><PERSON><PERSON></keywordMajor>, eine <PERSON> zu entfe<PERSON>n, die <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> an nahen <PERSON>n verursacht und sie in die Richtung der <keywordMajor><PERSON><PERSON></keywordMajor> <status>schleudert</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [410, 410, 410], "rangeBurn": "410", "image": {"full": "OrianaDetonateCommand.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Präzises Uhrwerk", "description": "<PERSON>iannas Angriffe verursachen zusätzlichen magischen Schaden. Der Schaden erhöht sich, wenn Orianna das gleiche Ziel wiederholt angreift.", "image": {"full": "OriannaPassive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}