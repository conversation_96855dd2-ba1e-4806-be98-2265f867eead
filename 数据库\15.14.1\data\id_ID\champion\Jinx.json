{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jinx": {"id": "<PERSON><PERSON>", "key": "222", "name": "<PERSON><PERSON>", "title": "the Loose Cannon", "image": {"full": "Jinx.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "222000", "num": 0, "name": "default", "chromas": false}, {"id": "222001", "num": 1, "name": "Crime City Jinx", "chromas": false}, {"id": "222002", "num": 2, "name": "Firecracker Jinx", "chromas": true}, {"id": "222003", "num": 3, "name": "Zombie Slayer Jin<PERSON>", "chromas": false}, {"id": "222004", "num": 4, "name": "Star Guardian Jinx", "chromas": true}, {"id": "222012", "num": 12, "name": "Ambitious <PERSON><PERSON>", "chromas": false}, {"id": "222013", "num": 13, "name": "Odyssey Jinx", "chromas": true}, {"id": "222020", "num": 20, "name": "PROJECT: <PERSON><PERSON>", "chromas": true}, {"id": "222029", "num": 29, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "222037", "num": 37, "name": "Arcane Enemy Jinx", "chromas": false}, {"id": "222038", "num": 38, "name": "Battle Cat Jinx", "chromas": true}, {"id": "222040", "num": 40, "name": "Prestige Battle Cat Jinx", "chromas": false}, {"id": "222051", "num": 51, "name": "Cafe Cuties Jinx", "chromas": true}, {"id": "222060", "num": 60, "name": "Arcane Fractured Jinx", "chromas": false}, {"id": "222062", "num": 62, "name": "T1 Jinx", "chromas": false}], "lore": "<PERSON><PERSON>, si kriminal gila dan impulsif dari kota bawah, dihan<PERSON>i masa lalunya yang kelam. <PERSON><PERSON>, itu tidak menghentikannya membawa kekacauan khasnya ke Piltover dan Zaun. Dia menggunakan berbagai senjata rakitannya untuk menimbulkan efek yang dahsyat, melepaskan tembakan dan ledakan warna-warni, menginspirasi orang-orang tertindas untuk memberontak dan melawan dengan kekacauan yang dia buat.", "blurb": "<PERSON><PERSON>, si kriminal gila dan impulsif dari kota bawah, dihan<PERSON>i masa lalunya yang kelam. <PERSON><PERSON>, itu tidak menghentikannya membawa kekacauan khasnya ke Piltover dan <PERSON>n. Dia menggunakan berbagai senjata rakitannya untuk menimbulkan efek yang dahsyat...", "allytips": ["Roket tidak selalu menjadi pilihan terbaik! Minigun Jinx sangat kuat saat sudah terisi penuh. Ganti ke minigun saat ada champion musuh yang terlalu dekat.", "Roket Jinx memberikan damage penuh ke semua musuh dalam ledakan. Gunakan pada minion di lane untuk mengenai champion musuh di sekitar tanpa menarik serangan minion.", "Saat per<PERSON>ungan dimulai, coba bertahan di pinggir dan serang menggunakan roket dan Zap!. <PERSON><PERSON> langsung masuk dan menembak dengan minigun sampai situasi aman."], "enemytips": ["Minigun Jinx butuh waktu untuk mencapai kekuatan maksimal. Saat Jinx menyerang dengan roket, lompatlah ke arahnya dan serang dia dengan serangan mematikan.", "Ultima Jinx memiliki damage lebih rendah pada jarak dekat.", "Granat jebakan Jinx membutuhkan waktu lama untuk siap digunakan kembali dan menjadi alat perlindungan utamanya.  <PERSON>ka dia meleset, dia akan kesulitan kabur jika diserang langsung. "], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 4, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 105, "mp": 260, "mpperlevel": 50, "movespeed": 325, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.75, "hpregenperlevel": 0.5, "mpregen": 6.7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.25, "attackspeedperlevel": 1.4, "attackspeed": 0.625}, "spells": [{"id": "JinxQ", "name": "Switcheroo!", "description": "Jinx memodifikasi basic attack-nya dengan secara bergantian menggunakan Pow-Pow, sebuah minigun, dan <PERSON>, sebuah peluncur roket. Serangan dengan Pow-Pow memberikan Attack Speed, sedangkan serangan dengan Fishbones menghasilkan damage AOE, meningkatkan range, tetapi menyedot Mana dan menyerang lebih lambat.", "tooltip": "Jinx bergonta-ganti senjata antara peluncur roket Fishbones dan minigun Pow-Pow.<br /><br />Saat menggunakan peluncur roket, Serangan <PERSON> menghasilkan <physicalDamage>{{ rocketdamage }} physical damage</physicalDamage> pada target dan musuh di sekitar, mendapatkan {{ rocketbonusrange }} range, mengurangi Mana, dan berta<PERSON>h {{ rocketaspdpenalty*100 }}% lebih sedikit dengan Attack Speed bonus.<br /><br />Saat menggunakan minigun, Serangan Jinx memberikan <attackSpeed>Attack Speed</attackSpeed> selama {{ minigunattackspeedduration }} detik, bisa stack hingga {{ minigunattackspeedstacks }} kali (<attackSpeed>+{{ minigunattackspeedmax }}% %i:scaleAS% maksimum</attackSpeed>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Range Bonus Roket", "Attack Speed Total Minigun"], "effect": ["{{ rocketbonusrange }}-> {{ rocketbonusrangeNL }}", "{{ minigunattackspeedmax }}%-> {{ minigunattackspeedmaxNL }}%"]}, "maxrank": 5, "cooldown": [0.9, 0.9, 0.9, 0.9, 0.9], "cooldownBurn": "0.9", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JinxQ.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} <PERSON><PERSON>"}, {"id": "JinxW", "name": "Zap!", "description": "<PERSON><PERSON>, pistol kej<PERSON>, untuk menembakkan ledakan yang mengh<PERSON>lkan damage pada musuh pertama yang terkena, membua<PERSON><PERSON> terkena slow, dan mengung<PERSON><PERSON> p<PERSON>.", "tooltip": "Jinx <PERSON>n Shock Blast yang men<PERSON><PERSON>an <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> pada musuh pertama yang terkena, menerapkan <status>Slow</status> padanya sebesar {{ slowpercent }}%, dan mengung<PERSON> selama {{ slowduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ slowpercent }}%-> {{ slowpercentNL }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "JinxW.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxE", "name": "Flame Chompers!", "description": "Jinx melem<PERSON>an granat dalam satu garis lurus yang akan meledak setelah 5 detik dan membakar musuh. Flame Chompers akan menggigit champion musuh yang men<PERSON>, menerapkan root pada mereka di tempat.", "tooltip": "Jinx melempar 3 Chomper yang bertahan selama {{ grenadeduration }} detik. Chomper meledak saat mengenai champion musuh, menerapkan <status>Root</status> pada mereka selama {{ rootduration }} detik dan men<PERSON>an <magicDamage>{{ totaldamage }} magic damage</magicDamage> pada musuh di sekitar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 20.5, 17, 13.5, 10], "cooldownBurn": "24/20.5/17/13.5/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "JinxE.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxR", "name": "Super Mega Death Rocket!", "description": "Jinx men<PERSON>n roket super di peta yang damage-nya terus meningkat seiring makin jauh jarak yang ditempuh. Roket akan meledak saat menghantam champion musuh, men<PERSON><PERSON><PERSON><PERSON> damage padanya dan musuh di sekitarnya berdasarkan Health mereka yang hilang.", "tooltip": "Jinx <PERSON>n roket yang meledak pada champion musuh yang pertama terkena, men<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ damagefloor }} hingga {{ damagemax }} + {{ percentdamage }}% physical damage dari Health yang hilang</physicalDamage>, mendapatkan damage selama detik pertama peluncurannya. Mu<PERSON>h di sekitar menerima {{ aoedamagemult*100 }}% Damage.<br /><br /><rules>Damage dari Health yang hilang tidak bisa melebihi {{ monsterexecutemax }} terhadap Monster.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Minimum", "Damage Maksimum", "Persentase Damage Health yang Hilang", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ maxdamage }}-> {{ maxdamageNL }}", "{{ percentdamage }}%-> {{ percentdamageNL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [85, 65, 45], "cooldownBurn": "85/65/45", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JinxR.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Get Excited!", "description": "<PERSON><PERSON> men<PERSON> peningkatan Move Speed dan Attack Speed besar-besar<PERSON> set<PERSON>p kali membantu kill atau menghancurkan champion musuh, monster jungle epik, atau bangunan.", "image": {"full": "Jinx_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}