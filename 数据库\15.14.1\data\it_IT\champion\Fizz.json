{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "Fizz", "title": "il prestigiatore delle maree", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "Fizz Atlantideo", "chromas": false}, {"id": "105002", "num": 2, "name": "Fizz Tundra", "chromas": false}, {"id": "105003", "num": 3, "name": "Fizz Pescatore", "chromas": false}, {"id": "105004", "num": 4, "name": "Fizz del Vuoto", "chromas": false}, {"id": "105008", "num": 8, "name": "Fizz Coda a Batuffoli", "chromas": false}, {"id": "105009", "num": 9, "name": "Fizz Megagalattico", "chromas": false}, {"id": "105010", "num": 10, "name": "Fizz Squadra Omega", "chromas": true}, {"id": "105014", "num": 14, "name": "Fuffi Fizz", "chromas": false}, {"id": "105015", "num": 15, "name": "<PERSON><PERSON>i <PERSON> (edizione prestigio)", "chromas": false}, {"id": "105016", "num": 16, "name": "Fizz Di<PERSON>tto", "chromas": true}, {"id": "105025", "num": 25, "name": "<PERSON><PERSON><PERSON> Fizz (edizione prestigio 2022)", "chromas": false}, {"id": "105026", "num": 26, "name": "Fizz Astronauta", "chromas": true}, {"id": "105035", "num": 35, "name": "Fizz Pastore pluviale", "chromas": true}], "lore": "Fizz è uno yordle anfibio che abita sulle coste intorno a Bilgewater. Spesso recupera e restituisce i tributi lanciati nel mare dai capitani più superstiziosi, ma anche i lupi di mare più induriti sanno che va lasciato in pace. Si narrano molte storie su chi ha sottovalutato il suo viscido carattere. A volte viene scambiato per una sorta di spirito capriccioso degli oceani e sembra in grado di comandare le enormi bestie carnivore degli abissi, provando grande piacere nel confondere alleati e nemici.", "blurb": "Fizz è uno yordle anfibio che abita sulle coste intorno a Bilgewater. Spesso recupera e restituisce i tributi lanciati nel mare dai capitani più superstiziosi, ma anche i lupi di mare più induriti sanno che va lasciato in pace. Si narrano molte storie...", "allytips": ["<PERSON><PERSON>me Fizz ignora le collisioni, cerca qualsiasi opportunità in corsia di camminare attraverso i minion e applicare la passiva Tridente della pietra di mare, per poi continuare con l'attacco dell'attiva dopo qualche secondo.", "L'abilità suprema di Fizz, Evocazione acquatica, può essere puntata verso un nemico o verso un'area dove pensi che si stia dirigendo.", "Le abilità di Fizz sono proporzionali al suo potere magico: valuta l'acquisto di oggetti come Clessidra di Zhonya o Velo della Banshee contro squadre che minacciano di infliggere danni a raffica e oggetti come Terrore dei lich o Copricapo di Rabadon, se pensi di poter sopravvivere senza ulteriori incrementi di salute."], "enemytips": ["Gli attacchi di Fizz diventano più letali per alcuni secondi dopo l'utilizzo del suo attacco potenziato. Tienilo a distanza mentre il suo tridente brilla!", "Fizz è molto sfuggevole quando le sue abilità non sono in ricarica. Spingilo a usarle in anticipo e puniscilo con un effetto di controllo e degli attacchi pesanti!"], "tags": ["Assassin", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "<PERSON><PERSON> degli aculei", "description": "Fizz scatta attraverso il bersaglio, infliggendo danni magici e applicando gli effetti sul colpo.", "tooltip": "Fizz scatta attraverso un nemico, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> più <magicDamage>{{ qdamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzW", "name": "Tridente della pietra di mare", "description": "Gli attacchi di Fizz fanno sanguinare i nemici, infliggendo danni magici nell'arco di alcuni secondi. Fizz può potenziare il suo prossimo attacco per infliggere danni bonus e potenziare i suoi attacchi successivi per un breve periodo.", "tooltip": "<spellPassive>Passiva</spellPassive>: gli attacchi di Fizz fanno sanguinare i suoi nemici, infliggendo <magicDamage>{{ dotdamage }} danni magici</magicDamage> in {{ bleedduration }} secondi. <br /><br /><spellActive>Attiva</spellActive>: il successivo attacco di Fizz infligge <magicDamage>{{ activedamage }} danni magici</magicDamage> aggiuntivi. Se questo attacco uccide il bersaglio, Fizz recupera <scaleMana>{{ onkillmanarefund }} mana</scaleMana> e riduce la ricarica di questa abilità di {{ onkillnewcooldown }} secondo/i. Se non lo uccide, gli attacchi di Fizz infliggono <magicDamage>{{ onhitbuffdamage }} danni magici</magicDamage> aggiuntivi per {{ onhitbuffduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> passivi", "<PERSON><PERSON>", "<PERSON><PERSON> sul colpo", "<PERSON><PERSON><PERSON><PERSON> mana", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ dotbasedamage }} -> {{ dotbasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }} -> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }} -> {{ onkillmanarefundNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzE", "name": "Giocoso/Ingannatore", "description": "Fizz salta in aria, atterrando con grazia sulla sua lancia, diventando inattaccabile. Da questa posizione, Fizz può colpire il terreno o saltare nuovamente, per poi colpire il terreno.", "tooltip": "Fizz salta sul suo tridente, diventando non bersagliabile per 0,75 secondi. Al termine di questo lasso di tempo, infligge <magicDamage>{{ edamage }} danni magici</magicDamage> ai nemici vicini e li <status>rallenta</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi. <br /><br />Fizz può <recast>rilanciare</recast> questa abilità mentre è non bersagliabile per scattare nuovamente. Così facendo, annulla l'effetto in anticipo, infligge danni in un'area più piccola e non <status>rallenta</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzR", "name": "Evocazione acquatica", "description": "Fizz lancia un pesce in una direzione. Il pesce si attacca a qualunque campione lo tocchi, rallentando il bersaglio. Dopo un breve ritardo emerge uno squalo dal terreno, che lancia in aria il bersaglio e i nemici vicini. Tutti i nemici colpiti subiscono danni magici e vengono rallentati.", "tooltip": "Fizz lancia un pesce che si attacca al primo campione che colpisce. La vittima subisce <keywordStealth>Visione magica</keywordStealth> ed è <status>rallentata</status> dal 40% all'80% in base alla distanza percorsa dal pesce prima di colpirlo. <br /><br /><PERSON><PERSON> {{ detonationtime }} secondi, uno squalo compare sotto al bersaglio con il pesce, <status>lanciandolo in aria</status> per 1 secondo, <status>respingendo</status> tutte le altre unità e infliggendo da <magicDamage>{{ smallsharkdamage }} a {{ bigsharkdamage }} danni magici</magicDamage> in base alla distanza percorsa dal pesce prima di attaccarsi al bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> squalo piccolo", "Danni squalo medio", "<PERSON>ni squalo grande"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ smalldamage }} -> {{ smalldamageNL }}", "{{ middamage }} -> {{ middamageNL }}", "{{ bigdamage }} -> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Agile combattente", "description": "Fizz può muoversi attraverso le unità e subisce una quantità fissa di danni ridotti da tutte le fonti.", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}