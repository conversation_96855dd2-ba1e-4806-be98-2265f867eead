{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nautilus": {"id": "Na<PERSON><PERSON>", "key": "111", "name": "노틸러스", "title": "심해의 타이탄", "image": {"full": "Nautilus.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "111000", "num": 0, "name": "default", "chromas": false}, {"id": "111001", "num": 1, "name": "아틀란티스 노틸러스", "chromas": false}, {"id": "111002", "num": 2, "name": "지하 세계 노틸러스", "chromas": false}, {"id": "111003", "num": 3, "name": "우주비행사 노틸러스", "chromas": true}, {"id": "111004", "num": 4, "name": "심판자 노틸러스", "chromas": false}, {"id": "111005", "num": 5, "name": "파괴의 신 노틸러스", "chromas": false}, {"id": "111006", "num": 6, "name": "정복자 노틸러스", "chromas": false}, {"id": "111009", "num": 9, "name": "전설의 산수화 노틸러스", "chromas": false}, {"id": "111018", "num": 18, "name": "공포의 밤 노틸러스", "chromas": false}, {"id": "111027", "num": 27, "name": "우주 성기사 노틸러스", "chromas": false}, {"id": "111036", "num": 36, "name": "야성의 수정 노틸러스", "chromas": false}], "lore": "빌지워터에는 처음으로 물에 잠긴 부두만큼이나 오래되었다는 쓸쓸한 전설이 하나 있다. 육중한 갑옷을 걸친 노틸러스라는 이름의 거인이 푸른 불꽃 제도 해안가의 검푸른 물을 배회한다는 이야기이다. 이제는 기억나지도 않을 복수심에 사로잡힌 그는 예고도 없이 거대한 닻을 휘둘러 가여운 자들을 구하고 탐욕스러운 자들을 죽음으로 인도한다. '빌지워터의 공물'이라는 절대 어겨선 안 될 약속을 잊은 자들을 바닷속으로 끌고 들어가며, 끌려들어 간 자는 누구도 그곳에서 살아 돌아올 수 없다고 한다.", "blurb": "빌지워터에는 처음으로 물에 잠긴 부두만큼이나 오래되었다는 쓸쓸한 전설이 하나 있다. 육중한 갑옷을 걸친 노틸러스라는 이름의 거인이 푸른 불꽃 제도 해안가의 검푸른 물을 배회한다는 이야기이다. 이제는 기억나지도 않을 복수심에 사로잡힌 그는 예고도 없이 거대한 닻을 휘둘러 가여운 자들을 구하고 탐욕스러운 자들을 죽음으로 인도한다. '빌지워터의 공물'이라는 절대 어겨선 안 될 약속을 잊은 자들을 바닷속으로 끌고 들어가며, 끌려들어 간 자는 누구도 그곳에서...", "allytips": ["기습할 때 닻줄 견인으로 근처 지형을 맞힌 다음 역조 스킬을 연계하면 공격 성공률을 높일 수 있습니다.", "역조 스킬은 사용 시 폭발하기까지 지연 시간이 있습니다. 달아날 때나 다가오는 적들을 방해할 때는 이 지연시간을 계산해서 활용하세요."], "enemytips": ["노틸러스가 곁에서 역조 스킬을 사용할 때는, 그 자리에서 버티다가 물결 효과가 끝난 다음 달아나세요. 미리 달아나면 이차 폭발에 맞아 추가 피해와 둔화 효과에 걸릴 수 있습니다.", "노틸러스는 보호막이 지속되는 동안은 기본 공격으로 큰 광역 피해와 둔화를 줄 수 있습니다. 시간이 있으면 보호막이 사라질 때까지 기다리세요."], "tags": ["Tank", "Support"], "partype": "마나", "info": {"attack": 4, "defense": 6, "magic": 6, "difficulty": 6}, "stats": {"hp": 646, "hpperlevel": 100, "mp": 400, "mpperlevel": 47, "movespeed": 325, "armor": 39, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.65, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1, "attackspeed": 0.706}, "spells": [{"id": "NautilusAnchorDrag", "name": "닻줄 견인", "description": "노틸러스가 전방으로 닻을 던집니다.  닻이 적을 맞히면 노틸러스와 대상이 가까이 당겨지며 대상에게 마법 피해를 입힙니다. 닻이 지형을 맞히면 노틸러스가 지형 쪽으로 끌려갑니다.", "tooltip": "노틸러스가 전방으로 닻을 던집니다. 닻이 적을 맞히면 노틸러스와 대상이 가까이 당겨지며 대상에게 <magicDamage>{{ qdamagecalc }}의 마법 피해</magicDamage>를 입히고 잠시 <status>기절</status>시킵니다. 닻이 지형을 맞히면 노틸러스가 지형 쪽으로 끌려갑니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "0", "0.5", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "NautilusAnchorDrag.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NautilusPiercingGaze", "name": "타이탄의 분노", "description": "노틸러스가 잠시 보호막을 얻습니다. 보호막이 지속되는 동안 노틸러스의 기본 공격은 대상과 대상 주위의 적에게 지속 피해를 입힙니다.", "tooltip": "노틸러스가 {{ shieldduration }}초 동안 <shield>{{ shieldcalc }}의 피해를 흡수하는 보호막</shield>을 얻습니다. <shield>보호막</shield>이 지속되는 동안 노틸러스의 기본 공격은 2초에 걸쳐 대상과 대상 주위의 모든 적에게 <magicDamage>{{ dotdamagecalc }}의 마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "마법 피해", "최대 체력 %"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ dotdamagebase }} -> {{ dotdamagebaseNL }}", "{{ shieldhealthratio*100.000000 }}% -> {{ shieldhealthrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "NautilusPiercingGaze.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NautilusSplashZone", "name": "역조", "description": "노틸러스가 주위에 세 번의 폭발을 일으킵니다. 폭발할 때마다 적에게 피해를 입히고 둔화시킵니다.", "tooltip": "노틸러스가 주위에 세 번의 폭발을 일으킵니다. 폭발할 때마다 범위 내의 모든 적에게 <magicDamage>{{ damagecalc }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ slowpercent*100 }}% <status>둔화</status>시킵니다. 둔화 효과는 시간이 지나면 사라집니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "소모값 @AbilityResourceName@", "피해량", "둔화"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NautilusSplashZone.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NautilusGrandLine", "name": "폭뢰", "description": "지면에 충격파를 발사하면 상대방을 추적해 갑니다. 충격파는 위의 지면을 찢으며 적들을 공중으로 띄웁니다. 충격파가 상대방에게 닿으면 폭발하여 대상을 공중으로 띄우고 기절시킵니다.", "tooltip": "노틸러스가 적 챔피언을 추격하는 충격파를 발사해 <magicDamage>{{ primarytargetdamage }}의 마법 피해</magicDamage>를 입히며, {{ stunduration }}초 동안 <status>공중으로</status> <status>띄워 올리고</status> <status>기절</status>시킵니다. 충격파에 맞은 다른 적 또한 <status>공중에 뜨고</status> <status>기절</status>하며 <magicDamage>{{ secondarytargetdamage }}의 마법 피해</magicDamage>를 입습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "관통 피해", "기절 지속시간:", "폭발 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ primarydamage }} -> {{ primarydamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "NautilusGrandLine.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "강력한 일격", "description": "노틸러스가 대상에 대한 첫 기본 공격 시 추가 물리 피해를 입히고 잠시 속박합니다.", "image": {"full": "Nautilus_StaggeringBlow.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}