{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mel": {"id": "<PERSON>", "key": "800", "name": "<PERSON>", "title": "das Spiegelbild der Seele", "image": {"full": "Mel.png", "sprite": "champion2.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "800000", "num": 0, "name": "default", "chromas": false}, {"id": "800001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Arcane)", "chromas": true}], "lore": "Mel Medarda ist die mutmaßliche Erbin der Medarda-Familie, die einst zu den mächtigsten Häusern von Noxus gehörte. Nach außen wirkt sie wie eine anmutige Aristokratin, doch dahinter verbirgt sich eine geschickte Politikerin, die es sich zur Aufgabe gemacht hat, alles über jeden zu wissen, den sie trifft. Nach einer Begegnung mit der geheimnisvollen Schwarzen Rose entdeckte Mel die Abgründe des Betrugs ihrer Mutter und sah sich zum ersten Mal mit einer Situation konfrontiert, die selbst sie nicht kontrollieren konnte. Mit ihren neu erweckten magischen Fähigkeiten machte sie sich auf den Weg in ihre Heimat, um nach Antworten zu suchen – und obwohl viele immer noch versuchen, das Licht in ihr zu unterdrücken, bleibt Mels Seele weiterhin trotzig.", "blurb": "Mel Medarda ist die mutmaßliche Erbin der Medarda-Familie, die einst zu den mächtigsten Häusern von Noxus gehörte. Nach außen wirkt sie wie eine anmutige Aristokratin, doch dahinter verbirgt sich eine geschickte Politikerin, die es sich zur Aufgabe...", "allytips": ["Mel kann gegnerische Projektile zurückwerfen, einsch<PERSON>ßlich mächtiger Fähigkeiten. Warte ab, bis sie „Zurückweisung“ aktiviert hat, bevor du mächtige Projektile auf sie abfeuerst.", "Je öfter <PERSON> dich trifft, desto mehr Steigerungen von „Überwältigen“ löst sie aus. Wenn du zu viel Leben verloren hast, wird dich ihr nächster Treffer töten, also ziehe dich für ein paar Sekunden zurück, damit die Steigerungen von „Überwältigen“ abfallen."], "enemytips": ["Mel kann gegnerische Projektile zurückwerfen, einsch<PERSON>ßlich mächtiger Fähigkeiten. Warte ab, bis sie „Zurückweisung“ aktiviert hat, bevor du mächtige Projektile auf sie abfeuerst.", "Je öfter <PERSON> dich trifft, desto mehr Steigerungen von „Überwältigen“ löst sie aus. Wenn du zu viel Leben verloren hast, wird dich ihr nächster Treffer töten, also ziehe dich für ein paar Sekunden zurück, damit die Steigerungen von „Überwältigen“ abfallen."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 93, "mp": 480, "mpperlevel": 28, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.9, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "MelQ", "name": "Strahlende Sal<PERSON>", "description": "Mel feuert ein Trommelfeuer aus Projektilen ab, die im Zielbereich explodieren und Gegnern im Wirkbereich wiederholt Schaden zufügen.", "tooltip": "Mel feuert ein Trommelfeuer aus {{ explosioncount }}&nbsp;Projektilen ab, die im Bereich um einen Zielort herum explodieren.<br /><br />Jede Explosion verursacht <magicDamage>{{ totalexplosiondamage }}&nbsp;magischen Schaden</magicDamage>, bis die Obergrenze von insgesamt <magicDamage>{{ alldamagehit }}&nbsp;magischer Schaden</magicDamage> erreicht ist.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Explosionsschaden", "Anzahl der Explosionen", "Abklingzeit", "Manakosten"], "effect": ["{{ explosiondamage }} -> {{ explosiondamageNL }}", "{{ explosioncount }} -> {{ explosioncountNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "MelQ.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelW", "name": "Zurückweisung", "description": "<PERSON> erzeugt eine Barriere um sich herum, die gegnerische Projektile auf den Angreifer zurückwirft, ver<PERSON><PERSON><PERSON>, dass sie Sc<PERSON>en erle<PERSON>t, und ihr erhöhtes Lauftempo gewährt.", "tooltip": "Mel erzeugt eine Barriere um sich herum, die Projektile von gegnerischen Champions zurückwirft, ver<PERSON><PERSON><PERSON>, dass sie Schaden erleidet, und ihr <speed>{{ movespeed*100 }}&nbsp;% abfallendes Lauftempo</speed> für {{ duration }}&nbsp;Sekunde(n) gewährt.<br /><br />Zurückgeworfene Projektile verursachen magischen Schaden in Höhe von <magicDamage>{{ damagepercent }} des ursprünglichen Schadens</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zurückgeworfener Schaden", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamagepercent*100.000000 }}&nbsp;% -> {{ basedamagepercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [35, 32, 29, 26, 23], "cooldownBurn": "35/32/29/26/23", "cost": [80, 60, 40, 20, 0], "costBurn": "80/60/40/20/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "MelW.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelE", "name": "Sonnenfesseln", "description": "Mel feuert eine leuchtende Kugel geradeaus, die Gegner in ihrem Zentrum festhält, Gegner im äußeren Bereich verlangsamt und zudem Schaden über Zeit verursacht.", "tooltip": "Mel feuert eine leuchtende Kugel ab, die Gegner in ihrem Zentrum {{ rootduration }}&nbsp;Sekunden lang <status>festhält</status> und <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht.<br /><br />Die Kugel erschafft einen feindlichen Bereich um sich herum, der Gegner um {{ areaslowamount*100 }}&nbsp;% <status>verlangsamt</status> und ihnen <magicDamage>{{ areadamagepersecond }}&nbsp;magischen Schaden pro Sekunde</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Festhaltedauer", "Schaden pro Sekunde", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ baseareadamage*8.000000 }} -> {{ baseareadamagenl*8.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "MelE.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelR", "name": "Goldene Eklipse", "description": "<PERSON> greift alle <PERSON>, die mit „Überwältigen“ markiert sind, unabhäng<PERSON> von deren Entfernung zu ihr an und fügt ihnen mit jeder Steigerung von „Überwältigen“ zusätzlichen Schaden zu.<br><br><PERSON><PERSON><PERSON> von „Goldene Eklipse“ erhöhen den Schaden von „Überwältigen“.", "tooltip": "<spellPassive>Passiv:</spellPassive> Der Schaden von <keywordMajor>Überwältigen</keywordMajor> ist erhöht auf <magicDamage>{{ passiveflatdamage }}&nbsp;magischer <PERSON><PERSON> plus {{ passivestackdamage }}&nbsp;magischer Schaden pro Steigerung</magicDamage>.<br /><br /><spellActive>Aktiv:</spellActive> Mel entfesselt ihre Macht auf alle Gegner, die von <keywordMajor>Überwältigen</keywordMajor> betroffen sind, und fügt ihnen <magicDamage>{{ ultflatdamage }}&nbsp;magischen Schaden plus {{ ultstackdamage }}&nbsp;magischen Schaden pro Steigerung von <keywordMajor>Überwältigen</keywordMajor> zu</magicDamage>.<br /><br /><rules>Kann nur aktiviert werden, wenn ein gegnerischer Champion von <keywordMajor>Überwältigen</keywordMajor> betroffen ist.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName><PERSON><PERSON></spellName> – g<PERSON><PERSON><PERSON><PERSON><PERSON>", "<spellName><PERSON><PERSON> Eklipse</spellName> – Schaden der Steigerungen", "Abklingzeit", "<keywordMajor>Überwältigen</keywordMajor> – gle<PERSON><PERSON><PERSON><PERSON>", "<keywordMajor>Überwältigen</keywordMajor> – Schaden der Steigerungen"], "effect": ["{{ baseultflatdamage }} -> {{ baseultflatdamageNL }}", "{{ baseultstackdamage }} -> {{ baseultstackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basepassiveflatdamage }} -> {{ basepassiveflatdamageNL }}", "{{ basepassivestackdamage }} -> {{ basepassivestackdamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MelR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Versengende Brillanz", "description": "Wenn Mel eine Fähigkeit einsetzt, erhält sie bei ihrem nächsten Angriff drei zusätzliche Projektile (maximal neun).<br><br>Wenn Mel durch eine Fähigkeit oder einen Angriff Schaden verursacht, löst sie „Überwältigen“ aus (unbegrenzt steigerbar). Wenn Mel einem Gegner ausreichend Schaden durch „Überwältigen“ zugefügt hat, werden die Steigerungen verbraucht und das Ziel exekutiert.", "image": {"full": "Mel_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}