{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aatrox": {"id": "Aatrox", "key": "266", "name": "Aatrox", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Aatrox.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "266000", "num": 0, "name": "default", "chromas": false}, {"id": "266001", "num": 1, "name": "Aatrox justicier", "chromas": false}, {"id": "266002", "num": 2, "name": "Mecha Aatrox", "chromas": true}, {"id": "266003", "num": 3, "name": "Aatrox chasseur marin", "chromas": false}, {"id": "266007", "num": 7, "name": "Aatrox lune de sang", "chromas": false}, {"id": "266008", "num": 8, "name": "Aatrox lune de sang prestige", "chromas": false}, {"id": "266009", "num": 9, "name": "Aatrox héros de guerre", "chromas": true}, {"id": "266011", "num": 11, "name": "Aatrox de l'Odyssée", "chromas": true}, {"id": "266020", "num": 20, "name": "Aatrox lune de sang prestige (2022)", "chromas": false}, {"id": "266021", "num": 21, "name": "Aatrox de l'éclipse lunaire", "chromas": true}, {"id": "266030", "num": 30, "name": "DRX Aatrox", "chromas": true}, {"id": "266031", "num": 31, "name": "DRX Aatrox prestige", "chromas": false}, {"id": "266033", "num": 33, "name": "Aatrox primordien", "chromas": true}], "lore": "Autre<PERSON><PERSON>, Aatrox et ses frères étaient honorés pour avoir défendu Shurima contre le Néant. Mais ils finirent par devenir une menace plus grande encore pour Runeterra : la ruse et la sorcellerie furent employées pour les battre. Cependant, après des siècles d'emprisonnement, Aatrox fut le premier à retrouver sa liberté, en corrompant et transformant les mortels assez stupides pour tenter de s'emparer de l'arme magique qui contenait son essence. Désormais en possession d'un corps qu'il a approximativement transformé pour rappeler son ancienne forme, il arpente Runeterra en cherchant à assouvir sa vengeance apocalyptique.", "blurb": "Autre<PERSON><PERSON>, Aatrox et ses frères étaient honorés pour avoir défendu Shurima contre le Néant. Mais ils finirent par devenir une menace plus grande encore pour Runeterra : la ruse et la sorcellerie furent employées pour les battre. Cependant, après des...", "allytips": ["Util<PERSON><PERSON> obscure tout en lançant Épée des Darkin pour augmenter vos chances de toucher l'ennemi.", "Facilitez <PERSON> des Darkin avec des compétences de contrôle de foule, telles que Chaînes infernales, ou avec les effets immobilisants de vos alliés.", "<PERSON><PERSON> des mondes quand vous êtes certain de pouvoir forcer le combat."], "enemytips": ["Les attaques d'Aatrox sont prévisibles. Profitez-en pour esquiver ses zones d'impact.", "Il est plus facile de fuir les Chaînes infernales d'Aatrox en courant vers un côté ou vers Aatrox.", "Quand <PERSON>rox utilise son ultime, gardez vos distances pour l'empêcher de revenir à la vie."], "tags": ["Fighter"], "partype": "<PERSON><PERSON><PERSON> de <PERSON>", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 38, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.651}, "spells": [{"id": "AatroxQ", "name": "<PERSON><PERSON><PERSON>", "description": "Aatrox abat son é<PERSON><PERSON> de<PERSON> lui, infligeant des dégâts physiques. Il peut frapper jusqu'à 3 fois et chaque coup a une zone d'effet différente.", "tooltip": "Aatrox abat son épée, infligeant <physicalDamage>{{ qdamage }} pts de dégâts physiques</physicalDamage>. Si l'ennemi est touché par le tranchant, il est brièvement <status>projeté dans les airs</status> et subit <physicalDamage>{{ qedgedamage }} pts de dégâts</physicalDamage> à la place. Cette compétence peut être <recast>réactivée</recast> deux fois, chaque coup changeant de forme et infligeant 25% de dégâts de plus que la précédente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ratio de dégâts d'attaque totaux"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ qtotaladratio*100.000000 }}% -> {{ qtotaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "AatroxW", "name": "Chaînes infernales", "description": "Aatrox frappe le sol, blessant le premier ennemi touché. Les champions et les grands monstres doivent vite quitter la zone d'effet sous peine d'être ramenés de force au point d'impact et de subir à nouveau les dégâts.", "tooltip": "Aatrox lance une chaîne, <status>ralentissant</status> le premier ennemi touché de {{ wslowpercentage*-100 }}% pendant {{ wslowduration }} sec et infligeant <physicalDamage>{{ wdamage }} pts de dégâts physiques</physicalDamage>. Les champions et les grands monstres doivent quitter la zone d'effet dans les {{ wslowduration }} sec sous peine d'être <status>ramenés de force</status> au point d'impact et de subir à nouveau les dégâts.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ralentissement"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wbasedamage }} -> {{ wbasedamageNL }}", "{{ wslowpercentage*-100.000000 }}% -> {{ wslowpercentagenl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AatroxW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "AatroxE", "name": "Ruée obscure", "description": "Passivement, Aatrox se soigne quand il blesse des champions ennemis. À l'activation, il se rue dans une direction.", "tooltip": "<spellPassive>Passive :</spellPassive> Aatrox récupère des PV équivalents à <lifeSteal>{{ totalevamp }}</lifeSteal> des dégâts qu'il inflige aux champions.<br /><br /><spellActive>Active :</spellActive> Aatrox se rue dans une direction. Il peut utiliser cette compétence tout en lançant ses autres compétences.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxE.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "AatroxR", "name": "Fossoyeur des mondes", "description": "Aatrox libère sa forme démoniaque, effrayant les sbires ennemis proches et augmentant ses dégâts d'attaque, ses soins et sa vitesse de déplacement. La durée est prolongée s'il participe à l'élimination d'un champion ennemi.", "tooltip": "Aatrox révèle sa vraie forme démoniaque, <status>effrayant</status> les sbires proches pendant {{ rminionfearduration }} sec et gagnant <speed>+{{ rmovementspeedbonus*100 }}% de vitesse de déplacement</speed> (ce bonus diminue en {{ rduration }} sec). Il gagne aussi <scaleAD>+{{ rtotaladamp*100 }}% de dégâts d'attaque</scaleAD> et augmente ses <healing>soins personnels de {{ rhealingamp*100 }}%</healing> pendant la durée.<br /><br />Participer à l'élimination d'un champion prolonge la durée de cet effet de {{ rextension }} sec et réinitialise le bonus en <speed>vitesse de déplacement</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Total du bonus en dégâts d'attaque", "Augmentation des soins", "Vitesse de d<PERSON>placement", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ rtotaladamp*100.000000 }}% -> {{ rtotaladampnl*100.000000 }}%", "{{ rhealingamp*100.000000 }}% -> {{ rhealingampnl*100.000000 }}%", "{{ rmovementspeedbonus*100.000000 }}% -> {{ rmovementspeedbonusnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxR.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}], "passive": {"name": "<PERSON>ure du massacreur", "description": "Régulièrement, la prochaine attaque de base d'Aatrox inflige des <physicalDamage>dégâts physiques</physicalDamage> supplémentaires et le soigne, selon un pourcentage des PV max de la cible. ", "image": {"full": "Aatrox_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}