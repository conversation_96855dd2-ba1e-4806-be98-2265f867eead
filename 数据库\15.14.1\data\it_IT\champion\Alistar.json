{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "Alistar", "title": "il minotauro", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "Alistar Nero", "chromas": false}, {"id": "12002", "num": 2, "name": "Alistar d'Oro", "chromas": false}, {"id": "12003", "num": 3, "name": "Alistar Matador", "chromas": false}, {"id": "12004", "num": 4, "name": "Alistar Corna <PERSON>", "chromas": false}, {"id": "12005", "num": 5, "name": "Alistar Scatenato", "chromas": false}, {"id": "12006", "num": 6, "name": "Alistar Infernale", "chromas": false}, {"id": "12007", "num": 7, "name": "Alistar Libero Insuperabile", "chromas": false}, {"id": "12008", "num": 8, "name": "Alistar Predone", "chromas": false}, {"id": "12009", "num": 9, "name": "Alistar SKT T1", "chromas": false}, {"id": "12010", "num": 10, "name": "Alistar <PERSON><PERSON>", "chromas": true}, {"id": "12019", "num": 19, "name": "Alistar Hextech", "chromas": false}, {"id": "12020", "num": 20, "name": "Alistar Conquistatore", "chromas": true}, {"id": "12022", "num": 22, "name": "Ali<PERSON>", "chromas": true}, {"id": "12029", "num": 29, "name": "Alistar Belva Lunare", "chromas": true}], "lore": "Un possente guerriero con una reputazione temibile, Alistar cerca vendetta per lo sterminio del suo clan a opera dei noxiani. Pur ridotto in schiavitù e costretto a divenire un gladiatore, la sua indomita volontà gli ha impedito di ridursi a un animale. <PERSON><PERSON>, libero dalle catene dei suoi padroni, combatte in nome dei deboli e dei diseredati armato di corna, zoccoli, pugni e della sua rabbia.", "blurb": "Un possente guerriero con una reputazione temibile, Alistar cerca vendetta per lo sterminio del suo clan a opera dei noxiani. Pur ridotto in schiavitù e costretto a divenire un gladiatore, la sua indomita volontà gli ha impedito di ridursi a un animale...", "allytips": ["Polverizzare ti permette di stabilire una posizione migliore per Testata.", "La velocità di movimento è molto importante per Alistar. Valuta bene quali stivali comprare.", "Usare Flash ti permette di cogliere di sorpresa il tuo bersaglio e gettarlo indietro verso i tuoi alleati con Polverizzare e Testata."], "enemytips": ["Alistar è un grosso elemento di disturbo ed è anche un osso duro: prova a concentrarti su bersagli che fanno danni, ma sono più fragili.", "Fai attenzione alla combo Polverizzazione-Testata quando sei vicino alle torri avversarie.", "Quando Alistar usa la sua abilità suprema, è sempre meglio arretrare e aspettare che gli effetti svaniscano prima di attaccarlo."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "Polverizzare", "description": "Alistar colpisce il suolo, infliggendo danni ai nemici nelle vicinanze e lanciandoli in aria.", "tooltip": "Alistar colpisce il terreno, <status>lanciando in aria</status> i nemici per {{ knockupduration }} secondo/i e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Headbutt", "name": "Testata", "description": "Alistar carica il bersaglio con la testa, infliggendo danni e respingendolo.", "tooltip": "Alistar incorna un nemico, <status>respingendolo</status> all'<status>indietro</status> e infliggendogli <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AlistarE", "name": "Travolgere", "description": "Alistar travolge le unità nemiche vicine, ignorando le collisioni con le unità e ottenendo una carica se danneggia un campione nemico. Al massimo delle cariche, l'attacco base successivo di Alistar contro un campione nemico infligge danni magici aggiuntivi e stordisce.", "tooltip": "Alistar inizia a calpestare il terreno, diventando spettrale e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> nell'arco di {{ e3 }} secondi ai nemici vicini. Ogni impulso che danneggia un campione conferisce una carica.<br /><br />A {{ e5 }} cariche, il successivo attacco di Alistar contro i campioni <status>stordisce</status> per {{ e6 }} secondo e infligge <magicDamage>{{ attackbonusdamage }} danni magici</magicDamage> aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FerociousHowl", "name": "Volontà indistruttibile", "description": "Alistar si abbandona a un feroce ruggito, liberandosi di eventuali effetti di controllo e riducendo i danni fisici e magici in ingresso per tutta la durata dell'abilità.", "tooltip": "Alistar purifica immediatamente tutti gli <status>effetti di impedimento</status> e subisce il {{ rdamagereduction }}% di danni in meno per {{ rduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Riduzione danni"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamagereduction }}% -> {{ rdamagereductionNL }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Alistar carica il suo Ruggito stordendo o spostando i campioni nemici, o quando i nemici nelle vicinanze muoiono. Quando è completamente carico cura se stesso e tutti i campioni alleati nelle vicinanze.", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}