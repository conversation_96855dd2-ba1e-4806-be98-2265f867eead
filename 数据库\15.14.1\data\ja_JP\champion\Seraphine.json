{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Seraphine": {"id": "<PERSON><PERSON><PERSON>", "key": "147", "name": "セラフィーン", "title": "希望のメロディー", "image": {"full": "Seraphine.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "147000", "num": 0, "name": "default", "chromas": false}, {"id": "147001", "num": 1, "name": "K/DA ALL OUT セラフィーン インディー", "chromas": false}, {"id": "147002", "num": 2, "name": "K/DA ALL OUT セラフィーン ライジングスター", "chromas": false}, {"id": "147003", "num": 3, "name": "K/DA ALL OUT セラフィーン スーパースター", "chromas": false}, {"id": "147004", "num": 4, "name": "麗しの不死鳥セラフィーン", "chromas": true}, {"id": "147014", "num": 14, "name": "オーシャンソング セラフィーン", "chromas": true}, {"id": "147015", "num": 15, "name": "プレステージ オーシャンソング セラフィーン", "chromas": false}, {"id": "147024", "num": 24, "name": "妖精の王宮セラフィーン", "chromas": true}, {"id": "147034", "num": 34, "name": "スターガーディアン セラフィーン", "chromas": true}, {"id": "147043", "num": 43, "name": "バトルドーヴ セラフィーン", "chromas": true}, {"id": "147050", "num": 50, "name": "点心の天使セラフィーン", "chromas": true}], "lore": "ピルトーヴァーでゾウン人の両親のもとに生まれたセラフィーンは、他者の魂の声を聴くことができる──世界が彼女に歌いかけ、彼女も歌い返す。若い頃は耳の中の喧噪に耐えられなかったが、今のセラフィーンはこの声からインスピレーションを得ることで、混沌を交響曲へと変えている。セラフィーンは二つの姉妹都市のために歌い、住民たちに自分たちが独りではないこと、団結すればより強くなれること、そして、彼女の目には無限の可能性が見えていることを伝えようとしている。", "blurb": "ピルトーヴァーでゾウン人の両親のもとに生まれたセラフィーンは、他者の魂の声を聴くことができる──世界が彼女に歌いかけ、彼女も歌い返す。若い頃は耳の中の喧噪に耐えられなかったが、今のセラフィーンはこの声からインスピレーションを得ることで、混沌を交響曲へと変えている。セラフィーンは二つの姉妹都市のために歌い、住民たちに自分たちが独りではないこと、団結すればより強くなれること、そして、彼女の目には無限の可能性が見えていることを伝えようとしている。", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 25, "movespeed": 330, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.669}, "spells": [{"id": "SeraphineQ", "name": "ハイノート", "description": "一定範囲内にダメージを与える。", "tooltip": "純粋な音を発射して、<magicDamage>{{ explosiondamage }}の魔法ダメージ</magicDamage>を与える。チャンピオンに対してはダメージが対象の減少体力に応じて増加し、体力が{{ executethreshold*100 }}%を下回っていると最大の<magicDamage>{{ totalempowereddamage }}ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "SeraphineQ.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SeraphineW", "name": "サラウンドサウンド", "description": "周囲の味方にシールドを付与して移動速度を増加させる。自身がすでにシールドを獲得している場合は、周囲の味方の体力を回復する。", "tooltip": "歌で周囲の味方チャンピオンを活気づけ、{{ shieldduration }}秒間、味方には<speed>{{ hastevalueallies }}の増加移動速度</speed>、自身には<speed>{{ wmsbonustotal }}の増加移動速度</speed>(効果時間をかけて減衰)を与え、両方に<shield>耐久値{{ shieldvalueseraphine }}のシールド</shield>を付与する。<br /><br />自身がすでに<shield>シールド</shield>を獲得していた場合は味方に声をかけ、{{ whealsplitdelay }}秒後に、周囲の味方チャンピオン1体につき<healing>減少体力の{{ wmissinghpheal }}%</healing>の体力を回復させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "回復の割合", "@AbilityResourceName@コスト"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ wmissinghpbase }} -> {{ wmissinghpbaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 22, 22, 22, 22], "cooldownBurn": "22", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SeraphineW.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SeraphineE", "name": "ビートドロップ", "description": "直線上の敵にダメージと移動妨害効果を与える。", "tooltip": "強烈な音波を発射して直線上の敵に<magicDamage>{{ finaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowvalue }}%の<status>スロウ効果</status>を付与する。<br /><br />すでに<status>スロウ効果</status>を受けている敵には代わりに<status>スネア効果</status>を与え、<status>移動不能効果</status>を受けている敵は<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果時間", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "SeraphineE.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SeraphineR", "name": "アンコール", "description": "敵にダメージとチャーム効果を与え、味方および敵のチャンピオンに触れるたびに射程がリフレッシュされていく。", "tooltip": "ステージに上がって魅惑的な力を発射し、{{ rchannelduration }}秒間の<status>チャーム効果</status>と<magicDamage>{{ r1totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />これに触れたチャンピオン(味方を含む)はパフォーマンスの一部となり、このスキルの射程が延長される。味方チャンピオンは最大の<keywordMajor>「ノート」</keywordMajor>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "チャーム効果時間", "クールダウン"], "effect": ["{{ r1basedamage }} -> {{ r1basedamageNL }}", "{{ rchannelduration }} -> {{ rchanneldurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SeraphineR.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ステージプレゼンス", "description": "通常スキルを3回使用すると、3回目のスキルが2連続で発動する。さらに味方の近くでスキルを使用すると、自身の次の通常攻撃は射程が増加し、追加魔法ダメージを与える。", "image": {"full": "Seraphine_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}