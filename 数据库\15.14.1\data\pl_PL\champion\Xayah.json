{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Xayah": {"id": "<PERSON><PERSON><PERSON>", "key": "498", "name": "<PERSON><PERSON><PERSON>", "title": "Buntowniczka", "image": {"full": "Xayah.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "498000", "num": 0, "name": "default", "chromas": false}, {"id": "498001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "498002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "498003", "num": 3, "name": "SSG Xayah", "chromas": false}, {"id": "498004", "num": 4, "name": "Czarodziejka Gwiazd Xayah", "chromas": true}, {"id": "498008", "num": 8, "name": "<PERSON><PERSON><PERSON> z Prastarej <PERSON>i", "chromas": true}, {"id": "498017", "num": 17, "name": "Odważny Feniks Xayah", "chromas": true}, {"id": "498026", "num": 26, "name": "Odważny Feniks X<PERSON>h (Prestiżowa)", "chromas": false}, {"id": "498028", "num": 28, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498037", "num": 37, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498038", "num": 38, "name": "Odkupiona Czarodziejka Gwiazd Xayah", "chromas": false}, {"id": "498047", "num": 47, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498057", "num": 57, "name": "Xayah z Akademii Bojowej", "chromas": true}], "lore": "Niebezpieczna i dokładna Xayah to vastajańska rewolucjonistka, która toczy prywatną wojnę, aby ocal<PERSON> swój lud. Wykorzystuje swoją szy<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i ostre jak brzytwa ostrza, aby pozbyć się każdego, kto stanie jej na drodze. Xayah walczy u boku partnera i kochanka, Ra<PERSON><PERSON>, aby chronić swoje wymierające plemię i przywrócić swojej rasie dawną chwałę.", "blurb": "Niebezpieczna i dokładna Xayah to vastajańska rewolucjonistka, która toczy prywatną wojnę, aby o<PERSON><PERSON> swój lud. Wykorzystuje swoj<PERSON> szy<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i ostre jak brzytwa ostrza, aby pozbyć się każdego, kto stanie jej na drodze. Xayah walczy u boku...", "allytips": ["Ataki i umiejętności Xayah pozostawiają na ziemi pióra, które może później przyzwać do siebie. Dzięki temu zyskuje możliwość kontroli pewnego obszaru i zadania tam znacznych obrażeń.", "<PERSON>ayah może użyć Nawałnicy Piór, aby uniknąć praktycznie każdej umiejętności i jednocześnie stworzyć multum piór. Postaraj się wykorzystać ofensywne i defensywne aspekty tej umiejętności."], "enemytips": ["Wezwanie Ostrzy Xayah unieruchomi cel tylko po trafieniu trzema lub więcej powracającymi Piórami.", "Długie starcia z Xayah w jednym miejscu umożliwią jej rozrzucenie wielu Piór. Pozostawaj w ruchu!", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> jesteś w pełnej got<PERSON>, g<PERSON> <PERSON><PERSON> j<PERSON> zabi<PERSON>. Brak możliwości obrania jej za cel podczas Nawałnicy Piór szybko może obrócić zasadzkę w sytuację sprzyjającą Xayah."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 6, "magic": 1, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 340, "mpperlevel": 40, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.25, "hpregenperlevel": 0.75, "mpregen": 8.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.9, "attackspeed": 0.658}, "spells": [{"id": "XayahQ", "name": "Bliźniacze Sztylety", "description": "Xayah rzuca dwoma sztyletami, zadając obrażenia i rozrzucając Pióra, które może przyzwać.", "tooltip": "<PERSON>ayah rzuca dwoma sztyletami, z<PERSON><PERSON>ą<PERSON> <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> każdym z nich i pozostawiając dwa <keywordMajor>pióra</keywordMajor>. Kolejne trafione cele otrzymują <physicalDamage>{{ multihitdamage }} pkt obrażeń</physicalDamage> od każdego sztyletu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [45, 60, 75, 90, 105], [0.5, 0.5, 0.5, 0.5, 0.5], [0.334, 0.334, 0.334, 0.334, 0.334], [0.584, 0.584, 0.584, 0.584, 0.584], [3500, 3500, 3500, 3500, 3500], [3500, 3500, 3500, 3500, 3500], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/60/75/90/105", "0.5", "0.33", "0.58", "3500", "3500", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "XayahQ.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "XayahW", "name": "Zabójcze Upierzenie", "description": "<PERSON>ayah wywołuje burzę o<PERSON>rzy, która zwiększa jej prędko<PERSON><PERSON> ataku oraz obrażenia. Je<PERSON>li zaatakuje bohatera, burza zwiększy również jej prędkość ruchu.", "tooltip": "<PERSON>ayah wywołuje trwającą {{ e2 }} sek. bur<PERSON><PERSON> o<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <attackSpeed>{{ e1 }}% prędko<PERSON><PERSON> ataku</attackSpeed> i sprawi<PERSON><PERSON>, że jej ataki wystrzeliwuj<PERSON> drugi sztylet, kt<PERSON><PERSON> zadaje {{ bonusdamagepercent }}% obrażeń.<br /><br />Gdy drugi sztylet trafi bohatera, <PERSON><PERSON><PERSON> zysku<PERSON> <speed>{{ e3 }}% prędkości ruchu</speed> na {{ e4 }} sek.<br /><br />Jeśli Rakan znajduje się w pobliżu, on również otrzyma efekty tej umiej<PERSON>, ale zyska <speed>prędkość ruchu</speed> tylko wtedy, gdy <i>Xayah</i> trafi cel.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [4, 4, 4, 4, 4], [30, 30, 30, 30, 30], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 20, 20, 20, 20], [1000, 1000, 1000, 1000, 1000], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "4", "30", "1.5", "20", "1000", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XayahW.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "XayahE", "name": "Wezwanie Ostrzy", "description": "Xayah przyzywa wszystkie rozrzucone Pióra, zadając obrażenia i unieruchamiając wrogów.", "tooltip": "Xayah przyzywa do siebie wszystkie <keywordMajor>pi<PERSON>ra</keywordMajor>, zada<PERSON><PERSON><PERSON> ka<PERSON> z nich <physicalDamage>{{ featherdamage }} pkt. obrażeń fizycznych</physicalDamage>. <PERSON><PERSON><PERSON> wróg zostanie trafiony co najmniej {{ featherthreshold }} <keywordMajor>piórami</keywordMajor>, zostanie <status>unieruchomiony</status> na {{ rootduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [2000, 2000, 2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "XayahE.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "XayahR", "name": "Nawałnica Piór", "description": "Xayah wyskakuje w powietrze i staje się niemożliwa do obrania za cel, jednocześnie rzucając wachlarzem sztyletów i rozrzucając Pióra, które może przyzwać.", "tooltip": "<PERSON><PERSON><PERSON> wyskakuje w powietrze, nie można jej obrać na cel i zyskuje przenikanie na 1,5 sek. Następnie rzuca sztyletami, kt<PERSON>re zadają <physicalDamage>{{ damage }} pkt. obrażeń fizycznych</physicalDamage> i pozostawiają po sobie linię <keywordMajor>piór</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "XayahR.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Po użyciu umiejętności następny atak podstawowy Xayah trafi wszystkie cele na jej drodze i pozostawi <font color='#C200E1'>Pióro</font>.", "image": {"full": "XayahPassive.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}