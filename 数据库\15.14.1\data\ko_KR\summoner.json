{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "방어막", "description": "잠시 동안 보호막을 얻습니다.", "tooltip": "{{ shieldduration }}초 동안 <shield>{{ shieldstrength }}의 피해를 흡수하는 보호막</shield>을 얻습니다.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "소모값 없음", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerBoost": {"id": "SummonerBoost", "name": "정화", "description": "챔피언에게 걸린 모든 이동 불가와 (제압 및 공중으로 띄우는 효과 제외) 소환사 주문에 의한 해로운 효과를 제거하고 강인함을 증가시킵니다.", "tooltip": "모든 군중 제어 효과(<keyword>제압</keyword> 및 <keyword>공중에 뜸</keyword> 제외)와 소환사 주문에 의한 해로운 효과를 제거하고 {{ tenacityduration }}초 동안 {{ tenacityvalue*100 }}%의 강인함을 얻습니다.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "소모값 없음", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "점멸", "description": "커서 방향으로 챔피언이 짧은 거리를 순간이동합니다.", "tooltip": "커서 방향으로 챔피언이 짧은 거리를 순간이동합니다.<br /><br />한 라운드 동안 다시 사용할 수 없습니다. <rules>(한 라운드는 구매 단계와 전투 단계로 구성되어 있습니다.)</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "소모값 없음", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "도주", "description": "적 챔피언으로부터 달아나는 동안 잠시 이동 속도가 대폭 증가합니다.", "tooltip": "<keywordMajor>사용 스킬 슬롯:</keywordMajor> 소환사 주문을 부여하는 증강이 이 슬롯을 대체합니다.<br /><br />{{ duration }}초 동안 <moveSpeed>이동 속도가 {{ basems*100 }}%</moveSpeed> 증가합니다. 뒤에 있는 적 하나당 이동 속도가 {{ bonusmsperenemybehind*100 }}% 증가합니다.", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "소모값 없음", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerDot": {"id": "SummonerDot", "name": "점화", "description": "대상 적 챔피언에게 지속 고정 피해를 입히고 그동안 체력 회복 효과를 감소시킵니다.", "tooltip": "대상 적 챔피언에게 5초에 걸쳐 <trueDamage>{{ tooltiptruedamagecalculation }}의 고정 피해</trueDamage>를 입히고 그동안 <keyword>{{ grievousamount*100 }}%의 고통스러운 상처</keyword>를 적용합니다.<br /><br /><keyword>상처</keyword>: 치유 및 회복 효과를 감소시킵니다.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "소모값 없음", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "탈진", "description": "대상 적 챔피언을 둔화시키고 입히는 피해량을 감소시킵니다.", "tooltip": "대상 적 챔피언을 {{ debuffduration }}초 동안 {{ slow }}% <keyword>둔화</keyword>시키고 그동안 가하는 피해량을 {{ damagereduction }}% 감소시킵니다.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "소모값 없음", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerFlash": {"id": "SummonerFlash", "name": "점멸", "description": "커서 방향으로 짧은 거리를 순간이동합니다.", "tooltip": "커서 방향으로 짧은 거리를 순간이동합니다.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "소모값 없음", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerHaste": {"id": "SummonerHaste", "name": "유체화", "description": "이동 속도가 상승하고 지속시간 동안 유닛과의 충돌을 무시합니다.", "tooltip": "{{ duration }}초 동안 <speed>{{ movespeedmod }}의 이동 속도</speed>를 얻고 <keyword>유체화</keyword> 상태가 됩니다.<br /><br /><keyword>유체화</keyword>: 다른 유닛과 충돌하지 않습니다.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "소모값 없음", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerHeal": {"id": "SummonerHeal", "name": "회복", "description": "자신과 대상 아군 챔피언의 체력을 회복시키고 이동 속도를 증가시킵니다.", "tooltip": "자신과 대상 아군 챔피언의 <healing>체력을 {{ totalheal }}</healing> 회복하고 {{ movespeedduration }}초 동안 <speed>이동 속도를 {{ movespeed*100 }}%</speed> 올려줍니다.<br /><br /><rules>대상을 지정하지 않고 사용하는 경우, 범위 내에서 가장 큰 부상을 입은 아군 챔피언에게 시전됩니다.<br />최근 회복 소환사 주문의 영향을 받은 유닛의 경우 치유량이 절반만 적용됩니다.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "소모값 없음", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "총명", "description": "자신과 아군 챔피언의 마나를 회복시킵니다.", "tooltip": "최대 마나의 {{ e1 }}%를 회복하고 주변 아군의 마나를 최대 마나의 {{ e2 }}%만큼 회복시켜줍니다.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "소모값 없음", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "왕을 향해!", "description": "포로 왕의 곁으로 빠르게 이동합니다.", "tooltip": "<span class=\"colorFFE076\">기본 지속 효과:</span> 포로로 적 챔피언을 맞히면 아군에게 포로 표식이 생깁니다. 포로 표식이 10개가 되면 아군은 포로 왕을 소환하여 함께 싸울 수 있습니다. 포로 왕이 활성화된 동안에는 어느 팀도 포로 표식을 획득할 수 없습니다.<br /><br /><span class=\"colorFFE076\">사용 시:</span> 포로 왕 쪽으로 빠르게 질주합니다. 아군이 포로 왕을 소환한 동안에만 사용할 수 있습니다. <br /><br /><i><span class=\"colorFDD017\">''포로들이 줄을 당깁니다. 여러분은 몸을 맡기기만 하면 됩니다.''</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "소모값 없음", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "포로 던지기", "description": "포로를 적에게 던집니다. 적이 맞으면 해당 적에게 빠르게 이동할 수 있습니다.", "tooltip": "포로를 멀리 던져서 첫 번째로 맞는 적에게 {{ f2 }}의 고정 피해를 입히고 대상에 대한 <span class=\"coloree91d7\">절대 시야</span>를 얻습니다.<br /><br />적을 맞히면 3초 안에 이 스킬을 다시 사용하여 맞힌 대상에게 질주할 수 있습니다. 대상에게 질주해 가면 {{ f2 }}의 추가 고정 피해를 입히고 포로 던지기의 재사용 대기시간이 {{ e4 }}초 감소합니다.<br /><br />포로는 마법이 아니라 동물이므로, 주문 보호막이나 바람 장벽으로 막을 수 없습니다!<br /><br /><i><span class=\"colorFDD017\">''룬테라 공기 역학의 결정판, 포로!''</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "소모값 없음", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerSmite": {"id": "SummonerSmite", "name": "강타", "description": "몬스터 또는 미니언에게 고정 피해를 입힙니다.", "tooltip": "대상 대형 몬스터 및 공격로 미니언에게 <trueDamage>{{ smitebasedamage }}의 고정 피해</trueDamage>를 입힙니다.<br /><br />소환수에게 <trueDamage>{{ firstpvpdamage }}의 고정 피해</trueDamage>를 입힙니다.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "소모값 없음", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "표식", "description": "적을 향해 직선으로 눈덩이를 던집니다. 눈덩이가 적을 맞히면 표식이 남아 절대 시야가 생기고, 표식이 붙은 대상을 향해 챔피언이 빠르게 이동할 수 있습니다.", "tooltip": "멀리 눈덩이를 던져 첫 번째 맞은 적에게 {{ tooltipdamagetotal }}의 고정 피해를 입히고 대상에 대한 <span class=\"coloree91d7\">절대 시야</span>를 얻습니다. 적을 맞히면 이 스킬을 {{ e3 }}초 내에 재사용하여 표식이 남은 유닛에게 돌진하여 추가로 {{ tooltipdamagetotal }}의 고정 피해를 입힙니다. 대상에게 돌진하면 표식의 재사용 대기시간이 {{ e4 }}% 감소합니다.<br /><br /><span class=\"colorFFFF00\">표식의 투사체는 주문 보호막이나 투사체 약화 효과로 막을 수 없습니다.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "소모값 없음", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "표식", "description": "적을 향해 직선으로 눈덩이를 던집니다. 눈덩이가 적을 맞히면 표식이 남아 절대 시야가 생기고, 표식이 붙은 대상을 향해 챔피언이 빠르게 이동할 수 있습니다.", "tooltip": "멀리 눈덩이를 던져 첫 번째 맞은 적에게 {{ tooltipdamagetotal }}의 고정 피해를 입히고 대상에 대한 <span class=\"coloree91d7\">절대 시야</span>를 얻습니다. 적을 맞히면 이 스킬을 {{ e3 }}초 내에 재사용하여 표식이 남은 유닛에게 돌진하여 추가로 {{ tooltipdamagetotal }}의 고정 피해를 입힙니다. 대상에게 돌진하면 표식의 재사용 대기시간이 {{ e4 }}% 감소합니다.<br /><br /><span class=\"colorFFFF00\">표식의 투사체는 주문 보호막이나 투사체 약화 효과로 막을 수 없습니다.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "소모값 없음", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "순간이동", "description": "짧은 정신 집중이 끝나면 대상으로 지정할 수 없는 상태가 되고 아군 유닛에게로 이동합니다. 강력 순간이동으로 업그레이드되어 이동 속도가 크게 증가합니다. ", "tooltip": "{{ channelduration }}초 동안 정신 집중을 한 후 <keyword>대상으로 지정할 수 없는 상태</keyword>가 되어 대상 아군 구조물, 미니언, 혹은 와드로 이동합니다. <br /><br />{{ upgrademinute }}분에 강력 순간이동으로 업그레이드되어 이동 속도가 크게 증가합니다.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "소모값 없음", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "게임 시작 후 결정", "description": "이 슬롯은 게임 시작 시 선택한 다른 챔피언의 궁극기로 대체됩니다. 궁극기 선택 시간이 30초 주어집니다. 준비하세요!", "tooltip": "선택한 궁극기 소환사 주문으로 대체됩니다.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "TBD 및 공격-강타", "description": "이 슬롯은 다른 챔피언의 궁극기로 대체되며 공격-강타 또한 획득합니다. 궁극기 선택 시간이 30초 주어집니다. 준비하세요!", "tooltip": "궁극기 소환사 주문으로 대체됩니다.<br /><br />공격-강타를 획득합니다. 공격-강타는 이로운 효과를 주는 아군 몬스터, 에픽 몬스터, 바위 게를 공격 시 처형합니다.<br /><br /><attention>공격-강타는 재사용 대기시간이 없습니다.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}