{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "<PERSON>rma", "title": "the Enlightened One", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "Sun Goddess Karma", "chromas": false}, {"id": "43002", "num": 2, "name": "Sakura Karma", "chromas": false}, {"id": "43003", "num": 3, "name": "Traditional Karma", "chromas": false}, {"id": "43004", "num": 4, "name": "Order of the Lotus Karma", "chromas": false}, {"id": "43005", "num": 5, "name": "Warden <PERSON>", "chromas": false}, {"id": "43006", "num": 6, "name": "Winter Wonder Karma", "chromas": false}, {"id": "43007", "num": 7, "name": "Con<PERSON><PERSON>", "chromas": true}, {"id": "43008", "num": 8, "name": "Dark Star Karma", "chromas": true}, {"id": "43019", "num": 19, "name": "Dawnbringer Karma", "chromas": false}, {"id": "43026", "num": 26, "name": "Odyssey Karma", "chromas": false}, {"id": "43027", "num": 27, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "43044", "num": 44, "name": "Tranquility Dragon Karma", "chromas": false}, {"id": "43054", "num": 54, "name": "Faerie Queen <PERSON>rma", "chromas": false}, {"id": "43061", "num": 61, "name": "Infernal Karma", "chromas": false}, {"id": "43070", "num": 70, "name": "Spirit Blossom Karma", "chromas": false}], "lore": "Tidak ada manusia yang menunjukkan tradisi spiritual <PERSON><PERSON>. Dia adalah perwujudan reinkarnasi jiwa kuno yang telah dibangkitkan berkali-kali, membawa semua ingatannya yang terkumpul ke setiap kehidupan barunya. Dia juga dianugerahi kekuatan yang hanya bisa dimengerti segelintir orang. Dia berusaha sebaik mungkin untuk menuntun kaumnya di masa sulit, meski dia tahu bahwa kedamaian dan keharmonisan bisa memerlukan pengorbanan besar, baik dari dirinya maupun negeri tercintanya.", "blurb": "Tidak ada manusia yang menu<PERSON>kkan tradisi spiritual <PERSON><PERSON>. Dia adalah perwujudan reinkarnasi jiwa kuno yang telah dibangkitkan berkali-kali, membawa semua ingatannya yang terkumpul ke setiap kehidupan barunya. Dia juga dianugerahi...", "allytips": ["Gathering Fire cocok untuk kamu yang suka bermain agresif. Serang musuh dengan ability dan basic attack untuk menurunkan cooldown Mantra dan terus menekan lawan.", "Saat menggunakan Focused Resolve, terapkan slow ke musuh menggunakan Inner Flame atau tingkatkan kecepatanmu dengan Inspire jika kesulitan mengejar target. ", "<PERSON>an terlalu pelit menggunakan Mantra. Gathering Fire paling kuat saat pertempuran tim, membuatnya mudah untuk mengisi ulang Mantra berkali-kali."], "enemytips": ["<PERSON><PERSON><PERSON> cooldown Mantra saat dia mengenai champion musuh dengan Ability dan basic attack. <PERSON><PERSON> biarkan dia mendapatkan serangan bebas kepadamu.", "Soulflare <PERSON>rma meledak untuk memberikan damage bonus di area tempatnya dipasang. Cepat bereaksi dan kabur dari lingkaran untuk menghindari damage besar.", "Focused Resolve adalah alat bagus untuk mundur. Buat jarak agar terhindar dari efek Root dan cari kesempatan menyerang lagi setelahnya."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "Inner Flame", "description": "<PERSON>rma mengiri<PERSON>kan bola energi roh yang meledak dan menghasilkan damage saat mengenai unit musuh.<br><br>Bonus Mantra: <PERSON><PERSON>, <PERSON><PERSON> meningkatkan daya hancur Inner Flame, menciptakan cataclysm yang menghasilkan damage set<PERSON>h jeda singkat.", "tooltip": "<PERSON><PERSON> men<PERSON>n ledakan energi, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> pada target pertama yang terkena dan musuh di sekitarnya, serta menerapkan <status>Slow</status> sebesar {{ slowamount*-100 }}% selama {{ slowduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSpiritBind", "name": "Focused Resolve", "description": "Karma menciptakan tether antara dirinya dengan target musuh, <PERSON><PERSON><PERSON><PERSON><PERSON> damage dan mengungkap mereka. Jika tether tidak rusak, musuh akan terkena root dan damage lagi.<br><br>Bonus Mantra: Karma memperkuat kaitan ini, memberi heal untuk dirinya sendiri dan memperpanjang durasi root.", "tooltip": "Karma mengikatkan dirinya dengan champion atau monster jungle, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ initialdamage }} magic damage</magicDamage> dan mengungkap mereka selama {{ tetherduration }} dtk. Jika tether-nya tidak terputus, target akan men<PERSON>ma <magicDamage>{{ initialdamage }} magic damage</magicDamage> lagi dan terkena <status>Root</status> selama {{ rootduration }} dtk.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ rootduration }}-> {{ rootdurationNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSolKimShield", "name": "Inspire", "description": "Karma memanggil shield pelindung yang menyerap damage yang akan datang dan meningkatkan Move Speed sekutu yang dilindungi.<br><br>Bonus Mantra: Energi terpancar keluar dari target, memperkuat shield awal dan menerapkan Inspire pada champion sekutu di sekitar.", "tooltip": "Karma memberi champion se<PERSON><PERSON> <shield>{{ totalshield }} Shield</shield> selama {{ shieldduration }} dtk dan <speed>{{ movespeed*100 }}% Move Speed</speed> selama {{ movespeedduration }} dtk.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Jumlah Shield", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ baseshield }}-> {{ baseshieldNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Mantra", "description": "Karma memperkuat ability-nya yang berikutnya untuk memberikan efek tambahan. Mantra tersedia di level 1 dan tidak memerlukan poin skill.", "tooltip": "Karma memperkuat Ability berikutnya yang dia gunakan dalam 8 detik.<br /><li><spellName>Inner Flame</spellName>: <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ rqimpactdamage }} magic damage</magicDamage> tambahan dan meninggalkan lingkaran api, menerapkan <status>Slow</status> pada musuh dan menghasilkan <magicDamage>{{ rqfielddamage }} magic damage</magicDamage> tambahan.<li><spellName>Focused Resolve</spellName>: Karma akan memulihkan <healing>{{ rwhealamount }} Health yang hilang</healing> di awal dan di akhir tether, dan menerapkan <status>Root</status> selama {{ rwbonusroot }} dtk lebih lama.<li><spellName>Inspire</spellName>: Karma melindungi targetnya dengan <shield>{{ rebonusshield }} Shield</shield> lagi, dan juga melindungi sekutu di dekat targetnya, memberinya <shield>{{ rebonusshieldarea }} Shield</shield> dan <speed>{{ removespeed*100 }}% Move Speed</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Soulflare Impact", "Damage Soulflare Circle", "Renewal Root Extension", "Shield Defiance", "Cooldown"], "effect": ["{{ qbonusdamage }}-> {{ qbonusdamageNL }}", "{{ qdetonationdamage }}-> {{ qdetonationdamageNL }}", "{{ rwbonusroot }}-> {{ rwbonusrootNL }}", "{{ ebonusshield }}-> {{ ebonusshieldNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}], "passive": {"name": "Gathering Fire", "description": "Ability penghasil damage dari <PERSON>rma akan mengu<PERSON>i cooldown Mantra.", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}