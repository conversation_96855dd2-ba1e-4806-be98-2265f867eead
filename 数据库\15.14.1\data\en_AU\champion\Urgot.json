{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Urgot": {"id": "Urgot", "key": "6", "name": "Urgot", "title": "the Dreadnought", "image": {"full": "Urgot.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "6000", "num": 0, "name": "default", "chromas": false}, {"id": "6001", "num": 1, "name": "Giant Enemy Crabgot", "chromas": false}, {"id": "6002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "6003", "num": 3, "name": "Battlecast Urgot", "chromas": true}, {"id": "6009", "num": 9, "name": "High Noon Urgot", "chromas": true}, {"id": "6015", "num": 15, "name": "Pajama Guardian Cosplay Urgot", "chromas": true}, {"id": "6023", "num": 23, "name": "Fright Night Urgot", "chromas": true}, {"id": "6032", "num": 32, "name": "<PERSON><PERSON><PERSON> the Clogfather", "chromas": true}], "lore": "Once a powerful Noxian headsman, <PERSON><PERSON><PERSON> was betrayed by the empire for which he had killed so many. Bound in iron chains, he was forced to learn the true meaning of strength in the Dredge—a prison mine deep beneath Zaun. Emerging in a disaster that spread chaos throughout the city, he now casts an imposing shadow over its criminal underworld. Raising his victims on the very chains that once enslaved him, he will purge his new home of the unworthy, making it a crucible of pain.", "blurb": "Once a powerful Noxian headsman, <PERSON><PERSON><PERSON> was betrayed by the empire for which he had killed so many. Bound in iron chains, he was forced to learn the true meaning of strength in the Dredge—a prison mine deep beneath Zaun. Emerging in a disaster that...", "allytips": ["Pay attention to your individual leg's cooldowns, as they make for a signficiant portion of your damage", "Land Corrosive Charge or Disdain to lock on to target's with Purge - a great way to trigger multiple legs in rapid succession.", "Save Fear Beyond Death for opponents you know are already too weak to survive. It's especially good for picking off fleeing foes."], "enemytips": ["<PERSON><PERSON><PERSON> relies heavily on blasting opponents with his legs, which have their own cooldowns and only detonate when he attacks in the direction they are facing. Avoid getting hit by multiple.", "<PERSON><PERSON><PERSON> can deal and absorb tremendous amounts of damage with P<PERSON>, but slows himself while firing.", "If you are struck by Fear Beyond Death, do your best to avoid falling below the execution threshold (25% of your Maximum Health) until the effect times out."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 102, "mp": 340, "mpperlevel": 45, "movespeed": 330, "armor": 36, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 350, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 4, "attackspeedperlevel": 3.75, "attackspeed": 0.625}, "spells": [{"id": "UrgotQ", "name": "Corrosive Charge", "description": "Fires an explosive charge at the target location, dealing physical damage and slowing enemies caught in the explosion.", "tooltip": "Urgot fires an explosive charge, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> and <status>Slowing</status> by {{ slowamount*100 }}% for {{ slowduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "Slow"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "UrgotQ.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotW", "name": "Purge", "description": "<PERSON><PERSON><PERSON> slows himself while he unloads his weapon on nearby enemies. Prioritizes enemy champions <PERSON><PERSON><PERSON> has recently struck with other abilities and triggers Echoing Flames.", "tooltip": "<spellPassive>Passive:</spellPassive> <PERSON><PERSON><PERSON>'s other Abilities mark the last champion hit for 5 seconds.<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON><PERSON> begins firing his chain gun at the closest enemy, prioritizing marked enemies. He Attacks them {{ e8 }} times a second dealing <physicalDamage>{{ damagepershot }} physical damage</physicalDamage> per shot. <PERSON><PERSON><PERSON> can move while firing and has {{ e2 }}% <status>Slow</status> resistance, but loses <speed>{{ e5 }} Move Speed</speed>.<br /><br />At max rank, this Ability lasts indefinitely and can be <toggle>Toggled</toggle> on and off.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Attack Damage Per Shot", "@AbilityResourceName@ Cost"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e7 }}% -> {{ e7NL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 9, 6, 3, 0], "cooldownBurn": "12/9/6/3/0", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [40, 40, 40, 40, 40], [4, 4, 4, 4, 25000], [0, 0, 0, 0, 0], [125, 125, 125, 125, 125], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 23.5, 27, 30.5, 34], [3, 3, 3, 3, 3], [12, 12, 12, 12, 12], [50, 50, 50, 50, 50]], "effectBurn": [null, "30/60/90/120/150", "40", "4/4/4/4/25000", "0", "125", "0.5", "20/23.5/27/30.5/34", "3", "12", "50"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "UrgotW.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotE", "name": "Disdain", "description": "<PERSON><PERSON><PERSON> charges in a direction, shielding himself and trampling non-champion enemies. If he catches an enemy champion, he will stop and hurl them out of his way.", "tooltip": "Urgot charges forward, gaining <shield>{{ etotalshieldhealth }} Shield</shield> for {{ eshieldduration }} seconds. The first champion hit is <status>Stunned</status> for {{ stunduration }} seconds and thrown behind <PERSON><PERSON><PERSON>. All enemies U<PERSON><PERSON> collides with take <physicalDamage>{{ edamage }} physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Shield Amount", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ ebasedamage }} -> {{ ebasedamageNL }}", "{{ eshieldbasehealth }} -> {{ eshieldbasehealthNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "UrgotE.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotR", "name": "Fear Beyond Death", "description": "<PERSON><PERSON><PERSON> fires a chem-drill that impales the first enemy champion hit. If that champion falls below a health threshold, <PERSON><PERSON><PERSON> judges them weak and can execute them.", "tooltip": "Urgot fires a chem-drill, impaling the first champion hit, dealing <physicalDamage>{{ rcalculateddamage }} physical damage</physicalDamage> and <status>Slowing</status> for {{ rslowduration }} seconds by 1% per 1% missing Health, up to {{ rmovespeedmod }}%.<br /><br />If the impaled victim falls below {{ rhealththreshold }}% Health, Urgot can <recast>Recast</recast> this Ability, <status>Suppressing</status> the victim and dragging them to himself. On reaching Urgot, they are killed and nearby enemies are <status>Feared</status> for {{ rfearduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "UrgotR.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Echoing Flames", "description": "<PERSON><PERSON><PERSON>'s basic attacks and Purge periodically trigger blasts of flame from his legs, dealing physical damage.", "image": {"full": "Urgot_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}