{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MonkeyKing": {"id": "MonkeyKing", "key": "62", "name": "오공", "title": "원숭이 왕", "image": {"full": "MonkeyKing.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "62000", "num": 0, "name": "default", "chromas": false}, {"id": "62001", "num": 1, "name": "불원숭이 오공", "chromas": false}, {"id": "62002", "num": 2, "name": "제천대성 오공", "chromas": false}, {"id": "62003", "num": 3, "name": "수룡 오공", "chromas": true}, {"id": "62004", "num": 4, "name": "언더월드 오공", "chromas": false}, {"id": "62005", "num": 5, "name": "찬란한 오공", "chromas": false}, {"id": "62006", "num": 6, "name": "안개의 창기병 오공", "chromas": false}, {"id": "62007", "num": 7, "name": "전투사관학교 오공", "chromas": true}, {"id": "62016", "num": 16, "name": "나무정령 오공", "chromas": true}], "lore": "오공은 바스타야로, 자신의 뛰어난 힘, 민첩성과 지혜로 상대방을 혼란에 빠뜨려 유리한 고지를 차지하는 악동이다. 마스터 이로 알려진 평생지기를 찾은 뒤, 오공은 우주류로 알려진 고대 무술의 마지막 제자가 되었다. 마법봉으로 무장한 오공은 아이오니아의 파멸을 막고자 한다.", "blurb": "오공은 바스타야로, 자신의 뛰어난 힘, 민첩성과 지혜로 상대방을 혼란에 빠뜨려 유리한 고지를 차지하는 악동이다. 마스터 이로 알려진 평생지기를 찾은 뒤, 오공은 우주류로 알려진 고대 무술의 마지막 제자가 되었다. 마법봉으로 무장한 오공은 아이오니아의 파멸을 막고자 한다.", "allytips": ["근두운 급습과 분신술을 조합하면 적이 반격하기 전에 빠르게 치고 빠질 수 있습니다.", "수풀 근처에서 분신술을 사용하여 적이 과민하게 반응하도록 유도하십시오."], "enemytips": ["오공은 자주 근두운 급습을 사용한 다음 분신술을 사용합니다. 잠시 기다렸다가 오공 본체를 확인한 다음 스킬을 사용하십시오.", "오공은 적에게 둘러싸여 있을 때 더 강해집니다. 먼저 고립시킨 다음 처치할 기회를 노리세요."], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 330, "mpperlevel": 65, "movespeed": 340, "armor": 31, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.69}, "spells": [{"id": "MonkeyKingDoubleAttack", "name": "파쇄격", "description": "오공의 다음 공격 사거리가 증가하고 추가 피해를 입히며 맞은 대상의 방어력이 몇 초 동안 낮아집니다.", "tooltip": "오공과 <keywordMajor>분신</keywordMajor>이 다음 공격 시 사거리가 {{ attackrangebonus }} 증가하고 <physicalDamage>{{ bonusdamagett }}의 물리 피해</physicalDamage>를 추가로 입히며 {{ shredduration }}초 동안 대상의 <scaleArmor>방어력이 {{ armorshredpercent*100 }}%</scaleArmor> 감소합니다.<br /><br />오공이나 오공의 <keywordMajor>분신</keywordMajor>이 기본 공격 및 스킬로 적을 공격할 때마다 이 스킬의 재사용 대기시간이 {{ cooldowndecrease }}초 감소합니다.<br /><br /><rules>이 스킬은 피해를 입힐 때 효과가 발동합니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "방어 감소 %", "사거리", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ armorshredpercent*100.000000 }}% -> {{ armorshredpercentnl*100.000000 }}%", "{{ attackrangebonus }} -> {{ attackrangebonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [250, 275, 300, 325, 350], "rangeBurn": "250/275/300/325/350", "image": {"full": "MonkeyKingDoubleAttack.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MonkeyKingDecoy", "name": "분신 전사", "description": "오공이 잠깐 동안 <font color='#91d7ee'>투명</font> 상태가 되고 지정한 방향으로 돌진하며 근처 적을 공격하는 분신을 남깁니다.", "tooltip": "오공이 돌격하며 {{ stealthduration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 되고 {{ cloneduration }}초 동안 움직이지 않는 <keywordMajor>분신</keywordMajor>을 생성합니다.<br /><br /><keywordMajor>분신</keywordMajor>은 오공의 궁극기를 모방하여 오공이 최근에 피해를 입힌 근처 적을 공격해 기존 피해량의 {{ clonedamagemod*100 }}%만큼 피해를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량 %", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ clonedamagemod*100.000000 }}% -> {{ clonedamagemodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "MonkeyKingDecoy.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MonkeyKingNimbus", "name": "근두운 급습", "description": "오공이 대상에게 돌격하는 동시에 자신의 분신을 만들어 대상 근처에 있는 적들에게 추가 공격을 하여 피해를 줍니다.", "tooltip": "오공이 적에게 돌격하며 자신의 <keywordMajor>분신</keywordMajor>을 만들어 근처의 적 최대 {{ extratargets }}명에게 돌격시킵니다. 적중당한 적은 각각 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입습니다. 오공과 <keywordMajor>분신</keywordMajor>은 {{ attackspeedduration }}초 동안 <attackSpeed>{{ attackspeed*100 }}%의 공격 속도</attackSpeed>를 얻습니다.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "공격 속도", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.25, 8.5, 7.75, 7], "cooldownBurn": "10/9.25/8.5/7.75/7", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MonkeyKingNimbus.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MonkeyKingSpinToWin", "name": "회전격", "description": "오공이 여의봉을 늘린 후 빙빙 휘두르며 이동 속도가 증가합니다.<br><br>적중당한 적은 피해를 입고 공중에 떠오릅니다.", "tooltip": "오공이 <speed>{{ movespeed*100 }}%의 이동 속도</speed>를 얻고 {{ spinduration }}초 동안 여의봉을 휘두릅니다. 여의봉에 맞은 근처 적들은 {{ knockupduration }}초 동안 <status>공중에</status> 뜨며 <physicalDamage>{{ totaldamagett }}+최대 체력의 {{ percenthpdamagett }}에 해당하는 물리 피해</physicalDamage>를 입습니다.<br /><br />재사용 대기시간이 적용되기 전 {{ recastwindow }}초 안에 이 스킬을 한 번 더 사용할 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["적 최대 체력 비례 피해량", "재사용 대기시간"], "effect": ["{{ basepercentmaxhpdmgpersec*200.000000 }}% -> {{ basepercentmaxhpdmgpersecnl*200.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [315, 315, 315], "rangeBurn": "315", "image": {"full": "MonkeyKingSpinToWin.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "바위 피부", "description": "오공이 챔피언 및 몬스터와 싸우는 동안 방어력이 점점 높아지며 최대 체력 재생 효과를 얻습니다.", "image": {"full": "MonkeyKingStoneSkin.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}