{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Bard", "title": "ocrotitorul misterelor", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "Bard, spiritul pădurii", "chromas": false}, {"id": "432005", "num": 5, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "432006", "num": 6, "name": "Bard bard", "chromas": false}, {"id": "432008", "num": 8, "name": "Bard astronaut", "chromas": true}, {"id": "432017", "num": 17, "name": "Bard din Cafeneaua adorabilă", "chromas": true}, {"id": "432026", "num": 26, "name": "Bard din Pergamentele Shan Hai", "chromas": true}, {"id": "432035", "num": 35, "name": "Bard T1", "chromas": true}, {"id": "432037", "num": 37, "name": "Bard, mugurul spiritelor", "chromas": true}], "lore": "Bard este un călător venit de dincolo de stele, o unealtă a destinului care luptă pentru a menține echilibrul și a se asigura că viața rezistă în fața haosului. Deși mulți locuitori ai Runeterrei i-au dedicat rătăcitorului cosmic felurite cântece și i-au slăvit trăsături care mai de care mai ieșite din comun, toți sunt de acord că este atras de artefacte cu puteri magice deosebite. Nimeni nu-l poate bănui pe Bard de gânduri malefice. Înconjurat de un cor vesel de spirite săritoare numite mipi, acesta slujește întotdeauna binele... în propriul său fel.", "blurb": "Bard este un călător venit de dincolo de stele, o unealtă a destinului care luptă pentru a menține echilibrul și a se asigura că viața rezistă în fața haosului. Deși mulți locuitori ai Runeterrei i-au dedicat rătăcitorului cosmic felurite cântece și...", "allytips": ["E important să strângi clopoței pentru a îmbunătăți atacurile mipilor tăi, dar nu uita de partenerul de culoar! Încearcă să-ți faci o intrare spectaculoasă, aducând cu tine pe culoar un aliat cu ajutorul ''Călătoriei fermecate''.", "Permite-le ''Altarelor ocrotirii'' s<PERSON> se î<PERSON>, deoarece te vindecă pentru mai multă viață când au putere maximă.", "Nu uita că și inamicii tăi pot folosi culoarele create prin ''Călătorie fermecată'' și că abilitatea ta supremă îți poate afecta și aliații!"], "enemytips": ["Inamicii lui Bard pot și ei să călătorească folosind portalurile ''Călătoriei fermecate''. Îl poți urma dacă nu crezi că te așteaptă vreun pericol de cealaltă parte.", "Poți distruge altarele de vindecare ale lui Bard dacă treci peste ele. Nu-i lăsa pe aliații săi să le folosească fără să opui rezistență.", "Abilitatea supremă a lui <PERSON>, ''Soart<PERSON> schimbătoare'', afectează aliații, inamicii, monștrii și turnurile. Uneori, s-ar putea să te ajute să te lași prins în ea!"], "tags": ["Support", "Mage"], "partype": "Mană", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Bard lansează un fulger de energie care îl va încetini pe primul inamic lovit și apoi își va continua drumul. Dacă vraja lovește un perete, ținta inițială va fi amețită; dacă lovește un alt inamic, ambii vor fi amețiți.", "tooltip": "Bard lansează un fulger de energie, provocând <magicDamage>{{ totaldamage }} daune magice</magicDamage> primilor doi inamici loviți. Prima țintă lovită este <status>încetinită</status> cu {{ slowamountpercentage }}% timp de {{ slowduration }} sec.<br /><br />Dacă fulgerul lovește un al doilea inamic sau un zid, inamicii loviți sunt <status>amețiți</status> timp de {{ stunduration }} sec.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON><PERSON>", "Durată amețire:", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Altarul ocrotirii", "description": "Dezvăluie un altar al vindecării care se încarcă de-a lungul unei perioade scurte. Când un aliat îl atinge, altarul îl vindecă și îi oferă un bonus la viteza de mișcare, după care dispare.", "tooltip": "Bard creează un altar tămăduitor care oferă <speed>{{ calc_movespeed }} vitez<PERSON> de <PERSON></speed>, valoare ce scade de-a lungul a {{ movespeed_duration }} secunde și îi reface cel puțin <healing>{{ initialheal }} viaț<PERSON></healing> primului aliat care pășește în el. Altarul crește, refăcând <healing>{{ maxheal }} via<PERSON><PERSON></healing>, după {{ chargeuptime }} secunde de la apariția sa.<br /><br />Bard poate avea până la {{ maxpacks }} altare în același timp. Dacă un inamic intră într-un altar, îl distruge.<br /><br />Abilitatea are {{ ammo_limit }} cumuluri.<br /><br />Altare active în prezent: {{ f1 }} / {{ f2 }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vindecare de bază", "Vindecare maximă", "Viteză de mișcare"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}% -> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Călătorie fermecată", "description": "Bard deschide un portal în terenul din apropiere. Atât aliații cât și inamicii pot traversa terenul respectiv (într-o singură direcție) dacă trec prin portal.", "tooltip": "Bard deschide un portal unidirecțional printr-un teren timp de {{ e1 }} secunde. Orice campion poate intra în portal deplasându-se spre el în timp ce se află lângă intrare.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "<PERSON><PERSON><PERSON> schi<PERSON>", "description": "Bard lansează o energie imaterială într-o anumită zonă, plasând în stază toți campionii, minionii, monștrii și turnurile din acea zonă pentru un scurt timp.", "tooltip": "Bard aruncă o energie magică protectoare într-o zonă, plasând toate unitățile și structurile lovite într-o stază timp de {{ rstasisduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "<font color='#FF9900'>Mipi:</font> Bard atrage spirite micuțe care îi intensifică atacurile de bază pentru a provoca daune magice bonus. După ce Bard adună suficienți <font color='#cccc00'>clopoței</font>, și mipii săi vor provoca daune într-o zonă și vor încetini inamicii loviți.<br><br><font color='#FF9900'>Clopoței:</font> pe hartă apar la întâmplare <font color='#cccc00'>clopoței</font> străvechi care pot fi adunați de Bard. Aceștia oferă experiență, refac mană și oferă viteză de mișcare în afara luptei.", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}