{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Yone", "title": "Wiecznie Żywy", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "777010", "num": 10, "name": "Yone z Akademii Bojowej", "chromas": true}, {"id": "777019", "num": 19, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "777026", "num": 26, "name": "<PERSON><PERSON> Pieśni", "chromas": true}, {"id": "777035", "num": 35, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "777045", "num": 45, "name": "Yone z HEARTSTEEL", "chromas": true}, {"id": "777046", "num": 46, "name": "Yone z HEARTSTEEL (Prestiżowy)", "chromas": false}, {"id": "777055", "num": 55, "name": "Yone w Samo Południe", "chromas": true}, {"id": "777058", "num": 58, "name": "Rozjemca Yone w Samo Południe", "chromas": false}, {"id": "777065", "num": 65, "name": "<PERSON><PERSON>prawied<PERSON>", "chromas": false}], "lore": "Za życia był Yone — przybranym bratem Yasuo i szanowanym uczniem w pobliskiej szkole miecza. Jednak po śmierci z rąk brata nawiedziła go złowroga istota, którą zmuszony był zgładzić przy użyciu jej własnego miecza. Teraz Yone, przeklęty i zmuszony do noszenia na twarzy demonicznej maski, niestrudzenie poluje na wszystkie podobne stworzenia, by <PERSON><PERSON><PERSON><PERSON><PERSON>, czym się stał.", "blurb": "Za życia był Yone — przybranym bratem Yasuo i szanowanym uczniem w pobliskiej szkole miecza. Jednak po śmierci z rąk brata nawiedziła go złowroga istota, którą zmuszony był zgładzić przy użyciu jej własnego miecza. Teraz Yone, przeklęty i zmuszony do...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "Śmiercionośna Stal", "description": "Yone tnie w przód, z<PERSON><PERSON><PERSON>c obrażenia wszystkim wrogom w linii.<br><br>Przy trafieniu umiejętność przyznaje ładunek Nadchodzącej Burzy na kilka sekund. Przy 2 ładunkach Śmiercionośna Stal Yone sprawia, <PERSON><PERSON> bohater doskakuje, gene<PERSON><PERSON><PERSON><PERSON> falę, która wyrzuca wrogów <status>w powietrze</status>.", "tooltip": "Yone atakuje w przód, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ qdamage }} pkt. obrażeń fizycznych</physicalDamage>.<br /><br />Przy trafieniu otrzymuje ładunek na {{ buffduration }} sek. Po uzyskaniu 2 ładunków ta umiejętność powoduje, ż<PERSON> <PERSON><PERSON> dos<PERSON>, generu<PERSON><PERSON><PERSON> falę wiatru, która <status>podrzuca</status> wrogów na {{ q3knockupduration }} sek. i zadaje <physicalDamage>{{ qdamage }} pkt. obrażeń fizycznych</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YoneW", "name": "Duchowe Rozpłatanie", "description": "Tnie w przód, zadając obrażenia wszystkim wrogom na obszarze w kształcie stożka. Przyznaje Yone tarczę, której wytrzymałość wzrasta wraz z liczbą trafionych atakiem bohaterów.<br><br>Czas odnowienia i czas rzucenia Duchowego Rozpłatania skalują się z prędkością ataku.", "tooltip": "Yone tnie w przód, zada<PERSON><PERSON><PERSON> wrogom <physicalDamage>obrażenia fizyczne w wysokości {{ basedamage*0.5 }} pkt. + {{ maxhealthdamage*50 }}% ich maksymalnego zdrowia</physicalDamage> i <magicDamage>obrażenia magiczne w wysokości {{ basedamage*0.5 }} pkt. + {{ maxhealthdamage*50 }}% ich maksymalnego zdrowia</magicDamage>.<br /><br />Przy trafieniu Yone zyskuje <shield>{{ wshield }} pkt. wytrzymałości tarczy</shield> na {{ shieldduration }} sek. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <shield>tarczy</shield> zwiększa się za każdego trafionego bohatera. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe", "Całkowite obrażenia od maksymalnego zdrowia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YoneE", "name": "Wyzwolona Dusza", "description": "Duch Yone zostawia za sobą jego ciało, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prędkość ruchu. Po zakończeniu działania tej umiejętności duch Yone powraca do jego ciała i ponownie zadaje część obrażeń, które zadał jako duch.", "tooltip": "Yone przyjmuje duchową postać na {{ returntimer }} sek., zys<PERSON><PERSON><PERSON>c od <speed>{{ startingms*100 }}%</speed> do <speed>{{ movementspeed*100 }}% zwiększającej się prędkości ruchu</speed> i zostawiając za sobą cielesną powłokę na czas trwania tej umiejętności.<br /><br /><PERSON>dy duchowa postać przeminie, Yone powr<PERSON>ci do swego ciała i zada {{ deathmarkpercent*100 }}% wszystkich obrażeń od ataków i umiejętności, które zadał bohaterom jako duch. Możesz <recast>ponownie użyć</recast> tej umiejętności w czasie trwania duchowej postaci.<br /><br /><recast>Ponowne użycie: </recast>Zak<PERSON><PERSON><PERSON> duchową postać wcześniej.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Powtórzone Obrażenia", "Czas odnowienia"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YoneR", "name": "Przypieczętowany Los", "description": "Yone miga za ostatniego bohatera w linii i wykonuje cięcie tak mocne, że przyciąga do siebie wszystkich trafionych wrogów.", "tooltip": "Yone uderza wszystkich wrogów na swej drodze, zadając <physicalDamage>{{ tooltipdamage }} pkt. obrażeń fizycznych</physicalDamage> oraz <magicDamage>{{ tooltipdamage }} pkt. obrażeń magicznych</magicDamage>, po czym pojawia się za ostatnim trafionym celem, <status>podrzuca</status> ofiary i przyciąga je do siebie.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Co drugi atak Yone zadaje obrażenia magiczne. Dodatkowo jego szansa na trafienie krytyczne jest zwiększona.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}