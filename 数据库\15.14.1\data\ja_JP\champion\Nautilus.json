{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nautilus": {"id": "Na<PERSON><PERSON>", "key": "111", "name": "ノーチラス", "title": "深海の巨人", "image": {"full": "Nautilus.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "111000", "num": 0, "name": "default", "chromas": false}, {"id": "111001", "num": 1, "name": "深海底の主ノーチラス", "chromas": false}, {"id": "111002", "num": 2, "name": "地底の支配者ノーチラス", "chromas": false}, {"id": "111003", "num": 3, "name": "アストロノーチラス", "chromas": true}, {"id": "111004", "num": 4, "name": "番人ノーチラス", "chromas": false}, {"id": "111005", "num": 5, "name": "天地の破壊者ノーチラス", "chromas": false}, {"id": "111006", "num": 6, "name": "覇者ノーチラス", "chromas": false}, {"id": "111009", "num": 9, "name": "山海絵巻伝ノーチラス", "chromas": false}, {"id": "111018", "num": 18, "name": "恐怖の夜ノーチラス", "chromas": false}, {"id": "111027", "num": 27, "name": "宇宙の勇士ノーチラス", "chromas": false}, {"id": "111036", "num": 36, "name": "クリスタリス インドゥミタス ノーチラス", "chromas": false}], "lore": "ビルジウォーターに最初の桟橋ができたころからすでに伝説となっていた孤独な男──「ノーチラス」の名で知られる防具に包まれた大男は、ブルーフレイム・アイルの沖の暗い海域をさまよっている。大昔の裏切りに突き動かされている彼は何の前触れもなく攻撃を仕掛け、巨大な錨を振り回しては、不運な者を助け、強欲な者を破滅へと引きずり込む。「ビルジウォーターの供物」を払わなかった者のもとに現れては、彼らを道連れにして波間へと沈むのだという。そうして「何人たりとも深海から逃れることはできない」という鉄の掟を知らしめているのだ。", "blurb": "ビルジウォーターに最初の桟橋ができたころからすでに伝説となっていた孤独な男──「ノーチラス」の名で知られる防具に包まれた大男は、ブルーフレイム・アイルの沖の暗い海域をさまよっている。大昔の裏切りに突き動かされている彼は何の前触れもなく攻撃を仕掛け、巨大な錨を振り回しては、不運な者を助け、強欲な者を破滅へと引きずり込む。「ビルジウォーターの供物」を払わなかった者のもとに現れては、彼らを道連れにして波間へと沈むのだという。そうして「何人たりとも深海から逃れることはできない」という鉄の掟を知らしめているのだ。", "allytips": ["レーンへの奇襲を行うときは、敵の近くの地形に向け「錨投げ」を使って接近し、続いて「粉砕水」や「爆雷発射」で相手を捕まえるという手もある。", "「粉砕水」は発動してから爆発するまで時間差がある。敵から逃げる際や、接近してくる敵を迎え撃つときに使うと、有効な妨害手段となる。"], "enemytips": ["彼の「粉砕水」は、衝撃波を3回引き起こす。範囲は徐々に広がっていくので、すぐそばで「粉砕水」を発動されたら、その場から動かないようにしよう。慌てて動くと2段目の爆発に巻き込まれ、連続でダメージを受けてしまう。", "ノーチラスはシールドに守られている間、通常攻撃に強力な範囲攻撃が付与される。余裕があれば、シールドを破壊してから戦おう。"], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 4, "defense": 6, "magic": 6, "difficulty": 6}, "stats": {"hp": 646, "hpperlevel": 100, "mp": 400, "mpperlevel": 47, "movespeed": 325, "armor": 39, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.65, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1, "attackspeed": 0.706}, "spells": [{"id": "NautilusAnchorDrag", "name": "錨投げ", "description": "前方に錨を投げる。敵に命中すると自身と対象を同時に引き寄せ、魔法ダメージを与える。錨が地形に命中した場合、自身を錨の地点まで引き寄せる。", "tooltip": "前方に錨を投げる。敵ユニットに命中すると自身と対象を同時に引き寄せ、対象に<magicDamage>{{ qdamagecalc }}の魔法ダメージ</magicDamage>を与えて短時間<status>スタン効果</status>を付与する。錨が地形に命中した場合、自身を錨の地点まで引き寄せる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "0", "0.5", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "NautilusAnchorDrag.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NautilusPiercingGaze", "name": "大海の激憤", "description": "一時的にシールドを獲得する。シールドが持続している間は通常攻撃が対象と周囲の敵に継続ダメージを与える。", "tooltip": "{{ shieldduration }}秒間、<shield>耐久値{{ shieldcalc }}のシールド</shield>を獲得する。<shield>シールド</shield>が持続している間は、通常攻撃が2秒かけて対象と周囲のすべての敵に<magicDamage>{{ dotdamagecalc }}の魔法ダメージ</magicDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "魔法ダメージ", "最大体力割合"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ dotdamagebase }} -> {{ dotdamagebaseNL }}", "{{ shieldhealthratio*100.000000 }}% -> {{ shieldhealthrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "NautilusPiercingGaze.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NautilusSplashZone", "name": "粉砕水", "description": "自身の周囲に3回爆発する衝撃波を発生させる。爆発のたびに敵にダメージとスロウ効果を与える。", "tooltip": "自身の周囲に3回爆発する衝撃波を発生させる。爆発のたびに範囲内の敵に<magicDamage>{{ damagecalc }}の魔法ダメージ</magicDamage>を与え、{{ slowpercent*100 }}%の<status>スロウ効果</status>を付与する。この効果は{{ slowduration }}秒かけて元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ", "スロウ効果"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NautilusSplashZone.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NautilusGrandLine", "name": "爆雷発射", "description": "錨を地面にたたきつけ、狙った敵チャンピオンを追尾する爆雷を発射する。爆雷は対象を追尾しながら、通った場所に衝撃波を引き起こし、巻き込んだ敵ユニットに魔法ダメージとノックアップを与える。爆雷が対象に命中すると爆発がおき、対象にノックアップとスタンを付与する。", "tooltip": "指定した敵チャンピオンを追尾する衝撃波を発射する。衝撃波は対象に<magicDamage>{{ primarytargetdamage }}の魔法ダメージ</magicDamage>を与えて<status>ノック</status><status>アップ</status>させ、{{ stunduration }}秒間の<status>スタン効果</status>を付与する。衝撃波が当たった他の敵も<status>ノックアップ</status>させて<status>スタン効果</status>を付与し、<magicDamage>{{ secondarytargetdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "通過ダメージ", "スタン効果時間:", "爆発ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ primarydamage }} -> {{ primarydamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "NautilusGrandLine.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "鉄の錨", "description": "対象に最初に行う通常攻撃は与える物理ダメージが増加し、短時間スネア効果を与える。", "image": {"full": "Nautilus_StaggeringBlow.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}