{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"KSante": {"id": "KSante", "key": "897", "name": "カ・サンテ", "title": "ナズーマの誇り", "image": {"full": "KSante.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "897000", "num": 0, "name": "default", "chromas": false}, {"id": "897001", "num": 1, "name": "荘厳の天球カ・サンテ", "chromas": true}, {"id": "897002", "num": 2, "name": "プレステージ荘厳の天球カ・サンテ", "chromas": false}, {"id": "897008", "num": 8, "name": "HEARTSTEEL カ・サンテ", "chromas": true}], "lore": "シュリーマの砂漠に位置する貴重なオアシス、ナズーマ。自らの故郷であるその地を守るため、尊大で勇敢なカ・サンテは巨大な獣や無慈悲な超越者と戦っている。だが、かつての相棒と仲たがいした彼は、民を率いるにふさわしい戦士となるには、成功を求めて身勝手になりがちな自己を抑えなければならないと悟る。それができて初めて、自らのうぬぼれに溺れることなく、民をおびやかす狂暴な怪物を倒すための知恵を見出せるのだ。", "blurb": "シュリーマの砂漠に位置する貴重なオアシス、ナズーマ。自らの故郷であるその地を守るため、尊大で勇敢なカ・サンテは巨大な獣や無慈悲な超越者と戦っている。だが、かつての相棒と仲たがいした彼は、民を率いるにふさわしい戦士となるには、成功を求めて身勝手になりがちな自己を抑えなければならないと悟る。それができて初めて、自らのうぬぼれに溺れることなく、民をおびやかす狂暴な怪物を倒すための知恵を見出せるのだ。", "allytips": ["「龍の怒り」の前に「響掌」を命中させておくことで「共鳴撃」での追撃が可能になる。", "「練気」の効果を活用しよう。スキルの合間に通常攻撃を挟むと、気の消費を最小に抑えながら最大のダメージを与えることができる。", "自分に「守りの型」を使用してから「鉄の意志」を使用すれば、ジャングルの中立モンスターを楽に倒すことができる。"], "enemytips": ["アルティメットスキル「龍の怒り」による被害を最小限に留めるため、一ヵ所に固まらないようにすること。", "「鉄の意志」と「縛脚」を使えるため物理ダメージへの耐性は高いが、魔法ダメージには弱い。", "リー・シンはコンボ技への依存度が高い。動きを封じるスキルで連続攻撃を阻止しよう。"], "tags": ["Tank", "Fighter"], "partype": "マナ", "info": {"attack": 8, "defense": 8, "magic": 7, "difficulty": 9}, "stats": {"hp": 625, "hpperlevel": 120, "mp": 320, "mpperlevel": 60, "movespeed": 330, "armor": 36, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 2.1, "attackrange": 150, "hpregen": 9.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.688}, "spells": [{"id": "KSanteQ", "name": "破撃のエントーフォ", "description": "武器を叩きつけ、短い直線上にいた敵にダメージとスロウ効果を与える。<br><br>命中時に「破撃のエントーフォ」のスタックを獲得する。2スタックになると衝撃波を放って、敵を自身の方向に引き寄せる。<br><br>「オールアウト」中はクールダウンが短縮される。", "tooltip": "武器を叩きつけて<physicalDamage>{{ basedamage }}の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間、{{ slowpercent*100 }}%の<status>スロウ効果</status>を与える。敵に命中すると、{{ recastwindow }}秒間「破撃のエントーフォ」のスタックを1獲得する。2スタックになると衝撃波を放つようになり、{{ stunduration }}秒間敵を<status>スタン</status>させて<status>引き寄せる</status>。<br /><br /><keywordMajor>オールアウト</keywordMajor>: クールダウンが{{ rcooldownreduction.0*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ flatdamage }} -> {{ flatdamageNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "KSanteQ.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KSanteW", "name": "切り開く猛進", "description": "被ダメージを軽減させながらチャージし、その後ダッシュして敵をノックバックしてスタンさせる。<br><br>「オールアウト」中は与ダメージが増加するが、ノックバックとスタンを与えなくなる。", "tooltip": "{{ mindurationtooltip }}-{{ maxduration.1 }}秒間、武器を構えて防御態勢を取る。この間はアンストッパブル状態になって、受けるダメージが{{ damagereduction*100 }}%軽減される。その後、前方に突撃して<physicalDamage>{{ basedamage }} + 最大体力の{{ totalmaxhealthdamage }}の物理ダメージ</physicalDamage>を与え、命中した敵を<status>ノックバック</status>し、{{ minknockbackduration }}-{{ maxknockbackduration }}秒間(チャージ時間に応じて)<status>スタン</status>させる。<br /><br /><keywordMajor>オールアウト:</keywordMajor> クールダウンが解消される。追加で{{ rdamageincreasemin*100 }}-{{ rdamageincreasemax*100 }}%の<trueDamage>確定ダメージ</trueDamage>(チャージ時間に応じて)を与え、ダメージ軽減効果が{{ rdamagereduction*100 }}%に増加し、ダッシュ速度が増加するが、<status>ノックバック</status>と<status>スタン</status>を与えなくなる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KSanteW.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KSanteE", "name": "辰砂の足取り", "description": "ダッシュしてシールドを獲得する。味方を対象にした場合は距離が増加し、自身と味方の両方がシールドを獲得する。<br><br>「オールアウト」中はクールダウンが短縮され、速度が増加する。", "tooltip": "ダッシュして{{ shieldduration }}秒間、<shield>耐久値{{ totalshield }}のシールド</shield>を獲得する。味方までダッシュする場合はダッシュ距離が大幅に増加し、その味方も<shield>シールドを獲得</shield>する。<br /><br /><keywordMajor>オールアウト</keywordMajor>: クールダウンが{{ cooldownmodao*100 }}%短縮されて、ダッシュ速度が増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ shieldbaseamountfast }} -> {{ shieldbaseamountfastNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "KSanteE.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KSanteR", "name": "オールアウト", "description": "敵をノックバックし、敵は通り道のあらゆる壁を通り抜けて弾き飛ばされる。その後、自身は「オールアウト」状態になり、その敵をダッシュして追いかけ、防御力が低下する代わりにダメージと体力回復量が大幅に増加し、スキルが変化する。", "tooltip": "エントーフォを砕き、敵チャンピオン1体を<status>ノックバック</status>して<physicalDamage>{{ basedamage }}の物理ダメージ</physicalDamage>を与え、その後、対象の背後までダッシュして、{{ alloutduration }}秒間、<keywordMajor>「オールアウト」</keywordMajor>状態になる。敵が壁にぶつかった場合は壁を越えて<status>ノックバック</status>し、自身はもう一度攻撃を行って<physicalDamage>{{ totaldamageslamdown }}の物理ダメージ</physicalDamage>を与える。<br /><br /><keywordMajor>「オールアウト」</keywordMajor>中はスキルがアップグレードされて、<attackSpeed>攻撃速度が{{ attackspeed*100 }}%</attackSpeed>、増加物理防御貫通が{{ armorpenpercent*100 }}%、<omnivamp>オムニヴァンプが{{ omnivamp*100 }}%</omnivamp>増加するが、<healing>最大体力が{{ healthlost*100 }}%</healing>、<scaleArmor>増加物理防御が{{ defenseslost*100 }}%</scaleArmor>、<scaleMR>増加魔法防御が{{ defenseslost*100 }}%</scaleMR>低下する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "追撃ダメージ", "クールダウン", "攻撃速度"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slamdownstrikedamage }} -> {{ slamdownstrikedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "KSanteR.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "不屈の本能", "description": "スキルが対象をマークする。マークされた対象への次の通常攻撃は、与えるダメージが増加する。<br><br>「オールアウト」中はあらゆる通常攻撃とスキルが与えるダメージが増加する。", "image": {"full": "Icons_KSante_P.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}