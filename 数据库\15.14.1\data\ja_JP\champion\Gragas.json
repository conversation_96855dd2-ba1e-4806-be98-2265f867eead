{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gragas": {"id": "Gragas", "key": "79", "name": "グラガス", "title": "騒乱の飲んだくれ", "image": {"full": "Gragas.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "79000", "num": 0, "name": "default", "chromas": false}, {"id": "79001", "num": 1, "name": "スキューバ グラガス", "chromas": false}, {"id": "79002", "num": 2, "name": "田舎もんグラガス", "chromas": false}, {"id": "79003", "num": 3, "name": "グラガスサンタ", "chromas": false}, {"id": "79004", "num": 4, "name": "グラガス殿下", "chromas": false}, {"id": "79005", "num": 5, "name": "アウトロー グラガス", "chromas": false}, {"id": "79006", "num": 6, "name": "グラガスビール", "chromas": false}, {"id": "79007", "num": 7, "name": "フーリガン グラガス", "chromas": false}, {"id": "79008", "num": 8, "name": "Fnatic グラガス", "chromas": false}, {"id": "79009", "num": 9, "name": "タル割り公グラガス", "chromas": false}, {"id": "79010", "num": 10, "name": "極寒の工作員グラガス", "chromas": false}, {"id": "79011", "num": 11, "name": "番人グラガス", "chromas": true}, {"id": "79020", "num": 20, "name": "スペースグルーヴ グラガス", "chromas": true}, {"id": "79029", "num": 29, "name": "荒野のグラガス", "chromas": true}, {"id": "79039", "num": 39, "name": "音楽愛好家グラガス", "chromas": true}], "lore": "陽気で立派な巨体を持つ、荒くれた風貌のグラガスは、常に人々の気持ちを明るくさせる新たな方法を探している醸造家だ。どこの出身なのかは不明だが、彼は完璧な調合を見つけるため、フレヨルドの誰も足を踏み入れない荒野で貴重な醸造材料を探している。向こう見ずで頑固な性格で、彼が始めた喧嘩の話は広く知られ、最後はいつも朝までどんちゃん騒ぎになって建物のあちこちが破壊されることになる。グラガスの現れるところ、必ずお祭り騒ぎと破壊が巻き起こる──いつもこの順番だ。", "blurb": "陽気で立派な巨体を持つ、荒くれた風貌のグラガスは、常に人々の気持ちを明るくさせる新たな方法を探している醸造家だ。どこの出身なのかは不明だが、彼は完璧な調合を見つけるため、フレヨルドの誰も足を踏み入れない荒野で貴重な醸造材料を探している。向こう見ずで頑固な性格で、彼が始めた喧嘩の話は広く知られ、最後はいつも朝までどんちゃん騒ぎになって建物のあちこちが破壊されることになる。グラガスの現れるところ、必ずお祭り騒ぎと破壊が巻き起こる──いつもこの順番だ。", "allytips": ["「飲みすぎ注意」による被ダメージ軽減効果は、グラガスが酒を飲み始めた瞬間から発動する。ダメージを受けそうな状況で使用すると効果的だ。", "「ワシの奢りじゃ！」で、敵を味方タワーのほうへ吹き飛ばそう。", "「ボディスラム」と「ワシの奢りじゃ！」を巧みに組み合わせれば、チームのキルに貢献できる。"], "enemytips": ["グラガスのアルティメットスキルは範囲内の敵ユニットをふき飛ばす、敵チームやタワーのほうへ運ばれてしまわないよう注意しよう。", "「ボディスラム」はクールダウンが短いため、連発されるとグラガスに追いつくのは難しい。深追いは避けよう。"], "tags": ["Fighter", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 115, "mp": 400, "mpperlevel": 47, "movespeed": 330, "armor": 38, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.05, "attackspeed": 0.675}, "spells": [{"id": "GragasQ", "name": "タル転がし", "description": "指定地点にタルを転がし、4秒後に爆発させる。タルは自分で起爆させることもできる。時間経過とともに爆発の威力が増加する。爆風を浴びた敵はスロウ状態になる。", "tooltip": "タルを転がす。タルは{{ e4 }}秒経つと爆発し、<magicDamage>{{ mindamage }}</magicDamage> - <magicDamage>{{ maxdamage }}の魔法ダメージ</magicDamage>を与えて、{{ e3 }}秒間{{ e2 }} - {{ effect2amount*1.5 }}%の<status>スロウ効果</status>を付与する。このダメージと<status>スロウ効果</status>は、タルが爆発するまでの時間に応じて増加する。<br /><br /><recast>再発動</recast>するとタルを早めに爆発できる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [40, 45, 50, 55, 60], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [2, 2, 2, 2, 2], [150, 150, 150, 150, 150], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "40/45/50/55/60", "2", "4", "2", "150", "70", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "GragasQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GragasW", "name": "飲みすぎ注意", "description": "最新の醸造酒を1秒間試飲し、飲み終えると騒々しく好戦的になる。効果時間中は受けるダメージが軽減され、次の通常攻撃で付近のすべての敵に魔法ダメージを与える。", "tooltip": "酒を試飲し、{{ defenseduration }}秒間被ダメージを{{ damagereduction }}軽減する。次の通常攻撃が強化され、対象と周囲の敵に<magicDamage>{{ totaldamage }}</magicDamage><magicDamage>(+最大体力の{{ maxhppercentdamage }}%)の魔法ダメージ</magicDamage>を追加で与えるようになる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ軽減", "ダメージ"], "effect": ["{{ basedamagereduction }}% -> {{ basedamagereductionNL }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "GragasW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GragasE", "name": "ボディスラム", "description": "指定方向へ突進し、最初に衝突した敵ユニットとその周囲の敵にダメージを与え、ノックバックとスタンを付与する。", "tooltip": "前方に突進して、最初に接触した敵を含む周囲の敵を{{ stunduration }}秒間<status>ノックアップ</status>させて、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />敵に接触した場合、このスキルのクールダウンが{{ cooldownrefund*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GragasE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GragasR", "name": "ワシの奢りじゃ！", "description": "指定地点にタルを放り投げる。着弾したタルは大爆発し、爆発範囲内の敵ユニットにダメージを与えてノックバックさせる。", "tooltip": "タルを放り投げて<magicDamage>{{ damagedone }}の魔法ダメージ</magicDamage>を与え、着弾地点から敵を<status>ノックバック</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GragasR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ハッピーアワー", "description": "一定時間ごとにスキル使用時に体力を回復する。", "image": {"full": "GragasPassiveHeal.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}