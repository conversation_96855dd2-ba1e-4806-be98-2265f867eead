{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Hecarim": {"id": "<PERSON><PERSON><PERSON>", "key": "120", "name": "헤카림", "title": "전쟁의 전조", "image": {"full": "Hecarim.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "120000", "num": 0, "name": "default", "chromas": false}, {"id": "120001", "num": 1, "name": "피의 기사 헤카림", "chromas": false}, {"id": "120002", "num": 2, "name": "사신 헤카림", "chromas": false}, {"id": "120003", "num": 3, "name": "호박머리 헤카림", "chromas": false}, {"id": "120004", "num": 4, "name": "아케이드 헤카림", "chromas": false}, {"id": "120005", "num": 5, "name": "나무정령 헤카림", "chromas": false}, {"id": "120006", "num": 6, "name": "파괴의 신 헤카림", "chromas": false}, {"id": "120007", "num": 7, "name": "빛의 창기병 헤카림", "chromas": false}, {"id": "120008", "num": 8, "name": "하이 눈 헤카림", "chromas": true}, {"id": "120014", "num": 14, "name": "우주 돌격대 헤카림", "chromas": true}, {"id": "120022", "num": 22, "name": "아르카나 헤카림", "chromas": true}, {"id": "120031", "num": 31, "name": "겨울의 축복 헤카림", "chromas": true}, {"id": "120041", "num": 41, "name": "어둠의 인도자 헤카림", "chromas": true}], "lore": "헤카림은 말과 인간이 한 몸인 반인반수 유령으로, 산 자들의 영혼을 끝없이 추적하는 저주를 받았다. 원래는 자존심 강한 기사로 자신의 기사단을 이끌었으나, 축복의 빛 군도에 그림자가 드리울 때 대몰락의 파괴 에너지에 휩쓸려 최후를 맞았다. 그 이후 헤카림과 기사단은 타고 있던 말과 한 몸이 되어, 검은 안개가 룬테라에 깔릴 때면 무자비한 돌격을 일삼는다. 이제 그에게 남은 쾌락이란 적을 학살하고 철갑을 두른 말발굽 아래 그 시체를 깔아뭉개는 것뿐이다.", "blurb": "헤카림은 말과 인간이 한 몸인 반인반수 유령으로, 산 자들의 영혼을 끝없이 추적하는 저주를 받았다. 원래는 자존심 강한 기사로 자신의 기사단을 이끌었으나, 축복의 빛 군도에 그림자가 드리울 때 대몰락의 파괴 에너지에 휩쓸려 최후를 맞았다. 그 이후 헤카림과 기사단은 타고 있던 말과 한 몸이 되어, 검은 안개가 룬테라에 깔릴 때면 무자비한 돌격을 일삼는다. 이제 그에게 남은 쾌락이란 적을 학살하고 철갑을 두른 말발굽 아래 그 시체를 깔아뭉개는 것뿐이다.", "allytips": ["공포의 망령 스킬은 주변 적들이 피해를 입을 때 체력을 회복해 주며, 아군이 피해를 입혀도 역시 체력이 회복됩니다. 대규모 교전 시 공포의 망령을 활용해 헤카림의 생존력을 올리세요.", "파멸의 돌격은 이동 거리에 비례한 피해를 줍니다. 그림자의 맹습이나 소환사 주문 유체화 혹은 점멸을 이용해 피해량을 극대화하세요."], "enemytips": ["헤카림은 공포의 망령 스킬을 이용해 주변 적들에게서 체력을 얻는 대신 방어도가 낮으니 큰 피해를 주는 데 집중하세요.", "헤카림은 궁극기로 적들을 공포에 질려 도망가게 만듭니다. 교전 시에는 흩어져 있으면 피해를 줄일 수 있습니다."], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 8, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 280, "mpperlevel": 40, "movespeed": 345, "armor": 32, "armorperlevel": 5.45, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 2.5, "attackspeed": 0.67}, "spells": [{"id": "HecarimRapidSlash", "name": "회오리 베기", "description": "헤카림이 주변 적을 베어 물리 피해를 입힙니다. 한 명의 적이라도 적중 시 다음 회오리 베기의 피해량이 증가하며 재사용 대기시간이 감소합니다.", "tooltip": "헤카림이 주위 적들을 베어 <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입힙니다. 이 스킬이 적중하면 효과가 중첩되어 {{ e6 }}초 동안 피해량이 {{ rampagebonusdamageperc }}% 늘어나고 이 스킬의 재사용 대기시간이 {{ rampagecooldownreduction }}초 감소합니다. 최대 {{ e2 }}번까지 중첩됩니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "마나 소모량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [28, 26, 24, 22, 20], "costBurn": "28/26/24/22/20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3", "1", "3", "60", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HecarimRapidSlash.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "HecarimW", "name": "공포의 망령", "description": "헤카림이 방어력과 마법 저항력을 얻습니다. 주변 적들에게 마법 피해를 입히고, 적들이 입은 피해의 일정 비율에 해당하는 체력을 회복합니다.", "tooltip": "헤카림이 {{ buffduration }}초에 걸쳐 주변 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. <br /><br />헤카림이 <passive>{{ resistamount }}</passive>만큼 <scaleArmor>방어력</scaleArmor>과 <scaleMR>마법 저항력</scaleMR>을 얻고, 주변 적들이 헤카림에게 받은 <healing>피해량의 {{ leechamount }}%</healing>와 헤카림의 아군에게 받은 <healing>피해량의 {{ allytooltipleachvalue }}%</healing>만큼 체력을 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "추가 저항력", "회복 최대치", "소모값 @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ resistamount }} -> {{ resistamountNL }}", "{{ minionhealcap }} -> {{ minionhealcapNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "HecarimW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "HecarimRamp", "name": "파멸의 돌격", "description": "잠깐 동안 헤카림의 이동 속도가 빨라지고 다른 유닛을 통과해 이동할 수 있습니다. 다음번 공격은 대상을 뒤로 밀어내고, 스킬을 사용한 후 이동한 거리에 비례하여 추가 물리 피해를 입힙니다.", "tooltip": "헤카림이 유체화 상태가 되어 <speed>이동 속도가 {{ minmovespeed*100 }}%</speed> 증가합니다. 이동 속도는 {{ e5 }}초에 걸쳐 <speed>{{ maxmovespeed*100 }}%</speed>까지 증가합니다. 다음 기본 공격은 <status>뒤로 밀어내며</status> <physicalDamage>{{ mindamage }}</physicalDamage>~<physicalDamage>{{ maxdamage }}의 물리 피해</physicalDamage>를 입힙니다. <status>뒤로 밀려나는</status> 거리와 피해량은 이 스킬을 사용하는 중 이동한 거리에 비례합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최소 피해량", "최대 피해량", "재사용 대기시간"], "effect": ["{{ minbasedamage }} -> {{ minbasedamageNL }}", "{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [150, 150, 150, 150, 150], [350, 350, 350, 350, 350], [60, 90, 120, 150, 180], [30, 45, 60, 75, 90], [4, 4, 4, 4, 4], [0.65, 0.65, 0.65, 0.65, 0.65], [1200, 1200, 1200, 1200, 1200], [0.25, 0.25, 0.25, 0.25, 0.25], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "150", "350", "60/90/120/150/180", "30/45/60/75/90", "4", "0.65", "1200", "0.25", "2.5", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "HecarimRamp.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "HecarimUlt", "name": "그림자의 맹습", "description": "헤카림이 유령 기수들을 소환하며 전방으로 돌진해 일직선 상에 마법 피해를 입힙니다. 헤카림이 돌격을 멈추면 충격파가 발생하여, 주변 적들이 공포에 사로잡혀 달아납니다.", "tooltip": "헤카림이 유령 기수들을 소환하며 전방으로 돌격하여 <magicDamage>{{ damagedone }}의 마법 피해</magicDamage>를 입힙니다. 돌격이 끝나면 충격파를 발산하여 돌격한 거리에 비례해 최소 {{ feardurationmin }}초에서 최대 {{ feardurationmax }}초 동안 <status>공포</status>에 질리게 합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.75, 0.75, 0.75], [1.5, 1.5, 1.5], [1100, 1100, 1100], [1000, 1000, 1000], [950, 950, 950], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.75", "1.5", "1100", "1000", "950", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [50000, 50000, 50000], "rangeBurn": "50000", "image": {"full": "HecarimUlt.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "출정", "description": "헤카림의 공격력이 추가 이동 속도의 일정 비율만큼 증가합니다.", "image": {"full": "Hecarim_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}