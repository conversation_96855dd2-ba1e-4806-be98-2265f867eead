{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Morgana": {"id": "<PERSON><PERSON>", "key": "25", "name": "<PERSON><PERSON>", "title": "la Caduta", "image": {"full": "Morgana.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "25000", "num": 0, "name": "default", "chromas": true}, {"id": "25001", "num": 1, "name": "Morgana Esiliata", "chromas": false}, {"id": "25002", "num": 2, "name": "Morgana Golosità Peccaminosa", "chromas": false}, {"id": "25003", "num": 3, "name": "Morgana Signora delle Lame", "chromas": false}, {"id": "25004", "num": 4, "name": "Morgana Aculeo Nero", "chromas": true}, {"id": "25005", "num": 5, "name": "Morgana Sposa Spettrale", "chromas": false}, {"id": "25006", "num": 6, "name": "Morgana <PERSON>", "chromas": false}, {"id": "25010", "num": 10, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "25011", "num": 11, "name": "St<PERSON>ga <PERSON>", "chromas": true}, {"id": "25017", "num": 17, "name": "Morgana Imperatrice Maestosa", "chromas": true}, {"id": "25026", "num": 26, "name": "Morgana della Congrega", "chromas": true}, {"id": "25039", "num": 39, "name": "Morgana Portatrice dell'Alba", "chromas": true}, {"id": "25041", "num": 41, "name": "Strega Morgana (edizione prestigio)", "chromas": false}, {"id": "25050", "num": 50, "name": "<PERSON><PERSON>em<PERSON>", "chromas": true}, {"id": "25060", "num": 60, "name": "<PERSON><PERSON> Candida", "chromas": true}, {"id": "25070", "num": 70, "name": "Morgana di Porcellana", "chromas": false}, {"id": "25080", "num": 80, "name": "Morgana Fiore spirituale", "chromas": false}], "lore": "Divisa tra la sua natura celestiale e mortale, <PERSON><PERSON> ha legato le sue ali per accettare l'umanità, e infligge il suo dolore e la sua amarezza ai disonesti e ai corrotti. Rifiuta le tradizioni e le leggi che reputa ingiuste, combattendo per la verità che molti vogliono reprimere, nelle ombre di Demacia, con scudi e catene di fuoco oscuro. Morgana crede che anche chi è stato cacciato e rifiutato possa un giorno sorgere nuovamente.", "blurb": "Divisa tra la sua natura celestiale e mortale, <PERSON><PERSON> ha legato le sue ali per accettare l'umanità, e infligge il suo dolore e la sua amarezza ai disonesti e ai corrotti. Rifiuta le tradizioni e le leggi che reputa ingiuste, combattendo per la verità...", "allytips": ["Un uso scaltro di Scudo nero può determinare l'esito di un combattimento a squadre.", "Oggetti che danno capacità di sopravvivenza, in combinazione con Scudo nero e Catene spirituali, permettono a Morgana di diventare estremamente difficile da uccidere.", "Ombra tormentata è un ottimo strumento per finire i minion quando si è da soli in corsia."], "enemytips": ["Ombra tormentata infligge danni ingenti alle unità a cui mancano grandi quantità di salute. Quando hai la salute bassa, fai attenzione ai tentativi di Morgana di intrappolarti.", "Morgana ha bisogno spesso di mettere a segno un Legame oscuro prima di poter lanciare gli altri suoi attacchi. Usa i tuoi minion come scudo contro Le<PERSON> oscuro.", "<PERSON>udo nero rende Morgana immune agli impedimenti, ma può essere spezzato infliggendo danni magici."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 6, "magic": 8, "difficulty": 1}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 340, "mpperlevel": 60, "movespeed": 335, "armor": 25, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 5.5, "hpregenperlevel": 0.4, "mpregen": 11, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.53, "attackspeed": 0.625}, "spells": [{"id": "MorganaQ", "name": "Legame oscuro", "description": "Morgana blocca un nemico con la magia nera, costringendolo a provare il dolore che ha causato e infliggendogli danni magici. ", "tooltip": "Morgana scaglia del fuoco stellare che <status>immobilizza</status> il primo nemico colpito per {{ rootduration }} secondi e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata immobilizzazione", "Costo in @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "MorganaQ.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MorganaW", "name": "Ombra tormentata", "description": "Morgana evoca un'ombra maledetta su un'area, danne<PERSON><PERSON><PERSON> i nemici che osano calpestare il suo cerchio oscuro. I nemici subiscono danni magici nel tempo, che aumentano al diminuire della loro salute.", "tooltip": "Morgana brucia il terreno per {{ wduration }} secondi, infliggendo <magicDamage>{{ totalmindamage }} danni magici</magicDamage> al secondo che aumentano fino a <magicDamage>{{ totalmaxdamage }}</magicDamage> in base alla percentuale di salute mancante dei bersagli.<br /><br />La ricarica di questa abilità si riduce di un {{ cdrefundpercent*100 }}% ogni volta che Morgana viene curata da <spellName>Risucchio spirituale</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [70, 85, 100, 115, 130], "costBurn": "70/85/100/115/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "MorganaW.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MorganaE", "name": "<PERSON><PERSON> nero", "description": "Morgana ricopre un alleato con una barriera protettiva di fuoco stellare che assorbe i danni magici e gli impedimenti finché non viene distrutta.", "tooltip": "Morgana dona a un campione alleato uno <shield>scudo magico da {{ totalshieldstrength }}</shield> per {{ shieldduration }} secondi. Lo scudo blocca gli effetti di <status>impedimento</status> e <status>immobilizzazione</status> fino alla sua rottura.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Ricarica"], "effect": ["{{ shieldstrength }} -> {{ shieldstrengthNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 23.5, 21, 18.5, 16], "cooldownBurn": "26/23.5/21/18.5/16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "MorganaE.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MorganaR", "name": "Catene spirituali", "description": "Morgana scatena tutto il suo potere celestiale spiegando le ali e librandosi in aria. Scaglia dolorose catene oscure contro i campioni nemici vicini, ottenendo velocità di movimento. Le catene rallentano e infliggono danni iniziali al nemico e, dopo un ritardo, stordiscono chi non è in grado di spezzarle.", "tooltip": "Morgana si incatena ai campioni nemici nelle vicinanze infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallentandoli</status> del {{ slowpercent }}%. Dopo {{ chainduration }} secondi, i nemici che non riescono a spezzare le catene subiscono <magicDamage>{{ totaldamage }} danni magici</magicDamage> aggiuntivi e vengono <status>storditi</status> per {{ stunduration }} secondi.<br /><br />Mentre lancia l'abilità, <PERSON><PERSON> ottiene <speed>{{ hastepercent }}% velocità di movimento</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "<PERSON><PERSON> stordimento:", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ hastepercent }}% -> {{ hastepercentNL }}%", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "MorganaR.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Risucchio spirituale", "description": "Morgana risucchia lo spirito dai suoi nemici, curandosi mentre infligge danni a campioni, minion grandi e mostri della giungla grandi e medi.", "image": {"full": "FallenAngel_Empathize.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}