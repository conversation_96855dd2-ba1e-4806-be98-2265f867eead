{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "Рената Гласк", "title": "Химбаронесса", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "Ад<PERSON><PERSON><PERSON><PERSON><PERSON>ласк", "chromas": true}, {"id": "888010", "num": 10, "name": "Рената Гласк из Мира ужасов", "chromas": true}, {"id": "888020", "num": 20, "name": "Потусторонняя Рената Гласк", "chromas": true}, {"id": "888021", "num": 21, "name": "Потусторонняя Рената Гласк (престижный)", "chromas": false}, {"id": "888031", "num": 31, "name": "Рената Гласк на маскараде Черной Розы", "chromas": false}], "lore": "Рената Гласк потеряла в пожаре дом и родителей. Остались лишь их медицинские исследования и доброе имя. За несколько десятков лет Рената сумела стать богатейшей химбаронессой Зауна, настоящим магнатом, чье богатство строится на умении превращать конкурентов в союзников. Работай на нее – и получишь неслыханно щедрую плату. Работай против нее – и всю жизнь будешь об этом жалеть. Но рано или поздно на ее сторону переходят все.", "blurb": "Рената Гласк потеряла в пожаре дом и родителей. Остались лишь их медицинские исследования и доброе имя. За несколько десятков лет Рената сумела стать богатейшей химбаронессой Зауна, настоящим магнатом, чье богатство строится на умении превращать...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "Мана", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "Рукопожатие", "description": "Рената выпускает снаряд, который обездвиживает первого пораженного врага. При повторном применении она отбрасывает цель в выбранном направлении.", "tooltip": "Рената выпускает из своей руки снаряд, который <status>обездвиживает</status> первого пораженного врага на {{ rootduration }} сек. и наносит ему <magicDamage>{{ totaldamage }}</magicDamage> <magicDamage>магического урона</magicDamage>.<br /><br /><recast>Повторное применение:</recast> Рената <status>отбрасывает</status> врага в выбранном направлении, нанося <magicDamage>{{ totaldamage }} магического урона</magicDamage> всем, с кем он сталкивается, и <status>оглушая</status> их на {{ stunduration }} сек., если отброшенная цель – чемпион.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RenataW", "name": "Антикризисные меры", "description": "Рената усиливает союзного чемпиона. После смерти он сразу возрождается, но начинает быстро терять здоровье. Если союзник успевает принять участие в убийстве врага, он остается в живых.", "tooltip": "Рената усиливает союзного чемпиона, увеличивая его <attackSpeed>скорость атаки</attackSpeed> на <attackSpeed>{{ ascalc }}</attackSpeed> и <speed>скорость передвижения</speed> при перемещении к врагам на <speed>{{ mscalc }}</speed>. В течение {{ duration }} сек. прибавки увеличиваются до {{ finalascalc }} и {{ finalmscalc }} соответственно. Участия в убийствах чемпионов обновляют продолжительность действия эффекта.<br /><br />При получении смертельного урона здоровье союзника восстанавливается до максимума, но начинает быстро убывать. Через 3 сек. он погибает окончательно.<br /><br />Если в течение этого периода он участвует в убийстве врага, эффект снимается, а его здоровье восстанавливается до <healing>{{ triumphpercent }}% от максимального запаса</healing>.<br /><br /><rules>Потерю здоровья нельзя остановить ничем, кроме участия в убийстве врага, однако смерть можно отсрочить при помощи лечения или подобных эффектов. Чемпионы могут отсрочить свою смерть не более раза.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Скорость атаки", "Скорость передвижения", "Перезарядка"], "effect": ["{{ bonusattackspeed }}% -> {{ bonusattackspeedNL }}%", "{{ bonusmovespeed }}% -> {{ bonusmovespeedNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RenataE", "name": "Программа лояльности", "description": "Рената выпускает две химтековые ракеты, которые дают щит союзникам, а также замедляют врагов и наносят им урон.", "tooltip": "Рената выпускает две химтековые ракеты, которые наносят пораженным врагам <magicDamage>{{ totaldamage }} магического урона</magicDamage> и <status>замедляют</status> их на 30% на {{ slowduration }} сек. Задетые союзники получают <shield>щит прочностью {{ shieldcalc }}</shield> на {{ shieldduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Прочность щита", "Стоимость – @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RenataR", "name": "Смена руководства", "description": "Рената выпускает волну химикатов, которая вызывает Бешенство у всех пораженных врагов.", "tooltip": "Рената выпускает волну химикатов, которая накладывает на врагов <status>Бешенство</status> на {{ berserkduration }} сек., заставляя их атаковать ближайшего бойца. В первую очередь они атакуют собственных союзников.<br /><br />Пока действует <status>Бешенство</status>, <attackSpeed>скорость атаки</attackSpeed> врагов увеличена на <attackSpeed>{{ bonusattackspeed*100 }}%</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Продолжительность"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Рычаг давления", "description": "Автоатаки Ренаты наносят дополнительный урон и помечают врагов. Когда союзники наносят урон помеченным врагам, те получают дополнительный урон.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}