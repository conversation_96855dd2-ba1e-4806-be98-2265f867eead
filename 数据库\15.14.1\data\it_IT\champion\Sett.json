{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sett": {"id": "<PERSON><PERSON>", "key": "875", "name": "<PERSON><PERSON>", "title": "il boss", "image": {"full": "Sett.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "875000", "num": 0, "name": "default", "chromas": false}, {"id": "875001", "num": 1, "name": "Sett dei Regni Mecha", "chromas": true}, {"id": "875008", "num": 8, "name": "Sett Drago di Ossidiana", "chromas": true}, {"id": "875009", "num": 9, "name": "Sett Drago di Ossidiana (edizione prestigio)", "chromas": false}, {"id": "875010", "num": 10, "name": "Sett Festa in Piscina", "chromas": true}, {"id": "875019", "num": 19, "name": "Sett Fuoco d'Artificio", "chromas": true}, {"id": "875038", "num": 38, "name": "Sett Fiore spirituale", "chromas": true}, {"id": "875045", "num": 45, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "875056", "num": 56, "name": "Sett HEARTSTEEL", "chromas": true}, {"id": "875066", "num": 66, "name": "<PERSON>t Serpente radioso", "chromas": false}], "lore": "Una figura di spicco del crescente mondo criminale di Ionia, Sett è salito al potere sfruttando la scia della guerra con Noxus. Anche se ha iniziato come umile lottatore nelle fosse di Navori, ha rapidamente ottenuto notorietà per la sua forza selvaggia e la sua capacità di subire una quantità apparentemente infinita di colpi. <PERSON><PERSON>, dopo aver scalato le fila dei lottatori, Sett è giunto in cima con la forza bruta, prendendo il controllo dell'arena in cui un tempo si batteva.", "blurb": "Una figura di spicco del crescente mondo criminale di Ionia, Sett è salito al potere sfruttando la scia della guerra con Noxus. Anche se ha iniziato come umile lottatore nelle fosse di Navori, ha rapidamente ottenuto notorietà per la sua forza selvaggia...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "Grin<PERSON>", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 670, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "SettQ", "name": "Di<PERSON><PERSON> dentro", "description": "I due prossimi attacchi di Sett infliggono danni aggiuntivi in base alla salute massima del bersaglio. Sett ottiene anche velocità di movimento quando si muove verso i campioni nemici.", "tooltip": "Sett cerca uno scontro, ottenendo <speed>{{ msamount*100 }}% velocità di movimento</speed> verso i campioni nemici per {{ msduration }} secondi.<br /><br />Inoltre, i prossimi due attacchi di Sett infliggono <physicalDamage>{{ basedamage }} danni fisici aggiuntivi più {{ maxhealthdamagecalc }} della salute massima del bersaglio in danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base", "% salute massima per 100 attacco fisico"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthtadratiotooltip }}% -> {{ maxhealthtadratiotooltipNL }}%"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "SettQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "SettW", "name": "<PERSON><PERSON><PERSON> nelle mani", "description": "Sett immagazzina passivamente i danni subiti come Grinta. <PERSON>ncio, Sett spende tutta la Grinta immagazzinata per ottenere uno scudo e colpisce un'area con un pugno, infliggendo danni puri al centro e danni fisici ai lati.", "tooltip": "<spellPassive>Passiva:</spellPassive> Sett conserva {{ damagestored*100 }}% dei danni subiti come <keywordMajor>G<PERSON><PERSON></keywordMajor>, fino a un massimo di <keywordMajor>{{ maxgrit }}</keywordMajor>. La <keywordMajor>Grinta</keywordMajor> diminuisce rapidamente {{ adrenalinestoragewindow }} secondi dopo aver subito danni.<br /><br /><spellActive>Attiva:</spellActive> Sett consuma tutta la <keywordMajor>Grinta</keywordMajor> e ottiene <shield>{{ shieldconversion*100 }}% della quantità consumata come scudo</shield>, che decresce nell'arco di {{ shieldmaxduration }} secondi. A questo punto, Sett sferra un potente pugno, infliggendo <trueDamage>{{ damagecalc }} più {{ damageconversion }} della Grinta consumata in danni puri</trueDamage> ai nemici al centro dell'area (massimo <trueDamage>{{ f1 }} danni</trueDamage>). I nemici non al centro subiscono invece <physicalDamage>danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SettW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "SettE", "name": "<PERSON><PERSON>cia sfasciata", "description": "Sett tira tutti i nemici da entrambi i suoi lati, infliggendo danni e stordendoli. Se i nemici sono solo nello stesso lato, vengono rallentati invece che storditi.", "tooltip": "Sett fa scontrare uno contro l'altro i nemici che ha lateralmente, infliggendo <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> e <status>rallentandoli</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi. Se Sett afferra almeno un nemico per lato, tutti i nemici vengono <status>storditi</status> per {{ stunduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "SettE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "SettR", "name": "Il pezzo forte", "description": "Sett porta un campione nemico in aria con sé per poi sbatterlo contro il terreno, infliggendo danni e rallentando tutti i nemici vicini al luogo dell'atterraggio.", "tooltip": "Sett afferra un campione nemico, lo <status>sopprime</status> mentre lo trascina in avanti e lo schianta a terra, infliggendo <physicalDamage>{{ damagecalc }} più {{ maxhealthdamage*100 }}% della salute bonus del nemico afferrato in danni fisici</physicalDamage> ai nemici vicini e <status>rallentandoli</status> del {{ slowamount*100 }}% per {{ slowduration }} secondo. Più i nemici sono lontani da dove atterra Sett, meno danni subiscono.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> salute bonus", "Danni base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "SettR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Determinazione della fossa", "description": "Gli attacchi base di Sett si alternano tra pugno sinistro e destro. Il pugno destro è leggermente più forte e veloce. Sett odia perdere, quindi ottiene rigenerazione salute aggiuntiva in base alla sua salute mancante.", "image": {"full": "Sett_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}