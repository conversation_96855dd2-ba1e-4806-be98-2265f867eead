{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Darius": {"id": "<PERSON>", "key": "122", "name": "<PERSON>", "title": "la mano di Noxus", "image": {"full": "Darius.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "122000", "num": 0, "name": "default", "chromas": true}, {"id": "122001", "num": 1, "name": "Lord <PERSON>", "chromas": false}, {"id": "122002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "122003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "122004", "num": 4, "name": "Darius Mae<PERSON> delle Schiacciate", "chromas": true}, {"id": "122008", "num": 8, "name": "Darius dell'Accademia", "chromas": false}, {"id": "122014", "num": 14, "name": "Darius <PERSON> del Terrore", "chromas": false}, {"id": "122015", "num": 15, "name": "<PERSON>", "chromas": false}, {"id": "122016", "num": 16, "name": "Darius <PERSON> Fuoco", "chromas": true}, {"id": "122024", "num": 24, "name": "<PERSON>", "chromas": true}, {"id": "122033", "num": 33, "name": "Darius <PERSON> nella Città del crimine", "chromas": false}, {"id": "122043", "num": 43, "name": "<PERSON>", "chromas": false}, {"id": "122054", "num": 54, "name": "<PERSON>", "chromas": false}, {"id": "122064", "num": 64, "name": "Darius Dio Re Glorioso", "chromas": false}, {"id": "122065", "num": 65, "name": "<PERSON>e Trionfante (edizione prestigio)", "chromas": false}], "lore": "<PERSON>, il comandante più temuto e glorioso della nazione, è il più grande simbolo della forza dei Noxiani. Nato da una famiglia umile e poi divenuto la mano di Noxus, lotta contro i nemici dell'impero (alcuni dei quali sono proprio di Noxus). Sapendo che non esita quando alza la sua ascia e che non dubita mai dei suoi obiettivi, chi si trova al cospetto del leader della legione trifariana sa di non potersi aspettare alcuna pietà.", "blurb": "<PERSON>, il comandante più temuto e glorioso della nazione, è il più grande simbolo della forza dei Noxiani. Nato da una famiglia umile e poi divenuto la mano di Noxus, lotta contro i nemici dell'impero (alcuni dei quali sono proprio di Noxus). Sapendo...", "allytips": ["Decimare è un'abilità molto forte. Colpisci un nemico alla gittata massima per un effetto devastante.", "Ghigliottina di Noxus infligge più danni se hai fatto più attacchi prima di lanciarla. Usa Forza di Noxus per infliggere il massimo dei danni.", "<PERSON> avvantaggiato da una sopravvivenza prolungata. <PERSON><PERSON> riesce a protrarre un combattimento, più diventa potente."], "enemytips": ["Quando la presa ad ascia di Darius è in ricarica, è vulnerabile agli attacchi da tormento.", "L'abilità di Darius di scappare dai combattimenti è limitata. Se hai un vantaggio, batti il ferro finché è caldo."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 652, "hpperlevel": 114, "mp": 263, "mpperlevel": 58, "movespeed": 340, "armor": 37, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 10, "hpregenperlevel": 0.95, "mpregen": 6.6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 5, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "DariusCleave", "name": "Decimare", "description": "Darius si carica e rotea la sua ascia in un ampio cerchio. I nemici colpiti dalla lama subiscono più danni di quelli colpiti dal manico. Darius si cura a seconda di quanti campioni nemici e mostri grandi sono stati colpiti dalla lama.", "tooltip": "<PERSON> solleva la sua ascia e la fa roteare, infliggendo <physicalDamage>{{ bladedamage }} danni fisici</physicalDamage> con la lama e <physicalDamage>{{ handledamage }} danni fisici</physicalDamage> con l'impugnatura. I nemici colpiti dall'impugnatura non ricevono cariche di <keywordMajor>Emorragia</keywordMajor>.<br /><br /><PERSON> recupera il <healing>{{ e5 }}% della salute mancante</healing> per ogni campione nemico e mostro grande della giungla colpito con la lama, fino a un massimo del <healing>{{ e7 }}%</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico totale", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [100, 110, 120, 130, 140], [50, 80, 110, 140, 170], [99, 99, 99, 99, 99], [0.1, 0.1, 0.1, 0.1, 0.1], [17, 17, 17, 17, 17], [35, 35, 35, 35, 35], [51, 51, 51, 51, 51], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/110/120/130/140", "50/80/110/140/170", "99", "0.1", "17", "35", "51", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "DariusCleave.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusNoxianTacticsONH", "name": "<PERSON><PERSON> mutilante", "description": "Il prossimo attacco di Darius colpisce un'arteria vitale del nemico. Mentre sanguina ha la velocità di movimento rallentata.", "tooltip": "Il prossimo attacco base di Darius infligge <physicalDamage>{{ empoweredattackdamage }} danni fi<PERSON>ci</physicalDamage> e <status>rallenta</status> il bersaglio del {{ e2 }}% per {{ e5 }} secondo.<br /><br />Se uccide il bersaglio, questa abilità rimborsa il suo costo in mana e riduce la sua ricarica del {{ e3 }}%.<br /><br /><rules>Questa abilità attiva gli effetti sull'incantesimo quando infligge danni.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rapporto attacco fisico totale"], "effect": ["{{ effect4amount*100.000000 }} -> {{ effect4amountnl*100.000000 }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [90, 90, 90, 90, 90], [50, 50, 50, 50, 50], [1.4, 1.45, 1.5, 1.55, 1.6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "90", "50", "1.4/1.45/1.5/1.55/1.6", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DariusNoxianTacticsONH.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusAxeGrabCone", "name": "<PERSON><PERSON><PERSON>", "description": "Darius affila la sua ascia, aumentando l'efficacia del suo attacco a scapito di una percentuale delle difese del nemico. Quando l'abilità viene attivata, Darius aggancia i nemici con l'ascia e li tira verso di sé.", "tooltip": "<spellPassive>Passiva:</spellPassive> <PERSON> otti<PERSON> {{ e1 }}% penetrazione armatura.<br /><br /><spellActive>Attiva:</spellActive> Darius aggancia i nemici con la sua ascia, <status>attirandoli</status>, <status>lanciandoli in aria</status> e <status>rallentandoli</status> di un {{ e2 }}% per {{ e3 }} secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Percentuale penetrazione armatura", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 23.5, 21, 18.5, 16], "cooldownBurn": "26/23.5/21/18.5/16", "cost": [70, 60, 50, 40, 30], "costBurn": "70/60/50/40/30", "datavalues": {}, "effect": [null, [20, 25, 30, 35, 40], [40, 40, 40, 40, 40], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/25/30/35/40", "40", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [535, 535, 535, 535, 535], "rangeBurn": "535", "image": {"full": "DariusAxeGrabCone.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusExecute", "name": "G<PERSON><PERSON><PERSON><PERSON> di Noxus", "description": "<PERSON> balza su un campione nemico sferrando un colpo letale, che infligge danni puri. Il danno aumenta per ogni carica di Emorragia sul bersaglio. Se Ghigliottina di Noxus infligge un colpo di grazia, il suo tempo di ricarica è azzerato per una breve durata.", "tooltip": "<PERSON> balza su un nemico sferrando un colpo letale, che infligge <trueDamage>{{ damage }} danni puri</trueDamage>. Questa abilità infligge il {{ rdamagepercentperhemostack*100 }}% di danni aggiuntivi per ogni carica di <keywordMajor>Emorragia</keywordMajor> applicata sul bersaglio, fino a un massimo di <trueDamage>{{ maximumdamage }} danni</trueDamage>.<br /><br />Se questo uccide il bersaglio, <PERSON> <recast>rilanciare</recast> questa abilità una volta entro {{ rrecastduration }} secondi. Al livello 3, questa abilità non ha costo in mana e le uccisioni ne azzerano completamente la ricarica.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 0], "costBurn": "100/100/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [460, 460, 460], "rangeBurn": "460", "image": {"full": "DariusExecute.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Emorragia", "description": "Gli attacchi e le abilità che infliggono danni di Darius fanno sanguinare i nemici, che subiscono danni fisici per 5 secondi. Si accumula fino a 5 volte. <PERSON> si infuria e ottiene un grande attacco fisico quando il suo bersaglio raggiunge il massimo delle cariche.", "image": {"full": "<PERSON>_<PERSON><PERSON>_Hemorrhage.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}