{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Olaf": {"id": "<PERSON>", "key": "2", "name": "オラフ", "title": "狂戦士", "image": {"full": "Olaf.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "2000", "num": 0, "name": "default", "chromas": false}, {"id": "2001", "num": 1, "name": "孤高の戦士オラフ", "chromas": false}, {"id": "2002", "num": 2, "name": "氷河のオラフ", "chromas": false}, {"id": "2003", "num": 3, "name": "オラフアニキ", "chromas": true}, {"id": "2004", "num": 4, "name": "Pentakill オラフ", "chromas": false}, {"id": "2005", "num": 5, "name": "略奪者オラフ", "chromas": false}, {"id": "2006", "num": 6, "name": "肉のオラフ", "chromas": false}, {"id": "2015", "num": 15, "name": "SKT T1 オラフ", "chromas": false}, {"id": "2016", "num": 16, "name": "龍殺しオラフ", "chromas": true}, {"id": "2025", "num": 25, "name": "光の番人オラフ", "chromas": true}, {"id": "2035", "num": 35, "name": "Pentakill III: ロストチャプター オラフ", "chromas": true}, {"id": "2044", "num": 44, "name": "地獄の業火オラフ", "chromas": true}], "lore": "制止不能な破壊の力、斧を振りかざすオラフが求めているのは栄光にあふれる戦いの中での自らの死だ。過酷な環境のフレヨルドのロクファール半島出身の彼は、ある日、安らかな死を迎えるという予言を受けた──それは一族の間では臆病者の死を意味し、大いなる屈辱とされていた。怒りに燃えた彼は自らの死を求め、自分にとどめを刺してくれる相手を探して、偉大な戦士や伝説の野獣たちを打ち負かしながら国中を暴れまわった。今ではウィンタークロウの容赦なき用心棒となった彼は、やがて訪れる偉大な戦いの中で自らの死に場所を探そうとしている。", "blurb": "制止不能な破壊の力、斧を振りかざすオラフが求めているのは栄光にあふれる戦いの中での自らの死だ。過酷な環境のフレヨルドのロクファール半島出身の彼は、ある日、安らかな死を迎えるという予言を受けた──それは一族の間では臆病者の死を意味し、大いなる屈辱とされていた。怒りに燃えた彼は自らの死を求め、自分にとどめを刺してくれる相手を探して、偉大な戦士や伝説の野獣たちを打ち負かしながら国中を暴れまわった。今ではウィンタークロウの容赦なき用心棒となった彼は、やがて訪れる偉大な戦いの中で自らの死に場所を探そうとしている。", "allytips": ["オラフの真骨頂は体力が減った時にこそ発揮される。とりわけ「狂戦士の怒り」、「残忍な斧」、「ラグナロク」のコンビネーションは強烈だ。", "「残忍な斧」はライフスティールによる体力回復効果だけでなく、味方からのあらゆる体力回復効果を強化する。"], "enemytips": ["オラフは体力が少ない時ほど危険な相手となるため、一気に止めを刺せるまでスキルを温存できるとベスト。", "レーン戦ではオラフに斧を拾わせないこと。これにより序盤にオラフに対して有利に立ち回れる。", "「ラグナロク」を発動中、オラフは行動妨害スキルを受け付けなくなるが、その一方で防御力は低下する。攻撃回避が難しければ、味方と協力して反撃に転じるのも一案だ。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 119, "mp": 316, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.7, "attackspeedperlevel": 2.7, "attackspeed": 0.694}, "spells": [{"id": "OlafAxeThrowCast", "name": "斧投げ", "description": "指定地点の地面をめがけて斧を投げ、命中した敵にダメージを与えて、物理防御と移動速度を低下させる。斧を拾うと、このスキルのクールダウンがリセットされる。", "tooltip": "斧を投げて<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与え、最大{{ e3 }}秒間(斧の飛距離に応じて)、{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。命中したチャンピオンは{{ debuffduration }}秒間、<scaleArmor>物理防御が{{ shredamount*100 }}%</scaleArmor>低下する。<br /><br />斧を拾うと、このスキルのクールダウンが{{ tooltipcdrefund }}秒に短縮される。{{ tooltipcdrefund }}秒経過していた場合は、クールダウンが完全に解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "モンスターに対するダメージ", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ monsterdamage }} -> {{ monsterdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [30, 30, 30, 30, 30], [2.5, 2.5, 2.5, 2.5, 2.5], [1.5, 1.5, 1.5, 1.5, 1.5], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "30", "2.5", "1.5", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "OlafAxeThrowCast.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "OlafFrenziedStrikes", "name": "根性比べ", "description": "攻撃速度が増加し、シールドを獲得する。", "tooltip": "{{ duration }}秒間、<attackSpeed>攻撃速度が{{ attackspeed*100 }}%</attackSpeed>増加し、{{ shieldduration }}秒間、<shield>耐久値{{ baseshield }}(+減少体力の{{ shieldpercmissinghp*100 }}%)のシールド(体力が{{ thresholdformax*100 }}%未満で最大の耐久値{{ maxshieldcalc }})</shield>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃速度", "基本シールド量", "クールダウン"], "effect": ["{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "OlafFrenziedStrikes.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "OlafRecklessStrike", "name": "捨て身切り", "description": "体力を消費して強烈な攻撃を繰り出し、対象に確定ダメージを与える。対象を倒した場合は消費した体力が回復する。", "tooltip": "激しく斧を振り下ろし、<trueDamage>{{ totaldamage }}の確定ダメージ</trueDamage>を与える。敵をキルすると、消費した体力コストが戻ってくる。<br /><br />通常攻撃を行うたびに、このスキルのクールダウンが1秒解消され、モンスターに通常攻撃を行うと2秒解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "30", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "体力を消費", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "OlafRecklessStrike.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "体力を消費"}, {"id": "Olaf<PERSON><PERSON><PERSON><PERSON>", "name": "ラグナロク", "description": "自動効果で物理防御と魔法防御が増加する。このスキルを発動すると、通常攻撃を続けている限りは行動妨害効果を受けなくなる。", "tooltip": "<spellPassive>自動効果:</spellPassive> <scaleArmor>物理防御が{{ resists }}</scaleArmor>、<scaleMR>魔法防御が{{ resists }}</scaleMR>増加する。<br /><br /><spellActive>発動効果: </spellActive>自身が受けている<status>移動不能効果</status>と<status>行動妨害効果</status>がすべて除去され、{{ duration }}秒間、それらの効果を受けなくなる。発動中は、<scaleAD>攻撃力が{{ ad }}</scaleAD>増加する。チャンピオンに通常攻撃または<spellName>「捨て身切り」</spellName>を命中させると、効果時間が{{ durationextension }}秒延長される。<br /><br />また{{ hasteduration }}秒間、敵チャンピオンに向かう際の<speed>移動速度が{{ haste*100 }}%</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["物理/魔法防御", "攻撃力", "移動速度", "クールダウン"], "effect": ["{{ resists }} -> {{ resistsNL }}", "{{ flatad }} -> {{ flatadNL }}", "{{ haste*100.000000 }} -> {{ hastenl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "OlafRagnarok.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "狂戦士の怒り", "description": "減少体力に応じて攻撃速度とライフスティールが増加する。", "image": {"full": "Olaf_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}