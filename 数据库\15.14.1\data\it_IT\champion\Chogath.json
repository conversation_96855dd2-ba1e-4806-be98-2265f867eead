{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Chogath": {"id": "<PERSON><PERSON><PERSON>", "key": "31", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "il terrore del Vuoto", "image": {"full": "Chogath.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "31000", "num": 0, "name": "default", "chromas": false}, {"id": "31001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "31002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "31003", "num": 3, "name": "Cho'Gath Loch Ness", "chromas": false}, {"id": "31004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "31005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "31006", "num": 6, "name": "<PERSON>'<PERSON>ath <PERSON>", "chromas": false}, {"id": "31007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "chromas": false}, {"id": "31014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "31023", "num": 23, "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON> violato", "chromas": true}, {"id": "31032", "num": 32, "name": "Cho'Gath Giocorrore", "chromas": true}], "lore": "Dal momento in cui Cho'Gath è emerso sotto la dura luce del sole di Runeterra, la bestia è stata animata da una fame pura e insaziabile. Cho'Gath è una perfetta espressione del desiderio del Vuoto di consumare la vita. Ha una struttura biologica complessa, che converte la materia in crescita corporea, conferendo massa e forza o rinforzando il suo carapace, come se fosse fatto di diamanti. Quando diventare più grande non è conveniente, la creatura vomita i materiali in eccesso sotto forma di spine affilate come rasoi, infilzando la preda per poi divorarla.", "blurb": "Dal momento in cui Cho'Gath è emerso sotto la dura luce del sole di Runeterra, la bestia è stata animata da una fame pura e insaziabile. Cho'Gath è una perfetta espressione del desiderio del Vuoto di consumare la vita. Ha una struttura biologica...", "allytips": ["Cerca di allineare gli attacchi con Spine vorpal per uccidere i minion e aggredire i nemici al tempo stesso.", "Se hai difficoltà a divorare i campioni, prova a mangiare i minion per diventare più grande.", "Usare Spaccatura sui minion, insieme a Carnivoro, è un ottimo modo per ottenere salute e mana."], "enemytips": ["Se compri qualche oggetto per la salute diminuisci le probabilità di essere ucciso velocemente da Cho'Gath.", "Concentrati nel non far raggiungere a Cho'Gath la sua stazza massima.", "Spaccatura ha una nuvola di polvere che indica l'area dove colpirà. Fai attenzione in modo da non lasciare la possibilità a Cho'Gath di realizzare una combo con le sue abilità."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 5}, "stats": {"hp": 644, "hpperlevel": 94, "mp": 270, "mpperlevel": 60, "movespeed": 345, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.2, "attackspeedperlevel": 1.44, "attackspeed": 0.658}, "spells": [{"id": "Rupture", "name": "Spaccatura", "description": "Rompe il suolo in una posizione bersaglio, lanciando le unità nemiche in aria, infliggendo loro danni e rallentandole.", "tooltip": "Cho'Gath spacca il terreno, <status>lanciando in aria</status> i nemici per {{ e5 }} secondo/i, infliggendo <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage> e <status>rallentandoli</status> del {{ e2 }}% per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [60, 60, 60, 60, 60], [1.5, 1.5, 1.5, 1.5, 1.5], [0.625, 0.625, 0.625, 0.625, 0.625], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "60", "1.5", "0.63", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "Rupture.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FeralScream", "name": "<PERSON><PERSON><PERSON> best<PERSON>e", "description": "Cho'Gath emette un terribile urlo che colpisce i nemici nell'area conica innanzi a lui, infliggendo danni magici e silenziando le vittime per qualche secondo.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> ruggisce, <status>silenziando</status> i nemici per {{ e2 }} secondi e infliggendo <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "<PERSON><PERSON> silenzio", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1.6, 1.7, 1.8, 1.9, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1.6/1.7/1.8/1.9/2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "FeralScream.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VorpalSpikes", "name": "Spine vorpal", "description": "<PERSON><PERSON> attacchi di Cho'Gath lanciano delle spine letali che infliggono danni e rallentano tutte le unità nemiche davanti a lui.", "tooltip": "I prossimi 3 attacchi di <PERSON>'Gath lanciano spine che infliggono <magicDamage>{{ flatdamagecalc }} + {{ maxhealthpercentcalc }} della salute massima del bersaglio in danni magici</magicDamage> e lo <status>rallentano</status> di un {{ slowamountpercentage }}%, con un effetto che decresce nell'arco di {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> salute massima", "Rallentamento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthealthdamage }}% -> {{ percenthealthdamageNL }}%", "{{ slowamountpercentage }}% -> {{ slowamountpercentageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [40, 40, 40, 40, 40], "rangeBurn": "40", "image": {"full": "VorpalSpikes.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Feast", "name": "Banchetto", "description": "Divora un'unità nemica, infliggendo un'elevata quantità di danni puri. Se il bersaglio viene ucciso, <PERSON><PERSON><PERSON><PERSON> cresce, ottenendo salute massima.", "tooltip": "Cho'Gath banchetta con il corpo di un nemico, infliggendo <trueDamage>{{ rdamage }} danni puri</trueDamage> ai campioni o <trueDamage>{{ rmonsterdamage }} danni puri</trueDamage> a minion e mostri della giungla. Se il bersaglio viene ucciso, Cho'Gath ottiene una carica di Banchetto, aumenta di dimensioni e incrementa di <healing>{{ rhealthperstack }} la sua salute massima</healing>. Può ottenere un massimo di {{ rminionmaxstacks }} cariche da minion e mostri della giungla non epici. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>ni ai campioni", "Salute per carica", "Gittata d'attacco per carica", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rhealthperstack }} -> {{ rhealthperstackNL }}", "{{ attackrangeperstack }} -> {{ attackrangeperstackNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 70, 60], "cooldownBurn": "80/70/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [175, 175, 175], "rangeBurn": "175", "image": {"full": "Feast.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Carnivoro", "description": "Quando <PERSON>ath uccide un'unità, recupera salute e mana. La quantità recuperata aumenta con il livello di <PERSON>'Gath.", "image": {"full": "GreenTerror_TailSpike.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}