{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viego": {"id": "Viego", "key": "234", "name": "Viego", "title": "der gestürzte König", "image": {"full": "Viego.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "234000", "num": 0, "name": "default", "chromas": false}, {"id": "234001", "num": 1, "name": "Mondzodiak-Viego", "chromas": true}, {"id": "234010", "num": 10, "name": "Dissonance of Pentakill-Viego", "chromas": false}, {"id": "234019", "num": 19, "name": "EDG-Viego", "chromas": false}, {"id": "234021", "num": 21, "name": "K<PERSON>nig <PERSON>", "chromas": false}, {"id": "234030", "num": 30, "name": "Soul Fighter Viego", "chromas": false}, {"id": "234037", "num": 37, "name": "WM 2024-Viego", "chromas": false}], "lore": "Viego war der Herrscher über ein inzwischen lange verlorenes Reich und starb vor über tausend Jahren bei dem Versuch, seine geliebte Frau von den Toten zurückzuholen. Dabei löste er eine magische Katastrophe aus, die man heute als die Zerstörung kennt. Er wurde zu einem mächtigen, untoten Gespenst und ist besessen von der Sehnsucht nach seiner Königin, die bereits seit Jahrhunderten tot ist. Man kennt Viego nun als den gestürzten König, der über die tödlichen Graunächte gebietet und Runeterra nach Mitteln und Wegen durchkämmt, seine Geliebte eines Tages zurückzubringen. Der schwarze Nebel strömt unaufhörlich aus seinem grausamen, gebrochenen Herz und zerstört alles auf seinem Weg.", "blurb": "Viego war der Herrscher über ein inzwischen lange verlorenes Reich und starb vor über tausend Jahren bei dem Versuch, seine geliebte Frau von den Toten zurückzuholen. Dabei löste er eine magische Katastrophe aus, die man heute als die Zerstörung kennt...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 10000, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 200, "hpregen": 7, "hpregenperlevel": 0.7, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "ViegoQ", "name": "Klinge des gestürzten Königs", "description": "Viegos Geisterklinge verursacht passiv <OnHit>bei einem Treffer</OnHit> einen Prozentsatz des aktuellen Lebens als zusätzlichen Schaden. Außerdem trifft er <PERSON><PERSON><PERSON> doppelt, die er kürzlich mit einer Fähigkeit getroffen hat, und raubt ihnen Leben.<br><br>Viego kann diese Fähigkeit aktivieren, um seinen Zweihänder nach vorne zu stoßen und Gegner vor ihm aufzuspießen.", "tooltip": "<spellPassive>Passiv:</spellPassive> Viegos Angriffe verursachen zusätzlich <physicalDamage>normalen Schaden</physicalDamage> in <PERSON><PERSON><PERSON> von {{ totalpercenthealthonhit }} des aktuellen Lebens. Wenn er einem Gegner kürzlich mit einer Fähigkeit Schaden zugefügt hat, trifft sein erster Angriff ein zweites Mal, verursacht <physicalDamage>{{ secondattackdamage }}&nbsp;normalen Schaden</physicalDamage> und heilt Viego in Höhe von <healing>{{ healmodvschamps*100 }}&nbsp;% des verursachten Schadens</healing>. Diese Boni bleiben erhalten, während er von einem Gegner <keywordMajor>Besitz ergreift</keywordMajor>.<br /><br /><spellActive>Aktiv: </spellActive>Viego schlägt nach vorn zu und verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Aktuelles Leben %", "Aktueller Mindestschaden (Leben)", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ percenthealthonhit }}&nbsp;% -> {{ percenthealthonhitNL }}&nbsp;%", "{{ mindamageon<PERSON> }} -> {{ mindamageonhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5, 4.5, 4, 3.5, 3], "cooldownBurn": "5/4.5/4/3.5/3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViegoQ.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "ViegoW", "name": "Spektralschleier", "description": "<PERSON><PERSON><PERSON> sammelt sich, springt dann nach vorn und entfesselt eine Kugel aus konzentriertem schwarzen Nebel, die den ersten getroffenen Gegner betäubt.", "tooltip": "<charge>Aufladungsbeginn:</charge> Viego zieht den Nebel zu sich und <status>verlangsamt</status> sich dabei selbst um {{ selfslowpercent*100 }}&nbsp;%.<br /><br /><release>Loslassen:</release> Viego springt nach vorn und entfesselt den gesammelten Nebel. Er verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und <status>betäubt</status> den ersten getroffenen Gegner abhängig von der Aufladungsdauer zwischen {{ stunduration }} und {{ maxstuntt }}&nbsp;Sekunden lang.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViegoW.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "ViegoE", "name": "Pfad des Leids", "description": "Viego befiehlt dem schwarzen Nebel, einen Teil des Terrains heimzusuchen und einzuhüllen. Viego kann sich im Nebel als Geist verstecken, um Camouflage, Lauftempo und Angriffstempo zu erhalten.", "tooltip": "Viego sendet einen Spukgeist aus, der das erste getroffene Terrain heimsucht und es {{ mistduration }}&nbsp;Sekunden lang in Nebel hüllt. Viego erhält <keywordStealth>Camouflage</keywordStealth>, <speed>{{ totalmovespeed }}&nbsp;Lauftempo</speed> und <attackSpeed>{{ attackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed>, während er sich im Nebel befindet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "Angriffstempo", "Abklingzeit"], "effect": ["{{ movespeed*100.000000 }}&nbsp;% -> {{ movespeednl*100.000000 }}&nbsp;%", "{{ attackspeed*100.000000 }}&nbsp;% -> {{ attackspeednl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViegoE.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "ViegoR", "name": "Herzensbrecher", "description": "Viego teleportiert sich an einen nahegelegenen Ort. Bei seiner Ankunft exekutiert er einen gegnerischen Champion und löst eine zerstörerische Schockwelle aus, die andere Gegner wegstößt.", "tooltip": "Viego entledigt sich jegliche<PERSON>, falls er aktuell von einer <keywordMajor>Besitz ergriffen</keywordMajor> hat, und teleportiert sich über eine kurze Distanz. Bei der Ankunft greift er den Champion mit dem niedrigsten prozentualen Leben an, <status>verlangsamt</status> ihn kurzzeitig um {{ slowpercent*100 }}&nbsp;% und fügt ihm <physicalDamage>normalen Schaden</physicalDamage> in Höhe von {{ totaldamage }} + {{ totalpercenthealth }}&nbsp;% des fehlenden Lebens zu. Andere Gegner in der Nähe werden <status>zurückgestoßen</status> und erleiden <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden abhängig vom fehlenden Leben", "Abklingzeit"], "effect": ["{{ maxhealthdamage }}&nbsp;% -> {{ maxhealthdamageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "ViegoR.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Verfügung des Herrschers", "description": "Mit <PERSON>iegos Hilfe getötete Gegner werden zu Geistern. Greift er einen Geist an, übernimmt er vorübergehend die Kontrolle über den Körper des Gegners, heilt sich um einen Prozentsatz des maximalen Lebens des Ziels und erhält Zugriff auf dessen Grundfähigkeiten und Gegenstände. Er ersetzt die ultimative Fähigkeit seines Opfers mit einer kostenlosen Aktivierung seiner eigenen.", "image": {"full": "Viego_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}