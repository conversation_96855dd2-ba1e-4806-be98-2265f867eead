{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "<PERSON><PERSON><PERSON>", "title": "der Todessänger", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "Phantom-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "30004", "num": 4, "name": "Pentakill-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30005", "num": 5, "name": "Fnatic-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30010", "num": 10, "name": "Infernalische<PERSON> Karthus", "chromas": true}, {"id": "30017", "num": 17, "name": "Pentakill III: Lost Chapter-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "30026", "num": 26, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON>rthus ist ein Bote des Vergessens, ein unsterbliches Geisterwesen, dessen quälende Lieder sein schauerliches Erscheinen ankündigen. Die Lebenden fürchten sich davor, auf ewig untot zu sein, doch Karthus sieht in diesem Geschenk nichts als Schönheit und Reinheit, eine vollkommene Einheit von Leben und Tod. Karthus erhebt sich von den Schatteninseln, um als Apostel der Toten den Sterblichen die Wonne des Todes zu schenken.", "blurb": "<PERSON>rth<PERSON> ist ein Bote des Vergessens, ein unsterbliches Geisterwesen, dessen quälende Lieder sein schauerliches Erscheinen ankündigen. Die Lebenden fürchten sich davor, auf ewig untot zu sein, doch Karth<PERSON> sieht in diesem Geschenk nichts als Schönheit...", "allytips": ["Arbeite mit deinen Verbündeten zusammen, um mit „Requiem“ auch Champions in anderen Lanes auszuschalten.", "„Verwüsten“ ist sehr stark, wenn es darum geht, Unmengen an Vasallen zu besiegen oder gegnerische Champions zu beharken."], "enemytips": ["<PERSON><PERSON><PERSON> kann noch kurz weiter Zauber wirken, nachdem er getötet wurde. Entferne dich von seiner Leiche, um dich in Sicherheit zu bringen.", "<PERSON><PERSON><PERSON> da<PERSON>, dass du immer genug Leben hast, um „Requiem“ zu überleben, selbst wenn du öfters zur Basis zurückkehren musst, um dort zu regenerieren."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "Verwüsten", "description": "<PERSON><PERSON><PERSON> erzeugt eine verzögerte Explosion an einer Stelle, wodurch er an allen nahen Gegnern Schaden verursacht. Isolierte Gegner erleiden erhöhten Schaden. ", "tooltip": "<PERSON><PERSON><PERSON> wirkt einen <PERSON>, der <magicDamage>{{ qdamage }}&nbsp;magischen Schaden verursacht</magicDamage>. Wenn der Stoß nur einen Gegner trifft, verursacht er stattdessen <magicDamage>{{ qsingletargetdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "Mauer des Schmerzes", "description": "<PERSON><PERSON><PERSON> erzeugt eine passierbare Wand aus negativer Energie. Jede gegnerische Einheit, welche die Wand durchschreitet, verliert für eine bestimmte Zeit Lauftempo und Magieresistenz.", "tooltip": "<PERSON><PERSON><PERSON> erzeugt eine <PERSON>, die {{ e4 }}&nbsp;Sekunden lang bestehen bleibt. <PERSON><PERSON><PERSON>, die sie passieren, verlieren {{ e5 }}&nbsp;Sekunden lang <scaleMR>{{ e1 }}&nbsp;% Magieresistenz</scaleMR> und werden um {{ e3 }}&nbsp;% <status>verlangsamt</status>. Die Verlangsamung fällt über die Dauer hinweg ab.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}&nbsp;% -> {{ e3NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Vernichtung", "description": "<PERSON><PERSON><PERSON> stiehlt passiv Energie von seinen Opfern, wodurch er mit jedem Todesstoß Mana gewinnt. Alternativ kann Karthus sich selbst mit den Seelen seiner Beute umgeben und so Schaden an nahen G<PERSON>nern verursachen. Doch dadurch verbraucht er sein Mana sehr schnell.", "tooltip": "<spellPassive>Passiv: </spellPassive><PERSON><PERSON> eine Einheit tötet, stellt er <scaleMana>{{ e2 }}&nbsp;<PERSON><PERSON></scale<PERSON>ana> wieder her.<br /><br /><toggle>Aktivierbar:</toggle> <PERSON><PERSON><PERSON> erschafft eine nekrotische Aura, die Gegnern in der Nähe <magicDamage>{{ totaldps }}&nbsp;magischen Schaden</magicDamage> pro Sekunde zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde", "Manawiederherstellung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana pro Sekunde", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}&nbsp;Mana pro Sekunde"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "Requiem", "description": "Nachdem Karthus sich 3 Sekunden lang konzentriert hat, fügt er allen gegnerischen Champions magischen Schaden zu, egal wo sie sich befinden.", "tooltip": "<PERSON><PERSON><PERSON> kanalisiert 3&nbsp;<PERSON><PERSON><PERSON> lang und fügt dann allen gegnerischen Champions <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu, egal wo sie sich befinden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> To<PERSON>", "description": "<PERSON><PERSON> stir<PERSON>, nimmt er die Form eines Geistes an. So kann er weitere Fähigkeiten einsetzen.", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}