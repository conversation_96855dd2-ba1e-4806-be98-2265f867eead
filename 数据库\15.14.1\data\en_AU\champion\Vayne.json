{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vayne": {"id": "<PERSON><PERSON>", "key": "67", "name": "<PERSON><PERSON>", "title": "the Night Hunter", "image": {"full": "Vayne.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "67000", "num": 0, "name": "default", "chromas": false}, {"id": "67001", "num": 1, "name": "Vindicator <PERSON><PERSON>", "chromas": false}, {"id": "67002", "num": 2, "name": "Aristo<PERSON>", "chromas": false}, {"id": "67003", "num": 3, "name": "Dragonslayer <PERSON><PERSON>", "chromas": true}, {"id": "67004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "67005", "num": 5, "name": "SKT T1 Vayne", "chromas": false}, {"id": "67006", "num": 6, "name": "Arclight Vayne", "chromas": false}, {"id": "67010", "num": 10, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "67011", "num": 11, "name": "PROJECT: <PERSON><PERSON>", "chromas": false}, {"id": "67012", "num": 12, "name": "Firecracker Vayne", "chromas": true}, {"id": "67013", "num": 13, "name": "Prestige Firecracker Vayne", "chromas": false}, {"id": "67014", "num": 14, "name": "Spirit Blossom Vayne", "chromas": true}, {"id": "67015", "num": 15, "name": "FPX Vayne", "chromas": true}, {"id": "67025", "num": 25, "name": "Sentinel Vayne", "chromas": true}, {"id": "67032", "num": 32, "name": "Battle Bat Vayne", "chromas": true}, {"id": "67033", "num": 33, "name": "Prestige Firecracker Vayne (2022)", "chromas": false}, {"id": "67044", "num": 44, "name": "Dawnbringer Vayne", "chromas": true}, {"id": "67055", "num": 55, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "67064", "num": 64, "name": "Risen Legend <PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> is a deadly, remorseless Demacian monster hunter, who has dedicated her life to finding and destroying the demon that murdered her family. Armed with a wrist-mounted crossbow and a heart full of vengeance, she is only truly happy when slaying practitioners or creations of the dark arts, striking from the shadows with a flurry of silver bolts.", "blurb": "<PERSON><PERSON> is a deadly, remorseless Demacian monster hunter, who has dedicated her life to finding and destroying the demon that murdered her family. Armed with a wrist-mounted crossbow and a heart full of vengeance, she is only truly happy when...", "allytips": ["Tumble has many uses, but it cannot pass over walls.", "Condemn can be used to both pin targets to walls to ensure a kill, or to escape a pursuer.", "Don't go in first to a large teamfight. Wait for your teammates to initiate."], "enemytips": ["<PERSON><PERSON> is fragile - turn the pressure up on her and she will be forced to play cautiously.", "<PERSON>y <PERSON> the opportunity to pin you against walls."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 1, "magic": 1, "difficulty": 8}, "stats": {"hp": 550, "hpperlevel": 103, "mp": 232, "mpperlevel": 35, "movespeed": 330, "armor": 23, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.35, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "VayneTumble", "name": "Tumble", "description": "<PERSON><PERSON> tumbles, maneuvering to carefully place her next shot. Her next attack deals bonus damage.", "tooltip": "<PERSON><PERSON> rolls a short distance, and deals an additional <physicalDamage>{{ adratiobonus }} physical damage</physicalDamage> on her next Attack.<br /><br /><rules>This Ability triggers spell effects upon dealing damage.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "AD Ratio %"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ totaladratio*100.000000 }}% -> {{ totaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VayneTumble.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>ayneSil<PERSON><PERSON><PERSON><PERSON>", "name": "Silver Bolts", "description": "<PERSON><PERSON> tips her bolts with a rare metal, toxic to evil things. The third consecutive attack or ability against the same target deals a percentage of the target's max health as bonus true damage.", "tooltip": "<spellPassive>Passive</spellPassive>: Every third consecutive Attack or Ability against an enemy deals an additional <trueDamage>{{ totaldamage }} max Health true damage</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["True Damage %", "Minimum Damage"], "effect": ["{{ maxhealthratio*100.000000 }}% -> {{ maxhealthrationl*100.000000 }}%", "{{ damagefloor }} -> {{ damagefloorNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passive", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VayneSilveredBolts.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Passive"}, {"id": "VayneCondemn", "name": "Condemn", "description": "<PERSON><PERSON> draws a heavy crossbow from her back, and fires a huge bolt at her target, knocking them back and dealing damage. If they collide with terrain, they are impaled, dealing bonus damage and stunning them.", "tooltip": "<PERSON><PERSON> fires a bolt that <status>Knocks Back</status> and deals <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>. If the target collides with terrain, they take <physicalDamage>{{ empowereddamagett }} bonus physical damage</physicalDamage> and become <status>Stunned</status> for {{ stunduration }} seconds. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "VayneCondemn.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VayneInquisition", "name": "Final Hour", "description": "Readying herself for an epic confrontation, <PERSON><PERSON> gains increased Attack Damage, Invisibility during Tumble, reduced Tumble cooldown, and more bonus Move Speed from Night Hunter", "tooltip": "<PERSON><PERSON> gains <physicalDamage>{{ bonusattackdamage }} Attack Damage</physicalDamage> for {{ baseduration }} seconds, extended by {{ durationtoadd }} seconds whenever a champion damaged by <PERSON><PERSON> dies within {{ damagedmarkerduration }} seconds. Additionally, during the duration:<li><spellName>Night Hunter</spellName> instead grants <speed>{{ movementspeed }} Move Speed</speed>.<li>The Cooldown of <spellName>Tumble</spellName> is reduced by {{ tumblecdreduction }}%, and grants <keywordStealth>Invisibility</keywordStealth> for {{ tumblestealthduration }} second.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Duration", "Bonus AD", "Tumble Cooldown Reduction"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseduration }} -> {{ basedurationNL }}", "{{ bonusattackdamage }} -> {{ bonusattackdamageNL }}", "{{ tumblecdreduction }}% -> {{ tumblecdreductionNL }}%"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "VayneInquisition.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Night Hunter", "description": "<PERSON><PERSON> ruthlessly hunts evil-doers, gaining Move Speed when moving toward nearby enemy champions.", "image": {"full": "Vayne_NightHunter.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}