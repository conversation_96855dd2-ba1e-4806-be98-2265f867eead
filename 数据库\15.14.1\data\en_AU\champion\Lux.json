{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lux": {"id": "<PERSON><PERSON>", "key": "99", "name": "<PERSON><PERSON>", "title": "the Lady of Luminosity", "image": {"full": "Lux.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "99000", "num": 0, "name": "default", "chromas": false}, {"id": "99001", "num": 1, "name": "Sorceress Lux", "chromas": false}, {"id": "99002", "num": 2, "name": "Spellthief Lux", "chromas": false}, {"id": "99003", "num": 3, "name": "Commando Lux", "chromas": false}, {"id": "99004", "num": 4, "name": "Imperial Lux", "chromas": false}, {"id": "99005", "num": 5, "name": "Steel Legion Lux", "chromas": false}, {"id": "99006", "num": 6, "name": "Star Guardian Lux", "chromas": false}, {"id": "99007", "num": 7, "name": "Elementalist Lux", "chromas": false}, {"id": "99008", "num": 8, "name": "Lunar Empress <PERSON><PERSON>", "chromas": true}, {"id": "99014", "num": 14, "name": "Pajama Guardian Lux", "chromas": false}, {"id": "99015", "num": 15, "name": "Battle Academia Lux", "chromas": false}, {"id": "99016", "num": 16, "name": "Prestige Battle Academia Lux", "chromas": false}, {"id": "99017", "num": 17, "name": "Dark Cosmic Lux", "chromas": false}, {"id": "99018", "num": 18, "name": "Cosmic Lux", "chromas": false}, {"id": "99019", "num": 19, "name": "Space Groove Lux", "chromas": true}, {"id": "99029", "num": 29, "name": "Porcelain Lu<PERSON>", "chromas": true}, {"id": "99038", "num": 38, "name": "Soul Fighter Lux", "chromas": true}, {"id": "99039", "num": 39, "name": "Prestige Battle Academia Lux (2022)", "chromas": false}, {"id": "99040", "num": 40, "name": "Prestige Porcelain Lux", "chromas": false}, {"id": "99042", "num": 42, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "99061", "num": 61, "name": "Faerie Court Lux", "chromas": true}, {"id": "99070", "num": 70, "name": "Prestige Spirit Blossom Lux", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> hails from Demacia, an insular realm where magical abilities are viewed with fear and suspicion. Able to bend light to her will, she grew up dreading discovery and exile, and was forced to keep her power secret, in order to preserve her family's noble status. Nonetheless, <PERSON><PERSON>'s optimism and resilience have led her to embrace her unique talents, and she now covertly wields them in service of her homeland.", "blurb": "<PERSON><PERSON><PERSON> hails from Demacia, an insular realm where magical abilities are viewed with fear and suspicion. Able to bend light to her will, she grew up dreading discovery and exile, and was forced to keep her power secret, in order to preserve...", "allytips": ["<PERSON><PERSON> has great zone control abilities. Try to set up Lucent Singularity to prevent an enemy's advance or escape.", "If you have trouble landing Prismatic Barrier, remember that it returns to you after it reaches max range. Try positioning yourself to hit your allies with its return trip.", "Lucent Singularity is a great scouting tool. Try throwing it into brush before walking into it to check for ambushes."], "enemytips": ["<PERSON><PERSON> has powerful zone control abilities. Try to spread out and attack from different directions so she cannot lock down a specific area.", "When retreating with low health, be prepared to dodge <PERSON><PERSON>'s Final Spark, a red targeting beam fires prior to the main beam, so try to move to the side if possible."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 580, "hpperlevel": 99, "mp": 480, "mpperlevel": 23.5, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3, "attackspeed": 0.669}, "spells": [{"id": "LuxLightBinding", "name": "Light Binding", "description": "<PERSON><PERSON> releases a sphere of light that binds and deals damage to up to two enemy units.", "tooltip": "<PERSON><PERSON> fires a ball of light, <status>Rooting</status> the first two enemies for {{ e3 }} seconds and dealing <magicDamage>{{ totaldamagett }} magic damage</magicDamage> to each.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "50", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1175, 1175, 1175, 1175, 1175], "rangeBurn": "1175", "image": {"full": "LuxLightBinding.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxPrismaticWave", "name": "Prismatic Barrier", "description": "<PERSON><PERSON> throws her wand and bends the light around any friendly target it touches, protecting them from enemy damage.", "tooltip": "<PERSON><PERSON> throws her wand, granting <shield>{{ totalshieldtt }} Shield</shield> for {{ e3 }} seconds to allies it passes through. Then it returns, granting the same <shield>Shield</shield> on its return.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Shield Amount", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [2, 4, 6, 8, 10], [40, 55, 70, 85, 100], [2.5, 2.5, 2.5, 2.5, 2.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2/4/6/8/10", "40/55/70/85/100", "2.5", "100", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "LuxPrismaticWave.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxLightStrikeKugel", "name": "Lucent Singularity", "description": "Fires an anomaly of twisted light to an area, which slows nearby enemies. <PERSON><PERSON> can detonate it to damage enemies in the area of effect.", "tooltip": "<PERSON><PERSON> creates a light zone that <status>Slows</status> by {{ e1 }}% and reveals the area. After {{ e3 }} seconds or on <recast>Recasting</recast> this Ability, it detonates, dealing <magicDamage>{{ totaldamagett }} magic damage</magicDamage> and <status>Slowing</status> for an additional {{ slowlingerduration }} second.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Movement Slow", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [65, 115, 165, 215, 265], [5, 5, 5, 5, 5], [310, 310, 310, 310, 310], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25/30/35/40/45", "65/115/165/215/265", "5", "310", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LuxLightStrikeKugel.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxR", "name": "Final Spark", "description": "After gathering energy, <PERSON><PERSON> fires a beam of light that deals damage to all targets in the area. In addition, triggers <PERSON><PERSON>'s passive ability and refreshes the Illumination debuff duration.", "tooltip": "<PERSON><PERSON> fires a dazzling ray of light, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to all enemies in a line.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 50, 40], "cooldownBurn": "60/50/40", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3340, 3340, 3340], "rangeBurn": "3340", "image": {"full": "LuxR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Illumination", "description": "<PERSON><PERSON>'s damaging spells charge the target with energy for a few seconds. <PERSON><PERSON>'s next attack ignites the energy, dealing bonus magic damage (depending on <PERSON><PERSON>'s level) to the target.", "image": {"full": "LuxIlluminatingFraulein.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}