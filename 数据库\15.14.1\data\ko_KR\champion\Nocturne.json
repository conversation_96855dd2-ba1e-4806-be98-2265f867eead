{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nocturne": {"id": "Nocturne", "key": "56", "name": "녹턴", "title": "영원한 악몽", "image": {"full": "Nocturne.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "56000", "num": 0, "name": "default", "chromas": false}, {"id": "56001", "num": 1, "name": "얼어붙은 공포 녹턴", "chromas": false}, {"id": "56002", "num": 2, "name": "공허의 녹턴", "chromas": false}, {"id": "56003", "num": 3, "name": "약탈자 녹턴", "chromas": false}, {"id": "56004", "num": 4, "name": "유령 녹턴", "chromas": false}, {"id": "56005", "num": 5, "name": "이터늄 녹턴", "chromas": false}, {"id": "56006", "num": 6, "name": "저주받은 망령 녹턴", "chromas": false}, {"id": "56007", "num": 7, "name": "고대 신 녹턴", "chromas": true}, {"id": "56016", "num": 16, "name": "마법공학 녹턴", "chromas": false}, {"id": "56017", "num": 17, "name": "깨진 언약 녹턴", "chromas": true}, {"id": "56026", "num": 26, "name": "창공 녹턴", "chromas": true}], "lore": "언제부터인가 자의식이 있는 존재라면 반드시 꾸는 악몽이 있었다. 그리고 그 악몽들이 모여 사악한 기운을 끌어들였고, 그 속에서 태곳적 힘을 지닌 순수한 악 그 자체인 녹턴이 생겨났다. 녹턴은 혼돈을 암흑의 액체로 표현한 듯한 형상으로, 얼굴은 없으나 차디찬 눈을 지녔으며 흉흉해 보이는 칼날로 무장했다. 영혼계에서 탈출하여 생명이 깨어 있는 세계로 내려온 녹턴은 진정한 암흑에서나 피어날 법한 공포를 먹이로 삼는다.", "blurb": "언제부터인가 자의식이 있는 존재라면 반드시 꾸는 악몽이 있었다. 그리고 그 악몽들이 모여 사악한 기운을 끌어들였고, 그 속에서 태곳적 힘을 지닌 순수한 악 그 자체인 녹턴이 생겨났다. 녹턴은 혼돈을 암흑의 액체로 표현한 듯한 형상으로, 얼굴은 없으나 차디찬 눈을 지녔으며 흉흉해 보이는 칼날로 무장했다. 영혼계에서 탈출하여 생명이 깨어 있는 세계로 내려온 녹턴은 진정한 암흑에서나 피어날 법한 공포를 먹이로 삼는다.", "allytips": ["피해망상 스킬은 모든 적군의 시야를 방해하므로 꼭 직접 공격용으로 사용하지 않아도 효과적입니다.", "황혼의 인도자는 공격에만 사용하는 능력이 아닙니다. 전투 시 간격을 좁히거나 위기에서 탈출할 때도 사용할 수 있습니다.", "어둠의 장막을 피해망상과 함께 사용하십시오. 적이 혼란에 빠져 플레이어의 주문 방어막에 결정적인 방해 효과 스킬을 낭비할지도 모릅니다."], "enemytips": ["피해망상이 시전되었을 때는 되도록 여러 명의 아군과 가까이 붙어 계십시오!", "녹턴의 말할 수 없는 공포 스킬은 거리가 멀면 실패하므로 이동 관련 스킬이나 주문이 있다면 적절하게 사용하십시오."], "tags": ["Fighter", "Assassin"], "partype": "마나", "info": {"attack": 9, "defense": 5, "magic": 2, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 109, "mp": 275, "mpperlevel": 35, "movespeed": 345, "armor": 38, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2.6, "attackspeedperlevel": 2.7, "attackspeed": 0.721}, "spells": [{"id": "Nocturn<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "황혼의 인도자", "description": "녹턴이 그림자 칼날을 던져 피해를 입히고 황혼의 궤적을 남깁니다. 황혼의 칼날에 적중당한 적 챔피언들 또한 황혼의 궤적을 남깁니다. 궤적에 있는 동안 녹턴은 다른 유닛을 통과할 수 있게 되며 이동 속도 및 공격력이 증가합니다.", "tooltip": "녹턴이 그림자 칼날을 던져 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ e3 }}초 동안 황혼의 궤적을 남깁니다. 공격당한 적 챔피언 역시 황혼의 궤적을 남깁니다. <br /><br />녹턴은 궤적 위로 이동 시 유체화 상태가 되고 <speed>이동 속도가 {{ movespeed }}%</speed> 상승하며 <physicalDamage>공격력이 {{ bonustrailad }}</physicalDamage>증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "이동 속도", "추가 공격력", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed }}% -> {{ movespeedNL }}%", "{{ bonustrailad }} -> {{ bonustrailadNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [15, 20, 25, 30, 35], [65, 110, 155, 200, 245], [5, 5, 5, 5, 5], [20, 30, 40, 50, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "15/20/25/30/35", "65/110/155/200/245", "5", "20/30/40/50/60", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1125, 1125, 1125, 1125, 1125], "rangeBurn": "1125", "image": {"full": "NocturneDuskbringer.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NocturneShroudofDarkness", "name": "어둠의 장막", "description": "기본 지속 효과로 녹턴이 자신의 검을 강화하여 추가 공격 속도를 얻습니다. 스킬 사용 시 녹턴이 그림자 속으로 몸을 숨겨 적의 스킬을 한 번 막아주는 보호막을 생성합니다. 성공적으로 적의 스킬을 막았을 경우 추가 공격 속도가 2배로 올라갑니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 녹턴의 <attackSpeed>공격 속도가 {{ e1 }}%</attackSpeed> 상승합니다.<br /><br /><spellActive>사용 시:</spellActive> 녹턴이 1.5초 동안 그림자 장벽을 생성해 적의 다음 스킬을 방어합니다. 스킬을 막아내면 {{ e4 }}초 동안 이 스킬의 기본 지속 효과가 강화되어 <attackSpeed>공격 속도가 {{ e1 }}%</attackSpeed>까지 상승합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["추가 공격 속도", "재사용 대기시간"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [60, 70, 80, 90, 100], [0.3, 0.05, 0.05, 0.05, 0.05], [1.5, 1.5, 1.5, 1.5, 1.5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/70/80/90/100", "0.3/0.05/0.05/0.05/0.05", "1.5", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "NocturneShroudofDarkness.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NocturneUnspeakableHorror", "name": "말할 수 없는 공포", "description": "녹턴이 대상의 정신에 악몽을 심어 매초 피해를 가하며 지속시간이 끝날 때까지 스킬의 사거리 밖으로 벗어나지 않을 경우 공포 효과를 적용합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 녹턴이 <status>공포</status>에 빠진 적에게 접근할 때 <speed>이동 속도가 {{ tooltipfearms*100 }}%</speed> 증가합니다.<br /><br /><spellActive>사용 시:</spellActive> 녹턴이 대상과 연결되어 악몽을 꾸게 하고 {{ e3 }}초 동안 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 연결이 끊어지지 않으면 대상이 {{ e2 }}초 동안 <status>공포</status>에 빠집니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "공포 지속시간", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [1.25, 1.5, 1.75, 2, 2.25], [2, 2, 2, 2, 2], [465, 465, 465, 465, 465], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "1.25/1.5/1.75/2/2.25", "2", "465", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "NocturneUnspeakableHorror.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NocturneParanoia", "name": "피해망상", "description": "녹턴이 모든 적 챔피언의 시야 반경을 줄이고 적 챔피언들 사이의 시야 공유를 중단합니다. 스킬 사용 후 적 챔피언을 대상으로 설정할 경우 그 챔피언에게 날아가 공격합니다.", "tooltip": "녹턴이 전장을 어둠으로 뒤덮어 {{ paranoiaduration }}초 동안 모든 적 챔피언의 시야 반경을 줄이고 시야 공유를 차단합니다. 지속시간 중에 스킬을 <recast>재사용</recast>하면 적 챔피언에게 돌격해 <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "사거리", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [2500, 3250, 4000], "rangeBurn": "2500/3250/4000", "image": {"full": "NocturneParanoia.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "그림자 칼날", "description": "몇 초마다 녹턴의 기본 공격이 주변 적에게 추가 물리 피해를 입히고 녹턴의 체력을 회복시킵니다. <br><br>녹턴이 기본 공격 시 이 효과의 재사용 대기시간이 감소합니다.", "image": {"full": "Nocturne_UmbraBlades.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}