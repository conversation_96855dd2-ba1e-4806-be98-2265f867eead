{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Quinn": {"id": "<PERSON>", "key": "133", "name": "<PERSON>", "title": "le ali di Demacia", "image": {"full": "Quinn.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "133000", "num": 0, "name": "default", "chromas": false}, {"id": "133001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "133002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "133003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "133004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "133005", "num": 5, "name": "Quinn Inquisitrice", "chromas": true}, {"id": "133014", "num": 14, "name": "<PERSON>", "chromas": true}], "lore": "Quinn è un cavaliere ranger dell'élite demaciana, che affronta pericolose missioni dietro le linee nemiche. Condivide un legame indivisibile e letale con Valor, la sua leggendaria aquila, e i loro nemici spesso periscono prima di realizzare che non stanno affrontando uno, bensì due dei più grandi eroi del regno. Agile e acrobatica quando serve, Quinn prende la mira con la sua balestra mentre Valor aggancia i bersagli dall'alto. Sono una coppia letale in battaglia.", "blurb": "Quinn è un cavaliere ranger dell'élite demaciana, che affronta pericolose missioni dietro le linee nemiche. Condivide un legame indivisibile e letale con Valor, la sua leggendaria aquila, e i loro nemici spesso periscono prima di realizzare che non...", "allytips": ["Se si attacca un bersaglio ", "vulnerabile", " <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> in ricarica.  Se i marchi di Rapace s<PERSON>, potrai rigenerarli velocemente.", "Colpo acrobatico è potente, ma deve essere usato con cautela, perché i nemici possono colpire Quinn mentre li colpisce. Colpo acrobatico può essere usato anche per attraversare un terreno quando si hanno le spalle al muro.", "Usa Dietro le linee nemiche per coprire rapidamente lunghe distanze, per finire i minion o inseguire i bersagli feriti."], "enemytips": ["Dopo essere stato marchiato, all<PERSON><PERSON><PERSON> da <PERSON> in modo da impedirle di sfruttare il vantaggio.", "Tieni d'occhio la posizione di Quinn. Dietro le linee nemiche permette a Valor di attraversare la mappa rapidamente per prenderti di sorpresa.", "Se Quinn sta usando Dietro le linee nemiche e subisce danno, la sua velocità di movimento bonus viene temporaneamente rimossa."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 565, "hpperlevel": 107, "mp": 269, "mpperlevel": 35, "movespeed": 330, "armor": 28, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.7, "attackspeedperlevel": 3.1, "attackspeed": 0.668}, "spells": [{"id": "QuinnQ", "name": "Assalto accecante", "description": "Quinn chiama Valor affinché marchi un nemico e ostacoli la sua visione, per poi danneggiare tutti i nemici nell'area circostante.", "tooltip": "<PERSON> la<PERSON> in picchiata, rendendo <keywordMajor>vulnerabile</keywordMajor> il primo nemico colpito, riducendo il suo raggio di visione per {{ e3 }} secondi e infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> a tutti i nemici circostanti.<br /><br />Se il primo nemico colpito non è un campione, viene <status>disarmato</status> per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico totale", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio }} -> {{ adratio<PERSON> }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [20, 40, 60, 80, 100], [-1000, -1000, -1000, -1000, -1000], [1.75, 1.75, 1.75, 1.75, 1.75], [0.8, 0.9, 1, 1.1, 1.2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/40/60/80/100", "-1000", "1.75", "0.8/0.9/1/1.1/1.2", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1025, 1025, 1025, 1025, 1025], "rangeBurn": "1025", "image": {"full": "QuinnQ.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QuinnW", "name": "Sensi affinati", "description": "Conferisce passivamente velocità d'attacco e di movimento a Quinn dopo aver attaccato un nemico <font color='#FFF673'>vulnerabile</font>. Attiva per far svelare a Valor un'ampia area vicina.", "tooltip": "<spellPassive>Passiva:</spellPassive> attaccare un bersaglio <keywordMajor>vulnerabile</keywordMajor> aumenta la <attackSpeed>velocità d'attacco</attackSpeed> di <PERSON> di un {{ attackspeedbonus*100 }}% e la sua <speed>velocità di movimento</speed> di un {{ e3 }}% per {{ e1 }} secondi.<br /><br /><spellActive>Attiva:</spellActive> Valor svela una grande area vicina per {{ e5 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Velocità di movimento", "Ricarica"], "effect": ["{{ attackspeedbonus*100.000000 }}% -> {{ attackspeedbonusnl*100.000000 }}%", "{{ effect3amount*100.000000 }}% -> {{ effect3amountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [50, 45, 40, 35, 30], "cooldownBurn": "50/45/40/35/30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [20, 25, 30, 35, 40], [2100, 2100, 2100, 2100, 2100], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "0", "20/25/30/35/40", "2100", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [2100, 2100, 2100, 2100, 2100], "rangeBurn": "2100", "image": {"full": "QuinnW.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "QuinnE", "name": "Colpo acrobatico", "description": "Quinn salta addosso a un nemico, infliggendogli danni fisici e rallentandone la velocità di movimento. Quando raggiunge il bersaglio, se ne stacca con un balzo, interrompendolo momentaneamente, e atterra a una distanza prossima alla sua portata massima dal bersaglio.", "tooltip": "<PERSON> scatta verso un nemico, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e marchian<PERSON><PERSON> come <keywordMajor>vulnerabile</keywordMajor>. <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> salta all'indietro, <status>respingendo</status> leggermente il bersaglio e <status>rallentandolo</status> del {{ e1 }}%, effetto che decade nel corso di {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 50, 50, 50, 50], [40, 65, 90, 115, 140], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50", "40/65/90/115/140", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "QuinnE.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QuinnR", "name": "<PERSON><PERSON> le linee nemiche", "description": "<PERSON><PERSON>me, <PERSON> e Valor spiccano il volo a grande velocità. Concludere l'abilità lancia Attacco dal cielo, che infligge danni ai nemici nelle vicinanze e marchia i campioni come vulnerabili.", "tooltip": "Quinn chiama Valor canalizzando per 2 secondi, ottenendo <speed>{{ movementspeedmod*100 }}% velocità di movimento</speed> con la possibilità di <recast>rilanciare</recast> questa abilità. Attaccare o usare <spellName>Assalto accecante</spellName> o <spellName>Colpo acrobatico</spellName> <recast>rilancia</recast> automaticamente questa abilità.<br /><br /><recast>Rilancio</recast>: <PERSON> e Valor eseguono una manovra aerea, infliggendo <physicalDamage>{{ damage }} danni fisici</physicalDamage>, marchian<PERSON> i campioni come <keywordMajor>vulnerabili</keywordMajor> e terminando questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Costo in mana"], "effect": ["{{ movementspeedmod*100.000000 }}% -> {{ movementspeedmodnl*100.000000 }}%", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 3, "cooldown": [3, 3, 3], "cooldownBurn": "3", "cost": [100, 50, 0], "costBurn": "100/50/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "QuinnR.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "<PERSON><PERSON>, l'aquila demaciana di <PERSON>, marchia periodicamente i nemici come <font color='#FFF673'>vulnerabili</font>. Il primo attacco base di Quinn contro un nemico <font color='#FFF673'>vulnerabile</font> infligge danni fisici bonus.", "image": {"full": "Quinn_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}