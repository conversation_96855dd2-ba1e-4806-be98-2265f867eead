{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Quinn": {"id": "<PERSON>", "key": "133", "name": "<PERSON>", "title": "<PERSON><PERSON><PERSON>'s Wings", "image": {"full": "Quinn.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "133000", "num": 0, "name": "default", "chromas": false}, {"id": "133001", "num": 1, "name": "<PERSON> Quinn", "chromas": false}, {"id": "133002", "num": 2, "name": "Woad <PERSON> Quinn", "chromas": false}, {"id": "133003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "133004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "133005", "num": 5, "name": "<PERSON>", "chromas": true}, {"id": "133014", "num": 14, "name": "Star Guardian Quinn", "chromas": true}], "lore": "<PERSON> is an elite ranger-knight of Demacia, who undertakes dangerous missions deep in enemy territory. She and her legendary eagle, <PERSON><PERSON>, share an unbreakable bond, and their foes are often slain before they realize they are fighting not one, but two of the kingdom's greatest heroes. Nimble and acrobatic when required, <PERSON> takes aim with her crossbow while <PERSON><PERSON> marks their elusive targets from above, making them a deadly pair on the battlefield.", "blurb": "<PERSON> is an elite ranger-knight of Demacia, who undertakes dangerous missions deep in enemy territory. She and her legendary eagle, <PERSON><PERSON>, share an unbreakable bond, and their foes are often slain before they realize they are fighting not one, but two...", "allytips": ["Attacking a ", "Vulnerable", " target marked by <PERSON><PERSON> will put Harrier on cooldown. Quickly consuming Harrier marks will allow you to generate them faster.", "<PERSON><PERSON> is powerful but must be used with caution, as enemies can attack <PERSON> when she strikes them. Vault can sometimes be used to cross terrain if your back is to a wall.", "Use Behind Enemy Lines for traveling very long distances quickly, for farming minions around the map, or for chasing wounded targets."], "enemytips": ["After being marked, move away from <PERSON> so she can't take advantage of it.", "Keep tabs on <PERSON>'s position. Behind Enemy Lines can enable <PERSON><PERSON> to cross the map very quickly and attack you by surprise.", "When <PERSON> is using Behind Enemy Lines, damaging her removes her Move Speed bonus temporarily."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 565, "hpperlevel": 107, "mp": 269, "mpperlevel": 35, "movespeed": 330, "armor": 28, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.7, "attackspeedperlevel": 3.1, "attackspeed": 0.668}, "spells": [{"id": "QuinnQ", "name": "Blinding Assault", "description": "<PERSON> calls <PERSON><PERSON> to mark an enemy and hinder its vision before damaging all enemies in the immediate area.", "tooltip": "<PERSON> orders <PERSON><PERSON> to dive, making the first enemy hit <keywordMajor>Vulnerable</keywordMajor>, reducing their vision radius for {{ e3 }} seconds and dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> to all surrounding enemies.<br /><br />If the first enemy struck is not a champion, it is <status>Disarmed</status> for {{ e3 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Total AD Ratio", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio }} -> {{ adratio<PERSON> }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [20, 40, 60, 80, 100], [-1000, -1000, -1000, -1000, -1000], [1.75, 1.75, 1.75, 1.75, 1.75], [0.8, 0.9, 1, 1.1, 1.2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/40/60/80/100", "-1000", "1.75", "0.8/0.9/1/1.1/1.2", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1025, 1025, 1025, 1025, 1025], "rangeBurn": "1025", "image": {"full": "QuinnQ.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QuinnW", "name": "Heightened Senses", "description": "Passively grants <PERSON> Attack Speed and Move Speed after she attacks a <font color='#FFF673'>Vulnerable</font> target. Activate to have <PERSON><PERSON> reveal a large area nearby.", "tooltip": "<spellPassive>Passive:</spellPassive> Attacking a <keywordMajor>Vulnerable</keywordMajor> target grants Quinn <attackSpeed>{{ attackspeedbonus*100 }}% Attack Speed</attackSpeed> and <speed>{{ e3 }}% Move Speed</speed> for {{ e1 }} seconds.<br /><br /><spellActive>Active:</spellActive> Valor reveals a large area nearby for {{ e5 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Speed", "Move Speed", "Cooldown"], "effect": ["{{ attackspeedbonus*100.000000 }}% -> {{ attackspeedbonusnl*100.000000 }}%", "{{ effect3amount*100.000000 }}% -> {{ effect3amountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [50, 45, 40, 35, 30], "cooldownBurn": "50/45/40/35/30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [20, 25, 30, 35, 40], [2100, 2100, 2100, 2100, 2100], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "0", "20/25/30/35/40", "2100", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [2100, 2100, 2100, 2100, 2100], "rangeBurn": "2100", "image": {"full": "QuinnW.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "QuinnE", "name": "<PERSON><PERSON>", "description": "<PERSON> dashes to an enemy, dealing physical damage and slowing the target's Move Speed. Upon reaching the target, she leaps off the target, briefly interrupting it, and lands near her maximum Attack Range away from the target.", "tooltip": "<PERSON> dashes to an enemy, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> and marking them <keywordMajor>Vulnerable</keywordMajor>. <PERSON> then leaps backwards from the target, slightly <status>Knocking</status> them <status>Back</status> and <status>Slowing</status> them by {{ e1 }}%, decaying over {{ e3 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 50, 50, 50, 50], [40, 65, 90, 115, 140], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50", "40/65/90/115/140", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "QuinnE.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QuinnR", "name": "Behind Enemy Lines", "description": "<PERSON> and <PERSON><PERSON> team up to fly around at great speed.  Ending the ability casts Skystrik<PERSON>, which deals damage to nearby enemies and marks champions as Vulnerable.", "tooltip": "<PERSON> calls to <PERSON><PERSON>, pairing up after channeling for 2 seconds gaining <speed>{{ movementspeedmod*100 }}% Move Speed</speed> and allowing her to <recast>Recast</recast> this Ability. Attacking or using <spellName>Blinding Assault</spellName> or <spellName>Vault</spellName> automatically <recast>Recasts</recast> this Ability.<br /><br /><recast>Recast</recast>: <PERSON> and <PERSON><PERSON> perform an aerial maneuver, dealing <physicalDamage>{{ damage }} physical damage</physicalDamage>, marking champions as <keywordMajor>Vulnerable</keywordMajor>, and ending this Ability.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Move Speed", "<PERSON><PERSON>"], "effect": ["{{ movementspeedmod*100.000000 }}% -> {{ movementspeedmodnl*100.000000 }}%", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 3, "cooldown": [3, 3, 3], "cooldownBurn": "3", "cost": [100, 50, 0], "costBurn": "100/50/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "QuinnR.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Harrier", "description": "<PERSON><PERSON>, <PERSON>'s De<PERSON><PERSON> eagle, periodically marks enemies as <font color='#FFF673'>Vulnerable</font>. <PERSON>'s first basic attack against <font color='#FFF673'>Vulnerable</font> targets will deal bonus physical damage.", "image": {"full": "Quinn_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}