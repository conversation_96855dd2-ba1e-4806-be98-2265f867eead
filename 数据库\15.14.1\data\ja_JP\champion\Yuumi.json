{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "ユーミ", "title": "マジカルキャット", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "バトルプリンシパル ユーミ", "chromas": true}, {"id": "350011", "num": 11, "name": "愛の狩人ユーミ", "chromas": true}, {"id": "350019", "num": 19, "name": "ユービィー", "chromas": true}, {"id": "350028", "num": 28, "name": "魅惑の魔女ユーミ", "chromas": true}, {"id": "350037", "num": 37, "name": "EDG ユーミ", "chromas": true}, {"id": "350039", "num": 39, "name": "シバイヌーミ", "chromas": true}, {"id": "350049", "num": 49, "name": "サイバーキャット ユーミ", "chromas": true}, {"id": "350050", "num": 50, "name": "プレステージ サイバーキャット ユーミ", "chromas": false}, {"id": "350061", "num": 61, "name": "混沌の闇ユーミ", "chromas": true}], "lore": "バンドルシティからやってきた魔法ネコのユーミは、かつてはノラという名のヨードル魔女の使い魔だった。ノラが謎の失踪を遂げたことで、ユーミはノラが所有していた意識を持つ本、「境界の書」の守り手となり、そのページのポータルを通って旅をしながら飼い主を探している。ノラの愛情を懐かしむユーミは、旅の連れとなる仲間を見つけては、光の盾と固い決意で彼らを守るのだった。ブックはユーミが脇道にそれないよう注意しているが、ユーミはすぐに昼寝やら魚やらの楽しそうなことに気をとられてしまう。だがそんなユーミも、最後はいつもきちんと仲間を探す旅に戻ってくるのだ。 ", "blurb": "バンドルシティからやってきた魔法ネコのユーミは、かつてはノラという名のヨードル魔女の使い魔だった。ノラが謎の失踪を遂げたことで、ユーミはノラが所有していた意識を持つ本、「境界の書」の守り手となり、そのページのポータルを通って旅をしながら飼い主を探している。ノラの愛情を懐かしむユーミは、旅の連れとなる仲間を見つけては、光の盾と固い決意で彼らを守るのだった。ブックはユーミが脇道にそれないよう注意しているが、ユーミはすぐに昼寝やら魚やらの楽しそうなことに気をとられてしまう。だがそんなユーミも、最後はいつもき...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "きまぐれミサイル", "description": "ミサイルを発射し、最初に当たった対象にダメージとスロウ効果を与える。発射後1.35秒以上経過すると、与えるダメージとスロウ効果が強化される。「ベストフレンド」にくっついている間は、ミサイルのスロウ効果が常に強化され、味方は通常攻撃時効果で追加ダメージを与えるようになる。<br><br>くっついている間は少しの間だけミサイルをマウスカーソルで操作できる。", "tooltip": "気まぐれなミサイルを召喚し、最初に当たった敵に<magicDamage>{{ totalmissiledamage }}の魔法ダメージ</magicDamage>と{{ slowamount }}%の<status>スロウ効果</status>を与える。<br /><br /><keywordMajor>くっついて</keywordMajor>いる間に発動すると、少しの間だけミサイルをマウスで操作することができる。その後、ミサイルは加速して真っすぐ飛んでいく。加速したミサイルは、代わりに<magicDamage>{{ totalmissiledamageempowered }}の魔法ダメージ</magicDamage>を与え、対象に{{ empoweredslowduration }}秒間{{ empoweredslowamount }}%の<status>スロウ効果</status>を付与する。<br /><br /><keywordMajor>「ベストフレンド」ボーナス:</keywordMajor> <spellName>「きまぐれミサイル」</spellName>の<status>スロウ効果</status>が常に強化され、ミサイルが敵チャンピオンに命中すると{{ buffduration }}秒間、「ベストフレンド」が<OnHit>通常攻撃時効果%i:OnHit%</OnHit>で<magicDamage>{{ onhitdamagecalc }}の魔法ダメージ</magicDamage>を与える。<br /><br /><rules>通常攻撃時効果の追加ダメージは味方のクリティカル率に応じて{{ allycritchancemaxamp*100 }}%増加する。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["マナコスト", "基本ダメージ", "強化時のダメージ", "強化時のスロウ効果", "通常攻撃時ダメージ"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "{{ empoweredslowamount }}% -> {{ empoweredslowamountNL }}%", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "YuumiW", "name": "ユー＆ミー！", "description": "対象の味方までダッシュして、タワー以外のすべてから対象指定不可状態になる。「ベストフレンド」にくっついている間は自身の体力回復/シールド効果が増加し、味方は通常攻撃時効果で体力が回復する。", "tooltip": "<spellPassive>自動効果:</spellPassive> <keywordMajor>「ベストフレンド」</keywordMajor>にくっついている間は、<keywordMajor>自身の体力回復/シールド効果が{{ healandshieldpower*100 }}%</keywordMajor>増加し、その味方が<OnHit>通常攻撃時効果%i:OnHit%</OnHit>で<healing>{{ healthonhit }}の体力</healing>を回復する。<br /><br /><spellActive>発動効果:</spellActive> 味方のチャンピオンのところまでダッシュして<keywordMajor>くっつく</keywordMajor>。<keywordMajor>くっついて</keywordMajor>いる間はパートナーの動きに合わせて付いていき、タワー以外からは対象指定不可になる。<br /><br />自身が<status>移動不能効果</status>を受けると、このスキルは{{ ccattachlockout }}秒間のクールダウンに入る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["通常攻撃時効果の体力回復量", "体力回復/シールド効果の増加量"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "YuumiE", "name": "バビューン！", "description": "シールドを獲得して、移動速度と攻撃速度が増加する。くっついている場合は、自身ではなく味方にこの効果を与える。<br>", "tooltip": "シールドを獲得して<shield>{{ totalshielding }}のダメージ</shield>をブロックし、{{ msduration }}秒間、<attackSpeed>攻撃速度が{{ totalattackspeed }}%</attackSpeed>増加する。シールドが持続している間は、対象も<speed>移動速度が{{ msamount }}%</speed>増加する。<br /><br />味方に<keywordMajor>くっついて</keywordMajor>いる場合、このスキルの効果は自身ではなくその味方に付与され、さらに対象の<magicDamage>マナを{{ manarestore }}</magicDamage>回復する。このマナ回復量は対象の減少マナに応じて最大{{ maxmanapercincrease*100 }}%増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "マナコスト", "マナ回復", "攻撃速度"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "YuumiR", "name": "ファイナルチャプター", "description": "詠唱してウェーブを5回発射する。ウェーブは対象が敵の場合はダメージを与えて、味方の場合は体力を回復する。詠唱中も移動と「バビューン！」の発動が可能で、味方にくっつくこともできる。「ベストフレンド」にくっついている間は、このスキルの発射方向をマウスで操作できる。", "tooltip": "{{ ultduration }}秒間詠唱して魔法のウェーブを{{ numberofwaves }}回発射する。このウェーブは両チームに影響を与える。<keywordMajor>くっついている</keywordMajor>間に初めて発動した時、マウスでウェーブの発射方向を操作できる。<br /><br />敵に命中すると<magicDamage>{{ totalmissiledamage }}の魔法ダメージ</magicDamage>を与え、{{ ccduration }}秒間{{ baseslow*-100 }}%の<status>スロウ効果</status>を付与する。命中したウェーブの数ごとにスロウ効果が{{ bonusslowperwave*-100 }}%増加する。<br /><br />味方チャンピオンはウェーブごとに<healing>{{ totalhealperwave }}の体力</healing>を回復する。余剰の回復量は<shield>シールド</shield>に変換される。<br /><br /><keywordMajor>「ベストフレンド」ボーナス: </keywordMajor><keywordMajor>「ベストフレンド」</keywordMajor>は、回復量が<healing>{{ enhancedhealperwave }}の体力</healing>まで増加する。<br /><br /><rules><spellName>「ユー＆ミー！」</spellName>を詠唱すると、ウェーブの進行方向が現在の方向で固定される。<br />詠唱中も移動したり、<spellName>「バビューン！」</spellName>を使用できる。</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ミサイルごとの基本ダメージ", "ウェーブごとの基本体力回復量"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ネコはトモダチ", "description": "一定時間ごとに、通常攻撃またはスキルをチャンピオンに命中させると、自身および次にくっつく味方の体力が回復する。<br><br>くっついている間は、その味方との間に特別な絆が生まれる。絆が最も強い味方にくっついている間は、自身のスキルが強化される。", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}