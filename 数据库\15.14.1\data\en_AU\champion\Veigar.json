{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Veigar": {"id": "<PERSON><PERSON><PERSON>", "key": "45", "name": "<PERSON><PERSON><PERSON>", "title": "the Tiny Master of Evil", "image": {"full": "Veigar.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "45000", "num": 0, "name": "default", "chromas": false}, {"id": "45001", "num": 1, "name": "White Mage Veigar", "chromas": false}, {"id": "45002", "num": 2, "name": "Curling Veigar", "chromas": false}, {"id": "45003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "45006", "num": 6, "name": "Superb <PERSON><PERSON>", "chromas": false}, {"id": "45007", "num": 7, "name": "Bad Santa Veigar", "chromas": false}, {"id": "45008", "num": 8, "name": "Final Boss <PERSON>", "chromas": true}, {"id": "45009", "num": 9, "name": "Omega Squad Veigar", "chromas": true}, {"id": "45013", "num": 13, "name": "<PERSON><PERSON> Veigar", "chromas": true}, {"id": "45023", "num": 23, "name": "Furyhorn Cosplay Veigar", "chromas": true}, {"id": "45032", "num": 32, "name": "Astronaut <PERSON>", "chromas": true}, {"id": "45041", "num": 41, "name": "<PERSON> Tam<PERSON>", "chromas": true}, {"id": "45051", "num": 51, "name": "<PERSON>", "chromas": true}, {"id": "45060", "num": 60, "name": "Fright Night Veigar", "chromas": true}], "lore": "An enthusiastic master of dark sorcery, <PERSON><PERSON><PERSON> has embraced powers that few mortals dare approach. As a free-spirited inhabitant of Bandle City, he longed to push beyond the limitations of yordle magic, and turned instead to arcane texts that had been hidden away for thousands of years. Now a stubborn creature with an endless fascination for the mysteries of the universe, <PERSON><PERSON><PERSON> is often underestimated by others—but even though he believes himself truly evil, he possesses an inner morality that leads some to question his deeper motivations.", "blurb": "An enthusiastic master of dark sorcery, <PERSON><PERSON><PERSON> has embraced powers that few mortals dare approach. As a free-spirited inhabitant of Bandle City, he longed to push beyond the limitations of yordle magic, and turned instead to arcane texts that had been...", "allytips": ["Use Event Horizon to increase your chances of landing Dark Matter.", "Veigar is extremely Mana and Cooldown Reduction dependent. Try buying items with these stats in order to increase the effectiveness of your passive and Baleful Strike.", "Veigar is very fragile. It is valuable to select at least one summoner spell that can be used defensively."], "enemytips": ["Dark Matter deals very high damage, but it can be avoided. Pay attention to the sound and visual indicator to be aware of when and where the spell will land.", "Event Horizon only stuns units on the edge. If you're inside the spell, you can still move and attack.", "Veigar's ultimate deals increased damage based on your missing health."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 580, "hpperlevel": 108, "mp": 490, "mpperlevel": 26, "movespeed": 340, "armor": 18, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.24, "attackspeed": 0.625}, "spells": [{"id": "VeigarBalefulStrike", "name": "Baleful Strike", "description": "<PERSON><PERSON><PERSON> unleashes a bolt of dark energy that deals magic damage to the first two enemies hit. Units killed by this bolt grant <PERSON><PERSON><PERSON> some ability power permanently.", "tooltip": "<PERSON><PERSON><PERSON> unleashes a bolt of dark energy, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to the first two enemies hit.<br /><br />If <PERSON><PERSON><PERSON> kills an enemy with this Ability, he gains {{ spell.veigarpassive:dqkillstacks }} stack of <keywordMajor>Phenomenal Evil</keywordMajor>. Large minions and large monsters grant {{ spell.veigarpassive:dqkillstackslarge }} stacks instead.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Total AP Ratio", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "VeigarBalefulStrike.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VeigarDarkMatter", "name": "Dark Matter", "description": "<PERSON><PERSON><PERSON> calls a great mass of dark matter to fall from the sky to the target location, dealing magic damage when it lands. Stacks of Phenomenal Evil reduce Dark Matter's cooldown.", "tooltip": "Veigar summons dark matter from the sky, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Every {{ spell.veigarpassive:pstacksperdarkmattercdr }} stacks of <keywordMajor>Phenomenal Evil</keywordMajor> reduce this Ability's Cooldown by {{ spell.veigarpassive:darkmattercdrincrement*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Total AP Ratio", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1.2, 1.2, 1.2, 1.2, 1.2], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1.2", "8", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "VeigarDarkMatter.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VeigarEventHorizon", "name": "Event Horizon", "description": "<PERSON><PERSON><PERSON> twists the edges of space, creating a cage that Stuns enemies that pass through.", "tooltip": "<PERSON><PERSON><PERSON> twists the edges of space, creating a cage that <status>Stuns</status> enemies that pass through for {{ e1 }} seconds. The cage lasts for 3 seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Stun Duration:", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18.5, 17, 15.5, 14], "cooldownBurn": "20/18.5/17/15.5/14", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [1.5, 1.75, 2, 2.25, 2.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5/1.75/2/2.25/2.5", "0.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "VeigarEventHorizon.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VeigarR", "name": "Primordial Burst", "description": "Blasts target enemy champion, dealing a large amount of magic damage, increasing based on the target's missing health.", "tooltip": "Veigar blasts an enemy champion with primal magic to deal between <magicDamage>{{ mindamage }} and {{ maxdamage }} magic damage</magicDamage>, increasing with the target's missing Health. Damage is maximized against enemies below 33% Health.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Total AP Ratio", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "VeigarR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Phenomenal Evil Power", "description": "<PERSON><PERSON><PERSON> is the greatest Evil to ever strike at the hearts of Runeterra - and he's only getting bigger! Striking an enemy Champion with a spell or scoring a takedown grants <PERSON><PERSON><PERSON> permanently increased Ability Power.", "image": {"full": "VeigarEntropy.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}