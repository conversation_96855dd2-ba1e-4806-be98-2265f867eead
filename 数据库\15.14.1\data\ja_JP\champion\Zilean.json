{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "ジリアン", "title": "時の番人", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "サンタクロース ジリアン", "chromas": false}, {"id": "26002", "num": 2, "name": "愛と平和のジリアン", "chromas": false}, {"id": "26003", "num": 3, "name": "シュリーマ砂漠ジリアン", "chromas": false}, {"id": "26004", "num": 4, "name": "タイムマシン ジリアン", "chromas": false}, {"id": "26005", "num": 5, "name": "ブラッドムーン ジリアン", "chromas": false}, {"id": "26006", "num": 6, "name": "シュガーラッシュ ジリアン", "chromas": true}, {"id": "26014", "num": 14, "name": "冬の祝福ジリアン", "chromas": true}], "lore": "ジリアンはかつてはイカシアの強力なメイジだったが、故郷がヴォイドに破壊されるのを目撃して、時の流れに執着するようになった。壊滅的な喪失を嘆く暇すら与えられなかった彼は、未来のあらゆる可能性を予言しようと古代の時空魔法を使った。実質的に不死身となったジリアンは過去と現在と未来の狭間を漂うようになり、自身の周囲の時間の流れを捻じ曲げながら、時計を巻き戻して壊滅したイカシアを元に戻す方法を探し続けている。", "blurb": "ジリアンはかつてはイカシアの強力なメイジだったが、故郷がヴォイドに破壊されるのを目撃して、時の流れに執着するようになった。壊滅的な喪失を嘆く暇すら与えられなかった彼は、未来のあらゆる可能性を予言しようと古代の時空魔法を使った。実質的に不死身となったジリアンは過去と現在と未来の狭間を漂うようになり、自身の周囲の時間の流れを捻じ曲げながら、時計を巻き戻して壊滅したイカシアを元に戻す方法を探し続けている。", "allytips": ["「タイムボム」をセット後、続けて「リワインド」を発動すると、同一の対象に素早く2個目の「タイムボム」をセットできる。2個目をセットした場合、1個目の「タイムボム」は即座に爆発して周辺の敵ユニットにスタン効果を付与する。", "「タイムワープ」は味方の移動速度を増加させるので、敵にとどめを刺したり、逆にピンチからの脱出にも使える。", "キャリーチャンピオンを敵の攻撃から守りたい時は「クロノシフト」が絶大な抑止力を発揮する。ただし、発動のタイミングが早すぎると対象をあっさりと切り替えられてしまうので注意しよう。"], "enemytips": ["ジリアンのスピードに対抗できるのであれば、アルティメットスキルの効果が消えるのを待ってから止めをさそう。無理な追撃は危険を伴う。", "ジリアンは大勢から集中攻撃を受けると脆いが、そうでもなければ倒すのが非常に困難だ。チーム一丸となって畳みかけよう。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "タイムボム", "description": "指定地点に爆弾を投げ、近付いたユニットに付着させる(チャンピオン優先)。付着した爆弾は3秒後に爆発し、範囲ダメージを与える。爆発前にもう一つ「タイムボム」を仕掛けられると即時に爆発し、敵にスタン効果を与える。", "tooltip": "時限式の爆弾を投げる。爆弾は範囲の中心部に入った最初のユニットに付着する。爆弾は{{ e2 }}秒後に爆発し、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />同じユニットに2個目の爆弾をつけると1個目の爆弾がすぐに爆発し、爆風範囲内にいる敵に{{ e4 }}秒間<status>スタン効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ", "スタン効果時間:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ZileanW", "name": "リワインド", "description": "ジリアンは近い未来の戦いに備え、通常スキルのクールダウンを短縮することができる。", "tooltip": "時間を進めて、他の通常スキルのクールダウンを{{ e2 }}秒短縮する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }}マナ"}, {"id": "TimeWarp", "name": "タイムワープ", "description": "対象ユニット周辺の時間軸を短時間ねじ曲げ、対象が敵の場合はスロウを与え、味方の場合は移動速度を増加させる。", "tooltip": "{{ e1 }}秒間、敵チャンピオンに{{ e2 }}%の<status>スロウ効果</status>を与えるか、味方チャンピオンの<speed>移動速度を{{ e2 }}%</speed>増加させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["スロウ効果", "移動速度"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ChronoShift", "name": "クロノシフト", "description": "対象の味方チャンピオンに砂時計の印を付与し、体力がゼロになった瞬間、過去へ遡らせて復活させる。", "tooltip": "味方チャンピオンに{{ rduration }}秒間、防護の砂時計の印を付与する。対象が倒されると砂時計が時間を巻き戻し、対象を{{ revivestateduration }}秒間、固有時停止状態にする。その後、対象を復活させて<healing>体力を{{ rtotalheal }}</healing>回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "体力回復量"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "タイムインボトル", "description": "時間を経験値として溜めて、味方に付与できる。味方のレベルを上げるのに必要な経験値が溜まっている状態で相手を右クリックすると、経験値を与えることができる。同時に、与えた量と同じ経験値を自身も獲得する。", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}