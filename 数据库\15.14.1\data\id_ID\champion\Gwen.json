{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gwen": {"id": "<PERSON>", "key": "887", "name": "<PERSON>", "title": "The Hallowed Seamstress", "image": {"full": "Gwen.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "887000", "num": 0, "name": "default", "chromas": false}, {"id": "887001", "num": 1, "name": "Space Groove Gwen", "chromas": true}, {"id": "887011", "num": 11, "name": "Cafe Cuties Gwen", "chromas": true}, {"id": "887020", "num": 20, "name": "Soul Fighter Gwen", "chromas": true}, {"id": "887030", "num": 30, "name": "Battle Queen Gwen", "chromas": true}], "lore": "<PERSON>, makhluk yang dulunya boneka ini bertransformasi dan diberi nyawa oleh sihir, lalu menguasai alat yang dulu menciptakannya. Dia membawa beban cinta penciptanya di tiap langkah, tanpa pernah mengecewakannya. Dia bisa mengendalikan Hallowed Mist, sihir kuno dan pelindung yang member<PERSON><PERSON> gunting, jarum, serta benang jahit <PERSON>. Begitu banyak hal baru baginya, tetapi Gwen tetap bertekad untuk berjuang demi sisa-sisa kebaikan di dunia yang telah hancur.", "blurb": "<PERSON>, makhluk yang dulunya boneka ini bertransformasi dan diberi nyawa oleh sihir, lalu menguasai alat yang dulu menciptakannya. Dia membawa beban cinta penciptanya di tiap lang<PERSON>h, tanpa pernah mengecewakannya. Dia bisa mengendalikan Hallowed Mist...", "allytips": ["<PERSON><PERSON><PERSON>. <PERSON><PERSON> member<PERSON>n bonus damage, Serangan Gwen memper<PERSON>at atau mereset banyak Ability-nya.", "Gwen masih bisa memberikan damage pada musuh di luar Hallowed Mist, teru<PERSON>a dengan jang<PERSON>.", "Beberapa Ability Gwen bisa menerapkan pasif ke beberapa musuh sekaligus, jadi bidik ke kelompok untuk memberikan damage dan mendapat heal maksimum."], "enemytips": ["Hallowed Mist Gwen hanya akan mengikuti dirinya se<PERSON>, set<PERSON>h itu akan hilang jika ia meninggalkan area tersebut.", "Gwen harus menyerang sesuatu untuk menggunakan Ultimanya lagi, jadi usahakan untuk menghindar di antara cast.", "<PERSON> membutuhkan beberapa serangan untuk menyiapkan damage-nya, jadi cobalah untuk mendahuluinya."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 4, "magic": 5, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 330, "mpperlevel": 40, "movespeed": 340, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.69}, "spells": [{"id": "GwenQ", "name": "Snip Snip!", "description": "Gwen menggunting dalam bentuk kerucut hingga 6 kali, men<PERSON><PERSON><PERSON>an magic damage. Gwen menghasilkan true damage pada unit di tengah dan menerapkan pasifnya ke mereka tiap kali menggunting.", "tooltip": "<spellPassive>Pasif</spellPassive>: Gwen mendapatkan 1 stack saat dia mengenai musuh dengan sebuah serangan (maksimal 4, selama {{ buffduration }} detik).<br /><br /><spellActive>Aktif</spellActive>: Mengonsumsi stack. Gwen menggunting 1 kali sebesar <magicDamage>{{ miniswipedamage }} magic damage</magicDamage>, menggunting lagi untuk tiap ammo yang dikonsumsi, dan menggunting untuk terakhir kalinya sebesar <magicDamage>{{ finalswipedamage }} magic damage</magicDamage>. <br /><br />Pusat tiap serangan mengubah {{ truedamageconversion*100 }}% damage menjadi <trueDamage>true damage</trueDamage> sebagai gantinya dan menerapkan <spellName>A Thousand Cuts</spellName> ke musuh yang kena.<br /><rules><br /><PERSON><PERSON><PERSON><PERSON><PERSON> {{ minionmod*100 }}% damage ke minion.<br />Minion dengan health di bawah {{ executethreshold*100 }}% menerima {{ executebonus }}% damage bonus, menggantikan pengurangan damage.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage Final Strike"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ swipedamagebase }}-> {{ swipedamagebaseNL }}"]}, "maxrank": 5, "cooldown": [6.5, 5.75, 5, 4.25, 3.5], "cooldownBurn": "6.5/5.75/5/4.25/3.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "GwenQ.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenW", "name": "Hallowed Mist", "description": "Gwen memanggil mist yang melindunginya dari musuh di luar itu. Dia hanya bisa ditarget oleh musuh yang memasuki mist.", "tooltip": "Gwen memanggil Hallowed Mist, membuatnya tak bisa ditarget oleh semua musuh (kecuali turret) di luar zona selama {{ zoneduration }} detik atau sampai dia meninggalkannya. Saat di dalam Mist, <PERSON> mendapatkan {{ totalresists }} <scaleArmor>Armor</scaleArmor> dan <scaleMR>Magic Resist</scaleMR>.<br /><br /><PERSON> dapat <recast>Recast</recast> Ability ini sekali lagi untuk memanggil Mist. Spell ini akan otomatis <recast>Recast</recast> saat pertama kali Gwen mencoba keluar dari zona.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GwenW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenE", "name": "Skip 'n Slash", "description": "<PERSON> melakukan dash jarak dekat, lalu mendapatkan Attack Speed, attack range, dan magic damage <OnHit>On-Hit</OnHit> selama beberapa detik. Jika dia mengenai musuh selama durasi tersebut, cooldown Ability ini akan di-refund. ", "tooltip": "Gwen melakukan dash dan memperkuat Serangan selama {{ buffduration }} detik.<br /><br />Serangan yang diperkuat mendapatkan <attackSpeed>{{ bonusattackspeed }} Attack Speed</attackSpeed>, <magicDamage>{{ onhitdamage }} magic damage</magicDamage> %i:OnHit% <OnHit>On-Hit</OnHit>, {{ bonusattackrange }} range, dan musuh pertama yang kena akan me-refund {{ cdrefund*100 }}% Cooldown Ability ini.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Attack Speed", "Damage On-Hit"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ baseattackspeed }}%-> {{ baseattackspeedNL }}%", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GwenE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenR", "name": "Needlework", "description": "Gwen melemparkan jarum yang menerapkan slow pada musuh yang kena, mengh<PERSON>lkan magic damage, dan menerapkan A Thousand Cuts kepada champion yang kena. <br><br>Ability ini bisa di-cast hingga dua kali lagi, masing-masing melemparkan jarum-jarum tambahan dan menghasilkan lebih banyak damage. ", "tooltip": "<spellActive>First Cast:</spellActive> Melemparkan jarum yang menghasilkan <magicDamage>{{ totaldamage }} magic damage</magicDamage>, menerapkan <status>Slow</status> sebesar {{ initialslow*-100 }}% selama {{ debuffduration }} detik, dan menerapkan <spellName>A Thousand Cuts</spellName> ke semua musuh yang kena. Gwen bisa <recast>Recast</recast> ability ini hingga 2 kali tambahan dalam 6 detik (cooldown {{ lockouttime }} dtk antar-cast).<br /><br /><recast>Second Cast:</recast> Menembakkan 3 jarum untuk menghasilkan <magicDamage>{{ totaldamage3 }} magic damage</magicDamage><br /><recast>Third Cast:</recast> Menembakkan 5 jarum untuk menghasilkan <magicDamage>{{ totaldamage5 }} magic damage</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "<PERSON><PERSON>"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "GwenR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "A Thousand Cuts", "description": "Serangan Gwen <PERSON><PERSON><PERSON><PERSON><PERSON> magic damage bonus pada health target. <PERSON><PERSON> men<PERSON><PERSON> heal untuk sebagian damage yang diberikan pada champion o<PERSON><PERSON> e<PERSON>k ini. ", "image": {"full": "Gwen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}