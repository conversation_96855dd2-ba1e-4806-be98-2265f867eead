{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Milio": {"id": "<PERSON><PERSON>", "key": "902", "name": "<PERSON><PERSON><PERSON>", "title": "やさしき炎", "image": {"full": "Milio.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "902000", "num": 0, "name": "default", "chromas": false}, {"id": "902001", "num": 1, "name": "妖精の王宮ミリオ", "chromas": true}, {"id": "902011", "num": 11, "name": "雨の導き手ミリオ", "chromas": true}], "lore": "イシュタル出身の心あたたかい少年。わずか12歳にして火のアクシオムを使いこなし、「癒やしの炎」という未知なる力を発見した。この新たな力を用い、ミリオはかつての祖母のように、ユン・タルの一員となることを目指す。それがかなえば、現在流謫の身にある故郷の家族を、正当な地位に引き上げることができるからだ。イシュタルのジャングルを抜け、首都イシャオカンまではるばる旅をしてきたミリオは、ユン・タルの座に就くため、ヴィダリオンの試練に備えて修練を積んでいる。その試練の内容も、それに伴う危険についても知らずに。", "blurb": "イシュタル出身の心あたたかい少年。わずか12歳にして火のアクシオムを使いこなし、「癒やしの炎」という未知なる力を発見した。この新たな力を用い、ミリオはかつての祖母のように、ユン・タルの一員となることを目指す。それがかなえば、現在流謫の身にある故郷の家族を、正当な地位に引き上げることができるからだ。イシュタルのジャングルを抜け、首都イシャオカンまではるばる旅をしてきたミリオは、ユン・タルの座に就くため、ヴィダリオンの試練に備えて修練を積んでいる。その試練の内容も、それに伴う危険についても知らずに。", "allytips": ["ミリオの能力を最大限に活かすには、味方に側にいてもらう必要がある。", "ミリオがダッシュするスピードは移動速度に応じて上昇する。増加したスピードを活かして敵を驚かせてやろう！", "危険は自らが招いてこその楽しみ。"], "enemytips": ["ミリオの移動スキルは到着地点が事前にわかる。これを活かして優位に立とう。", "素早く行動妨害を与えられるチャンピオンは、ミリオに対して効果的だ。", "周りに味方がいない時にミリオを捕まえれば、彼の機動力を大きく抑制できる。彼が1人の時を狙おう。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "MilioQ", "name": "ウルトラメガファイヤーキック", "description": "敵1体をノックバックさせるボールをキックする。ボールは敵に命中すると、跳ね上がってから対象に向かって落下し、着地時に範囲内の敵にダメージとスロウ効果を与える。", "tooltip": "炎のボールをキックして、最初に命中した敵を<status>ノックバック</status>させる。命中したボールは対象を跳び越えて爆発し、周囲の敵に<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowamountpercent }}の<status>スロウ効果</status>を付与する。<br /><br />「<spellName>ウルトラメガファイヤーキック</spellName>」が1体でも敵チャンピオンに命中すると、そのマナコストの{{ refundratio*100 }}%が回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "マナコスト"], "effect": ["{{ falldamage }} -> {{ falldamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "MilioQ.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MilioW", "name": "癒しの焚き火", "description": "範囲内の味方の体力を回復し、その射程距離を延長するゾーンを作り出す。このゾーンは発動地点から一番近い味方を追従する。", "tooltip": "{{ zoneduration }}秒間、味方チャンピオンを追いかける焚き火を作り出す。その近くにいる味方チャンピオンは射程距離が{{ rangepercent }}増加し、効果時間をかけて<healing>体力が{{ healingovertime }}</healing>回復する。また、焚き火は{{ healfrequencyseconds }}秒ごとに<spellName>「ファイヤーアップ！」</spellName>を適用する。<br /><br /><recast>再発動:</recast> 焚き火が追いかける味方を変更する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["射程", "体力回復量", "クールダウン", "マナコスト"], "effect": ["{{ rangepctincrease*100.000000 }}% -> {{ rangepctincreasenl*100.000000 }}%", "{{ totalhealingovertime }} -> {{ totalhealingovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [29, 27, 25, 23, 21], "cooldownBurn": "29/27/25/23/21", "cost": [90, 100, 110, 120, 130], "costBurn": "90/100/110/120/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "MilioW.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MilioE", "name": "抱擁のぬくもり", "description": "味方1体にシールドを付与し、一時的に対象の移動速度を上昇させる。このスキルは2回までチャージできる。", "tooltip": "味方チャンピオンを防御の炎で包み、{{ movespeedduration }}秒間、<shield>耐久値{{ shieldcalc }}のシールド</shield>を付与し、<speed>移動速度を{{ movespeedamount*100 }}%</speed>増加させる。<br /><br />このスキルは2回までチャージ可能で、同じ対象にその効果をスタックさせることができる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "移動速度", "マナコスト", "リチャージ時間"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MilioE.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MilioR", "name": "生命の息吹", "description": "穏やかな炎の波動を放ち、範囲内の味方の体力を回復して、行動妨害効果を除去する。", "tooltip": "穏やかな炎の波動を放ち、周囲の味方チャンピオンから<status>行動妨害効果</status>と<status>移動不能効果</status>を除去して、対象の<healing>体力を{{ healcalc }}</healing>回復し、{{ tenacityduration }}秒間{{ tenacityamount*100 }}%の行動妨害耐性を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "クールダウン"], "effect": ["{{ healbase }} -> {{ healbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 145, 130], "cooldownBurn": "160/145/130", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MilioR.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ファイヤーアップ！", "description": "ミリオのスキルに触れた味方は、次の攻撃で追加のバーストダメージを与え、対象を炎上させる。", "image": {"full": "Milio_P.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}