{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jayce": {"id": "<PERSON><PERSON>", "key": "126", "name": "<PERSON><PERSON>", "title": "the Defender of Tomorrow", "image": {"full": "Jayce.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "126000", "num": 0, "name": "default", "chromas": false}, {"id": "126001", "num": 1, "name": "Full Metal Jayce", "chromas": false}, {"id": "126002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "126003", "num": 3, "name": "Forsaken Jayce", "chromas": false}, {"id": "126004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "126005", "num": 5, "name": "Battle Academia Jayce", "chromas": true}, {"id": "126015", "num": 15, "name": "<PERSON>", "chromas": true}, {"id": "126024", "num": 24, "name": "<PERSON><PERSON> Inventor <PERSON>", "chromas": false}, {"id": "126025", "num": 25, "name": "Zenith Games Jayce", "chromas": true}, {"id": "126034", "num": 34, "name": "T1 Jayce", "chromas": true}, {"id": "126035", "num": 35, "name": "Arcane Survivor <PERSON>", "chromas": false}, {"id": "126036", "num": 36, "name": "Prestige T1 Jayce", "chromas": false}], "lore": "<PERSON><PERSON> is a brilliant inventor who, along with his friend <PERSON>, made the first great discoveries in the field of hextech. Celebrated across Piltover, he tries to live up to his reputation as \"the Man of Progress,\" but often struggles with the expectations placed upon him. Because of this, <PERSON><PERSON> has begun to see the ways in which his invention has furthered the division between Piltover and Zaun—and armed with his hextech hammer, he stands ready to defend tomorrow.", "blurb": "<PERSON><PERSON> is a brilliant inventor who, along with his friend <PERSON>, made the first great discoveries in the field of hextech. Celebrated across Piltover, he tries to live up to his reputation as \"the Man of Progress,\" but often struggles with the...", "allytips": ["Be sure to switch stances often. It will enhance your attacks and grant you quick bursts of speed.", "If you find yourself taking lots of damage, try using <PERSON><PERSON>'s <PERSON>, as it grants you additional defenses.", "For increased range and damage, try casting Shock Blast through the Acceleration Gate."], "enemytips": ["<PERSON><PERSON> can attack in melee or at range. Pay attention to his stance and weapon color to know how he is going to attack.", "If you see <PERSON><PERSON> drop his Acceleration Gate, be careful, he is probably about to cast Shock Blast.", "<PERSON><PERSON> is strong in the early game. If he gains the advantage, play defensively."], "tags": ["Fighter", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 7}, "stats": {"hp": 590, "hpperlevel": 109, "mp": 375, "mpperlevel": 45, "movespeed": 335, "armor": 22, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3, "attackspeed": 0.658}, "spells": [{"id": "JayceToTheSkies", "name": "To the Skies! / Shock Blast", "description": "Hammer Stance: Leaps to an enemy dealing physical damage and slowing enemies.<br><br><PERSON> Stance: Fires an orb of electricity that detonates upon hitting an enemy (or reaching the end of its path) dealing physical damage to all enemies hit.", "tooltip": "<keywordMajor><PERSON> Hammer:</keywordMajor> <PERSON><PERSON> leaps to an enemy, dealing <physicalDamage>{{ spell.jaycetotheskies:damage }} physical damage</physicalDamage> to surrounding enemies and <status>Slowing</status> them by {{ spell.jaycetotheskies:slow*-100 }}% for {{ spell.jaycetotheskies:slowduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "Slow"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%"]}, "maxrank": 6, "cooldown": [16, 14, 12, 10, 8, 6], "cooldownBurn": "16/14/12/10/8/6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JayceToTheSkies.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceStaticField", "name": "Lightning Field / Hyper Charge", "description": "Hammer Stance: Passive: <PERSON><PERSON> Mana per strike. Active: Creates a field of lightning damaging nearby enemies for several seconds.<br><br>Cannon Stance: Gains a burst of energy, increasing Attack Speed to maximum for several attacks.", "tooltip": "<keywordMajor><PERSON> Hammer - Passive:</keywordMajor> <PERSON><PERSON>'s <keywordMajor>Hammer</keywordMajor> Attacks grant <scaleMana>{{ spell.jaycestaticfield:managain }} Mana</scaleMana>.<br /><br /><keywordMajor><PERSON> Hammer - Active:</keywordMajor> <PERSON><PERSON> creates an electric aura dealing <magicDamage>{{ spell.jaycestaticfield:damage }} magic damage</magicDamage> over {{ spell.jaycestaticfield:duration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ managain }} -> {{ managainNL }}"]}, "maxrank": 6, "cooldown": [10, 10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [285, 285, 285, 285, 285, 285], "rangeBurn": "285", "image": {"full": "JayceStaticField.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceThunderingBlow", "name": "Thundering Blow / Acceleration Gate", "description": "Hammer Stance: Deals magic damage to an enemy and knocks them back a short distance.<br><br><PERSON> Stance: Deploys an Acceleration Gate increasing the Move Speed of all allied champions who pass through it. If Shock Blast is fired through the gate the missile speed, range, and damage will increase.", "tooltip": "<keywordMajor>Hammer Form</keywordMajor>: <PERSON><PERSON> swings his hammer, <status>Knocking Back</status> his target and dealing <magicDamage>{{ spell.jaycethunderingblow:flatdamage }} plus {{ spell.jaycethunderingblow:perchpdamage*100 }}% max Health magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Max Health %", "Cooldown", "Monster Damage Cap"], "effect": ["{{ perchpdamage*100.000000 }}% -> {{ perchpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ monstercap }} -> {{ monstercapNL }}"]}, "maxrank": 6, "cooldown": [20, 18, 16, 14, 12, 10], "cooldownBurn": "20/18/16/14/12/10", "cost": [55, 55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [240, 240, 240, 240, 240, 240], "rangeBurn": "240", "image": {"full": "JayceThunderingBlow.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceStanceHtG", "name": "Mercury Cannon / Mercury Hammer", "description": "Hammer Stance: Transforms the Mercury Hammer into the Mercury Cannon gaining new abilities and increased range. The first attack in this form reduces the target's Armor and Magic Resist.<br><br>Cannon Stance: Transforms the Mercury Cannon into the Mercury Hammer gaining new abilities and increasing Armor and Magic Resist. The first attack in this form deals additional magic damage.", "tooltip": "<keywordMajor><PERSON></keywordMajor>: <PERSON><PERSON> transforms his weapon into the <keywordMajor>Mercury Cannon</keywordMajor>, gaining Attack Range and new Abilities. <PERSON><PERSON>'s next Attack removes <scaleArmor>{{ spell.jaycestancehtg:rangedformshred }} Armor</scaleArmor> and <scaleMR>Magic Resist</scaleMR> for {{ spell.jaycestancehtg:shredduration }} seconds.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [6], "cooldownBurn": "6", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "JayceStanceHtG.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "No Cost"}], "passive": {"name": "Hextech Capacitor", "description": "When <PERSON><PERSON> swaps weapons he gains Move Speed for a short duration.", "image": {"full": "Jayce_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}