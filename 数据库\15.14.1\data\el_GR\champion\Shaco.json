{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shaco": {"id": "Shaco", "key": "35", "name": "Σάκο", "title": "ο Δαιμονικός Γελωτοποιός", "image": {"full": "Shaco.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "35000", "num": 0, "name": "default", "chromas": false}, {"id": "35001", "num": 1, "name": "Τρελο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>άκ<PERSON>", "chromas": false}, {"id": "35002", "num": 2, "name": "Αυτοκρατ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Σάκο", "chromas": false}, {"id": "35003", "num": 3, "name": "Σάκο ο Καρυοθραύστης", "chromas": false}, {"id": "35004", "num": 4, "name": "Σάκο <PERSON>ης", "chromas": false}, {"id": "35005", "num": 5, "name": "Σάκο ο Τρελός", "chromas": false}, {"id": "35006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "35007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "35008", "num": 8, "name": "Σάκο του Σκοτεινού Άστρου", "chromas": true}, {"id": "35015", "num": 15, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "35023", "num": 23, "name": "Ε<PERSON>ιάλτης στην Πόλη του Εγκλήματος <PERSON>", "chromas": true}, {"id": "35033", "num": 33, "name": "Ευλογημένος από τον Χειμώνα Σάκο", "chromas": true}, {"id": "35043", "num": 43, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "35044", "num": 44, "name": "Ψυχο<PERSON><PERSON><PERSON><PERSON> Σάκο - Έκδοση Κύρους", "chromas": false}, {"id": "35054", "num": 54, "name": "Σάκο της <PERSON>τας Τρόμου", "chromas": true}, {"id": "35064", "num": 64, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Η μαγεμένη μαριονέτα, ο <PERSON>άκ<PERSON>, είχε δημιουργηθεί πριν από πολλά χρόνια για να διασκεδάσει έναν μοναχικό πρίγκιπα, αλλ<PERSON> τώρα έχει μετατραπεί σε ένα δολοφονικό πλάσμα που λατρεύει τον θάνατο και το χάος. Έχοντας διαφθαρεί από μαύρη μαγεία και από την απώλεια του αγαπημένου κατόχου της, αυτή η άλλοτε καλοσυνάτη μαριονέτα βρίσκει ευχαρίστηση μόνο στη δυστυχία των άμοιρων ψυχών που βασανίζει. Χρησιμοποιεί παιχνίδια και ταχυδακτυλουργικά κόλπα ως φονικά όπλα και ξεκαρδίζεται στα γέλια όταν βλέπει τα αποτελέσματα των αιματοβαμμένων του «σκανταλιών» και, όποιος ακούει ένα μοχθηρό χαχανητό στα μαύρα μεσάνυχτα, ξέρει ότι ίσως έχει γίνει το επόμενο παιχνίδι του Δαιμονικού Γελωτοποιού.", "blurb": "Η μαγεμένη μαριον<PERSON>τα, ο <PERSON><PERSON><PERSON><PERSON>, είχ<PERSON> δημιουργηθεί πριν από πολλά χρόνια για να διασκεδάσει έναν μοναχικό πρίγκιπα, αλλ<PERSON> τώρα έχει μετατραπεί σε ένα δολοφονικό πλάσμα που λατρεύει τον θάνατο και το χάος. Έχοντας διαφθαρεί από μαύρη μαγεία και από την...", "allytips": ["Χρησιμοποιήστε την Εξαπάτηση στο έδαφος για να αποδράσετε με επιτυχία.", "Προσπαθήστε να αποκτήσετε αντικείμενα που επιδρούν κατά το χτύπημα. Αυτά θα ωφελήσουν επίσης τον κλώνο που δημιουργούν οι Παραισθήσεις.", "Η ζημιά από το Πισώπλατο Μαχαίρωμα μπορεί να αυξηθεί με επιδράσεις που αυξάνουν τη Ζημιά Καίριου Χτυπήματος, όπως η Αιχμή του Απείρου."], "enemytips": ["Αν ο Σάκο πάει καλά στην αρχή του παιχνιδιού, αξίζει να επενδύσετε σε Αόρατα Μάτια στις φωλιές της Ζούγκλας που τον ενδιαφέρουν.", "Αν ο Σάκο χρησιμοποιήσει την Εξαπάτηση για να μπει στη μάχη, δεν θα μπορεί να την χρησιμοποιήσει πάλι γρήγορα για να διαφύγει.  Συντονιστείτε με την ομάδα σας για να τον ρίξετε γρήγορα."], "tags": ["Assassin"], "partype": "Μάνα", "info": {"attack": 8, "defense": 4, "magic": 6, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 297, "mpperlevel": 40, "movespeed": 345, "armor": 30, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.694}, "spells": [{"id": "Deceive", "name": "Εξαπάτηση", "description": "Ο Σάκο γίνεται Αόρατος και τηλεμεταφέρεται στη στοχευμένη τοποθεσία.<br><br>Η πρώτη του επίθεση όσο είναι Αόρατος είναι ενισχυμένη και κάνει μπόνους Ζημιά, ενώ προκαλεί και Καίριο Χτύπημα, αν ο Σάκο επιτεθεί από πίσω.", "tooltip": "Ο Σάκο τηλεμεταφέρεται και γίνεται <keywordStealth>Αόρατος</keywordStealth> για {{ stealthduration }} δευτ. Η χρήση της ικανότητας <spellName>Κουτί Γελωτοποιού</spellName> ή <spellName>Παραισθήσεις</spellName> δεν καταργεί την ιδιότητα <keywordStealth>Αόρατος</keywordStealth>.<br /><br />Η επόμενη επίθεση του Σάκο όταν είναι <keywordStealth>Αόρατος</keywordStealth> προκαλεί επιπλέον <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage>. Αν το χτύπημα γίνεται από πίσω, αυτή η επίθεση προκαλεί επίσης Καίριο Χτύπημα για {{ qcritdamagemod }} ζημιά.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκεια Απόκρυψης", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Deceive.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JackInTheBox", "name": "Κουτί Γελωτοποιού", "description": "Ο Σάκο δημιουργεί ένα κρυμμένο, κινούμενο Κουτί Γελωτοποιού. Όταν ενεργοποιηθεί, θα τρομάξει τους εχθρούς που έρχονται κοντά και θα τους επιτεθεί.", "tooltip": "Ο Σάκο δημιουργεί μια παγίδα που γίνεται αόρατη μετά από {{ e5 }} δευτ. και διαρκεί {{ trapduration }} δευτ. Ενεργοποιείται όταν ένας εχθρός πλησιάσει ή αποκαλυφθεί, προκαλώντας <status>Φόβο</status> στους κοντινούς αντίπαλους Ήρωες για {{ fearduration }} δευτ. ή για {{ minionfearduration }} δευτ. σε υπηρέτες και σε τέρατα Ζούγκλας.<br /><br />Μόλις ενεργοποιηθεί, η παγίδα ρίχνει βολές σε όλους τους κοντινούς εχθρούς για 5 δευτ., προκαλώντας <magicDamage>{{ aoedamage }} Μαγική Ζημιά</magicDamage> ή, αν εστιαστεί σε έναν μεμονωμένο εχθρό, <magicDamage>{{ stdamage }} ζημιά</magicDamage>.<br /><br />Το Κουτί Γελωτοποιού προκαλεί επιπλέον <magicDamage>{{ monsterbonusdamage }}</magicDamage> ζημιά σε τέρατα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκεια Φόβου", "Μπόνους ζημιά σε τέρατα", "Κόστος @AbilityResourceName@"], "effect": ["{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ fearduration }} -> {{ feardurationNL }}", "{{ monsterbonusdamage }} -> {{ monsterbonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 65, 60, 55, 50], "costBurn": "70/65/60/55/50", "datavalues": {}, "effect": [null, [35, 50, 65, 80, 95], [200, 300, 400, 500, 600], [0.5, 0.75, 1, 1.25, 1.5], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/50/65/80/95", "200/300/400/500/600", "0.5/0.75/1/1.25/1.5", "40", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "JackInTheBox.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwoShivPoison", "name": "Δηλητή<PERSON><PERSON><PERSON>ιλέτων", "description": "Τα Στιλέτα του Σάκο δηλητηριάζ<PERSON>υν παθητικά τους στόχους κατά το χτύπημα, μειώνοντας την Ταχύτητα Κίνησής τους. Μπορεί να πετάξει τα Στιλέτα του για να προκαλέσει ζημιά και να δηλητηριάσει τον στόχο. Κάθε Στιλέτο που πετάει προκαλεί μπόνους ζημιά, εάν ο στόχος έχει Ζωή κάτω από 30%.", "tooltip": "<spellPassive>Παθητική:</spellPassive> Όταν αυτή η ικανότητα είναι εκτός Χρόνου Επαναφόρτισης, οι επιθέσεις του Σάκο προκαλούν στον στόχο <status>Επιβράδυνση</status> κατά {{ slowamount*-100 }}% για {{ slowdurationpassive }} δευτ.<br /><br /><spellActive>Ενεργή:</spellActive> Ο Σάκο πετά ένα στιλέτο, προκαλώντας <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> και <status>Επιβράδυνση</status> κατά {{ slowamount*-100 }}% για {{ slowdurationactive }} δευτ. Αν ο στόχος έχει κάτω από {{ executehealththreshold*100 }}% Ζωή, το στιλέτο προκαλεί <magicDamage>{{ totalexecutedamage }} ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Επιβράδυνση"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "TwoShivPoison.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HallucinateFull", "name": "Παραισθήσεις", "description": "Ο Σάκο δημιουργεί ένα είδωλο του εαυτού του κοντά του, το οποίο μπορεί να επιτεθεί στους κοντινούς εχθρούς (προκαλεί μειωμένη ζημιά στους Πύργους).  Όταν πεθάνει ανατινάσσεται, δημιουργεί τρία μικρά Κουτιά Γελωτοποιού και προκαλεί ζημιά στους κοντινούς εχθρούς.", "tooltip": "Ο Σάκο εξαφανίζεται για σύντομο διάστημα και μετά επανεμφανίζεται με έναν κλώνο που διαρκεί για {{ clonelifetime }} δευτ. και ανατινάζεται όταν πεθαίνει, προκαλώντας <magicDamage>{{ explosiontotaldamage }} Μαγική Ζημιά</magicDamage> και αφήνοντας πίσω του τρία μίνι <spellName>Κουτιά Γελωτοποιού</spellName>, που ενεργοποιούνται άμεσα. Ο κλώνος προκαλεί το {{ cloneaadamagepercent*100 }}% της ζημιάς του Σάκο και δέχεται {{ cloneincomingdamagepercent*100 }}% αυξημένη ζημιά.<br /><br />Τα μίνι <spellName>Κ<PERSON>υ<PERSON><PERSON><PERSON> Γελωτοποιού</spellName> προκαλούν <magicDamage>{{ aoedamage }} Μαγική Ζημιά</magicDamage> ή <magicDamage>{{ stdamage }} Μαγική Ζημιά</magicDamage>, αν ρίχνουν μόνο σε έναν εχθρό, καθώς και <status>Φόβο</status> για {{ boxfearduration }} δευτ.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά κατά το θάνατο", "Ζημιά μίνι κουτιού", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "HallucinateFull.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Πισώπ<PERSON>α<PERSON><PERSON>ω<PERSON>α", "description": "Οι βασικές επιθέσεις και το Δηλητήριο Δύο Στιλέτων του Σάκο κάνουν περισσότερη ζημιά όταν χτυπά από πίσω.", "image": {"full": "Jester_CarefulStrikes.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}