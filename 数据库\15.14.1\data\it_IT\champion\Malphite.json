{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Malphite": {"id": "Malphite", "key": "54", "name": "Malphite", "title": "la scheggia del monolite", "image": {"full": "Malphite.png", "sprite": "champion2.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "54000", "num": 0, "name": "default", "chromas": false}, {"id": "54001", "num": 1, "name": "Malphite del Trifoglio", "chromas": false}, {"id": "54002", "num": 2, "name": "Malphite <PERSON>", "chromas": false}, {"id": "54003", "num": 3, "name": "Malphite di Marmo", "chromas": false}, {"id": "54004", "num": 4, "name": "Malphite di Ossidiana", "chromas": false}, {"id": "54005", "num": 5, "name": "Malphite Glaciale", "chromas": false}, {"id": "54006", "num": 6, "name": "Mecha Malphite", "chromas": true}, {"id": "54007", "num": 7, "name": "<PERSON>zza<PERSON>", "chromas": false}, {"id": "54016", "num": 16, "name": "Malphite dell'Odissea", "chromas": true}, {"id": "54023", "num": 23, "name": "Malphi<PERSON> Oscu<PERSON>", "chromas": false}, {"id": "54024", "num": 24, "name": "Malphite <PERSON> Oscura (edizione prestigio)", "chromas": false}, {"id": "54025", "num": 25, "name": "Malphite FPX", "chromas": true}, {"id": "54027", "num": 27, "name": "Malphite Dio Antico", "chromas": true}, {"id": "54037", "num": 37, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "54048", "num": 48, "name": "Malphite Festa in Piscina", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, un'enorme creatura di pietra vivente, cerca di imporre l'ordine benedetto a un mondo caotico. Nato come servitore di un obelisco ultraterreno noto come il Monolite, ha usato la sua grande forza elementale per proteggere il suo progenitore, fallen<PERSON>. Essendo l'unico sopravvissuto alla distruzione che ne è conseguita, Malphite è costretto a sopportare il carattere delle soffici creature di Runeterra, mentre cerca un nuovo compito degno dell'ultimo della sua stirpe.", "blurb": "<PERSON><PERSON><PERSON>, un'enorme creatura di pietra vivente, cerca di imporre l'ordine benedetto a un mondo caotico. Nato come servitore di un obelisco ultraterreno noto come il Monolite, ha usato la sua grande forza elementale per proteggere il suo progenitore...", "allytips": ["L'armatura riduce naturalmente il ritmo con cui gli attacchi passano attraverso Scudo granitico, quindi Colpi brutali rinforza lo scudo contro i danni fisici.", "Anche se le sue abilità sono proporzionali all'armatura, in alcune partite Malphite ha bisogno di acquistare resistenza magica. In questi casi, prova a comprare Egida della legione, Calzari di Mercurio e Angelo custode."], "enemytips": ["Se sei un personaggio che infligge danni fisici, stai dietro i tuoi compagni quando combatti contro Mal<PERSON>. Onda d'urto può ridurre notevolmente i tuoi danni.", "Malphite è uno dei pochi tank in grado di essere utilizzato anche come campione da giungla. Fai attenzione se ha preso Punizione."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 9, "magic": 7, "difficulty": 2}, "stats": {"hp": 665, "hpperlevel": 104, "mp": 280, "mpperlevel": 60, "movespeed": 335, "armor": 37, "armorperlevel": 4.95, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 7.3, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3.4, "attackspeed": 0.736}, "spells": [{"id": "SeismicShard", "name": "<PERSON><PERSON><PERSON>ia sismica", "description": "Malphite scaglia un frammento di terra attraverso il suolo fino al suo nemico, infliggendo danni all'impatto e rubando velocità di movimento per 3 secondi.", "tooltip": "Malphite scaglia contro un nemico un frammento di terra che gli infligge <magicDamage>{{ qdamagecalc }} danni magici</magicDamage> e lo <status>rallenta</status> del {{ e2 }}% per {{ slowduration }} secondi. Malphite inoltre guadagna <speed>velocità di movimento</speed> pari all'ammontare del <status>rallentamento</status> per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "20/25/30/35/40", "3", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "SeismicShard.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Obdu<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON> del tuono", "description": "Malphite attacca con forza tale da creare un boato sonico. Per i prossimi secondi, i suoi attacchi creano delle scosse di assestamento di fronte a lui.", "tooltip": "<spellPassive>Passiva: </spellPassive><PERSON><PERSON><PERSON> ottiene <scaleArmor>{{ bonusarmorpassive*100 }}% armatura (%i:scaleArmor%{{ f1 }})</scaleArmor>. <PERSON>o effetto aumenta a <scaleArmor>{{ bonusarmorpassive*300 }}% (%i:scaleArmor%{{ f2 }})</scaleArmor> mentre <spellName>Scudo granitico</spellName> è attivo.<br /><br /><spellPassive>Attiva: </spellPassive>il prossimo attacco base di Malphite infligge <physicalDamage>{{ totalbonusdamage }} danni fisici</physicalDamage> aggiuntivi e crea nella direzione d'attacco un'onda d'urto che infligge <physicalDamage>{{ thunderclapsplash }} danni fisici</physicalDamage>. I suoi attacchi base continuano a creare onde d'urto per i prossimi {{ thunderclapbuffduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "Armatura", "<PERSON><PERSON>", "Danni ad area Scossa di assestamento", "Ricarica"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ bonusarmorpassive*100.000000 }}% -> {{ bonusarmorpassivenl*100.000000 }}%", "{{ thunderclapbasedamage }} -> {{ thunderclapbasedamageNL }}", "{{ thunderclapsplashdamage }} -> {{ thunderclapsplashdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Obduracy.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Landslide", "name": "Onda d'urto", "description": "Malphite colpisce il suolo generando un'onda d'urto che infligge danni magici in base alla sua armatura e riduce la velocità d'attacco dei nemici per un breve periodo.", "tooltip": "Malphite colpisce il suolo infliggendo {{ edamagecalc }}<magicDamage> danni magici</magicDamage> e riducendo la <attackSpeed>velocità d'attacco del {{ asreduction }}%</attackSpeed> per {{ duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Riduzione velocità d'attacco"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ asreduction }}% -> {{ asreductionNL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Landslide.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UFSlash", "name": "Forza inarrestabile", "description": "Malphite carica a tutta velocità verso una posizione, infliggendo danni ai nemici e lanciandoli in aria.", "tooltip": "Malphite carica con la potenza di una valanga, scattando e diventando Inarrestabile. Al termine dello scatto, Malphite <status>lancia in aria</status> il bersaglio per {{ knockupduration }} secondi e gli infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [1.5, 1.75, 2], [200, 300, 400], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "1.5/1.75/2", "200/300/400", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "UFSlash.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> granitico", "description": "Malphite è protetto da uno strato di roccia che assorbe i danni fino al 10% della sua salute massima. Se Malphite non viene colpito per qualche secondo, questo effetto si ricarica.", "image": {"full": "Malphite_GraniteShield.png", "sprite": "passive2.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}