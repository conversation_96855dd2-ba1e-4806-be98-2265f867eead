{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Camille": {"id": "<PERSON>", "key": "164", "name": "<PERSON>", "title": "the Steel Shadow", "image": {"full": "Camille.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "164000", "num": 0, "name": "default", "chromas": false}, {"id": "164001", "num": 1, "name": "Program Camille", "chromas": false}, {"id": "164002", "num": 2, "name": "Coven Camille", "chromas": true}, {"id": "164010", "num": 10, "name": "iG Camille", "chromas": false}, {"id": "164011", "num": 11, "name": "Arcana Camille", "chromas": true}, {"id": "164021", "num": 21, "name": "Strike Commander <PERSON>", "chromas": true}, {"id": "164031", "num": 31, "name": "Winterblessed Camille", "chromas": true}, {"id": "164032", "num": 32, "name": "Prestige Winterblessed Camille", "chromas": false}], "lore": "Dengan senjata yang bisa digunakan di luar batas huku<PERSON>, <PERSON> Principal Intelligencer <PERSON><PERSON>. Dia adalah agen elite dan elegan yang memastikan mesin Piltover dan Zaunite berjalan lancar. <PERSON><PERSON> beradaptasi dan presisi, dia menganggap teknik ceroboh sebagai hal memalukan yang harus ditertibkan. Dengan pikiran setajam pisau, ambisi Camille untuk menjadi superior melalui augmentasi tubuh menggunakan teknologi hextech membuat orang bertanya-tanya, apakah kini dia lebih mirip mesin atau wanita.", "blurb": "<PERSON>gan senjata yang bisa digunakan di luar bat<PERSON>, <PERSON> Principal Intelligencer <PERSON><PERSON>. Dia adalah agen elite dan elegan yang memastikan mesin Piltover dan Zaunite berjalan lancar. <PERSON><PERSON> berada<PERSON>si dan presisi, dia menganggap...", "allytips": ["<PERSON>ng<PERSON> sampai tim musuh sibuk bertarung dengan timmu, la<PERSON> gunakan Hookshot untuk menyerang target yang lemah.", "Gunakan CC dari ability-mu untuk mengenai musuh dengan kedua serangan Precision Protocol."], "enemytips": ["Shield Camille hanya efektif terhadap satu jenis damage, jadi serang dia saat dia tak memiliki perlindungan dari damage-mu.", "Hextech Ultimatum memiliki range yang sangat pendek, jadi segeralah menjauh sebelum dia mendekat."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 99, "mp": 339, "mpperlevel": 52, "movespeed": 340, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.8, "mpregen": 8.15, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.5, "attackspeed": 0.644}, "spells": [{"id": "CamilleQ", "name": "Precision Protocol", "description": "Serangan Camille be<PERSON><PERSON><PERSON> men<PERSON> damage bonus dan memberikan Move Speed bonus. Spell ini bisa di-recast untuk beberapa saat, memberikan peningkatan damage bonus secara signifikan jika <PERSON> menunggu beberapa saat sebelum serangan kedua.", "tooltip": "Serangan <PERSON> be<PERSON> menghasilkan <physicalDamage>{{ bonusdamage }} physical damage</physicalDamage> tambahan dan memberinya <speed>{{ msbonus*100 }}% Move Speed</speed> selama {{ msduration }} detik. Ability ini bisa di-<recast>Recast</recast> dalam {{ qtotalrecasttime }} detik berikutnya.<br /><br />Jika Serangan yang di-<recast>Recast</recast> mengenai setidaknya {{ qrampuptime }} detik setelah yang pertama, damage bonus meningkat hingga <physicalDamage>{{ empoweredbonusdamage }}</physicalDamage> dan {{ damageconversionpercentage }} dari damage Serangan itu akan dikonversi menjadi <trueDamage>true damage</trueDamage>.<br /><br /><rules>Ability ini memicu efek spell saat menghasilkan damage.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rasio Total AD", "Move Speed", "Cooldown"], "effect": ["{{ tadratio*100.000000 }}%-> {{ tadrationl*100.000000 }}%", "{{ msbonus*100.000000 }}%-> {{ msbonusnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "CamilleQ.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleW", "name": "Tactical Sweep", "description": "<PERSON> men<PERSON><PERSON><PERSON><PERSON> damage dalam jangkauan kerucut setelah beberapa saat. <PERSON><PERSON><PERSON> yang ada di paruh luar akan terkena slow, men<PERSON><PERSON> damage ta<PERSON><PERSON>, dan <PERSON> akan dipuli<PERSON>kan.", "tooltip": "<PERSON> men<PERSON>lkan tenaga dan menebas, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ basedamagetotal }} physical damage</physicalDamage>.<br /><br />Mu<PERSON>h yang terkena di paruh luar akan terkena efek <status>Slow</status> sebesar {{ slowpercentage }}% yang berkurang dalam kurun waktu {{ slowduration }} detik, dan menerima tambahan <physicalDamage>{{ outeredgetooltip }} physical damage dari Health maksimum</physicalDamage>. <PERSON> memulihkan <healing>{{ outerconehealingratio }}% dari damage bonus yang dihasilkan ke champion sebagai Health</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@ Cost", "Damage", "Damage Health Maksimum", "Cooldown"], "effect": ["{{ cost }}-> {{ costNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ outerconemaxhpdamage*100.000000 }}%-> {{ outerconemaxhpdamagenl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 15.5, 14, 12.5, 11], "cooldownBurn": "17/15.5/14/12.5/11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "CamilleW.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleE", "name": "Hookshot", "description": "<PERSON> menarik dirinya ke tembok, kemudian melompat dan <PERSON> Up champion musuh saat mendarat.", "tooltip": "<PERSON>n hookshot yang menempel di medan, membuatnya tertarik ke sana selama 1 detik dan memungkinkan Ability ini di-<recast>Recast</recast>.<br /><br /><recast>Recast:</recast> <PERSON> dash dari dinding, menabrak champion musuh pertama yang terkena. Sa<PERSON> mendarat, dia men<PERSON><PERSON><PERSON>an <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> pada musuh di sekitar dan menerapkan <status>Stun</status> pada champion musuh selama {{ knockupduration }} detik. Dash ke arah champion musuh akan berjarak dua kali lebih jauh, dan memberikan <attackSpeed>{{ asbuff*100 }}% Attack Speed</attackSpeed> selama {{ asduration }} detik saat bertabrakan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "Attack Speed"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ asbuff*100.000000 }}%-> {{ asbuffnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CamilleE.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleR", "name": "The Hextech Ultimatum", "description": "<PERSON> dash ke champion target, menahan target di area tersebut. Dia juga men<PERSON><PERSON><PERSON>an magic damage bonus pada target dengan basic attack-nya.", "tooltip": "<PERSON> menjadi Tak Bisa Ditarget dalam waktu singkat dan melompat ke champion musuh, menghentikan channeling dan mengurung mereka dalam area yang membuat mereka tidak dapat lolos selama {{ rduration }} detik. <PERSON><PERSON><PERSON> lain di sekitar akan <status>Terpental</status>. <PERSON><PERSON><PERSON><PERSON> terhadap musuh yang terkurung member<PERSON><PERSON> tambahan <magicDamage>{{ rpercentcurrenthpdamage }}% magic damage dari Health saat ini</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Health Saat Ini", "<PERSON><PERSON><PERSON>", "Cooldown"], "effect": ["{{ rpercentcurrenthpdamage }}%-> {{ rpercentcurrenthpdamageNL }}%", "{{ rduration }}-> {{ rdurationNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475], "rangeBurn": "475", "image": {"full": "CamilleR.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Adaptive Defenses", "description": "Basic attack pada champion member<PERSON>n shield sebesar sekian persen dari Health maksimum Camille se<PERSON>ai dengan jenis damage-nya (Physical atau Magic) selama beberapa saat.", "image": {"full": "Camille_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}