{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "<PERSON><PERSON><PERSON>", "title": "the Deathsinger", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "30002", "num": 2, "name": "Statue of Karthus", "chromas": false}, {"id": "30003", "num": 3, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "30004", "num": 4, "name": "Penta<PERSON>", "chromas": false}, {"id": "30005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30010", "num": 10, "name": "Infernal Karthus", "chromas": true}, {"id": "30017", "num": 17, "name": "Pentakill III: Lost Chapter Karthus", "chromas": true}, {"id": "30026", "num": 26, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON> pembawa kehan<PERSON>ran, <PERSON><PERSON><PERSON> adalah roh abadi yang lagu-lagunya menghantui dan menandakan awal kemunculannya yang mengerikan. Manusia menakuti keabadian alam maut, teta<PERSON>us justru melihat keindahan dan kemurnian di dalam<PERSON>, perpaduan sempurna antara hidup dan mati. Saat Karthus muncul dari <PERSON>, dia membawa sukacita kematian pada manusia, seorang rasul bagi yang mati.", "blurb": "<PERSON> pem<PERSON> k<PERSON>, <PERSON><PERSON><PERSON> ad<PERSON>h roh abadi yang lagu-lagunya menghantui dan menandakan awal kemunculannya yang mengerikan. Man<PERSON>ia menakuti keabadian alam maut, teta<PERSON> justru melihat keindahan dan kemurnian di dalamnya, perpaduan sempurna...", "allytips": ["Minta bantuan sekutumu untuk memberi tahu kapan waktu yang tepat menggunakan Requiem untuk mendapatkan kill di jalur lain.", "Lay Waste sangat efektif untuk farming minion dan mengganggu champion musuh."], "enemytips": ["<PERSON><PERSON><PERSON> bi<PERSON> cast spell dalam durasi pendek setelah dia dibunuh. <PERSON><PERSON> aman, men<PERSON><PERSON><PERSON> dari may<PERSON>.", "Pastikan kamu selalu punya cukup Health untuk bertahan dari Requiem, bahkan jika kamu harus kembali ke base lebih sering untuk dapat heal."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "Lay Waste", "description": "<PERSON><PERSON><PERSON> me<PERSON>kan ledakan yang berjeda di suatu lokasi, <PERSON><PERSON><PERSON><PERSON><PERSON> damage pada musuh di sekitar. <PERSON><PERSON><PERSON><PERSON><PERSON> damage yang meningkat pada musuh yang terisolasi. ", "tooltip": "<PERSON><PERSON><PERSON> menci<PERSON>kan ledakan si<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ qdamage }} magic damage</magicDamage>. Jika hanya mengenai satu musuh, ledakan tersebut jadi mengh<PERSON>an <magicDamage>{{ qsingletargetdamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "Wall of Pain", "description": "<PERSON><PERSON><PERSON> menciptakan layar penyedot energi yang dapat dilewati. Setiap unit musuh yang berjalan melewati layar tersebut akan berkurang Move Speed dan Magic Resist-nya selama beberapa saat.", "tooltip": "<PERSON><PERSON><PERSON> membuat dinding yang bertahan selama {{ e4 }} detik. Musuh yang melewatinya akan kehilangan <scaleMR>{{ e1 }}% Magic Resist</scaleMR> selama {{ e5 }} detik dan terkena <status>Slow</status> sebesar {{ e3 }}% yang berkurang di sepanjang durasi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Slow Move Speed"], "effect": ["{{ e2 }}-> {{ e2NL }}", "{{ e3 }}%-> {{ e3NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>rth<PERSON> secara pasif mencuri energi dari k<PERSON>, mendapatkan <PERSON> set<PERSON> kali kill. <PERSON><PERSON><PERSON><PERSON><PERSON>, Karthus bisa membuat jiwa mangsanya mengelilingi dirinya, <PERSON><PERSON><PERSON><PERSON><PERSON> damage pada musuh di sekitar, tetapi cepat menguras Mana-nya sendiri.", "tooltip": "<spellPassive>Pasif: </spellPassive>Saat menghabisi satu unit, Karthus memulihkan <scaleMana>{{ e2 }} Mana</scaleMana>.<br /><br /><toggle>Toggle: </toggle>Karthus menciptakan aura nekrotik, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldps }} magic damage</magicDamage> per detik pada musuh di sekitar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ e2 }}-> {{ e2NL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} <PERSON><PERSON> per <PERSON>ik"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "Requiem", "description": "<PERSON><PERSON><PERSON> channeling se<PERSON>a 3 detik, <PERSON><PERSON><PERSON> damage pada semua champion musuh.", "tooltip": "<PERSON><PERSON><PERSON> channel se<PERSON><PERSON> 3 de<PERSON>k, <PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> pada champion musuh, berapa pun jaraknya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Death Defied", "description": "<PERSON><PERSON> mati, <PERSON><PERSON><PERSON> memasuki spirit form yang membuatnya bisa terus cast spell.", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}