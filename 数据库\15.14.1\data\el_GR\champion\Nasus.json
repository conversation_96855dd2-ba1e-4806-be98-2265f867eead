{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nasus": {"id": "<PERSON><PERSON>", "key": "75", "name": "Νάσ<PERSON>υς", "title": "ο Φύλακας της Άμμου", "image": {"full": "Nasus.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "75000", "num": 0, "name": "default", "chromas": false}, {"id": "75001", "num": 1, "name": "Διαγαλ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Νάσους", "chromas": false}, {"id": "75002", "num": 2, "name": "Φαρα<PERSON>ς", "chromas": false}, {"id": "75003", "num": 3, "name": "Νάσ<PERSON>υς ο Ιππότης του Τρόμου", "chromas": true}, {"id": "75004", "num": 4, "name": "Riot Νάσους", "chromas": false}, {"id": "75005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "chromas": false}, {"id": "75006", "num": 6, "name": "Ωνάσους", "chromas": false}, {"id": "75010", "num": 10, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Καταστρο<PERSON><PERSON><PERSON><PERSON>σο<PERSON>ς", "chromas": false}, {"id": "75011", "num": 11, "name": "Νά<PERSON><PERSON><PERSON><PERSON>ύ<PERSON>ακας της Σελήνης", "chromas": true}, {"id": "75016", "num": 16, "name": "Μηχανή Πολέμου Νάσους", "chromas": true}, {"id": "75025", "num": 25, "name": "Νάσους του Διαστημικού Ρυθμού", "chromas": true}, {"id": "75035", "num": 35, "name": "Θωρα<PERSON><PERSON>σ<PERSON><PERSON><PERSON>ος Τιτ<PERSON><PERSON><PERSON><PERSON>σους", "chromas": true}, {"id": "75045", "num": 45, "name": "Κομιστής του Δειλινού Νάσους", "chromas": true}, {"id": "75054", "num": 54, "name": "Δημιου<PERSON><PERSON><PERSON>ς του Πεπρωμένου Νάσους", "chromas": true}], "lore": "Ο Νάσους είναι ένα επιβλητικό Εξυψωμένο ον με κεφάλι τσακαλιού από την αρχαία Σουρίμα, μια ηρωική φιγούρα - οι λαοί της ερήμου τον θεωρούν ημίθεο. Απερίγραπτα έξυπνος, ήταν ο φύλακας της γνώσης και ο ακαταμάχητος στρατηλάτης, του οποίου η σοφία οδήγησε την αυτοκρατορία της Σουρίμα στο μεγαλείο για πολλούς αιώνες. Μετά τη πτώση της αυτοκρατορίας, επέλεξε την αυτοεξορία, πράγμα που τον έκανε να φαντάζει σχεδόν μυθικός. Τώρα που η αρχαία πόλη της Σουρίμα εξυψώθηκε και πάλι, ο Νάσους επέστρεψε, αποφασισμένος να μην επιτρέψει ποτέ ξανά την πτώση της.", "blurb": "Ο Νάσους είναι ένα επιβλητικό Εξυψωμένο ον με κεφάλι τσακαλιού από την αρχαία Σουρίμα, μια ηρωική φιγούρα - οι λαοί της ερήμου τον θεωρούν ημίθεο. Απερίγραπτα έξυπνος, ήταν ο φύλακας της γνώσης και ο ακαταμάχητος στρατηλάτης, του οποίου η σοφία οδήγησε...", "allytips": ["Χρησιμοποιείτε το Χτύπημα Απορρόφησης για να αποτελειώνετε τους υπηρέτες. Αυτό θα παίξει μεγάλο ρόλο στο τέλος του παιχνιδιού.", "Αν παίζετε μόνος στη λωρίδα, η Πνευματική Φωτιά θα σας βοηθήσει να μαζέψετε χρυσό. Αν είστε σε λωρίδα με 2, μην προχωρήσετε υπερβολικά μπροστά. Βρείτε τη χρυσή τομή μεταξύ τελικών χτυπημάτων με το Χτύπημα Απορρόφησης και συλλογή χρυσού με ξόρκια που πιάνουν χώρο.", "Αν δεν έχετε δυνατές άμυνες, οι αντίπαλοι θα επικεντρωθούν πάνω σας, ακόμα και κατά τη διάρκεια της υπέρτατης ικανότητάς σας. Ακόμα κι αν έχετε εξειδικεύσεις με επιθετικό προσανατολισμό, αγοράστε μερικά αντικειμένα που θα βελτιώσουν την αντοχή σας."], "enemytips": ["Όταν τον έχει μεταμορφώσει η Υπέρτατη ικανότητά του, ο Νάσους είναι ίσως ο ισχυρότερος Ήρωας από όλους. Πολεμήστε τον μόνο όταν έχετε σαφές πλεονέκτημα.", "Στη μέγιστη βαθμίδα, το Σάπισμα είναι πολύ αποτελεσματικός τρόπος καταπολέμησης των Ηρώων που βασίζονται στη Ζημιά Επίθεσης, οπότε προσπαθήστε να αποφύγετε τις μονομαχίες μαζί του.", "Ο Νάσους είναι ευάλωτος σε τακτικές μακρινών επιθέσεων. Προσπαθήστε να μην τον αντιμετωπίζετε όταν έχει πλήρη Ζωή."], "tags": ["Fighter", "Tank"], "partype": "Μάνα", "info": {"attack": 7, "defense": 5, "magic": 6, "difficulty": 6}, "stats": {"hp": 631, "hpperlevel": 104, "mp": 326, "mpperlevel": 62, "movespeed": 350, "armor": 34, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.45, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 67, "attackdamageperlevel": 4, "attackspeedperlevel": 3.48, "attackspeed": 0.638}, "spells": [{"id": "NasusQ", "name": "Χτύπημα Απορρόφησης", "description": "Ο Νάσους χτυπά τον αντίπαλό του προκαλώντας του ζημιά, ενώ αυξάνει τη δύναμη των μελλοντικών Χτυπημάτων Απορρόφησης αν σκοτώσει τον στόχο του.", "tooltip": "Η επόμενη Επίθεση του Νάσους προκαλεί <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage>. Όταν σκοτώνεται ένας εχθρός με αυτήν την επίθεση, η ζημιά της αυξάνεται μόνιμα κατά {{ basicstacks }}, ενώ αυξάνεται και σε {{ bigstacks }} εναντίον Ηρώων, μεγάλων υπηρετών και μεγάλων τεράτων της Ζούγκλας.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7.5, 6.5, 5.5, 4.5, 3.5], "cooldownBurn": "7.5/6.5/5.5/4.5/3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [255, 255, 255, 255, 255], "rangeBurn": "255", "image": {"full": "NasusQ.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusW", "name": "Σάπισμα", "description": "Ο Νάσους γερνά έναν αντίπαλο Ήρωα και μειώνει την Ταχύτητα Κίνησης και Ταχύτητα Επίθεσής του με την πάροδο του χρόνου.", "tooltip": "Ο Νάσους προκαλεί τη γήρανση ενός Ήρωα, <status>Επιβραδύνοντάς</status> τον κατά {{ slowbase }}%, ποσοστό που αυξάνεται σε {{ max<PERSON><PERSON>oltiponly }}% μέσα σε {{ duration }} δευτ. Η <attackSpeed>Ταχύτητα Επίθεσης</attackSpeed> του Ήρωα μειώνεται επίσης κατά {{ attackspeedslowmult*100 }}% λόγω της <status>Επιβράδυνσης</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Μέγιστη Επιβράδυνση", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ maxslowtooltiponly }}% -> {{ maxslowtooltiponlyNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "NasusW.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusE", "name": "Πνευματική Φωτιά", "description": "Ο Νάσους εξαπολύει ένα πνεύμα φωτιάς σε μια τοποθεσία προκαλώντας ζημιά, ενώ μειώνει και τη Θωράκιση των εχθρών που στέκονται πάνω του.", "tooltip": "Ο Νάσους ανάβει μια φλόγα πνεύματος, προκαλώντας <magicDamage>{{ initialdamage }} Μαγική Ζημιά</magicDamage>. Οι εχθροί που βρίσκονται στην περιοχή χάνουν <scaleArmor>{{ e2 }}% Θωράκιση</scaleArmor> και δέχονται <magicDamage>{{ totaldotdamage }} Μαγική Ζημιά</magicDamage> μέσα σε {{ e3 }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Αρχική ζημιά", "Ζημιά ανά δευτερόλεπτο", "Μείωση Θωράκισης %", "Κόστος @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [10, 16, 22, 28, 34], [30, 35, 40, 45, 50], [5, 5, 5, 5, 5], [50, 80, 110, 140, 170], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/16/22/28/34", "30/35/40/45/50", "5", "50/80/110/140/170", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NasusE.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusR", "name": "Οργή της Άμμου", "description": "Ο Νάσους εξαπολύει μια πανίσχυρη αμμοθύελλα που χτυπά αλύπητα τους κοντινούς εχθρούς. Εν<PERSON> μαίνεται η θύελλα, απ<PERSON><PERSON><PERSON><PERSON> επιπλέον Ζωή, Εμβ<PERSON><PERSON><PERSON><PERSON><PERSON> Επίθεσης, προ<PERSON><PERSON><PERSON><PERSON><PERSON> Ζημιά στους κοντινούς εχθρούς, το Χτύπημα Απορρόφησης έχει μικρότερο Χρόνο Επαναφόρτισης και, ε<PERSON><PERSON><PERSON><PERSON><PERSON>ον, απο<PERSON><PERSON><PERSON> μπόνους Θωράκιση και Αντίσταση Μαγείας.", "tooltip": "Ο Νάσους ενδυναμώνεται μέσα στην αμμοθύελλα για 15 δευτ. Η <healing>μέγιστη Ζωή του αυξάνεται κατά {{ bonushealth }}</healing> και η <scaleArmor>Θωράκιση</scaleArmor> και η <scaleMR>Αντίσταση Μαγείας</scaleMR> αυξάνονται κατά {{ initialresistgain }}.<br /><br />Ενώ μαίνεται η θύελλα, οι κοντινοί εχθροί δέχονται Μαγική Ζημιά ίση με το <magicDamage>{{ damagecalc }} της μέγιστης Ζωής τους ανά δευτερόλεπτο</magicDamage>, ενώ το <spellName>Χτύπημα Απορρόφησης</spellName> έχει μειωμένο Χρόνο Επαναφόρτισης κατά {{ qcdr*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Μπόνους Ζωή", "% Μέγιστης Ζωής", "Μπ<PERSON><PERSON><PERSON><PERSON><PERSON> Θωράκιση και Αντίσταση Μαγείας", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ aoedamagepercent*100.000000 }}% -> {{ aoedamagepercentnl*100.000000 }}%", "{{ initialresistgain }} -> {{ initialresistgainNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NasusR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Θεριστ<PERSON><PERSON>ν", "description": "Ο Νάσους απορροφά την ψυχική ενέργεια των εχθρών, κερδίζοντας παραπάνω Αρπαγή Ζωής.", "image": {"full": "Nasus_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}