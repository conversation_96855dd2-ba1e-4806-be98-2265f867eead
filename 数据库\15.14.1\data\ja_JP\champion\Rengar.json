{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rengar": {"id": "<PERSON><PERSON>", "key": "107", "name": "レンガー", "title": "孤高のハンター", "image": {"full": "Rengar.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "107000", "num": 0, "name": "default", "chromas": false}, {"id": "107001", "num": 1, "name": "ヘッドハンター レンガー", "chromas": true}, {"id": "107002", "num": 2, "name": "ナイトハンター レンガー", "chromas": false}, {"id": "107003", "num": 3, "name": "SSW レンガー", "chromas": false}, {"id": "107008", "num": 8, "name": "メカレンガー", "chromas": true}, {"id": "107015", "num": 15, "name": "レンニャー", "chromas": true}, {"id": "107023", "num": 23, "name": "砂漠の守護者レンガー", "chromas": true}, {"id": "107030", "num": 30, "name": "光の番人レンガー", "chromas": true}, {"id": "107040", "num": 40, "name": "ストリートデーモン レンガー", "chromas": true}], "lore": "獰猛にして卓越…ヴァスタヤのハンターであるレンガーは、危険な生物を追跡して殺す、そのスリルを味わうために生きている。彼は強く恐ろしい猛獣たち、そしてかつて彼自身の片目を奪ったヴォイドの怪物カ＝ジックスの痕跡を求めて、世界中をさまよい歩く。レンガーは食事や名誉のために狩りをすることはない。狩猟は彼にとって美であり、その美しさのためにこそ、彼は獲物を追跡し引き裂くのだ。", "blurb": "獰猛にして卓越…ヴァスタヤのハンターであるレンガーは、危険な生物を追跡して殺す、そのスリルを味わうために生きている。彼は強く恐ろしい猛獣たち、そしてかつて彼自身の片目を奪ったヴォイドの怪物カ＝ジックスの痕跡を求めて、世界中をさまよい歩く。レンガーは食事や名誉のために狩りをすることはない。狩猟は彼にとって美であり、その美しさのためにこそ、彼は獲物を追跡し引き裂くのだ。", "allytips": ["集団戦や突発的な戦闘では、レンガーのアルティメットスキルを発動して危険な対象から密かにそして迅速に仕留めていこう。", "レンガーの戦闘能力は強化スキルを発動するタイミングによって大きく変動する。使いどころを間違えないように！", "敵を追跡する時は、レンガーの固有スキルを生かせるよう茂みを抜けていくのがベストだ。"], "enemytips": ["レンガーはフェロシティが最大になるとスキルが強化されるので、戦いを仕掛けるならフェロシティがたまる前を狙おう。", "レンガーは固有スキルにより、茂みの中から飛びかかって攻撃してくる。なるべく、茂みの近くで戦うことは避けよう。", "「狩猟本能」が発動してカモフラージュ状態になると、レンガーの一番近くの敵チャンピオンの頭上にアイコンが発生する。"], "tags": ["Assassin", "Fighter"], "partype": "フェロシティ", "info": {"attack": 7, "defense": 4, "magic": 2, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 4, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.667}, "spells": [{"id": "RengarQ", "name": "逆上", "description": "次の通常攻撃が追加ダメージを与える。<br><br>フェロシティボーナス: 与えるダメージと攻撃速度が増加。", "tooltip": "次に行う2回の通常攻撃の<attackSpeed>攻撃速度が{{ e5 }}%</attackSpeed>増加する。1回目の通常攻撃はクリティカルになり、<physicalDamage>{{ F4 }}(%i:scaleAD%%i:scaleCrit%)の物理ダメージ</physicalDamage>を与える。<br /><br /><keywordMajor>最大フェロシティ:</keywordMajor> 1回目の通常攻撃がクリティカルになり、<physicalDamage>{{ F5 }}(%i:scaleLevel%%i:scaleAD%%i:scaleCrit%)の物理ダメージ</physicalDamage>を与え、{{ e3 }}秒間<attackSpeed>攻撃速度が{{ empoweredqas }}</attackSpeed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "合計攻撃力反映率", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [100, 105, 110, 115, 120], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [40, 40, 40, 40, 40], [3, 3, 3, 3, 3], [0.2, 0.3, 0.4, 0.5, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/60/90/120/150", "100/105/110/115/120", "5", "5", "40", "3", "0.2/0.3/0.4/0.5/0.6", "0", "0", "0"], "vars": [], "costType": "フェロシティ1増加", "maxammo": "1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RengarQ.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "フェロシティ1増加"}, {"id": "RengarW", "name": "狩りの雄叫び", "description": "レンガーが雄叫びをあげて周囲の敵にダメージを与え、直前に受けたダメージの一部を回復する。<br><br>フェロシティボーナス: 自身が受けている行動妨害効果を除去する。", "tooltip": "咆哮をあげて周囲の敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、直前{{ e3 }}秒以内に受けたダメージの<healing>{{ damagepercentagehealed }}%にあたる体力</healing>を回復する。<br /><br /><keywordMajor>最大フェロシティ:</keywordMajor> <magicDamage>{{ totaldamageempowered }}の魔法ダメージ</magicDamage>を与えて、自身が受けている行動妨害効果を除去する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50", "1.5", "1.5", "100", "0", "0", "0", "0", "0"], "vars": [], "costType": "フェロシティ1増加", "maxammo": "1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RengarW.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "フェロシティ1増加"}, {"id": "RengarE", "name": "鉄球の投げ縄", "description": "投げ縄を投げ、最初に命中した対象に短時間スロウ効果を付与する。<br><br>フェロシティボーナス: 対象にスネア効果を付与する。", "tooltip": "投げ縄を放ち、最初に命中した敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与えて可視化し、{{ e3 }}秒間{{ e2 }}%の<status>スロウ効果</status>を付与する。<br /><br /><keywordMajor>最大フェロシティ:</keywordMajor> <physicalDamage>{{ totalempowereddamage }}の物理ダメージ</physicalDamage>を与え、{{ e4 }}秒間<status>スネア効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [55, 100, 145, 190, 235], [30, 45, 60, 75, 90], [1.75, 1.75, 1.75, 1.75, 1.75], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "55/100/145/190/235", "30/45/60/75/90", "1.75", "1.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "フェロシティ1増加", "maxammo": "1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RengarE.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "フェロシティ1増加"}, {"id": "RengarR", "name": "狩猟本能", "description": "「狩猟本能」の覚醒によってレンガーが<font color='#cd90ee'>カモフラージュ</font>状態となり、広範囲にわたって敵チャンピオンの位置を把握する。「狩猟本能」の覚醒中は移動速度が増加し、茂みの中にいなくても発見した敵に向かってジャンプ攻撃が可能になり、対象の物理防御を低下させる。", "tooltip": "<spellPassive>自動効果:</spellPassive> <keywordStealth>カモフラージュ</keywordStealth>状態の時、飛びかかって通常攻撃を行う。<br /><br /><spellActive>発動効果:</spellActive> {{ stealthduration }}秒間、<speed>移動速度が{{ stealthms }}%</speed>増加し、最も近い敵チャンピオンを中心とした狭い範囲で<keywordStealth>真の視界</keywordStealth>を得る。<br /><br />{{ fadetime }}秒後、<keywordStealth>カモフラージュ</keywordStealth>状態になり、茂みに入らなくても敵に飛びかかれるようになる。最も近くにいるチャンピオンに飛びかかると<physicalDamage>{{ bonusdamage }}の物理ダメージ</physicalDamage>を追加で与え、{{ armorshredduration }}秒間<scaleArmor>物理防御を{{ armorshred }}</scaleArmor>低下させて、このスキルが終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["物理防御低下", "効果時間", "移動速度", "感知範囲", "クールダウン"], "effect": ["{{ armorshred }} -> {{ armorshredNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ stealthms }}% -> {{ stealthmsNL }}%", "{{ selfvisionrange }} -> {{ selfvisionrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [2500, 3000, 3500], "rangeBurn": "2500/3000/3500", "image": {"full": "RengarR.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "見えざる襲撃者", "description": "茂みの中にいると、通常攻撃で対象に向かって飛びつく。<br><br>スキルを使用するたびにフェロシティを獲得する。フェロシティが最大になると次に使用するスキルが強化される。<br><br>敵チャンピオンを倒すと<font color='#BBFFFF'>「骨牙の首飾り」</font>のトロフィーを獲得し、増加攻撃力を獲得する。", "image": {"full": "Rengar_P.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}