{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ahri": {"id": "<PERSON><PERSON>", "key": "103", "name": "アーリ", "title": "九尾の狐", "image": {"full": "Ahri.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "103000", "num": 0, "name": "default", "chromas": false}, {"id": "103001", "num": 1, "name": "古王朝アーリ", "chromas": true}, {"id": "103002", "num": 2, "name": "宵闇姫アーリ", "chromas": true}, {"id": "103003", "num": 3, "name": "狐火娘アーリ", "chromas": true}, {"id": "103004", "num": 4, "name": "ポップスター アーリ", "chromas": true}, {"id": "103005", "num": 5, "name": "チャレンジャー アーリ", "chromas": true}, {"id": "103006", "num": 6, "name": "アカデミー アーリ", "chromas": true}, {"id": "103007", "num": 7, "name": "アーケード アーリ", "chromas": true}, {"id": "103014", "num": 14, "name": "スターガーディアン アーリ", "chromas": true}, {"id": "103015", "num": 15, "name": "K/DA アーリ", "chromas": true}, {"id": "103016", "num": 16, "name": "プレステージ K/DA アーリ", "chromas": false}, {"id": "103017", "num": 17, "name": "古の賢樹アーリ", "chromas": true}, {"id": "103027", "num": 27, "name": "精霊の花祭りアーリ", "chromas": true}, {"id": "103028", "num": 28, "name": "K/DA ALL OUT アーリ", "chromas": true}, {"id": "103042", "num": 42, "name": "盟約の魔女アーリ", "chromas": true}, {"id": "103065", "num": 65, "name": "プレステージ K/DA アーリ(2022)", "chromas": false}, {"id": "103066", "num": 66, "name": "アルカナ アーリ", "chromas": true}, {"id": "103076", "num": 76, "name": "スノームーン アーリ", "chromas": true}, {"id": "103085", "num": 85, "name": "覚醒せし伝説アーリ", "chromas": false}, {"id": "103086", "num": 86, "name": "不滅なる伝説アーリ", "chromas": false}], "lore": "生まれつき霊的領域の魔法との繋がりを持つアーリは、狐のような姿をしたヴァスタヤであり、獲物の感情を操ってその生気を自分に取り込む。その際、彼女は獲物の記憶や考えを垣間見ることができる。かつては強大な力を持つ気まぐれな捕食者だったが、今は祖先の足跡を探して世界中を旅しており、自らの記憶でこれまで奪ってきた記憶を置き換えようとしている。", "blurb": "生まれつき霊的領域の魔法との繋がりを持つアーリは、狐のような姿をしたヴァスタヤであり、獲物の感情を操ってその生気を自分に取り込む。その際、彼女は獲物の記憶や考えを垣間見ることができる。かつては強大な力を持つ気まぐれな捕食者だったが、今は祖先の足跡を探して世界中を旅しており、自らの記憶でこれまで奪ってきた記憶を置き換えようとしている。", "allytips": ["コンボ攻撃を狙うときは、まず「チャーム」を命中させることで「幻惑のオーブ」と「フォックスファイア」を命中させやすくなる。", "集団戦では「チャーム」で戦端を開き「スピリットラッシュ」で敵をかく乱しよう。", "「スピリットラッシュ」は他のスキルと組み合わせることでさらに効果を発揮する。「チャーム」を命中させるための位置取りや「幻惑のオーブ」を2ヒットさせることも容易になる。そして「フォックスファイア」の有効範囲に詰め寄ることもできる。"], "enemytips": ["アルティメットスキルの「スピリットラッシュ」がクールダウンに入っていると、アーリの生存力は大きく下がり攻めるチャンスとなる。", "ミニオンを盾にして「チャーム」を避ければ、アーリから大ダメージを受けるリスクを大幅に下げることができる。"], "tags": ["Mage", "Assassin"], "partype": "マナ", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 418, "mpperlevel": 25, "movespeed": 330, "armor": 21, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.2, "attackspeed": 0.668}, "spells": [{"id": "<PERSON><PERSON><PERSON>", "name": "幻惑のオーブ", "description": "往復するオーブを放ち、命中した敵に魔法ダメージを与える。戻る時に与えるダメージは確定ダメージになる。 ", "tooltip": "往復するオーブを放ち、命中した敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。戻る時に与えるダメージは<trueDamage>{{ totaldamage }}の確定ダメージ</trueDamage>になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "ダメージ"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [55, 65, 75, 85, 95], "costBurn": "55/65/75/85/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [970, 970, 970, 970, 970], "rangeBurn": "970", "image": {"full": "AhriQ.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AhriW", "name": "フォックスファイア", "description": "少しの間だけ移動速度が増加し、近くにいる敵を自動的に攻撃する、3つの狐火を放つ。", "tooltip": "近くにいる敵を自動で狙う3つの狐火を放ち、<magicDamage>{{ singlefiredamage }}の魔法ダメージ</magicDamage>を与える。複数の狐火が1体の敵に当たった場合、2つ目以降は<magicDamage>ダメージが{{ multifiredamage }}</magicDamage>に低下する。また、<speed>移動速度が{{ movementspeed*100 }}%</speed>増加し、{{ movementspeedduration }}秒かけて元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "AhriW.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AhriE", "name": "チャーム", "description": "投げキッスを放ち、最初に触れた敵にダメージとチャーム効果を与える。チャームされた敵は何もできなくなってアーリに引き寄せられ、発動中の移動スキルもただちに無効になる。", "tooltip": "投げキッスを放ち、最初に触れた敵に{{ charmduration }}秒間<status>チャーム効果</status>を付与し、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "効果時間"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ charmduration }} -> {{ charmdurationNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "AhriE.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AhriR", "name": "スピリットラッシュ", "description": "ダッシュしてエネルギーを放ち、周囲にいる敵にダメージを与える。「スピリットラッシュ」はクールダウンに入るまでの間に、最大3回使用できる。敵チャンピオンからキルまたはアシストを奪うと、再発動可能な回数が増える。", "tooltip": "すばやくダッシュして周囲の敵(チャンピオン優先)に{{ rmaxtargetspercast }}個のエネルギーを放つ。エネルギーはそれぞれが<magicDamage>{{ rcalculateddamage }}の魔法ダメージ</magicDamage>を与える。<spellName>「スピリットラッシュ」</spellName>は{{ rrecastwindow }}秒以内なら、最大2回<recast>再発動</recast>できる。<br /><br />この時間内に<spellName>「生気吸引」</spellName>でチャンピオンの「生気のかけら」を消費すると、<spellName>「スピリットラッシュ」</spellName>の再発動可能時間が最大{{ pdurationextension }}秒延長され、再発動可能回数が1回増加する(最大{{ rmaxcasts }}回まで蓄積)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "AhriR.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "生気吸引", "description": "ミニオンまたはモンスターを9体キルすると体力が回復する。<br>敵チャンピオンからキルまたはアシストを奪うと体力が大幅に回復する。", "image": {"full": "Ahri_SoulEater2.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}