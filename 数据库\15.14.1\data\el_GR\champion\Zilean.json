{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "Ζίλεαν", "title": "ο Φύλακας του Χρόνου", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "Ζίλεαν Άη Βασίλης", "chromas": false}, {"id": "26002", "num": 2, "name": "Χίπης Ζίλεαν", "chromas": false}, {"id": "26003", "num": 3, "name": "Ζίλεαν από την Έρημο Σουρίμα", "chromas": false}, {"id": "26004", "num": 4, "name": "Ζίλεαν με Χρονομηχανή", "chromas": false}, {"id": "26005", "num": 5, "name": "Ζίλεαν του Ματωμένου Φεγγαριού", "chromas": false}, {"id": "26006", "num": 6, "name": "Ζαχαρένιος Ζίλεαν", "chromas": true}, {"id": "26014", "num": 14, "name": "Ευλογημένος από τον Χειμώνα Ζίλεαν", "chromas": true}], "lore": "Ο Ζίλεαν ήταν κάποτε ένας πανίσχυρος μάγος από την Ικάθια, ο ο<PERSON><PERSON><PERSON>ος απέκτησε μια ανθυγιεινή εμμονή με το πέρασμα του χρόνου όταν είδε την πατρίδα του να καταστρέφεται από τα πλάσματα του Κενού. Αντί να χάσει χρόνο θρηνώντας την απώλεια της πατρίδας του, επικαλέστηκε τη μαγεία του χρόνου για να αναλύσει όλους τους πιθανούς συνδυασμούς γεγονότων που οδήγησαν στη μεγάλη καταστροφή. Χάρη στη μαγεία αυτή, ο Ζίλεαν έχει γίνει πλέον πρακτικά αθάνατος και τώρα ταξιδεύει στο παρελθόν, στο παρόν και στο μέλλον, αλλοιώνοντας τη ροή του χρόνου γύρω του και αναζητώντας εκείνη τη μοναδική στιγμή που θα του επιτρέψει να γυρίσει τον χρόνο πίσω και να αποκαταστήσει την καταστροφή της Ικάθια.", "blurb": "Ο Ζίλεαν ήταν κάποτε ένας πανίσχυρος μάγος από την Ικάθια, ο οπ<PERSON><PERSON>ος απέκτησε μια ανθυγιεινή εμμονή με το πέρασμα του χρόνου όταν είδε την πατρίδα του να καταστρέφεται από τα πλάσματα του Κενού. Αντί να χάσει χρόνο θρηνώντας την απώλεια της πατρίδας του...", "allytips": ["Μπορείτε να συνδυάσετε τη χρήση της ικανότητας Ωρολογιακή Βόμβα και της ικανότητας Επιστροφή για να τοποθετήσετε γρήγορα δύο ωρολογιακές βόμβες σε έναν στόχο. Αν τοποθετηθεί μια δεύτερη βόμβα θα εκραγεί η πρώτη και θα ακινητοποιηθούν όλοι οι κοντινοί εχθροί.", "Η ικανότητα Διαστρέβλωση χρόνου αποτελεί έναν καλό τρόπο να επιτρέψετε στους συμμάχους να αποτελειώσουν τους εχθρούς ή να ξεφύγουν από μια μάχη που πρόκειται να χάσουν.", "Η ικανότητα Χρονομετατόπιση αποθαρρύνει τις επιθέσεις στους αδύναμους Ήρωες, αλλά αν τη χρησιμοποιήσετε πολύ νωρίς σε μια μάχη, θα επιτρέψει στον εχθρό να αλλάξει στόχο νωρίτερα, μειώνοντας με αυτόν τον τρόπο την αποτελεσματικότητά της."], "enemytips": ["Αν μπορείτε να φτάσετε την ταχύτητα του Ζίλεαν, μερικές φορές είναι προτιμότερο να περιμένετε την απόλυτή του να φύγει προτού να επιτύχετε το τελειωτικό χτύπημα.", "Ο Ζίλεαν είναι πολύ ευάλωτος όταν μια ομάδα εστιάζει πάνω του, κατά τα άλλα όμως είναι δύσκολο να σκοτωθεί. Οργανωθείτε σαν ομάδα για να τον σκοτώσετε μαζί."], "tags": ["Support", "Mage"], "partype": "Μάνα", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "Ωρολογιακή Βόμβα", "description": "Εκτοξεύει μια βόμβα στην περιοχή-στόχ<PERSON> η οποία κολλάει σε οποιαδήποτε μονάδα βρεθεί κοντά της (με προτεραιότητα στους Ήρωες). Η βόμβα ανατινάσσεται μετά από 3 δευτερόλεπτα, προκαλώντας ζημιά πεδίου επιρροής. Αν μια Ωρολογιακή Βόμβα προκαλέσει νωρίτερα την έκρηξη μιας άλλης Ωρολογιακής Βόμβας, οι εχθροί θα ακινητοποιηθούν.", "tooltip": "Ο Ζίλεαν πετάει μια βόμβα με χρονοκαθυστέρηση, η οποία κολλάει στην πρώτη μονάδα που θα πλησιάσει εντός μιας μικρής κυκλικής περιοχής γύρω της. Μετά από {{ e2 }} δευτ. ανατινάζεται, προκαλώντας <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage>.<br /><br />Αν τοποθετήσει μια δεύτερη βόμβα σε μια μονάδα που έχει ήδη βόμβα, τότε η πρώτη βόμβα θα ανατιναχτεί αμέσως και θα <status>Ακινητοποιήσει</status> τους εχθρούς εντός της έκρηξης για {{ e4 }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@", "Ζημιά", "Διάρκ<PERSON><PERSON><PERSON> Ακινητοποίησης:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZileanW", "name": "Επιστροφή", "description": "Ο Ζίλεαν μπορεί να προετοιμαστεί για μελλοντικές αναμετρήσεις, μειώνοντας τους Χρόνους Επαναφόρτισης των άλλων βασικών ικανοτήτων του.", "tooltip": "Ο Ζίλεαν επηρεάζει τη ροή του χρόνου, μειώνοντας τους Χρόνους Επαναφόρτισης όλων των άλλων βασικών ικανοτήτων του κατά {{ e2 }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Μάνα", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }} Μάνα"}, {"id": "TimeWarp", "name": "Διαστρέβλωση Χρόνου", "description": "Ο Ζίλεαν διαστρεβλώνει το χρόνο γύρω από μια μονάδα, μειώνοντας την Ταχύτητα Κίνησης ενός εχθρού ή αυξάνοντας την Ταχύτητα Κίνησης ενός συμμάχου για μικρό χρονικό διάστημα.", "tooltip": "Ο Ζίλεαν <status>Επιβραδύνει</status> έναν αντίπαλο Ήρωα κατά {{ e2 }}% ή δίνει σε έναν σύμμαχο Ήρωα <speed>Ταχύτητα Κίνησης {{ e2 }}%</speed> για {{ e1 }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Επιβράδυνση", "Ταχύτητα Κίνησης"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ChronoShift", "name": "Χρονομετατόπιση", "description": "Ο Ζίλεαν τοποθετεί σε ένα σύμμαχο Ήρωα έναν προστατευτικό ρούνο χρόνου, ο οπ<PERSON>ίος τηλεμεταφέρει τον Ήρωα πίσω στον χρόνο στην περίπτωση που δεχτεί θανάσιμη ζημιά.", "tooltip": "Ο Ζίλεαν δίνει έναν προστατευτικ<PERSON> ρούνο του χρόνου σε έναν σύμμαχο Ήρωα για {{ rduration }} δευτ. Αν ο στόχος πρόκειται να πεθάνει, ο ρούνος επαναφέρει τη ροή του χρόνου και τον θέτει σε Καταστολή για {{ revivestateduration }} δευτ. Στη συνέχεια, τον ανασταίνει και αναπληρώνει <healing>{{ rtotalheal }} Ζωή</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@", "Θεραπεία"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Εμφ<PERSON>α<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ο Ζίλεαν αποθηκεύει χρόνο ως Εμπειρία, που μπορεί να μεταφέρει στους συμμάχους του. Όταν έχει αρκετή Εμπειρία, μπορεί να ανεβάσει το επίπεδο ενός συμμάχου, κάνοντας δεξί κλικ επάνω του για να του τη μεταφέρει. Ο Ζίλεαν αποκτά όση Εμπειρία προσφέρει.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}