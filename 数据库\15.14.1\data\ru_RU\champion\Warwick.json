{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "title": "Освобожденный гнев Зауна", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "Серый Варвик", "chromas": true}, {"id": "19002", "num": 2, "name": "<PERSON>и<PERSON><PERSON>й Урфик", "chromas": false}, {"id": "19003", "num": 3, "name": "Злой и страшный Варвик", "chromas": false}, {"id": "19004", "num": 4, "name": "Северный охотник Варвик", "chromas": false}, {"id": "19005", "num": 5, "name": "Оди<PERSON><PERSON><PERSON><PERSON><PERSON> Варвик", "chromas": false}, {"id": "19006", "num": 6, "name": "Варвик Огненный Клык", "chromas": false}, {"id": "19007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON> Варвик", "chromas": false}, {"id": "19008", "num": 8, "name": "Маро<PERSON><PERSON><PERSON> Варвик", "chromas": false}, {"id": "19009", "num": 9, "name": "Урфик", "chromas": false}, {"id": "19010", "num": 10, "name": "Лунн<PERSON>й страж Варвик", "chromas": true}, {"id": "19016", "num": 16, "name": "ПРОЕКТ: Варвик", "chromas": true}, {"id": "19035", "num": 35, "name": "Старый бог Варвик", "chromas": false}, {"id": "19045", "num": 45, "name": "Избранник зимы Варвик", "chromas": false}, {"id": "19046", "num": 46, "name": "Избранник зимы Варвик (престижный)", "chromas": false}, {"id": "19056", "num": 56, "name": "Подопытный Варвик из ''Аркейна''", "chromas": false}], "lore": "Варвик – чудовище, которое охотится среди серой мглы заунских улиц. В ходе страшного эксперимента его тело было сращено с затейливой системой насосов и емкостей. Машины вливают в него алхимическую злобу. Выпрыгивая из тени, он нападает на преступников, держащих в страхе обитателей городского дна. Варвика привлекает кровь – от ее запаха он сходит с ума. Тому, кто ее прольет, уже не спастись от Гнева Зауна.", "blurb": "Варвик – чудовище, которое охотится среди серой мглы заунских улиц. В ходе страшного эксперимента его тело было сращено с затейливой системой насосов и емкостей. Машины вливают в него алхимическую злобу. Выпрыгивая из тени, он нападает на преступников...", "allytips": ["Идите по следам Кровавой охоты, ведущим к чемпионам, у которых осталось мало здоровья.", "Дальность использования Бесконечной жестокости [R] зависит от скорости передвижения, поэтому она увеличивается даже от усилений союзников и заклинаний призывателя.", "При удержании клавиши Челюстей зверя [Q], Варвик будет следовать за врагом, даже если он совершит рывок или телепортируется."], "enemytips": ["Если Варвик серьезно ранен, его автоатаки восстанавливают здоровье. Сохраняйте ослабляющие эффекты для добивания.", "Варвик наносит увеличенный урон врагам, у которых осталось мало здоровья. Следите за своим здоровьем, чтобы не дать Варвику преимущество.", "Дальность использования абсолютного умения Варвика зависит от его скорости передвижения."], "tags": ["Fighter", "Tank"], "partype": "Мана", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "Челюсти зверя", "description": "Варвик бросается вперед и кусает цель, нанося урон от максимального запаса здоровья цели и восстанавливая себе здоровье в зависимости от нанесенного урона.", "tooltip": "<tap>Нажатие:</tap> Варвик совершает выпад вперед и кусает цель, нанося ей <magicDamage>магический урон в размере {{ basebitedamage }} плюс {{ targetpercenthpdamage }}% от максимального запаса здоровья</magicDamage>, а также восстанавливая себе <healing>здоровье в размере {{ e3 }}% от нанесенного урона</healing>.<br /><br /><hold>Удерживание:</hold> Варвик совершает выпад вперед, хватается челюстями за цель и перепрыгивает ее. При этом он следует за целью, куда бы та ни переместилась. После этого он наносит такой же урон и лечится на ту же величину.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Лечение", "Урон от % здоровья", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ e3 }}% -> {{ e3NL }}%", "{{ targetpercenthpdamage }}% -> {{ targetpercenthpdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "WarwickW", "name": "Кровавая охота", "description": "Варвик чует врагов, у которых осталось меньше 50% здоровья. Его скорость передвижения увеличивается при движении к раненым целям, а скорость атаки – при атаках на них. Если у них остается меньше 25% здоровья, он впадает в бешенство, и эти эффекты усиливаются втрое.", "tooltip": "<spellPassive>Пассивно:</spellPassive> Варвик чувствует чемпионов, у которых осталось менее 50% здоровья, и получает <speed>{{ passivemsbonus }}% скорости передвижения</speed> при движении к ним. Умения и автоатаки против врагов, у которых осталось менее 50% здоровья, увеличивают <speed>скорость атаки</speed> Варвика на <speed>{{ passiveasbonus }}%</speed>. Эти бонусы увеличиваются на 200% против врагов, у которых осталось менее 25% здоровья.<br /><br /><spellActive>Активно:</spellActive> Варвик может кратковременно учуять всех врагов, получая пассивный эффект этого умения против ближайшего чемпиона на 8 сек. независимо от его здоровья. Если чемпионы не найдены, перезарядка этого умения сокращается на 30%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Скорость передвижения", "Скорость атаки", "Перезарядка"], "effect": ["{{ passivemsbonus }}% -> {{ passivemsbonusNL }}%", "{{ passiveasbonus }}% -> {{ passiveasbonusNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "WarwickE", "name": "Первобытный вой", "description": "В течение 2,5 сек. Варвик получает меньше урона. По окончании действия умения или при повторном использовании Варвик издает вой, заставляя ближайших врагов бежать в страхе в течение 1 сек.", "tooltip": "Варвик получает на {{ e1 }}% меньше урона в течение 2.5 сек. После этого он воет, <status>пугая</status> врагов поблизости на {{ e3 }} сек. Варвик может <recast>повторно применить</recast> это умение, чтобы завершить его преждевременно.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Уменьшение урона", "Перезарядка"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "WarwickR", "name": "Бесконечная жестокость", "description": "Варвик бросается в выбранном направлении (дальность рывка зависит от скорости передвижения) и парализует первого чемпиона на пути на 1.5 сек.", "tooltip": "Варвик прыгает на большое расстояние, которое зависит от его <speed>скорости передвижения</speed>. Столкнувшись с чемпионом, он <status>подавляет</status> его на {{ rduration }} сек. В течение этого времени Варвик атакует цель 3 раза, нанося <magicDamage>{{ damagecumulative }} магического урона</magicDamage>. Варвик восстанавливает <healing>здоровье в размере 100% от всего урона</healing>, нанесенного во время действия умения.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Вечная жажда", "description": "Автоатаки Варвика наносят дополнительный магический урон. Если у Варвика осталось меньше 50% здоровья, он дополнительно лечится на ту же величину. Если у Варвика осталось меньше 25% здоровья, количество восстанавливаемого здоровья увеличивается в три раза.", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}