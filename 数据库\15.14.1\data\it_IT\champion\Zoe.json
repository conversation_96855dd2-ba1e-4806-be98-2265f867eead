{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zoe": {"id": "<PERSON>", "key": "142", "name": "<PERSON>", "title": "l'incarnazione del crepuscolo", "image": {"full": "Zoe.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "142000", "num": 0, "name": "default", "chromas": false}, {"id": "142001", "num": 1, "name": "Cyber-pop Zoe", "chromas": false}, {"id": "142002", "num": 2, "name": "Zoe Festa in Piscina", "chromas": true}, {"id": "142009", "num": 9, "name": "<PERSON>", "chromas": true}, {"id": "142018", "num": 18, "name": "<PERSON>", "chromas": false}, {"id": "142019", "num": 19, "name": "<PERSON> (edizione prestigio)", "chromas": false}, {"id": "142020", "num": 20, "name": "Zoe <PERSON>", "chromas": true}, {"id": "142022", "num": 22, "name": "<PERSON> dell'Inverno", "chromas": true}, {"id": "142033", "num": 33, "name": "<PERSON>", "chromas": true}], "lore": "Incarnazione di malizia, immaginazione e cambiamento, <PERSON> è la messaggera cosmica di Targon. La sua comparsa è portatrice di avvenimenti in grado di plasmare mondi interi. La sua semplice presenza, infatti, a volte può distorcere gli equilibri metafisici alla base della realtà fino a causare cataclismi, senza alcuna intenzione malevola. <PERSON><PERSON><PERSON> può forse spiegare la noncuranza con la quale Zoe assolve ai suoi doveri, cosa che le permette di dedicare tempo a giocare, ingannare i mortali o più generalmente divertirsi. Incontrare Zoe può essere un'epifania di gioia vitale, ma con un significato nascosto e spesso molto pericoloso.", "blurb": "Incarnazione di malizia, immaginazione e cambiamento, <PERSON> è la messaggera cosmica di Targon. La sua comparsa è portatrice di avvenimenti in grado di plasmare mondi interi. La sua semplice presenza, infatti, a volte può distorcere gli equilibri...", "allytips": ["Stella vagante infligge più danni in base alla distanza che percorre. Lanciarla alle tue spalle per poi deviarla può infliggere un sacco di danni.", "Interrompi il sonno con la tua fonte di danni più alta, perché i nemici addormentati subiscono il doppio dei danni.", "<PERSON>lla della nanna percorre più distanza oltre i muri. Trova un nascondiglio per preparare un'uccisione a lunga distanza."], "enemytips": ["Stella vagante di Zoe infligge più danni in base alla distanza che percorre.", "<PERSON> deve tornare al suo punto di partenza dopo aver lanciato Tuffo nel portale, il che la rende vulnerabile ai contrattacchi.", "Bolla della nanna copre più distanza oltre i muri. Impedisci a Zoe di nascondersi nella nebbia di guerra per evitare che prepari l'abilità."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 106, "mp": 425, "mpperlevel": 25, "movespeed": 340, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "Zoe<PERSON>", "name": "Stella vagante!", "description": "Zoe spara un proiettile che può deviare durante il volo. Infligge più danni in base a quanto vola in linea retta.", "tooltip": "Zoe spara una stella che infligge al primo nemico colpito e ai nemici circostanti danni che aumentano in base alla distanza che percorre: da <magicDamage>{{ totaldamagetooltip }} a {{ maxdamagetooltip }} danni magici</magicDamage>.<br /><br /><PERSON> <recast>rilanciare</recast> questa abilità per reindirizzare il missile verso una nuova posizione vicina a sé.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8.5, 8, 7.5, 7, 6.5], "cooldownBurn": "8.5/8/7.5/7/6.5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeQ.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZoeW", "name": "Furto magico", "description": "Zoe può raccogliere ciò che resta degli incantesimi dell'evocatore e dei lanci degli oggetti attivi e lanciarli nuovamente. Quando lancia un incantesimo dell'evocatore ottiene 3 proiettili che vengono sparati al bersaglio più vicino.", "tooltip": "<spellPassive>Passiva:</spellPassive> i nemici rilasciano frammenti magici quando lanciano un incantesimo dell'evocatore o usano un oggetto attivo. Inolt<PERSON>, alcuni minion rilasciano questi frammenti quando Zoe o un alleato vicino li uccide. Zoe può raccogliere un frammento per lanciarne l'abilità una volta.<br /><br /><spellPassive>Passiva:</spellPassive> quando Zoe lancia questa abilità o qualsiasi incantesimo dell'evocatore, guadagna <speed>{{ e9 }}% velocità di movimento</speed> per {{ e0 }} secondi e scaglia 3 proiettili all'ultimo bersaglio che ha attaccato. Ciascun missile infligge <magicDamage>{{ missiledamagetooltip }} danni magici</magicDamage>.<br /><br /><spellActive>Attiva:</spellActive> lancia l'abilità di un frammento magico raccolto da <PERSON>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> totali", "Velocità di movimento", "Durata velocità"], "effect": ["{{ totalbasedamage*3.000000 }} -> {{ totalbasedamagenl*3.000000 }}", "{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ movespeedduration }} -> {{ movespeeddurationNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3000, 4500, 6000, 0, 0], [0.1, 0.1, 0.1, 0.1, 0.1], [2500, 2500, 2500, 2500, 2500], [60, 60, 60, 60, 60], [20, 50, 80, 110, 140], [0.2, 0.2, 0.2, 0.2, 0.2], [0, 0, 0, 0, 0], [30, 40, 50, 60, 70], [2, 2.25, 2.5, 2.75, 3]], "effectBurn": [null, "0", "3000/4500/6000/0/0", "0.1", "2500", "60", "20/50/80/110/140", "0.2", "0", "30/40/50/60/70", "2/2.25/2.5/2.75/3"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [3000, 4500, 6000, 3000, 3000], "rangeBurn": "3000/4500/6000/3000/3000", "image": {"full": "ZoeW.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "ZoeE", "name": "<PERSON><PERSON>nna", "description": "Fa diventare sonnolento il bersaglio, per poi farlo addormentare. La resistenza magica del bersaglio è ridotta mentre sta dormendo. La prima fonte di danni che interrompe il sonno è raddoppiata, ma con un limite massimo di danni.", "tooltip": "Zoe calcia una bolla che infligge <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage> e rimane come trappola se non colpisce niente. La gittata della bolla aumenta quando supera un ostacolo.<br /><br /><PERSON><PERSON> dopo, la vittima si <status>addormenta</status> e la sua <scaleMR>resistenza magica</scaleMR> si riduce del {{ percentpen*100 }}% per 2 secondi. Gli attacchi e le abilità interrompono il sonno ma infliggono il doppio dei danni, fino a un massimo di <trueDamage>{{ breakdamagetooltip }} danni puri</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Limite danni bonus", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [0.1, 0.15, 0.2, 0.25, 0.3], [5, 15, 25, 35, 45], [5, 5, 5, 5, 5], [0.1, 0.15, 0.2, 0.25, 0.3], [70, 110, 150, 190, 230], [0.45, 0.45, 0.45, 0.45, 0.45], [1.4, 1.4, 1.4, 1.4, 1.4], [2.25, 2.25, 2.25, 2.25, 2.25], [1, 1, 1, 1, 1]], "effectBurn": [null, "70/110/150/190/230", "0.1/0.15/0.2/0.25/0.3", "5/15/25/35/45", "5", "0.1/0.15/0.2/0.25/0.3", "70/110/150/190/230", "0.45", "1.4", "2.25", "1"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeE.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZoeR", "name": "Tuffo nel portale", "description": "Ti teletrasporti in una posizione vicina per un secondo. Poi ti riteletrasporti indietro.", "tooltip": "<PERSON> si teletrasporta in una posizione vicina per un secondo, poi torna nella posizione d'origine. Durant<PERSON> l'effetto Zoe può lanciare abilità e attaccare, ma non può muoversi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [11, 8, 5], "cooldownBurn": "11/8/5", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [-0.3, -0.4, -0.5], [1.5, 2, 2.5], [4, 4, 4], [0.5, 0.5, 0.5], [3, 3, 3], [100, 200, 300], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "-0.3/-0.4/-0.5", "1.5/2/2.5", "4", "0.5", "3", "100/200/300", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575], "rangeBurn": "575", "image": {"full": "ZoeR.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> scintille!", "description": "Dopo aver lanciato un'abilità, il prossimo attacco base di Zoe infligge danni magici bonus.", "image": {"full": "Zoe_P.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}