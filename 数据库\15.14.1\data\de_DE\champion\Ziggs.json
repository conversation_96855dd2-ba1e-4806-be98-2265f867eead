{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ziggs": {"id": "<PERSON><PERSON>", "key": "115", "name": "<PERSON><PERSON>", "title": "der Hexplosions-Experte", "image": {"full": "Ziggs.png", "sprite": "champion5.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "115000", "num": 0, "name": "default", "chromas": false}, {"id": "115001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115002", "num": 2, "name": "Major <PERSON>", "chromas": false}, {"id": "115003", "num": 3, "name": "Poolparty<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115005", "num": 5, "name": "<PERSON>ster<PERSON><PERSON><PERSON> <PERSON><PERSON>", "chromas": false}, {"id": "115006", "num": 6, "name": "Schlachtboss<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115007", "num": 7, "name": "Odyssee<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "115014", "num": 14, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "115023", "num": 23, "name": "Hextech-Ziggs", "chromas": false}, {"id": "115024", "num": 24, "name": "BZZZiggs", "chromas": true}, {"id": "115033", "num": 33, "name": "La Ilusión Ziggs", "chromas": true}], "lore": "Mit seiner Vorliebe für große Bomben und kurze Lunten ist der Yordle Ziggs eine wahrlich explosive Naturgewalt. Die Assistentenstelle bei einem Erfinder in Piltover wurde ihm schnell zu langweilig und er freundete sich mit einer verrückten blauhaarigen Bombenlegerin namens Jinx an. Nach einer wilden Nacht in der Stadt befolgte Ziggs ihren Ratschlag und zog nach Zhaun. Dort frönt er ungestört seiner Faszination für alles Explosive und terrorisiert die Chem-Barone sowie normale Einwohner im ewigen Bestreben, etwas in die Luft zu jagen.", "blurb": "Mit seiner Vorliebe für große Bomben und kurze Lunten ist der Yordle Ziggs eine wahrlich explosive Naturgewalt. Die Assistentenstelle bei einem Erfinder in Piltover wurde ihm schnell zu langweilig und er freundete sich mit einer verrückten blauhaarigen...", "allytips": ["Sel<PERSON>t wenn du zu weit für einen Kampf entfernt bist, kannst du dennoch aus der Distanz mit „Megainferno-Bombe“ helfen.", "Verlangsame deine Gegner mit „Hexplosives Minenfeld“, um mit anderen Fähigkeiten leichter zu treffen.", "Die Möglichkeit, sich selbst mit „Sprengsatz“ über Hindernisse zu katapultieren, kann auf der Flucht oder bei einer Verfolgung nützlich sein."], "enemytips": ["Tritt nicht auf Ziggs' Minen! Sie verlangsamen dich, wodurch er dich mit seinen anderen Fähigkeiten viel leichter treffen kann.", "<PERSON><PERSON><PERSON> <PERSON>' Fähigkeiten besitzen lange Abklingzeiten. Versuche ihn zu erwischen, wenn er sie gerade erst eingesetzt hat.", "<PERSON><PERSON>' ultimative Fähigkeit, „Megainferno-Bombe“, verursacht im Zentrum der Explosion mehr Schaden."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 4}, "stats": {"hp": 606, "hpperlevel": 106, "mp": 480, "mpperlevel": 23.5, "movespeed": 325, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2, "attackspeed": 0.656}, "spells": [{"id": "ZiggsQ", "name": "Springende Bombe", "description": "<PERSON><PERSON> wirft eine springende Bombe, die magischen Schaden verursacht.", "tooltip": "<PERSON><PERSON> wirft eine springende Bombe, die <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit gesamter Fähigkeitsstärke", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio }} -> {{ apratioNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [850, 850, 850, 850, 850], [325, 325, 325, 325, 325], [225, 225, 225, 225, 225], [180, 180, 180, 180, 180], [240, 240, 240, 240, 240], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "850", "325", "225", "180", "240", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "ZiggsQ.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsW", "name": "Sprengsatz", "description": "<PERSON><PERSON> wirft eine Sprengladung, die nach kurzer Verzögerung oder bei erneuter Aktivierung dieser Fähigkeit detoniert. Die Explosion fügt Gegnern magischen Schaden zu und stößt sie weg. <PERSON><PERSON> wird ebenfalls weggestoßen, erleidet aber keinen Schaden. <br><br><PERSON><PERSON> kann die Sprengladung einsetzen, um stark beschädigte gegnerische Türme hexplodieren zu lassen.", "tooltip": "<PERSON><PERSON> schleudert eine explosive Ladung, die nach {{ bombduration }}&nbsp;Sekunden explodiert oder wenn diese Fähigkeit <recast>reaktiviert</recast> wird. Sie verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> an Gegnern und <status>stößt</status> sie <status>zurück</status>. <PERSON>iggs wird ebenfalls weggestoßen, erleidet aber keinen Schaden.<br /><br />„Sprengladung“ zerstört Türme unter {{ turretdestroypercent*100 }}&nbsp;% ihres Lebens automatisch.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Turmzerstörungsschwelle"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ turretdestroypercent*100.000000 }}&nbsp;% -> {{ turretdestroypercentnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "ZiggsW.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsE", "name": "Hexplosives Minenfeld", "description": "<PERSON>iggs verstreut Annäherungsminen, die bei Gegnerkontakt explodieren, magischen Schaden verursachen und verlangsamen. Aufeinanderfolgende Detonationen gegen dasselbe Ziel verursachen verringerten Schaden.", "tooltip": "<PERSON><PERSON> verstreut Annäherungsminen, die bei Kontakt mit Gegnern explodieren, <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursachen und {{ e4 }}&nbsp;Sekunden lang um {{ slow*-100 }}&nbsp;% <status>verlangsamen</status>. Minen bleiben {{ e3 }} Sekunden lang bestehen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Minenschaden", "Skalierung mit gesamter Fähigkeitsstärke", "Verlangsamung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ damagepermine }} -> {{ damagepermineNL }}", "{{ apratiopermine }} -> {{ apratiopermineNL }}", "{{ slow*-100.000000 }}&nbsp;% -> {{ slownl*-100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [10, 10, 10, 10, 10], [1.5, 1.5, 1.5, 1.5, 1.5], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [135, 135, 135, 135, 135], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "10", "1.5", "0.4", "0", "135", "150", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZiggsE.png", "sprite": "spell17.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsR", "name": "Megainferno-Bombe", "description": "<PERSON><PERSON> setzt seine ultimative Schöpfung ein – die Megainferno-Bombe – und schleudert sie über gewaltige Distanzen. Gegner im Hauptexplosionsbereich erleiden mehr magischen Schaden als weiter entfernte Gegner.", "tooltip": "<PERSON><PERSON> schleudert seine ultimative Schöpfung und verursacht <magicDamage>{{ empowereddamage }}&nbsp;magischen Schaden</magicDamage> im Zentrum des Explosionsradius oder <magicDamage>{{ blastdamage }}&nbsp;magischen <PERSON>haden</magicDamage> am Rand.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Detonationsbereich: <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*0.667000 }} -> {{ basedamagenl*0.667000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 95, 70], "cooldownBurn": "120/95/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [300, 450, 600], [66.6667, 66.6667, 66.6667], [525, 525, 525], [250, 250, 250], [200, 300, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "300/450/600", "66.67", "525", "250", "200/300/400", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "ZiggsR.png", "sprite": "spell17.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "In regelmäßigen Abständen verursacht Ziggs' nächster normaler Angriff zusätzlichen magischen Schaden. Die Abklingzeit verringert sich, wenn <PERSON> eine Fähigkeit einsetzt.", "image": {"full": "ZiggsPassiveReady.png", "sprite": "passive5.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}