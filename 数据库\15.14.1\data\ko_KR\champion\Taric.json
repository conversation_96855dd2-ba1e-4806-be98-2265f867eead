{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taric": {"id": "<PERSON><PERSON>", "key": "44", "name": "타릭", "title": "발로란의 방패", "image": {"full": "Taric.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "44000", "num": 0, "name": "default", "chromas": false}, {"id": "44001", "num": 1, "name": "에메랄드 타릭", "chromas": false}, {"id": "44002", "num": 2, "name": "다섯번째 성기사 타릭", "chromas": false}, {"id": "44003", "num": 3, "name": "혈석 타릭", "chromas": false}, {"id": "44004", "num": 4, "name": "수영장 파티 타릭", "chromas": true}, {"id": "44009", "num": 9, "name": "빛의 방패 타릭", "chromas": true}, {"id": "44018", "num": 18, "name": "우주 그루브 타릭", "chromas": true}, {"id": "44027", "num": 27, "name": "운명의 파괴자 타릭", "chromas": true}], "lore": "타릭은 초월적인 힘으로 룬테라의 생명과 사랑, 아름다움을 수호하는 수호자의 성위이다. 고국 데마시아에서 직무태만죄를 범하고 수치스럽게 추방당한 타릭은 속죄를 위해 타곤 산을 올랐고, 이 산 정상에서 하늘의 별들에게 더 큰 소명을 부여받았다. 고대 타곤의 신비한 힘을 가득 받아 끔찍한 공허의 침식으로부터 밤낮없이 세계를 지키는 발로란의 방패가 된 것이다.", "blurb": "타릭은 초월적인 힘으로 룬테라의 생명과 사랑, 아름다움을 수호하는 수호자의 성위이다. 고국 데마시아에서 직무태만죄를 범하고 수치스럽게 추방당한 타릭은 속죄를 위해 타곤 산을 올랐고, 이 산 정상에서 하늘의 별들에게 더 큰 소명을 부여받았다. 고대 타곤의 신비한 힘을 가득 받아 끔찍한 공허의 침식으로부터 밤낮없이 세계를 지키는 발로란의 방패가 된 것이다.", "allytips": ["담대함에 재사용 대기시간 감소 효과가 있기 때문에 타릭에게는 얼어붙은 심장, 얼어붙은 건틀릿, 정령의 형상 등의 아이템이 특히 좋습니다.", "별빛 손길은 충전량이 낮을 때 사용하면 마나 효율이 좋지 않지만 기본 지속 효과 덕분에 기본 공격이 강화되어 유용할 수 있습니다.", "우주의 광휘를 마지막 순간까지 아끼다가 아군이 처치당할 수도 있으니 팀 전투가 시작되기 직전에 미리 사용하는 편이 낫습니다."], "enemytips": ["타릭의 궁극기 우주의 광휘는 사용 후 실제 효과가 발동되기까지 시간이 조금 걸립니다. 타릭이 궁극기를 사용했다면 아예 일단 전투를 피하거나, 효과가 발동되기 전에 킬을 올리거나 둘 중 하나를 선택해야 합니다.", "타릭은 기본 지속 효과인 담대함 덕분에 스킬의 재사용 대기시간을 단축시킬 수 있습니다. 대규모 전투에서는 카이팅으로 대응하고, 공격로 전투에서는 타릭이 미니언을 처치하러 다가올 때를 노려보세요."], "tags": ["Support", "Tank"], "partype": "마나", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 40, "armorperlevel": 4.3, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "TaricQ", "name": "별빛 손길", "description": "충전량을 모두 소모해 근처의 아군 챔피언을 회복시킵니다.<br><br>담대함으로 강화된 기본 공격 시 충전 대기시간이 줄어듭니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> {{ stackcooldown }}초마다, <spellName>담대함</spellName> 기본 공격이 적중할 때마다 중첩을 1 얻습니다. (최대 {{ e6 }})<br /><br /><spellActive>사용 시:</spellActive> 모든 중첩을 소모해 중첩당 근처 아군 챔피언의 <healing>체력을 {{ healingperstack }}</healing>씩 회복시킵니다. ({{ e6 }}회 중첩 시 <healing>{{ maxstackhealing }}</healing>){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최대 충전량", "최대 회복량"], "effect": ["{{ e6 }} -> {{ e6NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [1, 1, 1, 1, 1], [0.15, 0.3, 0.45, 0.6, 0.75], [0.75, 1.5, 2.25, 3, 3.75], [25, 50, 75, 100, 125], [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "1", "0.15/0.3/0.45/0.6/0.75", "0.75/1.5/2.25/3/3.75", "25/50/75/100/125", "1/2/3/4/5", "0", "0", "0", "0"], "vars": [], "costType": ", 현재 충전량", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "TaricQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "마나 {{ cost }}, 현재 충전량"}, {"id": "TaricW", "name": "수호의 고리", "description": "수호의 고리로 연결된 아군 챔피언과 타릭의 방어력이 상승합니다.<br><br>연결된 아군 챔피언이 타릭 근처에 있는 동안 수호의 고리를 사용하면 연결된 아군에게 보호막을 씌웁니다. 타릭이 스킬을 사용하면 연결된 아군도 같은 스킬을 사용합니다.", "tooltip": "<spellPassive>기본 지속 효과: </spellPassive>타릭이 <scaleArmor>{{ bonusarmor }}의 방어력</scaleArmor>을 얻고 자신과 이 스킬로 묶인 아군 사이에 끈을 형성합니다. 서로 가까이 있으면 아군이 <scaleArmor>{{ bonusarmor }}의 방어력</scaleArmor>을 얻으며 타릭과 연결된 아군 둘 다 타릭의 모든 스킬을 사용합니다.<br /><br /><spellPassive>사용 시: </spellPassive>타릭이 아군 챔피언 하나와 묶이며 {{ e3 }}초 동안 <shield>최대 체력의 {{ e2 }}%에 해당하는 보호막</shield>을 부여합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 방어력", "보호막 계수"], "effect": ["{{ armorbonuspercentage*100.000000 }}% -> {{ armorbonuspercentagenl*100.000000 }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [7, 8, 9, 10, 11], [2.5, 2.5, 2.5, 2.5, 2.5], [1000, 1000, 1000, 1000, 1000], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "7/8/9/10/11", "2.5", "1000", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "TaricW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TaricE", "name": "황홀한 강타", "description": "타릭이 천상의 별빛을 준비해 잠시 후 적에게 마법 피해를 입히고 기절시킵니다.", "tooltip": "타릭이 별빛 광선을 발사합니다. 광선은 {{ e3 }}초 후 터지며 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 적을 {{ e2 }}초 동안 <status>기절</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [90, 130, 170, 210, 250], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/130/170/210/250", "1.5", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "TaricE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TaricR", "name": "우주의 광휘", "description": "잠시 후 근처의 아군 챔피언을 모두 우주의 에너지로 감싸 잠깐 동안 무적 상태로 만듭니다.", "tooltip": "타릭이 천상의 보호를 요청합니다. {{ initialdelay }}초 후 근처 아군 챔피언은 {{ invulnduration }}초 동안 무적 상태가 됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "TaricR.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "담대함", "description": "타릭이 스킬을 사용하면 다음 2회의 기본 공격에 추가 마법 피해가 적용되고 공격 속도가 빨라지며 스킬의 재사용 대기시간이 단축됩니다.", "image": {"full": "Taric_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}