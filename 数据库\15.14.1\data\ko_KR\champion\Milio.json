{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Milio": {"id": "<PERSON><PERSON>", "key": "902", "name": "밀리오", "title": "온화한 불꽃", "image": {"full": "Milio.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "902000", "num": 0, "name": "default", "chromas": false}, {"id": "902001", "num": 1, "name": "요정 왕국 밀리오", "chromas": true}, {"id": "902011", "num": 11, "name": "비의 인도자 밀리오", "chromas": true}], "lore": "밀리오는 이쉬탈 출신의 마음 따뜻한 소년으로, 어린 나이에 불의 액시옴을 숙달하고 '치유의 불꽃'이라는 새로운 능력을 발견했다. 그리고 자신의 할머니가 그랬듯, 이 새로운 능력을 활용해 윤 탈에 들어가서 가족의 옛 영광을 되찾으려고 한다. 이쉬탈 전역의 정글을 여행하며 결국 수도 이샤오칸에 도착한 밀리오는 윤 탈에 들어가기 위해 비달리온을 준비한다. 어떤 고난과 위험이 자신을 기다리는지 모르는 채로.", "blurb": "밀리오는 이쉬탈 출신의 마음 따뜻한 소년으로, 어린 나이에 불의 액시옴을 숙달하고 '치유의 불꽃'이라는 새로운 능력을 발견했다. 그리고 자신의 할머니가 그랬듯, 이 새로운 능력을 활용해 윤 탈에 들어가서 가족의 옛 영광을 되찾으려고 한다. 이쉬탈 전역의 정글을 여행하며 결국 수도 이샤오칸에 도착한 밀리오는 윤 탈에 들어가기 위해 비달리온을 준비한다. 어떤 고난과 위험이 자신을 기다리는지 모르는 채로.", "allytips": ["밀리오는 모든 스킬을 잘 활용하려면 근처에 아군이 함께 있어야 합니다.", "밀리오는 이동 속도가 증가하면 도약 속도도 증가합니다. 적을 깜짝 놀라게 해 보세요.", "위험해 보이는 상황도 충분히 즐길 수 있는 게 밀리오입니다."], "enemytips": ["밀리오의 이동 스킬은 모두 착지 지점을 미리 확인할 수 있으니 이 점을 활용하세요.", "밀리오을 상대할 때는 빠르게 군중 제어 스킬을 사용할 수 있는 챔피언이 특히 유리합니다.", "밀리오는 주위에 아군이 없으면 기동력이 크게 떨어집니다. 혼자 있을 때를 노리세요."], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "MilioQ", "name": "초특급 불꽃 킥", "description": "적을 밀쳐내는 공을 걷어찹니다. 적중한 공은 위로 날아가 적을 향해 떨어지며 충돌한 영역 주변에 있는 적들에게 피해를 입히고 둔화시킵니다.", "tooltip": "밀리오가 불꽃 공을 걷어차 처음으로 맞힌 적을 <status>밀어냅니다</status>. 적중한 공은 적들 사이를 튕기며 폭발해 주변 적에게 <magicDamage>{{ damage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ slowamountpercent }} <status>둔화</status>시킵니다.<br /><br /><spellName>초특급 불꽃 킥</spellName> 스킬을 한 명 이상의 적 챔피언에게 적중시키면 소모한 마나의 {{ refundratio*100 }}%를 돌려받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "마나 소모량"], "effect": ["{{ falldamage }} -> {{ falldamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "MilioQ.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MilioW", "name": "아늑한 모닥불", "description": "아군을 회복시키고 공격 사거리를 증가시키는 구역을 만듭니다. 이 구역은 스킬을 사용한 지점에서 가장 가까운 아군을 따라다닙니다.", "tooltip": "밀리오가 {{ zoneduration }}초 동안 아군 챔피언을 따라가는 온기를 생성합니다. 지속시간 동안 근처 아군 챔피언의 공격 사거리가 {{ rangepercent }} 증가하고 <healing>{{ healingovertime }}의 체력</healing>을 회복합니다. 또한, 온기는 {{ healfrequencyseconds }}초마다 <spellName>타오르는 힘</spellName> 효과를 적용합니다.<br /><br /><recast>재사용 시:</recast> 온기가 따라가는 아군을 변경합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["사거리", "회복량", "재사용 대기시간", "마나 소모량"], "effect": ["{{ rangepctincrease*100.000000 }}% -> {{ rangepctincreasenl*100.000000 }}%", "{{ totalhealingovertime }} -> {{ totalhealingovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [29, 27, 25, 23, 21], "cooldownBurn": "29/27/25/23/21", "cost": [90, 100, 110, 120, 130], "costBurn": "90/100/110/120/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "MilioW.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MilioE", "name": "따스한 포옹", "description": "아군 한 명에게 보호막을 부여해 잠시 동안 이동 속도를 높여줍니다. 이 스킬은 2회까지 충전됩니다.", "tooltip": "밀리오가 아군 챔피언을 보호의 불길로 감싸 <shield>{{ shieldcalc }}의 피해를 흡수하는 보호막</shield>을 부여하고 {{ movespeedduration }}초 동안 <speed>이동 속도를 {{ movespeedamount*100 }}%</speed> 증가시킵니다.<br /><br />이 스킬은 2회까지 충전되고 동일 대상에게 효과가 중첩됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "이동 속도", "마나 소모량", "재충전 시간"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "2", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MilioE.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MilioR", "name": "생명의 온기", "description": "치유의 불꽃을 보내 범위 내 아군의 체력을 회복시키고 군중 제어 효과를 제거합니다.", "tooltip": "밀리오가 근처 아군 챔피언에게 치유의 불꽃을 보내 <status>방해</status> 및 <status>이동 불가</status> 효과를 정화하고 <healing>{{ healcalc }}의 체력</healing>을 회복시키며 {{ tenacityduration }}초 동안 {{ tenacityamount*100 }}% 강인함을 부여합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "재사용 대기시간"], "effect": ["{{ healbase }} -> {{ healbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 145, 130], "cooldownBurn": "160/145/130", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MilioR.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "타오르는 힘", "description": "밀리오의 스킬에 닿은 아군은 다음 공격으로 큰 추가 피해를 입히고 대상을 불태웁니다.", "image": {"full": "Milio_P.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}