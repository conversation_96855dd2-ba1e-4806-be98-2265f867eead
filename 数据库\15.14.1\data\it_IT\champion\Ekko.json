{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ekko": {"id": "<PERSON><PERSON><PERSON>", "key": "245", "name": "<PERSON><PERSON><PERSON>", "title": "il ragazzo che ha infranto il tempo", "image": {"full": "Ekko.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "245000", "num": 0, "name": "default", "chromas": false}, {"id": "245001", "num": 1, "name": "<PERSON><PERSON><PERSON> Sabbia", "chromas": true}, {"id": "245002", "num": 2, "name": "Ekko dell'Accademia", "chromas": false}, {"id": "245003", "num": 3, "name": "PROGETTO: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "245011", "num": 11, "name": "Ekko SKT T1", "chromas": false}, {"id": "245012", "num": 12, "name": "<PERSON><PERSON><PERSON> o <PERSON>", "chromas": true}, {"id": "245019", "num": 19, "name": "Ekko True Damage", "chromas": true}, {"id": "245028", "num": 28, "name": "Ekko <PERSON>", "chromas": true}, {"id": "245036", "num": 36, "name": "<PERSON><PERSON><PERSON>: Firelight", "chromas": true}, {"id": "245045", "num": 45, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "245046", "num": 46, "name": "<PERSON><PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "245056", "num": 56, "name": "Ekko True Damage Breakout", "chromas": false}, {"id": "245057", "num": 57, "name": "<PERSON><PERSON><PERSON>: Resistenza", "chromas": true}], "lore": "<PERSON>k<PERSON> è un prodigio che viene dalle pericolose strade di Zaun, in grado di manipolare il tempo per volgere qualsiasi situazione a proprio vantaggio. Usando la sua invenzione, il Motore-Z, esplora le varie possibilità della realtà per realizzare il momento perfetto e fare l'impossibile, con facilità, sempre al primo tentativo. Per quanto sia uno spirito libero, se i suoi cari sono in pericolo è pronto a tutto per difenderli con l'aiuto dei Firelight.", "blurb": "<PERSON>k<PERSON> è un prodigio che viene dalle pericolose strade di Zaun, in grado di manipolare il tempo per volgere qualsiasi situazione a proprio vantaggio. Usando la sua invenzione, il Motore-Z, esplora le varie possibilità della realtà per realizzare il...", "allytips": ["Frattura temporale è un potente strumento di fuga, ma può rivelarsi forte anche se usato in attacco. Non sottovalutate il suo potenziale di danni.", "Se è possibile attivare Risonanza del Motore-Z su un campione nemico, vaIe la pena di accollarsi dei rischi per farlo. Il bonus alla velocità di movimento facilita la fuga.", "Lo scatto di Salto fasico è un ottimo strumento per preparare le altre abilità di Ekko. Usatelo per ottenere il doppio colpo con Cronodisco o mettervi in posizione per detonare Convergenza parallela."], "enemytips": ["Ekko è molto più debole quando la sua suprema non è pronta. Guardando la scia che lascia si può capire se Frattura temporale è disponibile.", "La zona di stordimento di Ekko ha bisogno di 3 secondi per essere attivata. Osservate l'immagine che crea al lancio e cercate di indovinare dove è stata piazzata la zona.", "Il secondo colpo di Cronodisco fa più danni del primo. Cercate di evitarlo."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 280, "mpperlevel": 70, "movespeed": 340, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.688}, "spells": [{"id": "EkkoQ", "name": "Cronodisco", "description": "<PERSON>kko lancia una granata temporale che esplode, creando un campo di distorsione temporale se colpisce un campione nemico, rallentando e danneggiando tutte le unità nemiche al suo interno. Dopo un periodo di tempo la granata si riavvolge e torna da Ekko, infliggendo danni lungo il percorso.", "tooltip": "Ekko lancia un dispositivo che infligge <magicDamage>{{ initialdamage }} danni magici</magicDamage>. Quando colpisce un campione o raggiunge la gittata massima, genera un campo che <status>rallenta</status> del {{ e2 }}% i nemici al suo interno. In seguito, Ekko lo richiama infliggendo <magicDamage>{{ recalldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON> in uscita", "Rallentamento", "<PERSON><PERSON> al ritorno"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ outgoingdamage }} -> {{ outgoingdamageNL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ returndamage }} -> {{ returndamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 85, 100, 115, 130], [40, 45, 50, 55, 60], [40, 65, 90, 115, 140], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [165, 165, 165, 165, 165], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/85/100/115/130", "40/45/50/55/60", "40/65/90/115/140", "100", "0", "165", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EkkoQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoW", "name": "Convergenza parallela", "description": "Gli attacchi base di Ekko infliggono danni magici bonus ai nemici con poca salute. Ekko può lanciare Convergenza parallela per dividere la linea temporale, creando un'anomalia dopo pochi secondi che rallenta i nemici al suo interno. Se Ekko entra nell'anomalia, ottiene uno scudo e stordisce i nemici sospendendoli nel tempo.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi base di Ekko contro i nemici con meno del 30% di salute residua infliggono <magicDamage>danni magici pari al {{ missinghealthpercent }} della salute mancante</magicDamage>.<br /><br /><spellActive>Attiva:</spellActive> Ekko lancia una cronosfera che dura 1,5 secondi dopo un ritardo e <status>rallenta</status> i nemici al suo interno del {{ e0 }}%. Se Ekko entra nella sfera, la fa esplodere <status>stordendo</status> i nemici per {{ e2 }} secondi e guadagnando uno <shield>scudo da {{ totalshield }}</shield>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [375, 375, 375, 375, 375], [2.25, 2.25, 2.25, 2.25, 2.25], [3, 3, 3, 3, 3], [100, 120, 140, 160, 180], [150, 150, 150, 150, 150], [15, 15, 15, 15, 15], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [40, 40, 40, 40, 40]], "effectBurn": [null, "375", "2.25", "3", "100/120/140/160/180", "150", "15", "1.5", "3", "2", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1600, 1600, 1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "EkkoW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoE", "name": "Salto fasico", "description": "Ekko esegue una manovra evasiva mentre carica il suo Motore-Z. Il suo prossimo attacco infligge danni bonus e distorce la realtà, teletrasportandolo verso il suo bersaglio.", "tooltip": "<PERSON><PERSON><PERSON> scatta e potenzia il suo prossimo attacco base, che ottiene gittata bonus, lo teletrasporta verso il bersaglio e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [350, 350, 350, 350, 350], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "350", "3", "300", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "EkkoE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoR", "name": "Frattura temporale", "description": "<PERSON>k<PERSON> distrugge la sua linea temporale, diventando non bersagliabile e tornando indietro nel tempo in un momento più favorevole. Ritorna dove si trovava pochi secondi prima, guarisce di una percentuale dei danni ricevuti in quel periodo e infligge danni ingenti ai nemici nella sua zona di arrivo.", "tooltip": "Ekko torna indietro nel tempo, entra in stasi e si teletrasporta dov'era 4 secondi prima, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici vicini. Inoltre, Ekko si cura di <healing>{{ totalbaseheal }} salute</healing>, più il {{ percenthealampperpercentmissinghealth }}% per ogni 1% di salute persa negli ultimi 4 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ flatheal }} -> {{ flathealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 80, 50], "cooldownBurn": "110/80/50", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850], "rangeBurn": "850", "image": {"full": "EkkoR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Risonanza del Motore-Z", "description": "Ogni terzo attacco o abilità che infligge danni allo stesso bersaglio infligge danni magici bonus e conferisce a Ekko un aumento della velocità se il bersaglio è un campione.<br><br>", "image": {"full": "Ekko_P.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}