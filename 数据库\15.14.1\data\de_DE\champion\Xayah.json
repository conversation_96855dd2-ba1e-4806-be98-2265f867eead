{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Xayah": {"id": "<PERSON><PERSON><PERSON>", "key": "498", "name": "<PERSON><PERSON><PERSON>", "title": "die Rebellin", "image": {"full": "Xayah.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "498000", "num": 0, "name": "default", "chromas": false}, {"id": "498001", "num": 1, "name": "Kosmischer Nachtschatten Xayah", "chromas": false}, {"id": "498002", "num": 2, "name": "Herzblatt-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "498003", "num": 3, "name": "SSG-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "498004", "num": 4, "name": "Sternenwächterin X<PERSON>", "chromas": true}, {"id": "498008", "num": 8, "name": "Ahnenholz<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498017", "num": 17, "name": "Tapferer Phönix-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498026", "num": 26, "name": "Tapferer Phönix-<PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "498028", "num": 28, "name": "Arkana<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "498037", "num": 37, "name": "Gebrochener Pakt-X<PERSON>h", "chromas": true}, {"id": "498038", "num": 38, "name": "Erlöste Sternenwächterin Xayah", "chromas": false}, {"id": "498047", "num": 47, "name": "Battle Bat Xayah", "chromas": true}, {"id": "498057", "num": 57, "name": "Academia Certaminis-<PERSON><PERSON>h", "chromas": true}], "lore": "Xayah ist eine vastayanische Revolutionärin, die tödlich und präzise ihren persönlichen Krieg führt, um ihr Volk zu retten. Mit ihrer Geschwindigkeit, List und messerscharfen Federklingen bringt sie alle zu Fall, die sich ihr in den Weg stellen. Xayah kämpft an der Seite ihres Partners und Geliebten, <PERSON><PERSON>, um ihren schwindenden Stamm zu beschützen und ihrem Volk wieder zu alter Größe zu verhelfen.", "blurb": "Xayah ist eine vastayanische Revolutionärin, die tödlich und präzise ihren persönlichen Krieg führt, um ihr Volk zu retten. Mit ihrer Geschwindigkeit, List und messerscharfen Federklingen bringt sie alle zu Fall, die sich ihr in den Weg stellen. Xayah...", "allytips": ["Xayahs Attacken und Fähigkeiten hinterlassen Federn auf dem Boden. Später kann sie diese für enormen Flächenschaden und -Kontrolle zurückrufen.", "<PERSON><PERSON><PERSON> kann „Tosende Federn“ nutzen, um viele Federn zu verteilen und nahezu jeder Fähigkeit auszuweichen. Nutze diese Fähigkeit sowohl für den Angriff als auch zur Verteidigung."], "enemytips": ["<PERSON><PERSON><PERSON> „Klingenrufer“ hält nur Ziele fest, die von 3 oder mehr zurückkehrenden Federn getroffen werden.", "<PERSON> im gleichen Gebiet geben Xayah die Möglichkeit, eine große Anzahl von Federn fallen zu lassen. <PERSON><PERSON><PERSON> daher, immer in Bewegung zu bleiben!", "<PERSON><PERSON> sic<PERSON>, dass du auch wirklich bereit bist, wenn du sie dir schnappen willst. Da sie dank „Tosende Federn“ kurzzeitig nicht anvisiert werden kann, kann diese Fähigkeit das Blatt bei einem Hinterhalt schnell zu Xayahs Gunsten wenden."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 6, "magic": 1, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 340, "mpperlevel": 40, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.25, "hpregenperlevel": 0.75, "mpregen": 8.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.9, "attackspeed": 0.658}, "spells": [{"id": "XayahQ", "name": "Zwillingsfedern", "description": "<PERSON><PERSON>h wirft 2 <PERSON><PERSON><PERSON>, die Schaden verursachen und zudem Federn hinterlassen, die sie zu sich zurückrufen kann.", "tooltip": "<PERSON><PERSON><PERSON> wirft 2&nbsp;<PERSON><PERSON><PERSON>, die <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursachen und 2&nbsp;<keywordMajor>Federn</keywordMajor> zurücklassen. Nach dem ersten getroffenen Ziel erleidet jedes weitere <physicalDamage>{{ multihitdamage }}&nbsp;Schaden</physicalDamage> von jedem Dolch.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [45, 60, 75, 90, 105], [0.5, 0.5, 0.5, 0.5, 0.5], [0.334, 0.334, 0.334, 0.334, 0.334], [0.584, 0.584, 0.584, 0.584, 0.584], [3500, 3500, 3500, 3500, 3500], [3500, 3500, 3500, 3500, 3500], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/60/75/90/105", "0.5", "0.33", "0.58", "3500", "3500", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "XayahQ.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XayahW", "name": "<PERSON>erwi<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> erzeugt einen Klingenwirbel, der ihr Angriffstempo und den verursachten Schaden erhöht und ihr außerdem bei Angriffen gegen Champions Lauftempo gewährt.", "tooltip": "Xayah erschafft {{ e2 }}&nbsp;Sekunden lang einen Klingensturm, der ihr <attackSpeed>{{ e1 }}&nbsp;% Angriffstempo</attackSpeed> verleiht. Ihre Angriffe feuern außerdem eine zweite Klinge ab, die {{ bonusdamagepercent }}&nbsp;% Schaden verursacht.<br /><br />Wenn die zweite Klinge einen Champion trifft, erhält Xayah {{ e4 }}&nbsp;Sekunden lang <speed>{{ e3 }}&nbsp;% Lauftempo</speed>.<br /><br />Wenn <PERSON> sich in der Nähe befindet, werden ihm ebenfalls die Effekte dieser Fähigkeit gewährt, allerdings erhält er <speed>Lauftempo</speed>, wenn <i>Xayah</i> ein Ziel trifft.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }}&nbsp;% -> {{ e1NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [4, 4, 4, 4, 4], [30, 30, 30, 30, 30], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 20, 20, 20, 20], [1000, 1000, 1000, 1000, 1000], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "4", "30", "1.5", "20", "1000", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XayahW.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XayahE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> ruft alle fallengelassenen Federn zu sich zurück, fügt ihren Gegnern dabei Schaden zu und hält sie fest.", "tooltip": "<PERSON><PERSON><PERSON> ruft alle <keywordMajor>Federn</keywordMajor> zu sich zurück. Sie verursachen dabei jeweils <physicalDamage>{{ featherdamage }}&nbsp;normalen Schaden</physicalDamage>. Wenn {{ featherthreshold }} oder mehr <keywordMajor>Federn</keywordMajor> einen <PERSON> treffen, wird er {{ rootduration }}&nbsp;Sekunden lang <status>festgehalten</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "XayahE.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XayahR", "name": "Tosende Federn", "description": "<PERSON><PERSON><PERSON> springt in die Luft und kann nicht anvisiert werden. Dann lässt sie es <PERSON>lche regnen, die Federn hinterlassen, welche sie wieder zu sich zurückrufen kann.", "tooltip": "<PERSON><PERSON><PERSON> springt in die Luft, kann 1,5&nbsp;Sekunden lang nicht anvisiert werden und erhält „Geist“. Daraufhin lässt sie Dol<PERSON> niederregnen, die <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> verursachen und eine Reihe von <keywordMajor>Federn</keywordMajor> hinterlassen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "XayahR.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Messerscharfe Federn", "description": "Nachdem Xayah eine Fähigkeit eingesetzt hat, treffen ihre nächsten normalen Angriffe alle Ziele auf ihrem Weg und lassen eine <font color='#C200E1'>Feder</font> zurück.", "image": {"full": "XayahPassive.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}