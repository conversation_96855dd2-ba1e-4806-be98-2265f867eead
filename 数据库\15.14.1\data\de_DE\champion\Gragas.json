{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gragas": {"id": "Gragas", "key": "79", "name": "Gragas", "title": "der Unruhestifter", "image": {"full": "Gragas.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "79000", "num": 0, "name": "default", "chromas": false}, {"id": "79001", "num": 1, "name": "<PERSON>cher-G<PERSON><PERSON>", "chromas": false}, {"id": "79002", "num": 2, "name": "Hinterwäldler-Gragas", "chromas": false}, {"id": "79003", "num": 3, "name": "Weihnachts-Gragas", "chromas": false}, {"id": "79004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "79005", "num": 5, "name": "Vandalen-Gragas", "chromas": false}, {"id": "79006", "num": 6, "name": "Oktoberfest-Gragas", "chromas": false}, {"id": "79007", "num": 7, "name": "Superfan-Gragas", "chromas": false}, {"id": "79008", "num": 8, "name": "Fnatic-Gragas", "chromas": false}, {"id": "79009", "num": 9, "name": "Gragas Fassbrecher", "chromas": false}, {"id": "79010", "num": 10, "name": "Schneeschlag-Gragas", "chromas": false}, {"id": "79011", "num": 11, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "79020", "num": 20, "name": "Weltraum-Groove-Gragas", "chromas": true}, {"id": "79029", "num": 29, "name": "High Noon-Gragas", "chromas": true}, {"id": "79039", "num": 39, "name": "Musikfan <PERSON>", "chromas": true}], "lore": "Gragas ist zu gleichen Teilen heiter und imposant, und ein gewaltiger, rauflustiger Braumeister, der immer nach neuen Möglichkeiten sucht, alle bei Laune zu halten. Er stammt aus unbekannten Landen und ist im unberührten Freljord auf der Suche nach Zutaten, um sein neuestes Gebräu zu perfektionieren. Er ist impulsiv, stur und bekannt für seine Schlägereien, die oft in nächtlichem Gelage und großem Sachschaden enden. Jedes Auftauchen von Gragas ist ein sicheres Zeichen für Spaß und Zerstörung – in dieser Reihenfolge.", "blurb": "Gragas ist zu gleichen Teilen heiter und imposant, und ein gewaltiger, rauflustiger Braumeister, der immer nach neuen Möglichkeiten sucht, alle bei Laune zu halten. Er stammt aus unbekannten Landen und ist im unberührten Freljord auf der Suche nach...", "allytips": ["Die Schadensverringerung durch „Betrunkene Wut“ tritt ein, sobald du zu trinken beginnst. Versuche sie ein<PERSON>zen, wenn Schaden zu erwarten ist.", "<PERSON><PERSON><PERSON> mit „Explosives Fass“ zu deinen Türmen zu schleudern.", "Versuche „Killerplauze“ und „Explosives Fass“ zu kombinieren, um Kills für dein Team verbuchen zu können."], "enemytips": ["Gragas kann mit seiner ultimativen Fähigkeit alle wegschleudern. Pass auf, dass du nicht zu ihm geschleudert wirst, oder schlimmer noch: in einen gegnerischen Turm.", "„Killerplauze“ hat eine sehr kurze Abklingzeit, wodurch man G<PERSON> nur schwer verfolgen kann. Vergeude deine Energien also lieber nicht, indem du ihm hinterher rennst."], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 115, "mp": 400, "mpperlevel": 47, "movespeed": 330, "armor": 38, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.05, "attackspeed": 0.675}, "spells": [{"id": "GragasQ", "name": "G<PERSON>lltes Fass", "description": "Gragas rollt sein Fass zu einem Zielort. Das Fass kann entweder manuell zur Explosion gebracht werden oder es explodiert nach 4 Sekunden automatisch. Die Stärke der Explosion nimmt mit der Zeit zu<PERSON> G<PERSON>, die von der Explosion erfasst werden, werden verlangsamt.", "tooltip": "Gragas rollt ein Fass, das nach {{ e4 }}&nbsp;Sekunden explodiert. Die Explosion verursacht zwischen <magicDamage>{{ mindamage }}</magicDamage> und <magicDamage>{{ maxdamage }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> {{ e3 }}&nbsp;Sekunden lang um {{ e2 }} bis {{ effect2amount*1.5 }}&nbsp;%. Der Schaden und die <status>Verlangsamung</status> erhöhen sich mit Vorbereitungszeit, bevor das Fass explodiert. <br /><br />Gragas kann die Fähigkeit <recast>reaktivieren</recast>, um das Fass vorzeitig zur Explosion zu bringen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [40, 45, 50, 55, 60], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [2, 2, 2, 2, 2], [150, 150, 150, 150, 150], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "40/45/50/55/60", "2", "4", "2", "150", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "GragasQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasW", "name": "<PERSON><PERSON><PERSON>", "description": "Gragas probiert 1 Sekunde lang sein neuestes Gebräu. <PERSON><PERSON> verfällt er in einen wilden und aggressiven <PERSON>, verursacht mit seinem nächsten normalen Angriff magischen Schaden an allen nahen Gegnern und erleidet selbst weniger Schaden.", "tooltip": "Gragas probiert sein <PERSON>, verringert den erlittenen Schaden {{ defenseduration }}&nbsp;Sekunden lang um {{ damagereduction }} und verstärkt seinen nächsten Angriff, sodass er dem Ziel und umliegenden Gegnern zusätzlichen <magicDamage>magischen Schaden</magicDamage> in <PERSON><PERSON><PERSON> von <magicDamage>{{ totaldamage }} plus {{ maxhppercentdamage }}&nbsp;% des maximalen Lebens</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schadensverringerung", "<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamagereduction }}&nbsp;% -> {{ basedamagereductionNL }}&nbsp;%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "GragasW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "G<PERSON>s stü<PERSON>t zum Zielort, rammt den ersten Gegner, der ihm über den Weg läuft, mit seinem Bauch, verursacht an allen nahen Gegnern Schaden und betäubt sie.", "tooltip": "Gragas stürmt los und rammt den ersten Gegner. Gegner in der Nähe werden dadurch {{ stunduration }}&nbsp;Sekunden lang <status>hochgeschleudert</status> und erleiden <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>.<br /><br />Die Abklingzeit dieser Fähigkeit wird um {{ cooldownrefund*100 }}&nbsp;% verringert, wenn Gragas mit einem Gegner zusammenstößt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GragasE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasR", "name": "Explosives Fass", "description": "G<PERSON>s schleudert sein F<PERSON> zum Zielort, veru<PERSON>cht Schaden und stößt getroffene Gegner im Explosionsradius zurück.", "tooltip": "G<PERSON>s schleudert sein <PERSON>, wodurch Gegner im Landebereich <status>weggestoßen</status> werden und <magicDamage>{{ damagedone }}&nbsp;magischen Schaden</magicDamage> erleiden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GragasR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Freibier", "description": "Gragas heilt sich regel<PERSON>, wenn er eine Fähigkeit einsetzt.", "image": {"full": "GragasPassiveHeal.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}