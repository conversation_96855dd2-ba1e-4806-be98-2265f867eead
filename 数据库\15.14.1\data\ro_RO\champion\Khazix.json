{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Khazix": {"id": "Khazix", "key": "121", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "prădătorul din Vid", "image": {"full": "Khazix.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "121000", "num": 0, "name": "default", "chromas": false}, {"id": "121001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "121002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, str<PERSON><PERSON> al nisipurilor", "chromas": false}, {"id": "121003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, floarea mor<PERSON>ii", "chromas": false}, {"id": "121004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>, steaua întunecată", "chromas": false}, {"id": "121011", "num": 11, "name": "Kha'Zix de la CM 2018", "chromas": true}, {"id": "121060", "num": 60, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> O<PERSON>", "chromas": false}, {"id": "121069", "num": 69, "name": "K<PERSON><PERSON><PERSON><PERSON> străjer al lunii", "chromas": false}, {"id": "121079", "num": 79, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "''Vidul crește, Vidul se adaptează'', iar <PERSON><PERSON>'<PERSON><PERSON> e întruparea perfectă a acestei ideologii. Această creatură oribilă aflată în continuă mutație este mânată de instinctul de a evolua, fiind născută pentru a supraviețui și a-i ucide pe cei puternici. Când se întâmplă să întâmpine obstacole, își dezvoltă mereu o modalitate mai eficientă de a-și contra și ucide victimele. Deși Kha'Zix a fost la început o bestie fără minte, inteligența i-a evoluat odată cu trupul. Acum, creatura își plănuiește cu grijă vânătorile și se folosește până și de teroarea pe care o sădește în sufletele victimelor sale.", "blurb": "''Vidul crește, Vidul se adaptează'', iar <PERSON><PERSON><PERSON>Z<PERSON> e întruparea perfectă a acestei ideologii. Această creatură oribilă aflată în continuă mutație este mânată de instinctul de a evolua, fiind născută pentru a supraviețui și a-i ucide pe cei puternici...", "allytips": ["Inamicii sunt considerați a fi izolați dacă nu au niciun aliat în imediata lor apropiere. Daunele provocate de ''Gustă-le frica'' cresc considerabil împotriva acestor ținte.", "''Amenințarea nevăzută'' se activează atunci când Kha'Zix nu poate fi văzut de echipa inamică. Reactiveaz-o cu ajutorul tufișurilor sau cu ''Asaltul Vidului''. Ca să profiți de efectul ''Amenințării nevăzute'', folosește-ți atacul de bază împotriva campionilor inamici.", "Kha'Zix poate alege locul și momentul următoarei lupte. Pentru a obține victoria, trebuie să intre doar în luptele care-l avantajează."], "enemytips": ["''Gustă-le frica'' provoacă daune bonus țintelor care sunt izolate. Câștigă un avantaj luptând lângă minioni, campioni sau turnuri aliate.", "''Salt'' și ''Asaltul Vidului'' au timpi de reactivare mari. Kha'Zix este foarte vulnerabil când aceste abilități nu sunt disponibile."], "tags": ["Assassin"], "partype": "Mană", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 6}, "stats": {"hp": 643, "hpperlevel": 99, "mp": 327, "mpperlevel": 40, "movespeed": 350, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.59, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.668}, "spells": [{"id": "KhazixQ", "name": "Gustă-le frica", "description": "Kha'Zix îi provoacă țintei daune fizice. Daunele cresc în cazul țintelor <font color='#FFF673'>izolate</font>. Dacă alege să <PERSON>ze <font color='#00DD33'>''Evoluție: gheare''</font>, aceasta va returna un procent din timpul său de reactivare la folosirea abilității asupra țintelor <font color='#FFF673'>izolate</font>. În plus, crește raza de acțiune a atacurilor de bază și a abilității ''Gustă-le frica''.", "tooltip": "Kha'Z<PERSON> lovește un inamic, provocându-i <physicalDamage>{{ spell.khazixq:basedamage }} daune fizice</physicalDamage>. Provoacă <physicalDamage>{{ spell.khazixq:isodamage }} daune</physicalDamage> împotriva inamicilor <keywordMajor>i<PERSON><PERSON><PERSON><PERSON></keywordMajor> de aliații lor. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [80, 105, 130, 155, 180], [110, 110, 110, 110, 110], [50, 50, 50, 50, 50], [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/105/130/155/180", "110", "50", "45", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "KhazixQ.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixW", "name": "Spinul Vidului", "description": "Kha'Zix aruncă spini care explodează și le provoacă daune fizice inamicilor loviți. Dacă se află și el în raza exploziei, Kha'Zix este vindecat. Dacă alege să <PERSON>ze <font color='#00DD33'>''Evoluție: șiruri de spini''</font>, ''Spinul Vidului'' va arunca trei spini într-o zonă în formă de con, va încetini inamicii loviți și va dezvălui campionii inamici loviți timp de 2 secunde. Țintele <font color='#FFF673'>izolate</font> sunt încetinite suplimentar.", "tooltip": "Kha'Zix aruncă un spin care provoacă <physicalDamage>{{ basedamage }} daune fizice</physicalDamage> într-o zonă mică din jurul primului inamic lovit. Dacă se află în interiorul zonei, Kha'Z<PERSON> își reface <healing>{{ healamount }} viață</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Vindecare", "Cost de @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [55, 75, 95, 115, 135], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "55/75/95/115/135", "40", "2", "0", "2", "60", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KhazixW.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixE", "name": "Salt", "description": "Kha'Zix sare într-o zonă și provoacă daune fizice la aterizare. Dacă alege să <PERSON>ze <font color='#00DD33'>''Evoluție: aripi''</font>, raza ''Saltului'' va crește cu 200, iar timpul său de reactivare se va reseta la fiecare ucidere sau participare la ucidere de campion.", "tooltip": "<PERSON><PERSON>'<PERSON><PERSON> sare și provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> la aterizare.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [30, 35, 40, 45, 50], [10, 10, 10, 10, 10], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "30/35/40/45/50", "10", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KhazixE.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixR", "name": "Asaltul Vidului", "description": "La fiecare nivel nou, <PERSON><PERSON><PERSON><PERSON><PERSON> își poate evolua una dintre abilități, oferindu-i astfel un efect suplimentar unic. La activare, Kha'Zix devine <font color='#91d7ee'>invizibil</font> și primește efectul ''Amenințare nevăzută'', precum și un bonus la viteza de mișcare. Dacă alege <font color='#00DD33'>''Evoluție: adaptare cameleonică''</font>, ''Asaltul Vidului'' poate fi folosit încă o dată, iar durata <font color='#91d7ee'>invizibilității</font> crește.", "tooltip": "<spellActive>Activă:</spellActive> Kha'Zix devine <keywordStealth>invizibil</keywordStealth> timp de {{ stealthduration }} secunde și primește <speed>{{ bonusmovementspeedpercent*100 }}% viteză de mișcare</speed>. Kha'Zix poate <recast>refolosi</recast> această abilitate o dată în decurs de {{ recastwindow }} secunde.<br /><br /><spellPassive>Pasivă:</spellPassive> când crește în nivel această abilitate, Kha'Zix poate <evolve>evolua</evolve> una dintre abilitățile sale, oferindu-i efecte suplimentare.<li><spellName>Gustă-le frica:</spellName> mărește raza de acțiune a abilității, mărește raza de atac și reduce timpul de reactivare cu {{ spell.khazixq:effect4amount }}% împotriva țintelor <keywordMajor>izolate</keywordMajor>.<li><spellName>Spinul Vidului:</spellName> aruncă 3 spini și <status>încetinește</status> cu {{ spell.khazixw:effect3amount }}%, valoare crescută împotriva țintelor <keywordMajor>izolate</keywordMajor>.<li><spellName>Salt:</spellName> crește raza și reîmprospătează timpul de reactivare la doborârea campionilor.<li><spellName>Asaltul Vidului:</spellName> durata <keywordStealth>invizibilității</keywordStealth> crește la {{ evolvedstealthduration }} secunde, iar abilitatea poate fi <recast>refolosită</recast> încă o dată.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Evoluții disponibile", "Timp de reactivare"], "effect": ["{{ evolutionsavailable }} -> {{ evolutionsavailableNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "KhazixR.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Amenințare nevăzută", "description": "Inamicii din apropiere care sunt <font color='#FFF673'>i<PERSON><PERSON>ți</font> de aliații lor sunt însemnați. Abilitățile lui Kha'Zix au interacțiuni specifice cu țintele <font color='#FFF673'>izolate</font>.<br><br><PERSON><PERSON><PERSON> când Kha'Zix nu poate fi văzut de echipa inamică, primește efectul ''Amenințare nevăzută'', iar următorul său atac de bază asupra unui campion inamic va provoca daune magice bonus și îl va încetini timp de câteva secunde.", "image": {"full": "Khazix_P.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}