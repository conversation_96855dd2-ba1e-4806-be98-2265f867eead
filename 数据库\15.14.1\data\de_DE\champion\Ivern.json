{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ivern": {"id": "Ivern", "key": "427", "name": "Ivern", "title": "der Hüter der Haine", "image": {"full": "Ivern.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "427000", "num": 0, "name": "default", "chromas": false}, {"id": "427001", "num": 1, "name": "Zuckerkönig-Ivern", "chromas": false}, {"id": "427002", "num": 2, "name": "Dunk<PERSON>", "chromas": true}, {"id": "427011", "num": 11, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "427020", "num": 20, "name": "Astronauten-Ivern", "chromas": true}, {"id": "427030", "num": 30, "name": "Seelenblumen-Ivern", "chromas": false}], "lore": "<PERSON><PERSON>, bekannt als der Hüter der Haine, ist ein seltsame<PERSON>, halb <PERSON>, halb <PERSON>. Er durchstreift Runeterras Wälder und pflegt das Leben, wo immer er hingeht. Er kennt die Geheimnisse der Natur und ist ein Freund von allem, was w<PERSON><PERSON><PERSON>, fliegt und krabbelt. Ivern durchwandert die Wildnis und lehrt allen, denen er begegnet, seltsame Weisheiten. Die Bäume flüstern sich seine Geschichten zu und manchmal gibt er seine Geheimnisse sogar an tratschende Schmetterlinge weiter.", "blurb": "<PERSON><PERSON>, bekannt als der Hüter der Haine, ist ein seltsames Wesen, halb <PERSON>, halb <PERSON>. Er durchstreift Runeterras Wälder und pflegt das Leben, wo immer er hingeht. Er kennt die Geheimnisse der Natur und ist ein Freund von allem, was...", "allytips": ["<PERSON><PERSON>, das Beste aus einem guten „Wurzelru<PERSON>“ zu machen, indem du „Zündsamen“ auf sie wirkst!", "Bereite mit „Wunderwuchern“ zukünftige Hinterhalte vor!", "Blümchen kann Skillshots blocken und Gegner verlangsamen. <PERSON><PERSON><PERSON> sie, um Gegner von deinen Teamkollegen abzudrängen!"], "enemytips": ["<PERSON><PERSON><PERSON> einen <PERSON> ist Ivern ziemlich schwer zu fassen. Se<PERSON> vorsich<PERSON>g, wenn du ihm zu weit nachläufst.", "Iverns Gras hält lange an. Mach dich auf Hinterhalte gefasst!", "Se<PERSON> v<PERSON><PERSON><PERSON>g, wenn du dich Ivern allein stellst und er Blümchen zu Hilfe rufen kann!"], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 450, "mpperlevel": 60, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 475, "hpregen": 7, "hpregenperlevel": 0.85, "mpregen": 6, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 3.4, "attackspeed": 0.644}, "spells": [{"id": "IvernQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ivern ruft eine Ranke herbei, die an getroffenen Gegnern Schaden verursacht und sie festhält. Iverns Verbündete können zum festgehaltenen Ziel springen.", "tooltip": "Ivern beschwört eine <PERSON>, die <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und den ersten getroffenen Gegner {{ rootduration }}&nbsp;Sekunden lang <status>festhält</status>. <PERSON><PERSON><PERSON><PERSON>nde<PERSON>, die einen <status>festgehaltenen</status> Gegner angreifen, springen in Angriffsreichweite. <br /><br /><recast>Reaktivierung:</recast> Ivern stürzt sich direkt auf den <status>festgehaltenen</status> Gegner.<br /><br /><rules>Werden nicht-epische Monster getroffen, wird die Abklingzeit von <spellName><PERSON><PERSON><PERSON><PERSON>fer</spellName> um 50&nbsp;% verringert.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Festhaltedauer", "<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rootduration }} -> {{ rootdurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1125, 1125, 1125, 1125, 1125], "rangeBurn": "1125", "image": {"full": "IvernQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IvernW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Im hohen Gras verursachen die Angriffe von Ivern und seinen Verbündeten in der Nähe zusätzlichen magischen Schaden. Ivern kann diese Fähigkeit aktivieren, um eine kleine Grasfläche entstehen zu lassen.", "tooltip": "<spellPassive>Passiv:</spellPassive> Während Ivern sich im hohen Gras befindet oder innerhalb von {{ buffduration }}&nbsp;Se<PERSON><PERSON>, nachdem er es verlassen hat, verursachen seine Angriffe zusätzlich <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>. Nahe Verbündete erhalten diesen Effekt {{ allybuffduration }}&nbsp;Sekunden lang und verursachen <magicDamage>{{ totalallydamage }}&nbsp;magischen Schaden</magicDamage>.<br /><br /><spellActive>Aktiv:</spellActive> Ivern lässt eine kleine Grasfläche entstehen und deckt sie {{ revealduration }}&nbsp;Sekunden lang auf. Das Gras bleibt bestehen, bis Iverns Team die Sicht darin verliert, oder bis zu {{ maxbrushduration }}&nbsp;Sekunden lang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ma<PERSON><PERSON>", "Magischer Schaden durch Verbündete"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ allybasedamage }} -> {{ allybasedamageNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "3", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "IvernW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IvernE", "name": "Zündsamen", "description": "Ivern verleiht einem Verbündeten einen Schild, der nach kurzer Zeit explodiert, <PERSON><PERSON><PERSON> in der Nähe verlangsamt und ihnen Schaden zufügt. Der Schild wird zurückgesetzt, wenn er keine Gegner trifft.", "tooltip": "Ivern verleiht einem verbündeten Champion oder Blümchen einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ totalshield }}. Nach {{ shieldduration }}&nbsp;Sekunden explodiert der Schild, verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> Gegner {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%.<br /><br />Falls <spellName>Zündsamen</spellName> explodiert und keine gegnerischen Champions getroffen werden, während der Schild noch aktiv ist, erhält der Verbündete {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in Höhe von {{ totalshield }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "IvernE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IvernR", "name": "Blümchen!", "description": "<PERSON><PERSON> beschwört seine Wächterfreundin Blümchen, die an seiner Seite kämpft. Kann reaktiviert werden, um Blümchen zu befehlen, anzugreifen oder sich zu bewegen.", "tooltip": "Ivern beschwört {{ daisyduration }}&nbsp;Sekunden lang seine Wächterfreundin Blümchen.<br /><br /><spellActive><PERSON><PERSON><PERSON><PERSON><PERSON>, schlag zu!:</spellActive> Blümchens dritter Angriff in Folge gegen denselben Champion oder dasselbe epische Monster entfesselt eine Schockwelle, die allen getroffenen Gegnern <magicDamage>{{ totalshockwavedamage }}&nbsp;magischen Schaden</magicDamage> zufügt und sie {{ shockwaveccduration }}&nbsp;Sekunden lang <status>hochschleudert</status>. Dieser Effekt kann nur alle {{ shockwavecd }}&nbsp;Sekunden ausgelöst werden.<br /><br /><recast>Reaktivierung:</recast> Befiehlt Blümchen, anzugreifen oder sich zu bewegen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffsschaden", "<PERSON><PERSON><PERSON> der Schockwelle", "Blümchen: Zusätzliches Angriffstempo", "Abklingzeit"], "effect": ["{{ daisyad }} -> {{ daisyadNL }}", "{{ shockwavebasedamage }} -> {{ shockwavebasedamageNL }}", "{{ daisyas }}&nbsp;% -> {{ daisyasNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 130, 120], "cooldownBurn": "140/130/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "IvernR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Freund des Waldes", "description": "Ivern kann nicht-epische Monster nicht angreifen und auch nicht von ihnen angegriffen werden. Ivern kann magische Haine auf Dschungellager erschaffen, die mit der Zeit wachsen. Wenn der Hain ausgewachsen ist, kann Ivern die Monster freisetzen und erhält dadurch Gold und Erfahrung.", "image": {"full": "IvernP.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}