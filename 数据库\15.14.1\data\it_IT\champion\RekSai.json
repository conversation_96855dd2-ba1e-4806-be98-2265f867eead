{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"RekSai": {"id": "RekSai", "key": "421", "name": "<PERSON><PERSON><PERSON>", "title": "la predatrice del Vuoto", "image": {"full": "RekSai.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "421000", "num": 0, "name": "default", "chromas": false}, {"id": "421001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "421002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON>a in Piscina", "chromas": true}, {"id": "421009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "421017", "num": 17, "name": "Rek'Sai del Bosco Antico", "chromas": true}, {"id": "421026", "num": 26, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON><PERSON>, predatrice suprema, è una spietata creatura del Vuoto che si muove sottoterra per cogliere alla sprovvista le sue prede, per poi divorarle. La sua fame insaziabile ha ridotto in rovina intere regioni dell'antico impero di Shurima. Mercanti, viaggiatori ed eserciti allungavano la strada di centinaia di miglia, pur di evitare il suo territorio e quello dei suoi figli. Tutti sanno che quando Rek'Sai appare all'orizzonte, la morte dal basso è una garanzia.", "blurb": "<PERSON><PERSON><PERSON><PERSON>, predatrice suprema, è una spietata creatura del Vuoto che si muove sottoterra per cogliere alla sprovvista le sue prede, per poi divorarle. La sua fame insaziabile ha ridotto in rovina intere regioni dell'antico impero di Shurima. Mercanti...", "allytips": ["Lasciare delle gallerie in giro per la mappa ti garantirà varie scelte quando vorrai lanciare Velocità del Vuoto.", "Lancia Immersione prima di muoverti in giro per la mappa per sfruttare la velocità di movimento aumentata e la sicurezza fornita da Senso sismico.", "Senso sismico può farti notare i nemici in avvicinamento e si rivela molto utile quando entri nel territorio ostile."], "enemytips": ["Se vedi una delle gallerie di Rek'Sai, posizionati su una delle sue entrate per distruggerla.", "Morso furioso di Re<PERSON>'Sai guadagna danni mano a mano che Re<PERSON>'<PERSON> otti<PERSON>. Stai molto attento, quando la sua Furia è al massimo.", "Quando <PERSON>Sai è vicina può vedere la posizione tua e dei tuoi alleati, ma solo se vi state muovendo."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 600, "hpperlevel": 99, "mp": 100, "mpperlevel": 0, "movespeed": 340, "armor": 36, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.667}, "spells": [{"id": "RekSaiQ", "name": "<PERSON> regina / <PERSON><PERSON>", "description": "I prossimi 3 attacchi base di Rek'Sai infliggono danni fisici bonus ai nemici vicini.<br><br><PERSON><PERSON><PERSON> <PERSON> Sottoterra, <PERSON><PERSON><PERSON><PERSON> lancia una raffica di terra carica di energia del Vuoto, che danneggia e rivela i nemici colpiti.", "tooltip": "<keywordMajor>Allo scoperto:</keywordMajor> i prossimi 3 attacchi di Rek'Sai entro {{ buffduration }} secondi ottengono un <attackSpeed>{{ attackspeed*100 }}% di velocità d'attacco</attackSpeed> e infliggono <physicalDamage>{{ totaldamagetooltip }} danni fisici</physicalDamage> aggiuntivi ai nemici vicini. Gli attacchi ripristinano la durata di questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rapporto attacco fisico Ira della regina", "Ricarica Ira della regina", "<PERSON><PERSON>"], "effect": ["{{ unburrowedadratio*100.000000 }}% -> {{ unburrowedadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ burrowedbasedamage }} -> {{ burrowedbasedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 3.5, 3, 2.5, 2], "cooldownBurn": "4/3.5/3/2.5/2", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [12, 11, 10, 9, 8], [5, 5, 5, 5, 5], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "12/11/10/9/8", "5", "3", "300", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RekSaiQ.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "RekSaiW", "name": "Immersione / Emersione", "description": "<PERSON><PERSON>'<PERSON> sca<PERSON> e va sottoterra, ottenendo nuove abilità e un aumento della velocità di movimento. Il suo raggio visivo si riduce e non può usare gli attacchi base.<br><br><PERSON><PERSON><PERSON> Sottoterra, <PERSON><PERSON>'<PERSON> può lanciare Emersione per emergere, lanciando in aria e danneggiando i nemici circostanti.", "tooltip": "<keywordMajor>Allo scoperto:</keywordMajor> <PERSON><PERSON><PERSON><PERSON> scava e va sottoterra ottenendo nuove abilità, ma non può attaccare. Ottiene <speed>{{ burrowedmovespeed }} velocità di movimento</speed>, ha un raggio visivo ridotto di un {{ visionradiusmod*-100 }}% e la posizione dei nemici vicini che si muovono e che sarebbero altrimenti celati viene rivelata sia a Rek'Sai che ai suoi alleati.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento sottoterra"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ burrowedmovespeed }} -> {{ burrowedmovespeedNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [1500, 1500, 1500, 1500, 1500], [50, 85, 120, 155, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [10, 10, 10, 10, 10], [250, 250, 250, 250, 250], [175, 175, 175, 175, 175], [3, 3, 3, 3, 3], [0.3, 0.3, 0.3, 0.3, 0.3]], "effectBurn": [null, "1500", "50/85/120/155/190", "0", "0", "1", "10", "250", "175", "3", "0.3"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1650, 1650, 1650, 1650, 1650], "rangeBurn": "1650", "image": {"full": "RekSaiW.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "RekSaiE", "name": "Morso furioso / Galleria", "description": "<PERSON><PERSON><PERSON><PERSON> morde il suo bersaglio, infliggendo danni bonus se ha la Furia al massimo.<br><br><PERSON><PERSON><PERSON> <PERSON> sottoterra, <PERSON><PERSON>'<PERSON> crea una galleria duratura e riutilizzabile. I nemici possono distruggerla stando sopra uno dei due ingressi.", "tooltip": "<scaleAD>Allo scoperto:</scaleAD> <PERSON><PERSON><PERSON><PERSON> morde un bersaglio, infliggendo <physicalDamage>{{ spell.reksaie:basedamagetooltip }} danni fisici</physicalDamage>. Se è al massimo della <keywordMajor><PERSON><PERSON></keywordMajor>, il morso infligge <physicalDamage>{{ spell.reksaie:furymaxhealthdamage*100 }}% danni fisici in base alla salute massima aggiuntivi</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% salute massima", "Ricarica lancio Galleria", "Ricarica uso Galleria"], "effect": ["{{ furymaxhealthdamage*100.000000 }}% -> {{ furymaxhealthdamagenl*100.000000 }}%", "{{ dashcooldown }} -> {{ dashcooldownNL }}", "{{ tunnelreusecooldown }} -> {{ tunnelreusecooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "RekSaiE.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "RekSaiR", "name": "Velocità del Vuoto", "description": "Re<PERSON>'Sai applica passivamente un marchio ai bersagli quando li danneggia. Può attivare questa abilità per diventare temporaneamente non bersagliabile e balzare verso un bersaglio marchiato, infliggendo ingenti danni in base alla salute mancante.", "tooltip": "Re<PERSON>'<PERSON> bersaglia un nemico che ha danneggiato negli ultimi {{ preymarkduration }} secondi e poi va sottoterra, diventando non bersagliabile. Pochi istanti dopo balza inesorabilmente contro il bersaglio, infliggendo <physicalDamage>{{ rbasedamagecalc }} (+{{ percentmissinghealthdamage }}% della salute mancante del bersaglio) danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>uali salute mancante", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ percentmissinghealthdamage }} -> {{ percentmissinghealthdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "RekSaiR.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Furia degli X<PERSON>'Sai", "description": "Rek'Sai genera Furia attaccando e colpendo con le abilità base. La Furia viene consumata mentre è Sottoterra per recuperare salute.", "image": {"full": "RekSai_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}