{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sivir": {"id": "<PERSON><PERSON>", "key": "15", "name": "시비르", "title": "전장의 여제", "image": {"full": "Sivir.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "15000", "num": 0, "name": "default", "chromas": false}, {"id": "15001", "num": 1, "name": "전장의 공주 시비르", "chromas": false}, {"id": "15002", "num": 2, "name": "눈부신 시비르", "chromas": false}, {"id": "15003", "num": 3, "name": "사냥꾼 시비르", "chromas": false}, {"id": "15004", "num": 4, "name": "약탈자 시비르", "chromas": false}, {"id": "15005", "num": 5, "name": "PAX 시비르", "chromas": false}, {"id": "15006", "num": 6, "name": "눈꽃 시비르", "chromas": true}, {"id": "15007", "num": 7, "name": "심판자 시비르", "chromas": false}, {"id": "15008", "num": 8, "name": "승리의 시비르", "chromas": false}, {"id": "15009", "num": 9, "name": "네오 PAX 시비르", "chromas": false}, {"id": "15010", "num": 10, "name": "피자 배달원 시비르", "chromas": true}, {"id": "15016", "num": 16, "name": "핏빛달 시비르", "chromas": true}, {"id": "15025", "num": 25, "name": "오디세이 시비르", "chromas": true}, {"id": "15034", "num": 34, "name": "귀염둥이 카페 시비르", "chromas": true}, {"id": "15043", "num": 43, "name": "해를 삼킨 시비르", "chromas": true}, {"id": "15050", "num": 50, "name": "신화 창조자 시비르", "chromas": true}, {"id": "15051", "num": 51, "name": "프레스티지 신화 창조자 시비르", "chromas": false}, {"id": "15061", "num": 61, "name": "태고의 습격 시비르", "chromas": true}, {"id": "15070", "num": 70, "name": "15주년 시비르", "chromas": false}], "lore": "시비르는 슈리마 사막에서 활동하는 보물 사냥꾼이자 용병 대장이다. 몸값은 입이 떡 벌어질 정도로 비싸지만 전투를 벌이는 족족 이겨 몸값에 걸맞은 솜씨로 명성이 자자하다. 대담무쌍한 성격에 원대한 야심까지 겸비한 시비르. 그녀는 위험하기 짝이 없는 슈리마의 묘역에서 진귀한 보물을 찾으며 남다른 자부심을 느낀다. 의뢰인에게서 두둑한 대가를 챙기는 것은 물론이다. 하지만 슈리마에 고대의 존재들이 귀환하면서 시비르도 운명의 갈림길에 서게 된다.", "blurb": "시비르는 슈리마 사막에서 활동하는 보물 사냥꾼이자 용병 대장이다. 몸값은 입이 떡 벌어질 정도로 비싸지만 전투를 벌이는 족족 이겨 몸값에 걸맞은 솜씨로 명성이 자자하다. 대담무쌍한 성격에 원대한 야심까지 겸비한 시비르. 그녀는 위험하기 짝이 없는 슈리마의 묘역에서 진귀한 보물을 찾으며 남다른 자부심을 느낀다. 의뢰인에게서 두둑한 대가를 챙기는 것은 물론이다. 하지만 슈리마에 고대의 존재들이 귀환하면서 시비르도 운명의 갈림길에 서게 된다.", "allytips": ["시비르의 부메랑 검은 최대 사거리에 도달하면 다시 되돌아오므로 되돌아오는 위치를 계산하며 이동하여 적이 부메랑에 맞도록 유도할 수 있습니다.", "튕기는 부메랑 스킬은 시비르의 기본 공격 모션을 취소합니다. 기본 공격을 사용한 후 튕기는 부메랑 스킬을 사용하면 피해량을 최대화 할 수 있습니다.", "주문 방어막은 기절이나 속박 등 방해 효과가 있는 적 스킬에 대비하여 아껴 두세요."], "enemytips": ["부메랑 검은 많은 마나를 소모하므로 빗나갈 경우 시비르에게도 치명적입니다. 만약 부메랑에 맞았을 경우, 되돌아 올 때 다시 맞지 않도록 피하십시오.", "시비르는 강력한 압박형 챔피언입니다. 시비르가 있는 공격로를 너무 오랫동안 내버려두면 포탑이 순식간에 파괴됩니다.", "마법사 챔피언일 경우 앞으로 나가 스킬을 쓰는 척하다가 빠지는 방식으로 주문 방어막을 낭비하게 해 보세요."], "tags": ["Marksman"], "partype": "마나", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 104, "mp": 340, "mpperlevel": 45, "movespeed": 335, "armor": 30, "armorperlevel": 4.45, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SivirQ", "name": "부메랑 검", "description": "시비르가 십자날 검을 부메랑처럼 던져서 두 번 피해를 입힙니다.", "tooltip": "시비르가 십자날 검을 부메랑처럼 던져서 관통하는 모든 적 챔피언에게 <physicalDamage>{{ totaldamage }}</physicalDamage>의 피해를 입힙니다. 챔피언이 아닌 대상에게는 순차적으로 감소된 피해를 입힙니다. 피해량은 최소 {{ falloffminimum*100 }}%까지 내려갈 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "마나 소모량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "SivirQ.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SivirW", "name": "튕기는 부메랑", "description": "시비르의 다음 몇 번의 기본 공격은 공격 속도가 증가하고 첫 번째 목표물에 적중한 후 주위 적들에게 튕기며, 튕겼을 때는 감소된 피해를 입힙니다.", "tooltip": "{{ buffduration }}초 동안 시비르가 <attackSpeed>{{ ricochetattackspeed*100 }}%의 공격 속도</attackSpeed>를 획득하고 기본 공격이 강화되어 주위 적들에게 튕길 때마다 <physicalDamage>{{ bouncedamage }}의 물리 피해</physicalDamage>를 입힙니다. 공격은 최대 {{ maxbounces }}회 튕깁니다.<br /><br />공격이 치명타라면 튕긴 공격도 치명타를 가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["공격 속도", "총 공격력 %"], "effect": ["{{ ricochetattackspeed*100.000000 }}% -> {{ ricochetattackspeednl*100.000000 }}%", "{{ bounceadratio*100.000000 }}% -> {{ bounceadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirW.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SivirE", "name": "주문 방어막", "description": "시비르가 마법 보호막을 만들어 자신을 대상으로 한 적 스킬 1개를 막아냅니다. 성공적으로 방어 시 체력을 얻고 일시적으로 이동 속도가 대폭 증가합니다.", "tooltip": "시비르가 {{ e1 }}초간 주문 방어막을 만들어 적의 스킬을 막아냅니다. 적의 스킬을 방어하는 데 성공하면 시비르가 <healing>{{ totalheal }}의 체력</healing>을 회복하고 재빠른 발놀림을 발동합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["총 공격력 %", "재사용 대기시간"], "effect": ["{{ healratio*100.000000 }}% -> {{ healrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [1.5, 1.5, 1.5, 1.5, 1.5], [55, 55, 55, 55, 55], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5", "55", "60", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirE.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SivirR", "name": "사냥 개시", "description": "시비르가 전장에서 아군을 이끌며 잠시 동안 이동 속도를 대폭 상승시킵니다. 또한 공격 시 시비르의 스킬 재사용 대기시간이 감소합니다.", "tooltip": "시비르가 주위 아군을 이끌며 {{ ultduration }}초 동안 <speed>이동 속도를 {{ maxms*100 }}%</speed> 상승시킵니다.<br /><br />사냥 개시 활성화 중 챔피언에게 기본 공격을 가하면 시비르의 기본 스킬 재사용 대기시간이 0.5초 감소합니다.<br /><br />최근 피해를 입힌 적 처치에 관여하면 사냥 개시의 지속시간이 초기화됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "최대 이동 속도", "지속시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxms*100.000000 }}% -> {{ maxmsnl*100.000000 }}%", "{{ ultduration }} -> {{ ultdurationNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SivirR.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "재빠른 발놀림", "description": "시비르가 적 챔피언을 공격할 때 짧은 시간 동안 이동 속도가 대폭 상승합니다.", "image": {"full": "Sivir_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}