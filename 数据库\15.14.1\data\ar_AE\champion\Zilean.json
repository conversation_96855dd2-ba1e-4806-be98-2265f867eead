{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "زيليان", "title": "<PERSON>ا<PERSON><PERSON> الزمن", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "زيليان القديس العجوز", "chromas": false}, {"id": "26002", "num": 2, "name": "زيليان الرائع", "chromas": false}, {"id": "26003", "num": 3, "name": "زيليان صحراء شوريما", "chromas": false}, {"id": "26004", "num": 4, "name": "زيليان آلة الزمن", "chromas": false}, {"id": "26005", "num": 5, "name": "زيليان القمر الدامي", "chromas": false}, {"id": "26006", "num": 6, "name": "زيليان نشوة السكر", "chromas": true}, {"id": "26014", "num": 14, "name": "زيليان مباركة الشتاء", "chromas": true}], "lore": "بعد أن كان ساحرًا متنفذًا في إيكاثيا، أصبح زيليان مهووسًا بمرور الوقت بعد أن شهد تدمير الفويد لموطنه. ومع فويد قدرته على ادخار دقيقة حتى للحزن على الخسارة الكارثية، استدعى قوة سحر زمنية قديمة لاستطلاع كل النتائج الممكنة. وبعد أن أصبح خالدًا من الناحية الوظيفية، ينجرف زيليان اليوم عبر الماضي والحاضر والمستقبل، حيث يغير ويحور تدفق الزمن من حوله، ويبحث على الدوام عن تلك اللحظة المتملصة القادرة على إعادة الساعة وإبطال تدمير إيكاثيا.", "blurb": "بعد أن كان ساحرًا متنفذًا في إيكاثيا، أصبح زيليان مهووسًا بمرور الوقت بعد أن شهد تدمير الفويد لموطنه. ومع فويد قدرته على ادخار دقيقة حتى للحزن على الخسارة الكارثية، استدعى قوة سحر زمنية قديمة لاستطلاع كل النتائج الممكنة. وبعد أن أصبح خالدًا من الناحية...", "allytips": ["يمكن مزج استخدام القنبلة الزمنية وإعادة الزمن لوضع قنبلتين زمنيتين على هدف بسرعة. ويؤدي وضع القنبلة الثانية إلى تفجير الأولى، وصعق كل الأعداء في الجوار.", "يعد التشوه الزمني وسيلة فعالة لتمكين الحلفاء من الإجهاز على الأعداء، أو الهرب من معركة خاسرة.", "تعد النقلة الزمنية رادعًا قويًا لمن يهاجم أبطال الكاري، ولكن يمكن لإلقاء النقلة الزمنية في وقت أبكر من اللازم في القتال أن يجعل العدو يغير الأهداف بشكل أسرع، مما يجعلها أقل فاعلية."], "enemytips": ["إن كنت قادرًا على مضاهاة سرعة زيليان، فقد يكون من المفيد أحيانًا انتظار تلاشي قدرته الخارقة قبل توجيه الضربة القاتلة إليه.", "يتصف زيليان بالهشاشة إن ركز الفريق عليه، ولكن قتله سيكون صعبًا في حالات أخرى. فاسع إلى قتله مع أفراد الفريق."], "tags": ["Support", "Mage"], "partype": "المانا", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "القنبلة الزمنية", "description": "يرمي قنبلة على المنطقة المنشودة فتلتصق بالوحدات التي تقترب منها (الأولوية للأبطال). تنفجر القنبلة بعد 3 ثوان فتلحق ضرر منطقة تأثير. إذا تم تفجير القنبلة الزمنية مبكرًا بواسطة قنبلة زمنية أخرى فسوف تصعق الأعداء أيضًا.", "tooltip": "يلقي زيليان قنبلة موقوتة تلتصق بأول وحدة تقترب من المنطقة الضيقة حول القنبلة. تنفجر بعد {{ e2 }}ث، ما يلحق <magicDamage>{{ totaldamage }} ضرر سحر</magicDamage>.<br /><br />وضع قنبلة ثانية على وحدة مفخخة بالفعل يفجّر القنبلة الأولى مباشرة و <status>يُرعب</status> الانفجار الأعداءَ لمدة {{ e4 }}ث.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["فترة التبريد", "تكلفة @AbilityResourceName@", "الضرر", "مدة الصعق:"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ bombbasedamage }}-> {{ bombbasedamageNL }}", "{{ stunduration }}-> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZileanW", "name": "العودة بالزمن", "description": "يستطيع زيليان تحضير نفسه للمواجهات المستقبلية بتقليص فترتي تبريد قدرتيه الأساسيتين الأخريين.", "tooltip": "يعيد زيليان الزمن، ما يقلص فترات تبريد قدرته الأساسية بمعدل {{ e2 }}ث.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["فترة التبريد"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " مانا", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }} مانا"}, {"id": "TimeWarp", "name": "التشوه الزمني", "description": "يتلاعب زيليان بالزمن حول أي وحدة، فيخفض سرعة حركة الأعداء أو يزيد سرعة حركة أحد الحلفاء لفترة قصيرة.", "tooltip": "<status>يبطئ</status> زيليان بطل معادٍ بنسبة {{ e2 }}% أو يمنح بطل حليف <speed>{{ e2 }}% من سرعة الحركة</speed> لمدة {{ e1 }}ث.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الإبطاء", "سرعة الحركة"], "effect": ["{{ e2 }}%-> {{ e2NL }}%", "{{ e2 }}%-> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ChronoShift", "name": "النقلة الزمنية", "description": "يضع زيليان رون حماية زمني على بطل حليف، مما يعيد البطل بسرعة عبر الزمن في حال تعرضه إلى ضرر قاتل.", "tooltip": "يضع زيليان رون حماية زمني على بطل حليف لمدة {{ rduration }} من الثواني. إذا كان الهدف سيموت، يعيد الرون الخط الزمني إلى الوراء، ويضعهم في حالة السبات لمدة {{ revivestateduration }} من الثواني، ثم إنعاشهم واستعادة <healing>{{ rtotalheal }}صحتهم</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["فترة التبريد", "تكلفة @AbilityResourceName@", "العلاج"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ rbaseheal }}-> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "قارورة الزمن", "description": "يختزل زيليان الوقت كخبرة وبإمكانه منحه لحلفائه. عندما يمتلك ما يكفي من الخبرة لإتمام ترقية مستوى حليف، يصبح بوسعه الضغط بالزر الأيمن عليه لمنحه إياها. يحصل زيليان على نفس مقدار الخبرة الذي يمنحه.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}