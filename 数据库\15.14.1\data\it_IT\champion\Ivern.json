{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ivern": {"id": "Ivern", "key": "427", "name": "Ivern", "title": "l'anziano della foresta", "image": {"full": "Ivern.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "427000", "num": 0, "name": "default", "chromas": false}, {"id": "427001", "num": 1, "name": "Ivern Re dei Dolci", "chromas": false}, {"id": "427002", "num": 2, "name": "Ivern Maestro delle Schiacciate", "chromas": true}, {"id": "427011", "num": 11, "name": "Ivern Dio Antico", "chromas": true}, {"id": "427020", "num": 20, "name": "Ivern Astronauta", "chromas": true}, {"id": "427030", "num": 30, "name": "Ivern Fiore spirituale", "chromas": false}], "lore": "Ivern <PERSON>, noto a molti come l'anziano della foresta, è un particolare mezzo uomo, mezzo albero, che si aggira per le foreste di Runeterrra, coltivando la vita ovunque vada. Conosce i segreti del mondo naturale e coltiva una profonda amicizia con tutto ciò che cresce, vola e striscia. Ivern si aggira per la natura selvaggia, impartendo la sua strana saggezza a chiunque incontri, arricchendo le foreste e confidando i suoi segreti a quelle chiacchierone delle farfalle.", "blurb": "<PERSON><PERSON>, noto a molti come l'anziano della foresta, è un particolare mezzo uomo, mezzo albero, che si aggira per le foreste di Runeterrra, coltivando la vita ovunque vada. Conosce i segreti del mondo naturale e coltiva una profonda amicizia con...", "allytips": ["Cerca di aiutare gli alleati a dare seguito a un buon Capturadix con Seminascudum!", "Usa Silvaflorida per preparare i luoghi delle future imboscate!", "Daisy può bloccare i colpi mirati e rallentare i nemici. Usala per far allontanare i tuoi compagni!"], "enemytips": ["Ivern può rivelarsi ingannevolmente sfuggente. Attenzione a inseguirlo troppo lontano.", "L'erba alta di Ivern dura a lungo. Attenzione alle imboscate!", "Attenzione quando combatti da solo con Ivern, se ha Daisy pronta ad aiutarlo!"], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 450, "mpperlevel": 60, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 475, "hpregen": 7, "hpregenperlevel": 0.85, "mpregen": 6, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 3.4, "attackspeed": 0.644}, "spells": [{"id": "IvernQ", "name": "Capturadix", "description": "Ivern evoca un rampicante, infliggendo danni e immobilizzando i bersagli nemici colpiti. Gli alleati di Ivern possono scattare dal bersaglio immobilizzato.", "tooltip": "Ivern evoca un rampicante che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>immobilizza</status> il primo nemico colpito per {{ rootduration }} secondi. Gli alleati che attaccano un nemico <status>immobilizzato</status> scattano a portata d'attacco. <br /><br /><recast>Rilancio:</recast> Ivern scatta direttamente verso un nemico <status>immobilizzato</status>.<br /><br /><rules>Colpire dei mostri non epici riduce la ricarica di <spellName>Capturadix</spellName> del 50%.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Durata immobilizzazione", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rootduration }} -> {{ rootdurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1125, 1125, 1125, 1125, 1125], "rangeBurn": "1125", "image": {"full": "IvernQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IvernW", "name": "Silvaflorida", "description": "Nell'erba alta, gli attacchi di Ivern e dei suoi alleati vicini infliggono danni magici bonus. Ivern può attivare questa abilità per creare un'aiuola di erba alta.", "tooltip": "<spellPassive>Passiva:</spellPassive> nell'erba alta e per {{ buffduration }} secondi dopo averla lasciata, gli attacchi di Ivern infliggono <magicDamage>{{ totaldamage }} danni magici</magicDamage> in più. Per gli alleati vicini l'effetto dura {{ allybuffduration }} secondi e gli attacchi infliggono <magicDamage>{{ totalallydamage }} danni magici</magicDamage>.<br /><br /><spellActive>Attiva:</spellActive> Ivern fa crescere un'aiuola di erba alta e la rivela per {{ revealduration }} secondi. L'erba alta permane finché la squadra di Ivern non la perde di vista, o per un massimo di {{ maxbrushduration }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> magici", "<PERSON>ni magici alleato"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ allybasedamage }} -> {{ allybasedamageNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "3", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "IvernW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IvernE", "name": "Seminascudum", "description": "Ivern piazza su un alleato uno scudo che esplode dopo un breve periodo, rallentando e danneggiando i nemici. Lo scudo si rigenera se non colpisce alcun nemico.", "tooltip": "Ivern conferisce <shield>{{ totalshield }} scudo</shield> a un campione alleato o a Daisy. Dopo {{ shieldduration }} secondi esplode, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallentando</status> i nemici di un {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />Se <spellName>Seminascudum</spellName> esplode senza colpire alcun campione nemico mentre lo scudo è ancora attivo, l'alleato viene protetto da uno scudo da <shield>{{ totalshield }} </shield>per {{ shieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "IvernE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IvernR", "name": "Daisy!", "description": "Ivern evoca la sua amica Sentinella Daisy, che combatte con lui. Il rilancio spinge Daisy ad attaccare o a muoversi.", "tooltip": "Ivern evoca la sua amica sentinella Daisy e la manda nella mischia per {{ daisyduration }} secondi.<br /><br /><spellActive><PERSON>, schianta!:</spellActive> il terzo attacco consecutivo di Daisy allo stesso campione o mostro epico scatena un'onda d'urto e infligge<magicDamage> {{ totalshockwavedamage }} danni magici</magicDamage> a tutti i nemici colpiti, <status>lanciandoli in aria</status> per {{ shockwaveccduration }} secondo/i. Questo effetto può verificarsi solo una volta ogni {{ shockwavecd }} secondi.<br /><br /><recast>Rilancio:</recast> ordina a Daisy di attaccare o muoversi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attacco fisico", "<PERSON><PERSON>'<PERSON>rto", "Velocità d'attacco bonus Daisy", "Ricarica"], "effect": ["{{ daisyad }} -> {{ daisyadNL }}", "{{ shockwavebasedamage }} -> {{ shockwavebasedamageNL }}", "{{ daisyas }}% -> {{ daisyasNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 130, 120], "cooldownBurn": "140/130/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "IvernR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Amico della foresta", "description": "Ivern non può attaccare o essere attaccato dai mostri non epici. Ivern può creare boschetti magici sui campi della giungla, che crescono nel tempo. Quando un boschetto è completamente cresciuto, Ivern può liberare i mostri e ricevere oro ed esperienza.", "image": {"full": "IvernP.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}