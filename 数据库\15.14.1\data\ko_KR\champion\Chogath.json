{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Chogath": {"id": "<PERSON><PERSON><PERSON>", "key": "31", "name": "초가스", "title": "공허의 공포", "image": {"full": "Chogath.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "31000", "num": 0, "name": "default", "chromas": false}, {"id": "31001", "num": 1, "name": "악몽의 초가스", "chromas": false}, {"id": "31002", "num": 2, "name": "신사 초가스", "chromas": false}, {"id": "31003", "num": 3, "name": "네스호 초가스", "chromas": false}, {"id": "31004", "num": 4, "name": "쥬라기 초가스", "chromas": false}, {"id": "31005", "num": 5, "name": "전투 기계 프라임 초가스", "chromas": true}, {"id": "31006", "num": 6, "name": "선사시대 초가스", "chromas": false}, {"id": "31007", "num": 7, "name": "암흑의 별 초가스", "chromas": false}, {"id": "31014", "num": 14, "name": "전설의 산수화 초가스", "chromas": true}, {"id": "31023", "num": 23, "name": "깨진 언약 초가스", "chromas": true}, {"id": "31032", "num": 32, "name": "공포의 장난감 초가스", "chromas": true}], "lore": "공허에서 나온 생명체 초가스는 룬테라의 따가운 햇빛 아래 처음 모습을 드러낸 순간부터, 그 무엇으로도 만족시킬 수 없는 가장 순수한 굶주림에 이끌려 움직였다. 그 존재 자체가 모든 생명체를 집어삼키려는 공허의 욕망을 온전하게 드러내는 것이다. 초가스는 복잡한 생체 활동을 통해 주변 물질에서 새로운 몸을 만들어냈고, 근육과 밀도를 키웠으며, 유기체 다이아몬드처럼 딱딱한 겉껍질로 몸을 덮었다. 하지만 몸집이 커져도 허기를 채울 수 없자, 초가스는 여분의 물질을 토해내어 면도날처럼 날카로운 등뼈를 만들었다. 먹잇감을 꼬치처럼 꿰어 두었다가 나중에 포식하기 위해서.", "blurb": "공허에서 나온 생명체 초가스는 룬테라의 따가운 햇빛 아래 처음 모습을 드러낸 순간부터, 그 무엇으로도 만족시킬 수 없는 가장 순수한 굶주림에 이끌려 움직였다. 그 존재 자체가 모든 생명체를 집어삼키려는 공허의 욕망을 온전하게 드러내는 것이다. 초가스는 복잡한 생체 활동을 통해 주변 물질에서 새로운 몸을 만들어냈고, 근육과 밀도를 키웠으며, 유기체 다이아몬드처럼 딱딱한 겉껍질로 몸을 덮었다. 하지만 몸집이 커져도 허기를 채울 수 없자, 초가스는 여분의...", "allytips": ["날카로운 가시 스킬을 잘 조준하여 미니언을 처치하면서 동시에 적 챔피언도 견제해 보세요.", "챔피언을 포식 스킬로 먹어 치우기 힘들다면 우선 미니언을 잡아먹어 체력을 키우십시오.", "미니언을 파열 스킬로 한꺼번에 처리하면 육식 기본 지속 효과 덕택에 체력과 마나를 모두 확보할 수 있습니다."], "enemytips": ["체력 아이템을 몇 개 사두면 초가스와 맞붙더라도 생존할 확률이 높아집니다.", "초가스가 최대 크기로 성장하지 못하도록 저지하세요.", "파열이 적중될 지역 위에는 연기구름이 생성됩니다. 연기구름이 생성되는지 잘 관찰해 초가스의 스킬 연계 공격을 막아 보세요."], "tags": ["Tank", "Mage"], "partype": "마나", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 5}, "stats": {"hp": 644, "hpperlevel": 94, "mp": 270, "mpperlevel": 60, "movespeed": 345, "armor": 38, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.2, "attackspeedperlevel": 1.44, "attackspeed": 0.658}, "spells": [{"id": "Rupture", "name": "파열", "description": "선택한 지역의 지면을 파열시켜 해당 지역에 있는 적을 공중으로 띄워 피해를 입히고 이동 속도를 늦춥니다.", "tooltip": "초가스가 땅을 파열시켜 {{ e5 }}초 동안 적들을 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamagetooltip }}의 마법 피해</magicDamage>를 입히며 {{ e3 }}초 동안 {{ e2 }}% <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [60, 60, 60, 60, 60], [1.5, 1.5, 1.5, 1.5, 1.5], [0.625, 0.625, 0.625, 0.625, 0.625], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "60", "1.5", "0.63", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "Rupture.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FeralScream", "name": "흉포한 울부짖음", "description": "초가스가 원뿔 형태의 지역에 무시무시한 비명을 질러 마법 피해를 입히고 몇 초 동안 적을 침묵시킵니다.", "tooltip": "초가스가 울부짖으며 {{ e2 }}초 동안 적들을 <status>침묵</status>시키고 <magicDamage>{{ totaldamagetooltip }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "침묵 지속시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1.6, 1.7, 1.8, 1.9, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1.6/1.7/1.8/1.9/2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "FeralScream.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VorpalSpikes", "name": "날카로운 가시", "description": "초가스가 공격할 때마다 치명적인 가시가 발사되어 정면에 있는 모든 적에게 피해를 입히고 둔화시킵니다.", "tooltip": "초가스가 다음 세 번의 기본 공격 시 가시를 발사하여 <magicDamage>{{ flatdamagecalc }}+대상 최대 체력의 {{ maxhealthpercentcalc }}에 해당하는 마법 피해</magicDamage>를 입힙니다. 피해를 입은 적은 {{ slowamountpercentage }}% <status>둔화</status>했다가 {{ slowduration }}초에 걸쳐 원래대로 돌아옵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "적 최대 체력 비례 피해량", "둔화", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthealthdamage }}% -> {{ percenthealthdamageNL }}%", "{{ slowamountpercentage }}% -> {{ slowamountpercentageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [40, 40, 40, 40, 40], "rangeBurn": "40", "image": {"full": "VorpalSpikes.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "Feast", "name": "포식", "description": "적 유닛을 먹어치워 적의 방어력을 무시하고 큰 고정 피해를 입힙니다. 대상을 처치하면 초가스가 성장하며 최대 체력이 증가합니다.", "tooltip": "초가스가 적을 게걸스럽게 먹어치워, 챔피언에게는 <trueDamage>{{ rdamage }}</trueDamage>, 미니언과 정글 몬스터에게는 <trueDamage>{{ rmonsterdamage }}</trueDamage>의 고정 피해를 입힙니다. 대상이 처치되면 초가스의 포식 중첩이 1 올라, 몸집이 커지며 <healing>최대 체력이 {{ rhealthperstack }}</healing> 오릅니다. 에픽 몬스터가 아닌 일반 정글 몬스터와 미니언 처치로는 최대 {{ rminionmaxstacks }}중첩까지만 얻을 수 있습니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["챔피언 피해량", "중첩당 추가 체력", "중첩당 공격 사거리", "재사용 대기시간"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rhealthperstack }} -> {{ rhealthperstackNL }}", "{{ attackrangeperstack }} -> {{ attackrangeperstackNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 70, 60], "cooldownBurn": "80/70/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [175, 175, 175], "rangeBurn": "175", "image": {"full": "Feast.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "육식", "description": "초가스는 유닛을 죽이면 체력과 마나를 회복합니다. 회복량은 초가스의 레벨이 높아질수록 증가합니다.", "image": {"full": "GreenTerror_TailSpike.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}