{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Corki": {"id": "<PERSON><PERSON>", "key": "42", "name": "<PERSON><PERSON>", "title": "the Daring Bombardier", "image": {"full": "Corki.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "42000", "num": 0, "name": "default", "chromas": false}, {"id": "42001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "42002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "42003", "num": 3, "name": "Red <PERSON>", "chromas": false}, {"id": "42004", "num": 4, "name": "<PERSON> <PERSON>", "chromas": false}, {"id": "42005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "42006", "num": 6, "name": "Dragon<PERSON>", "chromas": true}, {"id": "42007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "42008", "num": 8, "name": "<PERSON>", "chromas": true}, {"id": "42018", "num": 18, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "42026", "num": 26, "name": "Astronaut <PERSON>", "chromas": true}], "lore": "Pilot yordle, <PERSON><PERSON>, <PERSON><PERSON><PERSON> dua hal lebih dari segalanya: te<PERSON><PERSON>, dan kumis glamornya ... meski urutannya tidak harus selalu begitu. Setelah meninggalkan Bandle City, dia menetap di Piltover dan jatuh cinta pada mesin-mesin menakjubkan yang dia temukan di sana. Dia mengabdikan dirinya untuk pengembangan alat penerbangan, memimpin pasukan pertahanan udara berisi para veteran be<PERSON><PERSON><PERSON><PERSON>, yang dikenal sebagai Screaming Yipsnakes. Tetap tenang walau dihujani tembakan, Corki berpatroli di langit sekitar rumahnya, dan selalu bisa mengatasi kendala dengan missile barrage.", "blurb": "Pilot yord<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> dua hal lebih dari segalanya: te<PERSON><PERSON>, dan kumis glam<PERSON> ... meski urutannya tidak harus selalu begitu. Setelah meninggalkan Bandle City, dia menetap di Piltover dan jatuh cinta pada mesin-mesin menakjubkan yang dia...", "allytips": ["Phosphorus Bomb bisa digunakan untuk mengungkap unit musuh yang bersembunyi di semak sekitar.", "Valkyrie juga bisa digunakan untuk be<PERSON>han, jadi coba gunakan untuk kabur dengan cepat.", "Corki bisa terus menyerang sambil menggunakan Gatling Gun. Memaksimalkan Gatling Gun adalah kunci menguasai <PERSON>."], "enemytips": ["Berhati-hat<PERSON><PERSON> pada Missile Barrage Corki. <PERSON><PERSON><PERSON><PERSON> splash damage, jadi kamu bisa terkena meskipun berlindung di balik minion.", "<PERSON>i akan rentan setelah menggunakan Valkyrie atau Special Delivery, jadi coba fokuskan serangan padanya jika dia memakainya untuk memulai pertempuran."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 3, "magic": 6, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 100, "mp": 350, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2, "attackspeedperlevel": 2.8, "attackspeed": 0.644}, "spells": [{"id": "PhosphorusBomb", "name": "Phosphorus Bomb", "description": "Corki menembakkan flash bomb ke lokasi target, memberikan magic damage ke musuh di area tersebut. Serangan ini juga mengungkap unit di area tersebut selama durasi tertentu.", "tooltip": "<PERSON><PERSON> bom, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage>. Area dan champion yang terkena hit akan terlihat selama {{ revealduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "PhosphorusBomb.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CarpetBomb", "name": "Valkyrie", "description": "<PERSON>i terbang dalam jarak dekat sambil menjatuhkan bom yang meninggalkan jejak api yang mengh<PERSON>lkan damage pada musuh yang berada di dalamnya.", "tooltip": "<PERSON><PERSON> terbang dan membakar jalurnya selama {{ trailduration }} detik. <PERSON><PERSON>h di dalam api menerima hingga <magicDamage>{{ maximumdamage }} magic damage</magicDamage> selama durasi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Berkelanjutan", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "CarpetBomb.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GGun", "name": "Gatling Gun", "description": "Gatling Gun Corki menembakkan rentetan peluru dalam kerucut di depannya, men<PERSON><PERSON><PERSON>an damage serta mengurangi Armor dan Magic Resist musuh.", "tooltip": "<PERSON><PERSON> gatling gun di depannya, member<PERSON>n <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> selama {{ sprayduration }} detik dan mengurangi hingga <scaleArmor>{{ shredmax*-1 }} Armor</scaleArmor> dan <scaleMR>Magic Resist</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Pengurangan Defense", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ shredmax*-1.000000 }}-> {{ shredmaxnl*-1.000000 }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GGun.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissileBarrage", "name": "Missile Barrage", "description": "Corki menembakkan rudal ke lokasi sasarannya yang meledak saat mengenai sasaran, memberikan damage ke musuh dalam 1 area. Corki menyimpan rudal seiring waktu, hingga batas maksimum. Setiap rudal ketiga yang ditembakkan akan berupa Big One yang menghasilkan damage ekstra.", "tooltip": "<PERSON><PERSON> menem<PERSON>kkan misil yang meledak saat mengenai musuh pertama, memberikan <physicalDamage>{{ rsmallmissiledamage }} physical damage</physicalDamage> ke musuh di sekitarnya. Setiap misil ketiga memberikan <physicalDamage>{{ rbigmissiledamage }} physical damage</physicalDamage>.<br /><br />Ability ini memiliki hingga {{ maxammotooltip }} charge. Basic attack ke champion mengurangi waktu antar-charge sebesar <attention>{{ attackrefund }}</attention> detik saat mengenai target.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [2, 2, 2], "cooldownBurn": "2", "cost": [35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "4", "range": [1225, 1225, 1225], "rangeBurn": "1225", "image": {"full": "MissileBarrage.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Hextech Munitions", "description": "Persentase damage basic attack <PERSON><PERSON> se<PERSON>ai <trueDamage>true damage</trueDamage> bonus.", "image": {"full": "Corki_RapidReload.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}