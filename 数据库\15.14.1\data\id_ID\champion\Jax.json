{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "Jax", "title": "Grandmaster at Arms", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "The Mighty Jax", "chromas": false}, {"id": "24002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX Jax", "chromas": false}, {"id": "24005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24006", "num": 6, "name": "Temple Jax", "chromas": false}, {"id": "24007", "num": 7, "name": "Nemesis Jax", "chromas": true}, {"id": "24008", "num": 8, "name": "SKT T1 Jax", "chromas": false}, {"id": "24012", "num": 12, "name": "<PERSON>", "chromas": false}, {"id": "24013", "num": 13, "name": "God Staff Jax", "chromas": false}, {"id": "24014", "num": 14, "name": "Mecha Kingdoms Jax", "chromas": true}, {"id": "24020", "num": 20, "name": "Conquer<PERSON>", "chromas": false}, {"id": "24021", "num": 21, "name": "Prestige Conqueror Jax", "chromas": false}, {"id": "24022", "num": 22, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "24032", "num": 32, "name": "Neo PAX Jax", "chromas": false}, {"id": "24033", "num": 33, "name": "PROJECT: Jax", "chromas": true}], "lore": "Jax adalah ahli senjata terakhir yang ada di Icathia. Tak ada yang menandingi keahliannya dalam menggunakan persenjataan unik dan sarkasme pedas. Setelah tanah airnya hancur oleh kesombongannya sendiri akibat melepaskan Void, Jax dan kaumnya bersumpah untuk melindungi yang masih tersisa. Saat sihir kini bangkit di dunia, ancaman yang tertidur ini kembali bangkit. Jax menjelajah<PERSON>oran, membawa cahaya terakhir Icathia dan menguji semua warrior yang ditemuinya untuk mencari sosok yang pantas mendampinginya ...", "blurb": "Jax adalah ahli senjata terakhir yang ada di Icathia. Tak ada yang menandingi keahliannya dalam menggunakan persenjataan unik dan sarkasme pedas. <PERSON>elah tanah airnya hancur oleh kesombongannya sendiri akibat melepaskan Void, Jax dan kaumnya bersumpah...", "allytips": ["Jax bisa menggunakan Leap Strike ke unit teman, termasuk ward. Kamu bisa menggunakannya untuk merencanakan pelarian.", "Jax sangat diuntungkan dari item yang memiliki Ability Power dan Attack Damage seperti Guinsoo's Rageblade dan Hextech Gunblade."], "enemytips": ["Coba lawan dia dengan burst pendek daripada melawannya langsung. Mencegahnya menyerang terus-terusan akan menu<PERSON>an drastis output damage-nya.", "Jax bisa menghindari semua serangan yang akan datang dalam waktu yang sangat pendek dan memberi musuh Stun dalam range melee saat itu berakhir. Tunggu untuk menyerangnya sampai dia selesai menghindar."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "Leap Strike", "description": "Jax melompat ke arah sebuah unit. Jika unit itu adalah musuh, dia akan menyerang dengan senjatanya.", "tooltip": "Jax melompat ke unit sekutu atau musuh atau ward, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> jika target adalah musuh.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxW", "name": "Empower", "description": "Jax charge senja<PERSON><PERSON> dengan energi, menyebabkan serangan berikut<PERSON> men<PERSON><PERSON><PERSON>an damage tambahan.", "tooltip": "Jax charge senja<PERSON><PERSON> dengan energi, menyebabkan <PERSON>angan berikutnya atau <spellName>Leap Strike</spellName> men<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> tambahan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxE", "name": "Counter Strike", "description": "<PERSON>gan k<PERSON><PERSON>ian be<PERSON>, <PERSON> bisa mengelak dari semua serangan selama beberapa saat, lalu dengan cepat menyerang balik, menerapkan stun pada semua musuh di sekitarnya.", "tooltip": "Jax memasuki mode be<PERSON>han hingga {{ dodgeduration }} detik, menghindari semua serangan yang datang dan menerima {{ aoedamagereduction }}% damage lebih sedikit dari Ability AOE. Setelah {{ dodgeduration }} detik atau <recast>Recast</recast>, <PERSON>an <magicDamage>{{ totaldamage }} + {{ percenthealthdamage }}% magic damage dari Health maksimum</magicDamage> dan <status>Stun</status> musuh di sekitar selama {{ stunduration }} detik. <br /><br />Damage-nya meningkat sebesar {{ percentincreasedperdodge*100 }}% per Serangan yang dihindari, hingga maksimum <magicDamage>{{ maxdamage }} + {{ maxpercenthealthdamage }}% Health maksimum</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxR", "name": "<PERSON><PERSON>-at-<PERSON>", "description": "<PERSON>iap serangan beruntun ketiga menghasilkan Magic Damage tambahan. <PERSON><PERSON> itu, Jax bisa mengaktifkan ability ini untuk menghasilkan damage di sekelilingnya dan memperkuat resolve-nya, yang meningkatkan Armor dan Magic Resist selama beberapa saat.", "tooltip": "<spellPassive>Pasif:</spellPassive> Tiap <PERSON> ketiga dalam {{ passivefallofftime }} detik menghasilkan <magicDamage>{{ onhitdamage }} magic damage</magicDamage> tambahan.<br /><br /><spellActive>Aktif:</spellActive> Jax membanting lenteranya, men<PERSON><PERSON><PERSON>an <magicDamage>{{ swingdamagetotal }} magic damage</magicDamage> pada musuh di sekitar. Jika mengenai champion, dia akan mendapatkan <scaleArmor>{{ basearmor }} Armor</scaleArmor> dan <scaleMR>{{ basemr }} Magic Resist</scaleMR> plus <scaleArmor>{{ bonusarmor }} Armor</scaleArmor> dan <scaleMR>{{ bonusmr }} Magic Resist</scaleMR> per champion tambahan yang terkena selama {{ duration }} detik. Selama durasi ini, tiap Serangan kedua menghasilkan <magicDamage>magic damage</magicDamage> tambahan, dan bukan tiap <PERSON>angan ketiga.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Pasif", "Damage Aktif", "<PERSON><PERSON>", "Magic Resist Dasar", "Armor per Champion <PERSON>", "Magic Resist per Champion <PERSON><PERSON><PERSON>", "Cooldown"], "effect": ["{{ passivebasedamage }}-> {{ passivebasedamageNL }}", "{{ swingdamagebase }}-> {{ swingdamagebaseNL }}", "{{ baseresists }}-> {{ baseresistsNL }}", "{{ baseresists*0.600000 }}-> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }}-> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }}-> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Relentless Assault", "description": "Basic attack beruntun Jax terus meningkatkan Attack Speed miliknya.", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}