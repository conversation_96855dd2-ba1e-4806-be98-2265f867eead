{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vayne": {"id": "<PERSON><PERSON>", "key": "67", "name": "ヴェイン", "title": "ナイトハンター", "image": {"full": "Vayne.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "67000", "num": 0, "name": "default", "chromas": false}, {"id": "67001", "num": 1, "name": "死の執行人ヴェイン", "chromas": false}, {"id": "67002", "num": 2, "name": "貴族の狩人ヴェイン", "chromas": false}, {"id": "67003", "num": 3, "name": "龍殺しヴェイン", "chromas": true}, {"id": "67004", "num": 4, "name": "愛の狩人ヴェイン", "chromas": false}, {"id": "67005", "num": 5, "name": "SKT T1 ヴェイン", "chromas": false}, {"id": "67006", "num": 6, "name": "アークライト ヴェイン", "chromas": false}, {"id": "67010", "num": 10, "name": "魂の支配者ヴェイン", "chromas": true}, {"id": "67011", "num": 11, "name": "PROJECT: <PERSON><PERSON>", "chromas": false}, {"id": "67012", "num": 12, "name": "爆発花火ヴェイン", "chromas": true}, {"id": "67013", "num": 13, "name": "プレステージ爆発花火ヴェイン", "chromas": false}, {"id": "67014", "num": 14, "name": "精霊の花祭りヴェイン", "chromas": true}, {"id": "67015", "num": 15, "name": "FPX ヴェイン", "chromas": true}, {"id": "67025", "num": 25, "name": "光の番人ヴェイン", "chromas": true}, {"id": "67032", "num": 32, "name": "バトルバット ヴェイン", "chromas": true}, {"id": "67033", "num": 33, "name": "プレステージ爆発花火ヴェイン(2022)", "chromas": false}, {"id": "67044", "num": 44, "name": "秩序の光ヴェイン", "chromas": true}, {"id": "67055", "num": 55, "name": "龍術師ヴェイン", "chromas": true}, {"id": "67064", "num": 64, "name": "覚醒せし伝説ヴェイン", "chromas": true}], "lore": "シャウナ・ヴェインはデマーシアの無慈悲な怪物ハンターであり、自分の家族を殺した悪魔を見つけ出して殺すことに生涯をささげている。前腕部搭載式のクロスボウと復讐に燃える心を武器にする彼女だが、心からの喜びを感じることができるのは、影の中から銀の矢を飛ばして闇の魔術の使い手や、その不浄なる創造物を殺した時だけだ。", "blurb": "シャウナ・ヴェインはデマーシアの無慈悲な怪物ハンターであり、自分の家族を殺した悪魔を見つけ出して殺すことに生涯をささげている。前腕部搭載式のクロスボウと復讐に燃える心を武器にする彼女だが、心からの喜びを感じることができるのは、影の中から銀の矢を飛ばして闇の魔術の使い手や、その不浄なる創造物を殺した時だけだ。", "allytips": ["「タンブル」は使い道の多い便利なスキルだ。ただし、壁をすり抜けることはできないので注意しよう。", "「パニッシュメント」で敵を壁にたたきつけると、スタンさせることができる。戦闘を有利に進めるため活用しよう。追ってくる敵をはじいて逃げるのにも使えるだろう。", "集団戦で真っ先に飛び出すと危険！ チームメイトが火蓋を切るのを待ってから参戦しよう。"], "enemytips": ["ヴェインは防御力が低い。チャンスを逃さずにダメージを与えて、自由に動けない状況をつくろう。", "ヴェインに壁際に追い込まれると「パニッシュメント」で壁に叩きつけてスタンさせられてしまう。気をつけよう。"], "tags": ["Marksman", "Assassin"], "partype": "マナ", "info": {"attack": 10, "defense": 1, "magic": 1, "difficulty": 8}, "stats": {"hp": 550, "hpperlevel": 103, "mp": 232, "mpperlevel": 35, "movespeed": 330, "armor": 23, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.35, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "VayneTumble", "name": "タンブル", "description": "指定方向に前転して移動し、次の攻撃の準備をする。「タンブル」発動後の最初の通常攻撃は追加ダメージを与える。", "tooltip": "短い距離を転がり、次の通常攻撃が追加で<physicalDamage>{{ adratiobonus }}の物理ダメージ</physicalDamage>を与える。<br /><br /><rules>このスキルはダメージを与えた際にスキル命中時効果を発動する。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "攻撃力反映率"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ totaladratio*100.000000 }}% -> {{ totaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VayneTumble.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON>ayneSil<PERSON><PERSON><PERSON><PERSON>", "name": "シルバーボルト", "description": "邪悪な存在が嫌う銀の矢を、クロスボウにつがえる。同じ対象に通常攻撃またはスキルを3回連続で命中させると、対象の最大体力に比例する追加確定ダメージが発生する。", "tooltip": "<spellPassive>自動効果</spellPassive>: 同じ敵に通常攻撃またはスキルを3回連続して行うごとに、追加で<trueDamage>最大体力の{{ totaldamage }}の確定ダメージ</trueDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["確定ダメージ%", "最小ダメージ"], "effect": ["{{ maxhealthratio*100.000000 }}% -> {{ maxhealthrationl*100.000000 }}%", "{{ damagefloor }} -> {{ damagefloorNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "固有スキル", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VayneSilveredBolts.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "固有スキル"}, {"id": "VayneCondemn", "name": "パニッシュメント", "description": "背中に担いだ特大クロスボウを構え、指定対象に巨大な矢を撃ち込む。矢を受けたユニットはノックバックされダメージを受ける。ノックバック中に対象が地形に衝突した場合は、追加のダメージが発生し、スタン状態になる。", "tooltip": "矢を発射し、対象を<status>ノックバック</status>させて<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。対象が地形にぶつかると<physicalDamage>{{ empowereddamagett }}の追加物理ダメージ</physicalDamage>を与え、{{ stunduration }}秒間<status>スタン</status>させる。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "VayneCondemn.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VayneInquisition", "name": "ファイナルアワー", "description": "敵を殲滅すべく特大クロスボウを構え、攻撃力が増加する。効果時間中は「タンブル」発動時にインビジブル状態になり、「タンブル」のクールダウンが短縮される。また、「ナイトハンター」の移動速度増加量が上昇する。", "tooltip": "{{ baseduration }}秒間<physicalDamage>攻撃力が{{ bonusattackdamage }}</physicalDamage>増加する。自身がダメージを与えたチャンピオンが{{ damagedmarkerduration }}秒以内に倒されるたびに、効果時間が{{ durationtoadd }}秒延長される。さらに、効果時間中は以下の効果を獲得する:<li><spellName>「ナイトハンター」</spellName>による<speed>移動速度増加量が{{ movementspeed }}</speed>に変わる。<li><spellName>「タンブル」</spellName>のクールダウンが{{ tumblecdreduction }}%短縮されて、{{ tumblestealthduration }}秒間<keywordStealth>インビジブル</keywordStealth>状態になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "効果時間", "増加攻撃力", "「タンブル」のクールダウン短縮"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseduration }} -> {{ basedurationNL }}", "{{ bonusattackdamage }} -> {{ bonusattackdamageNL }}", "{{ tumblecdreduction }}% -> {{ tumblecdreductionNL }}%"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "VayneInquisition.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ナイトハンター", "description": "敵チャンピオンに向かって移動する時、移動速度が増加する。", "image": {"full": "Vayne_NightHunter.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}