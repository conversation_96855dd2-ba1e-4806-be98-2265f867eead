{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Veigar": {"id": "<PERSON><PERSON><PERSON>", "key": "45", "name": "ベイガー", "title": "小さき大魔王", "image": {"full": "Veigar.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "45000", "num": 0, "name": "default", "chromas": false}, {"id": "45001", "num": 1, "name": "白魔道士ベイガー", "chromas": false}, {"id": "45002", "num": 2, "name": "カーリング ベイガー", "chromas": false}, {"id": "45003", "num": 3, "name": "賢者ベイガー", "chromas": false}, {"id": "45004", "num": 4, "name": "四つ葉の妖精ベイガー", "chromas": false}, {"id": "45005", "num": 5, "name": "ベイガー男爵", "chromas": false}, {"id": "45006", "num": 6, "name": "悪の大首領ベイガー", "chromas": false}, {"id": "45007", "num": 7, "name": "邪悪なるサンタ ベイガー", "chromas": false}, {"id": "45008", "num": 8, "name": "ラスボス ベイガー", "chromas": true}, {"id": "45009", "num": 9, "name": "オメガ小隊ベイガー", "chromas": true}, {"id": "45013", "num": 13, "name": "古の賢樹ベイガー", "chromas": true}, {"id": "45023", "num": 23, "name": "フューリーホーン コスプレ ベイガー", "chromas": true}, {"id": "45032", "num": 32, "name": "宇宙飛行士ベイガー", "chromas": true}, {"id": "45041", "num": 41, "name": "モンスターテイマー ベイガー", "chromas": true}, {"id": "45051", "num": 51, "name": "蜂の王ビィガー", "chromas": true}, {"id": "45060", "num": 60, "name": "恐怖の夜ベイガー", "chromas": true}], "lore": "定命の者が恐れて近づこうとしなかった力を受け入れたベイガーは情熱的な闇の魔術の使い手だ。自由な精神を持つバンドルシティの住人として、彼はヨードルの魔法の限界を超えてみたいと願い、数千年に渡って隠されてきた古文書を紐解いた。世界の謎に尽きぬ興味を持つ頑固者のベイガーだが、周囲からは見くびられることが多く、彼自身は自分は真に邪悪な存在だと思っているにもかかわらず、彼の内面に存在する倫理観を見て、彼の真の動機に疑いを持つ者もいる。", "blurb": "定命の者が恐れて近づこうとしなかった力を受け入れたベイガーは情熱的な闇の魔術の使い手だ。自由な精神を持つバンドルシティの住人として、彼はヨードルの魔法の限界を超えてみたいと願い、数千年に渡って隠されてきた古文書を紐解いた。世界の謎に尽きぬ興味を持つ頑固者のベイガーだが、周囲からは見くびられることが多く、彼自身は自分は真に邪悪な存在だと思っているにもかかわらず、彼の内面に存在する倫理観を見て、彼の真の動機に疑いを持つ者もいる。", "allytips": ["「イベントホライズン」をうまく使って「ダークマター」の命中率を高めよう。", "ベイガーはマナとクールダウン短縮に激しく依存する。両方のステータスを上げる装備を購入して固有スキルと「イーヴィルストライク」の効果を最大限まで高めよう。", "ベイガーは防御力がかなり低い。敵の猛攻にさらされる場合は、防御面にも配慮する必要がある。"], "enemytips": ["「ダークマター」は大きなダメージを持つスキルだが、回避は難しくない。音とグラフィックスによく注意して、暗黒物質がいつどこに落ちてくるのか見極めよう。", "「イベントホライズン」でスタン状態になるのは、境界に触れた場合だけだ。境界の内側にいるだけなら、自由に動いて攻撃できる。", "ベイガーのアルティメットスキルのダメージ量は、減少体力に比例する。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 580, "hpperlevel": 108, "mp": 490, "mpperlevel": 26, "movespeed": 340, "armor": 18, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.24, "attackspeed": 0.625}, "spells": [{"id": "VeigarBalefulStrike", "name": "イーヴィルストライク", "description": "指定方向に闇のエネルギーを発射し、最初に命中した敵2体に魔法ダメージを与える。このスキルで敵ユニットにとどめを刺すと、ベイガーの魔力が永続的に増加する。", "tooltip": "闇のエネルギーを発射し、最初に命中した敵2体に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />このスキルで敵を倒すと<keywordMajor>「偉大なる悪の力」</keywordMajor>のスタックが{{ spell.veigarpassive:dqkillstacks }}増加する。対象が大型ミニオン、大型モンスターの場合は代わりにスタックを{{ spell.veigarpassive:dqkillstackslarge }}獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "合計魔力反映率", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "VeigarBalefulStrike.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VeigarDarkMatter", "name": "ダークマター", "description": "指定地点に巨大な暗黒物質を降らせ、着地時に魔法ダメージを与える。「偉大なる悪の力」のスタックに応じて「ダークマター」のクールダウンが短縮される。", "tooltip": "ダークマターを落下させて、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><keywordMajor>「偉大なる悪の力」</keywordMajor>のスタックが{{ spell.veigarpassive:pstacksperdarkmattercdr }}たまるごとに、このスキルのクールダウンが{{ spell.veigarpassive:darkmattercdrincrement*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "合計魔力反映率", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1.2, 1.2, 1.2, 1.2, 1.2], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1.2", "8", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "VeigarDarkMatter.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VeigarEventHorizon", "name": "イベントホライズン", "description": "空間の境界をねじ曲げて檻を作り出す。檻を通過した敵はスタンする。", "tooltip": "空間の境界をねじ曲げて檻を作り出す。檻を通過した敵は{{ e1 }}秒間<status>スタン</status>する。檻は3秒間持続する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["スタン効果時間:", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18.5, 17, 15.5, 14], "cooldownBurn": "20/18.5/17/15.5/14", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [1.5, 1.75, 2, 2.25, 2.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5/1.75/2/2.25/2.5", "0.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "VeigarEventHorizon.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "VeigarR", "name": "メテオバースト", "description": "敵チャンピオン1体に闇の大魔法を放ち、対象の体力欠損分に応じて増加する大量の魔法ダメージを与える。", "tooltip": "敵チャンピオン1体に闇の大魔法を放ち、対象の減少体力に応じて<magicDamage>{{ mindamage }}-{{ maxdamage }}の魔法ダメージ</magicDamage>を与える。ダメージは敵の体力が33%を下回ると最大になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "合計魔力反映率", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "VeigarR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "偉大なる悪の力", "description": "ルーンテラを脅かす強大な悪であるベイガー、その力はとどまるところを知らない！敵チャンピオンからキルまたはアシストを獲得したり、スキルを命中させると、ベイガーの魔力は永続的に増加してゆく。", "image": {"full": "VeigarEntropy.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}