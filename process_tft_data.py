#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TFT PBE数据处理脚本
用于分析和处理大型TFT JSON数据文件
"""

import json
import os
import sys
from pathlib import Path
from collections import defaultdict
import re

class TFTDataProcessor:
    def __init__(self, input_file):
        self.input_file = input_file
        self.output_dir = Path("processed_data")
        self.output_dir.mkdir(exist_ok=True)
        
        # 数据分类映射
        self.data_categories = {
            'champions': [],
            'traits': [],
            'items': [],
            'augments': [],
            'charms': [],
            'portals': [],
            'mechanics': [],
            'others': []
        }
        
        # 统计信息
        self.stats = defaultdict(int)
    
    def load_data(self):
        """加载JSON数据"""
        print(f"📁 正在加载文件: {self.input_file}")
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"✅ 文件加载成功")
            return True
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            return False
    
    def analyze_structure(self):
        """分析数据结构"""
        print("\n🔍 分析数据结构...")
        
        def analyze_object(obj, path="", level=0):
            if level > 3:  # 限制递归深度
                return
                
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    self.stats[f"key_{current_path}"] += 1
                    
                    if isinstance(value, list) and len(value) > 0:
                        self.stats[f"array_{current_path}"] = len(value)
                        print(f"  {'  ' * level}📋 {key}: {len(value)} 项")
                        
                        # 分析数组中的第一个元素
                        if isinstance(value[0], dict):
                            sample_keys = list(value[0].keys())[:5]  # 只显示前5个键
                            print(f"  {'  ' * level}   示例字段: {sample_keys}")
                    
                    elif isinstance(value, dict):
                        print(f"  {'  ' * level}📁 {key}: 对象")
                        analyze_object(value, current_path, level + 1)
                    
                    elif isinstance(value, str) and len(value) > 100:
                        self.stats[f"long_string_{current_path}"] += 1
        
        analyze_object(self.data)
        
        print(f"\n📊 结构分析完成")
        print(f"   - 发现 {len([k for k in self.stats.keys() if k.startswith('array_')])} 个数组")
        print(f"   - 发现 {len([k for k in self.stats.keys() if k.startswith('key_')])} 个不同的键")
    
    def categorize_items(self):
        """分类数据项"""
        print("\n🏷️  开始数据分类...")
        
        # 处理主要的items数组
        if 'items' in self.data and isinstance(self.data['items'], list):
            for item in self.data['items']:
                if not isinstance(item, dict):
                    continue
                    
                api_name = item.get('apiName', '')
                name = item.get('name', '')
                
                # 根据apiName和name进行分类
                if self._is_champion(item):
                    self.data_categories['champions'].append(item)
                elif self._is_trait(item):
                    self.data_categories['traits'].append(item)
                elif self._is_item(item):
                    self.data_categories['items'].append(item)
                elif self._is_augment(item):
                    self.data_categories['augments'].append(item)
                elif self._is_charm(item):
                    self.data_categories['charms'].append(item)
                elif self._is_portal(item):
                    self.data_categories['portals'].append(item)
                elif self._is_mechanic(item):
                    self.data_categories['mechanics'].append(item)
                else:
                    self.data_categories['others'].append(item)
        
        # 处理setData中的数据
        if 'setData' in self.data:
            set_data = self.data['setData']
            for category in ['champions', 'traits', 'items']:
                if category in set_data and isinstance(set_data[category], list):
                    self.data_categories[category].extend(set_data[category])
        
        # 打印分类统计
        print("📊 分类统计:")
        for category, items in self.data_categories.items():
            if items:
                print(f"   - {category}: {len(items)} 项")
    
    def _is_champion(self, item):
        """判断是否为弈子"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        # 弈子通常不包含这些关键词
        exclude_keywords = ['augment', 'crown', 'crest', 'teamup']
        if any(keyword in api_name for keyword in exclude_keywords):
            return False

        # 弈子通常有这些特征
        has_tier = 'tier' in item or 'cost' in item
        has_traits = 'traits' in item or 'associatedTraits' in item

        return has_tier or (has_traits and not any(keyword in api_name for keyword in exclude_keywords))

    def _is_trait(self, item):
        """判断是否为羁绊"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        trait_keywords = ['trait', 'crest', '纹章', '之徽']
        return any(keyword in api_name or keyword in name for keyword in trait_keywords)

    def _is_item(self, item):
        """判断是否为装备"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        # 装备通常有composition字段
        has_composition = 'composition' in item and item['composition']

        item_keywords = ['item', '装备', '武器', '护甲']
        return has_composition or any(keyword in name for keyword in item_keywords)

    def _is_augment(self, item):
        """判断是否为强化符文"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        augment_keywords = ['augment', 'crown', 'teamup', '强化', '之冕', '羁绊：']
        return any(keyword in api_name or keyword in name for keyword in augment_keywords)

    def _is_charm(self, item):
        """判断是否为强化果实"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        charm_keywords = ['charm', '果实', '符咒']
        return any(keyword in api_name or keyword in name for keyword in charm_keywords)

    def _is_portal(self, item):
        """判断是否为城邦"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        portal_keywords = ['portal', 'region', '城邦', '传送门']
        return any(keyword in api_name or keyword in name for keyword in portal_keywords)

    def _is_mechanic(self, item):
        """判断是否为游戏机制"""
        api_name = item.get('apiName', '') or ''
        name = item.get('name', '') or ''
        api_name = api_name.lower()
        name = name.lower()

        mechanic_keywords = ['mechanic', 'anomaly', '机制', '异常']
        return any(keyword in api_name or keyword in name for keyword in mechanic_keywords)
    
    def clean_item_data(self, item):
        """清理单个数据项"""
        cleaned = {}
        
        # 保留重要字段
        important_fields = [
            'apiName', 'name', 'desc', 'icon', 'id',
            'tier', 'cost', 'traits', 'associatedTraits',
            'effects', 'composition', 'from', 'to',
            'stats', 'ability', 'variables'
        ]
        
        for field in important_fields:
            if field in item:
                value = item[field]
                
                # 清理描述文本
                if field == 'desc' and isinstance(value, str):
                    # 移除HTML标签和特殊标记
                    value = re.sub(r'<[^>]+>', '', value)
                    value = re.sub(r'@[^@]+@', '', value)
                    value = value.strip()
                
                # 清理effects对象
                elif field == 'effects' and isinstance(value, dict):
                    # 只保留有意义的效果值
                    cleaned_effects = {}
                    for k, v in value.items():
                        if v is not None and v != 0 and v != "":
                            cleaned_effects[k] = v
                    value = cleaned_effects
                
                cleaned[field] = value
        
        return cleaned
    
    def save_categorized_data(self):
        """保存分类后的数据"""
        print("\n💾 保存分类数据...")
        
        for category, items in self.data_categories.items():
            if not items:
                continue
                
            # 清理数据
            cleaned_items = [self.clean_item_data(item) for item in items]
            
            # 创建输出文件
            output_file = self.output_dir / f"tft_{category}_zh_cn.json"
            
            output_data = {
                "type": category,
                "version": "15.14.1",
                "language": "zh_CN",
                "count": len(cleaned_items),
                "data": {item.get('apiName', f"item_{i}"): item for i, item in enumerate(cleaned_items)}
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ {category}: {len(cleaned_items)} 项 -> {output_file}")
    
    def generate_summary(self):
        """生成处理摘要"""
        summary = {
            "original_file": str(self.input_file),
            "original_size_mb": os.path.getsize(self.input_file) / (1024 * 1024),
            "processed_files": {},
            "total_items": sum(len(items) for items in self.data_categories.values()),
            "categories": {}
        }
        
        for category, items in self.data_categories.items():
            if items:
                output_file = self.output_dir / f"tft_{category}_zh_cn.json"
                if output_file.exists():
                    file_size = os.path.getsize(output_file) / 1024  # KB
                    summary["processed_files"][category] = {
                        "file": str(output_file),
                        "size_kb": round(file_size, 2),
                        "count": len(items)
                    }
                
                summary["categories"][category] = len(items)
        
        # 保存摘要
        summary_file = self.output_dir / "processing_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        print(f"\n📋 处理摘要已保存: {summary_file}")
        return summary
    
    def process(self):
        """执行完整的处理流程"""
        print("🚀 开始处理TFT数据...")
        
        if not self.load_data():
            return False
        
        self.analyze_structure()
        self.categorize_items()
        self.save_categorized_data()
        summary = self.generate_summary()
        
        print("\n🎉 处理完成!")
        print(f"   原文件大小: {summary['original_size_mb']:.1f} MB")
        print(f"   总数据项: {summary['total_items']}")
        print(f"   生成文件: {len(summary['processed_files'])}")
        
        return True

def main():
    if len(sys.argv) != 2:
        print("使用方法: python process_tft_data.py <input_file.json>")
        print("示例: python process_tft_data.py 'TFT PBE zh_cn.json'")
        return
    
    input_file = sys.argv[1]
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    processor = TFTDataProcessor(input_file)
    processor.process()

if __name__ == "__main__":
    main()
