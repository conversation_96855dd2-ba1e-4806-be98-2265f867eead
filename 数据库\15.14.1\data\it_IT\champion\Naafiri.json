{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Naafiri": {"id": "<PERSON><PERSON><PERSON>", "key": "950", "name": "<PERSON><PERSON><PERSON>", "title": "il segugio dai cento morsi", "image": {"full": "Naafiri.png", "sprite": "champion2.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "950000", "num": 0, "name": "default", "chromas": false}, {"id": "950001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "950011", "num": 11, "name": "PROGETTO: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "950020", "num": 20, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Un coro di ululati risuona tra le sabbie di Shurima. È il richiamo dei segugi delle dune, voraci predatori che vivono in branchi e lottano per il diritto di cacciare in queste lande desertiche. Tra questi, esiste un branco superiore a tutti gli altri, guidato oltre che dall'istinto anche dall'antico potere dei Darkin.", "blurb": "Un coro di ululati risuona tra le sabbie di Shurima. È il richiamo dei segugi delle dune, voraci predatori che vivono in branchi e lottano per il diritto di cacciare in queste lande desertiche. Tra questi, esiste un branco superiore a tutti gli altri...", "allytips": ["Un coro di ululati risuona tra le sabbie di Shurima. È il richiamo dei segugi delle dune, voraci predatori che vivono in branchi e lottano per il diritto di cacciare in queste lande desertiche. Tra questi, esiste un branco superiore a tutti gli altri, guidato oltre che dall'istinto anche dall'antico potere dei Darkin."], "enemytips": [], "tags": ["Assassin", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 0, "difficulty": 2}, "stats": {"hp": 610, "hpperlevel": 105, "mp": 400, "mpperlevel": 55, "movespeed": 340, "armor": 28, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2, "attackspeedperlevel": 2.1, "attackspeed": 0.663}, "spells": [{"id": "Na<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "Naafiri lancia fino a due pugnali, ciascuno dei quali infligge sanguinamento o danni bonus se il bersaglio è già affetto da sanguinamento.<br><br>I membri del branco balzano per attaccare il primo campione o mostro colpito da quest'abilità.<br>", "tooltip": "Naafiri scaglia delle lame corrotte da<PERSON>, che infliggono <physicalDamage>{{ spell.naafiriq:totaldamagefirstcast }} danni fisici</physicalDamage> e fanno sanguinare i bersagli causando <physicalDamage>{{ spell.naafiriq:totalbleeddamage }} danni fisici</physicalDamage> nell'arco di {{ spell.naafiriq:bleedduration }} secondi.<br /><br />Naafiri può <recast>rilanciare</recast> questa abilità. Se i nemici colpiti stanno già sanguinando a causa di questa abilità, infligge invece i danni restanti del sanguinamento più una quantità di danni fisici bonus compresa tra <physicalDamage>{{ spell.naafiriq:totalmindamagesecondcast }}</physicalDamage> e <physicalDamage>{{ spell.naafiriq:totalmaxdamagesecondcast }}</physicalDamage>, in base alla salute mancante. Se il bersaglio è un campione o un mostro grande, Naafiri ripristina <healing>{{ spell.naafiriq:totalhealsecondcast }} salute</healing>.<br /><br />I <keywordMajor>membri del branco</keywordMajor> balzano sul primo campione o mostro colpito e lo attaccano per {{ spell.naafirip:packmatetauntduration }} secondi. <br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> secondo lancio", "<PERSON><PERSON> da sanguinamento", "Guarigione", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ basedamagefirstcast }} -> {{ basedamagefirstcastNL }}", "{{ basedamagesecondcast }} -> {{ basedamagesecondcastNL }}", "{{ bleedbasedamage }} -> {{ bleedbasedamageNL }}", "{{ basehealsecondcast }} -> {{ basehealsecondcastNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NaafiriQ.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NaafiriR", "name": "Il richiamo del branco", "description": "Naafiri diventa non bersagliabile e potenzia il suo branco, generando altri membri e ottenendo un aumento di velocità di movimento e attacco fisico.<br>", "tooltip": "Naafiri diventa non bersagliabile per {{ untargetableduration }} secondo/i e si prepara a cacciare, generando <keywordMajor>{{ packmatestoadd }} ulteriori membri del branco</keywordMajor> e ottenendo <physicalDamage>{{ bonusad }} attacco fisico</physicalDamage> e <speed>{{ movespeedamount*100 }}% velocità di movimento</speed> per {{ duration }} secondi.<br /><br /><keywordMajor>I membri del branco</keywordMajor> diventano non bersagliabili e vengono chiamati a raccolta da Naafiri.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Ricarica"], "effect": ["{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24, 22, 20, 18], "cooldownBurn": "26/24/22/20/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "NaafiriR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NaafiriE", "name": "Sventramento", "description": "Naafiri scatta e danneggia i nemici nell'area circostante, richiamando e curando del tutto i membri del branco.", "tooltip": "<PERSON><PERSON><PERSON> balza in avanti, infliggendo <physicalDamage>{{ totaldamagefirstslash }} danni fisici</physicalDamage>, poi esplode in una raffica di lame, infliggendo <physicalDamage>{{ totaldamagesecondslash }} danni fisici.</physicalDamage><br /><br /><keywordMajor>I membri del branco</keywordMajor> diventano non bersagliabili e vengono chiamati a raccolta da Naafiri, <healing>ripristinando il 100% della salute</healing>. <br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> (primo attacco)", "<PERSON><PERSON> (secondo attacco)", "Ricarica"], "effect": ["{{ basedamagefirstslash }} -> {{ basedamagefirstslashNL }}", "{{ basedamagesecondhit }} -> {{ basedamagesecondhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "NaafiriE.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NaafiriW", "name": "Caccia in branco", "description": "Naafiri e i membri del suo branco scattano verso un campione infliggendo danni. Quando Naafiri mette a segno un'eliminazione rivela i nemici nelle vicinanze e può lanciare di nuovo l'abilità. Il secondo lancio fornisce uno scudo.", "tooltip": "Naafiri scatta verso un campione nemico, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e <status>rallentandolo</status> per un breve periodo. <keywordMajor>I membri del branco</keywordMajor> diventano non bersagliabili e scattano insieme a Naafiri, infliggendo <physicalDamage>{{ packmatedamage }} danni fisici</physicalDamage> per <keywordMajor>ciascun membro</keywordMajor>.<br /><br />Quando Naafiri mette a segno un'eliminazione entro {{ takedownwindow }} secondi rivela i nemici nelle vicinanze e può lanciare di nuovo l'abilità. Il secondo lancio fornisce uno <shield>scudo da {{ shieldtotal }}</shield> per {{ shieldduration }} secondi.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldsize }} -> {{ shieldsizeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "NaafiriW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Siamo di più", "description": "Naafiri genera membri del branco che attaccano i bersagli dei suoi attacchi e delle sue abilità.", "image": {"full": "Icons_Naafiri_P.png", "sprite": "passive2.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}