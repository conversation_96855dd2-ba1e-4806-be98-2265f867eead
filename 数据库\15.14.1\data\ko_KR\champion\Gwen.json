{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gwen": {"id": "<PERSON>", "key": "887", "name": "그웬", "title": "신성한 재봉사", "image": {"full": "Gwen.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "887000", "num": 0, "name": "default", "chromas": false}, {"id": "887001", "num": 1, "name": "우주 그루브 그웬", "chromas": true}, {"id": "887011", "num": 11, "name": "귀염둥이 카페 그웬", "chromas": true}, {"id": "887020", "num": 20, "name": "소울 파이터 그웬", "chromas": true}, {"id": "887030", "num": 30, "name": "전투 여왕 그웬", "chromas": true}], "lore": "마법의 힘으로 살아나 인간이 된 인형 그웬은 한때 자신을 만들었던 도구를 휘두른다. 발걸음을 내디딜 때마다 자신을 만든 창조자의 사랑을 느끼며 모든 것을 감사히 여긴다. 그웬이 부리는 신성한 안개는 그웬의 가위와 바늘, 실에 축복을 내린 고대의 보호 마법이다. 모든 게 새로운 것으로 가득하지만, 그웬은 망가진 세상에서 살아남은 선한 이들을 위해 기꺼이 싸우러 나선다.", "blurb": "마법의 힘으로 살아나 인간이 된 인형 그웬은 한때 자신을 만들었던 도구를 휘두른다. 발걸음을 내디딜 때마다 자신을 만든 창조자의 사랑을 느끼며 모든 것을 감사히 여긴다. 그웬이 부리는 신성한 안개는 그웬의 가위와 바늘, 실에 축복을 내린 고대의 보호 마법이다. 모든 게 새로운 것으로 가득하지만, 그웬은 망가진 세상에서 살아남은 선한 이들을 위해 기꺼이 싸우러 나선다.", "allytips": ["늘 기본 공격을 가하세요. 추가 피해를 입히며 그웬의 기본 공격을 강화하거나 여러 스킬을 초기화할 수 있습니다.", "그웬은 신성한 안개 밖에 있는 적에게도 피해를 입힐 수 있습니다. 궁극기 사거리를 활용하면 더욱 효과적입니다.", "그웬의 몇몇 스킬은 기본 지속 효과를 다수의 적에게 적용할 수 있으니 무리 지은 적을 노려 피해와 회복 효과를 최대로 높이세요."], "enemytips": ["그웬의 신성한 안개 장막은 그웬을 한 번만 따라오며 이후 그웬이 이동하면 사라집니다.", "그웬은 대상을 적중시켜야 궁극기를 다시 사용할 수 있으니 그웬이 궁극기를 사용한 후에는 그웬의 공격에 맞지 않도록 피하세요.", "그웬은 여러 차례 공격해야 큰 피해를 입힐 수 있으니 그웬보다 앞서나가는 게 중요합니다."], "tags": ["Fighter"], "partype": "마나", "info": {"attack": 7, "defense": 4, "magic": 5, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 330, "mpperlevel": 40, "movespeed": 340, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.69}, "spells": [{"id": "GwenQ", "name": "싹둑싹둑!", "description": "그웬이 최대 6회까지 가위질하여 원뿔 범위에 마법 피해를 입힙니다. 중앙에 있는 유닛에게는 고정 피해를 입히고 적중할 때마다 기본 지속 효과를 적용합니다.", "tooltip": "<spellPassive>기본 지속 효과</spellPassive>: 그웬이 적에게 기본 공격을 적중시키면 가위질이 1회 중첩됩니다. (최대 4회, {{ buffduration }}초 동안 지속)<br /><br /><spellActive>사용 시</spellActive>: 중첩된 가위질 횟수를 소모합니다. 그웬이 한 번 가위질하여 <magicDamage>{{ miniswipedamage }}의 마법 피해</magicDamage>를 입히고, 중첩된 가위질 횟수만큼 다시 가위질한 후 마지막 가위질로 <magicDamage>{{ finalswipedamage }}의 마법 피해</magicDamage>를 입힙니다. <br /><br />가위질할 때마다 중앙에 있는 적에게는 입히는 피해의 {{ truedamageconversion*100 }}%를 <trueDamage>고정 피해</trueDamage>로 전환하고 적중 시 <spellName>가위 난도질</spellName>을 적용합니다.<br /><rules><br />미니언에게는 {{ minionmod*100 }}%의 피해를 입힙니다.<br />체력이 {{ executethreshold*100 }}% 미만인 미니언은 감소한 피해 대신 {{ executebonus }}%의 추가 피해를 입습니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "마지막 공격 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ swipedamagebase }} -> {{ swipedamagebaseNL }}"]}, "maxrank": 5, "cooldown": [6.5, 5.75, 5, 4.25, 3.5], "cooldownBurn": "6.5/5.75/5/4.25/3.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "GwenQ.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GwenW", "name": "신성한 안개", "description": "그웬이 안개를 소환하여 안개 밖에 있는 적으로부터 자신을 보호합니다. 안개에 들어오는 적만이 그웬을 대상으로 지정할 수 있습니다.", "tooltip": "그웬이 신성한 안개를 소환하여 안개 밖에 있는 모든 적(포탑 제외)으로부터 대상으로 지정될 수 없는 상태가 됩니다. 이 효과는 {{ zoneduration }}초 동안 또는 그웬이 안개를 떠날 때까지 지속됩니다. 안개 속에서는 그웬의 <scaleArmor>방어력</scaleArmor>과 <scaleMR>마법 저항력</scaleMR>이 {{ totalresists }} 증가합니다.<br /><br />이 스킬을 한 번 <recast>재사용</recast>하면 안개를 불러올 수 있습니다. 그웬이 처음으로 안개를 떠나려고 하면 스킬이 자동으로 <recast>재사용</recast>됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GwenW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GwenE", "name": "돌격가위", "description": "짧은 거리를 돌진한 후 몇 초 동안 그웬의 공격 속도, 공격 사거리, <OnHit>적중 시</OnHit> 마법 피해가 증가합니다. 이때 적에게 공격을 적중시키면 이 스킬의 재사용 대기시간을 일부 돌려받습니다. ", "tooltip": "그웬이 돌진하며 {{ buffduration }}초 동안 기본 공격을 강화합니다.<br /><br />강화된 기본 공격은 <attackSpeed>공격 속도가 {{ bonusattackspeed }}</attackSpeed>, %i:OnHit% <OnHit>적중 시</OnHit> <magicDamage>마법 피해가 {{ onhitdamage }}</magicDamage>, 사거리가 {{ bonusattackrange }} 증가합니다. 적에게 처음 적중 시 이 스킬의 재사용 대기시간을 {{ cdrefund*100 }}%만큼 돌려받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "공격 속도", "적중 시 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseattackspeed }}% -> {{ baseattackspeedNL }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GwenE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GwenR", "name": "바느질", "description": "그웬이 바늘을 던져 적중한 적에게 마법 피해를 입히고 둔화시킵니다. 챔피언에게 적중 시 가위 난도질을 적용합니다. <br><br>이 스킬은 최대 2회 더 사용할 수 있으며, 재사용할 때마다 추가 바늘을 던져 더 많은 피해를 입힙니다. ", "tooltip": "<spellActive>첫 번째 사용:</spellActive> 바늘을 던져 적중한 모든 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고, {{ debuffduration }}초 동안 {{ initialslow*-100 }}% <status>둔화</status>시키며, <spellName>가위 난도질</spellName>을 적용합니다. 이 스킬은 6초 안에 최대 2회까지 추가로 <recast>재사용</recast>할 수 있습니다. (추가 재사용 대기시간 {{ lockouttime }}초)<br /><br /><recast>두 번째 사용:</recast> 바늘을 세 개 발사하여 <magicDamage>{{ totaldamage3 }}의 마법 피해</magicDamage>를 입힙니다.<br /><recast>세 번째 사용:</recast> 바늘을 다섯 개 발사하여 <magicDamage>{{ totaldamage5 }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "기본 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "GwenR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "가위 난도질", "description": "그웬의 기본 공격이 대상의 체력에 비례해 추가 마법 피해를 입힙니다. 챔피언을 상대로 기본 공격 시 해당 피해의 일부만큼 체력을 회복합니다. ", "image": {"full": "Gwen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}