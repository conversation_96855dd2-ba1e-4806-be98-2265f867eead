{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vladimir": {"id": "Vladimir", "key": "8", "name": "Vladimir", "title": "il mietitore cremisi", "image": {"full": "Vladimir.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "8000", "num": 0, "name": "default", "chromas": false}, {"id": "8001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "8002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "8003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "8004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "8005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "8006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "8007", "num": 7, "name": "Vladimir dell'Accademia", "chromas": false}, {"id": "8008", "num": 8, "name": "<PERSON>", "chromas": true}, {"id": "8014", "num": 14, "name": "Vladimir <PERSON>atore della Notte", "chromas": true}, {"id": "8021", "num": 21, "name": "Vladimir Divoratore Cosmico", "chromas": true}, {"id": "8030", "num": 30, "name": "<PERSON>", "chromas": true}, {"id": "8039", "num": 39, "name": "<PERSON> violato", "chromas": true}, {"id": "8048", "num": 48, "name": "<PERSON>", "chromas": false}], "lore": "Un mostro assetato di sangue mortale, <PERSON> ha interferito con gli affari di Noxus sin dalla sua nascita. Oltre ad avergli allungato la vita in modo innaturale, la sua padronanza dell'emomanzia gli consente di controllare il corpo e la mente degli altri come se fossero i suoi. Nelle lussuose sale dell'aristocrazia noxiana, questo gli ha permesso di circondarsi di persone che lo venerano, mentre nei vicoli dei bassifondi usa questi poteri per dissanguare i suoi nemici.", "blurb": "Un mostro assetato di sangue mortale, <PERSON> ha interferito con gli affari di Noxus sin dalla sua nascita. Oltre ad avergli allungato la vita in modo innaturale, la sua padronanza dell'emomanzia gli consente di controllare il corpo e la mente degli...", "allytips": ["Trasfusione infligge istantaneamente i danni al nemico prima di guarire Vladimir, rendendolo uno dei migliori strumenti per finire i minion nel gioco.", "Lancia Emopiaga quando può colpire più unità possibili.", "Lago di sangue evita anche i proiettili in arrivo, per questo può essere utilizzato per evitare gli impedimenti."], "enemytips": ["Prova ad abbattere Vladimir prima che Emopiaga esploda, perché lo curerà per ogni campione nemico colpito.", "Se fai usare Lago di sangue a Vladimir all'inizio del combattimento, massimizzerai il costo in salute per l'attivazione dell'abilità.", "<PERSON><PERSON> oggetti che neutralizzano l'accumulo di salute, come Tormento di Liandry e Lama del re in rovina, sono particolarmente efficaci contro Vladimir."], "tags": ["Mage", "Fighter"], "partype": "<PERSON><PERSON><PERSON> crem<PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 7}, "stats": {"hp": 607, "hpperlevel": 110, "mp": 2, "mpperlevel": 0, "movespeed": 330, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "VladimirQ", "name": "Trasfusione", "description": "<PERSON> ruba vita dal nemico bersaglio. Quando la riserva di Vladimir è piena, danni e cure di Trasfusione aumenteranno di molto per un breve periodo.", "tooltip": "<PERSON> assorbe la forza vitale del bersaglio, infliggendo <magicDamage>{{ basedamagetooltip }} danni magici</magicDamage> e recuperando <healing>{{ basehealtooltip }} salute</healing>. Dopo aver usato questa abilità per due volte, <PERSON> guadagna <speed>{{ movementspeedonq2 }}% velocità di movimento</speed> per 0,5 secondi e potenzia l'uso successivo di questa abilità per {{ e8 }} secondi.<br /><br />La versione potenziata di questa abilità infligge <magicDamage>{{ empowereddamagetooltip }} danni magici</magicDamage> e ripristina <healing>{{ empoweredhealtooltip }} (+ {{ empoweredhealpercenttooltip }}) salute mancante aggiuntiva</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 7.9, 6.8, 5.7, 4.6], "cooldownBurn": "9/7.9/6.8/5.7/4.6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 100, 120, 140, 160], [20, 25, 30, 35, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0.04, 0.04, 0.04, 0.04, 0.04], [85, 85, 85, 85, 85], [2.5, 2.5, 2.5, 2.5, 2.5], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/100/120/140/160", "20/25/30/35/40", "0", "0", "5", "0.04", "85", "2.5", "35", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "VladimirSanguinePool", "name": "Lago di sangue", "description": "<PERSON> si scioglie in una pozza di sangue, diventando non bersagliabile per 2 secondi. In più, i nemici sulla pozza sono rallentati e <PERSON> ne assorbe la vita.", "tooltip": "<PERSON> si trasforma in una pozza di sangue per 2 secondi, guad<PERSON>nan<PERSON> <speed>{{ hasteboost*100 }}% velocità di movimento decrescente</speed> per {{ hasteduration }} secondo, diventando <keyword>non bersagliabile</keyword> e <keyword>spettrale</keyword>, e <status>rallentando</status> di un {{ movespeedmod*-100 }}% i nemici nella pozza.<br /><br /><PERSON> infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> per la durata e recupera <healing>{{ totalheal }} salute</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 25, 22, 19, 16], "cooldownBurn": "28/25/22/19/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% della salute attuale", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "VladimirSanguinePool.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "Costa {{ healthcost*100 }}% della salute attuale"}, {"id": "VladimirE", "name": "<PERSON><PERSON> di <PERSON>ue", "description": "Vladimir utilizza la sua salute per caricare una riserva di sangue che, una volta rilasciata, infligge danni nell'area che lo circonda, ma che può essere anche bloccata dalle unità nemiche.", "tooltip": "<charge>Caricamento: </charge>Vladimir accumula una riserva di sangue, sacrificando fino a <span class=\"colorCC3300\">{{ chargehealthtooltip }} salute.</span> Al massimo della carica, <PERSON> è <status>rallentato</status> del 20%.<br /><br /><release>Rila<PERSON>cio: </release>Vladimir libera una scarica di proiettili di sangue contro i nemici circostanti, infliggendo tra <magicDamage>{{ mindamagetooltip }}</magicDamage> e <magicDamage>{{ maxdamagetooltip }} danni magici</magicDamage> in base al tempo di caricamento. Se questa abilità viene caricata per almeno 1 secondo, <status>rallenta</status> inoltre i bersagli di un {{ slowpercent }}% per 0,5 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e0 }} -> {{ e0NL }}", "{{ e9 }}% -> {{ e9NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11, 9, 7, 5], "cooldownBurn": "13/11/9/7/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [30, 45, 60, 75, 90], [6, 6, 6, 6, 6], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [40, 45, 50, 55, 60], [60, 90, 120, 150, 180]], "effectBurn": [null, "0", "8", "30/45/60/75/90", "6", "150", "0", "1.5", "1", "40/45/50/55/60", "60/90/120/150/180"], "vars": [], "costType": "% della salute massima ({{ chargehealthtooltip }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "Costo canalizzazione {{ e2 }}% della salute massima ({{ chargehealthtooltip }})"}, {"id": "VladimirHemoplague", "name": "Emopiaga", "description": "Vladimir infetta un'area con una piaga virulenta. I nemici colpiti subiscono danni aumentati per la durata. Do<PERSON> al<PERSON>ni secondi, Emopiaga infligge danni magici ai nemici infettati e cura Vladimir per ogni campione nemico colpito.", "tooltip": "Vladimir genera una piaga virulenta, che fa subire alle sue vittime il {{ e2 }}% di danni aggiuntivi da tutte le fonti per {{ e4 }} secondi. <PERSON><PERSON> la scadenza, <PERSON> infligge <magicDamage>{{ damage }} danni magici</magicDamage> a tutti i bersagli infetti. Vladimir recupera <healing>{{ damage }} salute</healing> se colpisce un campione, e <healing>{{ secondaryhealingtooltip }} salute</healing> aggiuntiva per ogni campione colpito oltre al primo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [10, 10, 10], [100, 100, 100], [4, 4, 4], [40, 40, 40], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "10", "100", "4", "40", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "VladimirHemoplague.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON><PERSON> crem<PERSON>", "description": "Vladimir guadagna 1 punto di potere magico ogni 30 punti di salute bonus e 1,6 di salute bonus per ogni punto di potere magico (non cumulabili fra loro).", "image": {"full": "VladimirP.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}