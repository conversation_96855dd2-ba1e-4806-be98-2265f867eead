{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yunara": {"id": "<PERSON><PERSON>", "key": "804", "name": "<PERSON><PERSON>", "title": "Yıkılmaz <PERSON>", "image": {"full": "Yunara.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "804000", "num": 0, "name": "default", "chromas": false}, {"id": "804001", "num": 1, "name": "Ruh Çiçeği Kaplıcası Yunara", "chromas": true}], "lore": "Ionia'ya sarsılmaz bir özveriyle bağ<PERSON><PERSON> <PERSON><PERSON>, e<PERSON><PERSON><PERSON> Kinkou yadigârı Aion Er'na ile becerilerini geliştirmek için ruhlar âleminde inzivaya çekilip yüzyıllarca çalıştı. <PERSON>ü<PERSON> fedakârlıklarına ra<PERSON><PERSON>, hem bu toprakları ahenksizlikten ve ihtilaftan kurtarma ahdine hem de inancına hâlâ sadık. Ama dünyanın artık ona ihtiyacı var ve kadim bir tehdidin bir kez daha yükselen gölgesi onun iradesini enine boyuna sınayacak.", "blurb": "Ionia'ya sarsılmaz bir özveriyle ba<PERSON><PERSON><PERSON> <PERSON><PERSON>, e<PERSON><PERSON><PERSON> Kinkou yadigârı Aion Er'na ile becerilerini geliştirmek için ruhlar âleminde inzivaya çekilip yüzyıllarca çalıştı. <PERSON>ü<PERSON> fedakârlıklarına ra<PERSON><PERSON>, hem bu toprakları ahenksizlikten ve...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 275, "mpperlevel": 45, "movespeed": 325, "armor": 25, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.65}, "spells": [{"id": "YunaraQ", "name": "<PERSON><PERSON>", "description": "<PERSON>ara saldırı hızı ka<PERSON>, is<PERSON>t halinde ilave hasar verir ve saldırıları yakındaki rakiplere saçılır.", "tooltip": "<spellPassive>Pasif:</spellPassive> Yun<PERSON>'nın <PERSON> <OnHit>%i:OnHit% isabet halinde</OnHit> <magicDamage>{{ calc_passive_damage }} <PERSON><PERSON>yü Hasarı</magicDamage> verir ve <evolve>{{ resource_nonchampion }} Özgürlük</evolve> yükü (şampiyonlardan <evolve>{{ resource_champion }} Özgürlük</evolve> yükü) kazandırır.<br /><br /><spellPassive>Aktif:</spellPassive> Yun<PERSON> <evolve>{{ resource_max }} Özgürlük</evolve> yük<PERSON> tüketerek {{ buff_duration }} saniyeliğine <attackSpeed>{{ calc_attack_speed }} Saldırı Hızı</attackSpeed> kazanır ve <OnHit>%i:OnHit% isabet halinde</OnHit> fazladan <magicDamage>{{ calc_damage }} B<PERSON><PERSON><PERSON> Hasarı</magicDamage> verir. Bu esnada saldırıları yakınındaki rakiplere saçılarak <physicalDamage>{{ calc_damage_spread }} Fiziksel Hasar</physicalDamage> verir.<br /><br /><keywordMajor>Sınırları Aşmışken</keywordMajor>: Bu yetenek {{ spell.yunarar:buff_duration }} saniyeliğine anında etkinleşir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>fin <PERSON>bet Halinde Hasarı", "Saldırı Hızı", "Aktifin İsabet Halinde Hasarı"], "effect": ["{{ damage_passive }} -> {{ damage_passiveNL }}", "%{{ attack_speed*100.000000 }} -> %{{ attack_speednl*100.000000 }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraQ.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ basecost }} Mana"}, {"id": "YunaraW", "name": "Yargı Kemeri | Yıkım Kemeri", "description": "<PERSON><PERSON>, rakiplere hasar veren ve onları yavaşlatan bir dönen tespih boncuğu gönderir. Sınırları aşmışken rakiplere hasar veren ve onları yavaşlatan bir lazer ateşler.", "tooltip": "Yunara bir tespih boncuğ<PERSON> gö<PERSON>k <magicDamage>{{ calc_damage_initial }} <PERSON>üyü Hasarı</magicDamage> verir ve rakibi <status>{{ slow_duration }} saniye içinde azalarak kaybolacak şekilde {{ calc_slow }} yavaşlatır</status>. <PERSON><PERSON><PERSON> her saniye fazladan <magicDamage>{{ calc_damage_per_second }} Büyü Hasarı</magicDamage> verir.<br /><br /><keywordMajor>Sınırları Aşmışken - Yıkım Kemeri</keywordMajor>: Yunara bir lazer ateşleyerek <magicDamage>{{ spell.yunarar:calc_rw_damage }} Büyü Hasarı</magicDamage> verir ve rakibi <status>{{ spell.yunarar:rw_slow_duration }} saniye içinde azalarak kaybolacak şekilde {{ spell.yunarar:calc_rw_slow_amount }} yavaşlatır</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Saniye <PERSON>"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ damage_per_second }} -> {{ damage_per_secondNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "YunaraW.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} <PERSON>a"}, {"id": "YunaraE", "name": "Kanmei'ni<PERSON> | Dokunulmaz Gölge", "description": "Yunara azalarak kaybolacak şekilde hareket hızı kazanır ve hayalet etkisi elde eder. Sınırları aşmışken belli bir yönde atılır.", "tooltip": "Yunara {{ buff_duration }} saniye içinde azalarak kaybolacak şekilde <speed>{{ calc_move_speed }} Hareket Hızı</speed> kazanır. Hareket hızı rakip şampiyonlara doğru ilerlerken artarak <speed>{{ calc_move_speed_enhanced }}</speed> olur.<br /><br /><keywordMajor>Sınırları Aşmışken - Dokunulmaz Gölge</keywordMajor>: <PERSON><PERSON> belli bir yönde atılır.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Hareket Hızı", "<PERSON><PERSON><PERSON>"], "effect": ["%{{ move_speed*100.000000 }} -> %{{ move_speednl*100.000000 }}", "{{ buff_duration }} -> {{ buff_durationNL }}"]}, "maxrank": 5, "cooldown": [7.5, 7.5, 7.5, 7.5, 7.5], "cooldownBurn": "7.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraE.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} <PERSON>a"}, {"id": "YunaraR", "name": "Sınırlarını Aş", "description": "Yunara sınırlarını aşarak temel yeteneklerini geliştirir.", "tooltip": "Yunara {{ buff_duration }} saniyeliğine <keywordMajor>sınırlarını aşarak</keywordMajor> bu süre boyunca temel yeteneklerini geliştirir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Yıkım Kemeri | Hasar", "Dokunulmaz Gölge | Atılma Hızı"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rw_damage_base }} -> {{ rw_damage_baseNL }}", "{{ re_dash_speed }} -> {{ re_dash_speedNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraR.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Yunara'nın kritik vuruşları ilave büyü hasarı verir.", "image": {"full": "Yunara_Passive.Yunara.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}