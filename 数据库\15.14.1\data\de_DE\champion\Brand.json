{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Brand": {"id": "Brand", "key": "63", "name": "Brand", "title": "die brennende Vergeltung", "image": {"full": "Brand.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "63000", "num": 0, "name": "default", "chromas": false}, {"id": "63001", "num": 1, "name": "Apokalyptischer Brand", "chromas": false}, {"id": "63002", "num": 2, "name": "Vandalen-Brand", "chromas": false}, {"id": "63003", "num": 3, "name": "<PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "63004", "num": 4, "name": "Zombie-Brand", "chromas": false}, {"id": "63005", "num": 5, "name": "Geisterfeuer-Brand", "chromas": false}, {"id": "63006", "num": 6, "name": "Schlachtboss-Brand", "chromas": false}, {"id": "63007", "num": 7, "name": "Lichtbogen-Brand", "chromas": true}, {"id": "63008", "num": 8, "name": "Ewigkeitsdrache Brand", "chromas": true}, {"id": "63021", "num": 21, "name": "Charmeur-<PERSON>", "chromas": true}, {"id": "63022", "num": 22, "name": "Charmeur-Brand (Prestige)", "chromas": false}, {"id": "63033", "num": 33, "name": "Straßendämonen-Brand", "chromas": true}, {"id": "63042", "num": 42, "name": "Elysischer Brand", "chromas": true}], "lore": "Die als „Brand“ bekannte K<PERSON> war einst ein Stammesangehöriger des eisigen Freljords namens Kegan Rodhe und ist ein mahnendes Beispiel für die Verlockung größerer Macht. Auf der Suche nach einer der legendären Weltrunen betrog Kegan seine Kameraden und riss die Rune an sich – dies bedeutete sein sofortiges Ende. Seine Seele wurde vollständig verbrannt und sein Körper ist nur noch eine Hülle für lebende Flammen. Brand durchstreift Valoran auf der Suche nach weiteren Runen, getrieben von seiner Rache für all das Unrecht, das ihm selbst in mehreren Lebzeiten als Sterblicher nie widerfahren wäre.", "blurb": "Die als „Brand“ bekannte Kreatur war einst ein Stammesangehöriger des eisigen Freljords namens Kegan Rodhe und ist ein mahnendes Beispiel für die Verlockung größerer Macht. Auf der Suche nach einer der legendären Weltrunen betrog Kegan seine Kameraden...", "allytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON>, dass Gegner zu nahe an ihren Vasallen bleiben, indem du sie in Flammen hüllst und dann durch „Feuersbrunst“ bedro<PERSON>t.", "Brands Fähigkeiten lassen sich auf verschiedene Weise kombinieren.", "„Pyroklasmus“ springt zufällig zwischen nahen Gegnern hin und her, weshalb die Nutzung gegen kleinere Gegnergruppen effektiver ist, da ein einzelnes Ziel womöglich häufiger getroffen wird."], "enemytips": ["Brand muss mit einer Fähigkeit treffen, um eine Kombo zu beginnen. Weiche seinem „Versengen“ oder „Flammensäule“ aus, um ihn aus dem Rhythmus zu bringen.", "Vers<PERSON> dich von Verbündeten zu entfernen, wenn er „Pyroklasmus“ einsetzt. Das erste Geschoss ist anfangs sehr langsam, was deinem Team genügend Zeit geben sollte, um zu reagieren.", "Brands passive Fähigkeit macht ihn besonders effektiv, wenn er gegen mehrere G<PERSON>ner auf kleinem Raum trifft. Verteilt euch!"], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 4}, "stats": {"hp": 570, "hpperlevel": 105, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.681}, "spells": [{"id": "BrandQ", "name": "Versengen", "description": "Brand schleudert einen Feuerball, der magischen Schaden verursacht. Ist das Ziel in Flammen gehüllt, wird es durch „Versengen“ @StunDuration@&nbsp;Sekunden lang betäubt.", "tooltip": "Brand schleudert einen Feuerball, der dem ersten getroffenen Gegner <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt.<br /><br /><PERSON><PERSON> das Ziel <keywordMajor>in Flammen gehüllt</keywordMajor>, wird es {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [70, 100, 130, 160, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/100/130/160/190", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "BrandQ.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandW", "name": "Flammensäule", "description": "Nach kurzer Verzögerung erschafft Brand eine Flammensäule im Zielbereich, die an allen Gegnern magischen Schaden verursacht. Ist ein Ziel in Flammen gehü<PERSON>t, erleidet es 25&nbsp;% zusätzlichen Schaden.", "tooltip": "Brand erschafft eine Säule aus reinem <PERSON>, die <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> verursacht.<br /><br /><PERSON><PERSON><PERSON><PERSON>, die <keywordMajor>in Flammen gehüllt</keywordMajor> sind, erleiden stattdessen <magicDamage>{{ empowereddamage }}&nbsp;magischen <PERSON></magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [20, 40, 60, 80, 100], [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "20/40/60/80/100", "0.25", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BrandW.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Brand beschwört eine gewaltige Explosion bei seinem Ziel, die sich auf nahe Gegner ausbreitet und ihnen magischen Schaden zufügt. Ist das Ziel in Flammen gehüllt, breitet sich „Feuersbrunst“ auf doppelt so viele Gegner aus.", "tooltip": "Brand wirkt eine mächtige Explosion auf sein <PERSON>, die umliegenden Einheiten <magicDamage>{{ edamagecalc }}&nbsp;magischen <PERSON>haden</magicDamage> zufügt.<br /><br /><PERSON><PERSON> Ziel <keywordMajor>in <PERSON>lam<PERSON> gehüllt</keywordMajor> ist, verdoppelt sich die Reichweite der Ausbreitung.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [375, 375, 375, 375, 375], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "375", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "BrandE.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandR", "name": "Pyroklasmus", "description": "Brand entfesselt einen verheerenden Feuerwirbel, der bis zu 5-mal von <PERSON> auf Gegner in der Nähe überspringt und Gegnern bei jedem Sprung magischen Schaden zufügt. Überspringende Feuerwirbel priorisieren das Steigern von „Glut“ bei Champions. Wenn ein Z<PERSON> in Flammen gehüllt ist, verlangsamt „Pyroklasmus“ es kurzzeitig.", "tooltip": "Brand entfesselt einen verheerenden Feuerwirbel, der bis zu 5-mal von <PERSON> oder einem anderen Gegner weiterspringen kann und Gegnern bei jedem Sprung <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt. Überspringende Feuerwirbel priorisieren das Steigern von <keywordMajor>Glut</keywordMajor> bei Champions.<br /><br /><PERSON><PERSON> das Ziel <keywordMajor>in Flammen gehüllt</keywordMajor> ist, wird es kurzzeitig um {{ slowamount }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Überspringen", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "BrandR.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Glut", "description": "Brands Zauber hüllen seine Ziele in Flammen, wodurch diese über 4&nbsp;Sekunden magischen Schaden erleiden (bis zu 3&nbsp;Steigerungen). Wenn Brand einen Gegner tötet, der in Flammen gehüllt ist, erhält er Mana zurück. Wenn „Glut“ die maximalen Steigerungen auf einem Champion oder großem Monster erreicht hat, wird sie instabil. Daraufhin explodiert sie nach 2&nbsp;Sekunden, löst Zaubereffekte aus und richtet großen Schaden im Umkreis des Ziels an.", "image": {"full": "BrandP.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}