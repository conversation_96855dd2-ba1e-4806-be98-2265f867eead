{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Caitlyn": {"id": "<PERSON><PERSON><PERSON>", "key": "51", "name": "<PERSON><PERSON><PERSON>", "title": "lo sceriffo di Piltover", "image": {"full": "Caitlyn.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "51000", "num": 0, "name": "default", "chromas": true}, {"id": "51001", "num": 1, "name": "<PERSON><PERSON><PERSON> Resistenza", "chromas": false}, {"id": "51002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "51004", "num": 4, "name": "Caitlyn Guerra Artica", "chromas": false}, {"id": "51005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "51006", "num": 6, "name": "<PERSON><PERSON><PERSON> di Teste", "chromas": false}, {"id": "51010", "num": 10, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "51011", "num": 11, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "51013", "num": 13, "name": "Caitlyn Festa in Piscina", "chromas": true}, {"id": "51019", "num": 19, "name": "Caitlyn Arcade", "chromas": false}, {"id": "51020", "num": 20, "name": "Caitlyn Arcade (edizione prestigio)", "chromas": false}, {"id": "51022", "num": 22, "name": "Caitlyn dell'Accademia di Battaglia", "chromas": true}, {"id": "51028", "num": 28, "name": "<PERSON><PERSON><PERSON>: <PERSON><PERSON>", "chromas": false}, {"id": "51029", "num": 29, "name": "Caitlyn Arcade (edizione prestigio 2022)", "chromas": false}, {"id": "51030", "num": 30, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "51039", "num": 39, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "51048", "num": 48, "name": "Caitlyn DRX", "chromas": true}, {"id": "51050", "num": 50, "name": "<PERSON><PERSON><PERSON>: Comandante", "chromas": true}, {"id": "51051", "num": 51, "name": "<PERSON><PERSON><PERSON>: Comandante (edizione prestigio)", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, nota come la migliore Agente di Piltover, rappresenta la migliore occasione per liberare la città dai suoi sfuggenti criminali. <PERSON><PERSON><PERSON> è in coppia con Vi, bilanciando la natura impetuosa della sua partner. Anche se possiede un fucile hextech unico al mondo, la vera arma segreta di Caitlyn è un intelletto superiore, che le permette di tendere elaborati tranelli ai criminali così stolti da agire nella Città del progresso.", "blurb": "<PERSON><PERSON><PERSON>, nota come la migliore Agente di Piltover, rappresenta la migliore occasione per liberare la città dai suoi sfuggenti criminali. <PERSON><PERSON><PERSON> <PERSON> in coppia con Vi, bilanciando la natura impetuosa della sua partner. Anche se possiede un fucile...", "allytips": ["Usa Trappola per yordle piazzandola preventivamente in modo da averla pronta per quando inizierà un combattimento.", "Evita di usare Colpo perfetto su squadre in mischia, perché potresti colpire il bersaglio sbagliato.", "Spara la Rete calibro 90 lontano dal nemico in modo da colmare le distanze o saltare oltre i muri."], "enemytips": ["Stai dietro ai minion alleati se Caitlyn ti sta tormentando con Pacificatore di Piltover (infligge meno danni per ogni bersaglio successivo).", "Puoi intercettare il proiettile del Colpo perfetto prima che colpisca un alleato, se ti metti sulla traiettoria."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 2, "difficulty": 6}, "stats": {"hp": 580, "hpperlevel": 107, "mp": 315, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 650, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.8, "attackspeedperlevel": 4, "attackspeed": 0.681}, "spells": [{"id": "CaitlynQ", "name": "Pacificatore di Piltover", "description": "Caitlyn carica il suo fucile per 1 secondo per sparare un colpo penetrante che infligge danni fisici. Dopo aver colpito il primo bersaglio il colpo diventa piu ampio ma infligge meno danni.", "tooltip": "<PERSON><PERSON><PERSON> prepara il fucile per sparare un colpo penetrante che infligge <physicalDamage>{{ initialdamage }} danni fisici</physicalDamage>. Dopo che il proiettile colpisce il primo bersaglio, si apre in un colpo più ampio che infligge <physicalDamage>{{ secondarydamage }} danni fisici</physicalDamage>.<br /><br />I nemici rivelati da <spellName>Trappola per Yordle</spellName> subiscono sempre danni pieni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico totale", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ tadratio*100.000000 }}% -> {{ tadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1, 1, 1, 1, 1], [1.3, 1.45, 1.6, 1.75, 1.9], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1", "1.3/1.45/1.6/1.75/1.9", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "CaitlynQ.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynW", "name": "<PERSON><PERSON><PERSON><PERSON> per <PERSON>", "description": "Caitlyn piazza una trappola che, se attivata, rivela e immobilizza il campione nemico per 1,5 secondi, conferendo a Caitlyn un Colpo alla testa potenziato.", "tooltip": "<PERSON><PERSON><PERSON> prepara una trappola che <status>immobilizza</status> il primo campione che ci passa sopra per {{ e6 }} secondi e conferisce <keywordStealth>Visione magica</keywordStealth> nei suoi confronti per 3 secondi. Le trappole durano per {{ e3 }} secondi e ne possono essere attive {{ e5 }} alla volta. Questa abilità ha {{ e5 }} cariche ({{ ammorechargetime }} secondi di ricarica).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni bonus Colpo alla testa sui bersagli intrappolati", "Tempo di ricarica", "Trappole massime", "<PERSON><PERSON> trappola"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [35, 80, 125, 170, 215], [30, 35, 40, 45, 50], [3, 3, 4, 4, 5], [3, 3, 4, 4, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "35/80/125/170/215", "30/35/40/45/50", "3/3/4/4/5", "3/3/4/4/5", "1.5", "30", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CaitlynW.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynE", "name": "Rete calibro 90", "description": "<PERSON><PERSON><PERSON> spara una pesante rete per rallentare il bersaglio. Il rinculo la spinge all'indietro.", "tooltip": "<PERSON><PERSON><PERSON> spara una rete e il rinculo la spinge all'indietro. La rete <status>rallenta</status> il primo bersaglio colpito del {{ e3 }}% per {{ e2 }} secondo/i e infligge <magicDamage>{{ netdamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [1, 1, 1, 1, 1], [50, 50, 50, 50, 50], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "1", "50", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "CaitlynE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CaitlynR", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> prende tempo per indirizzare il colpo perfetto, infliggendo danni ingenti a un bersaglio, a enorme distanza. I campioni nemici possono intercettare il proiettile per il loro alleato.", "tooltip": "Caitlyn si ferma un attimo per prendere la mira e spara, infliggendo <physicalDamage>{{ rtotaldamage }} danni fisici</physicalDamage>, ma gli altri campioni nemici possono intercettarlo. Quest'abilità conferisce <keywordStealth>Visione magica</keywordStealth> del bersaglio durante la canalizzazione.<br /><br /><rules>Infligge fino a un {{ critchanceamp*100 }}% aggiuntivo in base alla probabilità di colpo critico di Caitlyn.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3500, 3500, 3500], "rangeBurn": "3500", "image": {"full": "CaitlynR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON>po alla testa", "description": "Ogni pochi attacchi o negli attacchi ai danni di nemici in trappola o nella rete, <PERSON><PERSON><PERSON> spara un Colpo alla testa che infligge danni bonus e cresce con la sua probabilità di colpo critico. Sui bersagli in trappola o nella rete, Colpo alla testa di Caitlyn ha gittata d'attacco doppia.", "image": {"full": "Caitlyn_Headshot.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}