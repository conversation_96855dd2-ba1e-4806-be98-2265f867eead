{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akshan": {"id": "<PERSON><PERSON><PERSON>", "key": "166", "name": "<PERSON><PERSON><PERSON>", "title": "the Rogue Sentinel", "image": {"full": "Akshan.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "166000", "num": 0, "name": "default", "chromas": false}, {"id": "166001", "num": 1, "name": "Cyber Pop Akshan", "chromas": true}, {"id": "166010", "num": 10, "name": "<PERSON>", "chromas": true}, {"id": "166020", "num": 20, "name": "Three <PERSON> Akshan", "chromas": false}], "lore": "<PERSON><PERSON><PERSON> al<PERSON>nya terangkat saat mengh<PERSON><PERSON> bahaya, <PERSON><PERSON><PERSON> melawan kejahatan dengan karisma yang perkasa, pembalasan demi keadilan, dan berte<PERSON>jang dada. Dia sangat mahir dalam seni tempur stealth, mampu menghilang dari pandangan musuhnya, dan muncul kembali tiba-tiba saat mereka tak menduganya. Dengan rasa keadilan yang kuat dan senjata pembalik kematian legendaris, dia membalas kejahatan yang dilakukan para scoundrel di Runeterra, sembari menja<PERSON> prinsip hidupnya: “<PERSON><PERSON> bersikap menyebalkan.”", "blurb": "<PERSON><PERSON><PERSON>nya terangkat saat men<PERSON><PERSON><PERSON> bahaya, <PERSON><PERSON><PERSON>lawan kejahatan dengan karisma yang perkasa, pembalasan demi keadilan, dan be<PERSON><PERSON>g dada. Dia sangat mahir dalam seni tempur stealth, mampu menghilang dari pandangan musuhnya, dan muncul...", "allytips": ["<PERSON><PERSON><PERSON> al<PERSON>nya terangkat saat mengh<PERSON><PERSON> bahaya, <PERSON><PERSON><PERSON> melawan kejahatan dengan karisma yang perkasa, pembalasan demi keadilan, dan berte<PERSON>jang dada. Dia sangat mahir dalam seni tempur stealth, mampu menghilang dari pandangan musuhnya, dan muncul kembali tiba-tiba saat mereka tak menduganya. Dengan rasa keadilan yang kuat dan senjata pembalik kematian legendaris, dia membalas kejahatan yang dilakukan para scoundrel di Runeterra, sembari menja<PERSON> prinsip hidupnya: “<PERSON><PERSON> bersikap menyebalkan.”"], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "Avengerang", "description": "<PERSON><PERSON><PERSON> melempar bumerang yang men<PERSON>an damage saat dilempar dan kembali, memperpanjang jangkauannya tiap kali musuh terkena.", "tooltip": "<PERSON><PERSON><PERSON> melempar bumerang yang men<PERSON> <physicalDamage>{{ finaldamage }} physical damage</physicalDamage>, memperpanjang jangkauannya tiap kali musuh terkena.<br /><br />Mengenai champion member<PERSON><PERSON> <speed>{{ totalhaste }} Move Speed</speed> yang berkurang dalam kurun waktu {{ hasteduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage Minion %", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ secondarytargetdamage*100.000000 }}%-> {{ secondarytargetdamagenl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.25, 6.5, 5.75, 5], "cooldownBurn": "8/7.25/6.5/5.75/5", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "AkshanQ.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanW", "name": "Going Rogue", "description": "<PERSON><PERSON><PERSON> secara pasif menandai champion musuh sebagai Scoundrel saat mereka membunuh champion seku<PERSON><PERSON>. <PERSON><PERSON><PERSON>han membunuh Scoundrel, dia akan membangkitkan sekutunya yang terbunuh, mendapatkan bonus gold, dan menghapus semua tanda.<br><br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> be<PERSON>lase serta mendapatkan Move Speed dan Mana Regen saat bergerak ke arah Scoundrel. <PERSON><PERSON>han segera kehilangan kamuflasenya saat tidak berada di semak atau dekat medan.", "tooltip": "{{ Spell_AkshanW_Tooltip_{{ gamemodeinteger }} }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Move Speed"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ msvalue }}-> {{ msvalueNL }}"]}, "maxrank": 5, "cooldown": [18, 14, 10, 6, 2], "cooldownBurn": "18/14/10/6/2", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "AkshanW.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanE", "name": "Heroic Swing", "description": "<PERSON><PERSON><PERSON> men<PERSON>n kait ke medan lalu berayun mengelilingi medan itu, berulang kali menembak musuh terdekat saat berayun. Dia bisa turun lebih awal atau terjatuh saat bertabrakan dengan champion atau medan.", "tooltip": "<spellActive>Cast Pertama:</spellActive> <PERSON><PERSON><PERSON> menem<PERSON>kkan kait, menempel pada medan pertama yang terkena.<br /><br /><spellActive>Cast Kedua:</spellActive> A<PERSON>han berayun mengelilingi medan, berulang kali menembak musuh terdekat dan men<PERSON><PERSON>lkan <physicalDamage>{{ asmoddamagetodeal }} physical damage</physicalDamage> per tembakan.<br /><br /><spellActive>Cast Ketiga:</spellActive> <PERSON><PERSON>han melompat dari tali, menembakkan serangan terakhir.<br /><br />Bertabrakan dengan Champion musuh atau medan akan mengakhiri ayunan lebih awal.<br /><br />Takedown Champion akan me-refresh Cooldown Ability ini.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AkshanE.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanR", "name": "Comeuppance", "description": "<PERSON><PERSON><PERSON> men<PERSON> champion musuh dan mulai menyimpan peluru. <PERSON><PERSON> <PERSON><PERSON><PERSON>, dia men<PERSON><PERSON>kkan semua peluru ters<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> damage berdasarkan Health yang hilang ke champion, minion, atau bangunan pertama yang terkena.", "tooltip": "Akshan mengunci champion dan mulai mengisi senjatanya hingga {{ channelduration }} detik, menyimpan hingga {{ numberofbullets }} peluru.<br /><br /><recast>Recast:</recast> <PERSON><PERSON>han melepaskan peluru yang tersimpan, masing-masing men<PERSON><PERSON><PERSON><PERSON> setidaknya <physicalDamage>{{ damageperbulletwithcrit }} physical damage</physicalDamage> ke musuh atau bangunan pertama yang terkena, meningkat hingga <physicalDamage>{{ maxdamageperbullet }} physical damage</physicalDamage> berdasarkan Health yang hilang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "<PERSON><PERSON><PERSON>", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ numberofbullets }}-> {{ numberofbulletsNL }}", "{{ bonusdamage }}-> {{ bonusdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "AkshanR.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dirty Fighting", "description": "Tiap tiga hantaman dari <PERSON> atau Ability <PERSON><PERSON><PERSON> damage bonus dan memberinya Shield jika targetnya champion.<br><br><PERSON><PERSON><PERSON>, dia menembakkan Serangan tambahan dengan pengurangan damage. Jika membatalkan Serangan tambahan itu, dia mendapatkan Move Speed.", "image": {"full": "akshan_p.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}