{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taric": {"id": "<PERSON><PERSON>", "key": "44", "name": "タリック", "title": "ヴァロランの守護者", "image": {"full": "Taric.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "44000", "num": 0, "name": "default", "chromas": false}, {"id": "44001", "num": 1, "name": "エメラルド タリック", "chromas": false}, {"id": "44002", "num": 2, "name": "アメジスト タリック", "chromas": false}, {"id": "44003", "num": 3, "name": "血塗られた石晶タリック", "chromas": false}, {"id": "44004", "num": 4, "name": "プールパーティ タリック", "chromas": true}, {"id": "44009", "num": 9, "name": "タリック ルーミンシールド", "chromas": true}, {"id": "44018", "num": 18, "name": "スペースグルーヴ タリック", "chromas": true}, {"id": "44027", "num": 27, "name": "運命の破壊者タリック", "chromas": true}], "lore": "タリックは守護の神髄であり、ルーンテラの生命、愛、美の守護者として驚異的な力を発揮する。軍務放棄の汚名を受けて故郷のデマーシアから放逐され、贖罪のために霊峰ターゴンに登ったタリックは、図らずも天上の存在からさらなる使命を与えられることとなった。いにしえのターゴンから力を授かったヴァロランの守護者は、密かに侵略を進めるヴォイドの穢れに対し揺るぎなき決意と共に立ち向かっている。", "blurb": "タリックは守護の神髄であり、ルーンテラの生命、愛、美の守護者として驚異的な力を発揮する。軍務放棄の汚名を受けて故郷のデマーシアから放逐され、贖罪のために霊峰ターゴンに登ったタリックは、図らずも天上の存在からさらなる使命を与えられることとなった。いにしえのターゴンから力を授かったヴァロランの守護者は、密かに侵略を進めるヴォイドの穢れに対し揺るぎなき決意と共に立ち向かっている。", "allytips": ["ブラバドのクールダウン短縮は、「フローズンハート」、「アイスボーンガントレット」、「スピリットビサージュ」といったアイテムと相性抜群で、非常に大きな相乗効果を生み出す。", "チャージ数が少ない「スターライトタッチ」の体力回復量はマナ消費量に見合わないが、「ブラバド」の恩恵を受けるために積極的に発動することも重要だ。", "「コズミックレディアンス」を瀬戸際まで温存して、その発動までの数秒間の遅延で味方を危険に晒すよりも、集団戦が始まることが分かりきっているならばすぐに発動する方が効果的だ。"], "enemytips": ["「コズミックレディアンス」は発動から効果が付与されるまで時間がかかるので、発動したのを見て距離を取るか、効果が付与されるまでに大ダメージを与えて倒しきることを心がけよう。", "「ブラバド」の効果でタリックは通常スキルのクールダウンを短縮するので、集団戦では通常攻撃されないように動き、レーン戦ではミニオンを殴りにくるところを積極的に狙おう。"], "tags": ["Support", "Tank"], "partype": "マナ", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 40, "armorperlevel": 4.3, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "TaricQ", "name": "スターライトタッチ", "description": "周囲の味方チャンピオンの体力をチャージ数に応じて回復する。ブラバド効果中に通常攻撃を行うと、「スターライトタッチ」のチャージを1つ獲得する。", "tooltip": "<spellPassive>自動効果:</spellPassive> {{ stackcooldown }}秒ごと、および<spellName>「ブラバド」</spellName>の通常攻撃が命中すると、スタックを1つ獲得する(最大{{ e6 }}スタック)。<br /><br /><spellActive>発動効果:</spellActive> 全スタックを消費して、1スタックにつき周囲の味方チャンピオンの<healing>体力を{{ healingperstack }}</healing>回復する({{ e6 }}スタックで<healing>{{ maxstackhealing }}</healing>)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最大チャージ数", "最大体力回復量"], "effect": ["{{ e6 }} -> {{ e6NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [1, 1, 1, 1, 1], [0.15, 0.3, 0.45, 0.6, 0.75], [0.75, 1.5, 2.25, 3, 3.75], [25, 50, 75, 100, 125], [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "1", "0.15/0.3/0.45/0.6/0.75", "0.75/1.5/2.25/3/3.75", "25/50/75/100/125", "1/2/3/4/5", "0", "0", "0", "0"], "vars": [], "costType": "マナ、全チャージ", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "TaricQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}マナ、全チャージ"}, {"id": "TaricW", "name": "バスティオン", "description": "自動効果: このスキルで繋がっている味方チャンピオンとタリックの物理防御が増加する。<br><br>発動効果: 指定した味方チャンピオンと繋がり、両者にシールドを付与するが、離れすぎると繋がりは一時的に消滅する。さらにタリックの全スキルは、繋がっている味方チャンピオンからも同様に発動される。", "tooltip": "<spellPassive>自動効果: </spellPassive><scaleArmor>{{ bonusarmor }}の物理防御</scaleArmor>を獲得し、自身と味方の間にこのスキルによる繋がりを形成する。お互いが近くにいる間、味方は<scaleArmor>{{ bonusarmor }}の物理防御</scaleArmor>を獲得し、タリックが使用した全スキルが繋がった味方からも発動される。<br /><br /><spellPassive>発動効果: </spellPassive>味方チャンピオンと繋がり、{{ e3 }}秒間<shield>自身の最大体力の{{ e2 }}%にあたるシールド</shield>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["物理防御増加率", "シールド付与率"], "effect": ["{{ armorbonuspercentage*100.000000 }}% -> {{ armorbonuspercentagenl*100.000000 }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [7, 8, 9, 10, 11], [2.5, 2.5, 2.5, 2.5, 2.5], [1000, 1000, 1000, 1000, 1000], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "7/8/9/10/11", "2.5", "1000", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "TaricW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TaricE", "name": "ダズル", "description": "魔法ダメージとスタン効果を持つ星のビームを構え、短い遅延の後に発動する。", "tooltip": "星のビームを放ち、{{ e3 }}秒後に爆発させて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、敵を{{ e2 }}秒間<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [90, 130, 170, 210, 250], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/130/170/210/250", "1.5", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "TaricE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TaricR", "name": "コズミックレディアンス", "description": "発動から少しして宇宙のエナジーを放ち、自身の周囲の味方を数秒間、無敵状態にする。", "tooltip": "天空の加護を呼び寄せる。{{ initialdelay }}秒後、周囲の味方チャンピオンが{{ invulnduration }}秒間、無敵状態になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "TaricR.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ブラバド", "description": "スキル使用後の通常攻撃2回が素早くなり、追加魔法ダメージを付与する。さらに通常スキルのクールダウンを短縮する。", "image": {"full": "Taric_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}