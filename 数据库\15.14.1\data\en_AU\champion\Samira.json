{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Samira": {"id": "<PERSON><PERSON>", "key": "360", "name": "<PERSON><PERSON>", "title": "the Desert Rose", "image": {"full": "Samira.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "360000", "num": 0, "name": "default", "chromas": false}, {"id": "360001", "num": 1, "name": "PsyOps Sami<PERSON>", "chromas": true}, {"id": "360010", "num": 10, "name": "Space Groove Samira", "chromas": true}, {"id": "360020", "num": 20, "name": "High Noon Samira", "chromas": true}, {"id": "360030", "num": 30, "name": "Soul Fighter Samira", "chromas": false}, {"id": "360033", "num": 33, "name": "Masque of the Black Rose Samira", "chromas": false}], "lore": "<PERSON><PERSON> stares death in the eye with unyielding confidence, seeking thrill wherever she goes. After her Shuriman home was destroyed as a child, <PERSON><PERSON> found her true calling in Noxus, where she built a reputation as a stylish daredevil taking on dangerous missions of the highest caliber. Wielding black-powder pistols and a custom-engineered blade, <PERSON><PERSON> thrives in life-or-death circumstances, eliminating any who stand in her way with flash and flair.", "blurb": "<PERSON><PERSON> stares death in the eye with unyielding confidence, seeking thrill wherever she goes. After her Shuriman home was destroyed as a child, <PERSON><PERSON> found her true calling in Noxus, where she built a reputation as a stylish daredevil taking on...", "allytips": [], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 108, "mp": 349, "mpperlevel": 38, "movespeed": 335, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "SamiraQ", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> fires a shot or swings her sword, dealing damage. If cast during Wild Rush, strike all enemies in her path upon completion.", "tooltip": "<PERSON><PERSON> fires a shot, dealing <physicalDamage>{{ damagecalc }} physical damage</physicalDamage> to the first enemy hit.<br /><br />If this Ability is cast towards an enemy in melee range, <PERSON><PERSON> will instead slash with her sword, dealing <physicalDamage>{{ damagecalc }} physical damage</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "Total AD Ratio"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ qadratio*100.000000 }}% -> {{ qadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "SamiraQ.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraW", "name": "Blade Whirl", "description": "<PERSON><PERSON> slashes around her, damaging enemies and destroying enemy missiles.", "tooltip": "<PERSON><PERSON> slashes around her for {{ slashduration }} seconds, damaging enemies twice for <physicalDamage>{{ damagecalc }} physical damage</physicalDamage> each and destroying any enemy missiles that enter the area.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [30, 28, 26, 24, 22], "cooldownBurn": "30/28/26/24/22", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "SamiraW.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraE", "name": "<PERSON>", "description": "<PERSON><PERSON> dashes through an enemy (including structures), slashing enemies she passes through and gaining Attack Speed. Killing an enemy champion refreshes this ability's cooldown.", "tooltip": "<PERSON><PERSON> dashes through an enemy (including structures), slashing enemies she passes through dealing <magicDamage>{{ dashdamage }} magic damage</magicDamage> and gaining <attackSpeed>{{ bonusattackspeed*100 }}% Attack Speed</attackSpeed> for {{ attackspeedduration }} seconds. <br /><br />If an enemy champion is killed within 3 seconds of <PERSON><PERSON> damaging them, This Ability's Cooldown is reset.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "Attack Speed"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ bonusattackspeed*100.000000 }}% -> {{ bonusattackspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraE.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraR", "name": "Inferno Trigger", "description": "<PERSON><PERSON> unleashes a torrent of shots from her weapons, wildly shooting all enemies surrounding her.", "tooltip": "<PERSON><PERSON> can only use this Ability if her current <keywordMajor>Style</keywordMajor> rating is S. Using this Ability consumes all <keywordMajor>Style</keywordMajor> rating.<br /><br /><PERSON><PERSON> unleashes a torrent of shots from her weapons, wildly shooting all enemies surrounding her 10 times over 2 seconds, each shot dealing <physicalDamage>{{ damagecalc }} physical damage</physicalDamage> and applying Life Steal at {{ lifestealmod*100 }}% effectiveness. Each shot can also critically strike.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraR.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Daredevil Impulse", "description": "<PERSON><PERSON> builds a combo by hitting attacks or abilities unique from the previous hit. <PERSON><PERSON>'s attacks in melee range deal additional magic damage. <PERSON><PERSON>'s attacks against enemies affected by <status>Immobilizing</status> effects will dash her to her attack range. If the enemy is <status>Knocked Up</status>, she also keeps them <status>Knocked Up</status> briefly.", "image": {"full": "SamiraP.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}