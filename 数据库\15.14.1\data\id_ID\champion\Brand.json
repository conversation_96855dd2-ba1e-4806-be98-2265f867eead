{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Brand": {"id": "Brand", "key": "63", "name": "Brand", "title": "the Burning Vengeance", "image": {"full": "Brand.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "63000", "num": 0, "name": "default", "chromas": false}, {"id": "63001", "num": 1, "name": "Apocalyptic Brand", "chromas": false}, {"id": "63002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "63003", "num": 3, "name": "Cryocore Brand", "chromas": false}, {"id": "63004", "num": 4, "name": "Zombie Brand", "chromas": false}, {"id": "63005", "num": 5, "name": "Spirit Fire Brand", "chromas": false}, {"id": "63006", "num": 6, "name": "Battle Boss Brand", "chromas": false}, {"id": "63007", "num": 7, "name": "Arclight Brand", "chromas": true}, {"id": "63008", "num": 8, "name": "Eternal Dragon Brand", "chromas": true}, {"id": "63021", "num": 21, "name": "Debonair Brand", "chromas": true}, {"id": "63022", "num": 22, "name": "Prestige Debonair Brand", "chromas": false}, {"id": "63033", "num": 33, "name": "Street Demons Brand", "chromas": true}, {"id": "63042", "num": 42, "name": "Empyrean Brand", "chromas": true}], "lore": "Dulu dia berna<PERSON>, anggota suku <PERSON> yang sedingin es. Kini makhluk yang dikenal sebagai Brand ini jadi contoh nyata dari godaan kekuatan yang besar. <PERSON><PERSON> pencarian salah satu World Rune legendaris, <PERSON><PERSON> mengkh<PERSON><PERSON> teman-temannya dan merebut rune itu untuk dirinya sendiri. <PERSON><PERSON>, pria itu musnah. <PERSON><PERSON><PERSON> terbakar habis, tubuhnya menjadi pembawa api yang hidup. Brand kini menjelajahi Valoran untuk mencari Rune yang lain, dan bersumpah untuk membalas kesalahan yang tak mungkin dia derita dalam belasan kehidupan fana.", "blurb": "<PERSON><PERSON> dia be<PERSON><PERSON>, anggota suku <PERSON> yang sedingin es. Kini makhluk yang dikenal sebagai Brand ini jadi contoh nyata dari godaan kekuatan yang besar. <PERSON><PERSON> pencarian salah satu World Rune legendaris, <PERSON><PERSON> meng<PERSON><PERSON> teman-temannya dan...", "allytips": ["Kamu bisa mencegah musuh mendekati minion mereka dengan membakarnya menggunakan Conflagration.", "Kamu bisa menggunakan ability Brand dengan berbagai kombinasi untuk memaksimalkan damage di berbagai situasi.", "Pyroclasm memantul acak di antara musuh, jadi gunakan pada sekelompok kecil musuh jika ingin mengenai target yang sama berkali-kali."], "enemytips": ["Brand harus melancarkan Ability sebelum combo-nya bisa dimulai. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> at<PERSON> Pillar of Flame akan mengganggu ritmenya.", "<PERSON><PERSON><PERSON> menjauhi sekutu saat kamu melihat Pyroclasm di-cast. Kecepatan awal misilnya pelan, se<PERSON>ga timmu punya waktu untuk bereaksi.", "<PERSON><PERSON>f <PERSON> membuatnya efektif melawan tim yang bergerombol. Jadi pastikan untuk berpencar saat melawannya."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 4}, "stats": {"hp": 570, "hpperlevel": 105, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.681}, "spells": [{"id": "BrandQ", "name": "<PERSON><PERSON>", "description": "Brand meluncurkan bola api ke depan yang menghasilkan magic damage. Jika target <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> akan menyeba<PERSON>kan stun ke target selama @StunDuration@ detik.", "tooltip": "Brand meluncurkan bola api yang men<PERSON>an <magicDamage>{{ totaldamage }} magic damage</magicDamage> pada musuh pertama yang terkena.<br /><br />Jika target <keywordMajor>Ablaze</keywordMajor>, mereka akan <status>Terkena <PERSON>un</status> selama {{ stunduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [70, 100, 130, 160, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/100/130/160/190", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "BrandQ.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandW", "name": "Pillar of Flame", "description": "<PERSON><PERSON><PERSON> be<PERSON> saat, Brand menciptakan Pillar of Flame di area target, menghasilkan magic damage ke unit musuh di area tersebut. Unit yang terbakar mendapat 25% damage tambahan.", "tooltip": "Brand menciptakan pilar api murni, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Unit yang <keywordMajor>Terbakar</keywordMajor> menerima <magicDamage>{{ empowereddamage }} damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [20, 40, 60, 80, 100], [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "20/40/60/80/100", "0.25", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BrandW.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandE", "name": "Conflagration", "description": "Brand memunculkan ledakan kuat pada targetnya yang menyebar ke musuh di sekitar, men<PERSON><PERSON>lkan magic damage. <PERSON>ka target te<PERSON><PERSON><PERSON>, penyebaran Conflagration berlipat ganda.", "tooltip": "Brand memberikan ledakan kuat pada targetnya, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ edamagecalc }} magic damage</magicDamage> ke musuh di sekitar.<br /><br />Jika target <keywordMajor><PERSON><PERSON><PERSON><PERSON></keywordMajor>, jang<PERSON><PERSON> penyebarannya berlipat ganda.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [375, 375, 375, 375, 375], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "375", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "BrandE.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandR", "name": "Pyroclasm", "description": "Brand melepaskan gelombang api dahsyat yang memantul hingga 5 kali dari dirinya dan musuh di sekitar, mengh<PERSON>lkan magic damage setiap kali memantul. Pantulannya memprioritaskan stack Blaze sampai maksimum pada Champion. Jika target te<PERSON><PERSON><PERSON>, Pyroclasm akan menerapkan slow sesaat.", "tooltip": "Brand melepaskan gelombang api dahsyat yang memantul hingga 5 kali ke dirinya atau musuh lain, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> ke musuh setiap kali memantul. Pantulannya memprioritaskan stack <keywordMajor>Blaze</keywordMajor> sampai maksimum pada champion.<br /><br />Jika target <keywordMajor>Te<PERSON><PERSON>r</keywordMajor>, mereka akan terkena <status>Slow</status> sesaat sebesar {{ slowamount }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage per Bounce", "Slow", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ slowamount }}%-> {{ slowamountNL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "BrandR.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Blaze", "description": "Spell Brand membakar targetnya, mengh<PERSON>lkan damage selama 4 detik, bisa stack hingga 3 kali. Jika Brand membunuh musuh yang sedang terbakar, dia mendapatkan kembali mana. Saat Blaze mencapai stack maksimum pada Champion atau monster besar, <PERSON> menjadi tidak stabil. Blaze akan meledak dalam 2 detik, menerapkan efek spell dan menghasilkan damage besar dalam 1 area di sekitar korban.", "image": {"full": "BrandP.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}