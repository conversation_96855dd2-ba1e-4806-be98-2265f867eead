{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "Fizz", "title": "șarlat<PERSON>l a<PERSON>", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "Fizz din Atlantida", "chromas": false}, {"id": "105002", "num": 2, "name": "Fizz din tundră", "chromas": false}, {"id": "105003", "num": 3, "name": "Fizz pescar", "chromas": false}, {"id": "105004", "num": 4, "name": "Fizz din Vid", "chromas": false}, {"id": "105008", "num": 8, "name": "Fizz iepuraș", "chromas": false}, {"id": "105009", "num": 9, "name": "Fizz super galactic", "chromas": false}, {"id": "105010", "num": 10, "name": "Fizz din Legiunea Omega", "chromas": true}, {"id": "105014", "num": 14, "name": "Fizz pufi", "chromas": false}, {"id": "105015", "num": 15, "name": "Fizz pufi (Prestigiu)", "chromas": false}, {"id": "105016", "num": 16, "name": "Fizz, micuțul diavol", "chromas": true}, {"id": "105025", "num": 25, "name": "Fizz pufi (Prestigiu) – 2022", "chromas": false}, {"id": "105026", "num": 26, "name": "Fizz astronaut", "chromas": true}, {"id": "105035", "num": 35, "name": "Fizz, păstorul ploii", "chromas": true}], "lore": "Fizz este un yordle amfibian, care trăiește în recifurile din jurul Bilgewaterului. Deseori se scufundă după jertfele aruncate în ocean de căpitanii superstițioși și le aduce înapoi, însă chiar și cei mai nesăbuiți marinari știu că n-ar trebui să-l supere, deoarece nu puține sunt poveștile de groază despre cei care l-au subestimat pe micul amfibian. Crezut a fi un spirit capricios al oceanelor, Fizz poate controla bestiile din adâncuri și-și petrece timpul distrându-se pe seama prietenilor sau dușmanilor.", "blurb": "Fizz este un yordle amfibian, care trăiește în recifurile din jurul Bilgewaterului. Deseori se scufundă după jertfele aruncate în ocean de căpitanii superstițioși și le aduce înapoi, însă chiar și cei mai nesăbuiți marinari știu că n-ar trebui să-l...", "allytips": ["Din moment ce Fizz se poate deplasa prin unități, când ești pe culoar caută oportunități de a trece prin minioni și a folosi pasiva abilității ''Trident din ambră marină'', apoi folosește activa abilității după câteva secunde.", "Abilitatea supremă a lui Fizz, ''<PERSON>run<PERSON>a momelii'', poate fi lansată spre un inamic sau spre zona în care crezi că se va deplasa acesta.", "Vrăjile lui Fizz capătă mai multă forță în funcție de puterea abilităților, așa că înarmează-te fie cu obiecte precum ''Clepsidra Zhonyei'' sau ''Vălul ursitoarei'' împotriva echipelor care te pot ucide repede, fie cu obiecte precum ''Urgia de pe lume'' sau ''Pălăria malefică a lui Rabadon'' în cazul în care crezi că poți supraviețui fără viață în plus."], "enemytips": ["Atacurile lui Fizz devin mai periculoase timp de câteva secunde după ce își folosește atacul îmbunătățit – ține-te departe de el cât timp îi strălucește tridentul!", "Fizz îți poate scăpa ușor printre degete atunci când abilitățile lui nu se află în perioada de reactivare. Păcălește-l să le folosească de la început, apoi folosește efecte de control al maselor sau lovește-l cu atacuri puternice!"], "tags": ["Assassin", "Fighter"], "partype": "Mană", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "Lovitura ariciului-de-mare", "description": "Fizz se năpustește prin țintă, provocându-i daune magice și aplicându-i efecte la impact.", "tooltip": "Fizz se năpustește printr-un inamic, provocându-i <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> și <magicDamage>{{ qdamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzW", "name": "Trident din ambră marină", "description": "Țintele atacurilor lui Fizz sângerează și suferă daune magice timp de câteva secunde. Fizz își poate îmbunătăți următorul atac de bază pentru a provoca daune bonus și pentru a-și îmbunătăți următoarele atacuri pentru o perioadă scurtă.", "tooltip": "<spellPassive><PERSON><PERSON><PERSON><PERSON></spellPassive>: țintele atacurilor lui Fizz sângerează și suferă <magicDamage>{{ dotdamage }} daune magice</magicDamage> de-a lungul a {{ bleedduration }} secunde. <br /><br /><spellActive>Activă</spellActive>: următorul atac al lui Fizz provoacă încă <magicDamage>{{ activedamage }} daune magice</magicDamage>. Dacă atacul ucide ținta, Fizz primește înapoi <scaleMana>{{ onkillmanarefund }} mană</scaleMana> și își reduce timpul de reactivare al abilității la {{ onkillnewcooldown }} secundă. Dacă nu o ucide, atacurile lui Fizz provoacă încă <magicDamage>{{ onhitbuffdamage }} daune magice</magicDamage> timp de {{ onhitbuffduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune pasive", "Daune activă", "Daune la impact", "<PERSON><PERSON><PERSON>", "Cost de @AbilityResourceName@", "Timp de reactivare"], "effect": ["{{ dotbasedamage }} -> {{ dotbasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }} -> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }} -> {{ onkillmanarefundNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzE", "name": "Ghi<PERSON>ie / Viclenie", "description": "Fizz sare și face acro<PERSON><PERSON><PERSON> în aer, apoi se proptește în trident și devine imposibil de țintit. Din această poziție, Fizz poate să aterizeze izbind pământul sau poate să sară din nou înainte de a ateriza în forță.", "tooltip": "Fizz se sprijină în trident, devenind imposibil de țintit timp de 0,75 secunde, după care le provoacă <magicDamage>{{ edamage }} daune magice</magicDamage> inamicilor din apropiere și îi <status>încetinește</status> cu {{ slowamount*100 }}% timp de {{ slowduration }} secunde. <br /><br />Fizz poate <recast>refolosi</recast> abilitatea când este imposibil de țintit pentru a se năpusti din nou, ceea ce încheie efectul mai devreme și provoacă daune într-o zonă mai mică, făr<PERSON> să <status>încetinească</status> inamicii.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Cost de @AbilityResourceName@", "Daune", "Încetinire"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzR", "name": "<PERSON><PERSON><PERSON><PERSON> mom<PERSON>i", "description": "Fizz aruncă într-o direcție un pește care se lipește de orice campion pe care-l atinge, încetinindu-l. Dup<PERSON> un scurt timp, din subteran apare un rechin, care aruncă ținta în sus și îi dă la o parte pe ceilalți inamici aflați în apropiere. Atacul le provoacă daune magice tuturor inamicilor afectați și îi încetinește.", "tooltip": "Fizz aruncă un pește care se lipește de primul campion lovit. Victima este afectată de <keywordStealth>''viziune supranaturală''</keywordStealth> și este <status>încetinită</status> cu între 40% și 80% în funcție de distanța parcursă de pește înainte de atașare. <br /><br />Du<PERSON><PERSON> {{ detonationtime }} secunde, un rechin atacă ținta, <status>aruncând în sus</status> ținta cu peștele timp de 1 secundă, <status>aruncând în spate</status> celelalte unități și provocând între <magicDamage>{{ smallsharkdamage }} și {{ bigsharkdamage }} daune magice</magicDamage>, în funcție de distanța parcursă de pește înainte de atașare.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune rechin mic", "<PERSON>une rechin mij<PERSON>iu", "Daune rechin mare"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ smalldamage }} -> {{ smalldamageNL }}", "{{ middamage }} -> {{ middamageNL }}", "{{ bigdamage }} -> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON> agil", "description": "Fizz poate trece prin unități și suferă daune reduse cu o valoare fixă din toate sursele.", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}