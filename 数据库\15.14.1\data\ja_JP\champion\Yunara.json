{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yunara": {"id": "<PERSON><PERSON>", "key": "804", "name": "ユナラ", "title": "揺るがぬ誓い", "image": {"full": "Yunara.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "804000", "num": 0, "name": "default", "chromas": false}, {"id": "804001", "num": 1, "name": "精霊の花祭り 湯けむりユナラ", "chromas": true}], "lore": "アイオニアへの揺るぎない忠誠心を胸にユナラは精霊界に身を隠し、均衡の守人に代々受け継がれる遺物「アイオン・エルナ」で修行を重ねた。あらゆるものを犠牲にしてなお、調和を乱すものを排し、争いを絶つという誓いは揺るがず、その信仰もまた決して失われていない。だが、彼女を待ち受ける世界と、再び蘇った古の脅威の影は、ユナラの決意のすべてを試すことになる。", "blurb": "アイオニアへの揺るぎない忠誠心を胸にユナラは精霊界に身を隠し、均衡の守人に代々受け継がれる遺物「アイオン・エルナ」で修行を重ねた。あらゆるものを犠牲にしてなお、調和を乱すものを排し、争いを絶つという誓いは揺るがず、その信仰もまた決して失われていない。だが、彼女を待ち受ける世界と、再び蘇った古の脅威の影は、ユナラの決意のすべてを試すことになる。", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "マナ", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 275, "mpperlevel": 45, "movespeed": 325, "armor": 25, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.65}, "spells": [{"id": "YunaraQ", "name": "精神修養", "description": "攻撃速度が増加し、通常攻撃時効果で追加ダメージを与え、通常攻撃が周囲の敵に拡散する。", "tooltip": "<spellPassive>自動効果</spellPassive>: <OnHit>%i:OnHit%通常攻撃時効果</OnHit>で<magicDamage>{{ calc_passive_damage }}の魔法ダメージ</magicDamage>を与え、通常攻撃で<evolve>「解放」を{{ resource_nonchampion }}</evolve>(チャンピオンなら<evolve>{{ resource_champion }}</evolve>)獲得する。<br /><br /><spellPassive>発動効果</spellPassive>: <evolve>「解放」を{{ resource_max }}</evolve>消費して、{{ buff_duration }}秒間、<attackSpeed>攻撃速度が{{ calc_attack_speed }}</attackSpeed>増加し、<OnHit>%i:OnHit%通常攻撃時効果</OnHit>で<magicDamage>{{ calc_damage }}の魔法ダメージ</magicDamage>を追加で与える。この効果時間中、通常攻撃が周囲の敵に拡散し、<physicalDamage>{{ calc_damage_spread }}の物理ダメージ</physicalDamage>を与える。<br /><br /><keywordMajor>超越状態</keywordMajor>: このスキルが即座に発動可能になり{{ spell.yunarar:buff_duration }}秒間持続する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果の通常攻撃時ダメージ", "攻撃速度", "発動効果の通常攻撃時ダメージ"], "effect": ["{{ damage_passive }} -> {{ damage_passiveNL }}", "{{ attack_speed*100.000000 }}% -> {{ attack_speednl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraQ.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ basecost }}マナ"}, {"id": "YunaraW", "name": "裁きの弧 | 滅びの弧", "description": "回転する珠を撃ち出し、敵にダメージとスロウ効果を与える。超越状態では、レーザー状に精霊魔法を発射して、敵にダメージとスロウ効果を与える。", "tooltip": "珠を掴み取って投げ、<magicDamage>{{ calc_damage_initial }}の魔法ダメージ</magicDamage>と<status>{{ calc_slow }}のスロウ効果</status>を与える。このスロウ効果は{{ slow_duration }}秒かけて元に戻る。珠は追加で毎秒<magicDamage>{{ calc_damage_per_second }}の魔法ダメージ</magicDamage>を与える。<br /><br /><keywordMajor>超越状態 - 滅びの弧</keywordMajor>: レーザー状に精霊魔法を発射して<magicDamage>{{ spell.yunarar:calc_rw_damage }}の魔法ダメージ</magicDamage>と<status>{{ spell.yunarar:calc_rw_slow_amount }}のスロウ効果</status>を与える。このスロウ効果は{{ spell.yunarar:rw_slow_duration }}秒かけて元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "毎秒ダメージ"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ damage_per_second }} -> {{ damage_per_secondNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "YunaraW.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }}マナ"}, {"id": "YunaraE", "name": "カンメイの歩み | 触れ得ぬ影", "description": "移動速度が増加してゴースト化する。移動速度は徐々に元に戻る。超越状態では、指定方向にダッシュする。", "tooltip": "<speed>移動速度が{{ calc_move_speed }}</speed>増加する。敵チャンピオンに向かっている場合は<speed>移動速度が{{ calc_move_speed_enhanced }}</speed>まで増加する。この移動速度は{{ buff_duration }}秒かけて元に戻る。<br /><br /><keywordMajor>超越状態 - 触れ得ぬ影</keywordMajor>: 指定方向にダッシュする。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "効果時間"], "effect": ["{{ move_speed*100.000000 }}% -> {{ move_speednl*100.000000 }}%", "{{ buff_duration }} -> {{ buff_durationNL }}"]}, "maxrank": 5, "cooldown": [7.5, 7.5, 7.5, 7.5, 7.5], "cooldownBurn": "7.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraE.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }}マナ"}, {"id": "YunaraR", "name": "自己超越", "description": "超越状態になり、通常スキルがアップグレードされる。", "tooltip": "{{ buff_duration }}秒間、<keywordMajor>超越状態</keywordMajor>になり、効果時間中は通常スキルがアップグレードされる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "滅びの弧 | ダメージ", "触れ得ぬ影 | ダッシュ速度"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rw_damage_base }} -> {{ rw_damage_baseNL }}", "{{ re_dash_speed }} -> {{ re_dash_speedNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraR.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "始まりの地への誓い", "description": "クリティカル攻撃が追加魔法ダメージを与える。", "image": {"full": "Yunara_Passive.Yunara.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}