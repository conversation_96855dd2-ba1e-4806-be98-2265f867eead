{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Twitch": {"id": "Twitch", "key": "29", "name": "Twitch", "title": "Szczur Zarazy", "image": {"full": "Twitch.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "29000", "num": 0, "name": "default", "chromas": false}, {"id": "29001", "num": 1, "name": "Twitch Król Podziemia", "chromas": false}, {"id": "29002", "num": 2, "name": "Olimpijski Twitch", "chromas": false}, {"id": "29003", "num": 3, "name": "Średniowieczny Twitch", "chromas": true}, {"id": "29004", "num": 4, "name": "Twitch z Miasta Zbrodni", "chromas": false}, {"id": "29005", "num": 5, "name": "Twitch <PERSON>l", "chromas": false}, {"id": "29006", "num": 6, "name": "Kieszonkowiec Twitch", "chromas": false}, {"id": "29007", "num": 7, "name": "SSW Twitch", "chromas": false}, {"id": "29008", "num": 8, "name": "Twitch z Oddziału Omega", "chromas": true}, {"id": "29012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "29027", "num": 27, "name": "Twitch Kroczycień", "chromas": false}, {"id": "29036", "num": 36, "name": "Pogromca Smoków Twitch", "chromas": false}, {"id": "29045", "num": 45, "name": "Twitch w Samo Południe", "chromas": false}, {"id": "29055", "num": 55, "name": "Serowy Kucharz Twitch", "chromas": false}, {"id": "29064", "num": 64, "name": "Twitch Basenowej Imprezy", "chromas": false}], "lore": "Zauński szczur zarazy z urodzenia, lecz koneser obrzydlistw z pasji, Twitch nie boi ubrudzić sobie łap. Wymierza swoją zasilaną chemikaliami kuszę w złocone serce Piltover i przysięga dowieść wszystkim w mieście wyżej, jak bar<PERSON><PERSON> s<PERSON>awi. Zawsze szczwanie szczwany, a kiedy nie kręci się po Slumsach, pewnie tkwi po pas w śmieciach innych ludzi, szukając wyrzuconych skarbów... i spleśniałych kanapek.", "blurb": "Zauński szczur zarazy z urodzenia, lecz koneser obrzydlistw z pasji, Twitch nie boi ubrudzić sobie łap. Wymierza swoją zasilaną chemikaliami kuszę w złocone serce Piltover i przysięga dowieść wszystkim w mieście wyżej, jak bardzo są plugawi. Zawsze...", "allytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON>ć ataku Twitcha jest jedną z największych w całej grze. Kupuj przedmioty z efektami przy trafieniu, takie jak Czarny Tasak lub Koniec Rozumu.", "Skażenie ma duży zasięg. Zatruj przeciwników jak najbardziej za pomocą Śmiertelnego Jadu, zanim go użyjesz.", "<PERSON><PERSON><PERSON><PERSON> przeciwn<PERSON>, znajdującego się poza zasięgiem, d<PERSON><PERSON><PERSON> Jadu."], "enemytips": ["Twitch jest delikatny. Współpracuj z sojusznikami, aby zabi<PERSON> go s<PERSON>, gdy nie używa Kamuflażu.", "<PERSON>zne Tarcze nie zablokują obrażeń Śmiertelnego Jadu, ale zneutralizują efe<PERSON>y, j<PERSON><PERSON> może nim wy<PERSON>łać.", "<PERSON><PERSON><PERSON>, <PERSON><PERSON> opuścił aleję, powiadom sojuszników, pisz<PERSON><PERSON> „ss”."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 7.25, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.1, "attackspeedperlevel": 3.38, "attackspeed": 0.679}, "spells": [{"id": "TwitchHideInShadows", "name": "Zasadzka", "description": "Twitch na krótki czas zyskuje kamuflaż i premię do prędkości ruchu. Gdy z powrotem staje się widoczny, na krótki czas zyskuje premię do prędkości ataku.<br><br>Kiedy zginie wrogi bohater będący pod wpływem Śmiertelnego Jadu, czas odnowienia Zasadzki zostanie zresetowany.", "tooltip": "Twitch <keywordStealth>ukrywa się</keywordStealth> i zyskuje <speed>{{ e3 }}% prędkości ruchu</speed> na {{ e2 }} sek. Prędko<PERSON>ć ruchu zwiększa się do {{ e3 }}%, gdy Twitch znajduje się w pobliżu wrogiego bohatera, który go nie widzi. Po wyjściu z <keywordStealth>ukrycia</keywordStealth> Twitch zyskuje <attackSpeed>{{ e1 }}% prędkości ataku</attackSpeed> na {{ e6 }} sek.<br /><br />G<PERSON> wrogi bohater, kt<PERSON>ry był dotkni<PERSON>ty <keywordMajor>Jade<PERSON></keywordMajor>, ginie, czas odnowienia tej umiejętności zostaje odświeżony.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas trwania Kamuflażu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}% -> {{ e1NL }}%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [45, 50, 55, 60, 65], [10, 11, 12, 13, 14], [30, 30, 30, 30, 30], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [500, 500, 500, 500, 500], [1000, 1000, 1000, 1000, 1000], [30, 30, 30, 30, 30]], "effectBurn": [null, "45/50/55/60/65", "10/11/12/13/14", "30", "1", "1", "6", "3", "500", "1000", "30"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TwitchHideInShadows.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TwitchVenomCask", "name": "Beczka Jadu", "description": "Twitch rzuca wybuchającą Beczkę Jadu, która spowalnia znajdujące się w jej zasięgu cele i nakłada na nie Śmiertelny Jad.", "tooltip": "Twitch ciska be<PERSON>, kt<PERSON>ra nakłada ładunek <spellName>Śmiertelnego Jadu</spellName> na wszystkich trafionych wrogów i na {{ duration }} sek. pozostawia toksyczną chmurę.<br /><br />Wrogowie znajdujący się w chmurze są <status>spowolnieni</status> o {{ totalslowamount }}%, a ponadto co sekundę otrzymują dodatkowy ładunek <spellName>Śmiertelnego Jadu</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Spowolnienie prędkości ruchu", "Czas odnowienia"], "effect": ["{{ baseslowamount }}% -> {{ baseslowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "TwitchVenomCask.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TwitchExpunge", "name": "Skażenie", "description": "Twitch zadaje potężne obrażenia zatrutym wrogom, zarażając ich swoimi chorobami.", "tooltip": "Zadaje <physicalDamage>{{ basedamage }} pkt. obrażeń fizycznych</physicalDamage> wszystkim pobliskim wrogom dotkniętym <spellName>Śmiertelnym Jadem</spellName> oraz dodatko<PERSON> <physicalDamage>{{ physicaldamageperstack }} pkt. obrażeń fizycznych</physicalDamage> i <magicDamage>{{ magicdamageperstack }} pkt. obrażeń magicznych</magicDamage> za każdy <spellName>Śmiertelnego Jadu</spellName>.<br /><br />Maks. obrażenia: <physicalDamage>{{ maxphysicaldamage }} pkt. obrażeń fizycznych</physicalDamage> i <magicDamage>{{ maxmagicdamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Obrażenia za ładunek", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basephysicaldamageperstack }} -> {{ basephysicaldamageperstackNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchExpunge.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TwitchFullAutomatic", "name": "Walę na Oślep", "description": "Twitch przestawia swoją kuszę na pełną moc, wys<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bełty, które przebijają wszystkich wrogów na dużą odległość.", "tooltip": "Twitch przestawia swoją kuszę na pełną moc, z<PERSON><PERSON><PERSON><PERSON><PERSON> {{ bonusrange }} jedn. zasięgu ataku i <scaleAD>{{ bonusad }} pkt. obrażeń od ataku</scaleAD> oraz spra<PERSON>, że jego ataki na {{ duration }} sek. stają się przeszywającymi pociskami. Pociski trafiają wszystkich wrogów, przez kt<PERSON><PERSON>ch przenikną, ale zadają o {{ falloffdamage*100 }}% mniej obrażeń każdemu kolejnemu celowi (min. {{ minimumfalloffdamage*100 }}% obrażeń).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od ataku"], "effect": ["{{ bonusad }} -> {{ bonusadNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchFullAutomatic.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Śmiertelny Jad", "description": "Podstawowe ataki Twitcha zatruwają jego cel, zadając co sekundę nieuchronne obrażenia.", "image": {"full": "Twitch_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}