{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MasterYi": {"id": "MasterYi", "key": "11", "name": "Master <PERSON>", "title": "lo spadaccino Wuju", "image": {"full": "MasterYi.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "11000", "num": 0, "name": "default", "chromas": false}, {"id": "11001", "num": 1, "name": "Master <PERSON>", "chromas": false}, {"id": "11002", "num": 2, "name": "Master <PERSON>", "chromas": false}, {"id": "11003", "num": 3, "name": "Master <PERSON>", "chromas": false}, {"id": "11004", "num": 4, "name": "Samurai Yi", "chromas": false}, {"id": "11005", "num": 5, "name": "Master <PERSON>", "chromas": true}, {"id": "11009", "num": 9, "name": "PROGETTO: Yi", "chromas": false}, {"id": "11010", "num": 10, "name": "Master <PERSON>", "chromas": false}, {"id": "11011", "num": 11, "name": "Yi Spada Eterna", "chromas": true}, {"id": "11017", "num": 17, "name": "Yi Pupazzo di neve", "chromas": true}, {"id": "11024", "num": 24, "name": "Master <PERSON>", "chromas": false}, {"id": "11033", "num": 33, "name": "Master Yi OPSI", "chromas": false}, {"id": "11042", "num": 42, "name": "Master <PERSON>", "chromas": false}, {"id": "11052", "num": 52, "name": "Master <PERSON>e", "chromas": false}, {"id": "11053", "num": 53, "name": "Master <PERSON> (edizione prestigio)", "chromas": true}, {"id": "11089", "num": 89, "name": "Master <PERSON>", "chromas": false}, {"id": "11096", "num": 96, "name": "Master <PERSON> c<PERSON>", "chromas": false}, {"id": "11106", "num": 106, "name": "Master <PERSON>", "chromas": false}], "lore": "Master Yi è temprato nel corpo e nella mente al punto tale che il suo pensiero e le sue azioni sono quasi una cosa sola. Se<PERSON>ne faccia ricorso alla violenza solo come ultima risorsa, la grazia e la velocità della sua lama assicurano sempre una conclusione rapida. Come uno degli ultimi praticanti dell'arte di Ionia del Wuju, Yi ha consacrato la sua vita alla prosecuzione dell'eredità della sua gente, valutando potenziali nuovi discepoli con le Sette lenti dell'intuito per individuare il più degno.", "blurb": "Master Yi è temprato nel corpo e nella mente al punto tale che il suo pensiero e le sue azioni sono quasi una cosa sola. Se<PERSON>ne faccia ricorso alla violenza solo come ultima risorsa, la grazia e la velocità della sua lama assicurano sempre una...", "allytips": ["Se sei in corsia contro dei giocatori con dei campioni specializzati in attacchi a distanza, alzare di livello Meditazione ti permetterà di stare in corsia di più e guadagnare livelli più velocemente dei tuoi avversari.", "Stile Wuju è molto efficiente all'inizio per finire i minion.", "Prova a usare Colpo alfa sul minion di fronte a un campione nemico in modo da ritornare a una posizione di sicurezza alla fine dell'abilità."], "enemytips": ["Meditazione è un metodo efficace di guarire i danni nel tempo, ma <PERSON> è suscettibile agli assalti coordinati all'inizio delle partite.", "Se Master Yi cerca di finire i minion con Col<PERSON> al<PERSON>, colpiscilo un paio di volte in modo che sia costretto a usare il mana con Meditazione per guarire.", "Anche se Master Yi non può essere rallentato quando è in Agilità superiore, lanciare un impedimento può sempre essere utile."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 4, "magic": 2, "difficulty": 4}, "stats": {"hp": 669, "hpperlevel": 105, "mp": 251, "mpperlevel": 42, "movespeed": 355, "armor": 33, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.65, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.8, "attackspeedperlevel": 2.5, "attackspeed": 0.679}, "spells": [{"id": "AlphaStrike", "name": "<PERSON><PERSON> al<PERSON>", "description": "Master Yi si teletrasporta in giro per il campo di battaglia ad altissima velocità, infliggendo danni fisici a più unità sul suo percorso senza essere bersagliabile. Colpo alfa può infliggere colpi critici e infligge danni bonus ai mostri. Gli attacchi base riducono la ricarica di Colpo alfa.", "tooltip": "Master Yi diventa non bersagliabile e si teletrasporta per colpire rapidamente i nemici vicini al suo bersaglio, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> a tutti i nemici colpiti dopo {{ alphastrikebounces }} colpi. <br /><br />Se non sono presenti altri bersagli, questa abilità può colpire ripetutamente lo stesso nemico, infliggendo un {{ subsequenthitmultiplier*100 }}% dei danni per ogni colpo successivo (<physicalDamage>{{ subesquentdamage }}</physicalDamage>) fino a un massimo di <physicalDamage>{{ singletotaldamage }} danni fisici</physicalDamage> per singolo bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Danni bonus ai mostri", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ bonusmonsterdamage }} -> {{ bonusmonsterdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19.5, 19, 18.5, 18], "cooldownBurn": "20/19.5/19/18.5/18", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AlphaStrike.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Meditate", "name": "Meditazione", "description": "Master <PERSON> ring<PERSON>ce il suo corpo con la concentrazione della mente, recuperando salute e subendo meno danni per un breve periodo di tempo. <PERSON><PERSON><PERSON>, Master <PERSON> otterrà cariche di Doppio colpo e metterà in pausa la durata rimasta di Stile Wuju e Agilità superiore per ogni secondo di canalizzazione.", "tooltip": "Master Yi canalizza, recuperando <healing>{{ totalheal }} salute</healing> in {{ healduration }} secondi. La guarigione aumenta fino a un {{ maxmissinghealthpercent*100 }}% in base alla salute mancante di Master Yi.<br /><br />Mentre canalizza e per i {{ drlinger }} secondi successivi, subisce {{ initialdr }} danni ridotti, che diminuiscono a un {{ damagereduction*100 }}% dopo i primi {{ initialextradrduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute ripristinata", "Riduzione danni"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ damagereduction*100.000000 }}% -> {{ damagereductionnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Mana (+{{ percentmanacostpersecond*100 }}% mana massimo al secondo)", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "Meditate.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ basemanacost }} Mana (+{{ percentmanacostpersecond*100 }}% mana massimo al secondo)"}, {"id": "WujuStyle", "name": "<PERSON><PERSON>", "description": "Conferisce danni puri bonus agli attacchi base.", "tooltip": "<PERSON><PERSON> attacchi di <PERSON> Yi infliggono <trueDamage>{{ totaldamage }} danni puri</trueDamage> aggiuntivi per {{ duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "WujuStyle.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "Highlander", "name": "Agilità superiore", "description": "Master Yi si muove con agilità impareggiabile, aumentando temporaneamente velocità di movimento e di attacco, e diventando immune agli effetti di rallentamento. Quando l'effetto è attivo, le uccisioni dei campioni o gli assist aumentano la durata di Agilità superiore. Riduce passivamente la ricarica per le altre abilità quando si effettua un assist o un'uccisione.", "tooltip": "<spellPassive>Passiva:</spellPassive> le eliminazioni dei campioni riducono il tempo di ricarica rimanente delle abilità base di Master Yi del {{ rcooldownrefund*100 }}%.<br /><br /><spellActive>Attiva:</spellActive> Master Yi entra in trance, guadagnando <speed>{{ rmsbonus }}% velocità di movimento</speed>, <attackSpeed>{{ rasbonus }}% velocità d'attacco</attackSpeed> e immunità ai <status>rallentamenti</status> per {{ rduration }} secondi. Uccidere i campioni prolunga la durata di questa abilità di {{ rkillassistextension }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Velocità di movimento"], "effect": ["{{ rasbonus }}% -> {{ rasbonusNL }}%", "{{ rmsbonus }}% -> {{ rmsbonusNL }}%"]}, "maxrank": 3, "cooldown": [85, 85, 85], "cooldownBurn": "85", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "Highlander.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON> co<PERSON>po", "description": "<PERSON><PERSON> al<PERSON>ni attacchi base consecutivi, Master <PERSON> co<PERSON>ce due volte.", "image": {"full": "MasterYi_Passive1.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}