{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lissandra": {"id": "<PERSON><PERSON>", "key": "127", "name": "<PERSON><PERSON>", "title": "la strega dei ghiacci", "image": {"full": "Lissandra.png", "sprite": "champion2.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "127000", "num": 0, "name": "default", "chromas": false}, {"id": "127001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "127002", "num": 2, "name": "<PERSON><PERSON> delle Lame", "chromas": false}, {"id": "127003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "127004", "num": 4, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "127012", "num": 12, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "127023", "num": 23, "name": "<PERSON><PERSON>rcella<PERSON>", "chromas": true}, {"id": "127033", "num": 33, "name": "<PERSON>ndra di Porcellana (edizione prestigio)", "chromas": false}, {"id": "127034", "num": 34, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "La magia di Lissandra trasforma il puro potere del ghiaccio in qualcosa di oscuro e terribile. Con la forza del suo ghiaccio nero, non si limita a congelare i suoi avversari: li schiaccia e li trafigge. Per i terrorizzati abitanti del nord, è nota semplicemente come la ''Strega dei Ghiacci''. La verità è molto più sinistra: Lissandra corrompe la natura e vuole scatenare una nuova era glaciale sul mondo.", "blurb": "La magia di Lissandra trasforma il puro potere del ghiaccio in qualcosa di oscuro e terribile. Con la forza del suo ghiaccio nero, non si limita a congelare i suoi avversari: li schiaccia e li trafigge. Per i terrorizzati abitanti del nord, è nota...", "allytips": ["Puoi usare all'istante la suprema su di te premendo il tasto del lancio su se stessi insieme a quello della suprema (alt+R nelle impostazioni predefinite).", "Lanciare Sentiero glaciale e poi correre nella direzione opposta non darà ai nemici una direzione chiara da seguire.", "Le abilità di Lissandra hanno gittata più breve rispetto a quelle degli altri maghi. Di conseguenza, comprare oggetti che offrono potere magico e difesa, come Clessidra di Zhonya e Velo della Banshee può rivelarsi un'ottima idea per aiutarla a sopravvivere e a infliggere più danni."], "enemytips": ["Il modo migliore per impedire a Lissandra di usare il suo Sentiero glaciale è immobilizzarla prima che lo riattivi.", "Circolo polare di Lissandra ha un lungo tempo di ricarica ai livelli più bassi. Attaccala mentre è in ricarica.", "Scheggia glaciale di Lissandra rallenta la prima unità che colpisce. Avvicinati a Lissandra stando dietro ai tuoi minion per non farti rallentare."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 475, "mpperlevel": 30, "movespeed": 325, "armor": 22, "armorperlevel": 4.9, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.7, "attackspeedperlevel": 1.5, "attackspeed": 0.656}, "spells": [{"id": "LissandraQ", "name": "Scheggia glaciale", "description": "Lancia una punta di ghiaccio che infligge danni magici e rallenta la velocità di movimento. Scheggia glaciale supera il bersaglio, infliggendo gli stessi danni agli altri nemici colpiti.", "tooltip": "Lissandra scaglia una lancia di ghiaccio che si distrugge contro il primo nemico colpito, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>, <status>rallentandolo</status> del {{ e3 }}% per {{ e2 }} secondi, dannegg<PERSON><PERSON> e rallentando anche i nemici alle sue spalle.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ effect3amount*-100.000000 }}% -> {{ effect3amountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 24, 28, 32, 36], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1.5", "20/24/28/32/36", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "LissandraQ.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LissandraW", "name": "Circolo polare", "description": "Congela i campioni vicini, infliggendo danni magici e immobilizzandoli.", "tooltip": "<PERSON><PERSON> crea un campo di ghiaccio che <status>immobilizza</status> i nemici per {{ e2 }} secondi e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata immobilizzazione", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [1.25, 1.35, 1.45, 1.55, 1.65], [3, 3, 3, 3, 3], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "1.25/1.35/1.45/1.55/1.65", "3", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LissandraW.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LissandraE", "name": "Sentiero glaciale", "description": "Lissandra crea un artiglio di ghiaccio che infligge danni magici. Riattivare questa abilità teletrasporta Lissandra nella posizione attuale dell'artiglio.", "tooltip": "Lissandra lancia davanti a sé un artiglio di ghiaccio che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>. Mentre l'artiglio è in movimento, <PERSON><PERSON> può <recast>rilanciare</recast> questa abilità per teletrasportarsi nella sua posizione.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [24, 21, 18, 15, 12], "cooldownBurn": "24/21/18/15/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [14, 13, 12, 11, 10], [20, 20, 20, 20, 20], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "14/13/12/11/10", "20", "1", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "LissandraE.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LissandraR", "name": "Tomba di g<PERSON>ccio", "description": "Se lanciata su un campione nemico, congela il bersaglio, s<PERSON><PERSON><PERSON><PERSON>. Se lanciata su Lissandra, la incapsula nel ghiaccio oscuro, rendendola non bersagliabile e invulnerabile mentre si cura. In entrambi i casi, il ghiaccio oscuro emerge poi dal bersaglio, infliggendo danni magici ai nemici e rallentandone la velocità di movimento.", "tooltip": "Lissandra avvolge nel ghiaccio se stessa o un campione nemico. I nemici vengono <status>storditi</status> per {{ enemycastduration }} secondi. Quando invece lancia questa abilità su di sé, <PERSON><PERSON> entra in Stasi per {{ selfcastduration }} secondi e recupera <healing>{{ healamount }} salute</healing>, aumentata del {{ selfcastmissinghpratio }}% per ogni {{ selfcastmissinghpperabove }}% di salute mancante.<br /><br />In entrambi i casi, il bersaglio emana ghiaccio oscuro, che infligge <magicDamage>{{ calculateddamage }} danni magici</magicDamage>. Il ghiaccio persiste per {{ slowduration }} secondi, <status>rallentando</status> i nemici del {{ slowamount*-100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Guarigione", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ selfcastflatheal }} -> {{ selfcastflathealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "LissandraR.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Sottomissione alla Figlia del gelo", "description": "Quando un campione nemico muore vicino a Lissandra diventa uno Schiavo del gelo. Gli Schiavi del gelo rallentano la velocità di movimento dei nemici vicini e dopo un po' di tempo si frantumano per il freddo intenso, infliggendo danni magici ai bersagli vicini.", "image": {"full": "Lissandra_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}