{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Volibear": {"id": "Volibear", "key": "106", "name": "Volibear", "title": "der unerbittliche Sturm", "image": {"full": "Volibear.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "106000", "num": 0, "name": "default", "chromas": false}, {"id": "106001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "106002", "num": 2, "name": "Nordsturm-Volibear", "chromas": false}, {"id": "106003", "num": 3, "name": "Runenwächter-Volibear", "chromas": false}, {"id": "106004", "num": 4, "name": "Captain <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "106005", "num": 5, "name": "El Rayo Volibear", "chromas": false}, {"id": "106006", "num": 6, "name": "Volibear, der tausendfach Durchbohrte", "chromas": false}, {"id": "106007", "num": 7, "name": "Dualitätsdrache Volibear", "chromas": true}, {"id": "106009", "num": 9, "name": "Dualitätsdrache Volibear (Prestige)", "chromas": false}, {"id": "106019", "num": 19, "name": "Tintenschatten-Volibear", "chromas": false}], "lore": "<PERSON><PERSON><PERSON> jene, die ihn auch heute noch verehren, ist Volibear die Manifestation des Sturms. Er zog schon lange vor den ersten Sterblichen in all seiner zerstörerischen Wildheit und sturen Entschlossenheit durch Freljords Tundra. Das Land, das er und die anderen Halbgötter erschaffen haben, verteidigt er um jeden Preis. In ihm brodelt ein tiefer Hass gegen die Zivilisation und die Schwäche, die sie mit sich gebracht hat. Nun kämpft er, um die alten Traditionen zurückzubringen – und mit ihnen jene Zeit, in der das Land ungezähmt war und Blut noch in Strömen floss. <PERSON><PERSON>, der seinen Bestrebungen im Weg steht, wird mit Zahn, Klauen und donnerndem Gebrüll vernichtet.", "blurb": "<PERSON><PERSON><PERSON> <PERSON>, die ihn auch heute noch verehren, ist Volibear die Manifestation des Sturms. Er zog schon lange vor den ersten Sterblichen in all seiner zerstörerischen Wildheit und sturen Entschlossenheit durch Freljords Tundra. Das Land, das er und die...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 7, "magic": 4, "difficulty": 3}, "stats": {"hp": 650, "hpperlevel": 104, "mp": 350, "mpperlevel": 70, "movespeed": 340, "armor": 31, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.75, "mpregen": 6.25, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "VolibearQ", "name": "<PERSON><PERSON><PERSON>", "description": "Volibear erhält Lauftempo, wenn er sich auf Gegner zubewegt. Das erste Ziel seines Angriffs erleidet Schaden und wird <status>betäubt</status>.", "tooltip": "Volibear erhält für die nächsten {{ duration }}&nbsp;Sekunden <speed>{{ minspeedcalc }}&nbsp;Lauftempo</speed>, das in Richtung gegnerischer Champions auf <speed>{{ maxspeedcalc }}</speed> verdoppelt wird. Während „Donnernder Schlag“ aktiv ist, verursacht Volibears nächster Angriff <physicalDamage>{{ calculateddamage }}&nbsp;normalen Schaden</physicalDamage> und <status>betäubt</status> das Ziel {{ stunduration }}&nbsp;Sekunde(n) lang.<br /><br />Wird Volibear von einem Gegner <status>bewegungsunfähig</status> gemacht, bevor er ein Ziel <status>betäuben</status> konnte, endet die Fähigkeit vorzeitig und setzt ihre Abklingzeit zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Lauftempo", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ minspeed*100.000000 }}&nbsp;% -> {{ minspeednl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VolibearQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VolibearW", "name": "Zerfleischen", "description": "Volibear fügt einem Gegner Schaden zu, löst Treffereffekte aus und markiert ihn. Wirkt Volibear die Fähigkeit erneut auf den gleichen Gegner, verursacht er zusätzlichen Schaden und heilt sich.", "tooltip": "Volibear beißt e<PERSON>, fügt ihm <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und markiert ihn {{ markduration }}&nbsp;Sekunden lang.<br /><br />Wenn diese Fähigkeit gegen ein markiertes Ziel eingesetzt wird, wird der Schaden auf <physicalDamage>{{ empowereddamage }}</physicalDamage> erhöht und Volibear heilt sich um <healing>{{ baseheal }} plus {{ percentmissinghealthhealingratio }} seines fehlenden Lebens</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Grundheilung", "Prozentsatz des fehlenden Lebens", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ healpercent*100.000000 }}&nbsp;% -> {{ healpercentnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "VolibearW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VolibearE", "name": "Himmelsbrecher", "description": "Volibear lässt einen Blitz am Zielort einschlagen, der Gegnern Schaden zufügt und sie verlangsamt. Befindet er sich im Wirkradius, wird ihm ein <PERSON> gewährt.", "tooltip": "Volibear beschwört eine Donnerwolke, die einen Blitz entfesselt, der <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden plus {{ percentdamage*100 }}&nbsp;% des maximalen Lebens als magischen Schaden</magicDamage> verursacht und {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br />Wenn sich Volibear im Wirkbereich befindet, erhält er {{ shieldduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ shieldapratiotooltip }} plus {{ shieldamount*100 }}&nbsp;% seines maximalen Lebens.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (%)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percentdamage*100.000000 }}&nbsp;% -> {{ percentdamagenl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VolibearE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VolibearR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Volibear springt zu einem Zielort, wodurch die Gegner unter ihm Schaden erleiden und verlangsamt werden. Er erhält dabei zusätzliches Leben. Gegnerische Türme in der Nähe seiner Landung werden vorübergehend deaktiviert.", "tooltip": "Volibear verwandelt sich und springt nach vorn, wodurch er {{ transformduration }}&nbsp;Sekunden lang <healing>{{ healthamount }}&nbsp;<PERSON><PERSON></healing> und {{ bonusattackrange }}&nbsp;Angriffsreichweite erhält.<br /><br />Bei der Landung erschüttert Volibear den Boden, <status>deaktiviert</status> nahe Türme {{ towerdisableduration }}&nbsp;Sekunden lang und fügt ihnen <physicalDamage>{{ towerdamagetooltip }}&nbsp;normalen Schaden</physicalDamage> zu. Nahe Gegner werden um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>. Die Verlangsamung fällt über 1&nbsp;Sekunde hinweg ab. Gegner, die sich direkt unter Volibear befinden, erleiden <physicalDamage>{{ sweetspotdamagetooltip }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzliches Leben", "Dauer der Turmdeaktivierung", "Abklingzeit"], "effect": ["{{ sweetspotdamage }} -> {{ sweetspotdamageNL }}", "{{ healthamount }} -> {{ healthamountNL }}", "{{ towerdisableduration }} -> {{ towerdisabledurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 135, 110], "cooldownBurn": "160/135/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [75, 115, 155], [9, 9, 9], [12, 12, 12], [8, 8, 8], [0.08, 0.08, 0.08], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "75/115/155", "9", "12", "8", "0.08", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "VolibearR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Der unerbittliche Sturm", "description": "Volibears Angriffe und Fähigkeiten gewähren ihm Angriffstempo, bis sie nahen <PERSON>nern schließlich zusätzlichen magischen Schaden zufügen.", "image": {"full": "Volibear_Icon_P.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}