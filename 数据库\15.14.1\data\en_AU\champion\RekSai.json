{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"RekSai": {"id": "RekSai", "key": "421", "name": "<PERSON><PERSON><PERSON>", "title": "the Void Burrower", "image": {"full": "RekSai.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "421000", "num": 0, "name": "default", "chromas": false}, {"id": "421001", "num": 1, "name": "Eternum Rek'Sai", "chromas": false}, {"id": "421002", "num": 2, "name": "Pool Party Rek'Sai", "chromas": true}, {"id": "421009", "num": 9, "name": "Blackfrost Rek'Sai", "chromas": true}, {"id": "421017", "num": 17, "name": "<PERSON>wood Re<PERSON>Sai", "chromas": true}, {"id": "421026", "num": 26, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "An apex predator, <PERSON><PERSON><PERSON><PERSON> is a merciless Void-spawn that tunnels beneath the ground to ambush and devour unsuspecting prey. Her insatiable hunger has laid waste to entire regions of the once-great empire of Shurima—merchants, traders, even armed caravans, will go hundreds of miles out of their way to avoid her and her offspring's hunting grounds. All know that once <PERSON><PERSON><PERSON><PERSON> is seen on the horizon, death from below is all but guaranteed.", "blurb": "An apex predator, <PERSON><PERSON><PERSON><PERSON> is a merciless Void-spawn that tunnels beneath the ground to ambush and devour unsuspecting prey. Her insatiable hunger has laid waste to entire regions of the once-great empire of Shurima—merchants, traders, even armed...", "allytips": ["Keeping tunnels spread out across the map will ensure you have choices when you want to cast Void Rush.", "<PERSON> before travelling around the map to take advantage of the increased Move Speed and the safety provided by Tremor Sense.", "Tremor Sense can warn you of enemies closing in, and is particularly useful when entering enemy territory."], "enemytips": ["If you see one of <PERSON><PERSON><PERSON>'s tunnels, briefly stand on one of the entrances to destroy it.", "<PERSON><PERSON><PERSON>'s <PERSON><PERSON> <PERSON><PERSON> gains damage as she builds <PERSON>. Be very careful around her when her Fury is full.", "When <PERSON><PERSON><PERSON><PERSON> is near, she can see the position of you and your allies, but only if you're moving."], "tags": ["Fighter", "Tank"], "partype": "Rage", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 600, "hpperlevel": 99, "mp": 100, "mpperlevel": 0, "movespeed": 340, "armor": 36, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.667}, "spells": [{"id": "RekSaiQ", "name": "Queen's Wrath / Prey Seeker", "description": "<PERSON><PERSON><PERSON><PERSON>'s next 3 basic attacks deal bonus Physical Damage to nearby enemies.<br><br>While Burrowed, <PERSON><PERSON><PERSON><PERSON> launches a burst of void-charged earth that damages and reveals enemies hit.", "tooltip": "<keywordMajor>Unburrowed:</keywordMajor> <PERSON><PERSON><PERSON>'s next 3 Attacks within {{ buffduration }} seconds gain <attackSpeed>{{ attackspeed*100 }}% Attack Speed</attackSpeed> and deal an additional <physicalDamage>{{ totaldamagetooltip }} physical damage</physicalDamage> to nearby enemies. Attacks refresh the duration of this Ability.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Queen's Wrath AD Ratio", "Queen's Wrath Cooldown", "Prey Seeker Damage"], "effect": ["{{ unburrowedadratio*100.000000 }}% -> {{ unburrowedadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ burrowedbasedamage }} -> {{ burrowedbasedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 3.5, 3, 2.5, 2], "cooldownBurn": "4/3.5/3/2.5/2", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [12, 11, 10, 9, 8], [5, 5, 5, 5, 5], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "12/11/10/9/8", "5", "3", "300", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RekSaiQ.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "RekSaiW", "name": "Burrow / Un-burrow", "description": "<PERSON><PERSON><PERSON><PERSON> burrows into the ground, gaining new abilities and increased Move Speed. Her vision range is reduced and she cannot use basic attacks.<br><br>While Burrowed, <PERSON><PERSON><PERSON><PERSON> may cast Unburrow to knock up and damage nearby enemies.", "tooltip": "<keywordMajor>Unburrowed:</keywordMajor> <PERSON><PERSON><PERSON> burrows into the ground, gaining access to new Abilities but cannot Attack. She gains <speed>{{ burrowedmovespeed }} Move Speed</speed>, {{ visionradiusmod*-100 }}% reduced vision range, and nearby enemies that move and are otherwise unseen will have their positions revealed to <PERSON><PERSON><PERSON><PERSON><PERSON> and her allies.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Unburrow Damage", "Burrowed Move Speed"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ burrowedmovespeed }} -> {{ burrowedmovespeedNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [1500, 1500, 1500, 1500, 1500], [50, 85, 120, 155, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [10, 10, 10, 10, 10], [250, 250, 250, 250, 250], [175, 175, 175, 175, 175], [3, 3, 3, 3, 3], [0.3, 0.3, 0.3, 0.3, 0.3]], "effectBurn": [null, "1500", "50/85/120/155/190", "0", "0", "1", "10", "250", "175", "3", "0.3"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [1650, 1650, 1650, 1650, 1650], "rangeBurn": "1650", "image": {"full": "RekSaiW.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "RekSaiE", "name": "Furious Bite / Tunnel", "description": "<PERSON><PERSON><PERSON><PERSON> bites her target, dealing bonus damage if she has max <PERSON>.<br><br>While Burrowed, <PERSON><PERSON><PERSON><PERSON> creates a re-usable, long lasting tunnel. Enemies can destroy it by standing on top of either entrance.", "tooltip": "<scaleAD>Unburrowed:</scaleAD> <PERSON><PERSON><PERSON><PERSON> bites a target, dealing <physicalDamage>{{ spell.reksaie:basedamagetooltip }} physical damage</physicalDamage>. At max <keywordMajor>Fury</keywordMajor>, the bite deals an additional <physicalDamage>{{ spell.reksaie:furymaxhealthdamage*100 }}% max Health physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Max Health %", "Tunnel Cast Cooldown", "Tunnel Use Cooldown"], "effect": ["{{ furymaxhealthdamage*100.000000 }}% -> {{ furymaxhealthdamagenl*100.000000 }}%", "{{ dashcooldown }} -> {{ dashcooldownNL }}", "{{ tunnelreusecooldown }} -> {{ tunnelreusecooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "RekSaiE.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "RekSaiR", "name": "Void Rush", "description": "<PERSON><PERSON><PERSON><PERSON> passively marks targets by damaging them. She can activate this ability to become briefly untargetable and lunge at a marked target for heavy damage based on their missing health.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> targets an enemy damaged by her in the last {{ preymarkduration }} seconds and then dives underground, becoming Untargetable. Moments later, she leaps unstoppably at her target, dealing <physicalDamage>{{ rbasedamagecalc }} plus {{ percentmissinghealthdamage }}% missing Health physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Percent Missing Health Damage", "Cooldown"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ percentmissinghealthdamage }} -> {{ percentmissinghealthdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "RekSaiR.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "No Cost"}], "passive": {"name": "Fury of the Xer'Sai", "description": "<PERSON><PERSON><PERSON><PERSON> generates <PERSON> by Attacking and hitting with basic Abilities. She consumes this Fury while <PERSON><PERSON> to restore health.", "image": {"full": "RekSai_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}