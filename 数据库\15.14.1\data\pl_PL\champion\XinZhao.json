{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"XinZhao": {"id": "XinZhao", "key": "5", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "XinZhao.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "5000", "num": 0, "name": "default", "chromas": false}, {"id": "5001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "5002", "num": 2, "name": "<PERSON><PERSON> Xin <PERSON>", "chromas": false}, {"id": "5003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "5004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "5005", "num": 5, "name": "<PERSON><PERSON> z Walczących Królestw", "chromas": true}, {"id": "5006", "num": 6, "name": "Tajny Agent <PERSON><PERSON>", "chromas": false}, {"id": "5013", "num": 13, "name": "Pogromca Smoków Xin Zhao", "chromas": true}, {"id": "5020", "num": 20, "name": "Kosmiczny Obrońca Xi<PERSON> Zhao", "chromas": true}, {"id": "5027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "5036", "num": 36, "name": "Rozrywkowy <PERSON> Zhao", "chromas": true}], "lore": "<PERSON><PERSON> <PERSON> jest stanowczym wojownikiem lojalnym panującej Dynastii Promiennej Tarczy. Kiedyś skazany na walkę jako gladiator na noxiańskich arenach, prz<PERSON><PERSON><PERSON><PERSON> niez<PERSON>zone ilości pojedynków, a gdy został wyzwolony przez siły Demacii, przysiągł wieczną wierność swoim wybawicielom. Uzbrojony w swoją ulubioną włócznię o trzech szponach, Xin Zhao walczy teraz dla swojego przybranego królestwa, zuchwale stawiając czoła każdemu wrogowi.", "blurb": "<PERSON><PERSON> jest stanowczym wojownikiem lojalnym panującej Dynastii Promiennej Tarczy. Kiedyś skazany na walkę jako gladiator na noxiańskich arenach, prz<PERSON><PERSON><PERSON><PERSON> niez<PERSON>zone ilości pojedynków, a gdy został wyzwolony przez siły Demacii, przysiągł wieczną...", "allytips": ["<PERSON><PERSON> świetnie inicjuje starcia. Poprowadź natarcie i użyj swojej superumiejętności, aby zadać maksymalne obrażenia.", "Spróbuj wykorzystać superumiejętność w taki sposób, a<PERSON> o<PERSON><PERSON><PERSON><PERSON> jak największą liczbę wrogów."], "enemytips": ["<PERSON><PERSON> <PERSON> doskonale nadaje się do rozpoczynania walk, dzięki swojej szarży oraz superumiejętności, która zadaje obrażenia wszystkim jednostkom blisko niego. <PERSON><PERSON> swojej drużynie rozproszyć się, p<PERSON><PERSON>n Zhao nie użyje swojej superumiej<PERSON>ci.", "Najważniejszą umiej<PERSON>ścią Xin Zhao jest Cios Trzech Szponów, pozwalają<PERSON> mu zadawać obrażenia i wyzerowywać czas odnowienia, d<PERSON><PERSON> niez<PERSON> ważne jest, by nie poz<PERSON><PERSON><PERSON> mu korzystać z kilku umiejętności jednocześnie."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 2}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 274, "mpperlevel": 55, "movespeed": 345, "armor": 35, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "XinZhaoQ", "name": "Cios Trzech Szponów", "description": "3 kolejne zwykłe ataki Xin Zhao zadają zwiększone obrażenia, a ostatni wyrzuca przeciwnika w powietrze.", "tooltip": "3 kolejne ataki Xin Zhao z<PERSON> dodatkowo <physicalDamage>{{ bonusdamage }} pkt. obrażeń fizycznych</physicalDamage> i skracają czas odnowienia jego pozostałych umiejętności o 1 sek. Ponadto trzeci atak <status>podrzuca</status> na {{ e2 }} sek.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dodatkowe obrażenia", "Czas odnowienia"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [20, 35, 50, 65, 80], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/35/50/65/80", "0.75", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "XinZhaoQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "XinZhaoW", "name": "Z Wiatru Grom", "description": "<PERSON>n <PERSON> tnie włócznią przed sobą, a następnie wykonuje pchnięcie i spowalnia trafionych wrogów, oznaczając ich jako wyzwanych.", "tooltip": "<PERSON><PERSON> tnie, z<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ slashdamage }} pkt. obrażeń fizycznych</physicalDamage>, a następnie dźga w przód, zadając <physicalDamage>{{ thrustdamage }} pkt. obrażeń fizycznych</physicalDamage>. Wrogowie trafieni pchnięciem zostają <status>spowolnieni</status> o {{ e6 }}% na {{ e7 }} sek. <br /><br />Bohaterowie i duże potwory trafieni pchnięciem zostają oznaczeni jako <keywordMajor>wyzwani</keywordMajor> na {{ markduration }} sek. i zostają ujawnieni, o ile nie są <keywordStealth>ukryci</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia cięcia", "Obrażenia pchnięcia", "Koszt (@AbilityResourceName@)", "Czas odnowienia"], "effect": ["{{ slashbasedamage }} -> {{ slashbasedamageNL }}", "{{ thrustbasedamage }} -> {{ thrustbasedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [140, 140, 140, 140, 140], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0.5", "0.5", "0.5", "50", "1.5", "140", "200", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XinZhaoW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "XinZhaoE", "name": "Zuchwała Szarża", "description": "Xin Zhao szarżuje na wroga, zys<PERSON>jąc prędkość ataku, zadając obrażenia oraz spowalniając wszystkich pobliskich wrogów. Zasięg Zuchwałej Szarży zwiększa się przeciwko wyzwanym celom.", "tooltip": "<PERSON><PERSON> szar<PERSON> na wroga, z<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ chargedamage }} pkt. obrażeń magicznych</magicDamage> pobliskim wrogom i <status>spowalniając</status> ich o {{ baseslowamount }}% na {{ e6 }} sek.<br /><br /><PERSON><PERSON><PERSON><PERSON> Xi<PERSON> z<PERSON> <attackSpeed>{{ e3 }}% prędkości ataku</attackSpeed> na {{ e4 }} sek.<br /><br />Zasięg <spellName>Zuchwał<PERSON></spellName> jest wię<PERSON>zy na wrogach, któ<PERSON>y zostali <keywordMajor>wyzwani</keywordMajor>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect3amount*100.000000 }}% -> {{ effect3amountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [-0.3, -0.3, -0.3, -0.3, -0.3], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [250, 250, 250, 250, 250], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "-0.3", "40/45/50/55/60", "5", "250", "0.5", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "XinZhaoE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "XinZhaoR", "name": "Garda Półksiężyca", "description": "<PERSON>n Zhao wyzywa na pojedynek wroga, którego ostatnio zranił. Xin Zhao zadaje pobliskim wrogom obrażenia zależne od ich aktualnego zdrowia i odrzuca do tyłu cele, którym nie rzucił wyzwania, jednocześnie stając się niewrażliwym na obrażenia zadawane przez wrogów spoza stworzonego okręgu.", "tooltip": "Ostatni bohater trafiony atakiem lub <spellName><PERSON>uch<PERSON><PERSON><PERSON></spellName> <PERSON><PERSON> <keywordMajor>wy<PERSON><PERSON><PERSON></keywordMajor> na {{ markduration }} sek.<br /><br /><PERSON><PERSON> wykonuje zamaszysty cios, kt<PERSON><PERSON> zadaje <physicalDamage>obrażenia fizyczne równe {{ totaldamage }} pkt. plus {{ percentcurrenthealthdamage*100 }}% aktualnego zdrowia celu</physicalDamage> i <status>odrzuca</status> wszyst<PERSON>ch wrogów, którzy nie są <keywordMajor>wyzwani</keywordMajor>. <br />  <br />Następnie Xin Zhao na {{ missiledefensebaseduration }} sek. staje się niewrażliwy na obrażenia otrzymywane od wrogów, którzy znajdują się poza zasięgiem tego ciosu. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "XinZhaoR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Zdecydowanie", "description": "Co trzeci atak zadaje dodatkowe obrażenia i leczy Xin Zhao.", "image": {"full": "XinZhaoP.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}