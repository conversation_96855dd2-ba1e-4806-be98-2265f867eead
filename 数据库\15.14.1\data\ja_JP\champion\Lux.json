{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lux": {"id": "<PERSON><PERSON>", "key": "99", "name": "ラックス", "title": "光の才女", "image": {"full": "Lux.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "99000", "num": 0, "name": "default", "chromas": false}, {"id": "99001", "num": 1, "name": "ソーサレス ラックス", "chromas": false}, {"id": "99002", "num": 2, "name": "スペルシーフ ラックス", "chromas": false}, {"id": "99003", "num": 3, "name": "コマンドー ラックス", "chromas": false}, {"id": "99004", "num": 4, "name": "帝国兵士ラックス", "chromas": false}, {"id": "99005", "num": 5, "name": "鋼鉄軍人ラックス", "chromas": false}, {"id": "99006", "num": 6, "name": "スターガーディアン ラックス", "chromas": false}, {"id": "99007", "num": 7, "name": "エレメンタリスト ラックス", "chromas": false}, {"id": "99008", "num": 8, "name": "月の女帝ラックス", "chromas": true}, {"id": "99014", "num": 14, "name": "パジャマガーディアン ラックス", "chromas": false}, {"id": "99015", "num": 15, "name": "バトルアカデミア ラックス", "chromas": false}, {"id": "99016", "num": 16, "name": "プレステージ バトルアカデミア ラックス", "chromas": false}, {"id": "99017", "num": 17, "name": "暗黒の宇宙ラックス", "chromas": false}, {"id": "99018", "num": 18, "name": "宇宙のラックス", "chromas": false}, {"id": "99019", "num": 19, "name": "スペースグルーヴ ラックス", "chromas": true}, {"id": "99029", "num": 29, "name": "磁器ラックス", "chromas": true}, {"id": "99038", "num": 38, "name": "ソウルファイター ラックス", "chromas": true}, {"id": "99039", "num": 39, "name": "プレステージ バトルアカデミア ラックス(2022)", "chromas": false}, {"id": "99040", "num": 40, "name": "プレステージ磁器ラックス", "chromas": false}, {"id": "99042", "num": 42, "name": "荘厳の天球ラックス", "chromas": true}, {"id": "99061", "num": 61, "name": "妖精の王宮ラックス", "chromas": true}, {"id": "99070", "num": 70, "name": "プレステージ精霊の花祭りラックス", "chromas": true}], "lore": "ラクサーナ・クラウンガードは魔法の才能を恐怖と疑惑の目で見る偏狭な国、デマーシアで生まれた。意思の力で光を自在に操ることができる彼女だったが、力を持つことを見つけられて追放されることを恐れながら育ち、名門一家の権威を守るためにそれを秘密にすることを強いられてきた。しかし、持ち前の楽観主義と負けん気で自らのユニークな能力を受け入れることを決めて、今では祖国のためにその力を密かに行使するようになったのである。", "blurb": "ラクサーナ・クラウンガードは魔法の才能を恐怖と疑惑の目で見る偏狭な国、デマーシアで生まれた。意思の力で光を自在に操ることができる彼女だったが、力を持つことを見つけられて追放されることを恐れながら育ち、名門一家の権威を守るためにそれを秘密にすることを強いられてきた。しかし、持ち前の楽観主義と負けん気で自らのユニークな能力を受け入れることを決めて、今では祖国のためにその力を密かに行使するようになったのである。", "allytips": ["ラックスは強力な範囲妨害スキルの使い手だ。敵の侵攻を食い止めたいときや逃走を阻止したいときは「シンギュラリティ」を活用しよう。", "「プリズムバリア」がなかなか命中しないときは、ブーメランのように戻ってくる性質をうまく利用しよう。杖が戻ってくるときに味方に当たるよう、立ち位置を調整するといい。", "「シンギュラリティ」は偵察にも最適だ。茂みを狙って発動すれば、待ち伏せしている敵がいないか確認できる。"], "enemytips": ["ラックスは強力な範囲妨害スキルの使い手だ。まとめて行動妨害効果をかけられないよう、なるべく分散して、四方から攻撃を仕掛けよう。", "体力残量が低下して後退するときは「ファイナルスパーク」に注意しよう。ビームが発射される前に赤い光の筋が出現するので、その瞬間を見逃さずに射線の外側へ回避しよう。"], "tags": ["Mage", "Support"], "partype": "マナ", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 580, "hpperlevel": 99, "mp": 480, "mpperlevel": 23.5, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3, "attackspeed": 0.669}, "spells": [{"id": "LuxLightBinding", "name": "ライトバインド", "description": "光の玉を発射し、最大2体までの敵にダメージを与えてスネア効果を付与する。", "tooltip": "光の玉を発射し、最初に命中した2体の敵に<magicDamage>{{ totaldamagett }}の魔法ダメージ</magicDamage>と{{ e3 }}秒間の<status>スネア効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "50", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1175, 1175, 1175, 1175, 1175], "rangeBurn": "1175", "image": {"full": "LuxLightBinding.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LuxPrismaticWave", "name": "プリズムバリア", "description": "指定方向に杖を投げ、自身と杖に触れた味方チャンピオンに光を屈折させたシールドを付与する。杖は最大距離に達するとラックスのもとへ戻り、触れた味方チャンピオンと杖をキャッチしたラックスに再びシールドを付与する。", "tooltip": "杖を投げて、それに触れた味方に{{ e3 }}秒間、<shield>耐久値{{ totalshieldtt }}のシールド</shield>を付与する。杖が戻ってくる際も同量の<shield>シールド</shield>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [2, 4, 6, 8, 10], [40, 55, 70, 85, 100], [2.5, 2.5, 2.5, 2.5, 2.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2/4/6/8/10", "40/55/70/85/100", "2.5", "100", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "LuxPrismaticWave.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LuxLightStrikeKugel", "name": "シンギュラリティ", "description": "指定地点に特異な光の玉を放ち、範囲内の敵にスロウ効果を付与する。効果時間内に再度発動すると光の玉が爆発し、範囲内の敵にダメージを与える。", "tooltip": "光の領域を作り出し、範囲内の敵に{{ e1 }}%の<status>スロウ効果</status>を与えて視界を得る。{{ e3 }}秒経過するか<recast>再発動</recast>で爆発し、<magicDamage>{{ totaldamagett }}の魔法ダメージ</magicDamage>を与えて<status>スロウ効果</status>を追加で{{ slowlingerduration }}秒間付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [65, 115, 165, 215, 265], [5, 5, 5, 5, 5], [310, 310, 310, 310, 310], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25/30/35/40/45", "65/115/165/215/265", "5", "310", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LuxLightStrikeKugel.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LuxR", "name": "ファイナルスパーク", "description": "光のエネルギーを集めてビームを発射し、範囲内の敵にダメージを与える。「イルミネーション」効果を受けている敵に命中すると爆発させて追加魔法ダメージを与え、再度「イルミネーション」を付与する。", "tooltip": "まばゆい光線を発射し、直線上の敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 50, 40], "cooldownBurn": "60/50/40", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [3340, 3340, 3340], "rangeBurn": "3340", "image": {"full": "LuxR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "イルミネーション", "description": "攻撃スキルが命中した対象を数秒間「イルミネーション」でマークする。マークした対象にラックスの攻撃が命中すると、エネルギーが爆発して追加魔法ダメージを与える(追加ダメージの量はラックスのレベルに比例する)。", "image": {"full": "LuxIlluminatingFraulein.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}