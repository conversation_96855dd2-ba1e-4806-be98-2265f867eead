{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "<PERSON><PERSON>lder", "title": "der feurige Welpling", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "Himmelsschuppen-Smolder", "chromas": true}], "lore": "Versteckt zwischen den zerklüfteten Klippen der noxianischen Grenze, unter den wachsamen Augen seiner <PERSON>, lernt ein junger <PERSON>, was es bedeu<PERSON>, Erbe des camavoranischen Königdrachengeschlechts zu sein. Verspielt und begierig zu wachsen ergreift Smolder jede Gelegenheit, mit seinen aufkeimenden Fähigkeiten zu üben. Er ist zwar noch ein <PERSON>, aber seine Fähigkeiten sind nicht zu verachten. Er setzt mit Leichtigkeit alles in Brand, was brennen kann.", "blurb": "Versteckt zwischen den zerklüfteten Klippen der noxianischen Grenze, unter den wachsamen Augen seiner <PERSON>, lernt ein junger <PERSON>, was es bedeu<PERSON>, Erbe des camavoranischen Königdrachengeschlechts zu sein. Verspielt und begierig zu wachsen ergreift...", "allytips": ["Smolder ist im frühen Spiel sehr verwundbar. Konzentriere dich darauf, passive Steigerungen aufzubauen und am Leben zu bleiben, um später ein starker Drache zu werden!", "<PERSON>mo<PERSON> muss von seinem Team beschützt werden. <PERSON><PERSON><PERSON>, Verbündete zu finden, die dir gegen gegnerische Bedrohungen helfen können.", "Smolder kann Gegnergruppen hohen Schaden zufügen. Suche nach Angriffsgelegenheiten, wenn Gegner nahe beisammenstehen."], "enemytips": ["<PERSON>mo<PERSON> muss von seinem Team beschützt werden. <PERSON><PERSON><PERSON>hn an, wenn sein Team ihn nicht retten kann.", "<PERSON><PERSON> keine <PERSON>, wenn du Smolder gegenüberstehst!", "<PERSON>molder ist anfangs sehr verwundbar. <PERSON><PERSON><PERSON>, seine Schwäche auszunutzen, bevor er lernen kann, ein <PERSON> zu sein!", "Smolders Flug kann durch schwere Massenkontrolle unterbrochen und durch Verlangsamungen beeinträchtigt werden."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "Superfeueratem", "description": "Smolder speit Feuer auf einen Gegner. Je mehr Steigerungen er erhält, desto mächtiger wird diese Fähigkeit.", "tooltip": "Smolder speit Feuer und verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_qdamageincrease }}&nbsp;magischen Schaden</magicDamage>. Wen<PERSON> das Z<PERSON> stirbt, erhält Smolder einmal pro Aktivierung <scaleMana>{{ manarestore }} Mana</scaleMana> zurück.<br /><br />Abhä<PERSON><PERSON> von den Steigerungen von <spellName>Drachentraining</spellName> erhält diese Fähigkeit weitere Effekte:<li><keywordMajor>{{ stacktier1 }} Steigerungen</keywordMajor>: Fügt allen Gegnern um das Ziel Schaden zu.<li><keywordMajor>{{ stacktier2 }} Steigerungen</keywordMajor>: Löst <spellName>{{ tier2_numberofblowback }}</spellName> Explosionen hinter dem Ziel aus, die {{ tier2_blowbackpercentagedamage }}&nbsp;% des Schadens dieser Fähigkeit verursachen.<li><keywordMajor>{{ stacktier3 }} Steigerungen</keywordMajor>: Verbrennt das Ziel und verursacht über {{ tier3_dotlength }}&nbsp;Sekunden hinweg <trueDamage>absoluten Schaden</trueDamage> in Höhe von {{ tier3_burn }} des maximalen Lebens. Gegnerische Champions, deren Gesamtleben unter <trueDamage>{{ tier3_executethreshold }}</trueDamage> fällt, während sie brennen, sterben sofort.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderW", "name": "<PERSON><PERSON><PERSON>!", "description": "<PERSON><PERSON><PERSON> gibt ein niedliche<PERSON>, flammen<PERSON> von sich, das explodiert, wenn gegnerische Champions getroffen werden.", "tooltip": "Smolder gibt ein ni<PERSON><PERSON>, flammendes <PERSON>, das <physicalDamage>{{ initialdamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br /><PERSON><PERSON><PERSON> an Champions lösen eine Explosion aus, die <physicalDamage>{{ explosiondamage }}&nbsp;normalen Schaden</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_wdamageincrease }}&nbsp;magischen <PERSON>haden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Explosionsschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderE", "name": "Flatter, flatter, flatter", "description": "<PERSON>molder fliegt los, ignoriert Terrain und bombardiert den Gegner mit dem niedrigsten Leben.", "tooltip": "Smolder fliegt los, erhält <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed> und ignoriert {{ duration }}&nbsp;Sekunden lang Terrain.<br /><br />Im Flug greift Smolder den Gegner mit dem niedrigsten Leben <spellName>{{ totalnumberofattacks }}</spellName>-mal an (abgerundet) und verursacht pro Treffer <physicalDamage>{{ damageperhit }}&nbsp;normalen Schaden</physicalDamage> + <magicDamage>{{ spell.smolderp:ebonusdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderR", "name": "MAAAMAAAA!", "description": "Smolder ruft seine Mama herbei, die Feuer von oben herabspeit. Gegner im Zentrum ihres Feuers erleiden zusätzlichen Schaden und werden verlangsamt.", "tooltip": "Smolders <PERSON> speit Feuer von oben herab und verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>. <PERSON><PERSON><PERSON> im Zentrum erleiden <physicalDamage>{{ tooltiponly_totalsweetspotdamage }} normalen Schaden</physicalDamage> und werden {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.<br /><br />Mama heilt außerdem ihren Sohn um <healing>{{ momhealcalc }}&nbsp;Leben</healing>, wenn sie ihn trifft.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Drachentraining", "description": "Fähigkeitstreffer gegen Champions und das Töten von Gegnern mit Superfeueratem gewähren eine Steigerung von Drachentraining. Die Steigerungen erhöhen den Schaden von Smolders Grundfähigkeiten.", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}