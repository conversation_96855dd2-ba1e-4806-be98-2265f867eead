{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Riven": {"id": "Riven", "key": "92", "name": "リヴェン", "title": "贖罪の追放者", "image": {"full": "Riven.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "92000", "num": 0, "name": "default", "chromas": false}, {"id": "92001", "num": 1, "name": "浄罪者リヴェン", "chromas": false}, {"id": "92002", "num": 2, "name": "真紅の精鋭リヴェン", "chromas": false}, {"id": "92003", "num": 3, "name": "バトルバニー リヴェン", "chromas": true}, {"id": "92004", "num": 4, "name": "Worlds 2012 リヴェン", "chromas": false}, {"id": "92005", "num": 5, "name": "龍剣リヴェン", "chromas": true}, {"id": "92006", "num": 6, "name": "アーケード リヴェン", "chromas": true}, {"id": "92007", "num": 7, "name": "リイグナイテッド Worlds 2012 リヴェン", "chromas": true}, {"id": "92016", "num": 16, "name": "秩序の光リヴェン", "chromas": true}, {"id": "92018", "num": 18, "name": "パルスファイア リヴェン", "chromas": true}, {"id": "92020", "num": 20, "name": "勇猛の剣リヴェン", "chromas": false}, {"id": "92022", "num": 22, "name": "プレステージ勇猛の剣リヴェン", "chromas": false}, {"id": "92023", "num": 23, "name": "精霊の花祭りリヴェン", "chromas": true}, {"id": "92034", "num": 34, "name": "光の番人リヴェン", "chromas": true}, {"id": "92044", "num": 44, "name": "バトルバニー プライム リヴェン", "chromas": true}, {"id": "92045", "num": 45, "name": "プレステージ勇猛の剣リヴェン(2022)", "chromas": false}, {"id": "92055", "num": 55, "name": "破られし盟約リヴェン", "chromas": true}, {"id": "92063", "num": 63, "name": "原始の襲撃リヴェン", "chromas": true}], "lore": "リヴェンはかつてノクサス軍の剣術家だったが、今では自らが過去に征服しようとした土地で追放者として暮らしている。彼女は信念と非道なまでの手際よさを武器に軍隊で昇進し、伝説のルーンブレードと自らの戦団を授かった。しかし、アイオニアとの戦いで故郷への信念が試されることになり、結局、それは破壊されてしまった。ノクサスそのものが再構築されたという噂が盛んに飛び交うなか、彼女は帝国とのつながりをすべて断ち切り、砕けた世界で自分の居場所を探している。", "blurb": "リヴェンはかつてノクサス軍の剣術家だったが、今では自らが過去に征服しようとした土地で追放者として暮らしている。彼女は信念と非道なまでの手際よさを武器に軍隊で昇進し、伝説のルーンブレードと自らの戦団を授かった。しかし、アイオニアとの戦いで故郷への信念が試されることになり、結局、それは破壊されてしまった。ノクサスそのものが再構築されたという噂が盛んに飛び交うなか、彼女は帝国とのつながりをすべて断ち切り、砕けた世界で自分の居場所を探している。", "allytips": ["「折れた翼」は発動時のカーソル方向に繰り出される。敵との交戦を避けたい時などは、カーソル位置に気をつけよう。", "リヴェンは自己体力回復の手段が少なく、守備も強力とは言えない。そのため強威力のコンボ攻撃によって、こうした弱点をカバーする戦い方が求められる。「折れた翼」や「気功破」で戦闘へと斬り込み、危なくなったら「勇躍」で身を守りながら追撃をかわし退却しよう。"], "enemytips": ["敏捷性に優れるリヴェンだが、瞬時に長距離を移動できるスキルは持ち合わせていない。そのため攻撃を仕掛けてきたら、すかさずスタン状態あるいはサイレンス状態にさせて間合いを取ることが重要だ。", "リヴェンの攻撃はいずれも物理ダメージなので、リヴェンに苦しめられた時は物理防御を優先的に強化してみよう。", "リヴェンのスキルは自分の周囲の範囲にダメージを与えるものが多いため、近距離であれば複数の敵と同時に戦うのも可能だ。味方と共に攻めかかるならリヴェンのコンボ攻撃が途切れるのを待ったほうがいいだろう。"], "tags": ["Fighter", "Assassin"], "partype": "なし", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 8}, "stats": {"hp": 630, "hpperlevel": 100, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "RivenTriCleave", "name": "折れた翼", "description": "連続して敵を斬りつける。断続的に3回まで再使用でき、3回目を命中させると周囲の敵をノックアップさせる。", "tooltip": "前方にダッシュして<physicalDamage>{{ firstslashdamage }}の物理ダメージ</physicalDamage>を与える。このスキルは2回<recast>再発動</recast>できる。1回目の<recast>再発動</recast>は通常の効果と同じだが、2回目の再発動では別の効果を獲得する:<br /><br /><recast>再発動</recast>: 前方に跳躍して剣を叩きつけ、周囲の敵に<physicalDamage>{{ firstslashdamage }}の物理ダメージ</physicalDamage>を与えて、0.75秒間<status>ノックアップ</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "攻撃力反映率"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [13, 13, 13, 13, 13], "cooldownBurn": "13", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [45, 75, 105, 135, 165], [30, 55, 80, 105, 130], [150, 225, 300, 375, 450], [2, 2, 2, 2, 2], [65, 70, 75, 80, 85], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/75/105/135/165", "30/55/80/105/130", "150/225/300/375/450", "2", "65/70/75/80/85", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "RivenTriCleave.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "RivenMartyr", "name": "気功破", "description": "「気功破」を放ち、周囲にいる敵にダメージを与えてスタン効果を付与する。", "tooltip": "剣からルーンの力を解き放ち、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与えて、{{ e5 }}秒間<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [40, 70, 100, 130, 160], [3, 3, 3, 3, 3], [0.75, 0.75, 0.75, 0.75, 0.75], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "40/70/100/130/160", "3", "0.75", "0.75", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [260, 260, 260, 260, 260], "rangeBurn": "260", "image": {"full": "RivenMartyr.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "Riven<PERSON><PERSON><PERSON>", "name": "勇躍", "description": "前方へ短いステップを踏み、敵の攻撃を軽減するシールドを身にまとう。", "tooltip": "素早くダッシュして1.5秒間<shield>耐久値{{ totalshield }}のシールド</shield>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "クールダウン"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [70, 95, 120, 145, 170], [4, 4, 4, 4, 4], [800, 800, 800, 800, 800], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "70/95/120/145/170", "4", "800", "1.5", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "RivenFeint.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "RivenFengShuiEngine", "name": "追放者の剣", "description": "神秘的な力によって砕けた剣を再生させ、攻撃力と射程を増加させる。さらに効果時間中、強力な遠隔攻撃である「ウィンドスラッシュ」を一度だけ発動できる。", "tooltip": "剣が神秘的な力に包まれ、{{ duration }}秒間<physicalDamage>攻撃力が{{ bonusad }}</physicalDamage>増加して、攻撃スキルと通常攻撃の射程が増加する。また、効果時間中はこのスキルを<recast>再発動</recast>できる。<br /><br /><recast>再発動:</recast> 風の斬撃を放ち、対象の減少体力に応じて<physicalDamage>{{ mindamage }}</physicalDamage> - <physicalDamage>{{ maxdamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ウィンドスラッシュ」最小ダメージ", "「ウィンドスラッシュ」最大ダメージ", "クールダウン"], "effect": ["{{ minbase }} -> {{ minbaseNL }}", "{{ maxbase }} -> {{ maxbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [100, 150, 200], [300, 450, 600], [20, 20, 20], [15, 15, 15], [25, 25, 25], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "100/150/200", "300/450/600", "20", "15", "25", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "RivenFengShuiEngine.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "ルーンブレード", "description": "スキルを使用すると剣に力が宿り、通常攻撃でその力を消費して追加ダメージを与える。", "image": {"full": "RivenRunicBlades.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}