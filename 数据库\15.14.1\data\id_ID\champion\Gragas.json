{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gragas": {"id": "Gragas", "key": "79", "name": "Gragas", "title": "the Rabble Rouser", "image": {"full": "Gragas.png", "sprite": "champion1.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "79000", "num": 0, "name": "default", "chromas": false}, {"id": "79001", "num": 1, "name": "Scuba Gragas", "chromas": false}, {"id": "79002", "num": 2, "name": "Hillbilly Gragas", "chromas": false}, {"id": "79003", "num": 3, "name": "Santa Gragas", "chromas": false}, {"id": "79004", "num": 4, "name": "Gragas, Esq.", "chromas": false}, {"id": "79005", "num": 5, "name": "<PERSON><PERSON> Gragas", "chromas": false}, {"id": "79006", "num": 6, "name": "Oktoberfest Gragas", "chromas": false}, {"id": "79007", "num": 7, "name": "Superfan <PERSON>", "chromas": false}, {"id": "79008", "num": 8, "name": "Fnatic Gragas", "chromas": false}, {"id": "79009", "num": 9, "name": "Gragas Caskbreaker", "chromas": false}, {"id": "79010", "num": 10, "name": "Arctic Ops Gragas", "chromas": false}, {"id": "79011", "num": 11, "name": "<PERSON>", "chromas": true}, {"id": "79020", "num": 20, "name": "Space Groove Gragas", "chromas": true}, {"id": "79029", "num": 29, "name": "High Noon Gragas", "chromas": true}, {"id": "79039", "num": 39, "name": "Music Fan Gragas", "chromas": true}], "lore": "Gragas adalah raksasa pembuat minuman keras yang riang sekaligus mengintimidasi, selalu mencari cara baru untuk mengangkat semangat semua orang. Berasal dari tempat yang tak diketahui, ia men<PERSON><PERSON><PERSON>i alam liar <PERSON><PERSON><PERSON> yang belum tersentuh untuk mencari bahan-bahan demi menyempurnakan ramuan terbarunya. Ia impulsif, kera<PERSON> kep<PERSON>, dan terkenal karena keributan yang ia mulai. Sering kali itu berakhir dengan pesta semalam suntuk dan kerusakan besar-besaran. Setiap kemunculan Gragas hampir pasti menandakan kesenangan dan kehancuran—dalam urutan itu.", "blurb": "Gragas adalah raksasa pembuat minuman keras yang riang sekaligus mengintimidasi, selalu mencari cara baru untuk mengangkat semangat semua orang. Berasal dari tempat yang tak diketahui, ia men<PERSON><PERSON><PERSON>i alam liar <PERSON><PERSON><PERSON> yang belum tersentuh untuk mencari...", "allytips": ["Pengurangan damage dari Drunken Rage diterapkan saat kamu mulai minum, coba gunakan saat melihat damage akan datang.", "Coba dorong musuh ke turret-mu dengan Explosive Cask.", "Coba kombo Body Slam dengan Explosive Cask untuk menyiapkan kill bagi timmu."], "enemytips": ["Gragas bisa memukul mundur semuanya dengan ultimanya. Hati-hati agar tidak terdorong ke arah Graga<PERSON> atau, lebih buruk lagi, ke turret musuh.", "Body Slam punya cooldown yang sangat cepat, se<PERSON>ga sulit untuk mengejar Gragas. <PERSON>an habiskan waktumu untuk mengejarnya."], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 115, "mp": 400, "mpperlevel": 47, "movespeed": 330, "armor": 38, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.05, "attackspeed": 0.675}, "spells": [{"id": "GragasQ", "name": "Barrel Roll", "description": "Gragas menggulingkan tong ke sebuah lokasi. Tong dapat diaktifkan agar meledak atau meledak sendiri setelah 4 detik. Kekuatan ledakan akan meningkat sepanjang waktu. Move Speed musuh yang terkena ledakan ini menjadi slow.", "tooltip": "Gragas menggulingkan tong yang akan meledak setelah {{ e4 }} detik, menghasilkan antara <magicDamage>{{ mindamage }} magic damage</magicDamage> dan <magicDamage>{{ maxdamage }} magic damage</magicDamage>, sekaligus menerapkan <status>Slow</status> sebesar {{ e2 }} dan {{ effect2amount*1.5 }}% selama {{ e3 }} detik. Damage dan <status>Slow</status> meningkat sepanjang waktu tong belum meledak. <br /><br />Gragas dapat melakukan <recast>Recast</recast> untuk meledakkan tong lebih awal.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ e2 }}-> {{ e2NL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [40, 45, 50, 55, 60], [2, 2, 2, 2, 2], [4, 4, 4, 4, 4], [2, 2, 2, 2, 2], [150, 150, 150, 150, 150], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "40/45/50/55/60", "2", "4", "2", "150", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "GragasQ.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasW", "name": "Drunken Rage", "description": "Gragas mencicipi ramuan terbarunya selama 1 detik. <PERSON><PERSON><PERSON>, ia menjadi riang dan agres<PERSON>, men<PERSON><PERSON>lkan magic damage ke semua musuh di sekitar pada basic attack be<PERSON><PERSON><PERSON>, serta mengurangi damage yang diterima.", "tooltip": "Gragas mencicipi ramuannya, mengurangi damage yang diterima sebesar {{ damagereduction }} selama {{ defenseduration }} detik dan memperkuat Serangan berikutnya untuk memberikan <magicDamage>{{ totaldamage }}</magicDamage> tambahan ditambah <magicDamage>{{ maxhppercentdamage }}% dari Health maksimum sebagai magic damage</magicDamage> ke target dan musuh di sekitar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Pengurangan Damage", "Damage"], "effect": ["{{ basedamagereduction }}%-> {{ basedamagereductionNL }}%", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "GragasW.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasE", "name": "Body Slam", "description": "Gragas melakukan charge ke lokasi tertentu, menabrak unit musuh pertama yang dia temui, dan men<PERSON><PERSON><PERSON><PERSON> damage pada semua unit musuh di sekitar, serta menerapkan stun pada mereka.", "tooltip": "Gragas melakukan charge, menabrak musuh pertama, men<PERSON><PERSON><PERSON> <status>Knock Up</status> pada musuh di sekitar selama {{ stunduration }} detik dan menghasilkan <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Cooldown Ability ini dikurangi sebesar {{ cooldownrefund*100 }}% jika Gragas bertabrakan dengan musuh.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GragasE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GragasR", "name": "Explosive Cask", "description": "Gragas melemparkan tongnya ke suatu lokasi dan men<PERSON><PERSON><PERSON><PERSON> damage serta memukul mundur musuh yang terperangkap dalam radius ledakan.", "tooltip": "G<PERSON>s melem<PERSON>an ton<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damagedone }} magic damage</magicDamage> dan melakukan <status>Knock Away</status> musuh dari zona ledakan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GragasR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Happy Hour", "description": "Gragas secara berkala mendapatkan heal saat menggunakan keahlian.", "image": {"full": "GragasPassiveHeal.png", "sprite": "passive1.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}