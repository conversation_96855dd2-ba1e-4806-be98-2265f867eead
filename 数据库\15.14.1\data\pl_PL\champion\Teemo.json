{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Teemo": {"id": "<PERSON><PERSON><PERSON>", "key": "17", "name": "<PERSON><PERSON><PERSON>", "title": "Chyży Zwiadowca", "image": {"full": "Teemo.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "17000", "num": 0, "name": "default", "chromas": false}, {"id": "17001", "num": 1, "name": "Świąteczny Elf Teemo", "chromas": false}, {"id": "17002", "num": 2, "name": "Zwiadowca Teemo", "chromas": false}, {"id": "17003", "num": 3, "name": "Borsuk Teemo", "chromas": false}, {"id": "17004", "num": 4, "name": "Astron<PERSON><PERSON>", "chromas": true}, {"id": "17005", "num": 5, "name": "Króliczek Teemo", "chromas": true}, {"id": "17006", "num": 6, "name": "Super-Teemo", "chromas": false}, {"id": "17007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17008", "num": 8, "name": "<PERSON><PERSON><PERSON> z Oddziału Omega", "chromas": true}, {"id": "17014", "num": 14, "name": "<PERSON>ab<PERSON>", "chromas": true}, {"id": "17018", "num": 18, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "17025", "num": 25, "name": "<PERSON><PERSON><PERSON> Duchowe<PERSON>", "chromas": false}, {"id": "17027", "num": 27, "name": "Teemo Duchowego Rozkwitu (Prestiżowy)", "chromas": false}, {"id": "17037", "num": 37, "name": "Rozrywkowy Teemo", "chromas": true}, {"id": "17047", "num": 47, "name": "Teemo na Kosmofazie", "chromas": true}], "lore": "Nie bacząc na najbardziej niebezpieczne przeszkody, <PERSON>emo przemierza świat z niekończącym się entuzjazmem i radością. Jako Yordle z niezachwianym poczuciem moralności, jest dumny z przestrzegania Kodeksu Harcerza Bandle, czasami do takiego stopnia, że nie zdaje sobie sprawy z konsekwencji jego czynów. Niektórzy mówią, że istnienie Zwiadowców jest wątpliwe, lecz jedna rzecz jest pewna: z osądem Teemo nie można dyskutować.", "blurb": "Nie bacząc na najbardziej niebezpieczne przeszkody, Teemo przemierza świat z niekończącym się entuzjazmem i radością. Jako Yordle z niezachwianym poczuciem moralności, jest dumny z przestrzegania Kodeksu Harcerza Bandle, czasami do takiego stopnia, że...", "allytips": ["Grzyby Teemo mogą być użyte do skutecznego farmienia grup stworów.", "Umieszczaj grzyby w kluczowych lokacjach, takich jak <PERSON><PERSON><PERSON> lub <PERSON>, a<PERSON> w<PERSON>, k<PERSON>y przeciwnicy próbują ich zabić."], "enemytips": ["Toksyczne Strzałki Teemo karz<PERSON> graczy, którzy podchodzą zbyt blisko, a następnie się cofają. Trzymaj się w bezpiecznej odległości do momentu, w którym atak będzie możliwy.", "Dobrze wykorzystać Soczewkę Wyroczni (Talizman) do niszczenia grzybów w kluczowych punktach."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 6}, "stats": {"hp": 615, "hpperlevel": 104, "mp": 334, "mpperlevel": 25, "movespeed": 330, "armor": 24, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 9.6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3, "attackspeedperlevel": 3.38, "attackspeed": 0.69}, "spells": [{"id": "TeemoQ", "name": "Oślepiająca Strzałka", "description": "Oślepia przeciwnika potężnym jadem, zadając mu obrażenia w czasie działania.", "tooltip": "<PERSON><PERSON><PERSON> wystr<PERSON> str<PERSON>ł<PERSON>, kt<PERSON>ra <status>oślepia</status> cel na {{ blindduration }} sek. i zadaje <magicDamage>{{ calculateddamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Koszt (@AbilityResourceName@)", "Czas działania", "Obrażenia"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ blindduration }} -> {{ blinddurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoQ.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TeemoW", "name": "Szybkie Ruchy", "description": "<PERSON><PERSON><PERSON> tru<PERSON>, biernie zwiększając prędkość ruchu do momentu zostania trafionym przez wrogiego bohatera bądź wieżę. Teemo może szybko bi<PERSON>, dzięki czemu na chwilę zyskuje dodatkową prędkość ruchu, która nie jest redukowana przez trafienie.", "tooltip": "<spellPassive>B<PERSON>nie:</spellPassive> <PERSON><PERSON><PERSON> <speed>{{ passivemovespeedbonus*100 }}% prędkości ruchu</speed>, jeśli nie otrzy<PERSON>ł obrażeń od bohatera lub wieży w ciągu ostatnich {{ passivecooldownondamagetaken }} sek.<br /><br /><spellActive>Użycie:</spellActive> Teemo biegnie sprintem, zyskując na {{ activemovespeedbuffduration }} sek. <speed>{{ activemovespeedbonus*100 }}% prędkości ruchu</speed>, której nie traci przy otrzymaniu obrażeń.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bierna pręd<PERSON>ć ruchu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć ruchu po użyciu"], "effect": ["{{ passivemovespeedbonus*100.000000 }}% -> {{ passivemovespeedbonusnl*100.000000 }}%", "{{ activemovespeedbonus*100.000000 }}% -> {{ activemovespeedbonusnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TeemoW.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TeemoE", "name": "Toksyczne Strzałki", "description": "<PERSON><PERSON><PERSON> atak Teemo zatruwa cel, zadając obrażenia przy uderzeniu, a potem co sekundę przez 4 sekundy.", "tooltip": "<spellPassive>Biernie:</spellPassive> <PERSON><PERSON>em<PERSON> truciznę, która zadaje dodatkowo <magicDamage>{{ impactcalculateddamage }} pkt. obrażeń magicznych</magicDamage> plus <magicDamage>{{ totaldotdamage }} pkt. obrażeń magicznych</magicDamage> w ciągu {{ poisonduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od uderzenia", "Obrażenia na sekundę"], "effect": ["{{ impactbasedamage }} -> {{ impactbasedamageNL }}", "{{ tickbasedamage }} -> {{ tickbasedamageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoE.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "TeemoR", "name": "Trująca Pułapka", "description": "Teemo rzuca trującą pułapką wybuchową w postaci grzyba, z plecaka. Po uruchomieniu pułapka wypuści obłok trucizny, spowalniając przeciwników i zadając obrażenia wraz z upływem czasu. Jeśli Teemo rzuci pułapką w inną pułapkę, ta odbije się, zyskując dodatkowy zasięg.", "tooltip": "Teemo rzuca grzybową pułapkę, kt<PERSON>ra wybucha, gdy ktoś na nią nadepnie. Pułapki <status>spowalniają</status> o {{ slowamount }}% i zadają <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> w ciągu {{ debuffduration }} sek. Wrogowie zostają ujawnieni na tyle samo czasu.<br /><br />Pułapki utrzymują się przez {{ mushroomduration }} min i są ukryte. Grzyb rzucony na innego grzyba odbije się przed wylądowaniem. Ta umiejętność ma następującą liczbę ładunków: {{ maxammo }} ({{ ammorechargetime }} sek. odnowienia).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "<PERSON><PERSON><PERSON><PERSON> rzu<PERSON>", "Maksymalny dystans odbicia", "Maksymalna liczba pułapek", "Koszt many"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ maxbouncedistance }} -> {{ maxbouncedistanceNL }}", "{{ maxammo }} -> {{ maxammoNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 3, "cooldown": [0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [75, 55, 35], "costBurn": "75/55/35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "3", "range": [600, 750, 900], "rangeBurn": "600/750/900", "image": {"full": "TeemoR.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Partyzantka", "description": "Pozostając przez jakiś czas w bezruchu i nie wykonując żadnych akcji, Teemo na czas nieokreślony staje się niewidzialny. Będąc w z<PERSON><PERSON><PERSON><PERSON>, Teemo może aktywować i utrzymać niewidzialność w trakcie ruchu. Po utracie niewidzialności Teemo zyskuje Element Zaskoczenia, zwiększając swoją prędkość ataku na kilka sekund.", "image": {"full": "TeemoPassive.ASU_Teemo.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}