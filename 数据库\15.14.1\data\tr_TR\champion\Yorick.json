{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yorick": {"id": "<PERSON><PERSON>", "key": "83", "name": "<PERSON><PERSON>", "title": "Ruhların <PERSON>ı", "image": {"full": "Yorick.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "83000", "num": 0, "name": "default", "chromas": false}, {"id": "83001", "num": 1, "name": "Cenaze<PERSON>", "chromas": false}, {"id": "83002", "num": 2, "name": "Pentakill Yorick", "chromas": false}, {"id": "83003", "num": 3, "name": "Işık Bekçisi Yorick", "chromas": false}, {"id": "83004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "83012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "83021", "num": 21, "name": "Pentakill III: Lost Chapter Yorick", "chromas": true}, {"id": "83030", "num": 30, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "83040", "num": 40, "name": "Sonsuz Karanlık Yorick", "chromas": true}, {"id": "83050", "num": 50, "name": "Vahşi Batılı Yo<PERSON>", "chromas": true}], "lore": "Uzun zaman önce unutulmuş bir inanç nizamının hayatta kalan son <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> gücüyle hem kutsanmış hem de lanetlenmiş biri. Gölge Ada<PERSON>'a sıkışıp kalan Yorick'in çürüyen cesetler ve feryatlar içinde etrafında toplanan ruhlardan başka yoldaşı yok. Dehşet verici eylemlerinin gölgesinde kalsa da Yorick'in asil bir amacı var; memleketini Afet'le birlikte üstüne çöken lanetten kurtarmak.", "blurb": "Uzun zaman önce unutulmuş bir inanç nizamının hayatta kalan son <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> gü<PERSON><PERSON><PERSON> hem kutsanmış hem de lanetlenmiş biri. Gölge Ada<PERSON>'a sıkışıp kalan Yorick'in çürüyen cesetler ve feryatlar içinde etrafında toplanan ruhlardan başka yoldaşı...", "allytips": ["Son <PERSON><PERSON> yeniden kullanabilmek için Uyanış kullanman şart değildir.", "Sis'in Hanımı çatışmalarda sana yardımcı olmaya çalışır; bu yüzden hedeflerini iyi seç.", "Sis'in Hanımı'nı bir koridora tek başına gönderebilirsin ama dikkatli ol; çatışma gücünün büyük bölümünü Sis'in Hanımı'na borçlu olduğun için bu, gücünden fedakârlık yapman anlamına gelebilir."], "enemytips": ["<PERSON><PERSON>p b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hasar verme veya katletme amacıyla Sis Gezginleri ve Sis'in Hanımı üzerinde kullanabilirsin.", "<PERSON><PERSON><PERSON><PERSON> çatışmaya başlamadan önce minyonlarını azaltmaya çalış; Sis Gezginleri normal saldırı veya tek hedefli yeteneklerle katledilebilir.", "Kara Tören'le ortaya çıkan duvarı saldırılarınla yıkabilirsin."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 36, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON><PERSON>", "name": "<PERSON>", "description": "<PERSON><PERSON> bir sonraki saldırısında ilave hasar verir ve kendini iyileştirir. Hedefi bir şampiyon veya büyük canavarsa veya hedefi katledilirse arkasında bir mezar bırakır.", "tooltip": "Yorick'in sonraki saldırısı fazladan <physicalDamage>{{ bonusdamage }} Fiziksel <PERSON></physicalDamage> verir ve <healing>{{ qheal }} artı eksik canının %{{ missinghealthratio }} kadar<PERSON>na e<PERSON><PERSON> miktarda can</healing> yeniler. İyileştirme miktarı şampiyonlar harici birimlere karşı %{{ healreduction }} azalır. Bu saldırı, bir şampiyon veya büyük canavara isabet ederse ya da hedefini katlederse arkasında bir mezar bırakır.<br /><br /><PERSON><PERSON>, yakınında 3 veya daha fazla mezar varsa ve bu yetenek zaten kullanılmışsa onu <recast>yeniden kullanarak</recast> yakınındaki tüm mezarlardan <keywordMajor>Sis Gezgini</keywordMajor> çıkarabilir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "İyileştirme", "<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "%{{ missinghealthratio }} -> %{{ missinghealthratioNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YorickQ.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YorickW", "name": "<PERSON>", "description": "<PERSON><PERSON>'in hedef konumda ortaya çıkardığı yıkılabilir duvar rakiplerin hareketini engeller.", "tooltip": "<PERSON><PERSON> r<PERSON>in yolunu kesen ama takım arkadaşlarını engellemeyen bir ruh duvarı çağırır. Duvar <healing>{{ wallhealthtooltip }} Cana</healing> sahip olur ve {{ circleduration }} saniyenin ardından kaybolur.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Can"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wallhealthtooltip }} -> {{ wallhealthtooltipNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "YorickW.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON>E", "name": "<PERSON><PERSON>", "description": "Yorick'in attığı Kara Sis kitlesi hasar verdiği hedeflerin zırhını azaltır ve onları yavaşlatarak işaretler. Yorick'in çağırdığı birimler işaretli hedeflere doğru ilerlerken hareket hızı kazanır. ", "tooltip": "Yorick'in attığı Kara Sis kitlesi <magicDamage>azami canın {{ calc_healthdamage }} kadar<PERSON>na eşde<PERSON> miktarda b<PERSON><PERSON><PERSON> hasarı</magicDamage> verir, {{ slowduration }} saniyeliğine {{ calc_slow }} <status>yavaşlatır</status> ve şampiyonlar ile canavarları {{ markduration }} saniyeliğine işaretler. İşaretli rakipler yakınlarındaki mezarları <spellName>uyandırmaya</spellName> devam eder (En fazla {{ spell.yorickpassive:yorickpassiveghoulmax }} Sis Gezgini'ne sahip olabilir.) ve <scaleArmor>%{{ armorshred*100 }} zırh kaybeder</scaleArmor>.<br /><br />Yorick ve çağırdığı birimler, işaretli hedeflere doğru ilerlerken <speed>%{{ hasteamount*100 }} Hareket Hızı</speed> kazanır. <keywordMajor><PERSON><PERSON></keywordMajor> kendilerinden uzaklaşan işaretli rakiplerin üstüne atlar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zırh Azaltma Yüzdesi", "Hareket Hızı", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Bedeli", "<PERSON><PERSON><PERSON>"], "effect": ["%{{ armorshred*100.000000 }} -> %{{ armorshrednl*100.000000 }}", "%{{ hasteamount*100.000000 }} -> %{{ hasteamountnl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "%{{ healthdamage }} -> %{{ healthdamageNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YorickE.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Yo<PERSON>R", "name": "Adaların Ağıdı", "description": "<PERSON><PERSON>'in Hanımı'nı çağırır ve Yorick'in yapacağı Sis'in Hanımı'nın hedefine yönelik saldırıları ilave hasar verir. Sis'in Hanımı da katledilen rakipleri komuta ihtiyaç duymadan Sis Gezgini olarak diriltir.", "tooltip": "Yorick <healing>{{ yorickbigghoulhealth }} Can</healing> ve <magicDamage>{{ yorickbigghouldamage }} Büyü Hasarına</magicDamage> sahip <keywordMajor>Sis'in Hanımı</keywordMajor>'nın yanı sıra {{ rghoulnumbers }} <keywordMajor>Si<PERSON></keywordMajor> çağırır. <keywordMajor>Sis'in Hanımı</keywordMajor> yakınında katledilen rakiplerden otomatik olarak <keywordMajor>Si<PERSON></keywordMajor> ortaya çıkarır ve saldırılarıyla rakip şampiyonları işaretler. Yorick, <keywordMajor>Hanım</keywordMajor>'ın hedefine hasar verdiği zaman <magicDamage>hedefin azami canının %{{ rmarkdamagepercent }} kadarına eşdeğer miktarda bü<PERSON><PERSON> hasarı</magicDamage> verir.<br /><br />Yorick 10 saniyenin ardından bu yeteneği <recast>yeniden kull<PERSON></recast> <keywordMajor>Hanım</keywordMajor>'ı serbest bırakır ve onu en yakınındaki koridora gönderir.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Saldırı Gücü", "İşaret Hasarı", "<PERSON><PERSON> Gez<PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["{{ rbigghoulbonusad }} -> {{ rbigghoulbonusadNL }}", "%{{ rmarkdamagepercent }} -> %{{ rmarkdamagepercentNL }}", "{{ rghoulnumbers }} -> {{ rghoulnumbersNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 130, 100], "cooldownBurn": "160/130/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "YorickR.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ruhların <PERSON>ı", "description": "<font color='#FF9900'><PERSON><PERSON><PERSON>:</font> <PERSON><PERSON>ındaki rakiplerin etrafını saran ve onlara saldıran Sis Gezginleri çağırır.", "image": {"full": "Yo<PERSON>_P.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}