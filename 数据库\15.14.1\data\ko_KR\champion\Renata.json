{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "레나타 글라스크", "title": "화공 남작", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "제독 글라스크", "chromas": true}, {"id": "888010", "num": 10, "name": "공포의 밤 레나타 글라스크", "chromas": true}, {"id": "888020", "num": 20, "name": "망자의 환영 레나타 글라스크", "chromas": true}, {"id": "888021", "num": 21, "name": "프레스티지 망자의 환영 레나타 글라스크", "chromas": false}, {"id": "888031", "num": 31, "name": "검은 장미단의 가면 무도회 레나타 글라스크", "chromas": false}], "lore": "레나타 글라스크는 어린 시절 집의 잿더미를 딛고 일어섰다. 그때 레나타가 가진 것은 이름과 부모님의 연금술 연구 자료뿐이었다. 수십 년이 지나, 레나타는 자운에서 가장 부유한 화공 남작 겸 거물 사업가가 되었다. 그녀는 모든 사람의 이해관계를 자신과 묶어서 막대한 힘을 쌓았다. 레나타와 함께하는 자는 상상 이상의 보상을 받는다. 레나타를 거스르는 자는 그 선택을 후회하며 살아간다. 하지만 결국에는 모두가 그녀의 편에 설 것이다.", "blurb": "레나타 글라스크는 어린 시절 집의 잿더미를 딛고 일어섰다. 그때 레나타가 가진 것은 이름과 부모님의 연금술 연구 자료뿐이었다. 수십 년이 지나, 레나타는 자운에서 가장 부유한 화공 남작 겸 거물 사업가가 되었다. 그녀는 모든 사람의 이해관계를 자신과 묶어서 막대한 힘을 쌓았다. 레나타와 함께하는 자는 상상 이상의 보상을 받는다. 레나타를 거스르는 자는 그 선택을 후회하며 살아간다. 하지만 결국에는 모두가 그녀의 편에 설 것이다.", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "악수", "description": "레나타가 미사일을 발사해 처음 적중하는 적을 속박합니다. 스킬을 재사용해 대상을 지정한 방향으로 던질 수 있습니다.", "tooltip": "레나타 글라스크가 의수에서 미사일을 발사해 처음 적중하는 적을 {{ rootduration }}초간 <status>속박</status>하고 <magicDamage>{{ totaldamage }}</magicDamage>의 <magicDamage>마법 피해</magicDamage>를 입힙니다.<br /><br /><recast>재사용 시:</recast> 레나타가 대상을 지정한 방향으로 <status>던져</status> 적중하는 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 챔피언을 던진 경우 적중하는 적을 {{ stunduration }}초간 <status>기절</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RenataW", "name": "긴급 구제", "description": "레나타가 아군 챔피언을 강화하고 죽음을 늦춥니다. 해당 아군이 처치에 관여하면 목숨을 건질 수 있습니다.", "tooltip": "레나타가 아군 챔피언을 강화합니다. 강화된 대상은 <attackSpeed>{{ ascalc }}의 공격 속도</attackSpeed>를 얻고 적을 향해 이동할 때 <speed>{{ mscalc }}의 이동 속도</speed>를 얻습니다. 이 효과는 {{ duration }}초에 걸쳐 <attackSpeed>공격 속도는 {{ finalascalc }}</attackSpeed>, <speed>이동 속도는 {{ finalmscalc }}</speed>까지 증가합니다. 처치 관여 시 효과 지속시간이 초기화됩니다.<br /><br />대상이 죽으면 체력을 완전히 회복한 후 3초에 걸쳐 부식됩니다.<br /><br />부식 중에 처치에 관여하면 체력이 <healing>최대 체력의 {{ triumphpercent }}%</healing>가 되고 부식이 중단됩니다.<br /><br /><rules>부식 중인 챔피언의 죽음은 체력 회복 등의 효과로 늦출 수 있지만, 해당 챔피언이 처치에 관여하지 않는 한 죽음을 막을 수 없습니다. 챔피언의 죽음은 한 번만 늦출 수 있습니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["공격 속도", "이동 속도", "재사용 대기시간"], "effect": ["{{ bonusattackspeed }}% -> {{ bonusattackspeedNL }}%", "{{ bonusmovespeed }}% -> {{ bonusmovespeedNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RenataE", "name": "충성 고객 우대", "description": "레나타가 화학공학 미사일 두 발을 발사해 아군에게 보호막을 씌우며 적중하는 적에게 피해를 입히고 둔화시킵니다.", "tooltip": "레나타가 화학공학 미사일 두 발을 발사해 적중하는 적과 주변 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초간 30% <status>둔화</status>시킵니다. 적중하는 아군에게는 {{ shieldduration }}초간 <shield>{{ shieldcalc }}의 피해를 흡수하는 보호막</shield>을 씌웁니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "보호막 흡수량", "소모값 @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RenataR", "name": "적대적 인수", "description": "레나타가 화학 물질의 파도를 방출해 적중하는 적을 광란 상태에 빠트립니다.", "tooltip": "레나타가 화학 물질의 파도를 방출합니다. 적중당한 적은 {{ berserkduration }}초간 <status>광란</status> 상태에 빠져 근처 유닛을 기본 공격합니다. (자신의 아군 우선)<br /><br /><status>광란</status> 상태에 빠진 적은 <attackSpeed>공격 속도가 {{ bonusattackspeed*100 }}%</attackSpeed> 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "지속시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "영향력", "description": "레나타의 기본 공격이 추가 피해를 입히고 표식을 남깁니다. 아군은 표식이 남은 적을 공격해 추가 피해를 입힐 수 있습니다.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}