{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Trundle": {"id": "Trundle", "key": "48", "name": "Trundle", "title": "der Trollkönig", "image": {"full": "Trundle.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "48000", "num": 0, "name": "default", "chromas": false}, {"id": "48001", "num": 1, "name": "Baseball-Trundle", "chromas": false}, {"id": "48002", "num": 2, "name": "Schrottplatz-Trundle", "chromas": false}, {"id": "48003", "num": 3, "name": "<PERSON><PERSON> Trun<PERSON>", "chromas": false}, {"id": "48004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "48005", "num": 5, "name": "Weltenbrecher-Trundle", "chromas": false}, {"id": "48006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "48012", "num": 12, "name": "Schreckensnacht-Trundle", "chromas": true}, {"id": "48021", "num": 21, "name": "E-Sport-<PERSON>", "chromas": true}], "lore": "Trundle ist ein imposanter und verschlagener Troll mit besonders boshaftem Charakter, und es gibt nichts, was er mit seinem Knüppel nicht unterwerfen könnte – nicht ein<PERSON> Freljord selbst kann ihm widerstehen. Er kämpft erbittert um sein Territorium und verfolgt jeden, der dumm genug ist, sein Revier zu betreten. Mit seinem gewaltigen Knüppel aus wahrem Eis jagt er seinen Gegnern die Kälte in die Knochen, pfählt sie mit gezackten, gef<PERSON>renen Säulen und sieht ihnen lachend dabei zu, wie sie in der Tundra verbluten.", "blurb": "Trundle ist ein imposanter und verschlagener Troll mit besonders boshaftem Charakter, und es gibt nichts, was er mit seinem Knüppel nicht unterwerfen könnte – nicht ein<PERSON> Fr<PERSON>jord selbst kann ihm widerstehen. Er kämpft erbittert um sein Territorium und...", "allytips": ["Trundle sticht beim <PERSON> in seiner „Eisigen Domäne“ hervor. Versuche Gegner hinein zu locken.", "Benutze „Unterwerfen“, um einen gegnerischen Tank „aufzuweichen“ oder ein Ziel für den Fokus deines Teams zu schwächen.", "„Mampfen“ eignet sich gut, um den normalen Schaden von G<PERSON>n zu senken; konzentriere dich damit auf G<PERSON>, die vor allem auf Angriffsschaden setzen."], "enemytips": ["Trundle ist sehr gut darin, an einer bestimmten Stelle zu kämpfen. Versuche, ihn aus seiner „Eisigen Domäne“ zu locken.", "<PERSON><PERSON><PERSON>, mö<PERSON><PERSON>t schnell aus dem Wirkbereich seiner „Eissäule“ zu entkommen, da sie dich deutlich verlangsamt."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 6, "magic": 2, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 110, "mp": 340, "mpperlevel": 45, "movespeed": 350, "armor": 37, "armorperlevel": 3.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 2.9, "attackspeed": 0.67}, "spells": [{"id": "TrundleTrollSmash", "name": "Mampfen", "description": "Trundle beißt seinen <PERSON>, veru<PERSON><PERSON> so Schaden, verlangsamt kurz und entzieht diesem einen Teil seines Angriffsschadens.", "tooltip": "Trundles nächster Angriff verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> und <status>verlangsamt</status> den Gegner kurzzeitig um {{ slowamount*100 }}&nbsp;%. Dann erhält Trundle {{ sapdebuffduration }}&nbsp;Se<PERSON>nden lang <physicalDamage>{{ bonusad }}&nbsp;Angriffsschaden</physicalDamage> und der Gegner verliert <physicalDamage>{{ sappedad*-1 }}&nbsp;Angriffsschaden</physicalDamage> für dieselbe Dauer.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Angriffsschadenskalierung", "Angriffsschaden", "Angriffsschaden entfernt"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ adscaling*100.000000 }}&nbsp;% -> {{ adscalingnl*100.000000 }}&nbsp;%", "{{ bonusad }} -> {{ bonusadNL }}", "{{ sappedad*-1.000000 }} -> {{ sappedadnl*-1.000000 }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "TrundleTrollSmash.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "trundledesecrate", "name": "Eisige Domäne", "description": "Trundle verwandelt den Zielort in seine Domäne, wodurch er dort zusätzliches Angriffs- und Lauftempo sowie verstärkte Heilung aus allen Quellen erhält.", "tooltip": "Trundle lässt einen Bereich {{ e4 }}&nbsp;Sekunden lang gefrieren. In diesem Bereich erhält er <speed>{{ e1 }}&nbsp;% Lauftempo</speed>, <attackSpeed>{{ e2 }}&nbsp;Angriffstempo</attackSpeed> und {{ e3 }}&nbsp;% erhöhte Heilung.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "Angriffstempo", "Abklingzeit"], "effect": ["{{ effect1amount*100.000000 }}&nbsp;% -> {{ effect1amountnl*100.000000 }}&nbsp;%", "{{ effect2amount*100.000000 }}&nbsp;% -> {{ effect2amountnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [20, 28, 36, 44, 52], [30, 50, 70, 90, 110], [25, 25, 25, 25, 25], [8, 8, 8, 8, 8], [775, 775, 775, 775, 775], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/28/36/44/52", "30/50/70/90/110", "25", "8", "775", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "trundledesecrate.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TrundleCircle", "name": "Eissäule", "description": "Trundle erschafft eine Eissäule an der gewählten Position, die unpassierbar wird und nahe stehende gegnerische Einheiten verlangsamt.", "tooltip": "Trundle erschafft {{ e1 }}&nbsp;Sekunden lang eine Eissäule, die Gegner, die direkt über ihr stehen, kurzzeitig <status>zurückstößt</status> und Gegner in der Nähe um {{ e2 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Verlangsamung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [21, 19.5, 18, 16.5, 15], "cooldownBurn": "21/19.5/18/16.5/15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [34, 38, 42, 46, 50], [360, 360, 360, 360, 360], [225, 225, 225, 225, 225], [150, 150, 150, 150, 150], [225, 225, 225, 225, 225], [400, 400, 400, 400, 400], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "34/38/42/46/50", "360", "225", "150", "225", "400", "60", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "TrundleCircle.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TrundlePain", "name": "Unterwerfen", "description": "Trundle entzieht seinem Ziel sofort einen Prozentsatz an Leben, Rüstung und Magieresistenz. Während der nächsten 4&nbsp;Sekunden wird diesem nochmal die gleiche Menge entzogen.", "tooltip": "Trundle entzieht einem gegnerischen Champion die Lebenskraft, fügt ihm dabei <magicDamage>magischen <PERSON> in Höhe von {{ totalpercenthpdamage }} des maximalen Lebens</magicDamage> zu und raubt {{ actualdurationofdrainbuff }}&nbsp;Sekunden lang {{ armormrshred*100 }}&nbsp;% seiner <scaleArmor>Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lebensentzug", "Abklingzeit"], "effect": ["{{ percenthpdamage*100.000000 }}&nbsp;% -> {{ percenthpdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "TrundlePain.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tribut an den König", "description": "Wenn eine gegnerische Einheit in Trundles Nähe stirbt, heilt er sich in Höhe eines Prozentsatzes ihres maximalen Lebens.", "image": {"full": "Trundle_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}