{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rakan": {"id": "<PERSON><PERSON>", "key": "497", "name": "<PERSON><PERSON>", "title": "l'ammaliatore", "image": {"full": "Rakan.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "497000", "num": 0, "name": "default", "chromas": false}, {"id": "497001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "497002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "497003", "num": 3, "name": "Rakan <PERSON>G", "chromas": false}, {"id": "497004", "num": 4, "name": "Rakan iG", "chromas": false}, {"id": "497005", "num": 5, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "497009", "num": 9, "name": "Rakan del Bosco Antico", "chromas": true}, {"id": "497018", "num": 18, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "497027", "num": 27, "name": "<PERSON><PERSON> violato", "chromas": true}, {"id": "497036", "num": 36, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "497037", "num": 37, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "497038", "num": 38, "name": "<PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "497047", "num": 47, "name": "Rakan dell'Accademia di Battaglia", "chromas": true}], "lore": "Volubile e affascinante, <PERSON><PERSON> è un famigerato piantagrane vastayano, nonché il più grande danzatore guerriero mai vissuto della tribù di Lhotlan. Per gli umani dell'altopiano di Ionia, il suo nome è da tempo sinonimo di feste selvagge, musica anarchica e danze fuori controllo. Pochi sospettano che questo energico showman errante è anche il compagno della ribelle Xayah e che è dedito alla sua causa.", "blurb": "Volubile e affascinante, <PERSON><PERSON> è un famigerato piant<PERSON>rane vastayano, nonché il più grande danzatore guerriero mai vissuto della tribù di Lhotlan. Per gli umani dell'altopiano di Ionia, il suo nome è da tempo sinonimo di feste selvagge, musica anarchica...", "allytips": ["Rakan ha bisogno di alleati vicini per sfruttare al meglio i suoi strumenti.", "La velocità dello scatto di Rakan aumenta con la sua velocità di movimento. Usa la velocità extra per sorprendere i tuoi nemici!", "Il pericolo è divertente, se lo accetti."], "enemytips": ["Le abilità di movimento di Rakan mostrano la loro destinazione. Cerca di sfruttare questo dato a tuo vantaggio.", "I campioni con effetti di controllo veloci sono molto forti contro <PERSON>kan.", "Cogliere Rakan senza alleati intorno inibisce fortemente la sua mobilità. Cerca di prenderlo quando è solo."], "tags": ["Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 315, "mpperlevel": 50, "movespeed": 335, "armor": 30, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 300, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 8.75, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.635}, "spells": [{"id": "RakanQ", "name": "Piuma incantata", "description": "Scaglia una piuma magica che infligge danni magici. Colpire un campione o un mostro epico permette a Rakan di curare i suoi alleati.", "tooltip": "Rakan scaglia una piuma magica che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> al primo nemico colpito.<br /><br />Se la piuma colpisce un campione o un mostro epico della giungla, Rakan ripristina <healing>{{ totalheal }} salute</healing> a se stesso e agli alleati nelle vicinanze dopo {{ healdelay }} secondi o quando tocca un campione alleato.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RakanQ.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RakanW", "name": "Ingresso trionfale", "description": "Scatta verso una posizione, lanciando in aria i nemici vicini all'arrivo.", "tooltip": "<PERSON><PERSON> scatta e piroetta in aria, <status>lanciando</status> i nemici per {{ knockupduration }} secondo/i e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RakanW.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RakanE", "name": "Danza di guerra", "description": "Vola da un campione alleato, conferendogli uno scudo. P<PERSON>ò essere rilanciata gratuitamente entro un breve periodo.", "tooltip": "<PERSON>kan scatta verso un campione alleato, fornen<PERSON>li uno <shield>scudo da {{ totalshield }}</shield> per {{ e3 }} secondi.<br /><br /><PERSON><PERSON> <recast>rilanciare</recast> questa abilità una volta entro {{ e2 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute scudo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [3, 3, 3, 3, 3], [20, 18, 16, 14, 12], [40, 45, 50, 55, 60], [700, 700, 700, 700, 700], [1000, 1000, 1000, 1000, 1000], [1150, 1150, 1150, 1150, 1150], [1250, 1250, 1250, 1250, 1250], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "5", "3", "20/18/16/14/12", "40/45/50/55/60", "700", "1000", "1150", "1250", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "RakanE.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RakanR", "name": "Toccata e fuga", "description": "Ottiene velocità di movimento, ammaliando e infliggendo danni magici ai nemici toccati.", "tooltip": "<PERSON>kan ottiene <speed>{{ e5 }}% velocità di movimento</speed> per {{ e2 }} secondi. Ra<PERSON> infligge <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage> e <status>ammalia</status> i nemici per {{ e3 }} secondo/i la prima volta che li tocca. Il primo campione toccato conferisce a Rakan <speed>{{ e6 }}% velocità di movimento decrescente</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> amorosa", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 200, 300], [4, 4, 4], [1, 1.25, 1.5], [0.25, 0.25, 0.25], [75, 75, 75], [150, 150, 150], [150, 150, 150], [130, 110, 90], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "100/200/300", "4", "1/1.25/1.5", "0.25", "75", "150", "150", "130/110/90", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [150, 150, 150], "rangeBurn": "150", "image": {"full": "RakanR.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> p<PERSON>", "description": "<PERSON><PERSON> ottiene periodicamente uno scudo.", "image": {"full": "Rakan_P.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}