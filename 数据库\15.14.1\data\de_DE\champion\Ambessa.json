{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ambessa": {"id": "<PERSON><PERSON><PERSON>", "key": "799", "name": "<PERSON><PERSON><PERSON>", "title": "Matriarchin des Krieges", "image": {"full": "Ambessa.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "799000", "num": 0, "name": "default", "chromas": false}, {"id": "799001", "num": 1, "name": "Auserwählte des Wolfs-Ambessa", "chromas": true}], "lore": "<PERSON><PERSON>, die den Namen Medarda kennen, respektieren und fürchten Ambessa, die Anführerin der Familie. Als noxianische Generalin verkörpert sie eine tödliche Kombination aus rücksichtsloser Stärke und furchtloser Entschlossenheit im Kampf. Ihre Rolle als Matriarchin ist nicht anders: Sie muss die Medardas mit großer Gerissenheit führen und darf dabei keinen Raum für Versagen oder Mitleid lassen. Ambessa macht sich die gnadenlose Art der Wölfe zu eigen und wird alles tun, um das Erbe ihrer Familie zu schützen, selbst wenn es ihr die Liebe ihrer eigenen Kinder kostet.", "blurb": "<PERSON><PERSON>, die den Namen Medarda kennen, respektieren und fürchten Ambessa, die Anführerin der Familie. Als noxianische Generalin verkörpert sie eine tödliche Kombination aus rücksichtsloser Stärke und furchtloser Entschlossenheit im Kampf. Ihre Rolle als...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Energie", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 10}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 200, "mpperlevel": 0, "movespeed": 335, "armor": 35, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "AmbessaQ", "name": "Tollkühner Schwung/Wuchtschlag", "description": "Ambessa fegt ihre Zwillingsdrachenhunde in einem Halbkreis vor sich her und fügt <PERSON>, die von den Klingen getroffen werden, zusätzlichen Schaden zu. Wenn du einen Gegner triffst, wird die nächste Ausführung dieser Fähigkeit für kurze Zeit transformiert, sodass sie ihre Zwillingsdrachenhunde in einer Reihe vor sich niederwirft und dem ersten getroffenen Gegner zusätzlichen Schaden zufügt.", "tooltip": "<spellActive><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></spellActive>: Ambes<PERSON> schwingt ihre Klingen nach vorne und fügt Gegnern am Ende des Angriffs <physicalDamage>{{ calc_damage_1_max }} + {{ calc_damage_1_percent_max }} max. Lebensschaden</physicalDamage> zu. Alle anderen Gegner erleiden {{ calc_damage_1_min_ratio }} Schaden. Wenn du einen Gegner triffst, wird <spellActive>Wuchtschlag</spellActive> vorbereitet.<br /><br /><spellActive>Wuchtschlag</spellActive>: Ambessa schlägt ihre Klingen nieder und verursacht <physicalDamage>{{ calc_damage_2_max }} + {{ calc_damage_2_percent_max }} max. Lebensschaden </physicalDamage> gegen den ersten getroffenen Gegner. Alle anderen Gegner erleiden {{ calc_damage_2_min_ratio }} Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Tollkühner Schwung | Schaden", "Tollkühner Schwung | max. Lebensschaden", "Wuchtschlag | Schaden", "Wuchtschlag | max. Lebensschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage_1_base }} -> {{ damage_1_baseNL }}", "{{ damage_1_percent*100.000000 }}&nbsp;% -> {{ damage_1_percentnl*100.000000 }}&nbsp;%", "{{ damage_2_base }} -> {{ damage_2_baseNL }}", "{{ damage_1_percent*100.000000 }}&nbsp;% -> {{ damage_1_percentnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "AmbessaQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaW", "name": "Verstoßen", "description": "Ambessa erhält einen Schild, schützt sich kurzzeitig und schlägt dann auf den Boden, um <PERSON>n in der Nähe Schaden zuzufügen. Wenn sie Schaden geblockt hat, der nicht von Vasallen verursacht wurde, während sie sich geschützt hat, verursacht diese Fähigkeit erhöhten Schaden.", "tooltip": "Ambessa erhält für {{ shield_duration }}&nbsp;Sekunden einen <shield>{{ calc_shield }} Schild</shield> und bereitet sich für {{ buff_duration }}&nbsp;Sekunden vor. Dann schlägt sie auf den Boden und fügt <PERSON> in der Nähe <physicalDamage>{{ calc_damage_low }} normalen Schaden</physicalDamage> zu. Damit erhöht sich der Wert auf <physicalDamage>{{ calc_damage_high }} normalen Schaden</physicalDamage>, wenn sie sich auf Schaden eines gegnerischen Champions, eines großen Monsters oder eines Gebäudes vorbereitet hat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaE", "name": "Zerfetzen", "description": "Ambessa peitscht ihre Zwillingsdrachenhunde um sich herum und fügt <PERSON> in der Nähe Schaden zu und verlangsamt sie. Wenn du „Drachenhundstapfer“ mit dieser Fähigkeit auslöst, schlägt sie am Ende des Sprungs ein zweites Mal zu.", "tooltip": "Ambessa peitscht mit ihren Ketten und verursacht <physicalDamage>{{ calc_damage_flat }} normalen Schaden</physicalDamage> und <status>verlangsamt</status> Gegner um <status>{{ slow_amount*100 }}&nbsp;%</status>. Dieser Wert löst sich über {{ slow_duration }}&nbsp;Sekunden auf. Wenn du <spellName>Drachenhundstapfer</spellName> mit dieser Fähigkeit startest, löst dies einen zusätzlichen Schlag aus.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Angriffsschadenskalierung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage_flat_base }} -> {{ damage_flat_baseNL }}", "{{ adratio*100.000000 }}&nbsp;% -> {{ adrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaE.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaR", "name": "Öffentliche Hinrichtung", "description": "Ambessa teleportiert sich umgehend zu dem am weitesten entfernten gegnerischen Champion in einer von ihr gewählten Linie und unterdrückt ihn bei ihrer Ankunft. Dann rammt sie den Gegner in den Boden, wo er Schaden nimmt und betäubt wird.", "tooltip": "<spellPassive>Passiv</spellPassive>: Ambessa erhält <armorPen>{{ armor_penetration*100 }}&nbsp;% %i:scaleAPen% Rüstungsdurchdringung</armorPen> und ihre Fähigkeiten <healing>heilen sie um {{ calc_omnivamp }} des von ihr verursachten Schadens</healing>.<br /><br /><spellActive>Aktiv</spellActive>: Ambessa wird <attention>Unaufhaltsam</attention> und springt zu dem Gegner, der auf einer Linie am weitesten entfernt ist. Sie <status>unterdrückt</status> den Gegner für {{ suppress_duration }}&nbsp;Sekunden und rammt ihn in den Boden. Damit verursacht sie <physicalDamage>{{ calc_damage }} normalen Schaden</physicalDamage> und <status>betäubt</status> ihn {{ stun_duration }}&nbsp;Sekunden lang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Rüstungsdurchdringung", "Heilung", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ armor_penetration*100.000000 }}&nbsp;% -> {{ armor_penetrationnl*100.000000 }}&nbsp;%", "{{ omnivamp*100.000000 }}&nbsp;% -> {{ omnivampnl*100.000000 }}&nbsp;%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AmbessaR.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Drachenhundstapfer", "description": "Wenn du während des Wirkens einer Fähigkeit einen Angriffs- oder Bewegungsbefehl eingibst, stürmt Ambessa nach dem Wirken der Fähigkeit ein Stück nach vorne, wodurch ihr nächster Angriff Bonusreichweite, Schaden und Angriffsgeschwindigkeit erhält und Energie zurückerstattet wird.", "image": {"full": "Icon_Ambessa_Passive.Domina.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}