{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Volibear": {"id": "Volibear", "key": "106", "name": "볼리베어", "title": "무자비한 폭풍", "image": {"full": "Volibear.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "106000", "num": 0, "name": "default", "chromas": false}, {"id": "106001", "num": 1, "name": "천둥 군주 볼리베어", "chromas": false}, {"id": "106002", "num": 2, "name": "북극 폭풍 볼리베어", "chromas": false}, {"id": "106003", "num": 3, "name": "룬 수호자 볼리베어", "chromas": false}, {"id": "106004", "num": 4, "name": "볼리베어 경위", "chromas": false}, {"id": "106005", "num": 5, "name": "프로레슬러 볼리베어", "chromas": false}, {"id": "106006", "num": 6, "name": "천 번 찔린 곰", "chromas": false}, {"id": "106007", "num": 7, "name": "이중용 볼리베어", "chromas": true}, {"id": "106009", "num": 9, "name": "프레스티지 이중용 볼리베어", "chromas": false}, {"id": "106019", "num": 19, "name": "먹그림자 볼리베어", "chromas": false}], "lore": "추종자들에게 볼리베어는 여전히 폭풍 그 자체이다. 강력하고 야만적이며 고집스러울 정도로 단호한 그는 프렐요드의 동토에 필멸자들이 나타나기 전부터 존재했다. 다른 반신들과 함께 일구어낸 그 땅을 무척이나 아끼는 볼리베어는 나약하기 그지없는 문명의 발달을 극도로 혐오했고, 결국 거칠고 폭력적이었던 옛 전통을 되찾기 위해 싸움을 시작한다. 그 앞을 가로막는 자는 누구든 볼리베어의 이빨과 발톱, 천둥의 위력을 맛보게 될 것이다.", "blurb": "추종자들에게 볼리베어는 여전히 폭풍 그 자체이다. 강력하고 야만적이며 고집스러울 정도로 단호한 그는 프렐요드의 동토에 필멸자들이 나타나기 전부터 존재했다. 다른 반신들과 함께 일구어낸 그 땅을 무척이나 아끼는 볼리베어는 나약하기 그지없는 문명의 발달을 극도로 혐오했고, 결국 거칠고 폭력적이었던 옛 전통을 되찾기 위해 싸움을 시작한다. 그 앞을 가로막는 자는 누구든 볼리베어의 이빨과 발톱, 천둥의 위력을 맛보게 될 것이다.", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 7, "defense": 7, "magic": 4, "difficulty": 3}, "stats": {"hp": 650, "hpperlevel": 104, "mp": 350, "mpperlevel": 70, "movespeed": 340, "armor": 31, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.75, "mpregen": 6.25, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "VolibearQ", "name": "번개 강타", "description": "볼리베어가 적을 향해 이동할 때 이동 속도가 증가하며 처음으로 기본 공격하는 대상을 <status>기절</status>시키고 피해를 입힙니다.", "tooltip": "{{ duration }}초 동안 볼리베어의 <speed>이동 속도가 {{ minspeedcalc }}</speed> 증가합니다. 적 챔피언을 향해 이동 시에는 이동 속도 증가량이 두 배로 늘어 <speed>{{ maxspeedcalc }}</speed>까지 증가합니다. 스킬이 활성화된 동안 다음 기본 공격 시 <physicalDamage>{{ calculateddamage }}의 물리 피해</physicalDamage>를 입히고 {{ stunduration }}초 동안 <status>기절</status>시킵니다.<br /><br />볼리베어가 대상을 <status>기절</status>시키기 전에 <status>이동 불가</status> 효과를 받으면 스킬이 끝나지만, 볼리베어가 분노하여 재사용 대기시간이 초기화됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "이동 속도", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ minspeed*100.000000 }}% -> {{ minspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VolibearQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VolibearW", "name": "광란의 상처", "description": "볼리베어가 적에게 피해를 입혀 적중 시 효과를 적용하고 표식을 남깁니다. 표식을 남긴 대상에게 다시 이 스킬을 사용하면 추가 피해를 입히고 체력을 회복합니다.", "tooltip": "볼리베어가 적을 공격하여 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ markduration }}초 동안 표식을 남깁니다.<br /><br />표식이 남은 대상에게 이 스킬을 사용하면 피해가 <physicalDamage>{{ empowereddamage }}</physicalDamage>까지 증가하며 볼리베어가 <healing>{{ baseheal }}+잃은 체력의 {{ percentmissinghealthhealingratio }}만큼 체력</healing>을 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "기본 회복량", "잃은 체력 비례", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ healpercent*100.000000 }}% -> {{ healpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "VolibearW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VolibearE", "name": "천공 분열", "description": "볼리베어가 지정한 위치에 번개를 소환해 적에게 피해를 입히고 둔화시킵니다. 볼리베어가 폭발 반경 안에 있으면 보호막을 얻습니다.", "tooltip": "볼리베어가 뇌운을 소환해 번개를 내리쳐 <magicDamage>{{ totaldamagetooltip }}+대상 최대 체력의 {{ percentdamage*100 }}%에 해당하는 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>시킵니다.<br /><br />볼리베어가 폭발 지역 안에 있으면 {{ shieldduration }}초 동안 <shield>{{ shieldapratiotooltip }}+최대 체력의 {{ shieldamount*100 }}%에 해당하는 보호막</shield>을 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "비례 피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percentdamage*100.000000 }}% -> {{ percentdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VolibearE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VolibearR", "name": "폭풍을 부르는 자", "description": "볼리베어가 지정한 위치로 도약하여 아래에 있는 적을 둔화시키고 피해를 입히며 추가 체력을 얻습니다. 볼리베어가 착지한 곳 근처에 있는 포탑은 일시적으로 비활성화됩니다.", "tooltip": "볼리베어가 변신 후 지정한 위치로 도약합니다. {{ transformduration }}초 동안 <healing>체력이 {{ healthamount }}</healing>, 공격 사거리가 {{ bonusattackrange }} 증가합니다.<br /><br />볼리베어가 착지 시 땅에 균열이 생겨 근처 적 포탑이 {{ towerdisableduration }}초 동안 <status>비활성화</status>되며 <physicalDamage>{{ towerdamagetooltip }}의 물리 피해</physicalDamage>를 입습니다. 근처 적들은 {{ slowamount*100 }}% <status>둔화</status>했다가 1초에 걸쳐 원래대로 돌아옵니다. 볼리베어 바로 아래에 있는 적들은 <physicalDamage>{{ sweetspotdamagetooltip }}의 물리 피해</physicalDamage>를 입습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "추가 체력", "포탑 비활성화 지속시간", "재사용 대기시간"], "effect": ["{{ sweetspotdamage }} -> {{ sweetspotdamageNL }}", "{{ healthamount }} -> {{ healthamountNL }}", "{{ towerdisableduration }} -> {{ towerdisabledurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 135, 110], "cooldownBurn": "160/135/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [75, 115, 155], [9, 9, 9], [12, 12, 12], [8, 8, 8], [0.08, 0.08, 0.08], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "75/115/155", "9", "12", "8", "0.08", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "VolibearR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "무자비한 폭풍", "description": "볼리베어가 기본 공격을 하거나 스킬을 사용하면 공격 속도가 증가하며 최대 중첩 상태에서 공격 시 주변 적들에게 추가 마법 피해를 입힙니다.", "image": {"full": "Volibear_Icon_P.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}