{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "Jax", "title": "Grandmaster at Arms", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "The Mighty Jax", "chromas": false}, {"id": "24002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX Jax", "chromas": false}, {"id": "24005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24006", "num": 6, "name": "Temple Jax", "chromas": false}, {"id": "24007", "num": 7, "name": "Nemesis Jax", "chromas": true}, {"id": "24008", "num": 8, "name": "SKT T1 Jax", "chromas": false}, {"id": "24012", "num": 12, "name": "<PERSON>", "chromas": false}, {"id": "24013", "num": 13, "name": "God Staff Jax", "chromas": false}, {"id": "24014", "num": 14, "name": "Mecha Kingdoms Jax", "chromas": true}, {"id": "24020", "num": 20, "name": "Conquer<PERSON>", "chromas": false}, {"id": "24021", "num": 21, "name": "Prestige Conqueror Jax", "chromas": false}, {"id": "24022", "num": 22, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "24032", "num": 32, "name": "Neo PAX Jax", "chromas": false}, {"id": "24033", "num": 33, "name": "PROJECT: Jax", "chromas": true}], "lore": "Unmatched in both his skill with unique armaments and his biting sarcasm, <PERSON> is the last known weapons master of Icathia. After his homeland was laid low by its own hubris in unleashing the Void, <PERSON> and his kind vowed to protect what little remained. As magic now rises in the world, this slumbering threat stirs once more, and <PERSON> roams Valoran, wielding the last light of Icathia and testing all warriors he meets to see if any are strong enough to stand beside him...", "blurb": "Unmatched in both his skill with unique armaments and his biting sarcasm, <PERSON> is the last known weapons master of Icathia. After his homeland was laid low by its own hubris in unleashing the Void, <PERSON> and his kind vowed to protect what little remained...", "allytips": ["Jax can Leap Strike to friendly units, including wards. You can use them to plan your escape.", "Jax benefits greatly from items that have both Ability Power and Attack Damage such as Guinsoo's Rageblade and Hextech Gunblade."], "enemytips": ["Try to engage him in short bursts instead of going toe-to-toe with him. Preventing him from attacking consecutively drastically lowers his damage output.", "<PERSON> can dodge all incoming attacks for a very short amount of time and stuns enemies in melee range when it ends. Wait to strike him until after his dodge is finished."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "Leap Strike", "description": "<PERSON> leaps toward a unit. If they are an enemy, he strikes them with his weapon.", "tooltip": "Jax leaps to a friendly or enemy unit or ward, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> if it is an enemy.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxW", "name": "Empower", "description": "<PERSON> charges his weapon with energy, causing his next attack to deal additional damage.", "tooltip": "<PERSON> charges his weapon with energy, causing his next Attack or <spellName>Leap Strike</spellName> to deal an additional <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxE", "name": "Counter Strike", "description": "<PERSON>'s combat prowess allows him to dodge all incoming attacks for a short duration and then quickly counterattack, stunning all surrounding enemies.", "tooltip": "<PERSON> enters a defensive stance for up to {{ dodgeduration }} seconds, dodging incoming Attacks and taking {{ aoedamagereduction }}% less damage from area of effect Abilities. After {{ dodgeduration }} seconds or <recast>Recasting</recast>, Jax deals <magicDamage>{{ totaldamage }} + {{ percenthealthdamage }}% max Health magic damage</magicDamage> and <status>Stuns</status> nearby enemies for {{ stunduration }} second. <br /><br />The damage is increased by {{ percentincreasedperdodge*100 }}% per Attack dodged, up to a maximum of <magicDamage>{{ maxdamage }} + {{ maxpercenthealthdamage }}% max Health</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxR", "name": "<PERSON><PERSON>-at-<PERSON>", "description": "Every third consecutive attack deals additional Magic Damage. Additionally, <PERSON> can activate this ability to deal damage around himself and strengthen his resolve, increasing his Armor and Magic Resist for a short duration.", "tooltip": "<spellPassive>Passive:</spellPassive> Every third Attack within {{ passivefallofftime }} seconds deals an additional <magicDamage>{{ onhitdamage }} magic damage</magicDamage>.<br /><br /><spellActive>Active:</spellActive> <PERSON> slams his lantern down, dealing <magicDamage>{{ swingdamagetotal }} magic damage</magicDamage> to nearby enemies. If he hits a champion he gains <scaleArmor>{{ basearmor }} Armor</scaleArmor> and <scaleMR>{{ basemr }} Magic Resist</scaleMR> plus <scaleArmor>{{ bonusarmor }} Armor</scaleArmor> and <scaleMR>{{ bonusmr }} Magic Resist</scaleMR> per additional champion hit for {{ duration }} seconds. During this time every second Attack deals additional <magicDamage>magic damage</magicDamage> instead of every third.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Passive Damage", "Active Damage", "Base Armor", "Base Magic Resist", "Armor per Additional Champion", "Magic Resist per Additional Champion", "Cooldown"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ swingdamagebase }} -> {{ swingdamagebaseNL }}", "{{ baseresists }} -> {{ baseresistsNL }}", "{{ baseresists*0.600000 }} -> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }} -> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }} -> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Relentless Assault", "description": "<PERSON>'s consecutive basic attacks continuously increase his Attack Speed.", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}