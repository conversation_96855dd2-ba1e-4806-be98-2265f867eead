{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "Dr. <PERSON>", "title": "il folle di Zaun", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "Dr. <PERSON><PERSON>", "chromas": false}, {"id": "36002", "num": 2, "name": "Mr. <PERSON><PERSON>", "chromas": false}, {"id": "36003", "num": 3, "name": "Corporate Mundo", "chromas": true}, {"id": "36004", "num": 4, "name": "Mundo Mundo", "chromas": false}, {"id": "36005", "num": 5, "name": "Mundo Carnefice", "chromas": false}, {"id": "36006", "num": 6, "name": "Mundo Furia Cieca", "chromas": false}, {"id": "36007", "num": 7, "name": "TPA Mundo", "chromas": false}, {"id": "36008", "num": 8, "name": "Mundo Festa in Piscina", "chromas": false}, {"id": "36009", "num": 9, "name": "Mundo El Macho", "chromas": false}, {"id": "36010", "num": 10, "name": "Mundo P<PERSON>", "chromas": true}, {"id": "36021", "num": 21, "name": "Dr. <PERSON><PERSON> della strada", "chromas": true}], "lore": "Completamente folle, tragicamente omicida e orribilmente viola, Dr. <PERSON><PERSON> è il motivo per cui molti cittadini di Zaun non escono di casa durante le notti più buie. Benché ora si sia autoproclamato dottore, un tempo era un paziente del più famigerato manicomio di Zaun. Dopo aver ''curato'' tutto il suo staff, Dr. <PERSON><PERSON> ha stabilito la sua attività nei reparti vuoti dove un tempo veniva trattato e ha iniziato a imitare le procedure molto poco etiche a cui lui stesso è stato spesso sottoposto. Con un intero arsenale di medicinali a disposizione e nessuna conoscenza medica, ora diventa più mostruoso dopo ogni iniezione, terrorizzando i ''pazienti'' inermi che finiscono nelle vicinanze del suo laboratorio.", "blurb": "Completamente folle, tragicamente omicida e orribilmente viola, Dr<PERSON> <PERSON><PERSON> è il motivo per cui molti cittadini di Zaun non escono di casa durante le notti più buie. Benché ora si sia autoproclamato dottore, un tempo era un paziente del più famigerato...", "allytips": ["Un Sadismo lanciato con un certo tempismo può spingere i campioni nemici ad attaccarti anche se mancano dei danni per poterti finire.", "Corazza spirituale aumenta la guarigione della tua abilità suprema e abbassa la ricarica di tutte le tue abilità.", "Le mannaie sono un potente strumento per uccidere i mostri neutrali. Invece che tornare alla base, uccidi i mostri neutrali finché la tua abilità suprema non ti può guarire."], "enemytips": ["Cerca di coordinare le abilità con maggiori danni insieme ai tuoi alleati verso Dr. Mundo quando utilizza la sua abilità suprema, ma se non riuscirete a ucciderlo velocemente con la raffica iniziale, guarirà dai danni subiti.", "Lancia Ustione quando Dr. Mundo usa Sadismo per negare gran parte della sua cura."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "Segaossa infetta", "description": "Dr. <PERSON><PERSON> lancia una segaossa infetta, che infligge danni al primo nemico colpito in base alla sua salute attuale, rallentandolo.", "tooltip": "Dr. <PERSON><PERSON> scaglia la sua segaossa, infliggendo <magicDamage>{{ currenthealthdamage*100 }}% della salute attuale in danni magici</magicDamage> al primo nemico colpito e <status>rallentandolo</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi.<br /><br />Se la segaossa colpisce un campione o un mostro, Dr. <PERSON>ndo recupera <healing>{{ healthrestoreonhitchampionmonster }} salute</healing>. Se colpisce un non campione o un non mostro, Dr. <PERSON>ndo recupera invece <healing>{{ healthrestoreonhitminion }} salute</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> salute at<PERSON>ali", "<PERSON><PERSON> minimi", "Limite danni ai mostri", "<PERSON><PERSON> salute"], "effect": ["{{ currenthealthdamage*100.000000 }}% -> {{ currenthealthdamagenl*100.000000 }}%", "{{ minimumdamage }} -> {{ minimumdamageNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}", "{{ healthcost }} -> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " salute", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ healthcost }} salute"}, {"id": "DrMundoW", "name": "Defibrilla<PERSON>", "description": "Dr. <PERSON><PERSON> si fulmina con una scossa elettrica, infliggendo danni persistenti ai nemici vicini e accumulando una parte dei danni subiti. Alla fine della durata o al rilancio, Dr. <PERSON><PERSON> infligge una raffica di danni ai nemici vicini. Se colpisce un nemico, guarisce di una percentuale dei danni accumulati.", "tooltip": "Dr. Mundo carica un defibrillatore, infliggendo <magicDamage>{{ damagepertick*4 }} danni magici</magicDamage> al secondo per un massimo di {{ duration }} secondi ai nemici nelle vicinanze. Inoltre, conserva come salute grigia {{ grayhealthstorageinitial }} danni subiti per i primi {{ grayhealthinitialduration }} secondi e il {{ grayhealthstorage*100 }}% per il resto della durata e può <recast>rilanciare</recast>.<br /><br /><recast>Rilancio:</recast> il defibrillatore esplode, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici nelle vicinanze. Se colpisce almeno un campione, Dr. Mundo recupera il <healing>{{ grayhealthbigmod*100 }}% della salute grigia</healing>, altrimenti ne recupera il <healing>{{ grayhealthsmallmod*100 }}%</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni per tick", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damagepertick }} -> {{ damagepertickNL }}", "{{ recastbasedamage }} -> {{ recastbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% salute attuale", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ currenthealthcost*100 }}% salute attuale"}, {"id": "DrMundoE", "name": "Trauma cranico", "description": "Passiva - Dr. <PERSON><PERSON> ottiene attacco fisico bonus, che aumenta in base alla sua salute massima.<br><br>Attiva - Dr. <PERSON><PERSON> colpisce un nemico con la sua borsa ''medica'', infliggendo danni fisici in base alla sua salute mancante. Se il bersaglio muore, viene spinto via, infliggendo danni ai nemici che attraversa.", "tooltip": "<spellPassive>Passiva:</spellPassive> Dr. <PERSON><PERSON> ottiene <physicalDamage>{{ passivebonusad }} attacco fisico</physicalDamage>.<br /><br /><spellActive>Attiva:</spellActive> Dr. Mundo rotea violentemente la sua borsa ''medica'' e il suo prossimo attacco infligge ulteriori <physicalDamage>{{ additionaldamage }} danni fisici</physicalDamage>, che aumentano fino a {{ maxdamageamptooltip }} in base alla sua salute mancante. Se il nemico viene ucciso, Mundo lo spinge via, infliggendo <physicalDamage>{{ additionaldamage }} danni fisici</physicalDamage> ai nemici che attraversa.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base", "<PERSON><PERSON> salute", "Salute in attacco fisico"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ flathealthcost }} -> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}% -> {{ healthtoadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " salute", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ flathealthcost }} salute"}, {"id": "DrMundoR", "name": "Dosaggio massimo", "description": "Dr. <PERSON><PERSON> si inietta degli agenti chimici, guarendo istant<PERSON>e di una percentuale della sua salute mancante. Poi ottiene velocità di movimento e rigenera una parte della sua salute massima in un lungo periodo.", "tooltip": "Dr. <PERSON>ndo si inietta sostanze chimiche, otten<PERSON>o il <healing>{{ missinghealthheal*100 }}% della sua salute mancante come salute massima</healing>, il <speed>{{ speedboostamount*100 }}% della velocità di movimento</speed> e ripristinando il <healing>{{ maxhealthhot*100 }}% della salute massima</healing> per {{ duration }} secondi.<br /><br />Al livello 3, entrambi gli effetti curativi aumentano di un ulteriore {{ bonuspernearbychampion*100 }}% per ogni campione nemico nelle vicinanze.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute bonus", "Velocità di movimento", "% salute massima"], "effect": ["{{ missinghealthheal*100.000000 }}% -> {{ missinghealthhealnl*100.000000 }}%", "{{ speedboostamount*100.000000 }}% -> {{ speedboostamountnl*100.000000 }}%", "{{ maxhealthhot*100.000000 }}% -> {{ maxhealthhotnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Va dove vuole", "description": "Dr. <PERSON><PERSON> resiste al primo effetto di immobilizzazione che lo colpisce, perdendo invece salute e lasciando cadere un barile chimico nelle vicinanze. Dr. <PERSON><PERSON> pu<PERSON> raccoglierlo camminandoci sopra, ripristinando salute e riducendo la ricarica di questa passiva.<br><br>Dr. <PERSON><PERSON> ha anche la rigenerazione salute sensibilmente aumentata.<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}