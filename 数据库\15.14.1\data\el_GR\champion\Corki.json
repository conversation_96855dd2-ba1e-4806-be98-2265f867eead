{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Corki": {"id": "<PERSON><PERSON>", "key": "42", "name": "Κόρκι", "title": "ο Τολμηρός Βομβιστής", "image": {"full": "Corki.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "42000", "num": 0, "name": "default", "chromas": false}, {"id": "42001", "num": 1, "name": "Κόρκι ΑΤΙΑ", "chromas": false}, {"id": "42002", "num": 2, "name": "Κόρκι με Μπόμπσλεϊ", "chromas": false}, {"id": "42003", "num": 3, "name": "Κόρκι Κόκκινος <PERSON>ν<PERSON>", "chromas": false}, {"id": "42004", "num": 4, "name": "Κόρκι σε «Φτιαγμένο»", "chromas": false}, {"id": "42005", "num": 5, "name": "Κόρκι Καβάλα στον Ουρφ", "chromas": false}, {"id": "42006", "num": 6, "name": "Κόρκι Φτερωτός Δράκος", "chromas": true}, {"id": "42007", "num": 7, "name": "Fnatic <PERSON>", "chromas": false}, {"id": "42008", "num": 8, "name": "Arcade Κόρκι", "chromas": true}, {"id": "42018", "num": 18, "name": "Κόργκι Κόρκι", "chromas": true}, {"id": "42026", "num": 26, "name": "Αστρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Ο Κόρκι είναι ένας πιλότος Γιορντλ που αγαπάει δύο πράγματα περισσότερο από οτιδήποτε άλλο στη ζωή: τις πτήσεις και το εντυπω<PERSON><PERSON><PERSON><PERSON><PERSON> μουστάκι του... αν και κανείς δεν είναι σίγουρος ποιο προτιμά από τα δύο. Όταν έφυγε από την Πόλη του Μπαντλ, εγκαταστάθηκε στο Πιλτόβερ και ερωτεύτηκε τις θαυμαστές μηχανές που βρήκε εκεί. Αφοσιώθηκε στη δημιουργία ιπτάμενων μηχανών και ίδρυσε τις Ιπτάμενες Οχιές, μια επίλεκτη αεροπορική ομάδα κρούσης. Πάντα ψύχραιμος υπό πίεση, ο Κόρκι περιπολεί τους ουρανούς της νέας του πατρίδας και λύνει όλα του τα προβλήματα με μια γερή Ομοβροντία Πυραύλων.", "blurb": "Ο Κόρκι είναι ένας πιλότος Γιορντλ που αγαπάει δύο πράγματα περισσότερο από οτιδήποτε άλλο στη ζωή: τις πτήσεις και το εντυπωσι<PERSON><PERSON><PERSON> μουστάκι του... αν και κανείς δεν είναι σίγουρος ποιο προτιμά από τα δύο. Όταν έφυγε από την Πόλη του Μπαντλ...", "allytips": ["Η Βόμβα Φωσφόρου μπορεί να αποκαλύψει εχθρούς που ίσως κρύβονται σε κοντινούς θάμνους.", "Η Βαλκυρία μπορεί να χρησιμοποιηθεί και αμυντικά, θα σας βοηθήσει σε μια γρήγορη απόδραση.", "Ο Κόρκι συνεχίζει να επιτίθεται όσο χρησιμοποιεί το Μυδραλιοβόλο. Το μυστικό της τέλειας χρήσης του Κόρκι είναι στην εκμετάλλευση του Μυδραλιοβόλου."], "enemytips": ["Προσοχή στην Ομοβροντία Πυραύλων του Κόρκι. Προκαλεί ζημιά διασποράς, έτσι ίσως χτυπηθείτε ακόμα κι αν κρύβεστε πίσω από υπηρέτες.", "Ο Κόρκι είναι ευάλωτος αφού χρησιμοποιήσει τη Βαλκυρία ή το Συστημένο, οπότε αν μπει στη μάχη με αυτές τις ικανότητες εστιάστε τη ζημιά σας επάνω του."], "tags": ["Marksman", "Mage"], "partype": "Μάνα", "info": {"attack": 8, "defense": 3, "magic": 6, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 100, "mp": 350, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2, "attackspeedperlevel": 2.8, "attackspeed": 0.644}, "spells": [{"id": "PhosphorusBomb", "name": "Βόμβα Φωσφόρου", "description": "Ο Κόρκι εκτοξεύει μια βόμβα λάμψης σε μια στοχευμένη τοποθεσία και προκαλεί Μαγική Ζημιά στους εχθρούς που βρίσκονται στην περιοχή. Επιπλέον, αυτή η επίθεση αποκαλύπτει τις μονάδες που βρίσκονται στην περιοχή για λίγο.", "tooltip": "Ο Κόρκι πετάει μια βόμβα που προκαλεί <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage>. Η περιοχή και οι Ήρωες που πετυχαίνει αποκαλύπτονται για {{ revealduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "PhosphorusBomb.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CarpetBomb", "name": "Βαλκυρία", "description": "Ο Κόρκι διανύει πετώντας μια μικρή απόσταση, ρίχνοντας πίσω του βόμβες που δημιουργούν ένα φλεγόμενο μονο<PERSON>άτι, το οποίο προκαλεί ζημιά στους αντίπαλους που βρίσκονται επάνω του.", "tooltip": "Ο Κόρκι πετάει από πάνω και καίει ένα μονοπάτι, κάνοντάς το να φλέγεται για {{ trailduration }} δευτ. Οι εχθροί που στέκονται μέσα στη φωτιά δέχονται έως <magicDamage>{{ maximumdamage }} Μαγική Ζημιά</magicDamage> κατά τη διάρκεια.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά με την Πάροδο του Χρόνου", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "CarpetBomb.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GGun", "name": "Μυδραλιοβόλο", "description": "Το μυδραλιοβόλο του Κόρκι ρίχνει με μεγάλη ταχύτητα σε έναν κώνο μπροστά του, προ<PERSON><PERSON>λ<PERSON>ντα<PERSON> ζημιά και μειώνοντας τη Θωράκιση και την Αντίσταση Μαγείας του εχθρού.", "tooltip": "Ο Κόρκι ρίχνει μπροστά του με ένα μυδραλιοβόλο, προκαλώντας <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> μέσα σε {{ sprayduration }} δευτ. και αφαιρώντας έως και <scaleArmor>{{ shredmax*-1 }} Θωράκιση</scaleArmor> και <scaleMR>Αντίσταση Μαγείας</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Μείωση Άμυνας", "Κόστος @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredmax*-1.000000 }} -> {{ shredmaxnl*-1.000000 }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GGun.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissileBarrage", "name": "Ομοβροντία Πυραύλων", "description": "Ο Κόρκι εξαπολύει ένα βλήμα προς τη θέση του στόχου, το οποίο ανατινάζεται κατά την πρόσκρουση και προκαλεί ζημιά στους εχθρούς που βρίσκονται στην περιοχή. Ο Κόρκι αποθηκεύει βλήματα με την πάροδο του χρόνου μέχρι να φτάσει το μέγιστο όριο. Κάθε 3ος πύραυλος που εξαπολύει θα είναι Τεράστιος και θα προκαλεί επιπλέον ζημιά.", "tooltip": "Ο Κόρκι εκτοξεύει ένα βλήμα που ανατινάζεται μόλις χτυπήσει τον πρώτο εχθρό, προκαλώντας <physicalDamage>{{ rsmallmissiledamage }} Σωματική Ζημιά</physicalDamage> στους γύρω εχθρούς. Κάθε τρίτο βλήμα προκαλεί <physicalDamage>{{ rbigmissiledamage }} Σωματική Ζημιά</physicalDamage>.<br /><br />Αυτή η ικανότητα έχει έως {{ maxammotooltip }} φορτίσεις. Οι βασικές επιθέσεις ενάντια σε Ήρωες μειώνουν τον χρόνο μεταξύ των φορτίσεων κατά <attention>{{ attackrefund }}</attention> δευτ. κατά το χτύπημα.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [2, 2, 2], "cooldownBurn": "2", "cost": [35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "4", "range": [1225, 1225, 1225], "rangeBurn": "1225", "image": {"full": "MissileBarrage.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Μαγοτεχνικά Πυρομαχικά", "description": "Ένα ποσοστό της ζημιάς των βασικών επιθέσεων του Κόρκι προκαλείται ως μπόνους <trueDamage>Πραγματική Ζημιά</trueDamage>.", "image": {"full": "Corki_RapidReload.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}