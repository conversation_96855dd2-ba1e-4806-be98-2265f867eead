{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Γιόνε", "title": "ο Αλησμόνητος", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "Άνθος του Πνεύματος Γιόνε", "chromas": true}, {"id": "777010", "num": 10, "name": "Γιόνε της <PERSON>αδημ<PERSON>ας <PERSON>ς", "chromas": true}, {"id": "777019", "num": 19, "name": "Γι<PERSON><PERSON><PERSON>ομιστής της Αυγής", "chromas": true}, {"id": "777026", "num": 26, "name": "Γιόνε Τραγουδιού του Ωκεανού", "chromas": true}, {"id": "777035", "num": 35, "name": "Γιόνε της <PERSON><PERSON>ινής <PERSON>λ<PERSON>νης", "chromas": true}, {"id": "777045", "num": 45, "name": "HEARTSTEEL Γιόνε", "chromas": true}, {"id": "777046", "num": 46, "name": "HEARTSTEEL Γιόνε - Έκδοση Κύρους", "chromas": false}, {"id": "777055", "num": 55, "name": "Γιόνε της Άγριας Δύσης", "chromas": true}, {"id": "777058", "num": 58, "name": "Ειρηνοπ<PERSON>ι<PERSON>ς Γιόνε της Άγριας Δύσης", "chromas": false}, {"id": "777065", "num": 65, "name": "Μασ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> της Δικαιο<PERSON>ύνης Γ<PERSON>ε", "chromas": false}], "lore": "Όταν ήταν ζωντ<PERSON>ν<PERSON><PERSON>, ήταν ο Γιόνε, ετεροθαλής αδερφός του Υασούο και φημισμένος μαθητής της σχολής ξιφομαχίας του χωριού του. Όταν η λεπίδα του αδερφού του έκοψε το νήμα της ζωής του, βρέθηκε αντιμέτωπος με ένα αζακάνα, μια μοχθηρή οντότητα από το βασίλειο των πνευμάτων και αναγκάστηκε να το σκοτώσει με το ίδιο του το σπαθί. Τώρα, καταραμένος να φορά τη μάσκα του δαίμονα στο πρόσωπό του, ο Γιόνε κυνηγά ακούραστα όλα τα αζακάνα για να καταλάβει τι του έχει συμβεί.", "blurb": "Όταν ήταν ζων<PERSON><PERSON><PERSON><PERSON><PERSON>, ήταν ο Γιόνε, ετεροθαλής αδερφός του Υασούο και φημισμένος μαθητής της σχολής ξιφομαχίας του χωριού του. Όταν η λεπίδα του αδερφού του έκοψε το νήμα της ζωής του, βρέθηκε αντιμέτωπος με ένα αζακάνα, μια μοχθηρή οντότητα από το...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Ροή", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "Θνητό Ατσ<PERSON>λι", "description": "Χτυ<PERSON><PERSON> προς τα εμπρός και προκαλεί ζημιά σε όλους τους εχθρούς σε ευθεία γραμμή.<br><br><PERSON><PERSON><PERSON><PERSON> το χτύπημα, δίνει μια στοίβα Επερχόμενης Θύελλας για λίγα δευτερόλεπτα. Στις 2 στοίβες, το Θνητ<PERSON> Ατσάλι του Γιόνε τον κάνει να εφορμά προς τα εμπρός με ένα ισχυρό ρεύμα αέρα που <status>εκτοξεύει τους εχθρούς στον αέρα</status>.", "tooltip": "Ο Γιόνε χτυπά με το ξίφος του προς τα εμπρός και προκαλεί <physicalDamage>{{ qdamage }} Σωματική Ζημιά</physicalDamage>.<br /><br />Κατά το χτύπημα, δίνει μια στοίβα για {{ buffduration }} δευτ. Στις 2 στοίβες, η ικανότητα κάνει τον Γιόνε να εφορμά προς τα εμπρός με ένα ρεύμα αέρα που <status>Εκτοξεύει στον Αέρα</status> τους εχθρούς για {{ q3knockupduration }} δευτ. και προκαλεί <physicalDamage>{{ qdamage }} Σωματική Ζημιά</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική ζημιά"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "YoneW", "name": "Πνευματική Τομή", "description": "Χτυπ<PERSON> προς τα εμπρός και προκαλεί ζημιά σε όλους τους εχθρούς σε μια κωνική περιοχή μπροστά του. Δίνει στον Γιόνε μια ασπίδα, η ισχύς της οποίας αυξάνεται ανάλογα με τον αριθμό των Ηρώων που πέτυχε κατά το χτύπημα.<br><br>Ο Χρόνος Επαναφόρτισης και ο χρόνος χρήσης της Πνευματικής Τομής αυξάνονται αναλογικά με την Ταχύτητα Επίθεσης.", "tooltip": "Ο Γιόνε πραγματοποιεί μια τομή προς τα εμπρός και προκαλεί Σωματική Ζημιά ίση με το <physicalDamage>{{ basedamage*0.5 }} + {{ maxhealthdamage*50 }}% της μέγιστης Ζωής</physicalDamage>, καθώς και Μαγική Ζημιά ίση με το <magicDamage>{{ basedamage*0.5 }} + {{ maxhealthdamage*50 }}% της μέγιστης Ζωής</magicDamage>.<br /><br />Αν ο Γιόνε πετύχει εχθρούς, αποκτά <shield>{{ wshield }} ασπίδα</shield> για {{ shieldduration }} δευτ. Το ποσό της <shield>ασπίδας</shield> αυξάνεται για κάθε Ήρωα που χτυπά. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική ζημιά", "Συνολική Ζημιά επί ποσοστού Μέγιστης Ζωής"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "YoneE", "name": "Αδέσμευτη Ψυχή", "description": "Το πνεύμα του Γιόνε εγκαταλείπει το σώμα του και αποκτά Ταχύτητα Κίνησης. Με το τέλος της ικανότητας, το πνεύμα του Γιόνε γυρίζει στο σώμα του και προκαλεί ξανά μέρος της ζημιάς που έκανε ως πνεύμα.", "tooltip": "Ο Γιόνε αποκτά μορφή πνεύματος για {{ returntimer }} δευτ., εγκαταλείπει το σώμα του για τη διάρκεια της ικανότητας και αποκτά <speed>{{ startingms*100 }}%</speed> έως <speed>{{ movementspeed*100 }}% Ταχύτητα Κίνησης που αυξάνεται σταδιακά</speed>. <br /><br />Με το τέλος της μορφής πνεύματος, ο Γιόνε επιστρέφει στο σώμα του και προκαλεί ξανά το {{ deathmarkpercent*100 }}% της συνολικής ζημιάς που προκάλεσε σε Ήρωες με επιθέσεις και ικανότητες σε αυτό το διάστημα. Μπορείτε να <recast>χρησιμοποιήσετε ξανά</recast> αυτή την ικανότητα όταν είστε σε μορφή πνεύματος.<br /><br /><recast>Νέα χρήση: </recast>Τερματίστε νωρίτερα τη μορφή πνεύματος.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Επαναλαμβανόμενη Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, {"id": "YoneR", "name": "Σφράγισμα της Μοίρας", "description": "Ο Γιόνε τηλεμεταφέρεται πίσω από τον τελευταίο Ήρωα σε μια γραμμή, με ένα σκίσιμο τόσο ισχυρό που τραβά προς το μέρος του όλους τους εχθρούς που χτυπά.", "tooltip": "Ο Γιόνε χτυπά όλους τους εχθρούς κατά μήκος μιας ευθείας προκαλώντας <physicalDamage>{{ tooltipdamage }} Σωματική Ζημιά</physicalDamage> και <magicDamage>{{ tooltipdamage }} Μαγική Ζημιά</magicDamage>, τηλεμεταφέρεται πίσω από τον τελευταίο Ήρωα που χτύπησε και εκτοξεύει <status>Στον αέρα</status> τα θύματά του προς την κατεύθυνση του Γιόνε.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}], "passive": {"name": "Ο Δρόμος του Κυνηγού", "description": "Ο Γιόνε προκαλεί Μαγική Ζημιά με κάθε δεύτερη επίθεση. Επιπλέον, η Πιθανότητα Καίριου Χτυπήματος του Γιόνε αυξάνεται.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}