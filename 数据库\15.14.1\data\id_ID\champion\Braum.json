{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "<PERSON><PERSON><PERSON>", "title": "the Heart of the Freljord", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "Dragonslayer <PERSON>", "chromas": true}, {"id": "201002", "num": 2, "name": "El Tigre Braum", "chromas": false}, {"id": "201003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "201010", "num": 10, "name": "Santa Braum", "chromas": false}, {"id": "201011", "num": 11, "name": "Crime City Braum", "chromas": true}, {"id": "201024", "num": 24, "name": "Sugar Rush Braum", "chromas": true}, {"id": "201033", "num": 33, "name": "Pool Party Braum", "chromas": true}, {"id": "201042", "num": 42, "name": "Grill Master <PERSON>", "chromas": true}], "lore": "Punya bisep berotot kekar dan hati yang lebih besar lagi, <PERSON><PERSON><PERSON> adalah pahlawan yang dicintai di Freljord. Setiap area makan di utara Frostheld merayakan kekuatan legendarisnya, yang konon pernah membabat hutan pohon ek dalam semalam, juga menghantam gunung jadi reruntuhan. Dengan pintu kubah ajaib sebagai shield, Braum menjelajahi wilayah utara yang membeku dengan senyum selebar ototnya. Dia menjadi teman sejati bagi semua orang yang membutuhkan.", "blurb": "Punya bisep berotot kekar dan hati yang lebih besar lagi, <PERSON><PERSON><PERSON> adalah pahlawan yang dicintai di Freljord. Setiap area makan di utara Frostheld merayakan kekuatan legendarisnya, yang konon pernah membabat hutan pohon ek dalam semalam, juga menghantam...", "allytips": ["<PERSON><PERSON><PERSON><PERSON><PERSON> dengan sekutu untuk men<PERSON><PERSON><PERSON><PERSON> stack Concussive Blows, ajak mereka melancarkan basic attack ke target yang sudah ditandai.", "Lompat ke depan teman yang lemah dan lindungi mereka dari proyektil dengan Unbreakable.", "Glacial Fissure meninggalkan zona slow yang kuat, p<PERSON>ikan dengan baik untuk split push pertarungan tim dan memperlambat musuh."], "enemytips": ["Braum harus melancarkan Winter's Bite atau basic attack untuk memulai Concussive Blows. <PERSON><PERSON> kamu ditan<PERSON>, keluar dari range combat sebelum terkena 3 hit lagi agar kamu terhindar dari <PERSON>.", "Ultima Braum memiliki waktu cast yang lama, jadi gunakan waktu ekstra itu untuk menghindari. <PERSON><PERSON><PERSON><PERSON> di atas permukaan beku yang tertinggal akan memperlambat gerakanmu, posisikan diri agar tidak perlu melewatinya.", "Unbreakable memberi Braum pertahanan kuat dalam satu arah, tunggu sampai selesai atau posisikan diri agar bisa menghindari Ability tersebut."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "Winter's Bite", "description": "Braum melontarkan es beku dari shield-nya, menerapkan efek slow dan menghasilkan magic damage.<br><br>Serangan ini menerapkan satu stack <font color='#FFF673'>Concussive Blows</font>.", "tooltip": "Braum melontarkan es beku dari shield-nya, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totaldamage }} magic damage</magicDamage> pada musuh pertama yang terkena dan menerapkan <status>Slow</status> sebesar {{ e2 }}% yang berkurang dalam kurun waktu {{ e5 }} detik.<br /><br />Serangan ini menerapkan satu stack <keywordMajor>Concussive Blows</keywordMajor>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumW", "name": "Stand Behind Me", "description": "Braum melompat ke champion atau minion sekutu yang menjadi targetnya. <PERSON><PERSON> mendarat, Braum beserta sekutunya mendapatkan Armor dan Magic Resist selama beberapa detik.", "tooltip": "Braum melompat ke champion atau minion sekutu. Saat mendarat, Braum memberi target <scaleArmor>{{ grantedallyarmor }} Armor</scaleArmor> dan <scaleMR>{{ grantedallymr }} Magic Resist</scaleMR> selama {{ e1 }} detik. Braum memberi dirinya <scaleArmor>{{ grantedbraumarmor }} Armor</scaleArmor> dan <scaleMR>{{ grantedbraummr }} Magic Resist</scaleMR> selama durasi yang sama.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Cooldown"], "effect": ["{{ baseresists }}-> {{ baseresistsNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumE", "name": "Unbreakable", "description": "Braum mengangkat shield ke satu arah selama beberapa detik untuk mengadang semua proyektil agar hancur saat mengenainya. Braum mengabaikan damage serangan pertama dan mengurangi semua damage serangan selanjutnya dari arah ini.", "tooltip": "Braum mengangkat shield selama {{ e2 }} detik, mengadang misil musuh dari arah tertentu, menyebabkan misil mengenai Braum dan hancur. Misil pertama yang Braum adang tidak menghasilkan damage, dan proyektil selanjutnya menghasilkan {{ e3 }}% damage yang berkurang.<br /><br />Braum mendapatkan <speed>{{ e4 }}% Move Speed</speed> saat shield diangkat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Pengurangan Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e2 }}-> {{ e2NL }}", "{{ e3 }}%-> {{ e3NL }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumRWrapper", "name": "Glacial Fissure", "description": "Braum menghantam tanah serta Knock Up musuh di sekitar dan yang berada di satu garis di depannya. Retakan akan muncul pada garis tersebut dan memberi efek slow pada musuh.", "tooltip": "Braum menghantam tanah, memunculkan retakan yang <status>Knock Up</status> musuh di jalurnya dan yang ada di sekitar <PERSON>, serta mengh<PERSON>lkan <magicDamage>{{ totaldamage }} magic damage</magicDamage>. Target pertama yang dihantam akan <status>Terkena Knock Up</status> selama {{ minknockup }} hingga {{ maxknockup }} detik, yang meningkat dengan jarak dari Braum. Target lain yang dihantam akan <status>Terkena Knock Up</status> selama {{ minknockup }} detik.<br /><br />Retakan juga menciptakan area selama {{ slowzoneduration }} detik, yang menerapkan <status>Slow</status> sebanyak {{ movespeedmod }}%.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>", "Slow", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ firstknockupduration }}-> {{ firstknockupdurationNL }}", "{{ movespeedmod }}%-> {{ movespeedmodNL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Concussive Blows", "description": "Basic attack Braum menerapkan Concussive Blows. Saat stack pertama diterapkan, basic attack <font color='#FFF673'>sekutu</font> juga akan stack Concussive Blows. <br><br>Saat mencapai 4 stack, target akan terkena stun dan menerima magic damage. Untuk beberapa detik selanju<PERSON>nya, musuh tidak bisa menerima stack baru, tetapi akan menerima magic damage bonus dari serangan <PERSON>raum.", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}