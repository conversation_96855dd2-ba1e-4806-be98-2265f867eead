{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Udyr": {"id": "<PERSON><PERSON><PERSON>", "key": "77", "name": "ウディア", "title": "精霊と歩む者", "image": {"full": "Udyr.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "77000", "num": 0, "name": "default", "chromas": false}, {"id": "77001", "num": 1, "name": "黒帯ウディア", "chromas": false}, {"id": "77002", "num": 2, "name": "原始の力ウディア", "chromas": false}, {"id": "77003", "num": 3, "name": "精霊の守護者ウディア", "chromas": false}, {"id": "77004", "num": 4, "name": "これじゃないウディア", "chromas": false}, {"id": "77005", "num": 5, "name": "龍の啓示ウディア", "chromas": false}, {"id": "77006", "num": 6, "name": "墨影のウディア", "chromas": true}], "lore": "現存するスピリットウォーカーの中でも最大の力を持つウディアは、フレヨルドのあらゆる精霊と心を通わせることができる。彼らの欲求に共感して理解を示したり、その霊的なエネルギーを変換して、自身の原始的な戦闘法に取り入れることができるのだ。自らの心が周囲の心の声に埋もれてしまわないように、ウディアは内なる均衡を求めているが、外の世界についても均衡を欲している──フレヨルドの神秘的な風景は、対立と争いから生まれる成長によってのみ、繁栄することができるのだ。ウディアは、平和という停滞を避けるためには、犠牲を払うことも厭わない。", "blurb": "現存するスピリットウォーカーの中でも最大の力を持つウディアは、フレヨルドのあらゆる精霊と心を通わせることができる。彼らの欲求に共感して理解を示したり、その霊的なエネルギーを変換して、自身の原始的な戦闘法に取り入れることができるのだ。自らの心が周囲の心の声に埋もれてしまわないように、ウディアは内なる均衡を求めているが、外の世界についても均衡を欲している──フレヨルドの神秘的な風景は、対立と争いから生まれる成長によってのみ、繁栄することができるのだ。ウディアは、平和という停滞を避けるためには、犠牲を払うこ...", "allytips": ["シールドの耐久値は、防御力によって軽減されたダメージを受けるので、防御力を強化するアイテムを購入することが、生存力を大幅に高めるカギとなる。", "ウディアは、安定して中立モンスターを狩れるジャングラーだ。うまく活用してマップ上の状況を把握し、チームとしての経験値獲得量を大幅に増加させよう。"], "enemytips": ["ウディアは遠隔攻撃が限られているので、なるべく距離を取ろう。", "ウディアは、より強力な「覚醒」したスキルを使用すると、しばらくは他のスキルを「覚醒」できなくなる。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 8, "defense": 7, "magic": 4, "difficulty": 7}, "stats": {"hp": 664, "hpperlevel": 92, "mp": 271, "mpperlevel": 50, "movespeed": 350, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.65}, "spells": [{"id": "UdyrQ", "name": "野性の爪", "description": "攻撃速度が増加し、次の2回の通常攻撃が追加物理ダメージを与える。<br><br>再発動: 攻撃速度がさらに増加し、次の2回の通常攻撃が対象に電撃を放つようになる。", "tooltip": "<spellActive>爪の型:</spellActive> {{ attackspeeddurationbase }}秒間、<attackSpeed>攻撃速度が{{ attackspeedbase*100 }}%</attackSpeed>増加し、通常攻撃で<physicalDamage>{{ onhitdamage }}の物理ダメージ</physicalDamage> %i:OnHit% <OnHit>通常攻撃時効果</OnHit>を与える。さらに、この「型」で行う次の2回の通常攻撃が、追加で<physicalDamage>最大体力の{{ maxhponhit1 }}にあたる物理ダメージ</physicalDamage>を与え、射程距離が{{ attackrange }}増加する。<br /><br /><keywordMajor>覚醒:</keywordMajor> 増加<attackSpeed>攻撃速度</attackSpeed>が<attackSpeed>{{ empoweredtotalas }}</attackSpeed>に上昇し、最大体力に応じたダメージが<physicalDamage>{{ q2totalonhithpdamage }}</physicalDamage>に増加する。さらに、次の2回の通常攻撃が6回電撃を放ち、孤立した対象に合計で<magicDamage>最大体力の{{ empoweredlightningbonusmax }}にあたる魔法ダメージ</magicDamage>を与える(対象の周囲に他の敵がいる場合、電撃はそれらの敵に連鎖する)。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃速度", "最大体力に対するダメージ率", "通常攻撃時ダメージ"], "effect": ["{{ attackspeedbase*100.000000 }}% -> {{ attackspeedbasenl*100.000000 }}%", "{{ maxhponhitbase*100.000000 }}% -> {{ maxhponhitbasenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [20, 20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UdyrW", "name": "鉄の外皮", "description": "シールドを獲得し、次の2回の通常攻撃で自身の体力を回復する。<br><br>再発動: より耐久値の高いシールドを獲得し、数秒間かけて最大体力の一定割合を回復する。", "tooltip": "<spellPassive>外套の型:</spellPassive> {{ shieldduration }}秒間、<shield>耐久値{{ totalshield }}のシールド</shield>を獲得する。さらに、次の2回の通常攻撃が{{ lifesteal*100 }}%のライフスティールを得て、<healing>{{ lifeonhit }}の体力</healing>を回復する。<br /><br /><keywordMajor>覚醒:</keywordMajor> <shield>耐久値{{ recastshield }}のシールド</shield>を獲得し、{{ shieldduration }}秒かけて<healing>体力を{{ recastheal }}</healing>回復する。また、次の2回の通常攻撃が得るライフスティールが{{ lifesteal*200 }}%になり、<healing>{{ lifeonhitawakened }}の体力</healing>を回復する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "シールドへの体力反映率", "ライフスティール"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ shieldpercenthealth*100.000000 }}% -> {{ shieldpercenthealthnl*100.000000 }}%", "{{ lifesteal*100.000000 }}% -> {{ lifestealnl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "UdyrW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UdyrE", "name": "焔の猛進", "description": "移動速度が増加し、各対象への最初の通常攻撃が対象をスタンさせる。<br><br>再発動: 少しの間、移動速度がさらに増加し、移動不能効果を受けなくなる。 ", "tooltip": "<spellActive>猛進の型:</spellActive> <speed>移動速度が{{ movespeed*100 }}%</speed>増加し、{{ movespeedduration }}秒かけて元に戻る。さらに、通常攻撃を行うと対象までダッシュし、{{ stunduration }}秒間<status>スタン</status>させる(対象ごとにクールダウン{{ icd }}秒)。<br /><br /><keywordMajor>覚醒:</keywordMajor> {{ unstoppableduration }}秒間、<status>移動不能効果</status>および<status>行動妨害効果</status>を無視し、追加で<speed>移動速度が{{ movespeedbonus }}</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "ユニットごとのクールダウン"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ icd }} -> {{ icdNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "UdyrR", "name": "氷翼の嵐", "description": "極寒の嵐に身を包み、周囲の敵にダメージとスロウ効果を与える。<br><br>再発動: 嵐を強化して解き放ち、敵を追跡させて追加ダメージを与える。", "tooltip": "<spellActive>嵐の型:</spellActive> {{ buffduration }}秒間、極寒の嵐に身を包み、周囲の敵に毎秒<magicDamage>{{ stormdamage }}の魔法ダメージ</magicDamage>を与え、{{ slowpotency*100 }}%の<status>スロウ効果</status>を付与する。さらに、この「型」で行う次の2回の通常攻撃が、嵐の範囲内にいる敵に<magicDamage>{{ pulsedamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><keywordMajor>覚醒:</keywordMajor> 嵐を解き放ち、自身が最後に通常攻撃を行った敵を追跡させる。嵐はその効果時間をかけて追加で<magicDamage>最大体力の{{ percenthpblast }}にあたる魔法ダメージ</magicDamage>を与え、追加で{{ empoweredslow }}の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ", "スロウ効果"], "effect": ["{{ stormbasedamage }} -> {{ stormbasedamageNL }}", "{{ slowpotency*100.000000 }}% -> {{ slowpotencynl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [370, 370, 370, 370, 370, 370], "rangeBurn": "370", "image": {"full": "UdyrR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "精霊の繋ぎ手", "description": "4つの通常スキルで「型」を切り替え、スキルを再発動すると「型」がリフレッシュされて究極の効果を得る。さらに、スキル使用後、次の2回の通常攻撃の攻撃速度が増加する。", "image": {"full": "Udyr_P.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}