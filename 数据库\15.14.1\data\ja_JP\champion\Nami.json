{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nami": {"id": "<PERSON><PERSON>", "key": "267", "name": "ナミ", "title": "潮呼びの巫女", "image": {"full": "Nami.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "267000", "num": 0, "name": "default", "chromas": false}, {"id": "267001", "num": 1, "name": "鯉ナミ", "chromas": true}, {"id": "267002", "num": 2, "name": "川の精霊ナミ", "chromas": false}, {"id": "267003", "num": 3, "name": "アーフ・ザ・ナミティ", "chromas": false}, {"id": "267007", "num": 7, "name": "深海のナミ", "chromas": false}, {"id": "267008", "num": 8, "name": "SKT T1 ナミ", "chromas": false}, {"id": "267009", "num": 9, "name": "プログラム ナミ", "chromas": true}, {"id": "267015", "num": 15, "name": "壮麗の杖ナミ", "chromas": true}, {"id": "267024", "num": 24, "name": "宇宙の運命ナミ", "chromas": true}, {"id": "267032", "num": 32, "name": "魅惑の魔女ナミ", "chromas": true}, {"id": "267041", "num": 41, "name": "スペースグルーヴ ナミ", "chromas": true}, {"id": "267042", "num": 42, "name": "プレステージ スペースグルーヴ ナミ", "chromas": false}, {"id": "267051", "num": 51, "name": "盟約の魔女ナミ", "chromas": true}, {"id": "267058", "num": 58, "name": "神話創生ナミ", "chromas": true}], "lore": "向こう見ずな性格の海の若きヴァスタヤであるナミは、ターゴン人たちとの間で太古から続いていた協約が破られた時、マライの民として初めて海を離れて陸地に上がることになった。それ以外に方法がなかったことから、彼女は部族の安全を守るための聖なる儀式を自らの手で完遂することを決めた。この新たな混沌の時代の中で、ナミは潮呼びの巫女の杖を使って海の力を召喚しながら、不安だらけの未来に固い決意で挑んでいる。", "blurb": "向こう見ずな性格の海の若きヴァスタヤであるナミは、ターゴン人たちとの間で太古から続いていた協約が破られた時、マライの民として初めて海を離れて陸地に上がることになった。それ以外に方法がなかったことから、彼女は部族の安全を守るための聖なる儀式を自らの手で完遂することを決めた。この新たな混沌の時代の中で、ナミは潮呼びの巫女の杖を使って海の力を召喚しながら、不安だらけの未来に固い決意で挑んでいる。", "allytips": ["「水の牢獄」はクールダウンが長い。使うタイミングはよく見計らって決めよう。", "「潮の流れ」を複数のチャンピオンに命中させることで、状況を有利に導ける。潮の動きを読み、誰に使うかよく考えよう。", "ナミのアルティメットスキルは味方を守るのにも、敵に攻め込むのにも使える。状況を見極めよう。"], "enemytips": ["「水の牢獄」は強力だが、クールダウンが長い。ナミがこのスキルを空振りしたときは攻撃のチャンスだ。", "「海神の舞」は範囲が広いものの、移動速度が遅い。自分のほうへ向かってきたら、すかさず通り道から逃げよう。", "「潮使いの祝福」を受けた敵との戦闘は避けよう。効果時間は短いので、効果が消えるのを待つほうが得策だ。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 3, "magic": 7, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 335, "armor": 29, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.61, "attackspeed": 0.644}, "spells": [{"id": "NamiQ", "name": "水の牢獄", "description": "指定地点に水泡を飛ばし、着弾時に範囲内の敵ユニットにダメージを与えてスタン効果を付与する。", "tooltip": "水泡を飛ばし、敵に{{ e2 }}秒間の<status>スタン効果</status>と、<magicDamage>{{ totaldamagett }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 130, 185, 240, 295], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/130/185/240/295", "1.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "NamiQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NamiW", "name": "潮の流れ", "description": "味方と敵チャンピオンの間を交互に跳ね返る水流を放つ。命中した味方は体力が回復し、敵はダメージを受ける。", "tooltip": "味方と敵チャンピオンの間を交互に跳ね返る水流を放つ。水流は最大{{ maxtargets }}体まで命中するが、同じチャンピオンに跳ね返ることはない。<li>味方の<healing>体力を{{ totalheal }}</healing>回復して、近くの敵チャンピオンに跳ね返る。<li>敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて、近くの味方チャンピオンに跳ね返る。<br />跳ね返るたびにダメージと回復量に{{ bouncescaling }}の補正がかかる。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "NamiW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NamiE", "name": "潮使いの祝福", "description": "短時間、味方チャンピオンに力を与える。強化された味方は次の数回の通常攻撃とスキルで、対象に追加魔法ダメージとスロウ効果を付与する。", "tooltip": "{{ buffduration }}秒間、味方チャンピオン1体の通常攻撃とスキルが{{ hitcount }}回分強化されて、対象に{{ slowduration }}秒間{{ totalslow }}の<status>スロウ効果</status>と、追加で<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えるようになる。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslow }}% -> {{ baseslowNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [15, 20, 25, 30, 35], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "15/20/25/30/35", "1", "3", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NamiE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NamiR", "name": "海神の舞", "description": "ナミが海の力を借りて「海神の舞」を呼び寄せる。波に触れた敵ユニットはダメージを受け、ノックアップされてスロウ状態になる。命中した味方は「さざなみの後押し」の2倍の効果を得る。", "tooltip": "大きな波を起こし、0.5秒間<status>ノックアップ</status>して、{{ e4 }}%の<status>スロウ効果</status>と<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<status>スロウ効果</status>時間は波の移動距離に比例して長くなる(最長{{ e5 }}秒)。<br /><br />波に触れた味方は<spellName>「さざなみの後押し」</spellName>の2倍の効果を得る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [2, 2, 2], [70, 70, 70], [4, 4, 4], [0.002, 0.002, 0.002], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.5", "2", "70", "4", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [2550, 2550, 2550], "rangeBurn": "2550", "image": {"full": "NamiR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "さざなみの後押し", "description": "スキルが味方チャンピオンに命中するたびに、命中した相手の移動速度が短時間増加する。", "image": {"full": "NamiPassive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}