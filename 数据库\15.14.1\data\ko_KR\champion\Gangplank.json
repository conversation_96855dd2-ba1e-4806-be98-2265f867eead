{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gangplank": {"id": "Gangplank", "key": "41", "name": "갱플랭크", "title": "바다의 무법자", "image": {"full": "Gangplank.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "41000", "num": 0, "name": "default", "chromas": false}, {"id": "41001", "num": 1, "name": "으스스한 갱플랭크", "chromas": false}, {"id": "41002", "num": 2, "name": "민병대원 갱플랭크", "chromas": false}, {"id": "41003", "num": 3, "name": "해군 갱플랭크", "chromas": false}, {"id": "41004", "num": 4, "name": "장난감 병정 갱플랭크", "chromas": false}, {"id": "41005", "num": 5, "name": "특수 부대 갱플랭크", "chromas": false}, {"id": "41006", "num": 6, "name": "술탄 갱플랭크", "chromas": false}, {"id": "41007", "num": 7, "name": "캡틴 갱플랭크", "chromas": false}, {"id": "41008", "num": 8, "name": "행성 파괴자 갱플랭크", "chromas": true}, {"id": "41014", "num": 14, "name": "수영장 파티 갱플랭크", "chromas": true}, {"id": "41021", "num": 21, "name": "FPX 갱플랭크", "chromas": true}, {"id": "41023", "num": 23, "name": "배신자 갱플랭크", "chromas": true}, {"id": "41033", "num": 33, "name": "프로젝트: 갱플랭크", "chromas": true}], "lore": "몰락한 해적왕 갱플랭크는 잔인한 성격에다 종잡을 수 없으며 사악함은 타의 추종을 불허한다. 과거 항구도시 빌지워터를 힘으로 장악했으나 지금은 영향력을 잃었다. 하지만 사람들은 그렇기 때문에 오히려 갱플랭크가 더 미쳐 날뛰리라고 생각한다. 갱플랭크는 빌지워터를 다른 사람에게 넘기느니 다시 한 번 피바다로 만들어 버릴 인물이니까. 그리고 이제, 권총, 해적검, 화약통으로 무장한 갱플랭크가 잃었던 패권을 되찾기 위한 준비를 끝냈다.", "blurb": "몰락한 해적왕 갱플랭크는 잔인한 성격에다 종잡을 수 없으며 사악함은 타의 추종을 불허한다. 과거 항구도시 빌지워터를 힘으로 장악했으나 지금은 영향력을 잃었다. 하지만 사람들은 그렇기 때문에 오히려 갱플랭크가 더 미쳐 날뛰리라고 생각한다. 갱플랭크는 빌지워터를 다른 사람에게 넘기느니 다시 한 번 피바다로 만들어 버릴 인물이니까. 그리고 이제, 권총, 해적검, 화약통으로 무장한 갱플랭크가 잃었던 패권을 되찾기 위한 준비를 끝냈다.", "allytips": ["혀어어어업상은 얼어붙은 망치 또는 칠흑의 양날도끼와 같이 적에게 적중 시 효과가 발생하는 아이템 효과가 적용됩니다.", "지도에 체력이 낮은 적이 보이면 포탄 세례로 마무리하십시오.", "적의 탈출로에 포탄 세례를 사용하여 도망치는 적을 나머지 적 챔피언 무리로부터 끊어놓으십시오."], "enemytips": ["혀어어어업상은 매우 높은 물리 피해를 주므로 방어력을 높여주는 아이템이 있다면 그와 맞서는 데 도움이 됩니다.", "갱플랭크가 6레벨에 도달하면 맵 전역에 적용할 수 있는 궁극기 포탄 세례를 주의하세요."], "tags": ["Fighter"], "partype": "마나", "info": {"attack": 7, "defense": 6, "magic": 4, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 280, "mpperlevel": 60, "movespeed": 345, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 4.2, "attackspeedperlevel": 3.2, "attackspeed": 0.658}, "spells": [{"id": "GangplankQWrapper", "name": "혀어어어업상", "description": "대상에게 총을 발사해 처치한 적 유닛마다 골드를 약탈합니다.", "tooltip": "{{Spell_GangplankQWrapper_Tooltip_{{ gamemodeinteger }}}}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "약탈한 골드", "약탈한 바다뱀 은화", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e5 }} -> {{ e5NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4.5, 4.5, 4.5, 4.5, 4.5], "cooldownBurn": "4.5", "cost": [50, 45, 40, 35, 30], "costBurn": "50/45/40/35/30", "datavalues": {}, "effect": [null, [10, 40, 70, 100, 130], [3, 4, 5, 6, 7], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 5, 6, 7, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/40/70/100/130", "3/4/5/6/7", "0", "500", "4/5/6/7/8", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "GangplankQWrapper.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GangplankW", "name": "괴혈병 치료", "description": "귤을 먹어서 군중 제어 효과를 제거하고 체력을 회복합니다.", "tooltip": "갱플랭크가 귤을 많이 먹어서 모든 <status>방해</status> 효과를 제거하고 체력을 <healing>{{ basehealth }}+잃은 체력의 {{ e2 }}%</healing>만큼 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [45, 70, 95, 120, 145], [13, 13, 13, 13, 13], [0.25, 0.25, 0.25, 0.25, 0.25], [200, 200, 200, 200, 200], [30, 40, 50, 60, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/70/95/120/145", "13", "0.25", "200", "30/40/50/60/70", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GangplankW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GangplankE", "name": "화약통", "description": "갱플랭크가 대상 위치에 화약통을 비치합니다. 갱플랭크가 이 통을 공격하면 폭발하여 주위 적들에게 피해를 입히며 둔화를 적용합니다.", "tooltip": "{{ e5 }}초 동안 갱플랭크와 적 챔피언이 공격할 수 있는 화약통을 설치합니다. 적이 파괴하는 통은 사라집니다. 갱플랭크가 파괴하는 통은 폭발하여 {{ e2 }}초 동안 적을 {{ finalslowamount }}% <status>둔화</status>시키고 방어력의 {{ e0 }}%를 무시하며 <physicalDamage>기본 공격의 피해량</physicalDamage>만큼 피해를 입힙니다. 챔피언은 <physicalDamage>{{ e3 }}의 물리 피해</physicalDamage>를 추가로 입습니다.<br /><br />통의 체력은 {{ f5 }}초마다 줄어듭니다. 통이 폭발하면 폭발 지대에 겹쳐 있는 통들이 연쇄 폭발하지만 같은 대상이 여러 번 피해를 입지는 않습니다. <spellName>혀어어어업상</spellName> 스킬로 통을 터뜨리면 대상 처치 시 추가 골드를 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["챔피언에 대한 추가 피해량", "최대 충전량", "둔화", "재충전 시간"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ barrelslow }}% -> {{ barrelslowNL }}%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [3, 3, 4, 4, 5], [2, 2, 2, 2, 2], [75, 105, 135, 165, 195], [40, 50, 60, 70, 80], [25, 25, 25, 25, 25], [2, 2, 2, 2, 2], [10, 10, 10, 10, 10], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 40, 40, 40, 40]], "effectBurn": [null, "3/3/4/4/5", "2", "75/105/135/165/195", "40/50/60/70/80", "25", "2", "10", "100", "0.5", "40"], "vars": [], "costType": "소모값 없음", "maxammo": "3", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GangplankE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "GangplankR", "name": "포탄 세례", "description": "갱플랭크가 특정 지역을 포격하도록 배에 신호하여 적의 이동 속도를 늦추며 피해를 입힙니다.", "tooltip": "갱플랭크가 배에 신호를 보내 맵 어느 위치로든 {{ zoneduration }}초 동안 {{ totalwavestooltip }}차례 포탄을 발사하도록 합니다. 대포 세례마다 {{ slowduration }}초 동안 {{ slowpercent }}%의 <status>둔화</status>를 적용하며 <magicDamage>{{ onewavedamage }}의 마법 피해</magicDamage>를 입힙니다. 최대 피해량: {{ totaldamagetooltip }}<br /><br />이 스킬은 <spellName>혀어어어업상</spellName> 스킬을 통해 상점에서 업그레이드할 수 있습니다.<br /><br /><spellName>가차없는 포격</spellName>: 6차례 추가로 포탄을 발사합니다.<br /><spellName>죽음의 여신</spellName>: 대형 포탄을 발사해 <trueDamage>{{ deathsdaughterdamage }}의 고정 피해</trueDamage>를 입히고 {{ deathsdaughterslowduration }}초 동안 {{ deathsdaughterslow }}% <status>둔화</status>를 적용합니다.<br /><spellName>사기진작</spellName>: 포탄 세례 범위 안에 있는 아군의 이동 속도가 {{ raisemoralehasteduration }}초 동안 <speed>{{ raisemoralehaste }}%</speed> 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["대포 세례당 피해량", "재사용 대기시간"], "effect": ["{{ damageperwave }} -> {{ damageperwaveNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1.5, 0.5, 0.5], [0, 0, 0], [300, 300, 300], [75, 75, 75], [1, 1, 1], [40, 40, 40]], "effectBurn": [null, "0", "0", "0", "0", "1.5/0.5/0.5", "0", "300", "75", "1", "40"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [30000, 30000, 30000], "rangeBurn": "30000", "image": {"full": "GangplankR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "불의 심판", "description": "몇 초에 한 번씩 갱플랭크의 근접 공격이 적에게 불을 붙입니다.", "image": {"full": "Gangplank_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}