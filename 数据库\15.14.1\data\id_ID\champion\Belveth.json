{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Belveth": {"id": "Belveth", "key": "200", "name": "Bel'Veth", "title": "the Empress of the Void", "image": {"full": "Belveth.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "200000", "num": 0, "name": "default", "chromas": false}, {"id": "200001", "num": 1, "name": "Battle Boss Bel'Veth", "chromas": true}, {"id": "200010", "num": 10, "name": "Cosmic Matriarch Bel'Veth", "chromas": true}, {"id": "200019", "num": 19, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Per<PERSON><PERSON><PERSON> mengerikan yang tercipta dari musnahnya seisi kota, <PERSON><PERSON><PERSON><PERSON> menjadi akhir dari Runeterra sendiri ... dan awal dari kenyataan mengerikan yang dirancangnya sendiri. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan kenangan dari dunia atas yang direkayasa, dia gencar memenuhi kebutuhan yang makin besar akan pengalaman dan emosi baru, melahap semua yang dia temui. <PERSON><PERSON><PERSON>, keinginannya tak akan pernah terpuaskan hanya dengan satu dunia, karena dia mengalihkan pandangan laparnya ke arah penguasa terdahulu Void ...", "blurb": "<PERSON><PERSON><PERSON><PERSON> mengerikan yang tercipta dari mus<PERSON><PERSON> seisi kota, <PERSON><PERSON><PERSON><PERSON> menjadi akhir dari <PERSON>ra sendiri ... dan awal dari kenyataan mengerikan yang dirancangnya sendiri. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan kenangan dari dunia atas yang...", "allytips": [], "enemytips": [], "tags": ["Fighter"], "partype": "", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 60, "mpperlevel": 0, "movespeed": 340, "armor": 32, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 1.5, "attackspeedperlevel": 0, "attackspeed": 0.85}, "spells": [{"id": "BelvethQ", "name": "Void Surge", "description": "Bel'Veth dash ke arah terpilih dan memberi damage ke semua musuh yang dia lewati.", "tooltip": "Bel'Veth dash, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ basedamage }} physical damage</physicalDamage> ke musuh yang dia lewati.<br /><br />Setiap arah punya Cooldown unik {{ f1 }} detik yang berkurang berdasarkan <attackSpeed>Attack Speed</attackSpeed>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown per Arah", "Damage Monster", "Damage Minion"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ persidecooldown }}-> {{ persidecooldownNL }}", "{{ monstermod }}-> {{ monstermodNL }}", "{{ minonmod*100.000000 }}%-> {{ minonmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethQ.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "BelvethW", "name": "Above and Below", "description": "Bel'<PERSON>eth mengempaskan ekornya ke tanah, memberi damage, membuat musuh Knock Up, dan men<PERSON><PERSON><PERSON> slow.", "tooltip": "Bel'Veth men<PERSON><PERSON><PERSON><PERSON> e<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damage }} magic damage</magicDamage>, <status>Knock Up</status> musuh selama {{ duration }} detik, dan menerapkan <status>Slow</status> pada mereka sebesar {{ slowpercent*100 }}% selama {{ slowduration }} detik. Jika champion terkena serangan, Cooldown <spellName>Void Surge</spellName> di arah itu di-refresh.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ slowduration }}-> {{ slowdurationNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [715, 715, 715, 715, 715], "rangeBurn": "715", "image": {"full": "BelvethW.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "BelvethE", "name": "Royal Maelstrom", "description": "Bel'<PERSON><PERSON> berdiam di tempatnya, channeling badai serangan yang mengelilinginya dan menyerang musuh dengan Health paling rendah, sambil memberi lifesteal dan pengurangan damage.", "tooltip": "Bel'Veth channeling dan menebas di sekelilingnya, mendapatkan {{ drpercent*100 }}% Pengurangan Damage, {{ totallifesteal }} Life Steal, dan menyerang {{ f2.0 }} kali selama {{ totalduration }} detik dengan jumlah Serangan bertambah berdasarkan <attackSpeed>Attack Speed</attackSpeed>. Setiap Serangan menyerang musuh dengan Health paling rendah, menghasilkan <physicalDamage>{{ damageperstrike }}</physicalDamage> hingga <physicalDamage>{{ maxdamageperstriketooltip }} physical damage</physicalDamage> berdasarkan Health target yang hilang.<br /><br />Menggunakan Ability lain atau <recast>Recast</recast> akan mengakhiri Ability ini lebih awal.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Pengurangan Damage", "Cooldown"], "effect": ["{{ damageperhit }}-> {{ damageperhitNL }}", "{{ drpercent*100.000000 }}%-> {{ drpercentnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "BelvethE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "BelvethR", "name": "Endless Banquet", "description": "Bel'Veth menyerap sisa-sisa Void Coral, be<PERSON><PERSON> ke True Form, dan meningkatkan Health maksimum, Attack Range, Attack Speed, serta Move Speed di luar kombat. Mengonsumsi sisa-sisa Void Coral dari monster epik Void akan memberinya durasi Ultima lebih lama, begitu juga dengan kekuatan untuk memanggil Void Remora.", "tooltip": "<spellPassive>Pasif:</spellPassive> Setiap serangan kedua melawan target yang sama mengh<PERSON>lkan <trueDamage>{{ finalonhitdamage }} true damage</trueDamage> tambahan, bisa di-stack tanpa batas. Melakukan takedown champion dan monster epik akan menyisakan sebuah Void Coral.<br /><br /><spellActive>Aktif:</spellActive> Mengonsumsi Void Coral memberikan stack <keywordMajor>{{ passivestacksondevour }} Lavender</keywordMajor> dan mengaktifkan True Form Bel'Veth selama {{ steroidduration }} detik. Void Coral dari monster epik Void meningkatkan durasi itu hingga {{ voidduration }} detik dan menyebabkan minion yang mati di sekitar menjadi Void Remora. Saat casting, Bel'Veth menerapkan <status>Slow</status> pada musuh di sekitar sebelum meledak, menghasilkan <trueDamage>{{ totalexplosiondamage }} + {{ missinghealthdamage*100 }}% true damage dari Health yang hilang</trueDamage>.<br /><br /><PERSON>am True Form, Bel'Veth mendapatkan <healing>{{ maxhealthondevour }} Health maksimum</healing>, <speed>{{ oocms }} Move Speed</speed> di luar kombat, {{ bonusaarange }} Attack Range, <attackSpeed>{{ totalasmod*100 }}% Total Attack Speed</attackSpeed>, dan <spellName>Void Surge</spellName> yang bisa menembus dinding.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage On-Hit", "Damage Led<PERSON>n", "Health Bonus", "Move Speed", "Attack Speed", "Health Void Remora"], "effect": ["{{ onhitdamage }}-> {{ onhitdamageNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ basemaxhealth }}-> {{ basemaxhealthNL }}", "{{ oocms }}-> {{ oocmsNL }}", "{{ totalasmod*100.000000 }}%-> {{ totalasmodnl*100.000000 }}%", "{{ voidlinghpscale*100.000000 }}%-> {{ voidlinghpscalenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}], "passive": {"name": "Death in Lavender ", "description": "<PERSON>'<PERSON><PERSON> mendapatkan stack Attack Speed permanen setelah mengal<PERSON>kan minion besar, monster besar, dan champion. Dia juga mendapatkan Attack Speed bonus sementara setelah menggunakan ability.", "image": {"full": "Belveth_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}