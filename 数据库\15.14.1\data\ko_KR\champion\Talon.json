{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Talon": {"id": "Talon", "key": "91", "name": "탈론", "title": "검의 그림자", "image": {"full": "Talon.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "91000", "num": 0, "name": "default", "chromas": false}, {"id": "91001", "num": 1, "name": "무법자 탈론", "chromas": false}, {"id": "91002", "num": 2, "name": "핏빛 친위대 탈론", "chromas": false}, {"id": "91003", "num": 3, "name": "은룡검 탈론", "chromas": true}, {"id": "91004", "num": 4, "name": "삼성 화이트 탈론", "chromas": false}, {"id": "91005", "num": 5, "name": "핏빛달 탈론", "chromas": false}, {"id": "91012", "num": 12, "name": "불멸의 영웅 탈론", "chromas": true}, {"id": "91020", "num": 20, "name": "검은나무 탈론", "chromas": true}, {"id": "91029", "num": 29, "name": "메마른 장미 탈론", "chromas": true}, {"id": "91038", "num": 38, "name": "하이 눈 탈론", "chromas": true}, {"id": "91039", "num": 39, "name": "프레스티지 하이 눈 탈론", "chromas": false}, {"id": "91049", "num": 49, "name": "태고의 습격 탈론", "chromas": true}, {"id": "91059", "num": 59, "name": "장대한 심판 탈론", "chromas": false}], "lore": "탈론은 음지에서 암약하는 무자비한 자객이다. 불의의 일격을 가하는 데에도, 이상한 낌새가 느껴지기도 전에 자취를 감추는 데도 능하다. 거칠기 짝이 없는 녹서스의 거리에서 싸우고, 상대를 처치하고, 도둑질을 하면서 살아남았고 위험한 존재라는 명성을 쌓았다. 악명이 자자한 뒤 쿠토 가문에 발탁된 뒤로는 녹서스 제국의 명을 받들어 적의 지도자, 대장, 영웅은 물론이고, 감히 자신이 섬기는 주인의 심기를 거스르는 녹서스 인마저도 종횡무진 암살하고 있다.", "blurb": "탈론은 음지에서 암약하는 무자비한 자객이다. 불의의 일격을 가하는 데에도, 이상한 낌새가 느껴지기도 전에 자취를 감추는 데도 능하다. 거칠기 짝이 없는 녹서스의 거리에서 싸우고, 상대를 처치하고, 도둑질을 하면서 살아남았고 위험한 존재라는 명성을 쌓았다. 악명이 자자한 뒤 쿠토 가문에 발탁된 뒤로는 녹서스 제국의 명을 받들어 적의 지도자, 대장, 영웅은 물론이고, 감히 자신이 섬기는 주인의 심기를 거스르는 녹서스 인마저도 종횡무진 암살하고 있다.", "allytips": ["적 뒤에 자리를 잡고 녹서스식 외교의 근접 공격을 시도하고 싶을 때 암살자의 길을 사용해 보세요.", "그림자 공격은 탈출할 때 유용한 스킬이지만 여러 명을 공격할 때에도 유용하게 사용할 수 있습니다.", "전투 전에 미리 공격 대상을 선택해두십시오. 탈론의 모든 스킬을 하나의 대상에 집중시키면 최고의 성과를 거둘 수 있습니다. 스킬을 분산시켜 사용하면 위험한 상황에 처할 수도 있습니다."], "enemytips": ["탈론의 공격은 물리 피해를 줍니다. 초반에 방어력를 높여 대비하세요.", "탈론은 탈출 시 그림자 공격에 의지하는 편입니다. 그림자 공격이 끝나면 탈론은 크게 약해집니다.", "탈론은 기동성이 매우 좋은 챔피언입니다. 늘 위치를 파악해 두거나 공격로에서 강력하게 압박해 다른 곳으로 이동하지 못하게 하세요."], "tags": ["Assassin"], "partype": "마나", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 658, "hpperlevel": 109, "mp": 400, "mpperlevel": 37, "movespeed": 335, "armor": 30, "armorperlevel": 4.7, "spellblock": 36, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 7.6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.9, "attackspeed": 0.625}, "spells": [{"id": "TalonQ", "name": "녹서스식 외교", "description": "탈론이 대상 유닛을 찌릅니다. 근접 공격이 가능한 거리에서 사용하면 치명타가 적용됩니다. 근접 공격이 불가능한 거리에서 사용하면 대상에게 도약해서 찌릅니다. 이 스킬로 대상을 처치하면 체력을 약간 회복하고 재사용 대기시간을 일부 돌려받습니다.", "tooltip": "탈론이 대상에게 도약해 <physicalDamage>{{ leapdamage }}의 물리 피해</physicalDamage>를 입힙니다. 근접 공격이 가능한 거리에서 사용하면 대신 치명타가 적용되어 <physicalDamage>{{ criticaldamage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />이 스킬로 대상을 처치하면 <healing>체력을 {{ totalhealing }}</healing> 회복하고 재사용 대기시간의 {{ cooldownrefund*100 }}%를 돌려받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "TalonQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TalonW", "name": "갈퀴손", "description": "탈론이 부메랑 단검을 여러 개 던져 명중하는 적에게 물리 피해를 줍니다. 단검이 다시 돌아올 때 맞은 적은 추가 피해를 입고 둔화됩니다.", "tooltip": "탈론이 부메랑 단검을 여러 개 던져 <physicalDamage>{{ totalinitialdamage }}의 물리 피해</physicalDamage>를 입힙니다. 이후 단검이 돌아오며 <physicalDamage>{{ totalreturndamage }}의 물리 피해</physicalDamage>를 입히고 {{ slowduration }}초 동안 {{ movespeedslow*100 }}% <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["소모값 @AbilityResourceName@", "최초 피해량", "돌아올 때의 피해량", "둔화", "재사용 대기시간"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ initialbasedamage }} -> {{ initialbasedamageNL }}", "{{ returnbasedamage }} -> {{ returnbasedamageNL }}", "{{ movespeedslow*100.000000 }}% -> {{ movespeedslownl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TalonW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "TalonE", "name": "암살자의 길", "description": "탈론이 구조물이나 지형 위로 일정 거리를 도약해 넘습니다. 이 스킬 자체는 재사용 대기시간이 짧지만 한 번 넘은 지형은 상당 시간 동안 다시 넘을 수 없습니다.", "tooltip": "탈론이 가장 가까운 지형이나 구조물 위로 도약해 뛰어넘습니다. 한 번 넘어간 지형은 {{ wallcd }}초 동안 다시 넘을 수 없습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["지형 재사용 대기시간"], "effect": ["{{ wallcd }} -> {{ wallcdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [625, 625, 625, 625, 625], [1250, 1250, 1250, 1250, 1250], [2, 2, 2, 2, 2], [160, 135, 110, 85, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "625", "1250", "2", "160/135/110/85/60", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "TalonE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "TalonR", "name": "그림자 공격", "description": "탈론이 사방에 검을 던지면서 투명 상태가 되며 추가 이동 속도를 얻습니다. 투명 상태가 풀리면 검이 다시 탈론이 있는 곳으로 모여듭니다. 검이 움직일 때마다, 한 개 이상의 검에 맞은 적은 그림자 공격으로 물리 피해를 입습니다.", "tooltip": "탈론이 사방에 검을 던져 <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입히고, <speed>이동 속도가 {{ movespeed*100 }}%</speed> 상승하며 {{ duration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 됩니다. <keywordStealth>투명</keywordStealth> 상태가 끝나면 검이 탈론에게 돌아오며 다시 <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />탈론이 기본 공격이나 <spellName>녹서스식 외교</spellName> 스킬로 <keywordStealth>투명</keywordStealth> 상태를 해제하면 검이 탈론 대신 탈론의 대상에게 날아갑니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "이동 속도", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TalonR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "검의 최후", "description": "탈론이 챔피언이나 대형 몬스터에게 스킬을 사용하면 최대 3회까지 중첩되는 상처가 남습니다. 상처가 3회 중첩된 챔피언에게 기본 공격을 가하면 출혈을 일으켜 일정 시간 동안 큰 피해를 입힙니다.", "image": {"full": "TalonP.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}