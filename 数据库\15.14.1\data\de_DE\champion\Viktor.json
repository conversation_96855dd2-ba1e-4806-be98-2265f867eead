{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viktor": {"id": "<PERSON>", "key": "112", "name": "<PERSON>", "title": "<PERSON> Herold des Arcane", "image": {"full": "Viktor.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "112000", "num": 0, "name": "default", "chromas": false}, {"id": "112001", "num": 1, "name": "Vollmechanik-Viktor", "chromas": false}, {"id": "112002", "num": 2, "name": "Prototypen-Viktor", "chromas": false}, {"id": "112003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "112004", "num": 4, "name": "Todesschwur-Viktor", "chromas": false}, {"id": "112005", "num": 5, "name": "PsyOps<PERSON>Viktor", "chromas": true}, {"id": "112014", "num": 14, "name": "High Noon-<PERSON>", "chromas": true}, {"id": "112024", "num": 24, "name": "<PERSON><PERSON><PERSON> (Arcane)", "chromas": false}], "lore": "Als vollständig biomechanische Weiterentwicklung seines früheren Ichs hat Viktor seine glorreiche Evolution angenommen und ist für seine Anhänger zu einer Art Messias geworden. Er hat seine eigene Menschlichkeit geopfert, in der <PERSON>hme, dass durch die Beseitigung von Emotionen auch alles Leid beseitigt würde. <PERSON> nun, dem Rest der Welt die Offenbarung der Hextech-Kristalle nahezubringen, auch wenn dieser nicht in der Lage ist, ihren Nutzen zu verstehen. Letzten Endes ist für diesen Meister des Arkanen Gewalt nicht mehr als eine notwendige Variable, um die ultimative Gleichung in der Balance zu halten.", "blurb": "Als vollständig biomechanische Weiterentwicklung seines früheren Ichs hat Viktor seine glorreiche Evolution angenommen und ist für seine Anhänger zu einer Art Messias geworden. Er hat seine eigene Menschlichkeit geopfert, in der Annahme, dass durch die...", "allytips": ["„Hextech-Strahl“ kann G<PERSON>nern gezielt zusetzen oder ihnen eine Passage verwehren. Verwende es zusammen mit „Gravitationsfeld“, um deinem Gegner seine Position vorzugeben.", "<PERSON>chte darauf, die richtige Verstärkung zu wählen."], "enemytips": ["<PERSON><PERSON><PERSON>, wie nahe du <PERSON> an dich heran lässt. <PERSON> kann seine G<PERSON>ner besser kontrollieren, je näher er diesen ist.", "Reagiere auf die von Viktor benutzte Verstärkung, indem du auf das Licht seines Stabs achtest (lila, gelb, blau, rot)."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 600, "hpperlevel": 100, "mp": 405, "mpperlevel": 45, "movespeed": 335, "armor": 23, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 8, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "ViktorQ", "name": "Krafttransfer", "description": "<PERSON> fügt einer gegnerischen Einheit magischen Schaden zu, was ihm einen <PERSON> verle<PERSON> und seinen nächsten Grundangriff verstärkt.<br><br>Verstärkung: Der Schild von „Krafttransfer“ ist um 60&nbsp;% erhöht und Viktor erhält bei der Aktivierung zusätzliches Lauftempo.<br>", "tooltip": "<PERSON> feuert auf e<PERSON>, verursacht <magicDamage>{{ totalmissiledamage }}&nbsp;magischen <PERSON></magicDamage> und gewährt sich selbst {{ buffduration }}&nbsp;Sekunden lang einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ shieldlevelscaling }}.<br /><br />Viktors nächster Angriff innerhalb von 3,5&nbsp;Sekunden verursacht zusätzlich <magicDamage>{{ attacktotaldmg }}&nbsp;magischen <PERSON>haden</magicDamage>.<br /><br /><keywordMajor>Augmentierung:</keywordMajor> Gewährt einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ totalaugmentedshieldvalue }} und Viktor erhält zusätzlich {{ buffduration }}&nbsp;Sekunden lang <speed>{{ augmentmovespeedbonus }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "Schaden (Projektil)", "<PERSON><PERSON><PERSON> (Angriff)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ aabonusdamage }} -> {{ aabonusdamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [2.5, 2.5, 2.5, 2.5, 2.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0.08, 0.08, 0.08, 0.08, 0.08], [0.18, 0.18, 0.18, 0.18, 0.18], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "2.5", "30", "0", "20/45/70/95/120", "0.08", "0.18", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViktorQ.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorW", "name": "Gravitationsfeld", "description": "<PERSON> be<PERSON>rt ein schweres Gravitationsfeld, das Gegner im Wirkbereich verlangsamt. <PERSON><PERSON><PERSON>, die zu lange in dem <PERSON>ld bleiben, werden betäubt.<br><br>Verstärkung: Viktors Fähigkeiten verlangsamen getroffene Gegner.<br>", "tooltip": "<PERSON> stellt {{ fieldduration }}&nbsp;Sekunden lang einen Gravitationskäfig auf, der Gegner im Inneren um {{ slowpotency*-1 }}&nbsp;% <status>verlangsamt</status>. <PERSON><PERSON><PERSON>, die sich über 1,25&nbsp;Se<PERSON>nden in seinem Bereich aufhalten, werden {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status>.<br /><br /><keywordMajor>Passiv (verstärkt):</keywordMajor> Viktor<PERSON> Fähig<PERSON>iten <status>verlangsamen</status> Gegner 1 Sekunde lang um {{ augmentslow }}&nbsp;%.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Verlangsamung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ slowpotency*-1.000000 }}&nbsp;% -> {{ slowpotencynl*-1.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ViktorW.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorE", "name": "Hextech-Strahl", "description": "<PERSON> benutzt seinen biomechanischen Arm, um einen Hextech-Strahl abzufeuern, der einer Linie folgt und getroffenen Gegnern Schaden zufügt.<br><br>Verstärkung: Auf den Hextech-Strahl folgt eine Explosion, die magischen Schaden verursacht.<br>", "tooltip": "<PERSON> feuert einen Hextech-Strahl in eine ausgewählte Richtung ab und fügt getroffenen Gegnern <magicDamage>{{ laserdamage }} magischen <PERSON>had<PERSON></magicDamage> zu.<br /><br /><keywordMajor>Augmentiert:</keywordMajor> Ein Nachbeben folgt dem Hextech-Strahl und verursacht <magicDamage>{{ aftershockdamage }} magischen Schaden</magicDamage>.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Sc<PERSON>en (Hextech-Strahl)", "Schaden (Nachbeben)", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamagelaser }} -> {{ basedamagelaserNL }}", "{{ basedamageaftershock }} -> {{ basedamageaftershockNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [20, 60, 100, 140, 180], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "20/60/100/140/180", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "ViktorE.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorR", "name": "Arkaner Sturm", "description": "<PERSON> erzeugt einen Arkanen Sturm auf dem Feld. Er verursacht damit magischen Schaden und unterbricht die Kanalisierung von Gegnern. Danach fügt der Sturm regelmäßig allen Gegnern in der Nähe magischen Schaden zu und Viktor kann seine Richtung ändern.<br><br>Verstärkung: Der Arkane Sturm bewegt sich 25&nbsp;% schneller und wenn Champions sterben, nachdem sie von ihm Schaden erlitten haben, wächst der Sturm und bleibt länger aktiv.<br><br>", "tooltip": "<PERSON> beschw<PERSON>rt {{ stormduration }}&nbsp;<PERSON>kunden lang einen Arkanen Sturm in einem Bereich, der Gegnern in der Nähe sofort <magicDamage>{{ initialburstdamage }}&nbsp;magischen Schaden</magicDamage> und im Anschluss <magicDamage>{{ subsequentburstdamage }}&nbsp;magischen Schaden</magicDamage> pro Sekunde zufügt. Der Sturm folgt automatisch Champions, denen er vor Kurzem Schaden zugefügt hat.<br /><br /><recast>Reaktivierung:</recast> Viktor kann den Sturm bewegen.<br /><br /><keywordMajor>Aufgewertet:</keywordMajor> Der Sturm bewegt sich {{ augmentboost*100 }}&nbsp;% schneller. Wenn ein <PERSON>, der von dem Sturm getroffen wurde, stirbt, erhöht der Sturm seine Größe und verlängert seine Dauer um {{ tooltip_durationextension }} Sekunden (bis zu {{ maxgrowths }}-mal).<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Schaden (Initialschaden)", "Schaden (regelmäßige Auslöser)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ initialburstbasedamage }} -> {{ initialburstbasedamageNL }}", "{{ subsequentburstbasedamage }} -> {{ subsequentburstbasedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "ViktorR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Glorreiche Evolution", "description": "<PERSON> erhält Hex-Fragmente, wenn er einen Gegner tötet. <PERSON><PERSON> jeweils 100 Hex-Fragmenten, die er erhält, verstärkt Viktor dauerhaft eine aktive Fähigkeit. Nachdem er alle seine Grundfähigkeiten verstärkt hat, kann er 100 Verhexungsfragmente sammeln, um seine ultimative Fähigkeit aufzuwerten.", "image": {"full": "Viktor_Passive.ViktorVGU.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}