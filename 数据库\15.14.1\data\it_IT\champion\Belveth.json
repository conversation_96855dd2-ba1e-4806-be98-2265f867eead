{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Belveth": {"id": "Belveth", "key": "200", "name": "Bel'Veth", "title": "l'imperatrice del Vuoto", "image": {"full": "Belveth.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "200000", "num": 0, "name": "default", "chromas": false}, {"id": "200001", "num": 1, "name": "Bel<PERSON>V<PERSON> Boss da Battaglia", "chromas": true}, {"id": "200010", "num": 10, "name": "Bel'Veth Matriarca Cosmica", "chromas": true}, {"id": "200019", "num": 19, "name": "Bel'Veth Primordiana", "chromas": true}], "lore": "Bel'Veth è un'imperatrice uscita da un incubo, creata dal materiale grezzo di un'intera città divorata, e costituisce la fine per tutta Runeterra... e l'inizio di una realtà mostruosa creata da lei. Spinta da epoche di storia, conoscenza e ricordi provenienti dal mondo di superficie e riadattati ai suoi scopi, Bel'Veth si nutre voracemente per saziare un bisogno sempre crescente di nuove esperienze ed emozioni, consumando tutto ciò che incontra sulla sua strada. Ma la sua brama non potrà mai essere appagata da un solo mondo, e il suo sguardo famelico inizia già a volgersi verso i vecchi padroni del Vuoto...", "blurb": "Bel'Veth è un'imperatrice uscita da un incubo, creata dal materiale grezzo di un'intera città divorata, e costituisce la fine per tutta Runeterra... e l'inizio di una realtà mostruosa creata da lei. Spinta da epoche di storia, conoscenza e ricordi...", "allytips": [], "enemytips": [], "tags": ["Fighter"], "partype": "", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 60, "mpperlevel": 0, "movespeed": 340, "armor": 32, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 1.5, "attackspeedperlevel": 0, "attackspeed": 0.85}, "spells": [{"id": "BelvethQ", "name": "Impulso del Vuoto", "description": "Bel'Veth scatta nella direzione scelta e danneggia tutti i nemici che attraversa.", "tooltip": "Bel'Veth scatta, infliggendo <physicalDamage>{{ basedamage }} danni fisici</physicalDamage> ai nemici che attraversa.<br /><br />Ogni direzione ha una ricarica unica di {{ f1 }} secondi che decresce con la <attackSpeed>velocità d'attacco</attackSpeed>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica per direzione", "<PERSON><PERSON> ai mostri", "<PERSON><PERSON> minion"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ persidecooldown }} -> {{ persidecooldownNL }}", "{{ monstermod }} -> {{ monstermodNL }}", "{{ minonmod*100.000000 }}% -> {{ minonmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethQ.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "BelvethW", "name": "Disorientamento", "description": "Bel'Veth colpisce il terreno con la coda, da<PERSON><PERSON><PERSON><PERSON>, lanciando in aria e rallentando i nemici.", "tooltip": "Bel'Veth colpisce il terreno con la coda, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage>, <status>lanciando in aria</status> i nemici per {{ duration }} secondi e <status>rallentandoli</status> del {{ slowpercent*100 }}% per {{ slowduration }} secondi. Se un campione viene colpito, ripristina la ricarica di <spellName>Impulso del Vuoto</spellName> in quella direzione.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> rallenta<PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [715, 715, 715, 715, 715], "rangeBurn": "715", "image": {"full": "BelvethW.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "BelvethE", "name": "Gorgo imperiale", "description": "Bel'Veth si ferma sul posto e crea attorno a sé un turbinio di fendenti che bersaglia il nemico con la salute più bassa e le conferisce rubavita e riduzione dei danni.", "tooltip": "Bel'Veth canalizza e colpisce attorno a sé e ottiene {{ drpercent*100 }}% riduzione dei danni, {{ totallifesteal }} di rubavita e attacca {{ f2.0 }} volte nell'arco di {{ totalduration }} secondi con un numero di attacchi che aumenta con la <attackSpeed>velocità d'attacco</attackSpeed>. Ogni attacco colpisce il nemico con meno salute, infliggendo da <physicalDamage>{{ damageperstrike }}</physicalDamage> a <physicalDamage>{{ maxdamageperstriketooltip }} danni fisici</physicalDamage> in base alla salute mancante del bersaglio.<br /><br />Utilizzare un'altra abilità o <recast>lanciarla di nuovo</recast> termina l'abilità in anticipo.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Riduzione danni", "Ricarica"], "effect": ["{{ damageperhit }} -> {{ damageperhitNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "BelvethE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "BelvethR", "name": "Banchetto infinito", "description": "Bel'Veth consuma i resti di corallo del Vuoto e assume la sua Vera forma, aumentando la salute massima, la gittata d'attacco, la velocità d'attacco e la velocità di movimento fuori dai combattimenti. Consumare i resti di corallo del Vuoto di un mostro epico del Vuoto prolunga la durata della sua suprema e le conferisce il potere di evocare remore del Vuoto.", "tooltip": "<spellPassive>Passiva:</spellPassive> ogni secondo attacco contro lo stesso bersaglio infligge <trueDamage>{{ finalonhitdamage }} danni puri</trueDamage> aggiuntivi, cumulabili all'infinito. Le eliminazioni contro campioni e mostri epici lasciano cadere un pezzo di Corallo del Vuoto.<br /><br /><spellActive>Attiva:</spellActive> consumare Corallo del Vuoto conferisce <keywordMajor>{{ passivestacksondevour }} carica Indaco</keywordMajor> e attiva la Vera forma di Bel'Veth per {{ steroidduration }} secondi. Il corallo del Vuoto da mostri epici del Vuoto aumenta la durata a {{ voidduration }} secondi e i minion che muoiono nelle sue vicinanze rinascono come remore del Vuoto. Quando lancia, Bel'Veth <status>rallenta</status> i nemici nelle vicinanze prima di esplodere, infliggendo <trueDamage>{{ totalexplosiondamage }} + {{ missinghealthdamage*100 }}% danni puri sulla salute mancante</trueDamage>.<br /><br />Nella sua Vera forma, Bel'Veth ottiene <healing>{{ maxhealthondevour }} salute massima</healing>, <speed>{{ oocms }} velocità di movimento</speed> fuori dal combattimento, {{ bonusaarange }} gittata d'attacco, <attackSpeed>{{ totalasmod*100 }}% velocità d'attacco totale</attackSpeed> e <spellName>Impulso del Vuoto</spellName> può attraversare i muri.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> sul colpo", "<PERSON><PERSON> da esplosione", "Salute bonus", "Velocità di movimento", "Velocità d'attacco", "Remore del Vuoto - Salute"], "effect": ["{{ onhitdamage }} -> {{ onhitdamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealth }} -> {{ basemaxhealthNL }}", "{{ oocms }} -> {{ oocmsNL }}", "{{ totalasmod*100.000000 }}% -> {{ totalasmodnl*100.000000 }}%", "{{ voidlinghpscale*100.000000 }}% -> {{ voidlinghpscalenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Morte indaco ", "description": "Bel'Veth ottiene cariche di velocità d'attacco permanente dopo aver abbattuto campioni, minion e mostri grandi. Ottiene anche velocità d'attacco bonus temporanea dopo aver usato un'abilità.", "image": {"full": "Belveth_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}