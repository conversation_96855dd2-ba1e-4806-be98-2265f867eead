{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "Ю<PERSON>и", "title": "Волшебная кошка", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ю<PERSON>и", "chromas": true}, {"id": "350011", "num": 11, "name": "Сердцеедка Юми", "chromas": true}, {"id": "350019", "num": 19, "name": "Жужжуми", "chromas": true}, {"id": "350028", "num": 28, "name": "Ведьма Юми", "chromas": true}, {"id": "350037", "num": 37, "name": "EDG Юми", "chromas": true}, {"id": "350039", "num": 39, "name": "Сиба-Юми", "chromas": true}, {"id": "350049", "num": 49, "name": "Киберкошка Юми", "chromas": true}, {"id": "350050", "num": 50, "name": "Киберкошка Юми (престижный)", "chromas": false}, {"id": "350061", "num": 61, "name": "Предвестница ночи Юми", "chromas": true}], "lore": "Юми – волшебная кошка из Бандл Сити, фамильяр йордла-чародейки по имени Норра. Когда та таинственно исчезла, Юми стала хранительницей разумной Книги Пределов. Она путешествует сквозь страницы-порталы, пытаясь разыскать Норру. Без хозяйки Юми бывает одиноко, поэтому она ищет себе спутников и яростно защищает их сияющими магическими щитами. Волшебная кошка очень ценит простые жизненные радости – например, дневной сон и свежую рыбку. Книга пытается ее воспитывать, но на самом деле это не нужно, ведь Юми никогда не забывает о главной цели: вернуть свою подругу.", "blurb": "Юми – волшебная кошка из Бандл Сити, фамильяр йордла-чародейки по имени Норра. Когда та таинственно исчезла, Юми стала хранительницей разумной Книги Пределов. Она путешествует сквозь страницы-порталы, пытаясь разыскать Норру. Без хозяйки Юми бывает...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "Мана", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "Кошки-мышки", "description": "Юми выпускает заряд, который наносит урон первому пораженному врагу и замедляет его. Если заряд летит хотя бы 1.35 сек., он наносит больше урона и сильнее замедляет цель. Когда Юми привязана к лучшему другу, ее замедление всегда усилено, а партнер наносит дополнительный урон при попадании.<br><br>Если Юми привязана, вы можете в течение короткого времени управлять зарядом с помощью мыши.", "tooltip": "Юми выпускает блуждающий заряд, который наносит <magicDamage>{{ totalmissiledamage }} магического урона</magicDamage> первому пораженному врагу и <status>замедляет</status> его на {{ slowamount }}%.<br /><br />Если Юми <keywordMajor>привязана</keywordMajor>, вы можете в течение короткого времени управлять зарядом с помощью мыши, после чего он ускоряется и летит по прямой. Ускоренный заряд наносит <magicDamage>{{ totalmissiledamageempowered }} магического урона</magicDamage> и <status>замедляет</status> цель на {{ empoweredslowamount }}% на {{ empoweredslowduration }} сек.<br /><br /><keywordMajor>Бонус лучшего друга:</keywordMajor> <status>замедление</status> от умения <spellName>Кошки-мышки</spellName> всегда усилено. После поражения вражеского чемпиона партнер Юми наносит <magicDamage>{{ onhitdamagecalc }} магического урона</magicDamage> <OnHit>при попадании %i:OnHit%</OnHit> в течение {{ buffduration }} сек.<br /><br /><rules>Дополнительный урон при попадании может увеличиться на величину вплоть до {{ allycritchancemaxamp*100 }}% в зависимости от шанса критического удара партнера Юми.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Затраты маны", "Базовый урон", "Урон усиленного умения", "Фиксированное замедление усиленного умения", "Урон при попадании"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "{{ empoweredslowamount }}% -> {{ empoweredslowamountNL }}%", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "YuumiW", "name": "Ты и я!", "description": "Юми совершает рывок к выбранному союзнику, после чего становится недосягаемой для всех врагов, кроме башен. Когда Юми привязана к лучшему другу, ее эффективность лечения и щитов увеличена, а партнер лечится, когда попадает по врагам.", "tooltip": "<spellPassive>Пассивно:</spellPassive> когда Юми привязана к <keywordMajor>лучшему другу</keywordMajor>, <keywordMajor>эффективность ее лечения и щитов</keywordMajor> увеличена на <keywordMajor>{{ healandshieldpower*100 }}%</keywordMajor>, а ее партнер восстанавливает <healing>{{ healthonhit }} здоровья</healing> <OnHit>при попадании %i:OnHit%</OnHit>.<br /><br /><spellActive>Активно:</spellActive> Юми совершает рывок к союзному чемпиону и <keywordMajor>привязывается</keywordMajor> к нему. Пока Юми <keywordMajor>привязана</keywordMajor>, она следует за своим партнером. При этом она недосягаема для всех врагов, кроме башен.<br /><br />Когда на Юми накладывается эффект <status>обездвиживания</status>, это умение уходит на перезарядку в {{ ccattachlockout }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Лечение при попадании", "Доп. эффективность лечения и щитов"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "YuumiE", "name": "Догонялки", "description": "Юми получа<PERSON>т щит, а также увеличивает свои скорость передвижения и скорость атаки. Если она привязана, эти эффекты действуют на ее партнера, а не на нее.<br>", "tooltip": "Юми получает <shield>щит прочностью {{ totalshielding }}</shield> и <attackSpeed>{{ totalattackspeed }}% скорости атаки</attackSpeed> на {{ msduration }} сек. Пока держится щит, цель также получает <speed>{{ msamount }}% скорости передвижения</speed>.<br /><br />Если Юми <keywordMajor>привязана</keywordMajor>, эффекты этого умения действуют на ее партнера, а не на нее. Кроме того, союзник восстанавливает себе <magicDamage>{{ manarestore }} маны</magicDamage>. Это значение может увеличиться на величину вплоть до {{ maxmanapercincrease*100 }}% в зависимости от недостающей маны цели.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Прочность щита", "Затраты маны", "Восстановление маны", "Скорость атаки"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "YuumiR", "name": "Последняя глава", "description": "Юми выпускает пять волн, которые наносят урон врагам и лечат союзников. Во время применения умения Юми может перемещаться, привязываться к чемпионам и использовать Догонялки. Когда Юми привязана к лучшему другу, направление волн можно менять с помощью мыши.", "tooltip": "В течение {{ ultduration }} сек. Юми выпускает волшебные волны ({{ numberofwaves }}), которые влияют на обе команды. Если умение применяется, когда она <keywordMajor>привязана</keywordMajor>, направление волн можно менять с помощью мыши.<br /><br />Пораженные враги получают <magicDamage>{{ totalmissiledamage }} ед. магического урона</magicDamage> и <status>замедляются</status> на {{ baseslow*-100 }}% на {{ ccduration }} сек. (эффективность увеличивается на {{ bonusslowperwave*-100 }}% за каждую попавшую волну).<br /><br />Союзные чемпионы восстанавливают себе <healing>{{ totalhealperwave }} здоровья</healing> за волну. Избыточное лечение превращается в <shield>щит</shield>.<br /><br /><keywordMajor>Бонус лучшего друга:</keywordMajor> эффективность лечения <keywordMajor>лучшего друга</keywordMajor> увеличена до <healing>{{ enhancedhealperwave }}</healing>.<br /><br /><rules>При применении умения <spellName>Ты и я!</spellName> направление волн фиксируется.<br />Во время применения умения Юми может перемещаться и использовать <spellName>Догонялки</spellName>.</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Базовый урон за заряд", "Базовое лечение за волну"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Кошачья дружба", "description": "Периодически, когда Юми поражает чемпиона автоатакой или умением, она восстанавливает здоровье себе и следующему союзнику, к которому привяжется.<br><br>Привязываясь к союзникам, Ю<PERSON>и устанавливает с ними особую связь. Союзник с самой крепкой связью усиливает ее умения, когда она к нему привязана.", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}