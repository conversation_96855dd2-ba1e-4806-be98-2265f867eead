{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viego": {"id": "Viego", "key": "234", "name": "Viego", "title": "Il Re in rovina", "image": {"full": "Viego.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "234000", "num": 0, "name": "default", "chromas": false}, {"id": "234001", "num": 1, "name": "Viego Belva lunare", "chromas": true}, {"id": "234010", "num": 10, "name": "Viego Dissonanza dei Pentakill", "chromas": false}, {"id": "234019", "num": 19, "name": "Viego EDG", "chromas": false}, {"id": "234021", "num": 21, "name": "Re Viego", "chromas": false}, {"id": "234030", "num": 30, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "234037", "num": 37, "name": "Viego Mondiali 2024", "chromas": false}], "lore": "Un tempo sovrano di un regno dimenticato, Viego morì più di mille anni fa, quando il suo tentativo di riportare in vita la moglie scatenò la catastrofe magica nota come Rovina. Si è trasformato in un potente spettro non morto, torturato dall'ossessione per la sua regina da secoli passata a miglior vita. Viego è il Re in rovina, che controlla la letale Mietitura e razzia Runeterra in cerca di qualcosa che un giorno possa restituirgli la sua amata, distruggendo tutto ciò che incontra, mentre la letale Nebbia Oscura sgorga copiosa dal suo cuore spezzato.", "blurb": "Un tempo sovrano di un regno dimenticato, Viego morì più di mille anni fa, quando il suo tentativo di riportare in vita la moglie scatenò la catastrofe magica nota come Rovina. Si è trasformato in un potente spettro non morto, torturato dall'ossessione...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 10000, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 200, "hpregen": 7, "hpregenperlevel": 0.7, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "ViegoQ", "name": "Lama del re in rovina", "description": "La lama spettrale di Viego infligge passivamente <OnHit>sul colpo</OnHit> danni bonus pari a una percentuale della salute attuale e colpisce due volte i nemici che Viego ha colpito di recente con un'abilità, sottraendo salute.<br><br>Attivando questa abilità Viego esegue un affondo con lo spadone, impalando i nemici di fronte a sé.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi di Viego infliggono un <physicalDamage>{{ totalpercenthealthonhit }} della salute attuale in danni fisici</physicalDamage> aggiuntivi. Il suo primo attacco base contro un nemico che ha da poco danneggiato con un'abilità colpisce una seconda volta, infliggendo <physicalDamage>{{ secondattackdamage }} danni fisici</physicalDamage> e ripristinando un <healing>{{ healmodvschamps*100 }}% dei danni inflitti come salute.</healing> Questi bonus permangono durante la <keywordMajor>possessione</keywordMajor>.<br /><br /><spellActive>Attiva:</spellActive> Viego affonda l'arma, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "% salute attuale", "<PERSON><PERSON> salute minimi attuali", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ percenthealthonhit }}% -> {{ percenthealthonhitNL }}%", "{{ mindamageon<PERSON> }} -> {{ mindamageonhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5, 4.5, 4, 3.5, 3], "cooldownBurn": "5/4.5/4/3.5/3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViegoQ.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "ViegoW", "name": "<PERSON><PERSON><PERSON> s<PERSON>i", "description": "Viego si carica e scatta in avanti, sprigionando una sfera di Nebbia Oscura concentrata che stordisce il primo nemico colpito.", "tooltip": "<charge>Inizio caricamento:</charge> Viego inizia a concentrare la nebbia, <status>rallentandosi</status> del {{ selfslowpercent*100 }}%.<br /><br /><release>Rila<PERSON>cio:</release> Viego scatta in avanti e scaglia la nebbia concentrata, che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> al primo nemico colpito e lo <status>stordisce</status> da {{ stunduration }} a {{ maxstuntt }} secondi in base al tempo di carica.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViegoW.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "ViegoE", "name": "Sentiero della mietitura", "description": "Viego ordina alla Nebbia Oscura di infestare e circondare un'area di terreno. Viego può nascondersi nella nebbia come uno spettro, guadagnando mimesi, velocità di movimento e velocità d'attacco.", "tooltip": "Viego manda uno spettro a infestare la prima area di terreno colpita, ricoprendola di nebbia per {{ mistduration }} secondi. Mentre è al suo interno, Vie<PERSON> ottiene <keywordStealth>mimesi</keywordStealth>, <speed>{{ totalmovespeed }} velocità di movimento</speed> e <attackSpeed>{{ attackspeed*100 }}% velocità d'attacco</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Velocità d'attacco", "Ricarica"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViegoE.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "ViegoR", "name": "<PERSON>endetta lacerante", "description": "Viego si teletrasporta in un punto vicino e giustizia un campione al suo arrivo, trapassandogli il cuore e generando un'onda d'urto che spinge via i suoi alleati.", "tooltip": "Viego libera qualsiasi anima stia <keywordMajor>possedendo</keywordMajor> e si teletrasporta. Al suo arrivo attacca il campione con la percentuale di salute minore, <status>rallentandolo</status> brevemente del {{ slowpercent*100 }}% e infliggendogli <physicalDamage>{{ totaldamage }} (+{{ totalpercenthealth }}% salute mancante) danni fisici</physicalDamage>. Gli altri nemici vicini vengono <status>respinti</status> e subiscono <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> salute mancante", "Ricarica"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "ViegoR.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Dominio spirituale", "description": "I nemici che muoiono davanti a Viego diventano spettri. Attaccando uno spettro Viego può prendere temporaneamente il controllo del cadavere del nemico, curandosi per una percentuale della salute massima del bersaglio e ottenendo accesso ai suoi oggetti e alle sue abilità di base. La suprema del bersaglio viene sostituita con un lancio gratuito della suprema di Viego.", "image": {"full": "Viego_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}