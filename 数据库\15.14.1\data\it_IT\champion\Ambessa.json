{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ambessa": {"id": "<PERSON><PERSON><PERSON>", "key": "799", "name": "<PERSON><PERSON><PERSON>", "title": "Matriarca della guerra", "image": {"full": "Ambessa.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "799000", "num": 0, "name": "default", "chromas": false}, {"id": "799001", "num": 1, "name": "Ambessa Prescelta del Lupo", "chromas": true}], "lore": "Chiunque conosca il nome dei Medarda rispetta e teme quello della capofamiglia, Ambessa. In quanto generale di Noxus, incarna una letale combinazione di forza spietata e indomita risolutezza in battaglia. In quanto capo famiglia, usa la sua notevole astuzia per elevare i Medarda senza lasciar spazio a fallimenti o compassione. Ambessa ha fatto sua la crudele via del Lupo, ed è pronta a tutto pur di proteggere il retaggio della sua stirpe... anche a costo dell'amore dei suoi figli.", "blurb": "Chiunque conosca il nome dei Medarda rispetta e teme quello della capofamiglia, Ambessa. In quanto generale di Noxus, incarna una letale combinazione di forza spietata e indomita risolutezza in battaglia. In quanto capo famiglia, usa la sua notevole...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Energia", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 10}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 200, "mpperlevel": 0, "movespeed": 335, "armor": 35, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "AmbessaQ", "name": "Spazzata tattica / Divide et impera", "description": "Ambessa effettua una spazzata in un semicerchio di fronte a sé con i suoi mastini draconici gemelli, infliggendo danni bonus ai nemici colpiti dalle lame. Colpire un nemico trasforma il prossimo lancio di questa abilità per un breve periodo, consentendole di colpire con i mastini draconici in una linea davanti a sé, infliggendo danni bonus al primo nemico colpito.", "tooltip": "<spellActive>Spazzata tattica</spellActive>: Ambessa effettua una spazzata di fronte a sé con le sue armi, infliggendo <physicalDamage>{{ calc_damage_1_max }} + {{ calc_damage_1_percent_max }} di danni fisici in base alla salute massima</physicalDamage> dei nemici ai margini della gittata del colpo. Tutti gli altri nemici subiscono {{ calc_damage_1_min_ratio }} danni. Colpendo un nemico prepara <spellActive>Divide et impera</spellActive>.<br /><br /><spellActive>Divide et impera</spellActive>: Ambessa affonda le sue lame, infliggendo <physicalDamage>{{ calc_damage_2_max }} + {{ calc_damage_2_percent_max }} di danni fisici in base alla salute massima</physicalDamage> al primo nemico colpito. Tutti gli altri nemici subiscono {{ calc_damage_2_min_ratio }} danni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Spazzata tattica | Danni", "Spazzata tattica | <PERSON><PERSON> salute massima", "Divide et impera | <PERSON><PERSON>", "Divide et impera | <PERSON><PERSON> salute massima"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage_1_base }} -> {{ damage_1_baseNL }}", "{{ damage_1_percent*100.000000 }}% -> {{ damage_1_percentnl*100.000000 }}%", "{{ damage_2_base }} -> {{ damage_2_baseNL }}", "{{ damage_1_percent*100.000000 }}% -> {{ damage_1_percentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "AmbessaQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaW", "name": "Ritorsione", "description": "Ambessa ottiene uno scudo, si prepara per un istante e colpisce il terreno per infliggere danni ai nemici nelle vicinanze. Se durante la preparazione riesce a bloccare danni non provenienti da minion, l'abilità infligge danni aumentati.", "tooltip": "Ambessa ottiene uno scudo da <shield>{{ calc_shield }}</shield> per {{ shield_duration }} secondi e si prepara per {{ buff_duration }} secondi. Poi colpisce il terreno, infliggendo <physicalDamage>{{ calc_damage_low }} danni fisici</physicalDamage> ai nemici vicini, che aumentano a <physicalDamage>{{ calc_damage_high }} danni fisici</physicalDamage> se si protegge dai danni di un campione nemico, un mostro grande o una struttura.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaE", "name": "Straziare", "description": "Ambessa fa roteare i suoi mastini draconici gemelli attorno a sé, infliggendo danni e rallentando i nemici nelle vicinanze. Attivare Passo del mastino da questa abilità le consente di colpire una seconda volta al termine dello scatto.", "tooltip": "Ambessa fa roteare le sue catene, infliggendo <physicalDamage>{{ calc_damage_flat }} danni fisici</physicalDamage> e <status>rallentando</status> i nemici di un <status>{{ slow_amount*100 }}%</status>, che decresce nell'arco di {{ slow_duration }} secondo/i. Attivare <spellName>Passo del mastino</spellName> da questa abilità consente di sferrare un colpo aggiuntivo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Rapporto attacco fisico"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage_flat_base }} -> {{ damage_flat_baseNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaE.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaR", "name": "Esecuzione pubblica", "description": "Ambessa si teletrasporta accanto al campione nemico più lontano in una linea retta a sua scelta e lo sopprime al suo arrivo. Successivamente, sbatte il nemico a terra, infliggendogli danni e stordendolo.", "tooltip": "<spellPassive>Passiva</spellPassive>: Ambes<PERSON> ottiene <armorPen>{{ armor_penetration*100 }}% %i:scaleAPen% penetrazione armatura</armorPen> e le sue abilità <healing>la curano per un {{ calc_omnivamp }} dei danni che ha inflitto</healing>.<br /><br /><spellActive>Attiva</spellActive>: Am<PERSON><PERSON> diventa <attention>Inarrestabile</attention> e si teletrasporta accanto al campione nemico più lontano in una linea retta, <status>sopprimendolo</status> per {{ suppress_duration }} secondi e sbattendolo poi a terra, infliggendo <physicalDamage>{{ calc_damage }} danni fisici</physicalDamage> e <status>stordendolo</status> per {{ stun_duration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Penetrazione armatura", "Guarigione", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ armor_penetration*100.000000 }}% -> {{ armor_penetrationnl*100.000000 }}%", "{{ omnivamp*100.000000 }}% -> {{ omnivampnl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AmbessaR.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Passo del mastino", "description": "Inserire un comando di attacco o di movimento durante il lancio di un'abilità fa scattare Ambessa per una breve distanza quando l'abilità viene lanciata, conferendo inoltre al suo attacco successivo gittata, danni e velocità d'attacco bonus, e rimborsando energia.", "image": {"full": "Icon_Ambessa_Passive.Domina.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}