{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lucian": {"id": "Lucian", "key": "236", "name": "Lucian", "title": "il purificatore", "image": {"full": "Lucian.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "236000", "num": 0, "name": "default", "chromas": true}, {"id": "236001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "236002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "236006", "num": 6, "name": "PROGETTO: <PERSON>", "chromas": true}, {"id": "236007", "num": 7, "name": "<PERSON>", "chromas": false}, {"id": "236008", "num": 8, "name": "<PERSON> Fu<PERSON>o", "chromas": true}, {"id": "236009", "num": 9, "name": "<PERSON>", "chromas": true}, {"id": "236018", "num": 18, "name": "<PERSON>", "chromas": false}, {"id": "236019", "num": 19, "name": "<PERSON> (edizione prestigio)", "chromas": false}, {"id": "236025", "num": 25, "name": "<PERSON>", "chromas": true}, {"id": "236031", "num": 31, "name": "<PERSON>", "chromas": true}, {"id": "236040", "num": 40, "name": "<PERSON>'<PERSON>to", "chromas": true}, {"id": "236052", "num": 52, "name": "Lucian <PERSON> dell'Inverno", "chromas": true}, {"id": "236062", "num": 62, "name": "<PERSON> mascherata", "chromas": true}], "lore": "<PERSON>, una <PERSON><PERSON> della Luce, è un implacabile cacciatore di spiriti e fantasmi, che insegue e annienta con le sue pistole gemelle. Ha intrapreso la strada della vendetta quando il malvagio Thresh ha ucciso sua moglie, ma la sua furia non si è spenta neppure quando questa è tornata in vita. <PERSON><PERSON>tat<PERSON> e determinato, <PERSON> non si fermerà davanti a nulla pur di proteggere i vivi dagli orrori sepolcrali della Nebbia Oscura.", "blurb": "<PERSON>, una Sentinel<PERSON> della Luce, è un implacabile cacciatore di spiriti e fantasmi, che insegue e annienta con le sue pistole gemelle. Ha intrapreso la strada della vendetta quando il malvagio Thresh ha ucciso sua moglie, ma la sua furia non si è...", "allytips": ["Per una raffica perfetta, combina Determinazione e Luce perforante.", "Bagliore ardente esplode con uno schema a stella. Cerca di allinearlo in modo che i raggi colpiscano i campioni nemici.", "Dopo che hai scelto un'angolazione per Sterminio non puoi cambiarla. Scegli bene il momento!", "Grazie a <PERSON>o illuminato, <PERSON> trae più beneficio dall'attacco fisico che dalla velocità d'attacco."], "enemytips": ["<PERSON> è forte nelle raffiche, ma non nei danni sostenuti.", "Lucian non può cambiare la mira di Sterminio. Sfrutta questo dato per evitare la traiettoria dei proiettili.", "Luce perforante non dà a Lucian gittata d'attacco aggiuntiva. Deve comunque trovare un bersaglio a portata, per sparare. Evita Luce perforante prevedendo l'angolazione che sceglierà."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 641, "hpperlevel": 100, "mp": 320, "mpperlevel": 43, "movespeed": 335, "armor": 28, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.9, "attackspeedperlevel": 3.3, "attackspeed": 0.638}, "spells": [{"id": "LucianQ", "name": "<PERSON> perforante", "description": "<PERSON> spara un raggio di luce perforante attraverso un bersaglio.", "tooltip": "<PERSON> spara un raggio di luce perforante che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Attacco fisico", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [48, 56, 64, 72, 80], "costBurn": "48/56/64/72/80", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [60, 75, 90, 105, 120], [1000, 1000, 1000, 1000, 1000], [0.41, 0.41, 0.41, 0.41, 0.41], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "60/75/90/105/120", "1000", "0.41", "100", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "LucianQ.png", "sprite": "spell7.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianW", "name": "<PERSON><PERSON>ore a<PERSON>", "description": "Lucian spara un proiettile che esplode con uno schema a stella, march<PERSON><PERSON> e rivelando i nemici per un breve periodo. Attaccando i nemici marchiati Lucian guadagna velocità di movimento.", "tooltip": "<PERSON> spara un colpo che esplode una volta raggiunta la sua massima gittata o al primo nemico colpito, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>, rivelando gli avversari per un breve periodo e marchiandoli per 6 secondi.<br /><br />Quando lui o i suoi alleati infliggono danni a un bersaglio marchiato, <PERSON> otti<PERSON> <speed>{{ e2 }} velocità di movimento</speed> per 1 secondo. Anche gli alleati che attivano questo effetto conferiscono <attention>Vigilanza</attention> a Lucian.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [60, 65, 70, 75, 80], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [700, 700, 700, 700, 700]], "effectBurn": [null, "75/110/145/180/215", "60/65/70/75/80", "900", "0", "1", "200", "1", "6", "1", "700"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "LucianW.png", "sprite": "spell7.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianE", "name": "Determinazione", "description": "Lucian scatta rapidamente per una breve distanza. Gli attacchi di Pistolero illuminato riducono la ricarica di Determinazione.", "tooltip": "<PERSON> effettua uno scatto.<br /><br />Ogni volta che Lucian colpisce un nemico con <spellName>Pistolero illuminato</spellName>, la ricarica di questa abilità è ridotta di {{ e1 }} secondo ({{ e2 }} secondi per i campioni). {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [425, 425, 425, 425, 425], [200, 200, 200, 200, 200], [1350, 1350, 1350, 1350, 1350], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1", "2", "425", "200", "1350", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [445, 445, 445, 445, 445], "rangeBurn": "445", "image": {"full": "LucianE.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LucianR", "name": "Sterminio", "description": "Lucian scatena una pioggia di colpi con le sue armi.", "tooltip": "Lucian spara rapidamente <keywordMajor>{{ totalnumshots }}</keywordMajor> colpi in una direzione per {{ duration }} secondi o finché non <recast>rilancia</recast> questa abilità. Ciascun colpo infligge <physicalDamage>{{ damageperbullet }} danni fisici</physicalDamage> al primo nemico colpito.<br /><br />Lucian può usare <spellName>Determinazione</spellName> mentre spara.<br /><br />Danni totali: <physicalDamage>{{ totaldamage }} danni fisici.</physicalDamage><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1400, 1400, 1400], "rangeBurn": "1400", "image": {"full": "LucianR.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pistolero illuminato", "description": "Ogni volta che Lucian usa un'abilità, il suo attacco successivo diventa un doppio colpo. Quando Lucian è curato o protetto con uno scudo da un alleato, o quando un campione nemico vicino è immobilizzato, i suoi successivi due attacchi base infliggono danni magici bonus.", "image": {"full": "Lucian_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}