{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Janna": {"id": "<PERSON><PERSON>", "key": "40", "name": "ジャ<PERSON>ナ", "title": "嵐の怒り", "image": {"full": "Janna.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "40000", "num": 0, "name": "default", "chromas": false}, {"id": "40001", "num": 1, "name": "嵐の化身ジャンナ", "chromas": false}, {"id": "40002", "num": 2, "name": "ヘクステック ジャンナ", "chromas": false}, {"id": "40003", "num": 3, "name": "雪の女王ジャンナ", "chromas": false}, {"id": "40004", "num": 4, "name": "勝利の栄光ジャンナ", "chromas": false}, {"id": "40005", "num": 5, "name": "お天気お姉さんジャンナ", "chromas": false}, {"id": "40006", "num": 6, "name": "Fnatic ジャンナ", "chromas": false}, {"id": "40007", "num": 7, "name": "スターガーディアン ジャンナ", "chromas": false}, {"id": "40008", "num": 8, "name": "神聖の剣ジャンナ", "chromas": true}, {"id": "40013", "num": 13, "name": "魅惑の魔女ジャンナ", "chromas": true}, {"id": "40020", "num": 20, "name": "砂漠の守護者ジャンナ", "chromas": true}, {"id": "40027", "num": 27, "name": "バトルクイーン ジャンナ", "chromas": true}, {"id": "40036", "num": 36, "name": "水晶の薔薇ジャンナ", "chromas": true}, {"id": "40045", "num": 45, "name": "電脳機光ジャンナ", "chromas": true}, {"id": "40046", "num": 46, "name": "プレステージ電脳機光ジャンナ", "chromas": false}, {"id": "40056", "num": 56, "name": "天なる龍ジャンナ", "chromas": true}, {"id": "40066", "num": 66, "name": "秩序の光ジャンナ", "chromas": false}], "lore": "ルーンテラの嵐を操るジャンナは、謎めいた風の精霊として寄る辺ないゾウンの人々を守っている。彼女は、大嵐に遭う危険も顧みず海に出るルーンテラの船乗りたちが、無事に航海できるよう乞い願う気持ちから生まれたとも言われる。ジャンナの恵みと庇護はゾウンの奥深くにまで届き、救いを求める人々に希望の光を与える。いつ、どこに現れるかは誰にもわからないが、彼女は窮地に陥った者に手を差し伸べてくれるのだ。", "blurb": "ルーンテラの嵐を操るジャンナは、謎めいた風の精霊として寄る辺ないゾウンの人々を守っている。彼女は、大嵐に遭う危険も顧みず海に出るルーンテラの船乗りたちが、無事に航海できるよう乞い願う気持ちから生まれたとも言われる。ジャンナの恵みと庇護はゾウンの奥深くにまで届き、救いを求める人々に希望の光を与える。いつ、どこに現れるかは誰にもわからないが、彼女は窮地に陥った者に手を差し伸べてくれるのだ。", "allytips": ["「ストームブレス」は、味方のタワーにも使用できる。", "「ハウリングゲイル」をチャージせず即座に発動することで、相手チームの出足をくじける。", "ジャンナのアルティメットスキルを適切なタイミングで使えば、負傷した味方を回復しながら敵を遠ざけたり、敵の連携を分断できる。"], "enemytips": ["ジャンナのアルティメットスキルを妨害できるスキルがある場合は、ジャンナがアルティメットスキルを使用するまで温存しておこう。", "「ハウリングゲイル」のチャージ音を聞き逃さないこと。画面外や茂みの中からでも、チャンピオンを攻撃してくる。", "ジャンナの「ストームブレス」は厄介だ。「ストームブレス」のシールドが消えてから戦うことで、彼女の力を発揮させないようにしよう。"], "tags": ["Support", "Mage"], "partype": "マナ", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 50, "movespeed": 325, "armor": 28, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON>G<PERSON>", "name": "ハウリングゲイル", "description": "大気を操り、小さな竜巻を生み出す。竜巻は時間とともに成長し、時間がたつか再度このスキルを使うことで指定方向へ直進して、進路上の敵ユニットにダメージとノックアップを与える。", "tooltip": "竜巻を召喚する。竜巻は{{ maxduration }}秒かけて成長し、その後進路上にいる敵に<magicDamage>{{ minimumdamage }} - {{ maxdamage }}の魔法ダメージ</magicDamage>を与えて、{{ baseknockup }} - {{ maxknockup }}秒間<status>ノックアップ</status>させる。移動距離、ダメージ、<status>ノックアップ</status>時間は竜巻の成長に応じて増加する。<recast>再発動</recast>すると、即座に竜巻を送り出せる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "チャージ時毎秒増加ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "HowlingGale.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SowTheWind", "name": "ゼファー", "description": "風の精霊を召喚し、自動効果として移動速度が増加すると同時に、ユニットをすり抜けるようになる。発動すると対象に風の精霊を飛ばして、ダメージとスロウ効果を与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> <speed>移動速度が{{ totalms }}</speed>増加し、ゴースト化する。<br /><br /><spellActive>発動効果:</spellActive> エレメントの力で敵を攻撃して{{ slowduration }}秒間{{ totalslow }}の<status>スロウ効果</status>を付与し、<magicDamage>{{ totaldamage }} + {{ spell.tailwindself:bonusdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "自動効果による増加移動速度", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ mspercent*100.000000 }}% -> {{ mspercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [4294967295, 4294967295, 4294967295, 4294967295, 4294967295], "rangeBurn": "4294967295", "image": {"full": "SowTheWind.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "EyeOfTheStorm", "name": "ストームブレス", "description": "対象に魔法の風を呼び寄せる。風はシールドとなって味方チャンピオンやタワーをダメージから守ると同時に、攻撃力を増加させる。", "tooltip": "味方のチャンピオンまたはタワーに{{ shieldduration }}秒間、<shield>耐久値{{ totalshield }}のシールド</shield>を付与する。シールド展開中の対象は<scaleAD>攻撃力が{{ totalad }}</scaleAD>増加する。<br /><br />敵チャンピオンにスキルで移動妨害効果を与えるたびにクールダウンが{{ ecdrefundforcc*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "攻撃力", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ bonusad }} -> {{ bonusadNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EyeOfTheStorm.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ReapTheWhirlwind", "name": "モンスーン", "description": "魔法の嵐に包まれ、周囲の敵を吹き飛ばす。その後詠唱を続け、癒しの風によって範囲内の味方の体力を回復する。", "tooltip": "魔法の嵐を召喚して、周囲の敵を<status>ノックバック</status>させ、{{ e3 }}秒かけて周囲の味方の<healing>体力を{{ totalheal }}</healing>回復する。移動するかスキルを使用すると嵐が早めに終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒体力回復量", "クールダウン"], "effect": ["{{ healbasepersecond }} -> {{ healbasepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [300, 450, 600], [3, 3, 3], [700, 700, 700], [875, 875, 875], [875, 875, 875], [1200, 1200, 1200], [10, 10, 10], [0.5, 0.5, 0.5], [0, 0, 0]], "effectBurn": [null, "0", "300/450/600", "3", "700", "875", "875", "1200", "10", "0.5", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725], "rangeBurn": "725", "image": {"full": "ReapTheWhirlwind.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "テイルウィンド", "description": "ジャンナに向かって移動する際、味方チャンピオンは移動速度が増加する。<br><br>通常攻撃時効果および「ゼファー」で、増加移動速度の一定割合にあたる追加魔法ダメージを与える。", "image": {"full": "Janna_Tailwind.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}