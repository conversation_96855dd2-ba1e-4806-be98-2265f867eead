{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renekton": {"id": "Renekton", "key": "58", "name": "레넥톤", "title": "사막의 도살자", "image": {"full": "Renekton.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "58000", "num": 0, "name": "default", "chromas": false}, {"id": "58001", "num": 1, "name": "은하계 레넥톤", "chromas": false}, {"id": "58002", "num": 2, "name": "아웃백 레넥톤", "chromas": false}, {"id": "58003", "num": 3, "name": "핏빛 분노 레넥톤", "chromas": false}, {"id": "58004", "num": 4, "name": "룬 전쟁 레넥톤", "chromas": false}, {"id": "58005", "num": 5, "name": "용광로 레넥톤", "chromas": false}, {"id": "58006", "num": 6, "name": "수영장 파티 레넥톤", "chromas": false}, {"id": "58007", "num": 7, "name": "선사시대 레넥톤", "chromas": false}, {"id": "58008", "num": 8, "name": "SKT T1 레넥톤", "chromas": false}, {"id": "58009", "num": 9, "name": "레넥토이", "chromas": true}, {"id": "58017", "num": 17, "name": "마법공학 레넥톤", "chromas": false}, {"id": "58018", "num": 18, "name": "어둠서리 레넥톤", "chromas": true}, {"id": "58026", "num": 26, "name": "프로젝트: 레넥톤", "chromas": true}, {"id": "58033", "num": 33, "name": "빛의 인도자 레넥톤", "chromas": true}, {"id": "58042", "num": 42, "name": "2023 월드 챔피언십 레넥톤", "chromas": true}, {"id": "58048", "num": 48, "name": "먹그림자 레넥톤", "chromas": true}], "lore": "불길에 그을린 슈리마 사막에서 다시 일어선 무시무시한 분노의 초월체, 레넥톤. 한 때 그는 슈리마 최고의 전사로서 무수한 전쟁을 승리로 이끌었다. 하지만 슈리마의 몰락과 함께 사막 아래 무덤 속에 갇혔고, 강산이 변하는 억겁의 세월을 어둠 속에서 보내면서 서서히 광기에 굴복해 갔다. 다시 자유의 몸이 된 레넥톤이 원하는 것은 단 하나, 자신을 가둔 형에 대한 복수뿐이다.", "blurb": "불길에 그을린 슈리마 사막에서 다시 일어선 무시무시한 분노의 초월체, 레넥톤. 한 때 그는 슈리마 최고의 전사로서 무수한 전쟁을 승리로 이끌었다. 하지만 슈리마의 몰락과 함께 사막 아래 무덤 속에 갇혔고, 강산이 변하는 억겁의 세월을 어둠 속에서 보내면서 서서히 광기에 굴복해 갔다. 다시 자유의 몸이 된 레넥톤이 원하는 것은 단 하나, 자신을 가둔 형에 대한 복수뿐이다.", "allytips": ["자르고 토막내기는 적을 견제하며 괴롭힐 때 좋은 스킬입니다. 찌르며 들어가 다른 스킬을 사용한 뒤 다시 안전한 곳으로 물러날 수 있기 때문입니다.", "적이 많을 때 양떼 도륙 스킬을 사용할 경우 대량의 체력을 회복합니다. 이것을 미끼로 약한 척 행세하며 적을 유인할 수 있습니다.", "재사용 대기시간 감소 효과는 레넥톤의 분노를 빠르게 늘릴 수 있게 해줍니다."], "enemytips": ["레넥톤의 분노 게이지를 주의 깊게 살피면 언제 공격할지 대강 짐작할 수 있습니다.", "레넥톤은 싸우지 못할 경우 분노 게이지를 채우기 어렵습니다. 이 경우 레넥톤 스킬의 효과가 크게 저하됩니다."], "tags": ["Fighter", "Tank"], "partype": "분노", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 660, "hpperlevel": 111, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.15, "attackspeedperlevel": 2.75, "attackspeed": 0.665}, "spells": [{"id": "RenektonCleave", "name": "양떼 도륙", "description": "레넥톤이 검을 휘둘러 주변의 모든 적에게 어느 정도의 물리 피해를 입히고, 해당 피해 수치의 일부를 체력으로 회복합니다. 분노가 50이상 쌓이면 피해량과 치료 능력이 모두 상승합니다.", "tooltip": "레넥톤이 검을 휘둘러 <physicalDamage>{{ basicdamage }}의 물리 피해</physicalDamage>를 입힙니다. 챔피언이 아닌 대상을 맞히면 <healing>{{ nonchamphealing }}의 체력</healing>을, 챔피언을 맞힐 때는<healing>{{ champhealing }}</healing>의 체력을 회복합니다. 챔피언이 아닌 대상을 맞힐 때는 <keywordMajor>분노 {{ minionfurygain }}</keywordMajor>, 챔피언을 맞힐 때는 <keywordMajor>분노 {{ championfurygain }}</keywordMajor>이 생성됩니다.<br /><br /><keywordMajor>분노 추가 효과</keywordMajor>: 물리 피해로 입히는 피해량이 <physicalDamage>{{ empdamage }}</physicalDamage>만큼 상승합니다. 챔피언이 아닌 대상에게서 <healing>{{ empnonchamphealing }}의 체력</healing>을, 챔피언에게서 <healing>{{ empchamphealing }}의 체력</healing>을 회복합니다. 더 이상 <keywordMajor>분노</keywordMajor>가 생성되지 않습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "챔피언 하나당 체력 회복량", "챔피언이 아닌 유닛 하나당 체력 회복량", "최대 회복량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ champheal }} -> {{ champhealNL }}", "{{ baseminionheal }} -> {{ baseminionhealNL }}", "{{ basichealcap }} -> {{ basichealcapNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RenektonCleave.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "RenektonPreExecute", "name": "무자비한 포식자", "description": "레넥톤이 적을 두 번 베어 어느 정도의 물리 피해를 입히며, 0.75초 동안 적을 기절시킵니다. 레넥톤의 분노가 50 이상일 경우 적을 세 번 베어 대상의 보호막을 파괴한 후 높은 물리 피해를 입히고 1.5초 동안 적을 기절시킵니다.", "tooltip": "레넥톤의 다음 기본 공격은 두 번 베어 {{ stunduration }}초 동안 적을 <status>기절</status>시키고 총 <physicalDamage>{{ basictotaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 레넥톤이 챔피언을 맞히면 추가로 <keywordMajor>{{ bonusfuryvschamps }}의 분노</keywordMajor>를 얻습니다.<br /><br /><keywordMajor>분노 추가 효과</keywordMajor>: 레넥톤이 세 번 공격하여 대상의 <shield>보호막</shield>을 파괴한 후 <physicalDamage>{{ emptotaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ enragedstunduration }}초 동안 적을 <status>기절</status>시킵니다. <keywordMajor>분노</keywordMajor>가 생성되지 않습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "강화된 피해", "재사용 대기시간"], "effect": ["{{ basedamageperhit*2.000000 }} -> {{ basedamageperhitnl*2.000000 }}", "{{ basedamageperhit*3.000000 }} -> {{ basedamageperhitnl*3.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "RenektonPreExecute.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "RenektonSliceAndDice", "name": "자르고 토막내기", "description": "레넥톤이 돌격을 하며 여러 적 유닛에게 피해를 입힙니다. 강화시 레넥톤은 적에게 추가 피해를 입히고 방어력을 낮출 수 있습니다.", "tooltip": "레넥톤이 돌격하며 <physicalDamage>{{ basicdamage }}의 물리 피해</physicalDamage>를 입힙니다. 챔피언이 아닌 대상을 맞힐 때는 <keywordMajor>{{ minionragegeneration }}의 분노</keywordMajor>가, 챔피언을 맞힐 때는 <keywordMajor>{{ championragegeneration }}의 분노</keywordMajor>가 생성됩니다. 한 명 이상의 적에게 피해를 입힐 경우 {{ dicetimer }}초 내에 이 스킬을 한 번 <recast>재사용</recast>할 수 있습니다. <br /><br /><keywordMajor>분노 추가 효과</keywordMajor>: <recast>재사용</recast> 시 레넥톤이 돌격하며 <physicalDamage>{{ empdamage }}의 물리 피해</physicalDamage>를 입히고 {{ shredtimer }}초 동안 방어력을 <scaleArmor>{{ enragedarmorshred }}%</scaleArmor> 감소시킵니다. 더 이상 <keywordMajor>분노</keywordMajor>가 생성되지 않습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "강화된 피해", "방어 감소 %", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ enragedbasedamage }} -> {{ enragedbasedamageNL }}", "{{ enragedarmorshred }}% -> {{ enragedarmorshredNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RenektonSliceAndDice.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "RenektonReignOfTheTyrant", "name": "강신", "description": "레넥톤이 폭군의 형상을 취하여 추가 체력을 얻고 주변 적에게 피해를 입힙니다. 폭군 형태로 있는 동안에는 레넥톤의 분노가 주기적으로 상승합니다.", "tooltip": "레넥톤이 {{ buffduration }}초 동안 어둠의 기운으로 자신을 감싸며 <healing>{{ healthgain }}의 최대 체력</healing>과 <keywordMajor>{{ furyoncast }}의 분노</keywordMajor>를 얻습니다. 스킬이 활성화되어 있는 동안 레넥톤은 <magicDamage>{{ totaldamagepersecond }}의 마법 피해</magicDamage>를 입히고 초당 <keywordMajor>{{ furypersecond }}의 분노</keywordMajor>를 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["추가 체력", "초당 피해량", "재사용 대기시간"], "effect": ["{{ healthgain }} -> {{ healthgainNL }}", "{{ auradamagepersecond }} -> {{ auradamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "RenektonReignOfTheTyrant.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "분노의 지배", "description": "레넥톤이 기본 공격 시 분노를 생성합니다. 레넥톤의 체력이 낮으면 생성되는 분노가 증가합니다. 분노는 레넥톤의 스킬을 강화시키며 추가 효과를 줍니다.", "image": {"full": "Renekton_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}