{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sylas": {"id": "<PERSON><PERSON><PERSON>", "key": "517", "name": "<PERSON><PERSON><PERSON>", "title": "Sprenger der Ketten", "image": {"full": "Sylas.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "517000", "num": 0, "name": "default", "chromas": false}, {"id": "517001", "num": 1, "name": "Mon<PERSON><PERSON> <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517008", "num": 8, "name": "Freljord-Sylas", "chromas": true}, {"id": "517013", "num": 13, "name": "PROJEKT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517014", "num": 14, "name": "PROJEKT: <PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "517024", "num": 24, "name": "<PERSON> <PERSON>", "chromas": true}, {"id": "517034", "num": 34, "name": "Aschfahler Kämpfer Sylas", "chromas": true}, {"id": "517036", "num": 36, "name": "Wintergeweihter Sylas", "chromas": true}, {"id": "517046", "num": 46, "name": "Sternenvernichter-<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Sylas aus Gelichtergraben wuchs in einer der ärmeren Gegenden Demacias auf und wurde zu einem Symbol für die Schattenseite der großen Stadt. In seiner Kindheit erregte er die Aufmerksamkeit der berüchtigten Magiesuchenden, weil er verborgene Zauberei aufspüren konnte. Letztendlich wurde er von ihnen eingekerkert, nachdem er seine Kraft gegen sie eingesetzt hatte. Vor Kurzem gelang ihm der Ausbruch und Sylas lebt nun als skrupelloser Revolutionär mit dem Ziel, die Magie um ihn herum dazu zu nutzen, das Königreich, dem er einst diente, zu zerstören … und seine Anhängerschaft aus Magiern scheint mit jedem Tag zu wachsen.", "blurb": "Sylas aus Gelichtergraben wuchs in einer der ärmeren Gegenden Demacias auf und wurde zu einem Symbol für die Schattenseite der großen Stadt. In seiner Kindheit erregte er die Aufmerksamkeit der berüchtigten Magiesuchenden, weil er verborgene Zauberei...", "allytips": ["<PERSON><PERSON>, bis du oder dein Gegner nur noch wenig Leben haben, um „Königsmörder“ am wirkungsvollsten einzusetzen.", "Warte zwischen dem Einsatz deiner Fähigkeiten ein wenig, um alles aus „Petrizitwirbel“ zu holen.", "Wenn du die gegnerischen Ults richtig nutzt, erö<PERSON><PERSON> das neue Möglichkeiten für Teamkämpfe."], "enemytips": ["<PERSON><PERSON><PERSON>' Lebensbalken kann täuschen, pass auf „Königsmörder“ auf!", "Kämp<PERSON> gegen <PERSON>, wenn er deine ultimative Fähigkeit nicht stehlen kann."], "tags": ["Mage", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 122, "mp": 400, "mpperlevel": 70, "movespeed": 340, "armor": 29, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.55, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "<PERSON><PERSON>s<PERSON>", "name": "Gekreuzte Ketten", "description": "<PERSON><PERSON><PERSON> sch<PERSON>gt mit seinen Ketten zu. Sie überkreuzen sich an der anvisierten Position, verursachen Schaden und verlangsamen Gegner. <br><br>Nach einer kurzen Verzögerung explodiert magische Energie an ihrem Schnittpunkt und verursacht Schaden.", "tooltip": "<PERSON><PERSON><PERSON> sch<PERSON>gt mit seinen Ketten zu, die Gegnern <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> zufügen und sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowamountcalc }} <status>verlangsamen</status>. An dem <PERSON>, an dem die Ketten sich kreuzen, entsteht eine Explosion, die zusätzlich <magicDamage>{{ explosiondamage }}&nbsp;magischen Schaden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Explosionsschaden", "Verlangsamung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [775, 775, 775, 775, 775], "rangeBurn": "775", "image": {"full": "SylasQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasW", "name": "Königsmörder", "description": "<PERSON><PERSON><PERSON> stü<PERSON>t sich mit magischer Kraft auf einen Gegner, verursacht Schaden und heilt sich selbst gegen gegnerische Champions.", "tooltip": "<PERSON><PERSON><PERSON> stü<PERSON>t sich mit magischer Kraft auf einen Gegner und fügt ihm <magicDamage>{{ mindamage }}&nbsp;magischen Schaden</magicDamage> zu. Ist der Gegner ein Champion, stellt Sylas zwischen <healing>{{ minhealing }}</healing> und <healing>{{ maxhealing }}&nbsp;Leben</healing> wieder her (basierend auf seinem fehlenden Leben; max. Heilung bei nicht mehr als {{ maxexecutethreshold*100 }}&nbsp;% Leben).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ healing }} -> {{ healingNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasE", "name": "Davonstehlen / Entführen", "description": "<PERSON><PERSON><PERSON> s<PERSON> zu einem Zielort. <PERSON>ylas kann die Fähigkeiten erneut aktivieren und seine Ketten nach vorne schleudern, um sich zu einem getroffenen Gegner zu ziehen.", "tooltip": "<PERSON>ylas bewegt sich schnell nach vorn und bereitet 3,5&nbsp;Sekunden lang eine <recast>Reaktivierung</recast> vor.<br /><br /><recast>Reaktivierung:</recast> <PERSON>yla<PERSON> schleudert seine <PERSON>, zieht sich zum ersten getroffenen <PERSON>ner, fügt ihm <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> zu und <status>schleudert</status> ihn {{ knockupduration }}&nbsp;Sekunden lang hoch.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ pulldamage }} -> {{ pulldamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasR", "name": "Ultimative Übernahme", "description": "<PERSON><PERSON><PERSON> stiehlt die ultimative Fähigkeit eines Gegners und kann diese frei einsetzen.", "tooltip": "<PERSON><PERSON><PERSON> übernimmt die ultimative Fähigkeit eines gegnerischen Champions und kann sie auf die exakt gleiche Weise ausführen. Dabei zählt die Stufe seiner eigenen ultimativen Fähigkeit sowie seine eigenen Werte.<br /><br />Nach Übernahme einer ultimativen Fähigkeit kann der Gegner für einen Zeitraum, der {{ pertargetcooldown }}&nbsp;% der Abklingzeit der gegnerischen ultimativen Fähigkeit entspricht, mindestens {{ minimumenemycooldown }}&nbsp;Sekunden lang nicht erneut bestohlen werden (wird beeinflusst von Sylas' Fähigkeitstempo).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 55, 30], "cooldownBurn": "80/55/30", "cost": [75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "SylasR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Petrizitwirbel", "description": "Nach dem Einsatz einer Fähigkeit speichert Sylas eine Ladung Petrizitwirbel. Beim nächsten normalen Angriff verbraucht Sylas die Ladung und lässt seine magiegeladenen Ketten um sich herumwirbeln, die an getroffenen Gegnern zusätzlichen magischen Schaden verursachen. Wenn Sylas eine Ladung Petrizitwirbel besitzt, ist sein Angriffstempo erhöht. ", "image": {"full": "SylasP.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}