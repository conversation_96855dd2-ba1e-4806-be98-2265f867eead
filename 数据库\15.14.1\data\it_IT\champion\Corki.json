{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Corki": {"id": "<PERSON><PERSON>", "key": "42", "name": "<PERSON><PERSON>", "title": "il bombardiere spericolato", "image": {"full": "Corki.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "42000", "num": 0, "name": "default", "chromas": false}, {"id": "42001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "42002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "42003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "42004", "num": 4, "name": "<PERSON><PERSON> Hot Rod", "chromas": false}, {"id": "42005", "num": 5, "name": "Corki Cavalca-Urf", "chromas": false}, {"id": "42006", "num": 6, "name": "Corki Ala di Drago", "chromas": true}, {"id": "42007", "num": 7, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "42008", "num": 8, "name": "Corki Arcade", "chromas": true}, {"id": "42018", "num": 18, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "42026", "num": 26, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Il pilota yordle chiamato Corki ama due cose: volare e curare il suo baffo alla moda... ma non sempre in quest'ordine. Do<PERSON> aver lasciato Bandle City, si è stabilito a Piltover, dove si è innamorato delle incredibili macchine. Si è dedicato allo sviluppo di trabiccoli volanti e ha assunto la guida del gruppo di difesa aerea delle Canaglie urlanti, composto da veterani di guerra. Calmo anche nei momenti più concitati, Corki pattuglia i cieli della sua nuova patria e non ha mai incontrato un problema che non si possa risolvere con un po' di fuoco di sbarramento.", "blurb": "Il pilota yordle chiamato <PERSON>i ama due cose: volare e curare il suo baffo alla moda... ma non sempre in quest'ordine. Dopo aver lasciato Bandle City, si è stabilito a Piltover, dove si è innamorato delle incredibili macchine. Si è dedicato allo...", "allytips": ["Bomba al fosforo può essere usata per rivelare le unità nemiche che potrebbero essere nascoste nell'erba alta accanto alla tua posizione.", "Valchiria può essere usata anche in maniera difensiva, per esempio per avere una via di fuga veloce.", "Corki può continuare ad attaccare mentre usa Mitragliatrice Gatling. Usare appieno Mitragliatrice Gatling è la via per padroneggiare Corki."], "enemytips": ["Fai attenzione al Fuoco di sbarramento di Corki. Causa danni ad area, quindi puoi essere colpito anche se ti nascondi dietro i minion.", "Corki è vulnerabile una volta che ha usato Valchiria: inizia a bersagliare Corki se entra in un combattimento dopo aver usato quell'abilità."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 3, "magic": 6, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 100, "mp": 350, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2, "attackspeedperlevel": 2.8, "attackspeed": 0.644}, "spells": [{"id": "PhosphorusBomb", "name": "Bomba al fosforo", "description": "Corki spara una bomba accecante nella posizione bersaglio, infliggendo danni magici ai nemici nell'area. L'attacco rivela anche le unità nell'area per un periodo di tempo.", "tooltip": "<PERSON>i lancia una bomba che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>. L'area e i campioni colpiti vengono rivelati per {{ revealduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "PhosphorusBomb.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CarpetBomb", "name": "Val<PERSON><PERSON>", "description": "<PERSON>i si sposta in volo per una breve distanza, sganciando bombe e lasciando una scia di fiamme che danneggiano i nemici colpiti.", "tooltip": "Corki plana e incendia una linea di fronte a sé, facendola bruciare per {{ trailduration }} secondi. I nemici nelle fiamme subiscono fino a <magicDamage>{{ maximumdamage }} danni magici</magicDamage> nel tempo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> nel tempo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "CarpetBomb.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GGun", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "La mitragliatrice Gatling di Corki spara velocemente in un'area conica davanti a sé, infliggendo danni e riducendo l'armatura e la resistenza magica nemica.", "tooltip": "<PERSON>i spara con una gatling davanti a sé, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e deteriorando fino a <scaleArmor>{{ shredmax*-1 }} armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR> nell'arco di {{ sprayduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Riduzione difesa", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredmax*-1.000000 }} -> {{ shredmaxnl*-1.000000 }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GGun.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissileBarrage", "name": "Fuoco di sbarramento", "description": "Corki spara verso la posizione bersaglio un missile che esplode all'impatto, infliggendo danni a tutti i nemici nell'area. Corki immagazzina missili col passare del tempo, con un tetto massimo. Ogni 3 missili lancia un Super missile, che infligge danni extra.", "tooltip": "<PERSON>i spara un missile che esplode al contatto col primo nemico colpito e infligge <physicalDamage>{{ rsmallmissiledamage }} danni fisici</physicalDamage> ai nemici circostanti. Ogni terzo missile infligge invece <physicalDamage>{{ rbigmissiledamage }} danni fisici</physicalDamage>.<br /><br />Questa abilità ha fino a {{ maxammotooltip }} cariche. Gli attacchi base contro i campioni riducono di <attention>{{ attackrefund }}</attention> secondo/i sul colpo il tempo tra le cariche.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [2, 2, 2], "cooldownBurn": "2", "cost": [35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "4", "range": [1225, 1225, 1225], "rangeBurn": "1225", "image": {"full": "MissileBarrage.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Munizioni hextech", "description": "Una percentuale dei danni degli attacchi base di Corki viene inflitta come <trueDamage>danni puri</trueDamage> bonus.", "image": {"full": "Corki_RapidReload.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}