{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shaco": {"id": "Shaco", "key": "35", "name": "<PERSON>ャ<PERSON>", "title": "悪魔の道化師", "image": {"full": "Shaco.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "35000", "num": 0, "name": "default", "chromas": false}, {"id": "35001", "num": 1, "name": "いかれ帽子屋シャコ", "chromas": false}, {"id": "35002", "num": 2, "name": "宮廷道化師シャコ", "chromas": false}, {"id": "35003", "num": 3, "name": "くるみ割り人形シャコ", "chromas": false}, {"id": "35004", "num": 4, "name": "ネジ巻きシャコ", "chromas": false}, {"id": "35005", "num": 5, "name": "闇の廃人シャコ", "chromas": false}, {"id": "35006", "num": 6, "name": "仮面道化師シャコ", "chromas": false}, {"id": "35007", "num": 7, "name": "ジョーカー シャコ", "chromas": false}, {"id": "35008", "num": 8, "name": "ダークスター シャコ", "chromas": true}, {"id": "35015", "num": 15, "name": "魔紋使いシャコ", "chromas": true}, {"id": "35023", "num": 23, "name": "クライムシティー ナイトメア シャコ", "chromas": true}, {"id": "35033", "num": 33, "name": "冬の祝福シャコ", "chromas": true}, {"id": "35043", "num": 43, "name": "ソウルファイター シャコ", "chromas": true}, {"id": "35044", "num": 44, "name": "プレステージ ソウルファイター シャコ", "chromas": false}, {"id": "35054", "num": 54, "name": "恐怖の夜シャコ", "chromas": true}, {"id": "35064", "num": 64, "name": "ニャコ", "chromas": true}], "lore": "殺人と暴力に悦びを見出すシャコは、かつては寂しい王子の遊び道具として作られた魔法の操り人形だった。闇の魔法に穢されて、自らが愛していた役目を失ったことで、優しい操り人形は憐れな者たちを苦しめることに悦びを見出すように成り果てた。彼はおもちゃや単純なトリックを駆使して殺人を行い、その血塗られた「ゲーム」を楽しんで笑っている。夜中に彼の不気味な笑い声が聞こえたら…それは悪魔の道化師があなたを次のおもちゃとして選んだ証かもしれない。", "blurb": "殺人と暴力に悦びを見出すシャコは、かつては寂しい王子の遊び道具として作られた魔法の操り人形だった。闇の魔法に穢されて、自らが愛していた役目を失ったことで、優しい操り人形は憐れな者たちを苦しめることに悦びを見出すように成り果てた。彼はおもちゃや単純なトリックを駆使して殺人を行い、その血塗られた「ゲーム」を楽しんで笑っている。夜中に彼の不気味な笑い声が聞こえたら…それは悪魔の道化師があなたを次のおもちゃとして選んだ証かもしれない。", "allytips": ["「幻惑」で壁などを飛び越えることで、戦闘からスムーズに逃げられる。有効に活用して敵を惑わせよう。", "クリティカルダメージを増加させ、「バックスタブ」の特徴を最大限に引き立てる「インフィニティ エッジ」などのアイテムがおすすめだ。", "アイテムの通常攻撃時効果は「ハルシネイト」で生成される分身の攻撃にも適用される。"], "enemytips": ["ゲーム序盤でシャコに手を焼いているなら、彼のジャングルキャンプの近くに「ステルスワード」を設置してみよう。姿が見えれば怖くない。", "シャコは「幻惑」を使って前線にワープしてくるが、このスキルはクールダウンが長いため、危なくなったとしても逃げるために再発動することはできない。この隙に、チーム一丸となってシャコに総攻撃を仕掛けよう。"], "tags": ["Assassin"], "partype": "マナ", "info": {"attack": 8, "defense": 4, "magic": 6, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 297, "mpperlevel": 40, "movespeed": 345, "armor": 30, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.694}, "spells": [{"id": "Deceive", "name": "幻惑", "description": "インビジブル状態になり、瞬間移動する。<br><br>インビジブル中の最初の通常攻撃は追加ダメージを与え、対象の背後から攻撃した場合はクリティカルになる。", "tooltip": "瞬間移動して{{ stealthduration }}秒間<keywordStealth>インビジブル</keywordStealth>状態になる。<spellName>「びっくり箱」</spellName>または<spellName>「ハルシネイト」</spellName>を使用しても<keywordStealth>インビジブル</keywordStealth>状態は解除されない。<br /><br /><keywordStealth>インビジブル</keywordStealth>状態中、次の通常攻撃が<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を追加で与える。背後から攻撃した場合は、この通常攻撃がクリティカルになり、{{ qcritdamagemod }}のダメージを与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "ステルス効果時間", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Deceive.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "JackInTheBox", "name": "びっくり箱", "description": "隠された「びっくり箱」をフィールド上に設置する。敵が近づくと発動して、周囲の敵すべてを恐怖に陥れて攻撃する。", "tooltip": "トラップを設置する。トラップは{{ e5 }}秒後にステルス状態になり、{{ trapduration }}秒間持続する。敵が近づくか可視化されると発動し、周囲の敵チャンピオンに{{ fearduration }}秒間(ミニオンとジャングルモンスターに対しては{{ minionfearduration }}秒間)<status>フィアー効果</status>を与える。<br /><br />トラップは発動すると5秒間周囲のすべての敵を攻撃して<magicDamage>{{ aoedamage }}の魔法ダメージ</magicDamage>を与え、対象が1体の場合は代わりに<magicDamage>{{ stdamage }}のダメージ</magicDamage>を与える。<br /><br />「びっくり箱」の攻撃はモンスターに対して<magicDamage>{{ monsterbonusdamage }}のダメージ</magicDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "フィアー効果時間", "モンスターへの追加ダメージ", "@AbilityResourceName@コスト"], "effect": ["{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ fearduration }} -> {{ feardurationNL }}", "{{ monsterbonusdamage }} -> {{ monsterbonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 65, 60, 55, 50], "costBurn": "70/65/60/55/50", "datavalues": {}, "effect": [null, [35, 50, 65, 80, 95], [200, 300, 400, 500, 600], [0.5, 0.75, 1, 1.25, 1.5], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/50/65/80/95", "200/300/400/500/600", "0.5/0.75/1/1.25/1.5", "40", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "JackInTheBox.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TwoShivPoison", "name": "ポイズンダガー", "description": "「ポイズンダガー」は自動効果により通常攻撃が命中した敵に毒を与え、移動速度を低下させる。発動した場合は対象にナイフを投げ、ダメージと毒を与える。対象の体力が30%未満の場合は追加ダメージが発生する。", "tooltip": "<spellPassive>自動効果:</spellPassive> このスキルが使用可能な時は、通常攻撃が対象に{{ slowdurationpassive }}秒間、{{ slowamount*-100 }}%の<status>スロウ効果</status>を付与する。<br /><br /><spellActive>発動効果:</spellActive> ナイフを投げて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowdurationactive }}秒間{{ slowamount*-100 }}%の<status>スロウ効果</status>を付与する。対象の体力が{{ executehealththreshold*100 }}%未満の場合、代わりに<magicDamage>{{ totalexecutedamage }}のダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "TwoShivPoison.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "HallucinateFull", "name": "ハルシネイト", "description": "シャコの分身が生成され周囲の敵を攻撃する(タワーに対してはダメージが減少する)。分身の体力が尽きると爆発して、3つの「ミニびっくり箱」を発生させ、周囲の敵にダメージを与える。", "tooltip": "一瞬姿を消してから、分身を伴って出現する。分身は体力が尽きるか{{ clonelifetime }}秒間経過すると自爆し、<magicDamage>{{ explosiontotaldamage }}の魔法ダメージ</magicDamage>を与えて、即座に発動する<spellName>「ミニびっくり箱」</spellName>を3つ出現させる。分身はシャコの攻撃力の{{ cloneaadamagepercent*100 }}%のダメージを与え、受けるダメージは{{ cloneincomingdamagepercent*100 }}%増加する。<br /><br /><spellName>「ミニびっくり箱」</spellName>は<magicDamage>{{ aoedamage }}の魔法ダメージ</magicDamage>(敵が1体のみの場合は<magicDamage>{{ stdamage }}の魔法ダメージ</magicDamage>)を与え、{{ boxfearduration }}秒間<status>フィアー効果</status>を付与する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自爆ダメージ", "「ミニびっくり箱」基本ダメージ", "クールダウン"], "effect": ["{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "HallucinateFull.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "バックスタブ", "description": "対象の背後から攻撃した場合、通常攻撃と「ポイズンダガー」が追加ダメージを与える。", "image": {"full": "Jester_CarefulStrikes.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}