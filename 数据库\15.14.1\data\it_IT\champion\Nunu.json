{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nunu": {"id": "<PERSON><PERSON><PERSON>", "key": "20", "name": "<PERSON><PERSON><PERSON> e <PERSON>", "title": "il bambino e lo yeti", "image": {"full": "Nunu.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "20000", "num": 0, "name": "default", "chromas": false}, {"id": "20001", "num": 1, "name": "<PERSON><PERSON><PERSON> e <PERSON>", "chromas": false}, {"id": "20002", "num": 2, "name": "<PERSON><PERSON><PERSON> e <PERSON>", "chromas": false}, {"id": "20003", "num": 3, "name": "<PERSON><PERSON><PERSON> e <PERSON>", "chromas": false}, {"id": "20004", "num": 4, "name": "<PERSON><PERSON><PERSON> e <PERSON>-bot", "chromas": false}, {"id": "20005", "num": 5, "name": "Nunu e Willump Demolitori", "chromas": false}, {"id": "20006", "num": 6, "name": "Nunu e Willump TPA", "chromas": false}, {"id": "20007", "num": 7, "name": "<PERSON><PERSON><PERSON> e <PERSON>", "chromas": false}, {"id": "20008", "num": 8, "name": "Nunu e Willump Origami", "chromas": true}, {"id": "20016", "num": 16, "name": "Nunu e Willump Ritmo Spaziale", "chromas": true}, {"id": "20026", "num": 26, "name": "<PERSON><PERSON>u e <PERSON>ap<PERSON>", "chromas": true}, {"id": "20035", "num": 35, "name": "<PERSON>un<PERSON> e <PERSON>", "chromas": true}, {"id": "20044", "num": 44, "name": "Nunu e Willump Notte Inquietante", "chromas": true}], "lore": "C'era una volta un bambino che voleva dimostrare di essere un eroe sconfiggendo un temibile mostro, ma che scoprì che il mostro era in realtà uno yeti magico che aveva solo bisogno di un amico. Uniti da un antico potere e dall'amore per le palle di neve, <PERSON><PERSON><PERSON> e <PERSON>ump ora vagano per il Freljord, dando vita alle avventure dei loro sogni. Sperano un giorno di ritrovare la madre di Nunu... <PERSON><PERSON>, sal<PERSON><PERSON><PERSON>, potrebbero dimostrare di essere veramente degli eroi...", "blurb": "C'era una volta un bambino che voleva dimostrare di essere un eroe sconfiggendo un temibile mostro, ma che scoprì che il mostro era in realtà uno yeti magico che aveva solo bisogno di un amico. Uniti da un antico potere e dall'amore per le palle di neve...", "allytips": ["Consumazione permette a Nunu di rimanere in corsia contro avversari a distanza.", "Puoi decidere di interrompere Zero Assoluto prima per infliggere almeno un danno parziale, se un avversario cerca di allontanarsi.", "È sempre meglio lanciare Zero Assoluto dopo che sono stati utilizzati i primi impedimenti. Resta per un po' nelle retrovie prima di gettarti nella mischia."], "enemytips": ["Interrompere la carica di Zero Assoluto diminuisce la quantità di danni che subisce la tua squadra.", "Usare l'incantesimo dell'evocatore Flash è una maniera sicura di scappare da Zero Assoluto.", "La palla di neve più grande di sempre si muove molto velocemente ma non può girare rapidamente, quindi cerca di non scappare correndo in linea retta. Prova invece a fare curve brusche e improvvise."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 90, "mp": 280, "mpperlevel": 42, "movespeed": 345, "armor": 29, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "NunuQ", "name": "Consumazione", "description": "Willump azzanna un minion, mostro o campione nemico bersaglio, infliggendo danni e curandosi.", "tooltip": "Nunu chiede a Willump di mordere un nemico, infliggendo <trueDamage>{{ monsterminiondamage }} danni puri</trueDamage> e ripristinando <healing>{{ monsterhealing }} salute</healing> quando usata contro un minion o un mostro della giungla. Contro un campione, infligge invece <magicDamage>{{ totalchampiondamage }} danni magici</magicDamage> e cura per <healing>{{ championhealing }} salute</healing>.<br /><br />La <healing>guarigione</healing> aumenta del {{ lowhealthhealingscalar*100 }}% quando Nunu e Willump sono sotto {{ lowhealththreshhold*100 }}% salute.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON>ni ai campioni", "Guarigione", "Ricarica"], "effect": ["{{ monsterminiondamage }} -> {{ monsterminiondamageNL }}", "{{ championdamage }} -> {{ championdamageNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "NunuQ.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} mana"}, {"id": "NunuW", "name": "La palla di neve più grande di sempre!", "description": "Willump crea una palla di neve che diventa sempre più grande e veloce mano a mano che la fa rotolare. La palla di neve danneggia e lancia in aria i nemici.", "tooltip": "Nunu e Willump creano una palla di neve che diventa sempre più grande e veloce mentre rotola. Mentre la stanno facendo rotolare curvano più lentamente, ma possono aumentare la velocità della curva mantenendola.<br /><br />La palla di neve infligge tra <magicDamage>{{ noimpactsnowballdamage }}</magicDamage> e <magicDamage>{{ maximumsnowballdamage }} danni magici</magicDamage> quando colpisce un campione, mostro grande o muro. Il bersaglio colpito viene <status>lanciato in aria</status> per un minimo di {{ baseknockupduration }} e un massimo di {{ maximumstunduration }} secondi. Questi valori cambiano a seconda della distanza ricoperta dal rotolamento.<br /><br />Nunu e Willump possono <recast>rilanciare</recast> l'abilità per lasciar andare la palla prima.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "NunuW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuE", "name": "Raffica di palle di neve", "description": "Nunu lancia una raffica di palle di neve che danneggiano i nemici. Quando ha finito, Willump immobilizza tutti i campioni e i mostri grandi colpiti da una palla di neve.", "tooltip": "Nunu lancia tre palle di neve, infliggendo <magicDamage>{{ totalsnowballdamage }} danni magici</magicDamage> per palla di neve e <status>rallentando</status> i nemici colpiti da tutte e tre di un {{ slowamount*-100 }}% per {{ slowduration }} secondo. Nunu può <recast>rilanciarla</recast> per un massimo di altre due volte.<br /><br />Dopo {{ totalspellduration }} secondi, Nunu <status>immobilizza</status> per {{ rootduration }} secondi tutti i nemici vicini che sono stati <status>rallentati</status> dalle palle di neve e infligge <magicDamage>{{ totalrootdamage }} danni magici</magicDamage> in più.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Costo in mana", "Rallentamento del movimento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "NunuE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuR", "name": "Zero Assoluto", "description": "<PERSON><PERSON>u e Willump creano una potente tempesta in un'area che rallenta i nemici e infligge danni ingenti alla fine.", "tooltip": "Nunu e Willump canalizzano una potente tormenta fino a un massimo di {{ channelduration }} secondi. I nemici al suo interno vengono <status>rallentati</status> di un {{ slowstartamount*-100 }}%, che aumenta fino a {{ maxslowamount*-100 }}% nel corso della durata dell'effetto. Nunu e Willump guadagnano anche <shield>uno scudo da {{ totalshieldamount }}</shield> per la sua durata, che decade nel corso dei successivi {{ shielddecayduration }} secondi.<br /><br />Al termine della tormenta questa detona, infliggendo fino a <magicDamage>{{ maximumdamage }} danni magici</magicDamage> in base al tempo di canalizzazione. Nunu e Willump possono <recast>rilanciare</recast> per terminare prima la tormenta.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "NunuR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Chiamata del Freljord", "description": "Nunu aumenta le velocità di attacco e movimento di Willump e di un alleato vicino, e fa in modo che gli attacchi base di Willump danneggino i nemici intorno al bersaglio.", "image": {"full": "NunuPassive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}