{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nautilus": {"id": "Na<PERSON><PERSON>", "key": "111", "name": "Na<PERSON><PERSON>", "title": "der Titan der Tiefen", "image": {"full": "Nautilus.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "111000", "num": 0, "name": "default", "chromas": false}, {"id": "111001", "num": 1, "name": "Abgrund-Nautilus", "chromas": false}, {"id": "111002", "num": 2, "name": "Untergrund-Nautilus", "chromas": false}, {"id": "111003", "num": 3, "name": "Astronautil<PERSON>", "chromas": true}, {"id": "111004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>uti<PERSON>", "chromas": false}, {"id": "111005", "num": 5, "name": "Weltenbrecher-Nautilus", "chromas": false}, {"id": "111006", "num": 6, "name": "Eroberer-Nautilus", "chromas": false}, {"id": "111009", "num": 9, "name": "Shan Hai-Nautilus", "chromas": false}, {"id": "111018", "num": 18, "name": "Schreckensnacht-Nautilus", "chromas": false}, {"id": "111027", "num": 27, "name": "Kosm<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "111036", "num": 36, "name": "<PERSON><PERSON>", "chromas": false}], "lore": "Nautilus ist eine einsame Legende, so alt wie die ersten untergegangenen Landungsstege in Bilgewasser. Der gepanzerte Goliath durchstreift die tiefen Gewässer vor der Küste der Inseln der Blauen Flamme. Er ist auf Rache für einen vergessenen Vertrauensbruch aus, schlägt ohne Vorwarnung zu und schwingt seinen gewaltigen Anker, um die Leidenden zu retten oder die Gierigen in den Abgrund zu reißen. <PERSON> sagt, er hole sich jene, die die „Zehntabgaben von Bilgewasser“ nicht bezahlt haben, und zieht sie mit sich in die Wellen – eine eiserne Faust, die jedem klarmacht, dass man den Tiefen nicht entrinnen kann.", "blurb": "Nautilus ist eine einsame Legende, so alt wie die ersten untergegangenen Landungsstege in Bilgewasser. Der gepanzerte Goliath durchstreift die tiefen Gewässer vor der Küste der Inseln der Blauen Flamme. Er ist auf Rache für einen vergessenen...", "allytips": ["Versuche dich mit „Schlepphaken“ an nahem Terrain entlang zu hangeln, um Gegner zu überraschen, und nutze dann „Reißende Flut“, um besser zu treffen.", "„Reißende Flut“ wird verz<PERSON><PERSON>t ausgelöst, was sich auf der Flucht ausnutzen oder bei angreifenden Gegnern zur Ablenkung einsetzen lässt."], "enemytips": ["Nutzt Nautilus „Reißende Flut“ direkt neben dir, so bleib stehen bis die Wirkung vorbei ist und bewege dich nicht. Läufst du zu früh, begibst du dich direkt in die sekundären Explosionen, nimmst zusätzlichen Schaden und wirst verlangsamt.", "Nautilus kann viel Flächenschaden mit seinen normalen Angriffen verursachen, solange sein <PERSON><PERSON><PERSON> bestehen bleibt. Versuche den Schild au<PERSON>, wenn du kannst."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 6, "magic": 6, "difficulty": 6}, "stats": {"hp": 646, "hpperlevel": 100, "mp": 400, "mpperlevel": 47, "movespeed": 325, "armor": 39, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.65, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1, "attackspeed": 0.706}, "spells": [{"id": "NautilusAnchorDrag", "name": "Schlepphaken", "description": "Nautilus wirft seinen Anker nach vorne. Trifft er einen Gegner, werden dieser und Nautilus zusammengezogen und der Gegner erleidet magischen Schaden. Trifft Nautilus Terrain, wird er zu diesem herangezogen.", "tooltip": "Nautilus wirft seinen Anker nach vorne. Trifft er e<PERSON>ner, zieht Nautilus sich selbst und das Ziel zu<PERSON>mmen, wodurch es <magicDamage>{{ qdamagecalc }}&nbsp;magischen Schaden</magicDamage> erle<PERSON><PERSON> und kurzzeitig <status>betäubt</status> wird. Wenn der Anker auf Terrain trifft, zieht Nautilus sich zu ihm heran.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "0", "0.5", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "NautilusAnchorDrag.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusPiercingGaze", "name": "Zorn des Titanen", "description": "Nautilus erhält vorübergehend einen Schild. Während er abgeschirmt wird, fügen seine Angriffe dem Ziel und umstehenden Gegnern Schaden über Zeit zu.", "tooltip": "Nautilus erhält {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ shieldcalc }}. Während der <shield><PERSON>hil<PERSON></shield> besteht, fügen Nautilus' Angriffe dem Ziel und allen umstehenden Gegnern zusätzlich <magicDamage>{{ dotdamagecalc }}&nbsp;magischen Schaden</magicDamage> über 2&nbsp;Sekunden zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Ma<PERSON><PERSON>", "Maximales Leben (%)"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ dotdamagebase }} -> {{ dotdamagebaseNL }}", "{{ shieldhealthratio*100.000000 }}&nbsp;% -> {{ shieldhealthrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "NautilusPiercingGaze.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusSplashZone", "name": "Reißende Flut", "description": "Nautilus erzeugt drei explodierende Wellen um sich herum. Jede Explosion fügt Gegnern Schaden zu und verlangsamt sie.", "tooltip": "Nautilus erzeugt drei explodierende Wellen um sich herum. Jede fügt <PERSON>n im Wirkbereich <magicDamage>{{ damagecalc }}&nbsp;magischen Schaden</magicDamage> zu und <status>verlangsamt</status> sie um {{ slowpercent*100 }}&nbsp;%. Die Verlangsamung fällt über {{ slowduration }}&nbsp;Sekunden ab.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ slowpercent*100.000000 }}&nbsp;% -> {{ slowpercentnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NautilusSplashZone.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusGrandLine", "name": "Tiefenladung", "description": "Nautilus entsendet eine Scho<PERSON>welle in die Erde, die einen Gegner verfolgt. Diese reißt die darüber liegende Erde auf und stößt Gegner in die Luft. Erreicht sie ihr Ziel, bricht sie hervor, schleudert sein <PERSON> in die Luft und betäubt es.", "tooltip": "Nautilus entsendet eine <PERSON>, die einen gegnerischen Champion verfolgt, ihm <magicDamage>{{ primarytargetdamage }}&nbsp;magischen <PERSON></magicDamage> zuf<PERSON><PERSON>, ihn <status>hochschleudert</status> und {{ stunduration }}&nbsp;Sekunde(n) lang <status>betäubt</status>. <PERSON><PERSON> von der Schockwelle getroffene Gegner werden ebenfalls <status>hochgeschleudert</status>, <status>betäubt</status> und erleiden <magicDamage>{{ secondarytargetdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kontaktschaden", "Betäubungsdauer:", "Explosionsschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ primarydamage }} -> {{ primarydamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "NautilusGrandLine.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Nautilus' erster Angriff gegen ein Ziel verursacht erhöhten normalen Schaden und hält das Ziel kurz fest.", "image": {"full": "Nautilus_StaggeringBlow.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}