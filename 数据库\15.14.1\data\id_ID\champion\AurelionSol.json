{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"AurelionSol": {"id": "AurelionSol", "key": "136", "name": "Aurelion Sol", "title": "The Star Forger", "image": {"full": "AurelionSol.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "136000", "num": 0, "name": "default", "chromas": false}, {"id": "136001", "num": 1, "name": "Ashen Lord Aurel<PERSON>", "chromas": false}, {"id": "136002", "num": 2, "name": "Mecha Aurelion Sol", "chromas": true}, {"id": "136011", "num": 11, "name": "Storm Dragon Aurelion Sol", "chromas": false}, {"id": "136021", "num": 21, "name": "Inkshadow Aurelion Sol", "chromas": false}, {"id": "136031", "num": 31, "name": "Porcelain Protector Aurelion Sol", "chromas": false}], "lore": "Aurelion Sol pernah mengisi kekosongan semesta kosmos dengan keajaiban langit yang diciptakannya sendiri. <PERSON><PERSON>, dia dipaksa menggunakan kekuatan luar biasa miliknya atas perintah kerajaan ruang angkasa, yang menjebaknya menjadi budak. Mendambakan jalan pulang menjadi penempa bintang, Aurelion Sol menyeret bintang-bintang dari angkasa untuk mendapatkan kembali kebebasannya.", "blurb": "Aurelion Sol pernah mengisi kekosongan semesta kosmos dengan keajaiban langit yang diciptakannya sendiri. <PERSON><PERSON>, dia dipaksa menggunakan kekuatan luar biasa miliknya atas perintah kerajaan ruang ang<PERSON>a, yang menjebaknya menjadi budak. Mendambakan jalan...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 90, "mp": 530, "mpperlevel": 40, "movespeed": 335, "armor": 22, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "AurelionSolQ", "name": "Breath of Light", "description": "Aurelion Sol channeling napas naganya selama beberapa detik, memberi damage pada musuh pertama yang terkena serta memercikkan damage yang berkurang pada musuh di sekitar. Tiap detik napas di-channeling secara langsung ke musuh akan menghasilkan damage bonus, yang meningkat sesuai jumlah Stardust yang sudah terkumpul. Ability ini mengumpulkan Stardust jika targetnya adalah champion.", "tooltip": "Aurelion Sol mengembuskan starfire selama hingga {{ maxchannelduration }} detik, mengh<PERSON>lkan <magicDamage>{{ damagepersecond }} magic damage</magicDamage> per detik pada musuh pertama yang terkena dan {{ aoemodifier*100 }}% dari damage ke musuh di sekitar.<br /><br />Setiap napas kedua pada musuh yang sama akan menghasilkan burst <magicDamage>{{ burstdamage }} magic damage</magicDamage> plus <magicDamage>{{ burstbonustruedamagetochamps }} magic damage dari Health maksimum</magicDamage> dan menyerap <span class=\"color3458eb\">{{ qmassstolen }} Stardust</span> jika mereka adalah champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@ Cost", "Damage Per <PERSON>", "Burst Damage", "Durasi Channeling Maksimum"], "effect": ["{{ cost }}-> {{ costNL }}", "{{ rankdamagepersecond }}-> {{ rankdamagepersecondNL }}", "{{ rankburstdamage }}-> {{ rankburstdamageNL }}", "{{ maxchannelduration }}-> {{ maxchanneldurationNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Mana per detik", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolQ.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ manacostpersecond }} Mana per detik"}, {"id": "AurelionSolW", "name": "Astral Flight", "description": "Aurelion Sol terbang melintasi medan ke arah yang ditargetkan. Saat berada dalam kondisi ini, dia bisa cast ability lainnya. Breath of Light tidak lagi memiliki cooldown atau durasi channeling maksimum dan mengh<PERSON>lkan damage yang meningkat saat terbang.<br><br>Sisa cooldown Astral Flight berkurang setiap kali champion musuh mati setelah baru saja terkena damage dari Aurelion Sol.<br><br>Stardust meningkatkan range maksimum Astral Flight", "tooltip": "Aurelion Sol terbang ke satu arah. Saat terbang, <spellName>Breath of Light</spellName> tidak memiliki Cooldown, tidak ada durasi channeling maksimum, dan flat damage-nya meningkat sebanyak {{ truedamagebonus*100 }}%.<br /><br />Takedown pada champion dalam waktu {{ resetwindow }} detik saat memberi mereka damage akan mengembalikan {{ tooltiptakedowncooldownmultiplier }}% dari Cooldown Ability ini.<br /><br /><recast>Recast:</recast> <PERSON><PERSON> lebih awal.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Persentase Magic Damage Bonus", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ truedamagebonus*100.000000 }}%-> {{ truedamagebonusnl*100.000000 }}%", "{{ cost }}-> {{ costNL }}", "{{ cd }}-> {{ cdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "AurelionSolW.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolE", "name": "Singularity", "description": "Aurelion Sol memanggil black hole, men<PERSON><PERSON><PERSON><PERSON> damage pada musuh dan menarik mereka perlahan ke pusatnya. Ability ini memberikan Stardust setiap kali musuh mati di dalam black hole dan untuk setiap detik champion musuh yang terperangkap di dalamnya. Pusat black hole mengeksekusi musuh dengan Health maksimum di bawah persentase tertentu. Stardust meningkatkan area Singularity serta batas eksekusi.", "tooltip": "Aurelion Sol memanggil black hole, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damagepersecond }} magic damage</magicDamage> per detik dan <status>Drag</status> musuh ke tengah selama {{ duration }} detik. Musuh di tengah dengan kondisi <scaleHealth>Health maksimum di bawah {{ currentexecutionthreshold }}%</scaleHealth> akan langsung mati.<br /><br />Black hole menyerap <span class=\"color3458eb\">Stardust</span> saat musuh mati di dalamnya dan setiap detik champion musuh ada di dalamnya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per <PERSON>"], "effect": ["{{ basedamagepersecond }}-> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolE.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolR", "name": "Falling Star / The Skies Descend", "description": "Falling Star: Aurelion Sol menjatuhkan bintang ke bumi. Dampaknya menghasilkan magic damage dan menerapkan Stun pada musuh sekaligus memberi Stardust untuk tiap champion musuh yang terkena. Mengumpulkan Stardust yang cukup akan mengubah Falling Star Aurelion Sol berikutnya menjadi The Skies Descend.<br><br>The Skies Descend: Aurelion Sol menarik bintang besar dari langit dengan zona dampak dan damage yang meningkat, membuat musuh Knock Up, bukan menerapkan Stun. Shockwave kemudian menyebar dari tepi zona dampak, mengh<PERSON>lkan damage dan menerapkan Slow pada musuh yang terkena. Stardust meningkatkan area dampak Falling Star dan The Skies Descend.", "tooltip": "Aurelion Sol memetik bintang dari langit dan menjatuhkannya ke bumi, men<PERSON><PERSON><PERSON>an <magicDamage>{{ maxdamagetooltip }} magic damage</magicDamage>, menerapkan <status>Stun</status> pada musuh selama {{ stunduration }} detik, dan menyerap <span class=\"color3458eb\">{{ massstolen }} Stardust</span> untuk setiap champion yang terkena.<br /><br /><PERSON>gumpulkan <span class=\"color3458eb\">{{ calamitystacks }} Stardust</span> akan mengubah <spellName>Falling Star</spellName> berikutnya menjadi <spellName>The Skies Descend</spellName>.<br /><br /><spellName>The Skies Descend</spellName> Aurelion Sol menarik amarah konstelasi dari alam semesta, menghasilkan <magicDamage>{{ r2damage }} magic damage</magicDamage> di area yang lebih besar, <status>Knock Up</status> musuh yang terkena selama {{ stunduration }} detik, dan melepaskan shockwave dahsyat yang menghasilkan <magicDamage>{{ shockwavedamage }} magic damage</magicDamage> pada champion dan <PERSON> Epik, juga menerapkan <status>Slow</status> ke semua musuh yang terkena sebanyak {{ shockwaveslow*100 }}% selama 1 detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "Damage yang <PERSON>at", "Damage Shockwave"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ basedamage*1.250000 }}-> {{ basedamagenl*1.250000 }}", "{{ basedamage*0.900000 }}-> {{ basedamagenl*0.900000 }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AurelionSolR.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Cosmic Creator", "description": "Ability penghasil damage Aurelion Sol akan menghancurkan musuh menjadi stack <font color='#3458eb'>Stardust</font>, yang secara permanen meningkatkan tiap ability-nya. ", "image": {"full": "AurelionSolP.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}