{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jayce": {"id": "<PERSON><PERSON>", "key": "126", "name": "<PERSON><PERSON>", "title": "<PERSON> V<PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Jayce.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "126000", "num": 0, "name": "default", "chromas": false}, {"id": "126001", "num": 1, "name": "Fullmetal-<PERSON><PERSON>", "chromas": false}, {"id": "126002", "num": 2, "name": "Charm<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "126003", "num": 3, "name": "Verlassener Jayce", "chromas": false}, {"id": "126004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "126005", "num": 5, "name": "Academia Certaminis-Jayce", "chromas": true}, {"id": "126015", "num": 15, "name": "Widerstands-<PERSON><PERSON>", "chromas": true}, {"id": "126024", "num": 24, "name": "<PERSON><PERSON><PERSON> (Arcane)", "chromas": false}, {"id": "126025", "num": 25, "name": "Zenitspiele-Jayce", "chromas": true}, {"id": "126034", "num": 34, "name": "T1-<PERSON><PERSON>", "chromas": true}, {"id": "126035", "num": 35, "name": "Überlebender Jayce (Arcane)", "chromas": false}, {"id": "126036", "num": 36, "name": "T1-<PERSON><PERSON> (Prestige)", "chromas": false}], "lore": "<PERSON><PERSON> ist ein brillanter <PERSON>, der zusammen mit seinem Freund Viktor die ersten großen Entdeckungen auf dem Gebiet der Hextech-Technologie gemacht hat. Er wird in ganz Piltover verehrt und versucht, seinem <PERSON>uf als „Mann des Fortschritts“ gerecht zu werden, hat aber oft mit den in ihn gesetzten Erwartungen zu kämpfen. Mehr und mehr erkennt Jay<PERSON>, auf welche Weise seine Erfindungen die Kluft zwischen Piltover und Z<PERSON>un noch weiter vertieft haben, und ist bereit, mit seinem Hextech-Hammer die Zukunft zu verteidigen.", "blurb": "<PERSON><PERSON> ist ein brillanter <PERSON>, der zusammen mit seinem Freund Viktor die ersten großen Entdeckungen auf dem Gebiet der Hextech-Technologie gemacht hat. Er wird in ganz Piltover verehrt und versucht, seinem Ruf als „Mann des Fortschritts“...", "allytips": ["Wechsle häufig deine Waffe. So verstärkst du deine Angriffe und erhältst zusätzliches Tempo.", "Falls du zu viel einstecken musst, solltest du dich auf die Hammerhaltung konzentrieren, denn diese gewährt dir eine verbesserte Verteidigung.", "Um mehr Reichweite und Schaden zu bekommen, versuche mit „Schockstoß“ durch das „Beschleunigungstor“ zu feuern."], "enemytips": ["<PERSON><PERSON> kann sowohl im Nah- als auch Fernkampf angreifen. Achte auf seine aktuelle Form und seine Waffenfarbe, um zu wissen, worauf er gerade setzt.", "<PERSON>cht<PERSON> au<PERSON>, wenn er ein „Beschleunigungstor“ p<PERSON><PERSON><PERSON>, da er wahrscheinlich auch „<PERSON><PERSON><PERSON>to<PERSON>“ einsetzen wird.", "<PERSON><PERSON> ist im frühen Spielverlauf stark. Ist er im Vorteil, solltest du defensiv spielen."], "tags": ["Fighter", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 7}, "stats": {"hp": 590, "hpperlevel": 109, "mp": 375, "mpperlevel": 45, "movespeed": 335, "armor": 22, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3, "attackspeed": 0.658}, "spells": [{"id": "JayceToTheSkies", "name": "Bis zum Him<PERSON>! / <PERSON><PERSON><PERSON><PERSON>ß", "description": "Hammerform: <PERSON><PERSON> zu e<PERSON>m Gegner, verursacht normalen Schaden und verlangsamt Gegner.<br><br>Kanonenform: Feuert eine Kugel aus Elektrizität, die beim Auftreffen auf einen Gegner (oder nach einer bestimmten Zeit) detoniert und dabei an allen getroffenen Gegnern normalen Schaden verursacht.", "tooltip": "<keywordMajor>Merkurhammer:</keywordMajor> <PERSON><PERSON> spring<PERSON> zu e<PERSON>, fügt <PERSON> in der Nähe <physicalDamage>{{ spell.jaycetotheskies:damage }}&nbsp;normalen Schaden</physicalDamage> zu und <status>verlangsamt</status> diese {{ spell.jaycetotheskies:slowduration }}&nbsp;Sekunden lang um {{ spell.jaycetotheskies:slow*-100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Verlangsamung"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ slow*-100.000000 }}&nbsp;% -> {{ slownl*-100.000000 }}&nbsp;%"]}, "maxrank": 6, "cooldown": [16, 14, 12, 10, 8, 6], "cooldownBurn": "16/14/12/10/8/6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JayceToTheSkies.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceStaticField", "name": "Blitzfeld / Hyperladung", "description": "Hammerform: Passiv: <PERSON><PERSON><PERSON> mit jedem T<PERSON>ffer <PERSON> wieder her. Aktiv: <PERSON><PERSON><PERSON><PERSON> ein <PERSON>, das mehrere Sekunden bestehen bleibt und allen nahen G<PERSON>nern Schaden zufügt.<br><br>Kanonenform: Erh<PERSON><PERSON> einen Energieschub, der das Angriffstempo einige Angriffe lang auf das Maximum erhöht.", "tooltip": "<keywordMajor>Merkurhammer – Passiv:</keywordMajor> <PERSON><PERSON>' <keywordMajor>Hammer</keywordMajor>-<PERSON><PERSON><PERSON> gew<PERSON>hren ihm <scaleMana>{{ spell.jaycestaticfield:managain }}&nbsp;Mana</scaleMana>.<br /><br /><keywordMajor>Merkurhammer – Aktiv:</keywordMajor> <PERSON><PERSON> erzeugt ein Blitzfeld, das über {{ spell.jaycestaticfield:duration }}&nbsp;Sekunden hinweg <magicDamage>{{ spell.jaycestaticfield:damage }}&nbsp;magischen <PERSON>haden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Manawiederherstellung"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ managain }} -> {{ managainNL }}"]}, "maxrank": 6, "cooldown": [10, 10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [285, 285, 285, 285, 285, 285], "rangeBurn": "285", "image": {"full": "JayceStaticField.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceThunderingBlow", "name": "Donnernder Schlag / Beschleunigungstor", "description": "Hammerform: <PERSON><PERSON><PERSON> einem Gegner magischen Schaden zu und stößt diesen über eine kurze Distanz zurück.<br><br>Kanonenform: Platziert ein Beschleunigungstor. Alle verbündeten Champions, die es durchqueren, erhalten ein erhöhtes Lauftempo. Wenn man „Schockstoß“ durch das Tor feuert, erhöhen sich Geschwindigkeit, Reichweite und Schaden des Projektils.", "tooltip": "<keywordMajor><PERSON><PERSON></keywordMajor>: <PERSON><PERSON> sch<PERSON> seinen <PERSON>, <status>st<PERSON><PERSON><PERSON></status> sein Z<PERSON> zurück und fügt ihm <magicDamage>magischen <PERSON>haden in H<PERSON>he von {{ spell.jaycethunderingblow:flatdamage }} plus {{ spell.jaycethunderingblow:perchpdamage*100 }}&nbsp;% seines max. Lebens</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> (%)", "Abklingzeit", "Grenze für Monsterschaden"], "effect": ["{{ perchpdamage*100.000000 }}&nbsp;% -> {{ perchpdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ monstercap }} -> {{ monstercapNL }}"]}, "maxrank": 6, "cooldown": [20, 18, 16, 14, 12, 10], "cooldownBurn": "20/18/16/14/12/10", "cost": [55, 55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [240, 240, 240, 240, 240, 240], "rangeBurn": "240", "image": {"full": "JayceThunderingBlow.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JayceStanceHtG", "name": "Merkurkanone / Merkurhammer", "description": "Hammerform: Transformiert den Merkurhammer in die Merkurkanone, was neue Fähigkeiten und eine erhöhte Reichweite gewährt. Der erste Angriff in dieser Form verringert Rüstung und Magieresistenz des Ziels.<br><br>Kanonenform: Transformiert die Merkurkanone in den Merkurhammer. Der erste Angriff in dieser Form verursacht zusätzlichen magischen Schaden.", "tooltip": "<keywordMajor>Merkurhammer</keywordMajor>: <PERSON><PERSON> ver<PERSON>t seine Waffe in die <keywordMajor>Merkurkanone</keywordMajor> und erhält Angriffsreichweite und neue Fähigkeiten. Jayce' nächster Angriff entfernt {{ spell.jaycestancehtg:shredduration }}&nbsp;Sekunden lang <scaleArmor>{{ spell.jaycestancehtg:rangedformshred }}&nbsp;Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR>.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [6], "cooldownBurn": "6", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "JayceStanceHtG.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Hextech-Kondensator", "description": "<PERSON><PERSON> seine Waffe transformiert, ist sein Lauftempo für kurze Zeit erhöht.", "image": {"full": "Jayce_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}