{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Camille": {"id": "<PERSON>", "key": "164", "name": "카밀", "title": "강철의 그림자", "image": {"full": "Camille.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "164000", "num": 0, "name": "default", "chromas": false}, {"id": "164001", "num": 1, "name": "프로그램 카밀", "chromas": false}, {"id": "164002", "num": 2, "name": "악의 여단 카밀", "chromas": true}, {"id": "164010", "num": 10, "name": "iG 카밀", "chromas": false}, {"id": "164011", "num": 11, "name": "아르카나 카밀", "chromas": true}, {"id": "164021", "num": 21, "name": "타격대 지휘관 카밀", "chromas": true}, {"id": "164031", "num": 31, "name": "겨울의 축복 카밀", "chromas": true}, {"id": "164032", "num": 32, "name": "프레스티지 겨울의 축복 카밀", "chromas": false}], "lore": "카밀은 법이라는 테두리 밖에서 활동할 수 있도록 아예 자신의 몸을 무기화해 버렸다. 우아한 엘리트 첩보원이자 페로스 가문이 배출한 최고 정보 요원인 그녀의 임무는 고도화된 필트오버와 그 하층부의 자운이 원활하게 돌아가도록 기여하는 것이다. 적응력이 뛰어나고, 정확성을 중시하므로 엉성한 기술은 반드시 정리해 버려야 할 수치로 여긴다. 마법공학 증강을 통해 최고가 되려는 노력, 그리고 자신이 품고 있는 칼만큼이나 예리한 지성을 지닌 카밀의 모습을 보면, 그녀가 이제는 인간 여자보다는 기계에 가까운 존재가 아닌가 하는 의구심 마저 든다.", "blurb": "카밀은 법이라는 테두리 밖에서 활동할 수 있도록 아예 자신의 몸을 무기화해 버렸다. 우아한 엘리트 첩보원이자 페로스 가문이 배출한 최고 정보 요원인 그녀의 임무는 고도화된 필트오버와 그 하층부의 자운이 원활하게 돌아가도록 기여하는 것이다. 적응력이 뛰어나고, 정확성을 중시하므로 엉성한 기술은 반드시 정리해 버려야 할 수치로 여긴다. 마법공학 증강을 통해 최고가 되려는 노력, 그리고 자신이 품고 있는 칼만큼이나 예리한 지성을 지닌 카밀의 모습을 보면...", "allytips": ["상대 팀이 전투로 진영이 흐트러질 때까지 기다렸다가 갈고리 발사 스킬을 사용해 가장 약한 대상을 노리세요.", "군중 제어 스킬을 이용해 정확성 프로토콜 공격을 두 번 다 적중시켜 보세요."], "enemytips": ["카밀의 보호막은 물리 혹은 마법 피해 중 하나만 흡수합니다. 보호막 유형을 잘 확인한 후 피해를 입혀 보세요.", "마법공학 최후통첩은 사거리가 매우 짧은 스킬이므로 카밀이 가까이 다가오면 점멸로 거리를 벌리세요."], "tags": ["Fighter", "Assassin"], "partype": "마나", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 99, "mp": 339, "mpperlevel": 52, "movespeed": 340, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.8, "mpregen": 8.15, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.5, "attackspeed": 0.644}, "spells": [{"id": "CamilleQ", "name": "정확성 프로토콜", "description": "카밀이 다음 기본 공격 시 추가 피해를 입히고 이동 속도가 증가합니다. 이 스킬은 사용 후 일정 시간 동안 재사용할 수 있으며, 잠시 기다렸다가 재사용하면 추가 피해량이 크게 증가합니다.", "tooltip": "카밀이 다음 기본 공격 시 <physicalDamage>{{ bonusdamage }}의 추가 물리 피해</physicalDamage>를 입히고 {{ msduration }}초 동안 <speed>이동 속도가 {{ msbonus*100 }}%</speed> 증가합니다. {{ qtotalrecasttime }}초 후에 스킬을 <recast>재사용</recast>할 수 있습니다.<br /><br />첫 번째 기본 공격 후 {{ qrampuptime }}초가 지난 뒤 스킬을 <recast>재사용</recast>하여 공격 시 추가 피해량이 증가해 <physicalDamage>{{ empoweredbonusdamage }}</physicalDamage>의 피해를 입히고, 이 중 {{ damageconversionpercentage }}는 <trueDamage>고정 피해</trueDamage>로 적용됩니다.<br /><br /><rules>이 스킬은 피해를 입힐 때 효과가 발동합니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["총 공격력 %", "이동 속도", "재사용 대기시간"], "effect": ["{{ tadratio*100.000000 }}% -> {{ tadrationl*100.000000 }}%", "{{ msbonus*100.000000 }}% -> {{ msbonusnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "CamilleQ.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "CamilleW", "name": "전술적 휩쓸기", "description": "카밀이 잠시 후 전방을 원뿔형으로 휩쓸어 피해를 입힙니다. 원뿔의 바깥쪽 절반에 있는 적은 이동 속도가 느려지고 추가 피해를 입으며, 카밀의 체력을 회복시킵니다.", "tooltip": "카밀이 다리를 감아올려 휩쓸며 <physicalDamage>{{ basedamagetotal }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />바깥쪽 절반에서 맞은 적은 이동 속도가 {{ slowpercentage }}% <status>느려졌다가</status> {{ slowduration }}초에 걸쳐 원래대로 돌아오며, 추가로 <physicalDamage>최대 체력에 비례해 {{ outeredgetooltip }}의 물리 피해</physicalDamage>를 입습니다. 이때 카밀은 <healing>챔피언에게 입힌 추가 피해량의 {{ outerconehealingratio }}%만큼 체력</healing>을 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["소모값 @AbilityResourceName@", "피해량", "적 최대 체력 비례 피해량", "재사용 대기시간"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ outerconemaxhpdamage*100.000000 }}% -> {{ outerconemaxhpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 15.5, 14, 12.5, 11], "cooldownBurn": "17/15.5/14/12.5/11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "CamilleW.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "CamilleE", "name": "갈고리 발사", "description": "카밀이 벽으로 자신을 끌어당겼다가 다시 도약해 착지 시 적 챔피언들을 기절시킵니다.", "tooltip": "카밀이 지형에 걸리는 갈고리를 발사해 1초 동안 자신을 지형으로 끌어당깁니다. 이 스킬은 <recast>재사용</recast>할 수 있습니다.<br /><br /><recast>재사용 시:</recast> 카밀이 지형으로부터 도약해 처음 마주치는 적 챔피언과 충돌하며 근처 적에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 적 챔피언을 {{ knockupduration }}초 동안 <status>기절</status>시킵니다. 적 챔피언을 향해 도약할 경우 도약 거리가 두 배로 증가하고, 충돌 시 {{ asduration }}초 동안 <attackSpeed>공격 속도가 {{ asbuff*100 }}%</attackSpeed> 빨라집니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "피해량", "공격 속도"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ asbuff*100.000000 }}% -> {{ asbuffnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CamilleE.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "CamilleR", "name": "마법공학 최후통첩", "description": "카밀이 대상 챔피언에게 돌진해 일정 지역 내에 가둡니다. 대상에게 기본 공격 시 추가 마법 피해를 입힙니다.", "tooltip": "카밀이 잠시 대상으로 지정할 수 없게 되며 적 챔피언에게 도약해 {{ rduration }}초 동안 어떤 방법으로도 탈출할 수 없도록 일정 지역 내에 가두고 정신 집중을 방해합니다. 근처의 다른 적은 <status>뒤로 밀려납니다</status>. 갇힌 적에 대한 카밀의 기본 공격은 <magicDamage>대상 현재 체력의 {{ rpercentcurrenthpdamage }}%에 해당하는 마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["적 현재 체력 비례 피해량", "지속시간", "재사용 대기시간"], "effect": ["{{ rpercentcurrenthpdamage }}% -> {{ rpercentcurrenthpdamageNL }}%", "{{ rduration }} -> {{ rdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [475, 475, 475], "rangeBurn": "475", "image": {"full": "CamilleR.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "적응형 방어 체계", "description": "챔피언에게 기본 공격 시 잠시 동안 카밀 최대 체력의 일부에 해당하는 피해를 흡수하는 보호막이 생깁니다. 적 챔피언이 어떤 피해를 주는지에 따라 물리 혹은 마법 보호막 중 하나만 생성됩니다.", "image": {"full": "Camille_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}