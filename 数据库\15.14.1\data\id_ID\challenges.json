[{"id": 0, "name": "CRYSTAL", "description": "", "shortDescription": "<PERSON><PERSON><PERSON> k<PERSON> untuk <PERSON>", "hasLeaderboard": true, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 0, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Apprentice"}]}, "BRONZE": {"value": 750}, "SILVER": {"value": 1650}, "GOLD": {"value": 4300}, "PLATINUM": {"value": 8600}, "DIAMOND": {"value": 13800}, "MASTER": {"value": 24500}, "GRANDMASTER": {"value": 25000}, "CHALLENGER": {"value": 26500}}}, {"id": 1, "name": "IMAGINATION", "description": "", "shortDescription": "Capstone IMAGINATION", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 75}, "BRONZE": {"value": 175}, "SILVER": {"value": 300}, "GOLD": {"value": 700}, "PLATINUM": {"value": 1200}, "DIAMOND": {"value": 2000}, "MASTER": {"value": 3500}}}, {"id": 101000, "name": "ARAM Authority", "description": "<PERSON>h progres dari tantangan di grup ARAM Warrior, ARAM Finesse, dan <PERSON><PERSON> Champion", "shortDescription": "<PERSON>h progres dari tantangan di grup ARAM Warrior, ARAM Finesse, dan <PERSON><PERSON> Champion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101000-IRON.png", "BRONZE": "/challenges-images/101000-BRONZE.png", "SILVER": "/challenges-images/101000-SILVER.png", "GOLD": "/challenges-images/101000-GOLD.png", "PLATINUM": "/challenges-images/101000-PLATINUM.png", "DIAMOND": "/challenges-images/101000-DIAMOND.png", "MASTER": "/challenges-images/101000-MASTER.png", "GRANDMASTER": "/challenges-images/101000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 40}, "BRONZE": {"value": 85}, "SILVER": {"value": 140}, "GOLD": {"value": 360}, "PLATINUM": {"value": 590}, "DIAMOND": {"value": 1075}, "MASTER": {"value": 1850, "rewards": [{"category": "TITLE", "quantity": 1, "title": "ARAM God"}]}}}, {"id": 101100, "name": "ARAM Warrior", "description": "Raih progres dari tantangan di grup ARAM Warrior", "shortDescription": "Raih progres dari tantangan di grup ARAM Warrior", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101100-IRON.png", "BRONZE": "/challenges-images/101100-BRONZE.png", "SILVER": "/challenges-images/101100-SILVER.png", "GOLD": "/challenges-images/101100-GOLD.png", "PLATINUM": "/challenges-images/101100-PLATINUM.png", "DIAMOND": "/challenges-images/101100-DIAMOND.png", "MASTER": "/challenges-images/101100-MASTER.png", "GRANDMASTER": "/challenges-images/101100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 40}, "GOLD": {"value": 115}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 340, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Unstoppable"}]}, "MASTER": {"value": 650}}}, {"id": 101101, "name": "DPS Threat", "description": "Hasil<PERSON> lebih dari 1800 Damage Per Menit di game ARAM", "shortDescription": "Hasilkan lebih dari 1800 DPM", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101101-IRON.png", "BRONZE": "/challenges-images/101101-BRONZE.png", "SILVER": "/challenges-images/101101-SILVER.png", "GOLD": "/challenges-images/101101-GOLD.png", "PLATINUM": "/challenges-images/101101-PLATINUM.png", "DIAMOND": "/challenges-images/101101-DIAMOND.png", "MASTER": "/challenges-images/101101-MASTER.png", "GRANDMASTER": "/challenges-images/101101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101101-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 4}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 15}, "CHALLENGER": {"value": 20}}}, {"id": 101102, "name": "Double Decimation", "description": "Lakukan dua Pentakill dalam satu game ARAM", "shortDescription": "Dapatkan dua Pentakill dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101102-IRON.png", "BRONZE": "/challenges-images/101102-BRONZE.png", "SILVER": "/challenges-images/101102-SILVER.png", "GOLD": "/challenges-images/101102-GOLD.png", "PLATINUM": "/challenges-images/101102-PLATINUM.png", "DIAMOND": "/challenges-images/101102-DIAMOND.png", "MASTER": "/challenges-images/101102-MASTER.png", "GRANDMASTER": "/challenges-images/101102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101102-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 101103, "name": "ARAM Legend", "description": "Raih Legendary di game ARAM", "shortDescription": "Rai<PERSON> Legendary", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101103-IRON.png", "BRONZE": "/challenges-images/101103-BRONZE.png", "SILVER": "/challenges-images/101103-SILVER.png", "GOLD": "/challenges-images/101103-GOLD.png", "PLATINUM": "/challenges-images/101103-PLATINUM.png", "DIAMOND": "/challenges-images/101103-DIAMOND.png", "MASTER": "/challenges-images/101103-MASTER.png", "GRANDMASTER": "/challenges-images/101103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101103-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 10}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 20}, "CHALLENGER": {"value": 30}}}, {"id": 101104, "name": "Bad Medicine", "description": "Bunuh musuh yang baru saja mendapatkan paket health di ARAM", "shortDescription": "Bunuh musuh yang baru saja menerima heal dari paket health", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101104-IRON.png", "BRONZE": "/challenges-images/101104-BRONZE.png", "SILVER": "/challenges-images/101104-SILVER.png", "GOLD": "/challenges-images/101104-GOLD.png", "PLATINUM": "/challenges-images/101104-PLATINUM.png", "DIAMOND": "/challenges-images/101104-DIAMOND.png", "MASTER": "/challenges-images/101104-MASTER.png", "GRANDMASTER": "/challenges-images/101104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Angel of Mercy"}]}, "PLATINUM": {"value": 45}, "DIAMOND": {"value": 90}, "MASTER": {"value": 150}}}, {"id": 101105, "name": "No Hiding", "description": "Bunuh musuh di dekat salah satu turret mereka di ARAM", "shortDescription": "Bunuh musuh di dekat <PERSON> mereka", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101105-IRON.png", "BRONZE": "/challenges-images/101105-BRONZE.png", "SILVER": "/challenges-images/101105-SILVER.png", "GOLD": "/challenges-images/101105-GOLD.png", "PLATINUM": "/challenges-images/101105-PLATINUM.png", "DIAMOND": "/challenges-images/101105-DIAMOND.png", "MASTER": "/challenges-images/101105-MASTER.png", "GRANDMASTER": "/challenges-images/101105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 125}, "GOLD": {"value": 250}, "PLATINUM": {"value": 650}, "DIAMOND": {"value": 1500}, "MASTER": {"value": 2500}}}, {"id": 101106, "name": "ARAM Eradication", "description": "Lakukan Pentakill di ARAM", "shortDescription": "Lakukan <PERSON>kill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101106-IRON.png", "BRONZE": "/challenges-images/101106-BRONZE.png", "SILVER": "/challenges-images/101106-SILVER.png", "GOLD": "/challenges-images/101106-GOLD.png", "PLATINUM": "/challenges-images/101106-PLATINUM.png", "DIAMOND": "/challenges-images/101106-DIAMOND.png", "MASTER": "/challenges-images/101106-MASTER.png", "GRANDMASTER": "/challenges-images/101106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101106-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 7}, "GRANDMASTER": {"value": 12}, "CHALLENGER": {"value": 20}}}, {"id": 101107, "name": "Farm Champs Not Minions", "description": "Lakukan Takedown di ARAM", "shortDescription": "Lakukan Takedown", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101107-IRON.png", "BRONZE": "/challenges-images/101107-BRONZE.png", "SILVER": "/challenges-images/101107-SILVER.png", "GOLD": "/challenges-images/101107-GOLD.png", "PLATINUM": "/challenges-images/101107-PLATINUM.png", "DIAMOND": "/challenges-images/101107-DIAMOND.png", "MASTER": "/challenges-images/101107-MASTER.png", "GRANDMASTER": "/challenges-images/101107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101107-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 200}, "BRONZE": {"value": 800}, "SILVER": {"value": 2000}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 12500}, "DIAMOND": {"value": 20000}, "MASTER": {"value": 30000}, "GRANDMASTER": {"value": 40000}, "CHALLENGER": {"value": 50000}}}, {"id": 101108, "name": "Solo Carry", "description": "<PERSON><PERSON><PERSON> 40% atau lebih dari damage timmu pada champion di ARAM", "shortDescription": "<PERSON><PERSON><PERSON> 40%+ dari damage champion timmu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101108-IRON.png", "BRONZE": "/challenges-images/101108-BRONZE.png", "SILVER": "/challenges-images/101108-SILVER.png", "GOLD": "/challenges-images/101108-GOLD.png", "PLATINUM": "/challenges-images/101108-PLATINUM.png", "DIAMOND": "/challenges-images/101108-DIAMOND.png", "MASTER": "/challenges-images/101108-MASTER.png", "GRANDMASTER": "/challenges-images/101108-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101108-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 7}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 101200, "name": "ARAM Finesse", "description": "Raih progres dari tantangan di grup ARAM Finesse", "shortDescription": "Raih progres dari tantangan di grup ARAM Finesse", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101200-IRON.png", "BRONZE": "/challenges-images/101200-BRONZE.png", "SILVER": "/challenges-images/101200-SILVER.png", "GOLD": "/challenges-images/101200-GOLD.png", "PLATINUM": "/challenges-images/101200-PLATINUM.png", "DIAMOND": "/challenges-images/101200-DIAMOND.png", "MASTER": "/challenges-images/101200-MASTER.png", "GRANDMASTER": "/challenges-images/101200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 90}, "PLATINUM": {"value": 140}, "DIAMOND": {"value": 250, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Swift"}]}, "MASTER": {"value": 400}}}, {"id": 101201, "name": "Another Day, Another Bullseye", "description": "Hantam champion den<PERSON> skillshot (ability ranged tak terarah) di ARAM", "shortDescription": "Hantam champion den<PERSON> skillshot", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101201-IRON.png", "BRONZE": "/challenges-images/101201-BRONZE.png", "SILVER": "/challenges-images/101201-SILVER.png", "GOLD": "/challenges-images/101201-GOLD.png", "PLATINUM": "/challenges-images/101201-PLATINUM.png", "DIAMOND": "/challenges-images/101201-DIAMOND.png", "MASTER": "/challenges-images/101201-MASTER.png", "GRANDMASTER": "/challenges-images/101201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 200}, "BRONZE": {"value": 1000}, "SILVER": {"value": 2500}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 15000}, "DIAMOND": {"value": 30000}, "MASTER": {"value": 45000}, "GRANDMASTER": {"value": 60000}, "CHALLENGER": {"value": 75000}}}, {"id": 101202, "name": "It was a... Near-Hit", "description": "<PERSON><PERSON><PERSON> skillshot (ability ranged tak terarah) di ARAM", "shortDescription": "<PERSON><PERSON><PERSON> skillshot", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101202-IRON.png", "BRONZE": "/challenges-images/101202-BRONZE.png", "SILVER": "/challenges-images/101202-SILVER.png", "GOLD": "/challenges-images/101202-GOLD.png", "PLATINUM": "/challenges-images/101202-PLATINUM.png", "DIAMOND": "/challenges-images/101202-DIAMOND.png", "MASTER": "/challenges-images/101202-MASTER.png", "GRANDMASTER": "/challenges-images/101202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 300}, "BRONZE": {"value": 1200}, "SILVER": {"value": 3600}, "GOLD": {"value": 7200}, "PLATINUM": {"value": 21600}, "DIAMOND": {"value": 44000}, "MASTER": {"value": 78000}, "GRANDMASTER": {"value": 90000}, "CHALLENGER": {"value": 150000}}}, {"id": 101203, "name": "Snow Day", "description": "Hantam champion dengan snowball di ARAM", "shortDescription": "Hantam champion dengan snowball", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101203-IRON.png", "BRONZE": "/challenges-images/101203-BRONZE.png", "SILVER": "/challenges-images/101203-SILVER.png", "GOLD": "/challenges-images/101203-GOLD.png", "PLATINUM": "/challenges-images/101203-PLATINUM.png", "DIAMOND": "/challenges-images/101203-DIAMOND.png", "MASTER": "/challenges-images/101203-MASTER.png", "GRANDMASTER": "/challenges-images/101203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Avalanche"}]}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1200}, "MASTER": {"value": 3000}, "GRANDMASTER": {"value": 4000}, "CHALLENGER": {"value": 5000}}}, {"id": 101204, "name": "Free Money", "description": "Bunuh minion di ARAM", "shortDescription": "<PERSON><PERSON><PERSON> minion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101204-IRON.png", "BRONZE": "/challenges-images/101204-BRONZE.png", "SILVER": "/challenges-images/101204-SILVER.png", "GOLD": "/challenges-images/101204-GOLD.png", "PLATINUM": "/challenges-images/101204-PLATINUM.png", "DIAMOND": "/challenges-images/101204-DIAMOND.png", "MASTER": "/challenges-images/101204-MASTER.png", "GRANDMASTER": "/challenges-images/101204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 225}, "BRONZE": {"value": 900}, "SILVER": {"value": 2250}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 17000}, "DIAMOND": {"value": 30000}, "MASTER": {"value": 54000}}}, {"id": 101205, "name": "Free Ticket to Base", "description": "<PERSON>r<PERSON><PERSON> dieks<PERSON><PERSON>i oleh turret luar sebelum 10 menit di ARAM", "shortDescription": "Dieksekusi turret sebelum 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101205-IRON.png", "BRONZE": "/challenges-images/101205-BRONZE.png", "SILVER": "/challenges-images/101205-SILVER.png", "GOLD": "/challenges-images/101205-GOLD.png", "PLATINUM": "/challenges-images/101205-PLATINUM.png", "DIAMOND": "/challenges-images/101205-DIAMOND.png", "MASTER": "/challenges-images/101205-MASTER.png", "GRANDMASTER": "/challenges-images/101205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101205-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 4}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 18}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 101206, "name": "Pop Goes the Poro", "description": "Buat Poro meledak di ARAM", "shortDescription": "<PERSON><PERSON><PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101206-IRON.png", "BRONZE": "/challenges-images/101206-BRONZE.png", "SILVER": "/challenges-images/101206-SILVER.png", "GOLD": "/challenges-images/101206-GOLD.png", "PLATINUM": "/challenges-images/101206-PLATINUM.png", "DIAMOND": "/challenges-images/101206-DIAMOND.png", "MASTER": "/challenges-images/101206-MASTER.png", "GRANDMASTER": "/challenges-images/101206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101206-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Poroyalty"}]}}}, {"id": 101300, "name": "ARAM Champion", "description": "Raih progres dari tantangan di grup ARAM Champion", "shortDescription": "Raih progres dari tantangan di grup ARAM Champion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101300-IRON.png", "BRONZE": "/challenges-images/101300-BRONZE.png", "SILVER": "/challenges-images/101300-SILVER.png", "GOLD": "/challenges-images/101300-GOLD.png", "PLATINUM": "/challenges-images/101300-PLATINUM.png", "DIAMOND": "/challenges-images/101300-DIAMOND.png", "MASTER": "/challenges-images/101300-MASTER.png", "GRANDMASTER": "/challenges-images/101300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 100}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 340, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Abyssal"}]}, "MASTER": {"value": 550}}}, {"id": 101301, "name": "All Random All Champions", "description": "Raih grade S- atau lebih tinggi pada berbagai champion di ARAM", "shortDescription": "Raih grade S- dengan <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101301-IRON.png", "BRONZE": "/challenges-images/101301-BRONZE.png", "SILVER": "/challenges-images/101301-SILVER.png", "GOLD": "/challenges-images/101301-GOLD.png", "PLATINUM": "/challenges-images/101301-PLATINUM.png", "DIAMOND": "/challenges-images/101301-DIAMOND.png", "MASTER": "/challenges-images/101301-MASTER.png", "GRANDMASTER": "/challenges-images/101301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}}}, {"id": 101302, "name": "All Random All Flawless", "description": "Raih grade S atau lebih tinggi di ARAM", "shortDescription": "Raih grade S atau lebih tinggi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101302-IRON.png", "BRONZE": "/challenges-images/101302-BRONZE.png", "SILVER": "/challenges-images/101302-SILVER.png", "GOLD": "/challenges-images/101302-GOLD.png", "PLATINUM": "/challenges-images/101302-PLATINUM.png", "DIAMOND": "/challenges-images/101302-DIAMOND.png", "MASTER": "/challenges-images/101302-MASTER.png", "GRANDMASTER": "/challenges-images/101302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 35}, "MASTER": {"value": 75}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 300}}}, {"id": 101303, "name": "Rapid Demolition", "description": "Hancurkan turret pertama di ARAM sebelum lima menit berlalu", "shortDescription": "Hancurkan turret pertama sebelum 5 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101303-IRON.png", "BRONZE": "/challenges-images/101303-BRONZE.png", "SILVER": "/challenges-images/101303-SILVER.png", "GOLD": "/challenges-images/101303-GOLD.png", "PLATINUM": "/challenges-images/101303-PLATINUM.png", "DIAMOND": "/challenges-images/101303-DIAMOND.png", "MASTER": "/challenges-images/101303-MASTER.png", "GRANDMASTER": "/challenges-images/101303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 30}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 250}}}, {"id": 101304, "name": "Lightning Round", "description": "Menangkan game ARAM sebelum 13 menit berlalu", "shortDescription": "Menangkan game sebelum 13 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101304-IRON.png", "BRONZE": "/challenges-images/101304-BRONZE.png", "SILVER": "/challenges-images/101304-SILVER.png", "GOLD": "/challenges-images/101304-GOLD.png", "PLATINUM": "/challenges-images/101304-PLATINUM.png", "DIAMOND": "/challenges-images/101304-DIAMOND.png", "MASTER": "/challenges-images/101304-MASTER.png", "GRANDMASTER": "/challenges-images/101304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101304-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 20}}}, {"id": 101305, "name": "Active Participant", "description": "Raih partisipasi kill lebih dari 90% di game ARAM", "shortDescription": "Raih partisipasi kill lebih dari 90% di game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101305-IRON.png", "BRONZE": "/challenges-images/101305-BRONZE.png", "SILVER": "/challenges-images/101305-SILVER.png", "GOLD": "/challenges-images/101305-GOLD.png", "PLATINUM": "/challenges-images/101305-PLATINUM.png", "DIAMOND": "/challenges-images/101305-DIAMOND.png", "MASTER": "/challenges-images/101305-MASTER.png", "GRANDMASTER": "/challenges-images/101305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 125}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 101306, "name": "Can't Touch This", "description": "Menangkan game ARAM tanpa terbunuh oleh champion musuh (kamu masih boleh mengalami eksekusi)", "shortDescription": "<PERSON><PERSON> tanpa dibunuh oleh musuh", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101306-IRON.png", "BRONZE": "/challenges-images/101306-BRONZE.png", "SILVER": "/challenges-images/101306-SILVER.png", "GOLD": "/challenges-images/101306-GOLD.png", "PLATINUM": "/challenges-images/101306-PLATINUM.png", "DIAMOND": "/challenges-images/101306-DIAMOND.png", "MASTER": "/challenges-images/101306-MASTER.png", "GRANDMASTER": "/challenges-images/101306-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101306-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Untouchable"}]}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}}}, {"id": 101307, "name": "NA-RAM", "description": "Menangkan game ARAM", "shortDescription": "Menangkan game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101307-IRON.png", "BRONZE": "/challenges-images/101307-BRONZE.png", "SILVER": "/challenges-images/101307-SILVER.png", "GOLD": "/challenges-images/101307-GOLD.png", "PLATINUM": "/challenges-images/101307-PLATINUM.png", "DIAMOND": "/challenges-images/101307-DIAMOND.png", "MASTER": "/challenges-images/101307-MASTER.png", "GRANDMASTER": "/challenges-images/101307-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101307-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 35}, "GOLD": {"value": 70}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 700}, "CHALLENGER": {"value": 1000}}}, {"id": 103000, "name": "Cleverness and Creativity", "description": "Raih progres dari tantangan di grup Style, Innovation, dan Tactician", "shortDescription": "Raih progres dari tantangan di grup Style, Innovation, dan Tactician", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103000-IRON.png", "BRONZE": "/challenges-images/103000-BRONZE.png", "SILVER": "/challenges-images/103000-SILVER.png", "GOLD": "/challenges-images/103000-GOLD.png", "PLATINUM": "/challenges-images/103000-PLATINUM.png", "DIAMOND": "/challenges-images/103000-DIAMOND.png", "MASTER": "/challenges-images/103000-MASTER.png", "GRANDMASTER": "/challenges-images/103000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 50}, "SILVER": {"value": 100}, "GOLD": {"value": 230}, "PLATINUM": {"value": 400}, "DIAMOND": {"value": 675}, "MASTER": {"value": 1150, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Galaxy Brain"}]}}}, {"id": 103100, "name": "Style", "description": "Raih progres dari tantangan di grup Style", "shortDescription": "Raih progres dari tantangan di grup Style", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103100-IRON.png", "BRONZE": "/challenges-images/103100-BRONZE.png", "SILVER": "/challenges-images/103100-SILVER.png", "GOLD": "/challenges-images/103100-GOLD.png", "PLATINUM": "/challenges-images/103100-PLATINUM.png", "DIAMOND": "/challenges-images/103100-DIAMOND.png", "MASTER": "/challenges-images/103100-MASTER.png", "GRANDMASTER": "/challenges-images/103100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 45}, "PLATINUM": {"value": 65}, "DIAMOND": {"value": 110, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Stylish"}]}, "MASTER": {"value": 175}}}, {"id": 103101, "name": "Right Under Their Noses", "description": "Recall selagi tidak terlihat oleh champion musuh di sekitar", "shortDescription": "Recall selagi tidak terlihat oleh champion di sekitar", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103101-IRON.png", "BRONZE": "/challenges-images/103101-BRONZE.png", "SILVER": "/challenges-images/103101-SILVER.png", "GOLD": "/challenges-images/103101-GOLD.png", "PLATINUM": "/challenges-images/103101-PLATINUM.png", "DIAMOND": "/challenges-images/103101-DIAMOND.png", "MASTER": "/challenges-images/103101-MASTER.png", "GRANDMASTER": "/challenges-images/103101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103101-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 10}, "MASTER": {"value": 20}}}, {"id": 103102, "name": "Get On My Level", "description": "Lakukan takedown dalam waktu 5 detik setelah mendapatkan keuntungan level di 10 menit pertama game", "shortDescription": "Lakukan takedown set<PERSON>h memperoleh keunggulan level di awal", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/103102-IRON.png", "BRONZE": "/challenges-images/103102-BRONZE.png", "SILVER": "/challenges-images/103102-SILVER.png", "GOLD": "/challenges-images/103102-GOLD.png", "PLATINUM": "/challenges-images/103102-PLATINUM.png", "DIAMOND": "/challenges-images/103102-DIAMOND.png", "MASTER": "/challenges-images/103102-MASTER.png", "GRANDMASTER": "/challenges-images/103102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 120}, "CHALLENGER": {"value": 150}}}, {"id": 103103, "name": "Nowhere is Safe", "description": "Takedown champion musuh di <PERSON> mereka", "shortDescription": "Takedown champion di <PERSON>ka", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103103-IRON.png", "BRONZE": "/challenges-images/103103-BRONZE.png", "SILVER": "/challenges-images/103103-SILVER.png", "GOLD": "/challenges-images/103103-GOLD.png", "PLATINUM": "/challenges-images/103103-PLATINUM.png", "DIAMOND": "/challenges-images/103103-DIAMOND.png", "MASTER": "/challenges-images/103103-MASTER.png", "GRANDMASTER": "/challenges-images/103103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103103-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 103200, "name": "Innovation", "description": "Raih progres dari tantangan di grup Innovation", "shortDescription": "Raih progres dari tantangan di grup Innovation", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103200-IRON.png", "BRONZE": "/challenges-images/103200-BRONZE.png", "SILVER": "/challenges-images/103200-SILVER.png", "GOLD": "/challenges-images/103200-GOLD.png", "PLATINUM": "/challenges-images/103200-PLATINUM.png", "DIAMOND": "/challenges-images/103200-DIAMOND.png", "MASTER": "/challenges-images/103200-MASTER.png", "GRANDMASTER": "/challenges-images/103200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 30}, "GOLD": {"value": 85}, "PLATINUM": {"value": 175}, "DIAMOND": {"value": 280, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Innovative"}]}, "MASTER": {"value": 400}}}, {"id": 103201, "name": "Alcove Gaming", "description": "Takedown satu champion musuh di alcove", "shortDescription": "Takedown musuh di alcove", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103201-IRON.png", "BRONZE": "/challenges-images/103201-BRONZE.png", "SILVER": "/challenges-images/103201-SILVER.png", "GOLD": "/challenges-images/103201-GOLD.png", "PLATINUM": "/challenges-images/103201-PLATINUM.png", "DIAMOND": "/challenges-images/103201-DIAMOND.png", "MASTER": "/challenges-images/103201-MASTER.png", "GRANDMASTER": "/challenges-images/103201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 10}, "GOLD": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Alcove Gamer"}]}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}}}, {"id": 103202, "name": "Plant Tactics", "description": "Knock dirimu sendiri dan satu champion musuh ke arah yang berbeda setelah mengenai blast cone", "shortDescription": "Gunakan blast cone untuk kabur dari musuh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/103202-IRON.png", "BRONZE": "/challenges-images/103202-BRONZE.png", "SILVER": "/challenges-images/103202-SILVER.png", "GOLD": "/challenges-images/103202-GOLD.png", "PLATINUM": "/challenges-images/103202-PLATINUM.png", "DIAMOND": "/challenges-images/103202-DIAMOND.png", "MASTER": "/challenges-images/103202-MASTER.png", "GRANDMASTER": "/challenges-images/103202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103202-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 60}}}, {"id": 103203, "name": "Aggressive Positioning", "description": "Dapatkan multikill tepat setelah flash ke arah champion musuh", "shortDescription": "Dapatkan multikill setelah flash ke arah musuh", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103203-IRON.png", "BRONZE": "/challenges-images/103203-BRONZE.png", "SILVER": "/challenges-images/103203-SILVER.png", "GOLD": "/challenges-images/103203-GOLD.png", "PLATINUM": "/challenges-images/103203-PLATINUM.png", "DIAMOND": "/challenges-images/103203-DIAMOND.png", "MASTER": "/challenges-images/103203-MASTER.png", "GRANDMASTER": "/challenges-images/103203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Flashy"}]}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 70}, "MASTER": {"value": 125}}}, {"id": 103204, "name": "Wave Goodbye", "description": "Bunuh 20 minion dalam waktu tiga detik", "shortDescription": "Bunuh 20 minion dalam waktu 3 detik", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103204-IRON.png", "BRONZE": "/challenges-images/103204-BRONZE.png", "SILVER": "/challenges-images/103204-SILVER.png", "GOLD": "/challenges-images/103204-GOLD.png", "PLATINUM": "/challenges-images/103204-PLATINUM.png", "DIAMOND": "/challenges-images/103204-DIAMOND.png", "MASTER": "/challenges-images/103204-MASTER.png", "GRANDMASTER": "/challenges-images/103204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103204-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}}}, {"id": 103205, "name": "Not Even Close", "description": "<PERSON><PERSON> hidup dalam combat champion dengan health satu digit", "shortDescription": "<PERSON><PERSON> hidup dalam combat champion dengan health satu digit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103205-IRON.png", "BRONZE": "/challenges-images/103205-BRONZE.png", "SILVER": "/challenges-images/103205-SILVER.png", "GOLD": "/challenges-images/103205-GOLD.png", "PLATINUM": "/challenges-images/103205-PLATINUM.png", "DIAMOND": "/challenges-images/103205-DIAMOND.png", "MASTER": "/challenges-images/103205-MASTER.png", "GRANDMASTER": "/challenges-images/103205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103205-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Survivor"}]}}}, {"id": 103206, "name": "A Drake and a Hard Place", "description": "Bunuh musuh dengan bantuan Monster Epik. Monster Epik men<PERSON>, <PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Bunuh musuh dengan bantuan Monster Epik", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103206-IRON.png", "BRONZE": "/challenges-images/103206-BRONZE.png", "SILVER": "/challenges-images/103206-SILVER.png", "GOLD": "/challenges-images/103206-GOLD.png", "PLATINUM": "/challenges-images/103206-PLATINUM.png", "DIAMOND": "/challenges-images/103206-DIAMOND.png", "MASTER": "/challenges-images/103206-MASTER.png", "GRANDMASTER": "/challenges-images/103206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 18}, "GOLD": {"value": 40}, "PLATINUM": {"value": 120}, "DIAMOND": {"value": 225}, "MASTER": {"value": 420}}}, {"id": 103207, "name": "Double Ace", "description": "Ke<PERSON> tim mendapatkan ace di combat yang sama", "shortDescription": "Ke<PERSON> tim mendapatkan ace di combat yang sama", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103207-IRON.png", "BRONZE": "/challenges-images/103207-BRONZE.png", "SILVER": "/challenges-images/103207-SILVER.png", "GOLD": "/challenges-images/103207-GOLD.png", "PLATINUM": "/challenges-images/103207-PLATINUM.png", "DIAMOND": "/challenges-images/103207-DIAMOND.png", "MASTER": "/challenges-images/103207-MASTER.png", "GRANDMASTER": "/challenges-images/103207-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103207-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ace"}]}}}, {"id": 103300, "name": "Tactician", "description": "Raih progres dari tantangan di grup Tactician", "shortDescription": "Raih progres dari tantangan di grup Tactician", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103300-IRON.png", "BRONZE": "/challenges-images/103300-BRONZE.png", "SILVER": "/challenges-images/103300-SILVER.png", "GOLD": "/challenges-images/103300-GOLD.png", "PLATINUM": "/challenges-images/103300-PLATINUM.png", "DIAMOND": "/challenges-images/103300-DIAMOND.png", "MASTER": "/challenges-images/103300-MASTER.png", "GRANDMASTER": "/challenges-images/103300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tactical"}]}, "MASTER": {"value": 325}}}, {"id": 103301, "name": "Dragon's Fury", "description": "Dapatkan multikill dengan Buff Elder Dragon", "shortDescription": "Dapatkan multikill dengan Elder Dragon", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103301-IRON.png", "BRONZE": "/challenges-images/103301-BRONZE.png", "SILVER": "/challenges-images/103301-SILVER.png", "GOLD": "/challenges-images/103301-GOLD.png", "PLATINUM": "/challenges-images/103301-PLATINUM.png", "DIAMOND": "/challenges-images/103301-DIAMOND.png", "MASTER": "/challenges-images/103301-MASTER.png", "GRANDMASTER": "/challenges-images/103301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103301-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}}}, {"id": 103302, "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON>uh <PERSON>", "shortDescription": "<PERSON><PERSON>uh <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103302-IRON.png", "BRONZE": "/challenges-images/103302-BRONZE.png", "SILVER": "/challenges-images/103302-SILVER.png", "GOLD": "/challenges-images/103302-GOLD.png", "PLATINUM": "/challenges-images/103302-PLATINUM.png", "DIAMOND": "/challenges-images/103302-DIAMOND.png", "MASTER": "/challenges-images/103302-MASTER.png", "GRANDMASTER": "/challenges-images/103302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103302-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 6}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}}}, {"id": 103303, "name": "Target Selection", "description": "Takedown jungler di dekat Monster Epik yang terkena damage sebelum ia mati. Monster E<PERSON><PERSON>, <PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Takedown jungler di dekat Monster Epik yang terkena damage", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/103303-IRON.png", "BRONZE": "/challenges-images/103303-BRONZE.png", "SILVER": "/challenges-images/103303-SILVER.png", "GOLD": "/challenges-images/103303-GOLD.png", "PLATINUM": "/challenges-images/103303-PLATINUM.png", "DIAMOND": "/challenges-images/103303-DIAMOND.png", "MASTER": "/challenges-images/103303-MASTER.png", "GRANDMASTER": "/challenges-images/103303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15}, "PLATINUM": {"value": 45}, "DIAMOND": {"value": 80}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 400}}}, {"id": 103304, "name": "The Disrespect", "description": "Lakukan setidaknya 2000 damage dan hancurkan Nexus musuh selagi kalah jumlah melawan champion musuh", "shortDescription": "Hancurkan Nexus musuh selagi kalah jumlah", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103304-IRON.png", "BRONZE": "/challenges-images/103304-BRONZE.png", "SILVER": "/challenges-images/103304-SILVER.png", "GOLD": "/challenges-images/103304-GOLD.png", "PLATINUM": "/challenges-images/103304-PLATINUM.png", "DIAMOND": "/challenges-images/103304-DIAMOND.png", "MASTER": "/challenges-images/103304-MASTER.png", "GRANDMASTER": "/challenges-images/103304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103304-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 120000, "name": "Machine-Hunting Mercenary", "description": "Raih progres dari tantangan di grup Machine-Hunting Mercenary", "shortDescription": "Raih progres dari tantangan di grup Machine-Hunting Mercenary", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120000-IRON.png", "BRONZE": "/challenges-images/120000-BRONZE.png", "SILVER": "/challenges-images/120000-SILVER.png", "GOLD": "/challenges-images/120000-GOLD.png", "PLATINUM": "/challenges-images/120000-PLATINUM.png", "DIAMOND": "/challenges-images/120000-DIAMOND.png", "MASTER": "/challenges-images/120000-MASTER.png", "GRANDMASTER": "/challenges-images/120000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 75}, "PLATINUM": {"value": 120}, "DIAMOND": {"value": 180}, "MASTER": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}}}, {"id": 120001, "name": "Malfunction", "description": "Menangkan Game Co-Op vs AI", "shortDescription": "Menangkan game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120001-IRON.png", "BRONZE": "/challenges-images/120001-BRONZE.png", "SILVER": "/challenges-images/120001-SILVER.png", "GOLD": "/challenges-images/120001-GOLD.png", "PLATINUM": "/challenges-images/120001-PLATINUM.png", "DIAMOND": "/challenges-images/120001-DIAMOND.png", "MASTER": "/challenges-images/120001-MASTER.png", "GRANDMASTER": "/challenges-images/120001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 30}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 150}}}, {"id": 120002, "name": "Protean <PERSON>", "description": "Menangkan game Co-Op vs AI dengan berbagai champion", "shortDescription": "Menangkan game dengan <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120002-IRON.png", "BRONZE": "/challenges-images/120002-BRONZE.png", "SILVER": "/challenges-images/120002-SILVER.png", "GOLD": "/challenges-images/120002-GOLD.png", "PLATINUM": "/challenges-images/120002-PLATINUM.png", "DIAMOND": "/challenges-images/120002-DIAMOND.png", "MASTER": "/challenges-images/120002-MASTER.png", "GRANDMASTER": "/challenges-images/120002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 75}, "MASTER": {"value": 100}}}, {"id": 120003, "name": "<PERSON><PERSON>", "description": "Dapatkan kill di Game Co-Op vs AI", "shortDescription": "<PERSON><PERSON><PERSON> kill", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120003-IRON.png", "BRONZE": "/challenges-images/120003-BRONZE.png", "SILVER": "/challenges-images/120003-SILVER.png", "GOLD": "/challenges-images/120003-GOLD.png", "PLATINUM": "/challenges-images/120003-PLATINUM.png", "DIAMOND": "/challenges-images/120003-DIAMOND.png", "MASTER": "/challenges-images/120003-MASTER.png", "GRANDMASTER": "/challenges-images/120003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 150}, "SILVER": {"value": 300}, "GOLD": {"value": 750}, "PLATINUM": {"value": 2500}, "DIAMOND": {"value": 5000}, "MASTER": {"value": 7500}}}, {"id": 121000, "name": "Glorious Evolution", "description": "Raih progres dari tantangan di grup Glorious Evolution", "shortDescription": "Raih progres dari tantangan di grup Glorious Evolution", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121000-IRON.png", "BRONZE": "/challenges-images/121000-BRONZE.png", "SILVER": "/challenges-images/121000-SILVER.png", "GOLD": "/challenges-images/121000-GOLD.png", "PLATINUM": "/challenges-images/121000-PLATINUM.png", "DIAMOND": "/challenges-images/121000-DIAMOND.png", "MASTER": "/challenges-images/121000-MASTER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 75}, "PLATINUM": {"value": 120}, "DIAMOND": {"value": 180}, "MASTER": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Turing Tested"}]}}}, {"id": 121001, "name": "<PERSON>(t)died", "description": "Menangkan game Co-op vs. AI (Intermediate) tanpa mati dan setidaknya partisipasi kill 20%", "shortDescription": "Menangkan game Co-op vs. AI tanpa mati & partisipasi kill 20%", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121001-IRON.png", "BRONZE": "/challenges-images/121001-BRONZE.png", "SILVER": "/challenges-images/121001-SILVER.png", "GOLD": "/challenges-images/121001-GOLD.png", "PLATINUM": "/challenges-images/121001-PLATINUM.png", "DIAMOND": "/challenges-images/121001-DIAMOND.png", "MASTER": "/challenges-images/121001-MASTER.png", "GRANDMASTER": "/challenges-images/121001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/121001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 30}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 125}}}, {"id": 121002, "name": "Error 410: <PERSON><PERSON>", "description": "Se<PERSON><PERSON> jungler, bunuh jungler musuh di dalam jungle mereka sebelum 10 menit di game Co-Op vs. AI (Intermediate)", "shortDescription": "Sebagai jungler, bunuh jungler musuh di dalam jungle mereka sebelum 10 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121002-IRON.png", "BRONZE": "/challenges-images/121002-BRONZE.png", "SILVER": "/challenges-images/121002-SILVER.png", "GOLD": "/challenges-images/121002-GOLD.png", "PLATINUM": "/challenges-images/121002-PLATINUM.png", "DIAMOND": "/challenges-images/121002-DIAMOND.png", "MASTER": "/challenges-images/121002-MASTER.png", "GRANDMASTER": "/challenges-images/121002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/121002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 12}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 50}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 250}}}, {"id": 121003, "name": "That'll Machine Learn You", "description": "Hancurkan tiga inhibitor dalam waktu kurang dari 25 menit di game Co-Op vs. AI (Intermediate)", "shortDescription": "Hancurkan 3 inhibitor dalam waktu kurang dari 25 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121003-IRON.png", "BRONZE": "/challenges-images/121003-BRONZE.png", "SILVER": "/challenges-images/121003-SILVER.png", "GOLD": "/challenges-images/121003-GOLD.png", "PLATINUM": "/challenges-images/121003-PLATINUM.png", "DIAMOND": "/challenges-images/121003-DIAMOND.png", "MASTER": "/challenges-images/121003-MASTER.png", "GRANDMASTER": "/challenges-images/121003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/121003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 12}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 60}}}, {"id": 2, "name": "EXPERTISE", "description": "", "shortDescription": "Capstone EXPERTISE", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 125}, "BRONZE": {"value": 275}, "SILVER": {"value": 500}, "GOLD": {"value": 1100}, "PLATINUM": {"value": 1800}, "DIAMOND": {"value": 3200}, "MASTER": {"value": 5500}}}, {"id": 201000, "name": "Adept", "description": "Raih progres dari tantangan di grup Adept", "shortDescription": "Raih progres dari tantangan di grup Adept", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/201000-IRON.png", "BRONZE": "/challenges-images/201000-BRONZE.png", "SILVER": "/challenges-images/201000-SILVER.png", "GOLD": "/challenges-images/201000-GOLD.png", "PLATINUM": "/challenges-images/201000-PLATINUM.png", "DIAMOND": "/challenges-images/201000-DIAMOND.png", "MASTER": "/challenges-images/201000-MASTER.png", "GRANDMASTER": "/challenges-images/201000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 70}, "PLATINUM": {"value": 110}, "DIAMOND": {"value": 200}, "MASTER": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}}}, {"id": 201001, "name": "Shoddy Construction", "description": "Lakukan takedown (kill atau assist) pada turret pertama di game", "shortDescription": "Takedown turret pertama", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201001-IRON.png", "BRONZE": "/challenges-images/201001-BRONZE.png", "SILVER": "/challenges-images/201001-SILVER.png", "GOLD": "/challenges-images/201001-GOLD.png", "PLATINUM": "/challenges-images/201001-PLATINUM.png", "DIAMOND": "/challenges-images/201001-DIAMOND.png", "MASTER": "/challenges-images/201001-MASTER.png", "GRANDMASTER": "/challenges-images/201001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 201002, "name": "Lethal Efficiency", "description": "<PERSON><PERSON>uh 80 minion jalur dalam waktu 10 menit", "shortDescription": "<PERSON><PERSON>uh 80 minion dalam waktu 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201002-IRON.png", "BRONZE": "/challenges-images/201002-BRONZE.png", "SILVER": "/challenges-images/201002-SILVER.png", "GOLD": "/challenges-images/201002-GOLD.png", "PLATINUM": "/challenges-images/201002-PLATINUM.png", "DIAMOND": "/challenges-images/201002-DIAMOND.png", "MASTER": "/challenges-images/201002-MASTER.png", "GRANDMASTER": "/challenges-images/201002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201002-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Minion"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 125}}}, {"id": 201003, "name": "Unkillable Demon King", "description": "Menangkan game tanpa mati dan dengan setidaknya partisipasi kill 30%", "shortDescription": "Menangkan game tanpa mati dan setidaknya partisipasi kill 30%", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201003-IRON.png", "BRONZE": "/challenges-images/201003-BRONZE.png", "SILVER": "/challenges-images/201003-SILVER.png", "GOLD": "/challenges-images/201003-GOLD.png", "PLATINUM": "/challenges-images/201003-PLATINUM.png", "DIAMOND": "/challenges-images/201003-DIAMOND.png", "MASTER": "/challenges-images/201003-MASTER.png", "GRANDMASTER": "/challenges-images/201003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201003-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 7, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Unkillable Demon"}]}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 100}}}, {"id": 201004, "name": "Death Incarnate", "description": "Lakukan 12 takedown (kill atau assist) pada champion musuh dalam 15 menit", "shortDescription": "Takedown 12 champion da<PERSON> waktu 15 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201004-IRON.png", "BRONZE": "/challenges-images/201004-BRONZE.png", "SILVER": "/challenges-images/201004-SILVER.png", "GOLD": "/challenges-images/201004-GOLD.png", "PLATINUM": "/challenges-images/201004-PLATINUM.png", "DIAMOND": "/challenges-images/201004-DIAMOND.png", "MASTER": "/challenges-images/201004-MASTER.png", "GRANDMASTER": "/challenges-images/201004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201004-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 5}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 28}, "MASTER": {"value": 48}, "GRANDMASTER": {"value": 64}, "CHALLENGER": {"value": 96}}}, {"id": 202000, "name": "Ascendant", "description": "<PERSON>h progres dari tantangan di grup Domination, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON><PERSON>", "shortDescription": "<PERSON>h progres dari tantangan di grup Domination, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON><PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202000-IRON.png", "BRONZE": "/challenges-images/202000-BRONZE.png", "SILVER": "/challenges-images/202000-SILVER.png", "GOLD": "/challenges-images/202000-GOLD.png", "PLATINUM": "/challenges-images/202000-PLATINUM.png", "DIAMOND": "/challenges-images/202000-DIAMOND.png", "MASTER": "/challenges-images/202000-MASTER.png", "GRANDMASTER": "/challenges-images/202000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 75}, "SILVER": {"value": 120}, "GOLD": {"value": 280}, "PLATINUM": {"value": 450}, "DIAMOND": {"value": 825}, "MASTER": {"value": 1450, "rewards": [{"category": "TITLE", "quantity": 1, "title": "G.O.A.T."}]}}}, {"id": 202100, "name": "Domination", "description": "Raih progres dari tantangan di grup Domination", "shortDescription": "Raih progres dari tantangan di grup Domination", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202100-IRON.png", "BRONZE": "/challenges-images/202100-BRONZE.png", "SILVER": "/challenges-images/202100-SILVER.png", "GOLD": "/challenges-images/202100-GOLD.png", "PLATINUM": "/challenges-images/202100-PLATINUM.png", "DIAMOND": "/challenges-images/202100-DIAMOND.png", "MASTER": "/challenges-images/202100-MASTER.png", "GRANDMASTER": "/challenges-images/202100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Dominant"}]}, "MASTER": {"value": 400}}}, {"id": 202101, "name": "Flame Horizon", "description": "Menangkan game dengan memiliki setidaknya 100 CS lebih unggul daripada musuh dengan role yang sama kapan pun dalam game", "shortDescription": "Menangkan game dengan 100+ CS lebih banyak daripada musuh dengan role yang sama", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202101-IRON.png", "BRONZE": "/challenges-images/202101-BRONZE.png", "SILVER": "/challenges-images/202101-SILVER.png", "GOLD": "/challenges-images/202101-GOLD.png", "PLATINUM": "/challenges-images/202101-PLATINUM.png", "DIAMOND": "/challenges-images/202101-DIAMOND.png", "MASTER": "/challenges-images/202101-MASTER.png", "GRANDMASTER": "/challenges-images/202101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202101-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Spitfire"}]}, "DIAMOND": {"value": 35}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 250}}}, {"id": 202102, "name": "Lane Domination", "description": "<PERSON><PERSON><PERSON> fase pembers<PERSON>an jalur (14 menit) dengan 20% gold dan XP lebih banyak daripada musuh dengan role yang sama", "shortDescription": "Unggul 20% untuk Gold dan XP dalam 14 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202102-IRON.png", "BRONZE": "/challenges-images/202102-BRONZE.png", "SILVER": "/challenges-images/202102-SILVER.png", "GOLD": "/challenges-images/202102-GOLD.png", "PLATINUM": "/challenges-images/202102-PLATINUM.png", "DIAMOND": "/challenges-images/202102-DIAMOND.png", "MASTER": "/challenges-images/202102-MASTER.png", "GRANDMASTER": "/challenges-images/202102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 8}, "GOLD": {"value": 20}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 100}, "MASTER": {"value": 180}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 350}}}, {"id": 202103, "name": "Lane Command", "description": "<PERSON><PERSON><PERSON> fase pembersihan jalur awal (7 menit) dengan 20% gold dan XP lebih banyak daripada musuh dengan role yang sama", "shortDescription": "Unggul 20% untuk Gold dan XP dalam 7 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202103-IRON.png", "BRONZE": "/challenges-images/202103-BRONZE.png", "SILVER": "/challenges-images/202103-SILVER.png", "GOLD": "/challenges-images/202103-GOLD.png", "PLATINUM": "/challenges-images/202103-PLATINUM.png", "DIAMOND": "/challenges-images/202103-DIAMOND.png", "MASTER": "/challenges-images/202103-MASTER.png", "GRANDMASTER": "/challenges-images/202103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 70}, "MASTER": {"value": 125}, "GRANDMASTER": {"value": 175}, "CHALLENGER": {"value": 250}}}, {"id": 202104, "name": "The Brush Has Eyes", "description": "Akhiri game dengan Skor Vision 20% lebih banyak daripada musuh dengan role yang sama", "shortDescription": "Selesaikan game dengan Skor Vision unggul 20%", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202104-IRON.png", "BRONZE": "/challenges-images/202104-BRONZE.png", "SILVER": "/challenges-images/202104-SILVER.png", "GOLD": "/challenges-images/202104-GOLD.png", "PLATINUM": "/challenges-images/202104-PLATINUM.png", "DIAMOND": "/challenges-images/202104-DIAMOND.png", "MASTER": "/challenges-images/202104-MASTER.png", "GRANDMASTER": "/challenges-images/202104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 45}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 750}, "CHALLENGER": {"value": 1000}}}, {"id": 202105, "name": "On Another Level", "description": "Unggul tiga level atau lebih dari musuh dengan role yang sama kapan pun dalam game", "shortDescription": "Unggul tiga level daripada musuh dengan role yang sama kapan pun", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202105-IRON.png", "BRONZE": "/challenges-images/202105-BRONZE.png", "SILVER": "/challenges-images/202105-SILVER.png", "GOLD": "/challenges-images/202105-GOLD.png", "PLATINUM": "/challenges-images/202105-PLATINUM.png", "DIAMOND": "/challenges-images/202105-DIAMOND.png", "MASTER": "/challenges-images/202105-MASTER.png", "GRANDMASTER": "/challenges-images/202105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 135}, "MASTER": {"value": 240}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 202200, "name": "Unrivaled", "description": "Raih progres dari tantangan di grup Unrivaled", "shortDescription": "Raih progres dari tantangan di grup Unrivaled", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202200-IRON.png", "BRONZE": "/challenges-images/202200-BRONZE.png", "SILVER": "/challenges-images/202200-SILVER.png", "GOLD": "/challenges-images/202200-GOLD.png", "PLATINUM": "/challenges-images/202200-PLATINUM.png", "DIAMOND": "/challenges-images/202200-DIAMOND.png", "MASTER": "/challenges-images/202200-MASTER.png", "GRANDMASTER": "/challenges-images/202200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Unrivaled"}]}, "MASTER": {"value": 400}}}, {"id": 2022000, "name": "2022 Seasonal", "description": "Raih progres dari tantangan di grup 2022 Seasonal", "shortDescription": "Raih progres dari tantangan di grup 2022 Seasonal", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2022000-IRON.png", "BRONZE": "/challenges-images/2022000-BRONZE.png", "SILVER": "/challenges-images/2022000-SILVER.png", "GOLD": "/challenges-images/2022000-GOLD.png", "PLATINUM": "/challenges-images/2022000-PLATINUM.png", "DIAMOND": "/challenges-images/2022000-DIAMOND.png", "MASTER": "/challenges-images/2022000-MASTER.png", "GRANDMASTER": "/challenges-images/2022000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 75}, "SILVER": {"value": 115}, "GOLD": {"value": 265}, "PLATINUM": {"value": 455}, "DIAMOND": {"value": 860}, "MASTER": {"value": 1400, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Challenjour '22"}]}}}, {"id": 2022001, "name": "All Random All Champions: 2022", "description": "Raih grade S- atau lebih tinggi pada berbagai champion di ARAM", "shortDescription": "Raih grade S- dengan <em>berbagai champion</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022001-IRON.png", "BRONZE": "/challenges-images/2022001-BRONZE.png", "SILVER": "/challenges-images/2022001-SILVER.png", "GOLD": "/challenges-images/2022001-GOLD.png", "PLATINUM": "/challenges-images/2022001-PLATINUM.png", "DIAMOND": "/challenges-images/2022001-DIAMOND.png", "MASTER": "/challenges-images/2022001-MASTER.png", "GRANDMASTER": "/challenges-images/2022001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 15}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2022002, "name": "Rapid Demolition: 2022", "description": "Hancurkan turret pertama di ARAM sebelum 5 menit berlalu", "shortDescription": "Hancurkan turret pertama sebelum 5 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022002-IRON.png", "BRONZE": "/challenges-images/2022002-BRONZE.png", "SILVER": "/challenges-images/2022002-SILVER.png", "GOLD": "/challenges-images/2022002-GOLD.png", "PLATINUM": "/challenges-images/2022002-PLATINUM.png", "DIAMOND": "/challenges-images/2022002-DIAMOND.png", "MASTER": "/challenges-images/2022002-MASTER.png", "GRANDMASTER": "/challenges-images/2022002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022002-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 15}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2022003, "name": "Top Performer: 2022", "description": "Raih total grade S- atau lebih tinggi di Summoner's Rift", "shortDescription": "Raih grade S- atau lebih tinggi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022003-IRON.png", "BRONZE": "/challenges-images/2022003-BRONZE.png", "SILVER": "/challenges-images/2022003-SILVER.png", "GOLD": "/challenges-images/2022003-GOLD.png", "PLATINUM": "/challenges-images/2022003-PLATINUM.png", "DIAMOND": "/challenges-images/2022003-DIAMOND.png", "MASTER": "/challenges-images/2022003-MASTER.png", "GRANDMASTER": "/challenges-images/2022003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 100}}}, {"id": 2022004, "name": "Unkillable Demon King: 2022", "description": "Menangkan game tanpa mati dan dengan setidaknya partisipasi kill 30% di <PERSON><PERSON><PERSON><PERSON>'s Rift", "shortDescription": "Menangkan game tanpa mati dan partisipasi kill 30%", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022004-IRON.png", "BRONZE": "/challenges-images/2022004-BRONZE.png", "SILVER": "/challenges-images/2022004-SILVER.png", "GOLD": "/challenges-images/2022004-GOLD.png", "PLATINUM": "/challenges-images/2022004-PLATINUM.png", "DIAMOND": "/challenges-images/2022004-DIAMOND.png", "MASTER": "/challenges-images/2022004-MASTER.png", "GRANDMASTER": "/challenges-images/2022004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022004-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 4}, "MASTER": {"value": 6}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 12}}}, {"id": 2022005, "name": "Lane Command: 2022", "description": "<PERSON><PERSON><PERSON> fase pembersihan jalur awal (7 menit) dengan 20% gold dan XP lebih banyak daripada musuh dengan role yang sama di <PERSON>mmone<PERSON>'s Rift", "shortDescription": "Unggul 20% untuk Gold dan XP dalam 7 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022005-IRON.png", "BRONZE": "/challenges-images/2022005-BRONZE.png", "SILVER": "/challenges-images/2022005-SILVER.png", "GOLD": "/challenges-images/2022005-GOLD.png", "PLATINUM": "/challenges-images/2022005-PLATINUM.png", "DIAMOND": "/challenges-images/2022005-DIAMOND.png", "MASTER": "/challenges-images/2022005-MASTER.png", "GRANDMASTER": "/challenges-images/2022005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 7}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 2022006, "name": "Pain Prescriber: 2022", "description": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game di Summoner's Rift", "shortDescription": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022006-IRON.png", "BRONZE": "/challenges-images/2022006-BRONZE.png", "SILVER": "/challenges-images/2022006-SILVER.png", "GOLD": "/challenges-images/2022006-GOLD.png", "PLATINUM": "/challenges-images/2022006-PLATINUM.png", "DIAMOND": "/challenges-images/2022006-DIAMOND.png", "MASTER": "/challenges-images/2022006-MASTER.png", "GRANDMASTER": "/challenges-images/2022006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 115}}}, {"id": 2022007, "name": "Prodigy: 2022", "description": "<PERSON>adi Legendary (8 kill ekstrak) dalam waktu 15 menit di <PERSON><PERSON><PERSON><PERSON>'s Rift", "shortDescription": "Jadi Legendary dalam waktu 15 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2022007-IRON.png", "BRONZE": "/challenges-images/2022007-BRONZE.png", "SILVER": "/challenges-images/2022007-SILVER.png", "GOLD": "/challenges-images/2022007-GOLD.png", "PLATINUM": "/challenges-images/2022007-PLATINUM.png", "DIAMOND": "/challenges-images/2022007-DIAMOND.png", "MASTER": "/challenges-images/2022007-MASTER.png", "GRANDMASTER": "/challenges-images/2022007-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022007-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 2022008, "name": "One Stone: 2022", "description": "Bunuh 2 pema<PERSON> dengan cast ability yang sama di <PERSON>'s <PERSON>ift", "shortDescription": "Bunuh 2 pemain dengan satu cast ability", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022008-IRON.png", "BRONZE": "/challenges-images/2022008-BRONZE.png", "SILVER": "/challenges-images/2022008-SILVER.png", "GOLD": "/challenges-images/2022008-GOLD.png", "PLATINUM": "/challenges-images/2022008-PLATINUM.png", "DIAMOND": "/challenges-images/2022008-DIAMOND.png", "MASTER": "/challenges-images/2022008-MASTER.png", "GRANDMASTER": "/challenges-images/2022008-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022008-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 12}, "MASTER": {"value": 18}, "GRANDMASTER": {"value": 25}, "CHALLENGER": {"value": 30}}}, {"id": 2022009, "name": "Solo Bolo: 2022", "description": "<PERSON><PERSON><PERSON> solo kill di <PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON> (tan<PERSON> assist dari champion <PERSON><PERSON><PERSON>)", "shortDescription": "<PERSON><PERSON><PERSON> solo kill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022009-IRON.png", "BRONZE": "/challenges-images/2022009-BRONZE.png", "SILVER": "/challenges-images/2022009-SILVER.png", "GOLD": "/challenges-images/2022009-GOLD.png", "PLATINUM": "/challenges-images/2022009-PLATINUM.png", "DIAMOND": "/challenges-images/2022009-DIAMOND.png", "MASTER": "/challenges-images/2022009-MASTER.png", "GRANDMASTER": "/challenges-images/2022009-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022009-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 100}, "GOLD": {"value": 225}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 600}, "MASTER": {"value": 900}, "GRANDMASTER": {"value": 1200}, "CHALLENGER": {"value": 1500}}}, {"id": 202201, "name": "<PERSON><PERSON><PERSON>", "description": "Miliki Skor Crowd Control tertinggi di game", "shortDescription": "Dapatkan skor crowd control tertinggi dalam game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202201-IRON.png", "BRONZE": "/challenges-images/202201-BRONZE.png", "SILVER": "/challenges-images/202201-SILVER.png", "GOLD": "/challenges-images/202201-GOLD.png", "PLATINUM": "/challenges-images/202201-PLATINUM.png", "DIAMOND": "/challenges-images/202201-DIAMOND.png", "MASTER": "/challenges-images/202201-MASTER.png", "GRANDMASTER": "/challenges-images/202201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 160}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 2022010, "name": "Crab Wrangler: 2022", "description": "Bunuh 2 scuttle crab awal yang muncul di <PERSON>mm<PERSON>r's Rift", "shortDescription": "Bunuh kedua scuttle crab pertama yang muncul", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022010-IRON.png", "BRONZE": "/challenges-images/2022010-BRONZE.png", "SILVER": "/challenges-images/2022010-SILVER.png", "GOLD": "/challenges-images/2022010-GOLD.png", "PLATINUM": "/challenges-images/2022010-PLATINUM.png", "DIAMOND": "/challenges-images/2022010-DIAMOND.png", "MASTER": "/challenges-images/2022010-MASTER.png", "GRANDMASTER": "/challenges-images/2022010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 8}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 100}}}, {"id": 2022011, "name": "All-Seeing: 2022", "description": "Miliki lebih dari 2 Skor Vision per menit di Summoner's Rift", "shortDescription": "Miliki lebih dari 2 Skor Vision per menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022011-IRON.png", "BRONZE": "/challenges-images/2022011-BRONZE.png", "SILVER": "/challenges-images/2022011-SILVER.png", "GOLD": "/challenges-images/2022011-GOLD.png", "PLATINUM": "/challenges-images/2022011-PLATINUM.png", "DIAMOND": "/challenges-images/2022011-DIAMOND.png", "MASTER": "/challenges-images/2022011-MASTER.png", "GRANDMASTER": "/challenges-images/2022011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022011-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 12}, "GOLD": {"value": 22}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 60}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 120}, "CHALLENGER": {"value": 150}}}, {"id": 2022014, "name": "Clutch Steal: 2022", "description": "Curi Monster Epik di Summoner's Rift. Monster Epik men<PERSON>, Rift <PERSON>, dan <PERSON>.", "shortDescription": "Curi monster epik", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022014-IRON.png", "BRONZE": "/challenges-images/2022014-BRONZE.png", "SILVER": "/challenges-images/2022014-SILVER.png", "GOLD": "/challenges-images/2022014-GOLD.png", "PLATINUM": "/challenges-images/2022014-PLATINUM.png", "DIAMOND": "/challenges-images/2022014-DIAMOND.png", "MASTER": "/challenges-images/2022014-MASTER.png", "GRANDMASTER": "/challenges-images/2022014-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022014-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 8}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 2022015, "name": "Killing Spree Spree: 2022", "description": "Lakukan killing spree di <PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON> (boleh lebih dari satu, satu untuk killing spree, satu untuk Rampage, dll.)", "shortDescription": "<PERSON><PERSON><PERSON> killing spree", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022015-IRON.png", "BRONZE": "/challenges-images/2022015-BRONZE.png", "SILVER": "/challenges-images/2022015-SILVER.png", "GOLD": "/challenges-images/2022015-GOLD.png", "PLATINUM": "/challenges-images/2022015-PLATINUM.png", "DIAMOND": "/challenges-images/2022015-DIAMOND.png", "MASTER": "/challenges-images/2022015-MASTER.png", "GRANDMASTER": "/challenges-images/2022015-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022015-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 45}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 120}, "MASTER": {"value": 180}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 400}}}, {"id": 2022016, "name": "PENTAKIIIIIIIIL!!: 2022", "description": "Lakukan Pentakill di Summoner's Rift", "shortDescription": "Lakukan <PERSON>kill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022016-IRON.png", "BRONZE": "/challenges-images/2022016-BRONZE.png", "SILVER": "/challenges-images/2022016-SILVER.png", "GOLD": "/challenges-images/2022016-GOLD.png", "PLATINUM": "/challenges-images/2022016-PLATINUM.png", "DIAMOND": "/challenges-images/2022016-DIAMOND.png", "MASTER": "/challenges-images/2022016-MASTER.png", "GRANDMASTER": "/challenges-images/2022016-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022016-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}, "GRANDMASTER": {"value": 4}, "CHALLENGER": {"value": 5}}}, {"id": 2022017, "name": "Leading by Example: 2022", "description": "Dapatkan 12 assist atau lebih tanpa mati di Summoner's Rift.", "shortDescription": "Dapatkan assist streak 12+ tanpa mati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022017-IRON.png", "BRONZE": "/challenges-images/2022017-BRONZE.png", "SILVER": "/challenges-images/2022017-SILVER.png", "GOLD": "/challenges-images/2022017-GOLD.png", "PLATINUM": "/challenges-images/2022017-PLATINUM.png", "DIAMOND": "/challenges-images/2022017-DIAMOND.png", "MASTER": "/challenges-images/2022017-MASTER.png", "GRANDMASTER": "/challenges-images/2022017-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022017-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 60}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 115}, "CHALLENGER": {"value": 155}}}, {"id": 2022018, "name": "Soul Sweep: 2022", "description": "K<PERSON>m Dragon Soul tanpa tim musuh mengalahkan satu dragon pun di <PERSON><PERSON><PERSON><PERSON>'s Rift.", "shortDescription": "<PERSON><PERSON>m Dragon Soul 4-0", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022018-IRON.png", "BRONZE": "/challenges-images/2022018-BRONZE.png", "SILVER": "/challenges-images/2022018-SILVER.png", "GOLD": "/challenges-images/2022018-GOLD.png", "PLATINUM": "/challenges-images/2022018-PLATINUM.png", "DIAMOND": "/challenges-images/2022018-DIAMOND.png", "MASTER": "/challenges-images/2022018-MASTER.png", "GRANDMASTER": "/challenges-images/2022018-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022018-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 6}, "MASTER": {"value": 7}, "GRANDMASTER": {"value": 8}, "CHALLENGER": {"value": 10}}}, {"id": 2022019, "name": "Power Pair: 2022", "description": "Raih kemenangan dengan premade party berisi dua pemain di mode ranked", "shortDescription": "<PERSON><PERSON> dengan premade party berisi dua pemain di mode ranked", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022019-IRON.png", "BRONZE": "/challenges-images/2022019-BRONZE.png", "SILVER": "/challenges-images/2022019-SILVER.png", "GOLD": "/challenges-images/2022019-GOLD.png", "PLATINUM": "/challenges-images/2022019-PLATINUM.png", "DIAMOND": "/challenges-images/2022019-DIAMOND.png", "MASTER": "/challenges-images/2022019-MASTER.png", "GRANDMASTER": "/challenges-images/2022019-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022019-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 80}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 125}, "CHALLENGER": {"value": 150}}}, {"id": 202202, "name": "Darkness Distributer", "description": "Takedown ward paling banyak dalam game (kamu harus menghancurkan setidaknya satu ward)", "shortDescription": "Takedown ward paling banyak dalam game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202202-IRON.png", "BRONZE": "/challenges-images/202202-BRONZE.png", "SILVER": "/challenges-images/202202-SILVER.png", "GOLD": "/challenges-images/202202-GOLD.png", "PLATINUM": "/challenges-images/202202-PLATINUM.png", "DIAMOND": "/challenges-images/202202-DIAMOND.png", "MASTER": "/challenges-images/202202-MASTER.png", "GRANDMASTER": "/challenges-images/202202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 6}, "SILVER": {"value": 18}, "GOLD": {"value": 36}, "PLATINUM": {"value": 120, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Scout"}]}, "DIAMOND": {"value": 240}, "MASTER": {"value": 400}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 2022020, "name": "Clash Champion: 2022", "description": "Menangkan bracket Clash", "shortDescription": "Menangkan bracket Clash", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022020-IRON.png", "BRONZE": "/challenges-images/2022020-BRONZE.png", "SILVER": "/challenges-images/2022020-SILVER.png", "GOLD": "/challenges-images/2022020-GOLD.png", "PLATINUM": "/challenges-images/2022020-PLATINUM.png", "DIAMOND": "/challenges-images/2022020-DIAMOND.png", "MASTER": "/challenges-images/2022020-MASTER.png", "GRANDMASTER": "/challenges-images/2022020-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022020-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 5}, "SILVER": {"value": 8}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 60}, "CHALLENGER": {"value": 70}}}, {"id": 202203, "name": "Pain Prescriber", "description": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game", "shortDescription": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202203-IRON.png", "BRONZE": "/challenges-images/202203-BRONZE.png", "SILVER": "/challenges-images/202203-SILVER.png", "GOLD": "/challenges-images/202203-GOLD.png", "PLATINUM": "/challenges-images/202203-PLATINUM.png", "DIAMOND": "/challenges-images/202203-DIAMOND.png", "MASTER": "/challenges-images/202203-MASTER.png", "GRANDMASTER": "/challenges-images/202203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 250}}}, {"id": 202204, "name": "Damage Dispenser", "description": "Hasilkan lebih dari 700 Damage Per Menit", "shortDescription": "Hasilkan lebih dari 700 Damage Per Menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202204-IRON.png", "BRONZE": "/challenges-images/202204-BRONZE.png", "SILVER": "/challenges-images/202204-SILVER.png", "GOLD": "/challenges-images/202204-GOLD.png", "PLATINUM": "/challenges-images/202204-PLATINUM.png", "DIAMOND": "/challenges-images/202204-DIAMOND.png", "MASTER": "/challenges-images/202204-MASTER.png", "GRANDMASTER": "/challenges-images/202204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 275}, "MASTER": {"value": 480}, "GRANDMASTER": {"value": 640}, "CHALLENGER": {"value": 960}}}, {"id": 202205, "name": "Meat Shield", "description": "Menangkan game dengan kamu menerima 35% dari damage yang dihasilkan pada champion di timmu", "shortDescription": "Menangkan game dengan menerima setidaknya 35% dari damage tim", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202205-IRON.png", "BRONZE": "/challenges-images/202205-BRONZE.png", "SILVER": "/challenges-images/202205-SILVER.png", "GOLD": "/challenges-images/202205-GOLD.png", "PLATINUM": "/challenges-images/202205-PLATINUM.png", "DIAMOND": "/challenges-images/202205-DIAMOND.png", "MASTER": "/challenges-images/202205-MASTER.png", "GRANDMASTER": "/challenges-images/202205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202205-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 202300, "name": "Perfection", "description": "Raih progres dari tantangan di grup Perfection", "shortDescription": "Raih progres dari tantangan di grup Perfection", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202300-IRON.png", "BRONZE": "/challenges-images/202300-BRONZE.png", "SILVER": "/challenges-images/202300-SILVER.png", "GOLD": "/challenges-images/202300-GOLD.png", "PLATINUM": "/challenges-images/202300-PLATINUM.png", "DIAMOND": "/challenges-images/202300-DIAMOND.png", "MASTER": "/challenges-images/202300-MASTER.png", "GRANDMASTER": "/challenges-images/202300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 65}, "PLATINUM": {"value": 105}, "DIAMOND": {"value": 190, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Perfect"}]}, "MASTER": {"value": 400}}}, {"id": 2023000, "name": "2023 Seasonal", "description": "Raih progres dari tantangan di grup 2023 Seasonal", "shortDescription": "Raih progres dari tantangan di grup 2023 Seasonal", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023000-IRON.png", "BRONZE": "/challenges-images/2023000-BRONZE.png", "SILVER": "/challenges-images/2023000-SILVER.png", "GOLD": "/challenges-images/2023000-GOLD.png", "PLATINUM": "/challenges-images/2023000-PLATINUM.png", "DIAMOND": "/challenges-images/2023000-DIAMOND.png", "MASTER": "/challenges-images/2023000-MASTER.png", "GRANDMASTER": "/challenges-images/2023000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 70}, "SILVER": {"value": 105}, "GOLD": {"value": 235}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 670}, "MASTER": {"value": 1600, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Challenjour '23"}]}}}, {"id": 2023001, "name": "Death Fears Me: 2023", "description": "Menangkan game dengan 0 death dan setidaknya partisipasi kill 30%", "shortDescription": "Menangkan game dengan 0 death dan setidaknya partisipasi kill 30%", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023001-IRON.png", "BRONZE": "/challenges-images/2023001-BRONZE.png", "SILVER": "/challenges-images/2023001-SILVER.png", "GOLD": "/challenges-images/2023001-GOLD.png", "PLATINUM": "/challenges-images/2023001-PLATINUM.png", "DIAMOND": "/challenges-images/2023001-DIAMOND.png", "MASTER": "/challenges-images/2023001-MASTER.png", "GRANDMASTER": "/challenges-images/2023001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023001-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023002, "name": "It's All Me: 2023", "description": "<PERSON><PERSON><PERSON> 40% atau lebih dari total damage timmu pada champion di ARAM", "shortDescription": "<PERSON><PERSON><PERSON> 40%+ dari damage champion timmu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023002-IRON.png", "BRONZE": "/challenges-images/2023002-BRONZE.png", "SILVER": "/challenges-images/2023002-SILVER.png", "GOLD": "/challenges-images/2023002-GOLD.png", "PLATINUM": "/challenges-images/2023002-PLATINUM.png", "DIAMOND": "/challenges-images/2023002-DIAMOND.png", "MASTER": "/challenges-images/2023002-MASTER.png", "GRANDMASTER": "/challenges-images/2023002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023002-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023003, "name": "Outmanned, Never Outgunned: 2023", "description": "Dapatkan kill saat lebih banyak champion musuh da<PERSON>ada sekutu di sekitar", "shortDescription": "Dapatkan kill saat kalah jumlah", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023003-IRON.png", "BRONZE": "/challenges-images/2023003-BRONZE.png", "SILVER": "/challenges-images/2023003-SILVER.png", "GOLD": "/challenges-images/2023003-GOLD.png", "PLATINUM": "/challenges-images/2023003-PLATINUM.png", "DIAMOND": "/challenges-images/2023003-DIAMOND.png", "MASTER": "/challenges-images/2023003-MASTER.png", "GRANDMASTER": "/challenges-images/2023003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 150}, "MASTER": {"value": 200}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 300}}}, {"id": 2023004, "name": "Objective Secured: 2023", "description": "Curi dua Monster Epik dalam satu game. Monster Epik mencakup <PERSON>, <PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Curi dua Monster Epik dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023004-IRON.png", "BRONZE": "/challenges-images/2023004-BRONZE.png", "SILVER": "/challenges-images/2023004-SILVER.png", "GOLD": "/challenges-images/2023004-GOLD.png", "PLATINUM": "/challenges-images/2023004-PLATINUM.png", "DIAMOND": "/challenges-images/2023004-DIAMOND.png", "MASTER": "/challenges-images/2023004-MASTER.png", "GRANDMASTER": "/challenges-images/2023004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023004-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023005, "name": "Can Run, Can't Hide: 2023", "description": "Bunuh musuh di dekat salah satu turret mereka di ARAM", "shortDescription": "Bunuh musuh di dekat <PERSON> mereka", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023005-IRON.png", "BRONZE": "/challenges-images/2023005-BRONZE.png", "SILVER": "/challenges-images/2023005-SILVER.png", "GOLD": "/challenges-images/2023005-GOLD.png", "PLATINUM": "/challenges-images/2023005-PLATINUM.png", "DIAMOND": "/challenges-images/2023005-DIAMOND.png", "MASTER": "/challenges-images/2023005-MASTER.png", "GRANDMASTER": "/challenges-images/2023005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 60}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 140}, "MASTER": {"value": 200}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 300}}}, {"id": 2023006, "name": "Perfect Aim: 2023", "description": "Hantam champion dengan snowball di ARAM", "shortDescription": "Hantam champion dengan snowball", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023006-IRON.png", "BRONZE": "/challenges-images/2023006-BRONZE.png", "SILVER": "/challenges-images/2023006-SILVER.png", "GOLD": "/challenges-images/2023006-GOLD.png", "PLATINUM": "/challenges-images/2023006-PLATINUM.png", "DIAMOND": "/challenges-images/2023006-DIAMOND.png", "MASTER": "/challenges-images/2023006-MASTER.png", "GRANDMASTER": "/challenges-images/2023006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 100}, "PLATINUM": {"value": 144}, "DIAMOND": {"value": 200}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 300}, "CHALLENGER": {"value": 400}}}, {"id": 2023007, "name": "Legendary Slayer: 2023", "description": "<PERSON><PERSON>uh <PERSON>", "shortDescription": "<PERSON><PERSON>uh <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023007-IRON.png", "BRONZE": "/challenges-images/2023007-BRONZE.png", "SILVER": "/challenges-images/2023007-SILVER.png", "GOLD": "/challenges-images/2023007-GOLD.png", "PLATINUM": "/challenges-images/2023007-PLATINUM.png", "DIAMOND": "/challenges-images/2023007-DIAMOND.png", "MASTER": "/challenges-images/2023007-MASTER.png", "GRANDMASTER": "/challenges-images/2023007-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023007-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023008, "name": "Rise Above the Rest: 2023", "description": "Unggul tiga level atau lebih dari musuh dengan role yang sama kapan pun dalam game", "shortDescription": "Unggul level daripada musuh dengan role yang sama kapan pun", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023008-IRON.png", "BRONZE": "/challenges-images/2023008-BRONZE.png", "SILVER": "/challenges-images/2023008-SILVER.png", "GOLD": "/challenges-images/2023008-GOLD.png", "PLATINUM": "/challenges-images/2023008-PLATINUM.png", "DIAMOND": "/challenges-images/2023008-DIAMOND.png", "MASTER": "/challenges-images/2023008-MASTER.png", "GRANDMASTER": "/challenges-images/2023008-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023008-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 6}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 20}, "CHALLENGER": {"value": 30}}}, {"id": 2023009, "name": "Made My Mark: 2023", "description": "Menangkan game set<PERSON><PERSON> k<PERSON> 15 kill", "shortDescription": "<PERSON><PERSON> k<PERSON> 15 kill", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023009-IRON.png", "BRONZE": "/challenges-images/2023009-BRONZE.png", "SILVER": "/challenges-images/2023009-SILVER.png", "GOLD": "/challenges-images/2023009-GOLD.png", "PLATINUM": "/challenges-images/2023009-PLATINUM.png", "DIAMOND": "/challenges-images/2023009-DIAMOND.png", "MASTER": "/challenges-images/2023009-MASTER.png", "GRANDMASTER": "/challenges-images/2023009-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023009-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 202301, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Menangkan game dengan delapan kill atau lebih tanpa mati", "shortDescription": "Menangkan satu game dengan lebih dari delapan kill tanpa mati", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202301-IRON.png", "BRONZE": "/challenges-images/202301-BRONZE.png", "SILVER": "/challenges-images/202301-SILVER.png", "GOLD": "/challenges-images/202301-GOLD.png", "PLATINUM": "/challenges-images/202301-PLATINUM.png", "DIAMOND": "/challenges-images/202301-DIAMOND.png", "MASTER": "/challenges-images/202301-MASTER.png", "GRANDMASTER": "/challenges-images/202301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202301-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 2023010, "name": "Trust Nothing But Me: 2023", "description": "Lakukan 12 assist at<PERSON> lebih tanpa mati", "shortDescription": "Dapatkan assist streak 12+ tanpa mati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023010-IRON.png", "BRONZE": "/challenges-images/2023010-BRONZE.png", "SILVER": "/challenges-images/2023010-SILVER.png", "GOLD": "/challenges-images/2023010-GOLD.png", "PLATINUM": "/challenges-images/2023010-PLATINUM.png", "DIAMOND": "/challenges-images/2023010-DIAMOND.png", "MASTER": "/challenges-images/2023010-MASTER.png", "GRANDMASTER": "/challenges-images/2023010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 9}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 15}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2023011, "name": "First To Fall: 2023", "description": "Hancurkan turret pertama dalam waktu kurang dari 10 menit", "shortDescription": "Hancurkan turret pertama dalam waktu kurang dari 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023011-IRON.png", "BRONZE": "/challenges-images/2023011-BRONZE.png", "SILVER": "/challenges-images/2023011-SILVER.png", "GOLD": "/challenges-images/2023011-GOLD.png", "PLATINUM": "/challenges-images/2023011-PLATINUM.png", "DIAMOND": "/challenges-images/2023011-DIAMOND.png", "MASTER": "/challenges-images/2023011-MASTER.png", "GRANDMASTER": "/challenges-images/2023011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023011-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023012, "name": "Got Your Back: 2023", "description": "Selamatkan sekutu yang hampir menerima damage mematikan dengan heal atau shield", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> sekutu dengan heal atau shield", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023012-IRON.png", "BRONZE": "/challenges-images/2023012-BRONZE.png", "SILVER": "/challenges-images/2023012-SILVER.png", "GOLD": "/challenges-images/2023012-GOLD.png", "PLATINUM": "/challenges-images/2023012-PLATINUM.png", "DIAMOND": "/challenges-images/2023012-DIAMOND.png", "MASTER": "/challenges-images/2023012-MASTER.png", "GRANDMASTER": "/challenges-images/2023012-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023012-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 35}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 2023013, "name": "All In, Together: 2023", "description": "<PERSON><PERSON><PERSON> dengan tim yang sama di berbagai turnamen Clash", "shortDescription": "<PERSON><PERSON><PERSON> dengan tim yang sama di turnamen Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023013-IRON.png", "BRONZE": "/challenges-images/2023013-BRONZE.png", "SILVER": "/challenges-images/2023013-SILVER.png", "GOLD": "/challenges-images/2023013-GOLD.png", "PLATINUM": "/challenges-images/2023013-PLATINUM.png", "DIAMOND": "/challenges-images/2023013-DIAMOND.png", "MASTER": "/challenges-images/2023013-MASTER.png", "GRANDMASTER": "/challenges-images/2023013-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023013-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}}}, {"id": 2023014, "name": "Making History As 5: 2023", "description": "Raih kemenangan dengan grup berisi 5 pemain yang sama", "shortDescription": "Dapatkan kemenangan dengan premade berisi 5", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023014-IRON.png", "BRONZE": "/challenges-images/2023014-BRONZE.png", "SILVER": "/challenges-images/2023014-SILVER.png", "GOLD": "/challenges-images/2023014-GOLD.png", "PLATINUM": "/challenges-images/2023014-PLATINUM.png", "DIAMOND": "/challenges-images/2023014-DIAMOND.png", "MASTER": "/challenges-images/2023014-MASTER.png", "GRANDMASTER": "/challenges-images/2023014-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023014-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 13}, "CHALLENGER": {"value": 18}}}, {"id": 2023015, "name": "In Delightful Agony: 2023", "description": "Dapatkan First Blood", "shortDescription": "Dapatkan First Blood", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023015-IRON.png", "BRONZE": "/challenges-images/2023015-BRONZE.png", "SILVER": "/challenges-images/2023015-SILVER.png", "GOLD": "/challenges-images/2023015-GOLD.png", "PLATINUM": "/challenges-images/2023015-PLATINUM.png", "DIAMOND": "/challenges-images/2023015-DIAMOND.png", "MASTER": "/challenges-images/2023015-MASTER.png", "GRANDMASTER": "/challenges-images/2023015-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023015-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 13}, "CHALLENGER": {"value": 18}}}, {"id": 2023016, "name": "Immediate Immobilization: 2023", "description": "Immobilize champion musuh", "shortDescription": "Immobilize musuh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023016-IRON.png", "BRONZE": "/challenges-images/2023016-BRONZE.png", "SILVER": "/challenges-images/2023016-SILVER.png", "GOLD": "/challenges-images/2023016-GOLD.png", "PLATINUM": "/challenges-images/2023016-PLATINUM.png", "DIAMOND": "/challenges-images/2023016-DIAMOND.png", "MASTER": "/challenges-images/2023016-MASTER.png", "GRANDMASTER": "/challenges-images/2023016-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023016-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 300}, "SILVER": {"value": 600}, "GOLD": {"value": 900}, "PLATINUM": {"value": 1200}, "DIAMOND": {"value": 1500}, "MASTER": {"value": 2000}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 2023017, "name": "I Shall Feast: 2023", "description": "Bunuh monster jungle di jungle musuh", "shortDescription": "Bunuh monster jungle di jungle musuh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023017-IRON.png", "BRONZE": "/challenges-images/2023017-BRONZE.png", "SILVER": "/challenges-images/2023017-SILVER.png", "GOLD": "/challenges-images/2023017-GOLD.png", "PLATINUM": "/challenges-images/2023017-PLATINUM.png", "DIAMOND": "/challenges-images/2023017-DIAMOND.png", "MASTER": "/challenges-images/2023017-MASTER.png", "GRANDMASTER": "/challenges-images/2023017-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023017-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 100}, "PLATINUM": {"value": 200}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 750}, "CHALLENGER": {"value": 1000}}}, {"id": 2023018, "name": "Brave The Darkness: 2023", "description": "Tempatkan Control Ward yang efektif. Ward yang efektif adalah yang diletakkan di luar markasmu.", "shortDescription": "Tempatkan Control Ward", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023018-IRON.png", "BRONZE": "/challenges-images/2023018-BRONZE.png", "SILVER": "/challenges-images/2023018-SILVER.png", "GOLD": "/challenges-images/2023018-GOLD.png", "PLATINUM": "/challenges-images/2023018-PLATINUM.png", "DIAMOND": "/challenges-images/2023018-DIAMOND.png", "MASTER": "/challenges-images/2023018-MASTER.png", "GRANDMASTER": "/challenges-images/2023018-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023018-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 2023019, "name": "Breakable: 2023", "description": "Hancurkan turret", "shortDescription": "Hancurkan turret", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023019-IRON.png", "BRONZE": "/challenges-images/2023019-BRONZE.png", "SILVER": "/challenges-images/2023019-SILVER.png", "GOLD": "/challenges-images/2023019-GOLD.png", "PLATINUM": "/challenges-images/2023019-PLATINUM.png", "DIAMOND": "/challenges-images/2023019-DIAMOND.png", "MASTER": "/challenges-images/2023019-MASTER.png", "GRANDMASTER": "/challenges-images/2023019-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023019-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 202302, "name": "Prodigy", "description": "<PERSON>adi Legendary (delapan kill streak) dalam waktu 15 menit", "shortDescription": "Jadi Legendary dalam waktu 15 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202302-IRON.png", "BRONZE": "/challenges-images/202302-BRONZE.png", "SILVER": "/challenges-images/202302-SILVER.png", "GOLD": "/challenges-images/202302-GOLD.png", "PLATINUM": "/challenges-images/202302-PLATINUM.png", "DIAMOND": "/challenges-images/202302-DIAMOND.png", "MASTER": "/challenges-images/202302-MASTER.png", "GRANDMASTER": "/challenges-images/202302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202302-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Prodigy"}]}, "DIAMOND": {"value": 4}, "MASTER": {"value": 7}}}, {"id": 2023020, "name": "Unstoppable, Unshakeable: 2023", "description": "Menangkan game dengan masuk sebagai role Fill", "shortDescription": "Menangkan game dengan masuk sebagai Fill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023020-IRON.png", "BRONZE": "/challenges-images/2023020-BRONZE.png", "SILVER": "/challenges-images/2023020-SILVER.png", "GOLD": "/challenges-images/2023020-GOLD.png", "PLATINUM": "/challenges-images/2023020-PLATINUM.png", "DIAMOND": "/challenges-images/2023020-DIAMOND.png", "MASTER": "/challenges-images/2023020-MASTER.png", "GRANDMASTER": "/challenges-images/2023020-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023020-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 7}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 20}, "CHALLENGER": {"value": 25}}}, {"id": 202303, "name": "Invincible", "description": "Menangkan satu game tanpa mati dengan berbagai champion", "shortDescription": "<PERSON><PERSON> tanpa mati dengan <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202303-IRON.png", "BRONZE": "/challenges-images/202303-BRONZE.png", "SILVER": "/challenges-images/202303-SILVER.png", "GOLD": "/challenges-images/202303-GOLD.png", "PLATINUM": "/challenges-images/202303-PLATINUM.png", "DIAMOND": "/challenges-images/202303-DIAMOND.png", "MASTER": "/challenges-images/202303-MASTER.png", "GRANDMASTER": "/challenges-images/202303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 50}, "MASTER": {"value": 75}}}, {"id": 202304, "name": "Decimator", "description": "Dapatkan dua pentakill dalam satu game", "shortDescription": "Dapatkan dua Pentakill dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202304-IRON.png", "BRONZE": "/challenges-images/202304-BRONZE.png", "SILVER": "/challenges-images/202304-SILVER.png", "GOLD": "/challenges-images/202304-GOLD.png", "PLATINUM": "/challenges-images/202304-PLATINUM.png", "DIAMOND": "/challenges-images/202304-DIAMOND.png", "MASTER": "/challenges-images/202304-MASTER.png", "GRANDMASTER": "/challenges-images/202304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202304-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 202305, "name": "Golden", "description": "<PERSON>h lebih dari 450 Gold Per Menit", "shortDescription": "<PERSON><PERSON>i lebih dari 450 gold per menit dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202305-IRON.png", "BRONZE": "/challenges-images/202305-BRONZE.png", "SILVER": "/challenges-images/202305-SILVER.png", "GOLD": "/challenges-images/202305-GOLD.png", "PLATINUM": "/challenges-images/202305-PLATINUM.png", "DIAMOND": "/challenges-images/202305-DIAMOND.png", "MASTER": "/challenges-images/202305-MASTER.png", "GRANDMASTER": "/challenges-images/202305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 6}, "SILVER": {"value": 14}, "GOLD": {"value": 30}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 180}, "MASTER": {"value": 350}}}, {"id": 2024100, "name": "2024 Seasonal: Split 1", "description": "Raih progres dari tantangan di grup 2024 Seasonal: Split 1", "shortDescription": "Raih progres dari tantangan di grup 2024 Seasonal: Split 1", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024100-IRON.png", "BRONZE": "/challenges-images/2024100-BRONZE.png", "SILVER": "/challenges-images/2024100-SILVER.png", "GOLD": "/challenges-images/2024100-GOLD.png", "PLATINUM": "/challenges-images/2024100-PLATINUM.png", "DIAMOND": "/challenges-images/2024100-DIAMOND.png", "MASTER": "/challenges-images/2024100-MASTER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 135}, "PLATINUM": {"value": 215}, "DIAMOND": {"value": 380}, "MASTER": {"value": 650, "rewards": [{"category": "TITLE", "quantity": 1, "title": "V<PERSON>born"}]}}}, {"id": 2024101, "name": "Legendary Arms: 2024 Split 1", "description": "Menang dengan berbagai item Legendary", "shortDescription": "Menangkan game dengan <em>berb<PERSON><PERSON> Item Legendary</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024101-IRON.png", "BRONZE": "/challenges-images/2024101-BRONZE.png", "SILVER": "/challenges-images/2024101-SILVER.png", "GOLD": "/challenges-images/2024101-GOLD.png", "PLATINUM": "/challenges-images/2024101-PLATINUM.png", "DIAMOND": "/challenges-images/2024101-DIAMOND.png", "MASTER": "/challenges-images/2024101-MASTER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 16}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 60}, "MASTER": {"value": 85}}}, {"id": 2024102, "name": "Support Subsidy: 2024 Split 1", "description": "Selesaikan quest item support (1000 g) dalam waktu kurang dari 14 menit", "shortDescription": "<PERSON><PERSON>aikan seluruh quest support dalam waktu 14 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024102-IRON.png", "BRONZE": "/challenges-images/2024102-BRONZE.png", "SILVER": "/challenges-images/2024102-SILVER.png", "GOLD": "/challenges-images/2024102-GOLD.png", "PLATINUM": "/challenges-images/2024102-PLATINUM.png", "DIAMOND": "/challenges-images/2024102-DIAMOND.png", "MASTER": "/challenges-images/2024102-MASTER.png", "GRANDMASTER": "/challenges-images/2024102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 55}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 2024103, "name": "Crab Wrangler: 2024 Split 1", "description": "Sebagai jungler, bunuh kedua scuttle crab yang muncul di awal", "shortDescription": "Bunuh kedua scuttle crab pertama yang muncul", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024103-IRON.png", "BRONZE": "/challenges-images/2024103-BRONZE.png", "SILVER": "/challenges-images/2024103-SILVER.png", "GOLD": "/challenges-images/2024103-GOLD.png", "PLATINUM": "/challenges-images/2024103-PLATINUM.png", "DIAMOND": "/challenges-images/2024103-DIAMOND.png", "MASTER": "/challenges-images/2024103-MASTER.png", "GRANDMASTER": "/challenges-images/2024103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 25}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 60}, "CHALLENGER": {"value": 90}}}, {"id": 2024104, "name": "The Brush Has Eyes: 2024 Split 1", "description": "Akhiri game dengan Skor Vision 20% lebih banyak daripada musuh dengan role yang sama ", "shortDescription": "Akhiri game dengan Skor Vision 20% lebih banyak daripada musuh dengan role yang sama", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024104-IRON.png", "BRONZE": "/challenges-images/2024104-BRONZE.png", "SILVER": "/challenges-images/2024104-SILVER.png", "GOLD": "/challenges-images/2024104-GOLD.png", "PLATINUM": "/challenges-images/2024104-PLATINUM.png", "DIAMOND": "/challenges-images/2024104-DIAMOND.png", "MASTER": "/challenges-images/2024104-MASTER.png", "GRANDMASTER": "/challenges-images/2024104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 55}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 2024105, "name": "All Random All Champs: 2024 Split 1", "description": "Raih grade S- atau lebih tinggi pada berbagai champion di ARAM", "shortDescription": "Raih grade S- dengan <em>berbagai champion</em> ", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024105-IRON.png", "BRONZE": "/challenges-images/2024105-BRONZE.png", "SILVER": "/challenges-images/2024105-SILVER.png", "GOLD": "/challenges-images/2024105-GOLD.png", "PLATINUM": "/challenges-images/2024105-PLATINUM.png", "DIAMOND": "/challenges-images/2024105-DIAMOND.png", "MASTER": "/challenges-images/2024105-MASTER.png", "GRANDMASTER": "/challenges-images/2024105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 7}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 18}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 50}}}, {"id": 2024106, "name": "Wurm Fishing: 2024 Split 1", "description": "Takedown Baron", "shortDescription": "Takedown Baron", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024106-IRON.png", "BRONZE": "/challenges-images/2024106-BRONZE.png", "SILVER": "/challenges-images/2024106-SILVER.png", "GOLD": "/challenges-images/2024106-GOLD.png", "PLATINUM": "/challenges-images/2024106-PLATINUM.png", "DIAMOND": "/challenges-images/2024106-DIAMOND.png", "MASTER": "/challenges-images/2024106-MASTER.png", "GRANDMASTER": "/challenges-images/2024106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024106-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 14}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 30}, "MASTER": {"value": 45}, "GRANDMASTER": {"value": 65}, "CHALLENGER": {"value": 90}}}, {"id": 2024107, "name": "Right Under Their Noses: 2024 Split 1", "description": "Recall selagi tidak terlihat oleh champion musuh di sekitar", "shortDescription": "Recall selagi tidak terlihat oleh champion di sekitar", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024107-IRON.png", "BRONZE": "/challenges-images/2024107-BRONZE.png", "SILVER": "/challenges-images/2024107-SILVER.png", "GOLD": "/challenges-images/2024107-GOLD.png", "PLATINUM": "/challenges-images/2024107-PLATINUM.png", "DIAMOND": "/challenges-images/2024107-DIAMOND.png", "MASTER": "/challenges-images/2024107-MASTER.png", "GRANDMASTER": "/challenges-images/2024107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024107-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}}}, {"id": 2024108, "name": "Always On Time: 2024 Split 1", "description": "Bunuh Monster Epik dalam waktu 30 detik setelah mereka muncul. Monster Epik <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Bunuh Monster Epik dalam waktu 30 detik setelah muncul", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024108-IRON.png", "BRONZE": "/challenges-images/2024108-BRONZE.png", "SILVER": "/challenges-images/2024108-SILVER.png", "GOLD": "/challenges-images/2024108-GOLD.png", "PLATINUM": "/challenges-images/2024108-PLATINUM.png", "DIAMOND": "/challenges-images/2024108-DIAMOND.png", "MASTER": "/challenges-images/2024108-MASTER.png", "GRANDMASTER": "/challenges-images/2024108-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024108-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 7}, "PLATINUM": {"value": 11}, "DIAMOND": {"value": 16}, "MASTER": {"value": 22}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2024200, "name": "2024 Seasonal: Split 2", "description": "Raih progres dari tantangan di grup 2024 Seasonal: Split 2", "shortDescription": "Raih progres dari tantangan di grup 2024 Seasonal: Split 2", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024200-IRON.png", "BRONZE": "/challenges-images/2024200-BRONZE.png", "SILVER": "/challenges-images/2024200-SILVER.png", "GOLD": "/challenges-images/2024200-GOLD.png", "PLATINUM": "/challenges-images/2024200-PLATINUM.png", "DIAMOND": "/challenges-images/2024200-DIAMOND.png", "MASTER": "/challenges-images/2024200-MASTER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 135}, "PLATINUM": {"value": 215}, "DIAMOND": {"value": 380}, "MASTER": {"value": 650, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Infernal"}]}}}, {"id": 2024201, "name": "Darkness Distributor: 2024 Split 2", "description": "Takedown ward paling banyak dalam satu game", "shortDescription": "Takedown ward paling banyak dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024201-IRON.png", "BRONZE": "/challenges-images/2024201-BRONZE.png", "SILVER": "/challenges-images/2024201-SILVER.png", "GOLD": "/challenges-images/2024201-GOLD.png", "PLATINUM": "/challenges-images/2024201-PLATINUM.png", "DIAMOND": "/challenges-images/2024201-DIAMOND.png", "MASTER": "/challenges-images/2024201-MASTER.png", "GRANDMASTER": "/challenges-images/2024201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024201-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}}}, {"id": 2024202, "name": "Top Performer: 2024 Split 2", "description": "Dapatkan grade S- atau lebih tinggi", "shortDescription": "Dapatkan grade S- atau lebih tinggi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024202-IRON.png", "BRONZE": "/challenges-images/2024202-BRONZE.png", "SILVER": "/challenges-images/2024202-SILVER.png", "GOLD": "/challenges-images/2024202-GOLD.png", "PLATINUM": "/challenges-images/2024202-PLATINUM.png", "DIAMOND": "/challenges-images/2024202-DIAMOND.png", "MASTER": "/challenges-images/2024202-MASTER.png", "GRANDMASTER": "/challenges-images/2024202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 4}, "SILVER": {"value": 7}, "GOLD": {"value": 11}, "PLATINUM": {"value": 16}, "DIAMOND": {"value": 22}, "MASTER": {"value": 30}}}, {"id": 2024203, "name": "Stun n Gun: 2024 Split 2", "description": "Immobilize lalu takedown musuh bersama sekutu", "shortDescription": "Immobilize lalu takedown musuh bersama sekutu", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024203-IRON.png", "BRONZE": "/challenges-images/2024203-BRONZE.png", "SILVER": "/challenges-images/2024203-SILVER.png", "GOLD": "/challenges-images/2024203-GOLD.png", "PLATINUM": "/challenges-images/2024203-PLATINUM.png", "DIAMOND": "/challenges-images/2024203-DIAMOND.png", "MASTER": "/challenges-images/2024203-MASTER.png", "GRANDMASTER": "/challenges-images/2024203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 120}, "MASTER": {"value": 200}}}, {"id": 2024204, "name": "Nice Try: 2024 Split 2", "description": "Bunuh musuh di bawah turret-mu", "shortDescription": "Bunuh musuh di bawah turret-mu", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024204-IRON.png", "BRONZE": "/challenges-images/2024204-BRONZE.png", "SILVER": "/challenges-images/2024204-SILVER.png", "GOLD": "/challenges-images/2024204-GOLD.png", "PLATINUM": "/challenges-images/2024204-PLATINUM.png", "DIAMOND": "/challenges-images/2024204-DIAMOND.png", "MASTER": "/challenges-images/2024204-MASTER.png", "GRANDMASTER": "/challenges-images/2024204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024204-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 8}, "MASTER": {"value": 15}}}, {"id": 2024205, "name": "Farm Champion No Minions: 2024 Split 2", "description": "Lakukan Takedown di ARAM", "shortDescription": "Lakukan Takedown di ARAM", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024205-IRON.png", "BRONZE": "/challenges-images/2024205-BRONZE.png", "SILVER": "/challenges-images/2024205-SILVER.png", "GOLD": "/challenges-images/2024205-GOLD.png", "PLATINUM": "/challenges-images/2024205-PLATINUM.png", "DIAMOND": "/challenges-images/2024205-DIAMOND.png", "MASTER": "/challenges-images/2024205-MASTER.png", "GRANDMASTER": "/challenges-images/2024205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 60}, "SILVER": {"value": 120}, "GOLD": {"value": 240}, "PLATINUM": {"value": 420}, "DIAMOND": {"value": 750}, "MASTER": {"value": 1000}}}, {"id": 2024206, "name": "Nullify the Void: 2024 Split 2", "description": "Takedown Void Monster (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> <PERSON><PERSON>, <PERSON>, dan <PERSON>)", "shortDescription": "Takedown Void Monster", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024206-IRON.png", "BRONZE": "/challenges-images/2024206-BRONZE.png", "SILVER": "/challenges-images/2024206-SILVER.png", "GOLD": "/challenges-images/2024206-GOLD.png", "PLATINUM": "/challenges-images/2024206-PLATINUM.png", "DIAMOND": "/challenges-images/2024206-DIAMOND.png", "MASTER": "/challenges-images/2024206-MASTER.png", "GRANDMASTER": "/challenges-images/2024206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 40}, "MASTER": {"value": 55}}}, {"id": 2024207, "name": "Sparkles & Fireworks: 2024 Split 2", "description": "Berpartisipasi dalam Fistbump bersama setidaknya 1 pemain lainnya di game matchmade non-AI", "shortDescription": "Berpartisipasi dalam Fistbump di game matchmade", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024207-IRON.png", "BRONZE": "/challenges-images/2024207-BRONZE.png", "SILVER": "/challenges-images/2024207-SILVER.png", "GOLD": "/challenges-images/2024207-GOLD.png", "PLATINUM": "/challenges-images/2024207-PLATINUM.png", "DIAMOND": "/challenges-images/2024207-DIAMOND.png", "MASTER": "/challenges-images/2024207-MASTER.png", "GRANDMASTER": "/challenges-images/2024207-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024207-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 120}, "MASTER": {"value": 160}}}, {"id": 2024208, "name": "The Fire Rises: 2024 Split 2", "description": "Kumpulkan Infernal Cinder selama Infernal Terrain", "shortDescription": "Kumpulkan Infernal Cinder", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024208-IRON.png", "BRONZE": "/challenges-images/2024208-BRONZE.png", "SILVER": "/challenges-images/2024208-SILVER.png", "GOLD": "/challenges-images/2024208-GOLD.png", "PLATINUM": "/challenges-images/2024208-PLATINUM.png", "DIAMOND": "/challenges-images/2024208-DIAMOND.png", "MASTER": "/challenges-images/2024208-MASTER.png", "CHALLENGER": "/challenges-images/2024208-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 30}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 50}, "MASTER": {"value": 60}}}, {"id": 2024300, "name": "2024 Seasonal: Split 3", "description": "Raih poin dari tantangan di grup", "shortDescription": "Raih poin dari tantangan di grup 2024 Seasonal: Split 3", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024300-IRON.png", "BRONZE": "/challenges-images/2024300-BRONZE.png", "SILVER": "/challenges-images/2024300-SILVER.png", "GOLD": "/challenges-images/2024300-GOLD.png", "PLATINUM": "/challenges-images/2024300-PLATINUM.png", "DIAMOND": "/challenges-images/2024300-DIAMOND.png", "MASTER": "/challenges-images/2024300-MASTER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 40}, "GOLD": {"value": 100}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 380}, "MASTER": {"value": 600, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON><PERSON> '24"}]}}}, {"id": 2024301, "name": "Lane Domination: 2024 Split 3", "description": "<PERSON><PERSON><PERSON> fase pembersihan lane (14 menit) dengan 20% lebih Gold dan XP daripada musuh di role yang sama ", "shortDescription": "Unggul 20% untuk Gold dan XP dalam 14 menit", "hasLeaderboard": false, "levelToIconPath": {"NONE": "/challenges-images/2024301-IRON.png", "BRONZE": "/challenges-images/2024301-BRONZE.png", "SILVER": "/challenges-images/2024301-SILVER.png", "GOLD": "/challenges-images/2024301-GOLD.png", "PLATINUM": "/challenges-images/2024301-PLATINUM.png", "DIAMOND": "/challenges-images/2024301-DIAMOND.png", "MASTER": "/challenges-images/2024301-MASTER.png", "GRANDMASTER": "/challenges-images/2024301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024301-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}}}, {"id": 2024302, "name": "No Hiding: 2024 Split 3", "description": "Bunuh musuh di dekat turret mereka sendiri di mode ARAM", "shortDescription": "Bunuh musuh di dekat turret mereka sendiri", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024302-IRON.png", "BRONZE": "/challenges-images/2024302-BRONZE.png", "SILVER": "/challenges-images/2024302-SILVER.png", "GOLD": "/challenges-images/2024302-GOLD.png", "PLATINUM": "/challenges-images/2024302-PLATINUM.png", "DIAMOND": "/challenges-images/2024302-DIAMOND.png", "MASTER": "/challenges-images/2024302-MASTER.png", "GRANDMASTER": "/challenges-images/2024302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 15}, "GOLD": {"value": 20}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 65}, "MASTER": {"value": 100}}}, {"id": 2024303, "name": "One Stone: 2024 Split 3", "description": "<PERSON><PERSON><PERSON> dua pemain dengan cast ability yang sama ", "shortDescription": "<PERSON><PERSON><PERSON> dua pemain dengan cast ability yang sama ", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024303-IRON.png", "BRONZE": "/challenges-images/2024303-BRONZE.png", "SILVER": "/challenges-images/2024303-SILVER.png", "GOLD": "/challenges-images/2024303-GOLD.png", "PLATINUM": "/challenges-images/2024303-PLATINUM.png", "DIAMOND": "/challenges-images/2024303-DIAMOND.png", "MASTER": "/challenges-images/2024303-MASTER.png", "GRANDMASTER": "/challenges-images/2024303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024303-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 3}, "MASTER": {"value": 5}}}, {"id": 2024304, "name": "Global Defense System: 2024 Split 3", "description": "Memiliki Control Ward aktif di sungai atau setengah peta musuh selama lebih dari 65% durasi game ", "shortDescription": "Memiliki Control Ward di jungle musuh selama 65% lebih durasi game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024304-IRON.png", "BRONZE": "/challenges-images/2024304-BRONZE.png", "SILVER": "/challenges-images/2024304-SILVER.png", "GOLD": "/challenges-images/2024304-GOLD.png", "PLATINUM": "/challenges-images/2024304-PLATINUM.png", "DIAMOND": "/challenges-images/2024304-DIAMOND.png", "MASTER": "/challenges-images/2024304-MASTER.png", "GRANDMASTER": "/challenges-images/2024304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024304-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}}}, {"id": 2024305, "name": "Dedicated Master: 2024 Split 3", "description": "Dapatkan Milestone Mastery untuk champion mana pun", "shortDescription": "Raih Milestone Mastery", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024305-IRON.png", "BRONZE": "/challenges-images/2024305-BRONZE.png", "SILVER": "/challenges-images/2024305-SILVER.png", "GOLD": "/challenges-images/2024305-GOLD.png", "PLATINUM": "/challenges-images/2024305-PLATINUM.png", "DIAMOND": "/challenges-images/2024305-DIAMOND.png", "MASTER": "/challenges-images/2024305-MASTER.png", "GRANDMASTER": "/challenges-images/2024305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 25}}}, {"id": 2024306, "name": "A Honeyfruit a Day: 2024 Split 3", "description": "Dapatkan Health atau Shield lewat sumber healing dari peta (Honeyfruit, Health Shrine, dan <PERSON> dihitung)", "shortDescription": "Memulihkan Health lewat sumber healing dari peta", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024306-IRON.png", "BRONZE": "/challenges-images/2024305-BRONZE.png", "SILVER": "/challenges-images/2024305-SILVER.png", "GOLD": "/challenges-images/2024305-GOLD.png", "PLATINUM": "/challenges-images/2024305-PLATINUM.png", "DIAMOND": "/challenges-images/2024305-DIAMOND.png", "MASTER": "/challenges-images/2024305-MASTER.png", "GRANDMASTER": "/challenges-images/2024305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1000}, "BRONZE": {"value": 2500}, "SILVER": {"value": 4500}, "GOLD": {"value": 7000}, "PLATINUM": {"value": 10000}, "DIAMOND": {"value": 14000}, "MASTER": {"value": 20000}}}, {"id": 2024307, "name": "Pocket Picks: 2024 Split 3", "description": "Raih 12 Milestone Mastery untuk champion in Set Mastery-mu", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> Satu Set Mastery", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024307-IRON.png", "BRONZE": "/challenges-images/2024307-BRONZE.png", "SILVER": "/challenges-images/2024307-SILVER.png", "GOLD": "/challenges-images/2024307-GOLD.png", "PLATINUM": "/challenges-images/2024307-PLATINUM.png", "DIAMOND": "/challenges-images/2024307-DIAMOND.png", "MASTER": "/challenges-images/2024307-MASTER.png", "GRANDMASTER": "/challenges-images/2024307-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024307-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 12}}}, {"id": 2024308, "name": "Champion Ocean: 2024 Split 3", "description": "Menangkan game (semua mode dihitung) dengan champion berb<PERSON>", "shortDescription": "Menangkan game dengan <em>champion yang berb<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024308-IRON.png", "BRONZE": "/challenges-images/2024308-BRONZE.png", "SILVER": "/challenges-images/2024308-IRON.png", "GOLD": "/challenges-images/2024308-GOLD.png", "PLATINUM": "/challenges-images/2024308-PLATINUM.png", "DIAMOND": "/challenges-images/2024308-DIAMOND.png", "MASTER": "/challenges-images/2024308-MASTER.png", "GRANDMASTER": "/challenges-images/2024308-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024308-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 20}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 30}, "MASTER": {"value": 35}}}, {"id": 203000, "name": "Might", "description": "<PERSON>h progres dari tantangan di grup <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, da<PERSON>", "shortDescription": "<PERSON>h progres dari tantangan di grup <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, da<PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203000-IRON.png", "BRONZE": "/challenges-images/203000-BRONZE.png", "SILVER": "/challenges-images/203000-SILVER.png", "GOLD": "/challenges-images/203000-GOLD.png", "PLATINUM": "/challenges-images/203000-PLATINUM.png", "DIAMOND": "/challenges-images/203000-DIAMOND.png", "MASTER": "/challenges-images/203000-MASTER.png", "GRANDMASTER": "/challenges-images/203000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 105}, "SILVER": {"value": 190}, "GOLD": {"value": 440}, "PLATINUM": {"value": 700}, "DIAMOND": {"value": 1250}, "MASTER": {"value": 2100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Legend"}]}}}, {"id": 203100, "name": "<PERSON><PERSON>", "description": "Raih progres dari tantangan di grup Flair", "shortDescription": "Raih progres dari tantangan di grup Flair", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203100-IRON.png", "BRONZE": "/challenges-images/203100-BRONZE.png", "SILVER": "/challenges-images/203100-SILVER.png", "GOLD": "/challenges-images/203100-GOLD.png", "PLATINUM": "/challenges-images/203100-PLATINUM.png", "DIAMOND": "/challenges-images/203100-DIAMOND.png", "MASTER": "/challenges-images/203100-MASTER.png", "GRANDMASTER": "/challenges-images/203100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 45}, "GOLD": {"value": 95}, "PLATINUM": {"value": 145}, "DIAMOND": {"value": 250, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Show-Off"}]}, "MASTER": {"value": 400}}}, {"id": 203101, "name": "Can't Miss", "description": "Lakukan 20 skillshot (ability ranged tak terarah) sebelum tujuh menit", "shortDescription": "Lakukan 20 skillshot sebelum tujuh menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203101-IRON.png", "BRONZE": "/challenges-images/203101-BRONZE.png", "SILVER": "/challenges-images/203101-SILVER.png", "GOLD": "/challenges-images/203101-GOLD.png", "PLATINUM": "/challenges-images/203101-PLATINUM.png", "DIAMOND": "/challenges-images/203101-DIAMOND.png", "MASTER": "/challenges-images/203101-MASTER.png", "GRANDMASTER": "/challenges-images/203101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203101-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 5}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 25}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 203102, "name": "The Jukes!", "description": "<PERSON><PERSON><PERSON> lima skillshot (ability ranged tak terarah) dalam waktu delapan detik", "shortDescription": "<PERSON><PERSON><PERSON> lima skillshot dalam waktu delapan detik", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203102-IRON.png", "BRONZE": "/challenges-images/203102-BRONZE.png", "SILVER": "/challenges-images/203102-SILVER.png", "GOLD": "/challenges-images/203102-GOLD.png", "PLATINUM": "/challenges-images/203102-PLATINUM.png", "DIAMOND": "/challenges-images/203102-DIAMOND.png", "MASTER": "/challenges-images/203102-MASTER.png", "GRANDMASTER": "/challenges-images/203102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 45}, "GOLD": {"value": 120, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}, "PLATINUM": {"value": 350}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2000}}}, {"id": 203103, "name": "Captain <PERSON>", "description": "Bersihkan dirimu atau pemain lain dari efek immobilize (menggunakan Summoner Spell Cleanse, Quicksilver, atau Mikael's Blessing) dalam waktu 0,25 dtk saat efek dimulai", "shortDescription": "Bersihkan efek immobilize dalam waktu 0,25 dtk", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203103-IRON.png", "BRONZE": "/challenges-images/203103-BRONZE.png", "SILVER": "/challenges-images/203103-SILVER.png", "GOLD": "/challenges-images/203103-GOLD.png", "PLATINUM": "/challenges-images/203103-PLATINUM.png", "DIAMOND": "/challenges-images/203103-DIAMOND.png", "MASTER": "/challenges-images/203103-MASTER.png", "GRANDMASTER": "/challenges-images/203103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203103-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Captain <PERSON>"}]}}}, {"id": 203104, "name": "TY for Leash", "description": "Curi Monster Epik jungle tanpa menggunakan Summoner Smite. Monster Epik men<PERSON>kup <PERSON>, <PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Curi Monster Epik tanpa Smite", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203104-IRON.png", "BRONZE": "/challenges-images/203104-BRONZE.png", "SILVER": "/challenges-images/203104-SILVER.png", "GOLD": "/challenges-images/203104-GOLD.png", "PLATINUM": "/challenges-images/203104-PLATINUM.png", "DIAMOND": "/challenges-images/203104-DIAMOND.png", "MASTER": "/challenges-images/203104-MASTER.png", "GRANDMASTER": "/challenges-images/203104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 8}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 40}, "MASTER": {"value": 75}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 300}}}, {"id": 203105, "name": "Stacks on Stacks on Stacks", "description": "<PERSON><PERSON><PERSON> stack Soulstealer Me<PERSON>i sebelum 20 menit", "shortDescription": "<PERSON><PERSON><PERSON> stack Mejai sebelum 20 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203105-IRON.png", "BRONZE": "/challenges-images/203105-BRONZE.png", "SILVER": "/challenges-images/203105-SILVER.png", "GOLD": "/challenges-images/203105-GOLD.png", "PLATINUM": "/challenges-images/203105-PLATINUM.png", "DIAMOND": "/challenges-images/203105-DIAMOND.png", "MASTER": "/challenges-images/203105-MASTER.png", "GRANDMASTER": "/challenges-images/203105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203105-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 13}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 50}}}, {"id": 203106, "name": "One Stone", "description": "<PERSON><PERSON><PERSON> dua pemain dengan cast ability yang sama", "shortDescription": "Bunuh dua pemain dengan satu cast ability", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203106-IRON.png", "BRONZE": "/challenges-images/203106-BRONZE.png", "SILVER": "/challenges-images/203106-SILVER.png", "GOLD": "/challenges-images/203106-GOLD.png", "PLATINUM": "/challenges-images/203106-PLATINUM.png", "DIAMOND": "/challenges-images/203106-DIAMOND.png", "MASTER": "/challenges-images/203106-MASTER.png", "GRANDMASTER": "/challenges-images/203106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203106-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 8}, "PLATINUM": {"value": 23}, "DIAMOND": {"value": 43}, "MASTER": {"value": 73}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 150}}}, {"id": 203200, "name": "Behemoth", "description": "Raih progres dari tantangan di grup Behemoth", "shortDescription": "Raih progres dari tantangan di grup Behemoth", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203200-IRON.png", "BRONZE": "/challenges-images/203200-BRONZE.png", "SILVER": "/challenges-images/203200-SILVER.png", "GOLD": "/challenges-images/203200-GOLD.png", "PLATINUM": "/challenges-images/203200-PLATINUM.png", "DIAMOND": "/challenges-images/203200-DIAMOND.png", "MASTER": "/challenges-images/203200-MASTER.png", "GRANDMASTER": "/challenges-images/203200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Behemoth"}]}, "MASTER": {"value": 250}}}, {"id": 203201, "name": "Not Even a Scratch", "description": "Terima 10000 damage pramitigasi dari champion dalam satu combat tanpa mati", "shortDescription": "Terima 10 ribu damage dalam pertarungan dan be<PERSON>han hidup", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203201-IRON.png", "BRONZE": "/challenges-images/203201-BRONZE.png", "SILVER": "/challenges-images/203201-SILVER.png", "GOLD": "/challenges-images/203201-GOLD.png", "PLATINUM": "/challenges-images/203201-PLATINUM.png", "DIAMOND": "/challenges-images/203201-DIAMOND.png", "MASTER": "/challenges-images/203201-MASTER.png", "GRANDMASTER": "/challenges-images/203201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Absolute Unit"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 65}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 125}}}, {"id": 203202, "name": "Damage Sponge", "description": "Dalam satu combat, la<PERSON><PERSON> kill sambil menerima damage dari lima champion musuh dan tetap hidup", "shortDescription": "Dalam satu combat, la<PERSON><PERSON> kill samb<PERSON> men<PERSON><PERSON><PERSON> ke lima musuh dan be<PERSON>han hidup.", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203202-IRON.png", "BRONZE": "/challenges-images/203202-BRONZE.png", "SILVER": "/challenges-images/203202-SILVER.png", "GOLD": "/challenges-images/203202-GOLD.png", "PLATINUM": "/challenges-images/203202-PLATINUM.png", "DIAMOND": "/challenges-images/203202-DIAMOND.png", "MASTER": "/challenges-images/203202-MASTER.png", "GRANDMASTER": "/challenges-images/203202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203202-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 8}, "MASTER": {"value": 12}, "GRANDMASTER": {"value": 17}, "CHALLENGER": {"value": 25}}}, {"id": 203203, "name": "Go Where You Please", "description": "Terima tiga ability immobilize atau lebih dalam satu combat dan be<PERSON>han hidup", "shortDescription": "Terima tiga efek immobilize dalam pertarungan dan bertahan hidup", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203203-IRON.png", "BRONZE": "/challenges-images/203203-BRONZE.png", "SILVER": "/challenges-images/203203-SILVER.png", "GOLD": "/challenges-images/203203-GOLD.png", "PLATINUM": "/challenges-images/203203-PLATINUM.png", "DIAMOND": "/challenges-images/203203-DIAMOND.png", "MASTER": "/challenges-images/203203-MASTER.png", "GRANDMASTER": "/challenges-images/203203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 40}, "SILVER": {"value": 100}, "GOLD": {"value": 250}, "PLATINUM": {"value": 750}, "DIAMOND": {"value": 1500}, "MASTER": {"value": 3000}, "GRANDMASTER": {"value": 4000}, "CHALLENGER": {"value": 5000}}}, {"id": 203300, "name": "Slayer", "description": "Raih progres dari tantangan di grup <PERSON>", "shortDescription": "Raih progres dari tantangan di grup <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203300-IRON.png", "BRONZE": "/challenges-images/203300-BRONZE.png", "SILVER": "/challenges-images/203300-SILVER.png", "GOLD": "/challenges-images/203300-GOLD.png", "PLATINUM": "/challenges-images/203300-PLATINUM.png", "DIAMOND": "/challenges-images/203300-DIAMOND.png", "MASTER": "/challenges-images/203300-MASTER.png", "GRANDMASTER": "/challenges-images/203300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ma<PERSON><PERSON>"}]}, "MASTER": {"value": 400}}}, {"id": 203301, "name": "Solo Bolo", "description": "<PERSON><PERSON><PERSON> solo kill (tan<PERSON> assist dari champion se<PERSON><PERSON>)", "shortDescription": "<PERSON><PERSON><PERSON> solo kill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203301-IRON.png", "BRONZE": "/challenges-images/203301-BRONZE.png", "SILVER": "/challenges-images/203301-SILVER.png", "GOLD": "/challenges-images/203301-GOLD.png", "PLATINUM": "/challenges-images/203301-PLATINUM.png", "DIAMOND": "/challenges-images/203301-DIAMOND.png", "MASTER": "/challenges-images/203301-MASTER.png", "GRANDMASTER": "/challenges-images/203301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON> Wolf"}]}, "PLATINUM": {"value": 1000}, "DIAMOND": {"value": 2000}, "MASTER": {"value": 3000}, "GRANDMASTER": {"value": 4000}, "CHALLENGER": {"value": 5000}}}, {"id": 203302, "name": "Never Tell Me the Odds", "description": "<PERSON><PERSON>kan kill saat lebih banyak champion musuh daripada champion seku<PERSON> di sekitar", "shortDescription": "<PERSON><PERSON><PERSON> kill saat kalah jumlah", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203302-IRON.png", "BRONZE": "/challenges-images/203302-BRONZE.png", "SILVER": "/challenges-images/203302-SILVER.png", "GOLD": "/challenges-images/203302-GOLD.png", "PLATINUM": "/challenges-images/203302-PLATINUM.png", "DIAMOND": "/challenges-images/203302-DIAMOND.png", "MASTER": "/challenges-images/203302-MASTER.png", "GRANDMASTER": "/challenges-images/203302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 90}, "GOLD": {"value": 180}, "PLATINUM": {"value": 540, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Lucky"}]}, "DIAMOND": {"value": 1080}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 203303, "name": "No Quarter", "description": "Bunuh champion musuh selagi mereka berada di dekat turret mereka", "shortDescription": "Bunuh champion di <PERSON> mereka sendiri", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203303-IRON.png", "BRONZE": "/challenges-images/203303-BRONZE.png", "SILVER": "/challenges-images/203303-SILVER.png", "GOLD": "/challenges-images/203303-GOLD.png", "PLATINUM": "/challenges-images/203303-PLATINUM.png", "DIAMOND": "/challenges-images/203303-DIAMOND.png", "MASTER": "/challenges-images/203303-MASTER.png", "GRANDMASTER": "/challenges-images/203303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 90}, "GOLD": {"value": 180}, "PLATINUM": {"value": 540}, "DIAMOND": {"value": 1080}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 203304, "name": "Splitpush Strategy", "description": "Hancurkan turret jalur samping sendirian (kamu <PERSON><PERSON>n damage terbanyak) setelah 14 menit tanpa mati", "shortDescription": "Hancurkan turret jalur samping sendirian tanpa mati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203304-IRON.png", "BRONZE": "/challenges-images/203304-BRONZE.png", "SILVER": "/challenges-images/203304-SILVER.png", "GOLD": "/challenges-images/203304-GOLD.png", "PLATINUM": "/challenges-images/203304-PLATINUM.png", "DIAMOND": "/challenges-images/203304-DIAMOND.png", "MASTER": "/challenges-images/203304-MASTER.png", "GRANDMASTER": "/challenges-images/203304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203304-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 9}, "SILVER": {"value": 27}, "GOLD": {"value": 60}, "PLATINUM": {"value": 180}, "DIAMOND": {"value": 350}, "MASTER": {"value": 650}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 203305, "name": "Blink and You'll Miss It", "description": "Bunuh champion dengan health penuh dalam waktu 1,5 detik dengan damage mayor<PERSON> da<PERSON><PERSON> (90% atau lebih)", "shortDescription": "Bunuh champion dengan health penuh dalam waktu 1,5 detik", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203305-IRON.png", "BRONZE": "/challenges-images/203305-BRONZE.png", "SILVER": "/challenges-images/203305-SILVER.png", "GOLD": "/challenges-images/203305-GOLD.png", "PLATINUM": "/challenges-images/203305-PLATINUM.png", "DIAMOND": "/challenges-images/203305-DIAMOND.png", "MASTER": "/challenges-images/203305-MASTER.png", "GRANDMASTER": "/challenges-images/203305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 80}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 300}, "CHALLENGER": {"value": 500}}}, {"id": 203400, "name": "Predator", "description": "Raih progres dari tantangan di grup Predator", "shortDescription": "Raih progres dari tantangan di grup Predator", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203400-IRON.png", "BRONZE": "/challenges-images/203400-BRONZE.png", "SILVER": "/challenges-images/203400-SILVER.png", "GOLD": "/challenges-images/203400-GOLD.png", "PLATINUM": "/challenges-images/203400-PLATINUM.png", "DIAMOND": "/challenges-images/203400-DIAMOND.png", "MASTER": "/challenges-images/203400-MASTER.png", "GRANDMASTER": "/challenges-images/203400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 55}, "GOLD": {"value": 150}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 430, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Predator"}]}, "MASTER": {"value": 725}}}, {"id": 203401, "name": "Dragon On Too Long", "description": "Lakukan assist dalam membunuh dragon pertama sebelum 8 menit", "shortDescription": "Takedown dragon pertama sebelum delapan menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203401-IRON.png", "BRONZE": "/challenges-images/203401-BRONZE.png", "SILVER": "/challenges-images/203401-SILVER.png", "GOLD": "/challenges-images/203401-GOLD.png", "PLATINUM": "/challenges-images/203401-PLATINUM.png", "DIAMOND": "/challenges-images/203401-DIAMOND.png", "MASTER": "/challenges-images/203401-MASTER.png", "GRANDMASTER": "/challenges-images/203401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203401-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 203402, "name": "<PERSON><PERSON>", "description": "Ambil buff monster jungle dari jungle musuh", "shortDescription": "<PERSON>bil buff musuh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203402-IRON.png", "BRONZE": "/challenges-images/203402-BRONZE.png", "SILVER": "/challenges-images/203402-SILVER.png", "GOLD": "/challenges-images/203402-GOLD.png", "PLATINUM": "/challenges-images/203402-PLATINUM.png", "DIAMOND": "/challenges-images/203402-DIAMOND.png", "MASTER": "/challenges-images/203402-MASTER.png", "GRANDMASTER": "/challenges-images/203402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203402-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 16}, "SILVER": {"value": 40}, "GOLD": {"value": 120}, "PLATINUM": {"value": 360}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1200}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 1800}}}, {"id": 203403, "name": "Clean Clears", "description": "<PERSON><PERSON><PERSON> jungler, da<PERSON>tkan 70 CS dari monster jungle sebelum 10 menit", "shortDescription": "Dapatkan 70 CS dari monster jungle sebelum 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203403-IRON.png", "BRONZE": "/challenges-images/203403-BRONZE.png", "SILVER": "/challenges-images/203403-SILVER.png", "GOLD": "/challenges-images/203403-GOLD.png", "PLATINUM": "/challenges-images/203403-PLATINUM.png", "DIAMOND": "/challenges-images/203403-DIAMOND.png", "MASTER": "/challenges-images/203403-MASTER.png", "GRANDMASTER": "/challenges-images/203403-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203403-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 4}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 65}}}, {"id": 203404, "name": "Three Buffed", "description": "Sebagai jungler, ambil tiga dari empat buff camp pertama", "shortDescription": "Ambil 3 dari 4 buff camp pertama", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203404-IRON.png", "BRONZE": "/challenges-images/203404-BRONZE.png", "SILVER": "/challenges-images/203404-SILVER.png", "GOLD": "/challenges-images/203404-GOLD.png", "PLATINUM": "/challenges-images/203404-PLATINUM.png", "DIAMOND": "/challenges-images/203404-DIAMOND.png", "MASTER": "/challenges-images/203404-MASTER.png", "GRANDMASTER": "/challenges-images/203404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203404-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 250}}}, {"id": 203405, "name": "<PERSON><PERSON>", "description": "Sebagai jungler, bunuh kedua scuttle crab yang muncul di awal", "shortDescription": "Bunuh kedua scuttle crab pertama yang muncul", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203405-IRON.png", "BRONZE": "/challenges-images/203405-BRONZE.png", "SILVER": "/challenges-images/203405-SILVER.png", "GOLD": "/challenges-images/203405-GOLD.png", "PLATINUM": "/challenges-images/203405-PLATINUM.png", "DIAMOND": "/challenges-images/203405-DIAMOND.png", "MASTER": "/challenges-images/203405-MASTER.png", "GRANDMASTER": "/challenges-images/203405-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203405-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 10, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 203406, "name": "Target Secured", "description": "Amankan Monster Epik dengan ada jungler musuh di sekitar. Monster Epik men<PERSON>, Rift Herald, dan <PERSON>.", "shortDescription": "Amankan Monster Epik dengan ada jungler musuh di sekitar", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203406-IRON.png", "BRONZE": "/challenges-images/203406-BRONZE.png", "SILVER": "/challenges-images/203406-SILVER.png", "GOLD": "/challenges-images/203406-GOLD.png", "PLATINUM": "/challenges-images/203406-PLATINUM.png", "DIAMOND": "/challenges-images/203406-DIAMOND.png", "MASTER": "/challenges-images/203406-MASTER.png", "GRANDMASTER": "/challenges-images/203406-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203406-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 18}, "GOLD": {"value": 38}, "PLATINUM": {"value": 98}, "DIAMOND": {"value": 208}, "MASTER": {"value": 328}, "GRANDMASTER": {"value": 450}, "CHALLENGER": {"value": 600}}}, {"id": 203407, "name": "It's My Jungle Now", "description": "Sebagai jungler, sebelum 10 menit, kuasai jungle musuh lebih dari mereka", "shortDescription": "Sebelum 10 menit, kuasai jungle musuh lebih dari mereka", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203407-IRON.png", "BRONZE": "/challenges-images/203407-BRONZE.png", "SILVER": "/challenges-images/203407-SILVER.png", "GOLD": "/challenges-images/203407-GOLD.png", "PLATINUM": "/challenges-images/203407-PLATINUM.png", "DIAMOND": "/challenges-images/203407-DIAMOND.png", "MASTER": "/challenges-images/203407-MASTER.png", "GRANDMASTER": "/challenges-images/203407-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203407-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 13}, "CHALLENGER": {"value": 28}}}, {"id": 203408, "name": "The Most Dangerous Game", "description": "Sebagai jungler, la<PERSON><PERSON> kill pada jungler musuh di jungle mereka sendiri sebelum 10 menit", "shortDescription": "Sebagai jungler, bunuh jungler musuh di dalam jungle mereka sebelum 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203408-IRON.png", "BRONZE": "/challenges-images/203408-BRONZE.png", "SILVER": "/challenges-images/203408-SILVER.png", "GOLD": "/challenges-images/203408-GOLD.png", "PLATINUM": "/challenges-images/203408-PLATINUM.png", "DIAMOND": "/challenges-images/203408-DIAMOND.png", "MASTER": "/challenges-images/203408-MASTER.png", "GRANDMASTER": "/challenges-images/203408-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203408-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 15}, "PLATINUM": {"value": 35, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Invader"}]}, "DIAMOND": {"value": 70}, "MASTER": {"value": 120}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 203409, "name": "Epic Steal", "description": "Curi dua Monster Epik dalam satu game. Monster Epik mencakup <PERSON>, <PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Curi dua Monster Epik dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203409-IRON.png", "BRONZE": "/challenges-images/203409-BRONZE.png", "SILVER": "/challenges-images/203409-SILVER.png", "GOLD": "/challenges-images/203409-GOLD.png", "PLATINUM": "/challenges-images/203409-PLATINUM.png", "DIAMOND": "/challenges-images/203409-DIAMOND.png", "MASTER": "/challenges-images/203409-MASTER.png", "GRANDMASTER": "/challenges-images/203409-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203409-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 3}, "MASTER": {"value": 6}}}, {"id": 204000, "name": "Mastermind", "description": "Raih progres dari tantangan di grup Cornerstone dan Visionary", "shortDescription": "Raih progres dari tantangan di grup Cornerstone dan Visionary", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/204000-IRON.png", "BRONZE": "/challenges-images/204000-BRONZE.png", "SILVER": "/challenges-images/204000-SILVER.png", "GOLD": "/challenges-images/204000-GOLD.png", "PLATINUM": "/challenges-images/204000-PLATINUM.png", "DIAMOND": "/challenges-images/204000-DIAMOND.png", "MASTER": "/challenges-images/204000-MASTER.png", "GRANDMASTER": "/challenges-images/204000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 130}, "PLATINUM": {"value": 210}, "DIAMOND": {"value": 375}, "MASTER": {"value": 650, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Mastermind"}]}}}, {"id": 204100, "name": "Cornerstone", "description": "Raih progres dari tantangan di grup Cornerstone", "shortDescription": "Raih progres dari tantangan di grup Cornerstone", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/204100-IRON.png", "BRONZE": "/challenges-images/204100-BRONZE.png", "SILVER": "/challenges-images/204100-SILVER.png", "GOLD": "/challenges-images/204100-GOLD.png", "PLATINUM": "/challenges-images/204100-PLATINUM.png", "DIAMOND": "/challenges-images/204100-DIAMOND.png", "MASTER": "/challenges-images/204100-MASTER.png", "GRANDMASTER": "/challenges-images/204100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Cornerstone"}]}, "MASTER": {"value": 250}}}, {"id": 204101, "name": "Superior Supporting", "description": "Selesaikan quest support setidaknya 60 detik lebih cepat daripada support musuh", "shortDescription": "Selesaikan quest support lebih cepat daripada musuhmu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204101-IRON.png", "BRONZE": "/challenges-images/204101-BRONZE.png", "SILVER": "/challenges-images/204101-SILVER.png", "GOLD": "/challenges-images/204101-GOLD.png", "PLATINUM": "/challenges-images/204101-PLATINUM.png", "DIAMOND": "/challenges-images/204101-DIAMOND.png", "MASTER": "/challenges-images/204101-MASTER.png", "GRANDMASTER": "/challenges-images/204101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 600}, "GRANDMASTER": {"value": 900}, "CHALLENGER": {"value": 1500}}}, {"id": 204102, "name": "All-Seeing", "description": "Miliki lebih dari 2 Skor Vision per menit", "shortDescription": "Miliki lebih dari 2 Skor Vision per menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204102-IRON.png", "BRONZE": "/challenges-images/204102-BRONZE.png", "SILVER": "/challenges-images/204102-SILVER.png", "GOLD": "/challenges-images/204102-GOLD.png", "PLATINUM": "/challenges-images/204102-PLATINUM.png", "DIAMOND": "/challenges-images/204102-DIAMOND.png", "MASTER": "/challenges-images/204102-MASTER.png", "GRANDMASTER": "/challenges-images/204102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75, "rewards": [{"category": "TITLE", "quantity": 1, "title": "All-Seeing"}]}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 204103, "name": "Support Subsidy", "description": "Selesaikan quest item support dalam waktu kurang dari 14 menit", "shortDescription": "<PERSON><PERSON>aikan seluruh quest support dalam 14 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204103-IRON.png", "BRONZE": "/challenges-images/204103-BRONZE.png", "SILVER": "/challenges-images/204103-SILVER.png", "GOLD": "/challenges-images/204103-GOLD.png", "PLATINUM": "/challenges-images/204103-PLATINUM.png", "DIAMOND": "/challenges-images/204103-DIAMOND.png", "MASTER": "/challenges-images/204103-MASTER.png", "GRANDMASTER": "/challenges-images/204103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 204200, "name": "Visionary", "description": "Raih progres dari tantangan di grup Visionary", "shortDescription": "Raih progres dari tantangan di grup Visionary", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/204200-IRON.png", "BRONZE": "/challenges-images/204200-BRONZE.png", "SILVER": "/challenges-images/204200-SILVER.png", "GOLD": "/challenges-images/204200-GOLD.png", "PLATINUM": "/challenges-images/204200-PLATINUM.png", "DIAMOND": "/challenges-images/204200-DIAMOND.png", "MASTER": "/challenges-images/204200-MASTER.png", "GRANDMASTER": "/challenges-images/204200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Visionary"}]}, "MASTER": {"value": 250}}}, {"id": 204201, "name": "<PERSON>", "description": "Hancurkan 10 ward sebelum 20 menit", "shortDescription": "Hancurkan 10 ward sebelum 20 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204201-IRON.png", "BRONZE": "/challenges-images/204201-BRONZE.png", "SILVER": "/challenges-images/204201-SILVER.png", "GOLD": "/challenges-images/204201-GOLD.png", "PLATINUM": "/challenges-images/204201-PLATINUM.png", "DIAMOND": "/challenges-images/204201-DIAMOND.png", "MASTER": "/challenges-images/204201-MASTER.png", "GRANDMASTER": "/challenges-images/204201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204201-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 75}}}, {"id": 204202, "name": "Ward Ward-en", "description": "Lindungi ward dengan berada di sekitarnya saat musuh menyerang ward tetapi tetap bertahan", "shortDescription": "Lindungi ward", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204202-IRON.png", "BRONZE": "/challenges-images/204202-BRONZE.png", "SILVER": "/challenges-images/204202-SILVER.png", "GOLD": "/challenges-images/204202-GOLD.png", "PLATINUM": "/challenges-images/204202-PLATINUM.png", "DIAMOND": "/challenges-images/204202-DIAMOND.png", "MASTER": "/challenges-images/204202-MASTER.png", "GRANDMASTER": "/challenges-images/204202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON> Warden"}]}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 204203, "name": "Incredible Value", "description": "Hancurkan dua Sight Ward atau lebih dengan satu aktivasi Sweeper", "shortDescription": "Hancurkan dua ward dengan satu aktivasi Sweeper", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204203-IRON.png", "BRONZE": "/challenges-images/204203-BRONZE.png", "SILVER": "/challenges-images/204203-SILVER.png", "GOLD": "/challenges-images/204203-GOLD.png", "PLATINUM": "/challenges-images/204203-PLATINUM.png", "DIAMOND": "/challenges-images/204203-DIAMOND.png", "MASTER": "/challenges-images/204203-MASTER.png", "GRANDMASTER": "/challenges-images/204203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204203-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 6}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 24}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 210000, "name": "Magnum Opus", "description": "Raih progres dari tantangan di grup Magnum Opus", "shortDescription": "Raih progres dari tantangan di grup Magnum Opus", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/210000-IRON.png", "BRONZE": "/challenges-images/210000-BRONZE.png", "SILVER": "/challenges-images/210000-SILVER.png", "GOLD": "/challenges-images/210000-GOLD.png", "PLATINUM": "/challenges-images/210000-PLATINUM.png", "DIAMOND": "/challenges-images/210000-DIAMOND.png", "MASTER": "/challenges-images/210000-MASTER.png", "GRANDMASTER": "/challenges-images/210000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tingkat Top"}]}}}, {"id": 210001, "name": "Perfectionist", "description": "Raih grade S+ pada berbagai champion", "shortDescription": "Raih grade S+ dengan <em>berbagai champion</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210001-IRON.png", "BRONZE": "/challenges-images/210001-BRONZE.png", "SILVER": "/challenges-images/210001-SILVER.png", "GOLD": "/challenges-images/210001-GOLD.png", "PLATINUM": "/challenges-images/210001-PLATINUM.png", "DIAMOND": "/challenges-images/210001-DIAMOND.png", "MASTER": "/challenges-images/210001-MASTER.png", "GRANDMASTER": "/challenges-images/210001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 50, "rewards": [{"category": "TITLE", "quantity": 1, "title": "S+"}]}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 160}, "CHALLENGER": {"value": 170}}}, {"id": 210002, "name": "<PERSON> Penta, Different Champ", "description": "<PERSON><PERSON><PERSON> dengan berb<PERSON>i champion", "shortDescription": "<PERSON><PERSON><PERSON> <em>berbagai champion</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210002-IRON.png", "BRONZE": "/challenges-images/210002-BRONZE.png", "SILVER": "/challenges-images/210002-SILVER.png", "GOLD": "/challenges-images/210002-GOLD.png", "PLATINUM": "/challenges-images/210002-PLATINUM.png", "DIAMOND": "/challenges-images/210002-DIAMOND.png", "MASTER": "/challenges-images/210002-MASTER.png", "GRANDMASTER": "/challenges-images/210002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 210003, "name": "Top Performer", "description": "Raih grade S- atau lebih tinggi", "shortDescription": "Dapatkan grade S- atau lebih tinggi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210003-IRON.png", "BRONZE": "/challenges-images/210003-BRONZE.png", "SILVER": "/challenges-images/210003-SILVER.png", "GOLD": "/challenges-images/210003-GOLD.png", "PLATINUM": "/challenges-images/210003-PLATINUM.png", "DIAMOND": "/challenges-images/210003-DIAMOND.png", "MASTER": "/challenges-images/210003-MASTER.png", "GRANDMASTER": "/challenges-images/210003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 300}, "CHALLENGER": {"value": 500}}}, {"id": 210004, "name": "Ladder Climber", "description": "Raih rank di Mode Ranked Solo/Duo. <PERSON><PERSON> harus menyelesai<PERSON> provisional.", "shortDescription": "Raih rank di Mode Solo/Duo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/210004-IRON.png", "BRONZE": "/challenges-images/210004-BRONZE.png", "SILVER": "/challenges-images/210004-SILVER.png", "GOLD": "/challenges-images/210004-GOLD.png", "PLATINUM": "/challenges-images/210004-PLATINUM.png", "DIAMOND": "/challenges-images/210004-DIAMOND.png", "MASTER": "/challenges-images/210004-MASTER.png", "GRANDMASTER": "/challenges-images/210004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 9}, "BRONZE": {"value": 8}, "SILVER": {"value": 7}, "GOLD": {"value": 6}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 3}, "MASTER": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Masterful"}]}}}, {"id": 210005, "name": "KDA Player", "description": "Dapatkan KDA lebih dari 3 di Mode Ranked Solo/Duo", "shortDescription": "Dapatkan KDA lebih dari 3 di Mode Solo/Duo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210005-IRON.png", "BRONZE": "/challenges-images/210005-BRONZE.png", "SILVER": "/challenges-images/210005-SILVER.png", "GOLD": "/challenges-images/210005-GOLD.png", "PLATINUM": "/challenges-images/210005-PLATINUM.png", "DIAMOND": "/challenges-images/210005-DIAMOND.png", "MASTER": "/challenges-images/210005-MASTER.png", "GRANDMASTER": "/challenges-images/210005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 50}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 250}, "MASTER": {"value": 400, "rewards": [{"category": "TITLE", "quantity": 1, "title": "KDA Player"}]}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 600}}}, {"id": 210006, "name": "Forever Triumphant", "description": "Akhiri season split dengan rank Gold atau lebih tinggi", "shortDescription": "Akhiri season split dengan rank Gold atau lebih tinggi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/210006-IRON.png", "BRONZE": "/challenges-images/210006-BRONZE.png", "SILVER": "/challenges-images/210006-SILVER.png", "GOLD": "/challenges-images/210006-GOLD.png", "PLATINUM": "/challenges-images/210006-PLATINUM.png", "DIAMOND": "/challenges-images/210006-DIAMOND.png", "MASTER": "/challenges-images/210006-MASTER.png", "GRANDMASTER": "/challenges-images/210006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210006-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 2}, "GOLD": {"value": 4}, "PLATINUM": {"value": 6}, "DIAMOND": {"value": 8}, "MASTER": {"value": 10}}}, {"id": 3, "name": "VETERANCY", "description": "", "shortDescription": "Capstone VETERANCY", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 150}, "BRONZE": {"value": 275}, "SILVER": {"value": 400}, "GOLD": {"value": 1000}, "PLATINUM": {"value": 1500}, "DIAMOND": {"value": 2700}, "MASTER": {"value": 4500}}}, {"id": 301000, "name": "Determination", "description": "Raih progres dari tantangan di grup <PERSON>, Steadfast, dan <PERSON>ymbio<PERSON>", "shortDescription": "Raih progres dari tantangan di grup <PERSON>, Steadfast, dan <PERSON>ymbio<PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301000-IRON.png", "BRONZE": "/challenges-images/301000-BRONZE.png", "SILVER": "/challenges-images/301000-SILVER.png", "GOLD": "/challenges-images/301000-GOLD.png", "PLATINUM": "/challenges-images/301000-PLATINUM.png", "DIAMOND": "/challenges-images/301000-DIAMOND.png", "MASTER": "/challenges-images/301000-MASTER.png", "GRANDMASTER": "/challenges-images/301000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 70}, "GOLD": {"value": 245}, "PLATINUM": {"value": 410}, "DIAMOND": {"value": 840}, "MASTER": {"value": 1300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tenacious"}]}}}, {"id": 301100, "name": "Monster Hunter", "description": "Raih progres dari tantangan di grup Monster Hunter", "shortDescription": "Raih progres dari tantangan di grup Monster Hunter", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301100-IRON.png", "BRONZE": "/challenges-images/301100-BRONZE.png", "SILVER": "/challenges-images/301100-SILVER.png", "GOLD": "/challenges-images/301100-GOLD.png", "PLATINUM": "/challenges-images/301100-PLATINUM.png", "DIAMOND": "/challenges-images/301100-DIAMOND.png", "MASTER": "/challenges-images/301100-MASTER.png", "GRANDMASTER": "/challenges-images/301100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 65}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Big Game Hunter"}]}, "MASTER": {"value": 450}}}, {"id": 301101, "name": "Always On Time", "description": "Bunuh Monster Epik dalam waktu 30 detik setelah mereka muncul. Monster Epik <PERSON>, Rift Herald, dan <PERSON>.", "shortDescription": "Bunuh Monster Epik dalam waktu 30 detik setelah muncul", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301101-IRON.png", "BRONZE": "/challenges-images/301101-BRONZE.png", "SILVER": "/challenges-images/301101-SILVER.png", "GOLD": "/challenges-images/301101-GOLD.png", "PLATINUM": "/challenges-images/301101-PLATINUM.png", "DIAMOND": "/challenges-images/301101-DIAMOND.png", "MASTER": "/challenges-images/301101-MASTER.png", "GRANDMASTER": "/challenges-images/301101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Epic"}]}, "GOLD": {"value": 50}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 250}, "MASTER": {"value": 400}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 301102, "name": "Gets The Wurm", "description": "<PERSON><PERSON> sebelum 27 menit", "shortDescription": "Kalah<PERSON> Baron sebelum 27 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301102-IRON.png", "BRONZE": "/challenges-images/301102-BRONZE.png", "SILVER": "/challenges-images/301102-SILVER.png", "GOLD": "/challenges-images/301102-GOLD.png", "PLATINUM": "/challenges-images/301102-PLATINUM.png", "DIAMOND": "/challenges-images/301102-DIAMOND.png", "MASTER": "/challenges-images/301102-MASTER.png", "GRANDMASTER": "/challenges-images/301102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301102-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Early Bird"}]}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 10}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 25}, "CHALLENGER": {"value": 50}}}, {"id": 301103, "name": "Dragon Hunt", "description": "<PERSON><PERSON>kan Elder Dragon sebelum 28 menit", "shortDescription": "Kalahkan Elder Dragon sebelum 28 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301103-IRON.png", "BRONZE": "/challenges-images/301103-BRONZE.png", "SILVER": "/challenges-images/301103-SILVER.png", "GOLD": "/challenges-images/301103-GOLD.png", "PLATINUM": "/challenges-images/301103-PLATINUM.png", "DIAMOND": "/challenges-images/301103-DIAMOND.png", "MASTER": "/challenges-images/301103-MASTER.png", "GRANDMASTER": "/challenges-images/301103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301103-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}, "GRANDMASTER": {"value": 5}, "CHALLENGER": {"value": 8}}}, {"id": 301105, "name": "Soul Sweep", "description": "<PERSON><PERSON>m Dragon Soul tanpa tim musuh mengalahkan satu dragon", "shortDescription": "<PERSON><PERSON>m Dragon Soul 4-0", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301105-IRON.png", "BRONZE": "/challenges-images/301105-BRONZE.png", "SILVER": "/challenges-images/301105-SILVER.png", "GOLD": "/challenges-images/301105-GOLD.png", "PLATINUM": "/challenges-images/301105-PLATINUM.png", "DIAMOND": "/challenges-images/301105-DIAMOND.png", "MASTER": "/challenges-images/301105-MASTER.png", "GRANDMASTER": "/challenges-images/301105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 50}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 130}, "CHALLENGER": {"value": 250}}}, {"id": 301106, "name": "Can of Wurms", "description": "Bunuh tiga Baron dalam satu game", "shortDescription": "Bunuh tiga Baron dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301106-IRON.png", "BRONZE": "/challenges-images/301106-BRONZE.png", "SILVER": "/challenges-images/301106-SILVER.png", "GOLD": "/challenges-images/301106-GOLD.png", "PLATINUM": "/challenges-images/301106-PLATINUM.png", "DIAMOND": "/challenges-images/301106-DIAMOND.png", "MASTER": "/challenges-images/301106-MASTER.png", "GRANDMASTER": "/challenges-images/301106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301106-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 301107, "name": "Draconic Extinction", "description": "Bunuh dua Elder Dragon dalam satu game", "shortDescription": "Bunuh dua Elder Dragon dalam satu game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301107-IRON.png", "BRONZE": "/challenges-images/301107-BRONZE.png", "SILVER": "/challenges-images/301107-SILVER.png", "GOLD": "/challenges-images/301107-GOLD.png", "PLATINUM": "/challenges-images/301107-PLATINUM.png", "DIAMOND": "/challenges-images/301107-DIAMOND.png", "MASTER": "/challenges-images/301107-MASTER.png", "GRANDMASTER": "/challenges-images/301107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301107-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 301200, "name": "Steadfast", "description": "Raih progres dari tantangan di grup Steadfast", "shortDescription": "Raih progres dari tantangan di grup Steadfast", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301200-IRON.png", "BRONZE": "/challenges-images/301200-BRONZE.png", "SILVER": "/challenges-images/301200-SILVER.png", "GOLD": "/challenges-images/301200-GOLD.png", "PLATINUM": "/challenges-images/301200-PLATINUM.png", "DIAMOND": "/challenges-images/301200-DIAMOND.png", "MASTER": "/challenges-images/301200-MASTER.png", "GRANDMASTER": "/challenges-images/301200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301200-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 5}, "SILVER": {"value": 25}, "GOLD": {"value": 65}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 220, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Courageous"}]}, "MASTER": {"value": 350}}}, {"id": 301201, "name": "Open Victory", "description": "Menangkan game dengan Nexus terbuka", "shortDescription": "<PERSON><PERSON> dengan Nexus terbuka", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301201-IRON.png", "BRONZE": "/challenges-images/301201-BRONZE.png", "SILVER": "/challenges-images/301201-SILVER.png", "GOLD": "/challenges-images/301201-GOLD.png", "PLATINUM": "/challenges-images/301201-PLATINUM.png", "DIAMOND": "/challenges-images/301201-DIAMOND.png", "MASTER": "/challenges-images/301201-MASTER.png", "GRANDMASTER": "/challenges-images/301201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301201-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Comeback Kid"}]}, "DIAMOND": {"value": 4}, "MASTER": {"value": 7}}}, {"id": 301202, "name": "Uninhibited", "description": "Menangkan game setelah kehilangan satu inhibitor", "shortDescription": "<PERSON><PERSON> setelah kehilangan satu inhibitor", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301202-IRON.png", "BRONZE": "/challenges-images/301202-BRONZE.png", "SILVER": "/challenges-images/301202-SILVER.png", "GOLD": "/challenges-images/301202-GOLD.png", "PLATINUM": "/challenges-images/301202-PLATINUM.png", "DIAMOND": "/challenges-images/301202-DIAMOND.png", "MASTER": "/challenges-images/301202-MASTER.png", "GRANDMASTER": "/challenges-images/301202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301202-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 7, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Uninhibited"}]}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 45}, "MASTER": {"value": 75}}}, {"id": 301203, "name": "Comeback Kids", "description": "Menangkan game set<PERSON><PERSON> k<PERSON> 15 kill", "shortDescription": "<PERSON><PERSON> k<PERSON> 15 kill", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301203-IRON.png", "BRONZE": "/challenges-images/301203-BRONZE.png", "SILVER": "/challenges-images/301203-SILVER.png", "GOLD": "/challenges-images/301203-GOLD.png", "PLATINUM": "/challenges-images/301203-PLATINUM.png", "DIAMOND": "/challenges-images/301203-DIAMOND.png", "MASTER": "/challenges-images/301203-MASTER.png", "GRANDMASTER": "/challenges-images/301203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301203-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 4}, "DIAMOND": {"value": 8}, "MASTER": {"value": 12}}}, {"id": 301204, "name": "Who Needs 'Em", "description": "Menangkan game meski rekan tim <PERSON>", "shortDescription": "Menangkan game meski rekan tim <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301204-IRON.png", "BRONZE": "/challenges-images/301204-BRONZE.png", "SILVER": "/challenges-images/301204-SILVER.png", "GOLD": "/challenges-images/301204-GOLD.png", "PLATINUM": "/challenges-images/301204-PLATINUM.png", "DIAMOND": "/challenges-images/301204-DIAMOND.png", "MASTER": "/challenges-images/301204-MASTER.png", "GRANDMASTER": "/challenges-images/301204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301204-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}}}, {"id": 301205, "name": "No Loyalty Among Dragons", "description": "Kalahkan Elder Dragon saat musuhmu memiliki Dragon Soul", "shortDescription": "Ambil Elder Dragon saat musuh memiliki Dragon Soul", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301205-IRON.png", "BRONZE": "/challenges-images/301205-BRONZE.png", "SILVER": "/challenges-images/301205-SILVER.png", "GOLD": "/challenges-images/301205-GOLD.png", "PLATINUM": "/challenges-images/301205-PLATINUM.png", "DIAMOND": "/challenges-images/301205-DIAMOND.png", "MASTER": "/challenges-images/301205-MASTER.png", "GRANDMASTER": "/challenges-images/301205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301205-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Incendiary"}]}, "DIAMOND": {"value": 12}, "MASTER": {"value": 18}}}, {"id": 301300, "name": "Symbiosis", "description": "Raih progres dari tantangan di grup Symbiosis", "shortDescription": "Raih progres dari tantangan di grup Symbiosis", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301300-IRON.png", "BRONZE": "/challenges-images/301300-BRONZE.png", "SILVER": "/challenges-images/301300-SILVER.png", "GOLD": "/challenges-images/301300-GOLD.png", "PLATINUM": "/challenges-images/301300-PLATINUM.png", "DIAMOND": "/challenges-images/301300-DIAMOND.png", "MASTER": "/challenges-images/301300-MASTER.png", "GRANDMASTER": "/challenges-images/301300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 25}, "BRONZE": {"value": 50}, "SILVER": {"value": 75}, "GOLD": {"value": 100}, "PLATINUM": {"value": 105}, "DIAMOND": {"value": 160, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Exceptional"}]}, "MASTER": {"value": 300}}}, {"id": 301301, "name": "Flawless Victory", "description": "Menangkan Game Sempurna dengan tim musuh nol kill, tanpa dragon, tanpa Rift Herald atau Baron, dan tidak menghan<PERSON>rkan turret", "shortDescription": "Menangkan Game Sempurna", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301301-IRON.png", "BRONZE": "/challenges-images/301301-BRONZE.png", "SILVER": "/challenges-images/301301-SILVER.png", "GOLD": "/challenges-images/301301-GOLD.png", "PLATINUM": "/challenges-images/301301-PLATINUM.png", "DIAMOND": "/challenges-images/301301-DIAMOND.png", "MASTER": "/challenges-images/301301-MASTER.png", "GRANDMASTER": "/challenges-images/301301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301301-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Flawless"}]}}}, {"id": 301302, "name": "Leading by Example", "description": "Lakukan 12 assist at<PERSON> lebih tanpa mati", "shortDescription": "Dapatkan assist streak 12+ tanpa mati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301302-IRON.png", "BRONZE": "/challenges-images/301302-BRONZE.png", "SILVER": "/challenges-images/301302-SILVER.png", "GOLD": "/challenges-images/301302-GOLD.png", "PLATINUM": "/challenges-images/301302-PLATINUM.png", "DIAMOND": "/challenges-images/301302-DIAMOND.png", "MASTER": "/challenges-images/301302-MASTER.png", "GRANDMASTER": "/challenges-images/301302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 180}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 301303, "name": "Winhibitors", "description": "Hancurkan ketiga inhibitor musuh kurang dari 25 menit", "shortDescription": "Hancurkan ketiga inhibitor musuh kurang dari 25 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301303-IRON.png", "BRONZE": "/challenges-images/301303-BRONZE.png", "SILVER": "/challenges-images/301303-SILVER.png", "GOLD": "/challenges-images/301303-GOLD.png", "PLATINUM": "/challenges-images/301303-PLATINUM.png", "DIAMOND": "/challenges-images/301303-DIAMOND.png", "MASTER": "/challenges-images/301303-MASTER.png", "GRANDMASTER": "/challenges-images/301303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301303-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}}}, {"id": 301304, "name": "Team Diff", "description": "Raih ace set<PERSON>h minion muncul tetapi sebelum 15 menit", "shortDescription": "Dapatkan ace dari sejak minio muncul hingga 15 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301304-IRON.png", "BRONZE": "/challenges-images/301304-BRONZE.png", "SILVER": "/challenges-images/301304-SILVER.png", "GOLD": "/challenges-images/301304-GOLD.png", "PLATINUM": "/challenges-images/301304-PLATINUM.png", "DIAMOND": "/challenges-images/301304-DIAMOND.png", "MASTER": "/challenges-images/301304-MASTER.png", "GRANDMASTER": "/challenges-images/301304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301304-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 15}, "CHALLENGER": {"value": 20}}}, {"id": 301305, "name": "Happy Feet", "description": "Menari dengan Rift Herald di markas musuh di akhir game", "shortDescription": "Menari dengan Rift Herald di markas musuh", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301305-IRON.png", "BRONZE": "/challenges-images/301305-BRONZE.png", "SILVER": "/challenges-images/301305-SILVER.png", "GOLD": "/challenges-images/301305-GOLD.png", "PLATINUM": "/challenges-images/301305-PLATINUM.png", "DIAMOND": "/challenges-images/301305-DIAMOND.png", "MASTER": "/challenges-images/301305-MASTER.png", "GRANDMASTER": "/challenges-images/301305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301305-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Jitterbug"}]}}}, {"id": 301306, "name": "Let's Make This Quick", "description": "Hancurkan Nexus musuh dalam waktu kurang dari 15 menit", "shortDescription": "Hancurkan Nexus musuh dalam waktu kurang dari 15 menit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301306-IRON.png", "BRONZE": "/challenges-images/301306-BRONZE.png", "SILVER": "/challenges-images/301306-SILVER.png", "GOLD": "/challenges-images/301306-GOLD.png", "PLATINUM": "/challenges-images/301306-PLATINUM.png", "DIAMOND": "/challenges-images/301306-DIAMOND.png", "MASTER": "/challenges-images/301306-MASTER.png", "GRANDMASTER": "/challenges-images/301306-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301306-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}}}, {"id": 302000, "name": "Captain", "description": "Raih progres dari tantangan di grup Strategy, Demolition, Synergy, dan Team Spirit", "shortDescription": "Raih progres dari tantangan di grup Strategy, Demolition, Synergy, dan Team Spirit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302000-IRON.png", "BRONZE": "/challenges-images/302000-BRONZE.png", "SILVER": "/challenges-images/302000-SILVER.png", "GOLD": "/challenges-images/302000-GOLD.png", "PLATINUM": "/challenges-images/302000-PLATINUM.png", "DIAMOND": "/challenges-images/302000-DIAMOND.png", "MASTER": "/challenges-images/302000-MASTER.png", "GRANDMASTER": "/challenges-images/302000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 45}, "BRONZE": {"value": 95}, "SILVER": {"value": 150}, "GOLD": {"value": 350}, "PLATINUM": {"value": 560}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1700, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Playmaker"}]}}}, {"id": 302100, "name": "Strategy", "description": "Raih progres dari tantangan di grup Strategy", "shortDescription": "Raih progres dari tantangan di grup Strategy", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302100-IRON.png", "BRONZE": "/challenges-images/302100-BRONZE.png", "SILVER": "/challenges-images/302100-SILVER.png", "GOLD": "/challenges-images/302100-GOLD.png", "PLATINUM": "/challenges-images/302100-PLATINUM.png", "DIAMOND": "/challenges-images/302100-DIAMOND.png", "MASTER": "/challenges-images/302100-MASTER.png", "GRANDMASTER": "/challenges-images/302100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 40}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Strategist"}]}, "MASTER": {"value": 475}}}, {"id": 302101, "name": "Level 1 Fiesta", "description": "Lakukan takedown pada champion musuh sebelum camp jungle muncul (1:30)", "shortDescription": "Lakukan takedown sebelum camp jungle muncul", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302101-IRON.png", "BRONZE": "/challenges-images/302101-BRONZE.png", "SILVER": "/challenges-images/302101-SILVER.png", "GOLD": "/challenges-images/302101-GOLD.png", "PLATINUM": "/challenges-images/302101-PLATINUM.png", "DIAMOND": "/challenges-images/302101-DIAMOND.png", "MASTER": "/challenges-images/302101-MASTER.png", "GRANDMASTER": "/challenges-images/302101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 75}, "MASTER": {"value": 130}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 302102, "name": "Farm Champs not Camps", "description": "Sebagai jungler, lakukan kill pada pemain top lane, mid lane, bot lane, atau support sebelum 10 menit", "shortDescription": "Sebagai jungler, la<PERSON><PERSON> kill pada laner sebelum 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302102-IRON.png", "BRONZE": "/challenges-images/302102-BRONZE.png", "SILVER": "/challenges-images/302102-SILVER.png", "GOLD": "/challenges-images/302102-GOLD.png", "PLATINUM": "/challenges-images/302102-PLATINUM.png", "DIAMOND": "/challenges-images/302102-DIAMOND.png", "MASTER": "/challenges-images/302102-MASTER.png", "GRANDMASTER": "/challenges-images/302102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 125}, "MASTER": {"value": 225}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 650}}}, {"id": 302103, "name": "Overwhelming Presence", "description": "Sebagai laner, dalam satu game, laku<PERSON> kill sebelum 10 menit di luar jalurmu (siapa pun selain musuh di jalurmu)", "shortDescription": "Sebagai laner, da<PERSON> 1 game, la<PERSON><PERSON> kill di luar jalurmu sebelum 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302103-IRON.png", "BRONZE": "/challenges-images/302103-BRONZE.png", "SILVER": "/challenges-images/302103-SILVER.png", "GOLD": "/challenges-images/302103-GOLD.png", "PLATINUM": "/challenges-images/302103-PLATINUM.png", "DIAMOND": "/challenges-images/302103-DIAMOND.png", "MASTER": "/challenges-images/302103-MASTER.png", "GRANDMASTER": "/challenges-images/302103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302103-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 3}, "MASTER": {"value": 4}, "GRANDMASTER": {"value": 5}, "CHALLENGER": {"value": 6}}}, {"id": 302104, "name": "Whose Lane is it Anyway", "description": "<PERSON><PERSON><PERSON> laner, lakukan takedown di ketiga jalur dalam waktu 10 menit", "shortDescription": "<PERSON><PERSON><PERSON> laner, lakukan takedown di semua jalur sebelum 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302104-IRON.png", "BRONZE": "/challenges-images/302104-BRONZE.png", "SILVER": "/challenges-images/302104-SILVER.png", "GOLD": "/challenges-images/302104-GOLD.png", "PLATINUM": "/challenges-images/302104-PLATINUM.png", "DIAMOND": "/challenges-images/302104-DIAMOND.png", "MASTER": "/challenges-images/302104-MASTER.png", "GRANDMASTER": "/challenges-images/302104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302104-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 7}, "MASTER": {"value": 12}, "GRANDMASTER": {"value": 18}, "CHALLENGER": {"value": 50}}}, {"id": 302105, "name": "Global Defense System", "description": "Miliki Control Ward aktif di river atau setengah peta musuh selama lebih dari 65% durasi game", "shortDescription": "Memiliki Control Ward di jungle musuh selama 65+% durasi game", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302105-IRON.png", "BRONZE": "/challenges-images/302105-BRONZE.png", "SILVER": "/challenges-images/302105-SILVER.png", "GOLD": "/challenges-images/302105-GOLD.png", "PLATINUM": "/challenges-images/302105-PLATINUM.png", "DIAMOND": "/challenges-images/302105-DIAMOND.png", "MASTER": "/challenges-images/302105-MASTER.png", "GRANDMASTER": "/challenges-images/302105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302105-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 4, "rewards": [{"category": "TITLE", "quantity": 1, "title": "In Control"}]}, "DIAMOND": {"value": 8}, "MASTER": {"value": 12}, "GRANDMASTER": {"value": 15}, "CHALLENGER": {"value": 20}}}, {"id": 302106, "name": "Baron Power Play", "description": "Hasil<PERSON> 2.500 gold lebih unggul selama <PERSON>", "shortDescription": "<PERSON><PERSON><PERSON> 2.500 gold lebih unggul dengan <PERSON>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302106-IRON.png", "BRONZE": "/challenges-images/302106-BRONZE.png", "SILVER": "/challenges-images/302106-SILVER.png", "GOLD": "/challenges-images/302106-GOLD.png", "PLATINUM": "/challenges-images/302106-PLATINUM.png", "DIAMOND": "/challenges-images/302106-DIAMOND.png", "MASTER": "/challenges-images/302106-MASTER.png", "GRANDMASTER": "/challenges-images/302106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302106-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 302200, "name": "Demolition", "description": "Raih progres dari tantangan di grup Demolition", "shortDescription": "Raih progres dari tantangan di grup Demolition", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302200-IRON.png", "BRONZE": "/challenges-images/302200-BRONZE.png", "SILVER": "/challenges-images/302200-SILVER.png", "GOLD": "/challenges-images/302200-GOLD.png", "PLATINUM": "/challenges-images/302200-PLATINUM.png", "DIAMOND": "/challenges-images/302200-DIAMOND.png", "MASTER": "/challenges-images/302200-MASTER.png", "GRANDMASTER": "/challenges-images/302200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Demolisher"}]}, "MASTER": {"value": 250}}}, {"id": 302201, "name": "Go Shelly Go!", "description": "Berpartisipasi dalam menghancurkan 2 turret dengan Rift Herald yang sama", "shortDescription": "Ambil 2 turret dengan Rift Herald yang sama", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302201-IRON.png", "BRONZE": "/challenges-images/302201-BRONZE.png", "SILVER": "/challenges-images/302201-SILVER.png", "GOLD": "/challenges-images/302201-GOLD.png", "PLATINUM": "/challenges-images/302201-PLATINUM.png", "DIAMOND": "/challenges-images/302201-DIAMOND.png", "MASTER": "/challenges-images/302201-MASTER.png", "GRANDMASTER": "/challenges-images/302201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 20}, "GOLD": {"value": 35}, "PLATINUM": {"value": 105}, "DIAMOND": {"value": 225}, "MASTER": {"value": 400}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 600}}}, {"id": 302202, "name": "Shattered Plates", "description": "Hancurkan turret sebelum turret plate roboh (14 mnt)", "shortDescription": "Hancurkan turret sebelum plate roboh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302202-IRON.png", "BRONZE": "/challenges-images/302202-BRONZE.png", "SILVER": "/challenges-images/302202-SILVER.png", "GOLD": "/challenges-images/302202-GOLD.png", "PLATINUM": "/challenges-images/302202-PLATINUM.png", "DIAMOND": "/challenges-images/302202-DIAMOND.png", "MASTER": "/challenges-images/302202-MASTER.png", "GRANDMASTER": "/challenges-images/302202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 20}, "PLATINUM": {"value": 45, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Lumber<PERSON>"}]}, "DIAMOND": {"value": 90}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 225}, "CHALLENGER": {"value": 450}}}, {"id": 302203, "name": "Ten Minute Turret", "description": "Hancurkan turret pertama dalam waktu kurang dari 10 menit", "shortDescription": "Hancurkan turret pertama dalam waktu kurang dari 10 menit", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302203-IRON.png", "BRONZE": "/challenges-images/302203-BRONZE.png", "SILVER": "/challenges-images/302203-SILVER.png", "GOLD": "/challenges-images/302203-GOLD.png", "PLATINUM": "/challenges-images/302203-PLATINUM.png", "DIAMOND": "/challenges-images/302203-DIAMOND.png", "MASTER": "/challenges-images/302203-MASTER.png", "GRANDMASTER": "/challenges-images/302203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 70}, "MASTER": {"value": 120}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 200}}}, {"id": 302300, "name": "Synergy", "description": "Raih progres dari tantangan di grup Synergy", "shortDescription": "Raih progres dari tantangan di grup Synergy", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302300-IRON.png", "BRONZE": "/challenges-images/302300-BRONZE.png", "SILVER": "/challenges-images/302300-SILVER.png", "GOLD": "/challenges-images/302300-GOLD.png", "PLATINUM": "/challenges-images/302300-PLATINUM.png", "DIAMOND": "/challenges-images/302300-DIAMOND.png", "MASTER": "/challenges-images/302300-MASTER.png", "GRANDMASTER": "/challenges-images/302300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Synergized"}]}, "MASTER": {"value": 400}}}, {"id": 302301, "name": "Brush Fanatic", "description": "Takedown champion <PERSON><PERSON><PERSON> di dalam semak dengan setidaknya satu sekutu selama setidaknya 3 detik", "shortDescription": "Lakukan takedown bersa<PERSON> sekutu setelah bersembunyi di semak selama 3+ dtk", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302301-IRON.png", "BRONZE": "/challenges-images/302301-BRONZE.png", "SILVER": "/challenges-images/302301-SILVER.png", "GOLD": "/challenges-images/302301-GOLD.png", "PLATINUM": "/challenges-images/302301-PLATINUM.png", "DIAMOND": "/challenges-images/302301-DIAMOND.png", "MASTER": "/challenges-images/302301-MASTER.png", "GRANDMASTER": "/challenges-images/302301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 8}, "BRONZE": {"value": 32}, "SILVER": {"value": 64}, "GOLD": {"value": 160, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Fanatic"}]}, "PLATINUM": {"value": 400}, "DIAMOND": {"value": 1080}, "MASTER": {"value": 1920}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 302302, "name": "Team Takedown", "description": "Lakukan pick dengan setidaknya satu sekutu membantu. Pick adalah membunuh satu musuh tanpa balasan kill", "shortDescription": "Dapatkan pick dengan setida<PERSON>nya satu sekutu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302302-IRON.png", "BRONZE": "/challenges-images/302302-BRONZE.png", "SILVER": "/challenges-images/302302-SILVER.png", "GOLD": "/challenges-images/302302-GOLD.png", "PLATINUM": "/challenges-images/302302-PLATINUM.png", "DIAMOND": "/challenges-images/302302-DIAMOND.png", "MASTER": "/challenges-images/302302-MASTER.png", "GRANDMASTER": "/challenges-images/302302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 55}, "BRONZE": {"value": 220}, "SILVER": {"value": 660}, "GOLD": {"value": 1320}, "PLATINUM": {"value": 4125}, "DIAMOND": {"value": 7500}, "MASTER": {"value": 13000}, "GRANDMASTER": {"value": 18000}, "CHALLENGER": {"value": 24000}}}, {"id": 302303, "name": "That Was Close", "description": "Selamatkan sekutu yang hampir menerima damage mematikan dengan heal atau shield", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> sekutu dengan heal atau shield", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302303-IRON.png", "BRONZE": "/challenges-images/302303-BRONZE.png", "SILVER": "/challenges-images/302303-SILVER.png", "GOLD": "/challenges-images/302303-GOLD.png", "PLATINUM": "/challenges-images/302303-PLATINUM.png", "DIAMOND": "/challenges-images/302303-DIAMOND.png", "MASTER": "/challenges-images/302303-MASTER.png", "GRANDMASTER": "/challenges-images/302303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 50, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Guardian Angel"}]}, "GOLD": {"value": 150}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 675}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2000}}}, {"id": 302304, "name": "Stun n Gun", "description": "Immobilize seorang musuh, lalu takedown dia bersama sekutu", "shortDescription": "Immobilize lalu takedown musuh bersama sekutu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302304-IRON.png", "BRONZE": "/challenges-images/302304-BRONZE.png", "SILVER": "/challenges-images/302304-SILVER.png", "GOLD": "/challenges-images/302304-GOLD.png", "PLATINUM": "/challenges-images/302304-PLATINUM.png", "DIAMOND": "/challenges-images/302304-DIAMOND.png", "MASTER": "/challenges-images/302304-MASTER.png", "GRANDMASTER": "/challenges-images/302304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302304-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 30}, "BRONZE": {"value": 120}, "SILVER": {"value": 300}, "GOLD": {"value": 750}, "PLATINUM": {"value": 2250}, "DIAMOND": {"value": 4000}, "MASTER": {"value": 7200}, "GRANDMASTER": {"value": 10000}, "CHALLENGER": {"value": 12000}}}, {"id": 302305, "name": "Insec-urity Breach", "description": "Knock back musuh ke timmu, <PERSON><PERSON><PERSON><PERSON><PERSON> takedown", "shortDescription": "Knock back musuh ke timmu dan lakukan takedown", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302305-IRON.png", "BRONZE": "/challenges-images/302305-BRONZE.png", "SILVER": "/challenges-images/302305-SILVER.png", "GOLD": "/challenges-images/302305-GOLD.png", "PLATINUM": "/challenges-images/302305-PLATINUM.png", "DIAMOND": "/challenges-images/302305-DIAMOND.png", "MASTER": "/challenges-images/302305-MASTER.png", "GRANDMASTER": "/challenges-images/302305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 60}, "GOLD": {"value": 150}, "PLATINUM": {"value": 500, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Insec-ticide"}]}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2000}, "CHALLENGER": {"value": 2500}}}, {"id": 302400, "name": "Team Spirit", "description": "Raih progres dari tantangan di grup Team Spirit", "shortDescription": "Raih progres dari tantangan di grup Team Spirit", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302400-IRON.png", "BRONZE": "/challenges-images/302400-BRONZE.png", "SILVER": "/challenges-images/302400-SILVER.png", "GOLD": "/challenges-images/302400-GOLD.png", "PLATINUM": "/challenges-images/302400-PLATINUM.png", "DIAMOND": "/challenges-images/302400-DIAMOND.png", "MASTER": "/challenges-images/302400-MASTER.png", "GRANDMASTER": "/challenges-images/302400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Spirited"}]}, "MASTER": {"value": 250}}}, {"id": 302401, "name": "Flawless Ace", "description": "Ace tim musuh tanpa ada sekutu yang mati", "shortDescription": "<PERSON><PERSON>i semua anggota tim musuh tanpa ada sekutu yang mati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302401-IRON.png", "BRONZE": "/challenges-images/302401-BRONZE.png", "SILVER": "/challenges-images/302401-SILVER.png", "GOLD": "/challenges-images/302401-GOLD.png", "PLATINUM": "/challenges-images/302401-PLATINUM.png", "DIAMOND": "/challenges-images/302401-DIAMOND.png", "MASTER": "/challenges-images/302401-MASTER.png", "GRANDMASTER": "/challenges-images/302401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302401-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 35}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 170}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 302402, "name": "Making the Dream Work", "description": "Lakukan takedown dengan selu<PERSON>h tim be<PERSON> (melakukan kill atau assist)", "shortDescription": "Lakukan takedown dengan seluruh tim", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302402-IRON.png", "BRONZE": "/challenges-images/302402-BRONZE.png", "SILVER": "/challenges-images/302402-SILVER.png", "GOLD": "/challenges-images/302402-GOLD.png", "PLATINUM": "/challenges-images/302402-PLATINUM.png", "DIAMOND": "/challenges-images/302402-DIAMOND.png", "MASTER": "/challenges-images/302402-MASTER.png", "GRANDMASTER": "/challenges-images/302402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302402-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 12}, "SILVER": {"value": 30}, "GOLD": {"value": 75}, "PLATINUM": {"value": 200}, "DIAMOND": {"value": 400}, "MASTER": {"value": 650}, "GRANDMASTER": {"value": 900}, "CHALLENGER": {"value": 1200}}}, {"id": 302404, "name": "Fashionably Late", "description": "Lakukan takedown set<PERSON><PERSON> berte<PERSON><PERSON><PERSON> ke dalam combat yang melibatkan 4 champion at<PERSON> lebih", "shortDescription": "Lakukan takedown set<PERSON>h teleport ke per<PERSON>ungan >4 champion", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302404-IRON.png", "BRONZE": "/challenges-images/302404-BRONZE.png", "SILVER": "/challenges-images/302404-SILVER.png", "GOLD": "/challenges-images/302404-GOLD.png", "PLATINUM": "/challenges-images/302404-PLATINUM.png", "DIAMOND": "/challenges-images/302404-DIAMOND.png", "MASTER": "/challenges-images/302404-MASTER.png", "GRANDMASTER": "/challenges-images/302404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302404-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 50, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Party Crasher"}]}, "GOLD": {"value": 100}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 675}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2500}}}, {"id": 303000, "name": "Alliance", "description": "<PERSON>h progres dari tantangan di grup Clash, S<PERSON>hr<PERSON>ity, Harmony, da<PERSON>", "shortDescription": "<PERSON>h progres dari tantangan di grup Clash, S<PERSON>hr<PERSON>ity, Harmony, da<PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303000-IRON.png", "BRONZE": "/challenges-images/303000-BRONZE.png", "SILVER": "/challenges-images/303000-SILVER.png", "GOLD": "/challenges-images/303000-GOLD.png", "PLATINUM": "/challenges-images/303000-PLATINUM.png", "DIAMOND": "/challenges-images/303000-DIAMOND.png", "MASTER": "/challenges-images/303000-MASTER.png", "GRANDMASTER": "/challenges-images/303000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 30}, "BRONZE": {"value": 60}, "SILVER": {"value": 90}, "GOLD": {"value": 620}, "PLATINUM": {"value": 1030}, "DIAMOND": {"value": 1775}, "MASTER": {"value": 2950, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Team Player"}]}}}, {"id": 303200, "name": "Clash", "description": "Raih progres dari tantangan di grup Clash", "shortDescription": "Raih progres dari tantangan di grup Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303200-IRON.png", "BRONZE": "/challenges-images/303200-BRONZE.png", "SILVER": "/challenges-images/303200-SILVER.png", "GOLD": "/challenges-images/303200-GOLD.png", "PLATINUM": "/challenges-images/303200-PLATINUM.png", "DIAMOND": "/challenges-images/303200-DIAMOND.png", "MASTER": "/challenges-images/303200-MASTER.png", "GRANDMASTER": "/challenges-images/303200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON> Master"}]}, "MASTER": {"value": 400}}}, {"id": 303201, "name": "<PERSON><PERSON> Contenders", "description": "Menangkan game di Clash", "shortDescription": "Menangkan game di Clash", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303201-IRON.png", "BRONZE": "/challenges-images/303201-BRONZE.png", "SILVER": "/challenges-images/303201-SILVER.png", "GOLD": "/challenges-images/303201-GOLD.png", "PLATINUM": "/challenges-images/303201-PLATINUM.png", "DIAMOND": "/challenges-images/303201-DIAMOND.png", "MASTER": "/challenges-images/303201-MASTER.png", "GRANDMASTER": "/challenges-images/303201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 10}, "GOLD": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 75}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 225}, "CHALLENGER": {"value": 300}}}, {"id": 303202, "name": "Clash Champion", "description": "Menangkan bracket Clash", "shortDescription": "Menangkan bracket Clash", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303202-IRON.png", "BRONZE": "/challenges-images/303202-BRONZE.png", "SILVER": "/challenges-images/303202-SILVER.png", "GOLD": "/challenges-images/303202-GOLD.png", "PLATINUM": "/challenges-images/303202-PLATINUM.png", "DIAMOND": "/challenges-images/303202-DIAMOND.png", "MASTER": "/challenges-images/303202-MASTER.png", "GRANDMASTER": "/challenges-images/303202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 25}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 55}, "CHALLENGER": {"value": 70}}}, {"id": 303203, "name": "Coordinated Clash", "description": "<PERSON><PERSON>t seluruh anggota tim memiliki logo Clash", "shortDescription": "<PERSON><PERSON>t seluruh anggota tim memiliki logo Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303203-IRON.png", "BRONZE": "/challenges-images/303203-BRONZE.png", "SILVER": "/challenges-images/303203-SILVER.png", "GOLD": "/challenges-images/303203-GOLD.png", "PLATINUM": "/challenges-images/303203-PLATINUM.png", "DIAMOND": "/challenges-images/303203-DIAMOND.png", "MASTER": "/challenges-images/303203-MASTER.png", "GRANDMASTER": "/challenges-images/303203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 6}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 21}, "MASTER": {"value": 30}}}, {"id": 303204, "name": "Dream Team", "description": "<PERSON><PERSON><PERSON> dengan tim yang sama di berbagai turnamen Clash", "shortDescription": "<PERSON><PERSON><PERSON> dengan tim yang sama di turnamen Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303204-IRON.png", "BRONZE": "/challenges-images/303204-BRONZE.png", "SILVER": "/challenges-images/303204-SILVER.png", "GOLD": "/challenges-images/303204-GOLD.png", "PLATINUM": "/challenges-images/303204-PLATINUM.png", "DIAMOND": "/challenges-images/303204-DIAMOND.png", "MASTER": "/challenges-images/303204-MASTER.png", "GRANDMASTER": "/challenges-images/303204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 9}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 21}, "MASTER": {"value": 30}}}, {"id": 303205, "name": "Scoreboard", "description": "Menyelesaikan dengan lebih banyak kemenangan daripada kekalahan di Clash", "shortDescription": "Dapatkan lebih banyak kemenangan daripada kekalahan di Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303205-IRON.png", "BRONZE": "/challenges-images/303205-BRONZE.png", "SILVER": "/challenges-images/303205-SILVER.png", "GOLD": "/challenges-images/303205-GOLD.png", "PLATINUM": "/challenges-images/303205-PLATINUM.png", "DIAMOND": "/challenges-images/303205-DIAMOND.png", "MASTER": "/challenges-images/303205-MASTER.png", "GRANDMASTER": "/challenges-images/303205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 45}, "MASTER": {"value": 60}}}, {"id": 303300, "name": "Synchronicity", "description": "Raih progres dari tantangan di grup Synchronicity", "shortDescription": "Raih progres dari tantangan di grup Synchronicity", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303300-IRON.png", "BRONZE": "/challenges-images/303300-BRONZE.png", "SILVER": "/challenges-images/303300-SILVER.png", "GOLD": "/challenges-images/303300-GOLD.png", "PLATINUM": "/challenges-images/303300-PLATINUM.png", "DIAMOND": "/challenges-images/303300-DIAMOND.png", "MASTER": "/challenges-images/303300-MASTER.png", "GRANDMASTER": "/challenges-images/303300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "In Sync"}]}, "MASTER": {"value": 250}}}, {"id": 303301, "name": "Power Pair", "description": "Raih kemenangan dengan premade party berisi dua pemain di mode ranked", "shortDescription": "<PERSON><PERSON> dengan premade party berisi dua pemain di mode ranked", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303301-IRON.png", "BRONZE": "/challenges-images/303301-BRONZE.png", "SILVER": "/challenges-images/303301-SILVER.png", "GOLD": "/challenges-images/303301-GOLD.png", "PLATINUM": "/challenges-images/303301-PLATINUM.png", "DIAMOND": "/challenges-images/303301-DIAMOND.png", "MASTER": "/challenges-images/303301-MASTER.png", "GRANDMASTER": "/challenges-images/303301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 60}, "GOLD": {"value": 100}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 750}, "CHALLENGER": {"value": 1500}}}, {"id": 303302, "name": "Best Friends Forever", "description": "Raih kemenangan dengan grup berisi 5 pemain yang sama", "shortDescription": "Dapatkan kemenangan dengan premade berisi 5", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303302-IRON.png", "BRONZE": "/challenges-images/303302-BRONZE.png", "SILVER": "/challenges-images/303302-SILVER.png", "GOLD": "/challenges-images/303302-GOLD.png", "PLATINUM": "/challenges-images/303302-PLATINUM.png", "DIAMOND": "/challenges-images/303302-DIAMOND.png", "MASTER": "/challenges-images/303302-MASTER.png", "GRANDMASTER": "/challenges-images/303302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 25}, "PLATINUM": {"value": 65}, "DIAMOND": {"value": 125}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 303303, "name": "Fun with Friends", "description": "Mainkan game dengan grup premade berju<PERSON>lah berapa pun", "shortDescription": "Mainkan game dengan grup", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303303-IRON.png", "BRONZE": "/challenges-images/303303-BRONZE.png", "SILVER": "/challenges-images/303303-SILVER.png", "GOLD": "/challenges-images/303303-GOLD.png", "PLATINUM": "/challenges-images/303303-PLATINUM.png", "DIAMOND": "/challenges-images/303303-DIAMOND.png", "MASTER": "/challenges-images/303303-MASTER.png", "GRANDMASTER": "/challenges-images/303303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 75}, "GOLD": {"value": 135}, "PLATINUM": {"value": 250}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1200}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2000}}}, {"id": 303400, "name": "Harmony", "description": "Raih progres dari tantangan di grup Harmony", "shortDescription": "Raih progres dari tantangan di grup Harmony", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303400-IRON.png", "BRONZE": "/challenges-images/303400-BRONZE.png", "SILVER": "/challenges-images/303400-SILVER.png", "GOLD": "/challenges-images/303400-GOLD.png", "PLATINUM": "/challenges-images/303400-PLATINUM.png", "DIAMOND": "/challenges-images/303400-DIAMOND.png", "MASTER": "/challenges-images/303400-MASTER.png", "GRANDMASTER": "/challenges-images/303400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 100}, "SILVER": {"value": 150}, "GOLD": {"value": 200}, "PLATINUM": {"value": 320}, "DIAMOND": {"value": 580, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Harmonious"}]}, "MASTER": {"value": 950}}}, {"id": 303401, "name": "Nowhere to Hide", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan ability global", "shortDescription": "Menang dengan grup berlima berisi <em>champion dengan 3+ ability global</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303401-IRON.png", "BRONZE": "/challenges-images/303401-BRONZE.png", "SILVER": "/challenges-images/303401-SILVER.png", "GOLD": "/challenges-images/303401-GOLD.png", "PLATINUM": "/challenges-images/303401-PLATINUM.png", "DIAMOND": "/challenges-images/303401-DIAMOND.png", "MASTER": "/challenges-images/303401-MASTER.png", "GRANDMASTER": "/challenges-images/303401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303401-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303402, "name": "It Has \"Ultimate\" In the Name!", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan ultima dengan AoE yang besar", "shortDescription": "Menang dengan grup berlima berisi 3+ <em>champion dengan ultima AoE yang besar</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303402-IRON.png", "BRONZE": "/challenges-images/303402-BRONZE.png", "SILVER": "/challenges-images/303402-SILVER.png", "GOLD": "/challenges-images/303402-GOLD.png", "PLATINUM": "/challenges-images/303402-PLATINUM.png", "DIAMOND": "/challenges-images/303402-DIAMOND.png", "MASTER": "/challenges-images/303402-MASTER.png", "GRANDMASTER": "/challenges-images/303402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303402-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303403, "name": "We Protec", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan heal atau shield. <i>Heal dan shield diri sendiri tidak dihitung</i>", "shortDescription": "<PERSON>ang dengan grup berlima berisi 3+ <em>champion dengan heal atau shield.</em> ", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303403-IRON.png", "BRONZE": "/challenges-images/303403-BRONZE.png", "SILVER": "/challenges-images/303403-SILVER.png", "GOLD": "/challenges-images/303403-GOLD.png", "PLATINUM": "/challenges-images/303403-PLATINUM.png", "DIAMOND": "/challenges-images/303403-DIAMOND.png", "MASTER": "/challenges-images/303403-MASTER.png", "GRANDMASTER": "/challenges-images/303403-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303403-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303404, "name": "They Just... Don't... DIE!", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan ability revive, imunitas, atau zombie", "shortDescription": "<PERSON>ang dengan grup berlima berisi champion 3+ yang <em>melawan kematian</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303404-IRON.png", "BRONZE": "/challenges-images/303404-BRONZE.png", "SILVER": "/challenges-images/303404-SILVER.png", "GOLD": "/challenges-images/303404-GOLD.png", "PLATINUM": "/challenges-images/303404-PLATINUM.png", "DIAMOND": "/challenges-images/303404-DIAMOND.png", "MASTER": "/challenges-images/303404-MASTER.png", "GRANDMASTER": "/challenges-images/303404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303404-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303405, "name": "Where'd They Go?", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan stealth (termasuk camouflage atau invisibility)", "shortDescription": "<PERSON>ang dengan grup berlima berisi 3+ <em>champion dengan stealth</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303405-IRON.png", "BRONZE": "/challenges-images/303405-BRONZE.png", "SILVER": "/challenges-images/303405-SILVER.png", "GOLD": "/challenges-images/303405-GOLD.png", "PLATINUM": "/challenges-images/303405-PLATINUM.png", "DIAMOND": "/challenges-images/303405-DIAMOND.png", "MASTER": "/challenges-images/303405-MASTER.png", "GRANDMASTER": "/challenges-images/303405-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303405-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303406, "name": "We're Good Over Here", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion \"poke\" (champion dengan ability long-range dan non-target)", "shortDescription": "Menang dengan grup berlima berisi 3+ <em>champion poke</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303406-IRON.png", "BRONZE": "/challenges-images/303406-BRONZE.png", "SILVER": "/challenges-images/303406-SILVER.png", "GOLD": "/challenges-images/303406-GOLD.png", "PLATINUM": "/challenges-images/303406-PLATINUM.png", "DIAMOND": "/challenges-images/303406-DIAMOND.png", "MASTER": "/challenges-images/303406-MASTER.png", "GRANDMASTER": "/challenges-images/303406-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303406-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303407, "name": "Summoners on the Rift", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dengan satu summon atau satu pet", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion dengan summon atau pet</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303407-IRON.png", "BRONZE": "/challenges-images/303407-BRONZE.png", "SILVER": "/challenges-images/303407-SILVER.png", "GOLD": "/challenges-images/303407-GOLD.png", "PLATINUM": "/challenges-images/303407-PLATINUM.png", "DIAMOND": "/challenges-images/303407-DIAMOND.png", "MASTER": "/challenges-images/303407-MASTER.png", "GRANDMASTER": "/challenges-images/303407-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303407-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303408, "name": "Variety's Overrated", "description": "Sebagai premade 5 orang, menangkan game sebagai 5 champion dari class yang sama (assassin, mage, marksman, tank, support, atau fighter)", "shortDescription": "Menang dengan grup berlima dari satu kelas yang sama", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303408-IRON.png", "BRONZE": "/challenges-images/303408-BRONZE.png", "SILVER": "/challenges-images/303408-SILVER.png", "GOLD": "/challenges-images/303408-GOLD.png", "PLATINUM": "/challenges-images/303408-PLATINUM.png", "DIAMOND": "/challenges-images/303408-DIAMOND.png", "MASTER": "/challenges-images/303408-MASTER.png", "GRANDMASTER": "/challenges-images/303408-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303408-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303409, "name": "Get Over Here", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan efek displacement", "shortDescription": "Menang dengan grup berlima berisi 3+ <em>champion dengan displacement</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303409-IRON.png", "BRONZE": "/challenges-images/303409-BRONZE.png", "SILVER": "/challenges-images/303409-SILVER.png", "GOLD": "/challenges-images/303409-GOLD.png", "PLATINUM": "/challenges-images/303409-PLATINUM.png", "DIAMOND": "/challenges-images/303409-DIAMOND.png", "MASTER": "/challenges-images/303409-MASTER.png", "GRANDMASTER": "/challenges-images/303409-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303409-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303410, "name": "It's a Trap!", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan ability jebakan", "shortDescription": "<PERSON>ang dengan grup berlima berisi 3+ <em>champion den<PERSON> jebakan</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303410-IRON.png", "BRONZE": "/challenges-images/303410-BRONZE.png", "SILVER": "/challenges-images/303410-SILVER.png", "GOLD": "/challenges-images/303410-GOLD.png", "PLATINUM": "/challenges-images/303410-PLATINUM.png", "DIAMOND": "/challenges-images/303410-DIAMOND.png", "MASTER": "/challenges-images/303410-MASTER.png", "GRANDMASTER": "/challenges-images/303410-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303410-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303411, "name": "I'm Helping", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan ability menciptakan medan", "shortDescription": "Menang dengan grup berlima berisi 3+ <em>champion dengan pembuatan medan</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303411-IRON.png", "BRONZE": "/challenges-images/303411-BRONZE.png", "SILVER": "/challenges-images/303411-SILVER.png", "GOLD": "/challenges-images/303411-GOLD.png", "PLATINUM": "/challenges-images/303411-PLATINUM.png", "DIAMOND": "/challenges-images/303411-DIAMOND.png", "MASTER": "/challenges-images/303411-MASTER.png", "GRANDMASTER": "/challenges-images/303411-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303411-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303412, "name": "Hold That Pose", "description": "Sebagai premade 5 orang, menangkan game dengan 3 atau lebih champion dengan 2 atau lebih spell immobilize", "shortDescription": "Menang dengan grup berlima berisi 3+ <em>champion dengan 2+ spell immobilize</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303412-IRON.png", "BRONZE": "/challenges-images/303412-BRONZE.png", "SILVER": "/challenges-images/303412-SILVER.png", "GOLD": "/challenges-images/303412-GOLD.png", "PLATINUM": "/challenges-images/303412-PLATINUM.png", "DIAMOND": "/challenges-images/303412-DIAMOND.png", "MASTER": "/challenges-images/303412-MASTER.png", "GRANDMASTER": "/challenges-images/303412-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303412-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303500, "name": "Globetrotter", "description": "Raih progres dari tantangan di grup Globetrotter", "shortDescription": "Raih progres dari tantangan di grup Globetrotter", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303500-IRON.png", "BRONZE": "/challenges-images/303500-BRONZE.png", "SILVER": "/challenges-images/303500-SILVER.png", "GOLD": "/challenges-images/303500-GOLD.png", "PLATINUM": "/challenges-images/303500-PLATINUM.png", "DIAMOND": "/challenges-images/303500-DIAMOND.png", "MASTER": "/challenges-images/303500-MASTER.png", "GRANDMASTER": "/challenges-images/303500-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303500-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 100}, "SILVER": {"value": 150}, "GOLD": {"value": 215}, "PLATINUM": {"value": 385}, "DIAMOND": {"value": 620, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Runeterran"}]}, "MASTER": {"value": 1050}}}, {"id": 303501, "name": "5 Under 5'", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dari <PERSON>le City", "shortDescription": "Menang dengan grup berlima berisi <em>champion dari <PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303501-IRON.png", "BRONZE": "/challenges-images/303501-BRONZE.png", "SILVER": "/challenges-images/303501-SILVER.png", "GOLD": "/challenges-images/303501-GOLD.png", "PLATINUM": "/challenges-images/303501-PLATINUM.png", "DIAMOND": "/challenges-images/303501-DIAMOND.png", "MASTER": "/challenges-images/303501-MASTER.png", "GRANDMASTER": "/challenges-images/303501-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303501-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303502, "name": "All Hands on Deck", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dari Bil<PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303502-IRON.png", "BRONZE": "/challenges-images/303502-BRONZE.png", "SILVER": "/challenges-images/303502-SILVER.png", "GOLD": "/challenges-images/303502-GOLD.png", "PLATINUM": "/challenges-images/303502-PLATINUM.png", "DIAMOND": "/challenges-images/303502-DIAMOND.png", "MASTER": "/challenges-images/303502-MASTER.png", "GRANDMASTER": "/challenges-images/303502-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303502-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303503, "name": "FOR DEMACIA", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion <PERSON><PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303503-IRON.png", "BRONZE": "/challenges-images/303503-BRONZE.png", "SILVER": "/challenges-images/303503-SILVER.png", "GOLD": "/challenges-images/303503-GOLD.png", "PLATINUM": "/challenges-images/303503-PLATINUM.png", "DIAMOND": "/challenges-images/303503-DIAMOND.png", "MASTER": "/challenges-images/303503-MASTER.png", "GRANDMASTER": "/challenges-images/303503-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303503-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303504, "name": "Ice, Ice, Baby", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion <PERSON><PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303504-IRON.png", "BRONZE": "/challenges-images/303504-BRONZE.png", "SILVER": "/challenges-images/303504-SILVER.png", "GOLD": "/challenges-images/303504-GOLD.png", "PLATINUM": "/challenges-images/303504-PLATINUM.png", "DIAMOND": "/challenges-images/303504-DIAMOND.png", "MASTER": "/challenges-images/303504-MASTER.png", "GRANDMASTER": "/challenges-images/303504-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303504-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303505, "name": "Everybody was <PERSON><PERSON>", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion da<PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion da<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303505-IRON.png", "BRONZE": "/challenges-images/303505-BRONZE.png", "SILVER": "/challenges-images/303505-SILVER.png", "GOLD": "/challenges-images/303505-GOLD.png", "PLATINUM": "/challenges-images/303505-PLATINUM.png", "DIAMOND": "/challenges-images/303505-DIAMOND.png", "MASTER": "/challenges-images/303505-MASTER.png", "GRANDMASTER": "/challenges-images/303505-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303505-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303506, "name": "<PERSON>ement<PERSON>, <PERSON> <PERSON>", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion da<PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion da<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303506-IRON.png", "BRONZE": "/challenges-images/303506-BRONZE.png", "SILVER": "/challenges-images/303506-SILVER.png", "GOLD": "/challenges-images/303506-GOLD.png", "PLATINUM": "/challenges-images/303506-PLATINUM.png", "DIAMOND": "/challenges-images/303506-DIAMOND.png", "MASTER": "/challenges-images/303506-MASTER.png", "GRANDMASTER": "/challenges-images/303506-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303506-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303507, "name": "Strength Above All", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dari <PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion da<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303507-IRON.png", "BRONZE": "/challenges-images/303507-BRONZE.png", "SILVER": "/challenges-images/303507-SILVER.png", "GOLD": "/challenges-images/303507-GOLD.png", "PLATINUM": "/challenges-images/303507-PLATINUM.png", "DIAMOND": "/challenges-images/303507-DIAMOND.png", "MASTER": "/challenges-images/303507-MASTER.png", "GRANDMASTER": "/challenges-images/303507-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303507-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303508, "name": "Calculated", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion da<PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303508-IRON.png", "BRONZE": "/challenges-images/303508-BRONZE.png", "SILVER": "/challenges-images/303508-SILVER.png", "GOLD": "/challenges-images/303508-GOLD.png", "PLATINUM": "/challenges-images/303508-PLATINUM.png", "DIAMOND": "/challenges-images/303508-DIAMOND.png", "MASTER": "/challenges-images/303508-MASTER.png", "GRANDMASTER": "/challenges-images/303508-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303508-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303509, "name": "Spooky Scary Skeletons", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dari <PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion da<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303509-IRON.png", "BRONZE": "/challenges-images/303509-BRONZE.png", "SILVER": "/challenges-images/303509-SILVER.png", "GOLD": "/challenges-images/303509-GOLD.png", "PLATINUM": "/challenges-images/303509-PLATINUM.png", "DIAMOND": "/challenges-images/303509-DIAMOND.png", "MASTER": "/challenges-images/303509-MASTER.png", "GRANDMASTER": "/challenges-images/303509-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303509-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303510, "name": "The Sun Disc Never Sets", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion da<PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303510-IRON.png", "BRONZE": "/challenges-images/303510-BRONZE.png", "SILVER": "/challenges-images/303510-SILVER.png", "GOLD": "/challenges-images/303510-GOLD.png", "PLATINUM": "/challenges-images/303510-PLATINUM.png", "DIAMOND": "/challenges-images/303510-DIAMOND.png", "MASTER": "/challenges-images/303510-MASTER.png", "GRANDMASTER": "/challenges-images/303510-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303510-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303511, "name": "Peak Performance", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dari <PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303511-IRON.png", "BRONZE": "/challenges-images/303511-BRONZE.png", "SILVER": "/challenges-images/303511-SILVER.png", "GOLD": "/challenges-images/303511-GOLD.png", "PLATINUM": "/challenges-images/303511-PLATINUM.png", "DIAMOND": "/challenges-images/303511-DIAMOND.png", "MASTER": "/challenges-images/303511-MASTER.png", "GRANDMASTER": "/challenges-images/303511-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303511-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303512, "name": "(Inhuman Screeching Sounds)", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion dari <PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion da<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303512-IRON.png", "BRONZE": "/challenges-images/303512-BRONZE.png", "SILVER": "/challenges-images/303512-SILVER.png", "GOLD": "/challenges-images/303512-GOLD.png", "PLATINUM": "/challenges-images/303512-PLATINUM.png", "DIAMOND": "/challenges-images/303512-DIAMOND.png", "MASTER": "/challenges-images/303512-MASTER.png", "GRANDMASTER": "/challenges-images/303512-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303512-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303513, "name": "Chemtech Comrades", "description": "Sebagai premade 5 orang, menangkan game dengan 5 champion da<PERSON>", "shortDescription": "<PERSON>ang dengan grup berlima berisi <em>champion <PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303513-IRON.png", "BRONZE": "/challenges-images/303513-BRONZE.png", "SILVER": "/challenges-images/303513-SILVER.png", "GOLD": "/challenges-images/303513-GOLD.png", "PLATINUM": "/challenges-images/303513-PLATINUM.png", "DIAMOND": "/challenges-images/303513-DIAMOND.png", "MASTER": "/challenges-images/303513-MASTER.png", "GRANDMASTER": "/challenges-images/303513-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303513-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 4, "name": "TEAMWORK", "description": "", "shortDescription": "Capstone KERJA SAMA TIM", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 200}, "SILVER": {"value": 350}, "GOLD": {"value": 1300}, "PLATINUM": {"value": 2100}, "DIAMOND": {"value": 3800}, "MASTER": {"value": 6500}}}, {"id": 401000, "name": "Sage", "description": "<PERSON><PERSON> progres dari tantangan di grup <PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON>", "shortDescription": "<PERSON><PERSON> progres dari tantangan di grup <PERSON>, <PERSON><PERSON><PERSON><PERSON>, dan <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401000-IRON.png", "BRONZE": "/challenges-images/401000-BRONZE.png", "SILVER": "/challenges-images/401000-SILVER.png", "GOLD": "/challenges-images/401000-GOLD.png", "PLATINUM": "/challenges-images/401000-PLATINUM.png", "DIAMOND": "/challenges-images/401000-DIAMOND.png", "MASTER": "/challenges-images/401000-MASTER.png", "GRANDMASTER": "/challenges-images/401000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 55}, "BRONZE": {"value": 105}, "SILVER": {"value": 160}, "GOLD": {"value": 350}, "PLATINUM": {"value": 560}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1700, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Sage"}]}}}, {"id": 401100, "name": "<PERSON>", "description": "Raih progres dari tantangan di grup <PERSON>", "shortDescription": "Raih progres dari tantangan di grup <PERSON>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401100-IRON.png", "BRONZE": "/challenges-images/401100-BRONZE.png", "SILVER": "/challenges-images/401100-SILVER.png", "GOLD": "/challenges-images/401100-GOLD.png", "PLATINUM": "/challenges-images/401100-PLATINUM.png", "DIAMOND": "/challenges-images/401100-DIAMOND.png", "MASTER": "/challenges-images/401100-MASTER.png", "GRANDMASTER": "/challenges-images/401100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>"}]}, "MASTER": {"value": 475}, "GRANDMASTER": {"value": 570}, "CHALLENGER": {"value": 690}}}, {"id": 401101, "name": "Catch 'em All", "description": "Miliki 150 champion <PERSON><PERSON><PERSON>.", "shortDescription": "<PERSON><PERSON> poin mastery pada 150 champion", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401101-IRON.png", "BRONZE": "/challenges-images/401101-BRONZE.png", "SILVER": "/challenges-images/401101-SILVER.png", "GOLD": "/challenges-images/401101-GOLD.png", "PLATINUM": "/challenges-images/401101-PLATINUM.png", "DIAMOND": "/challenges-images/401101-DIAMOND.png", "MASTER": "/challenges-images/401101-MASTER.png", "GRANDMASTER": "/challenges-images/401101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 500}, "SILVER": {"value": 1000}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 10000, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Champ <PERSON>"}]}, "DIAMOND": {"value": 50000}, "MASTER": {"value": 100000}, "GRANDMASTER": {"value": 107500}, "CHALLENGER": {"value": 115000}}}, {"id": 401102, "name": "Wise Master", "description": "Raih total Poin Mastery", "shortDescription": "<PERSON><PERSON> Poin Mastery", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401102-IRON.png", "BRONZE": "/challenges-images/401102-BRONZE.png", "SILVER": "/challenges-images/401102-SILVER.png", "GOLD": "/challenges-images/401102-GOLD.png", "PLATINUM": "/challenges-images/401102-PLATINUM.png", "DIAMOND": "/challenges-images/401102-DIAMOND.png", "MASTER": "/challenges-images/401102-MASTER.png", "GRANDMASTER": "/challenges-images/401102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1200}, "BRONZE": {"value": 3500}, "SILVER": {"value": 35000}, "GOLD": {"value": 220000}, "PLATINUM": {"value": 900000}, "DIAMOND": {"value": 2500000}, "MASTER": {"value": 5000000}, "GRANDMASTER": {"value": 5000000}, "CHALLENGER": {"value": 5000000}}}, {"id": 401103, "name": "One-Trick", "description": "<PERSON><PERSON>in Mastery pada satu champion", "shortDescription": "Dapatkan Poin Mastery pada satu champion", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401103-IRON.png", "BRONZE": "/challenges-images/401103-BRONZE.png", "SILVER": "/challenges-images/401103-SILVER.png", "GOLD": "/challenges-images/401103-GOLD.png", "PLATINUM": "/challenges-images/401103-PLATINUM.png", "DIAMOND": "/challenges-images/401103-DIAMOND.png", "MASTER": "/challenges-images/401103-MASTER.png", "GRANDMASTER": "/challenges-images/401103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 850}, "BRONZE": {"value": 1500}, "SILVER": {"value": 9000}, "GOLD": {"value": 38000}, "PLATINUM": {"value": 110000}, "DIAMOND": {"value": 280000}, "MASTER": {"value": 840000}, "GRANDMASTER": {"value": 1000000}, "CHALLENGER": {"value": 1500000}}}, {"id": 401104, "name": "Master Yourself", "description": "Dapatkan Mastery 5 pada berbagai champion", "shortDescription": "Dapatkan Mastery 5 pada <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401104-IRON.png", "BRONZE": "/challenges-images/401104-BRONZE.png", "SILVER": "/challenges-images/401104-SILVER.png", "GOLD": "/challenges-images/401104-GOLD.png", "PLATINUM": "/challenges-images/401104-PLATINUM.png", "DIAMOND": "/challenges-images/401104-DIAMOND.png", "MASTER": "/challenges-images/401104-MASTER.png", "GRANDMASTER": "/challenges-images/401104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 50}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}}}, {"id": 401105, "name": "Master the Enemy (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai champion", "shortDescription": "Dapatkan Mastery 7 pada <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401105-IRON.png", "BRONZE": "/challenges-images/401105-BRONZE.png", "SILVER": "/challenges-images/401105-SILVER.png", "GOLD": "/challenges-images/401105-GOLD.png", "PLATINUM": "/challenges-images/401105-PLATINUM.png", "DIAMOND": "/challenges-images/401105-DIAMOND.png", "MASTER": "/challenges-images/401105-MASTER.png", "GRANDMASTER": "/challenges-images/401105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Deep Diver"}]}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}}}, {"id": 401106, "name": "<PERSON> of All Champs", "description": "Menangkan satu game dengan berbagai champion", "shortDescription": "Menangkan game dengan <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401106-IRON.png", "BRONZE": "/challenges-images/401106-BRONZE.png", "SILVER": "/challenges-images/401106-SILVER.png", "GOLD": "/challenges-images/401106-GOLD.png", "PLATINUM": "/challenges-images/401106-PLATINUM.png", "DIAMOND": "/challenges-images/401106-DIAMOND.png", "MASTER": "/challenges-images/401106-MASTER.png", "GRANDMASTER": "/challenges-images/401106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401106-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}}}, {"id": 401107, "name": "Master the Enemy", "description": "Dapatkan Mastery 10 pada berbagai champion", "shortDescription": "Dapatkan Mastery 10 pada <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401105-IRON.png", "BRONZE": "/challenges-images/401105-BRONZE.png", "SILVER": "/challenges-images/401105-SILVER.png", "GOLD": "/challenges-images/401105-GOLD.png", "PLATINUM": "/challenges-images/401105-PLATINUM.png", "DIAMOND": "/challenges-images/401105-DIAMOND.png", "MASTER": "/challenges-images/401105-MASTER.png", "GRANDMASTER": "/challenges-images/401105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Omni-master"}]}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}}}, {"id": 401200, "name": "Virtuoso", "description": "Raih progres dari tantangan di grup Virtuoso", "shortDescription": "Raih progres dari tantangan di grup Virtuoso", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401200-IRON.png", "BRONZE": "/challenges-images/401200-BRONZE.png", "SILVER": "/challenges-images/401200-SILVER.png", "GOLD": "/challenges-images/401200-GOLD.png", "PLATINUM": "/challenges-images/401200-PLATINUM.png", "DIAMOND": "/challenges-images/401200-DIAMOND.png", "MASTER": "/challenges-images/401200-MASTER.png", "GRANDMASTER": "/challenges-images/401200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Peer<PERSON>"}]}, "MASTER": {"value": 475}}}, {"id": 401201, "name": "Master <PERSON> (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai assassin", "shortDescription": "Dapatkan Mastery 7 pada berbagai <em>assassin</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401201-IRON.png", "BRONZE": "/challenges-images/401201-BRONZE.png", "SILVER": "/challenges-images/401201-SILVER.png", "GOLD": "/challenges-images/401201-GOLD.png", "PLATINUM": "/challenges-images/401201-PLATINUM.png", "DIAMOND": "/challenges-images/401201-DIAMOND.png", "MASTER": "/challenges-images/401201-MASTER.png", "GRANDMASTER": "/challenges-images/401201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Deathmaster"}]}, "DIAMOND": {"value": 35}, "MASTER": {"value": 45}}}, {"id": 401202, "name": "Master Fighter (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai fighter", "shortDescription": "Dapatkan Mastery 7 pada berbagai <em>fighter</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401202-IRON.png", "BRONZE": "/challenges-images/401202-BRONZE.png", "SILVER": "/challenges-images/401202-SILVER.png", "GOLD": "/challenges-images/401202-GOLD.png", "PLATINUM": "/challenges-images/401202-PLATINUM.png", "DIAMOND": "/challenges-images/401202-DIAMOND.png", "MASTER": "/challenges-images/401202-MASTER.png", "GRANDMASTER": "/challenges-images/401202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Warlord"}]}, "DIAMOND": {"value": 50}, "MASTER": {"value": 70}}}, {"id": 401203, "name": "Master Mage (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai mage", "shortDescription": "Dapatkan Mastery 7 pada berbagai <em>mage</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401203-IRON.png", "BRONZE": "/challenges-images/401203-BRONZE.png", "SILVER": "/challenges-images/401203-SILVER.png", "GOLD": "/challenges-images/401203-GOLD.png", "PLATINUM": "/challenges-images/401203-PLATINUM.png", "DIAMOND": "/challenges-images/401203-DIAMOND.png", "MASTER": "/challenges-images/401203-MASTER.png", "GRANDMASTER": "/challenges-images/401203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Archmage"}]}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}}}, {"id": 401204, "name": "Master <PERSON> (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai marksman", "shortDescription": "Dapatkan Mastery 7 pada berbagai <em>marksman</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401204-IRON.png", "BRONZE": "/challenges-images/401204-BRONZE.png", "SILVER": "/challenges-images/401204-SILVER.png", "GOLD": "/challenges-images/401204-GOLD.png", "PLATINUM": "/challenges-images/401204-PLATINUM.png", "DIAMOND": "/challenges-images/401204-DIAMOND.png", "MASTER": "/challenges-images/401204-MASTER.png", "GRANDMASTER": "/challenges-images/401204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Deadeye"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401205, "name": "Master Support (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai support", "shortDescription": "Dapatkan Mastery 7 pada berbagai <em>support</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401205-IRON.png", "BRONZE": "/challenges-images/401205-BRONZE.png", "SILVER": "/challenges-images/401205-SILVER.png", "GOLD": "/challenges-images/401205-GOLD.png", "PLATINUM": "/challenges-images/401205-PLATINUM.png", "DIAMOND": "/challenges-images/401205-DIAMOND.png", "MASTER": "/challenges-images/401205-MASTER.png", "GRANDMASTER": "/challenges-images/401205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Warden"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401206, "name": "Master Tank (Legacy)", "description": "Dapatkan Mastery 7 pada berbagai tank", "shortDescription": "Dapatkan Mastery 7 pada berbagai <em>tank</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401206-IRON.png", "BRONZE": "/challenges-images/401206-BRONZE.png", "SILVER": "/challenges-images/401206-SILVER.png", "GOLD": "/challenges-images/401206-GOLD.png", "PLATINUM": "/challenges-images/401206-PLATINUM.png", "DIAMOND": "/challenges-images/401206-DIAMOND.png", "MASTER": "/challenges-images/401206-MASTER.png", "GRANDMASTER": "/challenges-images/401206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Juggernaut"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 40}}}, {"id": 401207, "name": "Master <PERSON><PERSON><PERSON>", "description": "Dapatkan Mastery 10 pada berbagai assassin", "shortDescription": "Dapatkan Mastery 10 pada berbagai <em>assassin</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401201-IRON.png", "BRONZE": "/challenges-images/401201-BRONZE.png", "SILVER": "/challenges-images/401201-SILVER.png", "GOLD": "/challenges-images/401201-GOLD.png", "PLATINUM": "/challenges-images/401201-PLATINUM.png", "DIAMOND": "/challenges-images/401201-DIAMOND.png", "MASTER": "/challenges-images/401201-MASTER.png", "GRANDMASTER": "/challenges-images/401201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Reaper"}]}, "DIAMOND": {"value": 35}, "MASTER": {"value": 45}}}, {"id": 401208, "name": "Master Fighter", "description": "Dapatkan Mastery 10 pada berbagai fighter", "shortDescription": "Dapatkan Mastery 10 pada berbagai <em>fighter</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401202-IRON.png", "BRONZE": "/challenges-images/401202-BRONZE.png", "SILVER": "/challenges-images/401202-SILVER.png", "GOLD": "/challenges-images/401202-GOLD.png", "PLATINUM": "/challenges-images/401202-PLATINUM.png", "DIAMOND": "/challenges-images/401202-DIAMOND.png", "MASTER": "/challenges-images/401202-MASTER.png", "GRANDMASTER": "/challenges-images/401202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Conqueror"}]}, "DIAMOND": {"value": 50}, "MASTER": {"value": 70}}}, {"id": 401209, "name": "Master Mage", "description": "Dapatkan Mastery 10 pada berbagai mage", "shortDescription": "Dapatkan Mastery 10 pada berbagai <em>mage</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401203-IRON.png", "BRONZE": "/challenges-images/401203-BRONZE.png", "SILVER": "/challenges-images/401203-SILVER.png", "GOLD": "/challenges-images/401203-GOLD.png", "PLATINUM": "/challenges-images/401203-PLATINUM.png", "DIAMOND": "/challenges-images/401203-DIAMOND.png", "MASTER": "/challenges-images/401203-MASTER.png", "GRANDMASTER": "/challenges-images/401203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Supreme Sorcerer"}]}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}}}, {"id": 401210, "name": "Master Marksman", "description": "Dapatkan Mastery 10 pada berbagai marksman", "shortDescription": "Dapatkan Mastery 10 pada berbagai <em>marksman</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401204-IRON.png", "BRONZE": "/challenges-images/401204-BRONZE.png", "SILVER": "/challenges-images/401204-SILVER.png", "GOLD": "/challenges-images/401204-GOLD.png", "PLATINUM": "/challenges-images/401204-PLATINUM.png", "DIAMOND": "/challenges-images/401204-DIAMOND.png", "MASTER": "/challenges-images/401204-MASTER.png", "GRANDMASTER": "/challenges-images/401204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Sharpshooter"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401211, "name": "Master Support", "description": "Dapatkan Mastery 10 pada berbagai support", "shortDescription": "Dapatkan Mastery 10 pada berbagai <em>support</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401205-IRON.png", "BRONZE": "/challenges-images/401205-BRONZE.png", "SILVER": "/challenges-images/401205-SILVER.png", "GOLD": "/challenges-images/401205-GOLD.png", "PLATINUM": "/challenges-images/401205-PLATINUM.png", "DIAMOND": "/challenges-images/401205-DIAMOND.png", "MASTER": "/challenges-images/401205-MASTER.png", "GRANDMASTER": "/challenges-images/401205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ultimate Guardian"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401212, "name": "Master Tank", "description": "Dapatkan Mastery 10 pada berbagai tank", "shortDescription": "Dapatkan Mastery 10 pada berbagai <em>tank</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401206-IRON.png", "BRONZE": "/challenges-images/401206-BRONZE.png", "SILVER": "/challenges-images/401206-SILVER.png", "GOLD": "/challenges-images/401206-GOLD.png", "PLATINUM": "/challenges-images/401206-PLATINUM.png", "DIAMOND": "/challenges-images/401206-DIAMOND.png", "MASTER": "/challenges-images/401206-MASTER.png", "GRANDMASTER": "/challenges-images/401206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Coloss<PERSON>"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 40}}}, {"id": 401300, "name": "Ace", "description": "Raih progres dari tantangan di grup Ace", "shortDescription": "Raih progres dari tantangan di grup Ace", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401300-IRON.png", "BRONZE": "/challenges-images/401300-BRONZE.png", "SILVER": "/challenges-images/401300-SILVER.png", "GOLD": "/challenges-images/401300-GOLD.png", "PLATINUM": "/challenges-images/401300-PLATINUM.png", "DIAMOND": "/challenges-images/401300-DIAMOND.png", "MASTER": "/challenges-images/401300-MASTER.png", "GRANDMASTER": "/challenges-images/401300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Just Better"}]}, "MASTER": {"value": 475}, "GRANDMASTER": {"value": 570}, "CHALLENGER": {"value": 690}}}, {"id": 401301, "name": "Jungle Diff", "description": "Menangkan game sebagai Jungler", "shortDescription": "Menangkan game sebagai Jungler", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401301-IRON.png", "BRONZE": "/challenges-images/401301-BRONZE.png", "SILVER": "/challenges-images/401301-SILVER.png", "GOLD": "/challenges-images/401301-GOLD.png", "PLATINUM": "/challenges-images/401301-PLATINUM.png", "DIAMOND": "/challenges-images/401301-DIAMOND.png", "MASTER": "/challenges-images/401301-MASTER.png", "GRANDMASTER": "/challenges-images/401301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Jungle Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401302, "name": "Support Diff", "description": "Menangkan game sebagai Support", "shortDescription": "Menangkan game sebagai Support", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401302-IRON.png", "BRONZE": "/challenges-images/401302-BRONZE.png", "SILVER": "/challenges-images/401302-SILVER.png", "GOLD": "/challenges-images/401302-GOLD.png", "PLATINUM": "/challenges-images/401302-PLATINUM.png", "DIAMOND": "/challenges-images/401302-DIAMOND.png", "MASTER": "/challenges-images/401302-MASTER.png", "GRANDMASTER": "/challenges-images/401302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Support Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401303, "name": "<PERSON><PERSON>", "description": "Menangkan game sebagai Bot Carry", "shortDescription": "Menangkan game sebagai Bot Carry", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401303-IRON.png", "BRONZE": "/challenges-images/401303-BRONZE.png", "SILVER": "/challenges-images/401303-SILVER.png", "GOLD": "/challenges-images/401303-GOLD.png", "PLATINUM": "/challenges-images/401303-PLATINUM.png", "DIAMOND": "/challenges-images/401303-DIAMOND.png", "MASTER": "/challenges-images/401303-MASTER.png", "GRANDMASTER": "/challenges-images/401303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401304, "name": "Mid Diff", "description": "Menangkan game sebagai Mid", "shortDescription": "Menangkan game sebagai Mid", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401304-IRON.png", "BRONZE": "/challenges-images/401304-BRONZE.png", "SILVER": "/challenges-images/401304-SILVER.png", "GOLD": "/challenges-images/401304-GOLD.png", "PLATINUM": "/challenges-images/401304-PLATINUM.png", "DIAMOND": "/challenges-images/401304-DIAMOND.png", "MASTER": "/challenges-images/401304-MASTER.png", "GRANDMASTER": "/challenges-images/401304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401304-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Mid Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401305, "name": "Top Diff", "description": "Menangkan game sebagai Top", "shortDescription": "Menangkan game sebagai Top", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401305-IRON.png", "BRONZE": "/challenges-images/401305-BRONZE.png", "SILVER": "/challenges-images/401305-SILVER.png", "GOLD": "/challenges-images/401305-GOLD.png", "PLATINUM": "/challenges-images/401305-PLATINUM.png", "DIAMOND": "/challenges-images/401305-DIAMOND.png", "MASTER": "/challenges-images/401305-MASTER.png", "GRANDMASTER": "/challenges-images/401305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Top Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401306, "name": "Player Diff", "description": "Menangkan game dengan masuk sebagai Fill, dan kamu memainkan posisi yang kamu isi", "shortDescription": "Menangkan game dengan masuk sebagai Fill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401306-IRON.png", "BRONZE": "/challenges-images/401306-BRONZE.png", "SILVER": "/challenges-images/401306-SILVER.png", "GOLD": "/challenges-images/401306-GOLD.png", "PLATINUM": "/challenges-images/401306-PLATINUM.png", "DIAMOND": "/challenges-images/401306-DIAMOND.png", "MASTER": "/challenges-images/401306-MASTER.png", "GRANDMASTER": "/challenges-images/401306-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401306-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 65, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Player Diff"}]}, "DIAMOND": {"value": 90}, "MASTER": {"value": 115}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 300}}}, {"id": 402000, "name": "Medal of Honor", "description": "<PERSON>h progres dari tantangan di grup Executioner, Commando, Resourceful, dan <PERSON>stic", "shortDescription": "<PERSON>h progres dari tantangan di grup Executioner, Commando, Resourceful, dan <PERSON>stic", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/402000-IRON.png", "BRONZE": "/challenges-images/402000-BRONZE.png", "SILVER": "/challenges-images/402000-SILVER.png", "GOLD": "/challenges-images/402000-GOLD.png", "PLATINUM": "/challenges-images/402000-PLATINUM.png", "DIAMOND": "/challenges-images/402000-DIAMOND.png", "MASTER": "/challenges-images/402000-MASTER.png", "GRANDMASTER": "/challenges-images/402000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 80}, "BRONZE": {"value": 165}, "SILVER": {"value": 250}, "GOLD": {"value": 570}, "PLATINUM": {"value": 910}, "DIAMOND": {"value": 1625}, "MASTER": {"value": 2700, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Veteran"}]}}}, {"id": 402100, "name": "Executioner", "description": "Raih progres dari tantangan di grup Executioner", "shortDescription": "Raih progres dari tantangan di grup Executioner", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402100-IRON.png", "BRONZE": "/challenges-images/402100-BRONZE.png", "SILVER": "/challenges-images/402100-SILVER.png", "GOLD": "/challenges-images/402100-GOLD.png", "PLATINUM": "/challenges-images/402100-PLATINUM.png", "DIAMOND": "/challenges-images/402100-DIAMOND.png", "MASTER": "/challenges-images/402100-MASTER.png", "GRANDMASTER": "/challenges-images/402100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 150}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 430, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Executioner"}]}, "MASTER": {"value": 725}, "GRANDMASTER": {"value": 800}, "CHALLENGER": {"value": 900}}}, {"id": 402101, "name": "Movin' On Up", "description": "Menangkan game Ranked Solo/Duo atau Ranked Flex", "shortDescription": "Menangkan game ranked dalam mode Solo/Duo atau Flex", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402101-IRON.png", "BRONZE": "/challenges-images/402101-BRONZE.png", "SILVER": "/challenges-images/402101-SILVER.png", "GOLD": "/challenges-images/402101-GOLD.png", "PLATINUM": "/challenges-images/402101-PLATINUM.png", "DIAMOND": "/challenges-images/402101-DIAMOND.png", "MASTER": "/challenges-images/402101-MASTER.png", "GRANDMASTER": "/challenges-images/402101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 75}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 402102, "name": "Master of the Rift", "description": "Mainkan Game di Summoner's R<PERSON>. Mode Quickplay, Draft, dan Ranked di<PERSON>ung.", "shortDescription": "Mainkan Game Summoner's Rift", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402102-IRON.png", "BRONZE": "/challenges-images/402102-BRONZE.png", "SILVER": "/challenges-images/402102-SILVER.png", "GOLD": "/challenges-images/402102-GOLD.png", "PLATINUM": "/challenges-images/402102-PLATINUM.png", "DIAMOND": "/challenges-images/402102-DIAMOND.png", "MASTER": "/challenges-images/402102-MASTER.png", "GRANDMASTER": "/challenges-images/402102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 60}, "GOLD": {"value": 150}, "PLATINUM": {"value": 300}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 5000}}}, {"id": 402103, "name": "Legendary Legend", "description": "<PERSON><PERSON> (8-0 kill streak)", "shortDescription": "<PERSON><PERSON><PERSON>ary", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402103-IRON.png", "BRONZE": "/challenges-images/402103-BRONZE.png", "SILVER": "/challenges-images/402103-SILVER.png", "GOLD": "/challenges-images/402103-GOLD.png", "PLATINUM": "/challenges-images/402103-PLATINUM.png", "DIAMOND": "/challenges-images/402103-DIAMOND.png", "MASTER": "/challenges-images/402103-MASTER.png", "GRANDMASTER": "/challenges-images/402103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402103-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 65}, "MASTER": {"value": 125}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 200}}}, {"id": 402104, "name": "Killing Spree Spree", "description": "<PERSON><PERSON><PERSON> killing spree (3 kill atau lebih tanpa mati)", "shortDescription": "<PERSON><PERSON><PERSON> killing spree", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402104-IRON.png", "BRONZE": "/challenges-images/402104-BRONZE.png", "SILVER": "/challenges-images/402104-SILVER.png", "GOLD": "/challenges-images/402104-GOLD.png", "PLATINUM": "/challenges-images/402104-PLATINUM.png", "DIAMOND": "/challenges-images/402104-DIAMOND.png", "MASTER": "/challenges-images/402104-MASTER.png", "GRANDMASTER": "/challenges-images/402104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 12}, "SILVER": {"value": 30}, "GOLD": {"value": 80}, "PLATINUM": {"value": 200}, "DIAMOND": {"value": 400}, "MASTER": {"value": 720}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 402105, "name": "Multikill Madness", "description": "Lakukan Multikill", "shortDescription": "Lakukan Multikill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402105-IRON.png", "BRONZE": "/challenges-images/402105-BRONZE.png", "SILVER": "/challenges-images/402105-SILVER.png", "GOLD": "/challenges-images/402105-GOLD.png", "PLATINUM": "/challenges-images/402105-PLATINUM.png", "DIAMOND": "/challenges-images/402105-DIAMOND.png", "MASTER": "/challenges-images/402105-MASTER.png", "GRANDMASTER": "/challenges-images/402105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 16}, "SILVER": {"value": 40}, "GOLD": {"value": 120}, "PLATINUM": {"value": 300}, "DIAMOND": {"value": 540}, "MASTER": {"value": 960}, "GRANDMASTER": {"value": 1380}, "CHALLENGER": {"value": 1800}}}, {"id": 402106, "name": "PENTAKIIIIIIIIL!!", "description": "Lakukan <PERSON>kill", "shortDescription": "Lakukan <PERSON>kill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402106-IRON.png", "BRONZE": "/challenges-images/402106-BRONZE.png", "SILVER": "/challenges-images/402106-SILVER.png", "GOLD": "/challenges-images/402106-GOLD.png", "PLATINUM": "/challenges-images/402106-PLATINUM.png", "DIAMOND": "/challenges-images/402106-DIAMOND.png", "MASTER": "/challenges-images/402106-MASTER.png", "GRANDMASTER": "/challenges-images/402106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402106-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Pentakiller"}]}, "DIAMOND": {"value": 3}, "MASTER": {"value": 5}, "GRANDMASTER": {"value": 7}, "CHALLENGER": {"value": 10}}}, {"id": 402107, "name": "Hard Day's Killin'", "description": "Lakukan <PERSON>", "shortDescription": "Lakukan <PERSON>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402107-IRON.png", "BRONZE": "/challenges-images/402107-BRONZE.png", "SILVER": "/challenges-images/402107-SILVER.png", "GOLD": "/challenges-images/402107-GOLD.png", "PLATINUM": "/challenges-images/402107-PLATINUM.png", "DIAMOND": "/challenges-images/402107-DIAMOND.png", "MASTER": "/challenges-images/402107-MASTER.png", "GRANDMASTER": "/challenges-images/402107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402107-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 150}, "SILVER": {"value": 350}, "GOLD": {"value": 750}, "PLATINUM": {"value": 2000}, "DIAMOND": {"value": 5000}, "MASTER": {"value": 10000}, "GRANDMASTER": {"value": 15000}, "CHALLENGER": {"value": 25000}}}, {"id": 402108, "name": "Above-Average Assistance", "description": "Dapatkan Assist", "shortDescription": "Dapatkan Assist", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402108-IRON.png", "BRONZE": "/challenges-images/402108-BRONZE.png", "SILVER": "/challenges-images/402108-SILVER.png", "GOLD": "/challenges-images/402108-GOLD.png", "PLATINUM": "/challenges-images/402108-PLATINUM.png", "DIAMOND": "/challenges-images/402108-DIAMOND.png", "MASTER": "/challenges-images/402108-MASTER.png", "GRANDMASTER": "/challenges-images/402108-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402108-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 200}, "SILVER": {"value": 500}, "GOLD": {"value": 1250}, "PLATINUM": {"value": 3500}, "DIAMOND": {"value": 6750}, "MASTER": {"value": 15000}, "GRANDMASTER": {"value": 20000}, "CHALLENGER": {"value": 35000}}}, {"id": 402109, "name": "Bloodcrazed", "description": "Dapatkan First Blood", "shortDescription": "Dapatkan First Blood", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/402109-IRON.png", "BRONZE": "/challenges-images/402109-BRONZE.png", "SILVER": "/challenges-images/402109-SILVER.png", "GOLD": "/challenges-images/402109-GOLD.png", "PLATINUM": "/challenges-images/402109-PLATINUM.png", "DIAMOND": "/challenges-images/402109-DIAMOND.png", "MASTER": "/challenges-images/402109-MASTER.png", "GRANDMASTER": "/challenges-images/402109-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402109-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Bloodthirsty"}]}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 75}, "MASTER": {"value": 150}}}, {"id": 402200, "name": "Commando", "description": "Raih progres dari tantangan di grup Commando", "shortDescription": "Raih progres dari tantangan di grup Commando", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402200-IRON.png", "BRONZE": "/challenges-images/402200-BRONZE.png", "SILVER": "/challenges-images/402200-SILVER.png", "GOLD": "/challenges-images/402200-GOLD.png", "PLATINUM": "/challenges-images/402200-PLATINUM.png", "DIAMOND": "/challenges-images/402200-DIAMOND.png", "MASTER": "/challenges-images/402200-MASTER.png", "GRANDMASTER": "/challenges-images/402200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 25}, "BRONZE": {"value": 50}, "SILVER": {"value": 75}, "GOLD": {"value": 165}, "PLATINUM": {"value": 265}, "DIAMOND": {"value": 480, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Commando"}]}, "MASTER": {"value": 800}, "GRANDMASTER": {"value": 960}, "CHALLENGER": {"value": 1160}}}, {"id": 402201, "name": "Clutch Steal", "description": "<PERSON>uri Monster Epik. Monster Epik men<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, dan <PERSON>.", "shortDescription": "Curi monster epik", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402201-IRON.png", "BRONZE": "/challenges-images/402201-BRONZE.png", "SILVER": "/challenges-images/402201-SILVER.png", "GOLD": "/challenges-images/402201-GOLD.png", "PLATINUM": "/challenges-images/402201-PLATINUM.png", "DIAMOND": "/challenges-images/402201-DIAMOND.png", "MASTER": "/challenges-images/402201-MASTER.png", "GRANDMASTER": "/challenges-images/402201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>hief"}]}, "DIAMOND": {"value": 50}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 200}}}, {"id": 402202, "name": "Give 'em Shell, Shelly", "description": "Berpartisipasi menghancurkan turret dengan Rift Herald", "shortDescription": "Hancurkan Turret dengan Rift Herald", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402202-IRON.png", "BRONZE": "/challenges-images/402202-BRONZE.png", "SILVER": "/challenges-images/402202-SILVER.png", "GOLD": "/challenges-images/402202-GOLD.png", "PLATINUM": "/challenges-images/402202-PLATINUM.png", "DIAMOND": "/challenges-images/402202-DIAMOND.png", "MASTER": "/challenges-images/402202-MASTER.png", "GRANDMASTER": "/challenges-images/402202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300}, "PLATINUM": {"value": 750}, "DIAMOND": {"value": 1400}, "MASTER": {"value": 2500}, "GRANDMASTER": {"value": 3000}, "CHALLENGER": {"value": 4000}}}, {"id": 402203, "name": "Scuttle Along Now", "description": "<PERSON><PERSON><PERSON>", "shortDescription": "Bunuh scuttle crab", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402203-IRON.png", "BRONZE": "/challenges-images/402203-BRONZE.png", "SILVER": "/challenges-images/402203-SILVER.png", "GOLD": "/challenges-images/402203-GOLD.png", "PLATINUM": "/challenges-images/402203-PLATINUM.png", "DIAMOND": "/challenges-images/402203-DIAMOND.png", "MASTER": "/challenges-images/402203-MASTER.png", "GRANDMASTER": "/challenges-images/402203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300}, "PLATINUM": {"value": 750}, "DIAMOND": {"value": 1400}, "MASTER": {"value": 2500}, "GRANDMASTER": {"value": 3000}, "CHALLENGER": {"value": 4000}}}, {"id": 402204, "name": "<PERSON>e In", "description": "Raih CS dari monster jungle di jungle-mu", "shortDescription": "Bunuh monster jungle di jungle-mu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402204-IRON.png", "BRONZE": "/challenges-images/402204-BRONZE.png", "SILVER": "/challenges-images/402204-SILVER.png", "GOLD": "/challenges-images/402204-GOLD.png", "PLATINUM": "/challenges-images/402204-PLATINUM.png", "DIAMOND": "/challenges-images/402204-DIAMOND.png", "MASTER": "/challenges-images/402204-MASTER.png", "GRANDMASTER": "/challenges-images/402204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 400}, "BRONZE": {"value": 1600}, "SILVER": {"value": 4000}, "GOLD": {"value": 12000}, "PLATINUM": {"value": 30000}, "DIAMOND": {"value": 55000}, "MASTER": {"value": 96000}, "GRANDMASTER": {"value": 125000}, "CHALLENGER": {"value": 175000}}}, {"id": 402205, "name": "Dine Out", "description": "Bunuh monster jungle di jungle musuh", "shortDescription": "Bunuh monster jungle di jungle musuh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402205-IRON.png", "BRONZE": "/challenges-images/402205-BRONZE.png", "SILVER": "/challenges-images/402205-SILVER.png", "GOLD": "/challenges-images/402205-GOLD.png", "PLATINUM": "/challenges-images/402205-PLATINUM.png", "DIAMOND": "/challenges-images/402205-DIAMOND.png", "MASTER": "/challenges-images/402205-MASTER.png", "GRANDMASTER": "/challenges-images/402205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 60}, "BRONZE": {"value": 240}, "SILVER": {"value": 600}, "GOLD": {"value": 1500}, "PLATINUM": {"value": 4000}, "DIAMOND": {"value": 8000}, "MASTER": {"value": 14000}, "GRANDMASTER": {"value": 20000}, "CHALLENGER": {"value": 30000}}}, {"id": 402206, "name": "Lizard <PERSON>", "description": "Takedown Dragon", "shortDescription": "Takedown Dragon", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402206-IRON.png", "BRONZE": "/challenges-images/402206-BRONZE.png", "SILVER": "/challenges-images/402206-SILVER.png", "GOLD": "/challenges-images/402206-GOLD.png", "PLATINUM": "/challenges-images/402206-PLATINUM.png", "DIAMOND": "/challenges-images/402206-DIAMOND.png", "MASTER": "/challenges-images/402206-MASTER.png", "GRANDMASTER": "/challenges-images/402206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 75}, "GOLD": {"value": 200}, "PLATINUM": {"value": 550}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3500}}}, {"id": 402207, "name": "Wurm Fishing", "description": "Takedown Baron", "shortDescription": "Takedown Baron", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402207-IRON.png", "BRONZE": "/challenges-images/402207-BRONZE.png", "SILVER": "/challenges-images/402207-SILVER.png", "GOLD": "/challenges-images/402207-GOLD.png", "PLATINUM": "/challenges-images/402207-PLATINUM.png", "DIAMOND": "/challenges-images/402207-DIAMOND.png", "MASTER": "/challenges-images/402207-MASTER.png", "GRANDMASTER": "/challenges-images/402207-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402207-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 8}, "SILVER": {"value": 20}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 200}, "MASTER": {"value": 350}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 402208, "name": "Shell Collecting", "description": "Takedown Rift Herald", "shortDescription": "Takedown Rift Herald", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402208-IRON.png", "BRONZE": "/challenges-images/402208-BRONZE.png", "SILVER": "/challenges-images/402208-SILVER.png", "GOLD": "/challenges-images/402208-GOLD.png", "PLATINUM": "/challenges-images/402208-PLATINUM.png", "DIAMOND": "/challenges-images/402208-DIAMOND.png", "MASTER": "/challenges-images/402208-MASTER.png", "GRANDMASTER": "/challenges-images/402208-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402208-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 65}, "PLATINUM": {"value": 180}, "DIAMOND": {"value": 300}, "MASTER": {"value": 600}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 402209, "name": "Dinner's Ready", "description": "Hancurkan turret plate", "shortDescription": "Hancurkan Turret Plate", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402209-IRON.png", "BRONZE": "/challenges-images/402209-BRONZE.png", "SILVER": "/challenges-images/402209-SILVER.png", "GOLD": "/challenges-images/402209-GOLD.png", "PLATINUM": "/challenges-images/402209-PLATINUM.png", "DIAMOND": "/challenges-images/402209-DIAMOND.png", "MASTER": "/challenges-images/402209-MASTER.png", "GRANDMASTER": "/challenges-images/402209-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402209-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 35}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1350}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2400}, "CHALLENGER": {"value": 3000}}}, {"id": 402210, "name": "<PERSON><PERSON><PERSON>", "description": "Takedown turret", "shortDescription": "Hancurkan turret", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402210-IRON.png", "BRONZE": "/challenges-images/402210-BRONZE.png", "SILVER": "/challenges-images/402210-SILVER.png", "GOLD": "/challenges-images/402210-GOLD.png", "PLATINUM": "/challenges-images/402210-PLATINUM.png", "DIAMOND": "/challenges-images/402210-DIAMOND.png", "MASTER": "/challenges-images/402210-MASTER.png", "GRANDMASTER": "/challenges-images/402210-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402210-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 35}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1350}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2400}, "CHALLENGER": {"value": 3000}}}, {"id": 402400, "name": "Resourceful", "description": "Raih progres dari tantangan di grup Resourceful", "shortDescription": "Raih progres dari tantangan di grup Resourceful", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402400-IRON.png", "BRONZE": "/challenges-images/402400-BRONZE.png", "SILVER": "/challenges-images/402400-SILVER.png", "GOLD": "/challenges-images/402400-GOLD.png", "PLATINUM": "/challenges-images/402400-PLATINUM.png", "DIAMOND": "/challenges-images/402400-DIAMOND.png", "MASTER": "/challenges-images/402400-MASTER.png", "GRANDMASTER": "/challenges-images/402400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 135}, "PLATINUM": {"value": 215}, "DIAMOND": {"value": 380, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Resourceful"}]}, "MASTER": {"value": 650}, "GRANDMASTER": {"value": 780}, "CHALLENGER": {"value": 940}}}, {"id": 402401, "name": "Darkness is Everywhere", "description": "Takedown ward", "shortDescription": "Takedown ward", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402401-IRON.png", "BRONZE": "/challenges-images/402401-BRONZE.png", "SILVER": "/challenges-images/402401-SILVER.png", "GOLD": "/challenges-images/402401-GOLD.png", "PLATINUM": "/challenges-images/402401-PLATINUM.png", "DIAMOND": "/challenges-images/402401-DIAMOND.png", "MASTER": "/challenges-images/402401-MASTER.png", "GRANDMASTER": "/challenges-images/402401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402401-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1200}, "MASTER": {"value": 2400}, "GRANDMASTER": {"value": 3000}, "CHALLENGER": {"value": 3600}}}, {"id": 402402, "name": "Little Inanimate Spies", "description": "Tempatkan Stealth Ward yang efektif. Ward yang efektif adalah yang berkontribusi terhadap Skor Vision.", "shortDescription": "Tempatkan Stealth Ward", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402402-IRON.png", "BRONZE": "/challenges-images/402402-BRONZE.png", "SILVER": "/challenges-images/402402-SILVER.png", "GOLD": "/challenges-images/402402-GOLD.png", "PLATINUM": "/challenges-images/402402-PLATINUM.png", "DIAMOND": "/challenges-images/402402-DIAMOND.png", "MASTER": "/challenges-images/402402-MASTER.png", "GRANDMASTER": "/challenges-images/402402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402402-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 75}, "BRONZE": {"value": 300}, "SILVER": {"value": 750}, "GOLD": {"value": 1500}, "PLATINUM": {"value": 3750}, "DIAMOND": {"value": 9000}, "MASTER": {"value": 15000}, "GRANDMASTER": {"value": 18000}, "CHALLENGER": {"value": 25000}}}, {"id": 402403, "name": "Goodbye to the Darkness!", "description": "Tempatkan Control Ward yang efektif. Ward yang efektif adalah yang berkontribusi terhadap Skor Vision", "shortDescription": "Tempatkan Control Ward", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402403-IRON.png", "BRONZE": "/challenges-images/402403-BRONZE.png", "SILVER": "/challenges-images/402403-SILVER.png", "GOLD": "/challenges-images/402403-GOLD.png", "PLATINUM": "/challenges-images/402403-PLATINUM.png", "DIAMOND": "/challenges-images/402403-DIAMOND.png", "MASTER": "/challenges-images/402403-MASTER.png", "GRANDMASTER": "/challenges-images/402403-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402403-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 40}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 500}, "DIAMOND": {"value": 900}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2700}, "CHALLENGER": {"value": 3600}}}, {"id": 402404, "name": "Nice Try", "description": "Bunuh champion musuh di bawah turret-mu", "shortDescription": "Bunuh musuh di bawah turret-mu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402404-IRON.png", "BRONZE": "/challenges-images/402404-BRONZE.png", "SILVER": "/challenges-images/402404-SILVER.png", "GOLD": "/challenges-images/402404-GOLD.png", "PLATINUM": "/challenges-images/402404-PLATINUM.png", "DIAMOND": "/challenges-images/402404-DIAMOND.png", "MASTER": "/challenges-images/402404-MASTER.png", "GRANDMASTER": "/challenges-images/402404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402404-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 16}, "SILVER": {"value": 40}, "GOLD": {"value": 80}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 520}, "MASTER": {"value": 960}, "GRANDMASTER": {"value": 1200}, "CHALLENGER": {"value": 1600}}}, {"id": 402407, "name": "Do These Things Have Souls?", "description": "<PERSON><PERSON><PERSON> minion", "shortDescription": "<PERSON><PERSON><PERSON> minion", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402407-IRON.png", "BRONZE": "/challenges-images/402407-BRONZE.png", "SILVER": "/challenges-images/402407-SILVER.png", "GOLD": "/challenges-images/402407-GOLD.png", "PLATINUM": "/challenges-images/402407-PLATINUM.png", "DIAMOND": "/challenges-images/402407-DIAMOND.png", "MASTER": "/challenges-images/402407-MASTER.png", "GRANDMASTER": "/challenges-images/402407-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402407-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 750}, "BRONZE": {"value": 3000}, "SILVER": {"value": 7500}, "GOLD": {"value": 15000}, "PLATINUM": {"value": 37500}, "DIAMOND": {"value": 67500}, "MASTER": {"value": 120000}, "GRANDMASTER": {"value": 180000}, "CHALLENGER": {"value": 300000}}}, {"id": 402408, "name": "Everyone Pays!", "description": "Klaim gold bounty dengan me<PERSON>kan champion musuh", "shortDescription": "Dapatkan bounty gold", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402408-IRON.png", "BRONZE": "/challenges-images/402408-BRONZE.png", "SILVER": "/challenges-images/402408-SILVER.png", "GOLD": "/challenges-images/402408-GOLD.png", "PLATINUM": "/challenges-images/402408-PLATINUM.png", "DIAMOND": "/challenges-images/402408-DIAMOND.png", "MASTER": "/challenges-images/402408-MASTER.png", "GRANDMASTER": "/challenges-images/402408-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402408-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1250}, "BRONZE": {"value": 5000}, "SILVER": {"value": 15000, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Bount<PERSON> Hunter"}]}, "GOLD": {"value": 30000}, "PLATINUM": {"value": 90000}, "DIAMOND": {"value": 160000}, "MASTER": {"value": 300000}, "GRANDMASTER": {"value": 400000}, "CHALLENGER": {"value": 500000}}}, {"id": 402409, "name": "Get Paid, and Get Out", "description": "Dapatkan gold", "shortDescription": "Dapatkan gold", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402409-IRON.png", "BRONZE": "/challenges-images/402409-BRONZE.png", "SILVER": "/challenges-images/402409-SILVER.png", "GOLD": "/challenges-images/402409-GOLD.png", "PLATINUM": "/challenges-images/402409-PLATINUM.png", "DIAMOND": "/challenges-images/402409-DIAMOND.png", "MASTER": "/challenges-images/402409-MASTER.png", "GRANDMASTER": "/challenges-images/402409-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402409-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 55500}, "BRONZE": {"value": 222000}, "SILVER": {"value": 555000}, "GOLD": {"value": 1110000}, "PLATINUM": {"value": 4150000}, "DIAMOND": {"value": 7500000}, "MASTER": {"value": 15000000}, "GRANDMASTER": {"value": 25000000}, "CHALLENGER": {"value": 50000000}}}, {"id": 402500, "name": "Mystic", "description": "Raih progres dari tantangan di grup Mystic", "shortDescription": "Raih progres dari tantangan di grup Mystic", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/402500-IRON.png", "BRONZE": "/challenges-images/402500-BRONZE.png", "SILVER": "/challenges-images/402500-SILVER.png", "GOLD": "/challenges-images/402500-GOLD.png", "PLATINUM": "/challenges-images/402500-PLATINUM.png", "DIAMOND": "/challenges-images/402500-DIAMOND.png", "MASTER": "/challenges-images/402500-MASTER.png", "GRANDMASTER": "/challenges-images/402500-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402500-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Enchanted"}]}, "MASTER": {"value": 250}}}, {"id": 402501, "name": "Field Medic", "description": "Lakukan heal atau shield efektif ke sekutu (heal/shield diri sendiri tidak dihitung). Heal efektif harus benar-benar memulihkan health, dan shield efektif harus benar-benar menahan damage.", "shortDescription": "Heal atau shield sekutu dengan efektif", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402501-IRON.png", "BRONZE": "/challenges-images/402501-BRONZE.png", "SILVER": "/challenges-images/402501-SILVER.png", "GOLD": "/challenges-images/402501-GOLD.png", "PLATINUM": "/challenges-images/402501-PLATINUM.png", "DIAMOND": "/challenges-images/402501-DIAMOND.png", "MASTER": "/challenges-images/402501-MASTER.png", "GRANDMASTER": "/challenges-images/402501-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402501-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 6000}, "BRONZE": {"value": 24000}, "SILVER": {"value": 72000}, "GOLD": {"value": 150000}, "PLATINUM": {"value": 450000, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Protector"}]}, "DIAMOND": {"value": 800000}, "MASTER": {"value": 1400000}, "GRANDMASTER": {"value": 2000000}, "CHALLENGER": {"value": 3000000}}}, {"id": 402502, "name": "Immediate Immobilization", "description": "Immobilize champion musuh", "shortDescription": "Immobilize musuh", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402502-IRON.png", "BRONZE": "/challenges-images/402502-BRONZE.png", "SILVER": "/challenges-images/402502-SILVER.png", "GOLD": "/challenges-images/402502-GOLD.png", "PLATINUM": "/challenges-images/402502-PLATINUM.png", "DIAMOND": "/challenges-images/402502-DIAMOND.png", "MASTER": "/challenges-images/402502-MASTER.png", "GRANDMASTER": "/challenges-images/402502-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402502-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 500}, "SILVER": {"value": 1200}, "GOLD": {"value": 2000}, "PLATINUM": {"value": 6000}, "DIAMOND": {"value": 15000}, "MASTER": {"value": 25000}, "GRANDMASTER": {"value": 40000}, "CHALLENGER": {"value": 65000}}}, {"id": 402503, "name": "Ability Abuse", "description": "Gunakan Ability", "shortDescription": "Gunakan Ability", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402503-IRON.png", "BRONZE": "/challenges-images/402503-BRONZE.png", "SILVER": "/challenges-images/402503-SILVER.png", "GOLD": "/challenges-images/402503-GOLD.png", "PLATINUM": "/challenges-images/402503-PLATINUM.png", "DIAMOND": "/challenges-images/402503-DIAMOND.png", "MASTER": "/challenges-images/402503-MASTER.png", "GRANDMASTER": "/challenges-images/402503-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402503-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1100}, "BRONZE": {"value": 4400}, "SILVER": {"value": 11000}, "GOLD": {"value": 27000}, "PLATINUM": {"value": 65000}, "DIAMOND": {"value": 148000}, "MASTER": {"value": 260000}, "GRANDMASTER": {"value": 400000}, "CHALLENGER": {"value": 650000}}}, {"id": 5, "name": "COLLECTION", "description": "", "shortDescription": "Capstone KOLEKSI", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 75}, "BRONZE": {"value": 175}, "SILVER": {"value": 300}, "GOLD": {"value": 700}, "PLATINUM": {"value": 1100}, "DIAMOND": {"value": 2000}, "MASTER": {"value": 3500}}}, {"id": 501000, "name": "Experience", "description": "Raih progres dari tantangan di grup Experience", "shortDescription": "Raih progres dari tantangan di grup Experience", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501000-IRON.png", "BRONZE": "/challenges-images/501000-BRONZE.png", "SILVER": "/challenges-images/501000-SILVER.png", "GOLD": "/challenges-images/501000-GOLD.png", "PLATINUM": "/challenges-images/501000-PLATINUM.png", "DIAMOND": "/challenges-images/501000-DIAMOND.png", "MASTER": "/challenges-images/501000-MASTER.png", "GRANDMASTER": "/challenges-images/502000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 85}, "GOLD": {"value": 185}, "PLATINUM": {"value": 295}, "DIAMOND": {"value": 530}, "MASTER": {"value": 875}}}, {"id": 501001, "name": "<PERSON> Marker", "description": "Raih milestone pada Eternal mana pun", "shortDescription": "Raih milestone pada Eternal mana pun", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/501001-IRON.png", "BRONZE": "/challenges-images/501001-BRONZE.png", "SILVER": "/challenges-images/501001-SILVER.png", "GOLD": "/challenges-images/501001-GOLD.png", "PLATINUM": "/challenges-images/501001-PLATINUM.png", "DIAMOND": "/challenges-images/501001-DIAMOND.png", "MASTER": "/challenges-images/501001-MASTER.png", "GRANDMASTER": "/challenges-images/501001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 15}, "GOLD": {"value": 75}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1250}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2500}}}, {"id": 501002, "name": "Many Miles to Go", "description": "Capai milestone 5 atau lebih tinggi pada Eternal", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> kem<PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501002-IRON.png", "BRONZE": "/challenges-images/501002-BRONZE.png", "SILVER": "/challenges-images/501002-SILVER.png", "GOLD": "/challenges-images/501002-GOLD.png", "PLATINUM": "/challenges-images/501002-PLATINUM.png", "DIAMOND": "/challenges-images/501002-DIAMOND.png", "MASTER": "/challenges-images/501002-MASTER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 40}, "MASTER": {"value": 80}}}, {"id": 501003, "name": "Well-Rounded Traveller", "description": "Capai milestone 15 atau lebih tinggi pada Eternal", "shortDescription": "Capai milestone 15 atau lebih tinggi pada Eternal", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501003-IRON.png", "BRONZE": "/challenges-images/501003-BRONZE.png", "SILVER": "/challenges-images/501003-SILVER.png", "GOLD": "/challenges-images/501003-GOLD.png", "PLATINUM": "/challenges-images/501003-PLATINUM.png", "DIAMOND": "/challenges-images/501003-DIAMOND.png", "MASTER": "/challenges-images/501003-MASTER.png", "GRANDMASTER": "/challenges-images/501003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 12}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 45}}}, {"id": 501004, "name": "Old Friends", "description": "Capai milestone pada Eternal untuk satu champion", "shortDescription": "Capai milestone pada Eternal untuk satu champion", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/501004-IRON.png", "BRONZE": "/challenges-images/501004-BRONZE.png", "SILVER": "/challenges-images/501004-SILVER.png", "GOLD": "/challenges-images/501004-GOLD.png", "PLATINUM": "/challenges-images/501004-PLATINUM.png", "DIAMOND": "/challenges-images/501004-DIAMOND.png", "MASTER": "/challenges-images/501004-MASTER.png", "GRANDMASTER": "/challenges-images/501004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 90}, "MASTER": {"value": 125}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 175}}}, {"id": 501005, "name": "Rekindle the Old Furnace", "description": "<PERSON><PERSON><PERSON><PERSON> kembali set Eternal. Set yang dinyalakan kembali adalah yang tiga atau lebih Eternal-nya telah mencapai level 5 atau di atasnya", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> set Eternal", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501005-IRON.png", "BRONZE": "/challenges-images/501005-BRONZE.png", "SILVER": "/challenges-images/501005-SILVER.png", "GOLD": "/challenges-images/501005-GOLD.png", "PLATINUM": "/challenges-images/501005-PLATINUM.png", "DIAMOND": "/challenges-images/501005-DIAMOND.png", "MASTER": "/challenges-images/501005-MASTER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 6}, "DIAMOND": {"value": 15}, "MASTER": {"value": 30}}}, {"id": 501007, "name": "Assassin Specialist", "description": "<PERSON><PERSON><PERSON><PERSON> set Eternal untuk assassin", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> set <PERSON> untuk <em>assassin</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501007-IRON.png", "BRONZE": "/challenges-images/501007-BRONZE.png", "SILVER": "/challenges-images/501007-SILVER.png", "GOLD": "/challenges-images/501007-GOLD.png", "PLATINUM": "/challenges-images/501007-PLATINUM.png", "DIAMOND": "/challenges-images/501007-DIAMOND.png", "MASTER": "/challenges-images/501007-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501008, "name": "Fighter Specialist", "description": "<PERSON><PERSON><PERSON><PERSON> set Eternal untuk fighter", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> set <PERSON> untuk <em>fighter</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501008-IRON.png", "BRONZE": "/challenges-images/501008-BRONZE.png", "SILVER": "/challenges-images/501008-SILVER.png", "GOLD": "/challenges-images/501008-GOLD.png", "PLATINUM": "/challenges-images/501008-PLATINUM.png", "DIAMOND": "/challenges-images/501008-DIAMOND.png", "MASTER": "/challenges-images/501008-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501009, "name": "Mage <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> set Eternal untuk mage", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> kem<PERSON> set Eternal untuk <em>mage</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501009-IRON.png", "BRONZE": "/challenges-images/501009-BRONZE.png", "SILVER": "/challenges-images/501009-SILVER.png", "GOLD": "/challenges-images/501009-GOLD.png", "PLATINUM": "/challenges-images/501009-PLATINUM.png", "DIAMOND": "/challenges-images/501009-DIAMOND.png", "MASTER": "/challenges-images/501009-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501010, "name": "Marksman Specialist", "description": "<PERSON><PERSON><PERSON><PERSON> set Eternal untuk marksman", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> set <PERSON> untuk <em>marksman</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501010-IRON.png", "BRONZE": "/challenges-images/501010-BRONZE.png", "SILVER": "/challenges-images/501010-SILVER.png", "GOLD": "/challenges-images/501010-GOLD.png", "PLATINUM": "/challenges-images/501010-PLATINUM.png", "DIAMOND": "/challenges-images/501010-DIAMOND.png", "MASTER": "/challenges-images/501010-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501011, "name": "Support Specialist", "description": "<PERSON><PERSON><PERSON><PERSON> set Eternal untuk support", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> kem<PERSON> set Eternal untuk <em>support</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501011-IRON.png", "BRONZE": "/challenges-images/501011-BRONZE.png", "SILVER": "/challenges-images/501011-SILVER.png", "GOLD": "/challenges-images/501011-GOLD.png", "PLATINUM": "/challenges-images/501011-PLATINUM.png", "DIAMOND": "/challenges-images/501011-DIAMOND.png", "MASTER": "/challenges-images/501011-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501012, "name": "Tank Specialist", "description": "<PERSON><PERSON><PERSON><PERSON> set Eternal untuk tank", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> set Eternal untuk <em>tank</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501012-IRON.png", "BRONZE": "/challenges-images/501012-BRONZE.png", "SILVER": "/challenges-images/501012-SILVER.png", "GOLD": "/challenges-images/501012-GOLD.png", "PLATINUM": "/challenges-images/501012-PLATINUM.png", "DIAMOND": "/challenges-images/501012-DIAMOND.png", "MASTER": "/challenges-images/501012-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 502000, "name": "Overachiever", "description": "Raih progres dari tantangan di grup Overachiever", "shortDescription": "Raih progres dari tantangan di grup Overachiever", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502000-IRON.png", "BRONZE": "/challenges-images/502000-BRONZE.png", "SILVER": "/challenges-images/502000-SILVER.png", "GOLD": "/challenges-images/502000-GOLD.png", "PLATINUM": "/challenges-images/502000-PLATINUM.png", "DIAMOND": "/challenges-images/502000-DIAMOND.png", "MASTER": "/challenges-images/502000-MASTER.png", "GRANDMASTER": "/challenges-images/502000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Overachiever"}]}, "MASTER": {"value": 400}}}, {"id": 502001, "name": "Entitled", "description": "Raih gelar untuk berbagai tantangan", "shortDescription": "<PERSON>h gelar dari tantangan", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502001-IRON.png", "BRONZE": "/challenges-images/502001-BRONZE.png", "SILVER": "/challenges-images/502001-SILVER.png", "GOLD": "/challenges-images/502001-GOLD.png", "PLATINUM": "/challenges-images/502001-PLATINUM.png", "DIAMOND": "/challenges-images/502001-DIAMOND.png", "MASTER": "/challenges-images/502001-MASTER.png", "GRANDMASTER": "/challenges-images/502001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 30}, "PLATINUM": {"value": 45, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Titular"}]}, "DIAMOND": {"value": 70}, "MASTER": {"value": 100}}}, {"id": 502002, "name": "Recursive", "description": "Selesaikan tantangan koleksi dengan mencapai level non-leaderboard tertinggi", "shortDescription": "Selesaikan tantangan koleksi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502002-IRON.png", "BRONZE": "/challenges-images/502002-BRONZE.png", "SILVER": "/challenges-images/502002-SILVER.png", "GOLD": "/challenges-images/502002-GOLD.png", "PLATINUM": "/challenges-images/502002-PLATINUM.png", "DIAMOND": "/challenges-images/502002-DIAMOND.png", "MASTER": "/challenges-images/502002-MASTER.png", "GRANDMASTER": "/challenges-images/502002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 25}, "MASTER": {"value": 30}}}, {"id": 502003, "name": "Challenges are Forever", "description": "Capai tingkat Diamond pada tantangan", "shortDescription": "Capai tingkat Diamond pada berbagai tantangan", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502003-IRON.png", "BRONZE": "/challenges-images/502003-BRONZE.png", "SILVER": "/challenges-images/502003-SILVER.png", "GOLD": "/challenges-images/502003-GOLD.png", "PLATINUM": "/challenges-images/502003-PLATINUM.png", "DIAMOND": "/challenges-images/502003-DIAMOND.png", "MASTER": "/challenges-images/502003-MASTER.png", "GRANDMASTER": "/challenges-images/502003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}}}, {"id": 502004, "name": "Masterful Performance", "description": "Capai tingkat Master pada tantangan", "shortDescription": "Capai tingkat Master pada berbagai tantangan", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502004-IRON.png", "BRONZE": "/challenges-images/502004-BRONZE.png", "SILVER": "/challenges-images/502004-SILVER.png", "GOLD": "/challenges-images/502004-GOLD.png", "PLATINUM": "/challenges-images/502004-PLATINUM.png", "DIAMOND": "/challenges-images/502004-DIAMOND.png", "MASTER": "/challenges-images/502004-MASTER.png", "GRANDMASTER": "/challenges-images/502004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}}}, {"id": 502005, "name": "No Capstone Unturned", "description": "Capai tingkat Gold pada Capstone", "shortDescription": "Capai tingkat Gold pada Capstone", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502005-IRON.png", "BRONZE": "/challenges-images/502005-BRONZE.png", "SILVER": "/challenges-images/502005-SILVER.png", "GOLD": "/challenges-images/502005-GOLD.png", "PLATINUM": "/challenges-images/502005-PLATINUM.png", "DIAMOND": "/challenges-images/502005-DIAMOND.png", "MASTER": "/challenges-images/502005-MASTER.png", "GRANDMASTER": "/challenges-images/502005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 40}, "MASTER": {"value": 50}}}, {"id": 504000, "name": "Treasure", "description": "Raih progres dari tantangan di grup <PERSON>", "shortDescription": "Raih progres dari tantangan di grup <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504000-IRON.png", "BRONZE": "/challenges-images/504000-BRONZE.png", "SILVER": "/challenges-images/504000-SILVER.png", "GOLD": "/challenges-images/504000-GOLD.png", "PLATINUM": "/challenges-images/504000-PLATINUM.png", "DIAMOND": "/challenges-images/504000-DIAMOND.png", "MASTER": "/challenges-images/504000-MASTER.png", "GRANDMASTER": "/challenges-images/504000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 35}, "GOLD": {"value": 70}, "PLATINUM": {"value": 110}, "DIAMOND": {"value": 200, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Treasurer"}]}, "MASTER": {"value": 325}}}, {"id": 504001, "name": "Mascot Mayhem", "description": "Dapatkan logo tim <PERSON>", "shortDescription": "Dapatkan logo tim <PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504001-IRON.png", "BRONZE": "/challenges-images/504001-BRONZE.png", "SILVER": "/challenges-images/504001-SILVER.png", "GOLD": "/challenges-images/504001-GOLD.png", "PLATINUM": "/challenges-images/504001-PLATINUM.png", "DIAMOND": "/challenges-images/504001-DIAMOND.png", "MASTER": "/challenges-images/504001-MASTER.png", "GRANDMASTER": "/challenges-images/504001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 20}, "MASTER": {"value": 40}}}, {"id": 504002, "name": "Icon of the Rift", "description": "Dapatkan icon <PERSON><PERSON><PERSON>r", "shortDescription": "Dapatkan Icon Summoner", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504002-IRON.png", "BRONZE": "/challenges-images/504002-BRONZE.png", "SILVER": "/challenges-images/504002-SILVER.png", "GOLD": "/challenges-images/504002-GOLD.png", "PLATINUM": "/challenges-images/504002-PLATINUM.png", "DIAMOND": "/challenges-images/504002-DIAMOND.png", "MASTER": "/challenges-images/504002-MASTER.png", "GRANDMASTER": "/challenges-images/504002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Iconic"}]}, "DIAMOND": {"value": 200}, "MASTER": {"value": 400}}}, {"id": 504003, "name": "<PERSON>", "description": "Dapatkan skin ward", "shortDescription": "Dapatkan skin ward", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504003-IRON.png", "BRONZE": "/challenges-images/504003-BRONZE.png", "SILVER": "/challenges-images/504003-SILVER.png", "GOLD": "/challenges-images/504003-GOLD.png", "PLATINUM": "/challenges-images/504003-PLATINUM.png", "DIAMOND": "/challenges-images/504003-DIAMOND.png", "MASTER": "/challenges-images/504003-MASTER.png", "GRANDMASTER": "/challenges-images/504003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 30}, "MASTER": {"value": 60}}}, {"id": 504004, "name": "Emotive", "description": "Dapat<PERSON> emote", "shortDescription": "Dapat<PERSON> emote", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504004-IRON.png", "BRONZE": "/challenges-images/504004-BRONZE.png", "SILVER": "/challenges-images/504004-SILVER.png", "GOLD": "/challenges-images/504004-GOLD.png", "PLATINUM": "/challenges-images/504004-PLATINUM.png", "DIAMOND": "/challenges-images/504004-DIAMOND.png", "MASTER": "/challenges-images/504004-MASTER.png", "GRANDMASTER": "/challenges-images/504004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 40}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 150}, "MASTER": {"value": 275}}}, {"id": 505000, "name": "Champion", "description": "Raih progres dari tantangan di grup Champion", "shortDescription": "Raih progres dari tantangan di grup Champion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505000-IRON.png", "BRONZE": "/challenges-images/505000-BRONZE.png", "SILVER": "/challenges-images/505000-SILVER.png", "GOLD": "/challenges-images/505000-GOLD.png", "PLATINUM": "/challenges-images/505000-PLATINUM.png", "DIAMOND": "/challenges-images/505000-DIAMOND.png", "MASTER": "/challenges-images/505000-MASTER.png", "GRANDMASTER": "/challenges-images/505000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 35}, "SILVER": {"value": 55}, "GOLD": {"value": 115}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 340, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Collector"}]}, "MASTER": {"value": 550}}}, {"id": 505001, "name": "Spice of Life", "description": "Dapatkan champion", "shortDescription": "Dapatkan <em>champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505001-IRON.png", "BRONZE": "/challenges-images/505001-BRONZE.png", "SILVER": "/challenges-images/505001-SILVER.png", "GOLD": "/challenges-images/505001-GOLD.png", "PLATINUM": "/challenges-images/505001-PLATINUM.png", "DIAMOND": "/challenges-images/505001-DIAMOND.png", "MASTER": "/challenges-images/505001-MASTER.png", "GRANDMASTER": "/challenges-images/505001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}}}, {"id": 505002, "name": "Invisible", "description": "Dapatkan champion assassin", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>champion assassin</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505002-IRON.png", "BRONZE": "/challenges-images/505002-BRONZE.png", "SILVER": "/challenges-images/505002-SILVER.png", "GOLD": "/challenges-images/505002-GOLD.png", "PLATINUM": "/challenges-images/505002-PLATINUM.png", "DIAMOND": "/challenges-images/505002-DIAMOND.png", "MASTER": "/challenges-images/505002-MASTER.png", "GRANDMASTER": "/challenges-images/505002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 35}, "MASTER": {"value": 45}}}, {"id": 505003, "name": "Indomitable", "description": "Dapatkan champion fighter", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>champion fighter</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505003-IRON.png", "BRONZE": "/challenges-images/505003-BRONZE.png", "SILVER": "/challenges-images/505003-SILVER.png", "GOLD": "/challenges-images/505003-GOLD.png", "PLATINUM": "/challenges-images/505003-PLATINUM.png", "DIAMOND": "/challenges-images/505003-DIAMOND.png", "MASTER": "/challenges-images/505003-MASTER.png", "GRANDMASTER": "/challenges-images/505003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 50}, "MASTER": {"value": 70}}}, {"id": 505004, "name": "Incandescent", "description": "Dapatkan champion mage", "shortDescription": "Da<PERSON>t<PERSON> <em>champion mage</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505004-IRON.png", "BRONZE": "/challenges-images/505004-BRONZE.png", "SILVER": "/challenges-images/505004-SILVER.png", "GOLD": "/challenges-images/505004-GOLD.png", "PLATINUM": "/challenges-images/505004-PLATINUM.png", "DIAMOND": "/challenges-images/505004-DIAMOND.png", "MASTER": "/challenges-images/505004-MASTER.png", "GRANDMASTER": "/challenges-images/505004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}}}, {"id": 505005, "name": "Ingenious", "description": "Dapatkan champion marksman", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>champion marksman</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505005-IRON.png", "BRONZE": "/challenges-images/505005-BRONZE.png", "SILVER": "/challenges-images/505005-SILVER.png", "GOLD": "/challenges-images/505005-GOLD.png", "PLATINUM": "/challenges-images/505005-PLATINUM.png", "DIAMOND": "/challenges-images/505005-DIAMOND.png", "MASTER": "/challenges-images/505005-MASTER.png", "GRANDMASTER": "/challenges-images/505005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 505006, "name": "Inspiring", "description": "Dapatkan champion support", "shortDescription": "Dapatkan <em>champion support</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505006-IRON.png", "BRONZE": "/challenges-images/505006-BRONZE.png", "SILVER": "/challenges-images/505006-SILVER.png", "GOLD": "/challenges-images/505006-GOLD.png", "PLATINUM": "/challenges-images/505006-PLATINUM.png", "DIAMOND": "/challenges-images/505006-DIAMOND.png", "MASTER": "/challenges-images/505006-MASTER.png", "GRANDMASTER": "/challenges-images/505006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 505007, "name": "Invulnerable", "description": "Dapatkan champion tank", "shortDescription": "Dapatkan <em>champion tank</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505007-IRON.png", "BRONZE": "/challenges-images/505007-BRONZE.png", "SILVER": "/challenges-images/505007-SILVER.png", "GOLD": "/challenges-images/505007-GOLD.png", "PLATINUM": "/challenges-images/505007-PLATINUM.png", "DIAMOND": "/challenges-images/505007-DIAMOND.png", "MASTER": "/challenges-images/505007-MASTER.png", "CHALLENGER": "/challenges-images/505007-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 30}, "MASTER": {"value": 40}}}, {"id": 510000, "name": "Connoisseur", "description": "Raih progres dari tantangan di grup Connoisseur", "shortDescription": "Raih progres dari tantangan di grup Connoisseur", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510000-IRON.png", "BRONZE": "/challenges-images/510000-BRONZE.png", "SILVER": "/challenges-images/510000-SILVER.png", "GOLD": "/challenges-images/510000-GOLD.png", "PLATINUM": "/challenges-images/510000-PLATINUM.png", "DIAMOND": "/challenges-images/510000-DIAMOND.png", "MASTER": "/challenges-images/510000-MASTER.png", "GRANDMASTER": "/challenges-images/510000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 70}, "GOLD": {"value": 150}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 430}, "MASTER": {"value": 725, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Connoisseur"}]}}}, {"id": 510001, "name": "Fashion Forward", "description": "Dapatkan skin champion", "shortDescription": "Kumpulkan skin champion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510001-IRON.png", "BRONZE": "/challenges-images/510001-BRONZE.png", "SILVER": "/challenges-images/510001-SILVER.png", "GOLD": "/challenges-images/510001-GOLD.png", "PLATINUM": "/challenges-images/510001-PLATINUM.png", "DIAMOND": "/challenges-images/510001-DIAMOND.png", "MASTER": "/challenges-images/510001-MASTER.png", "GRANDMASTER": "/challenges-images/510001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 30}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 200}, "MASTER": {"value": 500}}}, {"id": 510003, "name": "That Drip", "description": "Kumpulkan banyak skin untuk satu champion", "shortDescription": "Kumpulkan banyak skin untuk satu champion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510003-IRON.png", "BRONZE": "/challenges-images/510003-BRONZE.png", "SILVER": "/challenges-images/510003-SILVER.png", "GOLD": "/challenges-images/510003-GOLD.png", "PLATINUM": "/challenges-images/510003-PLATINUM.png", "DIAMOND": "/challenges-images/510003-DIAMOND.png", "MASTER": "/challenges-images/510003-MASTER.png", "GRANDMASTER": "/challenges-images/510003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 7}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}}}, {"id": 510004, "name": "Need a Bigger Closet", "description": "Kumpulkan 5 atau lebih skin untuk satu champion", "shortDescription": "Kumpulkan 5 atau lebih skin untuk satu <em>champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510004-IRON.png", "BRONZE": "/challenges-images/510004-BRONZE.png", "SILVER": "/challenges-images/510004-SILVER.png", "GOLD": "/challenges-images/510004-GOLD.png", "PLATINUM": "/challenges-images/510004-PLATINUM.png", "DIAMOND": "/challenges-images/510004-DIAMOND.png", "MASTER": "/challenges-images/510004-MASTER.png", "GRANDMASTER": "/challenges-images/510004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 50}, "MASTER": {"value": 75}}}, {"id": 510005, "name": "Vintage Look", "description": "Kumpulkan skin champion legacy", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin champion legacy</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510005-IRON.png", "BRONZE": "/challenges-images/510005-BRONZE.png", "SILVER": "/challenges-images/510005-SILVER.png", "GOLD": "/challenges-images/510005-GOLD.png", "PLATINUM": "/challenges-images/510005-PLATINUM.png", "DIAMOND": "/challenges-images/510005-DIAMOND.png", "MASTER": "/challenges-images/510005-MASTER.png", "GRANDMASTER": "/challenges-images/510005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 100}, "MASTER": {"value": 200}}}, {"id": 510006, "name": "Victor<PERSON> Regalia", "description": "Kumpulkan skin champion victorious", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin champion victorious</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510006-IRON.png", "BRONZE": "/challenges-images/510006-BRONZE.png", "SILVER": "/challenges-images/510006-SILVER.png", "GOLD": "/challenges-images/510006-GOLD.png", "PLATINUM": "/challenges-images/510006-PLATINUM.png", "DIAMOND": "/challenges-images/510006-DIAMOND.png", "MASTER": "/challenges-images/510006-MASTER.png", "GRANDMASTER": "/challenges-images/510006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510006-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Elegant"}]}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 4}, "MASTER": {"value": 5}}}, {"id": 510007, "name": "Haute Couture", "description": "Kumpulkan skin champion ultimate", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin champion ultimate</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510007-IRON.png", "BRONZE": "/challenges-images/510007-BRONZE.png", "SILVER": "/challenges-images/510007-SILVER.png", "GOLD": "/challenges-images/510007-GOLD.png", "PLATINUM": "/challenges-images/510007-PLATINUM.png", "DIAMOND": "/challenges-images/510007-DIAMOND.png", "MASTER": "/challenges-images/510007-MASTER.png", "GRANDMASTER": "/challenges-images/510007-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510007-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 6}, "MASTER": {"value": 7}}}, {"id": 510008, "name": "Formal Attire", "description": "Kumpulkan skin champion mythic", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin champion mythic</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510008-IRON.png", "BRONZE": "/challenges-images/510008-BRONZE.png", "SILVER": "/challenges-images/510008-SILVER.png", "GOLD": "/challenges-images/510008-GOLD.png", "PLATINUM": "/challenges-images/510008-PLATINUM.png", "DIAMOND": "/challenges-images/510008-DIAMOND.png", "MASTER": "/challenges-images/510008-MASTER.png", "GRANDMASTER": "/challenges-images/510008-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510008-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 25}}}, {"id": 510009, "name": "Legendary <PERSON>", "description": "Kumpulkan skin champion legendary", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin champion legendary</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510009-IRON.png", "BRONZE": "/challenges-images/510009-BRONZE.png", "SILVER": "/challenges-images/510009-SILVER.png", "GOLD": "/challenges-images/510009-GOLD.png", "PLATINUM": "/challenges-images/510009-PLATINUM.png", "DIAMOND": "/challenges-images/510009-DIAMOND.png", "MASTER": "/challenges-images/510009-MASTER.png", "GRANDMASTER": "/challenges-images/510009-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510009-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 12}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Trendsetter"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 45}}}, {"id": 510010, "name": "<PERSON><PERSON>", "description": "Kumpulkan skin champion epic", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin champion epic</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510010-IRON.png", "BRONZE": "/challenges-images/510010-BRONZE.png", "SILVER": "/challenges-images/510010-SILVER.png", "GOLD": "/challenges-images/510010-GOLD.png", "PLATINUM": "/challenges-images/510010-PLATINUM.png", "DIAMOND": "/challenges-images/510010-DIAMOND.png", "MASTER": "/challenges-images/510010-MASTER.png", "GRANDMASTER": "/challenges-images/510010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 175}, "MASTER": {"value": 250}}}, {"id": 510011, "name": "Fashionista", "description": "Kumpulkan chroma", "shortDescription": "Kumpulkan chroma", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510011-IRON.png", "BRONZE": "/challenges-images/510011-BRONZE.png", "SILVER": "/challenges-images/510011-SILVER.png", "GOLD": "/challenges-images/510011-GOLD.png", "PLATINUM": "/challenges-images/510011-PLATINUM.png", "DIAMOND": "/challenges-images/510011-DIAMOND.png", "MASTER": "/challenges-images/510011-MASTER.png", "GRANDMASTER": "/challenges-images/510011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510011-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Fashionista"}]}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}}}, {"id": 510012, "name": "Everything but the Kitchen Sink", "description": "Kumpulkan skin mana pun yang bukan <PERSON>, <PERSON><PERSON>, Ultimate, Mythic, atau Legendary", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> <em>skin mana pun yang bukan <PERSON>, <PERSON><PERSON>, Ultimate, Mythic, atau Legendary</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510010-IRON.png", "BRONZE": "/challenges-images/510010-BRONZE.png", "SILVER": "/challenges-images/510010-SILVER.png", "GOLD": "/challenges-images/510010-GOLD.png", "PLATINUM": "/challenges-images/510010-PLATINUM.png", "DIAMOND": "/challenges-images/510010-DIAMOND.png", "MASTER": "/challenges-images/510010-MASTER.png", "GRANDMASTER": "/challenges-images/510010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 175}, "MASTER": {"value": 250}}}, {"id": 600006, "name": "Been Here a While", "description": "Tingkatkan level summoner-mu", "shortDescription": "Tingkatkan level summoner-mu", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/600006-IRON.png", "BRONZE": "/challenges-images/600006-BRONZE.png", "SILVER": "/challenges-images/600006-SILVER.png", "GOLD": "/challenges-images/600006-GOLD.png", "PLATINUM": "/challenges-images/600006-PLATINUM.png", "DIAMOND": "/challenges-images/600006-DIAMOND.png", "MASTER": "/challenges-images/600006-MASTER.png", "GRANDMASTER": "/challenges-images/600006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 65}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 250}, "MASTER": {"value": 350}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 600010, "name": "First to the Top", "description": "Capai Challenger pertama di regional dalam Ranked Solo/Duo pada split season", "shortDescription": "Capai Challenger pertama di regional dalam Ranked Solo/Duo pada split season", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/600010-IRON.png", "BRONZE": "/challenges-images/600010-BRONZE.png", "SILVER": "/challenges-images/600010-SILVER.png", "GOLD": "/challenges-images/600010-GOLD.png", "PLATINUM": "/challenges-images/600010-PLATINUM.png", "DIAMOND": "/challenges-images/600010-DIAMOND.png", "MASTER": "/challenges-images/600010-MASTER.png", "GRANDMASTER": "/challenges-images/600010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600010-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 1}, "CHALLENGER": {"value": 1}}}, {"id": 600011, "name": "Best of the Best", "description": "Selesaikan split season apa pun dengan menempati Rank 1 untuk Ranked Solo/Duo", "shortDescription": "Selesaikan split season dengan menempati Rank 1 untuk Solo/Duo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/600011-IRON.png", "BRONZE": "/challenges-images/600011-BRONZE.png", "SILVER": "/challenges-images/600011-SILVER.png", "GOLD": "/challenges-images/600011-GOLD.png", "PLATINUM": "/challenges-images/600011-PLATINUM.png", "DIAMOND": "/challenges-images/600011-DIAMOND.png", "MASTER": "/challenges-images/600011-MASTER.png", "GRANDMASTER": "/challenges-images/600011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600011-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 600012, "name": "Challenges are Here!", "description": "<PERSON>ai kamu belum tahu. Kami yakin kamu pasti tahu. <PERSON><PERSON>ng kamu tahu. Apakah kamu tahu?", "shortDescription": "<PERSON><PERSON><PERSON> pelun<PERSON><PERSON>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/600012-IRON.png", "BRONZE": "/challenges-images/600012-BRONZE.png", "SILVER": "/challenges-images/600012-SILVER.png", "GOLD": "/challenges-images/600012-GOLD.png", "PLATINUM": "/challenges-images/600012-PLATINUM.png", "DIAMOND": "/challenges-images/600012-DIAMOND.png", "MASTER": "/challenges-images/600012-MASTER.png", "GRANDMASTER": "/challenges-images/600012-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600012-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 0}}}, {"id": 601000, "name": "Arena Brawler", "description": "Raih progres dari tantangan di grup Arena Brawler", "shortDescription": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601000-IRON.png", "BRONZE": "/challenges-images/601000-BRONZE.png", "SILVER": "/challenges-images/601000-SILVER.png", "GOLD": "/challenges-images/601000-GOLD.png", "PLATINUM": "/challenges-images/601000-PLATINUM.png", "DIAMOND": "/challenges-images/601000-DIAMOND.png", "MASTER": "/challenges-images/601000-MASTER.png", "GRANDMASTER": "/challenges-images/601000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 45}, "GOLD": {"value": 95}, "PLATINUM": {"value": 145, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Arena Brawler"}]}, "DIAMOND": {"value": 250}, "MASTER": {"value": 400}}}, {"id": 601001, "name": "Cream of the Crop", "description": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game", "shortDescription": "<PERSON><PERSON><PERSON> paling banyak damage pada champion dalam game", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601001-IRON.png", "BRONZE": "/challenges-images/601001-BRONZE.png", "SILVER": "/challenges-images/601001-SILVER.png", "GOLD": "/challenges-images/601001-GOLD.png", "PLATINUM": "/challenges-images/601001-PLATINUM.png", "DIAMOND": "/challenges-images/601001-DIAMOND.png", "MASTER": "/challenges-images/601001-MASTER.png", "GRANDMASTER": "/challenges-images/601001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 25}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 60}, "CHALLENGER": {"value": 90}}}, {"id": 601002, "name": "Dancing Shoes", "description": "<PERSON><PERSON><PERSON> lima skillshot (ability ranged tak terarah) dalam waktu delapan detik", "shortDescription": "Dodge lima tembakan terarah dalam waktu delapan detik", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601002-IRON.png", "BRONZE": "/challenges-images/601002-BRONZE.png", "SILVER": "/challenges-images/601002-SILVER.png", "GOLD": "/challenges-images/601002-GOLD.png", "PLATINUM": "/challenges-images/601002-PLATINUM.png", "DIAMOND": "/challenges-images/601002-DIAMOND.png", "MASTER": "/challenges-images/601002-MASTER.png", "GRANDMASTER": "/challenges-images/601002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 10}, "SILVER": {"value": 18}, "GOLD": {"value": 32}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 90}, "MASTER": {"value": 140}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 601003, "name": "Double Down", "description": "<PERSON><PERSON><PERSON> double kill", "shortDescription": "<PERSON><PERSON><PERSON> double kill", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601003-IRON.png", "BRONZE": "/challenges-images/601003-BRONZE.png", "SILVER": "/challenges-images/601003-SILVER.png", "GOLD": "/challenges-images/601003-GOLD.png", "PLATINUM": "/challenges-images/601003-PLATINUM.png", "DIAMOND": "/challenges-images/601003-DIAMOND.png", "MASTER": "/challenges-images/601003-MASTER.png", "GRANDMASTER": "/challenges-images/601003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 14}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 28}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 65}, "CHALLENGER": {"value": 100}}}, {"id": 601004, "name": "The Wall", "description": "Terima 10000 damage pramitigasi dari champion dalam satu combat tanpa mati", "shortDescription": "Terima 10 ribu damage dalam pertarungan dan be<PERSON>han hidup", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601004-IRON.png", "BRONZE": "/challenges-images/601004-BRONZE.png", "SILVER": "/challenges-images/601004-SILVER.png", "GOLD": "/challenges-images/601004-GOLD.png", "PLATINUM": "/challenges-images/601004-PLATINUM.png", "DIAMOND": "/challenges-images/601004-DIAMOND.png", "MASTER": "/challenges-images/601004-MASTER.png", "GRANDMASTER": "/challenges-images/601004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601004-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 9}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 24}, "MASTER": {"value": 36}, "GRANDMASTER": {"value": 50}, "CHALLENGER": {"value": 70}}}, {"id": 601005, "name": "Outplaying the Odds", "description": "<PERSON><PERSON>kan kill saat lebih banyak champion musuh daripada champion seku<PERSON> di sekitar", "shortDescription": "<PERSON><PERSON><PERSON> kill saat kalah jumlah", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601005-IRON.png", "BRONZE": "/challenges-images/601005-BRONZE.png", "SILVER": "/challenges-images/601005-SILVER.png", "GOLD": "/challenges-images/601005-GOLD.png", "PLATINUM": "/challenges-images/601005-PLATINUM.png", "DIAMOND": "/challenges-images/601005-DIAMOND.png", "MASTER": "/challenges-images/601005-MASTER.png", "GRANDMASTER": "/challenges-images/601005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 14}, "DIAMOND": {"value": 22}, "MASTER": {"value": 35}, "GRANDMASTER": {"value": 50}, "CHALLENGER": {"value": 70}}}, {"id": 601006, "name": "I've Got You!", "description": "Selamatkan sekutu yang hampir menerima damage mematikan dengan heal atau shield", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> sekutu dengan heal atau shield", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601006-IRON.png", "BRONZE": "/challenges-images/601006-BRONZE.png", "SILVER": "/challenges-images/601006-SILVER.png", "GOLD": "/challenges-images/601006-GOLD.png", "PLATINUM": "/challenges-images/601006-PLATINUM.png", "DIAMOND": "/challenges-images/601006-DIAMOND.png", "MASTER": "/challenges-images/601006-MASTER.png", "GRANDMASTER": "/challenges-images/601006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}, "GRANDMASTER": {"value": 90}, "CHALLENGER": {"value": 120}}}, {"id": 602000, "name": "Arena Champion", "description": "Raih progres dari tantangan di grup Arena Champion", "shortDescription": "Raih progres dari tantangan di grup Arena Champion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/602000-IRON.png", "BRONZE": "/challenges-images/602000-BRONZE.png", "SILVER": "/challenges-images/602000-SILVER.png", "GOLD": "/challenges-images/602000-GOLD.png", "PLATINUM": "/challenges-images/602000-PLATINUM.png", "DIAMOND": "/challenges-images/602000-DIAMOND.png", "MASTER": "/challenges-images/602000-MASTER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 35}, "PLATINUM": {"value": 55, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Arena Champion"}]}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}}}, {"id": 602001, "name": "Arena Champion Ocean", "description": "Mainkan game Arena dengan berbagai champion", "shortDescription": "Mainkan game Arena dengan <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/602001-IRON.png", "BRONZE": "/challenges-images/602001-BRONZE.png", "SILVER": "/challenges-images/602001-SILVER.png", "GOLD": "/challenges-images/602001-GOLD.png", "PLATINUM": "/challenges-images/602001-PLATINUM.png", "DIAMOND": "/challenges-images/602001-DIAMOND.png", "MASTER": "/challenges-images/602001-MASTER.png"}, "thresholds": {"IRON": {"value": 8}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 55}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 135}, "MASTER": {"value": 168}}}, {"id": 602002, "name": "Adapt to All Situations", "description": "Raih posisi pertama dalam game Arena dengan berbagai champion", "shortDescription": "Raih posisi pertama dalam game Arena dengan <em>berbagai champion</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/602002-IRON.png", "BRONZE": "/challenges-images/602002-BRONZE.png", "SILVER": "/challenges-images/602002-SILVER.png", "GOLD": "/challenges-images/602002-GOLD.png", "PLATINUM": "/challenges-images/602002-PLATINUM.png", "DIAMOND": "/challenges-images/602002-DIAMOND.png", "MASTER": "/challenges-images/602002-MASTER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 32}, "DIAMOND": {"value": 45}, "MASTER": {"value": 60, "rewards": [{"category": "TITLE", "quantity": 0, "title": "Arena God"}]}}}, {"id": 603000, "name": "Swarm Survivor", "description": "Lencan<PERSON> penghargaan Anima Squad atas jasa selama Event Swarm 2024", "shortDescription": "Lencana penghargaan Anima Squad untuk Swarm 2024", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/603000-IRON.png", "BRONZE": "/challenges-images/603000-BRONZE.png", "SILVER": "/challenges-images/603000-SILVER.png", "GOLD": "/challenges-images/603000-GOLD.png", "PLATINUM": "/challenges-images/603000-PLATINUM.png", "DIAMOND": "/challenges-images/603000-DIAMOND.png", "MASTER": "/challenges-images/603000-MASTER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 40}, "GOLD": {"value": 70}, "PLATINUM": {"value": 120, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Swarm Survivor"}]}, "DIAMOND": {"value": 200}, "MASTER": {"value": 340}}}, {"id": 603001, "name": "Memancing Gold", "description": "Ambil 1.000 Gold", "shortDescription": "Ambil 1.000 Gold", "hasLeaderboard": false, "levelToIconPath": {"GOLD": "/challenges-images/603001-GOLD.png"}, "thresholds": {"GOLD": {"value": 1000}}}, {"id": 603002, "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "shortDescription": "<PERSON><PERSON><PERSON>", "hasLeaderboard": false, "levelToIconPath": {"SILVER": "/challenges-images/603002-SILVER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 603003, "name": "<PERSON><PERSON><PERSON>", "description": "Memiliki 3 Pasif se<PERSON>us", "shortDescription": "Memiliki 3 Pasif se<PERSON>us", "hasLeaderboard": false, "levelToIconPath": {"BRONZE": "/challenges-images/603003-BRONZE.png"}, "thresholds": {"BRONZE": {"value": 1}}}, {"id": 603004, "name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>", "description": "E<PERSON><PERSON>i senjata apa pun", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> apa pun", "hasLeaderboard": false, "levelToIconPath": {"SILVER": "/challenges-images/603004-SILVER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 603005, "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> 15 Menit", "shortDescription": "<PERSON><PERSON> 15 Menit", "hasLeaderboard": false, "levelToIconPath": {"SILVER": "/challenges-images/603005-SILVER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 603006, "name": "<PERSON><PERSON>bihan Level, Bertindak Berlebihan", "description": "Capai Level 30 dengan champion apa saja", "shortDescription": "Capai Level 30 dengan champion apa saja", "hasLeaderboard": false, "levelToIconPath": {"PLATINUM": "/challenges-images/603006-PLATINUM.png"}, "thresholds": {"PLATINUM": {"value": 1}}}, {"id": 603007, "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> musuh", "shortDescription": "<PERSON><PERSON><PERSON> musuh", "hasLeaderboard": false, "levelToIconPath": {"BRONZE": "/challenges-images/603007-BRONZE.png", "SILVER": "/challenges-images/603007-SILVER.png", "GOLD": "/challenges-images/603007-GOLD.png", "PLATINUM": "/challenges-images/603007-PLATINUM.png", "DIAMOND": "/challenges-images/603007-DIAMOND.png"}, "thresholds": {"BRONZE": {"value": 5000}, "SILVER": {"value": 10000}, "GOLD": {"value": 30000}, "PLATINUM": {"value": 60000}, "DIAMOND": {"value": 100000}}}, {"id": 603008, "name": "Berburu Ikan <PERSON>", "description": "Kalahkan 10 miniboss pada kesulitan 2+", "shortDescription": "Kalahkan 10 miniboss pada kesulitan 2+", "hasLeaderboard": false, "levelToIconPath": {"PLATINUM": "/challenges-images/603008-PLATINUM.png"}, "thresholds": {"PLATINUM": {"value": 10}}}, {"id": 603009, "name": "Perlengka<PERSON>", "description": "Dapatkan 5+ evolusi senjata pada kesulitan 3", "shortDescription": "Dapatkan 5+ evolusi senjata pada kesulitan 3", "hasLeaderboard": false, "levelToIconPath": {"DIAMOND": "/challenges-images/603009-DIAMOND.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 603010, "name": "<PERSON><PERSON>", "description": "Kalahkan Aatrox pada kesulitan 3", "shortDescription": "Kalahkan Aatrox pada kesulitan 3", "hasLeaderboard": false, "levelToIconPath": {"DIAMOND": "/challenges-images/603010-DIAMOND.png"}, "thresholds": {"DIAMOND": {"value": 1}}}]