{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Singed": {"id": "Singed", "key": "27", "name": "シンジド", "title": "マッドケミスト", "image": {"full": "Singed.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "27000", "num": 0, "name": "default", "chromas": false}, {"id": "27001", "num": 1, "name": "Riot小隊シンジド", "chromas": false}, {"id": "27002", "num": 2, "name": "ヘクステック シンジド", "chromas": false}, {"id": "27003", "num": 3, "name": "波乗りシンジド", "chromas": false}, {"id": "27004", "num": 4, "name": "狂気の化学者シンジド", "chromas": false}, {"id": "27005", "num": 5, "name": "強化人間シンジド", "chromas": false}, {"id": "27006", "num": 6, "name": "雪遊びシンジド", "chromas": false}, {"id": "27007", "num": 7, "name": "SSW シンジド", "chromas": false}, {"id": "27008", "num": 8, "name": "黒の災厄シンジド", "chromas": false}, {"id": "27009", "num": 9, "name": "はちみつシンジド", "chromas": false}, {"id": "27010", "num": 10, "name": "レジスタンス シンジド", "chromas": true}, {"id": "27019", "num": 19, "name": "宇宙飛行士シンジド", "chromas": true}, {"id": "27028", "num": 28, "name": "Arcane シマーラボ シンジド", "chromas": false}], "lore": "シンジドは優れた錬金術師だが、倫理観が欠如した存在でもあり、その実験は極めて残忍な犯罪者ですら吐き気をもよおすほどだ。彼は最も高い報酬を提示した者にその技術を売り、自らの有害な調合物がいかに使用されようがろくに関心を払わず、それがもたらす混沌すらをも実験の一環として見ているふしがある。彼が生み出したものの中でも最も悪名高いのが「シマー」であり、これによってケミ長者たちはゾウンを彼らの遊び場へと変貌させることとなった。それでもシンジドは狂気に突き動かされ、常に新しいなにかに取り組んでいる。堕落の一途をたどりながら…", "blurb": "シンジドは優れた錬金術師だが、倫理観が欠如した存在でもあり、その実験は極めて残忍な犯罪者ですら吐き気をもよおすほどだ。彼は最も高い報酬を提示した者にその技術を売り、自らの有害な調合物がいかに使用されようがろくに関心を払わず、それがもたらす混沌すらをも実験の一環として見ているふしがある。彼が生み出したものの中でも最も悪名高いのが「シマー」であり、これによってケミ長者たちはゾウンを彼らの遊び場へと変貌させることとなった。それでもシンジドは狂気に突き動かされ、常に新しいなにかに取り組んでいる。堕落の一途をた...", "allytips": ["「毒の軌跡」はミニオンをまとめて倒したり、敵チャンピオンをけん制するのに極めて有効なスキル。シンジドがどのレーンにいようと戦いの主導権を掌握できる。", "「狂人のポーション」を使ってわざと敵に追いかけさせ「毒の軌跡」の射程内へと誘い込もう、敵は追いかけているだけで体力を消耗していく。", "敵を自陣のタワーに向かって「すくい投げ」で放り投げると、大ダメージを与えられる。立ち位置や投げる方向を気をつけよう。"], "enemytips": ["シンジドに近づきすぎるのは危険だ。敵チームのチャンピオンたちが待ち構えているほうへ投げ飛ばされる恐れがある。", "シンジドのスキルは近接攻撃に偏っているため、この特性を逆手にとって、シンジドをスロウ効果やスタン効果で足止めしながら、周囲にいる敵チャンピオンを攻撃しよう。", "シンジドを追跡する時は注意すること。簡単に倒せる相手ではないし彼の残す「毒の軌跡」のダメージはとても危険だ。"], "tags": ["Tank", "Mage"], "partype": "マナ", "info": {"attack": 4, "defense": 8, "magic": 7, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 96, "mp": 330, "mpperlevel": 45, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9.5, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.9, "attackspeed": 0.7}, "spells": [{"id": "PoisonTrail", "name": "毒の軌跡", "description": "背中から毒ガスをまき散らし、ガスに接触した敵にダメージを与える。", "tooltip": "<toggle>発動中:</toggle> 自分が通った後に毒ガスをまき散らし、接触した敵に毎秒<magicDamage>{{ damagepersecond }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [13, 13, 13, 13, 13], "costBurn": "13", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "PoisonTrail.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "毎秒{{ cost }}マナ"}, {"id": "MegaAdhesive", "name": "強力粘着剤", "description": "強力な粘着剤の入ったビンを地面に投げ、接触した敵にスロウ効果を与えて釘付けにする。", "tooltip": "粘着剤の入った容器を投げ、{{ wduration }}秒間、範囲内の敵に<status>釘付け効果</status>と{{ slowpercent }}%の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["スロウ効果", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "MegaAdhesive.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Fling", "name": "すくい投げ", "description": "対象の敵ユニットにダメージを与え、シンジドの背後へ投げ飛ばす。<br>「強力粘着剤」の上に着地した場合は、スネア状態になる。", "tooltip": "敵を背後に放り投げ、<magicDamage>{{ basedamage }}(+最大体力の{{ e3 }}%)の魔法ダメージ</magicDamage>を与える。<br /><br />対象を<spellName>「強力粘着剤」</spellName>の範囲内に投げると、{{ e2 }}秒間の<status>スネア効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "最大体力ダメージ", "スネア効果時間", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [50, 60, 70, 80, 90], [1, 1.25, 1.5, 1.75, 2], [6, 6.5, 7, 7.5, 8], [420, 420, 420, 420, 420], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/60/70/80/90", "1/1.25/1.5/1.75/2", "6/6.5/7/7.5/8", "420", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "Fling.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "InsanityPotion", "name": "狂人のポーション", "description": "強力な調合薬を飲んで戦闘能力が一時的に強化され、「毒の軌跡」が「重傷」を付与するようになる。", "tooltip": "強力な調合薬を飲み、<scaleAP>魔力</scaleAP>、<scaleArmor>物理防御</scaleArmor>、<scaleMR>魔法防御</scaleMR>、<speed>移動速度</speed>、<healing>体力自動回復</healing>、<scaleMana>マナ自動回復</scaleMana>が{{ duration }}秒間、{{ statamount }}増加する。この効果時間中は<spellName>「毒の軌跡」</spellName>が、{{ grievousduration }}秒間{{ grievousamount*100 }}%の「重傷」を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ステータス増加"], "effect": ["{{ statamount }} -> {{ statamountNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "InsanityPotion.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "スリップストリーム", "description": "周囲のチャンピオンを利用して空気抵抗を減らし、通り過ぎる際に一時的に移動速度が増加する。", "image": {"full": "Singed_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}