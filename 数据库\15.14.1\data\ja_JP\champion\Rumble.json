{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rumble": {"id": "Rumble", "key": "68", "name": "ランブル", "title": "戦慄の機甲兵", "image": {"full": "Rumble.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "68000", "num": 0, "name": "default", "chromas": false}, {"id": "68001", "num": 1, "name": "ランブル＆ジャングル", "chromas": false}, {"id": "68002", "num": 2, "name": "なりきり船長ランブル", "chromas": false}, {"id": "68003", "num": 3, "name": "超銀河ランブル", "chromas": false}, {"id": "68004", "num": 4, "name": "荒くれバロン ランブル", "chromas": true}, {"id": "68013", "num": 13, "name": "スペースグルーヴ ランブル", "chromas": true}, {"id": "68023", "num": 23, "name": "カフェキューティーズ ランブル", "chromas": true}], "lore": "ランブルは若くて気性の荒い発明家だ。この気骨のあるヨードルは、ガラクタの山を使って、たった一人の力で電撃ハープーンと焼夷ロケット弾を搭載した巨大なメカスーツを作り出した。廃品置き場で作り出された彼の発明品を冷笑する者がいても、ランブルは気にしない──いざとなれば、火炎放射器で黙らせてやればいいだけだ。", "blurb": "ランブルは若くて気性の荒い発明家だ。この気骨のあるヨードルは、ガラクタの山を使って、たった一人の力で電撃ハープーンと焼夷ロケット弾を搭載した巨大なメカスーツを作り出した。廃品置き場で作り出された彼の発明品を冷笑する者がいても、ランブルは気にしない──いざとなれば、火炎放射器で黙らせてやればいいだけだ。", "allytips": ["スキルを乱発するとランブルはたちまち「オーバーヒート」してしまう。「デンジャーゾーン」を維持しながら効率よく戦おう。", "「スピットファイア」は対象に持続して大ダメージを与えることのできる、強力なスキルだ。絶対に射程から逃がさないこと。", "敵に止めを刺せそうな時に、アルティメットスキルで敵の退路を断つように発射するのは極めて有効かつ素晴らしい作戦だ。"], "enemytips": ["「デンジャーゾーン」の追加効果は強力なのでランブルのヒートゲージから目を離さないように気をつけよう。「オーバーヒート」してスキルが封じられている間が、一気に攻勢をかけるチャンスだ。", "ランブルのアルティメットスキルは範囲内の敵に大ダメージを与えるため、ミサイルが投下されたら急いで避難すること。", "ランブルの攻撃の大半は魔法ダメージであるため、魔法防御を強化してダメージを軽減するのがいいだろう。"], "tags": ["Fighter", "Mage"], "partype": "ヒート", "info": {"attack": 3, "defense": 6, "magic": 8, "difficulty": 10}, "stats": {"hp": 655, "hpperlevel": 105, "mp": 150, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 1.55, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.85, "attackspeed": 0.644}, "spells": [{"id": "RumbleFlameThrower", "name": "スピットファイア", "description": "扇状の範囲を3秒間にわたって焼き払い魔法ダメージを与える。「デンジャーゾーン」突入時はダメージが増加する。", "tooltip": "火炎放射器から火を放ち、{{ flamespitterduration }}秒かけて<magicDamage>{{ flatdamage }} + 最大体力の{{ healthdamage*100 }}%の魔法ダメージ</magicDamage>を与える。ミニオンに対してはダメージが<attention>{{ minionmod*100 }}%</attention>に低下する。<br /><br /><keywordMajor>デンジャーゾーン:</keywordMajor> ダメージが<magicDamage>{{ empowereddamage }} + 最大体力の{{ empoweredhealth }}</magicDamage>に増加する。<br /><br /><rules>最大体力に応じたダメージは、モンスターには{{ monstercap }}ダメージが上限となる。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "最大体力割合", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ healthdamage*100.000000 }}% -> {{ healthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " ヒート", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RumbleFlameThrower.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ initialheatcost }} ヒート"}, {"id": "RumbleShield", "name": "ジャンクシールド", "description": "シールドを発生させてダメージを防ぎ、さらに移動速度が一瞬増加する。「デンジャーゾーン」突入時はシールド耐久値と、増加移動速度が増加する。", "tooltip": "バリア装置を起動して、{{ shieldduration.1 }}秒間<shield>耐久値{{ totalshield }}のシールド</shield>を獲得し、{{ movespeedduration }}秒間<speed>移動速度が{{ movespeed*100 }}%</speed>増加する。<br /><br /><keywordMajor>デンジャーゾーン:</keywordMajor> 代わりに<shield>耐久値{{ empoweredshield }}のシールド</shield>を獲得し、<speed>移動速度が{{ empoweredms }}</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "移動速度"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [10, 15, 20, 25, 30], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "10/15/20/25/30", "20", "0", "1.5", "1", "0", "0", "0", "0"], "vars": [], "costType": " ヒート", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "RumbleShield.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ heatcost }} ヒート"}, {"id": "RumbleGrenade", "name": "エレクトロハープーン", "description": "銛を発射し、対象を感電させて魔法ダメージとスロウ効果を与え、魔法防御を低下させる。2発まで発射できる。「デンジャーゾーン」突入時はダメージとスロウ効果が増加する。", "tooltip": "帯電した銛を発射して<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ baseslowamount }}%の<status>スロウ効果</status>を付与し、{{ shredduration }}秒間敵の<scaleMR>魔法防御</scaleMR>を{{ percmagicpen*100 }}%低下させる。<br /><br />このスキルによる<status>スロウ効果</status>を受けている敵に命中すると、<status>スロウ効果</status>が{{ empoweredslowamount }}%に増加し、敵の<scaleMR>魔法防御</scaleMR>を{{ enhancedmagicpen*100 }}%低下させる。<br /><br /><keywordMajor>デンジャーゾーン:</keywordMajor> 銛が<magicDamage>{{ empdamage }}の魔法ダメージ</magicDamage>を与え、<status>スロウ効果</status>と<scaleMR>魔法防御</scaleMR>低下効果が50%増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "魔法防御低下量"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslowamount }}% -> {{ baseslowamountNL }}%", "{{ percmagicpen*100.000000 }}% -> {{ percmagicpennl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " ヒート", "maxammo": "2", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RumbleGrenade.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ firstcastheatcost }} ヒート"}, {"id": "RumbleCarpetBomb", "name": "イコライザー", "description": "複数のロケット弾を投下し、その地点を炎上させて敵にダメージとスロウ効果を与える。", "tooltip": "ロケットを一列に発射して着弾地点を炎上させ、{{ slowamount }}%の<status>スロウ効果</status>を付与し、{{ trailduration }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />このスキルは使用中にクリックしてドラッグすると、ロケットの着弾方向を変えられる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ", "クールダウン"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [1750, 1750, 1750], "rangeBurn": "1750", "image": {"full": "RumbleCarpetBomb.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "ポンコツタイタン", "description": "スキルを使用するたび、ヒートが溜まっていく。ヒートゲージが50%に達すると「デンジャーゾーン」に突入し、すべての通常スキルに追加効果が付与される。100%に達すると「オーバーヒート」し、攻撃速度が増加して通常攻撃に追加ダメージがつくが、数秒間スキルを使えなくなる。", "image": {"full": "Rumble_JunkyardTitan1.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}