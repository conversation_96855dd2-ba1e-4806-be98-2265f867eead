{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON>", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "<PERSON><PERSON><PERSON> p<PERSON>on", "chromas": false}, {"id": "32002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32005", "num": 5, "name": "<PERSON><PERSON><PERSON> quasi-roi du bal", "chromas": false}, {"id": "32006", "num": 6, "name": "Petit chevalier <PERSON><PERSON>", "chromas": false}, {"id": "32007", "num": 7, "name": "<PERSON><PERSON><PERSON> triste <PERSON>", "chromas": false}, {"id": "32008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "32017", "num": 17, "name": "Amumu infernal", "chromas": true}, {"id": "32023", "num": 23, "name": "Amumu Hextech", "chromas": false}, {"id": "32024", "num": 24, "name": "<PERSON><PERSON><PERSON> prince des citrouilles", "chromas": true}, {"id": "32034", "num": 34, "name": "<PERSON><PERSON><PERSON> de porcelaine", "chromas": true}, {"id": "32044", "num": 44, "name": "<PERSON><PERSON>u cœur brisé", "chromas": true}, {"id": "32053", "num": 53, "name": "<PERSON><PERSON><PERSON> ravioles ador<PERSON>", "chromas": true}], "lore": "La légende veut qu'Amumu soit une âme solitaire et mélancolique de la Shurima antique et qu'il parcoure le monde à la recherche d'un ami. Condamné par une malédiction à rester seul à jamais, il provoque la mort et la ruine à chaque geste d'affection. Ceux qui prétendent l'avoir vu le décrivent comme un cadavre vivant, petit de taille, enveloppé dans d'effrayants bandages. Il a inspiré bien des mythes, des chansons et des légendes, transmis de génération en génération pendant si longtemps qu'il est désormais impossible de démêler le vrai du faux.", "blurb": "La légende veut qu'Amumu soit une âme solitaire et mélancolique de la Shurima antique et qu'il parcoure le monde à la recherche d'un ami. Condamné par une malédiction à rester seul à jamais, il provoque la mort et la ruine à chaque geste d'affection...", "allytips": ["<PERSON><PERSON><PERSON> dépend beaucoup de ses équipiers ; essayez de rester à proximité de vos amis pour plus d'efficacité.", "Réduire les délais de récupération est une stratégie gagnante, mais difficile à mettre en œuvre. Profitez de l'enchantement de la sentinelle bleue autant que possible pour réduire les délais de récupération sans sacrifier d'autres stats.", "Désespoir est très efficace contre d'autres tanks, si vous êtes à portée des champions adverses ayant beaucoup de PV."], "enemytips": ["N'approchez pas de vos alliés quand l'ultime d'Amumu est disponible.", "Il est difficile pour Amumu d'attaquer avec Jet de bandelette si vous vous déplacez erratiquement ou si vous vous cachez derrière des vagues de sbires.", "Désespoir rend dangereux l'achat d'objets augmentant les PV."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "Jet de bandelette", "description": "Amumu jette une bandelette collante qui étourdit et blesse l'ennemi touché tandis qu'Amumu s'approche de lui.", "tooltip": "Amumu lance une bandelette dans une direction, se tractant jusqu'au premier ennemi touché, l'<status>étourdissant</status> pendant {{ e2 }} sec et lui infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>.<br /><br />Cette compétence a 2 charges.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> de rechargement", "Coût en @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "Désespoir", "description": "Saisis par l'angoisse, les ennemis proches perdent chaque seconde un pourcentage de leurs PV max et leurs <font color='#9b0f5f'>Malédictions</font> sont actualisées.", "tooltip": "<toggle>Activable/Désactivable :</toggle> Amumu commence à pleurer, infligeant aux ennemis proches <magicDamage>{{ basedamage }} plus {{ totalhealthdamage }}% des PV max en dégâts magiques</magicDamage> chaque seconde et réinitialisant <keywordMajor>Malédiction</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% des PV en dégâts"], "effect": ["{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }} par sec", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }} par sec"}, {"id": "Tantrum", "name": "<PERSON><PERSON>", "description": "Les dégâts physiques subis par Amumu sont réduits. Amumu peut libérer sa rage pour infliger des dégâts aux ennemis proches. Chaque fois qu'Amumu est touché, le délai de récupération de Colère est réduit.", "tooltip": "<spellPassive>Passive :</spellPassive> Amumu subit des dégâts physiques réduits de {{ damagereduction }}. De plus, quand Amumu est touché par une attaque, le délai de récupération de cette compétence est réduit de {{ e3 }} sec.<br /><br /><spellActive>Active :</spellActive> Amumu pique une colère, infligeant <magicDamage>{{ tantrumdamage }} pts de dégâts magiques</magicDamage> aux ennemis proches.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts réduits", "<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "Malédiction d'Amumu", "description": "Amumu emmêle les ennemis proches dans des bandelettes, appliquant sa <keywordMajor>Malédiction</keywordMajor>, leur infligeant des dégâts et les étourdissant.", "tooltip": "Amumu répand ses bandelettes autour de lui, <status>étourdissant</status> les ennemis pendant {{ rduration }} sec, leur infligeant <magicDamage>{{ rcalculateddamage }} pts de dégâts magiques</magicDamage> et appliquant <keywordMajor>Malédiction</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Coût en @AbilityResourceName@", "<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamage }} -> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Toucher maudit", "description": "Les attaques de base d'Amumu <font color='#9b0f5f'>maudissent</font> ses ennemis, leur faisant subir des dégâts bruts supplémentaires chaque fois qu'ils subissent des dégâts magiques.", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}