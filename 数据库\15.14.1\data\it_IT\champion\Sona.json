{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sona": {"id": "<PERSON><PERSON>", "key": "37", "name": "<PERSON><PERSON>", "title": "maestra delle corde", "image": {"full": "Sona.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "37000", "num": 0, "name": "default", "chromas": false}, {"id": "37001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "37002", "num": 2, "name": "Sona dei Pentakill", "chromas": false}, {"id": "37003", "num": 3, "name": "Sona Astro del Ciel", "chromas": false}, {"id": "37004", "num": 4, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "37005", "num": 5, "name": "Sona Arcade", "chromas": true}, {"id": "37006", "num": 6, "name": "DJ <PERSON><PERSON>", "chromas": false}, {"id": "37007", "num": 7, "name": "Sona del Cuore", "chromas": false}, {"id": "37009", "num": 9, "name": "Sona dell'Odissea", "chromas": true}, {"id": "37017", "num": 17, "name": "Sona OPSI", "chromas": true}, {"id": "37026", "num": 26, "name": "Sona Pentakill III: Lost Chapter", "chromas": true}, {"id": "37035", "num": 35, "name": "Sona Guardian<PERSON>", "chromas": true}, {"id": "37045", "num": 45, "name": "Sona Viaggio Immortale", "chromas": true}, {"id": "37046", "num": 46, "name": "Sona Viaggio Immortale (edizione prestigio)", "chromas": false}, {"id": "37056", "num": 56, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Sona è la più grande suonatrice di etwahl di Demacia e si esprime solo attraverso la sua musica. Questa sua grazia l'ha resa beneamata tra i nobili, sebbene altri sospettino che le sue melodie siano in realtà magiche... e la magia è vietata a Demacia. Sempre silenziosa e compresa solo da chi le è vicino, <PERSON><PERSON>, grazie alla sua musica, non soltanto cura i compagni feriti, ma uccide anche gli ignari nemici.", "blurb": "Sona è la più grande suonatrice di etwahl di Demacia e si esprime solo attraverso la sua musica. Questa sua grazia l'ha resa beneamata tra i nobili, sebbene altri sospettino che le sue melodie siano in realtà magiche... e la magia è vietata a Demacia...", "allytips": ["Accertati di marcare gli alleati mentre le aure di Sona sono attive, ma non farti prendere dai nemici.", "Conserva Crescendo per un momento decisivo per le sorti della partita.", "Usare Aria della perseveranza con un buon tempismo ti permetterà di sopravvivere il più a lungo possibile."], "enemytips": ["Dividetevi quando vedete Sona: in questo modo non potrà far ballare tutta la tua squadra.", "Uccidi Sona per prima, in modo che non possa guarire la squadra avversaria."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 2, "magic": 8, "difficulty": 4}, "stats": {"hp": 550, "hpperlevel": 91, "mp": 340, "mpperlevel": 45, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.3, "attackspeed": 0.644}, "spells": [{"id": "SonaQ", "name": "Inno al valore", "description": "Sona suona l'Inno al valore, sparando colpi sonori che infliggono danni magici a due nemici nelle vicinanze, con priorità a campioni e mostri. Sona ottiene un'aura temporanea che conferisce agli alleati toccati dalla zona danni bonus per il prossimo attacco contro i nemici.", "tooltip": "Sona infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai due nemici più vicini, dando la priorità ai campioni. Poi comincia con una nuova <keywordMajor>Melodia</keywordMajor>. Ottieni una carica di <keywordMajor>Accelerazione</keywordMajor> per ogni campione che danneggi in questo modo.<br /><br /><keywordMajor>Melodia:</keywordMajor> Sona ottiene un'aura per {{ auraduration }} secondi, conferendo ai campioni alleati ulteriori <magicDamage>{{ totalonhitdamage }} danni magici</magicDamage> %i:OnHit% al prossimo attacco entro {{ onhitduration }} secondi.<br /><br /><keywordMajor>Accordo di potenza - staccato:</keywordMajor> danni bonus Accordo di potenza (<magicDamage>{{ totalstaccatodamage }} danni magici totali</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> (attivi)", "<PERSON><PERSON> (melodia)", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "SonaQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaW", "name": "Aria della perseveranza", "description": "Sona suona l'Aria della perseveranza, producendo melodie protettive che curano lei e un alleato ferito nelle vicinanze.  Sona ottiene un'aura temporanea che conferisce agli alleati toccati dalla zona uno scudo temporaneo.", "tooltip": "<spellPassive>Attiva:</spellPassive> Sona ripristina <healing>{{ totalheal }} salute</healing> a se stessa e a un campione alleato vicino, dando la priorità ai più feriti. Poi comincia una nuova <keywordMajor>Melodia</keywordMajor>.<br /><br /><keywordMajor>Melodia:</keywordMajor> Sona ottiene un'aura per {{ auraduration }} secondi che conferisce ai campioni alleati <shield>{{ totalshield }} scudo </shield> per {{ shieldduration }} secondi.<br /><br />Ottieni una carica di <keywordMajor>Accelerazione</keywordMajor> quando curi un alleato e ogni volta che proteggi un alleato da almeno {{ accelerandoshieldbreakpoint }} danni con questo scudo.<br /><br /><keywordMajor>Accordo di potenza: diminuendo:</keywordMajor> Accordo di potenza riduce anche i danni fisici e magici inflitti dal bersaglio di {{ totaldiminuendoweakenpercent }} per {{ diminuendoduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione (attiva)", "Scudo (melodia)", "Costo in @AbilityResourceName@"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SonaW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaE", "name": "Ode alla celerità", "description": "Sona suona l'Ode alla celerità, conferendo velocità di movimento bonus agli alleati nelle vicinanze. Sona ottiene un'aura temporanea, che conferisce ai campioni alleati, vicini e toccati dalla zona velocità di movimento.", "tooltip": "<spellPassive>Attiva:</spellPassive> Inizia una nuova <keywordMajor>Melodia</keywordMajor>, conferendo a se stessa <speed>{{ totalselfmovementspeed }} velocità di movimento</speed> per {{ selfmovementspeeddurationmin }} secondi, che aumentano fino a {{ selfmovementspeeddurationmax }} secondi se non subisce danni. <br /><br /><keywordMajor>Melodia:</keywordMajor> Sona ottiene un'aura per {{ auraduration }} secondi che conferisce <speed>{{ totalallymovementspeed }} velocità di movimento</speed> ai campioni alleati per {{ allymovementspeedduration }} secondi.<br /><br /><keywordMajor>Accordo di potenza: tempo:</keywordMajor> Accordo di potenza <status>rallenta</status> il bersaglio di {{ totaltempomovespeedslow }} per {{ tempoduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento"], "effect": ["{{ allybasemovementspeed*100.000000 }}% -> {{ allybasemovementspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "SonaE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaR", "name": "Crescendo", "description": "Sona suona il suo accordo supremo, stordendo i campioni nemici, obbligandoli a ballare e a subire danni magici.", "tooltip": "Sona suona un accordo irresistibile, <status>stordendo</status> il nemico per {{ stunduration }} secondi e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "SonaR.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Accordo di potenza", "description": "<passive>Accelerazione</passive>: Sona ottiene velocità d'abilità permanentemente per le sue abilità di base quando usa al meglio le sue abilità, fino a un valore massimo. Oltre quel valore, ulteriori utilizzi positivi riducono la ricarica della sua suprema.<br><br><passive>A<PERSON><PERSON> di potenza</passive>: dopo qualche lancio di abilità, il prossimo attacco di Sona infligge danni magici bonus e ha un effetto aggiuntivo basato sull'ultima abilità di base che ha attivato.", "image": {"full": "Sona_Passive_Charged.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}