{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "<PERSON><PERSON><PERSON>", "title": "the Deathsinger", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "30002", "num": 2, "name": "Statue of Karthus", "chromas": false}, {"id": "30003", "num": 3, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "30004", "num": 4, "name": "Penta<PERSON>", "chromas": false}, {"id": "30005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30010", "num": 10, "name": "Infernal Karthus", "chromas": true}, {"id": "30017", "num": 17, "name": "Pentakill III: Lost Chapter Karthus", "chromas": true}, {"id": "30026", "num": 26, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "The harbinger of oblivion, <PERSON><PERSON><PERSON> is an undying spirit whose haunting songs are a prelude to the horror of his nightmarish appearance. The living fear the eternity of undeath, but <PERSON><PERSON><PERSON> sees only beauty and purity in its embrace, a perfect union of life and death. When <PERSON><PERSON><PERSON> emerges from the Shadow Isles, it is to bring the joy of death to mortals, an apostle of the unliving.", "blurb": "The harbinger of oblivion, <PERSON><PERSON><PERSON> is an undying spirit whose haunting songs are a prelude to the horror of his nightmarish appearance. The living fear the eternity of undeath, but <PERSON><PERSON><PERSON> sees only beauty and purity in its embrace, a perfect union of...", "allytips": ["Ask your allies to help point out when you should use Requiem to get kills in different lanes.", "Lay Waste is very strong at farming minions and harassing enemy champions."], "enemytips": ["<PERSON><PERSON><PERSON> can cast spells for a short duration after he is killed. Move away from his corpse to be safe.", "Make sure you always have enough health to survive <PERSON>, even if you have to go back to the base more frequently to heal."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "Lay Waste", "description": "<PERSON><PERSON><PERSON> unleashes a delayed blast at a location, dealing damage to nearby enemies. Deals increased damage to isolated enemies. ", "tooltip": "<PERSON><PERSON><PERSON> creates a blast of magic, dealing <magicDamage>{{ qdamage }} magic damage</magicDamage>. If the blast hits only one enemy, it instead deals <magicDamage>{{ qsingletargetdamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "Wall of Pain", "description": "<PERSON><PERSON><PERSON> creates a passable screen of leeching energy. Any enemy units that walk through the screen have their Move Speed and Magic Resist reduced for a period.", "tooltip": "<PERSON><PERSON><PERSON> creates a wall that lasts for {{ e4 }} seconds. Enemies that pass through lose <scaleMR>{{ e1 }}% Magic Resist</scaleMR> for {{ e5 }} seconds and are <status>Slowed</status> by {{ e3 }}% decaying over the duration.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>", "Move Speed Slow"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> passively steals energy from his victims, gaining <PERSON><PERSON> on each kill. Alternatively, <PERSON><PERSON><PERSON> can surround himself in the souls of his prey, dealing damage to nearby enemies, but quickly draining his own <PERSON><PERSON>.", "tooltip": "<spellPassive>Passive: </spellPassive>When <PERSON><PERSON><PERSON> kills a unit, he restores <scaleMana>{{ e2 }} <PERSON>a</scaleMana>.<br /><br /><toggle>Toggle: </toggle><PERSON><PERSON><PERSON> creates a necrotic aura, dealing <magicDamage>{{ totaldps }} magic damage</magicDamage> per second to nearby enemies.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per Second", "<PERSON><PERSON>", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Mana per Second", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} Mana per Second"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "Requiem", "description": "After channeling for 3 seconds, <PERSON><PERSON><PERSON> deals damage to all enemy champions.", "tooltip": "<PERSON><PERSON><PERSON> channels for 3 seconds, then deals <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemy champions, regardless of distance.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Death Defied", "description": "Upon dying, <PERSON><PERSON><PERSON> enters a spirit form that allows him to continue casting spells.", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}