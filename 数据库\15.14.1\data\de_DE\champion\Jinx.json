{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jinx": {"id": "<PERSON><PERSON>", "key": "222", "name": "<PERSON><PERSON>", "title": "die Schießwütige", "image": {"full": "Jinx.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "222000", "num": 0, "name": "default", "chromas": false}, {"id": "222001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "222002", "num": 2, "name": "Feuerwerks-Jinx", "chromas": true}, {"id": "222003", "num": 3, "name": "Zombieschlächter-Jinx", "chromas": false}, {"id": "222004", "num": 4, "name": "Sternenwächt<PERSON><PERSON>", "chromas": true}, {"id": "222012", "num": 12, "name": "Ehrgeizige Weihnachtselfe Jinx", "chromas": false}, {"id": "222013", "num": 13, "name": "Odyssee-Jinx", "chromas": true}, {"id": "222020", "num": 20, "name": "PROJEKT: <PERSON><PERSON>", "chromas": true}, {"id": "222029", "num": 29, "name": "<PERSON>z<PERSON>cher-Jinx", "chromas": true}, {"id": "222037", "num": 37, "name": "<PERSON><PERSON><PERSON><PERSON> (Arcane)", "chromas": false}, {"id": "222038", "num": 38, "name": "Battle Cat Jinx", "chromas": true}, {"id": "222040", "num": 40, "name": "Battle Cat <PERSON> (Prestige)", "chromas": false}, {"id": "222051", "num": 51, "name": "Konditorei-Jinx", "chromas": true}, {"id": "222060", "num": 60, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Arcane)", "chromas": false}, {"id": "222062", "num": 62, "name": "T1-<PERSON><PERSON>", "chromas": false}], "lore": "Jinx ist eine unberechenbare und impulsive Kriminelle aus der Unterstadt, verf<PERSON><PERSON> von den Konsequenzen ihrer früheren Handlungen. Dies hält sie jedoch nicht davon ab, für ihre eigene Art von Chaos auf den Straßen von Piltover und <PERSON><PERSON><PERSON> zu sorgen. Dabei setzt sie ihr Arsenal an selbstentwickelten Waffen mit verheerender Wirkung ein, entfesselt farbenfrohe Explosionen und stachelt mit dem Durcheinander, das sie überall hinterlässt, die Entrechteten zu Rebellion und Widerstand an.", "blurb": "<PERSON>x ist eine unberechenbare und impulsive Kriminelle aus der Unterstadt, verf<PERSON><PERSON> von den Konsequenzen ihrer früheren Handlungen. Dies hält sie jedoch nicht davon ab, für ihre eigene Art von Chaos auf den Straßen von Piltover und <PERSON><PERSON><PERSON> zu sorgen. Dabei...", "allytips": ["Raketen sind nicht immer die beste Wahl! Jinx' Minigun ist unglaublich mächtig, wenn sie vollständig aufgeladen ist. We<PERSON><PERSON> auf sie, wann immer ein gegnerischer Champion dir zu nahe kommt.", "Jinx' Raketen verursachen während der Explosion den vollen Schaden an allen Gegnern. Nutze sie gegen Vasallen in der Lane, um gegnerische Champions in der Nähe zu treffen, ohne die Vasallen auf dich zu lenken.", "<PERSON>n ein Ka<PERSON> ausbricht, soll<PERSON> du versuchen, am Rande der Auseinandersetzung zu bleiben, indem du mit Raketen und „Brzl!“ arbeitest. Renne nicht rein und vergeude deine aufgeladene Minigun, bis du nicht das Gefühl hast, dass es sicher ist."], "enemytips": ["Jinx' <PERSON><PERSON> braucht etwas Zeit, sich aufzuladen. <PERSON> du siehst, wie sie mit <PERSON>keten herum<PERSON>, vers<PERSON>, auf sie drauf zu springen und sie schnell umzupusten.", "Jinx' ultimative Fähigkeit verursacht weniger Schaden, je näher du an ihr dran bist.", "Jinx' Verlangsamungsgranaten haben eine lange Abklingzeit und sind ihr primäres Mittel, sich selbst zu schützen. Falls sie ihr Z<PERSON> verfehlen, hat sie es schwer zu entkommen, wenn sie angegriffen wird."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 4, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 105, "mp": 260, "mpperlevel": 50, "movespeed": 325, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 3.75, "hpregenperlevel": 0.5, "mpregen": 6.7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.25, "attackspeedperlevel": 1.4, "attackspeed": 0.625}, "spells": [{"id": "JinxQ", "name": "Waffenwechsel!", "description": "<PERSON><PERSON> kann für ihre normalen Angriffe zwischen Peng-Peng, ihr<PERSON>gun, und Gräte, ihr<PERSON>, wechseln. Angriffe mit Peng-Peng gewähren Angriffstempo, während Angriffe mit Gräte Flächenschaden verursachen und eine erhöhte Reichweite gewähren, dabei jedoch Mana verbrauchen und langsamer sind.", "tooltip": "Jinx kann zwischen ihren beiden Waffen „Gräte, der Raketenwerfer“ und „Peng-Peng, die Minigun“ wechseln.<br /><br />Angriffe mit dem Raketenwerfer fügen dem Ziel und Gegnern in der Nähe <physicalDamage>{{ rocketdamage }}&nbsp;normalen Schaden</physicalDamage> zu, erhalten {{ rocketbonusrange }}&nbsp;Reichweite, kosten Mana und skalieren {{ rocketaspdpenalty*100 }}&nbsp;% weniger mit zusätzlichem Angriffstempo.<br /><br />Angriffe mit der Minigun gewähren {{ minigunattackspeedduration }}&nbsp;Sekunden lang <attackSpeed>Angriffstempo</attackSpeed>, das bis zu {{ minigunattackspeedstacks }}&nbsp;-mal steigerbar ist (<attackSpeed>+{{ minigunattackspeedmax }}&nbsp;% %i:scaleAS% max.</attackSpeed>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Raketen: Zusätzliche Reichweite", "Minigun: Gesamtangriffstempo"], "effect": ["{{ rocketbonusrange }} -> {{ rocketbonusrangeNL }}", "{{ minigunattackspeedmax }}&nbsp;% -> {{ minigunattackspeedmaxNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0.9, 0.9, 0.9, 0.9, 0.9], "cooldownBurn": "0.9", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Mana Pro Rakete", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "JinxQ.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} Mana Pro Rakete"}, {"id": "JinxW", "name": "Brzl!", "description": "<PERSON><PERSON>, ihr<PERSON> <PERSON><PERSON>, um einen Sc<PERSON> a<PERSON>, der am zuerst getroffenen Gegner Schaden verursacht, ihn verlangsamt und aufdeckt.", "tooltip": "Jinx feuert einen Schockschuss ab, der dem ersten getroffenen Gegner <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zuf<PERSON><PERSON>, ihn um {{ slowpercent }}&nbsp;% <status>verlangsamt</status> und ihn {{ slowduration }}&nbsp;Sekunden lang aufdeckt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent }}&nbsp;% -> {{ slowpercentNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "JinxW.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxE", "name": "Flammenfresser!", "description": "<PERSON>x wirft eine Reihe Verlangsamungsgranaten, die nach 5 Sekunden explodieren und Gegner in Flammen setzen. Flammenfresser werden gegnerische Champions, die über sie hinweg laufen, er<PERSON>ssen und sie auf der Stelle festhalten.", "tooltip": "Jinx wirft 3&nbsp;<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, die {{ grenadeduration }}&nbsp;Sekunden lang bestehen bleiben. Sie explodieren bei Kontakt mit gegnerischen Champions, die daraufhin {{ rootduration }}&nbsp;Sekunden lang <status>festgehalten</status> werden, und fügen Gegnern in der Nähe <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 20.5, 17, 13.5, 10], "cooldownBurn": "24/20.5/17/13.5/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "JinxE.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JinxR", "name": "Super-Mega-Todesrakete!", "description": "<PERSON>x feuert eine Superrakete über die Karte, die im Flug Schaden dazugewinnt. Die Rakete explodiert, wenn sie mit einem gegnerischen Champion kollidiert, und verursacht dann an diesem und Gegnern um ihn herum Schaden, dessen Höhe von ihrem fehlenden Leben abhängt.", "tooltip": "Jinx feuert eine Rakete ab, die beim ersten getroffenen gegnerischen Champion explodiert und <physicalDamage>normalen Schaden</physicalDamage> in <PERSON>ö<PERSON> von {{ damagefloor }} bis {{ damagemax }} + {{ percentdamage }}&nbsp;% des fehlenden Lebens verursacht (Schaden steigt über die erste Flugsekunde hinweg an). Gegner in der Nähe erleiden {{ aoedamagemult*100 }}&nbsp;% des Schadens.<br /><br /><rules>Monster erleiden maximal {{ monsterexecutemax }}&nbsp;Schaden abhängig vom fehlenden Leben.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mindestschaden", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>en basierend auf fehlendem Leben (%)", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ percentdamage }}&nbsp;% -> {{ percentdamageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [85, 65, 45], "cooldownBurn": "85/65/45", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JinxR.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ich werd' verrückt!", "description": "<PERSON><PERSON> erh<PERSON>lt stark erhöhtes Lauf- und Angriffstempo, wenn sie da<PERSON> hilft, einen gegnerischen Champion, ein episches Dschungelmonster oder ein Gebäude zu töten bzw. zu zerstören.", "image": {"full": "Jinx_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}