{"type": "language", "version": "15.14.1", "data": {"Back": "Kembali", "Continue": "Lanjutkan", "Language": "Bahasa", "ItemInfo": "Info Item", "NextRank_": "Rank Berikutnya:", "Rank_": "Rank:", "PlayingAs": "<PERSON><PERSON><PERSON>", "PlayingAgainst": "<PERSON><PERSON><PERSON>", "CD_": "CD:", "Range": "Range", "Range_": "Range:", "Details_": "Detail:", "PrimaryRole": "<PERSON><PERSON>", "mobileCompanion": "Companion", "mobileForum": "Forum", "mobileFriends": "<PERSON><PERSON>", "mobilePleaseWait": "Silahkan tunggu...", "mobileNews": "Berita", "modeClassic": "Klasik", "modeOdin": "Definitely Not Dominion", "modeAram": "ARAM", "modeTutorial": "Tutorial", "modeOneforall": "FRONTEND_oneforall_game_mode_name", "modeFirstblood": "FRONTEND_firstblood_game_mode_name", "mode6v6": "FRONTEND_6v6_game_mode_name", "modeCof": "FRONTEND_cof_game_mode_name", "Map1": "Summ<PERSON><PERSON>'s Rift", "Map8": "<PERSON>", "Map10": "The Twisted Treeline!", "Map12": "Howling Abyss", "categoryChampion": "Champion", "categoryItem": "<PERSON><PERSON>", "categoryMastery": "Mastery", "categoryRune": "<PERSON><PERSON>", "categorySummoner": "Summoner Spell", "Gold": "Gold", "Level": "Level", "Abilities": "Ability", "ChampionInfo": "Info Champion", "Lore": "Lore", "Stats": "Stat", "Tips": "Tips", "statAbility": "Ability", "statAttack": "Attack", "statDefense": "Defense", "statDifficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "statUtility": "Utility", "Assassin": "Assassin", "Fighter": "Fighter", "Marksman": "Marksman", "Mage": "Mage", "Support": "Support", "Tank": "Tank", "spells_Self": "<PERSON><PERSON>", "spells_target_0": "<PERSON><PERSON>", "spells_target_1": "Target", "spells_target_2": "Area", "spells_target_3": "Kerucut", "spells_target_4": "Area Diri Sendiri", "spells_target_5": "Variable", "spells_target_6": "<PERSON><PERSON>", "spells_target_7": "<PERSON><PERSON>", "spells_target_8": "<PERSON><PERSON>", "spells_target_100": "Global", "AllItems": "<PERSON><PERSON><PERSON>", "Armor": "Armor", "Attack": "Attack", "AttackSpeed": "Attack Speed", "Consumable": "Dapat Dikonsumsi", "CooldownReduction": "Cooldown Reduction", "CriticalStrike": "Critical Strike", "Damage": "Damage", "Defense": "Defense", "Health": "Health", "HealthRegen": "Health Regen", "LifeSteal": "Life Steal", "Magic": "Magic", "Mana": "<PERSON><PERSON>", "ManaRegen": "<PERSON><PERSON>", "Movement": "Movement", "SpellBlock": "Magic Resist", "SpellDamage": "Ability Power", "Boots": "Boot", "NonbootsMovement": "Item <PERSON> Lainnya", "Tenacity": "Tenacity", "SpellVamp": "Spell Vamp", "GoldPer": "Pemasukan Gold", "Slow": "Slow", "Aura": "<PERSON>ra", "Active": "Aktif", "MagicPenetration": "Magic Penetration", "ArmorPenetration": "Armor Penetration", "colloq_Armor": ";armour", "colloq_Attack": ";", "colloq_AttackSpeed": ";as", "colloq_Consumables": ";", "colloq_CriticalStrike": ";", "colloq_Damage": ";ad", "colloq_Defense": ";", "colloq_Health": ";hp", "colloq_HealthRegen": ";hpregen;hp5", "colloq_LifeSteal": ";lifesteal", "colloq_Magic": ";", "colloq_Mana": ";mp", "colloq_ManaRegen": ";mpregen;mp5", "colloq_Movement": ";", "colloq_SpellBlock": ";mr", "colloq_SpellDamage": ";ap", "colloq_Consumable": ";", "colloq_Boots": ";", "colloq_NonbootsMovement": ";", "colloq_CooldownReduction": ";cdr", "colloq_Tenacity": ";", "colloq_SpellVamp": ";spellvamp", "colloq_GoldPer": ";gp10", "colloq_Slow": ";", "colloq_Aura": ";", "colloq_Active": ";", "colloq_MagicPenetration": ";", "colloq_ArmorPenetration": ";", "RecommendedItems": "<PERSON><PERSON> ya<PERSON>", "recommended_starting": "<PERSON><PERSON>", "recommended_essential": "<PERSON><PERSON>", "recommended_offensive": "Item Offensive", "recommended_defensive": "Item Defensive", "recommended_consumables": "Dapat Dikonsumsi", "Require_": "Memerlukan:", "Cost_": "Harga:", "OriginalCost_": "<PERSON><PERSON>:", "SellsFor_": "<PERSON><PERSON><PERSON> untuk: ", "UpgradeCost_": "Biaya untuk Upgrade:", "Builds_": "Upgrade menjadi:", "ButtonBuy": "BELI", "ButtonSell": "JUAL", "SpecialRecipeSmall": "*", "SpecialRecipeLarge": "<PERSON><PERSON><PERSON>", "FlatArmorMod": "Armor", "FlatAttackSpeedMod": "Attack Speed", "FlatBlockMod": "Block", "FlatCritChanceMod": "<PERSON><PERSON>", "FlatCritDamageMod": "Crit Dmg", "FlatEnergyPoolMod": "Energy", "FlatEnergyRegenMod": "Energy Regen / 5", "FlatEXPBonus": "Exp Bonus", "FlatHPPoolMod": "Health", "FlatHPRegenMod": "Health Regen / 5", "FlatMagicDamageMod": "Ability Power", "FlatMovementSpeedMod": "Move Speed", "FlatMPPoolMod": "<PERSON><PERSON>", "FlatMPRegenMod": "Man<PERSON>en / 5", "FlatPhysicalDamageMod": "Physical Dmg", "FlatSpellBlockMod": "Magic Resist", "PercentArmorMod": "Armor %", "PercentAttackSpeedMod": "Attack Speed %", "PercentBlockMod": "Block %", "PercentCritChanceMod": "Crit Chance %", "PercentCritDamageMod": "Crit Dmg %", "PercentDodgeMod": "Dodge %", "PercentEXPBonus": "Exp Bonus %", "PercentHPPoolMod": "Maks Health %", "PercentHPRegenMod": "Health % / 5", "PercentMagicDamageMod": "Maks Ability Power %", "PercentMovementSpeedMod": "Move Speed %", "PercentMPPoolMod": "Maks Mana %", "PercentMPRegenMod": "Mana % / 5", "PercentPhysicalDamageMod": "Physical Dmg %", "PercentSpellBlockMod": "Magic Resist %", "rFlatArmorModPerLevel": "Armor di level 18", "rFlatArmorPenetrationMod": "Armor Pen.", "rFlatArmorPenetrationModPerLevel": "Armor Pen. di level 18", "rFlatCritChanceModPerLevel": "Crit Chance di level 18", "rFlatCritDamageModPerLevel": "Crit Dmg di level 18", "rFlatDodgeMod": "Dodge", "rFlatDodgeModPerLevel": "Dodge di level 18", "rFlatEnergyModPerLevel": "Energy di level 18", "rFlatEnergyRegenModPerLevel": "Energy Regen / 5 di level 18", "rFlatGoldPer10Mod": "Gold per 10", "rFlatHPModPerLevel": "Health di level 18", "rFlatHPRegenModPerLevel": "Health Regen / 5 di level 18", "rFlatMagicDamageModPerLevel": "Ability Power di level 18", "rFlatMagicPenetrationMod": "Magic Pen.", "rFlatMagicPenetrationModPerLevel": "Magic Pen. di level 18", "rFlatMovementSpeedModPerLevel": "Move Speet di level 18", "rFlatMPModPerLevel": "Mana di level 18", "rFlatMPRegenModPerLevel": "Mana Regen / 5 di level 18", "rFlatPhysicalDamageModPerLevel": "Physical Dmg di level 18", "rFlatSpellBlockModPerLevel": "Magic Resist di level 18", "rFlatTimeDeadMod": "Time Dead", "rFlatTimeDeadModPerLevel": "Time Dead di level 18", "rPercentArmorPenetrationMod": "Armor Pen. %", "rPercentArmorPenetrationModPerLevel": "Armor Pen. % di level 18", "rPercentAttackSpeedModPerLevel": "Attack Speed % di level 18", "rPercentCooldownMod": "Cooldown %", "rPercentCooldownModPerLevel": "Cooldown % di level 18", "rPercentMagicPenetrationMod": "Magic Pen. %", "rPercentMagicPenetrationModPerLevel": "Magic Pen. % di level 18", "rPercentMovementSpeedModPerLevel": "Move Speed % di level 18", "rPercentTimeDeadMod": "Time Dead %", "rPercentTimeDeadModPerLevel": "Time Dead % / lvl", "PercentLifeStealMod": "Lifesteal Bonus %", "PercentSpellVampMod": "Spellvamp Bonus %", "masteryFerocity": "Ferocity", "masteryCunning": "<PERSON><PERSON><PERSON>", "masteryResolve": "Resolve", "native_﻿ar": "العربية", "native_bg": "български език", "native_cs": "čeština", "native_de": "De<PERSON>ch", "native_el": "ελληνικά", "native_en": "English", "native_es": "español", "native_fr": "français", "native_hu": "magyar", "native_id": "Bahasa Indonesia", "native_it": "Italiano", "native_ja": "日本語", "native_ko": "조선말", "native_nl": "Nederlands", "native_pl": "język polski", "native_pt": "português", "native_ro": "română", "native_ru": "русский язык", "native_th": "ภาษาไทย", "native_tr": "Türkçe", "native_vi": "Tiếng <PERSON>", "native_zh": "汉语", "native_zh_CN": "简体字", "native_zh_MY": "简体字", "native_zh_TW": "繁體字"}, "tree": {"searchKeyIgnore": "", "searchKeyRemap": []}}