{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "カルマ", "title": "目覚めし者", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "太陽の女神カルマ", "chromas": false}, {"id": "43002", "num": 2, "name": "サクラ カルマ", "chromas": false}, {"id": "43003", "num": 3, "name": "伝統的な衣装カルマ", "chromas": false}, {"id": "43004", "num": 4, "name": "蓮華の騎士団カルマ", "chromas": false}, {"id": "43005", "num": 5, "name": "番人カルマ", "chromas": false}, {"id": "43006", "num": 6, "name": "冬の奇跡カルマ", "chromas": false}, {"id": "43007", "num": 7, "name": "覇者カルマ", "chromas": true}, {"id": "43008", "num": 8, "name": "ダークスター カルマ", "chromas": true}, {"id": "43019", "num": 19, "name": "秩序の光カルマ", "chromas": false}, {"id": "43026", "num": 26, "name": "オデッセイ カルマ", "chromas": false}, {"id": "43027", "num": 27, "name": "滅びのカルマ", "chromas": true}, {"id": "43044", "num": 44, "name": "静逸龍カルマ", "chromas": false}, {"id": "43054", "num": 54, "name": "妖精の女王カルマ", "chromas": false}, {"id": "43061", "num": 61, "name": "地獄の業火カルマ", "chromas": false}, {"id": "43070", "num": 70, "name": "精霊の花祭りカルマ", "chromas": false}], "lore": "カルマは他の誰にも増して、アイオニアの精神性を重んじる伝統を体現する存在だ。彼女は無限に生まれ変わる古代の魂が実体化した存在であり、過去からの記憶をすべて新たな生へと継承するだけでなく、常人には到底理解の及ばない力を授かっている。近年訪れた危機の折には全力で人々を導いた彼女だが、平和と調和を手にするには多大な犠牲を払わなければならない場合があることを知っている──自分自身にとっても、そして何より大切な故郷にとっても。", "blurb": "カルマは他の誰にも増して、アイオニアの精神性を重んじる伝統を体現する存在だ。彼女は無限に生まれ変わる古代の魂が実体化した存在であり、過去からの記憶をすべて新たな生へと継承するだけでなく、常人には到底理解の及ばない力を授かっている。近年訪れた危機の折には全力で人々を導いた彼女だが、平和と調和を手にするには多大な犠牲を払わなければならない場合があることを知っている──自分自身にとっても、そして何より大切な故郷にとっても。", "allytips": ["「寄せ火」は攻撃的にプレイするほど高い効果を得られる固有スキルだ。", "積極的に敵を攻撃して「マントラ」のクールダウンを下げ、常に攻めの姿勢を崩さないようにしよう。", "「魂縛」を発動する際「心炎」で敵ユニットにスロウ効果を付与する、または「激励」で自身の移動速度を上げると、敵ユニットを捕まえやすい。", "「マントラ」を使い惜しみしないこと。特に集団戦では「寄せ火」の効果が最大限に発揮でき「マントラ」のリチャージが容易になる。"], "enemytips": ["カルマのクールダウンは、スキルと通常攻撃が敵チャンピオンに命中するたびに短縮される。簡単に攻撃を当てさせないこと。", "「魂の劫火」は指定エリア内の敵ユニットに追加ダメージを与える。炎の輪に取り囲まれたら、すばやく脱出して大ダメージを回避しよう。", "「魂縛」は敵の攻めを封じ込める強力なスキル。行動不能に陥らないよう距離を取り、スキルの効果が切れたら反撃しよう。"], "tags": ["Mage", "Support"], "partype": "マナ", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "心炎", "description": "精神の炎を解き放ち、敵に命中すると爆発してダメージを与える。<br><br>マントラボーナス: 爆発の威力が増し、さらに対象の足下に力場を発生させて範囲内に時間差でダメージを与える。", "tooltip": "精神の炎を解き放ち、最初に命中した対象と周囲の敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowamount*-100 }}%の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KarmaSpiritBind", "name": "魂縛", "description": "自身と標的を鎖でつなぎダメージを与え、ステルス状態を見破る。効果終了時まで鎖が破壊されなければ、敵はその場でスネア状態となり再びダメージを受ける。<br><br>マントラボーナス: 鎖が強化され自身の体力を回復し、敵に与えるスネア状態の効果時間が延長される。", "tooltip": "自身をチャンピオンまたはジャングルモンスターと鎖で繋ぎ、<magicDamage>{{ initialdamage }}の魔法ダメージ</magicDamage>を与えて{{ tetherduration }}秒間可視化する。鎖が切れなかった場合は対象に<magicDamage>{{ initialdamage }}の魔法ダメージ</magicDamage>を再び与え、{{ rootduration }}秒間の<status>スネア効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スネア効果時間", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "KarmaSolKimShield", "name": "激励", "description": "指定した味方ユニットにシールドを付与し、ダメージから守ると同時に移動速度を増加させる。<br><br>マントラボーナス: 対象からエネルギーが放射され、初期シールドを強化し、周囲にいる味方チャンピオンにも「激励」の効果を付与する。", "tooltip": "{{ shieldduration }}秒間、味方チャンピオンに<shield>耐久値{{ totalshield }}のシールド</shield>を付与し、{{ movespeedduration }}秒間、<speed>移動速度を{{ movespeed*100 }}%</speed>増加させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "マントラ", "description": "カルマが次に使用するスキルを強化し追加効果を付与する。<br>「マントラ」はレベル1から使用でき、スキルポイントを必要としない。", "tooltip": "次の8秒以内に使用するスキルが1回だけ強化される。<br /><li><spellName>心炎</spellName>: 追加で<magicDamage>{{ rqimpactdamage }}の魔法ダメージ</magicDamage>を与えて炎の輪を残す。炎の輪は敵に<status>スロウ効果</status>を付与し、<magicDamage>{{ rqfielddamage }}の魔法ダメージ</magicDamage>を追加で与える。<li><spellName>魂縛</spellName>: 鎖を繋いだ瞬間と、その効果時間の最後に<healing>減少体力の{{ rwhealamount }}</healing>を回復し、{{ rwbonusroot }}秒長く<status>スネア効果</status>を与える。<li><spellName>激励</spellName>: 対象に付与する<shield>シールドの耐久値が{{ rebonusshield }}</shield>増加し、対象の周囲にいる味方にも、<shield>耐久値{{ rebonusshieldarea }}のシールド</shield>と<speed>{{ removespeed*100 }}%の増加移動速度</speed>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「魂の劫火」初撃ダメージ", "「魂の劫火」爆発ダメージ", "「魂の新生」スネア効果延長時間", "「信念の抵抗」シールド量", "クールダウン"], "effect": ["{{ qbonusdamage }} -> {{ qbonusdamageNL }}", "{{ qdetonationdamage }} -> {{ qdetonationdamageNL }}", "{{ rwbonusroot }} -> {{ rwbonusrootNL }}", "{{ ebonusshield }} -> {{ ebonusshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "寄せ火", "description": "自身の攻撃スキルが「マントラ」のクールダウンを短縮する。", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}