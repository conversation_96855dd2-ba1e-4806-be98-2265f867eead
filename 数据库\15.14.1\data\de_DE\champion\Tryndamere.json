{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tryndamere": {"id": "Tryndamere", "key": "23", "name": "Tryndamere", "title": "der Barbarenkönig", "image": {"full": "Tryndamere.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "23000", "num": 0, "name": "default", "chromas": false}, {"id": "23001", "num": 1, "name": "Hochland-Tryndamere", "chromas": false}, {"id": "23002", "num": 2, "name": "König Tryndamere", "chromas": false}, {"id": "23003", "num": 3, "name": "Wikinger-Tryndamere", "chromas": false}, {"id": "23004", "num": 4, "name": "Dämonenklingen-Tryndamere", "chromas": false}, {"id": "23005", "num": 5, "name": "Sultan Tryndamere", "chromas": false}, {"id": "23006", "num": 6, "name": "Kriegsherr Tryndamere", "chromas": false}, {"id": "23007", "num": 7, "name": "Albtraum-Tryndamere", "chromas": false}, {"id": "23008", "num": 8, "name": "Bestienjäger-Tryndamere", "chromas": false}, {"id": "23009", "num": 9, "name": "Chemtech-Tryndamere", "chromas": false}, {"id": "23010", "num": 10, "name": "Blutmond-Tryndamere", "chromas": true}, {"id": "23018", "num": 18, "name": "Flammende Finsternis Tryndamere", "chromas": true}, {"id": "23027", "num": 27, "name": "Siegreicher Tryndamere", "chromas": true}], "lore": "Mit ungezügeltem Zorn und <PERSON>usch schlug sich Tryndamere einst durch Freljord und forderte die größten Krieger des Nordens heraus, um sich auf noch dunklere Tage vorzubereiten, die ihm bevorstanden. Der zornige Barbar hat für die Vernichtung seines Klans lange nach Rache gedürstet, doch kürz<PERSON> hat er sich mit Ashe, einer Kriegsmutter der Avarosa, zusammengetan und bei ihrem Volk ein Zuhause gefunden. Seine fast unmenschliche Stärke und Tapferkeit sind legendär und haben ihm und seinen neuen Verbündeten entgegen aller Schwierigkeiten unzählige Siege beschert.", "blurb": "Mit ungezügeltem Zorn und Rausch schlug sich Tryndamere einst durch Freljord und forderte die größten Krieger des Nordens heraus, um sich auf noch dunklere Tage vorzubereiten, die ihm bevorstanden. Der zornige Barbar hat für die Vernichtung seines Klans...", "allytips": ["Zöger<PERSON> die Verwendung von „Unbändige Wut“ hinaus, um Gegner zu einem unvorsichtigen Angriff zu bewegen und diese trotzdem zu besiegen.", "„Blutdurst“ ist besonders in einer frühen Phase effektiv, um Tryndamere zu heilen. Versuch also, möglichst nichts davon zu verschwenden.", "Falls der Gegner seine Rüstung verstärkt, versuch, Gegenstände wie „Letzter Atemzug“ oder „Youmus Geistklinge“ zu benutzen."], "enemytips": ["<PERSON><PERSON><PERSON>, Tryndamere schon früh zu bedr<PERSON>n, damit er keine Vasallen töten und sich durch „Blutdurst“ heilen kann.", "<PERSON><PERSON><PERSON><PERSON> nicht, dass Tryndamere dich nur verlangsamen kann, wenn du ihm den Rücken zudrehst.", "Tryndamere verursacht größtenteils normalen Schaden. Falls er zu stark wird, denk mal über die Anschaffung eines „Dornenpanzers“ nach."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 5, "magic": 2, "difficulty": 5}, "stats": {"hp": 696, "hpperlevel": 108, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.9, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 4, "attackspeedperlevel": 3.4, "attackspeed": 0.67}, "spells": [{"id": "TryndamereQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Der Nervenkitzel im Kampf dient Tryndamere als Ansporn, we<PERSON><PERSON><PERSON> sich mit der Schwere seiner Verletzungen auch sein Angriffsschaden erhöht. Er kann „Blutdurst“ aktivieren, um seinen Zorn zu verbrauchen und sich zu heilen.", "tooltip": "<spellPassive>Passiv:</spellPassive> Tryndamere dürstet nach Blut und erhält dadurch <scaleAD>{{ flatad }}&nbsp;Angriffsschaden</scaleAD> plus <scaleAD>{{ adperonepercentmissinghp }}</scaleAD> pro 1&nbsp;% seines fehlenden Lebens.<br /><br /><spellActive>Aktiv:</spellActive> Tryndamere verbraucht seinen <keywordMajor>Zorn</keywordMajor> und regeneriert dadurch <healing>{{ baseheal }} plus {{ healperfury }}&nbsp;Leben</healing> pro Zorn.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffsschaden", "Angriffsschaden basierend auf fehlendem Leben (%)", "Heilung", "Heilung pro Zorn"], "effect": ["{{ flatad }} -> {{ flatadNL }}", "{{ adperonepercentmissinghp }} -> {{ adperonepercentmissinghpNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ bonushealperfury }} -> {{ bonushealperfuryNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "TryndamereQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "TryndamereW", "name": "<PERSON><PERSON><PERSON>", "description": "Tryndamere brüllt eine Beleidigung, die den Angriffsschaden naher Champions verringert. <PERSON><PERSON>, die mit dem Rücken zu Tryndamere stehen, bewirkt sie außerdem eine Lauftempoverringerung.", "tooltip": "Tryndamere verhöhnt seinen Gegner und verringert den Angriffsschaden von Champions {{ reductionduration }}&nbsp;Sekunden lang um {{ adreduction*-1 }}. Gegnerische Champions, die Tryndamere den Rücken zuwenden, werden für dieselbe Dauer um {{ slowpotency*-100 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schadensverringerung (normaler Schaden)", "Verlangsamung"], "effect": ["{{ adreduction*-1.000000 }} -> {{ adreductionnl*-1.000000 }}", "{{ slowpotency*-100.000000 }}&nbsp;% -> {{ slowpotencynl*-100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "TryndamereW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "TryndamereE", "name": "Wirbelnde Klinge", "description": "Tryndamere wirbelt zu einer Zieleinheit und fügt dabei allen getroffenen Gegnern Schaden zu.", "tooltip": "Tryndamere wirbelt durch seine <PERSON>eg<PERSON> hindurch, fügt ihnen <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und generiert pro getroffenem Gegner <keywordMajor>{{ nonchampfurygain }}&nbsp;<PERSON><PERSON></keywordMajor>. Wird bei gegnerischen Champions auf <keywordMajor>{{ champfurygain }}&nbsp;Zorn</keywordMajor> erhöht.<br /><br />Wenn Tryndamere kritisch trifft, verringert sich die Abklingzeit dieser Fähigkeit um {{ nonchampcdrefund }}&nbsp;<PERSON><PERSON><PERSON>(n), bei einem kritischen Championtreffer um {{ champcdrefund }}&nbsp;Sekunde(n).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TryndamereE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "UndyingRage", "name": "Unbändige <PERSON>", "description": "Tryndameres Kampfeslust wird so heftig, dass er nicht mehr sterben kann, egal wie schwer er verwundet ist.", "tooltip": "Tryndamere wird {{ e3 }}&nbsp;Sekunden lang unsterblich. In diesem Zeitraum kann sein Leben nicht unter {{ e2 }} fallen und er erhält sofort <keywordMajor>{{ e1 }}&nbsp;<PERSON><PERSON></keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Minimales Leben", "<PERSON><PERSON><PERSON><PERSON> Zorn"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [50, 75, 100], [30, 50, 70], [5, 5, 5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "50/75/100", "30/50/70", "5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "UndyingRage.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Tryndamere sammelt mit jede<PERSON>ffer, kritischen Treffer und Todesstoß weiteren Zorn an. Dieser erhöht passiv seine Chance auf kritische Treffer und kann mit „Blutdurst“ verbraucht werden.", "image": {"full": "Tryndamere_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}