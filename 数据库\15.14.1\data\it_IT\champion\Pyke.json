{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pyke": {"id": "<PERSON><PERSON>", "key": "555", "name": "<PERSON><PERSON>", "title": "lo squartatore del porto insanguinato", "image": {"full": "Pyke.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "555000", "num": 0, "name": "default", "chromas": false}, {"id": "555001", "num": 1, "name": "Pyke Spettro delle Sabbie", "chromas": true}, {"id": "555009", "num": 9, "name": "<PERSON><PERSON> di <PERSON>ue", "chromas": true}, {"id": "555016", "num": 16, "name": "PROGETTO: <PERSON><PERSON>", "chromas": true}, {"id": "555025", "num": 25, "name": "Pyke OPSI", "chromas": true}, {"id": "555034", "num": 34, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "555044", "num": 44, "name": "Pyke Cavaliere cinereo", "chromas": true}, {"id": "555045", "num": 45, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "555053", "num": 53, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "555054", "num": 54, "name": "<PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "555064", "num": 64, "name": "Pyke Notte Inquietante", "chromas": true}, {"id": "555074", "num": 74, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON>yke, un famoso ramponiere dei Moli del sangue di Bilgewater, doveva morire nella pancia di una gigantesca creatura abissale... invece è tornato. Ora si aggira per gli umidi vicoli della sua vecchia città, usando i suoi nuovi doni sovrannaturali per finire in modo rapido e truculento chi si è arricchito sulle spalle degli altri. Una città famosa per la caccia ai mostri ha finalmente un mostro che dà la caccia ai suoi abitanti.", "blurb": "<PERSON><PERSON>, un famoso ramponiere dei Moli del sangue di Bilgewater, doveva morire nella pancia di una gigantesca creatura abissale... invece è tornato. Ora si aggira per gli umidi vicoli della sua vecchia città, usando i suoi nuovi doni sovrannaturali per...", "allytips": ["Pyke è molto fragile, quindi non aver paura di fuggire momentaneamente da uno scontro. Puoi rigenerare una grande quantità di salute con Dono degli affogati quando i nemici non ti vedono.", "Colpire un nemico con la versione mantenuta di Arpionaggio lo tirerà sempre della stessa distanza. Usalo a portata di mischia per tirare i bersagli alle tue spalle.", "La versione non mantenuta di Arpionaggio è molto più veloce e infligge più danni.", "Molte delle tue abilità aggressive sono anche le tue fughe. Cerca di avere sempre un piano per uscire da uno scontro."], "enemytips": ["Pyke rigenera una grande quantità dei danni che subisce dai campioni nemici, ma solo quando non lo vedi!", "Quando <PERSON> è nascosto nelle vicinanze con la sua Immersione spettrale, gli squali ti gireranno intorno.", "Cerca di non stare vicino agli alleati con poca salute. Se Pyke li giustizia con Morte dagli abissi, potresti essere il prossimo della lista."], "tags": ["Support", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 670, "hpperlevel": 110, "mp": 415, "mpperlevel": 50, "movespeed": 330, "armor": 43, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2, "attackspeedperlevel": 2.5, "attackspeed": 0.667}, "spells": [{"id": "PykeQ", "name": "Arpionaggio", "description": "Pyke infilza un nemico davanti a lui o lo tira verso di sé.", "tooltip": "<tap>Tocca:</tap> Pyke colpisce, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al primo nemico colpito (con priorità ai campioni). Il nemico viene <status>rallentato</status> di un {{ slowamount*100 }}% per {{ slowduration }} secondo.<br /><br /><hold>Tieni premuto: </hold>Pyke scaglia il suo arpione, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al primo nemico colpito e <status>attirandolo</status> verso di sé. I nemici vengono poi <status>rallentati</status> di un {{ slowamount*100 }}% per {{ slowduration }} secondo/i.<br /><br />Se Pyke colpisce un campione nemico o la canalizzazione non viene completata, un {{ manarefund*100 }}% del costo in mana viene rimborsato.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PykeQ.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PykeW", "name": "Immersione spettrale", "description": "Pyke entra in Mimesi e ottiene una dose significativa di velocità di movimento che decresce nel tempo.", "tooltip": "<PERSON><PERSON> ottiene <keywordStealth>mimesi</keywordStealth> e <speed>{{ movespeed }}% velocità di movimento</speed> che decresce nell'arco di {{ e0 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0.3, 0.3, 0.3, 0.3, 0.3], [0.03, 0.03, 0.03, 0.03, 0.03], [80, 80, 80, 80, 80], [0.55, 0.55, 0.55, 0.55, 0.55], [8, 8, 8, 8, 8], [0.01, 0.01, 0.01, 0.01, 0.01], [1.15, 1.15, 1.15, 1.15, 1.15], [5, 5, 5, 5, 5]], "effectBurn": [null, "45", "0", "0.3", "0.03", "80", "0.55", "8", "0.01", "1.15", "5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PykeW.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PykeE", "name": "<PERSON><PERSON><PERSON> fantasma", "description": "Pyke scatta e si lascia alle spalle uno spettro che torna da lui, stordendo i campioni nemici che incontra.", "tooltip": "<PERSON>yke scatta, las<PERSON><PERSON><PERSON> alle spalle un fantasma affogato che torna da lui dopo un breve periodo. Il fantasma <status>stordisce</status> per {{ stunduration }} secondi e infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> ai campioni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [100, 150, 200, 250, 300], [1.25, 1.25, 1.25, 1.25, 1.25], [550, 550, 550, 550, 550], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/150/200/250/300", "1.25", "550", "1", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "PykeE.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PykeR", "name": "Morte dagli abissi", "description": "Pyke si teletrasporta e giustizia i nemici con poca salute, cosa che gli permette di lanciare di nuovo questa abilità e fornire oro aggiuntivo a un alleato che lo aiuta.", "tooltip": "Pyke colpisce tutti i campioni nemici in un'area a forma di X, teletrasportandosi verso i bersagli e <danger>giustiziando</danger> quelli al di sotto di <scaleAD>{{ rdamage }}</scaleAD> salute. I campioni sopra la soglia e i non campioni subiscono invece danni fisici pari a un {{ reduceddamage*100 }}% di quel valore (<physicalDamage>{{ reduceddamagefinal }}</physicalDamage>). <br /><br />Quando un campione nemico muore dentro la X, Pyke può <recast>rilanciare</recast> questa abilità gratuitamente entro {{ rrecastduration }} secondi. Se giustizia quel campione, ottiene oro anche l'ultimo alleato che esegue un assist. In caso contrario, ottiene comunque oro come se lo avesse fatto.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "PykeR.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dono degli affogati", "description": "Quando Pyke è nascosto dai nemici si rigenera dei danni subiti di recente dai campioni. Pyke non può ottenere salute massima aggiuntiva da qualsiasi fonte. La converte in attacco fisico bonus.", "image": {"full": "PykePassive.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}