{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Trundle": {"id": "Trundle", "key": "48", "name": "Тран<PERSON>л", "title": "Король троллей", "image": {"full": "Trundle.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "48000", "num": 0, "name": "default", "chromas": false}, {"id": "48001", "num": 1, "name": "Бейсболист Трандл", "chromas": false}, {"id": "48002", "num": 2, "name": "Трандл со свалки", "chromas": false}, {"id": "48003", "num": 3, "name": "Традиционный Трандл", "chromas": false}, {"id": "48004", "num": 4, "name": "Констебль Трандл", "chromas": false}, {"id": "48005", "num": 5, "name": "Разрушитель миров Трандл", "chromas": false}, {"id": "48006", "num": 6, "name": "Убийца драконов Трандл", "chromas": true}, {"id": "48012", "num": 12, "name": "Трандл из Ми<PERSON><PERSON> ужа<PERSON>ов", "chromas": true}, {"id": "48021", "num": 21, "name": "Болельщик Трандл", "chromas": true}], "lore": "Трандл – неуклюжий, но коварный тролль с дурным характером, и даже сам Фрельйорд не может выстоять против его ударов. Он яростно охраняет свои владения и преследует любого, кому хватает глупости нарушить их границы. Его огромная дубина из Истинного льда замораживает врага, а затем пронзает его острыми ледяными столпами. Трандл смеется, наблюдая, как кровь жертвы орошает тундру.", "blurb": "Трандл – неуклюжий, но коварный тролль с дурным характером, и даже сам Фрельйорд не может выстоять против его ударов. Он яростно охраняет свои владения и преследует любого, кому хватает глупости нарушить их границы. Его огромная дубина из Истинного льда...", "allytips": ["Трандл очень опасен в зоне действия своего умения Ледяная земля. Старайтесь затащить врагов в эту зону.", "Умение Порабощение позволяет ослабить выносливого врага или подать союзникам сигнал к атаке цели.", "Умение Укус значительно снижает силу атаки. Старайтесь использовать его на зависящих от нее врагов."], "enemytips": ["Трандл очень силен на замороженной земле. Старайтесь выманить его из зоны действия умения Ледяная земля.", "Постарайтесь как можно быстрее уйти из зоны действия Ледяного столпа, так как он может значительно замедлить вас."], "tags": ["Fighter", "Tank"], "partype": "Мана", "info": {"attack": 7, "defense": 6, "magic": 2, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 110, "mp": 340, "mpperlevel": 45, "movespeed": 350, "armor": 37, "armorperlevel": 3.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 2.9, "attackspeed": 0.67}, "spells": [{"id": "TrundleTrollSmash", "name": "Укус", "description": "Трандл кусает врага, нанося ему урон, ненадолго замедляя и похищая часть его силы атаки.", "tooltip": "Следующая автоатака Трандла наносит <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> и ненадолго <status>замедляет</status> врага на {{ slowamount*100 }}%. После этого Трандл получает <physicalDamage>{{ bonusad }} силы атаки</physicalDamage> на {{ sapdebuffduration }} сек., а цель теряет <physicalDamage>{{ sappedad*-1 }} силы атаки</physicalDamage> на то же время.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Коэффициент урона от силы атаки", "Сила атаки", "Уменьшаемая сила атаки"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ adscaling*100.000000 }}% -> {{ adscalingnl*100.000000 }}%", "{{ bonusad }} -> {{ bonusadNL }}", "{{ sappedad*-1.000000 }} -> {{ sappedadnl*-1.000000 }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "TrundleTrollSmash.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "trundledesecrate", "name": "Ледяная земля", "description": "Трандл замораживает выбранную область. Пока он находится там, его скорость атаки и скорость передвижения увеличены, а действие всех восстанавливающих здоровье эффектов усилено.", "tooltip": "Трандл замораживает область на {{ e4 }} сек. Пока он находится в этой области, его <speed>скорость передвижения</speed> увеличена на <speed>{{ e1 }}%</speed>, <attackSpeed>скорость атаки</attackSpeed> - на <attackSpeed>{{ e2 }}%</attackSpeed>, а эффективность лечения - на {{ e3 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Скорость передвижения", "Скорость атаки", "Перезарядка"], "effect": ["{{ effect1amount*100.000000 }}% -> {{ effect1amountnl*100.000000 }}%", "{{ effect2amount*100.000000 }}% -> {{ effect2amountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [20, 28, 36, 44, 52], [30, 50, 70, 90, 110], [25, 25, 25, 25, 25], [8, 8, 8, 8, 8], [775, 775, 775, 775, 775], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/28/36/44/52", "30/50/70/90/110", "25", "8", "775", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "trundledesecrate.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TrundleCircle", "name": "Ледяной столп", "description": "Трандл создает в выбранной точке ледяной столп, который делает область непроходимой и замедляет всех врагов вокруг.", "tooltip": "Трандл создает ледяной столп на {{ e1 }} сек., слегка <status>отбрасывая</status> врагов, которые находятся прямо в точке его появления. Враги поблизости <status>замедляются</status> на {{ e2 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Замедление"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [21, 19.5, 18, 16.5, 15], "cooldownBurn": "21/19.5/18/16.5/15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [34, 38, 42, 46, 50], [360, 360, 360, 360, 360], [225, 225, 225, 225, 225], [150, 150, 150, 150, 150], [225, 225, 225, 225, 225], [400, 400, 400, 400, 400], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "34/38/42/46/50", "360", "225", "150", "225", "400", "60", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "TrundleCircle.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TrundlePain", "name": "Порабощение", "description": "Трандл похищает часть здоровья, брони и сопротивления магии цели. В течение следующих 4 секунд количество украденного здоровья, брони и сопротивления магии удваивается.", "tooltip": "Трандл вытягивает жизненные силы из вражеского чемпиона, нанося ему <magicDamage>магический урон в размере {{ totalpercenthpdamage }} от максимального запаса здоровья</magicDamage> и похищая {{ armormrshred*100 }}% его <scaleArmor>брони</scaleArmor> и <scaleMR>сопротивления магии</scaleMR> на {{ actualdurationofdrainbuff }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Похищение здоровья", "Перезарядка"], "effect": ["{{ percenthpdamage*100.000000 }}% -> {{ percenthpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "TrundlePain.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Подношение королю", "description": "Каждая смерть врага неподалеку от Трандла восстанавливает его здоровье на долю от максимального запаса здоровья умершего.", "image": {"full": "Trundle_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}