{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gangplank": {"id": "Gangplank", "key": "41", "name": "Gangplank", "title": "die Salzwassergeißel", "image": {"full": "Gangplank.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "41000", "num": 0, "name": "default", "chromas": false}, {"id": "41001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>lank", "chromas": false}, {"id": "41002", "num": 2, "name": "Milizionär Gangplank", "chromas": false}, {"id": "41003", "num": 3, "name": "Seemann-Gangplank", "chromas": false}, {"id": "41004", "num": 4, "name": "Spielzeugsoldat-Gangplank", "chromas": false}, {"id": "41005", "num": 5, "name": "Special Forces Gangplank", "chromas": false}, {"id": "41006", "num": 6, "name": "Sultan <PERSON>", "chromas": false}, {"id": "41007", "num": 7, "name": "Käpt'n Gangplank", "chromas": false}, {"id": "41008", "num": 8, "name": "Schreckensnova-Gangplank", "chromas": true}, {"id": "41014", "num": 14, "name": "Poolparty-Gangplank", "chromas": true}, {"id": "41021", "num": 21, "name": "FPX-Gangplank", "chromas": true}, {"id": "41023", "num": 23, "name": "Gangplank der Verräter", "chromas": true}, {"id": "41033", "num": 33, "name": "PROJEKT: Gangplank", "chromas": true}], "lore": "Der als unberechenbar und brutal geltende, entthronte Räuberkönig Gangplank wird überall auf der Welt gefürchtet. Einst herrschte er über die Hafenstadt Bilgewasser und obwohl seine Herrschaftszeit vorüber ist, glauben viele, dass er dadurch noch gefährlicher geworden ist. Gangplank würde Bilgewasser eher im Blut ertränken, als es jemand anderem zu überlassen. Mit einer Pistole, einem Entersäbel und Pulverfässern gerüstet, ist er entschlossen, sich zurückzuholen, was er verloren hat.", "blurb": "Der als unberechenbar und brutal geltende, entthronte Räuberkönig Gangplank wird überall auf der Welt gefürchtet. Einst herrschte er über die Hafenstadt Bilgewasser und obwohl seine Herrschaftszeit vorüber ist, glauben viele, dass er dadurch noch...", "allytips": ["„Parrrlay“ wendet auch Treffereffekte, wie sie durch „Frosthammer“ oder „Schwarzes Beil“ verursacht werden, an.", "Achte auf Champions mit wenig Leben auf der Karte, dann kann „Kanonensperrfeuer“ zu ihrem überraschenden Ende führen.", "Versuch „Kanonensperrfeuer“ auf den Fluchtweg fliehender Gegner zu platzieren."], "enemytips": ["„Parrrlay“ verursacht sehr viel normalen Schaden. Gegenstände mit Rüstung können gegen einen zu guten Gangplank helfen.", "Sobald Gangplank Stufe 6 erreicht, musst du auf seine ultimative Fähigkeit „Kanonensperrfeuer“ achten, die globale Reichweite hat."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 6, "magic": 4, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 280, "mpperlevel": 60, "movespeed": 345, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 4.2, "attackspeedperlevel": 3.2, "attackspeed": 0.658}, "spells": [{"id": "GangplankQWrapper", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sc<PERSON><PERSON><PERSON>t auf das Ziel und plündert Gold über jede getötete gegnerische Einheit.", "tooltip": "{{Spell_GangplankQWrapper_Tooltip_{{ gamemodeinteger }}}}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Geplündertes Gold", "Geplünderte Silberschlangen", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e5 }} -> {{ e5NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4.5, 4.5, 4.5, 4.5, 4.5], "cooldownBurn": "4.5", "cost": [50, 45, 40, 35, 30], "costBurn": "50/45/40/35/30", "datavalues": {}, "effect": [null, [10, 40, 70, 100, 130], [3, 4, 5, 6, 7], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 5, 6, 7, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/40/70/100/130", "3/4/5/6/7", "0", "500", "4/5/6/7/8", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "GangplankQWrapper.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankW", "name": "<PERSON><PERSON><PERSON> heilen", "description": "Isst Zitrusfrüchte, um Massenkontrolleffekte zu heilen und Leben wiederherzustellen.", "tooltip": "Gangplank nimmt eine große Menge Zitrusfrüchte zu sich. <PERSON><PERSON>ch entfernt er alle <status>kampfunfähig</status> machenden Effekte von sich selbst und stellt <healing>{{ basehealth }} plus {{ e2 }}&nbsp;% seines fehlenden Lebens</healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [45, 70, 95, 120, 145], [13, 13, 13, 13, 13], [0.25, 0.25, 0.25, 0.25, 0.25], [200, 200, 200, 200, 200], [30, 40, 50, 60, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/70/95/120/145", "13", "0.25", "200", "30/40/50/60/70", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GangplankW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankE", "name": "Pulverfass", "description": "Gangplank stellt am Zielort ein Pulverfass auf. Greift er es an, explodiert es und verursacht den Schaden des Angriffs an Gegnern in der Umgebung und verlangsamt sie.", "tooltip": "Gangplank stellt ein Pulverfass auf, das von Gangplank und gegnerischen Champions {{ e5 }}&nbsp;Sekunden lang angegriffen werden kann. Wenn ein Gegner es zerstört, wird das Fass entschärft. Wenn Gangplank es zerstört, explodiert es. Die Explosion <status>verlangsamt</status> Gegner {{ e2 }}&nbsp;Sekunden lang um {{ finalslowamount }}&nbsp;% und verursacht den <physicalDamage>Schaden des Angriffs</physicalDamage>, wobei {{ e0 }}&nbsp;% Rüstung ignoriert werden. Champions erleiden zusätzlich <physicalDamage>{{ e3 }}&nbsp;normalen Schaden</physicalDamage>.<br /><br />Das Fass verliert alle {{ f5 }}&nbsp;Sekunden Leben. Explodiert ein Fass, werden auch andere Fässer mit überlappenden Explosionsradien ausgelöst, aber dasselbe Ziel erleidet höchstens ein Mal Schaden. Durch <spellName>Parrrlay</spellName> ausgelöste Fassexplosionen gewähren zusätzliches Gold für getötete Ziele.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzlicher Schaden gegen Champions", "Maximale Aufladungen", "Verlangsamung", "Wiederaufladungsrate"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ barrelslow }}&nbsp;% -> {{ barrelslowNL }}&nbsp;%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [3, 3, 4, 4, 5], [2, 2, 2, 2, 2], [75, 105, 135, 165, 195], [40, 50, 60, 70, 80], [25, 25, 25, 25, 25], [2, 2, 2, 2, 2], [10, 10, 10, 10, 10], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 40, 40, 40, 40]], "effectBurn": [null, "3/3/4/4/5", "2", "75/105/135/165/195", "40/50/60/70/80", "25", "2", "10", "100", "0.5", "40"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "3", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GangplankE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GangplankR", "name": "Ka<PERSON>ensperrfeuer", "description": "Gangplank signalisiert se<PERSON><PERSON>, ein Gebiet unter Beschuss zu nehmen, verlangsamt Gegner und verursacht Schaden.", "tooltip": "Gangplank signalisiert seine<PERSON>, auf einen beliebigen Ort auf der Karte über {{ zoneduration }}&nbsp;Sekunden hinweg {{ totalwavestooltip }}&nbsp;Kanonenkugelsalven abzufeuern. Jede Welle <status>verlangsamt</status> {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercent }}&nbsp;% und verursacht <magicDamage>{{ onewavedamage }}&nbsp;magischen Schaden</magicDamage>. Maximaler Schaden: {{ totaldamagetooltip }}<br /><br />Diese Fähigkeit kann beim Händler über <spellName>Parrrlay</spellName> aufgewertet werden.<br /><br /><spellName>Trommelfeuer</spellName>: Feuert 6&nbsp;zusätzliche Wellen von Kanonenkugeln ab.<br /><spellName>Tochter des Todes</spellName>: Feuert eine Mega-Kanonenkugel ab, die <trueDamage>{{ deathsdaughterdamage }}&nbsp;absoluten Schaden</trueDamage> verursacht und {{ deathsdaughterslowduration }}&nbsp;Sekunde lang um {{ deathsdaughterslow }}&nbsp;% <status>verlangsamt</status>.<br /><spellName>Moral stärken</spellName>: Verbündete im Kanonensperrfeuer erhalten {{ raisemoralehasteduration }}&nbsp;Sekunden lang <speed>{{ raisemoralehaste }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> pro <PERSON>e", "Abklingzeit"], "effect": ["{{ damageperwave }} -> {{ damageperwaveNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1.5, 0.5, 0.5], [0, 0, 0], [300, 300, 300], [75, 75, 75], [1, 1, 1], [40, 40, 40]], "effectBurn": [null, "0", "0", "0", "0", "1.5/0.5/0.5", "0", "300", "75", "1", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [30000, 30000, 30000], "rangeBurn": "30000", "image": {"full": "GangplankR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Alle paar Sekunden setzt Gangplanks Nahkampftreffer seinen Gegenspieler in Brand.", "image": {"full": "Gangplank_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}