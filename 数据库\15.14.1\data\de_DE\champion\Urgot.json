{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Urgot": {"id": "Urgot", "key": "6", "name": "Urgot", "title": "der Chemtech-Terror", "image": {"full": "Urgot.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "6000", "num": 0, "name": "default", "chromas": false}, {"id": "6001", "num": 1, "name": "<PERSON><PERSON><PERSON>bengegne<PERSON>", "chromas": false}, {"id": "6002", "num": 2, "name": "Fleischer-Urgot", "chromas": false}, {"id": "6003", "num": 3, "name": "Stahlkrieger-Urgot", "chromas": true}, {"id": "6009", "num": 9, "name": "High Noon-Urgot", "chromas": true}, {"id": "6015", "num": 15, "name": "Pyjamawächter-Cosplay-Urgot", "chromas": true}, {"id": "6023", "num": 23, "name": "Schreckensnacht-Urgot", "chromas": true}, {"id": "6032", "num": 32, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Urgot ist ein ehemaliger noxianischer Scharfrichter, der von dem Imperium verraten wurde, für das er so viele hingerichtet hatte. Gezwungenermaßen lernte er in Eisenketten die wahre Bedeutung von Stärke in der Krätzerteufe kennen – einer Gefängnismine tief unter Zhaun. Im Zuge einer Katastrophe, welche die Stadt ins Chaos stürzte, trat er schließlich aus den Schatten hervor und ragt nun drohend über ihrer kriminellen Unterwelt. Er knüpft seine Opfer an genau den Ketten auf, die ihn einst zum Sklaven gemacht hatten, und hat es sich zum Z<PERSON> gesetzt, seine neue Heimat in eine Sinfonie des Schmerzes zu verwandeln, während er die Unwürdigen aus ihrem Antlitz brennt.", "blurb": "Urgot ist ein ehemaliger noxianischer Scharfrichter, der von dem Imperium verraten wurde, für das er so viele hingerichtet hatte. Gezwungenermaßen lernte er in Eisenketten die wahre Bedeutung von Stärke in der Krätzerteufe kennen – einer Gefängnismine...", "allytips": ["Beachte die jeweiligen Abklingzeiten deiner Beine, sie machen einen großen Anteil deines Schadens aus.", "Lande Ätzladung oder Verachtung, um Ziele mit Säuberung automatisch anzuvisieren – dies ist sehr hilfreich, wenn du mehrere Beine schnell hintereinander abfeuern willst.", "Hebe dir Schreckensherrschaft für Gegner auf, die bereits zu schwach sind, um zu überleben. Sie ist besonders gut dafür geeignet, fliehenden Gegnern einen Strich durch die Rechnung zu machen."], "enemytips": ["Urgot verlässt sich vor allem auf Angriffe mit seinen Beinen, die ihre eigenen Abklingzeiten haben und nur dann ausgelöst werden, wenn er in die Richtung eines Beins angreift. Vermeide es, von mehreren getroffen zu werden.", "<PERSON><PERSON><PERSON><PERSON> von „Säuberung“ kann er enormen Schaden verursachen und einstecken, wird jedoch verlang<PERSON>mt, während er schießt.", "Solltest du von „Schreckensherrschaft“ getroffen werden, versuche dich über der Exekutionsschwelle (25&nbsp;% des maximalen Lebens) zu halten, bis der Effekt ausgelaufen ist."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 102, "mp": 340, "mpperlevel": 45, "movespeed": 330, "armor": 36, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 350, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 4, "attackspeedperlevel": 3.75, "attackspeed": 0.625}, "spells": [{"id": "UrgotQ", "name": "Ätzladung", "description": "Urgot feuert eine explosive Ladung an einen Zielort, die normalen Schaden an nahen Gegnern verursacht und sie verlangsamt.", "tooltip": "Urgot feuert eine explosive Ladung, die nahen G<PERSON>nern <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zufügt und sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "UrgotQ.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotW", "name": "Säuberung", "description": "<PERSON>rgo<PERSON> wird langsamer, während er in schneller Folge seine Waffe auf die nächstgelegenen Gegner abfeuert. Priorisiert gegnerische Champions, die Urgot kürzlich mit anderen Fähigkeiten getroffen hat, und löst „Zhauns Fegefeuer“ aus.", "tooltip": "<spellPassive>Passiv:</spellPassive> Urgots andere Fähigkeiten markieren den letzten getroffenen Champion 5&nbsp;Sekunden lang.<br /><br /><spellActive>Aktiv:</spellActive> Urgot feuert seine Kettenwaffe auf den nächstbefindlichen Gegner ab, wobei er markierte Gegner priorisiert. Er greift ihn {{ e8 }}-mal pro Sekunde an und fügt ihm pro Schuss <physicalDamage>{{ damagepershot }}&nbsp;normalen Schaden</physicalDamage> zu. Urgot kann sich bewegen, während er feuert und hat {{ e2 }}&nbsp;% Resistenz gegen <status>Verlangsamung</status>, aber er verliert dabei <speed>{{ e5 }}&nbsp;Lauftempo</speed>.<br /><br />Auf dem höchsten Rang hält diese Fähigkeit unbegrenzt an und ist <toggle>de-/aktivierbar</toggle>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Angriffsschaden pro Schuss", "Kosten (@AbilityResourceName@)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e7 }}&nbsp;% -> {{ e7NL }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 9, 6, 3, 0], "cooldownBurn": "12/9/6/3/0", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [40, 40, 40, 40, 40], [4, 4, 4, 4, 25000], [0, 0, 0, 0, 0], [125, 125, 125, 125, 125], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 23.5, 27, 30.5, 34], [3, 3, 3, 3, 3], [12, 12, 12, 12, 12], [50, 50, 50, 50, 50]], "effectBurn": [null, "30/60/90/120/150", "40", "4/4/4/4/25000", "0", "125", "0.5", "20/23.5/27/30.5/34", "3", "12", "50"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "UrgotW.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotE", "name": "Verachtung", "description": "Urgot stürmt in eine Richtung, schirmt sich vor Schaden ab und mäht alle Gegner um, die keine Champions sind. Wenn er auf einen gegnerischen Champion trifft, hält er an und schleudert ihn aus dem Weg.", "tooltip": "Urgot stürmt nach vorn und erhält {{ eshieldduration }}&nbsp;Sekunden lang einen <shield><PERSON>hil<PERSON></shield> in <PERSON><PERSON><PERSON> von {{ etotalshieldhealth }}. Der erste getroffene Champion wird {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status> und hinter Urgot geschleudert. All<PERSON> Gegner, mit denen Urgot zusammenstößt, erleiden <physicalDamage>{{ edamage }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schildstärke", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ ebasedamage }} -> {{ ebasedamageNL }}", "{{ eshieldbasehealth }} -> {{ eshieldbasehealthNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "UrgotE.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UrgotR", "name": "Schreckensherrschaft", "description": "Urgot feuert einen Chembohrer ab, der den ersten getroffenen gegnerischen Champion aufspießt. Wenn das Leben dieses Champions unter einen bestimmten Wert fällt, befindet Urgot ihn für schwach und kann ihn exekutieren.", "tooltip": "Urgot feuert einen Chembohrer ab, der den ersten getroffenen Champion aufspießt. Dieser erleidet <physicalDamage>{{ rcalculateddamage }}&nbsp;normalen Schaden</physicalDamage> und wird {{ rslowduration }}&nbsp;Sekunden lang pro 1&nbsp;% des fehlenden Lebens um 1&nbsp;% <status>verlangsamt</status> (bis zu {{ rmovespeedmod }}&nbsp;%).<br /><br />Sollte das Leben des aufgespießten Ziels unter {{ rhealththreshold }}&nbsp;% fallen, kann Urgot die Fähigkeit <recast>reaktivieren</recast>, wodurch das Opfer <status>unterdrückt</status> und zu ihm herangezogen wird. Sobald es Urgot erreicht hat, wird es getötet und Gegner in der Nähe werden {{ rfearduration }}&nbsp;Sekunden lang in <status>Furcht</status> versetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "UrgotR.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Urgots normale Angriffe und die Angriffe von „Säuberung“ zünden regelmäßig einen Sprengkörper in seinen Beinen, der normalen Schaden verursacht.", "image": {"full": "Urgot_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}