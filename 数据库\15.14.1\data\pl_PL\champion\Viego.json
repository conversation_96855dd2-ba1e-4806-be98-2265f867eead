{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viego": {"id": "Viego", "key": "234", "name": "Viego", "title": "Zniszczony Król", "image": {"full": "Viego.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "234000", "num": 0, "name": "default", "chromas": false}, {"id": "234001", "num": 1, "name": "Księżycowa Bestia Viego", "chromas": true}, {"id": "234010", "num": 10, "name": "Viego Dysharmonia z Pentakill", "chromas": false}, {"id": "234019", "num": 19, "name": "EDG Viego", "chromas": false}, {"id": "234021", "num": 21, "name": "Król Viego", "chromas": false}, {"id": "234030", "num": 30, "name": "Duchowy Wojownik Viego", "chromas": false}, {"id": "234037", "num": 37, "name": "Viego Mistrzostw 2024", "chromas": false}], "lore": "Viego był władcą dawno zapomnianego królestwa, kt<PERSON><PERSON> zgin<PERSON>ł ponad tysiąc lat temu, gdy podczas próby przywrócenia do życia zmarłej żony spowodował magiczną katastrofę zwaną Zrujnowaniem. On sam przemienił się w potężnego, nieumarłego upiora nękanego obsesyjną tęsknotą za jego królową. <PERSON><PERSON>ś jako Zrujnowany Król kontroluje zabójcze Harrowing i przemierza Runeterrę w poszukiwaniu jakiegokolwiek sposobu na wskrzeszenie małżonki. Z okrutnego, złamanego serca Viego sączy się Czarna Mgła, która niszczy wszystko na jego drodze.", "blurb": "Viego był władcą dawno zapomnianego królestwa, kt<PERSON><PERSON> zginął ponad tysiąc lat temu, gdy podczas próby przywrócenia do życia zmarłej żony spowodował magiczną katastrofę zwaną Zrujnowaniem. On sam przemienił się w potężnego, nieumarłego upiora nękanego...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Brak", "info": {"attack": 6, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 10000, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 200, "hpregen": 7, "hpregenperlevel": 0.7, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "ViegoQ", "name": "Ostrze Zniszczonego Króla", "description": "Widmowe ostrze Viego biernie zadaje dodatkowe obrażenia o wartości procenta aktualnego zdrowia <OnHit>przy trafieniu</OnHit> i dwukrotnie uderza wrogów, którzy zostali niedawno trafieni jego umiej<PERSON>ściami, w<PERSON><PERSON>j<PERSON><PERSON> ich zdrowie.<br><br><PERSON>iego może użyć tej umiejętności, by p<PERSON><PERSON><PERSON><PERSON> swo<PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wrogów, któ<PERSON>y znajdują się przed nim.", "tooltip": "<spellPassive>Biernie:</spellPassive> Ataki Viego zadają dodatkowo <physicalDamage>obrażenia fizyczne równe {{ totalpercenthealthonhit }} aktualnego zdrowia celu</physicalDamage>. Je<PERSON> pierwszy atak przeciwko wrogowi, któremu niedawno zadał obrażenia za pomocą swojej <PERSON>, trafia drugi raz, zadając <physicalDamage>{{ secondattackdamage }} pkt. obrażeń fizycznych</physicalDamage> i lecząc Viego o <healing>{{ healmodvschamps*100 }}% zadanych obrażeń</healing>. Te premie pozostają aktywne podczas <keywordMajor>prz<PERSON><PERSON>cia</keywordMajor>.<br /><br /><spellActive>Użycie:</spellActive> Viego dźga w przód, zadając <physicalDamage>{{ totaldamage }} pkt. obraże<PERSON> fizycznych</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "% aktualnego zdrowia", "Minimalne obrażenia od aktualnego zdrowia", "Czas odnowienia"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ percenthealthonhit }}% -> {{ percenthealthonhitNL }}%", "{{ mindamageon<PERSON> }} -> {{ mindamageonhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5, 4.5, 4, 3.5, 3], "cooldownBurn": "5/4.5/4/3.5/3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViegoQ.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "ViegoW", "name": "Widmowa Paszcza", "description": "Viego kumuluje mgłę, a następnie doskakuje i wypuszcza kulę skoncentrowanej Czarnej Mgły, która ogłusza pierwszego trafionego wroga.", "tooltip": "<charge>Ładowanie:</charge> Viego z<PERSON>zy<PERSON> Mgłę, <status>spowal<PERSON>j<PERSON>c</status> się o {{ selfslowpercent*100 }}%.<br /><br /><release>Wypuszczenie:</release> Viego doskakuje naprzód i ciska zebraną Mgłą. Zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>ogłusza</status> pierwszego trafionego wroga na {{ stunduration }}-{{ maxstuntt }} sek. w zależności od czasu ładowania.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViegoW.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "ViegoE", "name": "Ścieżka Przerażenia", "description": "Viego rozkazuje Czarnej Mgle, by <PERSON><PERSON><PERSON><PERSON><PERSON> i otoczyła część terenu. Viego może ukryć się we Mgle jako upiór, by <PERSON><PERSON><PERSON><PERSON>, pręd<PERSON><PERSON>ć ruchu i prędkość ataku.", "tooltip": "Viego posyła widmo naprzód, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pierwszy trafiony teren, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> go Mgłą na {{ mistduration }} sek. Viego zyskuje <keywordStealth>kamu<PERSON><PERSON><PERSON></keywordStealth>, <speed>{{ totalmovespeed }} prędkości ruchu</speed> i <attackSpeed>{{ attackspeed*100 }}% prędkości ataku</attackSpeed>, gdy znajduje się we Mgle.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "Czas odnowienia"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViegoE.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "ViegoR", "name": "Łamacz Serc", "description": "Viego teleportuje się w pobliskie miejsce i atakuje wrogiego bohatera w momencie przybycia, przeb<PERSON><PERSON><PERSON>c jego serce i wywołując wokół swojego celu niszczycielską falę uderzeniową, która odrzuca pozostałych wrogów.", "tooltip": "Viego porzuca dusze, kt<PERSON><PERSON> <keywordMajor>prz<PERSON><PERSON><PERSON></keywordMajor>, i teleportuje się. W momencie przybycia atakuje bohatera o najniższym poziomie zdrowia, na krótko go <status>spowal<PERSON><PERSON><PERSON>c</status> o {{ slowpercent*100 }}% i zadając mu <physicalDamage>obrażenia fizyczne o wartości {{ totaldamage }} pkt. + {{ totalpercenthealth }}% brakującego zdrowia celu</physicalDamage>. Inni pobliscy wrogowie zostają <status>odr<PERSON><PERSON>ni</status> i otrzymują <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od brakującego zdrowia", "Czas odnowienia"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "ViegoR.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}], "passive": {"name": "Domina<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON><PERSON> zostaną pokonani przez Viego, staną się upiorami. Poprzez zaatakowanie upiora Viego przejmuje kontrolę nad martwym ciałem wroga, lecz<PERSON>c się o pewien procent maks. zdrowia celu i zyskując dostęp do podstawowych umiejętności i przedmiotów celu. Zamienia superumiejętność wroga na darmowe użycie swojej.", "image": {"full": "Viego_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}