{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Belveth": {"id": "Belveth", "key": "200", "name": "Bel'Veth", "title": "Impératrice du Néant", "image": {"full": "Belveth.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "200000", "num": 0, "name": "default", "chromas": false}, {"id": "200001", "num": 1, "name": "Bel'Veth boss de combat", "chromas": true}, {"id": "200010", "num": 10, "name": "Bel'Veth matriarche cosmique", "chromas": true}, {"id": "200019", "num": 19, "name": "Bel'Veth primordienne", "chromas": true}], "lore": "<PERSON><PERSON><PERSON><PERSON>, impératrice ca<PERSON> née d'une cité dévorée, représente la fin de Runeterra... et le début d'une monstrueuse réalité de sa propre conception. Mue par des siècles d'histoire, de connaissances et de souvenirs absorbés dans le monde du dessus, elle assouvit son appétit croissant pour les expériences et les émotions nouvelles, consumant tout sur son passage. Néanmoins, un seul monde ne suffira jamais à satisfaire tous ses désirs, aussi tourne-t-elle son regard avide vers les anciens maîtres du Néant...", "blurb": "<PERSON><PERSON><PERSON><PERSON>, impératrice <PERSON> née d'une cité dévorée, représente la fin de Runeterra... et le début d'une monstrueuse réalité de sa propre conception. Mue par des siècles d'histoire, de connaissances et de souvenirs absorbés dans le monde du...", "allytips": [], "enemytips": [], "tags": ["Fighter"], "partype": "", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 60, "mpperlevel": 0, "movespeed": 340, "armor": 32, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 1.5, "attackspeedperlevel": 0, "attackspeed": 0.85}, "spells": [{"id": "BelvethQ", "name": "Charge du Néant", "description": "Bel'Veth se rue dans la direction choisie et blesse tous les ennemis qu'elle traverse.", "tooltip": "Bel'Veth se rue dans une direction, infligeant <physicalDamage>{{ basedamage }} pts de dégâts physiques</physicalDamage> aux ennemis qu'elle traverse.<br /><br />Chaque direction a son propre délai de récupération de {{ f1 }} sec, lequel diminue à mesure que la <attackSpeed>vitesse d'attaque</attackSpeed> augmente.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>lai de récupération par direction", "Dégâts aux monstres", "Dégâts aux sbires"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ persidecooldown }} -> {{ persidecooldownNL }}", "{{ monstermod }} -> {{ monstermodNL }}", "{{ minonmod*100.000000 }}% -> {{ minonmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethQ.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "BelvethW", "name": "Projection cinglante", "description": "Bel'Veth donne un coup de queue au sol, ce qui blesse, projette dans les airs et ralentit les ennemis.", "tooltip": "Bel'Veth donne un coup de queue, infligeant <magicDamage>{{ damage }} pts de dégâts magiques</magicDamage>, <status>projetant dans les airs</status> les ennemis pendant {{ duration }} sec et les <status>ralentissant</status> de {{ slowpercent*100 }}% pendant {{ slowduration }} sec. Si Bel'Veth touche un champion, le délai de récupération de <spellName>Charge du Néant</spellName> est réinitialisé pour cette direction.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Du<PERSON>e de ralentissement", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [715, 715, 715, 715, 715], "rangeBurn": "715", "image": {"full": "BelvethW.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "BelvethE", "name": "<PERSON><PERSON><PERSON> impérial", "description": "Bel'Veth s'immobilise et canalise une tempête de coups autour d'elle qui cible l'ennemi ayant le moins de PV. De plus, elle gagne du vol de vie et de la réduction des dégâts.", "tooltip": "Bel'Veth canalise une tempête de coups autour d'elle. Elle gagne alors +{{ drpercent*100 }}% de réduction des dégâts et +{{ totallifesteal }} vol de vie, et elle attaque {{ f2.0 }} fois en {{ totalduration }} sec (le nombre d'attaques augmente avec la <attackSpeed>vitesse d'attaque</attackSpeed>). Chaque attaque frappe l'ennemi qui a le moins de PV, lui infligeant <physicalDamage>{{ damageperstrike }}</physicalDamage> - <physicalDamage>{{ maxdamageperstriketooltip }} pts de dégâts physiques</physicalDamage> selon les PV manquants de la cible.<br /><br />Lancer une autre compétence ou <recast>réactiver</recast> cette compétence y met fin prématurément.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Réduction des dégâts", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ damageperhit }} -> {{ damageperhitNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "BelvethE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "BelvethR", "name": "Banquet infini", "description": "Bel'Veth consomme du corail du Néant ; elle prend alors sa véritable forme, ce qui augmente ses PV max, sa portée d'attaque, sa vitesse d'attaque et sa vitesse de déplacement en dehors des combats. Consommer du corail du Néant généré par un monstre épique du Néant prolonge la durée de son ultime et lui donne le pouvoir d'invoquer des rémoras du Néant.", "tooltip": "<spellPassive>Passive :</spellPassive> toutes les deux attaques contre une même cible, Bel'Veth inflige <trueDamage>{{ finalonhitdamage }} pts de dégâts bruts</trueDamage> supplémentaires (cumulable à l'infini). Participer à l'élimination de champions et de monstres épiques génère des coraux du Néant.<br /><br /><spellActive>Active :</spellActive> consommer un corail du Néant octroie <keywordMajor>{{ passivestacksondevour }} effet <PERSON></keywordMajor> et active la véritable forme de Bel'Veth pendant {{ steroidduration }} sec. Les coraux du Néant générés par des monstres épiques du Néant augmentent la durée à {{ voidduration }} sec et transforment en rémoras du Néant les sbires tués à proximité. Pendant l'incantation, Bel'Veth <status>ralentit</status> les ennemis proches avant d'exploser, infligeant des <trueDamage>dégâts bruts équivalents à {{ totalexplosiondamage }} + {{ missinghealthdamage*100 }}% des PV manquants de la cible</trueDamage>.<br /><br />Dans sa véritable forme, Bel'Veth gagne <healing>+{{ maxhealthondevour }} PV max</healing>, <speed>+{{ oocms }} vitesse de déplacement</speed> en dehors des combats, +{{ bonusaarange }} portée d'attaque et <attackSpeed>+{{ totalasmod*100 }}% de vitesse d'attaque totale</attackSpeed>, et <spellName>Charge du Néant</spellName> peut traverser les murs.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts à l'impact", "Dégâts de l'explosion", "PV supplémentaires", "Vitesse de d<PERSON>placement", "Vitesse d'attaque", "PV des rémoras du Néant"], "effect": ["{{ onhitdamage }} -> {{ onhitdamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealth }} -> {{ basemaxhealthNL }}", "{{ oocms }} -> {{ oocmsNL }}", "{{ totalasmod*100.000000 }}% -> {{ totalasmodnl*100.000000 }}%", "{{ voidlinghpscale*100.000000 }}% -> {{ voidlinghpscalenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}], "passive": {"name": "<PERSON><PERSON> <PERSON> ", "description": "Bel'Veth gagne définitivement des bonus cumulables en vitesse d'attaque après avoir tué des grands monstres, des grands sbires et des champions. Elle gagne aussi un bonus temporaire en vitesse d'attaque après avoir utilisé une compétence.", "image": {"full": "Belveth_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}