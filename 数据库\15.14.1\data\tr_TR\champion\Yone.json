{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Yone", "title": "Unutulmayan <PERSON>", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "<PERSON><PERSON>çeği Yo<PERSON>", "chromas": true}, {"id": "777010", "num": 10, "name": "Savaş Akademisi Yone", "chromas": true}, {"id": "777019", "num": 19, "name": "Şafa<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "777026", "num": 26, "name": "<PERSON>yanus <PERSON> Yone", "chromas": true}, {"id": "777035", "num": 35, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "777045", "num": 45, "name": "HEARTSTEEL Yone", "chromas": true}, {"id": "777046", "num": 46, "name": "Prestij HEARTSTEEL Yone", "chromas": false}, {"id": "777055", "num": 55, "name": "Vahşi Batılı Yone", "chromas": true}, {"id": "777058", "num": 58, "name": "Uzlaştırıcı Vahşi Batılı Yone", "chromas": false}, {"id": "777065", "num": 65, "name": "<PERSON><PERSON>", "chromas": false}], "lore": "Yaşarken adı Yone'ydi. Yasuo'nun üvey ağabeyi ve köyündeki kılıç okulunun en başarılı öğrencisiydi. Ama kardeşinin elinde can verdikten sonra peşine ruhlar âleminin kötücül bir yaratığı takıldı. Yone bu yaratığı onun kendi kılıcıyla öldürmek zorunda kaldı. Artık yaratığın yüzünü maske olarak takmakla lanetlenen Yone, neye dönüştüğünü anlamak için bu cins yaratıkları yorulmak bilmeden avlıyor.", "blurb": "Yaşarken adı Yone'ydi. Yasu<PERSON>'nun üvey ağabeyi ve köyündeki kılıç okulunun en başarılı öğrencisiydi. Ama kardeşinin elinde can verdikten sonra peşine ruhlar âleminin kötücül bir yaratığı takıldı. Yone bu yaratığı onun kendi kılıcıyla öldürmek zorunda...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Akış", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "İleri hücum ederek bir hat üstündeki tüm rakiplere hasar verir.<br><br>İsabet halinde birkaç saniyeliğine Yaklaşan Kasırga yükü kazandırır. <PERSON>ki yüke ulaştığında Ölü<PERSON><PERSON>k, <PERSON>ne'nin rüzgârla birlikte ileri atılarak rakipleri <status>havaya savurmasını</status> sağlar.", "tooltip": "Yone ileri hücum ederek rakiplere <physicalDamage>{{ qdamage }} Fiziksel Hasar</physicalDamage> verir.<br /><br /><PERSON><PERSON><PERSON> halinde {{ buffduration }} saniyeliğine bir yük kazanır. 2 yük <PERSON>, <PERSON><PERSON> rakipler<PERSON> {{ q3knockupduration }} saniyeliğine <status>havaya savuran</status> ve onlara <physicalDamage>{{ qdamage }} Fiziksel Hasar</physicalDamage> veren bir rüzgâr dalgasıyla ileri atılır. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "YoneW", "name": "Tayfbiçen", "description": "Yone kılıcını önündeki koni şeklindeki alanda savurarak tüm rakiplere hasar verir. Bu yetenek Yone'ye bir kalkan kazandırır. Kalkan miktarı isabet alan şampiyonların sayısına bağlı olarak artar.<br><br>Tayfbiçen'in bekleme ve kullanım süresi saldırı hızı arttıkça azalır.", "tooltip": "Yone kılıcını ileri savurarak <physicalDamage>{{ basedamage*0.5 }} + rakiplerin azami canlarının %{{ maxhealthdamage*50 }} kadarına eşdeğer fiziksel hasar</physicalDamage> ve <magicDamage>{{ basedamage*0.5 }} + rakiplerin azami canlarının %{{ maxhealthdamage*50 }} kadarına eşdeğer büyü hasarı</magicDamage> verir.<br /><br /><PERSON><PERSON><PERSON> yetenek isabet ederse, Yone {{ shieldduration }} saniyeliğine <shield>{{ wshield }} Ka<PERSON><PERSON></shield> kazanır. <shield>Ka<PERSON><PERSON></shield> miktarı isabet alan şamp<PERSON>yon başına artar. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Toplam Azami Can Hasarı"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "%{{ maxhealthdamage*100.000000 }} -> %{{ maxhealthdamagenl*100.000000 }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "YoneE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Yone'nin ruhu bedeninden ayrılarak hareket hızı kazanır. Bu yetenek sona erdiğinde Yone'nin ruhu bedenine geri döner ve Yone ruh olarak verdiği hasarın bir kısmını tekrar verir.", "tooltip": "Yone {{ returntimer }} saniyeliğine ruh biçimine bürünerek bedenini bu süre boyunca geride bırakır ve <speed>%{{ startingms*100 }} Hareket Hızı</speed> kazanır. Bu ilave, yetenek süresince artarak <speed>%{{ movementspeed*100 }} Hareket Hızı</speed> olur.<br /><br />Ruh biçimi sona erdiğinde Yone hızla bedenine geri döner ve bu sırada şampiyonlara saldırı ve yetenekleriyle verdiği hasarın %{{ deathmarkpercent*100 }} kadarını tekrar verir. Ruh biçimindeyken bu yeteneği <recast>yeniden kullanabilirsin</recast>.<br /><br /><recast>Yeniden Kullanım: </recast>Ruh biçimini erkenden sonlandırır.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["%{{ deathmarkpercent*100.000000 }} -> %{{ deathmarkpercentnl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "YoneR", "name": "<PERSON>üh<PERSON><PERSON><PERSON>", "description": "<PERSON>ne kılıçlarını tüm gücüyle savurarak bir hat üstünde duran son şampiyonun arkasında belirir ve isabet alan tüm rakipleri kendisine doğru çeker.", "tooltip": "Yone bir hat üstündeki tüm şampiyonlara <physicalDamage>{{ tooltipdamage }} Fiziksel Hasar</physicalDamage> ve <magicDamage>{{ tooltipdamage }} Büyü Hasarı</magicDamage> vererek isabet alan son şampiyonun arkasında belirir ve diğer rakipleri <status>havaya savurarak</status> kendisine doğru çeker.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}], "passive": {"name": "Avcın<PERSON><PERSON>", "description": "<PERSON><PERSON> her ikinci saldırıyla b<PERSON><PERSON>ü hasarı verir. Ayr<PERSON>ca, kritik vuruş ihtimali artar.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}