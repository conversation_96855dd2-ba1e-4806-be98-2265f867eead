{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pyke": {"id": "<PERSON><PERSON>", "key": "555", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Потрошитель из Кровавой гавани", "image": {"full": "Pyke.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "555000", "num": 0, "name": "default", "chromas": false}, {"id": "555001", "num": 1, "name": "Песчаный призрак Пайк", "chromas": true}, {"id": "555009", "num": 9, "name": "Пайк Кровавая Луна", "chromas": true}, {"id": "555016", "num": 16, "name": "ПРОЕКТ: Пайк", "chromas": true}, {"id": "555025", "num": 25, "name": "Пайк из Пси-отряда", "chromas": true}, {"id": "555034", "num": 34, "name": "Страж света Пайк", "chromas": true}, {"id": "555044", "num": 44, "name": "Пепельный рыцарь Пайк", "chromas": true}, {"id": "555045", "num": 45, "name": "Эмпир<PERSON><PERSON>ц Пайк", "chromas": true}, {"id": "555053", "num": 53, "name": "Боевая душа Пайк", "chromas": true}, {"id": "555054", "num": 54, "name": "Боевая душа Пайк (престижный)", "chromas": false}, {"id": "555064", "num": 64, "name": "Пайк из Мира ужа<PERSON>ов", "chromas": true}, {"id": "555074", "num": 74, "name": "Пайк Чернильная Тень", "chromas": true}], "lore": "Знаменитый гарпунер из Кровавых доков Билджвотера, Пайк должен был встретить свою смерть в брюхе огромного челюстозуба... но на этом его история не закончилась. Теперь он бродит по промозглым переулкам бывшего родного города, используя свои новообретенные сверхъестественные силы, чтобы быстро и жестоко расправляться с теми, кто наживается на труде других. В городе, который славится охотниками на монстров, появился монстр, который охотится на них самих.", "blurb": "Знаменитый гарпунер из Кровавых доков Билджвотера, Пайк должен был встретить свою смерть в брюхе огромного челюстозуба... но на этом его история не закончилась. Теперь он бродит по промозглым переулкам бывшего родного города, используя свои...", "allytips": ["Пайк очень хрупок, поэтому не бойтесь на некоторое время сбегать из боя. Когда враги не видят вас, Дар утопленников восстанавливает вам значительную часть здоровья.", "Поражая врагов вторым эффектом Костяного гарпуна, который активируется удерживанием клавиши, вы всегда притягиваете их на фиксированное расстояние. Применив гарпун, стоя вплотную к цели, вы перебросите ее себе за спину.", "Первый эффект Костяного гарпуна, который активируется простым нажатием клавиши, применяется гораздо быстрее и наносит дополнительный урон.", "Многие ваши умения позволяют не только атаковать врага, но и сбежать. Всегда продумывайте план отступления."], "enemytips": ["Пайк восстанавливает значительное количество здоровья, потерянного в бою с вражескими чемпионами, но только когда его не видят!", "Если где-то рядом находится Пайк, скрытый Кошмарным погружением, у вас под ногами начинают плавать акулы.", "Старайтесь держаться подальше от союзников с низким уровнем здоровья. Если Пайк казнит их с помощью Смерти из глубин, его следующей целью можете стать вы."], "tags": ["Support", "Assassin"], "partype": "Мана", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 670, "hpperlevel": 110, "mp": 415, "mpperlevel": 50, "movespeed": 330, "armor": 43, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2, "attackspeedperlevel": 2.5, "attackspeed": 0.667}, "spells": [{"id": "PykeQ", "name": "Костяной гарпун", "description": "Пайк наносит удар врагу перед собой или подтягивает врага к себе.", "tooltip": "<tap>Нажатие:</tap> Пайк совершает удар, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> первому пораженному врагу (приоритет отдается чемпионам). После этого цель <status>замедляется</status> на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br /><hold>Удерживание:</hold> Пайк бросает гарпун, нанося <physicalDamage>{{ totaldamage }} физического урона</physicalDamage> первому пораженному врагу и <status>подтягивая</status> его к себе. После этого цель <status>замедляется</status> на {{ slowamount*100 }}% на {{ slowduration }} сек.<br /><br />Если Пайк поражает вражеского чемпиона или не завершает подготовку, он восстанавливает себе {{ manarefund*100 }}% от затраченной маны.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PykeQ.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "PykeW", "name": "Кошмарное погружение", "description": "Пайк получает маскировку и значительно увеличивает свою скорость передвижения (ускорение постепенно ослабевает со временем).", "tooltip": "Пайк получает <keywordStealth>маскировку</keywordStealth> и увеличивает свою <speed>скорость передвижения на {{ movespeed }}%</speed> (уменьшается в течение {{ e0 }} сек.).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0.3, 0.3, 0.3, 0.3, 0.3], [0.03, 0.03, 0.03, 0.03, 0.03], [80, 80, 80, 80, 80], [0.55, 0.55, 0.55, 0.55, 0.55], [8, 8, 8, 8, 8], [0.01, 0.01, 0.01, 0.01, 0.01], [1.15, 1.15, 1.15, 1.15, 1.15], [5, 5, 5, 5, 5]], "effectBurn": [null, "45", "0", "0.3", "0.03", "80", "0.55", "8", "0.01", "1.15", "5"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PykeW.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "PykeE", "name": "Призрачный прилив", "description": "Пайк совершает рывок и оставляет позади себя призрака, который возвращается к нему, оглушая вражеских чемпионов на своем пути.", "tooltip": "Пайк совершает рывок, оставляя за собой призрака утопленника, который вскоре возвращается к нему. Призрак <status>оглушает</status> врагов на {{ stunduration }} сек. и наносит чемпионам <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [100, 150, 200, 250, 300], [1.25, 1.25, 1.25, 1.25, 1.25], [550, 550, 550, 550, 550], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/150/200/250/300", "1.25", "550", "1", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "PykeE.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "PykeR", "name": "Смерть из глубин", "description": "Пайк телепортируется к чемпионам и казнит врагов с низким уровнем здоровья. Если цель умирает, Пайк может применить умение еще раз, а союзник, помогавший с убийством, получает дополнительное золото.", "tooltip": "Пайк поражает всех вражеских чемпионов в X-образной области, телепортируясь к ним и <danger>казня</danger> тех, у кого осталось менее <scaleAD>{{ rdamage }}</scaleAD> здоровья. Чемпионы, у которых больше здоровья, и враги, не являющиеся чемпионами, получают физический урон в размере {{ reduceddamage*100 }}% от этого значения (<physicalDamage>{{ reduceddamagefinal }}</physicalDamage>). <br /><br />Если в области действия умения погибает вражеский чемпион, Пайк может <recast>повторно применить</recast> это умение без затрат маны в течение {{ rrecastduration }} сек. Если эту цель казнил Пайк, последний помогавший ему союзник также получает золото за убийство. Сам Пайк всегда получает золото за убийство - даже если цель добил не он.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "PykeR.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "<PERSON>а<PERSON> уто<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Прячась от врагов, Пайк восстанавливает здоровье, которое недавно потерял в бою с вражескими чемпионами. Кроме того, он не может увеличить свой максимальный запас здоровья из любых источников, получая вместо этого дополнительную силу атаки.", "image": {"full": "PykePassive.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}