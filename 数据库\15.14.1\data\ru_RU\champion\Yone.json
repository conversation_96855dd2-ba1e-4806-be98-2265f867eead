{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Ёнэ", "title": "Незабытый", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "Дух цветения Ёнэ", "chromas": true}, {"id": "777010", "num": 10, "name": "Ёнэ из Боевой академии", "chromas": true}, {"id": "777019", "num": 19, "name": "Предвестник зари Ёнэ", "chromas": true}, {"id": "777026", "num": 26, "name": "Пляжный Ёнэ", "chromas": true}, {"id": "777035", "num": 35, "name": "Ёнэ Чернильная Тень", "chromas": true}, {"id": "777045", "num": 45, "name": "Ёнэ из HEARTSTEEL", "chromas": true}, {"id": "777046", "num": 46, "name": "Ёнэ из HEARTSTEEL (престижный)", "chromas": false}, {"id": "777055", "num": 55, "name": "Ковбой Ёнэ", "chromas": true}, {"id": "777058", "num": 58, "name": "Светлый ковбой Ёнэ", "chromas": false}, {"id": "777065", "num": 65, "name": "Герой в маске Ёнэ", "chromas": false}], "lore": "При жизни Ёнэ, единоутробный брат Ясуо, был одним из самых талантливых учеников в школе мечников недалеко от родной деревни. Он погиб от руки брата, и в мире духов на него напала злобная сущность. У Ёнэ не оставалось иного выбора, кроме как сразить чудовище его собственным мечом. Обреченный носить на лице маску убитого демона, Ёнэ без устали охотится на других подобных тварей, чтобы понять, в кого превратился он сам.", "blurb": "При жизни Ёнэ, единоутробный брат Ясуо, был одним из самых талантливых учеников в школе мечников недалеко от родной деревни. Он погиб от руки брата, и в мире духов на него напала злобная сущность. У Ёнэ не оставалось иного выбора, кроме как сразить...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Поток", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "Сталь смертного", "description": "Ёнэ делает выпад вперед, нанося урон всем врагам на прямой линии.<br><br>При попадании Ёнэ получает заряд Надвигающейся бури на несколько секунд. После накопления 2 зарядов Ёнэ при использовании Стали смертного совершает рывок вперед с порывом ветра, <status>подбрасывая</status> врагов.", "tooltip": "Ёнэ делает выпад вперед, нанося <physicalDamage>{{ qdamage }} физического урона</physicalDamage>.<br /><br />При попадании Ёнэ получает заряд на {{ buffduration }} сек. После накопления 2 зарядов Ёнэ при использовании этого умения совершает рывок вперед с порывом ветра, <status>подбрасывая</status> врагов на {{ q3knockupduration }} сек. и нанося им <physicalDamage>{{ qdamage }} физического урона</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "YoneW", "name": "Рассечение духа", "description": "Ёнэ совершает удар, нанося урон всем врагам в конусе перед собой. При этом Ёнэ получает щит, прочность которого зависит от количества пораженных чемпионов.<br><br>Время перезарядки и время произнесения Рассечения духа зависят от скорости атаки.", "tooltip": "Ёнэ совершает удар, нанося врагам перед собой <physicalDamage>физический урон в размере {{ basedamage*0.5 }} + {{ maxhealthdamage*50 }}% от максимального запаса здоровья цели</physicalDamage> и <magicDamage>магический урон в размере {{ basedamage*0.5 }} + {{ maxhealthdamage*50 }}% от максимального запаса здоровья цели</magicDamage>.<br /><br />Если Ёнэ попадает по врагу, он получает <shield>щит прочностью {{ wshield }}</shield> на {{ shieldduration }} сек. Прочность <shield>щита</shield> зависит от количества пораженных чемпионов. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон", "Общий урон от максимального запаса здоровья"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "YoneE", "name": "Освобожденная душа", "description": "Ёнэ покидает тело в форме духа и увеличивает свою скорость передвижения. По окончании действия умения дух Ёнэ возвращается в тело и снова наносит часть урона, нанесенного перед этим.", "tooltip": "Ёнэ принимает форму духа на {{ returntimer }} сек., покидая тело и увеличивая свою <speed>скорость передвижения на {{ startingms*100 }}%</speed> (со временем ускорение возрастает до <speed>{{ movementspeed*100 }}%</speed>). <br /><br />По окончании действия умения Ёнэ возвращается в тело и снова наносит {{ deathmarkpercent*100 }}% от всего урона от автоатак и умений, который он нанес чемпионам во время пребывания в форме духа. Во время действия умения его можно <recast>применить повторно</recast>.<br /><br /><recast>Повторное применение: </recast>дух Ёнэ преждевременно возвращается в тело.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Повторный урон", "Перезарядка"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "YoneR", "name": "Решение судьбы", "description": "Ёнэ телепортируется за последнего чемпиона на прямой, поражая всех врагов на пути с такой силой, что они подтягиваются к нему.", "tooltip": "Ёнэ поражает всех врагов на пути перед собой, нанося <physicalDamage>{{ tooltipdamage }} физического урона</physicalDamage> и <magicDamage>{{ tooltipdamage }} магического урона</magicDamage>. При этом Ёнэ телепортируется за последнего пораженного чемпиона и <status>подбрасывает</status> жертв, подтягивая их к себе.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Путь охотника", "description": "Каждая вторая автоатака Ёнэ наносит магический урон. Кроме того, у него увеличен шанс критического удара.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}