{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Velkoz": {"id": "Velkoz", "key": "161", "name": "Vel'Koz", "title": "<PERSON><PERSON>", "image": {"full": "Velkoz.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "161000", "num": 0, "name": "default", "chromas": false}, {"id": "161001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "161002", "num": 2, "name": "Świetlisty Vel'Koz", "chromas": false}, {"id": "161003", "num": 3, "name": "Na Pewno Nie Vel'Koz", "chromas": false}, {"id": "161004", "num": 4, "name": "Piekielny Vel'Koz", "chromas": true}, {"id": "161011", "num": 11, "name": "<PERSON><PERSON>'<PERSON><PERSON>", "chromas": true}, {"id": "161020", "num": 20, "name": "Pszczel'Ko<PERSON>", "chromas": true}], "lore": "Nie wiadomo czy Vel'<PERSON><PERSON> był pierwszym Pomiotem Pustki, kt<PERSON><PERSON> pojawił się w Runeterze, ale na pewno żaden inny Pomiot nie doścignął poziomu jego chłodnego, wykalkulowanego rozumowania świata. Choć jego pobratymcy pożerają lub profanują wszystko wokół, on woli analizować i przyglądać się fizycznemu wymiarowi — oraz dziwnym, wo<PERSON><PERSON><PERSON><PERSON> istotom, które go zamieszkują — s<PERSON><PERSON><PERSON><PERSON> słabości, które Pustka mogłaby wykorzystać. Lecz Vel'Koz bynajmniej nie przygląda się biernie temu wszystkiemu, atakuje zagrażające mu osobniki, wystr<PERSON><PERSON>wuj<PERSON><PERSON> zabójczą plazmę i przerywając materiał świata.", "blurb": "Nie wiadomo czy V<PERSON>'<PERSON><PERSON> był pierwszym Pomiotem Pustki, kt<PERSON><PERSON> pojawił się w Runeterze, ale na pewno żaden inny Pomiot nie doścignął poziomu jego chłodnego, wykalkulowanego rozumowania świata. Choć jego pobratymcy pożerają lub profanują wszystko wokół, on...", "allytips": ["Walcząc w alei, zabijaj stwory Wyłomem Pustki, jednocześnie nakładając na wrogów efekty Dekonstrukcji Organicznej. Inne umiejętności będą lepiej działać na naznaczonych w ten sposób przeciwników.", "Strzelaj pociskami z umiejętności Rozszczepienie Plazmowe w taki sposób, aby rozdzielały się po przebyciu jak największej odległości. <PERSON>ć nie jest to łatwe, moż<PERSON>z w ten sposób trafić wrogów będących poza zasięgiem pierwszego pocisku.", "Staraj się rozważnie korzystać z Promienia Dezintegracji Istot Żywych. Wielu bohaterów dysponuje umiejętnościami mogącymi przerwać działanie tego czaru."], "enemytips": ["Vel'<PERSON><PERSON> może być bardzo niebezpiecznym przeciwnikiem w walce, je<PERSON>eli pozwoli mu się działać. Postaraj się go zabić jak najszybciej.", "Vel'Koz nie jest zbyt mobilny i łatwo go zaskoczyć.", "<PERSON><PERSON><PERSON> z<PERSON>ł<PERSON>y, uciszony lub podrzucony w powietrze, przestanie emitować Promień Dezintegracji Istot Żywych."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 102, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 22, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1416, "attackspeedperlevel": 1.59, "attackspeed": 0.643}, "spells": [{"id": "VelkozQ", "name": "Rozszczepienie Plazmowe", "description": "Vel'<PERSON><PERSON> wystrzeliwuje plazmowy pocisk, który rozdziela się na dwie części przy powtórnej aktywacji lub trafieniu wroga. Pocisk przy trafieniu spowalnia i zadaje obrażenia.", "tooltip": "V<PERSON><PERSON><PERSON><PERSON> wystr<PERSON>wu<PERSON> plazmowy pocisk, kt<PERSON><PERSON> zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia</status> o {{ slowamount*100 }}% na {{ slowduration }} sek. Po osiągnięciu maks. dystansu, trafieniu celu lub <recast>ponownym użyciu</recast> pocisk rozdziela się na dwa pociski pod kątem 90 stopni.<br /><br />Zabicie jednostki za pomocą tej umiejętności przywraca <scaleMana>{{ tooltipmanarefund }} pkt. many</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Całkowite obrażenia", "Czas działania spowolnienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "VelkozQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VelkozW", "name": "Wyłom Pustki", "description": "Vel'Koz otwiera szczelinę do Pustki, która zadaje początkowe obrażenia na obszarze wybuchu. Po chwili następuje drugi wybuch.", "tooltip": "Vel'Koz otwiera szczelinę prowadzącą do pustki, zadając <magicDamage>{{ initialdamage }} pkt. obrażeń magicznych</magicDamage>. Następnie szczelina wybucha, zadając <magicDamage>{{ secondarydamage }} pkt. obrażeń magicznych</magicDamage>.<br /><br />Ta umiej<PERSON>tność ma 2 ładunki ({{ ammorechargetime }} sek. odnowienia).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia początkowe", "Obrażenia wtórne", "<PERSON>zas ładowania", "Koszt (@AbilityResourceName@)"], "effect": ["{{ baseinitialdamage }} -> {{ baseinitialdamageNL }}", "{{ basesecondarydamage }} -> {{ basesecondarydamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [45, 75, 105, 135, 165], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0.25, 0.25, 0.25, 0.25, 0.25], [0.5, 0.5, 0.5, 0.5, 0.5], [88, 88, 88, 88, 88], [500, 500, 500, 500, 500], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "45/75/105/135/165", "100", "0", "0.25", "0.5", "88", "500", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "2", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "VelkozW.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VelkozE", "name": "Tektoniczne Rozerwanie", "description": "Vel'Ko<PERSON> wywołuje wybuch, który wyrzuca wrogów w powietrze. Przeciwnicy znajdujący się bliżej zostaną dodatkowo nieco odrzuceni.", "tooltip": "<PERSON><PERSON>'<PERSON><PERSON> rozrywa pobliski obszar, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że teren ten wybucha, <status>podrz<PERSON>jąc</status> na {{ stunduration }} sek. i zadając <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>. Wrogowie w pobliżu Vel'Koza zostają <status>odr<PERSON><PERSON>ni</status> zamiast <status>podrzuceni</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "VelkozE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VelkozR", "name": "Promień Dezintegracji Istot Żywych", "description": "Vel'Koz wysyła promień podążający za kursorem przez 2,5 sek. i zadający obrażenia magiczne. Dekonstrukcja Organiczna analizuje wrogich bohaterów, w wyniku czego zamiast magicznych otrzymują oni obrażenia nieuchronne.", "tooltip": "V<PERSON><PERSON><PERSON><PERSON> w<PERSON> promień energii, kt<PERSON><PERSON> podą<PERSON>a za kursorem, zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> w ciągu 2,5 sek. i <status>spowalnia</status> o {{ e3 }}%. Zadaje <trueDamage>obrażenia nieuchronne</trueDamage> wrogom, którzy krótko wcześniej otrzymali obrażenia od umiejętności biernej <spellName>Dekonstrukcja Organiczna</spellName>.<br /><br />Promień co jaki<PERSON> czas nakłada na wrogów znajdujących się w nim ładunek <keywordMajor>Dekonstrukcji</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Całkowite obrażenia", "Czas odnowienia"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [450, 625, 800], [7, 7, 7], [20, 20, 20], [40, 40, 40], [175, 175, 175], [7, 7, 7], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "450/625/800", "7", "20", "40", "175", "7", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1575, 1575, 1575], "rangeBurn": "1575", "image": {"full": "VelkozR.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Dekonstrukcja Organiczna", "description": "Umiejętności Vel'Koza przy trafieniu nakładają na wrogów <keywordName>Dekonstrukcję Organiczną</keywordName>. Je<PERSON><PERSON> uzbierają się 3 ładunki, wr<PERSON><PERSON> otrzyma dodatkowe, nieuchronne obrażenia.", "image": {"full": "VelKoz_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}