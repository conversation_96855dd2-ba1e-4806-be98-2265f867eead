{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Khazix": {"id": "Khazix", "key": "121", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Räuber der Leere", "image": {"full": "Khazix.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "121000", "num": 0, "name": "default", "chromas": false}, {"id": "121001", "num": 1, "name": "Mecha-Kha'Z<PERSON>", "chromas": true}, {"id": "121002", "num": 2, "name": "Sandwächter Kha'Zix", "chromas": false}, {"id": "121003", "num": 3, "name": "Todesblüten-Kha'Zix", "chromas": false}, {"id": "121004", "num": 4, "name": "Sternenvernichter-Kha'Zix", "chromas": false}, {"id": "121011", "num": 11, "name": "WM 2018-<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "121060", "num": 60, "name": "Odyssee-Kha'Zix", "chromas": false}, {"id": "121069", "num": 69, "name": "Mondwächter Kha'Zix", "chromas": false}, {"id": "121079", "num": 79, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Die Leere wächst und passt sich an – diese Tatsache lässt sich an Kha'<PERSON><PERSON> besonders gut sehen. Die Evolution arbeitet im Kern dieses mutierenden Schreckens, der geboren wurde, um zu überleben und die Starken zu töten. Trifft er auf Probleme, so bildet er neue, effektivere Mechanismen, um seine Opfer zu überwältigen. Anfangs war Kha'Zix ein hirnloses Tier, doch seine Intelligenz wuchs parallel zu seinem Körper. Jetzt plant die Kreatur ihre Jagd sorgfältig und nutzt den instinktiven Schrecken, den er in seinen Opfern auslöst.", "blurb": "Die Leere wächst und passt sich an – diese Tatsache lässt sich an <PERSON><PERSON><PERSON><PERSON> besonders gut sehen. Die Evolution arbeitet im Kern dieses mutierenden Schreckens, der geboren wurde, um zu überleben und die Starken zu töten. Trifft er auf Probleme, so bildet...", "allytips": ["<PERSON><PERSON><PERSON> werden als isoliert betrachtet, wenn sich keine Verbündete in der Nähe aufhalten. Der Schaden von „Geschmack der Furcht“ ist gegen solche Ziele massiv erhöht.", "„Verborgene Bedrohung“ wird aktiv, wenn Kha'Zix vom gegnerischen Team nicht gesehen werden kann. Reaktivieren durch „Leerenangriff“ oder hohes Gras. Ver<PERSON><PERSON> nicht, „Verborgene Bedrohung“ anzuwenden, indem du gegnerische Champions angreifst.", "<PERSON><PERSON><PERSON><PERSON><PERSON> kann sich gut aussuchen, wo und wann er kämpfen möchte. Wähle deine Kämpfe mit Bedacht, wenn du siegen willst."], "enemytips": ["„Geschmack der Furcht“ verursacht nur gegen isolierte Ziele zusätzlichen Schaden. <PERSON><PERSON><PERSON> das au<PERSON>, indem du in der Nähe von verbündeten Vasallen, Champions oder Türmen bleibst.", "„Sprung“ und „Leerenangriff“ besitzen lange Abklingzeiten. Kha'Zix ist sehr verwundbar, wenn er sie nicht einsetzen kann."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 6}, "stats": {"hp": 643, "hpperlevel": 99, "mp": 327, "mpperlevel": 40, "movespeed": 350, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.59, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.668}, "spells": [{"id": "KhazixQ", "name": "Geschmack der Furcht", "description": "Verursacht normalen Schaden am Ziel. Bei <font color='#FFF673'>isolierten</font> Zielen wird der Schaden erhöht. Kha'Zix kann die <font color='#00DD33'>vergrößerten Klauen entwickeln</font>, um bei Angriffen gegen <font color='#FFF673'>isolierte</font> Ziele einen Teil der Abklingzeit zurückerstattet zu bekommen. Außerdem vergrößert sich die Reichweite von Kha'Zix' normalen Angriffen und „Geschmack der Furcht“.", "tooltip": "<PERSON>ha'<PERSON><PERSON> greift einen Gegner in der Nähe an und fügt ihm <physicalDamage>{{ spell.khazixq:basedamage }}&nbsp;normalen Schaden</physicalDamage> zu<PERSON> <PERSON><PERSON><PERSON>, die von ihren Verbündeten <keywordMajor>isoliert</keywordMajor> sind, erleiden stattdessen <physicalDamage>{{ spell.khazixq:isodamage }}&nbsp;Schaden</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [80, 105, 130, 155, 180], [110, 110, 110, 110, 110], [50, 50, 50, 50, 50], [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/105/130/155/180", "110", "50", "45", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "KhazixQ.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>'<PERSON><PERSON> schießt explodierende Stacheln, die an getroffenen Gegnern normalen Schaden verursachen. Kha'<PERSON>ix heilt sich, wenn er im Explosionsradius steht. Wenn er sich dazu entschließt, seine <font color='#00DD33'>Stachelreihen zu entwickeln</font>, feuert „Leerenstachel“ 3&nbsp;Stacheln in einem Kegel ab, verlangsamt getroffene Gegner und deckt gegnerische Champions für 2&nbsp;Sekunden auf. <font color='#FFF673'>Isolierte</font> Ziele werden stärker verlangsamt.", "tooltip": "<PERSON><PERSON>'<PERSON><PERSON> feuert einen Stachel ab, der dem ersten getroffenen Gegner in einem kleinen Bereich <physicalDamage>{{ basedamage }}&nbsp;normalen Schaden</physicalDamage> zufügt. Wenn Kha'Zix sich innerhalb dieses Bere<PERSON>s befindet, stellt er <healing>{{ healamount }}&nbsp;<PERSON><PERSON></healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [55, 75, 95, 115, 135], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "55/75/95/115/135", "40", "2", "0", "2", "60", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KhazixW.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixE", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> springt in einen Bereich und verursacht bei seiner Landung normalen Schaden. Er kann die <font color='#00DD33'>Flügel entwickeln</font>, um die Reichweite des Sprungs um 200 zu erhöhen. Außerdem wird dann bei Champion-Kills und Unterstützungen die Abklingzeit von „Sprung“ zurückgesetzt.", "tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> springt und verursacht bei der Landung <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [30, 35, 40, 45, 50], [10, 10, 10, 10, 10], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "30/35/40/45/50", "10", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KhazixE.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixR", "name": "Leerenangriff", "description": "Jeder Rang ermöglicht es Kha'Zix, eine seiner Fähigkeiten weiterzuentwickeln, wodurch diese zusätzlich einzigartige Effekte erhalten. Die Aktivierung macht Kha'Zix <font color='#91d7ee'>unsichtbar</font>, löst „Verborgene Bedrohung“ aus und erhöht sein Lauftempo. Wenn er <font color='#00DD33'>Adaptive Tarnung</font> weiterentwickelt, hält die <font color='#91d7ee'>Unsichtbarkeit</font> länger an und kann ein zusätzliches Mal eingesetzt werden.", "tooltip": "<spellActive>Aktiv:</spellActive> Kha'Zix wird {{ stealthduration }}&nbsp;Sekunden lang <keywordStealth>unsichtbar</keywordStealth> und erhält <speed>{{ bonusmovementspeedpercent*100 }}&nbsp;% Lauftempo</speed>. Kha'Zix kann diese Fähigkeit innerhalb von {{ recastwindow }}&nbsp;Sekunden <recast>reaktivieren</recast>.<br /><br /><spellPassive>Passiv:</spellPassive> Die Aufwertung dieser Fähigkeit ermöglicht es Kha'Zix, eine seiner Fähigkeiten <evolve>weiterzuentwickeln</evolve>, um ihr zusätzliche Effekte zu verleihen.<li><spellName>Geschmack der Furcht:</spellName> Er erhält zusätzliche Fähigkeitsreichweite und Angriffsreichweite und die Abklingzeit verringert sich um {{ spell.khazixq:effect4amount }}&nbsp;% gegen <keywordMajor>isolierte</keywordMajor> Ziele.<li><spellName>Leerenstachel:</spellName> Schleudert 3&nbsp;Stacheln und <status>verlangsamt</status> um {{ spell.khazixw:effect3amount }}&nbsp;% (erhöht gegen <keywordMajor>isolierte</keywordMajor> Ziele).<li><spellName>Sprung:</spellName> Erhöhte Reichweite und die Abklingzeit wird bei Kills/Unterstützungen zurückgesetzt.<li><spellName>Leerenangriff:</spellName> Die <keywordStealth>Unsichtbarkeit</keywordStealth> hält {{ evolvedstealthduration }}&nbsp;Sekunden lang an und eine <recast>Reaktivierung</recast> ist möglich.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Verfügbare Evolutionen", "Abklingzeit"], "effect": ["{{ evolutionsavailable }} -> {{ evolutionsavailableNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "KhazixR.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Verborgene <PERSON>hung", "description": "<PERSON><PERSON>, die von ihren Verbündeten <font color='#FFF673'>isoliert</font> sind, werden markiert. Kha'Zix' Fähigkeiten interagieren besonders mit <font color='#FFF673'>isolierten</font> Zielen.<br><br><PERSON><PERSON>'Zix für das gegnerische Team nicht sichtbar ist, wird er zur „verborgenen Bedrohung“, wodurch sein nächster normaler Angriff auf einen gegnerischen Champion zusätzlichen magischen Schaden verursacht und diesen einige Sekunden lang verlangsamt.", "image": {"full": "Khazix_P.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}