{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Corki": {"id": "<PERSON><PERSON>", "key": "42", "name": "<PERSON><PERSON>", "title": "der kühne Bombenschütze", "image": {"full": "Corki.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "42000", "num": 0, "name": "default", "chromas": false}, {"id": "42001", "num": 1, "name": "UFO-<PERSON><PERSON>", "chromas": false}, {"id": "42002", "num": 2, "name": "Rennschlitten-Corki", "chromas": false}, {"id": "42003", "num": 3, "name": "Roter <PERSON>", "chromas": false}, {"id": "42004", "num": 4, "name": "Frisierter <PERSON>", "chromas": false}, {"id": "42005", "num": 5, "name": "Urfreiter-Corki", "chromas": false}, {"id": "42006", "num": 6, "name": "Drachenschwingen-Corki", "chromas": true}, {"id": "42007", "num": 7, "name": "Fnatic-Corki", "chromas": false}, {"id": "42008", "num": 8, "name": "Arcade-<PERSON><PERSON>", "chromas": true}, {"id": "42018", "num": 18, "name": "Corgi-<PERSON><PERSON>", "chromas": true}, {"id": "42026", "num": 26, "name": "Astronauten-Corki", "chromas": true}], "lore": "Der Yordle-Pilot Corki liebt zwei Dinge am meisten: Fliegen und seinen prächtigen Schnauzbart … wobei seine Prioritäten nicht ganz klar sind. Nachdem er Bandle verlassen hatte, ließ er sich in Piltover nieder und verliebte sich in dessen wundersame Maschinen. Er verschrieb sich der Entwicklung von Fluggeräten und leitet eine Luftwaffentruppe aus in die Jahre gekommenen Veteranen, die „Dröhnenden Jodelnattern“. Corki behält auch unter Beschuss die Nerven, patrouilliert am Himmel über seinem neuen Zuhause und ist noch auf kein Problem getroffen, das sein Raketenwerfer nicht lösen konnte.", "blurb": "Der Yordle-Pilot <PERSON>i liebt zwei Dinge am meisten: Fliegen und seinen prächtigen Schnauzbart … wobei seine Prioritäten nicht ganz klar sind. Nachdem er Bandle verlassen hatte, ließ er sich in Piltover nieder und verliebte sich in dessen wundersame...", "allytips": ["„Phosphorbombe“ kann Gegner aufdecken, die sich in nahen hohen Gräsern versteckt halten.", "„Walküre“ kann auch defensiv angewendet werden, um schnell zu entkommen.", "<PERSON><PERSON> kann auch während der Nutzung von „Repetiergeschütz“ weiter angreifen. Das voll ausgebaute „Repetiergeschütz“ ist der Schlüssel, <PERSON><PERSON> zu meistern."], "enemytips": ["Achte auf Corkis Raketenwerfer. Er verursacht Flächenschaden und kann dich so auch hinter Vasallen treffen.", "<PERSON><PERSON> ist nach „Walküre“ oder „Sonderzustellung“ verwund<PERSON>, konzentriere dich auf ihn, wenn er sie benutzt hat, um einen Kampf zu erö<PERSON>nen."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 3, "magic": 6, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 100, "mp": 350, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2, "attackspeedperlevel": 2.8, "attackspeed": 0.644}, "spells": [{"id": "PhosphorusBomb", "name": "Phosph<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> feuert eine <PERSON>uchtbombe auf einen Zielort ab und verursacht magischen Schaden an Gegnern im Bereich. Dieser Angriff deckt zudem Einheiten im Bereich für kurze Zeit auf.", "tooltip": "<PERSON>i wirft eine Bombe, die <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Der Bereich und die getroffenen Champions werden {{ revealduration }}&nbsp;Sekunden lang aufgedeckt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "PhosphorusBomb.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CarpetBomb", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> fliegt über kurze Distanz und lässt dabei Bomben fallen, die eine Feuerspur hinterlassen. <PERSON><PERSON><PERSON><PERSON>, die in dieser stehen<PERSON>iben, wird <PERSON> zugefügt.", "tooltip": "<PERSON><PERSON> fliegt eine Strecke ab und versengt {{ trailduration }}&nbsp;Sekunden lang den Boden. Gegner im Feuer erleiden über die Dauer bis zu <magicDamage>{{ maximumdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden über Zeit", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "CarpetBomb.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GGun", "name": "Repetiergeschütz", "description": "Corkis Repetiergeschütz feuert schnell in einem Kegel vor ihm, verursacht Schaden und verringert Rüstung sowie Magieresistenz von getroffenen Gegnern.", "tooltip": "<PERSON><PERSON> schießt mit dem Repetiergeschütz auf Gegner vor ihm, fügt ihnen über {{ sprayduration }}&nbsp;Sekunden hinweg <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und verringert ihre <scaleArmor>Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR> um bis zu {{ shredmax*-1 }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verteidigungsverringerung", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredmax*-1.000000 }} -> {{ shredmaxnl*-1.000000 }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GGun.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissileBarrage", "name": "Raketenwerfer", "description": "<PERSON>i feuert eine Rakete in Richtung des Zielorts ab, die beim Aufprall explodiert und Schaden an Gegnern im Bereich verursacht. <PERSON>i sammelt nach und nach bis zu einem festgelegten Maximum Raketen an. Jede 3.&nbsp;abgefeuerte Rakete ist eine besonders große, die zusätzlichen Schaden verursacht.", "tooltip": "<PERSON>i feuert ein Geschoss ab, das beim ersten getroffenen Gegner explodiert und Gegnern in der Nähe <physicalDamage>{{ rsmallmissiledamage }}&nbsp;normalen Schaden</physicalDamage> zufügt. Jedes dritte Geschoss verursacht stattdessen <physicalDamage>{{ rbigmissiledamage }} normalen Schaden</physicalDamage>.<br /><br />Diese Fähigkeit hat bis zu {{ maxammotooltip }} Aufladungen. Normale Angriffe gegen Champions verringern die Zeit zwischen Aufladungen um <attention>{{ attackrefund }}</attention>&nbsp;Sekunden pro Treffer.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [2, 2, 2], "cooldownBurn": "2", "cost": [35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "4", "range": [1225, 1225, 1225], "rangeBurn": "1225", "image": {"full": "MissileBarrage.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Hextech-Munition", "description": "Corkis normale Angriffe verursachen zusätzlichen <trueDamage>absoluten Schaden</trueDamage> in Höhe eines Prozentsatzes des Angriffsschadens.", "image": {"full": "Corki_RapidReload.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}