{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Khazix": {"id": "Khazix", "key": "121", "name": "카직스", "title": "공허의 약탈자", "image": {"full": "Khazix.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "121000", "num": 0, "name": "default", "chromas": false}, {"id": "121001", "num": 1, "name": "메카 카직스", "chromas": true}, {"id": "121002", "num": 2, "name": "사막의 수호자 카직스", "chromas": false}, {"id": "121003", "num": 3, "name": "죽음의 꽃 카직스", "chromas": false}, {"id": "121004", "num": 4, "name": "암흑의 별 카직스", "chromas": false}, {"id": "121011", "num": 11, "name": "2018 월드 챔피언십 카직스", "chromas": true}, {"id": "121060", "num": 60, "name": "오디세이 카직스", "chromas": false}, {"id": "121069", "num": 69, "name": "달빛 수호자 카직스", "chromas": false}, {"id": "121079", "num": 79, "name": "야성의 수정 카직스", "chromas": false}], "lore": "'공허는 성장하고, 공허는 적응한다.' 공허태생 생명체는 많지만, 이 진리를 카직스보다 더 명백히 입증하는 존재는 없다. 카직스가 공포스러운 것은 진화를 거듭하기 때문이다. 생존을 위해 사냥을 하고, 사냥감을 집어삼킬 때마다 강해지고, 더 강한 상대를 찾아 또다시 사냥에 나선다. 게다가 진화를 할수록 먹이를 낚아채는 사냥 기술은 더 새로워지고 효율도 높아진다. 공허에서 갓 넘어왔을 때에는 아무 생각 없는 야수에 불과했으나, 이제는 그 형체만큼이나 지능도 완전히 변모했다. 지금의 카직스는 세심한 계획을 세워 사냥을 하고, 자신의 모습을 본 희생양이 본능적으로 일으키는 공포심을 활용하기도 한다.", "blurb": "'공허는 성장하고, 공허는 적응한다.' 공허태생 생명체는 많지만, 이 진리를 카직스보다 더 명백히 입증하는 존재는 없다. 카직스가 공포스러운 것은 진화를 거듭하기 때문이다. 생존을 위해 사냥을 하고, 사냥감을 집어삼킬 때마다 강해지고, 더 강한 상대를 찾아 또다시 사냥에 나선다. 게다가 진화를 할수록 먹이를 낚아채는 사냥 기술은 더 새로워지고 효율도 높아진다. 공허에서 갓 넘어왔을 때에는 아무 생각 없는 야수에 불과했으나, 이제는 그 형체만큼이나...", "allytips": ["가까운 범위 내에 아군이 없으면 적은 고립된 것으로 간주됩니다. 이런 적에게는 공포 감지 스킬의 피해량이 대폭 증가합니다.", "보이지 않는 위협은 카직스가 적의 시야에 노출되지 않을 때 발동됩니다. 수풀에 들어가거나 공허의 습격을 시전하면 보이지 않는 위협이 재발동됩니다. 적 챔피언을 자동 공격할 때는 계속 보이지 않는 위협을 적용해 주세요.", "카직스는 교전 시점과 위치를 자유자재로 선택할 수 있습니다. 이 점을 잘 활용하면 이길 수 있습니다."], "enemytips": ["공포 감지는 고립된 대상에게 추가 피해를 입히므로, 아군 미니언이나 챔피언, 포탑 근처에서 싸워야 유리합니다.", "도약과 공허의 습격은 재사용 대기시간이 깁니다. 두 스킬을 쓸 수 없을 땐 카직스가 매우 취약해집니다."], "tags": ["Assassin"], "partype": "마나", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 6}, "stats": {"hp": 643, "hpperlevel": 99, "mp": 327, "mpperlevel": 40, "movespeed": 350, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.59, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.668}, "spells": [{"id": "KhazixQ", "name": "공포 감지", "description": "대상에게 물리 피해를 줍니다. <font color='#FFF673'>고립</font>된 대상에게는 피해량이 늘어납니다. <font color='#00DD33'>거대 갈고리 진화</font>를 <font color='#FFF673'>고립</font>된 대상에게 사용 시, 재사용 대기시간이 일부 감소합니다. 또한, 카직스의 기본 공격과 공포 감지 범위도 증가합니다.", "tooltip": "카직스가 근처 적을 공격해 <physicalDamage>{{ spell.khazixq:basedamage }}의 물리 피해</physicalDamage>를 입힙니다. 아군으로부터 <keywordMajor>고립</keywordMajor>된 적에게는 <physicalDamage>{{ spell.khazixq:isodamage }}의 피해</physicalDamage>를 입힙니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [80, 105, 130, 155, 180], [110, 110, 110, 110, 110], [50, 50, 50, 50, 50], [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/105/130/155/180", "110", "50", "45", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "KhazixQ.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KhazixW", "name": "공허의 가시", "description": "카직스가 폭발하는 가시를 발사하여 적중한 적에게 물리 피해를 가합니다. 폭발 범위 안에 있으면 카직스의 체력이 회복됩니다. <font color='#00DD33'>가시 어깨 진화</font>를 선택하면, 공허의 가시 세 개가 원뿔 형태로 발사되며 적중한 적에게 둔화를 걸고 2초 동안 적 챔피언을 드러냅니다. <font color='#FFF673'>고립</font>된 대상에게는 둔화 효과가 증가합니다.", "tooltip": "카직스가 가시를 발사하여 처음 적중하는 적과 그 주변 좁은 반경에 <physicalDamage>{{ basedamage }}의 물리 피해</physicalDamage>를 입힙니다. 카직스가 폭발 반경 내에 있으면 <healing>체력을 {{ healamount }}</healing> 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "회복량", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [55, 75, 95, 115, 135], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "55/75/95/115/135", "40", "2", "0", "2", "60", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KhazixW.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KhazixE", "name": "도약", "description": "카직스가 도약하여, 착지할 때 물리 피해를 가합니다. <font color='#00DD33'>날개 진화</font>를 선택하면 도약 범위가 200 증가하며, 킬이나 어시스트를 기록하면 도약의 재사용 대기시간이 초기화됩니다.", "tooltip": "카직스가 도약 후 착지하며 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [30, 35, 40, 45, 50], [10, 10, 10, 10, 10], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "30/35/40/45/50", "10", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KhazixE.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KhazixR", "name": "공허의 습격", "description": "카직스의 스킬을 하나 진화시켜 고유한 추가 효과를 부여합니다. 공허의 습격을 활성화하면 카직스가 <font color='#91d7ee'>은신</font> 상태가 되고 보이지 않는 위협이 발동되어, 이동 속도가 증가합니다. <font color='#00DD33'>활성 보호색 진화</font>를 선택하면, <font color='#91d7ee'>은신</font>의 지속시간이 증가하며 공허의 습격을 추가로 사용할 수 있습니다.", "tooltip": "<spellActive>사용 시:</spellActive> 카직스가 {{ stealthduration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 되고 <speed>이동 속도가 {{ bonusmovementspeedpercent*100 }}%</speed> 상승합니다. {{ recastwindow }}초 안에 이 스킬을 <recast>재사용</recast>할 수 있습니다.<br /><br /><spellPassive>기본 지속 효과:</spellPassive> 이 스킬을 레벨 업하면 <evolve>진화</evolve>를 통해 스킬 하나에 추가 효과를 부여합니다.<li><spellName>공포 감지:</spellName> 스킬 및 기본 공격 사거리가 늘어나고 <keywordMajor>고립</keywordMajor>된 대상에게 사용 시 재사용 대기시간이 {{ spell.khazixq:effect4amount }}% 감소합니다.<li><spellName>공허의 가시:</spellName> 가시를 세 개 발사하고 적을 {{ spell.khazixw:effect3amount }}% <status>둔화</status>합니다. <keywordMajor>고립</keywordMajor>된 대상에게는 효과가 증가합니다.<li><spellName>도약:</spellName> 사거리가 늘어나고 챔피언 처치 관여 시 재사용 대기시간이 초기화됩니다.<li><spellName>공허의 습격:</spellName> {{ evolvedstealthduration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 되고 2회 <recast>재사용</recast>할 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["진화 가능", "재사용 대기시간"], "effect": ["{{ evolutionsavailable }} -> {{ evolutionsavailableNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "KhazixR.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "보이지 않는 위협", "description": "동료로부터 <font color='#FFF673'>고립</font>된 근처 적에게 표식이 남습니다. <font color='#FFF673'>고립</font>된 대상에게는 카직스의 스킬이 추가 효과를 발휘합니다.<br><br>카직스는 적의 시야에 노출되지 않을 때 보이지 않는 위협 효과를 받아, 다음 기본 공격으로 적 챔피언에게 추가 마법 피해를 입히고 몇 초간 둔화를 적용합니다.", "image": {"full": "Khazix_P.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}