{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taric": {"id": "<PERSON><PERSON>", "key": "44", "name": "Тарик", "title": "Щит Валорана", "image": {"full": "Taric.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "44000", "num": 0, "name": "default", "chromas": false}, {"id": "44001", "num": 1, "name": "Изумрудный Тарик", "chromas": false}, {"id": "44002", "num": 2, "name": "Тарик в броне Пятой эпохи", "chromas": false}, {"id": "44003", "num": 3, "name": "Рубиновый Тарик", "chromas": false}, {"id": "44004", "num": 4, "name": "Тусовый Тарик", "chromas": true}, {"id": "44009", "num": 9, "name": "Тарик Светлый Щит", "chromas": true}, {"id": "44018", "num": 18, "name": "Тарик из Галактики грува", "chromas": true}, {"id": "44027", "num": 27, "name": "Борец с судьбой Тарик", "chromas": true}], "lore": "Тарик – Сущность Заступника, могучий поборник жизни, любви и красоты на Рунтерре. Однажды Тарик пренебрег долгом, за что был предан позору и изгнан из родной Демасии. Он совершил восхождение на гору Таргон, чтобы обрести там искупление, однако, дойдя до вершины, услышал зов, шедший от самых звезд. Пропитавшись могуществом древнего Таргона, Щит Валорана стал неусыпным стражем, противостоящим коварству Бездны.", "blurb": "Тарик – Сущность Заступника, могучий поборник жизни, любви и красоты на Рунтерре. Однажды Тарик пренебрег долгом, за что был предан позору и изгнан из родной Демасии. Он совершил восхождение на гору Таргон, чтобы обрести там искупление, однако, дойдя до...", "allytips": ["Сокращающий перезарядку эффект Бравады делает такие предметы, как Ледяное сердце, Хладорожденная рукавица и Облачение духов чрезвычайно полезными для Тарика.", "Использование не полностью заряженного Прикосновения звезд сделает лечение более маназатратным, зато позволит существенно увеличить наносимый Тариком урон благодаря эффекту Бравады.", "Если вы уверены в неизбежности командного боя, то вместо того чтобы беречь Космическое сияние для решающего момента и рисковать жизнью союзников, можно попробовать использовать это умение за мгновение до начала столкновения с противником."], "enemytips": ["Космического сияние Тарика требует много времени для использования. Старайтесь как можно быстрее оценивать обстановку, чтобы принять решение об отступлении или атаке союзников Тарика прежде, чем он успеет активировать свое абсолютное умение.", "Бравада позволяет Тарику сокращать время перезарядки умений автоатаками. Во время командных боев пробуйте атаковать его с безопасной для вас дистанции. Во время игры на линии наказывайте его за желание приблизиться к волне миньонов."], "tags": ["Support", "Tank"], "partype": "Мана", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 40, "armorperlevel": 4.3, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "TaricQ", "name": "Прикосновение звезд", "description": "Тарик восстанавливает окружающим союзным чемпионам здоровье на величину, зависящую от количества накопленных зарядов. Усиленные Бравадой автоатаки дают заряд Прикосновения звезд.", "tooltip": "<spellPassive>Пассивно:</spellPassive> раз в {{ stackcooldown }} сек. или при применении автоатаки, усиленной <spellName>Бравадой</spellName>, Тарик получает заряд (максимум зарядов - {{ e6 }}).<br /><br /><spellActive>Активно:</spellActive> Тарик поглощает все заряды и восстанавливает <healing>{{ healingperstack }} здоровья</healing> за заряд союзным чемпионам поблизости (<healing>{{ maxstackhealing }} здоровья</healing> при следующем количестве зарядов: {{ e6 }}).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Максимальное количество зарядов", "Максимальное лечение"], "effect": ["{{ e6 }} -> {{ e6NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [1, 1, 1, 1, 1], [0.15, 0.3, 0.45, 0.6, 0.75], [0.75, 1.5, 2.25, 3, 3.75], [25, 50, 75, 100, 125], [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "1", "0.15/0.3/0.45/0.6/0.75", "0.75/1.5/2.25/3/3.75", "25/50/75/100/125", "1/2/3/4/5", "0", "0", "0", "0"], "vars": [], "costType": " маны, все заряды", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "TaricQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} маны, все заряды"}, {"id": "TaricW", "name": "Бастион", "description": "Пассивно увеличивает броню Тарика и союзника, на которого наложен Бастион.<br><br>При активации умения Тарик накладывает Бастион на союзного чемпиона, укрывая его от урона щитом. Бастион остается на союзнике до тех пор, пока чемпионы не разойдутся достаточно далеко, чтобы разорвать связь. Все умения Тарика одновременно используются и им, и союзником с Бастионом.", "tooltip": "<spellPassive>Пассивно:</spellPassive> <scaleArmor>броня</scaleArmor> Тарика увеличена на <scaleArmor>{{ bonusarmor }}</scaleArmor>, и он поддерживает связь между собой и союзником, помеченным этим умением. Пока они находятся друг около друга, все умения Тарика одновременно используются и им, и этим союзником, а <scaleArmor>броня</scaleArmor> союзника увеличена на <scaleArmor>{{ bonusarmor }}</scaleArmor>.<br /><br /><spellPassive>Активно:</spellPassive> Тарик помечает союзного чемпиона, накладывая на него <shield>щит прочностью {{ e2 }}% от максимального запаса здоровья</shield> на {{ e3 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Пассивное увеличение брони", "Коэффициент прочности щита"], "effect": ["{{ armorbonuspercentage*100.000000 }}% -> {{ armorbonuspercentagenl*100.000000 }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [7, 8, 9, 10, 11], [2.5, 2.5, 2.5, 2.5, 2.5], [1000, 1000, 1000, 1000, 1000], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "7/8/9/10/11", "2.5", "1000", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "TaricW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TaricE", "name": "Ослепление", "description": "Тарик подготавливает к использованию луч звездного света, который после короткой задержки наносит магический урон и оглушает противников.", "tooltip": "Тарик подготавливает луч звездного света, который по истечении {{ e3 }} сек. наносит врагам <magicDamage>{{ totaldamage }} магического урона</magicDamage> и <status>оглушает</status> их на {{ e2 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [90, 130, 170, 210, 250], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/130/170/210/250", "1.5", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "TaricE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TaricR", "name": "Космическое сияние", "description": "После некоторой задержки Тарик направляет космическую энергию на окружающих союзных чемпионов, делая их на короткое время неуязвимыми.", "tooltip": "Тарик призывает защитную энергию небес. Через {{ initialdelay }} сек. союзные чемпионы поблизости становятся неуязвимыми на {{ invulnduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "TaricR.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Бравада", "description": "После каждого использования умения Тарик проводит 2 следующие автоатаки в быстрой последовательности. Также эти автоатаки усилены: они дополнительно наносят магический урон и сокращают перезарядку базовых умений.", "image": {"full": "Taric_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}