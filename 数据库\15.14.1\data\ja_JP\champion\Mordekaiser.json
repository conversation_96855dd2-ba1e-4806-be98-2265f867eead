{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mordekaiser": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "82", "name": "モルデカイザー", "title": "鋼の魂奪者", "image": {"full": "Mordekaiser.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "82000", "num": 0, "name": "default", "chromas": false}, {"id": "82001", "num": 1, "name": "龍騎士モルデカイザー", "chromas": false}, {"id": "82002", "num": 2, "name": "地獄の業火モルデカイザー", "chromas": false}, {"id": "82003", "num": 3, "name": "Pentakill モルデカイザー", "chromas": false}, {"id": "82004", "num": 4, "name": "冥王モルデカイザー", "chromas": false}, {"id": "82005", "num": 5, "name": "クラブキング モルデカイザー", "chromas": false}, {"id": "82006", "num": 6, "name": "ダークスター モルデカイザー", "chromas": true}, {"id": "82013", "num": 13, "name": "PROJECT: <PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "82023", "num": 23, "name": "Pentakill III: ロストチャプター モルデカイザー", "chromas": true}, {"id": "82032", "num": 32, "name": "荒野のモルデカイザー", "chromas": true}, {"id": "82042", "num": 42, "name": "灰の重騎士モルデカイザー", "chromas": true}, {"id": "82044", "num": 44, "name": "古の神モルデカイザー", "chromas": true}, {"id": "82054", "num": 54, "name": "サン・ウザル モルデカイザー", "chromas": false}], "lore": "二度殺され、三度生まれたモルデカイザーは死霊術によって人の魂を拘束し、彼らを永遠の奴隷に変えてしまう、古の時代の残忍な武闘王である。彼の過去の覇業を覚えている者や、真の力を知る者はほとんど残っていないが、モルデカイザーを知るわずかな者たちは、彼が再び現れて生ける者も死せる者も支配してしまう日が来ることを恐れている。", "blurb": "二度殺され、三度生まれたモルデカイザーは死霊術によって人の魂を拘束し、彼らを永遠の奴隷に変えてしまう、古の時代の残忍な武闘王である。彼の過去の覇業を覚えている者や、真の力を知る者はほとんど残っていないが、モルデカイザーを知るわずかな者たちは、彼が再び現れて生ける者も死せる者も支配してしまう日が来ることを恐れている。", "allytips": ["攻撃は最大の防御なり。戦い続ければ、次第に「不滅の鎧」が強固になっていく。", "1つのスキルを複数のチャンピオンに命中させると、短時間で「無窮の闇」を発動できるようになる。", "体力が低下している敵に「死の国」を使って確実に仕留め、チームファイトが終わるまで奪ったステータスを保持しよう。"], "enemytips": ["モルデカイザーはチャンピオンとの戦闘時に、ダメージを与える強力なオーラを発生させる。できるだけ距離を置いて戦おう。", "モルデカイザーが与えたダメージはシールドへと変換可能。また、シールドを消費することで体力を回復することもできる。", "「死の国」を発動されると、味方の力が全く及ばない場所へと飛ばされてしまう。引きずりこまれたときに備えて、移動系のスキルは温存しておくといいだろう"], "tags": ["Fighter", "Mage"], "partype": "シールド", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 645, "hpperlevel": 104, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 37, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 4, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "MordekaiserQ", "name": "滅魂の一撃", "description": "地面にメイスを叩きつけ、命中したすべての敵にダメージを与える。対象が1体のみだった場合はダメージが上昇する。", "tooltip": "ナイトフォールを地面に叩きつけ、<magicDamage>{{ qdamage }}の魔法ダメージ</magicDamage>を与える。命中した敵が1体のみだった場合はダメージが<magicDamage>{{ empowereddamagetooltip }}</magicDamage>に上昇する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "単体ダメージ", "クールダウン"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ isolationscalar*100.000000 }}% -> {{ isolationscalarnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "MordekaiserQ.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "MordekaiserW", "name": "不滅の鎧", "description": "与えたダメージや受けたダメージを蓄え、シールドを作り出す。シールドを消費して体力を回復することも可能。", "tooltip": "<passive>自動効果:</passive> 与えたダメージの{{ damageconversion*100 }}%と受けたダメージの{{ damagetakenconversion*100 }}%を蓄える。<br /><br /><active>発動効果:</active> 蓄えたダメージを<shield>シールド</shield>として獲得する。<recast>再発動</recast>すると<healing>残ったシールド量の{{ healingpercent*100 }}%を体力</healing>として回復する。<br /><br />最小シールド量: <shield>{{ minhealthtooltip }}</shield><br />最大シールド量: <shield>{{ maxhealthtooltip }}</shield>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["回復率", "クールダウン"], "effect": ["{{ healingpercent*100.000000 }}% -> {{ healingpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MordekaiserW.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "MordekaiserE", "name": "死の呪縛", "description": "範囲内にいるすべての敵を引き寄せる。", "tooltip": "<spellPassive>自動効果:</spellPassive> {{ magicpen*100 }}%の魔法防御貫通を獲得する。<br /><br /><spellActive>発動効果:</spellActive> 敵を自身の方向に引き寄せ、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["魔法防御貫通率", "ダメージ", "クールダウン"], "effect": ["{{ magicpen*100.000000 }}% -> {{ magicpennl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "MordekaiserE.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "MordekaiserR", "name": "死の国", "description": "獲物を別の次元へと引きずり込み、ステータスの一部を奪い取る。対象を倒した場合は、その対象が復活するまで奪ったステータスが維持される。", "tooltip": "チャンピオン1体を{{ spiritrealmduration }}秒間冥界へと引きずりこみ、その間、敵のコアステータスの{{ statstealpercentscalar*100 }}%を奪い取る。<br /><br />冥界の中で敵を倒した場合は相手の魂を吸収し、その対象が復活するまで奪ったステータスが維持される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "MordekaiserR.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "無窮の闇", "description": "チャンピオンまたはモンスターに通常攻撃かスキルを3回命中させると、ダメージを与える強力なオーラを発生させ、移動速度が増加する。", "image": {"full": "MordekaiserPassive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}