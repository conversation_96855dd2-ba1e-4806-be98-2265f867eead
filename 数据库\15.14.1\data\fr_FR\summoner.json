{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Barrière", "description": "Vous gagnez brièvement un bouclier.", "tooltip": "Vous gagnez un bouclier de <shield>{{ shieldstrength }} PV</shield> pendant {{ shieldduration }} sec.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerBoost": {"id": "SummonerBoost", "name": "Purge", "description": "Dissipe toutes les entraves (sauf les neutralisations et les projections dans les airs) et tous les effets de sorts d'invocateur qui affectent votre champion, en plus d'augmenter votre ténacité.", "tooltip": "Dissipe tous les contrôles de foule (sauf les <keyword>neutralisations</keyword> et les <keyword>projections dans les airs</keyword>) et tous les effets de sorts d'invocateur qui affectent votre champion, en plus d'augmenter votre ténacité de {{ tenacityvalue*100 }}% pendant {{ tenacityduration }} sec.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "Saut éclair", "description": "Téléporte votre champion sur une courte distance vers l'emplacement de votre curseur.", "tooltip": "Téléporte votre champion sur une courte distance vers l'emplacement de votre curseur.<br /><br />Ne peut pas être relancé avant la manche suivante <rules>(une manche est à la fois composée de la phase d'achat et de la phase de combat).</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "Fuite", "description": "Vous gagnez un bref bonus en vitesse de déplacement. Ce bonus est augmenté lorsque vous vous éloignez de champions ennemis.", "tooltip": "<keywordMajor>Emplacement de sort actif :</keywordMajor> les optimisations qui confèrent un sort d'invocateur remplaceront cet emplacement.<br /><br />Vous gagnez <moveSpeed>+{{ basems*100 }}% de vitesse de déplacement</moveSpeed> pendant {{ duration }} sec. Ce bonus augmente à +{{ bonusmsperenemybehind*100 }}% pour chaque ennemi derrière vous.", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerDot": {"id": "SummonerDot", "name": "Embrasement", "description": "Inflige des dégâts bruts sur la durée au champion ennemi ciblé et réduit les effets des soins dont il bénéficie pendant la durée de l'effet.", "tooltip": "Inflige <trueDamage>{{ tooltiptruedamagecalculation }} pts de dégâts bruts</trueDamage> au champion ennemi ciblé en 5 sec et inflige <keyword>{{ grievousamount*100 }}% d'Hémorragie</keyword> pour la durée de l'effet.<br /><br /><keyword>Hémorragie</keyword> : réduit l'efficacité des soins et des effets de régénération.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "Fatigue", "description": "Ra<PERSON><PERSON> le champion ennemi ciblé et réduit les dégâts qu'il inflige.", "tooltip": "<keyword>Ralentit</keyword> le champion ennemi ciblé de {{ slow }}% et réduit les dégâts qu'il inflige de {{ damagereduction }}% pendant {{ debuffduration }} sec.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerFlash": {"id": "SummonerFlash", "name": "Saut éclair", "description": "Vous téléporte sur une courte distance vers l'emplacement de votre curseur.", "tooltip": "Vous téléporte sur une courte distance vers l'emplacement de votre curseur.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerHaste": {"id": "SummonerHaste", "name": "<PERSON><PERSON><PERSON>", "description": "Vous gagnez de la vitesse de déplacement et ignorez les collisions avec les unités pendant la durée de l'effet.", "tooltip": "Vous gagnez <speed>+{{ movespeedmod }} vitesse de d<PERSON></speed> et devenez <keyword>fantomatique</keyword> pendant {{ duration }} sec.<br /><br /><keyword>Fantomatique</keyword> : capable de traverser les autres unités.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerHeal": {"id": "SummonerHeal", "name": "Soins", "description": "Rend des PV à votre champion et au champion allié ciblé et augmente vos vitesses de déplacement.", "tooltip": "Rend <healing>{{ totalheal }} PV</healing> à votre champion et au champion allié ciblé, en plus d'augmenter vos <speed>vitesses de déplacement de {{ movespeed*100 }}%</speed> pendant {{ movespeedduration }} sec.<br /><br /><rules>Si le sort n'a pas de cible, il sera lancé sur le champion allié le plus mal en point à portée.<br />Le soin est divisé par deux sur les unités récemment touchées par le sort d'invocateur Soins.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "<PERSON><PERSON><PERSON>", "description": "Rend du mana à votre champion et aux champions alliés proches.", "tooltip": "Rend {{ e1 }}% de son mana max à votre champion et {{ e2 }}% de leur mana max aux alliés proches.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "Au roi !", "description": "Rendez-vous rapidement près du roi des Poros.", "tooltip": "<span class=\"colorFFE076\">Passive :</span> toucher un champion ennemi avec un Poro octroie une Marque de Poro à votre équipe. À 10 Marques de Poro, votre équipe invoque le roi des Poros pour qu'il combatte à vos côtés. Tant que le roi des Poros est actif, les équipes ne gagnent aucune Marque de Poro.<br /><br /><span class=\"colorFFE076\">Active :</span> permet de foncer rapidement auprès du roi des Poros. Ne peut être utilisé que si le roi des Poros a été invoqué par votre équipe. <br /><br /><i><span class=\"colorFDD017\">''Les Poros font vibrer votre corde sensible, et ce sont ces vibrations qui vous rapprochent d'eux.''</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "Jet de Poro", "description": "Lancez un Poro sur vos ennemis. S'il touche une cible, vous pouvez vous rendre rapidement près de la cible.", "tooltip": "Lance un Poro sur une longue distance, infligeant {{ f2 }} pts de dégâts bruts au premier ennemi touché et vous octroyant une <span class=\"coloree91d7\">vision pure</span> de la cible.<br /><br />Si le Poro touche un ennemi, cette compétence peut être réactivée dans les 3 sec pour foncer sur la cible touchée, infligeant de nouveau {{ f2 }} pts de dégâts bruts et réduisant de {{ e4 }} sec le délai de récupération du prochain Jet de Poro.<br /><br />Les Poros ne sont pas bloqués par les boucliers antisorts ou par les murs de vent, parce que ce sont des animaux, pas des sorts !<br /><br /><i><span class=\"colorFDD017\">''Les Poros sont la parfaite incarnation de l'aérodynamisme runeterran.''</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerSmite": {"id": "SummonerSmite", "name": "Châtiment", "description": "Vous infligez des dégâts bruts au monstre ou au sbire ciblé.", "tooltip": "Vous infligez <trueDamage>{{ smitebasedamage }} pts de dégâts bruts</trueDamage> au grand monstre ou au sbire ciblé.<br /><br />Vous infligez <trueDamage>{{ firstpvpdamage }} pts de dégâts bruts</trueDamage> aux familiers de champions.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "Pas <PERSON>", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "Marquage", "description": "Lancez une boule de neige en ligne droite. Si elle touche un ennemi, celui-ci est marqué, vous obtenez une vision pure de cet ennemi et votre champion peut foncer vers lui.", "tooltip": "Lancez une boule de neige sur une longue distance, infligeant {{ tooltipdamagetotal }} pts de dégâts bruts à la première unité ennemie touchée et vous octroyant une <span class=\"coloree91d7\">vision pure</span> de la cible. Si la boule touche un ennemi, ce sort peut être relancé dans les {{ e3 }} sec pour foncer vers l'unité marquée et infliger {{ tooltipdamagetotal }} pts de dégâts bruts supplémentaires. Foncer vers la cible réduira de {{ e4 }}% le délai de récupération de Marquage.<br /><br /><span class=\"colorFFFF00\">Les projectiles de Marquage ne sont pas affectés par les boucliers antisorts ou par l'affaiblissement des projectiles.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "Marquage", "description": "Lancez une boule de neige en ligne droite. Si elle touche un ennemi, celui-ci est marqué, vous obtenez une vision pure de cet ennemi et votre champion peut foncer vers lui.", "tooltip": "Lancez une boule de neige sur une longue distance, infligeant {{ tooltipdamagetotal }} pts de dégâts bruts à la première unité ennemie touchée et vous octroyant une <span class=\"coloree91d7\">vision pure</span> de la cible. Si la boule touche un ennemi, ce sort peut être relancé dans les {{ e3 }} sec pour foncer vers l'unité marquée et infliger {{ tooltipdamagetotal }} pts de dégâts bruts supplémentaires. Foncer vers la cible réduira de {{ e4 }}% le délai de récupération de Marquage.<br /><br /><span class=\"colorFFFF00\">Les projectiles de Marquage ne sont pas affectés par les boucliers antisorts ou par l'affaiblissement des projectiles.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "Téléportation", "description": "Après une courte canalisation, vous devenez impossible à cibler et voyagez jusqu'à l'unité alliée ciblée. S'améliore en Téléportation débridée, ce qui augmente considérablement la vitesse du voyage. ", "tooltip": "Après {{ channelduration }} sec de canalisation, vous devenez <keyword>impossible à cibler</keyword> et vous voyagez jusqu'à la balise, le bâtiment ou le sbire allié ciblé. <br /><br />S'améliore en Téléportation débridée après {{ upgrademinute }} minutes, ce qui augmente considérablement la vitesse du voyage.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "Choisi en début de partie", "description": "Cet emplacement sera remplacé par l'ultime d'un autre champion sélectionné au début de la partie. Vous aurez 30 sec pour sélectionner un ultime. Préparez-vous !", "tooltip": "Sera remplacé par le sort d'invocateur ultime sélectionné.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "Bouche-trou et Attaque-Châtiment", "description": "Cet emplacement sera remplacé par l'ultime d'un autre champion et vous gagnerez Attaque-Châtiment. Vous aurez 30 sec pour sélectionner un ultime. Préparez-vous !", "tooltip": "Sera remplacé par votre sort d'invocateur ultime.<br /><br /><PERSON><PERSON> gagnez Attaque-Châtiment. Attaque-Châtiment exécutera les monstres à enchantement alliés, les monstres épiques et les carapateurs que vous attaquerez.<br /><br /><attention>Attaque-Châtiment n'a pas de délai de récupération.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}