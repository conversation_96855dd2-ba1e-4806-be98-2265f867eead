# TFT 弈子(英雄)数据结构详解

## 📋 概述

TFT弈子数据包含了游戏中每个英雄的完整信息，包括基础属性、技能、羁绊等。以下是详细的字段说明。

## 🏗️ 数据结构

### 顶级结构
```json
{
  "type": "champions",
  "version": "15.14.1", 
  "set": "15",
  "language": "zh_CN",
  "count": 79,
  "data": {
    "TFT15_Aatrox": { /* 弈子数据 */ }
  }
}
```

### 单个弈子数据结构

#### 🔑 基础信息
```json
{
  "apiName": "TFT15_Aatrox",     // API内部名称
  "name": "亚托克斯",             // 显示名称
  "icon": "ASSETS/...",          // 弈子头像路径
  "cost": 1,                     // 费用等级 (1-5费)
  "traits": [                    // 羁绊列表
    "超级战队",
    "主宰", 
    "重量级斗士"
  ]
}
```

#### 📊 属性数值 (stats)
```json
"stats": {
  "armor": 40.0,                 // 护甲值
  "attackSpeed": 0.65,           // 攻击速度
  "critChance": 0.25,            // 暴击几率 (25%)
  "critMultiplier": 1.4,         // 暴击倍数 (140%)
  "damage": 50.0,                // 攻击力
  "hp": 650.0,                   // 生命值
  "initialMana": 20.0,           // 初始法力值
  "magicResist": 40.0,           // 魔法抗性
  "mana": 80.0,                  // 最大法力值
  "range": 1.0                   // 攻击距离
}
```

#### ⚡ 技能信息 (ability)
```json
"ability": {
  "desc": "治疗生命值并造成物理伤害...",  // 技能描述
  "icon": "ASSETS/...",                    // 技能图标路径
  "name": "赐死剑气",                      // 技能名称
  "variables": [                           // 技能数值变量
    {
      "name": "ADDamage",                  // 变量名
      "value": [180.0, 100.0, 150.0, ...]  // 各星级数值
    }
  ]
}
```

## 💰 费用等级说明

| 费用 | 特点 | 示例英雄 |
|------|------|----------|
| 1费 | 基础英雄，容易获得 | 亚托克斯、伊泽瑞尔、盖伦 |
| 2费 | 中等强度 | 迦娜、烬、卡莎 |
| 3费 | 较强英雄 | 阿狸、凯特琳、德莱厄斯 |
| 4费 | 高级英雄 | 阿卡丽、艾希、嘉文四世 |
| 5费 | 最强英雄 | 布隆、格温、婕拉 |

## 🎯 属性详解

### 攻击相关
- **damage**: 基础攻击力
- **attackSpeed**: 攻击速度 (每秒攻击次数)
- **critChance**: 暴击几率 (0.25 = 25%)
- **critMultiplier**: 暴击伤害倍数 (1.4 = 140%)
- **range**: 攻击距离 (格数)

### 防御相关  
- **hp**: 生命值
- **armor**: 物理防御力
- **magicResist**: 魔法防御力

### 法力相关
- **mana**: 最大法力值 (释放技能所需)
- **initialMana**: 初始法力值 (战斗开始时的法力)

## 🔗 羁绊系统

弈子可以拥有1-3个羁绊，常见羁绊包括：

### Set 15 主要羁绊
- **战斗学院**: 潜能机制
- **超级战队**: 合体技能
- **星之守护者**: 魔法增强
- **水晶玫瑰**: 水晶机制
- **至高天**: 神圣力量
- **奥德赛**: 宇宙能量

### 职业羁绊
- **法师**: 技能伤害增强
- **狙神**: 远程攻击
- **护卫**: 防御增强
- **决斗大师**: 单挑能力
- **重量级斗士**: 近战坦克

## ⚡ 技能系统

### 技能类型
1. **主动技能**: 需要法力值释放
2. **被动技能**: 持续生效
3. **混合技能**: 包含主动和被动效果

### 技能数值 (variables)
技能数值数组通常包含7个值：
```json
"value": [180.0, 100.0, 150.0, 225.0, 300.0, 2.75, 2.75]
```
- 索引0: 基础值
- 索引1-4: 1-4星级数值
- 索引5-6: 额外参数

### 伤害类型标记
- `<physicalDamage>`: 物理伤害
- `<magicDamage>`: 魔法伤害  
- `<trueDamage>`: 真实伤害
- `<scaleHealth>`: 生命值加成
- `<scaleMana>`: 法力值加成

## 🌟 星级系统

弈子可以通过合成升星：
- **1星**: 基础形态
- **2星**: 3个1星合成，属性翻倍
- **3星**: 3个2星合成，属性再次提升

技能数值会根据星级调整，通常在variables数组的索引1-4位置。

## 🎮 实际应用示例

### 1费弈子 - 亚托克斯
```json
{
  "cost": 1,
  "hp": 650.0,
  "damage": 50.0,
  "traits": ["超级战队", "主宰", "重量级斗士"],
  "ability": {
    "name": "赐死剑气",
    "desc": "治疗并造成伤害，基于已损失生命值提升效果"
  }
}
```

### 5费弈子 - 布隆  
```json
{
  "cost": 5,
  "hp": 1200.0,
  "damage": 80.0,
  "traits": ["魄罗之心", "假面摔角手", "护卫"],
  "ability": {
    "name": "猛虎龙卷风", 
    "desc": "晕眩并投掷敌人，低血量时直接击杀"
  }
}
```

## 🔍 数据使用建议

### 前端展示
1. **弈子卡片**: 显示头像、名称、费用、羁绊
2. **属性面板**: 展示完整数值
3. **技能详情**: 解析技能描述和数值

### 数据处理
1. **星级计算**: 根据星级调整属性
2. **羁绊匹配**: 统计羁绊激活情况  
3. **伤害计算**: 解析技能伤害公式

### 搜索筛选
1. **按费用筛选**: cost字段
2. **按羁绊筛选**: traits数组
3. **按名称搜索**: name字段
4. **按属性排序**: stats对象

## 📝 注意事项

1. **数值精度**: 浮点数可能有精度问题
2. **本地化**: 描述文本包含HTML标记需要处理
3. **版本兼容**: 不同版本数据结构可能有差异
4. **特殊英雄**: 某些英雄有特殊机制(如超级机甲)

## 🛠️ 开发工具

建议使用以下方法处理弈子数据：

```javascript
// 获取弈子基础信息
function getChampionInfo(champion) {
  return {
    name: champion.name,
    cost: champion.cost,
    traits: champion.traits,
    hp: champion.stats.hp,
    damage: champion.stats.damage
  };
}

// 计算星级属性
function getStarStats(champion, star) {
  const multiplier = star === 1 ? 1 : star === 2 ? 2 : 3;
  return {
    hp: champion.stats.hp * multiplier,
    damage: champion.stats.damage * multiplier
  };
}
```

这个数据结构为TFT游戏提供了完整的弈子信息，支持各种游戏功能的实现。
