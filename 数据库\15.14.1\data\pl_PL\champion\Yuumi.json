{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "<PERSON><PERSON>", "title": "Magiczna <PERSON>", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "Bojowa Dyrektor<PERSON>", "chromas": true}, {"id": "350011", "num": 11, "name": "Łamaczka Serc Yuumi", "chromas": true}, {"id": "350019", "num": 19, "name": "Bzzuumi", "chromas": true}, {"id": "350028", "num": 28, "name": "Czaruj<PERSON><PERSON>", "chromas": true}, {"id": "350037", "num": 37, "name": "EDG <PERSON>", "chromas": true}, {"id": "350039", "num": 39, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "350049", "num": 49, "name": "Cyberkotka Yuumi", "chromas": true}, {"id": "350050", "num": 50, "name": "Cyberkotka Yuumi (Prestiżowa)", "chromas": false}, {"id": "350061", "num": 61, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON>, magiczna kotka z Bandle City, by<PERSON> nieg<PERSON>ś chowańcem yord<PERSON> czar<PERSON>i, Norry. <PERSON><PERSON> jej pani z<PERSON>ęła w tajemniczych okolicznościach, <PERSON><PERSON> stała się Strażniczką żywej Księgi Wrót Norry, podróżując przez portale na jej stronach w poszukiwaniu swej właścicielki. Pragn<PERSON><PERSON>, Yuumi poszukuje przyjaznych towarzyszy, którzy wspomogliby ją w podróży, i chroni ich za pomocą świetlistych tarcz oraz swojej nieposkromionej odwagi. Podczas gdy Książka próbuje trzymać się wyznaczonego zadania, Yuumi często oddaje się przyziemnym przyjemnościom takim jak drzemki czy jedzenie ryb. Zawsze powraca jednak do poszukiwań swojej przyjaciółki.", "blurb": "<PERSON><PERSON>, magiczna kotka z Bandle City, by<PERSON> nieg<PERSON>ś chowańcem yordlowej <PERSON>, Norry. Gdy jej pani zniknęła w tajemniczych okolicznościach, <PERSON><PERSON> stała się Strażniczką żywej Księgi Wrót Norry, podróżując przez portale na jej stronach w poszukiwaniu...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "Grasuj<PERSON><PERSON> Pocisk", "description": "<PERSON><PERSON> wystrzel<PERSON><PERSON><PERSON> pocisk, kt<PERSON>ry zadaje obrażenia i spowalnia pierwszy trafiony cel. Pocisk zadaje dodatkowe obrażenia i nakłada wzmocnione spowolnienie, jeżeli dotarcie do celu zajmie mu co najmniej 1,35 sek. <PERSON><PERSON><PERSON> Yuumi znajduje się przy swoim Najlepszym Przyjacielu, spowolnienie zawsze jest wzmocnione i zapewnia dodatkowe obrażenia przy trafieniu jej sojusznikowi.<br><br><PERSON><PERSON> <PERSON><PERSON> jest Przywiązana, pociskiem przez krótki czas można sterować za pomocą kursora myszy.", "tooltip": "<PERSON><PERSON> przywołuje zbłąkany pocisk, kt<PERSON><PERSON> zadaje <magicDamage>{{ totalmissiledamage }} pkt. obrażeń magicznych</magicDamage> pierwszemu trafionemu wrogowi i <status>spowalnia</status> cel o {{ slowamount }}%.<br /><br />G<PERSON> jest <keywordMajor>Przywiązana</keywordMajor>, pociskiem przez krótki czas można sterować za pomocą kursora myszy. Następnie pocisk przyspiesza i leci w linii prostej. Przyspieszony pocisk zadaje <magicDamage>{{ totalmissiledamageempowered }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia</status> cel o {{ empoweredslowamount }}% na {{ empoweredslowduration }} sek.<br /><br /><keywordMajor>Premia Najlepszego Przyjaciela:</keywordMajor> <status>spowolnienie</status> <spellName>Grasuj<PERSON><PERSON>go Pocisku</spellName> z<PERSON><PERSON> bę<PERSON><PERSON> wzmo<PERSON>, a trafienie wrogiego bohatera przyzna Najlepszemu Przyjacielowi <magicDamage>{{ onhitdamagecalc }} pkt. obrażeń magicznych</magicDamage> <OnHit>przy trafieniu %i:OnHit%</OnHit> na {{ buffduration }} sek.<br /><br /><rules>Dodatkowe obrażenia przy trafieniu mogą wzrosnąć o {{ allycritchancemaxamp*100 }}% w zależności od szansy na trafienie krytyczne sojusznika Yuumi.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Koszt many", "Obrażenia podstawowe", "Wzmocnione obrażenia", "Wzmocnione spowolnienie", "Obrażenia przy trafieniu"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "{{ empoweredslowamount }}% -> {{ empoweredslowamountNL }}%", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "YuumiW", "name": "Ty i Ja!", "description": "Yuumi doskakuje do wybranego sojusznika i staje się niemożliwa do obrania za cel przez wszystko poza wieżami. Będąc przy swoim Najlepszym Przyjacielu, zyskuje ona siłę leczenia i tarcz oraz zapewnia swojemu sojusznikowi leczenie przy trafieniu.", "tooltip": "<spellPassive>Biernie:</spellPassive> <PERSON><PERSON><PERSON><PERSON><PERSON> przy swoim <keywordMajor>Najlepszym Przyjacielu</keywordMajor>, <PERSON><PERSON> zyskuje dodatkowe <keywordMajor>{{ healandshieldpower*100 }}% siły leczenia i tarcz</keywordMajor>, a jej sojusznik odzyskuje <healing>{{ healthonhit }} pkt. zdrowia</healing> <OnHit>przy trafieniu %i:OnHit%</OnHit>.<br /><br /><spellActive>Użycie:</spellActive> Yuumi doskakuje do sojuszniczego bohatera i <keywordMajor>Przywiązuje się</keywordMajor> do niego. Gdy Yuumi jest <keywordMajor>Przywiązana</keywordMajor>, podąża za swoim partnerem i jest niemożliwa do obrania za cel przez wszystko z wyjątkiem wież.<br /><br />Efekty <status>unieruchamiające</status> użyte na Yuumi sprawiają, że ta umiejętność odnawia się przez {{ ccattachlockout }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Leczenie przy trafieniu", "Dodatkowa siła leczenia i tarcz"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "YuumiE", "name": "<PERSON><PERSON><PERSON>", "description": "Osłania Yuumi tarczą i zwiększa jej prędkość ruchu oraz prędkość ataku. <PERSON><PERSON><PERSON>umi jest przywiązana, premię otrzymuje jej sojusznik.<br>", "tooltip": "<PERSON><PERSON> osłania się tarczą, kt<PERSON>ra blokuje <shield>{{ totalshielding }} pkt. obrażeń</shield> i zyskuje <attackSpeed>{{ totalattackspeed }}% prędkości ataku</attackSpeed> na {{ msduration }} sek. Podczas utrzymywania się efektu tarczy cel również zyskuje <speed>{{ msamount }}% prędkości ruchu</speed>.<br /><br />Jeś<PERSON> jest <keywordMajor>Przywiązana</keywordMajor>, efekt tej umiejętności przeniesiony jest na sojusznika i dodatkowo przywraca mu <magicDamage>{{ manarestore }} pkt. many</magicDamage>, zwiększone maksymalnie do {{ maxmanapercincrease*100 }}% w zależ<PERSON>ści od brakującej many celu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczy", "Koszt many", "Przywrócenie many", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "YuumiR", "name": "Ostatni Rozdział", "description": "<PERSON><PERSON> wysyła pię<PERSON> fal, kt<PERSON>re zadają obrażenia wrogom i leczą sojuszników. W trakcie używania tej umiejętności Yuumi może się poruszać, przywiązywać i używać Ziuum. Gdy <PERSON> jest przy swoim Najlepszym Przyjacielu, to zaklęcie również można kontrolować kursorem myszy.", "tooltip": "<PERSON><PERSON> w ciągu {{ ultduration }} sek. ładowania wysyła następującą liczbę magicznych fal: {{ numberofwaves }}, które mają wpływ na obie drużyny. <PERSON><PERSON><PERSON> umiej<PERSON> zostanie uż<PERSON>a, gdy <PERSON> jest <keywordMajor>przywiązana</keywordMajor>, falami można sterować za pomocą kursora myszy.<br /><br />Trafieni wrogowie otrzymują <magicDamage>{{ totalmissiledamage }} pkt. obrażeń magicznych</magicDamage> i zostają <status>spowolnieni</status> o {{ baseslow*-100 }}% na {{ ccduration }} sek. Wartość spowolnienia wzrasta o {{ bonusslowperwave*-100 }}% za każdą falę, która trafi cel.<br /><br />So<PERSON><PERSON><PERSON><PERSON> bohaterowie otrzymują leczenie równe <healing>{{ totalhealperwave }} pkt.</healing> zdrowia za każdą falę. Nadwyżka leczenia zostaje zamieniona na <shield>tarczę</shield>.<br /><br /><keywordMajor>Premia Najlepszego Przyjaciela:</keywordMajor> W przypadku <keywordMajor>Najlepszego Przyjaciela</keywordMajor> leczenie wzrasta do <healing>{{ enhancedhealperwave }} pkt. zdrowia</healing>.<br /><br /><rules>Rzucenie <spellName>Ty i Ja!</spellName> zablokuje fale. Będą one wysyłane w aktualnym kierunku.<br />Yuumi może się poruszać i rzucać <spellName>Ziuum</spellName> podczas ładowania.</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Podstawowe obrażenia każdego pocisku", "Podstawowe leczenie za każdą falę"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON>cia Przyjaźń", "description": "Co jakiś czas, kiedy <PERSON> uderzy bohatera atakiem lub umiej<PERSON>cią, odnawia ona zdrowie swoje i kolejnego sojusznika, do którego się Przywiąże.<br><br>Yuumi generuje wyjątkową więź ze swoimi sojusznikami, kiedy jest Przywiązana. Sojusznik z najsilniejszą więzią wzmacnia umiejętności Yuumi, gdy jest ona do niego Przywiązana.", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}