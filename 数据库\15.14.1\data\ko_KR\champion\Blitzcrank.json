{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "블리츠크랭크", "title": "거대 증기 골렘", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "녹슨 블리츠크랭크", "chromas": false}, {"id": "53002", "num": 2, "name": "골키퍼 블리츠크랭크", "chromas": false}, {"id": "53003", "num": 3, "name": "핵펀치 블리츠크랭크", "chromas": false}, {"id": "53004", "num": 4, "name": "필트오버 커스텀 블리츠크랭크", "chromas": false}, {"id": "53005", "num": 5, "name": "나 블리츠크랭크 아니다", "chromas": false}, {"id": "53006", "num": 6, "name": "i블리츠크랭크", "chromas": false}, {"id": "53007", "num": 7, "name": "라이엇 블리츠크랭크", "chromas": false}, {"id": "53011", "num": 11, "name": "중간보스 블리츠크랭크", "chromas": true}, {"id": "53020", "num": 20, "name": "악의 창기병 블리츠크랭크", "chromas": false}, {"id": "53021", "num": 21, "name": "정의의 창기병 블리츠크랭크", "chromas": false}, {"id": "53022", "num": 22, "name": "마녀 가마솥 블리츠크랭크", "chromas": true}, {"id": "53029", "num": 29, "name": "우주 그루브 블리츠와 크랭크", "chromas": true}, {"id": "53036", "num": 36, "name": "승리의 블리츠크랭크", "chromas": true}, {"id": "53047", "num": 47, "name": "한계 돌파 블리츠크랭크", "chromas": true}, {"id": "53056", "num": 56, "name": "블리츠꿀랭크", "chromas": true}], "lore": "블리츠크랭크는 지하도시 자운의 기술이 만들어낸 거대한 기계로 사실상 파괴불가능에 가깝다. 원래는 자운을 뒤덮은 유독 폐기물을 처리하기 위해 만들어진 골렘이었으나, 연약하기 짝이 없는 자운 시민들을 지키기 위해서는 폐기물을 치우는 것만으로는 모자란다고 판단하여 스스로 형체를 변형시키기에 이르렀다. 블리츠크랭크는 골렘다운 힘과 내구성을 사심 없이 활용하여 사람들을 보호하고, 말썽꾼이 눈에 띄면 강철 주먹을 내지르거나 에너지를 분출하여 진압한다.", "blurb": "블리츠크랭크는 지하도시 자운의 기술이 만들어낸 거대한 기계로 사실상 파괴불가능에 가깝다. 원래는 자운을 뒤덮은 유독 폐기물을 처리하기 위해 만들어진 골렘이었으나, 연약하기 짝이 없는 자운 시민들을 지키기 위해서는 폐기물을 치우는 것만으로는 모자란다고 판단하여 스스로 형체를 변형시키기에 이르렀다. 블리츠크랭크는 골렘다운 힘과 내구성을 사심 없이 활용하여 사람들을 보호하고, 말썽꾼이 눈에 띄면 강철 주먹을 내지르거나 에너지를 분출하여 진압한다.", "allytips": ["로켓 손, 강철 주먹, 정전기장을 순서대로 사용하면 대상 적에게 치명적인 피해를 줄 수 있습니다.", "로켓 손에 아군 포탑 근처로 끌려온 적에게 강철 주먹 스킬을 사용하면 포탑이 적을 여러 대 가격할 수 있습니다."], "enemytips": ["블리츠크랭크의 기본 지속 효과 마나 보호막은 체력이 낮아지면 보호막을 씌워줍니다.", "미니언 뒤에 숨으면 로켓 손을 피할 수 있습니다. 블리츠크랭크의 로켓 손은 가장 처음 잡히는 적만 잡아당깁니다."], "tags": ["Tank", "Support"], "partype": "마나", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "로켓 손", "description": "블리츠크랭크가 오른손을 발사하여 앞에 있는 적을 붙잡아 당기면서 피해를 입힙니다.", "tooltip": "블리츠크랭크가 오른손을 발사하여 적중당한 적을 <status>끌어당기고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "Overdrive", "name": "폭주", "description": "블리츠크랭크가 힘을 충전하여 이동 및 공격 속도를 크게 향상시킵니다. 이 효과가 끝나면 일시적으로 이동 속도가 느려집니다.", "tooltip": "블리츠크랭크가 힘을 충전하여 {{ duration }}초 동안 <speed>이동 속도가 {{ movespeedmod*100 }}% 증가했다가 점차 원래대로 돌아오고</speed>, <attackSpeed>공격 속도가 {{ attackspeedmod*100 }}%</attackSpeed> 증가합니다.<br /><br />폭주 효과가 끝나면 블리츠크랭크가 {{ slowduration }}초 동안 {{ movespeedmodreduction*100 }}% <status>둔화</status>됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동 속도", "공격 속도"], "effect": ["{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "PowerFist", "name": "강철 주먹", "description": "블리츠크랭크가 주먹에 힘을 모아 다음 공격 시 적에게 두 배의 피해를 입히고 적을 공중으로 띄웁니다.", "tooltip": "블리츠크랭크가 주먹에 힘을 모아 다음 공격 시 적을 {{ ccduration }}초 동안 <status>공중으로 띄워 올리고</status> <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "StaticField", "name": "정전기장", "description": "블리츠크랭크의 공격을 받은 적에게 표식을 남기고 1초 후 번개 피해를 입힙니다. 스킬 사용 시 주변 적의 보호막을 제거하고 피해를 입히며 잠시 침묵에 걸리게 합니다.", "tooltip": "<spellPassive>기본 지속 효과: </spellPassive>스킬이 재사용 대기 상태가 아닐 때 블리츠크랭크의 주먹에 번개가 충전되어 공격 대상에게 표식을 남깁니다. 1초가 지나면 해당 적이 감전되어 <magicDamage>{{ passivedamage }}의 마법 피해</magicDamage>를 입습니다.<br /><br /><spellActive>사용 시: </spellActive>블리츠크랭크가 과충전해 주변 적에게 <magicDamage>{{ activedamage }}의 마법 피해</magicDamage>를 입히고 {{ silenceduration }}초 동안 <status>침묵</status>시킵니다. 또한 적의 보호막도 파괴합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 지속 효과 기본 피해량", "기본 지속 효과 주문력 계수", "사용 시 기본 피해량", "재사용 대기시간"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}% -> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "마나 보호막", "description": "체력이 낮아지면 블리츠크랭크가 마나에 비례하여 보호막을 얻습니다.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}