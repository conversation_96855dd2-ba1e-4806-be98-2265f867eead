{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Β<PERSON><PERSON><PERSON><PERSON>", "title": "ο Αλλόκο<PERSON><PERSON>ος Οδοιπόρος", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "Β<PERSON><PERSON><PERSON>ος του Αρχαίου Δάσους", "chromas": false}, {"id": "432005", "num": 5, "name": "Χειμων<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "432006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "432008", "num": 8, "name": "Αστρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "432017", "num": 17, "name": "Β<PERSON><PERSON><PERSON><PERSON> του Καφέ της Χαριτωμενιάς", "chromas": true}, {"id": "432026", "num": 26, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>υρ<PERSON>ι Σαν Χάι", "chromas": true}, {"id": "432035", "num": 35, "name": "Τ1 Βάρδος", "chromas": true}, {"id": "432037", "num": 37, "name": "Βάρδος Άνθος του Πνεύματος", "chromas": true}], "lore": "Ο Βάρδος είναι ένας ταξιδιώτης που έρχεται πέρα από τα άστρα. Φέρνει μαζί του την καλοτυχία και πολεμάει να διατηρήσει την ισορροπία εκεί που η ζωή μπορεί να αντέξει την αδιαφορία του χάους. Πολλοί κάτοικοι της Γης των Ρούνων τραγουδούν για την απίστευτη φύση του και όλοι συμφωνούν ότι αυτός ο διαστημικός απατεωνίσκος προσελκύεται από μαγικά αντικείμενα με τεράστια δύναμη. Τον περιτριγυρίζει μία χαρούμενη χορωδία από τα πρόθυμα πνεύματα Μιπ και είναι αδύνατο να περάσεις τις πράξεις του για κακόβουλες. Ο Βάρδος υπηρετεί το γενικό καλό… με τον δικό του παράξενο τρόπο.", "blurb": "Ο Βάρδος είναι ένας ταξιδιώτης που έρχεται πέρα από τα άστρα. Φέρνει μαζί του την καλοτυχία και πολεμάει να διατηρήσει την ισορροπία εκεί που η ζωή μπορεί να αντέξει την αδιαφορία του χάους. Πολλοί κάτοικοι της Γης των Ρούνων τραγουδούν για την...", "allytips": ["Είναι σημαντικό να συλλέγετε καμπάνες, για να βελτιώνετε τις επιθέσεις των Μιπ, αλλά μην παραμελείτε το σύντροφό σας στη λωρίδα! Προσπαθήστε να κάνετε εντυπωσιακή είσοδο, φέρνοντας έναν σύμμαχο στη λωρίδα με το Μαγικό Ταξίδι.", "Αφήστε τους Βωμούς του Οδοιπόρου να φορτιστούν. Η θεραπεία τους είναι πολύ μεγαλύτερη, όταν είναι πλήρως φορτισμένοι.", "Μην ξεχνάτε ότι οι εχθροί μπορούν, ε<PERSON><PERSON><PERSON><PERSON><PERSON>, να χρησιμοποιήσουν τις πύλες από τα Μαγικά Ταξίδια και ότι η Υπέρτατη ικανότητά σας μπορεί, ε<PERSON><PERSON><PERSON><PERSON><PERSON>, να επηρεάσει τους συμμάχους σας!"], "enemytips": ["Οι αντίπαλοι του Βάρδου μπορούν επίσης να χρησιμοποιήσουν τις πύλες του Βάρδου για το Μαγικό Ταξίδι. Μπορείτε να τον ακολουθήσετε, αν πιστεύετε ότι θα είστε ασφαλείς.", "Μπορείτε να καταστρέψετε τους ναούς θεραπείας του Βάρδου, αν πατήσετε πάνω σε αυτούς.", "Η υπέρτατη ικανότητα του Βάρδου, η Αλλαγή του Πεπρωμένου , επηρεάζει συμμάχους, εχθρούς, τέρατα και πύργους. Μερικές φορές μπορεί να είναι ωφέλιμο για εσάς να επηρεαστείτε από την ικανότητα!"], "tags": ["Support", "Mage"], "partype": "Μάνα", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "Αστρικά Δεσμά", "description": "Ο Βάρδος εκτοξεύει ένα βλήμα που επιβραδύνει τον πρώτο εχθρό που θα χτυπήσει και συνεχίζει την πορεία του. Αν χτυπήσει σε τοίχο, θα εξουδετερώσει τον αρχικό στόχο. Αν χτυπήσει έναν άλλο εχθρό, θα εξουδετερώσει και τους δύο.", "tooltip": "Ο Βάρδος εξαπολύει ένα βλήμα ενέργειας που προκαλεί <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> στους πρώτους δύο εχθρούς που πετυχαίνει. Ο πρώτος στόχος που θα δεχτεί χτύπημα <status>Επιβραδύνεται</status> κατά {{ slowamountpercentage }}% για {{ slowduration }} δευτ. <br /><br />Αν η βολή χτυπήσει άλλο εχθρό ή τοίχο, όλοι οι εχθροί που έχουν δεχτεί χτύπημα <status>Ακινητοποιούνται</status> για {{ stunduration }} δευτ.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "Διάρκεια Επιβράδυνσης", "Διάρκ<PERSON><PERSON><PERSON> Ακινητοποίησης:", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Βωμός του Οδοιπόρου", "description": "Αποκαλύπτει έναν βωμό Ζωής που φορτίζει για μικρό χρονικό διάστημα και εξαφανίζεται, όταν θεραπεύσει και επιταχύνει τον πρώτο σύμμαχο που θα τον αγγίξει.", "tooltip": "Ο Βάρδος δημιουργεί έναν βωμό Ζωής που δίνει <speed>{{ calc_movespeed }} Ταχύτητα Κίνησης</speed> που μειώνεται μέσα σε {{ movespeed_duration }} δευτ. και ανακτά τουλάχιστον <healing>{{ initialheal }} Ζωή</healing> στον πρώτο σύμμαχο που μπαίνει. Ο βωμός μεγαλώνει και αναπληρώνει <healing>{{ maxheal }} Ζωή</healing>, εφόσον παραμείνει στο παιχνίδι για {{ chargeuptime }} δευτ.<br /><br />Ο Βάρδος μπορεί να έχει έως {{ maxpacks }} βωμούς ταυτόχρονα. Εάν ένας αντίπαλος Ήρωας μπει σε έναν βωμό, αυτός καταστρέφεται.<br /><br />Η ικανότητα έχει {{ ammo_limit }} φορτίσεις.<br /><br />Τρέχοντες Ενεργοί Βωμοί: {{ f1 }} / {{ f2 }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Βασική Θεραπεία", "Μέγιστη Θεραπεία", "Ταχύτητα Κίνησης"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}% -> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Μαγικό Ταξίδι", "description": "Ο Βάρδος ανοίγει μια πύλη σε ένα κοντινό τοίχο. Τόσο οι σύμμαχοι όσο και οι εχθροί μπορούν να διαπεράσουν το εμπόδιο, χωρ<PERSON>ς να μπορούν να επιστρέψουν, αν περάσουν την πύλη.", "tooltip": "Ο Βάρδος ανοίγει μια πύλη χωρίς επιστροφή μέσα από Έδαφος για {{ e1 }} δευτ. Οποιοσδήποτε Ήρωας μπορεί να μπει στη πύλη όταν μετακινηθεί μέσα της ενώ βρίσκεται κοντά στην είσοδο.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "Αλλαγή του Πεπρωμένου", "description": "Ο Βάρδος στέλνει ψυχική ενέργεια προς μια θέση και θέτει όλες τις μονάδες και τους πύργους σε καταστολή για μικρό χρονικό διάστημα.", "tooltip": "Ο Βάρδος εξαπολύει μαγική προστατευτική ενέργεια σε μια περιοχή, θέτοντας όλες τις μονάδες και όλα τα κτίρια που δέχτηκαν χτύπημα σε Στάση για {{ rstasisduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ταξιδευτής των Κόσμων", "description": "<font color='#FF9900'>Μιπ:</font> Ο Βάρδος προσελκύει μικρά πνεύματα που τον βοηθούν στις βασικές του επιθέσεις, προ<PERSON><PERSON><PERSON><PERSON>ντας επιπλέον Μαγική Ζημιά. Όταν ο Βάρδος συλλέξει αρκετά <font color='#cccc00'>Καμπανάκια</font>, τα Μιπ του προκαλούν, επίσης, ζημιά σε ευρύτερη περιοχή και επιβραδύνουν τους εχθρούς που χτυπάνε.<br><br><font color='#FF9900'>Καμπανάκια:</font> Αρχαία <font color='#cccc00'>Καμπανάκια</font> εμφανίζονται τυχαία για να τα συλλέξει ο Βάρδος. <PERSON>υτά δίνουν εμπειρία, αν<PERSON><PERSON><PERSON><PERSON>ύ<PERSON> Μάνα και παρέχουν Ταχύτητα Κίνησης εκτός μάχης.", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}