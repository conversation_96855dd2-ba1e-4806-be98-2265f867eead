{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sivir": {"id": "<PERSON><PERSON>", "key": "15", "name": "<PERSON><PERSON>", "title": "die Kampfmeisterin", "image": {"full": "Sivir.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "15000", "num": 0, "name": "default", "chromas": false}, {"id": "15001", "num": 1, "name": "Kriegerprinzes<PERSON>", "chromas": false}, {"id": "15002", "num": 2, "name": "Prächtige Sivir", "chromas": false}, {"id": "15003", "num": 3, "name": "Jägerin-<PERSON><PERSON>", "chromas": false}, {"id": "15004", "num": 4, "name": "Banditen-Sivir", "chromas": false}, {"id": "15005", "num": 5, "name": "PAX-Sivir", "chromas": false}, {"id": "15006", "num": 6, "name": "Schneesturm-Sivir", "chromas": true}, {"id": "15007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "15008", "num": 8, "name": "Siegre<PERSON>", "chromas": false}, {"id": "15009", "num": 9, "name": "Neo-PAX-Sivir", "chromas": false}, {"id": "15010", "num": 10, "name": "Pizzabotin-Sivir", "chromas": true}, {"id": "15016", "num": 16, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "15025", "num": 25, "name": "Odyssee-Sivir", "chromas": true}, {"id": "15034", "num": 34, "name": "Konditorei-Sivir", "chromas": true}, {"id": "15043", "num": 43, "name": "Sonnenfinsternis-Sivir", "chromas": true}, {"id": "15050", "num": 50, "name": "Sagenschöpf<PERSON><PERSON>", "chromas": true}, {"id": "15051", "num": 51, "name": "Sagenschöpf<PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "15061", "num": 61, "name": "Krallen des Tigers-Sivir", "chromas": true}, {"id": "15070", "num": 70, "name": "Jubiläums-Sivir", "chromas": false}], "lore": "Sivir ist eine berühmte Glücksjägerin und S<PERSON>ld<PERSON>hauptmann, die ihrem Geschäft in den Wüsten Shurimas nachgeht. Mit ihrer legend<PERSON><PERSON>, mit Juwelen bestückten Kreuzklinge hat sie für diejenigen, die sich ihren exorbitanten Preis leisten können, bereits viele Schlachten gewonnen. Sie ist bekannt für ihre furchtlose Entschlossenheit und ihre grenzenlosen Ambitionen, und außerdem sehr stolz darauf, dass sie immer wieder vergrabene Schätze aus den gefährlichen Gräbern Shurimas birgt – für eine großzügige Belohnung, versteht sich. <PERSON><PERSON>, da uralte Kräfte Shurima bis auf die Knochen erschüttern, findet sich auch Sivir zwischen den Fronten miteinander ringender Schicksale hin- und hergerissen.", "blurb": "Sivir ist eine berühmte Glücksjägerin und Söldnerhauptmann, die ihrem Geschäft in den Wüsten Shurimas nachgeht. Mit ihrer legendären, mit Juwelen bestückten Kreuzklinge hat sie für diejenigen, die sich ihren exorbitanten Preis leisten können, bereits...", "allytips": ["Sivirs „Bumerangklinge“ kehrt zu ihr zurück, wenn sie die maximale Reichweite erreicht hat. Du kannst also die Position wechseln, um Gegner zu treffen, die dir andernfalls entgehen würden.", "„Querschläger“ setzt bei der Aktivierung Sivirs normalen Angriff zurück. Dadurch lässt sich ihr Schaden maximieren, indem man diese Fähigkeit erst kurz nach einem erfolgten normalen Angriff benutzt.", "Versuche „Zauberschild“ für gegnerische Fähigkeiten aufzusparen, die dich kampfunfähig machen können, wie etwa Betäubungen und Festhalteeffekte."], "enemytips": ["„Bumerangklinge“ kostet viel Mana. Weichst du ihr aus, bedeutet das für Sivir einen großen Rückschlag. Wirst du von der ausgehenden Spiralklinge getroffen, vermeide es erneut auf ihrem Rückweg getroffen zu werden.", "<PERSON><PERSON> ist ein Champion, der sehr gut pushen kann. Lässt man sie zu lange unbeaufsichtigt, kann dies zum Verlust eines Turms führen.", "Wenn du gegen <PERSON>, kannst du das Timing von „Zauberschild“ st<PERSON><PERSON>, indem du einen Vorstoß vortäuschst und dich dann zurückziehst."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 104, "mp": 340, "mpperlevel": 45, "movespeed": 335, "armor": 30, "armorperlevel": 4.45, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SivirQ", "name": "B<PERSON>rangkling<PERSON>", "description": "<PERSON><PERSON> schleudert ihre Kreuzklinge wie einen Bumerang und verursacht in beiden Richtungen Schaden.", "tooltip": "<PERSON><PERSON> schleudert ihre Kreuzklinge wie einen Bumerang und fügt allen getroffenen Gegnern <physicalDamage>{{ totaldamage }}</physicalDamage> <PERSON><PERSON><PERSON> zu. Trifft sie eine Einheit, die kein Champion ist, wird der Schaden an jedem weiteren Z<PERSON> verring<PERSON>, bis zu einem Minimum von {{ falloffminimum*100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "SivirQ.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SivirW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Die nächsten normalen Angriffe von Sivir erhalten zusätzliches Angriffstempo, springen auf Ziele in der Nähe über und verursachen dabei verringerten Schaden.", "tooltip": "Für die nächsten {{ buffduration }}&nbsp;Sekunden erhält Sivir <attackSpeed>{{ ricochetattackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed> und ihre normalen Angriffe springen auf weitere umstehende Gegner über. Jeder Sprung verursacht <physicalDamage>{{ bouncedamage }}&nbsp;normalen Schaden</physicalDamage> (bis zu {{ maxbounces }}&nbsp;Sprünge).<br /><br />Diese Sprünge treffen kritisch, wenn ihr ursprünglicher Angriff ein kritischer Treffer war.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Skalierung mit Gesamtangriffsschaden"], "effect": ["{{ ricochetattackspeed*100.000000 }}&nbsp;% -> {{ ricochetattackspeednl*100.000000 }}&nbsp;%", "{{ bounceadratio*100.000000 }}&nbsp;% -> {{ bounceadrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirW.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SivirE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Erschafft eine magische Barriere, die Sivir vor einer auf sie gewirkte gegnerischen Fähigkeit schützt. Sie erhält Leben und einen Lauftemposchub, wenn eine Fähigkeit geblockt wird.", "tooltip": "Sivir erschafft {{ e1 }}&nbsp;Sekunden lang eine magische Barriere, wodurch die nächste eingehende gegnerische Fähigkeit abgewehrt wird. Wenn eine Fähigkeit geblockt wird, stellt Sivir <healing>{{ totalheal }}&nbsp;<PERSON><PERSON></healing> wieder her und löst „Leichtfüßigkeit“ aus.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Skalierung mit Gesamtangriffsschaden", "Abklingzeit"], "effect": ["{{ healratio*100.000000 }}&nbsp;% -> {{ healrationl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [1.5, 1.5, 1.5, 1.5, 1.5], [55, 55, 55, 55, 55], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5", "55", "60", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirE.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SivirR", "name": "<PERSON><PERSON> der Jagd", "description": "<PERSON><PERSON> führt ihre Verbündeten in den Kampf und gewährt ihnen für gewisse Zeit einen Lauftemposchub. Außerdem verringern Sivirs Angriffe die Abklingzeiten ihrer Fähigkeiten.", "tooltip": "Sivir mobilisiert nahe Verbündete und gewährt ihnen {{ ultduration }}&nbsp;Sekunden lang <speed>{{ maxms*100 }}&nbsp;% Lauftempo</speed>.<br /><br />Sivirs Angriffe gegen Champions während „Auf der Jagd“ verringern die Abklingzeit ihrer Grundfähigkeiten um 0,5&nbsp;Sekunden.<br /><br />Kills/Unterstützungen bei Gegnern, die kürzlich Schaden erlitten haben, setzen die Dauer von „Auf der Jagd“ zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON>", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxms*100.000000 }}&nbsp;% -> {{ maxmsnl*100.000000 }}&nbsp;%", "{{ ultduration }} -> {{ ultdurationNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SivirR.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Leichtfüßigkeit", "description": "Sivir erhält kurzzeitig erhöhtes Lauftempo, wenn sie einen gegnerischen Champion angreift.", "image": {"full": "Sivir_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}