{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aphelios": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "523", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Arme des Lunaris", "image": {"full": "Aphelios.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "523000", "num": 0, "name": "default", "chromas": false}, {"id": "523001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON> de la nuit", "chromas": true}, {"id": "523009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> lunaire", "chromas": true}, {"id": "523018", "num": 18, "name": "EDG Aphelios", "chromas": false}, {"id": "523020", "num": 20, "name": "<PERSON><PERSON><PERSON><PERSON> fleur spirituelle", "chromas": false}, {"id": "523030", "num": 30, "name": "HEARTSTEEL Aphelios", "chromas": false}], "lore": "Émergeant des ombres au clair de lune, <PERSON><PERSON><PERSON><PERSON> abat ceux qui voudraient anéantir sa foi sans un mot ; ses armes et sa précision mortelle parlent pour lui. Un poison qui le rend muet coule dans ses veines, mais il est constamment guidé par sa sœur, Alune. Depuis son temple lointain, elle lui confère un arsenal d'armes en pierre de lune. Tant que la lune brillera dans le ciel, A<PERSON><PERSON><PERSON> ne sera jamais seul.", "blurb": "Émergeant des ombres au clair de lune, <PERSON><PERSON><PERSON><PERSON> abat ceux qui voudraient anéantir sa foi sans un mot ; ses armes et sa précision mortelle parlent pour lui. Un poison qui le rend muet coule dans ses veines, mais il est constamment guidé par sa sœur...", "allytips": ["Chacune des armes d'Aphelios a ses propres forces. Essayez de trouver la situation parfaite pour vos armes actuelles. "], "enemytips": ["Chacune des armes d'Aphelios a ses propres faiblesses. Essayez d'exploiter celles qui bénéficieront le plus à votre champion. Attention à l'arme gravitationnelle violette ! Elle peut vous immobiliser."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 2, "magic": 1, "difficulty": 10}, "stats": {"hp": 600, "hpperlevel": 102, "mp": 348, "mpperlevel": 42, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 6.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.3, "attackspeedperlevel": 2.1, "attackspeed": 0.665}, "spells": [{"id": "ApheliosQ_ClientTooltipWrapper", "name": "Compétences d'arme", "description": "Aphelios a 5 compétences actives différentes, chacune liée à son arme principale :<br><br>Calibrum (fusil) : tire une balle à longue portée qui marque la cible et permet une seconde attaque à très longue portée.<br>Severum (pistolet-faux) : court rapidement en attaquant les ennemis proches avec les deux armes.<br><PERSON><PERSON><PERSON><PERSON> (canon) : immobilise tous les ennemis ralentis par cette arme.<br>Infernum (lance-flammes) : frappe les ennemis dans un cône et les attaque avec l'arme secondaire.<br>Crescendum (chakram) : déploie une vigie qui attaque avec l'arme secondaire.<br>", "tooltip": "{{ spellmodifierdescriptionappend }}", "leveltip": {"label": [], "effect": []}, "maxrank": 6, "cooldown": [9, 9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1450, 1450, 1450, 1450, 1450, 1450], "rangeBurn": "1450", "image": {"full": "ApheliosQ_ClientTooltipWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ApheliosW", "name": "Phase", "description": "<PERSON><PERSON><PERSON><PERSON> échange son arme principale avec son arme secondaire, ce qui remplace ses attaques de base ainsi que sa compétence active.", "tooltip": "Échange les armes principale et secondaire pour s'équiper de : <b><i><span class=\"colora64dff\">Gravitum</span></i></b>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": [], "effect": []}, "maxrank": 6, "cooldown": [0.8, 0.8, 0.8, 0.8, 0.8, 0.8], "cooldownBurn": "0.8", "cost": [0, 0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Pas <PERSON>", "maxammo": "-1", "range": [250, 250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ApheliosW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Pas <PERSON>"}, {"id": "ApheliosE_ClientTooltipWrapper", "name": "Système de file d'armes", "description": "Aphelios n'a pas de troisième compétence. Cet emplacement lui indique la prochaine arme qu'Alune lui donnera. L'ordre des armes est toujours le même en début de partie, mais peut évoluer au fil de la partie. Lorsqu'une arme est à court de munitions, elle se place tout en bas de la rotation.", "tooltip": "{{ spellmodifierdescriptionappend }}", "leveltip": {"label": [], "effect": []}, "maxrank": 6, "cooldown": [0, 0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "ApheliosE_ClientTooltipWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ApheliosR", "name": "<PERSON><PERSON><PERSON> au clair de lune", "description": "Envoie un rayon de lune concentré qui explose au contact d'un champion ennemi. Applique l'effet unique de l'arme principale d'Aphelios.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tire un rayon de lune concentré qui explose au contact d'un champion et qui inflige <physicalDamage>{{ maxdamage }} pts de dégâts physiques</physicalDamage> aux ennemis proches.<br /><br />Aphelios attaque ensuite tous les champions touchés avec son arme principale. {{ Spell_ApheliosR_WeaponMod_{{ f1 }} }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bonus du Calibrum : dég<PERSON><PERSON> de la marque", "Bonus du Severum : soins", "Bonus de l'Infernum : d<PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ calibrumrmarkbonusdamage }} -> {{ calibrumrmarkbonusdamageNL }}", "{{ severumrhealbonus }} -> {{ severumrhealbonusNL }}", "{{ infernumrbonusdamagebase }} -> {{ infernumrbonusdamagebaseNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "ApheliosR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Le Tueur et l'Oracle", "description": "Aphelios manie 5 armes de Lunari forgées par sa sœur Alune. Il peut en porter deux à la fois : une principale et une secondaire. Chaque arme possède une attaque de base et une compétence uniques. Les attaques et les compétences consomment les munitions de son arme. Lorsqu'il n'a plus de munitions, <PERSON><PERSON><PERSON><PERSON> jette son arme et Alune invoque la suivante (parmi les 5). ", "image": {"full": "ApheliosP.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}