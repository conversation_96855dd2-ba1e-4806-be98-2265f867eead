{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Quinn": {"id": "<PERSON>", "key": "133", "name": "퀸", "title": "데마시아의 날개", "image": {"full": "Quinn.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "133000", "num": 0, "name": "default", "chromas": false}, {"id": "133001", "num": 1, "name": "불사조 퀸", "chromas": false}, {"id": "133002", "num": 2, "name": "북방 정찰대 퀸", "chromas": false}, {"id": "133003", "num": 3, "name": "해적 퀸", "chromas": false}, {"id": "133004", "num": 4, "name": "사랑의 추적자 퀸", "chromas": false}, {"id": "133005", "num": 5, "name": "심판자 퀸", "chromas": true}, {"id": "133014", "num": 14, "name": "별 수호자 퀸", "chromas": true}], "lore": "데마시아의 정예 기동대 기사인 퀸은 전설적인 독수리 발러와 함께 적의 영토 한복판에 잠입해 갖가지 위험한 임무를 수행한다. 퀸과 발러의 강력한 결속력은 전투에서 무시무시한 힘을 발휘한다. 적들은 그들의 상대가 데마시아 제일가는 영웅, 그것도 하나가 아니라 둘이라는 사실을 알지도 못한 채 순식간에 목숨을 잃고 만다. 상황에 따라 곡예 수준의 동작을 보여주는 퀸이 민첩한 몸놀림으로 석궁을 꺼내들면, 발러는 공중에서 유영하며 숨어 있는 적을 찾아낸다. 데마시아의 적에게는 그야말로 공포의 한 쌍이다.", "blurb": "데마시아의 정예 기동대 기사인 퀸은 전설적인 독수리 발러와 함께 적의 영토 한복판에 잠입해 갖가지 위험한 임무를 수행한다. 퀸과 발러의 강력한 결속력은 전투에서 무시무시한 힘을 발휘한다. 적들은 그들의 상대가 데마시아 제일가는 영웅, 그것도 하나가 아니라 둘이라는 사실을 알지도 못한 채 순식간에 목숨을 잃고 만다. 상황에 따라 곡예 수준의 동작을 보여주는 퀸이 민첩한 몸놀림으로 석궁을 꺼내들면, 발러는 공중에서 유영하며 숨어 있는 적을 찾아낸다...", "allytips": ["발러가 ", "취약", " 표식을 남긴 적을 공격하면 매사냥의 재사용 대기시간이 시작됩니다. 표식을 빠르게 소모하면 다음 표식이 더 빠르게 생성됩니다.", "공중제비 스킬이 강력하긴 하지만, 적이 이 때를 노려 퀸에게 반격할 수 있으니 주의해서 사용해야 합니다. 공중제비는 벽을 등지고 있을 때 활용하면 좋습니다.", "후방 지원 스킬로 아주 먼 거리를 신속하게 이동할 수 있으니 다른 라인의 미니언을 정리하고 싶을 때나 부상 당한 적을 추격할 때 활용하세요."], "enemytips": ["발러의 표식이 찍혔다면, 퀸이 이득을 취할 수 없도록 일단 후퇴하세요.", "퀸의 현재 위치를 예의주시하세요. 후방 지원 스킬로 발러가 아주 빠르게 맵을 가로질러와 급습할 수 있습니다.", "퀸이 후방 지원 스킬을 사용 중일 때 피해를 입히면 이동 속도 증가 효과가 일시적으로 사라집니다."], "tags": ["Marksman", "Assassin"], "partype": "마나", "info": {"attack": 9, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 565, "hpperlevel": 107, "mp": 269, "mpperlevel": 35, "movespeed": 330, "armor": 28, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.7, "attackspeedperlevel": 3.1, "attackspeed": 0.668}, "spells": [{"id": "QuinnQ", "name": "실명 공격", "description": "퀸이 발러를 보내 하나의 적에게 표식을 남기고 대상의 시야를 크게 좁힙니다. 또한 근처의 모든 적에게 피해를 입힙니다.", "tooltip": "발러가 날아가 처음 적중한 적에게 <keywordMajor>취약</keywordMajor> 표식을 남기고 {{ e3 }}초간 대상의 시야 반경을 줄입니다. 이후 주변의 모든 적에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />최초 대상이 챔피언이 아닌 경우 대상은 {{ e3 }}초 동안 <status>공격 불가</status> 상태가 됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "총 공격력 %", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio }} -> {{ adratio<PERSON> }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [20, 40, 60, 80, 100], [-1000, -1000, -1000, -1000, -1000], [1.75, 1.75, 1.75, 1.75, 1.75], [0.8, 0.9, 1, 1.1, 1.2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/40/60/80/100", "-1000", "1.75", "0.8/0.9/1/1.1/1.2", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1025, 1025, 1025, 1025, 1025], "rangeBurn": "1025", "image": {"full": "QuinnQ.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "QuinnW", "name": "예리한 감각", "description": "기본 지속 효과로 퀸이 <font color='#FFF673'>취약</font>해진 대상을 공격한 이후 공격 속도와 이동 속도가 상승합니다. 사용하면 발러가 주변의 넓은 지역을 드러냅니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> <keywordMajor>취약</keywordMajor> 상태인 대상을 공격하면 공격 속도가 <attackSpeed>{{ attackspeedbonus*100 }}%</attackSpeed> 상승하고 이동 속도가 {{ e1 }}초간 <speed>{{ e3 }}%</speed> 상승합니다.<br /><br /><spellActive>사용 시:</spellActive> 발러가 {{ e5 }}초 동안 주변의 넓은 지역을 드러냅니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["공격 속도", "이동 속도", "재사용 대기시간"], "effect": ["{{ attackspeedbonus*100.000000 }}% -> {{ attackspeedbonusnl*100.000000 }}%", "{{ effect3amount*100.000000 }}% -> {{ effect3amountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [50, 45, 40, 35, 30], "cooldownBurn": "50/45/40/35/30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [20, 25, 30, 35, 40], [2100, 2100, 2100, 2100, 2100], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "0", "20/25/30/35/40", "2100", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [2100, 2100, 2100, 2100, 2100], "rangeBurn": "2100", "image": {"full": "QuinnW.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "QuinnE", "name": "공중제비", "description": "퀸이 적에게 돌진하여 물리 피해를 입히고 적의 이동 속도를 늦춥니다. 대상에게 도달하면 공중제비를 넘어 대상을 잠깐 방해하고, 최대 공격 사거리로 물러나 착지합니다.", "tooltip": "퀸이 적에게 돌진해 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 대상에게 <keywordMajor>취약</keywordMajor> 표식을 남깁니다. 퀸이 뛰어오르며 뒤쪽으로 물러나며 대상을 잠시 <status>뒤로</status> <status>밀어내고</status> {{ e1 }}% <status>둔화</status>시킵니다. 둔화 효과는 {{ e3 }}초에 걸쳐 사라집니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 50, 50, 50, 50], [40, 65, 90, 115, 140], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50", "40/65/90/115/140", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "QuinnE.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "QuinnR", "name": "후방 지원", "description": "퀸과 발러가 뭉쳐 엄청난 속도로 날아다닙니다. 스킬을 종료하면 공중 강습을 사용하게 되며 근처의 모든 적에게 피해를 입히고 적 챔피언에게 취약 표식을 남깁니다.", "tooltip": "퀸이 발러를 불러 자신을 돕게 합니다. 2초간 정신 집중 후 둘은 하나가 되어 이동 속도가 <speed>{{ movementspeedmod*100 }}%</speed> 증가하고 이 스킬을 <recast>재사용</recast>할 수 있게 됩니다. 공격하거나 <spellName>실명 공격</spellName> 또는 <spellName>공중제비</spellName> 스킬을 사용하면 이 스킬이 자동으로 <recast>재사용</recast>됩니다.<br /><br /><recast>재사용 시</recast>: 퀸과 발러가 공중에서 강습해 적 챔피언에게 <physicalDamage>{{ damage }}의 물리 피해</physicalDamage>를 입히고 <keywordMajor>취약</keywordMajor> 표식을 남긴 후 스킬을 종료합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동 속도", "마나 소모량"], "effect": ["{{ movementspeedmod*100.000000 }}% -> {{ movementspeedmodnl*100.000000 }}%", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 3, "cooldown": [3, 3, 3], "cooldownBurn": "3", "cost": [100, 50, 0], "costBurn": "100/50/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "QuinnR.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "매사냥", "description": "데마시아 독수리 발러가 주기적으로 적에게 <font color='#FFF673'>취약</font> 표식을 남깁니다. <font color='#FFF673'>취약</font>해진 대상에 대한 퀸의 첫 번째 기본 공격은 추가 물리 피해를 입힙니다.", "image": {"full": "Quinn_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}