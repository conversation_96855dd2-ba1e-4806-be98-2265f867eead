{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zeri": {"id": "<PERSON><PERSON>", "key": "221", "name": "제리", "title": "자운의 불꽃", "image": {"full": "Zeri.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "221000", "num": 0, "name": "default", "chromas": false}, {"id": "221001", "num": 1, "name": "메마른 장미 제리", "chromas": true}, {"id": "221010", "num": 10, "name": "바다의 노래 제리", "chromas": true}, {"id": "221019", "num": 19, "name": "불멸의 영웅 제리", "chromas": true}, {"id": "221028", "num": 28, "name": "공포의 밤 제리", "chromas": true}, {"id": "221029", "num": 29, "name": "프레스티지 공포의 밤 제리", "chromas": false}], "lore": "자운 노동자 계층 출신의 고집 세고 활발한 제리는 전기 마법으로 자신의 힘과 손수 제작한 총을 충전한다. 그녀의 불안정한 힘은 감정을 반영하고 그 불꽃은 번개처럼 빠른 삶을 닮았다. 동정심 많은 제리는 가족과 고향에 대한 애정으로 싸움에 임한다. 가끔 도우려는 의지가 강해 역효과를 일으키기도 하지만, 이웃을 위해 싸울 때 그들 또한 함께한다는 사실을 믿고 있다.", "blurb": "자운 노동자 계층 출신의 고집 세고 활발한 제리는 전기 마법으로 자신의 힘과 손수 제작한 총을 충전한다. 그녀의 불안정한 힘은 감정을 반영하고 그 불꽃은 번개처럼 빠른 삶을 닮았다. 동정심 많은 제리는 가족과 고향에 대한 애정으로 싸움에 임한다. 가끔 도우려는 의지가 강해 역효과를 일으키기도 하지만, 이웃을 위해 싸울 때 그들 또한 함께한다는 사실을 믿고 있다.", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "마나", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 250, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "ZeriQ", "name": "집중 사격", "description": "처음 적중한 적에게 물리 피해를 입히는 공격 7발을 발사합니다. 이 스킬은 기본 공격으로 간주됩니다.", "tooltip": "제리가 단숨에 {{ numberofmissiles }}발을 발사해 처음 적중하는 적에게 <physicalDamage>{{ activedamagethatcancrit }}의 물리 피해</physicalDamage>를 입힙니다. 이 스킬은 기본 공격으로 간주됩니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "총 공격력 %"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ activeadratio*100.000000 }}% -> {{ activeadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "ZeriQ.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "ZeriW", "name": "초강력 레이저", "description": "제리가 전기 파동을 발사해 처음 적중하는 적을 둔화시키고 피해를 입힙니다. 파동이 벽에 맞으면 장거리 레이저로 확산됩니다.", "tooltip": "제리가 전기 파동을 발사해 처음 적중하는 적에게 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ slowduration }}초 동안 {{ slowpercent*100 }}% <status>둔화</status>시킵니다.<br /><br />파동이 지형에 맞으면 광선으로 확산되어 범위 내 모든 적에게 같은 효과를 적용하고 챔피언과 몬스터에게 치명타를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "소모값 @AbilityResourceName@", "재사용 대기시간"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "ZeriW.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ZeriE", "name": "스파크 돌진", "description": "제리가 짧은 거리를 돌진하며 맞닥트리는 지형을 모두 뛰어넘습니다. 집중 사격의 다음 3발이 충전되어 적을 꿰뚫습니다.", "tooltip": "제리가 짧은 거리를 돌진하며 맞닥트리는 지형을 모두 뛰어넘습니다. 지형을 뛰어넘으면 돌진 거리가 크게 늘어납니다. {{ buffduration }}초 동안 <spellName>집중 사격</spellName>의 다음 사격이 적을 꿰뚫어, 두 번째 적부터는 {{ pendamagepercent*100 }}%의 피해를 입히며 처음 적중한 대상에게 추가 <magicDamage>{{ bonusdamagetotal }}의 마법 피해</magicDamage>를 입힙니다. <br /><br />기본 공격 또는 스킬로 적 챔피언을 맞히면 이 스킬의 재사용 대기시간이 {{ cdreductionperhit }}초 감소합니다. 치명타 적중 시 재사용 대기시간이 {{ critcdreductionperhit }}초 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["비례 피해량", "기본 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ pendamagepercent*100.000000 }}% -> {{ pendamagepercentnl*100.000000 }}%", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [90, 85, 80, 75, 70], "costBurn": "90/85/80/75/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "ZeriE.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ZeriR", "name": "번개 방출", "description": "제리가 전류를 방출하고 자신을 과충전해, 피해량과 이동 속도가 증가합니다. 제리가 적 챔피언을 맞힐 때마다 이동 속도 증가 효과가 중첩되고 지속시간이 회복됩니다. 과충전된 동안 집중 사격이 더욱 빠른 3연발 사격으로 바뀌며, 대상 주변 적에게 번개가 연쇄적으로 튑니다.", "tooltip": "제리가 전류를 방출해 근처 적에게 <magicDamage>{{ totalactivedamage }}의 마법 피해</magicDamage>를 입힙니다. 적 챔피언에게 적중하면 제리가 {{ rduration }}초 동안 <attackSpeed>공격 속도 {{ baseaspercent*100 }}%</attackSpeed>와 <speed>이동 속도{{ basebonusms*100 }}%</speed>를 얻습니다. 기본 공격이나 스킬로 적 챔피언을 맞히면 스킬 지속시간이 증가하고 {{ maxhyperchargeduration }}초 동안 과충전 중첩이 1 쌓입니다. 치명타 적중 시 추가 중첩이 2 쌓입니다. 중첩 하나당 <speed>이동 속도가 {{ mspercent*100 }}%</speed> 증가합니다.<br /><br />이 동안 <spellName>집중 사격</spellName>이 더욱 빠른 3연발 사격으로 바뀌어 대상 주변 적에게 추가 <physicalDamage>{{ chainphysicaldamage }}의 물리 피해</physicalDamage>를 연쇄적으로 입힙니다.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ activedamage }} -> {{ activedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 75, 70], "cooldownBurn": "80/75/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ZeriR.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "살아있는 배터리", "description": "제리의 기본 공격은 마법 피해를 입히며 스킬로 간주됩니다. 이동하거나 집중 사격을 사용하면 제리의 스파크 팩에 에너지가 쌓입니다. 완전히 충전되면 제리의 다음 기본 공격이 추가 피해를 입힙니다.", "image": {"full": "ZeriP.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}