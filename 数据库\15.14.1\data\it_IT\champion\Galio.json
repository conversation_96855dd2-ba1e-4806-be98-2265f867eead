{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Galio": {"id": "<PERSON><PERSON><PERSON>", "key": "3", "name": "<PERSON><PERSON><PERSON>", "title": "il colosso", "image": {"full": "Galio.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "3000", "num": 0, "name": "default", "chromas": false}, {"id": "3001", "num": 1, "name": "Galio Incantato", "chromas": false}, {"id": "3002", "num": 2, "name": "Galio Hextech", "chromas": false}, {"id": "3003", "num": 3, "name": "Galio Commando", "chromas": false}, {"id": "3004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "3005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "3006", "num": 6, "name": "Gallio", "chromas": true}, {"id": "3013", "num": 13, "name": "Galio Infernale", "chromas": true}, {"id": "3019", "num": 19, "name": "Galio Guardiano dei Draghi", "chromas": true}, {"id": "3028", "num": 28, "name": "Galio Artefice di Miti", "chromas": true}], "lore": "Fuori dalla scintillante città di Demacia, il colosso di pietra Galio monta una guardia costante. Costruito come protezione dai maghi nemici, è in grado di rimanere immobile per intere decadi, fino a che non si manifesta una presenza magica tale da riportarlo in vita. Quando si attiva, Galio non perde tempo e assapora il brivido del combattimento e il raro onore di difendere i suoi compatrioti. I suoi trionfi hanno sempre un retrogusto amaro, poiché la magia che distrugge è anche la fonte della sua rianimazione, e ogni vittoria lo fa tornare al suo sonno eterno.", "blurb": "<PERSON>ori dalla scintillante città di Demacia, il colosso di pietra Galio monta una guardia costante. Costruito come protezione dai maghi nemici, è in grado di rimanere immobile per intere decadi, fino a che non si manifesta una presenza magica tale da...", "allytips": ["Puoi rilasciare Scudo di Durand anche quando stai subendo un effetto di controllo.", "Puoi usare le icone degli alleati sulla minimappa per lanciare Entrata dell'eroe.", "Puoi usare il passo indietro di Pugno della giustizia per schivare le abilità nemiche."], "enemytips": ["Galio si muove più lentamente quando carica Sc<PERSON>.", "Entrata dell'eroe può essere interrotta prima che Galio salti.", "Galio non può attraversare i muri con Pugno della giustizia."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 10, "magic": 6, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 126, "mp": 410, "mpperlevel": 40, "movespeed": 340, "armor": 24, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 9.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "GalioQ", "name": "Venti di guerra", "description": "G<PERSON>o spara due raffiche di vento che convergono in un grande tornado infliggendo danni nel tempo.", "tooltip": "Galio spara due raffiche di vento che infliggono <magicDamage>{{ qmissiledamage }} danni magici</magicDamage> ciascuna. Quando le raffiche si incontrano, si uniscono formando un tornado che infligge <magicDamage>{{ percentsuperqdamagett }}% della salute massima dei nemici in danni magici</magicDamage> nell'arco di {{ superqduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>ni raf<PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "GalioQ.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GalioW", "name": "<PERSON><PERSON>", "description": "Galio carica una posizione difensiva, muovendosi lentamente. Al rilascio, provoca e danneggia i nemici nelle vicinanze.", "tooltip": "<spellPassive>Passiva:</spellPassive> G<PERSON><PERSON> ottiene uno <shield>Scudo magico da {{ totalpassiveshield }}</shield> dopo non aver subito danni per {{ passiveshieldooctimer }} secondi.<br /><br /><charge>Inizio carica:</charge> Galio riduce i danni magici subiti di {{ magicdamagereduction }} e quelli fisici di {{ physicaldamagereduction }}, e <status>rallenta</status> se stesso di {{ e3 }}%.<br /><br /><release>Rilascio:</release> Galio <status>provoca</status> i campioni nemici da {{ e4 }} a {{ e7 }} secondi, infligge da <magicDamage>{{ mintotaldamage }}</magicDamage> a <magicDamage>{{ maxtotaldamage }} danni magici</magicDamage> e ripristina la riduzione danni per {{ e8 }} secondi. La durata, la gittata e i danni della provocazione aumentano con il tempo di carica.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rapporto salute scudo", "Riduzione danni magici", "Riduzione danni fisici", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ passiveshieldhealthratio*100.000000 }}% -> {{ passiveshieldhealthrationl*100.000000 }}%", "{{ e1 }}% -> {{ e1NL }}%", "{{ effect1amount*0.500000 }}% -> {{ effect1amountnl*0.500000 }}%", "{{ maximumwbasedamage }} -> {{ maximumwbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [2, 2, 2, 2, 2], [15, 15, 15, 15, 15], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [2, 2, 2, 2, 2], [1.25, 1.25, 1.25, 1.25, 1.25], [4, 4, 4, 4, 4]], "effectBurn": [null, "25/30/35/40/45", "2", "15", "0.5", "0", "1", "1.5", "2", "1.25", "4"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "GalioW.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GalioE", "name": "Pugno della giustizia", "description": "Galio fa un passo indietro e carica, lanciando in aria il primo campione nemico che incontra.", "tooltip": "<PERSON><PERSON><PERSON> balza in avanti con un possente colpo, <status>lanciando in aria</status> il primo campione colpito per {{ knockupduration }} secondi e infliggendogli <magicDamage>{{ totaldamage }} danni magici</magicDamage>. Gli altri nemici sulla traiettoria subiscono <magicDamage>{{ pvedamage }} danni magici</magicDamage>.<br /><br />Lo scatto di Galio si ferma quando colpisce il terreno.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "GalioE.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GalioR", "name": "Entrata dell'eroe", "description": "Galio sceglie la posizione di un campione alleato come punto di atterraggio e fornisce uno scudo magico a tutti gli alleati nell'area. Dopo un breve lasso di tempo Galio colpisce la posizione, lanciando in aria i nemici nelle vicinanze.", "tooltip": "Galio sceglie la posizione di un campione alleato come punto di atterraggio, fornendo a tutti i campioni alleati nell'area lo <shield>scudo</shield> passivo di <spellName>Scudo di Durand</spellName> per {{ temporarywshieldduration }} secondi. A questo punto, Galio vola verso la zona di atterraggio.<br /><br /><PERSON>uando atterra, <status>lancia in aria</status> i nemici per {{ stundurationouter }} secondi e gli infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Gitt<PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4000, 4750, 5500], "rangeBurn": "4000/4750/5500", "image": {"full": "GalioR.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Distruzione del colosso", "description": "Ogni manciata di secondi, il prossimo attacco base di Galio infligge danni magici bonus in un'area.", "image": {"full": "Galio_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}