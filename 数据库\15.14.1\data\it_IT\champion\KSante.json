{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"KSante": {"id": "KSante", "key": "897", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "l'orgoglio di Nazumah", "image": {"full": "KSante.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "897000", "num": 0, "name": "default", "chromas": false}, {"id": "897001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "897002", "num": 2, "name": "K'Sant<PERSON> (edizione prestigio)", "chromas": false}, {"id": "897008", "num": 8, "name": "K'Sante HEARTSTEEL", "chromas": true}], "lore": "<PERSON>prez<PERSON><PERSON> e coraggioso, K'Sante affronta mostri colossali e gli spietati Ascesi per proteggere la sua patria, <PERSON><PERSON><PERSON>, un'ambitissima oasi tra le sabbie di Shurima. Dopo essersi separato dal suo compagno, K'Sante capisce che per diventare un guerriero degno di guidare la sua città deve prima porre freno al suo ego affamato di successo. Solo così potrà evitare la trappola dell'orgoglio, trovando la saggezza necessaria per sconfiggere i mostri che minacciano il suo popolo.", "blurb": "Sprez<PERSON><PERSON> e coraggioso, K'Sante affronta mostri colossali e gli spietati Ascesi per proteggere la sua patria, <PERSON><PERSON><PERSON>, un'ambitissima oasi tra le sabbie di Shurima. Dopo essersi separato dal suo compagno, K'Sante capisce che per diventare un guerriero...", "allytips": ["Usa Onda sonica prima di impiegare Furia del dragone in modo da poter inseguire il bersaglio con Colpo risonante.", "Approfitta di Millemani, colpendo con degli attacchi base tra un lancio di incantesimo e l'altro: in questo modo aumenterai i danni al massimo e ridurrai il costo in energia.", "Lanciare su di sé Salvaguardia e usare poi Volontà di ferro è il modo ideale per uccidere i mostri neutrali nella giungla."], "enemytips": ["State divisi per minimizzare l'impatto dell'abilità suprema di Lee Sin, Furia del dragone.", "Lee Sin può combattere i danni fisici attraverso l'uso di Volontà di ferro e Menomazione, ma è sempre vulnerabile ai danni magici.", "Lee Sin dipende molto dall'usare le proprie abilità una dietro l'altra. Lancia degli impedimenti per evitare che colleghi le sue abilità e i suoi attacchi."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 8, "magic": 7, "difficulty": 9}, "stats": {"hp": 625, "hpperlevel": 120, "mp": 320, "mpperlevel": 60, "movespeed": 330, "armor": 36, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 2.1, "attackrange": 150, "hpregen": 9.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.688}, "spells": [{"id": "KSanteQ", "name": "Colpi di Ntofo", "description": "<PERSON><PERSON><PERSON><PERSON>, da<PERSON><PERSON><PERSON><PERSON> e rallentando i nemici lungo una breve linea.<br><br><PERSON> colpo, conferisce una carica di Colpi di Ntofo. A 2 cariche, K'Sante spara un'onda d'urto che attira i nemici.<br><br>In Tutto per tutto, la ricarica è ridotta.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> colpisce il terreno con la sua arma, infliggendo <physicalDamage>{{ basedamage }} danni fisici</physicalDamage> e <status>rallentando</status> i nemici di un {{ slowpercent*100 }}% per {{ slowduration }} secondo/i. Se colpisce, conferisce una carica di Colpi di Ntofo per {{ recastwindow }} secondi. A 2 cariche, K'Sante spara invece un'onda d'urto che <status>stordisce</status> e <status>attira</status> i nemici per {{ stunduration }} secondo/i.<br /><br /><keywordMajor><PERSON><PERSON> per tutto</keywordMajor>: la ricarica è ridotta di un {{ rcooldownreduction.0*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ flatdamage }} -> {{ flatdamageNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "KSanteQ.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteW", "name": "Apripista", "description": "<PERSON>'Sant<PERSON> si carica subendo danni ridotti, poi scatta, respingendo e stordendo i nemici.<br><br>In Tutto per tutto, infligge danni aumentati e perde gli effetti di respinta e stordimento.", "tooltip": "K'<PERSON><PERSON> alza le sue armi per difendersi per {{ mindurationtooltip }}-{{ maxduration.1 }} secondo/i, diventa inarrestabile e riduce i danni subiti di un {{ damagereduction*100 }}%. Poi parte alla carica, infliggendo <physicalDamage>{{ basedamage }} + {{ totalmaxhealthdamage }} della salute massima del bersaglio in danni fisici</physicalDamage>. I nemici colpiti vengono <status>respinti</status> e <status>storditi</status> per {{ minknockbackduration }}-{{ maxknockbackduration }} secondo/i (in base al tempo di carica).<br /><br /><keywordMajor>Tutto per tutto:</keywordMajor> la ricarica si azzera. Infligge un {{ rdamageincreasemin*100 }}-{{ rdamageincreasemax*100 }}% di danni aggiuntivi come <trueDamage>danni puri</trueDamage> (in base al tempo di carica), la riduzione dei danni aumenta a {{ rdamagereduction*100 }}% e la velocità dello scatto aumenta, ma non ci sono più gli effetti di <status>respinta</status> o <status>stordimento</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KSanteW.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteE", "name": "<PERSON><PERSON><PERSON> d'<PERSON>lio", "description": "<PERSON>'<PERSON><PERSON> scatta e ottiene uno scudo. Se si punta un alleato, K'Sant<PERSON> scatta verso di esso con gittata aumentata ed entrambi ottengono uno scudo.<br><br>In Tutto per tutto, la ricarica è ridotta e la velocità è aumentata.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> scatta e ottiene <shield>uno scudo da {{ totalshield }}</shield> per {{ shieldduration }} secondi. Se scatta verso un alleato, la distanza dello scatto aumenta sensibilmente e anche l'alleato ottiene uno <shield>scudo</shield>.<br /><br /><keywordMajor>Tutto per tutto</keywordMajor>: la ricarica è ridotta di un {{ cooldownmodao*100 }}% e la velocità dello scatto è aumentata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ shieldbaseamountfast }} -> {{ shieldbaseamountfastNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "KSanteE.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteR", "name": "Tutto per tutto", "description": "K'<PERSON><PERSON> respinge un nemico, lanciandolo contro i muri lungo la traiettoria. <PERSON><PERSON>, entra nello stato Tutto per tutto e scatta verso il bersaglio, ottenendo un aumento di danni, cure e abilità trasformate, al costo di una riduzione delle difese.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> distrugge i suoi ntofo per <status>respingere</status> un campione nemico, infliggendo <physicalDamage>{{ basedamage }} danni fisici</physicalDamage>, poi scatta alle sue spalle ed entra nello stato <keywordMajor>Tutto per tutto</keywordMajor> per {{ alloutduration }} secondo/i. Se il nemico colpisce un muro, viene <status>respinto</status> e K'Sante lo colpisce ancora infliggendo <physicalDamage>{{ totaldamageslamdown }} danni fisici</physicalDamage>.<br /><br />In <keywordMajor>Tutto per tutto</keywordMajor>, le abilità di K'Sante vengono potenziate e lui ottiene <attackSpeed>{{ attackspeed*100 }}% velocità d'attacco</attackSpeed>, {{ armorpenpercent*100 }}% penetrazione armatura bonus e <omnivamp>{{ omnivamp*100 }}% Rubavita globale</omnivamp>, ma perde un <healing>{{ healthlost*100 }}% della salute massima</healing>, un <scaleArmor>{{ defenseslost*100 }}% dell'armatura bonus</scaleArmor> e un <scaleMR>{{ defenseslost*100 }}% della resistenza magica bonus</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ricarica", "Velocità d'attacco"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slamdownstrikedamage }} -> {{ slamdownstrikedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "KSanteR.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> in<PERSON>", "description": "Le abilità di K'Sante marchiano i bersagli per infliggere più danni all'attacco successivo.<br><br>In Tutto per tutto, gli attacchi e le abilità di K'Sante infliggono più danni.", "image": {"full": "Icons_KSante_P.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}