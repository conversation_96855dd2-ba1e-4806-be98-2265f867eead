{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MissFortune": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "21", "name": "Miss Fortune", "title": "the Bounty Hunter", "image": {"full": "MissFortune.png", "sprite": "champion2.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "21000", "num": 0, "name": "default", "chromas": false}, {"id": "21001", "num": 1, "name": "Cowgirl Miss <PERSON>", "chromas": false}, {"id": "21002", "num": 2, "name": "Waterloo Miss Fortune", "chromas": false}, {"id": "21003", "num": 3, "name": "Secret Agent <PERSON>", "chromas": false}, {"id": "21004", "num": 4, "name": "<PERSON> Cane <PERSON>", "chromas": false}, {"id": "21005", "num": 5, "name": "Road Warrior Miss <PERSON>", "chromas": false}, {"id": "21006", "num": 6, "name": "Crime City Miss Fortune", "chromas": false}, {"id": "21007", "num": 7, "name": "Arcade Miss Fortune", "chromas": true}, {"id": "21008", "num": 8, "name": "Captain <PERSON>", "chromas": false}, {"id": "21009", "num": 9, "name": "Pool Party Miss Fortune", "chromas": false}, {"id": "21015", "num": 15, "name": "Star Guardian Miss Fortune", "chromas": false}, {"id": "21016", "num": 16, "name": "Gun Goddess Miss Fortune", "chromas": false}, {"id": "21017", "num": 17, "name": "Pajama Guardian Miss Fortune", "chromas": false}, {"id": "21018", "num": 18, "name": "Bewitching Miss <PERSON>", "chromas": false}, {"id": "21020", "num": 20, "name": "Prestige Bewitching Miss Fortune", "chromas": false}, {"id": "21021", "num": 21, "name": "Ruined <PERSON>", "chromas": true}, {"id": "21031", "num": 31, "name": "Battle Bunny Miss <PERSON>", "chromas": true}, {"id": "21032", "num": 32, "name": "Admiral <PERSON>", "chromas": false}, {"id": "21033", "num": 33, "name": "Prestige Bewitching Miss Fortune (2022)", "chromas": false}, {"id": "21040", "num": 40, "name": "Broken Covenant Miss Fortune", "chromas": true}, {"id": "21041", "num": 41, "name": "Prestige Broken Covenant Miss Fortune", "chromas": false}, {"id": "21050", "num": 50, "name": "Porcelain Miss Fortune", "chromas": true}, {"id": "21060", "num": 60, "name": "Battle Queen Miss Fortune", "chromas": true}], "lore": "A Bilgewater captain famed for her looks but feared for her ruthlessness, <PERSON> paints a stark figure among the hardened criminals of the port city. As a child, she witnessed the reaver king <PERSON><PERSON><PERSON> murder her family—an act she brutally avenged years later, blowing up his flagship while he was still aboard. Those who underestimate her will face a beguiling and unpredictable opponent… and, likely, a bullet or two in their guts.", "blurb": "A Bilgewater captain famed for her looks but feared for her ruthlessness, <PERSON> paints a stark figure among the hardened criminals of the port city. As a child, she witnessed the reaver king <PERSON><PERSON><PERSON> murder her family—an act she brutally...", "allytips": ["<PERSON> ramps up speed if she hasn't recently taken damage. Avoid getting hit to move very fast.", "Use Double Up on the furthest enemy minion if enemy champions are hiding in back; it will bounce to them for lots of damage.", "Make sure to utilize Love Tap while <PERSON><PERSON><PERSON> is on cooldown to maximize the active's availability."], "enemytips": ["<PERSON>'s speed boost is removed if she is damaged by an enemy.", "<PERSON> is easy to kill if you can reach her; target her first in team fights."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 1}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 300, "mpperlevel": 40, "movespeed": 325, "armor": 25, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 2.4, "attackspeedperlevel": 3, "attackspeed": 0.656}, "spells": [{"id": "MissFortuneRicochetShot", "name": "Double Up", "description": "<PERSON> fires a bullet at an enemy, damaging them and a target behind them. Both strikes can also apply Love Tap.", "tooltip": "<PERSON> fires a bouncing shot, dealing <physicalDamage>{{ totaldamagetooltip }} physical damage</physicalDamage> to an enemy and to another one behind them. <br /><br />The second shot can critically strike. It always critically strikes if the first shot kills its target. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [20, 20, 20, 20, 20], [40, 40, 40, 40, 40], [110, 110, 110, 110, 110], [160, 160, 160, 160, 160], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "20/45/70/95/120", "0", "0", "40", "20", "40", "110", "160", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MissFortuneRicochetShot.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissFortuneViciousStrikes", "name": "Strut", "description": "<PERSON> passively gains Move Speed when not attacked. This ability can be activated to grant bonus Attack Speed for a short duration. While it's on cooldown, Love Taps reduce the remaining cooldown of Strut.", "tooltip": "<passive>Passive:</passive> After {{ passivebasemsooc }} seconds of not taking damage, <PERSON> gains <speed>{{ passivebasems }} Move Speed</speed>. After another {{ passivemaxmsextraooc }} seconds, this increases to <speed>{{ passivemaxms }}</speed>.<br /><br /><active>Active:</active> Gain the passive effect's full <speed>Move Speed</speed> bonus and <attackSpeed>{{ activeas*100 }}% Attack Speed</attackSpeed> for {{ activeduration }} seconds.<br /><br /><spellName>Love Taps</spellName> reduce the Cooldown of this Ability by {{ lovetaprefund }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Speed", "Move Speed", "Max Move Speed"], "effect": ["{{ activeas*100.000000 }}% -> {{ activeasnl*100.000000 }}%", "{{ passivebasems }} -> {{ passivebasemsNL }}", "{{ passivemaxms }} -> {{ passivemaxmsNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "MissFortuneViciousStrikes.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissFortuneScattershot", "name": "Make It Rain", "description": "<PERSON> reveals an area with a flurry of bullets, dealing waves of damage to opponents and slowing them.", "tooltip": "Miss <PERSON> rains bullets, revealing an area, <status>Slowing</status> by {{ totalslowamount }} and dealing <magicDamage>{{ totaldamagepersecond }} magic damage</magicDamage> per second for {{ baseduration }} seconds (total <magicDamage>{{ totaldamage }} magic damage</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Per Second", "Cooldown"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "MissFortuneScattershot.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MissFortuneBulletTime", "name": "Bullet Time", "description": "<PERSON> channels a barrage of bullets into a cone in front of her, dealing large amounts of damage to enemies. Each wave of Bullet Time can critically strike", "tooltip": "Miss <PERSON> channels a barrage of bullets, firing {{ totalwaves }} waves over {{ totalchannelduration }} seconds, dealing <physicalDamage>{{ physicaldamageperwave }} physical damage</physicalDamage> per wave (total <physicalDamage>{{ totalphysicaldamage }} physical damage</physicalDamage>).<br /><br />Each wave can critically strike for %i:scaleCrit% <physicalDamage>{{ physicalcritperwave }} physical damage</physicalDamage> (total %i:scaleCrit% <physicalDamage>{{ totalphysicalcritdamage }} physical damage</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bullet Barrages", "Cooldown"], "effect": ["{{ basewaves }} -> {{ basewavesNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MissFortuneBulletTime.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Love Tap", "description": "<PERSON> deals bonus physical damage whenever she basic attacks a new target.", "image": {"full": "MissFortune_W.png", "sprite": "passive2.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}