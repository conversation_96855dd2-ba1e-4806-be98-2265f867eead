{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Orianna": {"id": "<PERSON><PERSON><PERSON>", "key": "61", "name": "<PERSON><PERSON><PERSON>", "title": "la signora degli ingranaggi", "image": {"full": "Orianna.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "61000", "num": 0, "name": "default", "chromas": false}, {"id": "61001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61003", "num": 3, "name": "Orianna delle Lame", "chromas": false}, {"id": "61004", "num": 4, "name": "TPA Orianna", "chromas": false}, {"id": "61005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "61007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "61008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "61011", "num": 11, "name": "Orianna Festa in Piscina", "chromas": true}, {"id": "61020", "num": 20, "name": "Oriape", "chromas": true}, {"id": "61029", "num": 29, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "61038", "num": 38, "name": "Orianna T1", "chromas": true}], "lore": "<PERSON><PERSON>na è una meraviglia tecnologica interamente composta da ingranaggi, ma in realtà un tempo era una curiosa ragazza in carne e ossa. Si ammalò gravemente dopo un incidente nei settori inferiori di Zaun e il suo corpo distrutto venne ricostruito pezzo dopo pezzo con grande perizia. Accompagnata e protetta da un'enorme sfera di ottone, <PERSON><PERSON>na è ora libera di esplorare le meraviglie di Piltover e oltre.", "blurb": "<PERSON>ianna è una meraviglia tecnologica interamente composta da ingranaggi, ma in realtà un tempo era una curiosa ragazza in carne e ossa. Si ammalò gravemente dopo un incidente nei settori inferiori di Zaun e il suo corpo distrutto venne ricostruito pezzo...", "allytips": ["Comando: protezione può essere usato su te stesso per fare in modo che la Palla torni a te velocemente. Combinalo con Comando: attacco per un attacco veloce.", "Comando: dissonanza è una potente via di fuga se <PERSON>ianna ha la Palla. La combinazione di velocità e ostacolo rallentante può essere molto efficace.", "Comando: onda d'urto può essere usato per trascinare i nemici verso di te o lontano da te in base a come posizioni la Palla."], "enemytips": ["<PERSON><PERSON><PERSON> solo colpire l'area intorno alla Palla. Usa questa cosa a tuo vantaggio.", "Fai attenzione al ritorno inaspettato della Palla di Orianna a causa della gittata. Potrebbe causare delle situazioni inaspettate."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 3, "magic": 9, "difficulty": 7}, "stats": {"hp": 585, "hpperlevel": 110, "mp": 418, "mpperlevel": 25, "movespeed": 325, "armor": 20, "armorperlevel": 4.2, "spellblock": 26, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 44, "attackdamageperlevel": 2.6, "attackspeedperlevel": 3.5, "attackspeed": 0.658}, "spells": [{"id": "OrianaIzunaCommand", "name": "Comando: attacco", "description": "Orianna ordina alla sua Palla di spararsi verso una posizione bersaglio, infliggendo danni magici ai bersagli sulla sua traiettoria (infligge meno danni ai bersagli successivi). La Palla rimane in quella posizione dopo l'attacco.", "tooltip": "<PERSON><PERSON>na ordina alla sua <keywordMajor><PERSON><PERSON></keywordMajor> di spostarsi verso un'area, infliggendo <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage> ai nemici circostanti e a quelli che attraversa. Infligge un {{ e2 }}% di danni in meno a tutti i nemici dopo il primo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [6, 5.25, 4.5, 3.75, 3], "cooldownBurn": "6/5.25/4.5/3.75/3", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [30, 30, 30, 30, 30], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "30", "70", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [815, 815, 815, 815, 815], "rangeBurn": "815", "image": {"full": "OrianaIzunaCommand.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrianaDissonanceCommand", "name": "Comando: Dissonanza", "description": "<PERSON><PERSON>na ordina alla sua Palla di rilasciare un impulso di energia, che infligge danni magici intorno a sé. Questa energia lascia un campo che accelera gli alleati e rallenta i nemici.", "tooltip": "<PERSON><PERSON>na ordina alla sua <keywordMajor>Palla</keywordMajor> di rilasciare un impulso elettrico che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici circostanti.<br /><br />L'impulso lascia dietro di sé un campo energetico per {{ fieldduration }} secondi, che <status>rallenta</status> i nemici del {{ slowamount*100 }}% e fornisce agli alleati <speed>{{ hasteamount*100 }}% velocità di movimento</speed> che decresce in {{ slowandhasteduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "Rallentamento", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ hasteamount*100.000000 }}% -> {{ hasteamountnl*100.000000 }}%", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [30, 35, 40, 45, 50], [30, 35, 40, 45, 50], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/105/150/195/240", "30/35/40/45/50", "30/35/40/45/50", "3", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [225, 225, 225, 225, 225], "rangeBurn": "225", "image": {"full": "OrianaDissonanceCommand.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrianaRedactCommand", "name": "Comando: protezione", "description": "Orianna ordina alla sua Palla di attaccarsi a un campione alleato, proteggendolo e infliggendo danni magici ai nemici che colpisce durante i suoi movimenti. In più, la Palla conferisce armatura e resistenza magica aggiuntive al campione a cui è attaccata.", "tooltip": "<spellPassive>Passiva: </spellPassive>la <keywordMajor>Palla</keywordMajor> aggiunge <scaleArmor>{{ e2 }} armatura</scaleArmor> e <scaleMR>{{ e2 }} resistenza magica</scaleMR> al campione alleato a cui è attaccata.<br /><br /><spellActive>Attiva: </spellActive>Orianna ordina alla sua <keywordMajor>Palla</keywordMajor> di attaccarsi a un campione alleato, fornendogli uno <shield>scudo da {{ totalshieldtooltip }}</shield> per {{ e5 }} secondi. I nemici a cui la <keywordMajor>Palla</keywordMajor> passa attraverso subiscono <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Bonus armatura", "Bonus resistenza magica", "Quantità scudo"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [55, 90, 125, 160, 195], [6, 12, 18, 24, 30], [60, 90, 120, 150, 180], [75, 75, 75, 75, 75], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "55/90/125/160/195", "6/12/18/24/30", "60/90/120/150/180", "75", "2.5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1095, 1095, 1095, 1095, 1095], "rangeBurn": "1095", "image": {"full": "OrianaRedactCommand.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "OrianaDetonateCommand", "name": "Comando: onda d'urto", "description": "<PERSON>ianna ordina alla sua Palla di rilasciare un'onda d'urto, che infligge danni magici e lancia i nemici nelle vicinanze verso la Palla per un breve attimo.", "tooltip": "<PERSON><PERSON>na ordina alla sua <keywordMajor>Palla</keywordMajor> di scatenare un'onda d'urto che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici nelle vicinanze e li <status>lancia</status> in direzione della <keywordMajor>Palla</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [410, 410, 410], "rangeBurn": "410", "image": {"full": "OrianaDetonateCommand.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Movimento meccanico", "description": "<PERSON>li attacchi di Orianna infliggono danni magici aggiuntivi. <PERSON>ù Orianna attacca lo stesso bersaglio, più i danni aumentano.", "image": {"full": "OriannaPassive.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}