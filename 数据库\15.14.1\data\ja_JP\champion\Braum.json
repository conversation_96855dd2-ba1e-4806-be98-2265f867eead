{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "ブラウム", "title": "フレヨルドの漢気", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "龍殺しブラウム", "chromas": true}, {"id": "201002", "num": 2, "name": "エル・ティグレ ブラウム", "chromas": false}, {"id": "201003", "num": 3, "name": "獅子心王ブラウム", "chromas": false}, {"id": "201010", "num": 10, "name": "サンタ ブラウム", "chromas": false}, {"id": "201011", "num": 11, "name": "クライムシティー ブラウム", "chromas": true}, {"id": "201024", "num": 24, "name": "シュガーラッシュ ブラウム", "chromas": true}, {"id": "201033", "num": 33, "name": "プールパーティ ブラウム", "chromas": true}, {"id": "201042", "num": 42, "name": "グリルマスター ブラウム", "chromas": true}], "lore": "巨大な筋肉が盛り上がる腕と、その筋肉よりも大きな優しい心を持つブラウムは、フレヨルドの誰もが愛する英雄だ。フロストヘルドの北の酒場では、一晩でオークの森を伐採した話や、パンチで山が崩れ去った話など、誰もが彼の怪力伝説に花を咲かせて酒を飲む。魔法の力を宿した宝物庫の扉を盾にして、その筋肉にも負けぬ立派な口ひげで微笑みながら凍てつく北部を歩き回る彼は、助けを必要とする者にとって最高に頼りになる存在だ。", "blurb": "巨大な筋肉が盛り上がる腕と、その筋肉よりも大きな優しい心を持つブラウムは、フレヨルドの誰もが愛する英雄だ。フロストヘルドの北の酒場では、一晩でオークの森を伐採した話や、パンチで山が崩れ去った話など、誰もが彼の怪力伝説に花を咲かせて酒を飲む。魔法の力を宿した宝物庫の扉を盾にして、その筋肉にも負けぬ立派な口ひげで微笑みながら凍てつく北部を歩き回る彼は、助けを必要とする者にとって最高に頼りになる存在だ。", "allytips": ["味方と協力して「漢の拳」を有効活用しよう。「漢の拳」でマークした対象を、味方に積極的に通常攻撃してもらうのだ。", "「不破の盾」で防御力の低い味方の前に移動し、敵の遠距離攻撃から守ってあげよう。", "「氷河の裂溝」は強力なスロウゾーンを出現させる。戦略的に配置して敵チームを分断し、敵の侵攻を食い止めよう。"], "enemytips": ["ブラウムは「漢の拳」をスタックするために、まず「冬の凍瘡」か通常攻撃を命中させる必要がある。", "もし攻撃を当てられたら、さらに3回攻撃されてスタン状態になる前に戦闘範囲から離脱しよう。", "ブラウムのアルティメットスキルは発動までの時間が長いので、この隙を利用してうまく回避しよう。", "また、発動後も氷の地割れを踏むとスロウ状態になってしまう。逃げるときは地割れを踏まずにすむ位置に移動するのが重要だ。", "「不破の盾」は構えた方向からの攻撃をブロックしてしまう。効果が消えるまで待つか、別方向からの攻撃を狙おう。"], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "冬の凍瘡", "description": "盾から氷塊を発射し、最初に命中した敵ユニットに魔法ダメージとスロウ効果を与える。<br><br>命中した敵には<font color='#FFF673'>「漢の拳」</font>がスタックする。", "tooltip": "盾から氷塊を放つ。最初に命中した敵ユニットに<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ e2 }}%の<status>スロウ効果</status>を付与する。このスロウ効果は{{ e5 }}秒かけて徐々に減少する。<br /><br />命中した対象には<keywordMajor>「漢の拳」</keywordMajor>がスタックする。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "BraumW", "name": "ワシに任せとけ！", "description": "指定した味方チャンピオンまたはミニオンのもとへ跳躍する。付近に敵チャンピオンがいた場合、対象と敵チャンピオンの間に着地する。着地したあと、自身と対象の物理防御と魔法防御を数秒間増加させる。", "tooltip": "味方チャンピオンまたはミニオンのもとへ駆けつける。移動したあと{{ e1 }}秒間、対象の<scaleArmor>物理防御を{{ grantedallyarmor }}</scaleArmor>、<scaleMR>魔法防御を{{ grantedallymr }}</scaleMR>増加させる。自身も同じ時間、<scaleArmor>物理防御が{{ grantedbraumarmor }}</scaleArmor>、<scaleMR>魔法防御が{{ grantedbraummr }}</scaleMR>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本防御力", "クールダウン"], "effect": ["{{ baseresists }} -> {{ baseresistsNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "BraumE", "name": "不破の盾", "description": "指定方向に巨大な盾を掲げ、数秒間すべての遠距離攻撃を体を張って食い止める。盾を構えている間は移動速度が増加し、最初に盾に当たった攻撃のダメージを無効化する。盾を構えている間、同方向から来る攻撃のダメージを軽減する。", "tooltip": "{{ e2 }}秒間盾をかまえ、指定方向から飛んでくる敵の飛翔物を食い止める(自身に命中させ、消滅させる)。最初に受けた飛翔物からはダメージを受けず、2発目以降から受けるダメージは{{ e3 }}%軽減される。<br /><br />盾をかまえている間は<speed>移動速度が{{ e4 }}%</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["効果時間", "ダメージ軽減", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "BraumRWrapper", "name": "氷河の裂溝", "description": "盾を地面にたたきつけて指定方向に地割れを起こし、周囲にいる敵と地割れのライン上にいる敵をノックアップさせる。<br>地割れは短時間持続し、範囲内に入った敵にスロウ効果を付与する。", "tooltip": "盾を地面にたたきつけて指定方向に地割れを起こし、自身の周囲と地割れのライン上にいる敵を<status>ノックアップ</status>させ、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。最初に命中した対象を自身からの距離に応じて{{ minknockup }} - {{ maxknockup }}秒間<status>ノックアップ</status>させる。命中した他の対象は{{ minknockup }}秒間<status>ノックアップ</status>させる。<br /><br />地割れは{{ movespeedmod }}%の<status>スロウ効果</status>を与えるエリアを{{ slowzoneduration }}秒間発生させる。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "ノックアップ効果時間", "スロウ効果", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ firstknockupduration }} -> {{ firstknockupdurationNL }}", "{{ movespeedmod }}% -> {{ movespeedmodNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "漢の拳", "description": "通常攻撃を命中させると「漢の拳」がスタックする。スタックが1つ以上ある敵には、<font color='#FFF673'>味方</font>の通常攻撃でも効果がスタックする。 <br><br>スタックが4つ溜まると対象はスタン状態になり、魔法ダメージを受ける。スタン状態になった敵はその後、数秒間はスタックが増加しない。ただしブラウムの攻撃が命中すると追加魔法ダメージを受ける。", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}