{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "<PERSON><PERSON><PERSON>", "title": "il camminatore del Vuoto", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "Kassadin del Festival", "chromas": false}, {"id": "38002", "num": 2, "name": "<PERSON><PERSON><PERSON> del Profondo", "chromas": false}, {"id": "38003", "num": 3, "name": "<PERSON><PERSON>din Prima del Vuoto", "chromas": false}, {"id": "38004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38006", "num": 6, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "38014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38015", "num": 15, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "38024", "num": 24, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Mentre lascia una scia di fiamme nei luoghi più oscuri del mondo, <PERSON><PERSON><PERSON> sa bene di avere i giorni contati. Avventuriero navigato ed esperta guida di Shurima, scelse di farsi una famiglia tra le pacifiche tribù del sud... fino al giorno in cui il suo villaggio fu consumato dal Vuoto. Da allora ha giurato vendetta, iniziando a combinare manufatti arcani e tecnologie proibite per prepararsi allo scontro imminente. Ora Kassadin è partito per le terre desolate di Icathia, pronto ad affrontare qualsiasi mostruoso costrutto del Vuoto alla ricerca di colui che si è proclamato il loro profeta: Malzahar.", "blurb": "Mentre lascia una scia di fiamme nei luoghi più oscuri del mondo, <PERSON><PERSON><PERSON> sa bene di avere i giorni contati. Avventuriero navigato ed esperta guida di Shurima, scelse di farsi una famiglia tra le pacifiche tribù del sud... fino al giorno in cui il suo...", "allytips": ["<PERSON><PERSON><PERSON> ha diverse possibilità per gli oggetti; può diventare un mago con mana e potere magico, oppure un anti-mago con riduzione ricarica e resistenza magica.", "L'abilità suprema di Kassadin ha diversi usi e ha un tempo di ricarica molto basso rispetto alle altre: usala spesso.", "Cerca di prendere il buff del Guardiano di quarzo per neutralizzare il costo crescente di mana di Frattura spazio temporale."], "enemytips": ["<PERSON><PERSON><PERSON> infligge per lo più danni magici. Se sta andando bene, considera l'acquisto di oggetti con resistenza magica come i Calzari di Mercurio e il Velo della banshee.", "Ogni volta che Kassadin usa Frattura spazio temporale, consuma sempre più mana. Tienilo a mente mentre lo insegui.", "Servono 6 abilità per poter lanciare Pulsar della forza. Se prende un livello in questa abilità, usa con giudizio le tue abilità in corsia contro di lui."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "Sfera del nulla", "description": "<PERSON><PERSON><PERSON> spara una sfera di energia del Vuoto a un bersaglio, infliggendogli danni e interrompendo le canalizzazioni. L'energia in eccesso gli si plasma addosso, conferendo uno scudo temporaneo che assorbe i danni magici.", "tooltip": "<PERSON><PERSON><PERSON> spara una sfera di energia del Vuoto, infliggendogli <magicDamage>{{ totaldamage }} danni magici</magicDamage> e interrompendo le canalizzazioni. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> ottiene uno <shield>scudo magico da {{ totalshield }}</shield> per 1,5 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>herBlade", "name": "<PERSON> degli inferi", "description": "Passiva: gli attacchi base di Kassadin infliggono danni magici bonus. Attiva: il prossimo attacco base di Kassadin infligge molti danni magici bonus e fa recuperare mana.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi di Kassadin infliggono <magicDamage>{{ onhitdamage }} danni magici</magicDamage> bonus.<br /><br /><spellActive>Attiva:</spellActive> Ka<PERSON><PERSON> carica la sua lama e il suo prossimo attacco infligge <magicDamage>{{ activedamage }} danni magici</magicDamage> e ripristina <scaleMana>{{ e1 }}% del suo mana mancante</scaleMana> aumentato al <scaleMana>{{ e4 }}%</scaleMana> contro i campioni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ripristino mana base", "<PERSON><PERSON><PERSON><PERSON> mana campione"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ e4 }}% -> {{ e4NL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ForcePulse", "name": "<PERSON><PERSON><PERSON>za", "description": "<PERSON><PERSON><PERSON> attinge all'energia delle abilità lanciate nelle vicinanze. Una volta carico, <PERSON><PERSON><PERSON> può usare Pulsar della forza per danneggiare e rallentare i nemici in un'area conica davanti a sé.", "tooltip": "<spellPassive>Passiva:</spellPassive> la ricarica di <spellName>P<PERSON>sar della forza</spellName> è ridotta di {{ reductionperspellcast }} secondo/i ogni volta che viene usata un'abilità nei pressi di <PERSON><PERSON>din.<br /><br /><spellActive>Attiva:</spellActive> <PERSON><PERSON><PERSON> scatena un impulso di vuoto, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallentando</status> i nemici di un {{ e2 }}% per {{ e3 }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RiftWalk", "name": "Frattura spazio temporale", "description": "<PERSON><PERSON><PERSON> si teletrasporta in una posizione vicina, infliggendo danni alle unità nemiche nelle vicinanze. Usare più Fratture spazio temporali in un breve periodo di tempo costa più mana, ma permette di infliggere più danni.", "tooltip": "<PERSON><PERSON><PERSON> si teletrasporta in un punto vicino e infligge <magicDamage>{{ basedamage }} danni magici</magicDamage>.<br /><br />Ogni successivo utilizzo di questa abilità entro i prossimi {{ rstackduration }} secondi raddoppia il costo in mana e infligge <magicDamage>{{ bonusdamage }} danni magici</magicDamage> aggiuntivi. L'aumento del costo e dei danni può accumularsi fino a {{ maxstacks }} volte.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> per carica", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ stackdamage }} -> {{ stackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pietra del nulla", "description": "<PERSON><PERSON><PERSON> subisce danni magici ridotti e ignora le collisioni.", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}