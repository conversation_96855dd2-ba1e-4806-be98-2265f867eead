{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"LeeSin": {"id": "<PERSON><PERSON><PERSON>", "key": "64", "name": "リー・シン", "title": "盲目の修行僧", "image": {"full": "LeeSin.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "64000", "num": 0, "name": "default", "chromas": false}, {"id": "64001", "num": 1, "name": "伝統的な衣装リー・シン", "chromas": false}, {"id": "64002", "num": 2, "name": "従者リー・シン", "chromas": false}, {"id": "64003", "num": 3, "name": "龍拳リー・シン", "chromas": true}, {"id": "64004", "num": 4, "name": "ムエタイ リー・シン", "chromas": true}, {"id": "64005", "num": 5, "name": "プールパーティ リー・シン", "chromas": false}, {"id": "64006", "num": 6, "name": "SKT T1 リー・シン", "chromas": false}, {"id": "64010", "num": 10, "name": "K.O. リー・シン", "chromas": false}, {"id": "64011", "num": 11, "name": "神の拳リー・シン", "chromas": false}, {"id": "64012", "num": 12, "name": "司令塔リー・シン", "chromas": true}, {"id": "64027", "num": 27, "name": "混沌の闇リー・シン", "chromas": false}, {"id": "64028", "num": 28, "name": "プレステージ混沌の闇リー・シン", "chromas": false}, {"id": "64029", "num": 29, "name": "FPX リー・シン", "chromas": false}, {"id": "64031", "num": 31, "name": "豪嵐龍リー・シン", "chromas": false}, {"id": "64039", "num": 39, "name": "プレステージ混沌の闇リー・シン(2022)", "chromas": false}, {"id": "64041", "num": 41, "name": "至高のゲーム リー・シン", "chromas": false}, {"id": "64051", "num": 51, "name": "天なる龍リー・シン", "chromas": false}, {"id": "64052", "num": 52, "name": "天なる神龍リー・シン", "chromas": false}, {"id": "64068", "num": 68, "name": "T1 リー・シン", "chromas": false}, {"id": "64072", "num": 72, "name": "墨影のリー・シン", "chromas": false}], "lore": "アイオニアの古代の格闘技をマスターしたリー・シンは、龍の精霊のエッセンスを操ってあらゆる困難を克服する厳しい訓練を積んだ格闘家だ。何年も前に視力を失ったが、この戦闘僧は聖なる均衡を破ろうとするあらゆる脅威から故郷を守るために、自らの人生を捧げている。深い瞑想から得た彼の力を侮る敵は、彼の伝説の燃える拳と炎の回し蹴りの餌食となるだろう。", "blurb": "アイオニアの古代の格闘技をマスターしたリー・シンは、龍の精霊のエッセンスを操ってあらゆる困難を克服する厳しい訓練を積んだ格闘家だ。何年も前に視力を失ったが、この戦闘僧は聖なる均衡を破ろうとするあらゆる脅威から故郷を守るために、自らの人生を捧げている。深い瞑想から得た彼の力を侮る敵は、彼の伝説の燃える拳と炎の回し蹴りの餌食となるだろう。", "allytips": ["「龍の怒り」の前に「響掌」を命中させておくことで「共鳴撃」での追撃が可能になる。", "「練気」の効果を活用しよう。スキルの合間に通常攻撃を挟むと、気の消費を最小に抑えながら最大のダメージを与えることができる。", "自分に「守りの型」を使用してから「鉄の意志」を使用すれば、ジャングルの中立モンスターを楽に倒すことができる。"], "enemytips": ["アルティメットスキル「龍の怒り」による被害を最小限に留めるため、一ヵ所に固まらないようにすること。", "「鉄の意志」と「縛脚」を使えるため物理ダメージへの耐性は高いが、魔法ダメージには弱い。", "リー・シンはコンボ技への依存度が高い。動きを封じるスキルで連続攻撃を阻止しよう。"], "tags": ["Fighter", "Assassin"], "partype": "気", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 645, "hpperlevel": 108, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.7, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 3, "attackspeed": 0.651}, "spells": [{"id": "LeeSinQOne", "name": "響掌/共鳴撃", "description": "響掌: 敵の位置を探るひずんだ音波を発射して、最初に当たった敵に物理ダメージを与える。また、命中してから3秒間は「共鳴撃」を発動できる。<br><br>共鳴撃: 「響掌」が命中した敵に素早く接近し、対象の減少体力に応じた物理ダメージを与える。", "tooltip": "ひずんだ音波を発射して、最初に当たった敵ユニットに<physicalDamage>{{ initialdamage }}の物理ダメージ</physicalDamage>を与え、対象の真の視界を得る。命中してから{{ reactivatetime }}秒間は<recast>再発動</recast>できる。<br /><br /><recast>再発動:</recast> 音波が命中した敵に突撃し、対象の減少体力に応じて<physicalDamage>{{ recastdamage }} - {{ empowereddamage }}の物理ダメージ</physicalDamage>を与える。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「響掌」基本ダメージ", "「共鳴撃」最小基本ダメージ", "「共鳴撃」最大基本ダメージ", "クールダウン"], "effect": ["{{ q1basedamage }} -> {{ q1basedamageNL }}", "{{ q2basedamage }} -> {{ q2basedamageNL }}", "{{ q2basedamage*2.000000 }} -> {{ q2basedamagenl*2.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LeeSinQOne.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LeeSinWOne", "name": "守りの型/鉄の意志", "description": "守りの型: 指定した味方に素早く接近し、シールドを出現させてダメージから身を守る。接近した相手が味方チャンピオンの場合、自身と相手を両方シールドで保護する。また、使用後は「鉄の意志」を発動できる。<br><br>鉄の意志: 厳しい修行の成果により、ライフスティールとスペルヴァンプを増加させる。", "tooltip": "味方のユニットかワードのもとへ素早く移動する。対象が味方チャンピオンの場合、自身とその味方に{{ shieldduration }}秒間、<shield>耐久値{{ shieldamount }}のシールド</shield>を付与する。さらに、このスキルのクールダウンが{{ w1cooldownrecovered*100 }}%短縮される。使用後{{ w1reactivatetime }}秒間は<recast>再発動</recast>できる。<br /><br /><recast>再発動:</recast> {{ lifestealandspellvamptime }}秒間ライフスティールとスペルヴァンプが{{ lifestealandspellvamp }}%増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「守りの型」シールド量", "「鉄の意志」増加ライフスティール/スペルヴァンプ"], "effect": ["{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ lifestealandspellvamp }}% -> {{ lifestealandspellvampNL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LeeSinWOne.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LeeSinEOne", "name": "破風/縛脚", "description": "破風: 地面を強打して衝撃波を発生させることで魔法ダメージを与え、命中した敵ユニットを可視状態にする。「破風」が敵に命中した場合は、「縛脚」で追撃できる。<br><br>縛脚: 「破風」でダメージを受けた敵にスロウ効果を付与する。低下した移動速度は時間の経過とともに徐々に元に戻る。", "tooltip": "破風: 地面を強打して衝撃波を発生させることで<magicDamage>{{ initialdamage }}の魔法ダメージ</magicDamage>を与え、命中した敵ユニットを{{ slowduration }}秒間、可視状態にする。このスキルが敵に命中した場合は、その後{{ reactivatetime }}秒間<recast>再発動</recast>できる。<br /><br /><recast>再発動:</recast> 衝撃波を受けた周囲の敵に{{ slowamount }}%の<status>スロウ効果</status>を与える。この効果は{{ slowduration }}秒かけて元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「破風」基本ダメージ", "「縛脚」スロウ効果"], "effect": ["{{ e1damage }} -> {{ e1damageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LeeSinEOne.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LeeSinR", "name": "龍の怒り", "description": "対象に強力な回し蹴りを食らわせ、物理ダメージを与えると同時に後方へノックバックさせ、その際に衝突した敵ユニットにも物理ダメージを与える。ノックバックした対象と接触した敵は、短時間ノックアップ状態になる(この技はジェシー・パーリングにより伝授されたものだが、リー・シンはプレイヤーをゲームからキックすることはない…きっと)。", "tooltip": "敵チャンピオン1体に強力な回し蹴りを食らわせて後方に<status>ノックバック</status>させ、<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。<br /><br />蹴られたチャンピオンと衝突した敵は短時間<status>ノックアップ</status>されて、<physicalDamage>{{ damage }}(+蹴られたチャンピオンの増加体力の{{ percenthpcarrythrough }}%)の物理ダメージ</physicalDamage>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "攻撃した対象の増加体力によるダメージ率", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ percenthpcarrythrough }}% -> {{ percenthpcarrythroughNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 85, 60], "cooldownBurn": "110/85/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [375, 375, 375], "rangeBurn": "375", "image": {"full": "LeeSinR.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "練気", "description": "スキル使用後、次の通常攻撃2回の攻撃速度が増加し、それぞれ気を回復する。", "image": {"full": "LeeSinPassive.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}