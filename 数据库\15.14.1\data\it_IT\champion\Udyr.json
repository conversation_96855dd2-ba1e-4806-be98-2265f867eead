{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Udyr": {"id": "<PERSON><PERSON><PERSON>", "key": "77", "name": "<PERSON><PERSON><PERSON>", "title": "l'eremita spirituale", "image": {"full": "Udyr.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "77000", "num": 0, "name": "default", "chromas": false}, {"id": "77001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "77002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "77003", "num": 3, "name": "Udyr Guardia Spirituale", "chromas": false}, {"id": "77004", "num": 4, "name": "Sicuramente non Udyr", "chromas": false}, {"id": "77005", "num": 5, "name": "<PERSON><PERSON><PERSON>ago", "chromas": false}, {"id": "77006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Udyr è il più potente eremita spirituale vivente ed entra in comunione con gli spiriti del Freljord, intuendo empaticamente le loro necessità o canalizzando e trasformando le loro energie eteree nel suo stile di combattimento primordiale. Cerca di trovare il suo equilibrio con se stesso, per far sì che la sua mente non si perda tra le altre, ma anche con il mondo, poiché l'universo mistico del Freljord può prosperare solo attraverso la crescita che deriva dal conflitto e dallo scontro... e Udyr sa che vanno fatti dei sacrifici per allontanare l'immobilità della pace.", "blurb": "Udyr è il più potente eremita spirituale vivente ed entra in comunione con gli spiriti del Freljord, intuendo empaticamente le loro necessità o canalizzando e trasformando le loro energie eteree nel suo stile di combattimento primordiale. Cerca di...", "allytips": ["I danni vengono inflitti dopo aver considerato la protezione aggiuntiva dello scudo della tartaruga. <PERSON><PERSON><PERSON>, comprare molti oggetti difensivi può aumentare sensibilmente la tua sopravvivenza.", "Udyr è uno dei migliori campioni da giungla del gioco. Approfitta di questo per dare alla tua squadra un grosso vantaggio di PE e di controllo della mappa."], "enemytips": ["U<PERSON>r ha opzioni a distanza limitate, cerca di mantenere le distanze.", "Dopo aver usato la versione più potente di un'abilità con Risveglio, Udyr non può risvegliare altre abilità per un po' di tempo."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 7, "magic": 4, "difficulty": 7}, "stats": {"hp": 664, "hpperlevel": 92, "mp": 271, "mpperlevel": 50, "movespeed": 350, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.65}, "spells": [{"id": "UdyrQ", "name": "<PERSON><PERSON><PERSON>", "description": "Udyr ottiene velocità d'attacco e i suoi due attacchi successivi infliggono danni fisici bonus. Rilancio: ottiene ancora più velocità d'attacco e i due attacchi successivi evocano un fulmine sul bersaglio.", "tooltip": "<spellActive>Posizione dell'artiglio:</spellActive> <PERSON>dyr <PERSON>tti<PERSON> <attackSpeed>{{ attackspeedbase*100 }}% velocità d'attacco</attackSpeed> e i suoi attacchi infliggono <physicalDamage>{{ onhitdamage }} danni fisici</physicalDamage> %i:OnHit% <OnHit>Sul colpo</OnHit> per {{ attackspeeddurationbase }} secondi. Inoltre, i due attacchi successivi di Udyr in questa posizione infliggono <physicalDamage>{{ maxhponhit1 }} danni fisici bonus in base alla salute massima</physicalDamage> e ottengono una gittata di {{ attackrange }}.<br /><br /><keywordMajor>Risveglio:</keywordMajor> aumenta la <attackSpeed>velocità d'attacco</attackSpeed> bonus fino a <attackSpeed>{{ empoweredtotalas }}</attackSpeed> e i danni in base alla salute massima fino a <physicalDamage>{{ q2totalonhithpdamage }}</physicalDamage>. Inoltre, i due attacchi successivi di Udyr evocano sei fulmini, infliggendo un totale di <magicDamage>{{ empoweredlightningbonusmax }} danni magici in base alla salute massima</magicDamage> ai bersagli isolati (quando possibile, i colpi rimbalzano verso i bersagli vicini).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "% danni salute massima", "<PERSON><PERSON> sul colpo"], "effect": ["{{ attackspeedbase*100.000000 }}% -> {{ attackspeedbasenl*100.000000 }}%", "{{ maxhponhitbase*100.000000 }}% -> {{ maxhponhitbasenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [20, 20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UdyrW", "name": "Manto di ferro", "description": "U<PERSON>r ottiene uno scudo e i suoi due attacchi successivi lo curano. Rilancio: ottiene uno scudo e una guarigione ancora più grandi in base alla salute massima durante i due secondi successivi.", "tooltip": "<spellPassive>Posizione del Manto:</spellPassive> Udyr ottiene uno <shield>scudo da {{ totalshield }}</shield> per {{ shieldduration }} secondi. Inoltre, i due attacchi successivi di Udyr ottengono {{ lifesteal*100 }}% rubavita e ripristinano <healing>{{ lifeonhit }} salute</healing>.<br /><br /><keywordMajor>Risveglio:</keywordMajor> ottiene <shield>uno scudo da {{ recastshield }}</shield>, ripristina <healing>{{ recastheal }} salute</healing> nell'arco di {{ shieldduration }} secondi e i due attacchi successivi di Udyr hanno {{ lifesteal*200 }}% rubavita e ripristinano <healing>{{ lifeonhitawakened }} salute</healing>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "% scudo salute", "<PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ shieldpercenthealth*100.000000 }}% -> {{ shieldpercenthealthnl*100.000000 }}%", "{{ lifesteal*100.000000 }}% -> {{ lifestealnl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "UdyrW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UdyrE", "name": "Carica ardente", "description": "Udyr ottiene velocità di movimento e il suo primo attacco contro ogni bersaglio lo stordisce. Rilancio: ottiene ancora più velocità di movimento e immunità agli effetti immobilizzanti per un paio di secondi. ", "tooltip": "<spellActive>Posizione della Carica:</spellActive> <PERSON>dy<PERSON> ottiene <speed>{{ movespeed*100 }}% velocità di movimento</speed>, che decresce nell'arco di {{ movespeedduration }} secondi. <PERSON><PERSON><PERSON>, gli attacchi di Udyr lo fanno scattare verso il bersaglio, che viene <status>stordito</status> per {{ stunduration }} secondi ({{ icd }} secondo/i di ricarica per bersaglio).<br /><br /><keywordMajor>Risveglio:</keywordMajor> conferisce immunità agli effetti di <status>immobilizzazione</status> e <status>impedimento</status> e +<speed>{{ movespeedbonus }} velocità di movimento</speed> per {{ unstoppableduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Ricarica per unità"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ icd }} -> {{ icdNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "UdyrR", "name": "<PERSON><PERSON>a alata", "description": "<PERSON><PERSON>r si circonda di una tempesta glaciale che infligge danni e rallenta i nemici vicini. Rilancio: potenzia e scatena la tempesta, che insegue i nemici e infligge danni aggiuntivi.", "tooltip": "<spellActive>Posizione della Tempesta:</spellActive> Udyr si circonda di una tempesta glaciale per {{ buffduration }} secondi, infliggendo <magicDamage>{{ stormdamage }} danni magici</magicDamage> al secondo ai nemici vicini e <status>rallentandoli</status> di un {{ slowpotency*100 }}%. Inoltre, i due attacchi successivi di Udyr in questa posizione infliggono <magicDamage>{{ pulsedamage }} danni magici</magicDamage> ai nemici nella tempesta.<br /><br /><keywordMajor>Risveglio:</keywordMajor> scatena la tempesta, che insegue l'ultimo nemico attaccato da Udyr infliggendogli <magicDamage>danni magici pari a {{ percenthpblast }} della salute massima</magicDamage> del bersaglio aggiuntivi per tutta la durata e <status>rallentandolo</status> di {{ empoweredslow }} in più.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo", "Rallentamento"], "effect": ["{{ stormbasedamage }} -> {{ stormbasedamageNL }}", "{{ slowpotency*100.000000 }}% -> {{ slowpotencynl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [370, 370, 370, 370, 370, 370], "rangeBurn": "370", "image": {"full": "UdyrR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ponte tra i mondi", "description": "Udyr ha quattro abilità di base per cambiare posizione che, se rilanciate, si rinnovano con benefici alla Suprema. In<PERSON><PERSON>, dopo aver usato un'abilità, i due attacchi successivi di Udyr ottengono velocità d'attacco aggiuntiva.", "image": {"full": "Udyr_P.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}