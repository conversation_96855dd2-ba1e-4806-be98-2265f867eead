{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viego": {"id": "Viego", "key": "234", "name": "ヴィエゴ", "title": "滅びの王", "image": {"full": "Viego.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "234000", "num": 0, "name": "default", "chromas": false}, {"id": "234001", "num": 1, "name": "月の聖獣ヴィエゴ", "chromas": true}, {"id": "234010", "num": 10, "name": "Pentakill ディソナンス ヴィエゴ", "chromas": false}, {"id": "234019", "num": 19, "name": "EDG ヴィエゴ", "chromas": false}, {"id": "234021", "num": 21, "name": "国王ヴィエゴ", "chromas": false}, {"id": "234030", "num": 30, "name": "ソウルファイター ヴィエゴ", "chromas": false}, {"id": "234037", "num": 37, "name": "Worlds 2024 ヴィエゴ", "chromas": false}], "lore": "遠い昔に滅びた王国の支配者であったヴィエゴは、千年以上前に亡き妻を甦らせようと試みたことで「破滅」と呼ばれる魔力の大災厄を引き起こし、自らも命を落とした。遥か昔に喪った王妃への執着と愛に苛まれ、強力な不死の霊と化したヴィエゴは「滅びの王」として君臨し、王妃を甦らせる術を求め、「暗黒の刻」を操ってこの世界を調べ回っている。その行く手を阻むものは、空虚で冷酷なる胸からとめどなく流れ出でる「黒き霧」によってことごとく滅ぼされるだろう。", "blurb": "遠い昔に滅びた王国の支配者であったヴィエゴは、千年以上前に亡き妻を甦らせようと試みたことで「破滅」と呼ばれる魔力の大災厄を引き起こし、自らも命を落とした。遥か昔に喪った王妃への執着と愛に苛まれ、強力な不死の霊と化したヴィエゴは「滅びの王」として君臨し、王妃を甦らせる術を求め、「暗黒の刻」を操ってこの世界を調べ回っている。その行く手を阻むものは、空虚で冷酷なる胸からとめどなく流れ出でる「黒き霧」によってことごとく滅ぼされるだろう。", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "なし", "info": {"attack": 6, "defense": 4, "magic": 2, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 10000, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 200, "hpregen": 7, "hpregenperlevel": 0.7, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "ViegoQ", "name": "滅びの王剣", "description": "自動効果により、霊体の刃が<OnHit>通常攻撃時効果</OnHit>で現在体力に応じた追加ダメージを与える。直前にスキルで攻撃した敵には2回攻撃を行い、体力を奪う。<br><br>このスキルを発動すると、剣で前方に突きを放ち、自身の前にいる敵を貫く。", "tooltip": "<spellPassive>自動効果:</spellPassive> 通常攻撃が<physicalDamage>現在体力の{{ totalpercenthealthonhit }}の物理ダメージ</physicalDamage>を追加で与える。直前にスキルでダメージを与えた敵に対する最初の通常攻撃が2回攻撃になる。この2撃目は<physicalDamage>{{ secondattackdamage }}の物理ダメージ</physicalDamage>を与え、<healing>与えたダメージの{{ healmodvschamps*100 }}%にあたる体力</healing>を回復する。これらの追加効果は<keywordMajor>「憑依」</keywordMajor>している間も継続する。<br /><br /><spellActive>発動効果: </spellActive>前方に突きを放ち、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "現在体力%", "最小現在体力ダメージ", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ percenthealthonhit }}% -> {{ percenthealthonhitNL }}%", "{{ mindamageon<PERSON> }} -> {{ mindamageonhitNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5, 4.5, 4, 3.5, 3], "cooldownBurn": "5/4.5/4/3.5/3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViegoQ.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "ViegoW", "name": "亡霊の嘆き", "description": "チャージしてから前方にダッシュし、凝縮した「黒き霧」を発射して最初に当たった敵をスタンさせる。", "tooltip": "<charge>チャージ開始:</charge> 「霧」の凝縮を開始して、自身が{{ selfslowpercent*100 }}%の<status>スロウ効果</status>を受ける。<br /><br /><release>解放:</release> 前方にダッシュして凝縮した「霧」を放つ。「霧」は最初に当たった敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、チャージ時間に応じて{{ stunduration }} - {{ maxstuntt }}秒間<status>スタン</status>させる。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViegoW.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "ViegoE", "name": "彷徨える苦悶", "description": "「黒き霧」を出現させて、地形の周囲を覆う。自身は「霧」の中では亡霊となって隠れることができ、カモフラージュ状態となり、移動速度と攻撃速度が増加する。", "tooltip": "前方に亡霊を送り出し、最初に当たった地形に憑りつかせ、その周囲を{{ mistduration }}秒間、「霧」で覆う。自身は「霧」の中にいると<keywordStealth>カモフラージュ</keywordStealth>状態になり、<speed>移動速度が{{ totalmovespeed }}</speed>、<attackSpeed>攻撃速度が{{ attackspeed*100 }}%</attackSpeed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "攻撃速度", "クールダウン"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViegoE.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "ViegoR", "name": "ハートブレイカー", "description": "近くの地点に瞬間移動し、到着時に敵チャンピオンを斬りつけ、対象の心臓を貫いて衝撃を発生させ、周囲の敵をノックバックさせる。", "tooltip": "現在<keywordMajor>憑依</keywordMajor>している魂を捨てて瞬間移動する。到着後、最も残り体力の割合が低いチャンピオンに通常攻撃を行い、少しの間だけ{{ slowpercent*100 }}%の<status>スロウ効果</status>を与えて、<physicalDamage>{{ totaldamage }} + 減少体力の{{ totalpercenthealth }}%の物理ダメージ</physicalDamage>を与える。周囲の他の敵は<status>ノックバック</status>されて、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["減少体力ダメージ", "クールダウン"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "ViegoR.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "王の支配", "description": "ヴィエゴの周囲で倒された敵は亡霊になる。ヴィエゴは亡霊に通常攻撃を行うことで、その倒された敵の死体を一時的に操れるようになり、対象の最大体力の一定割合にあたる体力を回復し、対象の通常スキルとアイテムを使用できる。対象のアルティメットスキルは自身のものと入れ替わり、自由に1回発動できるようになる。", "image": {"full": "Viego_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}