{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ekko": {"id": "<PERSON><PERSON><PERSON>", "key": "245", "name": "<PERSON><PERSON><PERSON>", "title": "der Zeitbrecher", "image": {"full": "Ekko.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "245000", "num": 0, "name": "default", "chromas": false}, {"id": "245001", "num": 1, "name": "Sandsturm-Ekko", "chromas": true}, {"id": "245002", "num": 2, "name": "Akademie-Ekko", "chromas": false}, {"id": "245003", "num": 3, "name": "PROJEKT: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "245011", "num": 11, "name": "SKT T1-Ekko", "chromas": false}, {"id": "245012", "num": 12, "name": "<PERSON><PERSON><PERSON> de los Santos", "chromas": true}, {"id": "245019", "num": 19, "name": "True Damage Ekko", "chromas": true}, {"id": "245028", "num": 28, "name": "Pulsfeuer-Ekko", "chromas": true}, {"id": "245036", "num": 36, "name": "Firelight-<PERSON><PERSON><PERSON> (Arcane)", "chromas": true}, {"id": "245045", "num": 45, "name": "Sternenwächter Ekko", "chromas": true}, {"id": "245046", "num": 46, "name": "Sternenwächter Ekko (Prestige)", "chromas": false}, {"id": "245056", "num": 56, "name": "Breakout True Damage Ekko", "chromas": false}, {"id": "245057", "num": 57, "name": "Letztes Gefecht-Ekko (Arcane)", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, ein <PERSON> von den rauen Straßen Zhauns, kann die Zeit manipulieren, um jede Situation zu seinem Vorteil zu wenden. Mittels seiner Erfindung, dem Z-Antrieb, erkundet er die sich immer weiter verzweigenden Pfade der Wirklichkeit, um den perfekten Augenblick jedes Mal beim ersten Mal zu kreieren. Obwohl er diese Freiheit sehr genießt, lassen <PERSON> und die Firelights nichts unversucht, um ihren Freunden zu helfen, sollte ihnen Gefahr drohen.", "blurb": "<PERSON><PERSON><PERSON>, ein <PERSON> von den rauen Straßen Zhauns, kann die Zeit manipulieren, um jede Situation zu seinem Vorteil zu wenden. Mi<PERSON><PERSON> seiner Erfindung, dem Z-Antrieb, erkundet er die sich immer weiter verzweigenden Pfade der Wirklichkeit, um den...", "allytips": ["„Zeitsprung“ ist sehr nützlich für die Flucht, kann aber auch mächtig sein, wenn es offensiv eingesetzt wird. Unterschätze nicht sein Schadenspotenzial.", "Falls du einen gegnerischen Champion mit „Z-Antrieb-Resonanz“ belegen kannst, ist es das Risiko meist wert. Das zusätzliche Lauftempo erleichtert die Flucht.", "Der Sprint von „Phasensprung“ eignet sich groß<PERSON>, um Ekkos andere Fähigkeiten vorzubereiten. Du bekommst damit doppelte Treffer mit „Zeitspuler“ oder kommst in die richtige Position, um „Parallelkonvergenz“ detonieren zu lassen."], "enemytips": ["<PERSON><PERSON><PERSON> ist um einiges schwächer, wenn er seine ultimative Fähigkeit nicht zur Verfügung hat. Achte auf die Spur, die er hinterlässt, um festzustellen, ob „Zeitsprung“ bereit ist.", "Ekkos Betäubungszone muss sich erst 3 Sekunden aufladen. Halte nach dem Abbild Ausschau, das er beim Ausführen erzeugt, und versuche zu erraten, wo die Zone platziert wurde.", "<PERSON><PERSON> z<PERSON><PERSON> Treffer von „Zeitspuler“ wird mehr Schaden verursacht als beim <PERSON>, also versuche, diesen nicht abzubekommen."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 280, "mpperlevel": 70, "movespeed": 340, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.688}, "spells": [{"id": "EkkoQ", "name": "Zeitspuler", "description": "<PERSON><PERSON><PERSON> wirft eine Zeitgranate, die sich zu einem Zeitverzerrungsfeld ausweitet, wenn sie auf einen gegnerischen Champion trifft, verlang<PERSON><PERSON>t und jeden, der im Feld gefangen ist, <PERSON><PERSON><PERSON> zufügt. Nach einer Verzögerung kehrt die Granate zu Ekko zurück, wobei sie auf dem Rückweg erneut Schaden verursacht.", "tooltip": "Ekko wirft ein Gerät, das <magicDamage>{{ initialdamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Wenn es einen Champion trifft oder das Ende seiner Reichwei<PERSON> erreicht, explodiert es in ein Feld, das Gegner in seinem Inneren um {{ e2 }}&nbsp;% <status>verlangsamt</status>. Nachdem es sich ausgebreitet hat, ruft Ekko es zurück und verursacht <magicDamage>{{ recalldamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON> (Hinweg)", "Verlangsamung", "<PERSON><PERSON><PERSON> (Rückweg)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ outgoingdamage }} -> {{ outgoingdamageNL }}", "{{ effect2amount*-100.000000 }}&nbsp;% -> {{ effect2amountnl*-100.000000 }}&nbsp;%", "{{ returndamage }} -> {{ returndamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 85, 100, 115, 130], [40, 45, 50, 55, 60], [40, 65, 90, 115, 140], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [165, 165, 165, 165, 165], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/85/100/115/130", "40/45/50/55/60", "40/65/90/115/140", "100", "0", "165", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EkkoQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoW", "name": "Parallelkonvergenz", "description": "Ekkos normale Angriffe fügen Gegnern mit wenig Leben zusätzlichen magischen Schaden zu. <PERSON><PERSON><PERSON> kann „Parallelkonvergenz“ aktivieren, um die Zeitlinie aufzubrechen und nach ein paar Sekunden eine Anomalie zu erschaffen, die Gegner, die in ihr gefangen sind, verlangsamt. Betritt Ek<PERSON> die Anomalie, erhält er einen Schild und betäubt Gegner, indem er sie in der Zeit feststecken lässt.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON>kkos Angriffe gegen Gegner mit weniger als 30&nbsp;% Leben verursachen <magicDamage>magischen <PERSON></magicDamage> in <PERSON>ö<PERSON> von {{ missinghealthpercent }}&nbsp;des fehlenden Lebens.<br /><br /><spellActive>Aktiv:</spellActive> Ekko feuert nach einer Verzögerung eine Chronosphäre ab, die 1,5 Sekunden anhält und Gegner in ihrem Inneren um {{ e0 }}&nbsp;% <status>verlangsamt</status>. Wenn Ekko die Sphäre betritt, explodiert sie, <status>betäubt</status> Gegner {{ e2 }}&nbsp;Sekunden lang und gewährt ihm einen <shield>Schild</shield> in Hö<PERSON> von {{ totalshield }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [375, 375, 375, 375, 375], [2.25, 2.25, 2.25, 2.25, 2.25], [3, 3, 3, 3, 3], [100, 120, 140, 160, 180], [150, 150, 150, 150, 150], [15, 15, 15, 15, 15], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [40, 40, 40, 40, 40]], "effectBurn": [null, "375", "2.25", "3", "100/120/140/160/180", "150", "15", "1.5", "3", "2", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1600, 1600, 1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "EkkoW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoE", "name": "Phasensprung", "description": "<PERSON><PERSON><PERSON> führt eine Ausweichrolle aus, während er seinen Z-Antrieb auflädt. Sein nächster Angriff verursacht zusätzlichen Schaden und verzerrt die Realität, wodurch er zu seinem Ziel teleportiert wird.", "tooltip": "<PERSON><PERSON><PERSON> springt und verstärkt seinen nächsten Angriff. Dieser hat zusätzliche Reichweite, teleportiert ihn zu seinem Ziel und verursacht zusätzlich <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [350, 350, 350, 350, 350], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "350", "3", "300", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "EkkoE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EkkoR", "name": "Zeitsprung", "description": "<PERSON><PERSON><PERSON>cht seine Zeitlinie, wodurch er nicht ins Ziel genommen werden kann und sich an einen günstigeren Augenblick in der Zeit zurückversetzt. Er kehrt an die Stelle zurück, wo er vor einigen Sekunden gewesen ist, und heilt sich in Höhe eines Prozentsatzes des während dieser Dauer erlittenen Schadens. <PERSON><PERSON><PERSON>, die sich in der Nähe seines Ankunftsortes befinden, erleiden massiven Schaden.", "tooltip": "<PERSON>kko dreht die Zeit zurück, betritt <PERSON>, teleportiert sich dahin, wo er vor 4 Sekunden war und verursacht an nahen Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>haden</magicDamage>. <PERSON><PERSON><PERSON> hinaus stellt Ekko <healing>{{ totalbaseheal }}&nbsp;<PERSON><PERSON></healing> wieder her. Pro 1&nbsp;% <PERSON><PERSON>, das er in den letzten 4 Sekunden verloren hat, wird der Wert um {{ percenthealampperpercentmissinghealth }}&nbsp;% erhöht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ flatheal }} -> {{ flathealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 80, 50], "cooldownBurn": "110/80/50", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850], "rangeBurn": "850", "image": {"full": "EkkoR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Z-Antrieb-Re<PERSON>anz", "description": "Jeder dritte Angriff oder jede dritte schadensverursachende Fähigkeit auf dasselbe Ziel verursacht zusätzlichen magischen Schaden und gewährt Ek<PERSON> einen Temposchub, wenn das Ziel ein Champion ist.<br><br>", "image": {"full": "Ekko_P.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}