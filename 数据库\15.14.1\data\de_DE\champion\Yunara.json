{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yunara": {"id": "<PERSON><PERSON>", "key": "804", "name": "<PERSON><PERSON>", "title": "der unerschütterliche Glaube", "image": {"full": "Yunara.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "804000", "num": 0, "name": "default", "chromas": false}, {"id": "804001", "num": 1, "name": "Seelenblumenthermen-Yunara", "chromas": true}], "lore": "<PERSON><PERSON> ist une<PERSON><PERSON><PERSON><PERSON> in ihrer Hingabe an Ionia und hat Jahrhunderte in Klausur im Geisterreich verbracht, um ihre Fähigkeiten mit den Aion E<PERSON>na, einem legendären Relikt der Kinkou, zu verfeinern. <PERSON><PERSON><PERSON> allem, was sie g<PERSON><PERSON><PERSON> hat, hält <PERSON> an ihrem <PERSON>, das Land von Disharmonie und Zwietracht zu befreien, und an ihrem Glauben fest. Doch die Welt, die sie nun erwartet – und der Schatten einer uralten Bedrohung, die sich wieder erhoben hat – wird ihre Entschlossenheit auf die Probe stellen.", "blurb": "Yun<PERSON> ist une<PERSON><PERSON><PERSON><PERSON> in ihrer Hingabe an Ionia und hat Jahrhunderte in Klausur im Geisterreich verbracht, um ihre Fähigkeiten mit den Aion E<PERSON>'na, einem legendären Relikt der Kinkou, zu verfeinern. <PERSON><PERSON><PERSON> allem, was sie geo<PERSON><PERSON> hat, hält <PERSON> an...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 275, "mpperlevel": 45, "movespeed": 325, "armor": 25, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.65}, "spells": [{"id": "YunaraQ", "name": "Kultivierung des Geistes", "description": "Yunara erhält Angriffstempo, zusätzlichen Schaden bei Treffern und ihre Angriffe springen auf Gegner in der Nähe über.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON><PERSON> verursacht <OnHit>%i:OnHit% bei Treffern</OnHit> <magicDamage>{{ calc_passive_damage }}&nbsp;magischen Schaden</magicDamage> und erhält durch Angriffe <evolve>{{ resource_nonchampion }}-mal <PERSON></evolve> (<evolve>{{ resource_champion }}-mal <PERSON></evolve> bei Angriffen gegen Champions).<br /><br /><spellPassive>Aktiv:</spellPassive> Yunara verbraucht <evolve>{{ resource_max }}-mal Entfesselt</evolve>. Dadurch erhält sie {{ buff_duration }} Sekunden lang <attackSpeed>{{ calc_attack_speed }} Angriffstempo</attackSpeed> und verursacht zusätzlich <magicDamage>{{ calc_damage }} magischen Schaden</magicDamage> <OnHit>%i:OnHit% bei Treffern</OnHit>. Während dieser Zeit springen ihre Angriffe auf Gegner in der Nähe über und fügen ihnen <physicalDamage>{{ calc_damage_spread }} normalen Schaden</physicalDamage> zu.<br /><br /><keywordMajor>Transzendenter Zustand</keywordMajor>: Diese Fähigkeit wird sofort {{ spell.yunarar:buff_duration }} Sekunden lang aktiviert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Trefferschaden der passiven Fähigkeit", "Angriffstempo", "Trefferschaden der aktiven Fähigkeit"], "effect": ["{{ damage_passive }} -> {{ damage_passiveNL }}", "{{ attack_speed*100.000000 }}&nbsp;% -> {{ attack_speednl*100.000000 }}&nbsp;%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraQ.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ basecost }} Mana"}, {"id": "YunaraW", "name": "Bogen des Urteils | Bogen des Verfalls", "description": "Yunara feuert eine wirbelnde Gebetsperle ab, die Schaden verursacht und Gegner verlangsamt. In ihrem transzendenten Zustand feuert sie stattdessen einen Laserstrahl ab, der Schaden verursacht und Gegner verlangsamt.", "tooltip": "<PERSON><PERSON> entfesselt eine <PERSON>, die <magicDamage>{{ calc_damage_initial }} magischen <PERSON></magicDamage> verursacht und um {{ calc_slow }} <status>verlangsamt, wobei die Verlangsamung im Verlauf von {{ slow_duration }} Sekunden abfällt</status>. Sie verursacht zusätzlich <magicDamage>{{ calc_damage_per_second }} magischen Schaden</magicDamage> pro Sekunde.<br /><br /><keywordMajor>Transzendenter Zustand – Bogen des Verfalls</keywordMajor>: <PERSON><PERSON> feuert einen Laserstrahl ab, der <magicDamage>{{ spell.yunarar:calc_rw_damage }} magischen <PERSON>had<PERSON></magicDamage> verursacht und um <status>{{ spell.yunarar:calc_rw_slow_amount }} verlangsamt, wobei die Verlangsamung im Verlauf von {{ spell.yunarar:rw_slow_duration }} Sekunden abfällt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden pro Sekunde"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ damage_per_second }} -> {{ damage_per_secondNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "YunaraW.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} <PERSON>a"}, {"id": "YunaraE", "name": "Schritte der Kanmei | Unberührbare Schatten", "description": "Yunara erhält abfallendes Lauftempo und „Geist“. In ihrem transzendeten Zustand springt sie stattdessen in eine Richtung.", "tooltip": "<PERSON>ara erhält <speed>{{ calc_move_speed }} Lauftempo</speed>, welches sich auf <speed>{{ calc_move_speed_enhanced }} Lauftempo</speed> erhöht, wenn sie sich auf einen gegnerischen Champion zubewegt. Das Lauftempo fällt im Verlauf von {{ buff_duration }} Sekunden ab.<br /><br /><keywordMajor>Transzendenter Zustand – Unberührbare Schatten</keywordMajor>: <PERSON><PERSON> springt in eine Richtung.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "<PERSON><PERSON>"], "effect": ["{{ move_speed*100.000000 }}&nbsp;% -> {{ move_speednl*100.000000 }}&nbsp;%", "{{ buff_duration }} -> {{ buff_durationNL }}"]}, "maxrank": 5, "cooldown": [7.5, 7.5, 7.5, 7.5, 7.5], "cooldownBurn": "7.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " <PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraE.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} <PERSON>a"}, {"id": "YunaraR", "name": "Über das Selbst hinaus", "description": "Yun<PERSON> verfällt in einen transzendeten Zustand, in dem ihre Grundfähigkeiten aufgewertet werden.", "tooltip": "<PERSON><PERSON> verfällt {{ buff_duration }} <PERSON><PERSON><PERSON> lang in einen <keywordMajor>transzendeten Zustand</keywordMajor>, in dem ihre Grundfähigkeiten aufgewertet werden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Bogen des Verfalls | Schaden", "Unberührbare Schatten | Sprungtempo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rw_damage_base }} -> {{ rw_damage_baseNL }}", "{{ re_dash_speed }} -> {{ re_dash_speedNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraR.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Gelöbnis an die Ersten Lande", "description": "Yunaras kritische Treffer verursachen zusätzlichen magischen Schaden.", "image": {"full": "Yunara_Passive.Yunara.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}