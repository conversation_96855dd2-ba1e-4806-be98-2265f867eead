{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Barrier", "description": "Mendapatkan Shield sesaat.", "tooltip": "Mendapatkan <shield>{{ shieldstrength }} Shield damage</shield> selama {{ shieldduration }} detik.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerBoost": {"id": "SummonerBoost", "name": "Cleanse", "description": "Menghilangkan semua efek crowd control (kecuali suppression dan airborne) dan debuff summoner spell yang mengenai champion-mu, serta memberi efek Tenacity.", "tooltip": "Menghilangkan semua debuff crowd control (kecuali <keyword>Suppression</keyword> dan <keyword>Airborne</keyword>) dan debuff summoner spell darimu, serta memberikan {{ tenacityvalue*100 }}% Tenacity selama {{ tenacityduration }} detik.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "Flash", "description": "Memindahkan champion-mu da<PERSON> jarak dekat ke arah kursormu.", "tooltip": "Memindahkan champion-mu da<PERSON> jarak dekat ke arah kursormu.<br /><br />Tidak bisa di-cast lagi selama satu ronde penuh <rules>(satu ronde termasuk fase beli dan fase combat).</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "Flee", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> burst Move Speed singkat yang meningkat ketika kabur dari champion musuh.", "tooltip": "<keywordMajor>Active Spell Slot:</keywordMajor> Augment yang memberikan Summoner Spell akan menggantikan slot ini.<br /><br />Mendapatkan <moveSpeed>{{ basems*100 }}% Move Speed</moveSpeed> selama {{ duration }} detik, meningkat sebanyak {{ bonusmsperenemybehind*100 }}% untuk setiap musuh yang ada di belakangmu.", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerDot": {"id": "SummonerDot", "name": "Ignite", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> true damage berkelanjutan kepada champion musuh target dan men<PERSON><PERSON> efek heal pada target selama durasi.", "tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> <trueDamage>{{ tooltiptruedamagecalculation }} true damage</trueDamage> kepada champion musuh target selama 5 detik dan menerapkan <keyword>{{ grievousamount*100 }}% Grievous Wounds</keyword> selama durasi tersebut.<br /><br /><keyword>Wounds</keyword>: Mengurangi efektifitas dari efek Heal dan Reg<PERSON>asi.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "Exhaust", "description": "<PERSON><PERSON> <PERSON><PERSON>k slow kepada champion musuh target dan men<PERSON><PERSON><PERSON> damage yang <PERSON>.", "tooltip": "<PERSON>i efek <keyword>Slow</keyword> kepada champion musuh target sebesar {{ slow }}% dan mengurangi damage yang dihasilkannya sebesar {{ damagereduction }}% selama {{ debuffduration }} detik.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerFlash": {"id": "SummonerFlash", "name": "Flash", "description": "<PERSON><PERSON> berte<PERSON><PERSON>asi ke arah kursor dalam jarak dekat.", "tooltip": "<PERSON><PERSON> berte<PERSON><PERSON>asi ke arah kursor dalam jarak dekat.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerHaste": {"id": "SummonerHaste", "name": "Ghost", "description": "Mendapatkan Move Speed dan dapat melewati unit lain selama durasi itu.", "tooltip": "Mendapatkan <speed>{{ movespeedmod }} Move Speed</speed> dan menjadi <keyword>Ghost</keyword> selama {{ duration }} detik.<br /><br /><keyword>Ghost</keyword>: Mengabaikan tabrakan dengan unit lainnya.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerHeal": {"id": "SummonerHeal", "name": "Heal", "description": "Memulihkan Health dan memberikan Move Speed untukmu dan champion sekutu target.", "tooltip": "Memulihkan <healing>{{ totalheal }} Health</healing> dan member<PERSON>n <speed>{{ movespeed*100 }}% Move Speed</speed> selama {{ movespeedduration }} detik ke kamu dan champion sekutu yang ditarget.<br /><br /><rules><PERSON>ka digunakan tanpa target, akan otomatis cast ke champion sekutu terdekat yang paling kritis.<br />Heal berkurang setengah untuk unit yang baru saja terkena Summoner Heal.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "Clarity", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>-mu dan champion se<PERSON><PERSON>.", "tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ e1 }}% <PERSON><PERSON> maksimum ke champion-mu dan {{ e2 }}% ke sekutu di sekitar.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "To the King!", "description": "<PERSON><PERSON><PERSON><PERSON> cepat ke sisi <PERSON>.", "tooltip": "<span class=\"colorFFE076\">Pasif:</span> Menyerang satu champion musuh dengan Poro memberi timmu <PERSON>. Saat mencapai 10 <PERSON><PERSON>, timmu memanggil Poro King untuk bertempur bersama mereka. Saat Poro King aktif, tak ada <PERSON>ro Mark yang bisa didapatkan oleh kedua tim.<br /><br /><span class=\"colorFFE076\">Aktif:</span> Dengan cepat dash ke sisi King Poro. Hanya bisa di-cast saat Poro King dipanggil ke timmu. <br /><br /><i><span class=\"colorFDD017\">''Poro menyentuh hati. <PERSON>an sisanya hanya ikut saja.''</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "<PERSON><PERSON>", "description": "Melempar <PERSON> ke arah musuh. <PERSON><PERSON> men<PERSON> musuh, kamu bisa berpindah cepat ke musuh tersebut.", "tooltip": "Melempar Poro ke jarak jauh, memberikan {{ f2 }} true damage ke unit musuh yang pertama terkena, memberimu <span class=\"coloree91d7\">True Sight</span> dari target.<br /><br />Ability ini bisa di-cast ulang selama 3 detik jika mengenai musuh untuk dash untuk mengenai target, memberikan {{ f2 }} lebih banyak true damage dan mengurangi cooldown Poro Toss selanjutnya sebanyak {{ e4 }} detik.<br /><br />Poro tidak bisa dihadang oleh Spell Shield atau Wind Wall karena Poro itu hewan, bukan spell!<br /><br /><i><span class=\"colorFDD017\">''Poro adalah model untuk aerodinamika Runeterra.''</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerSmite": {"id": "SummonerSmite", "name": "Smite", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> true damage ke monster atau minion.", "tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> <trueDamage>{{ smitebasedamage }} true damage</trueDamage> ke monster besar atau minion lane yang ditarget.<br /><br /><PERSON><PERSON><PERSON><PERSON><PERSON> <trueDamage>{{ firstpvpdamage }} true damage</trueDamage> ke pet milik champion.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "Tanpa Biaya", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "<PERSON>", "description": "<PERSON><PERSON><PERSON> bola salju lurus ke arah musuh. <PERSON><PERSON><PERSON> yang terkena akan di<PERSON>, <PERSON><PERSON><PERSON>, dan champion-mu bisa berpindah cepat ke target bertanda tersebut.", "tooltip": "Melemparkan bola salju jarak jauh, men<PERSON><PERSON><PERSON><PERSON> {{ tooltipdamagetotal }} true damage ke musuh yang kena pertama kali dan memberikan <span class=\"coloree91d7\">True Sight</span> pada target. <PERSON><PERSON> mengenai musuh, ability ini bisa di-recast selama {{ e3 }} detik untuk Dash ke unit yang ditandai, men<PERSON><PERSON><PERSON><PERSON> tambahan {{ tooltipdamagetotal }} true damage. Dash ke target akan mengurangi cooldown Mark sebesar {{ e4 }}%.<br /><br /><span class=\"colorFFFF00\">Proyektil Mark tidak dihentikan oleh spell shield atau penghalau proyektil.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "<PERSON>", "description": "<PERSON><PERSON><PERSON> bola salju lurus ke arah musuh. <PERSON><PERSON><PERSON> yang terkena akan di<PERSON>, <PERSON><PERSON><PERSON>, dan champion-mu bisa berpindah cepat ke target bertanda tersebut.", "tooltip": "Melemparkan bola salju jarak jauh, men<PERSON><PERSON><PERSON><PERSON> {{ tooltipdamagetotal }} true damage ke musuh yang kena pertama kali dan memberikan <span class=\"coloree91d7\">True Sight</span> pada target. <PERSON><PERSON> mengenai musuh, ability ini bisa di-recast selama {{ e3 }} detik untuk Dash ke unit yang ditandai, men<PERSON><PERSON><PERSON><PERSON> tambahan {{ tooltipdamagetotal }} true damage. Dash ke target akan mengurangi cooldown Mark sebesar {{ e4 }}%.<br /><br /><span class=\"colorFFFF00\">Proyektil Mark tidak dihentikan oleh spell shield atau penghalau proyektil.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "Teleport", "description": "Setelah channeling singkat, menjadi tidak bisa ditarget dan pergi ke satu unit sekutu. Upgrade menjadi Unleashed Teleport yang secara signifikan meningkatkan kecepatan tempuh. ", "tooltip": "Sesudah channeling selama {{ channelduration }} detik, menjadi <keyword>Tak Bisa Ditarget</keyword> dan bergerak ke target bangunan, minion, atau ward sekutu. <br /><br />Upgrade menjadi Unleashed Teleport setelah {{ upgrademinute }} menit, yang secara signifikan meningkatkan kecepatan tempuh.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "Tanpa Biaya", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "Placeholder", "description": "Slot ini akan digantikan oleh ultima champion lain yang dipilih di awal game. Periode waktu untuk memilih ultima adalah 30 detik. <PERSON><PERSON>, be<PERSON><PERSON><PERSON><PERSON>!", "tooltip": "Akan digantikan oleh Summoner Spell Ultima yang kamu pilih.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "Placeholder dan <PERSON>-<PERSON><PERSON>", "description": "Slot ini akan digantikan oleh ultima champion lain dan kamu akan mendapatkan Attack-Smite. Periode waktu untuk memilih ultima adalah 30 detik. <PERSON><PERSON>, bers<PERSON><PERSON><PERSON>!", "tooltip": "Akan digantikan oleh Summoner Spell Ultima.<br /><br />Mendapatkan Attack-Smite. Attack-Smite akan menge<PERSON>ekusi Monster Buff, <PERSON>, dan <PERSON><PERSON><PERSON> sekutu saat kamu menyerang mereka.<br /><br /><attention>Attack-Smite tidak memiliki cooldown.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}