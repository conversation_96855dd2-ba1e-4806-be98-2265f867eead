{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Barrier", "description": "ได้รับโล่ป้องกันในช่วงเวลาสั้น ๆ", "tooltip": "ได้รับ<shield>โล่ป้องกันความเสียหาย {{ shieldstrength }} หน่วย</shield> เป็นเวลา {{ shieldduration }} วินาที", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerBoost": {"id": "SummonerBoost", "name": "Cleanse", "description": "ลบสถานะผิดปกติทั้งหมด (ยกเว้นเอฟเฟกต์หยุดยั้งและลอยขึ้น) รวมถึงสถานะผิดปกติจากเวทซัมมอนเนอร์ที่ส่งผลต่อแชมเปี้ยนของคุณออก และมอบ Tenacity", "tooltip": "ลบล้างดีบัฟ CC ทั้งหมด (ไม่รวม<keyword>หยุดยั้ง</keyword> และ<keyword>ลอยขึ้น</keyword>) รวมถึงดีบัฟจากเวทซัมมอนเนอร์จากคุณ และมอบ Tenacity {{ tenacityvalue*100 }}% เป็นเวลา {{ tenacityduration }} วินาที", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "Flash", "description": "เทเลพอร์ตระยะสั้น ๆ ให้แชมเปี้ยนของคุณไปยังตำแหน่งที่เมาส์ของคุณชี้อยู่", "tooltip": "เทเลพอร์ตระยะสั้น ๆ ให้แชมเปี้ยนของคุณไปยังตำแหน่งที่เมาส์ของคุณชี้อยู่<br /><br />ไม่สามารถร่ายอีกครั้งได้อีกหนึ่งรอบเต็ม <rules>(หนึ่งรอบประกอบไปด้วยทั้งช่วงการซื้อและช่วงต่อสู้)</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "Flee", "description": "ได้รับความเร็วเคลื่อนที่ในระยะเวลาสั้น ๆ โดยจะเพิ่มสูงขึ้นเมื่อหนีออกห่างแชมเปี้ยนศัตรู", "tooltip": "<keywordMajor>ช่องเวทกดใช้:</keywordMajor> Augment ที่มอบเวทซัมมอนเนอร์จะมาแทนที่ช่องตรงนี้<br /><br />ได้รับ<moveSpeed>ความเร็วเคลื่อนที่ {{ basems*100 }}%</moveSpeed> เป็นเวลา {{ duration }} วินาที เพิ่มขึ้น {{ bonusmsperenemybehind*100 }}% ต่อศัตรูแต่ละตัวที่อยู่ด้านหลังคุณ", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerDot": {"id": "SummonerDot", "name": "Ignite", "description": "สร้างความเสียหายจริงอย่างต่อเนื่องกับเป้าหมายแชมเปี้ยนศัตรู และลดผลการฮีลบนตัวพวกเขาลงในช่วงเวลาหนึ่ง", "tooltip": "สร้าง<trueDamage>ความเสียหายจริง {{ tooltiptruedamagecalculation }} หน่วย</trueDamage>กับเป้าหมายแชมเปี้ยนศัตรูในระยะเวลา 5 วินาที และทำให้ติดเอฟเฟกต์ <keyword>Grievous Wound {{ grievousamount*100 }}%</keyword> ตลอดระยะเวลานั้น<br /><br /><keyword>Wounds</keyword>: จะลดผลการฮีล และลดอัตราฟื้นฟูพลังชีวิตลง", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "Exhaust", "description": "สโลว์เป้าหมายแชมเปี้ยนศัตรูและทำให้พวกเขาสร้างความเสียหายได้น้อยลง", "tooltip": "<keyword>สโลว์</keyword>เป้าหมายแชมเปี้ยนศัตรู {{ slow }}% และทำให้พวกเขาสร้างความเสียหายได้น้อยลง {{ damagereduction }}% เป็นเวลา {{ debuffduration }} วินาที", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerFlash": {"id": "SummonerFlash", "name": "Flash", "description": "เทเลพอร์ตคุณในระยะสั้น ๆ ไปยังตำแหน่งที่เมาส์ของคุณชี้อยู่", "tooltip": "เทเลพอร์ตคุณในระยะสั้น ๆ ไปยังตำแหน่งที่เมาส์ของคุณชี้อยู่", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerHaste": {"id": "SummonerHaste", "name": "Ghost", "description": "ได้รับความเร็วเคลื่อนที่และสามารถเคลื่อนที่ทะลุผ่านยูนิตอื่นได้ในระยะเวลาหนึ่ง", "tooltip": "ได้รับ<speed>ความเร็วเคลื่อนที่ {{ movespeedmod }}</speed> และสามารถ<keyword>เดินทะลุผ่านยูนิต</keyword>ได้เป็นเวลา {{ duration }} วินาที<br /><br /><keyword>Ghost</keyword>: สามารถวิ่งทะลุผ่านยูนิตอื่นได้", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerHeal": {"id": "SummonerHeal", "name": "Heal", "description": "ฟื้นฟูพลังชีวิตและมอบความเร็วเคลื่อนที่ให้กับคุณและแชมเปี้ยนร่วมทีมที่เลือก", "tooltip": "ฟื้นฟู<healing>พลังชีวิต {{ totalheal }} หน่วย </healing>และมอบ<speed>ความเร็วเคลื่อนที่ {{ movespeed*100 }}%</speed> เป็นเวลา {{ movespeedduration }} วินาที ให้กับแชมเปี้ยนของคุณและแชมเปี้ยนเพื่อนร่วมทีมที่เป็นเป้าหมาย<br /><br /><rules>หากใช้เวทนี้โดยไม่ได้กำหนดเป้าหมาย มันจะเลือกแชมเปี้ยนเพื่อนร่วมทีมที่บาดเจ็บมากที่สุดที่อยู่ในระยะของสกิล<br />ผลการฮีลนี้จะส่งผลเพียงครึ่งเดียวหากใช้กับผู้ที่เพิ่งได้รับผลของเวทซัมมอนเนอร์ Heal มา</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "Clarity", "description": "ฟื้นฟูมานาให้กับคุณและแชมเปี้ยนร่วมทีม", "tooltip": "ฟื้นฟูมานา {{ e1 }}% ตามมานาสูงสุดให้แชมเปี้ยนของคุณ และฟื้นฟูมานา {{ e2 }}% ให้แก่เพื่อนร่วมทีมที่อยู่รอบข้าง", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "To the King!", "description": "เคลื่อนที่อย่างรวดเร็ว เดินทางไปอยู่เคียงข้าง <PERSON><PERSON> King", "tooltip": "<span class=\"colorFFE076\">ติดตัว:</span> การโจมตีแชมเปี้ยนศัตรูด้วย Poro จะช่วยให้ทีมของคุณสะสม Poro Mark เพิ่มขึ้น แล้วเมื่อสะสม Poro Mark ครบ 10 อัน ทีมของคุณจะเรียก Poro King มาช่วยต่อสู้เคียงข้างพวกเขา ในระหว่างที่ Poro King ออกมาต่อสู้นี้ ทั้งสองทีมจะสะสม Poro Mark เพิ่มไม่ได้<br /><br /><span class=\"colorFFE076\">กดใช้:</span> เคลื่อนที่ไปอยู่เคียงข้าง Poro King อย่างรวดเร็ว สามารถใช้ได้เฉพาะเวลาที่ทีมของคุณเรียก Poro King ออกมา<br /><br /><i><span class=\"colorFDD017\">''Poro จะทำให้รู้สึกรักอย่างจับใจ ที่เหลือก็แค่คุณพร้อมที่จะลุยกับมัน''</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "<PERSON><PERSON>", "description": "ขว้าง Poro ไปใส่ศัตรูของคุณ ถ้าหากโยนไปโดน คุณจะสามารถเคลื่อนที่อย่างรวดเร็ว เพื่อติดตามไปหาเป้าหมายที่โดนได้", "tooltip": "ขว้าง Poro ออกไปโจมตีระยะไกล สร้างความเสียหายจริง {{ f2 }} หน่วย ให้แก่ศัตรูตัวแรกที่โดน และได้รับ <span class=\"coloree91d7\">True Sight</span> บนตัวเป้าหมาย<br /><br />หากขว้างถูกเป้าหมาย สามารถกดใช้งานสกิลได้อีกครั้งภายในเวลา 3 วินาทีเพื่อพุ่งไปยังเป้าหมาย สร้างความเสียหายจริง {{ f2 }} หน่วย และลดคูลดาวน์ให้กับสกิล Poro Toss เป็นเวลา {{ e4 }} วินาที<br /><br />Poro ที่โยนออกไปนั้นจะบล็อกด้วย Spell Shield หรือ Wind Wall ไม่ได้ เพราะพวกมันเป็นสัตว์น้อยแสนน่ารัก ไม่ใช่เวท!<br /><br /><i><span class=\"colorFDD017\">''Poro นั้นเป็นแบบจำลองสำหรับหลักอากาศพลศาสตร์แห่ง Runeterran''</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerSmite": {"id": "SummonerSmite", "name": "Smite", "description": "สร้างความเสียหายจริงให้กับมอนสเตอร์หรือกับมินเนี่ยน", "tooltip": "สร้าง<trueDamage>ความเสียหายจริง {{ smitebasedamage }} หน่วย</trueDamage>ให้กับเป้าหมายที่เป็นมอนสเตอร์ขนาดใหญ่หรือมินเนี่ยนในเลน<br /><br />สร้าง<trueDamage>ความเสียหายจริง {{ firstpvpdamage }} หน่วย</trueDamage>แก่ยูนิตลูกน้องของแชมเปี้ยน", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "ไม่เสียอะไรเลย", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "<PERSON>", "description": "ขว้างบอลหิมะเป็นเส้นตรงออกไปใส่ศัตรูของคุณ ถ้าหากโดนศัตรู จะติดเครื่องหมายไว้ที่ตัวศัตรู ทำให้ได้รับ True Sight แล้วหลังจากนั้น แชมเปี้ยนของคุณจะสามารถเคลื่อนที่พุ่งเข้าหาศัตรูได้อย่างรวดเร็ว", "tooltip": "ขว้างบอลหิมะออกไปในระยะไกล สร้างความเสียหายจริง {{ tooltipdamagetotal }} หน่วย ให้กับศัตรูตัวแรกที่โดนและได้รับ <span class=\"coloree91d7\">True Sight</span> บนตัวเป้าหมาย หากขว้างโดนศัตรูได้สำเร็จ สามารถกดใช้ซ้ำได้ภายในเวลา {{ e3 }} วินาทีเพื่อทำการพุ่งเข้าหายูนิตศัตรูที่โดน สร้างความเสียหายจริงเพิ่มเติม {{ tooltipdamagetotal }} หน่วย การพุ่งตัวเข้าหาเป้าหมาย จะลดคูลดาวน์ของเวทบอลหิมะนี้ลง {{ e4 }}%<br /><br /><span class=\"colorFFFF00\">บอลหิมะจะไม่สามารถหยุดได้ด้วยผลของโล่ป้องกันเวทหรือสกิลป้องกันการโจมตีระยะไกลใด ๆ </span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "<PERSON>", "description": "ขว้างบอลหิมะเป็นเส้นตรงออกไปใส่ศัตรูของคุณ ถ้าหากโดนศัตรู จะติดเครื่องหมายไว้ที่ตัวศัตรู ทำให้ได้รับ True Sight แล้วหลังจากนั้น แชมเปี้ยนของคุณจะสามารถเคลื่อนที่พุ่งเข้าหาศัตรูได้อย่างรวดเร็ว", "tooltip": "ขว้างบอลหิมะออกไปในระยะไกล สร้างความเสียหายจริง {{ tooltipdamagetotal }} หน่วย ให้กับศัตรูตัวแรกที่โดนและได้รับ <span class=\"coloree91d7\">True Sight</span> บนตัวเป้าหมาย หากขว้างโดนศัตรูได้สำเร็จ สามารถกดใช้ซ้ำได้ภายในเวลา {{ e3 }} วินาทีเพื่อทำการพุ่งเข้าหายูนิตศัตรูที่โดน สร้างความเสียหายจริงเพิ่มเติม {{ tooltipdamagetotal }} หน่วย การพุ่งตัวเข้าหาเป้าหมาย จะลดคูลดาวน์ของเวทบอลหิมะนี้ลง {{ e4 }}%<br /><br /><span class=\"colorFFFF00\">บอลหิมะจะไม่สามารถหยุดได้ด้วยผลของโล่ป้องกันเวทหรือสกิลป้องกันการโจมตีระยะไกลใด ๆ </span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "Teleport", "description": "หลังร่ายเป็นเวลาชั่วครู่ จะเข้าสู่สถานะไม่ตกเป็นเป้าหมายและเคลื่อนที่ไปหายูนิตพันธมิตร สามารถอัปเกรดเป็น Unleashed Teleport ซึ่งจะเพิ่มความเร็วการเทเลพอร์ตเป็นอย่างมาก", "tooltip": "หลังจากร่ายเป็นเวลา {{ channelduration }} วินาที จะ<keyword>ไม่ตกเป็นเป้าหมาย</keyword>และเทเลพอร์ตไปยังสิ่งปลูกสร้าง มินเนี่ยน หรือวอร์ดร่วมทีมที่เลือก <br /><br />อัปเกรดเป็น Unleashed Teleport ในนาทีที่ {{ upgrademinute }} ซึ่งจะเพิ่มความเร็วการเทเลพอร์ตเป็นอย่างมาก", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "ที่ยึดตำแหน่ง", "description": "ช่องนี้จะถูกแทนที่โดยสกิลอัลติเมทของแชมเปี้ยนตัวอื่นที่ถูกเลือกตอนเริ่มเกม มีเวลา 30 วินาทีในการเลือกสกิลอัลติเมท เตรียมตัวให้พร้อม!", "tooltip": "จะถูกแทนที่ด้วย Ultimate Summoner Spell ที่คุณเลือก{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "ที่ยึดตำแหน่ง และ Attack-Smite", "description": "ช่องนี้จะถูกแทนที่โดยสกิลอัลติเมทของแชมเปี้ยนตัวอื่นและคุณจะได้รับ Attack-Smite มีเวลา 30 วินาทีในการเลือกสกิลอัลติเมท เตรียมตัวให้พร้อม!", "tooltip": "จะถูกแทนที่ด้วย Ultimate Summoner Spell ที่คุณเลือก <br /><br />ได้รับ Attack-Smite Attack-Smite จะสังหารมอนสเตอร์ป่าที่มีบัฟ มอนสเตอร์ในตำนาน และ Scuttle Crab เมื่อคุณโจมตีพวกมัน<br /><br /><attention>Attack-Smite ไม่มีคูลดาวน์</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}