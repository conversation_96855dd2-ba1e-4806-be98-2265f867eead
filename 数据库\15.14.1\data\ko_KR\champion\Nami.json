{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nami": {"id": "<PERSON><PERSON>", "key": "267", "name": "나미", "title": "파도 소환사", "image": {"full": "Nami.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "267000", "num": 0, "name": "default", "chromas": false}, {"id": "267001", "num": 1, "name": "비단인어 나미", "chromas": true}, {"id": "267002", "num": 2, "name": "강의 정령 나미", "chromas": false}, {"id": "267003", "num": 3, "name": "바다나미 우르프", "chromas": false}, {"id": "267007", "num": 7, "name": "깊은 바다 나미", "chromas": false}, {"id": "267008", "num": 8, "name": "SKT T1 나미", "chromas": false}, {"id": "267009", "num": 9, "name": "프로그램 나미", "chromas": true}, {"id": "267015", "num": 15, "name": "불멸의 영웅 나미", "chromas": true}, {"id": "267024", "num": 24, "name": "우주의 운명 나미", "chromas": true}, {"id": "267032", "num": 32, "name": "마녀 나미", "chromas": true}, {"id": "267041", "num": 41, "name": "우주 그루브 나미", "chromas": true}, {"id": "267042", "num": 42, "name": "프레스티지 우주 그루브 나미", "chromas": false}, {"id": "267051", "num": 51, "name": "악의 여단 나미", "chromas": true}, {"id": "267058", "num": 58, "name": "신화 창조자 나미", "chromas": true}], "lore": "나미는 바다에 사는 바스타야 종족으로, 어리지만 완고할 정도로 고집이 세다. 먼 옛날 타곤 인과 맺었던 약속이 깨지자, 마라이 종족으로는 처음으로 파도 치는 바다에서 나와 마른 육지로 모험을 떠났다. 달리 해결책이 없었기에, 자신의 종족을 안전하게 지켜주는 성스러운 의식을 완수한다는 임무를 자청한 것이었다. 새로운 시대는 혼란 그 자체지만, 나미는 용기와 결단력으로 불확실한 미래를 마주한다. 그녀의 무기는 바다의 힘을 소환하는 파도 소환사의 지팡이다.", "blurb": "나미는 바다에 사는 바스타야 종족으로, 어리지만 완고할 정도로 고집이 세다. 먼 옛날 타곤 인과 맺었던 약속이 깨지자, 마라이 종족으로는 처음으로 파도 치는 바다에서 나와 마른 육지로 모험을 떠났다. 달리 해결책이 없었기에, 자신의 종족을 안전하게 지켜주는 성스러운 의식을 완수한다는 임무를 자청한 것이었다. 새로운 시대는 혼란 그 자체지만, 나미는 용기와 결단력으로 불확실한 미래를 마주한다. 그녀의 무기는 바다의 힘을 소환하는 파도 소환사의 지팡이다.", "allytips": ["물의 감옥은 재사용 대기시간이 길기 때문에 적절한 타이밍에 사용해야 합니다.", "적과의 교전 시에 밀물 썰물을 활용하면 전세를 유리하게 이끌 수 있습니다.", "나미의 궁극기는 멀리 있는 적에 대한 전투 개시용으로 활용하기에 좋습니다."], "enemytips": ["물의 감옥은 강력한 스킬이지만 재사용 대기시간도 깁니다. 이 점을 이용해서 물의 감옥 스킬을 사용할 수 없을 때를 노리세요.", "해일은 사거리가 길지만 천천히 발사되므로, 잘 살피면 피할 수 있습니다.", "파도 소환사의 축복을 받은 적과는 싸움을 피하세요. 지속시간이 짧기 때문에 조금만 기다리면 유리해집니다."], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 4, "defense": 3, "magic": 7, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 335, "armor": 29, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.61, "attackspeed": 0.644}, "spells": [{"id": "NamiQ", "name": "물의 감옥", "description": "거대한 물방울을 지정한 위치로 발사해 물방울이 닿은 적에게 피해를 입히고 닿는 순간 기절시킵니다.", "tooltip": "나미가 물방울을 던져 {{ e2 }}초 동안 <status>기절</status>시키고 <magicDamage>{{ totaldamagett }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 130, 185, 240, 295], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/130/185/240/295", "1.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "NamiQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NamiW", "name": "밀물 썰물", "description": "밀려드는 파도를 보내 아군 및 적 챔피언을 번갈아 맞히면서, 아군은 치유하고 적에게는 피해를 입힙니다.", "tooltip": "나미가 밀려드는 파도를 보내 아군 및 적 챔피언을 번갈아 맞힙니다. 파도는 각 챔피언을 한 번만 맞힐 수 있으며 최대 {{ maxtargets }}명의 대상에게 튕깁니다.<li>아군의 <healing>체력을 {{ totalheal }}</healing>만큼 회복시키고 근처 적 챔피언에게 튕깁니다. <li>적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 근처 아군 챔피언에게 튕깁니다.<br />피해량과 회복량은 한 번 튕길 때마다 {{ bouncescaling }}씩 조정됩니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "피해량", "소모값 @AbilityResourceName@"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "NamiW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NamiE", "name": "파도 소환사의 축복", "description": "잠시 동안 아군 챔피언들을 강화해, 기본 공격과 스킬 사용 시 추가 마법 피해를 입히고 대상에게 둔화를 걸 수 있게 만듭니다.", "tooltip": "나미가 {{ buffduration }}초 동안 아군 챔피언의 다음 기본 공격과 스킬 {{ hitcount }}회를 강화합니다. 강화된 기본 공격과 스킬은 대상을 {{ slowduration }}초 동안 {{ totalslow }}만큼 <status>둔화</status>시키고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 추가로 입힙니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslow }}% -> {{ baseslowNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [15, 20, 25, 30, 35], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "15/20/25/30/35", "1", "3", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NamiE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NamiR", "name": "해일", "description": "적들을 공중으로 띄우고 둔화를 걸면서 피해를 입히는 거대한 해일을 소환합니다. 여기에 맞은 아군은 밀려오는 파도의 효과를 두 배로 받습니다.", "tooltip": "나미가 해일을 소환하여 0.5초 동안 <status>공중으로 띄워 올리고</status> {{ e4 }}% <status>둔화</status>시키며 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. <status>둔화</status> 지속시간은 해일이 이동한 거리에 비례하며 최대 {{ e5 }}초입니다.<br /><br />파도에 맞은 아군은 <spellName>밀려오는 파도</spellName>의 효과를 두 배로 받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [2, 2, 2], [70, 70, 70], [4, 4, 4], [0.002, 0.002, 0.002], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.5", "2", "70", "4", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [2550, 2550, 2550], "rangeBurn": "2550", "image": {"full": "NamiR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "밀려오는 파도", "description": "나미의 스킬에 맞은 아군 챔피언은 짧은 시간 동안 이동 속도가 상승합니다.", "image": {"full": "NamiPassive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}