{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TahmKench": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "223", "name": "タム・ケンチ", "title": "川の王様", "image": {"full": "TahmKench.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "223000", "num": 0, "name": "default", "chromas": false}, {"id": "223001", "num": 1, "name": "天才料理人タム・ケンチ", "chromas": false}, {"id": "223002", "num": 2, "name": "アーフ・ケンチ", "chromas": false}, {"id": "223003", "num": 3, "name": "金貨皇帝タム・ケンチ", "chromas": true}, {"id": "223011", "num": 11, "name": "アルカナ タム・ケンチ", "chromas": false}, {"id": "223020", "num": 20, "name": "荒野のタム・ケンチ", "chromas": false}, {"id": "223030", "num": 30, "name": "山海絵巻伝タム・ケンチ", "chromas": false}], "lore": "タム・ケンチは過去に様々な名前で呼ばれてきた悪魔であり、ルーンテラの水路を移ろいながら、尽きることのない食欲を満たすために他者を餌食にしている。彼は非常に魅力的で誇り高き存在の振りをして、自信たっぷりな態度で物質世界を放浪しながら不用心な獲物を探している。彼の長い舌は頑丈な鎧に身を包んだ戦士ですら離れた場所から気絶させることが可能で、音を立てる奈落のような彼の腹の中に納まれば、そこから戻れる可能性はほとんどない。", "blurb": "タム・ケンチは過去に様々な名前で呼ばれてきた悪魔であり、ルーンテラの水路を移ろいながら、尽きることのない食欲を満たすために他者を餌食にしている。彼は非常に魅力的で誇り高き存在の振りをして、自信たっぷりな態度で物質世界を放浪しながら不用心な獲物を探している。彼の長い舌は頑丈な鎧に身を包んだ戦士ですら離れた場所から気絶させることが可能で、音を立てる奈落のような彼の腹の中に納まれば、そこから戻れる可能性はほとんどない。", "allytips": ["彼をプレイする上で一番大事なことは、倒されやすい味方チャンピオンを守ること。「丸呑み」の有効範囲とクールダウンに、常に注意を払おう。", "「ゆるゆる皮膜」を発動するタイミングにも注意が必要。次に来るダメージに備えてシールドを発動するか、自動効果による回復の方がより重要か、戦況を判断しよう。"], "enemytips": ["タム・ケンチが「ゆるゆる皮膜」のシールドを発動したら、それはつまり自動効果の回復が使えなくなったことを意味する。また、灰色の体力を再び発動するには「ゆるゆる皮膜」のクールダウンを待たなければならない。タム・ケンチと対峙する際はこの状況をうまく使おう！", "タム・ケンチの「川潜り」に注意しておこう。この詠唱は移動不能効果で妨害できる。"], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 3, "defense": 9, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 325, "mpperlevel": 50, "movespeed": 335, "armor": 39, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.2, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "TahmKenchQ", "name": "味見", "description": "舌をムチを打つように放つ。最初に命中したユニットにダメージを与え、スロウ効果を付与する。対象が敵チャンピオンだった場合は自分の体力を回復する。<br><br>敵チャンピオンに<spellName>「舌慣らし」</spellName>のスタックを付与する。<spellName>「舌慣らし」</spellName>を3スタック付与しているチャンピオンにこのスキルを使用すると、スタックを消費して対象をスタンさせる。", "tooltip": "最初に命中した敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間、{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。<br /><br />チャンピオンに命中すると、<healing>{{ baseheal }} + 自身の減少体力の{{ percenthealthhealing*100 }}%</healing>の体力を回復し、<spellName>「舌慣らし」</spellName>のスタックを適用して、<magicDamage>{{ spell.tahmkenchpassive:totaldamage }}の追加魔法ダメージ</magicDamage>を与える。対象のチャンピオンに<spellName>「舌慣らし」</spellName>を3スタック付与していた場合は、スタックを消費して対象を{{ stunduration }}秒間<status>スタン</status>させる。<br /><br />対象の敵チャンピオンに<spellName>「舌慣らし」</spellName>を3スタック付与していた場合は、舌を伸ばしている最中に<span class=\"color0bf7de\">「丸呑み」</span>を使用することで、離れたところから呑み込める。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "体力回復量", "減少体力に対する回復率", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ percenthealthhealing*100.000000 }}% -> {{ percenthealthhealingnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 46, 42, 38, 34], "costBurn": "50/46/42/38/34", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TahmKenchQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TahmKenchW", "name": "川潜り", "description": "水中に潜ってから指定地点に現れ、範囲内のすべての敵にダメージを与えてノックアップさせる。", "tooltip": "水中に潜ってから指定位置に現れ、範囲内のすべての敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて、{{ knockupduration }}秒間<status>ノックアップ</status>させる。敵チャンピオンに命中した場合は、クールダウンと<scaleMana>マナ</scaleMana>コストの{{ champrefund*100 }}%が回復する。<br /><br /><span class=\"color0bf7de\">「丸呑み」</span>した味方も一緒に連れていくことができる(いつでも早めに出ることが可能)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ", "射程", "@AbilityResourceName@コスト", "クールダウン短縮"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cost }} -> {{ costNL }}", "{{ champrefund*100.000000 }}% -> {{ champrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 75, 90, 105, 120], "costBurn": "60/75/90/105/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1050, 1100, 1150, 1200], "rangeBurn": "1000/1050/1100/1150/1200", "image": {"full": "TahmKenchW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TahmKenchE", "name": "ゆるゆる皮膜", "description": "<passive>自動効果:</passive> 受けたダメージの一定割合を蓄え、非戦闘時にそれに応じて体力を回復する。<br><br><active>発動効果:</active> 蓄えていた全ダメージを一時的なシールドに変換する。", "tooltip": "<passive>自動効果:</passive> 受けたダメージの{{ greyhealthratio*100 }}%を<spellName>「ゆるゆる皮膜」</spellName>で蓄える。この割合は周囲に敵チャンピオンが{{ enhancedthreshold }}体以上いた場合は{{ greyhealthratioenhanced*100 }}%に増加する。{{ ooctimer }}秒以上ダメージを受けずにいると、<spellName>「ゆるゆる皮膜」</spellName>が急速に消費されて、その値の{{ greyhealthhealingratio }}にあたる体力を回復する。<br /><br /><active>発動効果:</active> 蓄えたすべての<spellName>「ゆるゆる皮膜」</spellName>を<shield>シールド</shield>に変換する。このシールドは{{ shieldduration }}秒間持続する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ゆるゆる皮膜」へのダメージ割合", "強化時の「ゆるゆる皮膜」へのダメージ割合"], "effect": ["{{ greyhealthratio*100.000000 }}% -> {{ greyhealthrationl*100.000000 }}%", "{{ greyhealthratioenhanced*100.000000 }}% -> {{ greyhealthratioenhancednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [2400, 2400, 2400, 2400, 2400], "rangeBurn": "2400", "image": {"full": "TahmKenchE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "TahmKenchRWrapper", "name": "丸呑み", "description": "数秒間チャンピオンを丸呑みし、敵には魔法ダメージを与え、味方にはシールドを付与する。", "tooltip": "数秒間、チャンピオンを呑み込む。<recast>再発動</recast>すると吐き出す。<br /><br /><specialRules>敵チャンピオン:</specialRules><spellName> 「舌慣らし」</spellName>が3スタック必要になる。最大{{ enemyduration }}秒間呑み込み、<magicDamage>{{ basedamage }} (+対象の最大体力の{{ percenthpdamage }})の魔法ダメージ</magicDamage>を与える。この効果中は{{ slowamount*100 }}%の<status>スロウ効果</status>を受け、<keywordName>釘付け</keywordName>状態になる。<br /><br /><specialRules>味方チャンピオン:</specialRules> 最大{{ allyduration }}秒間呑み込み、<shield>耐久値{{ totalshield }}のシールド</shield>を付与する。このシールドは吐き出されたあと、徐々に減少していく。味方は自分から早めに出ることもできる。この効果中は自身も<status>釘付け状態</status>になるが、<keywordName>「川潜り」</keywordName>は使用可能で、{{ allyduration }}秒間、<speed>移動速度が{{ allyspeedamount*100 }}%</speed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "シールド量", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ datacooldown }} -> {{ datacooldownNL }}"]}, "maxrank": 3, "cooldown": [0, 0, 0], "cooldownBurn": "0", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "TahmKenchRWrapper.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "マナ {{ datamanacost }}"}], "passive": {"name": "舌慣らし", "description": "巨大な体躯から繰り出す通常攻撃に、自身の合計体力に応じた追加ダメージを付与する。ダメージを受けた敵チャンピオンには<spellName>「舌慣らし」</spellName>がスタックされ、3スタックたまった敵チャンピオンに対しては<spellName>「丸呑み」</spellName>を使えるようになる。", "image": {"full": "TahmKenchP.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}