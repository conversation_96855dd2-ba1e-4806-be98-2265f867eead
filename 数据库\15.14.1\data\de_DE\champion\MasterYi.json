{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MasterYi": {"id": "MasterYi", "key": "11", "name": "Master <PERSON>", "title": "der Wuju-Schwertkämpfer", "image": {"full": "MasterYi.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "11000", "num": 0, "name": "default", "chromas": false}, {"id": "11001", "num": 1, "name": "As<PERSON><PERSON>en<PERSON>Yi", "chromas": false}, {"id": "11002", "num": 2, "name": "Auserwählter Yi", "chromas": false}, {"id": "11003", "num": 3, "name": "Ionia-Yi", "chromas": false}, {"id": "11004", "num": 4, "name": "Samurai-Yi", "chromas": false}, {"id": "11005", "num": 5, "name": "Kopfjäger-Yi", "chromas": true}, {"id": "11009", "num": 9, "name": "PROJEKT: Yi", "chromas": false}, {"id": "11010", "num": 10, "name": "Kosmische Klinge Master Yi", "chromas": false}, {"id": "11011", "num": 11, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "11017", "num": 17, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "11024", "num": 24, "name": "<PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "11033", "num": 33, "name": "PsyOps-Master Yi", "chromas": false}, {"id": "11042", "num": 42, "name": "Charmeur-Master Yi", "chromas": false}, {"id": "11052", "num": 52, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-Master <PERSON>", "chromas": false}, {"id": "11053", "num": 53, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON> (Prestige)", "chromas": true}, {"id": "11089", "num": 89, "name": "<PERSON><PERSON><PERSON><PERSON>-Master <PERSON>", "chromas": false}, {"id": "11096", "num": 96, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Master <PERSON>", "chromas": false}, {"id": "11106", "num": 106, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Master Yi", "chromas": false}], "lore": "Master Yi stählte seinen Körper und seinen Verstand, bis Gedanken und Bewegungen beinahe eins wurden. Auch wenn er Gewalt nur als letzten Ausweg wählt, stellen die Anmut und die Geschwindigkeit seiner Klinge sicher, dass die Lösung stets zügig erfolgt. Yi hat es sich als einer der letzten lebenden Vertreter des Wuju, einer ionischen Kampfkunst, zur Lebensaufgabe gemacht, das Erbe seiner Vorfahren weiterzugeben. Mit den Sieben Sehgläsern der Einsicht mustert er neue Schüleranwärter, um unter ihnen den würdigsten auszumachen.", "blurb": "Master Yi st<PERSON>hlte seinen Körper und seinen Verstand, bis Gedanken und Bewegungen beinahe eins wurden. Auch wenn er Gewalt nur als letzten Ausweg wählt, stellen die Anmut und die Geschwindigkeit seiner Klinge sicher, dass die Lösung stets zügig erfolgt...", "allytips": ["Trittst du in deiner Lane gegen Fernkämpfer an, kann dir „Meditieren“ den notwendigen Vorteil verschaffen, um länger als diese in der Lane zu bleiben und schneller als diese Stufen zu gewinnen.", "Der „Wuju-Stil“ ist besonders am Anfang sehr stark, um Todesstöße gegen Vasallen zu erzielen.", "Versuch „Eröffnungsschlag“ auf einen Vasallen vor einem gegnerischen Champion anzuwenden, damit du am Ende des Zaubers eine sichere Distanz aufgebaut hast."], "enemytips": ["„Meditieren“ ist ein effektiver Heilzauber über Zeit. Zu Beginn ist Master <PERSON>ch anfällig gegen koordinierte Teamhinterhalte.", "Wenn Master <PERSON>, mit „Eröffnungsschlag“ effektiver Vasallen zu töten, dann greife ihn einige Male an, damit er zum Heilen Mana für „Meditieren“ aufbrauchen muss.", "Obwohl Master Yi beim <PERSON> von „Highlander“ nicht verlangsamt werden kann, können andere kampfunfähig machende Fähigkeiten ihn aufhalten."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 4, "magic": 2, "difficulty": 4}, "stats": {"hp": 669, "hpperlevel": 105, "mp": 251, "mpperlevel": 42, "movespeed": 355, "armor": 33, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.65, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.8, "attackspeedperlevel": 2.5, "attackspeed": 0.679}, "spells": [{"id": "AlphaStrike", "name": "Eröffnungsschlag", "description": "Master <PERSON> teleport<PERSON>t sich blitzschnell über das Schlachtfeld und verursacht auf seinem Weg an mehreren Einheiten normalen Schaden, während er gleichzeitig nicht anvisiert werden kann. „Eröffnungsschlag“ kann kritisch treffen und verursacht zusätzlichen normalen Schaden an Monstern. Normale Angriffe verringern die Abklingzeit von „Eröffnungsschlag“.", "tooltip": "Master Yi kann nicht anvisiert werden und teleportiert sich, um <PERSON>egner in der Nähe seines Ziels schnell anzugreifen. Dabei fügt er nach {{ alphastrikebounces }}&nbsp;Treffern allen getroffenen Gegnern <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu. <br /><br />Diese Fähigkeit kann denselben Gegner wiederholt treffen, wenn es keine anderen Ziele gibt, und verursacht bei darauffolgenden Treffern {{ subsequenthitmultiplier*100 }}&nbsp;% normalen Schaden (<physicalDamage>{{ subesquentdamage }}</physicalDamage>). Ein einzelnes Ziel kann dabei maximal <physicalDamage>{{ singletotaldamage }}&nbsp;normalen Schaden</physicalDamage> erleiden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Zusätzlicher Schaden an Monstern", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ bonusmonsterdamage }} -> {{ bonusmonsterdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19.5, 19, 18.5, 18], "cooldownBurn": "20/19.5/19/18.5/18", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AlphaStrike.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Meditate", "name": "Meditation", "description": "Master <PERSON> regene<PERSON>t seinen <PERSON>rper durch geistige Konzentration. Er stellt <PERSON> wieder her und erleidet für kurze Zeit verringerten Schaden. Außerdem erhält Master Yi Steigerungen für „Doppelschlag“ und pausiert die übrige Dauer von „Wuju-Stil“ und „Highlander“ für jede Sekunde, die er sich konzentriert.", "tooltip": "Master <PERSON> kanal<PERSON>, wodurch er über {{ healduration }}&nbsp;Sekunden hinweg <healing>{{ totalheal }}&nbsp;<PERSON><PERSON></healing> wiederherstellt. Abhä<PERSON><PERSON> von Master Yis fehlendem Leben erhöht sich diese Heilung um bis zu {{ maxmissinghealthpercent*100 }}&nbsp;%.<br /><br />Während der Kanalisierung und für {{ drlinger }}&nbsp;Sekunden nach ihrem Ende erleidet er {{ initialdr }} weniger Schaden, was nach den ersten {{ initialextradrduration }}&nbsp;Sekunden auf {{ damagereduction*100 }}&nbsp;% verringert wird.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Wiederhergestelltes Leben", "Schadensverringerung"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ damagereduction*100.000000 }}&nbsp;% -> {{ damagereductionnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana (+{{ percentmanacostpersecond*100 }}&nbsp;% max. Mana pro Sekunde)", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "Meditate.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ basemanacost }}&nbsp;Mana (+{{ percentmanacostpersecond*100 }}&nbsp;% max. Mana pro Sekunde)"}, {"id": "WujuStyle", "name": "Wuju-Stil", "description": "Verleiht normalen Angriffen zusätzlichen absoluten Schaden.", "tooltip": "Master <PERSON><PERSON> ve<PERSON> {{ duration }}&nbsp;Se<PERSON>nden lang zusätzlich <trueDamage>{{ totaldamage }}&nbsp;absoluten Schaden</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "WujuStyle.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "Highlander", "name": "Highlander", "description": "Dank seiner unvergleichlichen Beweglichkeit sind Master Yis <PERSON>- und Lauftempo kurzzeitig erhöht, während er gleichzeitig immun gegen Verlangsamung ist. Während dieser Effekt aktiv ist, erhö<PERSON> Champion-Kills oder Unterstützungen die Dauer von „Highlander“. Die Abklingzeiten seiner anderen Fähigkeiten werden bei Kills/Unterstützungen passiv verringert.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON>t Master Yi an einem Champion-<PERSON> beteiligt, werden die verbleibenden Abklingzeiten seiner Grundfähigkeiten um {{ rcooldownrefund*100 }}&nbsp;% verringert.<br /><br /><spellActive>Aktiv:</spellActive> Master <PERSON>t sich in Trance und erhält {{ rduration }}&nbsp;Sekunden lang <speed>{{ rmsbonus }}&nbsp;% Lauftempo</speed>, <attackSpeed>{{ rasbonus }}&nbsp;% Angriffstempo</attackSpeed> und Immunität gegen <status>Verlangsamungen</status>. Champion-Kills/Unterstützungen verlängern die Dauer dieser Fähigkeit um {{ rkillassistextension }}&nbsp;Sekunden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Angriffstempo", "Lauftempo"], "effect": ["{{ rasbonus }}&nbsp;% -> {{ rasbonusNL }}&nbsp;%", "{{ rmsbonus }}&nbsp;% -> {{ rmsbonusNL }}&nbsp;%"]}, "maxrank": 3, "cooldown": [85, 85, 85], "cooldownBurn": "85", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "Highlander.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Doppelschlag", "description": "Alle paar aufeinanderfolgende normale Angriffe schlägt <PERSON> zu.", "image": {"full": "MasterYi_Passive1.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}