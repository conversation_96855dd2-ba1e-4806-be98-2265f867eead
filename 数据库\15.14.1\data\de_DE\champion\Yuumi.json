{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "<PERSON><PERSON>", "title": "die magische Katze", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "Academia-Direktor<PERSON>", "chromas": true}, {"id": "350011", "num": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "350019", "num": 19, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "350028", "num": 28, "name": "Hexerei-Yuumi", "chromas": true}, {"id": "350037", "num": 37, "name": "EDG-Yuumi", "chromas": true}, {"id": "350039", "num": 39, "name": "Shi<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "350049", "num": 49, "name": "Cyber Cat <PERSON>", "chromas": true}, {"id": "350050", "num": 50, "name": "<PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "350061", "num": 61, "name": "Flammende Finsternis Yuumi", "chromas": true}], "lore": "<PERSON>umi ist eine magische Katze aus Bandle und war einst die Vertraute einer Yordle-Zauberin namens Norra. Als ihr Frauchen unter mysteriösen Umständen verschwand, wurde <PERSON><PERSON> zur Hüterin von Norras lebendigem Buch der Schwellen und reist nun auf der Suche nach ihr durch die Portale auf seinen Seiten. Da Yuumi viel Zuneigung braucht, hält sie immer die Augen nach freundlichen Gefährten offen, die sie auf ihrer Reise begleiten kann, und beschützt sie mit leuchtenden Schilden. Buch bemüht sich darum, <PERSON><PERSON>s Aufmerksamkeit auf ihre Mission zu lenken, allerdings gibt sich diese oft weltlichen Annehmlichkeiten wie Nickerchen und Fisch hin. Letzten Endes kehrt sie aber immer zur Suche nach ihrer Freundin zurück.", "blurb": "<PERSON><PERSON> ist eine magische Katze aus Bandle und war einst die Vertraute einer Yordle-Zauberin namens Norra. Als ihr Frauchen unter mysteriösen Umständen verschwand, wurde <PERSON><PERSON> zur Hüterin von Norras lebendigem Buch der Schwellen und reist nun auf der...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "Pirschende Projektile", "description": "<PERSON><PERSON> feuert ein Projektil ab, das dem ersten getroffenen Ziel Schaden zufügt und es verlangsamt. Es verursacht zusätzlichen Schaden und verlangsamt das Ziel stärker, wenn es mindestens 1,35 Sekunden lang fliegt. Wenn sie auf ihrem besten Freund sitzt, ist die Verlangsamung immer verstärkt und ihr Verbündeter erhält zusätzliche Heilung bei Treffern.<br><br>Wenn <PERSON><PERSON> jemanden begleitet, kannst du das Projektil für kurze Zeit mit dem Mauszeiger steuern.", "tooltip": "<PERSON><PERSON> feuert ein umherfliegendes Projektil ab, das dem ersten getroffenen Gegner <magicDamage>{{ totalmissiledamage }}&nbsp;magischen Schaden</magicDamage> zufügt und das Ziel mit einer <status>Verlangsamung</status> in <PERSON><PERSON><PERSON> von {{ slowamount }}&nbsp;% belegt.<br /><br />Wenn <PERSON> diese Fähigkeit einsetzt, während sie jemanden <keywordMajor>begleitet</keywordMajor>, kann das Geschoss kurzfristig mit dem Mauszeiger gesteuert werden, bevor es in gerader Linie beschleunigt. Das beschleunigte Geschoss verursacht <magicDamage>{{ totalmissiledamageempowered }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> das Ziel stattdessen {{ empoweredslowduration }}&nbsp;Sekunden lang um {{ empoweredslowamount }}&nbsp;%.<br /><br /><keywordMajor>Bester Freund-Bonus:</keywordMajor> Die <status>Verlangsamung</status> von <spellName>Pirschende Projektile</spellName> ist immer verstärkt und Championtreffer gewähren zudem {{ buffduration }}&nbsp;Sekunden lang <magicDamage>{{ onhitdamagecalc }}&nbsp;magischen Schaden</magicDamage> <OnHit>bei Treffern %i:OnHit%</OnHit>.<br /><br /><rules>Der zusätzliche Trefferschaden kann basierend auf der kritischen Trefferchance ihres Verbündeten um {{ allycritchancemaxamp*100 }}&nbsp;% erhöht werden.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Manakosten", "Grundschaden", "Verstärkter Schaden", "Verstärkte Verlangsamung", "Trefferschaden"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "{{ empoweredslowamount }}&nbsp;% -> {{ empoweredslowamountNL }}&nbsp;%", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YuumiW", "name": "Hand in Pfote", "description": "<PERSON><PERSON> springt zum verbündeten Ziel und kann nur noch von Türmen anvisiert werden. Wenn sie auf ihrem besten Freund sitzt, erhält sie Heil- und Schildkraft und gewährt ihrem Verbündeten Heilung bei Treffern.", "tooltip": "<spellPassive>Passiv:</spellPassive> Wenn sie auf ihrem <keywordMajor>besten Freund</keywordMajor> sitzt, erhält <PERSON><PERSON> zu<PERSON> <keywordMajor>{{ healandshieldpower*100 }}&nbsp;% Heil- und Schildkraft</keywordMajor>, und ihr Verbündeter stellt <OnHit>bei Treffern %i:OnHit%</OnHit> <healing>{{ healthonhit }}&nbsp;Leben</healing> wieder her.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON>umi springt zu einem verbündeten Champion und <keywordMajor>begleitet</keywordMajor> ihn. Während Yuumi einen Verbündeten <keywordMajor>begleitet</keywordMajor>, folgt sie ihrem Partner und kann nur von Türmen anvisiert werden.<br /><br /><status>Bewegungsunfähig</status> machende Effekte, die auf Yuumi wirken, belegen diese Fähigkeit mit einer Abklingzeit von {{ ccattachlockout }}&nbsp;Sekunde(n).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung bei Treffern", "Zusätzliche Heil- und Schildkraft"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "YuumiE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Schirmt Yuumi ab und verbessert ihr Lauf- und Angriffstempo. Wenn sie jemanden begle<PERSON>t, wirkt sie die Effekte auf ihren Verbündeten.<br>", "tooltip": "<PERSON><PERSON> schützt sich mit e<PERSON><PERSON>hil<PERSON>, blockt <shield>{{ totalshielding }}&nbsp;<PERSON><PERSON><PERSON></shield> und erhält {{ msduration }}&nbsp;Sekunden lang <attackSpeed>{{ totalattackspeed }}&nbsp;% Angriffstempo</attackSpeed>. Während der Schild besteht, erhält das Ziel ebenfalls <speed>{{ msamount }}&nbsp;% Lauftempo</speed>.<br /><br />Wenn Yu<PERSON> einen Verbündeten <keywordMajor>begleitet</keywordMajor>, wirkt diese Fähigkeit stattdessen auf ihn. Außerdem stellt er <magicDamage>{{ manarestore }}&nbsp;<PERSON><PERSON></magicDamage> wieder her (wird abhängig vom fehlenden Mana des Ziels um bis zu {{ maxmanapercincrease*100 }}&nbsp;% erhöht).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Manakosten", "Manawiederherstellung", "Angriffstempo"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YuumiR", "name": "<PERSON><PERSON><PERSON> Kapitel", "description": "<PERSON><PERSON> kanalisiert fü<PERSON><PERSON>, die Gegnern Schaden zufügen und Verbündete heilen. <PERSON><PERSON> kann sich bewegen, einen Verbündeten begleiten und „Ratzfatz“ einsetzen, während sie kanalisiert. Wenn sie auf ihrem besten Freund sitzt, folgt diese Fähigkeit auch ihrem Mauszeiger.", "tooltip": "Yuumi kanalisiert {{ ultduration }}&nbsp;Sekunden lang und feuert {{ numberofwaves }}&nbsp;magische Wellen ab, die einen Effekt auf beide Teams haben. Wenn Yuumi bei der ersten Aktivierung jemanden <keywordMajor>begleitet</keywordMajor>, können die Wellen mit dem Mauszeiger gesteuert werden.<br /><br />Getroffene Gegner erleiden <magicDamage>{{ totalmissiledamage }}&nbsp;magischen Schaden</magicDamage> und werden {{ ccduration }}&nbsp;Sekunden lang um {{ baseslow*-100 }}&nbsp;% <status>verlangsamt</status> (wird pro Wellentreffer um {{ bonusslowperwave*-100 }}&nbsp;% erhöht).<br /><br />Verbündete Champions werden pro Welle um <healing>{{ totalhealperwave }}&nbsp;Leben</healing> geheilt. Überschüssige Heilung wird in einen <shield><PERSON>hild</shield> umgewandelt.<br /><br /><keywordMajor>„Bester Freund“-Bonus:</keywordMajor> Für ihren <keywordMajor>besten Freund</keywordMajor> wird die Heilung auf <healing>{{ enhancedhealperwave }}&nbsp;Leben</healing> erhöht.<br /><br /><rules>Bei Aktivierung von <spellName>Hand in Pfote</spellName> behalten die Wellen ihre aktuelle Richtung bei.<br />Während sie kanalisiert, kann Yuumi sich bewegen und <spellName>Ratzfatz</spellName> einsetzen.</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden pro Geschoss", "Grundwert für Heilung pro Welle"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Flauschige Freundschaft", "description": "<PERSON><PERSON> einen Champion mit einem Angriff oder einer Fähigkeit trifft, stellt sie für sich und den nächsten Verbündeten, den sie begle<PERSON>t, <PERSON><PERSON> wieder her.<br><br><PERSON><PERSON> einen Verbündeten begleitet, knüpft sie eine besondere Verbindung mit ihm. Der Verbündete mit der stärksten Verbindung verstärkt Yuumis Fähigkeiten, während sie ihn begleitet.", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}