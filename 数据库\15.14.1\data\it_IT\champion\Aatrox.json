{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aatrox": {"id": "Aatrox", "key": "266", "name": "Aatrox", "title": "la lama dei Darkin", "image": {"full": "Aatrox.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "266000", "num": 0, "name": "default", "chromas": false}, {"id": "266001", "num": 1, "name": "Aatrox <PERSON>", "chromas": false}, {"id": "266002", "num": 2, "name": "Mecha Aatrox", "chromas": true}, {"id": "266003", "num": 3, "name": "Aatrox Cacciatore dei Mari", "chromas": false}, {"id": "266007", "num": 7, "name": "Aatrox Luna di Sangue", "chromas": false}, {"id": "266008", "num": 8, "name": "Aatrox Luna di Sangue (edizione prestigio)", "chromas": false}, {"id": "266009", "num": 9, "name": "Aatrox Vittorioso", "chromas": true}, {"id": "266011", "num": 11, "name": "Aatrox dell'Odissea", "chromas": true}, {"id": "266020", "num": 20, "name": "Aatrox Luna di Sangue (edizione prestigio 2022)", "chromas": false}, {"id": "266021", "num": 21, "name": "Aatrox Eclissi Lunare", "chromas": true}, {"id": "266030", "num": 30, "name": "Aatrox DRX", "chromas": true}, {"id": "266031", "num": 31, "name": "Aatrox DRX (edizione prestigio)", "chromas": false}, {"id": "266033", "num": 33, "name": "Aatrox Primordiano", "chromas": true}], "lore": "Un tempo onorati difensori di Shurima contro il Vuoto, Aatrox e i suoi compagni finirono per diventare una minaccia ancora maggiore per Runeterra, e vennero sconfitti solo da una subdola stregoneria dei mortali. Ma dopo secoli di prigionia Aatrox fu il primo a ritrovare la libertà, corrompendo e trasformando chi si dimostrava abbastanza folle da brandire l'arma magica che conteneva la sua essenza. Or<PERSON>, con un corpo rubato, vaga per Runeterra con un brutale simulacro della sua vecchia forma, alla ricerca di un'apocalittica e tanto desiderata vendetta.", "blurb": "Un tempo onorati difensori di Shurima contro il Vuoto, Aatrox e i suoi compagni finirono per diventare una minaccia ancora maggiore per Runeterra, e vennero sconfitti solo da una subdola stregoneria dei mortali. Ma dopo secoli di prigionia Aatrox fu il...", "allytips": ["Usa Scatto dell'ombra mentre lanci La lama dei Darkin per aumentare le tue probabilità di colpire il nemico.", "Gli effetti di controllo di abilità come Catene infernali o gli effetti immobilizzanti degli alleati aiutano la preparazione di La lama dei Darkin.", "Lancia Sterminatore di Mondi quando sei sicuro di poter vincere una battaglia."], "enemytips": ["<PERSON><PERSON> attacchi di Aatrox sono molto prevedibili, quindi approfittane per schivare le aree d'impatto.", "È più facile uscire dalle Catene infernali di Aatrox correndo verso i lati o verso Aatrox.", "Tieni le distanze quando Aatrox usa la sua suprema per evitare che si rianimi."], "tags": ["Fighter"], "partype": "Pozzo di sangue", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 38, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.651}, "spells": [{"id": "AatroxQ", "name": "La lama dei Darkin", "description": "Aatrox colpisce a terra con la sua spada, infliggendo danni fisici. <PERSON><PERSON>ò farlo tre volte, ogni volta con un'area d'effetto diversa.", "tooltip": "Aatrox colpisce dall'alto con la sua spada, infliggendo <physicalDamage>{{ qdamage }} danni fisici</physicalDamage>. Se vengono colpiti sul bordo dell'area, i nemici vengono brevemente <status>lanciati in aria</status> e subiscono invece <physicalDamage>{{ qedgedamage }}</physicalDamage>. Questa abilità può essere <recast>rilanciata</recast> due volte, ciascuna delle quali ha una forma diversa e infligge il 25% di danni in più rispetto alla precedente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Rapporto attacco fisico totale"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ qtotaladratio*100.000000 }}% -> {{ qtotaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "AatroxW", "name": "<PERSON>ene infernali", "description": "Aatrox colpisce il terreno, danne<PERSON><PERSON><PERSON> il primo nemico colpito. I campioni e i mostri grandi devono lasciare rapidamente la zona dell'impatto o verranno trascinati al centro, dove subiranno nuovamente i danni.", "tooltip": "Aatrox lancia una catena, che <status>rallenta</status> il primo nemico colpito di un {{ wslowpercentage*-100 }}% per {{ wslowduration }} secondi e infligge <physicalDamage>{{ wdamage }} danni fisici</physicalDamage>. I campioni e i mostri grandi della giungla hanno {{ wslowduration }} secondi per lasciare la zona dell'impatto o verranno <status>attirati</status> verso il centro, dove subiranno nuovamente la stessa quantità danni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wbasedamage }} -> {{ wbasedamageNL }}", "{{ wslowpercentage*-100.000000 }}% -> {{ wslowpercentagenl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AatroxW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "AatroxE", "name": "Scatto dell'ombra", "description": "Passivamente, Aatrox guarisce quando danneggia i campioni nemici. All'attivazione, scatta in una direzione.", "tooltip": "<spellPassive>Passiva:</spellPassive> Aatrox si cura di un <lifeSteal>{{ totalevamp }}</lifeSteal> dei danni che infligge ai campioni. <br /><br /><spellActive>Attiva:</spellActive> Aatrox scatta. Può usare questa abilità mentre ne prepara un'altra.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxE.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "AatroxR", "name": "Sterminatore di Mondi", "description": "Aatrox spiega le sue ali demoniache, impaurendo i minion nemici nelle vicinanze e ottenendo attacco fisico, guarigione aumentata e velocità di movimento. Se ottiene un'uccisione, questo effetto viene esteso.", "tooltip": "Aatrox rivela la sua vera forma demoniaca, <status>impaurendo</status> i minion vicini per {{ rminionfearduration }} secondi e ottenendo <speed>{{ rmovementspeedbonus*100 }}% velocità di movimento</speed>, che decresce nell'arco di {{ rduration }} secondi. Inoltre, ottiene <scaleAD>{{ rtotaladamp*100 }}% attacco fisico</scaleAD> e aumenta l'<healing>autoguarigione del {{ rhealingamp*100 }}%</healing> per la durata.<br /><br />Eliminare un campione estende la durata di questo effetto di {{ rextension }} secondi e ripristina quello di <speed>velocità di movimento</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Aumento totale attacco fisico", "Aumento guarigione", "Velocità di movimento", "Ricarica"], "effect": ["{{ rtotaladamp*100.000000 }}% -> {{ rtotaladampnl*100.000000 }}%", "{{ rhealingamp*100.000000 }}% -> {{ rhealingampnl*100.000000 }}%", "{{ rmovementspeedbonus*100.000000 }}% -> {{ rmovementspeedbonusnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxR.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Posizione del Messaggero della morte", "description": "Periodicamente l'attacco base successivo di Aatrox infligge <physicalDamage>danni fi<PERSON><PERSON></physicalDamage> bonus e lo cura in base alla salute massima del bersaglio. ", "image": {"full": "Aatrox_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}