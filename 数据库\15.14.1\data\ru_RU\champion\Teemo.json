{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Teemo": {"id": "<PERSON><PERSON><PERSON>", "key": "17", "name": "Тимо", "title": "Шустрый скаут", "image": {"full": "Teemo.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "17000", "num": 0, "name": "default", "chromas": false}, {"id": "17001", "num": 1, "name": "Радостный эльф Тимо", "chromas": false}, {"id": "17002", "num": 2, "name": "Разведчик Тимо", "chromas": false}, {"id": "17003", "num": 3, "name": "Барсук Тимо", "chromas": false}, {"id": "17004", "num": 4, "name": "Астронавт Тимо", "chromas": true}, {"id": "17005", "num": 5, "name": "Зайчик Тимо", "chromas": true}, {"id": "17006", "num": 6, "name": "Супер-Тимо", "chromas": false}, {"id": "17007", "num": 7, "name": "Панда Тимо", "chromas": false}, {"id": "17008", "num": 8, "name": "Тимо из отряда ''Омега''", "chromas": true}, {"id": "17014", "num": 14, "name": "Дьяволенок Тимо", "chromas": true}, {"id": "17018", "num": 18, "name": "Жуж<PERSON>имо", "chromas": true}, {"id": "17025", "num": 25, "name": "Дух цветения Тимо", "chromas": false}, {"id": "17027", "num": 27, "name": "Дух цветения Тимо (престижный)", "chromas": false}, {"id": "17037", "num": 37, "name": "Пиротехник Тимо", "chromas": true}, {"id": "17047", "num": 47, "name": "Тимо из Галактики грува", "chromas": true}], "lore": "Тимо путешествует по миру, заряженный бесконечным энтузиазмом и бодростью, и его не пугают даже самые опасные и грозные преграды. Йордл, наделенный небывалой высоконравственностью, неукоснительно следует Кодексу скаутов Бандла – порой с таким рвением, что не осознает последствий своих действий. Некоторые ставят под сомнение само существование скаутов, но одно известно наверняка: с Тимо лучше не спорить.", "blurb": "Тимо путешествует по миру, заряженный бесконечным энтузиазмом и бодростью, и его не пугают даже самые опасные и грозные преграды. Йордл, наделенный небывалой высоконравственностью, неукоснительно следует Кодексу скаутов Бандла – порой с таким рвением...", "allytips": ["Грибы можно использовать для убийства волн миньонов.", "Грибы можно ставить в ключевых точках карты наподобие дракона или Барона Нашора. Если враг попытается убить их, вы об этом узнаете."], "enemytips": ["Отравленный выстрел поражает игроков, которые получают удар и отходят. Оставайтесь на безопасном расстоянии до тех пор, пока не будете готовы действовать.", "Вы можете использовать Альтернативу оракула (аксессуар) для уничтожения грибов Тимо в стратегических точках."], "tags": ["Marksman", "Mage"], "partype": "Мана", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 6}, "stats": {"hp": 615, "hpperlevel": 104, "mp": 334, "mpperlevel": 25, "movespeed": 330, "armor": 24, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 9.6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3, "attackspeedperlevel": 3.38, "attackspeed": 0.69}, "spells": [{"id": "TeemoQ", "name": "Слепящий дротик", "description": "Тимо ослепляет противника сильным ядом, нанося ему урон и заставляя его некоторое время промахиваться.", "tooltip": "Тимо выпускает дротик, <status>ослепляя</status> цель на {{ blindduration }} сек. и нанося ей <magicDamage>{{ calculateddamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Стоимость – @AbilityResourceName@", "Продолжительность", "Урон"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ blindduration }} -> {{ blinddurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoQ.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TeemoW", "name": "Быстрый бег", "description": "Скорость передвижения Тимо пассивно увеличена, пока его не поразит вражеский чемпион или башня. Тимо может рвануть вперед, при этом он получает дополнительную скорость передвижения и не замедляется при получении урона.", "tooltip": "<spellPassive>Пассивно:</spellPassive> <speed>скорость передвижения</speed> Тимо увеличивается на <speed>{{ passivemovespeedbonus*100 }}%</speed>, если он не получает урон от чемпионов или башен в течение {{ passivecooldownondamagetaken }} сек.<br /><br /><spellActive>Активно:</spellActive> Тимо разгоняется, получая {{ activemovespeedbonus*100 }}% скорости передвижения на {{ activemovespeedbuffduration }} сек. Полученный урон не влияет на активный эффект.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Скорость передвижения от пассивного эффекта", "Скорость передвижения от активного эффекта"], "effect": ["{{ passivemovespeedbonus*100.000000 }}% -> {{ passivemovespeedbonusnl*100.000000 }}%", "{{ activemovespeedbonus*100.000000 }}% -> {{ activemovespeedbonusnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TeemoW.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "TeemoE", "name": "Отравленный выстрел", "description": "Автоатаки Тимо отравляют цель, нанося урон сразу и затем дополнительный урон каждую секунду в течение 4 сек.", "tooltip": "<spellPassive>Пассивно:</spellPassive> автоатаки Тимо отравляют цель, дополнительно нанося <magicDamage>{{ impactcalculateddamage }} магического урона</magicDamage> плюс <magicDamage>{{ totaldotdamage }} магического урона</magicDamage> в течение {{ poisonduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Первоначальный урон", "Урон в секунду"], "effect": ["{{ impactbasedamage }} -> {{ impactbasedamageNL }}", "{{ tickbasedamage }} -> {{ tickbasedamageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Пассивно", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoE.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Пассивно"}, {"id": "TeemoR", "name": "Ядовитая ловушка", "description": "Тимо бросает взрывающуюся отравляющую ловушку, используя один из хранящихся в его рюкзаке грибов. Если противник задевает ее, ловушка выпускает ядовитое облако, замедляющее врагов и наносящее им урон с течением времени. Если брошенная ловушка попадает в другую ловушку, она отскакивает и приземляется дальше.", "tooltip": "Тимо бросает ловушку в виде гриба, которая взрывается, когда на нее наступает враг. Ловушки <status>замедляют</status> на {{ slowamount }}% и наносят <magicDamage>{{ totaldamage }} магического урона</magicDamage> в течение {{ debuffduration }} сек. Кроме того, пораженные враги раскрываются на то же время.<br /><br />Ловушки существуют {{ mushroomduration }} мин., и враги их не видят. Гриб, брошенный на другой гриб, отскакивает и приземляется в новое место. У этого умения может быть до {{ maxammo }} зарядов (новый заряд дается раз в {{ ammorechargetime }} сек.).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Дальность броска", "Максимальная дальность отскока", "Максимальное количество ловушек в запасе", "Затраты маны"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ maxbouncedistance }} -> {{ maxbouncedistanceNL }}", "{{ maxammo }} -> {{ maxammoNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 3, "cooldown": [0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [75, 55, 35], "costBurn": "75/55/35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "3", "range": [600, 750, 900], "rangeBurn": "600/750/900", "image": {"full": "TeemoR.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Партизанская война", "description": "Если Тимо некоторое время остается неподвижным и не предпринимает никаких действий, он становится невидимым. В кустах Тимо становится невидимым даже во время движения и может двигаться, не выходя из невидимости. После выхода из невидимости в течение нескольких секунд скорость атаки Тимо увеличена благодаря Фактору внезапности.", "image": {"full": "TeemoPassive.ASU_Teemo.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}