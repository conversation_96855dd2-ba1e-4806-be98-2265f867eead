{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kalista": {"id": "Kalista", "key": "429", "name": "Kalista", "title": "der Speer der Rache", "image": {"full": "Kalista.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "429000", "num": 0, "name": "default", "chromas": false}, {"id": "429001", "num": 1, "name": "Blutmond-Kalista", "chromas": false}, {"id": "429002", "num": 2, "name": "WM 2015-Kalista", "chromas": true}, {"id": "429003", "num": 3, "name": "SKT T1-Kalista", "chromas": false}, {"id": "429005", "num": 5, "name": "Marodeur-Kalista", "chromas": true}, {"id": "429014", "num": 14, "name": "Feenhof-Kalista", "chromas": true}, {"id": "429024", "num": 24, "name": "Rächende Dämmerung Kalista", "chromas": true}], "lore": "Kalista ist ein Schemen des Zorns und der Vergeltung. Sie ist der untote Geist der Rache und ein gepanzerter Albtraum, der von den Schatteninseln beschworen wurde, um Betrüger und Verräter zu jagen. Die Betrogenen mögen blutend darum flehen, ger<PERSON><PERSON> zu werden, doch Kalista erhört nur jene, die bereit sind, ihre Seele herzugeben. <PERSON><PERSON>, die zum Ziel von Kali<PERSON>s Zorn werden, sollten Frieden schließen mit der Welt, denn jeder <PERSON>t mit der grimmigen Jägerin kann einzig durch das durchdringende Feuer ihrer Seelenspeere enden.", "blurb": "Kalista ist ein Schemen des Zorns und der Vergeltung. Sie ist der untote Geist der Rache und ein gepanzerter Albtraum, der von den Schatteninseln beschworen wurde, um Betrüger und Verräter zu jagen. Die Betrogenen mögen blutend darum flehen, gerächt zu...", "allytips": ["„Reißen“ eignet sich besonders gut für Todesstöße, da dessen Abklingzeit zurückgesetzt wird, falls es das Ziel tötet.", "Wenn du einmal einen Bewegungsbefehl eingibst, um „Kriegerische Anmut“ au<PERSON><PERSON><PERSON>sen, wird Kalistas Ziel für den normalen Angriff nicht gelöscht.", "Durch ihre passive Fähigkeit wird Kalistas Lauftempo durch Angriffstempo effektiv erhöht."], "enemytips": ["Kalistas Mobilität beruht auf ihren Angriffen. Wenn sie nicht in Angriffsreichweite ist, ist diese eingeschränkt. Zudem verringern Verlangsamungen ihres Angriffstempos die Distanz, die sie in einem Ansturm zurücklegen kann.", "Kalista kann den Aufbau ihres normalen Angriffs nicht abbrechen. Auch wenn sie sehr beweglich ist, bietet dir dies ein Zeitfenster, um Fähigkeiten auf ihr zu platzieren, falls du genau vorhersagen kannst, wann sie ihren Angriff beginnen wird.", "Falls du den Sichtkontakt mit ihr unterbrechen kannst (auch durch hohes Gras), werden ihre normalen Angriffe verfehlen."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 4, "difficulty": 7}, "stats": {"hp": 560, "hpperlevel": 114, "mp": 300, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 6.3, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 4, "attackspeedperlevel": 4.5, "attackspeed": 0.694}, "spells": [{"id": "KalistaMysticShot", "name": "Durchbohren", "description": "<PERSON><PERSON><PERSON><PERSON>t einen schnell fliegenden Speer, der durch Gegner, die er tötet, hindurch fliegt.", "tooltip": "Kalista schleudert einen Speer und fügt dem ersten getroffenen Ziel <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zu. Wird das Ziel dadurch getötet, fliegt der Speer weiter und trägt alle Steigerungen von <spellName>Reißen</spellName> zum nächsten getroffenen Ziel.<br /><br />Kalista kann nach dem Einsatz dieser Fähigkeit springen, indem sie <spellName>Kriegerische Anmut</spellName> benutzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "KalistaMysticShot.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KalistaW", "name": "Wachposten", "description": "Du verursachst zusätzlichen Schaden, wenn Kalista und ihr Eidgeschworener beide dasselbe Ziel angreifen. <br><br><PERSON><PERSON><PERSON><PERSON><PERSON>, um eine Seele auszusenden, die auf einem Pfad patrouilliert und den Bereich vor sich aufdeckt.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON><PERSON> und ihr <keywordMajor>Eidgeschworener</keywordMajor> dasselbe Ziel angreifen, fügt sie dem <PERSON> <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ maxhealthdamage*100 }}&nbsp;% seines maximalen Lebens zu. Dieser Effekt hat pro Ziel eine Abklingzeit von {{ pertargetcooldown }}&nbsp;Sekunde(n) und gegen Einheiten, die keine Champions sind, eine Obergrenze von {{ maximummonsterdamage }}.<br /><br /><spellPassive>Aktiv: </spellPassive>Kalista sendet einen Geist aus, um einen Bereich 3&nbsp;Runden lang zu patrouillieren. Von ihm entdeckte Champions werden 4&nbsp;Sekunden lang aufgedeckt. Diese Fähigkeit hat 2&nbsp;Aufladungen ({{ ammorechargetooltip }}&nbsp;Sekunden Aufladungszeit).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden abhängig vom maximalen Leben ", "Wiederaufladungsrate", "Grenze für Monsterschaden"], "effect": ["{{ maxhealthdamage*100.000000 }}&nbsp;% -> {{ maxhealthdamagenl*100.000000 }}&nbsp;%", "{{ ammorechargetooltip }} -> {{ ammorechargetooltipNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}"]}, "maxrank": 5, "cooldown": [30, 30, 30, 30, 30], "cooldownBurn": "30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 80, 80, 80, 80], [0, 0, 0, 0, 0], [125, 150, 175, 200, 225], [90, 80, 70, 60, 50], [0, 0, 0, 0, 0], [75, 100, 125, 150, 175], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80", "0", "125/150/175/200/225", "90/80/70/60/50", "0", "75/100/125/150/175", "10", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "2", "range": [5000, 5000, 5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "KalistaW.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "KalistaExpungeWrapper", "name": "Reißen", "description": "Angriffe durchbohren ihre Ziele mit Speeren. Aktivieren, um die Speere herauszuziehen, zu verlangsamen und ansteigenden Schaden zu verursachen.", "tooltip": "<spellPassive>Passiv: </spellPassive>Kalistas Speere verharren 4&nbsp;Sekunden lang in ihrem Ziel (unbegrenzt steigerbar).<br /><br /><spellActive>Aktiv:</spellActive> Kalista reißt die Speere aus Gegnern in der Nähe heraus. <PERSON><PERSON>, nach dem ersten, fügt ihnen <physicalDamage>normalen Schaden</physicalDamage> in Höhe von <physicalDamage>{{ normaldamage }} plus {{ additionaldamage }}</physicalDamage> zu. Getroffene Gegner werden {{ slowduration }}&nbsp;Sekunden lang um <attention>{{ totalslowamount }}</attention> <status>verlangsamt</status>.<br /><br />Wenn diese Fähigkeit mindestens ein Ziel tötet, wird die Abklingzeit zurückgesetzt und <scaleMana>{{ manarefund }}&nbsp;Mana</scaleMana> zurückerstattet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden pro Steigerung", "Angriffsschadenskalierung pro Steigerung", "Verlangsamung", "Manarückerstattung", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ additionalbasedamage }} -> {{ additionalbasedamageNL }}", "{{ additionaladratio*100.000000 }}&nbsp;% -> {{ additionaladrationl*100.000000 }}&nbsp;%", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ manarefund }} -> {{ manarefundNL }}", "{{ fakedcooldown }} -> {{ fakedcooldownNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "2", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaExpungeWrapper.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KalistaRx", "name": "Ruf des Schicksals", "description": "Kalista teleportiert den Eidgeschworenen zu sich. Der gewählte Verbündete erhält die Fähigkeit, zu einer Position vor zu springen und gegnerische Champions zurückzuschlagen.", "tooltip": "Kalista versetzt ihren <keywordMajor>Eidgeschworenen</keywordMajor> bis zu 4&nbsp;Sekunden lang in Stase und zieht ihn zu sich heran. Der <keywordMajor>Eidgeschworene</keywordMajor> kann klicken, um sich selbst in eine Richtung zu schleudern. Er hält beim ersten getroffenen Champion an, wobei alle Gegner in der Nähe <status>zurückgestoßen</status> werden. Wenn der <keywordMajor>Eidgeschworene</keywordMajor> einen Champion trifft, landet er auf seiner maximalen Angriffsreichweite von ihm entfernt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Dauer des Hochschleuderns"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [1, 1.5, 2], [40, 60, 80], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "1/1.5/2", "40/60/80", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaRx.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Kriegerische Anmut", "description": "G<PERSON> einen Bewegungsbefehl ein, während du Kalistas normalen Angriff oder „Durchbohren“ vorbereitest, um ein kleines Stück nach vorn zu springen, wenn sie ihren Angriff ausführt.", "image": {"full": "Kalista_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}