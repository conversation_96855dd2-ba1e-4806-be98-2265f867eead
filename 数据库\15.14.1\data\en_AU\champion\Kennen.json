{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kennen": {"id": "<PERSON><PERSON>", "key": "85", "name": "<PERSON><PERSON>", "title": "the Heart of the Tempest", "image": {"full": "Kennen.png", "sprite": "champion2.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "85000", "num": 0, "name": "default", "chromas": false}, {"id": "85001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "85002", "num": 2, "name": "Swamp Master <PERSON>", "chromas": false}, {"id": "85003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "85004", "num": 4, "name": "<PERSON>nen M.D.", "chromas": true}, {"id": "85005", "num": 5, "name": "Arctic Ops Kennen", "chromas": false}, {"id": "85006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "85007", "num": 7, "name": "Super Kennen", "chromas": false}, {"id": "85008", "num": 8, "name": "Infernal Kennen", "chromas": true}, {"id": "85023", "num": 23, "name": "DWG <PERSON>", "chromas": true}, {"id": "85025", "num": 25, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "More than just the lightning-quick enforcer of Ionian balance, <PERSON><PERSON> is the only yordle member of the Kinkou. Despite his small, furry stature, he is eager to take on any threat with a whirling storm of shuriken and boundless enthusiasm. Alongside his master <PERSON>, <PERSON><PERSON> patrols the spirit realm, employing devastating electrical energy to strike down his enemies.", "blurb": "More than just the lightning-quick enforcer of Ionian balance, <PERSON><PERSON> is the only yordle member of the Kinkou. Despite his small, furry stature, he is eager to take on any threat with a whirling storm of shuriken and boundless enthusiasm. Alongside his...", "allytips": ["You can stun your opponents by inflicting 3 Marks of the Storm upon them.", "Lightning Rush can be used to initiate fights with its Energy return component, allowing him to use other abilities afterward.", "You can land an initial Mark of the Storm debuff on an opponent with Thundering Shuriken, and then add to it with Electrical Surge."], "enemytips": ["Consider playing a bit more cautiously around <PERSON><PERSON> when you have a Mark of the Storm debuff. If he hits you with 3 Marks of the Storm, you will get stunned.", "<PERSON><PERSON> is flimsy by nature - turn and attack him if he gets careless with <PERSON>."], "tags": ["Mage"], "partype": "Energy", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 4}, "stats": {"hp": 580, "hpperlevel": 98, "mp": 200, "mpperlevel": 0, "movespeed": 335, "armor": 29, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.75, "attackspeedperlevel": 3.4, "attackspeed": 0.625}, "spells": [{"id": "KennenShurikenHurlMissile1", "name": "Thundering Shuriken", "description": "<PERSON><PERSON> throws a fast moving shuriken towards a location, causing damage and adding a Mark of the Storm to any opponent that it hits.", "tooltip": "<PERSON><PERSON> throws a shuriken, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to the first enemy hit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [7, 6.25, 5.5, 4.75, 4], "cooldownBurn": "7/6.25/5.5/4.75/4", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KennenShurikenHurlMissile1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KennenBringTheLight", "name": "Electrical Surge", "description": "<PERSON><PERSON> passively deals extra damage and adds a Mark of the Storm to his target every few attacks, and he can activate this ability to damage and add another Mark of the Storm to targets who are already marked.", "tooltip": "<spellPassive>Passive:</spellPassive> Every 5th Attack deals an additional <magicDamage>{{ totaldamagepassive }} magic damage</magicDamage>.<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON> sends out a blast of electricity, dealing <magicDamage>{{ totaldamageactive }} magic damage</magicDamage> to nearby enemies affected by <spellName>Mark of the Storm</spellName>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Bonus AD Ratio", "Damage (passive)", "Damage (active)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ badratio*100.000000 }}% -> {{ badrationl*100.000000 }}%", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [13, 11.25, 9.5, 7.75, 6], "cooldownBurn": "13/11.25/9.5/7.75/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [25, 25, 25, 25, 25], [0.7, 0.7, 0.7, 0.7, 0.7], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "25", "0.7", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "KennenBringTheLight.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KennenLightningRush", "name": "Lightning Rush", "description": "<PERSON><PERSON> morphs into a lightning form, enabling him to pass through units and apply a Mark of the Storm. <PERSON><PERSON> gains Move Speed when entering this form, and attack speed when leaving it.", "tooltip": "<PERSON><PERSON> turns into a ball of lightning, gaining <speed>{{ movementspeed*100 }}% Move Speed</speed>, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies he passes through, and becoming Ghost<PERSON> for {{ durationasball }} seconds. If <PERSON><PERSON> damages at least one enemy, he gains {{ energyrefund }} Energy. <br /><br />After this ability ends, <PERSON><PERSON> gains <attackSpeed>{{ attackspeed*100 }}% Attack Speed</attackSpeed> for {{ durationafterball }} seconds. <PERSON><PERSON> can <recast>Recast</recast> to end this Ability early.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "Attack Speed"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [85, 125, 165, 205, 245], [40, 40, 40, 40, 40], [10, 20, 30, 40, 50], [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/125/165/205/245", "40", "10/20/30/40/50", "1", "2", "0", "4", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [170, 170, 170, 170, 170], "rangeBurn": "170", "image": {"full": "KennenLightningRush.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KennenShurikenStorm", "name": "Slicing <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> summons a storm that strikes at nearby enemy champions for magical damage.", "tooltip": "<PERSON><PERSON> unleashes a magical storm, dealing <magicDamage>{{ pertickdamagecalculated }} magic damage</magicDamage> to all nearby enemies every {{ kennenrtickrate }} seconds, and gaining <scaleArmor>{{ kennenrdefenses }} Armor</scaleArmor> and <scaleMR>{{ kennenrdefenses }} Magic Resist</scaleMR> for {{ kennenrduration }} seconds. Successive hits deal {{ damageamp*100 }}% increased damage for each hit the enemy has already suffered.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Armor Bonus", "Magic Resist Bonus", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ kennenrdefenses }} -> {{ kennenrdefensesNL }}", "{{ kennenrdefenses }} -> {{ kennenrdefensesNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "KennenShurikenStorm.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "No Cost"}], "passive": {"name": "Mark of the Storm", "description": "<PERSON><PERSON> stuns enemies he hits 3 times with his abilities.", "image": {"full": "Kennen_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}