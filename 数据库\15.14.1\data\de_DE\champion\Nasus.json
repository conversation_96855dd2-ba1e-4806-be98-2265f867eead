{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nasus": {"id": "<PERSON><PERSON>", "key": "75", "name": "<PERSON><PERSON>", "title": "der Bewahrer des Sandes", "image": {"full": "Nasus.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "75000", "num": 0, "name": "default", "chromas": false}, {"id": "75001", "num": 1, "name": "Galaktischer Nasus", "chromas": false}, {"id": "75002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "75003", "num": 3, "name": "Schreckensritter Nasus", "chromas": true}, {"id": "75004", "num": 4, "name": "Riot GSG-9 Nasus", "chromas": false}, {"id": "75005", "num": 5, "name": "Infernalischer Nasus", "chromas": false}, {"id": "75006", "num": 6, "name": "Erzherzog Nasus", "chromas": false}, {"id": "75010", "num": 10, "name": "Weltenbrecher-Nasus", "chromas": false}, {"id": "75011", "num": 11, "name": "Mondwächter Nasus", "chromas": true}, {"id": "75016", "num": 16, "name": "Stahlkrieger-Nasus", "chromas": true}, {"id": "75025", "num": 25, "name": "Weltraum-Groove-Nasus", "chromas": true}, {"id": "75035", "num": 35, "name": "Gepanzerter Titan Nasus", "chromas": true}, {"id": "75045", "num": 45, "name": "Flammende Finsternis Nasus", "chromas": true}, {"id": "75054", "num": 54, "name": "Schicksalsformer Nasus", "chromas": true}], "lore": "Nasus ist ein <PERSON><PERSON><PERSON><PERSON><PERSON>, s<PERSON><PERSON>kö<PERSON>iger Aufgestiegener aus dem alten Shurima, eine Heldengestalt, die von den Wüstenvölkern als Hal<PERSON>t verehrt wird. Er ist hochintelligent, war ein Wächter des Wissens und ein Stratege, der seinesgleichen sucht und dessen Weisheit das alte shurimanische Reich viele Jahrhunderte lang zu wahrer Größe führte. Nach dem Fall des Reiches ging er in ein selbstauferlegtes Exil, womit er schließlich zu einer Legende wurde. <PERSON><PERSON>, da die uralte Hauptstadt Shurimas sich erneut erhoben hat, ist er zurückgekehrt und tut alles, was in seiner Macht steht, damit sie niemals wieder untergeht.", "blurb": "Nasus ist ein <PERSON><PERSON><PERSON><PERSON><PERSON>, schakalköpfiger Aufgestiegener aus dem alten Shurima, eine Heldengestalt, die von den Wüstenvölkern als Halbgott verehrt wird. Er ist hochintelligent, war ein Wächter des Wissens und ein Stratege, der seinesgleichen sucht...", "allytips": ["<PERSON><PERSON><PERSON> da<PERSON>, mit dem „Aussaugenden Schlag“ den letzten Treffer zu erzielen. Dies hat im späten Spiel große Auswirkungen.", "Wenn du allein spielst, ist „Geisterfeuer“ bestens zum Farmen einer Lane geeignet. Wenn ihr zu zweit seid, ist es eine schlechte Wahl, wenn du dich zu weit vorwagst. Du musst die richtige Balance zwischen dem letzten Treffer durch den „Aussaugenden Schlag“ und Flächenschaden finden.", "Wenn deine Verteidigung schwach ist, kannst du, selbst während deine ultimative Fähigkeit läuft, mit einem gezielten Fokus zu Fall gebracht werden. Versuche - auch wenn du dich auf DPS spezialisierst - ein paar Gegenstände zu kaufen, die deine Überlebenschance erhöhen."], "enemytips": ["Solange Nasus durch seine ultimative Fähigkeit verwandelt ist, ist er stärker als die meisten Champions der Liga. Tritt ihm nur gegenüber, wenn du klar im Vorteil bist.", "„Verdorren“ auf der höchsten Stufe ist sehr effektiv, wenn es darum geht, Charak<PERSON><PERSON> mit hohem Angriffsschaden die Stirn zu bieten. Versuche zu vermeiden, allein davon erwischt zu werden.", "<PERSON><PERSON>n Nasus kann man gut aggressiv flüchten. <PERSON><PERSON><PERSON> nicht, es mit ihm aufzunehmen, wenn er volles Leben hat."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 5, "magic": 6, "difficulty": 6}, "stats": {"hp": 631, "hpperlevel": 104, "mp": 326, "mpperlevel": 62, "movespeed": 350, "armor": 34, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.45, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 67, "attackdamageperlevel": 4, "attackspeedperlevel": 3.48, "attackspeed": 0.638}, "spells": [{"id": "NasusQ", "name": "Aussaugender Schlag", "description": "Nasus schlägt auf seinen G<PERSON>ner ein, veru<PERSON><PERSON> Schaden und erhöht die Kraft seiner folgenden „Aussaugenden Schläge“, wenn er sein <PERSON> tötet.", "tooltip": "Nasus' nächster Angriff verursacht <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>. Wenn er mit diesem Angriff einen Gegner tötet, wird der Schaden dauerhaft um {{ basicstacks }} erhöht (gegen Champions, große Vasallen und große Dschungelmonster erhöht auf {{ bigstacks }}).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Abklingzeit"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7.5, 6.5, 5.5, 4.5, 3.5], "cooldownBurn": "7.5/6.5/5.5/4.5/3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [255, 255, 255, 255, 255], "rangeBurn": "255", "image": {"full": "NasusQ.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Nasus lässt einen gegnerischen Champion altern, wodurch dessen Lauf- und Angriffstempo über eine bestimmte Zeit verringert werden.", "tooltip": "Nasus lässt einen Champion altern und <status>verlangsamt</status> ihn dadurch um {{ slowbase }}&nbsp;%. Die Verlangsamung wird über {{ duration }}&nbsp;Sekunden hinweg auf {{ maxslowtooltiponly }}&nbsp;% erhöht. Außerdem verringert sich das <attackSpeed>Angriffstempo</attackSpeed> des Ziels um {{ attackspeedslowmult*100 }}&nbsp;% der <status>Verlangsamung</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ maxslowtooltiponly }}&nbsp;% -> {{ maxslowtooltiponlyNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "NasusW.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Nasus entfesselt an einem Ort eine Geisterflamme, die Schaden verursacht und die Rüstung der Gegner verringert, die in ihr stehen.", "tooltip": "Nasus entzündet eine Geisterflamme, die <magicDamage>{{ initialdamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Gegner im Bereich verlieren <scaleArmor>{{ e2 }}&nbsp;% Rüstung</scaleArmor> und erleiden über {{ e3 }}&nbsp;Sekunden hinweg <magicDamage>{{ totaldotdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "Schaden pro Sekunde", "Rüstungsverringerung (%)", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ effect2amount*-100.000000 }}&nbsp;% -> {{ effect2amountnl*-100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [10, 16, 22, 28, 34], [30, 35, 40, 45, 50], [5, 5, 5, 5, 5], [50, 80, 110, 140, 170], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/16/22/28/34", "30/35/40/45/50", "5", "50/80/110/140/170", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NasusE.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusR", "name": "Zorn des Sandes", "description": "Nasus entfesselt einen mächtigen Sandsturm, der nahe Gegner beeinträchtigt. Solange der Sturm wütet, erhält Nasus zusätzliches Leben und Angriffsreichweite, fügt nahen <PERSON> zu, verringert die Abklingzeit von „Aussaugender Schlag“ und erhält für die Dauer zusätzliche Rüstung und Magieresistenz.", "tooltip": "Nasus umgibt sich 15&nbsp;<PERSON><PERSON>nden lang mit eine<PERSON> Sandsturm, wodurch sich sein <healing>maximales Leben um {{ bonushealth }}</healing> und seine <scaleArmor>Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR> um {{ initialresistgain }} erhöhen.<br /><br />Solange der Sturm tobt, erle<PERSON> Gegner in der Nähe jede Sekunde <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ damagecalc }} des maximalen Lebens. Außerdem wird die Abklingzeit von <spellName>Aussaugender Schlag</spellName> um {{ qcdr*100 }}&nbsp;% verringert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzliches Leben", "<PERSON><PERSON> (%)", "Zusätzliche Rüstung und Magieresistenz", "Abklingzeit"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ aoedamagepercent*100.000000 }}&nbsp;% -> {{ aoedamagepercentnl*100.000000 }}&nbsp;%", "{{ initialresistgain }} -> {{ initialresistgainNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NasusR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> entz<PERSON>ht seinen Gegnern Seelenenergie, wodurch er zusätzlichen Lebensraub erhält.", "image": {"full": "Nasus_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}