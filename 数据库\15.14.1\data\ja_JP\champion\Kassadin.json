{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "カサディン", "title": "ヴォイドを歩む者", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "お祭りカサディン", "chromas": false}, {"id": "38002", "num": 2, "name": "深淵の零カサディン", "chromas": false}, {"id": "38003", "num": 3, "name": "在りし日のカサディン", "chromas": false}, {"id": "38004", "num": 4, "name": "闇の前兆カサディン", "chromas": false}, {"id": "38005", "num": 5, "name": "宇宙の略奪者カサディン", "chromas": false}, {"id": "38006", "num": 6, "name": "カサディン伯爵", "chromas": true}, {"id": "38014", "num": 14, "name": "ヘクステック カサディン", "chromas": false}, {"id": "38015", "num": 15, "name": "雷刃カサディン", "chromas": false}, {"id": "38024", "num": 24, "name": "龍術師カサディン", "chromas": false}], "lore": "世界の最も昏き闇を炎で切り裂くカサディンは、自身にあまり時間が残されていないことを理解している。案内人として、そして冒険家としてシュリーマ全土を旅してまわった彼はかつて、南部の平和な部族に囲まれて家族を育む人生を選んだ──だがヴォイドが、彼の村を飲み込んだ。彼は復讐を誓い、先で待ち受ける困難に立ち向かうため、いくつもの古代の秘宝と禁じられた技術をかき集めた。そしてついに、荒涼としたイカシアの地へと出発したのだ。“預言者”を僭称するマルザハールを見つけ出すためであれば、彼はいかなる悪鬼のごときヴォイドの創造物であろうと対峙する覚悟でいる。", "blurb": "世界の最も昏き闇を炎で切り裂くカサディンは、自身にあまり時間が残されていないことを理解している。案内人として、そして冒険家としてシュリーマ全土を旅してまわった彼はかつて、南部の平和な部族に囲まれて家族を育む人生を選んだ──だがヴォイドが、彼の村を飲み込んだ。彼は復讐を誓い、先で待ち受ける困難に立ち向かうため、いくつもの古代の秘宝と禁じられた技術をかき集めた。そしてついに、荒涼としたイカシアの地へと出発したのだ。“預言者”を僭称するマルザハールを見つけ出すためであれば、彼はいかなる悪鬼のごときヴォイドの...", "allytips": ["マナや魔力を重視した純正メイジ、あるいはクールダウン短縮や魔法防御を重視した対メイジ特化型など、カサディンは購入するアイテム次第で戦い方を柔軟に変更できる。", "カサディンのアルティメットスキルは様々な使い道があるうえにクールダウンも短いので、積極的に活用すべし。", "「ブルーセンチネル」のステータス強化を活用して、増加していく「リフトウォーク」のマナコストを補おう。"], "enemytips": ["カサディンの攻撃は大半が魔法ダメージだ。このチャンピオンに手を焼いている場合は「マーキュリーブーツ」や「バンシーヴェール」といった魔法耐性強化アイテムを購入しよう。", "「リフトウォーク」は連続して使用するとマナコストが増大していくので、カサディンを追う時はそのことを念頭に置いておこう。", "スロウ効果を付与する「ヴォイドパルス」は、カサディン付近で6回スキルを使用するまで発動できない。カサディンが「ヴォイドパルス」をレベルアップしている場合は特に、近くでスキルを乱用するのは避けたほうがいい。"], "tags": ["Assassin", "Mage"], "partype": "マナ", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "ヴォイドスフィア", "description": "指定した対象にヴォイドエネルギーの球体を発射してダメージを与え、詠唱を妨害する。<br>さらに余ったエネルギーがカサディンを包み込み、短時間魔法ダメージを防ぐシールドを発生させる。", "tooltip": "ヴォイドエネルギーの球体を発射して<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、詠唱を妨害する。また1.5秒間、<shield>{{ totalshield }}の魔法ダメージを防ぐシールド</shield>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "シールド量", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON>herBlade", "name": "ネザーブレード", "description": "自動効果: 通常攻撃に追加魔法ダメージが付与される。<br><br>発動効果: 次の通常攻撃に強烈な追加魔法ダメージが付与され、敵に命中するとマナが回復する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 通常攻撃が<magicDamage>{{ onhitdamage }}の魔法ダメージ</magicDamage>を追加で与える。<br /><br /><spellActive>発動効果:</spellActive> ブレードをチャージし、次の通常攻撃が<magicDamage>{{ activedamage }}の魔法ダメージ</magicDamage>を追加で与え、<scaleMana>減少マナの{{ e1 }}%</scaleMana>を回復する(対象がチャンピオンの場合は回復量が<scaleMana>{{ e4 }}%</scaleMana>に増加)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["発動効果ダメージ", "基本マナ回復率", "チャンピオンに対するマナ回復率"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ e4 }}% -> {{ e4NL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ForcePulse", "name": "ヴォイドパルス", "description": "自身の付近でスキルが使用されると、そのエネルギーを「ヴォイドパルス」にスタックする。スタックが溜まると同時に「ヴォイドパルス」が発動可能になり、使用すると扇状の範囲内の敵にダメージを与え、スロウ効果を付与する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 自身の周囲でスキルが使用されるたびに、<spellName>「ヴォイドパルス」</spellName>のクールダウンが{{ reductionperspellcast }}秒短縮される。<br /><br /><spellActive>発動効果:</spellActive> ヴォイドの波動を解き放ち、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与えて、{{ e3 }}秒間{{ e2 }}%の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RiftWalk", "name": "リフトウォーク", "description": "近くの地点に瞬間移動し、付近の敵ユニットにダメージを与える。連続して使うとマナコストが増加するが、同時に与えるダメージも増えていく。", "tooltip": "近くの地点に瞬間移動し、<magicDamage>{{ basedamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />続けて{{ rstackduration }}秒以内にこのスキルを使用すると、マナコストが倍になるが、<magicDamage>{{ bonusdamage }}の魔法ダメージ</magicDamage>を追加で与える。コストとダメージの増加は最大{{ maxstacks }}回までスタックする。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "1スタックごとのダメージ", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ stackdamage }} -> {{ stackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ヴォイドストーン", "description": "受ける魔法ダメージが減少し、ユニットをすり抜けられるようになる。", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}