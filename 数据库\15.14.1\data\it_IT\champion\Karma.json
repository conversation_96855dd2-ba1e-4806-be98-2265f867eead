{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "<PERSON>rma", "title": "l'illuminata", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "<PERSON><PERSON> del Sole", "chromas": false}, {"id": "43002", "num": 2, "name": "Sakura Karma", "chromas": false}, {"id": "43003", "num": 3, "name": "Karma Tradizionale", "chromas": false}, {"id": "43004", "num": 4, "name": "Karma Ordine del Loto", "chromas": false}, {"id": "43005", "num": 5, "name": "Karma Inquisitrice", "chromas": false}, {"id": "43006", "num": 6, "name": "<PERSON><PERSON> Prodigio Invernale", "chromas": false}, {"id": "43007", "num": 7, "name": "<PERSON>rma Conquistatrice", "chromas": true}, {"id": "43008", "num": 8, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "43019", "num": 19, "name": "Karma Portatrice dell'Alba", "chromas": false}, {"id": "43026", "num": 26, "name": "Karma dell'Odissea", "chromas": false}, {"id": "43027", "num": 27, "name": "Karma in Rovina", "chromas": true}, {"id": "43044", "num": 44, "name": "<PERSON><PERSON> Tranquillità", "chromas": false}, {"id": "43054", "num": 54, "name": "<PERSON><PERSON> Regina delle Fate", "chromas": false}, {"id": "43061", "num": 61, "name": "Karma Infernale", "chromas": false}, {"id": "43070", "num": 70, "name": "<PERSON><PERSON> Fiore spirituale", "chromas": false}], "lore": "Nessun mortale rappresenta le tradizioni spirituali di Ionia meglio di Karma. È la manifestazione di un'anima antica, reincarnatasi più e più volte, che in ogni vita si porta dietro i ricordi di quelle precedenti, brandendo un potere che in pochi riescono a comprendere. Ha fatto del suo meglio per guidare il suo popolo nei recenti tempi di crisi, ma sa che la pace e l'armonia a volte hanno un caro prezzo, tanto per lei quanto per la terra che ama.", "blurb": "Nessun mortale rappresenta le tradizioni spirituali di Ionia meglio di Karma. È la manifestazione di un'anima antica, reincarnatasi più e più volte, che in ogni vita si porta dietro i ricordi di quelle precedenti, brandendo un potere che in pochi...", "allytips": ["Falò ricompensa strategie aggressive. Cerca di colpire i tuoi avversari con abilità e attacchi base per abbassare il tempo di ricarica di Mantra e restare sull'offensiva.", "Quando usi Volontà di ferro, rallenta i tuoi avversari con Fuoco interiore o velocizzati tu con Ispirazione, se hai problemi a stare vicino a un bersaglio.", "Non fare economia con Mantra. Falò è più efficace in scontri di gruppo, rendendo più facile ricaricare più volte Mantra."], "enemytips": ["L'abilità passiva di Karma riduce il tempo di ricarica di Mantra quando colpisce campioni nemici con le sue abilità e gli attacchi base. Impediscile di colpirti.", "La Luce spirituale di Karma esplode, causando danni ad area, nel luogo in cui è posta. Reagisci rapidamente e allontanati dal cerchio per evitare di subire ingenti danni.", "Volontà di ferro è un ottimo strumento di evasione. Tieniti a distanza ed evita di venir bloccato cercando di attaccare."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "Fuoco interiore", "description": "Karma emette una sfera di energia spirituale che esplode e infligge danni quando colpisce le unità nemiche.<br><br>Bonus Mantra: oltre all'esplosione, Mantra aumenta il potere distruttivo di Fuoco interiore, creando un cataclisma che infligge danni dopo un breve ritardo.", "tooltip": "Karma spara una scarica di energia, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> al primo bersaglio colpito e ai nemici vicini, <status>rallentandoli</status> del {{ slowamount*-100 }}% per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSpiritBind", "name": "Volontà di ferro", "description": "Karma crea un legame tra sé e il nemico bersaglio, rive<PERSON><PERSON> e infliggendo danni. Se il legame non viene spezzato, il nemico verrà immobilizzato e danneggiato nuovamente.<br><br>Bonus Mantra: Karma rinforza il legame, curandosi e aumentando la durata dell'immobilizzazione.", "tooltip": "Karma si lega a un campione o a un mostro della giungla, infliggendo <magicDamage>{{ initialdamage }} danni magici</magicDamage> e rivelandolo per {{ tetherduration }} secondo/i. Se il legame non viene interrotto, il bersaglio subisce nuovamente <magicDamage>{{ initialdamage }} danni magici</magicDamage> ed è <status>immobilizzato</status> per {{ rootduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata immobilizzazione", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSolKimShield", "name": "Ispirazione", "description": "Karma evoca uno scudo protettivo che assorbe i danni e aumenta la velocità di movimento dell'alleato protetto.<br><br>Bonus Mantra: oltre allo scudo, viene irradiata dell'energia che infligge danni ai nemici e ispira i campioni alleati.", "tooltip": "<PERSON><PERSON> conferisce a un campione alleato <shield>uno scudo da {{ totalshield }}</shield> per {{ shieldduration }} secondo/i e <speed>{{ movespeed*100 }}% velocità di movimento</speed> per {{ movespeedduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Mantra", "description": "Karma potenzia la sua prossima abilità con un effetto bonus. Mantra è disponibile al livello 1 e non richiede punti abilità.", "tooltip": "Karma potenzia la sua abilità successiva entro 8 secondi.<br /><li><spellName>Fuoco interiore</spellName>: infligge <magicDamage>{{ rqimpactdamage }} danni magici</magicDamage> aggiuntivi e lascia dietro di sé un cerchio di fiamme, che <status>rallenta</status> i nemici e infligge altri <magicDamage>{{ rqfielddamage }} danni magici</magicDamage>.<li><spellName>Volontà di ferro</spellName>: Karma recupera <healing>{{ rwhealamount }} salute mancante</healing> all'inizio e alla fine del legame, <status>immobilizzando</status> il nemico per {{ rwbonusroot }} secondo/i in più quando si spezza.<li><spellName>Ispirazione</spellName>: Karma conferisce uno <shield>scudo da {{ rebonusshield }} aggiuntivi</shield> al suo bersaglio e agli alleati nelle sue vicinanze, che ottengono uno <shield>scudo da {{ rebonusshieldarea }}</shield> e <speed>{{ removespeed*100 }}% velocità di movimento</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Impatto Luce spirituale", "<PERSON><PERSON> cer<PERSON>o <PERSON> spirituale", "Estensione immobilizzazione Rinnovamento", "<PERSON><PERSON> di Sprezzo", "Ricarica"], "effect": ["{{ qbonusdamage }} -> {{ qbonusdamageNL }}", "{{ qdetonationdamage }} -> {{ qdetonationdamageNL }}", "{{ rwbonusroot }} -> {{ rwbonusrootNL }}", "{{ ebonusshield }} -> {{ ebonusshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Le abilità di Karma che infliggono danni riducono la ricarica di Mantra.", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}