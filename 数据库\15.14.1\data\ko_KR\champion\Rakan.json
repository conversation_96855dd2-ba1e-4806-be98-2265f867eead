{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rakan": {"id": "<PERSON><PERSON>", "key": "497", "name": "라칸", "title": "매혹하는 자", "image": {"full": "Rakan.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "497000", "num": 0, "name": "default", "chromas": false}, {"id": "497001", "num": 1, "name": "여명 우주 라칸", "chromas": false}, {"id": "497002", "num": 2, "name": "사랑의 라칸", "chromas": false}, {"id": "497003", "num": 3, "name": "삼성 갤럭시 라칸", "chromas": false}, {"id": "497004", "num": 4, "name": "iG 라칸", "chromas": false}, {"id": "497005", "num": 5, "name": "별 수호자 라칸", "chromas": true}, {"id": "497009", "num": 9, "name": "나무정령 라칸", "chromas": true}, {"id": "497018", "num": 18, "name": "아르카나 라칸", "chromas": true}, {"id": "497027", "num": 27, "name": "깨진 언약 라칸", "chromas": true}, {"id": "497036", "num": 36, "name": "구원받은 별 수호자 라칸", "chromas": false}, {"id": "497037", "num": 37, "name": "용술사 라칸", "chromas": true}, {"id": "497038", "num": 38, "name": "프레스티지 용술사 라칸", "chromas": false}, {"id": "497047", "num": 47, "name": "전투사관학교 라칸", "chromas": true}], "lore": "활달하고 변덕스러우면서도 거부하기 힘든 매혹을 발산하는 라칸은 악명 높은 바스타야 말썽꾼이자 로틀란 부족 역사상 가장 훌륭한 전장의 춤꾼이다. 아이오니아 고원 지대에 사는 사람들에게 라칸이라는 이름은 꽤 오래 전부터 시끌벅적한 축제, 흥이 넘치는 파티, 기존의 규칙을 거부하는 음악과 동일시되고 있다. 하지만 타고난 춤꾼이자 활력 넘치는 방랑자인 라칸이 저항 운동을 하는 자야와 동반자 관계가 되었으며, 자야의 대의에 헌신하고 있다는 사실을 아는 사람은 극히 드물다.", "blurb": "활달하고 변덕스러우면서도 거부하기 힘든 매혹을 발산하는 라칸은 악명 높은 바스타야 말썽꾼이자 로틀란 부족 역사상 가장 훌륭한 전장의 춤꾼이다. 아이오니아 고원 지대에 사는 사람들에게 라칸이라는 이름은 꽤 오래 전부터 시끌벅적한 축제, 흥이 넘치는 파티, 기존의 규칙을 거부하는 음악과 동일시되고 있다. 하지만 타고난 춤꾼이자 활력 넘치는 방랑자인 라칸이 저항 운동을 하는 자야와 동반자 관계가 되었으며, 자야의 대의에 헌신하고 있다는 사실을 아는 사람은...", "allytips": ["라칸은 모든 스킬을 잘 활용하려면 근처에 아군이 함께 있어야 합니다.", "라칸은 이동 속도가 증가하면 도약 속도도 증가합니다. 적을 깜짝 놀라게 해 보세요.", "위험해 보이는 상황도 충분히 즐길 수 있는 게 라칸입니다."], "enemytips": ["라칸의 이동 스킬은 모두 착지 지점을 미리 확인할 수 있으니 이 점을 활용하세요.", "라칸을 상대할 때는 빠르게 군중 제어 스킬을 사용할 수 있는 챔피언이 특히 유리합니다.", "라칸은 주위에 아군이 없으면 기동력이 크게 떨어집니다. 혼자 있을 때를 노리세요."], "tags": ["Support"], "partype": "마나", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 315, "mpperlevel": 50, "movespeed": 335, "armor": 30, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 300, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 8.75, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.635}, "spells": [{"id": "RakanQ", "name": "빛나는 깃털", "description": "마법이 깃든 깃털을 던져 마법 피해를 입힙니다. 챔피언이나 에픽 몬스터에게 깃털을 맞히면 라칸 주변 아군의 체력을 회복시킬 수 있습니다.", "tooltip": "마법이 깃든 깃털을 던져 처음 적중한 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />챔피언 또는 에픽 정글 몬스터를 맞힐 경우 {{ healdelay }}초 뒤 라칸과 주변 아군이 <healing>{{ totalheal }}만큼 체력을 회복</healing>합니다. 라칸이 아군에게 닿으면 회복 효과가 즉시 발동됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RakanQ.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RakanW", "name": "화려한 등장", "description": "목표 지점으로 도약해 착지 시 근처의 적을 모두 공중으로 띄워올립니다.", "tooltip": "라칸이 돌진했다가 공중으로 날아 오르며 적들을 {{ knockupduration }}초 동안 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "마나 소모량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RakanW.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RakanE", "name": "전쟁무도", "description": "아군 챔피언에게 도약해 보호막을 씌웁니다. 몇 초 안에 소모값 없이 다시 사용할 수 있습니다.", "tooltip": "라칸이 아군 챔피언에게 도약해 {{ e3 }}초 동안 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 씌웁니다.<br /><br />라칸은 {{ e2 }}초 안에 이 스킬을 <recast>재사용</recast>할 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [3, 3, 3, 3, 3], [20, 18, 16, 14, 12], [40, 45, 50, 55, 60], [700, 700, 700, 700, 700], [1000, 1000, 1000, 1000, 1000], [1150, 1150, 1150, 1150, 1150], [1250, 1250, 1250, 1250, 1250], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "5", "3", "20/18/16/14/12", "40/45/50/55/60", "700", "1000", "1150", "1250", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "RakanE.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RakanR", "name": "매혹의 질주", "description": "이동 속도가 증가하며 닿은 적을 모두 매혹하고 마법 피해를 입힙니다.", "tooltip": "라칸이 {{ e2 }}초 동안 <speed>{{ e5 }}%의 이동 속도</speed>를 얻습니다. 처음 라칸과 닿는 적은 <magicDamage>{{ totaldamagetooltip }}의 마법 피해</magicDamage>를 입고 {{ e3 }}초 동안 <status>매혹</status>됩니다. 처음 챔피언에게 닿으면 <speed>이동 속도가 {{ e6 }}% 빨라졌다가 점차 감소</speed>합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["매혹 지속시간", "피해량", "재사용 대기시간"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 200, 300], [4, 4, 4], [1, 1.25, 1.5], [0.25, 0.25, 0.25], [75, 75, 75], [150, 150, 150], [150, 150, 150], [130, 110, 90], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "100/200/300", "4", "1/1.25/1.5", "0.25", "75", "150", "150", "130/110/90", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [150, 150, 150], "rangeBurn": "150", "image": {"full": "RakanR.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "요술 망토", "description": "라칸에게 주기적으로 보호막이 생성됩니다.", "image": {"full": "Rakan_P.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}