{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gwen": {"id": "<PERSON>", "key": "887", "name": "<PERSON>", "title": "Die begnadete Näherin", "image": {"full": "Gwen.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "887000", "num": 0, "name": "default", "chromas": false}, {"id": "887001", "num": 1, "name": "Weltraum-Groove-Gwen", "chromas": true}, {"id": "887011", "num": 11, "name": "Konditorei-<PERSON>", "chromas": true}, {"id": "887020", "num": 20, "name": "Soul Fighter Gwen", "chromas": true}, {"id": "887030", "num": 30, "name": "Kriegerkönigin Gwen", "chromas": true}], "lore": "Gwen war einst eine Puppe und wurde durch Magie zum Leben erweckt. Ihre Waffen sind die Werkzeuge, mit denen sie geschaffen wurde. <PERSON><PERSON> ihrer Schritte zeugt von der Liebe ihrer Schöpferin. Gwen hält nichts für selbstverständlich. Sie kontrolliert den geheiligten Nebel – eine uralte Schutzmagie, mit der Gwens Schere, Nadeln und Faden gesegnet wurden. Vieles ist ihr weiterhin neu, aber Gwen ist stets mit ganzem Herzen um das Gute bemüht, das in einer zerrütteten Welt noch verbleibt.", "blurb": "Gwen war einst eine Puppe und wurde durch Magie zum Leben erweckt. Ihre Waffen sind die Werkzeuge, mit denen sie geschaffen wurde. <PERSON>er ihrer Schritte zeugt von der Liebe ihrer Schöpferin. Gwen hält nichts für selbstverständlich. Sie kontrolliert den...", "allytips": ["Gwens <PERSON><PERSON>e verursachen nicht nur zusätzlichen Schaden, sie verstärken auch viele ihrer Fähigkeiten oder setzen sie zurück.", "Gwen kann auch G<PERSON>nern <PERSON> zu<PERSON>ügen, die sich außerhalb des Wirkbereichs von „Geheiligter Nebel“ befinden, vor allem durch die Reichweite ihrer ultimativen Fähigkeit.", "<PERSON><PERSON> von Gwens Fähigkeiten können ihr Passiv auf mehrere Gegner anwenden, also ziele auf Gruppen, um maximalen Schaden und Heilung zu erzielen."], "enemytips": ["Gwens „<PERSON><PERSON><PERSON><PERSON>ter N<PERSON>“ folgt ihr nur e<PERSON>, danach löst er sich auf, wenn sie aus ihm heraustritt.", "<PERSON> muss etwas treffen, um ihre ultimative Fähigkeit erneut zu aktivieren. <PERSON><PERSON><PERSON>, ihr zwischen den Aktivierungen auszuweichen.", "Gwen muss ein paar <PERSON>, um ihren Schaden zu erhöhen, also vers<PERSON>, sie zu überraschen."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 4, "magic": 5, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 330, "mpperlevel": 40, "movespeed": 340, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.69}, "spells": [{"id": "GwenQ", "name": "<PERSON><PERSON><PERSON><PERSON>-schnapp!", "description": "<PERSON> schneidet bis zu 6-mal schnell hintereinander mit ihrer Schere und verursacht in einem kegelförmigen Bereich magischen Schaden. Einheiten im Zentrum erleiden absoluten Schaden und werden bei jedem Schnitt mit Gwens Passiv belegt.", "tooltip": "<spellPassive>Passiv:</spellPassive> Gwen erhält 1&nbsp;Steigerung, wenn sie einen Gegner mit einem Angriff trifft (max. 4, hält {{ buffduration }}&nbsp;Sekunden lang an).<br /><br /><spellActive>Aktiv</spellActive>: Verbraucht Steigerungen. Gwen schneidet einmal mit <magicDamage>{{ miniswipedamage }}&nbsp;magischem Schaden</magicDamage>, schneidet erneut für jede verbrauchte Steigerung und schneidet ein letztes Mal mit <magicDamage>{{ finalswipedamage }}&nbsp;magischem Schaden</magicDamage>.<br /><br />Die Mitte jedes Angriffs wandelt stattdessen {{ truedamageconversion*100 }}&nbsp;% des Schadens in <trueDamage>absoluten Schaden</trueDamage> um und belegt getroffene Gegner mit „<spellName>Tausend Schnitte</spellName>“.<br /><rules><br />Vasallen erleiden {{ minionmod*100 }}&nbsp;% Schaden.<br />Vasallen mit weniger als {{ executethreshold*100 }}&nbsp;% Leben erleiden statt verringertem Schaden {{ executebonus }}&nbsp;% zusätzlichen Schaden.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Trefferschaden des letzten Treffers"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ swipedamagebase }} -> {{ swipedamagebaseNL }}"]}, "maxrank": 5, "cooldown": [6.5, 5.75, 5, 4.25, 3.5], "cooldownBurn": "6.5/5.75/5/4.25/3.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "GwenQ.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> be<PERSON>rt e<PERSON>, der sie vor Gegnern außerhalb des Bereichs schützt. Sie kann nur von G<PERSON>nern anvisiert werden, die den Nebel betreten.", "tooltip": "<PERSON> beschwört den geheiligten Nebel, durch den sie von all<PERSON> (Türme ausgenommen) außerhalb des Bereichs {{ zoneduration }}&nbsp;Sekunden lang nicht anvisiert werden kann. Der Effekt endet, wenn sie den Bereich verlässt. Während sie sich im Nebel aufhält, erhält Gwen {{ totalresists }}&nbsp;<scaleArmor>Rüstung</scaleArmor> und <scaleMR>Magieresistenz</scaleMR>.<br /><br />Gwen kann diese Fähigkeit einmal <recast>reaktivieren</recast>, um den Nebel zu sich zu rufen. Die Fähigkeit wird automatisch <recast>reaktiviert</recast>, wenn Gwen das erste Mal den Bereich verlässt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GwenW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenE", "name": "Scherensprung", "description": "<PERSON> springt über eine kurze Distanz nach vorn und erhält ein paar Sekunden lang Angriffstempo, Angriffsreichweite und magischen Schaden <OnHit>bei Treffern</OnHit>. Wenn sie während dieser Zeit einen Gegner trifft, wird die Abklingzeit dieser Fähigkeit teilweise zurückerstattet. ", "tooltip": "Gwen springt nach vorn und verstärkt ihre Angriffe {{ buffduration }}&nbsp;Sekunden lang.<br /><br />Verstärkte Angriffe erhalten <attackSpeed>{{ bonusattackspeed }}&nbsp;Angriffstempo</attackSpeed>, <magicDamage>{{ onhitdamage }}&nbsp;magischen Schaden</magicDamage> %i:OnHit% <OnHit>bei Treffer</OnHit>, {{ bonusattackrange }} Reichweite und der erste Angriff, der einen Gegner trifft, erstattet {{ cdrefund*100 }}&nbsp;% der Abklingzeit dieser Fähigkeit zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Angriffstempo", "Trefferschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseattackspeed }}&nbsp;% -> {{ baseattackspeedNL }}&nbsp;%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GwenE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenR", "name": "Nadelarbeit", "description": "Gwen wirft eine <PERSON>, die getroffene Gegner verlangsamt, ihnen magischen Schaden zufügt und getroffene Champions mit „Tausend Schnitte“ belegt. <br><br>Diese Fähigkeit kann bis zu zwei weitere Male aktiviert werden. Gwen wirft bei jeder Aktivierung zusätzliche Nadeln und verursacht mehr Schaden. ", "tooltip": "<spellActive>1.&nbsp;Aktivierung:</spellActive> Wirft eine Nadel, die <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht, {{ debuffduration }}&nbsp;Sekunden lang um {{ initialslow*-100 }}&nbsp;% <status>verlangsamt</status> und alle getroffenen Gegner mit „<spellName>Tausend Schnitte</spellName>“ belegt. Gwen kann diese Fähigkeit innerhalb von 6 Sekunden bis zu 2-mal <recast>reaktivieren</recast> ({{ lockouttime }}&nbsp;Sek. Abklingzeit zwischen den Aktivierungen).<br /><br /><recast>2.&nbsp;Aktivierung:</recast> Wirft drei Nadeln, die <magicDamage>{{ totaldamage3 }}&nbsp;magischen Schaden</magicDamage> verursachen.<br /><recast>3.&nbsp;Aktivierung:</recast> Wirft fünf Nadel<PERSON>, die <magicDamage>{{ totaldamage5 }}&nbsp;magischen Schaden</magicDamage> verursachen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "GwenR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Gwens Angriffe verursachen zusätzlichen magischen Schaden abhängig vom Leben des Ziels. Sie wird in Höhe eines Teils des Schadens geheilt, den dieser Effekt an Champions verursacht. ", "image": {"full": "Gwen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}