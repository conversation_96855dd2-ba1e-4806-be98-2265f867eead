{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renata": {"id": "Renata", "key": "888", "name": "Ρενάτα Γκλασκ", "title": "η Βαρόνη των Χη<PERSON>κών", "image": {"full": "Renata.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "888000", "num": 0, "name": "default", "chromas": false}, {"id": "888001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "888010", "num": 10, "name": "Ρενάτα Γκλασκ της Νύχτας Τρόμου", "chromas": true}, {"id": "888020", "num": 20, "name": "Ρενάτα Γκλασκ η Οφθαλμαπάτη", "chromas": true}, {"id": "888021", "num": 21, "name": "Ρενάτα Γκλασκ η Οφθαλμαπάτη - Έκδοση Κύρους", "chromas": false}, {"id": "888031", "num": 31, "name": "Ρενάτα Γκλασκ της Μάσκας του Μαύρου Ρόδου", "chromas": false}], "lore": "Όταν το πατρικό της παραδόθηκε στις φλόγες, η Ρενάτα Γκλασκ έμεινε μόνη της στον κόσμο, με μοναδική της περιουσία το όνομά της και την αλχημική έρευνα των γονιών της. Στις δεκαετίες που ακολούθησαν, έγινε η πλουσιότερη Βαρόνη των Χημικών του Ζάουν, μια μεγιστάνας που κατάφερε να αποκτήσει τρομακτική εξουσία συνδέοντας τα συμφέροντα του ανταγωνισμού με τα δικά της. Όποιος συνεργάζεται μαζί της, πλουτίζει πέρα από κάθε φαντασία. Όποιος της αντιστέκεται, αρ<PERSON><PERSON> <PERSON> γρήγορα το μετανιώνει. Με τον έναν ή τον άλλον τρόπο, όλοι πάνε με τα νερά της.", "blurb": "Όταν το πατρικό της παραδόθηκε στις φλόγες, η Ρενάτα Γκλασκ έμεινε μόνη της στον κόσμο, με μοναδική της περιουσία το όνομά της και την αλχημική έρευνα των γονιών της. Στις δεκαετίες που ακολούθησαν, έγινε η πλουσιότερη Βαρόνη των Χημικών του Ζάουν, μια...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "Μάνα", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 545, "hpperlevel": 94, "mp": 350, "mpperlevel": 50, "movespeed": 330, "armor": 27, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "RenataQ", "name": "Χειραψία", "description": "Η Ρενάτα εξαπολύει ένα βλήμα που ριζώνει τον πρώτο εχθρό που χτυπάει και μπορεί να κάνει νέα χρήση της ικανότητας για να πετάξει τη μονάδα προς μια κατεύθυνση.", "tooltip": "Η Ρενάτα εξαπολύει ένα βλήμα από το χέρι της που <status>Ριζώνει</status> τον πρώτο εχθρό που χτυπάει για {{ rootduration }} δευτ. και προκαλεί <magicDamage>{{ totaldamage }}</magicDamage> <magicDamage>Μαγική Ζημιά</magicDamage>.<br /><br /><recast>Νέα Χρήση:</recast> η Ρενάτα <status>Τραβάει</status> τον εχθρό προς μια κατεύθυνση, προκαλώντας <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> σε εχθρούς πάνω στους οποίους η μονάδα προσγειώνεται και τους <status>Ακινητοποιεί</status> για {{ stunduration }} δευτ. εάν η μονάδα που προσγειώνεται είναι Ήρωας.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά"], "effect": ["{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "RenataQ.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataW", "name": "Διάσωση", "description": "Η Ρενάτα ενισχύει έναν συμμαχικό Ήρωα για να πολεμήσει καλύτερα, καθυστερώντας τον θάνατό του και δίνοντάς του την πιθανότητα να σωθεί εάν καταφέρει να κάνει μια Εξόντωση.", "tooltip": "Η Ρενάτα ενισχύει έναν σύμμαχο Ήρωα, δίνοντάς του <attackSpeed>{{ ascalc }} Ταχύτητα Επίθεσης</attackSpeed> και <speed>{{ mscalc }} Ταχύτητα Κίνησης</speed> προς εχθρούς, που αυξάνεται σε <attackSpeed>{{ finalascalc }} Ταχύτητα Επίθεσης</attackSpeed> και <speed>{{ finalmscalc }} Ταχύτητα Κίνησης</speed> μέσα σε {{ duration }} δευτ. Οι εξοντώσεις Ηρώων ανανεώνουν τη διάρκεια της ενίσχυσης.<br /><br />Εάν ο σύμμαχος θα πέθαινε, τότε επιστρέφει σε πλήρη ζωή, η οποία μειώνεται μέσα σε 3 δευτ.<br /><br />Εάν ο σύμμαχος καταφέρει μια Εξόντωση ενώ αποσυντίθεται, θα επιστρέψει στο <healing>{{ triumphpercent }}% της μέγιστης Ζωής του</healing> και θα σταματήσει να αποσυντίθεται.<br /><br /><rules>Ο θάνατος του Ήρωα μπορεί να καθυστερήσει μέσω Θεραπείας ή αντίστοιχων επιδράσεων όσο βρίσκεται σε κατάσταση αποσύνθεσης, αλλά δεν μπορεί να αποτραπεί, εκτός αν ο Ήρωας καταφέρει να κάνει μια Εξόντωση. Οι Ήρωες μπορούν να καθυστερήσουν τον θάνατό τους μόνο μία φορά.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ταχύτητα Επίθεσης", "Ταχύτητα Κίνησης", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ bonusattackspeed }}% -> {{ bonusattackspeedNL }}%", "{{ bonusmovespeed }}% -> {{ bonusmovespeedNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 27, 26, 25, 24], "cooldownBurn": "28/27/26/25/24", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataE", "name": "Πρόγραμμα αφοσίωσης", "description": "Η Ρενάτα εξαπολύει ένα ζευγάρι χημοτεχνικών βλημάτων που δίνουν ασπίδα στους συμμάχους, εν<PERSON> προκαλούν ζημιά και επιβραδύνουν τους εχθρούς που χτυπούν.", "tooltip": "Η Ρενάτα εξαπολύει ένα ζευγάρι χημοτεχνικών βλημάτων που προκαλούν <magicDamage>{{ totaldamage }} Μαγική Ζημιά</magicDamage> και <status>Επιβραδύνουν</status> τους κοντινούς εχθρούς και τους εχθρούς που χτυπά κατά 30% για {{ slowduration }} δευτ. Οι σύμμαχοι που χτυπά αποκτούν <shield>{{ shieldcalc }} Ασπίδα</shield> για {{ shieldduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Αντοχή Ασπίδας", "Κόστος @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldvalue }} -> {{ shieldvalueNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "RenataE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RenataR", "name": "Εχθρική εξαγορά", "description": "Η Ρενάτα εξαπολύει ένα κύμα χημικών, με αποτέλεσμα όλοι οι εχθροί που χτυπάει να γίνονται Αφιονισμένοι.", "tooltip": "Η Ρενάτα εξαπολύει ένα κύμα από χημικά, με αποτέλεσμα όλοι οι εχθροί που χτυπάει να γίνονται <status>Αφιονισμένοι</status> για {{ berserkduration }} δευτ. και να επιτίθενται σε όποια μονάδα βρίσκεται πιο κοντά τους, δίνοντας προτεραιότητα στους συμμάχους τους.<br /><br />Όσο είναι <status>Αφιονισμένοι</status>, οι εχθροί αποκτούν <attackSpeed>{{ bonusattackspeed*100 }}% Ταχύτητα Επίθεσης</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Διάρκεια"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ berserkduration }} -> {{ berserkdurationNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2000, 2000], "rangeBurn": "2000", "image": {"full": "RenataR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Αξιοποίηση", "description": "Οι βασικές επιθέσεις της Ρενάτα προκαλούν μπόνους Ζημιά και σημαδεύουν τους εχθρούς. Οι σύμμαχοι της Ρενάτα μπορούν να κάνουν ζημιά σε σημαδεμένους εχθρούς για να προκαλέσουν μπόνους ζημιά.", "image": {"full": "Renata_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}