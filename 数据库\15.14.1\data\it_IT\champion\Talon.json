{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Talon": {"id": "Talon", "key": "91", "name": "Talon", "title": "l'ombra della lama", "image": {"full": "Talon.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "91000", "num": 0, "name": "default", "chromas": false}, {"id": "91001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "91002", "num": 2, "name": "Talon dell'Élite Cremisi", "chromas": false}, {"id": "91003", "num": 3, "name": "<PERSON>", "chromas": true}, {"id": "91004", "num": 4, "name": "Talon SSW", "chromas": false}, {"id": "91005", "num": 5, "name": "Talon Luna di Sangue", "chromas": false}, {"id": "91012", "num": 12, "name": "Talon Spada Eterna", "chromas": true}, {"id": "91020", "num": 20, "name": "Talon Boscocupo", "chromas": true}, {"id": "91029", "num": 29, "name": "<PERSON>", "chromas": true}, {"id": "91038", "num": 38, "name": "Talon Mezzogiorno di Fuoco", "chromas": true}, {"id": "91039", "num": 39, "name": "Talon Mezzogiorno di Fuoco (edizione prestigio)", "chromas": false}, {"id": "91049", "num": 49, "name": "Talon Agguato ferino", "chromas": true}, {"id": "91059", "num": 59, "name": "Talon Ordalia Gloriosa", "chromas": false}], "lore": "Talon è la lama dell'oscurità, uno spietato assassino capace di colpire senza preavviso e fuggire prima che venga dato l'allarme. Si è fatto una reputazione per le violente strade di Noxus, dove per sopravvivere è stato costretto a rubare, combattere e uccidere. Adottato dalla famigerata famiglia Du Couteau, porta avanti la sua attività omicida al servizio dell'impero assassinando capi, capitani ed eroi nemici... oltre a ogni noxiano abbastanza sciocco da scatenare l'ira dei suoi padroni.", "blurb": "Talon è la lama dell'oscurità, uno spietato assassino capace di colpire senza preavviso e fuggire prima che venga dato l'allarme. Si è fatto una reputazione per le violente strade di Noxus, dove per sopravvivere è stato costretto a rubare, combattere e...", "allytips": ["Usa Via dell'assassino per arrivare alle spalle del nemico e prepararti all'attacco in mischia di Diplomazia di Noxus.", "Assalto delle ombre è un ottimo strumento di fuga, ma può essere usato anche in modo aggressivo per assalire un gruppo.", "Ricordati di scegliere un bersaglio prima di iniziare un combattimento. Se concentri tutte le abilità di Talon su un solo bersaglio otterrai degli enormi risultati; al contrario dividerli su più bersagli ti renderà impotente."], "enemytips": ["Gli attacchi di Talon infliggono tutti danni fisici. Compra un'armatura in fretta per neutralizzare i suoi danni a raffica.", "Talon dipende molto da Assalto delle ombre per fuggire da un combattimento. Quando non l'ha disponibile, è molto vulnerabile.", "Talon non ha rivali nella sua abilità di muoversi nella mappa. Tieni traccia della sua posizione per obbligarlo a stare in corsia, avanzando in modo aggressivo."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 658, "hpperlevel": 109, "mp": 400, "mpperlevel": 37, "movespeed": 335, "armor": 30, "armorperlevel": 4.7, "spellblock": 36, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 7.6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.9, "attackspeed": 0.625}, "spells": [{"id": "TalonQ", "name": "Diplomazia di Noxus", "description": "Talon trafigge l'unità bersaglio. Se si trova a gittata di corpo a corpo, questo attacco infligge danni critici. Se si trova al di fuori della gittata di corpo a corpo, Talon balza verso il bersaglio prima di colpirlo. Talon recupera un po' di salute e ricarica se quest'abilità uccide il bersaglio.", "tooltip": "Talon balza addosso a un bersaglio e gli infligge <physicalDamage>{{ leapdamage }} danni fisici</physicalDamage>. Se usata in corpo a corpo, questa abilità infligge invece colpi critici per <physicalDamage>{{ criticaldamage }} danni fisici</physicalDamage>.<br /><br />Se Talon uccide il bersaglio con questa abilità, recupera <healing>{{ totalhealing }} salute</healing> e ripristina un {{ cooldownrefund*100 }}% della sua ricarica.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "TalonQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TalonW", "name": "Lame rotanti", "description": "Talon lancia una raffica di pugnali che ritornano a lui, infliggendo danni fisici ogni volta che passano attraverso un nemico. Le lame di ritorno infliggono danni bonus e rallentano le unità colpite.", "tooltip": "Talon lancia una raffica di lame che infliggono <physicalDamage>{{ totalinitialdamage }} danni fisici</physicalDamage>. Successivamente, le lame tornano da lui, infliggendo <physicalDamage>{{ totalreturndamage }} danni fisici</physicalDamage> e <status>rallentando</status> i nemici del {{ movespeedslow*100 }}% per {{ slowduration }} secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON>", "<PERSON><PERSON> al ritorno", "Rallentamento", "Ricarica"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ initialbasedamage }} -> {{ initialbasedamageNL }}", "{{ returnbasedamage }} -> {{ returnbasedamageNL }}", "{{ movespeedslow*100.000000 }}% -> {{ movespeedslownl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TalonW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TalonE", "name": "Via dell'assassino", "description": "Talon supera ogni terreno o struttura, fino a una distanza massima. Questa abilità ha una ricarica breve, ma manda il terreno usato in una lunga ricarica.", "tooltip": "Talon supera con un balzo il terreno o la struttura più vicini. Talon non può oltrepassare la stessa parte di terreno più di una volta ogni {{ wallcd }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica terreno"], "effect": ["{{ wallcd }} -> {{ wallcdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [625, 625, 625, 625, 625], [1250, 1250, 1250, 1250, 1250], [2, 2, 2, 2, 2], [160, 135, 110, 85, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "625", "1250", "2", "160/135/110/85/60", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "TalonE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "TalonR", "name": "Assalto delle ombre", "description": "Talon disperde un anello di lame e diventa invisibile guadagnando della velocità di movimento bonus. Quando Talon emerge dall'Invisibilità, le lame convergono verso la sua posizione. Ogni volta che la lama si muove, Assalto delle ombre infligge danni fisici ai nemici colpiti da almeno una lama.", "tooltip": "Talon lancia un anello di lame che infliggono <physicalDamage>{{ damage }} danni fisici</physicalDamage>, ottiene il <speed>{{ movespeed*100 }}% di velocità di movimento</speed> e diventa <keywordStealth>invisibile</keywordStealth> per {{ duration }} secondi. Quando l'<keywordStealth>invisibilità</keywordStealth> termina, le lame tornano da Talon, infliggendo di nuovo <physicalDamage>{{ damage }} danni fisici</physicalDamage>.<br /><br />Se Talon annulla l'<keywordStealth>invisibilità</keywordStealth> con un attacco o con <spellName>Diplomazia di Noxus</spellName>, le lame ritornano invece dal bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità di movimento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TalonR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Punta di lama", "description": "Le abilità di Talon infliggono ferite su campioni e mostri grandi, accumulandosi fino a un massimo di 3 volte. Quando Talon attacca un campione con 3 cariche di ferita, questo subisce pesanti danni da emorragia nel tempo.", "image": {"full": "TalonP.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}