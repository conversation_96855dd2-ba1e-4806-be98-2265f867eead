{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"AurelionSol": {"id": "AurelionSol", "key": "136", "name": "オレリオン・ソル", "title": "星を創りし者", "image": {"full": "AurelionSol.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "136000", "num": 0, "name": "default", "chromas": false}, {"id": "136001", "num": 1, "name": "死灰の化身オレリオン・ソル", "chromas": false}, {"id": "136002", "num": 2, "name": "メカリオン・ソル", "chromas": true}, {"id": "136011", "num": 11, "name": "豪嵐龍オレリオン・ソル", "chromas": false}, {"id": "136021", "num": 21, "name": "墨影のオレリオン・ソル", "chromas": false}, {"id": "136031", "num": 31, "name": "磁器の守護者オレリオン・ソル", "chromas": false}], "lore": "かつて何もない巨大な虚空であった宇宙に己が生み出した煌めく驚異を散りばめ、その恩寵を授けたオレリオン・ソル。しかし彼は今、領土拡大を目論む帝国の罠にかけられ、命ぜられるがままにその凄まじい力を振りかざしている。星を創るという本来の神聖なる役目への回帰を願うオレリオン・ソルは、必要とあらば天空から星をも引き寄せる。自由を再びその手に取り戻すために。", "blurb": "かつて何もない巨大な虚空であった宇宙に己が生み出した煌めく驚異を散りばめ、その恩寵を授けたオレリオン・ソル。しかし彼は今、領土拡大を目論む帝国の罠にかけられ、命ぜられるがままにその凄まじい力を振りかざしている。星を創るという本来の神聖なる役目への回帰を願うオレリオン・ソルは、必要とあらば天空から星をも引き寄せる。自由を再びその手に取り戻すために。", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 90, "mp": 530, "mpperlevel": 40, "movespeed": 335, "armor": 22, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "AurelionSolQ", "name": "星炎の息吹", "description": "数秒間詠唱して炎を吹き出し、最初に命中した敵にダメージを与え、その周囲の敵にはそれよりも少ないダメージを与える。敵に直接炎を吹きかけている間は、毎秒追加ダメージを与える。このダメージは獲得した「星屑」の数に応じて増加する。このスキルの対象がチャンピオンだった場合、「星屑」を獲得できる。", "tooltip": "星炎を最大{{ maxchannelduration }}秒間吹き出して、最初に命中した敵に毎秒<magicDamage>{{ damagepersecond }}の魔法ダメージ</magicDamage>を与え、周囲の敵にはその{{ aoemodifier*100 }}%のダメージを与える。<br /><br />同じ敵に星炎を当て続けると、1秒ごとに爆発を引き起こして<magicDamage>{{ burstdamage }}の魔法ダメージ</magicDamage> + <magicDamage>最大体力の{{ burstbonustruedamagetochamps }}にあたる魔法ダメージ</magicDamage>を与え、さらに対象がチャンピオンだった場合は<span class=\"color3458eb\">「星屑」を{{ qmassstolen }}個</span>獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "毎秒ダメージ", "爆発ダメージ", "最大詠唱時間"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ rankdamagepersecond }} -> {{ rankdamagepersecondNL }}", "{{ rankburstdamage }} -> {{ rankburstdamageNL }}", "{{ maxchannelduration }} -> {{ maxchanneldurationNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolQ.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "毎秒{{ manacostpersecond }}マナ"}, {"id": "AurelionSolW", "name": "天空への飛翔", "description": "飛翔して、指定方向に地形を超えて移動する。飛翔している間も、他のスキルを発動できる。また、飛翔中は「星炎の呼吸」のクールダウンおよび最大詠唱時間がなくなり、与えるダメージが増加する。<br><br>自身がダメージを与えた敵チャンピオンがその直後に倒されるたびに、このスキルの残りクールダウンが短縮される。<br><br>獲得した「星屑」の数に応じて、このスキルの最大射程が増加する。", "tooltip": "指定方向に飛翔する。飛翔中は<spellName>「星炎の息吹」</spellName>のクールダウンと最大詠唱時間がなくなり、基本ダメージが{{ truedamagebonus*100 }}%増加する。<br /><br />自身がダメージを与えたチャンピオンから{{ resetwindow }}秒以内にキルまたはアシストを獲得すると、このスキルのクールダウンが{{ tooltiptakedowncooldownmultiplier }}%短縮される。<br /><br /><recast>再発動:</recast> 飛翔を早めに終了する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["割合に応じた追加魔法ダメージ", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ truedamagebonus*100.000000 }}% -> {{ truedamagebonusnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cd }} -> {{ cdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "AurelionSolW.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AurelionSolE", "name": "特異点", "description": "ブラックホールを召喚して敵にダメージを与え、ゆっくりとその中心に向かって引き寄せる。敵がブラックホールの範囲内で倒されるたびに、「星屑」を獲得する。また、敵チャンピオンを範囲内に捕らえている間も、毎秒「星屑」を獲得できる。ブラックホールの中心部は、体力が最大体力の一定割合を下回っている敵に、とどめを刺すことができる。「星屑」の数に応じて「特異点」の効果範囲が増加し、とどめを刺せる体力割合の基準値が上昇する。", "tooltip": "ブラックホールを召喚して{{ duration }}秒間、敵に毎秒<magicDamage>{{ damagepersecond }}の魔法ダメージ</magicDamage>を与え、その中心に向かって<status>引き付ける</status>。中心部にいる敵は体力が<scaleHealth>最大体力の{{ currentexecutionthreshold }}%</scaleHealth>を下回っていると、即座に倒される。<br /><br />ブラックホールの範囲内で敵が倒されるたびに<span class=\"color3458eb\">「星屑」</span>を獲得する。また、範囲内に敵チャンピオンを捕らえている間も、毎秒<span class=\"color3458eb\">「星屑」</span>を獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolE.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AurelionSolR", "name": "星の邂逅 / 崩れ落つ天穹", "description": "星の邂逅: 地上に星を降らせ、その衝撃で敵に魔法ダメージを与えて、スタンさせる。また、命中した敵チャンピオン1体ごとに「星屑」を獲得する。「星屑」を一定数獲得すると、次に使用する「星の邂逅」が「崩れ落つ天穹」に変化する。<br><br>崩れ落つ天穹: 天空から巨大な星を引き寄せる。この星は落下時の効果範囲とダメージが増加しており、敵をスタンではなくノックアップさせる。また、落下時の効果範囲の端から衝撃波が広がり、命中した敵にダメージとスロウ効果を与える。獲得した「星屑」の数に応じて、「星の邂逅」と「崩れ落つ天穹」は落下時の効果範囲が増加する。", "tooltip": "天空から星をひとつ引き寄せ、地上に衝突させて<magicDamage>{{ maxdamagetooltip }}の魔法ダメージ</magicDamage>を与え、敵を{{ stunduration }}秒間<status>スタン</status>させる。また、命中したチャンピオン1体ごとに<span class=\"color3458eb\">「星屑」を{{ massstolen }}個</span>獲得する。<br /><br /><span class=\"color3458eb\">「星屑」を{{ calamitystacks }}個</span>集めると、次に使用する<spellName>「星の邂逅」</spellName>が<spellName>「崩れ落つ天穹」</spellName>に変化する。<br /><br /><spellName>崩れ落つ天穹</spellName>: 宇宙から激しい星の怒りを降らせて、広範囲に<magicDamage>{{ r2damage }}の魔法ダメージ</magicDamage>を与え、命中した敵を{{ stunduration }}秒間<status>ノックアップ</status>させる。さらに、巨大な衝撃波を発生させて、チャンピオンとエピックモンスターに<magicDamage>{{ shockwavedamage }}の魔法ダメージ</magicDamage>を与え、命中したすべての敵に1秒間{{ shockwaveslow*100 }}%の<status>スロウ効果</status>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ", "強化ダメージ", "衝撃波ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*1.250000 }} -> {{ basedamagenl*1.250000 }}", "{{ basedamage*0.900000 }} -> {{ basedamagenl*0.900000 }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AurelionSolR.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "宇宙の創造者", "description": "攻撃スキルを敵に命中させると、<font color='#3458eb'>「星屑」</font>のスタックを獲得できる。このスタックは各スキルを恒久的に強化する。 ", "image": {"full": "AurelionSolP.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}