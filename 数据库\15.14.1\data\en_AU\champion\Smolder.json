{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "<PERSON><PERSON>lder", "title": "the Fiery Fledgling", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "Heavenscale Smolder", "chromas": true}], "lore": "Hidden amongst the craggy cliffs of the Noxian frontier, under the watchful eyes of his mother, a young dragon is learning what it means to be heir to the Camavoran imperial dragon lineage. Playful and eager to grow up, <PERSON><PERSON><PERSON> looks for any excuse to practice his burgeoning abilities. Though he's still a fledgling, his skills are nothing to sneeze at, easily setting fire to anything that burns.", "blurb": "Hidden amongst the craggy cliffs of the Noxian frontier, under the watchful eyes of his mother, a young dragon is learning what it means to be heir to the Camavoran imperial dragon lineage. Playful and eager to grow up, <PERSON><PERSON><PERSON> looks for any excuse to...", "allytips": ["<PERSON><PERSON><PERSON> has a vulnerable early game. Focus on passive stacking and staying alive to become a strong dragon later!", "<PERSON><PERSON><PERSON> relies on his team to keep him safe. Try to find allies who can help you against enemy threats.", "Smolder can do a lot of damage to areas of enemies. Look for attack opportunities where the enemies are close together."], "enemytips": ["<PERSON><PERSON><PERSON> relies on his team for safety. Attack him when his team can't save him.", "Don't group up when facing <PERSON><PERSON><PERSON>!", "<PERSON><PERSON><PERSON> is very vulnerable in the early stages. Try to take advantage of his weakness before he can learn how to be a dragon!", "Smolders flight can be interrupted by hard CC and impacted by slows."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "Super Scorcher Breath", "description": "<PERSON><PERSON><PERSON> breathes fire on an enemy. As he gains more stacks, this ability becomes more powerful.", "tooltip": "Smolder belches flame, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_qdamageincrease }} magic damage</magicDamage>. If the target dies, Smolder refunds <scaleMana>{{ manarestore }} Mana</scaleMana>, once per cast.<br /><br />Based on stacks of <spellName>Dragon Practice</spellName>, this Ability evolves to gain the following effects:<li><keywordMajor>{{ stacktier1 }} Stacks</keywordMajor>: Damages all enemies surrounding the target.<li><keywordMajor>{{ stacktier2 }} Stacks</keywordMajor>: Sends <spellName>{{ tier2_numberofblowback }}</spellName> explosions beyond the target that deal {{ tier2_blowbackpercentagedamage }}% of this Ability's damage.<li><keywordMajor>{{ stacktier3 }} Stacks</keywordMajor>: Burns the target, dealing <trueDamage>{{ tier3_burn }} max Health true damage</trueDamage> over {{ tier3_dotlength }} seconds. Enemy champions that drop below <trueDamage>{{ tier3_executethreshold }}</trueDamage> total health while burning are killed instantly.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Base Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderW", "name": "Achooo!", "description": "<PERSON><PERSON><PERSON> lets out an adorable flaming sneeze that explodes when hitting enemy champions.", "tooltip": "<PERSON><PERSON><PERSON> lets out an adorable flaming sneeze, dealing <physicalDamage>{{ initialdamage }} physical damage</physicalDamage> and <status>Slowing</status> by {{ slowamount*100 }}% for {{ slowduration }} seconds.<br /><br />Hitting champions causes an explosion, dealing <physicalDamage>{{ explosiondamage }} physical</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_wdamageincrease }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Explosion Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderE", "name": "Flap, Flap, Flap", "description": "<PERSON><PERSON><PERSON> takes flight ignoring terrain and bombarding the lowest health enemy.", "tooltip": "<PERSON>molder takes flight, gaining <speed>{{ movespeed*100 }}% Move Speed</speed> and ignoring terrain for {{ duration }} seconds.<br /><br />While flying, <PERSON><PERSON><PERSON> bombards the lowest Health enemy <spellName>{{ totalnumberofattacks }}</spellName> (rounded down) times for <physicalDamage>{{ damageperhit }} physical damage</physicalDamage> + <magicDamage>{{ spell.smolderp:ebonusdamage }} magic damage</magicDamage> per hit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Base Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SmolderR", "name": "MMOOOMMMM!", "description": "<PERSON><PERSON><PERSON> calls his mom to breath fire from above, dealing extra damage and slowing enemies in the center of her fire.", "tooltip": "<PERSON><PERSON><PERSON>'s mom breathes fire from above, dealing <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>. Enemies in the center take <physicalDamage>{{ tooltiponly_totalsweetspotdamage }} physical damage</physicalDamage> instead and are <status>Slowed</status> by {{ slowamount*100 }}% for {{ slowduration }} seconds.<br /><br />Smolder's mom heals her son for <healing>{{ momhealcalc }} Health</healing> if she hits him.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Heal", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dragon Practice", "description": "Hitting champions with Abilities and killing enemies with Super Scorcher Breath grants a stack of Dragon Practice. Stacks increase the damage of Smolders basic Abilities.", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}