{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Velkoz": {"id": "Velkoz", "key": "161", "name": "Vel'Koz", "title": "l'occhio del Vuoto", "image": {"full": "Velkoz.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "161000", "num": 0, "name": "default", "chromas": false}, {"id": "161001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "161002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "161003", "num": 3, "name": "Sicuramente non Vel'Koz", "chromas": false}, {"id": "161004", "num": 4, "name": "Vel'Koz Infernale", "chromas": true}, {"id": "161011", "num": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "161020", "num": 20, "name": "Vel'KoBzzz", "chromas": true}], "lore": "Non si sa se Vel'Koz sia stata la prima creatura del Vuoto a emergere a Runeterra, ma di certo nessuna ha raggiunto il suo livello di coscienza crudele e calcolatrice. Mentre i suoi simili divorano o contaminano tutto ciò che li circonda, lui cerca invece di analizzare e studiare il reame fisico (e le strane, bellicose creature che lo abitano) per trovare punti deboli dei quali il Vuoto possa approfittare. Ma Vel'Koz non è un semplice osservatore passivo; affronta le minacce con un plasma letale, o distruggendo il tessuto stesso del mondo.", "blurb": "Non si sa se V<PERSON>'Koz sia stata la prima creatura del Vuoto a emergere a Runeterra, ma di certo nessuna ha raggiunto il suo livello di coscienza crudele e calcolatrice. Mentre i suoi simili divorano o contaminano tutto ciò che li circonda, lui cerca...", "allytips": ["In corsia, usa Frattura sul Vuoto per uccidere i minion accumulando cariche di Scomposizione organica sul nemico. Poi puoi proseguire con le tue altre abilità.", "Lanciando Fissione al plasma in diagonale e dividendola alla distanza massima potrai colpire i nemici che si trovano fuori dalla gittata del proiettile iniziale, ma riuscirci sarà più difficile.", "Usa Raggio disintegratore con cautela. Molti campioni possiedono abilità in grado di interrompere l'abilità."], "enemytips": ["Vel'<PERSON><PERSON> è molto pericoloso, se lasciato da solo in uno scontro. Cerca di concentrarti su di lui al più presto.", "V<PERSON>'<PERSON><PERSON> ha <PERSON>a mobilità ed è vulnerabile agli assalti.", "Raggio disintegratore può essere interrotto da stordimenti, silenzi e lanci in aria."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 102, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 22, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1416, "attackspeedperlevel": 1.59, "attackspeed": 0.643}, "spells": [{"id": "VelkozQ", "name": "Fissione al plasma", "description": "Vel'Ko<PERSON> spara un colpo di plasma che si divide in due quando colpisce un nemico o quando viene riattivato. Il colpo rallenta e danneggia sul colpo.", "tooltip": "Vel'Koz spara un colpo al plasma che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallenta</status> i nemici di un {{ slowamount*100 }}%, che decresce nell'arco di {{ slowduration }} secondo/i. Al termine della sua gittata, se colpisce un bersaglio o se l'abilità viene <recast>rilanciata</recast>, il colpo si divide e spara due nuovi dardi con un'angolazione di 90 gradi.<br /><br />Uccidere un'unità con questa abilità ripristina <scaleMana>{{ tooltipmanarefund }} mana</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> totali", "<PERSON><PERSON> rallenta<PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "VelkozQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VelkozW", "name": "Frattura sul Vuoto", "description": "Vel'<PERSON><PERSON> apre uno strappo verso il Vuoto che infligge una raffica iniziale di danni, per poi esplodere con una seconda raffica dopo un periodo di tempo.", "tooltip": "V<PERSON>'<PERSON><PERSON> apre uno strappo verso il vuoto che infligge <magicDamage>{{ initialdamage }} danni magici</magicDamage>. Lo strappo esplode e infligge <magicDamage>{{ secondarydamage }} danni magici</magicDamage>.<br /><br />Questa abilità ha 2 cariche ({{ ammorechargetime }} secondo di ricarica).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Tempo di ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ baseinitialdamage }} -> {{ baseinitialdamageNL }}", "{{ basesecondarydamage }} -> {{ basesecondarydamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [45, 75, 105, 135, 165], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0.25, 0.25, 0.25, 0.25, 0.25], [0.5, 0.5, 0.5, 0.5, 0.5], [88, 88, 88, 88, 88], [500, 500, 500, 500, 500], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "45/75/105/135/165", "100", "0", "0.25", "0.5", "88", "500", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "VelkozW.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VelkozE", "name": "Distruzione tettonica", "description": "Vel'Koz fa esplodere un'area, lanciando in aria i nemici e respingendo lievemente i nemici vicini.", "tooltip": "Vel'Koz distrugge un'area vicina, facendola esplodere, <status>lanciando in aria</status> i nemici per {{ stunduration }} secondi e infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>. I nemici vicini a Vel'Ko<PERSON> vengono <status>respinti</status> invece che <status>lanciati in aria</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "VelkozE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VelkozR", "name": "Raggio disintegratore", "description": "Vel'Koz scatena un raggio canalizzato che segue il cursore per 2,5 secondi, infliggendo danni magici. Scomposizione organica analizza i campioni nemici facendo invece subire loro danni puri.", "tooltip": "Vel'Koz canalizza un raggio di energia che segue il cursore, infliggendo un totale di <magicDamage>{{ totaldamage }} danni magici</magicDamage> nell'arco di 2,5 secondi e <status>rallentando</status> i nemici del {{ e3 }}%. Questa abilità infligge invece <trueDamage>danni puri</trueDamage> ai nemici che hanno recentemente subito danni da <spellName>Scomposizione organica</spellName>.<br /><br />Mentre sono all'interno del raggio, gli avversari ottengono periodicamente cariche di <keywordMajor>scomposizione</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> totali", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [450, 625, 800], [7, 7, 7], [20, 20, 20], [40, 40, 40], [175, 175, 175], [7, 7, 7], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "450/625/800", "7", "20", "40", "175", "7", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1575, 1575, 1575], "rangeBurn": "1575", "image": {"full": "VelkozR.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Scomposizione organica", "description": "Le abilità di Vel'Koz applicano <keywordName>Scomposizione organica</keywordName> ai nemici sul colpo. Se vengono accumulate 3 cariche, il nemico subirà una raffica di danni puri.", "image": {"full": "VelKoz_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}