{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mel": {"id": "<PERSON>", "key": "800", "name": "멜", "title": "영혼의 반향", "image": {"full": "Mel.png", "sprite": "champion2.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "800000", "num": 0, "name": "default", "chromas": false}, {"id": "800001", "num": 1, "name": "아케인 의원 멜", "chromas": true}], "lore": "멜 메다르다는 한때 녹서스에서 손꼽히는 세도가 중 하나였던 메다르다 가문의 후계자로 추정된다. 겉보기에는 우아한 귀족이지만 그 이면에는 자신이 만나는 모든 사람의 모든 것을 파악하는 노련한 정치가가 있다. 수수께끼에 싸인 검은 장미단과 조우한 후, 멜은 어머니의 기만이 얼마나 깊은지 알아냈고 처음으로 자신의 통제력을 벗어난 상황에 직면했다. 각성하여 마법의 힘을 손에 넣은 멜은 답을 찾아 고향으로 향했다. 멜 내면의 빛을 길들이고자 하는 이는 여전히 많지만 멜의 영혼이 의지를 꺾는 일은 결코 없을 것이다.", "blurb": "멜 메다르다는 한때 녹서스에서 손꼽히는 세도가 중 하나였던 메다르다 가문의 후계자로 추정된다. 겉보기에는 우아한 귀족이지만 그 이면에는 자신이 만나는 모든 사람의 모든 것을 파악하는 노련한 정치가가 있다. 수수께끼에 싸인 검은 장미단과 조우한 후, 멜은 어머니의 기만이 얼마나 깊은지 알아냈고 처음으로 자신의 통제력을 벗어난 상황에 직면했다. 각성하여 마법의 힘을 손에 넣은 멜은 답을 찾아 고향으로 향했다. 멜 내면의 빛을 길들이고자 하는 이는 여전히...", "allytips": ["멜은 강력한 주문을 비롯한 적 투사체를 반사할 수 있습니다. 멜에게 강력한 투사체를 날리려면 멜이 반박을 사용할 때까지 기다리세요.", "멜의 공격이 적중할수록 압도 중첩이 쌓입니다. 체력이 너무 낮아지면 멜의 다음 공격으로 죽을 수 있으니 압도 중첩이 줄어들 때까지 잠시 물러나세요."], "enemytips": ["멜은 강력한 주문을 비롯한 적 투사체를 반사할 수 있습니다. 멜에게 강력한 투사체를 날리려면 멜이 반박을 사용할 때까지 기다리세요.", "멜의 공격이 적중할수록 압도 중첩이 쌓입니다. 체력이 너무 낮아지면 멜의 다음 공격으로 죽을 수 있으니 압도 중첩이 줄어들 때까지 잠시 물러나세요."], "tags": ["Mage", "Support"], "partype": "마나", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 93, "mp": 480, "mpperlevel": 28, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.9, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "MelQ", "name": "빛의 세례", "description": "멜이 대상 지점 주위에 폭발하는 투사체를 퍼부어 해당 지역 안에 있는 적에게 반복해서 피해를 입힙니다.", "tooltip": "멜이 대상 지점 주위에 폭발하는 투사체 {{ explosioncount }}개를 퍼붓습니다.<br /><br />각 폭발은 <magicDamage>{{ totalexplosiondamage }}의 마법 피해</magicDamage>를 입히며 모두 합쳐 최대 <magicDamage>{{ alldamagehit }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["폭발 피해량", "폭발 횟수", "재사용 대기시간", "마나 소모량"], "effect": ["{{ explosiondamage }} -> {{ explosiondamageNL }}", "{{ explosioncount }} -> {{ explosioncountNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "MelQ.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MelW", "name": "반박", "description": "멜이 자신을 방어막으로 감싸 적 투사체를 공격자에게 반사하고 자신이 받는 피해를 막으며 이동 속도를 얻습니다.", "tooltip": "멜이 자신을 방어막으로 감싸 적 챔피언이 날리는 투사체를 반사하고 자신이 받는 피해를 막으며 {{ duration }}초 동안 <speed>이동 속도가 {{ movespeed*100 }}% 증가했다가 점차 감소</speed>합니다.<br /><br />반사된 투사체는 <magicDamage>최초 피해량의 {{ damagepercent }}에 해당하는 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["반사 피해량", "재사용 대기시간", "마나 소모량"], "effect": ["{{ basedamagepercent*100.000000 }}% -> {{ basedamagepercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [35, 32, 29, 26, 23], "cooldownBurn": "35/32/29/26/23", "cost": [80, 60, 40, 20, 0], "costBurn": "80/60/40/20/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "MelW.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MelE", "name": "태양 올가미", "description": "멜이 전방으로 찬란한 구체를 발사하여 중심에 있는 적을 속박하는 한편 그 주변에 있는 적을 둔화시키며 지속 피해를 입힙니다.", "tooltip": "멜이 찬란한 구체를 발사하여 중심에 있는 적을 {{ rootduration }}초 동안 <status>속박</status>하고 <magicDamage>{{ damage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />구체는 그 주변에 적대적 영역을 형성하여 적을 {{ areaslowamount*100 }}% <status>둔화</status>시키고 <magicDamage>초당 {{ areadamagepersecond }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "속박 지속시간", "초당 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ baseareadamage*8.000000 }} -> {{ baseareadamagenl*8.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "MelE.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MelR", "name": "황금 일식", "description": "멜이 압도 중첩이 적용된 모든 적을 거리와 상관없이 공격하여 압도 중첩당 추가 피해를 입힙니다.<br><br>황금 일식의 레벨이 높을수록 압도의 피해가 커집니다.", "tooltip": "<spellPassive>기본 지속 효과</spellPassive>: <keywordMajor>압도</keywordMajor> 피해가 <magicDamage>{{ passiveflatdamage }}의 마법 피해+중첩당 {{ passivestackdamage }}의 마법 피해</magicDamage>까지 증가합니다.<br /><br /><spellActive>사용 시</spellActive>: 멜이 <keywordMajor>압도</keywordMajor>의 영향을 받는 모든 적에게 힘을 방출하여 <magicDamage>{{ ultflatdamage }}의 마법 피해+<keywordMajor>압도</keywordMajor> 중첩당 {{ ultstackdamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br /><rules>적 챔피언이 <keywordMajor>압도</keywordMajor>의 영향을 받고 있을 때만 사용할 수 있습니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName>황금 일식</spellName> 기본 피해량", "<spellName>황금 일식</spellName> 중첩 피해량", "재사용 대기시간", "<keywordMajor>압도</keywordMajor> 기본 피해량", "<keywordMajor>압도</keywordMajor> 중첩 피해량"], "effect": ["{{ baseultflatdamage }} -> {{ baseultflatdamageNL }}", "{{ baseultstackdamage }} -> {{ baseultstackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basepassiveflatdamage }} -> {{ basepassiveflatdamageNL }}", "{{ basepassivestackdamage }} -> {{ basepassivestackdamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MelR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "이글거리는 광휘", "description": "멜이 스킬을 사용할 때마다 다음 기본 공격 시 투사체가 3개 (최대 9개) 추가됩니다.<br><br>멜이 스킬 또는 기본 공격으로 피해를 입히면 무한히 중첩할 수 있는 압도를 적용합니다. 압도 피해가 충분한 상태에서 멜의 공격이 적에게 적중하면 중첩을 소모하여 대상을 처형합니다.", "image": {"full": "Mel_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}