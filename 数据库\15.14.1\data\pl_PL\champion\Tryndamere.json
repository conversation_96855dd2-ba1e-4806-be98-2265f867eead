{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tryndamere": {"id": "Tryndamere", "key": "23", "name": "Tryndamere", "title": "Król <PERSON>zy<PERSON>ów", "image": {"full": "Tryndamere.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "23000", "num": 0, "name": "default", "chromas": false}, {"id": "23001", "num": 1, "name": "Nieśmiertelny Tryndamere", "chromas": false}, {"id": "23002", "num": 2, "name": "Król Tryndamere", "chromas": false}, {"id": "23003", "num": 3, "name": "Tryndamere Wiking", "chromas": false}, {"id": "23004", "num": 4, "name": "Tryndamere z Demonicznym Mieczem", "chromas": false}, {"id": "23005", "num": 5, "name": "Tryndamere Sułtan", "chromas": false}, {"id": "23006", "num": 6, "name": "Tryndamere z Walczących Królestw", "chromas": false}, {"id": "23007", "num": 7, "name": "Koszmarny Tryndamere", "chromas": false}, {"id": "23008", "num": 8, "name": "Tryndamere Łowca Bestii", "chromas": false}, {"id": "23009", "num": 9, "name": "Chemtechowy Tryndamere", "chromas": false}, {"id": "23010", "num": 10, "name": "Tryndamere Krwawego Księżyca", "chromas": true}, {"id": "23018", "num": 18, "name": "Tryndamere Zwiastun Nocy", "chromas": true}, {"id": "23027", "num": 27, "name": "Zwycięski Tryndamere", "chromas": true}], "lore": "Napędzany nieokiełz<PERSON>ym <PERSON>, Tryndamere kiedyś przeszedł przez cały <PERSON>eljord, otwarcie wyzywając do walki najlepszych wojowników północy, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się na nadchodzące czarne dni. Ten gniewny barbarzyńca od dawien dawna chciał zemścić się za ludobójstwo dokonane na jego klanie, choć ostatnio znalazł miejsce oraz dom u boku Ashe, avaro<PERSON><PERSON><PERSON> matki wojny, i jej plemienia. Prawie nieludzka siła i hart ducha Tryndamere'a są legendarne i zapewniły jemu i jego nowym sojusznikom niezliczone zwycięstwa nawet w najgorszych sytuacjach.", "blurb": "Napędzany nieokieł<PERSON>, Tryndamere kiedyś przeszedł przez cały <PERSON>eljord, otwarcie wyzywając do walki najlepszych wojowników północy, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się na nadchodzące czarne dni. Ten gniewny barbarzyńca od dawien dawna chciał zemścić się za...", "allytips": ["Opóźnienie użycia Niezmordowanego Szału to skuteczny sposób na sprawienie, że przeciwnik zapędzi się za daleko, próbując cię zabić.", "<PERSON><PERSON> to idealny sposób na wyleczenie Tryndamere'a. Postaraj się użyć go, gdy jest w pełni naładowany.", "Je<PERSON>li przeciwnik ma wysoką wartość pancerza, kup przedmioty takie jak Ostatni Szept czy Widmowe Ostrze Youmuu."], "enemytips": ["Postaraj się nękać Tryndamere'a wcześnie, aby nie mógł zabijać stworów i leczyć się Zewem Krwi.", "Tryndamere może cię spo<PERSON> tylko, gdy j<PERSON> do niego zwrócony plecami.", "Ataki Tryndamere'a są głównie fizyczne. Je<PERSON>eli jest zbyt silny, roz<PERSON>ż zakup Kolczastej Kolczugi."], "tags": ["Fighter", "Assassin"], "partype": "Furia", "info": {"attack": 10, "defense": 5, "magic": 2, "difficulty": 5}, "stats": {"hp": 696, "hpperlevel": 108, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.9, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 4, "attackspeedperlevel": 3.4, "attackspeed": 0.67}, "spells": [{"id": "TryndamereQ", "name": "<PERSON><PERSON>", "description": "Tryndamere rozkoszuje się walką, przez co jego obrażenia od ataku są tym wię<PERSON>, im bardziej jest ranny. <PERSON><PERSON><PERSON> <PERSON>ż<PERSON>wi, aby zuż<PERSON>ć swą Furię do uleczenia się.", "tooltip": "<spellPassive>Biernie:</spellPassive> Tryndamere jest żądny krwi i zyskuje <scaleAD>{{ flatad }} pkt. obrażeń od ataku</scaleAD> plus <scaleAD>{{ adperonepercentmissinghp }} pkt.</scaleAD> za każdy 1% brakującego zdrowia.<br /><br /><spellActive>Użycie:</spellActive> Tryndamere pochłania <keywordMajor>furię</keywordMajor>, przywracając sobie <healing>{{ baseheal }} pkt. zdrowia plus {{ healperfury }} pkt. za każdy ładunek furii</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od ataku", "Obrażenia od ataku za procent brakującego zdrowia", "Leczenie", "Leczenie za punkt furii"], "effect": ["{{ flatad }} -> {{ flatadNL }}", "{{ adperonepercentmissinghp }} -> {{ adperonepercentmissinghpNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ bonushealperfury }} -> {{ bonushealperfuryNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "TryndamereQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "TryndamereW", "name": "Prześmiewczy Okrzyk", "description": "Tryndamere wydaje z siebie obelżywy krzyk, który zmniejsza obrażenia od ataku pobliskich wrogich bohaterów. Wrogowie odwróceni do Tryndamere'a plecami mają też zmniejszoną prędkość ruchu.", "tooltip": "Tryndamere rzuca wyzwiskami w stronę wrogów, zmniejszając obrażenia od ataku bohaterów o {{ adreduction*-1 }} pkt. na {{ reductionduration }} sek. Wrodzy bohaterowie odwróceni plecami do Tryndamere'a zostają <status>spowolnieni</status> o {{ slowpotency*-100 }}% na tyle samo czasu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zmniejszenie obrażeń fizycznych", "Spowolnienie"], "effect": ["{{ adreduction*-1.000000 }} -> {{ adreductionnl*-1.000000 }}", "{{ slowpotency*-100.000000 }}% -> {{ slowpotencynl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "TryndamereW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "TryndamereE", "name": "Wirujące Cięcie", "description": "Tryndamere przedziera się do celu, zadając obrażenia wszystkim wrogom na swojej drodze.", "tooltip": "Tryndamere atakuje wrogów z półobrotu, z<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> i generując <keywordMajor>{{ nonchampfurygain }} pkt. furii</keywordMajor> za każdego trafionego wroga, zwiększonej do <keywordMajor>{{ champfurygain }} pkt. furii</keywordMajor> przeciwko wrogim bohaterom.<br /><br />Czas odnowienia tej umiejętności skraca się o {{ nonchampcdrefund }} sek. za każdym razem, gdy Tryndamere trafia krytycznie, oraz o {{ champcdrefund }} sek., gdy trafia krytycznie bohatera.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "TryndamereE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "UndyingRage", "name": "Niezmordowan<PERSON>ł", "description": "Żądza walki Tryndamere'a staje się tak silna, że nie moż<PERSON>, niez<PERSON>żnie od odniesionych ran.", "tooltip": "Tryndamere na {{ e3 }} sek. staje się nieśmiertelny, nie dając się sprowadzić poniżej {{ e2 }} pkt. zdrowia i zyskuje <keywordMajor>{{ e1 }} pkt. furii</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Minimalne zdrowie", "Zyskiwana furia"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [50, 75, 100], [30, 50, 70], [5, 5, 5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "50/75/100", "30/50/70", "5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "UndyingRage.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Darmowa"}], "passive": {"name": "Bitewny Szał", "description": "Tryndamere zyskuje furię za każdy atak, trafienie krytyczne i ostateczny cios, który zada. Furia zwiększa jego szanse na trafienie krytyczne oraz może zostać zużyta przez umiejętność Zew Krwi.", "image": {"full": "Tryndamere_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}