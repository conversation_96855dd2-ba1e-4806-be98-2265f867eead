{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ezreal": {"id": "Ezreal", "key": "81", "name": "Ezreal", "title": "il prodigo esploratore", "image": {"full": "Ezreal.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "81000", "num": 0, "name": "default", "chromas": false}, {"id": "81001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "81002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "81003", "num": 3, "name": "Ezreal dei Ghiacci", "chromas": true}, {"id": "81004", "num": 4, "name": "Ezreal Esploratore", "chromas": false}, {"id": "81005", "num": 5, "name": "Ezreal Pulsefire", "chromas": false}, {"id": "81006", "num": 6, "name": "TPA Ezreal", "chromas": false}, {"id": "81007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "81008", "num": 8, "name": "Ezreal Asso di Picche", "chromas": false}, {"id": "81009", "num": 9, "name": "Ezreal Arcade", "chromas": false}, {"id": "81018", "num": 18, "name": "Ezreal Guardiano Stellare", "chromas": false}, {"id": "81019", "num": 19, "name": "Ezreal SSG", "chromas": false}, {"id": "81020", "num": 20, "name": "Ezreal Guardiano in Pigiama", "chromas": false}, {"id": "81021", "num": 21, "name": "Ezreal dell'Accademia di Battaglia", "chromas": true}, {"id": "81022", "num": 22, "name": "Ezreal OPSI", "chromas": false}, {"id": "81023", "num": 23, "name": "Ezreal OPSI (edizione prestigio)", "chromas": false}, {"id": "81025", "num": 25, "name": "Ezreal Protettore di Porcellana", "chromas": false}, {"id": "81033", "num": 33, "name": "Ezreal Corte Fatata", "chromas": false}, {"id": "81043", "num": 43, "name": "Ezreal HEARTSTEEL", "chromas": false}, {"id": "81044", "num": 44, "name": "<PERSON><PERSON><PERSON> celeste", "chromas": true}, {"id": "81054", "num": 54, "name": "<PERSON><PERSON><PERSON>ug<PERSON> c<PERSON> (edizione prestigio)", "chromas": false}, {"id": "81065", "num": 65, "name": "<PERSON><PERSON><PERSON> della Rosa Nera", "chromas": false}], "lore": "Un avventuriero audace, con un inaspettato dono per le arti magiche, Ezreal si avventura tra catacombe perdute e antiche maledizioni, superando difficoltà impossibili con facilità. Il suo coraggio non conosce confini, motivo per cui ama improvvisare in ogni situazione, sfruttando il suo ingegno ma soprattutto il suo mistico guanto shurimano, che usa per scatenare devastanti raffiche di potere arcano. Una sola cosa è certa: quando c'è Ezreal, i guai non sono mai troppo lontani. Né troppo nascosti. Sono probabilmente ovunque.", "blurb": "Un avventuriero audace, con un inaspettato dono per le arti magiche, Ezreal si avventura tra catacombe perdute e antiche maledizioni, superando difficoltà impossibili con facilità. Il suo coraggio non conosce confini, motivo per cui ama improvvisare in...", "allytips": ["Usa Spostamento arcano in combinazione con gli altri tuoi colpi di precisione.", "Puoi vestire i panni di Ezreal sia come Tiratore basato sull'attacco fisico sia sul potere magico.", "Puoi posizionare Sbarramento energetico in modo da colpire più ondate di minion o anche mostri neutrali."], "enemytips": ["Ezreal è un campione molto fragile, quindi cerca di attirarlo in battaglia.", "<PERSON><PERSON><PERSON> possiede solo colpi di precisione, cerca di frapporre dei minion tra di te e i campioni avversari.", "Colpo mistico applica i buff al bersaglio colpito, incluso lo Stemma cinereo."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 102, "mp": 375, "mpperlevel": 70, "movespeed": 325, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.65, "mpregen": 8.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "EzrealQ", "name": "Colpo mistico", "description": "Ezreal spara un forte dardo di energia che riduce lievemente i suoi tempi di ricarica se colpisce un'unità nemica.", "tooltip": "Ezreal spara un dardo di energia che infligge <physicalDamage>{{ damage }} danni fisici</physicalDamage> al primo nemico colpito e riduce la ricarica delle sue abilità di {{ cdrefund }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5.25, 5, 4.75, 4.5], "cooldownBurn": "5.5/5.25/5/4.75/4.5", "cost": [28, 31, 34, 37, 40], "costBurn": "28/31/34/37/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealQ.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealW", "name": "<PERSON><PERSON><PERSON>", "description": "Ezreal spara una sfera che si attacca al primo campione o obiettivo colpito. Se Ezreal colpisce un nemico con la sfera, la sfera esplode e infligge danni.", "tooltip": "Ezreal spara una sfera magica che si attacca al primo campione, struttura, o mostro della giungla epico colpito per {{ detonationtimeout }} secondi. Se Ezreal colpisce quel bersaglio con un attacco o un'abilità, la sfera esplode, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage>. Farla esplodere con un'abilità rimborsa il costo dell'abilità più <scaleMana>{{ manareturn }} mana</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto potere magico totale"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealW.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealE", "name": "Spostamento arcano", "description": "Ezreal si teletrasporta in un punto bersaglio nelle vicinanze e spara un dardo a ricerca che colpisce il nemico più vicino. Dà priorità ai nemici colpiti da Flusso essenziale.", "tooltip": "Ezreal si teletrasporta e spara un dardo al nemico più vicino, infliggendogli <magicDamage>{{ damage }} danni magici</magicDamage>. Il dardo dà priorità ai nemici affetti da <spellName>Flusso essenziale</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [26, 23, 20, 17, 14], "cooldownBurn": "26/23/20/17/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "EzrealE.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealR", "name": "Sbarramento energetico", "description": "<PERSON>zreal si prepara prima di scatenare una potente raffica di energia che infligge danni ingenti a tutte le unità che attraversa (i danni sono ridotti per minion e mostri non epici).", "tooltip": "Ezreal spara un enorme arco di energia che infligge <magicDamage>{{ damage }} danni magici</magicDamage>. L'arco infligge il {{ damagereductionwaveclear.0*100 }}% dei danni a minion e mostri della giungla non epici.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EzrealR.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Forza dell'incantesimo nascente", "description": "<PERSON><PERSON>real ottiene velocità d'attacco che aumenta ogni volta che mette a segno un'abilità. Si accumula fino a 5 volte.", "image": {"full": "Ezreal_RisingSpellForce.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}