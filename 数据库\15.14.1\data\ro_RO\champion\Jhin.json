{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jhin": {"id": "<PERSON><PERSON>", "key": "202", "name": "<PERSON><PERSON>", "title": "virtuozul", "image": {"full": "Jhin.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "202000", "num": 0, "name": "default", "chromas": false}, {"id": "202001", "num": 1, "name": "Jhin din Vestul Sălbatic", "chromas": true}, {"id": "202002", "num": 2, "name": "<PERSON><PERSON> lun<PERSON> sângerie", "chromas": false}, {"id": "202003", "num": 3, "name": "Jhin SKT T1", "chromas": false}, {"id": "202004", "num": 4, "name": "PROIECT: <PERSON><PERSON>", "chromas": false}, {"id": "202005", "num": 5, "name": "Jhin cosmos întunecat", "chromas": false}, {"id": "202014", "num": 14, "name": "<PERSON><PERSON> din Pergamentele Shan Hai", "chromas": true}, {"id": "202023", "num": 23, "name": "Jhin DWG", "chromas": true}, {"id": "202025", "num": 25, "name": "<PERSON><PERSON> <PERSON> paradis", "chromas": true}, {"id": "202036", "num": 36, "name": "<PERSON><PERSON>, luptătorul sufletelor", "chromas": false}, {"id": "202037", "num": 37, "name": "Jhin cosmos întunecat absolut", "chromas": true}, {"id": "202047", "num": 47, "name": "Jhin creator de mituri", "chromas": false}], "lore": "Jhin este un psihopat meticulos, pentru care crima înseamnă artă. Multă vreme a fost ținut prizonier într-o temniță ioniană, dar în cele din urmă a fost eliberat de câțiva membri din consiliul conducător al Ioniei și a ajuns să lucreze ca asasin pentru acest grup secret. Folosindu-și arma pe post de pensulă, Jhin creează tablouri de o brutalitate sublimă, spre groaza victimelor și a celor din jur. Îi face o plăcere crudă să pună în scenă spectacole macabre, fiind astfel unealta perfectă pentru a transmite cel mai puternic mesaj: teroarea.", "blurb": "<PERSON>hin este un psihopat meticulos, pentru care crima înseamnă artă. Multă vreme a fost ținut prizonier într-o temniță ioniană, dar în cele din urmă a fost eliberat de câțiva membri din consiliul conducător al Ioniei și a ajuns să lucreze ca asasin pentru...", "allytips": ["''Drama în două acte'' are o rază incredibilă. Atunci când te apropii de o luptă, uită-te după inamici pe care i-ai putea țintui.", "Suprema ta le provoacă mult mai puține daune inamicilor care au bara de viață plină. Încearcă să nimerești țintele slăbite când se retrag.", "Chiar dacă îți reîncarci arma, poți folosi în continuare abilități. Activează-le în timpii morți."], "enemytips": ["''Drama în două acte'' îi țintuiește doar pe cei loviți de aliații, de atacurile de bază sau de capcanele lui Jhin în ultimele 4 secunde.", "<PERSON><PERSON> plasează capcane invizibile pe toată harta. Ai grijă unde calci!", "Atacurile lui <PERSON> sunt puternice, dar virtuozul rămâne fără muniție după a patra lovitură. Atacă-l în timp ce își reîncarcă arma."], "tags": ["Marksman", "Mage"], "partype": "Mană", "info": {"attack": 10, "defense": 2, "magic": 6, "difficulty": 6}, "stats": {"hp": 655, "hpperlevel": 107, "mp": 300, "mpperlevel": 50, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.4, "attackspeedperlevel": 0, "attackspeed": 0.625}, "spells": [{"id": "JhinQ", "name": "Dansul sublimei distrugeri", "description": "<PERSON><PERSON> aruncă un cartuș magic către un inamic. Acesta poate lovi până la patru ținte, iar daunele cresc de fiecare dat<PERSON> când face o victim<PERSON>.", "tooltip": "<PERSON>hin lansează un cartuș care provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> și ricoșează către un inamic din apropiere care nu a fost încă lovit.<br /><br />Cartușul poate lovi maximum {{ tooltipmaxtargetshit }} ținte. Inamicii care mor la scurt timp după ce sunt loviți cresc daunele loviturilor ulterioare ale acestuia cu {{ percentamponkill*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Raport daune din atac totale", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio*100.000000 }}% -> {{ adrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "JhinQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinW", "name": "Dramă în două acte", "description": "<PERSON>hin își armează pușca și trage un singur glonț de la o distanță incredibilă. Glonțul trece prin minioni și <PERSON>, dar se oprește când lovește un campion. Dacă ținta a fost lovită recent de către aliații lui <PERSON>, de capcanele lui sau de el, atunci este imobilizată.", "tooltip": "<PERSON>hin lansează un glonț de la mare distanță, care îi provoacă <physicalDamage>{{ totaldamage }} daune fizice</physicalDamage> primului campion lovit și celorlalți inamici aflați pe traiectorie.<br /><br />Dacă abilitatea lovește un campion care a fost lovit de un campion aliat în ultimele {{ spottingduration }} secunde, inamicul va fi <status>țintuit</status> timp de {{ rootduration }} secunde, iar Jhin va primi bonusul la viteza de mișcare oferit de <spellName>''Șoaptă''</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON><PERSON>", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "JhinW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinE", "name": "Public captiv", "description": "<PERSON>hin plasează o capcană-lotus invizibilă, care se activează atunci când cineva trece peste ea. Capcana încetinește inamicii din apropiere, după care explodează într-o furtună de petale ascuțite și provoacă daune. <br><br><font color='#FFFFFF'>''Frumusețe prin moarte'' –</font> când Jhin ucide un campion inamic, lângă cadavrul acestuia apare o capcană-lotus.", "tooltip": "<passive>Pasivă:</passive> campionii uciși de Jhin creează și detonează o capcană-lotus în locația lor.<br /><br /><active>Activă:</active> Jhin plasează o capcană-lotus invizibilă timp de {{ trapduration }} minute care creează o zonă ce îi <status>încetinește</status> cu {{ trapslowamount*100 }}% pe inamicii care calcă pe ea. După {{ trapdetonationtime }} secunde, capcana se detonează, provocând <magicDamage>{{ totaldamage }} daune magice</magicDamage>.<br /><br />Această abilitate are 2 cumuluri (timp de reîncărcare: {{ ammorechargeratetooltip }} sec.).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON> <PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [2, 2, 2, 2, 2], "cooldownBurn": "2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "JhinE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinR", "name": "Punct culminant", "description": "<PERSON>hin se pregă<PERSON><PERSON>, apoi transformă ''Șoapta'' într-un mega-tun de umăr care poate trage 4 gloanțe de la o distanță imensă. Gloanțele trec prin toți minionii și monștrii, dar se opresc la primul campion lovit. Aceste lovituri scad viteza de mișcare a inamicilor loviți și le provoacă daune bonus în funcție de viața lipsă. Cel de-al patrulea glonț este extrem de puternic și făurit cu măiestrie, provocând garantat o lovitură critică.", "tooltip": "Jhin se pregătește și apoi trage 4 super-gloanțe, fiecare provocându-i între <physicalDamage>{{ damagecalc }}</physicalDamage> și <physicalDamage>{{ maxincreasecalc }} daune fizice</physicalDamage> primului campion lovit (în funcție de procentul din viața lipsă a țintei) și <status>încetinindu-l</status> cu {{ slowpercent*100 }}% timp de {{ slowduration }} secunde. Cea de-a patra lovitură este mereu o lovitură critică și provoacă {{ fourthshotmultiplier*100 }}% daune.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JhinR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Șoaptă", "description": "Tunul de mână al lui Jhin, ''Șoapt<PERSON>'', este un instrument de mare precizie, creat cu scopul de a provoca daune enorme. Frecvența cu care trage este fixă și are doar patru gloanțe. Jhin aplică un suflu de magie neagră asupra ultimului glonț, transformându-l într-o lovitură critică garantată ce provoacă daune bonus în funcție de viața lipsă a țintei. De fiecare dată când ''Șoapta'' provoacă o lovitură critică, <PERSON>hin primește un bonus scurt la viteza de mișcare.", "image": {"full": "Jhin_P.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}