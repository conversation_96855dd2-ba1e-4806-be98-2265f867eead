{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Skarner": {"id": "<PERSON><PERSON><PERSON>", "key": "72", "name": "<PERSON><PERSON><PERSON>", "title": "the Primordial Sovereign", "image": {"full": "Skarner.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "72000", "num": 0, "name": "default", "chromas": false}, {"id": "72001", "num": 1, "name": "Sandscourge Skarner", "chromas": false}, {"id": "72002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "72003", "num": 3, "name": "Battlecast Alpha Skarner", "chromas": false}, {"id": "72004", "num": 4, "name": "Guardian of the Sands Skarner", "chromas": false}, {"id": "72005", "num": 5, "name": "Cosmic Sting Skarner", "chromas": true}], "lore": "The ancient, colossal brackern Skarner is revered in Ixtal as one of the founding members of its ruling caste, the Yun Tal. Devoted to keeping his nation safe from the rest of the world, <PERSON><PERSON><PERSON> dwells in a chamber beneath Ixaocan where he can hear the vibrations of the earth and detect potential threats. As more members of the Yun Tal begin questioning <PERSON><PERSON><PERSON>'s self-isolation, <PERSON><PERSON><PERSON> grows increasingly paranoid and will do anything to keep Ixtal and its people safe—no matter the cost.", "blurb": "The ancient, colossal brackern Skarner is revered in Ixtal as one of the founding members of its ruling caste, the Yun Tal. Devoted to keeping his nation safe from the rest of the world, <PERSON><PERSON><PERSON> dwells in a chamber beneath Ixaocan where he can hear the...", "allytips": ["Basic attacks apply Quaking. Stick to your target and re-apply it for maximum damage.", "Capturing <PERSON> Spires before attempting neutral objectives or team fights around them improves <PERSON><PERSON><PERSON>'s performance in said fights dramatically.", "Impale is extremely powerful when you use it to position an enemy so your allies can attack them."], "enemytips": ["<PERSON><PERSON><PERSON>'s Impact can be stopped by any stun, root, or knock-up. Save a key cooldown if he's coming for a gank!", "<PERSON><PERSON><PERSON> can be dodged. Use Flash or another ability to get out of reach.", "<PERSON><PERSON><PERSON>'s sustained damage can eventually take you down. Be careful not to ignore him."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 8, "magic": 5, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 320, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.2, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SkarnerQ", "name": "Shattered Earth / Upheaval", "description": "<PERSON><PERSON><PERSON> rips a boulder from the earth that empowers his attacks and can be thrown as a powerful projectile.", "tooltip": "<PERSON><PERSON><PERSON> rips a boulder from the ground, empowering his next 3 Attacks with <attackSpeed>{{ attackspeed*100 }}% Attack Speed</attackSpeed> and dealing <physicalDamage>{{ abilitydamage }} physical damage</physicalDamage> to surrounding enemies. His final Attack will deal an additional <physicalDamage>{{ maxhppercent*100 }}% max Health physical damage</physicalDamage> and <status>Slow</status> affected foes by {{ slowpercent*100 }}% for {{ slowduration }}s.<br /><br /><recast>Recast:</recast> S<PERSON><PERSON> ends this Ability and throws his boulder, dealing <physicalDamage>{{ spell.skarnerq:abilitydamage }} + {{ spell.skarnerq:maxhppercent*100 }}% max Health physical damage</physicalDamage>, and additionally <status>Slowing</status> the first enemy hit — and any other surrounding enemies — by {{ spell.skarnerq:slowpercent*100 }}% for {{ spell.skarnerq:slowduration }}s.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Attack Speed", "Monster Cap", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ monsterdamagecap }} -> {{ monsterdamagecapNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.5, 10, 8.5, 7], "cooldownBurn": "13/11.5/10/8.5/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SkarnerQ.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SkarnerW", "name": "Seismic Bastion", "description": "<PERSON><PERSON><PERSON> gains a shield and creates an earthquake whose shockwave damages and slows foes.", "tooltip": "Skarner gains <shield>{{ initialshield }} Shield</shield> for {{ shieldduration }}s and triggers an earthquake that deals <magicDamage>{{ damage }} magic damage</magicDamage> and <status>Slows</status> nearby enemies by {{ sloweffect*-100 }}% for {{ slowduration }}s.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SkarnerW.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SkarnerE", "name": "Ixtal's Impact", "description": "Skarner charges forward and passes through terrain. If he collides with a champion or large monster, he'll slam them into the next wall he hits to damage and stun them.", "tooltip": "<PERSON><PERSON><PERSON> charges forward, steering toward the chosen direction and ignoring terrain. If he runs into a champion or large monster, <PERSON><PERSON><PERSON> drags them along for the rest of his charge.<br /><br />Colliding into a wall with an enemy in tow deals <physicalDamage>{{ pindamage }} physical damage</physicalDamage> to the enemy and <status>Stuns</status> them for {{ stunduration }}s.<br /><br /><PERSON><PERSON><PERSON> can <recast>Recast</recast> this Ability to end his charge early.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ pinbasedamage }} -> {{ pinbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1700, 1700, 1700, 1700, 1700], "rangeBurn": "1700", "image": {"full": "SkarnerE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SkarnerR", "name": "Impale", "description": "<PERSON><PERSON><PERSON>'s tails lash forward to suppress enemy champions. Once suppressed, the victims will be dragged along by <PERSON><PERSON><PERSON> as he moves around.", "tooltip": "<PERSON><PERSON><PERSON>'s tails lash forward, dealing <magicDamage>{{ damage }} magic damage</magicDamage> and <status>Suppressing</status> the first 3 champions he hits for {{ duration }}s. Those hit are dragged along by <PERSON><PERSON><PERSON> for the duration of this Ability.<br /><br />If <PERSON><PERSON><PERSON> hits at least one champion he gains <speed>{{ speedboostamount*100 }}% Move Speed</speed> for {{ speedboostduration }} seconds.<br /><br />If <spellName>Shattered Earth</spellName> is active, <PERSON><PERSON><PERSON> will cast <spellName>Upheaval</spellName> first.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "SkarnerR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Threads of Vibration", "description": "<PERSON><PERSON><PERSON>'s Attacks, Shattered Earth, Upheaval, and Impale, apply Quaking. At max stacks of Quaking, enemies take max Health magic damage over its duration.", "image": {"full": "Skarner_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}