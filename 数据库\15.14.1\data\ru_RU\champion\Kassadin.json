{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "Касса<PERSON>ин", "title": "Скиталец Бездны", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "Праздничный Кассадин", "chromas": false}, {"id": "38002", "num": 2, "name": "Глубоководный Кассадин", "chromas": false}, {"id": "38003", "num": 3, "name": "Кассадин до преображения", "chromas": false}, {"id": "38004", "num": 4, "name": "Вестник гибели Кассадин", "chromas": false}, {"id": "38005", "num": 5, "name": "Космический похититель Кассадин", "chromas": false}, {"id": "38006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "38014", "num": 14, "name": "Хекстековый Кассадин", "chromas": false}, {"id": "38015", "num": 15, "name": "Кас<PERSON><PERSON><PERSON><PERSON><PERSON> Грозовой Клинок", "chromas": false}, {"id": "38024", "num": 24, "name": "Дракомант Кассадин", "chromas": false}], "lore": "Кассадин пробирается по самым темным уголкам этого мира, оставляя за собой разрушение. Он знает, что его дни сочтены. Когда-то он был известным проводником и авантюристом, но затем отказался от приключений и стал мирно жить с семьей среди южных кочевых племен – пока однажды его деревню не поглотила Бездна. Он поклялся отомстить и начал собирать магические артефакты и запретные знания для предстоящей борьбы. В конце концов Кассадин отправился в пустошь Икатии, полный решимости встретиться лицом к лицу с любым порождением Бездны и отыскать самопровозглашенного пророка по имени Мальзахар.", "blurb": "Кассадин пробирается по самым темным уголкам этого мира, оставляя за собой разрушение. Он знает, что его дни сочтены. Когда-то он был известным проводником и авантюристом, но затем отказался от приключений и стал мирно жить с семьей среди южных кочевых...", "allytips": ["Кассадин может пользоваться многими предметами. Он может пойти по пути мага и набрать ману и силу умений, а может стать антимагом, приобретя сокращение перезарядки и сопротивление магии.", "Абсолютное умение Кассадина можно применять по-разному и весьма часто. Пользуйтесь этим.", "Возрастающая стоимость Шага в Бездну не так страшна с усилением ''Знак озарения''."], "enemytips": [" Рассмотрите возможность покупки предметов, увеличивающих сопротивление магии, таких как Поступь Меркурия и Завеса банши.", "Помните, что Кассадин тратит все больше и больше маны, когда последовательно использует Шаг в Бездну.", "Когда рядом с ним было использовано 6 умений, Кассадин получает возможность использовать свое замедляющее умение - Выброс силы. Если он поднимает уровень этого умения, аккуратно используйте умения, стоя на линии против него."], "tags": ["Assassin", "Mage"], "partype": "Мана", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "Сфера пустоты", "description": "Кассадин выпускает во врага сгусток энергии Бездны, который наносит урон и прерывает использование умений. Кроме того, избыток энергии формирует вокруг Кассадина временный щит, поглощающий магический урон.", "tooltip": "Кассадин выпускает сферу энергии Бездны, нанося <magicDamage>{{ totaldamage }} магического урона</magicDamage> и прерывая врагов, которые применяют умения. Кроме того, Кассадин получает <shield>щит от магического урона прочностью {{ totalshield }}</shield> на 1.5 сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Прочность щита", "Стоимость – @AbilityResourceName@", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "<PERSON>herBlade", "name": "Клинок пустоты", "description": "Кассадин пассивно наносит дополнительный магический урон при автоатаках. При активации умения следующая автоатака Кассадина наносит существенный дополнительный магический урон и восстанавливает ману.", "tooltip": "<spellPassive>Пассивно:</spellPassive> автоатаки Кассадина дополнительно наносят <magicDamage>{{ onhitdamage }} магического урона</magicDamage>.<br /><br /><spellActive>Активно:</spellActive> Кассадин заряжает свой клинок и при следующей автоатаке дополнительно наносит <magicDamage>{{ activedamage }} магического урона</magicDamage>, а также восстанавливает себе <scaleMana>{{ e1 }}% от недостающей маны</scaleMana>. Если цель - чемпион, восстановление увеличивается до <scaleMana>{{ e4 }}%</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон активного эффекта", "Базовое восстановление маны", "Восстановление маны при поражении чемпионов"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ e4 }}% -> {{ e4NL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "ForcePulse", "name": "Выброс силы", "description": "Кассадин вытягивает энергию из заклинаний, применяющихся вокруг. Когда вытянуто достаточно энергии, Ка<PERSON><PERSON>адин может использовать Выброс силы, чтобы нанести урон и замедлить противников перед собой.", "tooltip": "<spellPassive>Пассивно:</spellPassive>: перезарядка умения <spellName>Выброс силы</spellName> сокращается на {{ reductionperspellcast }} сек. каждый раз, когда около Кассадина применяется любое умение.<br /><br /><spellActive>Активно:</spellActive> Кассадин выпускает волну энергии Бездны, нанося <magicDamage>{{ totaldamage }} магического урона</magicDamage> и <status>замедляя</status> цели на {{ e2 }}% на {{ e3 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "RiftWalk", "name": "Шаг в Бездну", "description": "Кассадин телепортируется в указанную точку, нанося урон ближайшим противникам. Последующие Шаги в Бездну в течение короткого промежутка времени требуют больше маны, но наносят больше урона.", "tooltip": "Кассадин телепортируется в указанную точку, нанося врагам <magicDamage>{{ basedamage }} магического урона</magicDamage>.<br /><br />Каждое последующее применение этого умения в течение следующих {{ rstackduration }} сек. расходует вдвое больше маны и дополнительно наносит <magicDamage>{{ bonusdamage }} магического урона</magicDamage>. Затраты и урон суммируются до {{ maxstacks }} раз.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Урон за заряд", "Перезарядка"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ stackdamage }} -> {{ stackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Камень Бездны", "description": "Кассадин получает меньше магического урона и может проходить сквозь бойцов.", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}