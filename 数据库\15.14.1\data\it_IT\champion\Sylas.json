{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sylas": {"id": "<PERSON><PERSON><PERSON>", "key": "517", "name": "<PERSON><PERSON><PERSON>", "title": "il rivoluzionario scatenato", "image": {"full": "Sylas.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "517000", "num": 0, "name": "default", "chromas": false}, {"id": "517001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517013", "num": 13, "name": "PROGETTO: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517014", "num": 14, "name": "PROGETTO: <PERSON><PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "517024", "num": 24, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517034", "num": 34, "name": "<PERSON><PERSON><PERSON> cinereo", "chromas": true}, {"id": "517036", "num": 36, "name": "<PERSON><PERSON><PERSON> dell'Inverno", "chromas": true}, {"id": "517046", "num": 46, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Cres<PERSON>uto in uno dei quartieri più poveri di Demacia, <PERSON><PERSON><PERSON> di Dregbourne è divenuto il simbolo del lato più oscuro della grande città. Quando era solo un ragazzo, la sua capacità di individuare la stregoneria nascosta attirò l'attenzione dei famigerati cercatori di magia, che alla fine lo imprigionarono per aver utilizzato quegli stessi poteri contro di loro. <PERSON><PERSON>i libero, Sylas è diventato uno spietato rivoluzionario che utilizza la magia delle persone intorno a lui per distruggere il regno che un tempo serviva... e la sua banda di maghi reietti sembra crescere di giorno in giorno.", "blurb": "Cresciuto in uno dei quartieri più poveri di Demacia, <PERSON><PERSON><PERSON> di Dregbourne è divenuto il simbolo del lato più oscuro della grande città. Quando era solo un ragazzo, la sua capacità di individuare la stregoneria nascosta attirò l'attenzione dei famigerati...", "allytips": ["Aspetta che tu o il nemico abbiate poca salute, per mettere bene a frutto Regicidio.", "<PERSON><PERSON> passare del tempo tra un'abilità e l'altra per ottenere il massimo effetto da Raffica di petricite.", "L'uso intelligente delle supreme nemiche può aprire nuove possibilità nell'approccio ai combattimenti a squadre."], "enemytips": ["La barra della salute di <PERSON>ylas può trarre in inganno, attenzione al suo Regicidio!", "Cerca di combattere Sylas quando non può rubare la tua suprema."], "tags": ["Mage", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 122, "mp": 400, "mpperlevel": 70, "movespeed": 340, "armor": 29, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.55, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "<PERSON><PERSON>s<PERSON>", "name": "Catene a frusta", "description": "<PERSON><PERSON><PERSON> scaglia le sue catene che si intersecano nella posizione del bersaglio infliggendo danni ai nemici e rallentandoli. <br><br><PERSON><PERSON> un ritardo, l'energia magica esplode nel punto di intersezione infliggendo danni.", "tooltip": "<PERSON><PERSON><PERSON> sca<PERSON> le sue catene, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage> e <status>rallentando</status> i nemici di {{ slowamountcalc }} per {{ slowduration }} secondi. Il punto in cui le catene si incrociano esplode, infliggendo altri <magicDamage>{{ explosiondamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> da esplosione", "Rallentamento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [775, 775, 775, 775, 775], "rangeBurn": "775", "image": {"full": "SylasQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasW", "name": "Regicidio", "description": "<PERSON><PERSON><PERSON> balza addosso a un nemico con forza magica, infliggendo danni e curandosi contro i campioni nemici.", "tooltip": "<PERSON>ylas balza addosso a un nemico con forza magica, infliggendo <magicDamage>{{ mindamage }} danni magici</magicDamage>. <PERSON>tro i campioni, <PERSON>yla<PERSON> recupera da <healing>{{ minhealing }}</healing> a <healing>{{ maxhealing }} salute</healing> in base alla sua salute mancante (guarigione massima a {{ maxexecutethreshold*100 }}% salute o meno).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ healing }} -> {{ healingNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasE", "name": "Latitanza/Rapimento", "description": "<PERSON><PERSON><PERSON> scatta in un punto. Può lanciare nuovamente l'abilità per scagliare le sue catene, tirandosi verso il nemico che colpisce.", "tooltip": "Sylas scatta rapidamente e si prepara a <recast>rilanciare</recast> questa abilità per 3,5 secondi.<br /><br /><recast>Rilanc<PERSON>:</recast> <PERSON>ylas lancia le sue catene, tirandosi verso il primo nemico colpito, infliggendogli <magicDamage>{{ damage }} danni magici</magicDamage> e <status>lanciandolo in aria</status> per {{ knockupduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ pulldamage }} -> {{ pulldamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SylasR", "name": "Violazione", "description": "<PERSON>ylas ruba la suprema del nemico e può utilizzarla liberamente.", "tooltip": "Sylas ruba da un campione nemico e può lanciare una copia della sua suprema, in base al livello della propria suprema e usando le proprie statistiche.<br /><br />Rubare da un nemico lo fa entrare in una ricarica del {{ pertargetcooldown }}% (modificata dalla velocità abilità di Sylas) della ricarica della suprema del nemico, per un minimo di {{ minimumenemycooldown }} secondi, durante la quale Sylas non può più rubare dallo stesso bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 55, 30], "cooldownBurn": "80/55/30", "cost": [75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "SylasR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Raffica di petricite", "description": "Dopo aver lanciato un'abilità, <PERSON><PERSON><PERSON> aggiunge una carica di Raffica di petricite. Gli attacchi base di Sylas spendono una carica e gli avvolgono intorno le catene energizzate, infliggendo danni magici bonus ai nemici colpiti. <PERSON><PERSON>ylas ha una carica di Raffica di petricite, ottiene velocità d'attacco. ", "image": {"full": "SylasP.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}