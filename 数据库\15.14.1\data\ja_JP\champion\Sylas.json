{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sylas": {"id": "<PERSON><PERSON><PERSON>", "key": "517", "name": "サイラス", "title": "解き放たれし者", "image": {"full": "Sylas.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "517000", "num": 0, "name": "default", "chromas": false}, {"id": "517001", "num": 1, "name": "月の生霊サイラス", "chromas": true}, {"id": "517008", "num": 8, "name": "フレヨルド サイラス", "chromas": true}, {"id": "517013", "num": 13, "name": "PROJECT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517014", "num": 14, "name": "プレステージ PROJECT: <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "517024", "num": 24, "name": "バトルウルフ サイラス", "chromas": true}, {"id": "517034", "num": 34, "name": "灰の暗殺者サイラス", "chromas": true}, {"id": "517036", "num": 36, "name": "冬の祝福サイラス", "chromas": true}, {"id": "517046", "num": 46, "name": "ダークスター サイラス", "chromas": true}], "lore": "デマーシアの貧しい地域に育ったドレグボーンのサイラスは、この大都市の闇を象徴する存在となった。少年期の彼には隠れた魔力を発見する才能があり、それゆえに悪名高きメイジ狩りに重用されていた。だがある時、その力をメイジ狩りたちに向けて用いたために投獄されてしまった。やがて脱獄に成功した彼は、今では強硬派の革命家となり、周囲の魔力を盗み取って自分がかつて仕えた王国を破壊しようとしている──そして彼に従う追放されたメイジたちの数は、日を追うごとに増えているのだ。", "blurb": "デマーシアの貧しい地域に育ったドレグボーンのサイラスは、この大都市の闇を象徴する存在となった。少年期の彼には隠れた魔力を発見する才能があり、それゆえに悪名高きメイジ狩りに重用されていた。だがある時、その力をメイジ狩りたちに向けて用いたために投獄されてしまった。やがて脱獄に成功した彼は、今では強硬派の革命家となり、周囲の魔力を盗み取って自分がかつて仕えた王国を破壊しようとしている──そして彼に従う追放されたメイジたちの数は、日を追うごとに増えているのだ。", "allytips": ["「王殺し」の効果を最大限発揮するには、相手か自分の体力が低くなっている時を狙おう。", "スキルとスキルの間に通常攻撃を挟むことで、「ペトリサイトバースト」の効果を最大限活用できる。", "敵のアルティメットスキルと上手く組み合わせて、チームファイトに新たな可能性を生み出そう。"], "enemytips": ["サイラスには「王殺し」があるので、サイラスの体力バーをあてにしすぎないように注意が必要。", "サイラスに戦いを挑むときは、アルティメットスキルが奪われない時を選ぶとよい。"], "tags": ["Mage", "Assassin"], "partype": "マナ", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 122, "mp": 400, "mpperlevel": 70, "movespeed": 340, "armor": 29, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.55, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "<PERSON><PERSON>s<PERSON>", "name": "鎖の鞭", "description": "指定地点で交わるように2本の鎖を叩きつけ、敵にダメージとスロウ効果を与える。<br><br>少ししてから交差地点で魔法エネルギーが爆発し、ダメージを与える。", "tooltip": "2本の鎖を叩きつけ、<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与えて、{{ slowduration }}秒間{{ slowamountcalc }}の<status>スロウ効果</status>を与える。鎖が交差した場所は爆発して、さらに<magicDamage>{{ explosiondamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "爆発ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [775, 775, 775, 775, 775], "rangeBurn": "775", "image": {"full": "SylasQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SylasW", "name": "王殺し", "description": "魔法エネルギーを纏って敵に突進し、ダメージを与える。対象がチャンピオンの場合は自身の体力を回復する。", "tooltip": "魔法の力で敵に向かってダッシュし、<magicDamage>{{ mindamage }}の魔法ダメージ</magicDamage>を与える。対象がチャンピオンの場合、自身の減少体力に応じて<healing>{{ minhealing }}</healing> - <healing>{{ maxhealing }}の体力</healing>を回復する(体力が{{ maxexecutethreshold*100 }}%以下で回復量が最大になる)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "体力回復量", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ healing }} -> {{ healingNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SylasE", "name": "逃亡/拉致", "description": "指定地点にダッシュする。再発動で鎖を投げつけて命中した敵に向かって自身を引き寄せる。", "tooltip": "素早くダッシュして、3.5秒間<recast>再発動</recast>可能になる。<br /><br /><recast>再発動:</recast> 鎖を投げ、最初に命中した敵の方向へ自身を引き寄せて<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与え、{{ knockupduration }}秒間<status>ノックアップ</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ pulldamage }} -> {{ pulldamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SylasR", "name": "乗っ取り", "description": "敵のアルティメットスキルを奪い、自由に発動できる。", "tooltip": "敵チャンピオンを乗っ取り、対象のアルティメットスキルをコピーして使用可能になる。スキルレベルとステータスは、自身のアルティメットスキルのレベルとステータスを反映する。<br /><br />敵を乗っ取ると、同じ敵に対してはクールダウンが発生し、その間は乗っ取りができなくなる。クールダウンの長さはその敵のアルティメットスキルのクールダウンの{{ pertargetcooldown }}%で、自身のスキルヘイストによって最短{{ minimumenemycooldown }}秒まで短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 55, 30], "cooldownBurn": "80/55/30", "cost": [75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "SylasR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ペトリサイトバースト", "description": "スキル使用後、「ペトリサイトバースト」がチャージされる。通常攻撃でチャージを解放し、魔力のこもった鎖を旋回させて当たった敵に追加魔法ダメージを与える。「ペトリサイトバースト」のチャージを保持している間は攻撃速度が増加する。 ", "image": {"full": "SylasP.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}