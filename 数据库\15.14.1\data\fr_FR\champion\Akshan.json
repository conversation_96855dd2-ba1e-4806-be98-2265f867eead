{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akshan": {"id": "<PERSON><PERSON><PERSON>", "key": "166", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON> rebelle", "image": {"full": "Akshan.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "166000", "num": 0, "name": "default", "chromas": false}, {"id": "166001", "num": 1, "name": "<PERSON><PERSON><PERSON> cyberpop", "chromas": true}, {"id": "166010", "num": 10, "name": "<PERSON><PERSON><PERSON> rose de cristal", "chromas": true}, {"id": "166020", "num": 20, "name": "<PERSON><PERSON><PERSON> des Trois Honneurs", "chromas": false}], "lore": "Se jouant du danger, <PERSON><PERSON><PERSON> combat le mal sans jamais se départir de son charisme (il ne faut jamais sous-estimer l'importance de la cape) et de sa droiture. Il est passé maître dans l'art du combat furtif, ce qui lui permet d'échapper au regard de ses ennemis et de réapparaître lorsqu'ils s'y attendent le moins. À l'aide de son sens aigu de la justice et d'une arme légendaire défiant la mort elle-même, il redresse les torts des nombreux vauriens de Runeterra. Sa règle d'or : « ne sois pas crapuleux. »", "blurb": "Se jouant du danger, <PERSON><PERSON><PERSON> combat le mal sans jamais se départir de son charisme (il ne faut jamais sous-estimer l'importance de la cape) et de sa droiture. Il est passé maître dans l'art du combat furtif, ce qui lui permet d'échapper au regard de ses...", "allytips": ["Se jouant du danger, <PERSON><PERSON><PERSON> combat le mal sans jamais se départir de son charisme (il ne faut jamais sous-estimer l'importance de la cape) et de sa droiture. Il est passé maître dans l'art du combat furtif, ce qui lui permet d'échapper au regard de ses ennemis et de réapparaître lorsqu'ils s'y attendent le moins. À l'aide de son sens aigu de la justice et d'une arme légendaire défiant la mort elle-même, il redresse les torts des nombreux vauriens de Runeterra. Sa règle d'or : « ne sois pas crapuleux. »"], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 630, "hpperlevel": 107, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.75, "hpregenperlevel": 0.65, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "Vengerang", "description": "<PERSON><PERSON><PERSON> lance un boomerang qui inflige des dégâts à l'aller et au retour. Sa portée augmente chaque fois qu'il touche un ennemi.", "tooltip": "<PERSON><PERSON><PERSON> lance un boomerang qui inflige <physicalDamage>{{ finaldamage }} pts de dégâts physiques</physicalDamage> et dont la portée augmente chaque fois qu'il touche un ennemi.<br /><br /><PERSON><PERSON><PERSON> gagne <speed>+{{ totalhaste }} de vitesse de déplacement</speed> (ce bonus diminue en {{ hasteduration }} sec) quand le boomerang touche un champion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "% de dégâts aux sbires", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ secondarytargetdamage*100.000000 }}% -> {{ secondarytargetdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.25, 6.5, 5.75, 5], "cooldownBurn": "8/7.25/6.5/5.75/5", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "AkshanQ.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanW", "name": "Cavalier seul", "description": "Passivement, <PERSON><PERSON><PERSON> applique une marque Crapule sur les champions ennemis quand ils tuent des champions alliés. Si Akshan tue une Crapule, il ressuscite les alliés qu'elle a tués, gagne des PO supplémentaires et dissipe toutes les marques.<br><br>À l'activation, <PERSON><PERSON><PERSON> se camoufle. Il gagne aussi de la vitesse de déplacement et de la régénération du mana quand il se dirige vers des Crapules. <PERSON><PERSON><PERSON> perd rapidement son camouflage quand il est hors des hautes herbes ou loin des éléments de terrain.", "tooltip": "{{ Spell_AkshanW_Tooltip_{{ gamemodeinteger }} }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@", "Vitesse de d<PERSON>placement"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ msvalue }} -> {{ msvalueNL }}"]}, "maxrank": 5, "cooldown": [18, 14, 10, 6, 2], "cooldownBurn": "18/14/10/6/2", "cost": [40, 30, 20, 10, 0], "costBurn": "40/30/20/10/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "AkshanW.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanE", "name": "Envolée héroïque", "description": "<PERSON><PERSON><PERSON> tire un grappin sur un élément de terrain et se balance autour. Pendant ce balancement, il tire à répétition sur l'ennemi le plus proche. Percuter un champion ou un élément de terrain le fait tomber de la corde, mais il peut aussi sauter de la corde prématurément.", "tooltip": "<spellActive>Première activation :</spellActive> <PERSON><PERSON><PERSON> tire un grappin et s'attache au premier élément de terrain touché.<br /><br /><spellActive>Deuxième activation :</spellActive> A<PERSON>han se balance autour de l'élément de terrain tout en tirant à répétition sur l'ennemi le plus proche en lui infligeant <physicalDamage>{{ asmoddamagetodeal }} pts de dégâts physiques</physicalDamage> par tir.<br /><br /><spellActive>Troisième activation :</spellActive> A<PERSON>han lâche la corde et effectue un dernier tir.<br /><br />Percuter un champion ennemi ou un élément de terrain met fin prématurément au balancement.<br /><br />Participer à l'élimination de champions met fin au délai de récupération de cette compétence.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AkshanE.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkshanR", "name": "Bien mérité !", "description": "<PERSON><PERSON><PERSON> se verrouille sur un champion ennemi et commence à stocker des balles. Quand il relâche la compétence, il tire toutes les balles stockées et inflige des dégâts selon les PV manquants du premier champion, sbire ou bâtiment touché.", "tooltip": "<PERSON><PERSON><PERSON> se verrouille sur un champion et commence à surcharger son pistolet pendant un maximum de {{ channelduration }} sec, ce qui stocke jusqu'à {{ numberofbullets }} balles.<br /><br /><recast>Réactivation :</recast> <PERSON><PERSON><PERSON> tire toutes les balles stockées, chacune infligeant au moins <physicalDamage>{{ damageperbulletwithcrit }} pts de dégâts physiques</physicalDamage> au premier ennemi ou bâtiment touché. Ces dégâts peuvent augmenter jusqu'à <physicalDamage>{{ maxdamageperbullet }}</physicalDamage> selon les PV manquants de la cible.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>", "Nombre max de balles", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ numberofbullets }} -> {{ numberofbulletsNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "AkshanR.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Tous les trois coups venant de ses attaques ou de ses compétences, <PERSON><PERSON><PERSON> inflige des dégâts supplémentaires. Il gagne aussi un bouclier si la cible est un champion.<br><br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> atta<PERSON>, il effectue une attaque supplémentaire qui inflige moins de dégâts. S'il annule l'attaque supplémentaire, il gagne à la place de la vitesse de déplacement.", "image": {"full": "akshan_p.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}