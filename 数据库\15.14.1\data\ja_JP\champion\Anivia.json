{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Anivia": {"id": "An<PERSON><PERSON>", "key": "34", "name": "アニビア", "title": "氷の不死鳥", "image": {"full": "Anivia.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "34000", "num": 0, "name": "default", "chromas": false}, {"id": "34001", "num": 1, "name": "団結する心アニビア", "chromas": false}, {"id": "34002", "num": 2, "name": "猛禽類アニビア", "chromas": false}, {"id": "34003", "num": 3, "name": "ノクサスキラー アニビア", "chromas": false}, {"id": "34004", "num": 4, "name": "ヘクステック アニビア", "chromas": false}, {"id": "34005", "num": 5, "name": "漆黒の霧氷アニビア", "chromas": false}, {"id": "34006", "num": 6, "name": "先史時代アニビア", "chromas": false}, {"id": "34007", "num": 7, "name": "フェスティバルクイーン アニビア", "chromas": false}, {"id": "34008", "num": 8, "name": "紙細工アニビア", "chromas": true}, {"id": "34017", "num": 17, "name": "宇宙の飛翔アニビア", "chromas": true}, {"id": "34027", "num": 27, "name": "神聖なる不死鳥アニビア", "chromas": true}, {"id": "34037", "num": 37, "name": "魅惑の魔女バットニビア", "chromas": true}, {"id": "34046", "num": 46, "name": "勝利の栄光アニビア", "chromas": true}], "lore": "アニビアは翼を持った慈悲の守護者であり、無限に繰り返される生と死、そして再誕の繰り返しに耐えてフレヨルドを守っている。凍てつくような氷と激しい風の中から生まれた半神は、その元素の力を操って侵略者から自分の故郷を守り、過酷な環境の北部に住む部族たちを守り導いている。彼らにとってアニビアは希望の象徴であり、大いなる変化の前触れとして崇拝されている。彼女は自らの命が尽きるまで戦う。自身を犠牲にすることで彼女の記憶は留まり、新たな明日に向かって生まれ変われることを知っているからだ。", "blurb": "アニビアは翼を持った慈悲の守護者であり、無限に繰り返される生と死、そして再誕の繰り返しに耐えてフレヨルドを守っている。凍てつくような氷と激しい風の中から生まれた半神は、その元素の力を操って侵略者から自分の故郷を守り、過酷な環境の北部に住む部族たちを守り導いている。彼らにとってアニビアは希望の象徴であり、大いなる変化の前触れとして崇拝されている。彼女は自らの命が尽きるまで戦う。自身を犠牲にすることで彼女の記憶は留まり、新たな明日に向かって生まれ変われることを知っているからだ。", "allytips": ["「フラッシュフロスト」から「フロストバイト」につなぐコンボは、大きなダメージ源になる。敵の隙をついて狙っていこう。", "「ブリザード」はマナを大量に消費する。マナを強化するアイテムを購入するか、サモナーズリフトで「ブルーセンチネル」を倒してバフをもらおう。", "序盤は卵を破壊されるリスクがかなり低い。卵になることができるうちは、積極的に攻めることも選択肢にいれよう。"], "enemytips": ["アニビアがレーンにいるときは、積極的にギャンクをしよう。卵になっても、多人数で攻撃すれば6秒以内に倒せる可能性が上がる。", "遠距離攻撃系のチャンピオンでプレイする場合「フラッシュフロスト」で狙われてもよけられるよう、アニビアからは常に十分な距離を取っておくこと。", "ジャングル内でアニビアと戦闘するのは避けよう。ジャングル内では、低レベルの「アイスウォール」でも進路をふさがれる危険がある。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 1, "defense": 4, "magic": 10, "difficulty": 10}, "stats": {"hp": 550, "hpperlevel": 92, "mp": 495, "mpperlevel": 45, "movespeed": 325, "armor": 21, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 600, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.68, "attackspeed": 0.658}, "spells": [{"id": "FlashFrost", "name": "フラッシュフロスト", "description": "指定方向に両翼で力強く羽ばたいて、氷の塊を放つ。氷は触れた敵に魔法ダメージと「チルド」を与える。最大距離に達するかこのスキルを再使用すると氷の塊が炸裂して、範囲内の敵にダメージとスタンを与える。", "tooltip": "巨大な氷の塊を発射して<magicDamage>{{ totalpassthroughdamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間敵を<keywordMajor>「チルド」</keywordMajor>状態にし、{{ spell.glacialstorm:slowamount }}%の<status>スロウ効果</status>を付与する。最大射程に到達すると氷の塊は炸裂し、敵に{{ stunduration }}秒間の<status>スタン効果</status>と、<magicDamage>{{ totalexplosiondamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />氷が飛んでいる最中に<recast>再発動</recast>すると早めに炸裂させられる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "爆発ダメージ", "スタン効果時間:", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ passthroughbasedamage }} -> {{ passthroughbasedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "FlashFrost.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Crystallize", "name": "アイスウォール", "description": "空気中の水分を凝縮して氷の壁をつくり、相手の移動を妨害する。壁は数秒後に溶けて消滅する。", "tooltip": "幅{{ e2 }}ユニットの氷の壁を作り出す。壁は{{ e1 }}秒後に溶けて消滅する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["幅"], "effect": ["{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [17, 17, 17, 17, 17], "cooldownBurn": "17", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [5, 5, 5, 5, 5], [400, 500, 600, 700, 800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5", "400/500/600/700/800", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "Crystallize.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Frostbite", "name": "フロストバイト", "description": "翼をはためかせて対象を凍てつく氷柱で攻撃し、ダメージを与える。直前に「フラッシュフロスト」が当たったか、最大範囲の「ブリザード」でダメージを受けた対象には、2倍のダメージを与える。", "tooltip": "敵を凍てつく氷柱で攻撃して、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<keywordMajor>「チルド」</keywordMajor>状態の敵には、代わりに<magicDamage>{{ empowereddamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Frostbite.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GlacialStorm", "name": "ブリザード", "description": "指定範囲に激しい吹雪を召喚する。吹雪は範囲内の敵に継続ダメージを与え「チルド」を付与する。", "tooltip": "<toggle>発動中:</toggle> 激しい吹雪を召喚し、敵に{{ slowamount }}%の<status>スロウ効果</status>を付与し、<magicDamage>毎秒{{ totaldamagepersecond }}の魔法ダメージ</magicDamage>を与える。「ブリザード」は{{ growthtime }}秒かけて効果範囲が拡大する。<br /><br />「ブリザード」の効果範囲が最大の時は、対象を<keywordMajor>「チルド」</keywordMajor>状態にして{{ slowpercentempoweredtt }}%の<status>スロウ効果</status>を付与し、<magicDamage>毎秒{{ empowereddamagepersecondtooltiponly }}の魔法ダメージ</magicDamage>を与えるようになる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["毎秒ダメージ", "スロウ効果", "「チルド」スロウ効果", "毎秒マナコスト", "クールダウン"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ slowpercentempoweredtt }}% -> {{ slowpercentempoweredttNL }}%", "{{ manacostpersecond }} -> {{ manacostpersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [4, 3, 2], "cooldownBurn": "4/3/2", "cost": [60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " + 毎秒{{ manacostpersecond }}マナ", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "GlacialStorm.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} + 毎秒{{ manacostpersecond }}マナ"}], "passive": {"name": "再誕", "description": "アニビアは力尽きると、体力が最大の状態で卵に戻る。", "image": {"full": "Anivia_P.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}