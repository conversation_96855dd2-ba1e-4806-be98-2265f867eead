{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "Warwick", "title": "<PERSON><PERSON><PERSON> ent<PERSON><PERSON><PERSON>", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "19002", "num": 2, "name": "<PERSON><PERSON>, der Manati", "chromas": false}, {"id": "19003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "chromas": false}, {"id": "19004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON>", "chromas": false}, {"id": "19005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "19006", "num": 6, "name": "Feuerklauen-Warwick", "chromas": false}, {"id": "19007", "num": 7, "name": "Hyänen-Warwick", "chromas": false}, {"id": "19008", "num": 8, "name": "Marodeur-Warwick", "chromas": false}, {"id": "19009", "num": 9, "name": "Urfwick", "chromas": false}, {"id": "19010", "num": 10, "name": "Mondwächter Warwick", "chromas": true}, {"id": "19016", "num": 16, "name": "PROJEKT: Warwick", "chromas": true}, {"id": "19035", "num": 35, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "19045", "num": 45, "name": "Wintergeweihter Warwick", "chromas": false}, {"id": "19046", "num": 46, "name": "Wintergeweihter Warwick (Prestige)", "chromas": false}, {"id": "19056", "num": 56, "name": "<PERSON><PERSON><PERSON> (Arcane)", "chromas": false}], "lore": "Warwick ist ein Monster, das die grauen Gassen von <PERSON> unsicher macht. In qualvollen Experimenten wurde sein Körper transformiert und mit einem komplexen System aus Pumpen und Kammern verschmolzen, dessen Maschinerie alchemistischen Zorn durch seine Adern pumpt. Er macht Jagd auf die Kriminellen, die die Untiefen der Stadt terrorisieren, und bricht aus dem Schatten hervor, wenn sie es am wenigsten erwarten. Warwick ist vollkommen verrückt nach Blut und verliert die Beherrschung, wenn er es wittert … und wer Blut vergießt, kommt nicht davon.", "blurb": "Warwick ist ein Monster, das die grauen Gassen von <PERSON> unsicher macht. In qualvollen Experimenten wurde sein Körper transformiert und mit einem komplexen System aus Pumpen und Kammern verschmolzen, dessen Maschinerie alchemistischen Zorn durch seine...", "allytips": ["Folge den Spuren von „Blutjagd“ zu gegnerischen Champions mit wenig Leben.", "Die Entfernung von „Rasende Wut“ (R) skaliert mit jeglichem zusätzlichen Lauftempo, auch dem von verbündeten Buffs und Beschwörerzaubern.", "„Reißzahn“ (Q) folgt <PERSON>, die laufen, springen oder teleportieren, wenn du den Knopf gedrückt hältst."], "enemytips": ["<PERSON>s <PERSON><PERSON><PERSON> heilen ihn bei niedrigem <PERSON>. Mach ihn dann kampfunfähig, um ihn erledigen zu können.", "Warwick ist stärker gegen Gegner mit niedrigem Leben. Pass auf dein Leben auf, um nicht auf seinem Speiseplan zu landen.", "Die Reichweite von Warwicks ultimativer Fähigkeit skaliert mit seinem Lauftempo."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON> springt vor und beißt sein <PERSON>, wobei er Schaden basierend auf dessen maximalem Leben anrichtet und sich selbst um diesen Schaden heilt.", "tooltip": "<tap><PERSON><PERSON> drücken:</tap> <PERSON> springt nach vorne und beißt zu, wodurch er <magicDamage>magischen <PERSON></magicDamage> in <PERSON>ö<PERSON> von {{ basebitedamage }} plus {{ targetpercenthpdamage }}&nbsp;% des maximalen Lebens verursacht und <healing>Leben in Höhe von {{ e3 }}&nbsp;%</healing> des verursachten Schadens wiederherstellt.<br /><br /><hold>Gedr<PERSON>t halten:</hold> Warwick springt hinter das Ziel und schnappt zu. Während er sich festbeißt, folgt Warwick allen Bewegungen seines Opfers. Nachdem er es freilässt, verursacht er denselben Schaden und heilt sich um denselben Betrag.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung", "Schaden in Höhe eines Prozentsatzes des Lebens", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e3 }}&nbsp;% -> {{ e3NL }}&nbsp;%", "{{ targetpercenthpdamage }}&nbsp;% -> {{ targetpercenthpdamageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickW", "name": "Blutjagd", "description": "Warwick spürt G<PERSON> mit weniger als 50&nbsp;% ihres Lebens auf und erhält erhöhtes Lauftempo, wenn er sich auf sie zubewegt, sowie Ang<PERSON>stempo, wenn er sie angreift. Bei Gegnern unter 25&nbsp;% verfällt er in Raserei und die Boni verdreifachen sich.", "tooltip": "<spellPassive>Passiv:</spellPassive> Warwick spürt Champions mit weniger als 50&nbsp;% ihres Lebens auf und erhält <speed>{{ passivemsbonus }}&nbsp;% Lauftempo</speed>, wenn er sich auf sie zubewegt. Fähigkeiten und Angriffe gegen Gegner mit weniger als 50&nbsp;% ihres Lebens gewähren <speed>{{ passiveasbonus }}&nbsp;% Angriffstempo</speed>. Diese Effekte werden um 200&nbsp;% erhöht, wenn die Gegner über weniger als 25&nbsp;% ihres Lebens verfügen. <br /><br /><spellActive>Aktiv:</spellActive> Warwick spürt kurzzeitig alle Gegner auf und erhält 8&nbsp;Sekunden lang den passiven Effekt dieser Fähigkeit gegen den nächstbefindlichen Champion, unabhängig von dessen Leben. Findet Warwick keine Champions, wird die Abklingzeit dieser Fähigkeit um 30&nbsp;% verkürzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "Angriffstempo", "Abklingzeit"], "effect": ["{{ passivemsbonus }}&nbsp;% -> {{ passivemsbonusNL }}&nbsp;%", "{{ passiveasbonus }}&nbsp;% -> {{ passiveasbonusNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Warwick erhält 2,5 Sekunden lang reduzierten Schaden. Wenn die Fähigkeit endet oder ein 2. <PERSON> aktiviert wird, heult er auf und lässt nahe Gegner 1 Sekunde lang fliehen.", "tooltip": "Warwick erhält 2,5&nbsp;Sekunden lang {{ e1 }}&nbsp;% Schadensverringerung. Nach dem Ende heult Warwick auf und versetzt Gegner in der Nähe {{ e3 }}&nbsp;Sekunde(n) lang in <status>Furcht</status>. <PERSON> kann die Fähigkeit <recast>reaktivieren</recast>, um sie vorzeitig zu beenden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schadensverringerung", "Abklingzeit"], "effect": ["{{ e1 }}&nbsp;% -> {{ e1NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickR", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON> springt in eine Richtung (skaliert mit seinem zusätzlichen Lauftempo) und unterdrückt 1,5&nbsp;Sekunden lang den ersten Champion, mit dem er zusammenstößt.", "tooltip": "<PERSON> springt über eine weite Entfernung, die mit seinem <speed>Lauftempo</speed> skaliert, und <status>unterdrückt</status> den ersten Champion, auf den er trifft, während er {{ rduration }}&nbsp;Sekunden lang kanalisiert. Er greift diesen Champion während der Dauer dreimal an und verursacht <magicDamage>{{ damagecumulative }}&nbsp;magischen Schaden</magicDamage>. <PERSON> stellt während der Kanalisierung <healing>Leben</healing> in Hö<PERSON> von 100&nbsp;% des gesamten verursachten Schadens wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Warwicks normale Angriffe verursachen zusätzlichen magischen Schaden. Wenn Warwicks Leben unter 50&nbsp;% fällt, heilt er sich um den gleichen Betrag. Die Heilung verdreifacht sich, wenn Warwicks Leben unter 25&nbsp;% fällt.", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}