{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nasus": {"id": "<PERSON><PERSON>", "key": "75", "name": "<PERSON><PERSON>", "title": "il custode delle dune", "image": {"full": "Nasus.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "75000", "num": 0, "name": "default", "chromas": false}, {"id": "75001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "75002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "75003", "num": 3, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "75004", "num": 4, "name": "Riot K-9 Nasus", "chromas": false}, {"id": "75005", "num": 5, "name": "Nasus Infernale", "chromas": false}, {"id": "75006", "num": 6, "name": "Duca Conte Nasus", "chromas": false}, {"id": "75010", "num": 10, "name": "Nasus Distruttore del mondo", "chromas": false}, {"id": "75011", "num": 11, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "75016", "num": 16, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "75025", "num": 25, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "75035", "num": 35, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "75045", "num": 45, "name": "Nasus Portatore della Notte", "chromas": true}, {"id": "75054", "num": 54, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Nasus è un imponente essere Asceso con il volto da sciacallo originario dell'antica Shurima, una figura eroica che la gente del deserto considera un semidio. Estremamente intelligente, era un guardiano della conoscenza e un eccelso stratega il cui buon senso guidò per secoli l'antico impero di Shurima verso la grandezza. In seguito alla caduta dell'impero, andò in esilio per sua scelta, diventando poco più che una leggenda. Ora che l'antica città di Shurima è risorta, Nasus è tornato, determinato a impedire un'altra simile sorte.", "blurb": "Nasus è un imponente essere Asceso con il volto da sciacallo originario dell'antica Shurima, una figura eroica che la gente del deserto considera un semidio. Estremamente intelligente, era un guardiano della conoscenza e un eccelso stratega il cui buon...", "allytips": ["Usare bene Attacco incrementale per finire i minion ha un grande impatto alla fine della partita.", "Se sei da solo, Fuoco dello spirito è un ottimo strumento per finire i minion. Può non essere ideale quando si è in coppia e si avanza troppo. Trova l'equilibrio giusto tra Attacco incrementale e il farming ad area.", "Se hai delle difese basse, gli avversari cercheranno di concentrarsi su di te, anche se stai usando l'abilità suprema. Prova a comprare qualche oggetto per la sopravvivenza anche se ti stai costruendo come DPS."], "enemytips": ["Quando è trasformato dalla sua abilità suprema, Nasus è uno dei campioni più forti nella League. Ingaggialo solo se hai un grande vantaggio.", "Un Deperimento di livello massimo è una contromossa efficace contro personaggi con attacco fisico, quindi evita sempre di farti trovare da solo.", "Cerca di aggirarlo e non affrontarlo se la sua salute è al massimo."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 5, "magic": 6, "difficulty": 6}, "stats": {"hp": 631, "hpperlevel": 104, "mp": 326, "mpperlevel": 62, "movespeed": 350, "armor": 34, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.45, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 67, "attackdamageperlevel": 4, "attackspeedperlevel": 3.48, "attackspeed": 0.638}, "spells": [{"id": "NasusQ", "name": "Attacco incrementale", "description": "Nasus colpisce il suo nemico, infligge danni e, se uccide il bersaglio, aumenta il potere del suo prossimo Attacco incrementale.", "tooltip": "Il prossimo attacco di Nasus infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>. Uccidere un nemico con questo attacco aumenta permanentemente il suo danno di {{ basicstacks }}, aumentato a {{ bigstacks }} contro campioni, minion grandi e mostri grandi della giungla.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Ricarica"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7.5, 6.5, 5.5, 4.5, 3.5], "cooldownBurn": "7.5/6.5/5.5/4.5/3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [255, 255, 255, 255, 255], "rangeBurn": "255", "image": {"full": "NasusQ.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusW", "name": "Deperimento", "description": "Nasus invecchia un campione nemico, rallentando la sua velocità di movimento e di attacco nel tempo.", "tooltip": "Nasus invecchia un campione, <status>rallentandolo</status> di un {{ slowbase }}% e fino a {{ maxslowtooltiponly }}% nel corso di {{ duration }} secondi. La <attackSpeed>velocità d'attacco</attackSpeed> viene inoltre ridotta per un {{ attackspeedslowmult*100 }}% del <status>rallentamento</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rallentamento massimo", "Ricarica"], "effect": ["{{ maxslowtooltiponly }}% -> {{ maxslowtooltiponlyNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "NasusW.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusE", "name": "Fuoco dello spirito", "description": "Nasus scatena uno spirito fiamma in una zona del campo di battaglia, infliggendo danni e riducendo l'armatura dei nemici che sono all'interno di tale area.", "tooltip": "Nasus accende una fiamma spirituale, infliggendo <magicDamage>{{ initialdamage }} danni magici</magicDamage>. I nemici nell'area perdono <scaleArmor>{{ e2 }}% armatura</scaleArmor> e subiscono <magicDamage>{{ totaldotdamage }} danni magici</magicDamage> per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> al secondo", "Riduzione armatura %", "Costo in @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [10, 16, 22, 28, 34], [30, 35, 40, 45, 50], [5, 5, 5, 5, 5], [50, 80, 110, 140, 170], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/16/22/28/34", "30/35/40/45/50", "5", "50/80/110/140/170", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NasusE.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NasusR", "name": "Furia della sabbia", "description": "Nasus scatena una possente tempesta di sabbia che colpisce i nemici vicini. Durante la tempesta ottiene salute e gittata d'attacco, danneggia i nemici vicini, ha una ricarica ridotta su Attacco incrementale e ottiene armatura e resistenza magica bonus.", "tooltip": "Nasus viene potenziato dalla tempesta di sabbia per 15 secondi, aumentando la sua <healing>salute massima di {{ bonushealth }}</healing> e l'<scaleArmor>armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR> di {{ initialresistgain }}.<br /><br />Mentre la tempesta imperversa, i nemici nelle vicinanze subiscono <magicDamage>{{ damagecalc }} della loro salute massima in danni magici</magicDamage> ogni secondo e <spellName>Attacco incrementale</spellName> ha la ricarica ridotta del {{ qcdr*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute bonus", "% salute massima", "Armatura bonus e resistenza magica", "Ricarica"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ aoedamagepercent*100.000000 }}% -> {{ aoedamagepercentnl*100.000000 }}%", "{{ initialresistgain }} -> {{ initialresistgainNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NasusR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Divora anime", "description": "Nasus assorbe l'energia spirituale del nemico e ottiene rubavita bonus.", "image": {"full": "Nasus_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}