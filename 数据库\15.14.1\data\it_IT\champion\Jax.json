{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "Jax", "title": "gran maestro d'armi", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "<PERSON>ssente Jax", "chromas": false}, {"id": "24002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "24003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX Jax", "chromas": false}, {"id": "24005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "24006", "num": 6, "name": "Jax del Tempio", "chromas": false}, {"id": "24007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "24008", "num": 8, "name": "Jax SKT T1", "chromas": false}, {"id": "24012", "num": 12, "name": "<PERSON>quisitore", "chromas": false}, {"id": "24013", "num": 13, "name": "<PERSON>", "chromas": false}, {"id": "24014", "num": 14, "name": "Jax dei Regni Mecha", "chromas": true}, {"id": "24020", "num": 20, "name": "<PERSON>", "chromas": false}, {"id": "24021", "num": 21, "name": "Jax Con<PERSON>atore (edizione prestigio)", "chromas": false}, {"id": "24022", "num": 22, "name": "<PERSON>", "chromas": true}, {"id": "24032", "num": 32, "name": "Neo PAX Jax", "chromas": false}, {"id": "24033", "num": 33, "name": "PROGETTO: <PERSON>", "chromas": true}], "lore": "Jax non conosce rivali, né con le sue armi, né con il suo pungente sarcasmo. È l'ultimo maestro d'armi conosciuto di Icathia. <PERSON><PERSON> che la sua tracotanza ha scatenato il Vuoto, devastando la sua terra, <PERSON> ha giurato di proteggere quel poco che ne restava. Ora che la magia sta prendendo piede nel mondo, la minaccia sopita torna ad agitarsi. Jax si aggira per Valoran, brandendo l'ultima luce di Icathia e mettendo alla prova i guerrieri che incontra, per vedere se sono abbastanza forti da lottare al suo fianco.", "blurb": "Jax non conosce rivali, né con le sue armi, né con il suo pungente sarcasmo. È l'ultimo maestro d'armi conosciuto di Icathia. <PERSON><PERSON> che la sua tracotanza ha scatenato il Vuoto, devastando la sua terra, <PERSON> ha giurato di proteggere quel poco che ne...", "allytips": ["Jax può spiccare un Balzo mistico verso le unità alleate, inclusi lumi. Puoi usarle per preparare la tua fuga.", "Jax è favorito dagli oggetti che hanno sia potere magico sia attacco fisico come Lama della furia di Guinsoo e Pistola a lame hextech."], "enemytips": ["Cerca di ingaggiarlo con raffiche veloci piuttosto che in un corpo a corpo. Se riesci a non farlo attaccare consecutivamente, riduci i danni che infligge.", "Jax può schivare tutti gli attacchi in arrivo per un breve periodo di tempo e stordire i nemici in mischia quando finisce l'effetto. Aspetta a colpirlo finché la sua schivata non è finita."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "<PERSON><PERSON><PERSON> mistico", "description": "Jax balza verso un'unità. Se è nemica, la colpisce con la sua arma.", "tooltip": "Jax balza su un'unità o un lume nemico o alleato, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> se il bersaglio è un nemico.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxW", "name": "Caricamento energetico", "description": "Jax carica la sua arma di energia, che infligge con il suo prossimo attacco danni bonus.", "tooltip": "Jax potenzia la sua arma con energia e il suo prossimo attacco o <spellName>Ba<PERSON><PERSON> mistico</spellName> infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxE", "name": "Contrattacco stordente", "description": "La bravura di Jax nel combattimento gli permette di schivare tutti gli attacchi per un breve periodo di tempo e, quindi, contrattaccare velocemente, stordendo tutti i nemici circostanti.", "tooltip": "Jax assume una posizione difensiva fino a {{ dodgeduration }} secondi, schivando gli attacchi e subendo un {{ aoedamagereduction }}% di danni in meno dalle abilità ad area. Dopo {{ dodgeduration }} secondi o se questa abilità viene <recast>rilanciata</recast>, <PERSON> infligge <magicDamage>{{ totaldamage }} più un {{ percenthealthdamage }}% di salute massima del bersaglio in danni magici</magicDamage> e <status>stordisce</status> i nemici nelle vicinanze per {{ stunduration }} secondo. <br /><br />I danni aumentano di un {{ percentincreasedperdodge*100 }}% per ogni attacco schivato, fino a un massimo di <magicDamage>{{ maxdamage }} + {{ maxpercenthealthdamage }}% della salute massima</magicDamage> del bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JaxR", "name": "Gran maestro d'armi", "description": "Ogni terzo attacco consecutivo infligge danni magici bonus. In più, Jax può attivare questa abilità per infliggere danni intorno a sé e rafforzare la sua determinazione, aumentando la sua armatura e la sua resistenza magica per un breve periodo.", "tooltip": "<spellPassive>Passiva:</spellPassive> ogni terzo attacco entro {{ passivefallofftime }} secondi infligge <magicDamage>{{ onhitdamage }} danni magici</magicDamage> aggiuntivi.<br /><br /><spellActive>Attiva:</spellActive> Jax getta a terra la sua lanterna, infliggendo <magicDamage>{{ swingdamagetotal }} danni magici</magicDamage> ai nemici vicini. Se colpisce un campione ottiene <scaleArmor>{{ basearmor }} armatura</scaleArmor> e <scaleMR>{{ basemr }} resistenza magica</scaleMR>, più <scaleArmor>{{ bonusarmor }} armatura</scaleArmor> e <scaleMR>{{ bonusmr }} resistenza magica</scaleMR> per ogni campione colpito per i prossimi {{ duration }} secondi. In questo periodo, infligge <magicDamage>danni magici</magicDamage> aggiuntivi ogni secondo attacco invece di ogni terzo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> passivi", "<PERSON><PERSON>", "Armatura base", "Resistenza magica base", "Armatura per campione aggiuntivo", "Resistenza magica per campione aggiuntivo", "Ricarica"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ swingdamagebase }} -> {{ swingdamagebaseNL }}", "{{ baseresists }} -> {{ baseresistsNL }}", "{{ baseresists*0.600000 }} -> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }} -> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }} -> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Assalto implacabile", "description": "Gli attacchi base consecutivi di Jax aumentano continuamente la sua velocità d'attacco.", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}