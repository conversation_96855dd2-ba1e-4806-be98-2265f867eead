{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sett": {"id": "<PERSON><PERSON>", "key": "875", "name": "<PERSON><PERSON>", "title": "the Boss", "image": {"full": "Sett.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "875000", "num": 0, "name": "default", "chromas": false}, {"id": "875001", "num": 1, "name": "Mecha <PERSON>", "chromas": true}, {"id": "875008", "num": 8, "name": "Obsidian Dragon Sett", "chromas": true}, {"id": "875009", "num": 9, "name": "Prestige Obsidian Dragon Sett", "chromas": false}, {"id": "875010", "num": 10, "name": "Pool Party Sett", "chromas": true}, {"id": "875019", "num": 19, "name": "Firecracker Sett", "chromas": true}, {"id": "875038", "num": 38, "name": "Spirit Blossom Sett", "chromas": true}, {"id": "875045", "num": 45, "name": "Soul Fighter Sett", "chromas": true}, {"id": "875056", "num": 56, "name": "HEARTSTEEL Sett", "chromas": true}, {"id": "875066", "num": 66, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "A leader of Ionia's growing criminal underworld, <PERSON><PERSON> rose to prominence in the wake of the war with Noxus. Though he began as a humble challenger in the fighting pits of Navori, he quickly gained notoriety for his savage strength, and his ability to take seemingly endless amounts of punishment. Now, having climbed through the ranks of local combatants, <PERSON><PERSON> has muscled to the top, reigning over the pits he once fought in.", "blurb": "A leader of Ionia's growing criminal underworld, <PERSON><PERSON> rose to prominence in the wake of the war with Noxus. Though he began as a humble challenger in the fighting pits of Navori, he quickly gained notoriety for his savage strength, and his ability to...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "Grit", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 670, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "SettQ", "name": "Knuckle Down", "description": "<PERSON><PERSON>'s next two attacks deal additional damage based off of the target's max health. <PERSON><PERSON> also gains Move Speed while moving towards enemy champions.", "tooltip": "Sett itches for a fight, gaining <speed>{{ msamount*100 }}% Move Speed</speed> towards enemy champions for {{ msduration }} seconds.<br /><br />Additionally <PERSON><PERSON>'s next two Attacks deal an additional <physicalDamage>{{ basedamage }} plus {{ maxhealthdamagecalc }} max Health physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Base Damage", "% Max Health Per 100 AD"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthtadratiotooltip }}% -> {{ maxhealthtadratiotooltipNL }}%"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "SettQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "SettW", "name": "Haymaker", "description": "<PERSON><PERSON> passively stores damage he takes as Grit. On cast, <PERSON><PERSON> expends all stored Grit to gain a shield and punch an area, dealing true damage in the center and physical damage on the sides.", "tooltip": "<spellPassive>Passive:</spellPassive> Sett stores {{ damagestored*100 }}% of damage taken as <keywordMajor>Grit</keywordMajor>, up to <keywordMajor>{{ maxgrit }}</keywordMajor>. <keywordMajor>Grit</keywordMajor> decays quickly {{ adrenalinestoragewindow }} seconds after the damage was taken.<br /><br /><spellActive>Active:</spellActive> Sett consumes all <keywordMajor>Grit</keywordMajor>, gaining a <shield>{{ shieldconversion*100 }}% Grit consumed Shield</shield> decaying over {{ shieldmaxduration }} seconds. <PERSON>t then delivers a massive punch, dealing <trueDamage>{{ damagecalc }} plus {{ damageconversion }} Grit consumed true damage</trueDamage> to enemies in the center (max <trueDamage>{{ f1 }} damage</trueDamage>). Enemies not in the center instead take <physicalDamage>physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Base Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SettW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "SettE", "name": "Facebreaker", "description": "<PERSON><PERSON> pulls in all enemies on opposite sides of him, dealing damage and stunning them. If enemies were only on one side, they are slowed instead of stunned.", "tooltip": "<PERSON><PERSON> smashes enemies on either side of him into each other, dealing <physicalDamage>{{ damagecalc }} physical damage</physicalDamage> and <status>Slowing</status> them by {{ slowamount*100 }}% for {{ slowduration }} seconds. If <PERSON><PERSON> grabbed at least one enemy on each side, all enemies are <status>Stunned</status> for {{ stunduration }} second.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Base Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "SettE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "SettR", "name": "The Show Stopper", "description": "<PERSON><PERSON> carries an enemy champion through the air and slams them into the ground, dealing damage and slowing all enemies near where they land.", "tooltip": "<PERSON><PERSON> grabs an enemy champion and <status>Suppresses</status> them as he carries them forward, then slams them into the ground, dealing <physicalDamage>{{ damagecalc }} plus {{ maxhealthdamage*100 }}% of the grabbed enemy's bonus Health physical damage</physicalDamage> to surrounding enemies and <status>Slowing</status> them by {{ slowamount*100 }}% for {{ slowduration }} second. Enemies take less damage the further they are from where <PERSON><PERSON> lands.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Bonus Health Damage", "Base Damage"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "SettR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "No Cost"}], "passive": {"name": "Pit Grit", "description": "<PERSON><PERSON>'s basic attacks alternate between left and right punch. Right punch is slightly stronger and faster. <PERSON><PERSON> also hates losing, gaining additional health regeneration based off of his missing health.", "image": {"full": "Sett_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}