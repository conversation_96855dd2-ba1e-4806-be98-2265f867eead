{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Khazix": {"id": "Khazix", "key": "121", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "il razziatore del Vuoto", "image": {"full": "Khazix.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "121000", "num": 0, "name": "default", "chromas": false}, {"id": "121001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "121002", "num": 2, "name": "Kha'Zix Guardiano delle Sabbie", "chromas": false}, {"id": "121003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Bocciolo Mortale", "chromas": false}, {"id": "121004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Oscura", "chromas": false}, {"id": "121011", "num": 11, "name": "Kha'Zix Mondiali 2018", "chromas": true}, {"id": "121060", "num": 60, "name": "Kha'Zix dell'Odissea", "chromas": false}, {"id": "121069", "num": 69, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "121079", "num": 79, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Il Vuoto cresce e si adatta: questa verità è particolarmente evidente in Kha'Zix. L'evoluzione guida il suo orrore mutante, nato per sopravvivere e uccidere i più forti. Ogni difficoltà gli fa scoprire nuovi e più efficaci modi per uccidere la sua preda. Nonostante all'inizio fosse un animale irrazionale, l'intelligenza di Kha'Zix si è sviluppata insieme al suo corpo. Ora la creatura pianifica la sua caccia e approfitta del terrore che genera nelle sue vittime.", "blurb": "Il Vuoto cresce e si adatta: questa verità è particolarmente evidente in Kha'Zix. L'evoluzione guida il suo orrore mutante, nato per sopravvivere e uccidere i più forti. Ogni difficoltà gli fa scoprire nuovi e più efficaci modi per uccidere la sua preda...", "allytips": ["I nemici sono considerati isolati se non hanno alleati entro una breve distanza. I danni de Il sapore della paura aumentano enormemente contro questi bersagli.", "Minaccia celata si attiva quando Kha'Zix non viene visto dalla squadra nemica. Riattivalo usando l'erba alta o Assalto del Vuoto. Non dimenticare di applicare la Minaccia celata attaccando i campioni nemici.", "K<PERSON>'<PERSON><PERSON> ha molta libertà di scelta su dove e quando attaccare. Scegli bene i tuoi scontri per vincere."], "enemytips": ["Il sapore della paura infligge danni fisici bonus ai bersagli isolati. Riceve un vantaggio combattendo nei pressi di minion, campioni e torrette alleate.", "Balzo e Assalto del Vuoto hanno tempi di ricarica lunghi. Kha'Zix è molto vulnerabile quando non sono disponibili."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 6}, "stats": {"hp": 643, "hpperlevel": 99, "mp": 327, "mpperlevel": 40, "movespeed": 350, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7.59, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.668}, "spells": [{"id": "KhazixQ", "name": "Il sapore della paura", "description": "Infligge danni fisici al bersaglio. Danni aumentati sui bersagli <font color='#FFF673'>isolati</font>. Se sceglie <font color='#00DD33'>Evoluzione: Artigli <PERSON></font>, rimborsa una parte della sua ricarica contro i bersagli <font color='#FFF673'>isolati</font>. Kha'Zix ottiene anche più gittata per gli attacchi base e Il sapore della paura.", "tooltip": "Kha'Z<PERSON> colpisce un nemico vicino, infliggendo <physicalDamage>{{ spell.khazixq:basedamage }} danni fisici</physicalDamage>. Questa abilità infligge invece <physicalDamage>{{ spell.khazixq:isodamage }} danni</physicalDamage> ai nemici <keywordMajor>isolati</keywordMajor> dagli alleati. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [80, 105, 130, 155, 180], [110, 110, 110, 110, 110], [50, 50, 50, 50, 50], [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/105/130/155/180", "110", "50", "45", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "KhazixQ.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixW", "name": "Spina del Vuoto", "description": "Kha'Zix spara aculei esplosivi che infliggono danni fisici ai nemici colpiti. Kha'Zix viene curato se è entro la portata dell'esplosione. Se sceglie <font color='#00DD33'>Evoluzione: File di aculei</font>, Spina del Vuoto lancia tre spine in un'area conica, rallenta i nemici colpiti e rivela per 2 secondi i campioni nemici colpiti. I bersagli <font color='#FFF673'>isolati</font> vengono rallentati maggiormente.", "tooltip": "<PERSON><PERSON>'<PERSON>ix spara un aculeo che infligge <physicalDamage>{{ basedamage }} danni fisici</physicalDamage> al primo nemico colpito in una piccola area. Se Kha'Zix è all'interno di quest'area, recupera <healing>{{ healamount }} salute</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [85, 115, 145, 175, 205], [55, 75, 95, 115, 135], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "85/115/145/175/205", "55/75/95/115/135", "40", "2", "0", "2", "60", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KhazixW.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixE", "name": "<PERSON><PERSON><PERSON>", "description": "Kha'Zix balza in un'area, infliggendo danni fisici all'impatto. Se sceglie <font color='#00DD33'>Evoluzione: Ali</font>, la distanza del Balzo aumenta di 200 e la ricarica si azzera all'uccisione di campioni o con gli assist.", "tooltip": "Kha'Zix effettua un balzo, infliggendo <physicalDamage>{{ totaldamage }} danni fi<PERSON>ci</physicalDamage> quando atterra.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [30, 35, 40, 45, 50], [10, 10, 10, 10, 10], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "30/35/40/45/50", "10", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "KhazixE.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KhazixR", "name": "Assalto del Vuoto", "description": "Ogni livello permette a Kha'Zix di evolvere una delle sue abilità, dandogli un effetto aggiuntivo unico. Quando è attivo, Kha'Zix diventa <font color='#91d7ee'>invisibile</font>, attivando Minaccia celata e aumentando la velocità di movimento. Se sceglie <font color='#00DD33'>Evoluzione: Mimetismo dinamico,</font> la durata dell'<font color='#91d7ee'>invisibilità</font> di Assalto del Vuoto aumenta e ha un uso aggiuntivo.", "tooltip": "<spellActive>Attiva:</spellActive> Kha'Zix diventa <keywordStealth>invisibile</keywordStealth> per {{ stealthduration }} secondi e ottiene <speed>{{ bonusmovementspeedpercent*100 }}% velocità di movimento</speed>. Kha'Zix può <recast>rilanciare</recast> questa abilità una volta entro {{ recastwindow }} secondi.<br /><br /><spellPassive>Passiva:</spellPassive> aumentare di livello questa abilità permette a Kha'Zix di far <evolve>evolvere</evolve> una delle sue abilità, conferendogli effetti aggiuntivi.<li><spellName>Il sapore della paura:</spellName> ottiene gittata per abilità e attacchi e riduce la ricarica del {{ spell.khazixq:effect4amount }}% contro i bersagli <keywordMajor>isolati</keywordMajor>.<li><spellName>Spina del Vuoto:</spellName> spara 3 aculei e <status>rallenta</status> i bersagli del {{ spell.khazixw:effect3amount }}%, aumentato contro quelli <keywordMajor>isolati</keywordMajor>.<li><spellName>Balzo:</spellName> aumenta la gittata e azzera la ricarica quando elimina un campione.<li><spellName>Assalto del Vuoto:</spellName> l'<keywordStealth>invisibilità</keywordStealth> dura {{ evolvedstealthduration }} secondi e ottiene la possibilità di essere <recast>rilanciata</recast> una seconda volta.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Evoluzioni disponibili", "Ricarica"], "effect": ["{{ evolutionsavailable }} -> {{ evolutionsavailableNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "KhazixR.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Minaccia celata", "description": "I nemici vicini <font color='#FFF673'>isolati</font> dagli alleati sono marchiati. Le abilità di Kha'Zix hanno interazioni con i bersagli <font color='#FFF673'>isolati</font>.<br><br><PERSON><PERSON><PERSON>'Zix non è visibile alla squadra nemica, otti<PERSON> celata. Il suo prossimo attacco base contro un campione nemico infligge danni magici bonus e lo rallenta per qualche secondo.", "image": {"full": "Khazix_P.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}