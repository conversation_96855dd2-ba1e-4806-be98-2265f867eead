{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shaco": {"id": "Shaco", "key": "35", "name": "Shaco", "title": "il demone giullare", "image": {"full": "Shaco.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "35000", "num": 0, "name": "default", "chromas": false}, {"id": "35001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "35002", "num": 2, "name": "Shaco Reale", "chromas": false}, {"id": "35003", "num": 3, "name": "Schiacciashaco", "chromas": false}, {"id": "35004", "num": 4, "name": "Shaco a Carica", "chromas": false}, {"id": "35005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "35006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "35007", "num": 7, "name": "<PERSON><PERSON><PERSON> ", "chromas": false}, {"id": "35008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "35015", "num": 15, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "35023", "num": 23, "name": "Shaco <PERSON>o nella Città del crimine", "chromas": true}, {"id": "35033", "num": 33, "name": "Shaco Favore dell'Inverno", "chromas": true}, {"id": "35043", "num": 43, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "35044", "num": 44, "name": "<PERSON><PERSON><PERSON> (edizione prestigio)", "chromas": false}, {"id": "35054", "num": 54, "name": "Shaco Notte Inquietante", "chromas": true}, {"id": "35064", "num": 64, "name": "<PERSON><PERSON><PERSON> a molla", "chromas": true}], "lore": "<PERSON><PERSON><PERSON>, la marionetta incantata creata molto tempo fa come giocattolo per un giovane principe, ora si diletta a uccidere. Corrotto dalla magia oscura e dalla perdita del suo amato compito, quello che un tempo era un pupazzo gentile gode solo della miseria delle povere anime che tormenta. Utilizza in modo letale giocattoli e trucchetti, e si diverte vedendo i risultati dei suoi sanguinosi \"giochi\"... se sentite una risata oscura nella notte, il demone giullare potrebbe avervi scelto come prossimo compagno di giochi.", "blurb": "<PERSON><PERSON><PERSON>, la marionetta incantata creata molto tempo fa come gio<PERSON>tolo per un giovane principe, ora si diletta a uccidere. Corrotto dalla magia oscura e dalla perdita del suo amato compito, quello che un tempo era un pupazzo gentile gode solo della...", "allytips": ["Usare Inganno oltre i muri ti permette di effettuare una fuga perfetta.", "Prova a comprare oggetti con effetti sul colpo. Ne ricava beneficio anche il tuo clone da Allucinazione.", "I danni di Tradimento possono essere incrementati con effetti di aumento dei danni critici, come Frammento d'infinito."], "enemytips": ["Se Shaco sta giocando bene nella prima parte della partita, piazzare lumi invisibili vicino ai suoi campi della giungla è un buon investimento.", "Se Shaco usa Inganno per entrare in un combattimento non potrà usarlo per poter scappare. Fate squadra per abbatterlo velocemente."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 6, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 297, "mpperlevel": 40, "movespeed": 345, "armor": 30, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.694}, "spells": [{"id": "Deceive", "name": "Inganno", "description": "<PERSON>haco diventa invisibile e si teletrasporta nella posizione bersaglio.<br><br>Il suo primo attacco da invisibile è potenziato, infligge danni bonus e mette a segno un colpo critico se attacca da dietro.", "tooltip": "Shaco si teletrasporta e diventa <keywordStealth>invisibile</keywordStealth> per {{ stealthduration }} secondi. Usare <spellName><PERSON>ato<PERSON> Joker a molla</spellName> o <spellName>Allucinazione</spellName> non interrompe l'<keywordStealth>invisibilità</keywordStealth>.<br /><br />Il successivo attacco di Shaco mentre è <keywordStealth>invisibile</keywordStealth> infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> in più. Se colpisce da dietro, questo attacco infligge colpi critici per {{ qcritdamagemod }} danni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata invisibilità", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Deceive.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JackInTheBox", "name": "Scatola Joker a molla", "description": "<PERSON><PERSON><PERSON> crea una scatola Joker a molla invisibile. Una volta attivata, impaurisce e attacca i nemici nelle vicinanze.", "tooltip": "<PERSON><PERSON>co crea una trappola che diventa invisibile dopo {{ e5 }} secondi e dura per {{ trapduration }} secondi. Si attiva se un nemico si avvicina o se viene rivelata, <status>impaurendo</status> i campioni nemici vicini per {{ fearduration }} secondi, o {{ minionfearduration }} secondi per i minion e i mostri della giungla.<br /><br />Una volta attivata, la trappola spara a tutti i nemici vicini per 5 secondi, infliggendo <magicDamage>{{ aoedamage }} danni magici</magicDamage>, o <magicDamage>{{ stdamage }} danni</magicDamage> se su un singolo bersaglio.<br /><br />Gli attacchi di una Scatola joker a molla infliggono <magicDamage>{{ monsterbonusdamage }}</magicDamage> danni magici aggiuntivi ai mostri.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> paura", "Danni bonus ai mostri", "Costo in @AbilityResourceName@"], "effect": ["{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ fearduration }} -> {{ feardurationNL }}", "{{ monsterbonusdamage }} -> {{ monsterbonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 65, 60, 55, 50], "costBurn": "70/65/60/55/50", "datavalues": {}, "effect": [null, [35, 50, 65, 80, 95], [200, 300, 400, 500, 600], [0.5, 0.75, 1, 1.25, 1.5], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/50/65/80/95", "200/300/400/500/600", "0.5/0.75/1/1.25/1.5", "40", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "JackInTheBox.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwoShivPoison", "name": "Due lame del veleno", "description": "I pugnali di Shaco avvelenano passivamente i bersagli sul colpo, rallentando la loro velocità di movimento. Può lanciare i suoi coltelli per infliggere danni e avvelenare il bersaglio. Il coltello lanciato infligge danni bonus se il bersaglio ha meno del 30% di salute massima.", "tooltip": "<spellPassive>Passiva:</spellPassive> quando questa abilità è pronta per essere lanciata, gli attacchi base di Shaco <status>rallentano</status> il bersaglio del {{ slowamount*-100 }}% per {{ slowdurationpassive }} secondi.<br /><br /><spellActive>Attiva:</spellActive> Shaco lancia una lama che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> al nemico bersaglio e lo <status>rallentano</status> del {{ slowamount*-100 }}% per {{ slowdurationactive }} secondi. Se il bersaglio ha meno del {{ executehealththreshold*100 }}% di salute, il pugnale infligge invece <magicDamage>{{ totalexecutedamage }} danni</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "TwoShivPoison.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HallucinateFull", "name": "Allucinazione", "description": "<PERSON><PERSON><PERSON> crea un'illusione di se stesso, in grado di attaccare i nemici vicini (infligge danni ridotti alle torri). Alla morte, esplode, generando tre Scatole Joker a molla e infliggendo danni ai nemici nelle vicinanze.", "tooltip": "<PERSON><PERSON><PERSON> scompare e riappare con un clone che dura fino a {{ clonelifetime }} secondi ed esplode quando muore, infliggendo <magicDamage>{{ explosiontotaldamage }} danni magici</magicDamage> ai nemici nelle vicinanze e generando tre mini <spellName>Scatole Joker a molla</spellName> che si attivano all'istante. Il clone infligge un {{ cloneaadamagepercent*100 }}% del danno di Shaco e riceve {{ cloneincomingdamagepercent*100 }}% danni in più.<br /><br />Le mini <spellName>Scatole Joker a molla</spellName> infliggono <magicDamage>{{ aoedamage }} danni magici</magicDamage>, o <magicDamage>{{ stdamage }} danni magici</magicDamage> se sparano a un solo nemico e <status>paura</status> per {{ boxfearduration }} secondo.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>no alla morte", "Danni mini scatola", "Ricarica"], "effect": ["{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "HallucinateFull.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tradimento", "description": "L'attacco base di Shaco e Due lame del veleno infliggono danni aggiuntivi se colpiscono alle spalle.", "image": {"full": "Jester_CarefulStrikes.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}