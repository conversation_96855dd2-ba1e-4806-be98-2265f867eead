{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Milio": {"id": "<PERSON><PERSON>", "key": "902", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "image": {"full": "Milio.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "902000", "num": 0, "name": "default", "chromas": false}, {"id": "902001", "num": 1, "name": "<PERSON><PERSON> <PERSON> curtea <PERSON>", "chromas": true}, {"id": "902011", "num": 11, "name": "<PERSON><PERSON>, pă<PERSON>rul ploii", "chromas": true}], "lore": "<PERSON><PERSON> e un băiat din Ixtal cu un suflet plin de căldură. În ciuda vârstei fragede, a atins măiestria în axioma focului și a descoperit ceva nou: focul vindecător. Cu ajutorul puterii sale noi, are de gând să-și ajute familia să revină din exil, intrând el însuși în Yun Tal, la fel cum a făcut și bunica lui odinioară. După ce a călătorit prin junglele din Ixtal până în capitala Ixaocan, <PERSON><PERSON> se pregătește acum să treacă prin Vidalion și să ajungă în Yun Tal, fără să știe ce încercări – și pericole – îl așteaptă.", "blurb": "<PERSON><PERSON> e un băiat din Ixtal cu un suflet plin de căldură. În ciuda vârstei fragede, a atins măiestria în axioma focului și a descoperit ceva nou: focul vindecător. Cu ajutorul puterii sale noi, are de gând să-și ajute familia să revină din exil, intrând...", "allytips": ["Pentru ca <PERSON>lio să-și poată folosi potențialul la maximum, trebuie să aibă aliați în apropiere.", "Viteza năpustirilor lui <PERSON> cre<PERSON>te odată cu viteza lui de mișcare. Profită de acest bonus și surprinde-ți adversarii!", "Pericolul e distractiv, dacă-i permiți."], "enemytips": ["Poți vedea destinația abilităților de mișcare ale lui Milio. Folosește-te de informație pentru a fi mereu în avantaj.", "Campionii care pot folosi efecte de control al maselor ce se declanșează rapid excelează împotriva lui Milio.", "Mobilitatea lui Milio are mult de suferit dacă reușești să-l prinzi fără niciun aliat prin preajmă. Încearcă să-l ataci când e singur."], "tags": ["Support", "Mage"], "partype": "Mană", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "MilioQ", "name": "Ultra mega șutul de foc", "description": "Lovește cu piciorul o minge care aruncă în spate un inamic. Mingea sare la impact și cade în direcția adversarului, provocându-i daune și încetinind inamicii din zonă.", "tooltip": "<PERSON><PERSON> o minge de foc, <status>aruncând în spate</status> primul inamic lovit. Dacă mingea nimerește un inamic, aceasta sare în spatele lui și explodează, provocându-le <magicDamage>{{ damage }} daune magice</magicDamage> tuturor inamicilor din apropiere și <status>încetinindu-i</status> cu {{ slowamountpercent }} timp de {{ slowduration }} secunde.<br /><br />Dac<PERSON> <spellName>''Ultra mega șutul de foc''</spellName> nimerește cel puțin un inamic, {{ refundratio*100 }}% din costul său de mană este rambursat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Cost de man<PERSON>"], "effect": ["{{ falldamage }} -> {{ falldamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "MilioQ.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioW", "name": "Foc de tabă<PERSON><PERSON> binef<PERSON><PERSON><PERSON><PERSON>", "description": "Creează o zonă care crește puterile aliaților, vindecându-i și crescându-le raza de atac. Zona urmează aliatul cel mai apropiat de punctul în care a fost folosită abilitatea.", "tooltip": "<PERSON><PERSON> o vatră care urmează campionii aliați timp de {{ zoneduration }} secunde. Campionii aliați din apropiere primesc {{ rangepercent }} rază de atac și își refac <healing>{{ healingovertime }} viață</healing> de-a lungul duratei. Vatra aplică și <spellName>Ard de nerăbdare!</spellName> la fiecare {{ healfrequencyseconds }} secunde.<br /><br /><recast>Refolosire:</recast> schimbi aliatul urmat de vatră.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rază", "Vindecare", "Timp de reactivare", "Cost de man<PERSON>"], "effect": ["{{ rangepctincrease*100.000000 }}% -> {{ rangepctincreasenl*100.000000 }}%", "{{ totalhealingovertime }} -> {{ totalhealingovertimeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [29, 27, 25, 23, 21], "cooldownBurn": "29/27/25/23/21", "cost": [90, 100, 110, 120, 130], "costBurn": "90/100/110/120/130", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "MilioW.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioE", "name": "Îmbră<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "description": "<PERSON><PERSON>i aruncă un scut unui aliat, mărindu-i temporar viteza de mișcare. Abilitatea are 2 cumuluri.", "tooltip": "<PERSON><PERSON> înf<PERSON>șoară un campion aliat în flăc<PERSON>ri protectoare, oferindu-i un <shield>scut în valoare de {{ shieldcalc }}</shield> și <speed>{{ movespeedamount*100 }}% vitez<PERSON> de mișcare</speed> timp de {{ movespeedduration }} secunde.<br /><br />Această abilitate are 2 cumuluri, iar efectele se cumulează asupra țintelor repetate.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Valoarea scutului", "Viteză de mișcare", "Cost de man<PERSON>", "<PERSON><PERSON> <PERSON>"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ movespeedamount*100.000000 }}% -> {{ movespeedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MilioE.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MilioR", "name": "Suflarea vieții", "description": "<PERSON><PERSON><PERSON> un val de flăcări benefice, care oferă vindecare și elimină efectele de control al maselor pentru aliații aflați în raza sa.", "tooltip": "<PERSON><PERSON>e un val de flăcări benefice către campionii aliați din apropiere, eliminând efectele de <status>neutralizare</status> și <status>imobilizare</status>, refăcând <healing>{{ healcalc }} viață</healing> și oferind {{ tenacityamount*100 }}% tenacitate timp de {{ tenacityduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vindecare", "Timp de reactivare"], "effect": ["{{ healbase }} -> {{ healbaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 145, 130], "cooldownBurn": "160/145/130", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MilioR.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ard de ner<PERSON><PERSON><PERSON><PERSON>!", "description": "Abilitățile lui <PERSON> farmecă aliații la atingere, făcând ca următoarele lor daune să provoace o rafală de daune suplimentare și să ardă ținta.", "image": {"full": "Milio_P.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}