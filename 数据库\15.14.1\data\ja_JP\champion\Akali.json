{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akali": {"id": "Akali", "key": "84", "name": "<PERSON><PERSON><PERSON>", "title": "主なき暗殺者", "image": {"full": "Akali.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "84000", "num": 0, "name": "default", "chromas": false}, {"id": "84001", "num": 1, "name": "刺突の暗殺者アカリ", "chromas": false}, {"id": "84002", "num": 2, "name": "地獄の業火アカリ", "chromas": false}, {"id": "84003", "num": 3, "name": "オールスター アカリ", "chromas": false}, {"id": "84004", "num": 4, "name": "白衣の天使アカリ", "chromas": true}, {"id": "84005", "num": 5, "name": "ブラッドムーン アカリ", "chromas": false}, {"id": "84006", "num": 6, "name": "白銀の牙アカリ", "chromas": false}, {"id": "84007", "num": 7, "name": "ヘッドハンター アカリ", "chromas": true}, {"id": "84008", "num": 8, "name": "刺身アカリ", "chromas": false}, {"id": "84009", "num": 9, "name": "K/DA アカリ", "chromas": false}, {"id": "84013", "num": 13, "name": "プレステージ K/DA アカリ", "chromas": false}, {"id": "84014", "num": 14, "name": "PROJECT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "84015", "num": 15, "name": "True Damage アカリ", "chromas": true}, {"id": "84032", "num": 32, "name": "K/DA ALL OUT アカリ", "chromas": true}, {"id": "84050", "num": 50, "name": "クライムシティー ナイトメア アカリ", "chromas": false}, {"id": "84060", "num": 60, "name": "プレステージ K/DA アカリ(2022)", "chromas": false}, {"id": "84061", "num": 61, "name": "スターガーディアン アカリ", "chromas": false}, {"id": "84068", "num": 68, "name": "DRX アカリ", "chromas": false}, {"id": "84070", "num": 70, "name": "盟約の魔女アカリ", "chromas": false}, {"id": "84071", "num": 71, "name": "プレステージ盟約の魔女アカリ", "chromas": true}, {"id": "84082", "num": 82, "name": "荘厳の天球アカリ", "chromas": false}, {"id": "84092", "num": 92, "name": "精霊の花祭りアカリ", "chromas": false}], "lore": "「均衡の守人」であることをやめ、「影の拳」という立場も捨てたアカリは、自分こそ故郷の人々が必要としている武器になろうと決め、独り戦いに挑む。師であるシェンから授かった教えを忘れることなく、アイオニアを襲う敵をひとりずつ、確実に排除すると誓ったのである。アカリは音もなく襲い掛かるが、そのメッセージは誰の耳にも届くだろう──主なき暗殺者を恐れよ、と。", "blurb": "「均衡の守人」であることをやめ、「影の拳」という立場も捨てたアカリは、自分こそ故郷の人々が必要としている武器になろうと決め、独り戦いに挑む。師であるシェンから授かった教えを忘れることなく、アイオニアを襲う敵をひとりずつ、確実に排除すると誓ったのである。アカリは音もなく襲い掛かるが、そのメッセージは誰の耳にも届くだろう──主なき暗殺者を恐れよ、と。", "allytips": ["アカリは防御力の低いチャンピオンをすばやく倒すのが得意だ。チームメイトに切り込んでもらい、後方にいる敵チャンピオンを狙おう。", "「黄昏の帳」を使えば危険な状況も切り抜けられる。その時間を活かして気を溜めてから素早く攻撃しよう。"], "enemytips": ["「黄昏の帳」で見えなくなっている間も、範囲効果を持つスキルを使えばアカリを攻撃でき、それがヒットした場合は一時的に可視化して位置を知ることができる。", "アカリの「五連苦無」は射程と気が最大のときに最も大きな効果を発揮するので、アカリとダメージトレードをする場合は気が減少しているときに戦闘をしかけよう。", "自分の体力が少なくて、アカリがアルティメットスキルを利用可能な場合は本拠地に戻ろう。"], "tags": ["Assassin"], "partype": "気", "info": {"attack": 5, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 119, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 23, "armorperlevel": 4.7, "spellblock": 37, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.625}, "spells": [{"id": "AkaliQ", "name": "五連苦無", "description": "5本のくないを投げて自身の増加攻撃力と魔力に応じたダメージを与えてスロウ効果を与える。", "tooltip": "くないを扇状に投げて<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与え、先端にいた敵に{{ slowduration }}秒間{{ slowpercentage*100 }}%の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@コスト", "ダメージ"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamagenamed }} -> {{ basedamagenamedNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [110, 100, 90, 80, 70], "costBurn": "110/100/90/80/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "AkaliQ.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AkaliW", "name": "黄昏の帳", "description": "姿を隠すための煙幕を張り、少しの間だけ移動速度が増加する。「帳」の中ではインビジブル状態になり、敵のスキルや通常攻撃で対象指定されなくなる。通常攻撃を行うかスキルを使用すると一時的に可視化される。", "tooltip": "煙玉を投げて姿を隠すための煙幕を張る。煙幕は徐々に広がって{{ baseduration }}秒間持続し、自身の<speed>移動速度を{{ movementspeed }}%</speed>増加させる({{ movementspeedduration }}秒かけて元に戻る)。<br /><br />煙幕が有効な間は最大気が{{ energyrestore }}増加する。<br /><br />自身は煙幕の中にいる間<keywordStealth>インビジブル</keywordStealth>状態になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "効果時間", "クールダウン"], "effect": ["{{ movementspeed }}% -> {{ movementspeedNL }}%", "{{ baseduration }} -> {{ basedurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [140, 140, 140, 140, 140], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [250, 250, 250, 250, 250], [60, 65, 70, 75, 80], [0.3, 0.35, 0.4, 0.45, 0.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [5, 5.5, 6, 6.5, 7], [0, 0, 0, 0, 0]], "effectBurn": [null, "140", "4", "0", "250", "60/65/70/75/80", "0.3/0.35/0.4/0.45/0.5", "1", "0", "5/5.5/6/6.5/7", "0"], "vars": [], "costType": "の気を回復", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AkaliW.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ energyrestore }}の気を回復"}, {"id": "AkaliE", "name": "翻身手裏剣", "description": "後方に宙返りして前方に手裏剣を投げ、魔法ダメージを与える。最初に当たった敵または煙幕はマークされる。再発動するとマークされた対象までダッシュして追加でダメージを与える。", "tooltip": "後方に宙返りしながら手裏剣を投げ、<magicDamage>{{ e1damage }}の魔法ダメージ</magicDamage>を与え、最初に当たった敵または煙幕をマークする。<recast>再発動</recast>するとマークされた対象までダッシュして<magicDamage>{{ e2damagecalc }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "基本ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AkaliE.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AkaliR", "name": "完遂", "description": "指定方向に跳躍して攻撃した敵にダメージを与える。<br><br>再発動: 指定方向にダッシュして、攻撃したすべての敵に対象の減少体力に応じたダメージを与える。", "tooltip": "敵チャンピオンを飛び越え、その間に触れたすべての敵に<magicDamage>{{ cast1damage }}の魔法ダメージ</magicDamage>を与える。<br /><br />{{ cooldownbetweencasts }}秒後に<recast>再発動</recast>でダッシュして敵を貫く攻撃が可能となり、減少体力に応じて<magicDamage>{{ cast2damagemin }}</magicDamage> - <magicDamage>{{ cast2damagemax }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "初期ダメージ", "最小ダメージ", "最大ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ r2basedamage }} -> {{ r2basedamageNL }}", "{{ r2basedamage*3.000000 }} -> {{ r2basedamagenl*3.000000 }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [1, 1, 1], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [675, 675, 675], "rangeBurn": "675", "image": {"full": "AkaliR.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "刺客の刻印", "description": "チャンピオンにスキルでダメージを与えると対象の周囲に気の輪が形成される。輪の外に出るとアカリの次の通常攻撃の射程とダメージが増加する。", "image": {"full": "Akali_P.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}