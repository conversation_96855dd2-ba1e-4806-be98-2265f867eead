{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nami": {"id": "<PERSON><PERSON>", "key": "267", "name": "<PERSON><PERSON>", "title": "the Tidecaller", "image": {"full": "Nami.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "267000", "num": 0, "name": "default", "chromas": false}, {"id": "267001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "267002", "num": 2, "name": "River Spirit Nami", "chromas": false}, {"id": "267003", "num": 3, "name": "Urf the Nami-tee", "chromas": false}, {"id": "267007", "num": 7, "name": "Deep Sea Nami", "chromas": false}, {"id": "267008", "num": 8, "name": "SKT T1 Nami", "chromas": false}, {"id": "267009", "num": 9, "name": "Program Nami", "chromas": true}, {"id": "267015", "num": 15, "name": "Splendid Staff Nami", "chromas": true}, {"id": "267024", "num": 24, "name": "Cosmic Destiny Nami", "chromas": true}, {"id": "267032", "num": 32, "name": "Bewitching <PERSON><PERSON>", "chromas": true}, {"id": "267041", "num": 41, "name": "Space Groove Nami", "chromas": true}, {"id": "267042", "num": 42, "name": "Prestige Space Groove Nami", "chromas": false}, {"id": "267051", "num": 51, "name": "Coven <PERSON>", "chromas": true}, {"id": "267058", "num": 58, "name": "Myth<PERSON> <PERSON><PERSON>", "chromas": true}], "lore": "A headstrong young vastaya of the seas, <PERSON><PERSON> was the first of the Marai tribe to leave the waves and venture onto dry land, when their ancient accord with the Targonians was broken. With no other option, she took it upon herself to complete the sacred ritual that would ensure the safety of her people. Amidst the chaos of this new age, <PERSON><PERSON> faces an uncertain future with grit and determination, using her Tidecaller staff to summon the strength of the oceans themselves.", "blurb": "A headstrong young vastaya of the seas, <PERSON><PERSON> was the first of the Marai tribe to leave the waves and venture onto dry land, when their ancient accord with the Targonians was broken. With no other option, she took it upon herself to complete the sacred...", "allytips": ["Aqua Prison has a long cooldown, make sure to use it at the right moment.", "Using Ebb and Flow during an engagement with enemy champions will help sway the battle in your favor.", "<PERSON><PERSON>'s ultimate can come in very handy for initiating on enemies who are far away."], "enemytips": ["Aqua Prison is a very powerful ability with a long cooldown, take advantage of the situation if <PERSON><PERSON> misses this ability.", "Tidal Wave has a very long range but travels slowly, be aware of when it is coming your way as you can move out of its path.", "Try to avoid fighting an opponent affected by <PERSON><PERSON><PERSON>'s Blessing, it lasts a short duration so waiting it out can help you greatly."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 3, "magic": 7, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 335, "armor": 29, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.61, "attackspeed": 0.644}, "spells": [{"id": "NamiQ", "name": "Aqua Prison", "description": "Sends a bubble to a target area, dealing damage and stunning all enemies on impact.", "tooltip": "<PERSON><PERSON> hurls a bubble, <status>Stunning</status> for {{ e2 }} seconds and dealing <magicDamage>{{ totaldamagett }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 130, 185, 240, 295], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/130/185/240/295", "1.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "NamiQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiW", "name": "Ebb and Flow", "description": "Unleashes a stream of water that bounces back and forth between allied and enemy champions, healing allies and damaging enemies.", "tooltip": "<PERSON><PERSON> unleashes a stream of water that bounces between allied and enemy champions. Each champion can only be hit once, and it hits up to {{ maxtargets }} targets.<li>Restores <healing>{{ totalheal }} Health</healing> to allies and will bounce to a nearby enemy champion. <li>Deals <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies and will bounce to a nearby allied champion.<br />The damage and healing value is modified by {{ bouncescaling }} each bounce. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Healing", "Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "NamiW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiE", "name": "Tidecaller's Blessing", "description": "Empowers an allied champion for a short duration. The ally's basic attacks and spells deal bonus magic damage and slow the target.", "tooltip": "<PERSON><PERSON> empowers an allied champion's next {{ hitcount }} Attacks and Abilities for {{ buffduration }} seconds, causing them to <status>Slow</status> their targets by {{ totalslow }} for {{ slowduration }}s and deal an additional <magicDamage>{{ totaldamage }} magic damage</magicDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslow }}% -> {{ baseslowNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [15, 20, 25, 30, 35], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "15/20/25/30/35", "1", "3", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NamiE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiR", "name": "Tidal Wave", "description": "Summons a massive Tidal Wave that knocks up, slows, and damages enemies. Allies hit gain double the effect of Surging Tides.", "tooltip": "<PERSON><PERSON> summons a tidal wave, <status>Knocking Up</status> for 0.5 seconds, <status>Slowing</status> by {{ e4 }}%, and dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>. The duration of the <status>Slow</status> increases based on distance the wave traveled, up to {{ e5 }} seconds.<br /><br />Allies hit by the wave gain double the effect of <spellName>Surging Tides</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [2, 2, 2], [70, 70, 70], [4, 4, 4], [0.002, 0.002, 0.002], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.5", "2", "70", "4", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2550, 2550, 2550], "rangeBurn": "2550", "image": {"full": "NamiR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Surging Tides", "description": "When <PERSON><PERSON>'s Abilities hit allied champions they gain Move Speed for a short duration.", "image": {"full": "NamiPassive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}