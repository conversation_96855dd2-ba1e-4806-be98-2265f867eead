{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zac": {"id": "<PERSON><PERSON>", "key": "154", "name": "<PERSON><PERSON>", "title": "l'arma segreta", "image": {"full": "Zac.png", "sprite": "champion5.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "154000", "num": 0, "name": "default", "chromas": true}, {"id": "154001", "num": 1, "name": "Zac Arma Speciale", "chromas": false}, {"id": "154002", "num": 2, "name": "Zac Festa in Piscina", "chromas": false}, {"id": "154006", "num": 6, "name": "Zac SKT T1", "chromas": false}, {"id": "154007", "num": 7, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "154014", "num": 14, "name": "Zac <PERSON>", "chromas": true}, {"id": "154024", "num": 24, "name": "Zac <PERSON> piccan<PERSON>", "chromas": true}], "lore": "Zac è il risultato di una fuga di materiali tossici fuoriuscita da una tubatura chemtech e accumulatisi in una caverna isolata nel Sump di Zaun. Nonostante queste umili origini, Zac si è evoluto da massa informe primordiale a creatura senziente che abita nelle tubature della città. Di tanto in tanto emerge per aiutare gli indifesi o per ricostruire le infrastrutture danneggiate di Zaun.", "blurb": "Zac è il risultato di una fuga di materiali tossici fuoriuscita da una tubatura chemtech e accumulatisi in una caverna isolata nel Sump di Zaun. Nonostante queste umili origini, Zac si è evoluto da massa informe primordiale a creatura senziente che...", "allytips": ["Raccogliere globuli di melma è importante per restare vivo.", "Quando Divisione cellulare è pronta, cerca di morire in luoghi che mettono in difficoltà il nemico per distruggere le tue masse.", "Caricare Catapulta elastica nella nebbia di guerra darà al nemico meno tempo per reagire."], "enemytips": ["<PERSON>ac si cura con la melma che si separa da lui. Puoi distruggere i globuli di melma calpestandoli.", "Distruggi tutte le masse di Zac quando si suddivide, per impedirgli di riformarsi.", "<PERSON><PERSON><PERSON>, stor<PERSON>menti, immobilizzazioni e respingimenti interromperanno Zac quando sta caricando Catapulta elastica."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 7, "magic": 7, "difficulty": 8}, "stats": {"hp": 685, "hpperlevel": 109, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.4, "attackspeedperlevel": 1.6, "attackspeed": 0.736}, "spells": [{"id": "ZacQ", "name": "Colpi allungati", "description": "Zac allunga un braccio, afferrando un nemico. Attaccare un nemico diverso gli fa lanciare i bersagli uno contro l'altro.", "tooltip": "Zac allunga il braccio, attaccandolo al primo nemico colpito infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallentandolo</status> brevemente. Il prossimo attacco di Zac ottiene gittata aumentata e applica gli stessi danni e <status>rallentamento</status>. <br /><br />Se Zac colpisce un nemico <i>diverso</i> con questo attacco, li <status>lancia in aria</status> entrambi, l'uno verso l'altro. Se si scontrano, loro e i nemici circostanti subiscono <magicDamage>{{ totaldamage }} danni magici</magicDamage> e vengono <status>rallentati</status> brevemente.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12.5, 11, 9.5, 8], "cooldownBurn": "14/12.5/11/9.5/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [8, 8, 8, 8, 8], [800, 800, 800, 800, 800], [-0.4, -0.4, -0.4, -0.4, -0.4], [0.5, 0.5, 0.5, 0.5, 0.5], [2.5, 2.5, 2.5, 2.5, 2.5], [900, 900, 900, 900, 900], [700, 700, 700, 700, 700], [300, 300, 300, 300, 300], [300, 300, 300, 300, 300]], "effectBurn": [null, "40/55/70/85/100", "8", "800", "-0.4", "0.5", "2.5", "900", "700", "300", "300"], "vars": [], "costType": "% della salute attuale ({{ healthcosttooltip }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZacQ.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "Costa {{ e2 }}% della salute attuale ({{ healthcosttooltip }})"}, {"id": "ZacW", "name": "Materia instabile", "description": "Zac esplode verso l'esterno, in direzione dei nemici nelle vicinanze, ai quali infligge una percentuale della loro salute massima in danni magici.", "tooltip": "Il corpo di Zac erutta, infliggendo danni magici pari a <magicDamage>{{ basedamage }} + {{ displaypercentdamage }} della salute massima del bersaglio</magicDamage> ai nemici vicini.<br /><br />Assorbire <keywordMajor>melma</keywordMajor> riduce la ricarica di questa abilità di 1 secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> salute massima"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealthdamage*100.000000 }}% -> {{ basemaxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [40, 50, 60, 70, 80], [4, 4, 4, 4, 4], [0.04, 0.05, 0.06, 0.07, 0.08], [1, 1, 1, 1, 1], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/50/60/70/80", "4", "0.04/0.05/0.06/0.07/0.08", "1", "200", "0", "0", "0", "0", "0"], "vars": [], "costType": "% della salute attuale ({{ tooltiphealthcost }})", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "ZacW.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "Costa {{ e2 }}% della salute attuale ({{ tooltiphealthcost }})"}, {"id": "ZacE", "name": "Catapulta elastica", "description": "Zac attacca le braccia al terreno e si tende all'indietro, per poi lanciarsi in avanti.", "tooltip": "<charge>Caricamento:</charge> <PERSON><PERSON> si tende, caricando uno scatto in {{ e4 }} secondi.<br /><br /><release>Rilascio:</release> Zac si scaglia e <status>lancia in aria</status> i nemici dove atterra fino a {{ maxstun }} secondo/i (in base al tempo di caricamento), infliggendo <magicDamage>{{ damage }} danni magici</magicDamage>. Zac lascia cadere un ulteriore pezzo di <keywordMajor>melma</keywordMajor> per ogni campione nemico colpito.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Gitt<PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 105, 150, 195, 240], [4, 4, 4, 4, 4], [1200, 1350, 1500, 1650, 1800], [0.9, 1, 1.1, 1.2, 1.3], [0.5, 0.5, 0.5, 0.5, 0.5], [500, 500, 500, 500, 500], [1350, 1350, 1350, 1350, 1350], [0.6, 0.6, 0.6, 0.6, 0.6], [265, 265, 265, 265, 265], [1, 1, 1, 1, 1]], "effectBurn": [null, "60/105/150/195/240", "4", "1200/1350/1500/1650/1800", "0.9/1/1.1/1.2/1.3", "0.5", "500", "1350", "0.6", "265", "1"], "vars": [], "costType": "% della salute attuale ({{ healthcosttooltip }})", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacE.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "Costa {{ e2 }}% della salute attuale ({{ healthcosttooltip }})"}, {"id": "ZacR", "name": "Boing!", "description": "<PERSON><PERSON>za quattro volte, lanciando in alto i nemici colpiti e rallentandoli.", "tooltip": "Zac rimbalza {{ bounces }} volte. Il primo rimbalzo che colpisce ciascun nemico lo <status>respinge</status> e infligge <magicDamage>{{ damageperbounce }} danni magici</magicDamage>. I rimbalzi successivi infliggono <magicDamage>{{ damagepersubsequentbounce }} danni magici</magicDamage> e <status>rallentano</status> di un {{ slowamount*100 }}% per {{ slowduration }} secondo/i.<br /><br />Zac ottiene fino a un <speed>{{ endingms*100 }}% di velocità di movimento</speed> con il passare del tempo e può lanciare <spellName>Materia instabile</spellName> mentre rimbalza.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base rimbalzo", "Ricarica"], "effect": ["{{ basedamagebounce }} -> {{ basedamagebounceNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [1.1, 1.1, 1.1], [700, 850, 1000], [-0.3, -0.4, -0.5], [2.5, 2.5, 2.5], [275, 275, 275], [300, 300, 300], [1, 1, 1], [0.25, 0.25, 0.25], [0.1, 0.1, 0.1]], "effectBurn": [null, "150/250/350", "1.1", "700/850/1000", "-0.3/-0.4/-0.5", "2.5", "275", "300", "1", "0.25", "0.1"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [300, 300, 300], "rangeBurn": "300", "image": {"full": "ZacR.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Divisione cellulare", "description": "Ogni volta che Zac colpisce un nemico con un'abilità, lascia cadere un pezzo di se stesso che può essere riassorbito per recuperare salute. Quando subisce danni fatali, Zac si divide in 4 masse che cercano di ricombinarsi. Se le masse sono intatte, torna in vita con una quantità di salute proporzionale a quella delle masse sopravvissute. Ogni massa ha una percentuale della salute massima, dell'armatura e della resistenza magica di Zac. L'abilità ha 5 minuti di ricarica.", "image": {"full": "ZacPassive.png", "sprite": "passive5.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}