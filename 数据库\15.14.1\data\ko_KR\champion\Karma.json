{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "카르마", "title": "깨우친 자", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "태양의 여신 카르마", "chromas": false}, {"id": "43002", "num": 2, "name": "사쿠라 카르마", "chromas": false}, {"id": "43003", "num": 3, "name": "(구) 카르마", "chromas": false}, {"id": "43004", "num": 4, "name": "연꽃 수도회 카르마", "chromas": false}, {"id": "43005", "num": 5, "name": "심판자 카르마", "chromas": false}, {"id": "43006", "num": 6, "name": "겨울 동화 카르마", "chromas": false}, {"id": "43007", "num": 7, "name": "정복자 카르마", "chromas": true}, {"id": "43008", "num": 8, "name": "암흑의 별 카르마", "chromas": true}, {"id": "43019", "num": 19, "name": "빛의 인도자 카르마", "chromas": false}, {"id": "43026", "num": 26, "name": "오디세이 카르마", "chromas": false}, {"id": "43027", "num": 27, "name": "몰락한 카르마", "chromas": true}, {"id": "43044", "num": 44, "name": "평온용 카르마", "chromas": false}, {"id": "43054", "num": 54, "name": "요정 여왕 카르마", "chromas": false}, {"id": "43061", "num": 61, "name": "지옥의 카르마", "chromas": false}, {"id": "43070", "num": 70, "name": "영혼의 꽃 카르마", "chromas": false}], "lore": "아이오니아의 영적인 전통을 카르마보다 잘 나타내는 필멸자가 있을까. 카르마는 지난 시간 동안 축적된 기억을 새로운 생명으로 온존하며 수없이 부활한 고대 영혼의 살아있는 현신으로, 극소수만이 깨우칠 수 있는 힘을 손에 넣었다. 최근 위기가 닥쳤을 때 최선을 다해 자신을 따르는 이들을 이끈 카르마는 자신과 자신이 그토록 아끼는 조국 모두가 상당한 대가를 치러야만 평화와 조화가 찾아오리라는 것을 알고 있다.", "blurb": "아이오니아의 영적인 전통을 카르마보다 잘 나타내는 필멸자가 있을까. 카르마는 지난 시간 동안 축적된 기억을 새로운 생명으로 온존하며 수없이 부활한 고대 영혼의 살아있는 현신으로, 극소수만이 깨우칠 수 있는 힘을 손에 넣었다. 최근 위기가 닥쳤을 때 최선을 다해 자신을 따르는 이들을 이끈 카르마는 자신과 자신이 그토록 아끼는 조국 모두가 상당한 대가를 치러야만 평화와 조화가 찾아오리라는 것을 알고 있다.", "allytips": ["열정 응집은 공격적인 플레이 시 빛을 발합니다. 적에게 스킬이나 기본 공격을 맞혀서 만트라의 재사용 대기시간을 줄이면서 계속 공세를 펼치세요.", "굳은 결의를 시전할 때 적을 따라잡기 힘들 때는 내면의 열정으로 적에게 둔화를 걸거나 고무로 자신의 속도를 높여 보세요.", "만트라는 아낄 필요가 없습니다. 열정 응집은 팀간 교전 시 위력을 발휘하며 만트라를 여러 번 재충전하게 만들어 줍니다."], "enemytips": ["카르마의 기본 지속 효과는 스킬이나 기본 공격으로 적 챔피언을 맞힐 때마다 만트라의 재사용 대기시간을 줄여줍니다. 카르마가 쉽사리 맞힐 수 없도록 잘 피하세요.", "카르마의 영혼의 불꽃이 터지면 해당 위치에 추가 피해를 줍니다. 빠르게 원형 범위를 벗어나도록 하세요.", "굳은 결의는 강력한 무력화 스킬입니다. 거리를 확보하여 속박을 피한 다음 다시 접근하도록 하세요."], "tags": ["Mage", "Support"], "partype": "마나", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "내면의 열정", "description": "카르마가 영적인 에너지의 구체를 전방으로 던져, 적 유닛에게 부딪치면 폭발하며 피해를 입힙니다.<br><br>만트라 추가 효과: 만트라가 내면의 열정의 위력을 강화하여, 구체가 폭발한 잠시 후 원형 범위 내에 피해를 가합니다.", "tooltip": "카르마가 에너지 구체를 발사하여 처음 적중한 대상과 그 주변 적들에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고, {{ slowduration }}초 동안 {{ slowamount*-100 }}% <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KarmaSpiritBind", "name": "굳은 결의", "description": "카르마가 자신과 대상 적 사이에 끈을 만들어 피해를 주고 연결된 적의 위치를 드러냅니다. 끈이 끊어지지 않으면 적은 발이 묶이고 피해를 한 번 더 입습니다.<br><br>만트라 추가 효과: 카르마가 결속을 강화하여 체력을 회복하며 속박 지속시간이 증가합니다.", "tooltip": "카르마가 챔피언이나 정글 몬스터와 자신을 연결하여 <magicDamage>{{ initialdamage }}의 마법 피해</magicDamage>를 입히고 {{ tetherduration }}초 동안 모습을 드러냅니다. 연결이 끊어지지 않으면 대상은 다시 <magicDamage>{{ initialdamage }}의 마법 피해</magicDamage>를 입고 {{ rootduration }}초 동안 <status>속박</status>됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "속박 지속시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "KarmaSolKimShield", "name": "고무", "description": "카르마가 보호막을 소환하여 적이 가하는 피해를 막아내고, 보호받는 아군은 이동 속도가 상승합니다.<br><br>만트라 추가 효과: 대상에게서 에너지가 흘러나와 보호막을 강화하고 주변 아군 챔피언들에게도 고무 효과를 적용합니다.", "tooltip": "카르마가 아군 챔피언에게 {{ shieldduration }}초 동안 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 씌우고 {{ movespeedduration }}초 동안 <speed>이동 속도를 {{ movespeed*100 }}%</speed> 상승시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "만트라", "description": "카르마가 다음 스킬을 강화하여 추가 효과를 부여합니다. 만트라는 1레벨부터 사용할 수 있으며 스킬 포인트가 필요 없습니다.", "tooltip": "카르마가 8초 동안 다음 스킬을 강화합니다.<br /><li><spellName>내면의 열정</spellName>: <magicDamage>{{ rqimpactdamage }}의 마법 피해</magicDamage>를 추가로 입히고 원형의 불꽃을 남깁니다. 불꽃은 적들을 <status>둔화</status>시키고 <magicDamage>{{ rqfielddamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.<li><spellName>굳은 결의</spellName>: 카르마가 지속시간 처음과 끝에 <healing>{{ rwhealamount }}의 잃은 체력</healing>을 회복하고, {{ rwbonusroot }}초 더 <status>속박</status>합니다.<li><spellName>고무</spellName>: 카르마가 대상에게 <shield>피해를 {{ rebonusshield }}만큼 더 흡수하는 보호막</shield>을 씌웁니다. 주변 아군에게도 <shield>{{ rebonusshieldarea }}의 피해를 흡수하는 보호막</shield>을 씌우고 <speed>이동 속도를{{ removespeed*100 }}%</speed> 상승시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["영혼의 불꽃 타격", "영혼의 불꽃 피해량", "재생 속박 연장", "보호막 흡수량", "재사용 대기시간"], "effect": ["{{ qbonusdamage }} -> {{ qbonusdamageNL }}", "{{ qdetonationdamage }} -> {{ qdetonationdamageNL }}", "{{ rwbonusroot }} -> {{ rwbonusrootNL }}", "{{ ebonusshield }} -> {{ ebonusshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "열정 응집", "description": "카르마가 스킬로 피해를 입히면 만트라의 재사용 대기시간이 감소합니다.", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}