{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Qiyana": {"id": "<PERSON><PERSON>", "key": "246", "name": "<PERSON><PERSON>", "title": "L'imperatrice degli elementi", "image": {"full": "Qiyana.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "246000", "num": 0, "name": "default", "chromas": false}, {"id": "246001", "num": 1, "name": "<PERSON><PERSON> da Battaglia", "chromas": true}, {"id": "246002", "num": 2, "name": "<PERSON>yana True Damage", "chromas": false}, {"id": "246010", "num": 10, "name": "Qiyana True Damage (edizione prestigio)", "chromas": false}, {"id": "246011", "num": 11, "name": "<PERSON><PERSON> guer<PERSON>", "chromas": true}, {"id": "246020", "num": 20, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "246021", "num": 21, "name": "Qiyana True Damage (edizione prestigio 2022)", "chromas": false}, {"id": "246030", "num": 30, "name": "Qiyana Imperatrice Lunare", "chromas": true}, {"id": "246040", "num": 40, "name": "Qiyana la Ilusión", "chromas": true}, {"id": "246050", "num": 50, "name": "Qiyana dell'Accademia di Battaglia (edizione prestigio)", "chromas": false}], "lore": "Nella città giungla di <PERSON>, <PERSON><PERSON> architetta il suo spietato piano per sedersi sull'alto seggio degli Yun Tal. Ultima nell'ordine di successione, affronta chi osa ostacolarla con una sfacciata sicurezza e un dominio sulla magia elementale senza precedenti. La terra obbedisce a ogni suo comando e Qiyana è convinta di essere la più grande elementalista della storia di Ixaocan, e di meritare quindi non solo una città, ma un impero.", "blurb": "Nella città giungla di <PERSON>, <PERSON><PERSON> architetta il suo spietato piano per sedersi sull'alto seggio degli Yun Tal. Ultima nell'ordine di successione, affronta chi osa ostacolarla con una sfacciata sicurezza e un dominio sulla magia elementale senza...", "allytips": [], "enemytips": [], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 2, "magic": 4, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 124, "mp": 375, "mpperlevel": 60, "movespeed": 335, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.1, "attackspeed": 0.688}, "spells": [{"id": "QiyanaQ", "name": "<PERSON><PERSON> elementale/<PERSON> di <PERSON>", "description": "<PERSON><PERSON> rotea la sua arma, infliggendo danni con un effetto bonus in base al suo elemento.", "tooltip": "Se Qiyana non ha un <keywordMajor>incantamento</keywordMajor>, colpisce infliggendo <physicalDamage>{{ vanilladamage }} danni fisici</physicalDamage> a nemici in una piccola area. Se lo fa, questa abilità guadagna gittata e un effetto addizionale in base al tipo di <keywordMajor>incantamento</keywordMajor>:<li><keywordMajor>Incantamento ghiaccio</keywordMajor>: <status>immobilizza</status> per un breve periodo, poi <status>rallenta</status> del {{ slowpotency*-100 }}% per {{ slowduration }} secondo.<li><keywordMajor>Incantamento roccia</keywordMajor>: infligge <physicalDamage>{{ tremordamage }} danni fisici</physicalDamage> in più alle unità con meno di {{ critthreshold*100 }}% salute.<li><keywordMajor>Incantamento selvaggio</keywordMajor>: lascia una scia che rende Qiyana <keywordStealth>invisibile</keywordStealth> e conferisce <speed>{{ haste*100 }}% velocità di movimento</speed>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ vanillabase }} -> {{ vanillabaseNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "QiyanaQ.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QiyanaW", "name": "Terraformazione", "description": "<PERSON><PERSON> scatta verso una posizione e incanta la sua arma con un elemento. I suoi attacchi e le sue abilità infliggono danni bonus quando l'arma è incantata. ", "tooltip": "<spellPassive>Passiva:</spellPassive> Mentre l'arma di Qiyana è <keywordMajor>incantata</keywordMajor>, guadagna <attackSpeed>{{ attackspeed*100 }}% velocità d'attacco</attackSpeed> e i suoi attacchi infliggono <magicDamage>{{ onhitdamage }} danni magici</magicDamage> addizionali. Inoltre, vicino al terreno di tipo corrispondente, guadagna <speed>{{ passivems*100 }}% velocità di movimento</speed> mentre è fuori dal combattimento.<br /><br /><spellActive>Attiva:</spellActive> <PERSON>yana scatta verso il cespuglio, terreno, o fiume più vicino e <keywordMajor>incanta</keywordMajor> la sua arma con quel particolare tipo di terreno. Questo inoltre ripristina la ricarica di <spellName>Furia elementale/Lama di Ixtal</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> sul colpo", "Velocità di movimento", "Velocità d'attacco bonus", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ passivems*100.000000 }}% -> {{ passivemsnl*100.000000 }}%", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "QiyanaW.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QiyanaE", "name": "Sfrontatezza", "description": "<PERSON><PERSON> scatta verso un nemico, da<PERSON><PERSON><PERSON><PERSON><PERSON>.", "tooltip": "<PERSON><PERSON> scatta attraverso un bersaglio nemico e infligge <physicalDamage>{{ damage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Ricarica", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "QiyanaE.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "QiyanaR", "name": "<PERSON><PERSON><PERSON>", "description": "Qiyana lancia un'onda d'urto che esplode con il primo elemento colpito, stordendo e danneggiando i nemici vicini.", "tooltip": "Qiyana lancia un'onda d'urto che <status>respinge</status> i nemici ed esplode quando incontra un muro. L'esplosione segue il limite del terreno, <status>stordendo</status> per una durata tra 0,5 e {{ stunduration }} secondo e infliggendo <physicalDamage>{{ damage }}</physicalDamage> più <physicalDamage>{{ missinghealthdamagerock }} di salute massima del bersaglio in danni fisici</physicalDamage>. La durata dello <status>stordimento</status> diminuisce proporzionalmente alla distanza dall'origine dell'esplosione.<br /><br />Fiumi o erba alta incontrati dall'onda d'urto esplodono dopo un periodo di tempo, infliggendo gli stessi danni e <status>stordendo</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "QiyanaR.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Privilegio regale", "description": "Il primo attacco base o abilità di Qiyana contro ogni nemico infligge danni bonus.", "image": {"full": "Qiyana_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}