{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Varus": {"id": "Varus", "key": "110", "name": "Varus", "title": "la freccia del castigo", "image": {"full": "Varus.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "110000", "num": 0, "name": "default", "chromas": false}, {"id": "110001", "num": 1, "name": "Varus Cristalli del <PERSON>ello", "chromas": false}, {"id": "110002", "num": 2, "name": "Varus della Luce", "chromas": false}, {"id": "110003", "num": 3, "name": "Varus Operazioni Artiche", "chromas": false}, {"id": "110004", "num": 4, "name": "Varus Cerca Cuori", "chromas": false}, {"id": "110005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "110006", "num": 6, "name": "V<PERSON>s <PERSON>", "chromas": true}, {"id": "110007", "num": 7, "name": "Varus Conquistatore", "chromas": true}, {"id": "110009", "num": 9, "name": "Varus Infernale", "chromas": true}, {"id": "110016", "num": 16, "name": "PROGETTO: Varus", "chromas": true}, {"id": "110017", "num": 17, "name": "Varus Cacciatore Cosmico", "chromas": true}, {"id": "110034", "num": 34, "name": "Varus Mezzogiorno di Fuoco", "chromas": true}, {"id": "110044", "num": 44, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "110053", "num": 53, "name": "Varus Empireo", "chromas": true}, {"id": "110060", "num": 60, "name": "Varus Fi<PERSON> spirituale", "chromas": true}], "lore": "Membro dell'antica razza dei darkin, Varus era un abile assassino che amava tormentare le sue vittime, sferrando la freccia letale solo dopo averle portate alla follia. Venne imprigionato al termine della Grande Guerra Darkin ma, centinaia di anni dopo, r<PERSON><PERSON><PERSON> a scappare grazie ai corpi di due cacciatori di Ionia, che lo liberarono inconsapevolmente, condannati ora a imbracciare l'arco che ne contiene l'essenza. Varus è a caccia di coloro che l'hanno imprigionato per poter portare a termine la sua brutale vendetta, ma le anime mortali che porta in sé cercano di resistergli.", "blurb": "Membro dell'antica razza dei darkin, <PERSON>arus era un abile assassino che amava tormentare le sue vittime, sferrando la freccia letale solo dopo averle portate alla follia. Venne imprigionato al termine della Grande Guerra Darkin ma, centinaia di anni dopo...", "allytips": ["Se metti subito un punto in Faretra del flagello ti aiuterà ad aggredire i campioni nemici e finire i minion.", "Durante i combattimenti a distanza ravvicinata, a volte è meglio scoccare una Freccia penetrante in fretta, piuttosto che caricarla fino alla massima potenza.", "Approfitta della lunga gittata della Freccia penetrante per colpire i campioni nemici prima di un combattimento, oppure quando cercano di fuggire."], "enemytips": ["Se sei colpito da<PERSON>, le abilità di Varus ti infliggeranno danni extra.", "Quando Varus commette un'uccisione o realizza un assist, guadagna temporaneamente velocità d'attacco, diventando più per<PERSON>o.", "<PERSON><PERSON> intrappolato, se un tentacolo dell'abilità suprema di Varus, Catena della corruzione, ti raggiunge. Puoi però fare in modo che il tentacolo muoia, se prendi le distanze in fretta."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 3, "magic": 4, "difficulty": 2}, "stats": {"hp": 600, "hpperlevel": 105, "mp": 320, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.4, "attackspeedperlevel": 3.5, "attackspeed": 0.658}, "spells": [{"id": "VarusQ", "name": "<PERSON><PERSON><PERSON> penetrante", "description": "Varus si prepara e poi spara un colpo di incredibile potenza. Più tempo impiega a preparare il colpo e più guadagna gittata e danni.", "tooltip": "<attention>Inizio carica:</attention> Varus trattiene il colpo successivo, <status>rallentando</status> se stesso di un {{ e7 }}%. Se non ha ancora scoccato la freccia dopo {{ e5 }} secondi, Varus annulla l'abilità e recupera un {{ e4 }}% del suo costo in mana.<br /><br /><attention><PERSON><PERSON><PERSON><PERSON>:</attention> Varus scocca la freccia, infliggendo <physicalDamage>{{ totaldamagemintooltip }} danni fisici</physicalDamage> ridotti di un {{ e3 }}% per ogni nemico colpito (minimo {{ e9 }}%). I danni e gli effetti della detonazione di <keywordMajor>Flag<PERSON></keywordMajor> aumentano al massimo di un {{ maxchargeamp*100 }}% in base al tempo di caricamento (massimo <physicalDamage>{{ totaldamagemax }}</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico bonus", "Ricarica", "Costo in mana"], "effect": ["{{ basedamagemax }} -> {{ basedamagemaxNL }}", "{{ tadratiomax }} -> {{ tadratiomaxNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [3, 3, 3, 3, 3], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [33, 33, 33, 33, 33], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "15", "50", "4", "3", "20", "0", "33", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VarusW", "name": "Faretra del flagello", "description": "Passiva: gli attacchi base di Varus infliggono danni magici bonus e applicano Flagello. Le altre abilità di Varus fanno esplodere Flagello, infliggendo danni magici in base alla salute massima del bersaglio. Attiva: Varus potenzia la sua prossima Freccia penetrante.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi base di Varus infliggono <magicDamage>{{ onhitdamage }} danni magici</magicDamage> aggiuntivi e applicano una carica di <keywordMajor><PERSON><PERSON></keywordMajor> per {{ e3 }} secondi (massimo {{ e4 }} cariche).<br /><br />Le altre abilità di Varus fanno esplodere le cariche di <keywordMajor><PERSON>ello</keywordMajor>, infliggendo il <magicDamage>{{ percenthpperstack }} della salute massima del bersaglio in danni magici</magicDamage> per ogni carica (massimo <magicDamage>{{ maxpercenthpperstack }} della salute massima del bersaglio in danni</magicDamage>). Far esplodere <keywordMajor>Flag<PERSON></keywordMajor> contro campioni e mostri epici riduce inoltre il tempo di ricarica delle sue abilità del {{ cdrperblightstack*100 }}% del loro tempo massimo per ogni carica.<br /><br /><spellActive>Attiva:</spellActive> la prossima <spellName>Freccia penetrante</spellName> di Varus infligge il <magicDamage>{{ qempowerpercenthp }} della salute mancante del bersaglio in danni magici</magicDamage> aggiuntivi, aumentati fino al <magicDamage>{{ maxqempowerpercenthp }} della salute mancante del bersaglio in danni</magicDamage> in base al tempo di caricamento.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> all'attacco", "<PERSON><PERSON> salute massima", "<PERSON><PERSON> salute mancante"], "effect": ["{{ varuswonhitdamage }} -> {{ varuswonhitdamageNL }}", "{{ basepercenthpperstack*100.000000 }}% -> {{ basepercenthpperstacknl*100.000000 }}%", "{{ wqhealthdamage*100.000000 }}% -> {{ wqhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [40, 40, 40, 40, 40], "cooldownBurn": "40", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0.03, 0.035, 0.04, 0.045, 0.05], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [120, 120, 120, 120, 120], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0.03/0.035/0.04/0.045/0.05", "6", "3", "120", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VarusW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "VarusE", "name": "Pioggia di frecce", "description": "Varus spara una pioggia di frecce che infliggono danni fisici e profanano il terreno. Il suolo profanato rallenta la velocità di movimento dei nemici e riduce la rigenerazione e la guarigione su se stessi.", "tooltip": "Varus spara una pioggia di frecce che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e profana il terreno per {{ e3 }} secondi, <status>rallentando</status> i nemici di un {{ slowpercent*-100 }}% e applicando un {{ grievousamount*100 }}% di Ferita grave.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent*-100.000000 }}% -> {{ slowpercentnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [60, 100, 140, 180, 220], [-0.3, -0.35, -0.4, -0.45, -0.5], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/100/140/180/220", "-0.3/-0.35/-0.4/-0.45/-0.5", "4", "0", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VarusR", "name": "Catena della corruzione", "description": "Varus scaglia un dannoso tentacolo di corruzione che immobilizza il primo campione nemico colpito e l'effetto si diffonde verso i campioni non infetti nelle vicinanze, immobilizzandoli a contatto.", "tooltip": "Varus scaglia un tentacolo di corruzione che <status>immobilizza</status> il primo campione colpito per {{ e2 }} secondi e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>. I nemici <status>immobilizzati</status> ottengono {{ e4 }} cariche di <keywordMajor><PERSON><PERSON></keywordMajor> nel corso della durata.<br /><br />La corruzione si diffonde dal bersaglio verso i campioni nemici non ancora infetti. Se li raggiunge, vengono <status>immobilizzati</status> e subiscono la stessa quantità di danni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [2, 2, 2], [650, 650, 650], [3, 3, 3], [0.5, 0.5, 0.5], [600, 600, 600], [1.75, 1.75, 1.75], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "2", "650", "3", "0.5", "600", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "VarusR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Vendetta vivente", "description": "Dopo un'uccisione o un assist, Varus guadagna temporaneamente attacco fisico e potere magico. Questo bonus è più grande se il nemico ucciso è un campione.", "image": {"full": "VarusPassive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}