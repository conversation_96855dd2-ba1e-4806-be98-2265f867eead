{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sion": {"id": "Sion", "key": "14", "name": "Sion", "title": "Nemrtvý kolos", "image": {"full": "Sion.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "14000", "num": 0, "name": "default", "chromas": false}, {"id": "14001", "num": 1, "name": "Magitechnický Sion", "chromas": false}, {"id": "14002", "num": 2, "name": "Barbar <PERSON>", "chromas": false}, {"id": "14003", "num": 3, "name": "Dřevorubec Sion", "chromas": false}, {"id": "14004", "num": 4, "name": "Válečný štváč Sion", "chromas": false}, {"id": "14005", "num": 5, "name": "Mechanický <PERSON>", "chromas": true}, {"id": "14014", "num": 14, "name": "Ničitel světa <PERSON>", "chromas": true}, {"id": "14022", "num": 22, "name": "Sion temn<PERSON><PERSON>u", "chromas": true}, {"id": "14030", "num": 30, "name": "Nesmiřitelný Sion", "chromas": true}, {"id": "14040", "num": 40, "name": "Kosmický paladin <PERSON>", "chromas": true}, {"id": "14049", "num": 49, "name": "Sion Velkého <PERSON>", "chromas": false}], "lore": "Sion je válečný hrdina z dávných dob, k<PERSON><PERSON> je v Noxu oslav<PERSON>n za to, že holýma rukama vymáčkl život z těla demacijského krále. Nebylo mu však dopřáno požeh<PERSON>ní věčného odpočinku – místo toho byl znovu oživen, aby své říši sloužil i po smrti. <PERSON><PERSON><PERSON> kolem sebe začne rozsévat zk<PERSON>zu, neohl<PERSON><PERSON><PERSON> se na to, zda zabíj<PERSON> nepř<PERSON>tele, nebo spojence, což je důka<PERSON>m, že o svou lidskost už nenávratně přišel. Přesto ve svém hrubém brnění, nanýtovaném přímo do hnijícího těla, vyráží bezhlavě do boje a mezi jednotlivými machy mocnou sekerou se snaží vzpomenout na své dřívější já.", "blurb": "Sion je válečný hrdina z dávných dob, k<PERSON><PERSON> je v Noxu oslavován za to, že holýma rukama vymáčkl život z těla demacijského krále. Nebylo mu však dopřáno požehnání věčného odpočinku – místo toho byl znovu oživen, aby své říši sloužil i po smrti. Jakmile...", "allytips": ["<PERSON><PERSON><PERSON>ho ma<PERSON>ru mů<PERSON><PERSON> měnit směr pouze ve velice omezen<PERSON> míř<PERSON>, tak jej p<PERSON>, jen k<PERSON><PERSON> máš před sebou rovnou cestu.", "Zabijákovým řevem si můžeš připravit ideální podmínky pro velmi silný zásah Ničivým úderem.", "Na buffu Výheň duší je z<PERSON>, kolik štítu máš ještě k dispozici. Využij tuto informaci k dokonalému načasování jeho exploze."], "enemytips": ["Pokud př<PERSON><PERSON><PERSON>š Siona ukončit nápřah Ničivého úderu př<PERSON>ně, jeho <PERSON><PERSON> bude slabší, i k<PERSON>ž tě zasáhne.", "Čas po Sionově smrti využij k zaujetí nové pozice a přípravě na jeho návrat."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 655, "hpperlevel": 87, "mp": 400, "mpperlevel": 52, "movespeed": 345, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 1.3, "attackspeed": 0.679}, "spells": [{"id": "SionQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sion se napřáhne k mocnému máchnutí v oblasti před sebou, které po aktivaci způsobí poškození všem zasaženým nepřátelům. Pokud se napřahuje dostatečně dlouho, budou zasažení protivníci navíc vyhozeni do vzduchu a omráčeni.", "tooltip": "<charge>Začátek nabíjení</charge>: <PERSON>on se až 2&nbsp;sekundy napřahuje k mocnému úderu.<br /><br /><release>Uvolnění</release>: <PERSON><PERSON> udeř<PERSON> svou sekerou do země, na chvíli <status>zpomalí</status> zasažené nepřátele a v závislosti na době nápřahu jim způsobí <physicalDamage>{{ mindamagetotal }} až {{ maxdamagetotal }} bod<PERSON> fyzického poš<PERSON>í</physicalDamage>. Pokud se Sion napřahoval alespoň 1&nbsp;sekundu, vyhodí nepřátele <status>do vzduchu</status> a v závislosti na době nápřahu je <status>omráč<PERSON></status> na {{ basestuntime }} až 2,25&nbsp;sekundy.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Minimální poškození", "Maximální poškození", "Závislost na útočném poškození", "Závislost na útočném poškození", "Přebíjecí doba"], "effect": ["{{ lowdamage }} -> {{ lowdamageNL }}", "{{ highdamage }} -> {{ highdamageNL }}", "{{ adratiomin*100.000000 }} % -> {{ adratiominnl*100.000000 }} %", "{{ adratiomax*100.000000 }} % -> {{ adratiomaxnl*100.000000 }} %", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [7.5, 7.5, 7.5, 7.5, 7.5], [60, 60, 60, 60, 60], [150, 150, 150, 150, 150], [-0.8, -0.8, -0.8, -0.8, -0.8]], "effectBurn": [null, "0", "0", "0", "0", "2.5", "0", "7.5", "60", "150", "-0.8"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "SionQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SionW", "name": "Výheň duší", "description": "Sion kolem sebe vytvoří <PERSON>; po 3&nbsp;sekund<PERSON><PERSON> může tuto schopnost použít znovu a způsobit okolním nepřátelům magické poškození. Sion zabíjením nepřátel pasivně zvyšuje své maximální zdraví.", "tooltip": "<spellPassive><PERSON>siv<PERSON><PERSON> efekt</spellPassive>: <PERSON><PERSON><PERSON> zabije nějak<PERSON> jednotku, jeho maximální hodnota zdraví se zvýší o <scaleHealth>{{ hpperkill }} body</scaleHealth>, v případě likvidace šampiona či zabití velkého poskoka nebo velké příš<PERSON> o {{ hpperchampkill }} bodů.<br /><br /><spellActive>Aktivace</spellActive>: Sion získá na 6&nbsp;sek. <shield>štít o síle {{ totalshield }} bodů</shield>. Pokud se tento štít během {{ e7 }}&nbsp;sek. nero<PERSON>ne, může jej <PERSON> <recast>opětovným sesláním</recast> schopnosti odpálit a způsobit tak <magicDamage>magické poškození ve výši {{ totaldamage }} bod<PERSON> plus {{ e4 }}&nbsp;% maxim<PERSON>ln<PERSON>ho zdraví</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Poškození", "<PERSON><PERSON><PERSON><PERSON> s<PERSON> k max. zdraví", "Mana pro seslání", "Přebíjecí doba"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ shieldpercenthealthtooltip*100.000000 }} % -> {{ shieldpercenthealthtooltipnl*100.000000 }} %", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [65, 70, 75, 80, 85], "costBurn": "65/70/75/80/85", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [40, 65, 90, 115, 140], [8, 10, 12, 14, 16], [14, 14, 14, 14, 14], [4, 4, 4, 4, 4], [15, 15, 15, 15, 15], [3, 3, 3, 3, 3], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "40/65/90/115/140", "8/10/12/14/16", "14", "4", "15", "3", "15", "6", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "SionW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SionE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Sion vyšle rázovou vlnu s krátk<PERSON><PERSON> dosahem, která prvnímu zasaženému cíli způsobí po<PERSON>, zpomalí jej a sníží mu hodnotu brnění. Pokud vlna zasáhne poskoka nebo příš<PERSON>u, odhod<PERSON> je zpět a způsobí poškození, zpomalení a snížení hodnoty brnění vš<PERSON> ne<PERSON>, ji<PERSON><PERSON> jednotka proletí.", "tooltip": "<PERSON>on vyšle rázovou vlnu, k<PERSON><PERSON> způsob<PERSON> <magicDamage>{{ totaldamage }} bod<PERSON> magického po<PERSON></magicDamage>, na {{ slowduration }}&nbsp;sek. <status>zpomal<PERSON></status> zasa<PERSON><PERSON><PERSON> jednotky o {{ slowamount }}&nbsp;% a na dobu {{ armorshredduration }}&nbsp;sek. jim sn<PERSON> <scaleArmor>hodnotu brnění o {{ armorshred }}&nbsp;%</scaleArmor>. Zasa<PERSON><PERSON><PERSON> jednotky s výjimkou šampionů budou <status>odhozeny zpět</status>. Nep<PERSON><PERSON>telé, kter<PERSON> z<PERSON> tyto <status>odhozené</status> jed<PERSON><PERSON>, utrpí stejné poškozen<PERSON> a efekty.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození", "<PERSON><PERSON><PERSON>", "Přebíjecí doba", "@AbilityResourceName@ pro seslání"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ slowamount }} % -> {{ slowamountNL }} %", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [40, 45, 50, 55, 60], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "40/45/50/55/60", "5", "40/45/50/55/60", "20", "30", "4", "2.5", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SionE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SionR", "name": "Nezastavitelný masakr", "description": "Sion se rozběhne zvoleným směrem, jeho rychlost se postupně zvyšuje. Pomocí kurzoru může v omezené míře řídit směr běhu. Když narazí do nějakého nepřítele, způsobí mu poškození a vyhodí jej do vzduchu; hodnoty se odvíjejí od toho, jak velkou vzdálenost urazil.", "tooltip": "Sion po dobu 8&nbsp;sekund nezastavitelně běž<PERSON> zvoleným směrem, ot<PERSON><PERSON><PERSON> se přitom za kurzorem. Zastaví se po srá<PERSON>ce s nepřátelským šampionem nebo zdí, p<PERSON><PERSON><PERSON><PERSON><PERSON> po <recast>opětovném seslání</recast> této schopnosti.  <br /><br /><PERSON><PERSON><PERSON> se <PERSON> zastaví, způsobí <physicalDamage>{{ mindamagetotal }} až {{ maxdamagetotal }} bod<PERSON> fyzického poš<PERSON>í</physicalDamage>, tato hodnota se odvíjí od uražené vzdálenosti. Nepřátelé v Sionově blízkosti budou na {{ minstunduration }} až {{ maxstunduration }}&nbsp;sek. <status>omráčeni</status>, i tato doba se odvíjí od vzdálenosti, kterou Sion urazil. Nepřátelé v širší oblasti budou na 3&nbsp;sek. <status>zpomaleni</status> o {{ slowamount }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Minimální poškození", "Maximální poškození", "<PERSON><PERSON><PERSON>", "Přebíjecí doba"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ slowamount }} % -> {{ slowamountNL }} %", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 100, 60], "cooldownBurn": "140/100/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "SionR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Slavná smrt", "description": "Sion se po smrti na omezenou dobu znovu oživí, ale hodnota jeho zdraví bude rychle klesat. <PERSON><PERSON> jsou velice rych<PERSON>, léč<PERSON> se jimi a způsobují bonusové poš<PERSON>zení, jeho<PERSON> výše se odvíjí od maximálního zdraví cíle.", "image": {"full": "Sion_Passive1.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}