{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renekton": {"id": "Renekton", "key": "58", "name": "Renekton", "title": "der Schlächter des Sandes", "image": {"full": "Renekton.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "58000", "num": 0, "name": "default", "chromas": false}, {"id": "58001", "num": 1, "name": "Galaktischer Renekton", "chromas": false}, {"id": "58002", "num": 2, "name": "Renekton Dundee", "chromas": false}, {"id": "58003", "num": 3, "name": "Blutzorn-Renekton", "chromas": false}, {"id": "58004", "num": 4, "name": "Runenkriege-Renekton", "chromas": false}, {"id": "58005", "num": 5, "name": "Erdenfeuer-Renekton", "chromas": false}, {"id": "58006", "num": 6, "name": "Poolparty-Renekton", "chromas": false}, {"id": "58007", "num": 7, "name": "Urzeit-Renekton", "chromas": false}, {"id": "58008", "num": 8, "name": "SKT T1-Renekton", "chromas": false}, {"id": "58009", "num": 9, "name": "Renactionfigur", "chromas": true}, {"id": "58017", "num": 17, "name": "Hextech-Renekton", "chromas": false}, {"id": "58018", "num": 18, "name": "Schwarzfrost-Renekton", "chromas": true}, {"id": "58026", "num": 26, "name": "PROJEKT: Renekton", "chromas": true}, {"id": "58033", "num": 33, "name": "Rächende Dämmerung Renekton", "chromas": true}, {"id": "58042", "num": 42, "name": "WM 2023-Renekton", "chromas": true}, {"id": "58048", "num": 48, "name": "Tintenschatten-Renekton", "chromas": true}], "lore": "Renekton ist ein schrecklicher, zornentbrannter Aufgestiegener aus den sengenden Wüsten von Shurima. Einst war er der angesehenste Krieger seines Reiches, der dessen Armeen zu zahlreichen Siegen führte. Allerdings wurde Renekton nach dem Fall des Imperiums unter dem Sand begraben, wo er langsam dem Wahnsinn anheimfiel, während die Welt sich weiter drehte und veränderte. Nun hat er seine Freiheit wiedererlangt und nichts weiter im Sinn, als seinen Bruder Nasus zu finden und zu töten, den er in seinem Wahn für die Jahrhunderte in der Dunkelheit verantwortlich macht.", "blurb": "Renekton ist ein schrecklicher, zornentbrannter Aufgestiegener aus den sengenden Wüsten von Shurima. Einst war er der angesehenste Krieger seines Reiches, der dessen Armeen zu zahlreichen Siegen führte. Allerdings wurde Renekton nach dem Fall des...", "allytips": ["„<PERSON><PERSON><PERSON> und Tritt“ eignet sich hervorragend dafür, andere Champions zu belästigen. Benutze „<PERSON>hnitt“ gefolgt von einer anderen Fähigkeit und dann „Tritt“, um wieder zu entkommen.", "„Fällen der Demütigen“ entzieht Gegnern im Umfeld eine Menge Leben, also nutze es möglichst mitten im Gerangel. Auch kannst du damit Gegnern eine nicht vorhandene Schwäche vortäuschen.", "Abklingzeitverringerungen eignen sich besonders gut für Renekton, da er so seinen Zorn noch schneller ab- und wieder aufbauen kann."], "enemytips": ["Achte genau auf Renektons Zorn, denn so lässt sich leichter vorhersagen, wann er angreift.", "Die Effektivität von Renektons Fähigkeiten lässt sich am einfachsten beeinflussen, indem man ihn kontinuierlich angreift, so dass er seinen Z<PERSON> nur langsam oder überhaupt nicht aufbauen kann."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 660, "hpperlevel": 111, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.15, "attackspeedperlevel": 2.75, "attackspeed": 0.665}, "spells": [{"id": "RenektonCleave", "name": "Fällen der Demütigen", "description": "Renekton schlägt um sich, wobei er mäßigen normalen Schaden an allen Zielen um ihn herum verursacht und sich in Höhe eines kleinen Teils des verursachten Schadens heilt. Besitzt er mehr als 50 Zorn, so verursacht er mehr Schaden und stellt mehr Leben wieder her.", "tooltip": "Renekton schwingt seine <PERSON>, verursacht <physicalDamage>{{ basicdamage }}&nbsp;normalen Schaden</physicalDamage> und stellt pro getroffene Einheit, die kein Champion ist, <healing>{{ nonchamphealing }}&nbsp;<PERSON><PERSON></healing> und pro getroffenen Champion <healing>{{ champhealing }}&nbsp;<PERSON><PERSON></healing> wieder her. Er generiert <keywordMajor>{{ minionfurygain }}&nbsp;<PERSON>orn</keywordMajor> pro getroffene Einheit, die kein Champion ist, und <keywordMajor>{{ championfurygain }}&nbsp;<PERSON>orn</keywordMajor> pro getroffenen Champion.<br /><br /><keywordMajor>Zorn-Bonus</keywordMajor>: Der Schaden erhöht sich auf <physicalDamage>{{ empdamage }}&nbsp;normalen Schaden</physicalDamage>, Heilung per Einheit, die kein Champion ist, auf <healing>{{ empnonchamphealing }}&nbsp;<PERSON><PERSON></healing> und Heilung per Champion auf <healing>{{ empchamphealing }}&nbsp;<PERSON><PERSON></healing>. Es wird kein <keywordMajor><PERSON>orn</keywordMajor> generiert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung pro Champion", "Heilung pro Einheit", "Maximale Heilung"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ champheal }} -> {{ champhealNL }}", "{{ baseminionheal }} -> {{ baseminionhealNL }}", "{{ basichealcap }} -> {{ basichealcapNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RenektonCleave.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "RenektonPreExecute", "name": "Rücksichtsloser Räuber", "description": "Renekton schlitzt sein Ziel 2 Mal auf, verursacht dabei mäßigen normalen Schaden und betäubt es 0,75&nbsp;Sekunden lang. Besitzt er mehr als 50&nbsp;<PERSON><PERSON>, so schlägt er 3 <PERSON> zu, zerst<PERSON>rt Schaden abschirmende Schilde, die auf das Ziel wirken, verursacht hohen normalen Schaden und betäubt das Ziel 1,5&nbsp;Sekunden lang.", "tooltip": "Renektons nächster Angriff trifft zweimal, <status>betäubt</status> das Ziel {{ stunduration }}&nbsp;Sekunden lang und verursacht insgesamt <physicalDamage>{{ basictotaldamage }}&nbsp;normalen Schaden</physicalDamage>. Championtreffer generieren zusätzlich <keywordMajor>{{ bonusfuryvschamps }}&nbsp;Zorn</keywordMajor>.<br /><br /><keywordMajor>Zornbonus</keywordMajor>: Renekton greift stattdessen 3-mal an, zerstört <shield>Schilde</shield>, verursacht dann <physicalDamage>{{ emptotaldamage }}&nbsp;normalen Schaden</physicalDamage> und <status>betäubt</status> {{ enragedstunduration }}&nbsp;Sekunden lang. Es wird kein <keywordMajor>Zorn</keywordMajor> generiert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verstärkter Schaden", "Abklingzeit"], "effect": ["{{ basedamageperhit*2.000000 }} -> {{ basedamageperhitnl*2.000000 }}", "{{ basedamageperhit*3.000000 }} -> {{ basedamageperhitnl*3.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "RenektonPreExecute.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "RenektonSliceAndDice", "name": "<PERSON><PERSON><PERSON> und <PERSON>", "description": "Renekton stürmt nach vorne und verursacht an allen getroffenen Gegnern Schaden. Bemächtigt verursacht Renekton zusätzlichen Schaden und verringert die Rüstung der getroffenen Einheiten.", "tooltip": "Renekton springt nach vorn und verursacht <physicalDamage>{{ basicdamage }}&nbsp;normalen Schaden</physicalDamage>. Er generiert <keywordMajor>{{ minionragegeneration }}&nbsp;<PERSON><PERSON></keywordMajor> pro getroffene Einheit, die kein Champion ist, und <keywordMajor>{{ championragegeneration }}&nbsp;Zorn</keywordMajor> pro getroffenen Champion. Wenn Renekton mindestens einen Gegner trifft, kann er diese Fähigkeit einmal {{ dicetimer }}&nbsp;Sekunden lang <recast>reaktivieren</recast>. <br /><br /><keywordMajor>Zornbonus</keywordMajor>: Die <recast>Reaktivierung</recast> des Sprunges verursacht stattdessen <physicalDamage>{{ empdamage }}&nbsp;normalen Schaden</physicalDamage> und entfernt {{ shredtimer }}&nbsp;Sekunden lang <scaleArmor>{{ enragedarmorshred }}&nbsp;% Rüstung</scaleArmor>. <PERSON><PERSON><PERSON> keinen <keywordMajor>Zorn</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verstärkter Schaden", "Rüstungsverringerung (%)", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ enragedbasedamage }} -> {{ enragedbasedamageNL }}", "{{ enragedarmorshred }}&nbsp;% -> {{ enragedarmorshredNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RenektonSliceAndDice.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "RenektonReignOfTheTyrant", "name": "<PERSON><PERSON>", "description": "Renekton nimmt die Gestalt des Tyrannen an, wodurch er zusätzliches Leben erhält und an allen umstehenden Gegnern Schaden verursacht. In dieser Gestalt erhöht sich Renektons Zorn zusätzlich pro Sekunde und für jeden getroffenen Champion.", "tooltip": "Renekton umgibt sich {{ buffduration }}&nbsp;Sekunden lang mit dunkler Energie, wodurch er <healing>{{ healthgain }}&nbsp;max. <PERSON></healing> und <keywordMajor>{{ furyoncast }}&nbsp;<PERSON><PERSON></keywordMajor> erhält. Solange die Fähigkeit aktiv ist, verursacht er <magicDamage>{{ totaldamagepersecond }}&nbsp;magischen Schaden</magicDamage> und erhält <keywordMajor>{{ furypersecond }}&nbsp;<PERSON>orn</keywordMajor> pro Sekunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzliches Leben", "Schaden pro Sekunde", "Abklingzeit"], "effect": ["{{ healthgain }} -> {{ healthgainNL }}", "{{ auradamagepersecond }} -> {{ auradamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "RenektonReignOfTheTyrant.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Herrschaft des Zorns", "description": "Renektons Angriffe generieren Zorn. Dieser Effekt ist erhöht, wenn er über wenig Leben verfügt. Dieser Zorn kann seine Fähigkeiten mit zusätzlichen Effekten verstärken.", "image": {"full": "Renekton_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}