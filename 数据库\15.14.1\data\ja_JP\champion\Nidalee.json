{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nidalee": {"id": "<PERSON><PERSON><PERSON>", "key": "76", "name": "ニダリー", "title": "半獣の狩人", "image": {"full": "Nidalee.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "76000", "num": 0, "name": "default", "chromas": false}, {"id": "76001", "num": 1, "name": "雪兎ニダリー", "chromas": false}, {"id": "76002", "num": 2, "name": "女豹ニダリー", "chromas": false}, {"id": "76003", "num": 3, "name": "メイド ニダリー", "chromas": false}, {"id": "76004", "num": 4, "name": "ファラオ ニダリー", "chromas": false}, {"id": "76005", "num": 5, "name": "魅惑の魔女ニダリー", "chromas": false}, {"id": "76006", "num": 6, "name": "ヘッドハンター ニダリー", "chromas": false}, {"id": "76007", "num": 7, "name": "三国武将ニダリー", "chromas": false}, {"id": "76008", "num": 8, "name": "チャレンジャー ニダリー", "chromas": true}, {"id": "76009", "num": 9, "name": "超銀河ニダリー", "chromas": false}, {"id": "76011", "num": 11, "name": "秩序の光ニダリー", "chromas": true}, {"id": "76018", "num": 18, "name": "宇宙の女狩人ニダリー", "chromas": true}, {"id": "76027", "num": 27, "name": "DWG ニダリー", "chromas": true}, {"id": "76029", "num": 29, "name": "オーシャンソング ニダリー", "chromas": true}, {"id": "76039", "num": 39, "name": "ニャダリー", "chromas": false}, {"id": "76048", "num": 48, "name": "ラ・イルシオン ニダリー", "chromas": false}, {"id": "76058", "num": 58, "name": "精霊の花祭りニダリー", "chromas": false}], "lore": "ジャングルの奥深くで育ったニダリーは凶暴なクーガーに自由に姿を変えることができる追跡の天才だ。女性でも獣でもなく、彼女は巧妙に仕掛けた罠と素早い投げ槍で、あらゆる侵入者から徹底して自分の縄張りを守っている。彼女は獲物の脚を傷つけて動けなくしてからクーガーの姿になって襲い掛かる──辛うじて襲撃を逃れて生き残った者たちの話によれば、剃刀のように鋭い本能と、さらに鋭い爪を持った野生の女だったという…", "blurb": "ジャングルの奥深くで育ったニダリーは凶暴なクーガーに自由に姿を変えることができる追跡の天才だ。女性でも獣でもなく、彼女は巧妙に仕掛けた罠と素早い投げ槍で、あらゆる侵入者から徹底して自分の縄張りを守っている。彼女は獲物の脚を傷つけて動けなくしてからクーガーの姿になって襲い掛かる──辛うじて襲撃を逃れて生き残った者たちの話によれば、剃刀のように鋭い本能と、さらに鋭い爪を持った野生の女だったという…", "allytips": ["茂みをうまく活用しよう！ 出たり入ったりしながら戦うことで、優位に立つことができる。", "まずは遠距離から「槍投げ」で相手の体力を減らし、それからクーガーに変身して「マーキング」した対象を追跡しよう。", "「虎挟み」は対象の現在体力に比例したダメージを与える。戦闘開始前にトラップを仕掛け、飛び込んできた前線の敵にダメージを与えてチームメイトをサポートしよう。"], "enemytips": ["「マーキング」のデバフが付くのは「槍投げ」か「虎挟み」でダメージを与えられたときだけだ。この2つのスキルを避ければ、他のスキルでの追撃で通常より大きなダメージを食らわずに済む。", "「槍投げ」は、遠くから狙われるほど命中時のダメージが大きくなる。ニダリーとの距離が離れているときは、必ず避けるようにしよう。", "ニダリーに「マーキング」された対象は「テイクダウン」で受けるダメージがかなり大きくなるものの、このスキルは射程距離が狭い。接近されてとどめを刺されそうになったときに備えて、行動妨害や防御効果のあるスキルを温存しておこう。"], "tags": ["Assassin", "Mage"], "partype": "マナ", "info": {"attack": 5, "defense": 4, "magic": 7, "difficulty": 8}, "stats": {"hp": 610, "hpperlevel": 109, "mp": 295, "mpperlevel": 45, "movespeed": 335, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.45, "attackrange": 525, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3.22, "attackspeed": 0.638}, "spells": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "name": "槍投げ/テイクダウン", "description": "ヒト形態: 対象に向かって槍を投げる。ダメージ量は槍の飛距離に比例して大きくなる。<br><br>クーガー形態: 次の通常攻撃時に大量の追加ダメージを与える。ダメージ量は対象の失った体力に比例して増える。", "tooltip": "<keywordMajor>ヒト形態:</keywordMajor> 槍を投げて<magicDamage>{{ humanminimumdamage }}の魔法ダメージ</magicDamage>を与える。ダメージは槍の飛距離に応じて最大<magicDamage>{{ humanmaximumdamage }}の魔法ダメージ</magicDamage>まで増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「槍投げ」最小ダメージ", "「槍投げ」最大ダメージ", "「槍投げ」マナコスト"], "effect": ["{{ spearminimumdamage }} -> {{ spearminimumdamageNL }}", "{{ spearmaximumdamage }} -> {{ spearmaximumdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [70, 90, 110, 130, 150], [227.5, 292.5, 357.5, 422.5, 487.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.4, 0.4, 0.4, 0.4, 0.4], [1.2, 1.2, 1.2, 1.2, 1.2], [525, 525, 525, 525, 525], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/90/110/130/150", "227.5/292.5/357.5/422.5/487.5", "0", "0", "0.4", "1.2", "525", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "JavelinToss.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Bushwhack", "name": "虎挟み/ジャンプ", "description": "ヒト形態: 地面にトラップを仕掛ける。気づかず踏んだ敵は、ダメージを受けて可視状態になる。<br><br>クーガー形態: 指定方向にジャンプし、着地点周辺の範囲内にいる敵ユニットにダメージを与える。", "tooltip": "<keywordMajor>ヒト形態:</keywordMajor> インビジブル状態のトラップを2分間設置する。トラップは敵が踏むと{{ e3 }}秒間、毎秒<magicDamage>{{ damagepersecond }}の魔法ダメージ</magicDamage>を与える。<br /><br />トラップは同時に{{ maxtraps }}個まで設置しておくことができる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「虎挟み」基本ダメージ", "「トラップ」クールダウン", "「トラップ」マナコスト"], "effect": ["{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [13, 12, 11, 10, 9], [10, 20, 30, 40, 50], [120, 120, 120, 120, 120], [40, 80, 120, 160, 200], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "4", "13/12/11/10/9", "10/20/30/40/50", "120", "40/80/120/160/200", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "Bushwhack.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PrimalSurge", "name": "高揚/クロウ", "description": "ヒト形態: クーガーの精霊を呼び出し、味方の体力を回復して短時間攻撃速度を増加させる。<br><br>クーガー形態: かぎ爪で攻撃し、自身の前方範囲内の敵ユニットにダメージを与える。", "tooltip": "<keywordMajor>ヒト形態:</keywordMajor> <healing>体力を{{ totalhealing }}</healing>回復する。回復量は減少体力に応じて最大<healing>{{ maxhealing }}</healing>まで増加する。また{{ asduration }}秒間、<attackSpeed>攻撃速度が{{ bonusas*100 }}%</attackSpeed>増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「高揚」基本体力回復量", "「高揚」増加攻撃速度", "「高揚」マナコスト"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ bonusas*100.000000 }}% -> {{ bonusasnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "PrimalSurge.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AspectOfTheCougar", "name": "クーガーの心", "description": "クーガーに変身し、特別なスキルを使用する。", "tooltip": "<spellPassive>自動効果:</spellPassive> <keywordMajor>ヒト形態</keywordMajor>の間は、<keywordMajor>「マーキング」</keywordMajor>を適用するとこのスキルのクールダウンが解消される。<br /><br /><keywordMajor>ヒト形態:</keywordMajor> <keywordMajor>クーガー形態</keywordMajor>に変化して通常攻撃が近接になり、発動スキルが入れ替わる。<br /><br /><keywordMajor>クーガー形態:</keywordMajor> <keywordMajor>ヒト形態</keywordMajor>に変化して通常攻撃が遠隔になり、発動スキルが入れ替わる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「テイクダウン」基本ダメージ", "「テイクダウン」追加ダメージ", "「ジャンプ」基本ダメージ", "「クロウ」基本ダメージ", "強化「ジャンプ」クールダウン"], "effect": ["{{ takedownbasedamage }} -> {{ takedownbasedamageNL }}", "{{ takedowndamageamp*100.000000 }}% -> {{ takedowndamageampnl*100.000000 }}%", "{{ pouncedamage }} -> {{ pouncedamageNL }}", "{{ swipedamage }} -> {{ swipedamageNL }}", "{{ pouncecooldown }} -> {{ pouncecooldownNL }}"]}, "maxrank": 4, "cooldown": [3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [6, 6, 6, 6], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "AspectOfTheCougar.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "品定め", "description": "茂みに入ると移動速度が2秒間10%増加する。さらに、距離1400の範囲内にいる可視状態の敵チャンピオンに向かって移動する場合は30%増加する。<br><br>敵チャンピオンまたは中立モンスターに「槍投げ」または「虎挟み」でダメージを与えると、対象に4秒間<font color='#FFF673'>「マーキング」</font>を付与し、<font color='#ee91d7'>真の視界</font>を得る。この間、ニダリーの移動速度が10%増加し、さらに、<font color='#FFF673'>「マーキング」</font>中の対象に向かって移動する場合は30%増加する。また、対象に対する「テイクダウン」と「ジャンプ」の効果が増加する。", "image": {"full": "Nidalee_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}