{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "<PERSON><PERSON><PERSON>", "title": "der Zeitwächter", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "<PERSON><PERSON> <PERSON><PERSON>", "chromas": false}, {"id": "26002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "26003", "num": 3, "name": "Shurima-<PERSON><PERSON><PERSON>-Z<PERSON>an", "chromas": false}, {"id": "26004", "num": 4, "name": "Zeitmaschinen-Zilean", "chromas": false}, {"id": "26005", "num": 5, "name": "Blu<PERSON>mond-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "26006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON>ck-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "26014", "num": 14, "name": "Wintergeweihter Zilean", "chromas": true}], "lore": "<PERSON>ile<PERSON> ist ein mächtiger Magier aus Icathia, der sich obsessiv mit Zeitreisen auseinandersetzte, nachdem sein Heimatland von der Leere zerstört worden war. Ohne eine Minute um diesen katastrophalen Verlust zu trauern, rief er uralte Zeitmagie an, um alle möglichen Enden vorauszusagen. Praktisch unsterblich durchstreift Zilean nun die Vergangenheit, Gegenwart und Zukunft, und krümmt und verzerrt auf der Suche nach jenem flüchtigen Moment, der die Uhr zurückdrehen und die Zerstörung von Icathia ungeschehen machen soll, den Fluss der Zeit um ihn herum.", "blurb": "<PERSON><PERSON><PERSON> ist ein mächtiger Magier aus Icathia, der sich obsessiv mit Zeitreisen auseinandersetzte, nachdem sein Heimatland von der Leere zerstört worden war. Ohne eine Minute um diesen katastrophalen Verlust zu trauern, rief er uralte Zeitmagie an, um...", "allytips": ["Du kannst „Zeitbombe“ und „Zurückspulen“ zusammen nutzen, um „Zeitbombe“ 2-mal schneller hintereinander auf ein Ziel anzuwenden. Durch Anbringen der 2. Bombe detoniert die 1. und betäubt alle Gegner in der Nähe.", "„Zeitkrümmung“ ist ideal, um Verbündeten bei einem fliehenden Gegner zu helfen oder selbst zu entkommen.", "„Zeitverschiebung“ ist eine sehr wirksame Abschreckung gegen einen Angriff auf deine Carries, aber wenn du „Zeitverschiebung“ zu früh im Kampf einsetzt, kann es dazu führen, dass der Feind die Ziele zu schnell wechselt und der Zauber weniger effektiv ist."], "enemytips": ["<PERSON><PERSON> du Z<PERSON>an auf den Fersen bleiben kannst, dann ist es manchmal sinnvoll zu warten, bis seine ultimative Fähigkeit nicht mehr wirkt, bevor du ihm den Todesstoß versetzt.", "<PERSON><PERSON><PERSON> ist sehr schwach, wenn er von einem Team angegriffen wird, an<PERSON>ten aber sehr schwer zu töten. Versuche ihn daher als Team anzugreifen."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "Zeitbombe", "description": "Wirft eine Bombe auf ein Zielgebiet. Diese haftet an Einheiten, die sich ihr nähern (Champions werden priorisiert). Sie detoniert nach 3 Sekunden und verursacht Flächenschaden. Falls eine Zeitbombe von einer anderen Zeitbombe frühzeitig zum Explodieren gebracht wird, werden Gegner zudem betäubt.", "tooltip": "Zilean wirft eine zeitverzögerte Bombe, die an der ersten Einheit hängen bleibt, die einen kleinen Bereich um sie herum betritt. Nach {{ e2 }}&nbsp;Sekunden explodiert sie und verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>.<br /><br />Wenn Zilean eine zweite Bombe auf eine Einheit wirft, die bereits eine aufgesammelt hat, explodiert die erste Bombe sofort und <status>betäubt</status> Gegner im Explosionsbereich {{ e4 }}&nbsp;Sekunden lang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>", "Betäubungsdauer:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZileanW", "name": "Zurückspulen", "description": "<PERSON><PERSON><PERSON> kann sich auf bevorstehende Konfrontationen vorbereiten, wodurch die Abklingzeit seiner normalen Fähigkeiten reduziert wird.", "tooltip": "Zilean manipuliert die Zeit und verringert die Abklingzeiten seiner anderen Grundfähigkeiten um {{ e2 }}&nbsp;Sekunden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }}&nbsp;Mana"}, {"id": "TimeWarp", "name": "Zeitkrümmung", "description": "Zilean krümmt die Zeit um eine Einheit herum, wodurch er entweder das Lauftempo eines Gegners verringert oder für kurze Zeit das Lauftempo einer verbündeten Einheit erhöht.", "tooltip": "Zilean <status>verlangsamt</status> einen gegnerischen Champion um {{ e2 }}&nbsp;% oder gewährt einem verbündeten Champion {{ e1 }}&nbsp;Sekunden lang <speed>{{ e2 }}&nbsp;% Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Verlangsamung", "Lauftempo"], "effect": ["{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ChronoShift", "name": "Zeitverschiebung", "description": "<PERSON><PERSON><PERSON> wirkt vorübergehend eine schützende Rune auf einen verbündeten Champion und teleportiert den Champion in der Zeit zurück, falls er einen tödlichen Treffer erleidet.", "tooltip": "Zilean gewährt einem verbündeten Champion {{ rduration }}&nbsp;Sekunden lang eine schützende Zeitrune. Wenn das Ziel tödlichen Schaden erleidet, dreht die Rune die Zeit zurück und versetzt es {{ revivestateduration }}&nbsp;Sekunden lang in Stase. Danach wird es mit <healing>{{ rtotalheal }}&nbsp;Leben</healing> wiederbelebt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Kosten (@AbilityResourceName@)", "Heilung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Zeit in einer Flasche", "description": "Zilean speichert Zeit als Erfahrung, die er seinen Verbündeten gewähren kann. Wenn er genügend Erfahrung angesammelt hat, um den Stufenaufstieg eines Verbündeten abzuschließen, kann er diese per Rechtsklick an ihn weitergeben. Zilean erhält die gleiche Menge von Erfahrung, die er anderen gewährt.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}