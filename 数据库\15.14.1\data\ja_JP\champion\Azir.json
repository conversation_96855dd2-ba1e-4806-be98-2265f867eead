{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Azir": {"id": "<PERSON><PERSON><PERSON>", "key": "268", "name": "アジール", "title": "砂塵の皇帝", "image": {"full": "Azir.png", "sprite": "champion0.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "268000", "num": 0, "name": "default", "chromas": false}, {"id": "268001", "num": 1, "name": "銀河アジール", "chromas": false}, {"id": "268002", "num": 2, "name": "グレイヴロード アジール", "chromas": false}, {"id": "268003", "num": 3, "name": "SKT T1 アジール", "chromas": false}, {"id": "268004", "num": 4, "name": "三国武将アジール", "chromas": false}, {"id": "268005", "num": 5, "name": "古の賢樹アジール", "chromas": true}, {"id": "268014", "num": 14, "name": "Worlds 2022 アジール", "chromas": true}, {"id": "268019", "num": 19, "name": "弁護人アジール", "chromas": true}], "lore": "遥か昔、アジールは定命なるシュリーマの皇帝であり永遠の命を手にしかけた誇り高き男だったが、傲慢から生み出された裏切りによって、最大の偉業を成し遂げようとした瞬間に殺害されてしまった。しかし今、数千年の時を経て、彼は強大な力を持つ超越者として甦った。埋もれた都市が砂の中から姿を現した今、アジールはシュリーマを復興し、かつての栄光を取り戻そうとしている。", "blurb": "遥か昔、アジールは定命なるシュリーマの皇帝であり永遠の命を手にしかけた誇り高き男だったが、傲慢から生み出された裏切りによって、最大の偉業を成し遂げようとした瞬間に殺害されてしまった。しかし今、数千年の時を経て、彼は強大な力を持つ超越者として甦った。埋もれた都市が砂の中から姿を現した今、アジールはシュリーマを復興し、かつての栄光を取り戻そうとしている。", "allytips": ["「目覚めよ！」のスタックを2つとも使い切って兵士を配置する場合は、慎重に状況を把握すること。全力攻撃を仕掛けるつもりでないかぎり、兵士は1体温存しておいたほうがいい。いざという時の脱出手段や、敵を追撃したいのにあと一歩足りない、という状況にも対応できる。", "レーンにいるときは、敵ミニオンと敵チャンピオンとの間に兵士を置くようにすれば、どちらに対しても攻撃することが可能となる。レーンで対面する敵は、常にプレッシャーを感じ続けることになるだろう。", "「皇帝の分砂嶺」は基本的に防御スキルだと考え、敵が自身や味方に突っこんできたときに使うと有効だ。なお、味方は「皇帝の分砂嶺」で召喚された近衛兵の間をすり抜けられることも覚えておこう。敵の近接系チャンピオンに接近された時は、後ろに近衛兵たちを召喚してそこに逃げ込むこともできる。", "アジールは貴方に翼を授ける。鳥になれ！"], "enemytips": ["アジールの主なダメージ源となるのは兵士達だが、それほど頻繁に動かせるわけではない。兵士たちを動かせないタイミングで攻撃しよう。", "アジールは他のメイジ系チャンピオンのような瞬間的に大ダメージを出す能力に欠けるが、長期的に攻撃力を発揮することができる。彼がダメージを出す前に、一気に畳み掛けよう。", "砂塵兵は燃え盛る炎のようなものだと考えてみよう。炎の中に静止するのは、もちろん危険極まりないことだ。"], "tags": ["Mage", "Marksman"], "partype": "マナ", "info": {"attack": 6, "defense": 3, "magic": 8, "difficulty": 9}, "stats": {"hp": 575, "hpperlevel": 119, "mp": 320, "mpperlevel": 40, "movespeed": 330, "armor": 25, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.5, "attackspeedperlevel": 5.5, "attackspeed": 0.625}, "spells": [{"id": "AzirQWrapper", "name": "征服の勅命", "description": "すべての砂塵兵を指定地点に集結させる。砂塵兵は進路上でぶつかった全ての敵ユニットに、魔法ダメージと1秒間のスロウ効果を与える。", "tooltip": "すべての<keywordMajor>砂塵兵</keywordMajor>を指定範囲に集結させる。<keywordMajor>砂塵兵</keywordMajor>は進路上でぶつかった敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、1秒間{{ e2 }}%の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [60, 80, 100, 120, 140], [25, 25, 25, 25, 25], [0, 0, 0, 0, 0], [70, 70, 70, 70, 70], [1600, 1600, 1600, 1600, 1600], [200, 200, 200, 200, 200], [325, 325, 325, 325, 325], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/80/100/120/140", "25", "0", "70", "1600", "200", "325", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [740, 740, 740, 740, 740], "rangeBurn": "740", "image": {"full": "AzirQWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AzirW", "name": "目覚めよ！", "description": "アジールに代わって敵を攻撃する砂塵兵を1体召喚する。砂塵兵の射程距離内にいる対象にアジールが通常攻撃をすると、砂塵兵が対象の敵に向かって槍を突き、直線上にいる敵ユニットすべてに魔法ダメージを与える。また自動効果により、アジールと砂塵兵の攻撃速度が増加する。", "tooltip": "{{ e1 }}秒間、<keywordMajor>砂塵兵</keywordMajor>を召喚する。自身が<keywordMajor>砂塵兵</keywordMajor>の近くにいる敵を通常攻撃するときは、兵士に命令を出して代わりに攻撃させ、その方向にいる敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />このスキルは{{ maxammo }}回までチャージできる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["砂塵兵の攻撃力", "合計魔力反映率", "リチャージ時間", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio }} -> {{ apratioNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [25, 25, 25, 25, 25], [0.5, 0.5, 0.5, 0.5, 0.5]], "effectBurn": [null, "10", "0", "0", "0", "0", "3", "0", "5", "25", "0.5"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "AzirW.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AzirEWrapper", "name": "流砂の衝撃", "description": "アジールが少しの間だけシールドを獲得して、指定した砂塵兵に向かってダッシュし、触れた敵ユニットにダメージを与える。敵チャンピオンに衝突するとその場で停止し、直ちに新たな砂塵兵のチャージを獲得する。", "tooltip": "自身が{{ e6 }}秒間、<shield>耐久値{{ totalshield }}のシールド</shield>を獲得して、<keywordMajor>砂塵兵</keywordMajor>の1体に向かってダッシュし、触れた敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />敵チャンピオンに衝突するとその場で停止し、<keywordMajor>砂塵兵</keywordMajor>のチャージを獲得する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "ダメージ", "クールダウン"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [1700, 1700, 1700, 1700, 1700], [0, 0, 0, 0, 0], [60, 100, 140, 180, 220], [70, 110, 150, 190, 230], [0.3, 0.5, 0.7, 0.9, 1.1], [1.5, 1.5, 1.5, 1.5, 1.5], [1100, 1100, 1100, 1100, 1100], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [25, 25, 25, 25, 25]], "effectBurn": [null, "1700", "0", "60/100/140/180/220", "70/110/150/190/230", "0.3/0.5/0.7/0.9/1.1", "1.5", "1100", "0", "5", "25"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "AzirEWrapper.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AzirR", "name": "皇帝の分砂嶺", "description": "兵士たちの壁を召喚する。兵士たちは前方に突進し、衝突した敵にダメージとノックバックを与えたあと、その場で敵の進行を防ぐ壁となる。", "tooltip": "兵士たちの壁を召喚する。兵士たちは前方に突進し、衝突した敵に<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、<status>ノックバック</status>させる。兵士たちはその後{{ e4 }}秒間その場にとどまり、敵の通過を阻止する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "兵士数", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ numberofsoldiers }} -> {{ numberofsoldiersNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [200, 400, 600], [0, 0, 0], [6, 7, 8], [5, 5, 5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "200/400/600", "0", "6/7/8", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "AzirR.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "シュリーマの遺産", "description": "アジールは敵や味方のタワーの残骸から「太陽の円盤」を召喚できる。", "image": {"full": "Azir_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}