{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TahmKench": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "223", "name": "<PERSON><PERSON>", "title": "Il re del fiume", "image": {"full": "TahmKench.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "223000", "num": 0, "name": "default", "chromas": false}, {"id": "223001", "num": 1, "name": "<PERSON><PERSON> Master Chef", "chromas": false}, {"id": "223002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "223003", "num": 3, "name": "<PERSON><PERSON> Imperatore del Denaro", "chromas": true}, {"id": "223011", "num": 11, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "223020", "num": 20, "name": "<PERSON><PERSON> Mezzogiorno di Fuoco", "chromas": false}, {"id": "223030", "num": 30, "name": "<PERSON><PERSON>", "chromas": false}], "lore": "Noto nella storia con molti nomi, il demone Tahm Kench percorre le acque di Runeterra placando il suo insaziabile appetito con la miseria altrui. Nonostante possa apparire stranamente fiero e affascinante, erra per il reame fisico come un vagabondo alla ricerca di un'ignara preda. La sua lingua saettante può stordire a distanza anche un guerriero corazzato, e finire nel suo ventre è come precipitare in un abisso dal quale ci sono poche speranze di fuggire.", "blurb": "Noto nella storia con molti nomi, il demone Tahm Kench percorre le acque di Runeterra placando il suo insaziabile appetito con la miseria altrui. Nonostante possa apparire stranamente fiero e affascinante, erra per il reame fisico come un vagabondo alla...", "allytips": ["La tua funzione più importante è tenere al sicuro i tuoi alleati meno resistenti. Fai caso alla gittata e alla ricarica di Divorare e posizionati di conseguenza!", "Valuta bene quando usare l'attiva di Pelle coriacea. A volte usare lo scudo per evitare danni è una buona idea, altre è più vantaggioso sfruttare la guarigione."], "enemytips": ["<PERSON>hm <PERSON> usa lo scudo di Pelle coriacea, ricorda che ha rinunciato a una buona dose di guarigione e non accumulerà altra salute grigia fino alla fine della ricarica. Sfrutta la situazione a tuo vantaggio!", "Fai attenzione al Tuffo nell'abisso di Kench, puoi annullarne la canalizzazione con effetti di controllo immobilizzanti."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 325, "mpperlevel": 50, "movespeed": 335, "armor": 39, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.2, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "TahmKenchQ", "name": "Lingua a frusta", "description": "<PERSON><PERSON> lancia la sua lingua, danne<PERSON><PERSON><PERSON> e rallentando la prima unità colpita e curandosi se colpisce un campione nemico.<br><br>Questa abilità applica una carica di <spellName><PERSON><PERSON> acquisito</spellName> ai campioni nemici. Se il campione ha già 3 cariche di <spellName><PERSON><PERSON> acquisito</spellName>, viene stordito e le cariche consumate.", "tooltip": "Infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> al primo nemico colpito e lo <status>rallenta</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi. <br /><br />Quando colpisce un campione, gua<PERSON><PERSON> Tahm del <healing>{{ baseheal }} +{{ percenthealthhealing*100 }}% della sua salute mancante</healing> e applica una carica di <spellName><PERSON><PERSON> acquisito</spellName>, infliggendo <magicDamage>{{ spell.tahmkenchpassive:totaldamage }} danni magici aggiuntivi</magicDamage>. Se il campione ha già 3 cariche di <spellName>Gusto acquisito</spellName>, viene anche <status>stordito</status> per {{ stunduration }} secondi, consumando le cariche.<br /><br />Attiva <span class=\"color0bf7de\">Divorare</span> mentre la lingua si trova a mezz'aria per sbranare i campioni nemici colpiti che hanno già 3 cariche di <spellName>Gusto acquisito</spellName> dalla distanza.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Guarigione", "Guarigione percentuale della salute mancante", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ percenthealthhealing*100.000000 }}% -> {{ percenthealthhealingnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 46, 42, 38, 34], "costBurn": "50/46/42/38/34", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TahmKenchQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TahmKenchW", "name": "<PERSON><PERSON><PERSON> nell'abisso", "description": "<PERSON><PERSON> si immerge e riappare nella destinazione bersaglio, dannegg<PERSON><PERSON> e lanciando in aria tutti i nemici nell'area.", "tooltip": "Tahm <PERSON>ch si immerge e riappare nella destinazione bersaglio, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>lanciando in aria</status> tutti i nemici nell'area per {{ knockupduration }} secondo. Colpire almeno un campione nemico rimborsa il {{ champrefund*100 }}% della ricarica e del costo in <scaleMana>mana</scaleMana>. <br /><br />Gli alleati <span class=\"color0bf7de\">divorati</span> possono viaggiare con lui (ma possono sempre scegliere di uscire in anticipo).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Gitt<PERSON>", "Costo in @AbilityResourceName@", "<PERSON><PERSON><PERSON><PERSON> rica<PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cost }} -> {{ costNL }}", "{{ champrefund*100.000000 }}% -> {{ champrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 75, 90, 105, 120], "costBurn": "60/75/90/105/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1050, 1100, 1150, 1200], "rangeBurn": "1000/1050/1100/1150/1200", "image": {"full": "TahmKenchW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TahmKenchE", "name": "<PERSON><PERSON> coria<PERSON>", "description": "<passive>Passiva:</passive> <PERSON><PERSON> immagazzina una percentuale dei danni e guarisce in base a questo valore mentre è fuori dal combattimento.<br><br><active>Attiva:</active> Converte tutti i danni immagazzinati in uno scudo temporaneo.", "tooltip": "<passive>Passiva:</passive> il {{ greyhealthratio*100 }}% dei danni subiti da Tahm Kench viene immagazzinato dalla sua <spellName>Pelle coriacea</spellName>, aumentato a {{ greyhealthratioenhanced*100 }}% se ci sono almeno {{ enhancedthreshold }} campioni nemici nelle vicinanze. Se Kench non ha subito danni entro {{ ooctimer }} secondi, <spellName>Pelle coriacea</spellName> viene rapidamente consumata per guarire Tahm del {{ greyhealthhealingratio }} del suo valore.<br /><br /><active>Attiva:</active> converte tutti i danni immagazzinati da <spellName>Pelle coriacea</spellName> in uno <shield>scudo</shield> che dura {{ shieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% Danni a Pelle coriacea", "% Danni a Pelle coriacea aumentati"], "effect": ["{{ greyhealthratio*100.000000 }}% -> {{ greyhealthrationl*100.000000 }}%", "{{ greyhealthratioenhanced*100.000000 }}% -> {{ greyhealthratioenhancednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2400, 2400, 2400, 2400, 2400], "rangeBurn": "2400", "image": {"full": "TahmKenchE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TahmKenchRWrapper", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> divora un campione per qualche secondo, infliggendo danni magici se è un nemico o applicando uno scudo se si tratta di un alleato.", "tooltip": "Tahm Kench divora un campione per qualche secondo. Può <recast>rilanciare</recast> questa abilità per sputarlo.<br /><br /><specialRules>Campioni nemici:</specialRules> Richiede 3 cariche di <spellName>Gusto acquisito</spellName>. Vengono divorati per un massimo di {{ enemyduration }} secondi e subiscono <magicDamage>{{ basedamage }} (+{{ percenthpdamage }} della loro salute massima) in danni magici</magicDamage>. Tahm Kench è <status>rallentato</status> del {{ slowamount*100 }}% e <keywordName>ancorato</keywordName> per la durata di questo effetto.<br /><br /><specialRules>Campioni alleati:</specialRules> vengono divorati per un massimo di {{ allyduration }} secondi e ricevono uno <shield>scudo da {{ totalshield }}</shield> che decade gradualmente dopo essere stati sputati. Gli alleati possono decidere di uscire in anticipo. Tahm Kench viene <status>ancorato</status> durante questo effetto, ma può lanciare <keywordName>Tuffo nell'abisso</keywordName> e ottiene un <speed>{{ allyspeedamount*100 }}% di velocità di movimento</speed> per {{ allyduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Quantità scudo", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ datacooldown }} -> {{ datacooldownNL }}"]}, "maxrank": 3, "cooldown": [0, 0, 0], "cooldownBurn": "0", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "TahmKenchRWrapper.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ datamanacost }} mana"}], "passive": {"name": "<PERSON><PERSON> a<PERSON>", "description": "<PERSON><PERSON> mette il peso del suo immenso corpo nei suoi attacchi, otten<PERSON>o danni aggiuntivi in base alla sua salute totale. <PERSON><PERSON><PERSON><PERSON><PERSON> i campioni nemici, <PERSON><PERSON><PERSON> accumula cariche di <spellName><PERSON><PERSON> acquisito</spellName>. A tre cariche, può usare <spellName>Divorare</spellName> su un campione nemico.", "image": {"full": "TahmKenchP.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}