{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Malzahar": {"id": "<PERSON><PERSON><PERSON>", "key": "90", "name": "マルザハール", "title": "ヴォイドの予言者", "image": {"full": "Malzahar.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "90000", "num": 0, "name": "default", "chromas": false}, {"id": "90001", "num": 1, "name": "マルザハール卿", "chromas": false}, {"id": "90002", "num": 2, "name": "影の王子マルザハール", "chromas": false}, {"id": "90003", "num": 3, "name": "魔神マルザハール", "chromas": false}, {"id": "90004", "num": 4, "name": "オーバーロード マルザハール", "chromas": false}, {"id": "90005", "num": 5, "name": "雪遊びマルザハール", "chromas": true}, {"id": "90006", "num": 6, "name": "バトルボス マルザハール", "chromas": false}, {"id": "90007", "num": 7, "name": "ヘクステック マルザハール", "chromas": false}, {"id": "90009", "num": 9, "name": "天地の破壊者マルザハール", "chromas": true}, {"id": "90018", "num": 18, "name": "ハチザハール", "chromas": true}, {"id": "90028", "num": 28, "name": "おしゃれなマルザハール", "chromas": true}, {"id": "90038", "num": 38, "name": "三徳のマルザハール", "chromas": false}, {"id": "90039", "num": 39, "name": "荘厳の天球マルザハール", "chromas": true}, {"id": "90049", "num": 49, "name": "運命の破壊者マルザハール", "chromas": true}], "lore": "あらゆる生命の合一にすべてを捧げる熱狂的な予言者マルザハールは、新たに現れたヴォイドこそルーンテラを救済へと導く道なのだと固く信じている。シュリーマの不毛の砂漠の中、彼は心の中に囁いた声に導かれ、古代イカシアにたどり着いた。その廃墟の地で彼は、ヴォイドそのものの核たる闇を覗き込み、新たな力と目的を与えられた。今やマルザハールは己を「羊飼い」と考えており、人々にその福音を広めるための力…あるいは、地の底に棲むヴォイドの怪物たちを解き放つ力を振るうのである。", "blurb": "あらゆる生命の合一にすべてを捧げる熱狂的な予言者マルザハールは、新たに現れたヴォイドこそルーンテラを救済へと導く道なのだと固く信じている。シュリーマの不毛の砂漠の中、彼は心の中に囁いた声に導かれ、古代イカシアにたどり着いた。その廃墟の地で彼は、ヴォイドそのものの核たる闇を覗き込み、新たな力と目的を与えられた。今やマルザハールは己を「羊飼い」と考えており、人々にその福音を広めるための力…あるいは、地の底に棲むヴォイドの怪物たちを解き放つ力を振るうのである。", "allytips": ["「ヴォイドスワーム」は近くの敵をキルか攻撃できる時に使おう。", "「ヴォイドコール」と「ネザーグラスプ」をつかって「虚性侵蝕」の効果時間を延長しよう。", "レーンでダメージを受けないことで、常に「ヴォイドシフト」を維持し安全にファームをすることができる。"], "enemytips": ["「虚性侵蝕」に感染している対象をマルザハールが通常攻撃すると、効果時間がリセットされる。", "「虚性侵蝕」をかけられたミニオンの群れには近寄らないようにしよう。体力がゼロになったとき近くにいると、侵蝕が伝染する危険がある。", "大量の「ヴォイドリング」を従えているマルザハールと戦うのは危険だ。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 6}, "stats": {"hp": 580, "hpperlevel": 101, "mp": 375, "mpperlevel": 28, "movespeed": 335, "armor": 18, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "MalzaharQ", "name": "ヴォイドコール", "description": "ヴォイドへ繋がるゲートを2カ所に発生させる。発動から一瞬遅れてゲートからエネルギーが発射され、命中した敵ユニットに魔法ダメージを与える。敵チャンピオンに対しては、さらにサイレンス効果を与える。", "tooltip": "指定地点にヴォイドへ通じるゲートを2カ所発生させ、ゲートからエネルギーが内向きに放たれる。命中した敵に<magicDamage>{{ totaldamagetooltip }}の魔法ダメージ</magicDamage>を与え、{{ e2 }}秒間<status>サイレンス効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "サイレンス効果時間", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [1, 1.25, 1.5, 1.75, 2], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "1/1.25/1.5/1.75/2", "0.4", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "MalzaharQ.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MalzaharW", "name": "ヴォイドスワーム", "description": "近くの敵を攻撃する「ヴォイドリング」を召喚する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 他のスキルを使用するとスタックを1つ獲得する(最大{{ stackcap }})。<br /><br /><spellActive>発動効果:</spellActive> 「ヴォイドリング」を1体召喚し、さらに「スワームの兆し」のスタックごとに追加で1体の「ヴォイドリング」を召喚する。{{ voidlingduration }}秒間持続し、命中するたびに<magicDamage>{{ voidlingbonusdamagetooltip }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ヴォイドリング」ダメージ", "「ヴォイドリング」効果時間", "@AbilityResourceName@コスト"], "effect": ["{{ voidlingbasedamage }} -> {{ voidlingbasedamageNL }}", "{{ voidlingduration }} -> {{ voidlingdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [25000, 25000, 25000, 25000, 25000], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "25000", "2", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "0", "range": [150, 150, 150, 150, 150], "rangeBurn": "150", "image": {"full": "MalzaharW.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MalzaharE", "name": "虚性侵蝕", "description": "指定した対象の精神を痛みに悶え苦しむ幻覚で蝕み、継続ダメージを与える。対象に他のスペルを使用すると幻覚の効果が更新される。<br><br>幻覚に侵蝕されている敵が倒れると近くの敵ユニットに効果が伝染し、マルザハールのマナが回復する。幻覚に蝕まれている敵は、マルザハールが召喚した「ヴォイドリング」に狙われる。", "tooltip": "恐ろしい幻覚を見せ、{{ duration }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。この効果中に<spellName>「ヴォイドコール」</spellName>または<spellName>「ネザーグラスプ」</spellName>を同じ対象に適用すると、「虚性侵蝕」の効果時間が更新される。<br /><br />対象が倒されると、自身は<scaleMana>{{ manarestore }}マナ</scaleMana>を獲得して、最も近くにいる敵に「虚性侵蝕」が伝染する。<br /><br /><rules>「虚性侵蝕」は体力が{{ minionexecutethreshold }}未満のミニオンにとどめを刺す。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "8", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MalzaharE.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MalzaharR", "name": "ネザーグラスプ", "description": "ダメージを与える負のエネルギーに満ちた領域上で敵チャンピオンにヴォイドのエネルギーを注ぎ込み、サプレッション効果を付与する。", "tooltip": "敵チャンピオンに<status>サプレッション効果</status>を与えて、{{ e4 }}秒かけて<magicDamage>{{ totaldamagetooltip }}の魔法ダメージ</magicDamage>を与える。対象の周囲に負のエネルギーに満ちた領域を召喚して、{{ e3 }}秒かけて<magicDamage>最大体力の{{ zonedamagetooltip }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ネザーグラスプ」基本ダメージ", "「ヴォイドゾーン」毎秒ダメージ", "クールダウン"], "effect": ["{{ beamdamage }} -> {{ beamdamageNL }}", "{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [5, 5, 5], [2.5, 2.5, 2.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "5", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MalzaharR.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ヴォイドシフト", "description": "一定時間ダメージか行動妨害を受けていない時、マルザハールは強力なダメージ軽減と行動阻害無効を得る。この効果はダメージを受けた後も短期間継続する。", "image": {"full": "Malzahar_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}