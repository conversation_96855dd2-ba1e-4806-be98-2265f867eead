{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"AurelionSol": {"id": "AurelionSol", "key": "136", "name": "Aurelion Sol", "title": "făuritorul de stele", "image": {"full": "AurelionSol.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "136000", "num": 0, "name": "default", "chromas": false}, {"id": "136001", "num": 1, "name": "<PERSON><PERSON><PERSON>, lordul întunericului", "chromas": false}, {"id": "136002", "num": 2, "name": "Aurelion Sol Mecha", "chromas": true}, {"id": "136011", "num": 11, "name": "<PERSON><PERSON><PERSON>, dragonul furtunii", "chromas": false}, {"id": "136021", "num": 21, "name": "Aurelion Sol umbră de cerneală", "chromas": false}, {"id": "136031", "num": 31, "name": "Aurelion Sol ocrotitorul de porțelan", "chromas": false}], "lore": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Aurelion Sol făurea minuni celeste pe care le răspândea în vidul nesfârșit al Cosmosului. Acum însă, e obligat să-și folosească puterile în slujba unui imperiu care l-a subjugat prin viclenie, un popor înzestrat cu puterea de a călători prin spațiu. Aurelion Sol tânjește să se întoarcă la creațiile lui și e dispus să facă orice pentru a-și recâștiga libertatea, iar furia lui va cutremura până și stelele de pe cer.", "blurb": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Aurelion Sol făurea minuni celeste pe care le răspândea în vidul nesfârșit al Cosmosului. Acum însă, e obligat să-și folosească puterile în slujba unui imperiu care l-a subjugat prin viclenie, un popor înzestrat cu puterea de a călători...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "Mană", "info": {"attack": 2, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 90, "mp": 530, "mpperlevel": 40, "movespeed": 335, "armor": 22, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "AurelionSolQ", "name": "Suflu de lumină", "description": "Aurelion Sol își folosește răsuflarea de dragon timp de câteva secunde, provocându-i daune primului inamic lovit și afectând și inamicii din apropiere, că<PERSON>ra le provoacă daune reduse. Fiecare secundă în care lovește direct un inamic provoacă daune bonus, a căror valoare crește în funcție de ''praful de stele'' obținut. Dacă ținta este un campion, abilitatea strânge ''praf de stele''.", "tooltip": "Aurelion Sol suflă foc de stele până la {{ maxchannelduration }} secunde, provocându-le <magicDamage>{{ damagepersecond }} daune magice</magicDamage> pe secundă primului inamic lovit și {{ aoemodifier*100 }}% din daune inamicilor din jur.<br /><br />Fiecare secundă completă de suflu asupra aceluiași inamic provoacă o explozie de <magicDamage>{{ burstdamage }} daune magice</magicDamage> plus <magicDamage>{{ burstbonustruedamagetochamps }} din viața maximă</magicDamage> și absoarbe <span class=\"color3458eb\">{{ qmassstolen }} ''praf de stele''</span> dacă inamicul este un campion.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cost de @AbilityResourceName@", "Daune pe secundă", "Daune explozie", "Durată maximă de pregătire"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ rankdamagepersecond }} -> {{ rankdamagepersecondNL }}", "{{ rankburstdamage }} -> {{ rankburstdamageNL }}", "{{ maxchannelduration }} -> {{ maxchanneldurationNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mană pe secundă", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolQ.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ manacostpersecond }} mană pe secundă"}, {"id": "AurelionSolW", "name": "Zbor astral", "description": "Aurelion Sol zboară pe deasupra terenului într-o direcție-țintă. În acest timp, poate folosi alte abilități. ''Suflul de lumină'' nu mai are timp de reactivare sau durată maximă de pregătire și provoacă daune mai mari în timpul zborului.<br><br>Timpul de reactivare rămas al ''Zborului astral'' se reduce de fiecare dată când un campion moare la scurt timp după ce Aurelion Sol i-a provocat daune.<br><br>''Praful de stele'' crește raza maximă a ''Zborului astral''.", "tooltip": "Aurelion Sol zboară într-o direcție. În timpul zborului, <spellName>''Suflu de lumină''</spellName> nu are timp de reactivare sau durată maximă de pregătire, iar daunele fixe cresc cu {{ truedamagebonus*100 }}%.<br /><br />Doborârile de campioni în decurs de {{ resetwindow }} secunde de când le-a provocat daune îi rambursează {{ tooltiptakedowncooldownmultiplier }}% din timpul de reactivare al acestei abilități.<br /><br /><recast>Refolosire:</recast> încheie zborul mai devreme.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune magice bonus procentuale", "Cost de @AbilityResourceName@", "Timp de reactivare"], "effect": ["{{ truedamagebonus*100.000000 }}% -> {{ truedamagebonusnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cd }} -> {{ cdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "AurelionSolW.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolE", "name": "Singularitate", "description": "Aurelion Sol invocă o gaură neagră, provocându-le daune inamicilor și trăgându-i încet spre centru. Această abilitate oferă ''praf de stele'' pentru fiecare inamic care moare în gaura neagră și pentru fiecare secundă în care un campion inamic e blocat în ea. Centrul găurii negre execută inamicii care au sub un anumit procent din viața maximă. ''Praful de stele'' crește atât zona ''Singularității'', cât și pragul de execuție.", "tooltip": "Aurelion Sol invocă o gaură neagră, provocându-le inamicilor <magicDamage>{{ damagepersecond }} daune magice</magicDamage> pe secundă și <status>trăgându-i</status> spre centru timp de {{ duration }} secunde. Inamicii din centru care au sub <scaleHealth>{{ currentexecutionthreshold }}% viață maximă</scaleHealth> mor instantaneu.<br /><br />Gaura neagră absoarbe <span class=\"color3458eb\">''praf de stele''</span> când inamicii mor în ea și în fiecare secundă în care un campion inamic se află în interior.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune pe secundă"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolE.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolR", "name": "<PERSON><PERSON> c<PERSON> / Prăbușirea cer<PERSON>lor", "description": "''Stea căzătoare'': Aurelion Sol face o stea să se prăbușească pe pământ. Impactul ei provoacă daune magice și amețește inamicii loviți, oferind ''praf de stele'' pentru fiecare inamic lovit. Când strânge suficient ''praf de stele'', următoarea ''Stea căzătoare'' a lui Aurelion Sol se transformă în ''Prăbușirea cerurilor''.<br><br>''Prăbușirea cerurilor'': Aurelion Sol trage de pe cer o stea uriașă care are o zonă de impact mai mare și provoacă mai multe daune, aruncând inamicii în sus în loc să-i amețească. Apoi, de la marginea zonei de impact se răspândește o undă de șoc care provoacă daune și încetinește inamicii loviți. ''Praful de stele'' crește zona de impact atât pentru ''Steaua căzătoare'', cât și pentru ''Prăbușirea cerurilor''.", "tooltip": "Aurelion Sol culege o stea de pe cer și o izbeș<PERSON> de pământ, provocând <magicDamage>{{ maxdamagetooltip }} daune magice</magicDamage>, <status>amețind</status> inamicii timp de {{ stunduration }} secundă și absorbind <span class=\"color3458eb\">{{ massstolen }} ''praf de stele''</span> pentru fiecare campion lovit.<br /><br />Dac<PERSON> adună <span class=\"color3458eb\">{{ calamitystacks }} ''praf de stele''</span>, transformă următoarea <spellName>''Stea căzătoare''</spellName> în <spellName>''Prăbușirea cerurilor''</spellName>.<br /><br /><spellName>''Prăbușirea cerurilor''</spellName>: Aurelion Sol trage o constelație plină de furie din cosmos, provocând <magicDamage>{{ r2damage }} daune magice</magicDamage> într-o zonă mai mare, <status>aruncând în sus</status> inamicii loviți timp de {{ stunduration }} secundă și dezlănțuind o undă de șoc uriașă care le provoacă <magicDamage>{{ shockwavedamage }} daune magice</magicDamage> inamicilor și monștrilor epici și <status>încetinește</status> toți inamicii loviți cu {{ shockwaveslow*100 }}% timp de 1 secundă.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune", "Daune îmbunătățite", "Daune undă de șoc"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*1.250000 }} -> {{ basedamagenl*1.250000 }}", "{{ basedamage*0.900000 }} -> {{ basedamagenl*0.900000 }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AurelionSolR.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Creatorul cosmosului", "description": "Abilitățile ofensive ale lui Aurelion Sol extrag <font color='#3458eb'>''praf de stele''</font> din inamicii lui, îmbunătățindu-i permanent toate abilitățile. ", "image": {"full": "AurelionSolP.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}