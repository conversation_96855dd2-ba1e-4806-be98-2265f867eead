{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sivir": {"id": "<PERSON><PERSON>", "key": "15", "name": "<PERSON><PERSON>", "title": "la signora delle battaglie", "image": {"full": "Sivir.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "15000", "num": 0, "name": "default", "chromas": false}, {"id": "15001", "num": 1, "name": "<PERSON>vir <PERSON>", "chromas": false}, {"id": "15002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "15003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "15004", "num": 4, "name": "Sivir <PERSON>", "chromas": false}, {"id": "15005", "num": 5, "name": "PAX Sivir", "chromas": false}, {"id": "15006", "num": 6, "name": "<PERSON><PERSON>eve", "chromas": true}, {"id": "15007", "num": 7, "name": "Sivir Inquisitrice", "chromas": false}, {"id": "15008", "num": 8, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "15009", "num": 9, "name": "Neo PAX Sivir", "chromas": false}, {"id": "15010", "num": 10, "name": "Sivir Pizza a Domicilio", "chromas": true}, {"id": "15016", "num": 16, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "15025", "num": 25, "name": "Sivir dell'Odissea", "chromas": true}, {"id": "15034", "num": 34, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "15043", "num": 43, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "15050", "num": 50, "name": "Sivir <PERSON> Miti", "chromas": true}, {"id": "15051", "num": 51, "name": "Sivir <PERSON> Miti (edizione prestigio)", "chromas": false}, {"id": "15061", "num": 61, "name": "<PERSON><PERSON> ferino", "chromas": true}, {"id": "15070", "num": 70, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Sivir è una famosa avventuriera e mercenaria che opera nei deserti di Shurima. Armata di una leggendaria lama a croce ornata di pietre preziose, ha combattuto e vinto innumerevoli battaglie per conto di coloro che possono permettersi il suo prezzo esorbitante. Famosa per l'incrollabile coraggio e l'ambizione senza confini, si vanta di riuscire a recuperare tesori sepolti nelle pericolose tombe di Shurima, in cambio di una generosa ricompensa. Ora che antiche forze scuotono le fondamenta stesse di Shurima, Sivir si ritrova divisa tra due opposti destini.", "blurb": "Sivir è una famosa avventuriera e mercenaria che opera nei deserti di Shurima. Armata di una leggendaria lama a croce ornata di pietre preziose, ha combattuto e vinto innumerevoli battaglie per conto di coloro che possono permettersi il suo prezzo...", "allytips": ["La Lama boomerang torna da Sivir dopo aver raggiunto la sua gittata massima. In questo modo è possibile cambiare posizione e colpire altri nemici che l'avrebbero evitata.", "Colpo di rimbalzo azzera il timer dell'attacco base di Sivir all'attivazione, quindi attivarlo subito dopo aver colpito con un attacco base ottimizza i danni inflitti.", "Cerca di risparmiare Scudo incantato per le abilità che possono ostacolarti, come gli stordimenti e le immobilizzazioni."], "enemytips": ["Per lanciare Lama boomerang serve molto mana, quindi è bene schivarlo. Se ti colpisce mentre si sta avvicinando, togliti dalla traiettoria quando torna indietro.", "<PERSON><PERSON> <PERSON> in grado di avanzare con rapidità. <PERSON><PERSON><PERSON><PERSON> da sola in corsia per troppo tempo avrà come risultato la perdita di diverse torri.", "Quando sei in corsia contro <PERSON>vir, è possibile farle perdere il ritmo con il suo Scudo incantato fingendo un'avanzata e poi tornando indietro."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 104, "mp": 340, "mpperlevel": 45, "movespeed": 335, "armor": 30, "armorperlevel": 4.45, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SivirQ", "name": "<PERSON> boomerang", "description": "<PERSON>vir scaglia la sua lama a croce come un boomerang, infliggendo danni sia all'andata che al ritorno.", "tooltip": "Sivir scaglia la sua lama a croce come un boomerang, infliggendo <physicalDamage>{{ totaldamage }}</physicalDamage> a tutti i nemici che attraversa. Colpire i non campioni riduce i danni ai bersagli successivi, fino a un minimo di {{ falloffminimum*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in mana"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "SivirQ.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SivirW", "name": "Colpo di rimbalzo", "description": "I prossimi attacchi base di Sivir ottengono velocità d'attacco aggiuntiva e rimbalzano verso i bersagli vicini, infliggendo danni ridotti quando rimbalzano.", "tooltip": "Per i prossimi {{ buffduration }} secondi, <PERSON><PERSON> o<PERSON> <attackSpeed>{{ ricochetattackspeed*100 }}% velocità d'attacco</attackSpeed> e i suoi auto-attacchi sono potenziati e rimbalzano sui nemici circostanti, infliggendo <physicalDamage>{{ bouncedamage }} danni fisici</physicalDamage> a ogni rimbalzo fino a {{ maxbounces }} rimbalzi.<br /><br />Questi rimbalzi infliggono colpi critici se lo fa l'attacco che li genera.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Rapporto attacco fisico totale"], "effect": ["{{ ricochetattackspeed*100.000000 }}% -> {{ ricochetattackspeednl*100.000000 }}%", "{{ bounceadratio*100.000000 }}% -> {{ bounceadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirW.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SivirE", "name": "<PERSON><PERSON> incantato", "description": "Crea una barriera magica che blocca una singola abilità nemica lanciata su Sivir. Ottiene salute e un aumento di velocità di movimento se un'abilità viene bloccata.", "tooltip": "<PERSON>vir crea per {{ e1 }} secondi una barriera magica che blocca la prossima abilità nemica. Se un'abilità viene bloccata, <PERSON><PERSON> recupera <healing>{{ totalheal }} salute</healing> e attiva Gioco di gambe.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rapporto attacco fisico totale", "Ricarica"], "effect": ["{{ healratio*100.000000 }}% -> {{ healrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [1.5, 1.5, 1.5, 1.5, 1.5], [55, 55, 55, 55, 55], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5", "55", "60", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirE.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SivirR", "name": "<PERSON><PERSON>cia grossa", "description": "Sivir conduce i suoi alleati in battaglia, conferendo un bonus di velocità di movimento per un periodo di tempo. In<PERSON>re, gli attacchi di Sivir riducono la ricarica delle sue abilità.", "tooltip": "Sivir raduna gli alleati vicini, conferendo loro <speed>{{ maxms*100 }}% velocità di movimento</speed> per {{ ultduration }} secondi.<br /><br />Gli attacchi di Sivir contro i campioni durante Caccia grossa riducono la ricarica delle sue abilità base di 0,5 secondi.<br /><br />Le uccisioni di nemici danneggiati di recente ripristinano la durata della caccia.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Velocità di movimento massima", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxms*100.000000 }}% -> {{ maxmsnl*100.000000 }}%", "{{ ultduration }} -> {{ ultdurationNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SivirR.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Gioco di gambe", "description": "<PERSON>vir guadagna un'accelerazione alla velocità di movimento quando attacca un campione nemico.", "image": {"full": "Sivir_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}