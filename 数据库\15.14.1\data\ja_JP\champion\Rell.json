{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rell": {"id": "<PERSON><PERSON>", "key": "526", "name": "レル", "title": "鋼鉄の乙女", "image": {"full": "Rell.png", "sprite": "champion3.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "526000", "num": 0, "name": "default", "chromas": false}, {"id": "526001", "num": 1, "name": "バトルクイーン レル", "chromas": true}, {"id": "526010", "num": 10, "name": "スターガーディアン レル", "chromas": true}, {"id": "526020", "num": 20, "name": "荒野のレル", "chromas": true}, {"id": "526030", "num": 30, "name": "最後の清算レル", "chromas": false}], "lore": "黒薔薇団による残酷な実験の産物であるレルは、ノクサスの打倒を胸に誓った、反逆の生きた兵器である。レルの幼少期は、惨めで恐ろしいものだった。魔力を完成させ、兵器化するために、彼女は口にするのも憚られるような処置に耐えてきた──暴力的な逃亡を成し遂げ、自分を捕らえようとした者たちの多くを殺めてしまうまでは。犯罪者の烙印を押されたレルは、ノクサス兵を目にした瞬間に攻撃する。かつてのアカデミーの生存者を探す彼女は、従順な者たちを守りながら、かつての教師たちには無慈悲な死を与えている。", "blurb": "黒薔薇団による残酷な実験の産物であるレルは、ノクサスの打倒を胸に誓った、反逆の生きた兵器である。レルの幼少期は、惨めで恐ろしいものだった。魔力を完成させ、兵器化するために、彼女は口にするのも憚られるような処置に耐えてきた──暴力的な逃亡を成し遂げ、自分を捕らえようとした者たちの多くを殺めてしまうまでは。犯罪者の烙印を押されたレルは、ノクサス兵を目にした瞬間に攻撃する。かつてのアカデミーの生存者を探す彼女は、従順な者たちを守りながら、かつての教師たちには無慈悲な死を与えている。", "allytips": [], "enemytips": [], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 620, "hpperlevel": 104, "mp": 320, "mpperlevel": 40, "movespeed": 315, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.8, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.85, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "RellQ", "name": "破鋼撃", "description": "直線上のユニットに魔法ダメージを与え、対象のシールドを破壊してスタンさせる。 ", "tooltip": "ランスを突き出して、対象を{{ stunduration }}秒間<status>スタン</status>させ、<shield>シールド</shield>をすべて破壊し、<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RellQ.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON>_Dismount", "name": "フェロマンシー: 装着", "description": "騎馬形態: 「騎馬解除」をして、鎧をまとって突撃し、敵をノックアップさせて大量のシールドを獲得する。騎馬解除中は物理防御、魔法防御、攻撃速度、射程距離が増加するが、移動速度が低下する。<br><br>騎馬解除中: 鋼鉄の馬を形成して「騎馬形態」となり、瞬間的に移動速度が増加して、次に通常攻撃を行った敵をノックアップさせる。<br><br>", "tooltip": "<spellPassive>自動効果 - 騎馬の俊足</spellPassive>: 騎乗中は<speed>移動速度が{{ spell.rellw_dismount:mountedmovespeed }}</speed>増加する。<br /><br /><spellActive>発動効果 - 「フェロマンシー: 装着」: </spellActive>騎馬を解除して跳躍し、敵を<status>ノックアップ</status>させて、<magicDamage>{{ spell.rellw_dismount:dismountdamage }}の魔法ダメージ</magicDamage>を与える。自身は<shield>耐久値{{ spell.rellw_dismount:shield }}のシールド</shield>を獲得する。このシールドは再度騎乗するまで持続する。<br /><br />その後、鎧身形態となって<scaleMR>魔法防御</scaleMR>と<scaleArmor>物理防御が{{ spell.rellw_dismount:resistanceincrease*100 }}%</scaleArmor>、<attackSpeed>攻撃速度が{{ spell.rellw_dismount:dismountedasboost*100 }}%</attackSpeed>、射程距離が{{ spell.rellw_dismount:dismountedrangeboost }}増加する。鎧身形態中は<spellName>「フェロマンシー: 騎馬」</spellName>を使用できる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["騎馬解除時ダメージ", "シールド量", "放り投げダメージ", "自動効果による増加移動速度"], "effect": ["{{ crashdowndamage }} -> {{ crashdowndamageNL }}", "{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ mountupdamage }} -> {{ mountupdamageNL }}", "{{ mountedmovespeed }} -> {{ mountedmovespeedNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RellW_Dismount.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Rell<PERSON>", "name": "重槍突貫", "description": "自動効果: 非戦闘時の移動速度が増加する。<br><br>発動効果: 自身と味方1体の移動速度が徐々に増加する。敵またはお互いの方向に移動する場合、この増加量は2倍になる。次の通常攻撃で爆発を引き起こし、魔法ダメージを与える。<br>", "tooltip": "レルと味方1体が突撃し、{{ duration }}秒間、<speed>移動速度が{{ minms*100 }}%</speed>増加する。敵チャンピオンまたはお互いの方を向いている場合は、この増加移動速度が<speed>{{ maxms*100 }}%</speed>に増加する。次の通常攻撃または範囲内の<spellName>「破鋼撃」</spellName>が爆発し、<magicDamage>最大体力の{{ maxhealthdamagecalc }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ percenthealthdamage*100.000000 }}% -> {{ percenthealthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "RellE.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RellR", "name": "磁気嵐流", "description": "猛烈な磁場を発生させ、周囲の敵を自身の方向に引き寄せる。その後も少しの間、継続的に周囲の敵を自身の方向に引き付け、効果時間をかけて魔法ダメージを与える。", "tooltip": "猛烈な磁場を発生させ、周囲の敵を自身の方向に<status>引き寄せる</status>。その後も継続的に周囲の敵を自身の方向に<status>引き付け</status>、{{ duration }}秒間かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamagepersecond*2.000000 }} -> {{ basedamagepersecondnl*2.000000 }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "RellR.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "革新の鬨", "description": "通常攻撃またはスキルを命中させると追加で魔法ダメージを与え、対象から物理防御と魔法防御を奪う。", "image": {"full": "RellP.png", "sprite": "passive3.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}