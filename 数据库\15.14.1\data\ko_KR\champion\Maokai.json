{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Maokai": {"id": "Maokai", "key": "57", "name": "마오카이", "title": "뒤틀린 나무 정령", "image": {"full": "Maokai.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "57000", "num": 0, "name": "default", "chromas": false}, {"id": "57001", "num": 1, "name": "그을린 마오카이", "chromas": false}, {"id": "57002", "num": 2, "name": "토템 마오카이", "chromas": false}, {"id": "57003", "num": 3, "name": "눈맞이 축제 마오카이", "chromas": false}, {"id": "57004", "num": 4, "name": "악령 마오카이", "chromas": false}, {"id": "57005", "num": 5, "name": "골키퍼 마오카이", "chromas": false}, {"id": "57006", "num": 6, "name": "냐옹카이", "chromas": true}, {"id": "57007", "num": 7, "name": "승리의 마오카이", "chromas": false}, {"id": "57016", "num": 16, "name": "파괴의 신 마오카이", "chromas": true}, {"id": "57024", "num": 24, "name": "우주비행사 마오카이", "chromas": true}, {"id": "57033", "num": 33, "name": "DRX 마오카이", "chromas": true}], "lore": "거대한 나무 정령 마오카이는 분노에 휩싸여 그림자 군도의 초자연적인 언데드와 싸운다. 마법에 의한 대격변으로 고향이 파괴되었을 때 그는 자신의 나무 심장에 스며 있는 생명의 정수로 언데드의 상태는 모면했지만 형체가 뒤틀린 복수의 화신이 되었다. 한 때는 평화를 사랑하는 자연의 정령이었으나 이제 그는 그림자 군도를 뒤덮은 언데드를 몰아내고 아름답던 고향의 옛 모습을 되찾기 위해 맹렬하게 싸운다.", "blurb": "거대한 나무 정령 마오카이는 분노에 휩싸여 그림자 군도의 초자연적인 언데드와 싸운다. 마법에 의한 대격변으로 고향이 파괴되었을 때 그는 자신의 나무 심장에 스며 있는 생명의 정수로 언데드의 상태는 모면했지만 형체가 뒤틀린 복수의 화신이 되었다. 한 때는 평화를 사랑하는 자연의 정령이었으나 이제 그는 그림자 군도를 뒤덮은 언데드를 몰아내고 아름답던 고향의 옛 모습을 되찾기 위해 맹렬하게 싸운다.", "allytips": ["묘목을 수풀에 던지면 효과가 향상되지만 잘 쌓이지는 않습니다.", "대자연의 마수는 적이 도저히 피할 수 없는 상황에서 쓰거나 기습할 때 사용하면 좋습니다.", "마오카이는 기본 지속 효과 덕분에 기본 공격만 꾸준히 할 수 있다면 적이 스킬을 마구 퍼부어도 잘 버틸 수 있습니다."], "enemytips": ["묘목은 처음으로 사거리 안에 들어온 적을 추격하며, 몇 초가 지나거나 다른 적에 닿으면 폭발합니다. 특히 수풀에 있는 묘목은 효과가 더 강력하니 반드시 조심하세요.", "마오카이의 체력 회복 재사용 대기시간은 스킬에 맞을 때마다 짧아지니 상황에 따라서는 마오카이에게 스킬을 허비하지 않는 게 좋습니다.", "마오카이가 뒤틀린 전진을 쓰는 동안에는 피해를 입지 않으므로 스킬이나 주문을 낭비하지 마세요."], "tags": ["Tank", "Support"], "partype": "마나", "info": {"attack": 3, "defense": 8, "magic": 6, "difficulty": 3}, "stats": {"hp": 665, "hpperlevel": 109, "mp": 375, "mpperlevel": 43, "movespeed": 335, "armor": 35, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.75, "mpregen": 6, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.125, "attackspeed": 0.8}, "spells": [{"id": "MaokaiQ", "name": "덤불 주먹", "description": "마오카이가 충격파를 일으켜 근처의 적을 뒤로 날려버리고 마법 피해를 입히며 둔화시킵니다.", "tooltip": "마오카이가 지면을 주먹으로 내리쳐 <magicDamage>{{ totaldamage }}+최대 체력의 {{ basepercenthealth*100 }}%에 해당하는 마법 피해</magicDamage>를 입히고 적들을 잠시 <status>둔화</status>시킵니다. 주변 적들은 <status>뒤로 밀려납니다</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "최대 체력 %", "몬스터 피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basepercenthealth*100.000000 }}% -> {{ basepercenthealthnl*100.000000 }}%", "{{ bonusmonsterdamage }} -> {{ bonusmonsterdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "MaokaiQ.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MaokaiW", "name": "뒤틀린 전진", "description": "마오카이가 움직이는 뿌리 덩어리로 변신해 대상에게 도약해 속박합니다. 이때 마오카이는 대상으로 지정할 수 없습니다.", "tooltip": "마오카이가 움직이는 뿌리 덩어리로 변신해 대상에게 돌진합니다. 이때 마오카이는 대상으로 지정할 수 없습니다. 적에게 부딪히면 {{ e2 }}초간 대상을 <status>속박</status>하고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "속박 지속시간", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [60, 85, 110, 135, 160], [1, 1.1, 1.2, 1.3, 1.4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/85/110/135/160", "1/1.1/1.2/1.3/1.4", "0", "0", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "MaokaiW.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MaokaiE", "name": "묘목 던지기", "description": "마오카이가 묘목을 던져 해당 지역을 감시하게 합니다. 수풀에서는 효과가 향상됩니다.", "tooltip": "마오카이가 {{ saplingduration }}초 동안 주변을 감시하는 묘목을 던집니다. 묘목은 근처 적을 추격해 접근 시 폭발하며 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 주변 적들을 {{ slowduration }}초간 {{ slowamount*100 }}% <status>둔화</status>시킵니다. 묘목이 적 챔피언이나 에픽 몬스터를 맞히면 <keywordMajor>마법 흡수</keywordMajor>의 재사용 대기시간이 추가로 4초 감소합니다.<br /><br />수풀에 설치된 묘목은 {{ empoweredsaplingduration }}초간 유지되며 더 큰 폭발을 일으켜 <magicDamage>{{ totalempowereddamage }}의 마법 피해</magicDamage>를 {{ empowereddotduration }}초에 걸쳐 입히고 적들을 {{ empoweredslowamount }} <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "강화된 피해", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ empoweredbasedam<PERSON> }} -> {{ empoweredbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "MaokaiE.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MaokaiR", "name": "대자연의 마수", "description": "마오카이가 나뭇가지와 가시로 된 거대 벽을 소환합니다. 벽은 천천히 전진하며 벽에 닿은 적에게 피해를 입히고 속박합니다.", "tooltip": "마오카이가 나뭇가지와 가시로 된 거대한 벽을 소환합니다. 벽은 이동한 거리에 비례해 {{ minrootduration }}~{{ maxrootduration }}초간 적들을 <status>속박</status>하고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 벽이 적 챔피언에게 부딪히면 마오카이의 <speed>이동 속도가 {{ movehaste*100 }}%</speed> 증가했다 {{ hasteduration }}초에 걸쳐 원래대로 돌아옵니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "이동 속도", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movehaste*100.000000 }}% -> {{ movehastenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0.6, 0.6, 0.6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0.6", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "MaokaiR.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "마법 흡수", "description": "마오카이는 기본 지속 효과로 기본 공격 시 체력을 회복하고 추가 피해를 입힙니다. 적 스킬에 맞거나 직접 스킬을 사용하면 기본 지속 효과의 재사용 대기시간이 감소합니다.", "image": {"full": "Maokai_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}