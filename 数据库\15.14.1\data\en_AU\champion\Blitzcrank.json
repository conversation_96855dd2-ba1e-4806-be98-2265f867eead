{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "the Great Steam Golem", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "53002", "num": 2, "name": "Goalkeeper <PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "53003", "num": 3, "name": "Boom Boom Blitzcrank", "chromas": false}, {"id": "53004", "num": 4, "name": "Piltover Customs Blitzcrank", "chromas": false}, {"id": "53005", "num": 5, "name": "Definitely Not Blitzcrank", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Riot Blitzcrank", "chromas": false}, {"id": "53011", "num": 11, "name": "Battle Boss Blitzcrank", "chromas": true}, {"id": "53020", "num": 20, "name": "Lancer <PERSON>crank", "chromas": false}, {"id": "53021", "num": 21, "name": "Lancer Paragon Blitzcrank", "chromas": false}, {"id": "53022", "num": 22, "name": "Witch's <PERSON><PERSON>", "chromas": true}, {"id": "53029", "num": 29, "name": "Space Groove Blitz & Crank", "chromas": true}, {"id": "53036", "num": 36, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "53047", "num": 47, "name": "Zenith Games Blitzcrank", "chromas": true}, {"id": "53056", "num": 56, "name": "Bee<PERSON><PERSON>rank", "chromas": true}], "lore": "<PERSON><PERSON><PERSON><PERSON> is an enormous, near-indestructible automaton from Zaun, originally built to dispose of hazardous waste. However, he found this primary purpose too restricting, and modified his own form to better serve the fragile people of the Sump. <PERSON><PERSON><PERSON><PERSON> selflessly uses his strength and durability to protect others, extending a helpful metal fist or burst of energy to subdue any troublemakers.", "blurb": "<PERSON><PERSON><PERSON><PERSON> is an enormous, near-indestructible automaton from Zaun, originally built to dispose of hazardous waste. However, he found this primary purpose too restricting, and modified his own form to better serve the fragile people of the Sump...", "allytips": ["The 1-2-3 combo of Rocket Grab, Power Fist, and Static Field can devastate an individual opponent.", "Using <PERSON><PERSON><PERSON><PERSON>'s grab to pull an enemy into your tower range followed by a Power Fist will allow the tower to get several hits on them."], "enemytips": ["B<PERSON>crank's passive <PERSON><PERSON> grants him a shield when he is low on health.", "Staying behind creeps can prevent you from being Rocket Grabbed. Blitzcrank's <PERSON> Grab only pulls the first enemy target it encounters."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "Rocket Grab", "description": "Blitzcrank fires their right hand to grab an opponent on its path, dealing damage and dragging it back to them.", "tooltip": "Blitzcrank fires their right hand, <status>Pulling</status> the first enemy hit towards them and dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Overdrive", "name": "Overdrive", "description": "Blitzcrank super charges themself to get dramatically increased Move and Attack Speed. They are temporarily slowed after the effect ends.", "tooltip": "Blitzcrank supercharges themself, gaining <speed>{{ movespeedmod*100 }}% decaying Move Speed</speed> and <attackSpeed>{{ attackspeedmod*100 }}% Attack Speed</attackSpeed> for {{ duration }} seconds.<br /><br />Afterwards, Blitzcrank is <status>Slowed</status> by {{ movespeedmodreduction*100 }}% for {{ slowduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Move Speed", "Attack Speed"], "effect": ["{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PowerFist", "name": "Power Fist", "description": "Blitzcrank charges up their fist to make the next attack deal double damage and pop their target up in the air.", "tooltip": "Blitzcrank charges up their fist, causing their next Attack to <status>Knock Up</status> for {{ ccduration }} second(s) and deal <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "StaticField", "name": "Static Field", "description": "Enemies attacked by Blitzcrank are marked and take lightning damage after 1 second. Additionally, Blitzcrank can activate this ability to remove nearby enemies' shields, damage them, and silence them briefly.", "tooltip": "<spellPassive>Passive: </spellPassive>While this ability is available, lightning charges <PERSON><PERSON><PERSON><PERSON>'s fists, marking those  Attacked. After 1 second, they are shocked for <magicDamage>{{ passivedamage }} magic damage</magicDamage>.<br /><br /><spellActive>Active: </spellActive>Blitzcrank overcharges, dealing <magicDamage>{{ activedamage }} magic damage</magicDamage> and <status>Silencing</status> nearby enemies for {{ silenceduration }} seconds. Their shields are also destroyed.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Passive Base Damage", "Passive AP Ratio", "Active Base Damage", "Active Cooldown"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}% -> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Blitzcrank gains a shield based on their mana when dropping to low health.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}