{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Renekton": {"id": "Renekton", "key": "58", "name": "Renekton", "title": "il macellaio delle dune", "image": {"full": "Renekton.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "58000", "num": 0, "name": "default", "chromas": false}, {"id": "58001", "num": 1, "name": "Renekton Galattico", "chromas": false}, {"id": "58002", "num": 2, "name": "Renekton Dundee", "chromas": false}, {"id": "58003", "num": 3, "name": "Renekton Furia Sanguinaria", "chromas": false}, {"id": "58004", "num": 4, "name": "Renekton Guerriero delle Rune", "chromas": false}, {"id": "58005", "num": 5, "name": "Renekton Terra Bruciata", "chromas": false}, {"id": "58006", "num": 6, "name": "Renekton Festa in Piscina", "chromas": false}, {"id": "58007", "num": 7, "name": "Renekton Preistorico", "chromas": false}, {"id": "58008", "num": 8, "name": "Renekton SKT T1", "chromas": false}, {"id": "58009", "num": 9, "name": "Renekton Giocattolo", "chromas": true}, {"id": "58017", "num": 17, "name": "Renekton Hextech", "chromas": false}, {"id": "58018", "num": 18, "name": "Renekt<PERSON>o", "chromas": true}, {"id": "58026", "num": 26, "name": "PROGETTO: Renekton", "chromas": true}, {"id": "58033", "num": 33, "name": "Renekton Portatore dell'Alba", "chromas": true}, {"id": "58042", "num": 42, "name": "Renekton Mondiali 2023", "chromas": true}, {"id": "58048", "num": 48, "name": "Renekton Nerinchiostro", "chromas": true}], "lore": "Renekton è un terribile e violento essere Asceso che proviene dagli aridi deserti di Shurima. Un tempo era il guerriero più stimato dell'impero e grazie a lui le armate della nazione ottennero innumerevoli vittorie. Tuttavia, dopo la caduta dell'impero, Renekton fu infossato sotto le sabbie e, mentre il mondo veniva stravolto, cadde lentamente  nella follia. Ora è di nuovo libero, logorato dall'ossessione di trovare e uccidere suo fratello Nasus, che, secondo la sua pazzia, è l'unica causa per la quale ha trascorso tutti questi secoli nell'oscurità.", "blurb": "Renekton è un terribile e violento essere Asceso che proviene dagli aridi deserti di Shurima. Un tempo era il guerriero più stimato dell'impero e grazie a lui le armate della nazione ottennero innumerevoli vittorie. Tuttavia, dopo la caduta dell'impero...", "allytips": ["Taglia e affetta eccelle quando fai manovre di tormento. <PERSON><PERSON>tta, colpisci con un'altra abilità e ri-affetta all'indietro a distanza di sicurezza.", " Elimina i deboli assorbe un'enorme quantità di vita quando è usata nel mezzo della mischia. Puoi usarla per attirare i tuoi avversari, facendogli credere che tu sia più debole di quanto non sei in realtà.", "La riduzione ricarica è molto utile per Renekton: gli permette di usare più velocemente le sue abilità e la sua Furia."], "enemytips": ["Fai attenzione alla barra della Furia di Renekton: ti indica quando sta per attaccare.", "Impedire a Renekton di combattere e guadagnare Furia tormentandolo continuamente riduce l'efficacia delle sue abilità."], "tags": ["Fighter", "Tank"], "partype": "Furia", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 660, "hpperlevel": 111, "mp": 100, "mpperlevel": 0, "movespeed": 345, "armor": 35, "armorperlevel": 5.2, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.15, "attackspeedperlevel": 2.75, "attackspeed": 0.665}, "spells": [{"id": "RenektonCleave", "name": "Elimina i deboli", "description": "Renekton colpisce con la sua lama, che infligge modesti danni fisici a tutti i bersagli intorno a lui e lo guarisce di una piccola parte dei danni inflitti. Se ha più di 50 Furia, i suoi danni e la sua guarigione sono aumentati.", "tooltip": "Renekton colpisce con la sua lama, infliggendo <physicalDamage>{{ basicdamage }} danni fisici</physicalDamage> e ripristinando <healing>{{ nonchamphealing }} salute</healing> per ogni non campione colpito e <healing>{{ champhealing }} salute</healing> per ogni campione colpito. Genera anche <keywordMajor>{{ minionfurygain }} Furia</keywordMajor> per ogni non campione colpito e <keywordMajor>{{ championfurygain }} Furia</keywordMajor> per ogni campione colpito.<br /><br /><keywordMajor>Bonus Furia</keywordMajor>: I danni vengono aumentati a <physicalDamage>{{ empdamage }} danni fisici</physicalDamage> e la guarigione viene aumentata a <healing>{{ empnonchamphealing }} salute</healing> per ogni non campione colpito e a <healing>{{ empchamphealing }} salute</healing> per ogni campione colpito. Non genera <keywordMajor>Furia</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Guarigione per campione", "Guarigione per non campione", "Guarigione massima"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ champheal }} -> {{ champhealNL }}", "{{ baseminionheal }} -> {{ baseminionhealNL }}", "{{ basichealcap }} -> {{ basichealcapNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "RenektonCleave.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "RenektonPreExecute", "name": "Predatore spie<PERSON>o", "description": "Renekton colpisce il suo bersaglio due volte, infliggendo modesti danni fisici e stordendolo per 0,75 secondi. Se Renekton ha più di 50 Furia, colpisce il suo nemico tre volte, distruggendo gli scudi del bersaglio e infliggendo seri danni fisici, stordendolo per 1,5 secondi.", "tooltip": "Il prossimo attacco di Renekton colpisce due volte, <status>stordendo</status> il suo bersaglio per {{ stunduration }} secondi e infliggendo un totale di <physicalDamage>{{ basictotaldamage }} danni fisici</physicalDamage>. Colpire un campione genera <keywordMajor>{{ bonusfuryvschamps }} Furia</keywordMajor> in più.<br /><br /><keywordMajor>Bonus Furia</keywordMajor>: Renekton attacca 3 volte, distruggendo gli <shield>scudi</shield> prima di infliggere <physicalDamage>{{ emptotaldamage }} danni fisici</physicalDamage> e <status>stordendo</status> per {{ enragedstunduration }} secondi. Non genera <keywordMajor>Furia</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamageperhit*2.000000 }} -> {{ basedamageperhitnl*2.000000 }}", "{{ basedamageperhit*3.000000 }} -> {{ basedamageperhitnl*3.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "RenektonPreExecute.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "RenektonSliceAndDice", "name": "Taglia e affetta", "description": "Renekton scatta in avanti, infliggendo danni alle unità sulla sua strada. Se potenziato, Renekton infligge danni bonus e riduce l'armatura delle unità colpite.", "tooltip": "Renekton scatta in avanti, infliggendo <physicalDamage>{{ basicdamage }} danni fisici</physicalDamage>. Genera <keywordMajor>{{ minionragegeneration }} Furia</keywordMajor> per ogni non campione colpito e <keywordMajor>{{ championragegeneration }} Furia</keywordMajor> per ogni campione colpito. Colpire almeno un nemico permette a Renekton di <recast>rilanciare</recast> questa abilità una volta per {{ dicetimer }} secondi. <br /><br /><keywordMajor>Bonus Furia</keywordMajor>: lo scatto del <recast>rilancio</recast> infligge <physicalDamage>{{ empdamage }} danni fisici</physicalDamage> e rimuove <scaleArmor>{{ enragedarmorshred }}% armatura</scaleArmor> per {{ shredtimer }} secondi. Non genera <keywordMajor>Furia</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Riduzione armatura %", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ enragedbasedamage }} -> {{ enragedbasedamageNL }}", "{{ enragedarmorshred }}% -> {{ enragedarmorshredNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RenektonSliceAndDice.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "RenektonReignOfTheTyrant", "name": "<PERSON><PERSON>", "description": "Renekton si trasforma nel Tiranno, guadagnando salute bonus e infliggendo danni ai nemici intorno a sé. <PERSON><PERSON> è in questa forma, Renekton guadagna Furia nel tempo.", "tooltip": "Renekton si circonda di energie oscure per {{ buffduration }} secondi, guadagnando <healing>{{ healthgain }} salute massima</healing> e <keywordMajor>{{ furyoncast }} Furia</keywordMajor>. Quando è attiva, infligge <magicDamage>{{ totaldamagepersecond }} danni magici</magicDamage> e ottiene <keywordMajor>{{ furypersecond }} Furia</keywordMajor> al secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute bonus", "<PERSON><PERSON> al secondo", "Ricarica"], "effect": ["{{ healthgain }} -> {{ healthgainNL }}", "{{ auradamagepersecond }} -> {{ auradamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "RenektonReignOfTheTyrant.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Regno della rabbia", "description": "Gli attacchi di Renekton generano Furia, che aumenta quando ha salute bassa. Questa Furia può potenziare le abilità con degli effetti bonus.", "image": {"full": "Renekton_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}