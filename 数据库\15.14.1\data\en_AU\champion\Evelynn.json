{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Evelynn": {"id": "<PERSON><PERSON>", "key": "28", "name": "<PERSON><PERSON>", "title": "Agony's Em<PERSON>ce", "image": {"full": "Evelynn.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "28000", "num": 0, "name": "default", "chromas": false}, {"id": "28001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "28002", "num": 2, "name": "Masquerade Evelynn", "chromas": false}, {"id": "28003", "num": 3, "name": "<PERSON>", "chromas": false}, {"id": "28004", "num": 4, "name": "Safecracker <PERSON>", "chromas": false}, {"id": "28005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "28006", "num": 6, "name": "K/DA Evelynn", "chromas": false}, {"id": "28007", "num": 7, "name": "Prestige K/DA Evelynn", "chromas": false}, {"id": "28008", "num": 8, "name": "<PERSON>", "chromas": true}, {"id": "28015", "num": 15, "name": "K/DA ALL OUT Evelynn", "chromas": true}, {"id": "28024", "num": 24, "name": "Coven <PERSON>", "chromas": true}, {"id": "28031", "num": 31, "name": "Prestige K/DA Evelynn (2022)", "chromas": false}, {"id": "28032", "num": 32, "name": "Spirit Blossom <PERSON><PERSON>", "chromas": true}, {"id": "28042", "num": 42, "name": "Soul Fighter Evelyn<PERSON>", "chromas": true}, {"id": "28052", "num": 52, "name": "High Noon Evelynn", "chromas": true}, {"id": "28053", "num": 53, "name": "Prestige High Noon Evelynn", "chromas": false}, {"id": "28064", "num": 64, "name": "Nightbringer <PERSON>", "chromas": false}], "lore": "Within the dark seams of Runeterra, the demon <PERSON><PERSON> searches for her next victim. She lures in prey with the voluptuous façade of a human female, but once a person succumbs to her charms, <PERSON><PERSON>'s true form is unleashed. She then subjects her victim to unspeakable torment, gratifying herself with their pain. To the demon, these liaisons are innocent flings. To the rest of Runeterra, they are ghoulish tales of lust gone awry and horrific reminders of the cost of wanton desire.", "blurb": "Within the dark seams of Runeterra, the demon <PERSON><PERSON> searches for her next victim. She lures in prey with the voluptuous façade of a human female, but once a person succumbs to her charms, <PERSON><PERSON>'s true form is unleashed. She then subjects her victim...", "allytips": ["<PERSON><PERSON>'s arm time might seem long, but the Charm and magic resist shred put <PERSON><PERSON> at an exteme advantage so are worth the wait.", "While stealthed, pay attention to when you are (near) being detected by enemy champions. This is identified by the glowing yellow and red eyes over nearby enemy champions.", "If at low health, you can take advantage of <PERSON> Shade's healing and Camouflage to return to the fight and surprise opponents."], "enemytips": ["Purchasing Vision Wards can help you detect <PERSON><PERSON>'s location in order to prepare for her ambushes.", "A large share of <PERSON><PERSON>'s threat is in her charm, '<PERSON><PERSON>.' Protect allies marked with 'Allure' or, if you are marked, make sure allies are between you and where <PERSON><PERSON> may attack from.", "If you suspect <PERSON><PERSON> is about to ambush one of your teammates, let them know by pinging the minimap and typing in chat."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 642, "hpperlevel": 98, "mp": 315, "mpperlevel": 42, "movespeed": 335, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 8.11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.1, "attackspeed": 0.667}, "spells": [{"id": "Evelynn<PERSON>", "name": "Hate Spike", "description": "<PERSON><PERSON> strikes out with her <PERSON><PERSON>, dealing damage to the first unit hit. Then, <PERSON><PERSON> can shoot a line of spikes at nearby foes a few times.", "tooltip": "<PERSON><PERSON> strikes with her <PERSON><PERSON>, dealing <magicDamage>{{ missiledamage }} magic damage</magicDamage> to the first enemy hit and causing <PERSON><PERSON>'s next 3 Attacks or Abilities on that unit to deal an additional <magicDamage>{{ totalbonusdamage }} magic damage</magicDamage>. <PERSON><PERSON> can <recast>Recast</recast> this Ability up to {{ qstackcount }} times.<br /><br /><recast>Recast:</recast> <PERSON><PERSON> fires spikes through the nearest enemy, dealing <magicDamage>{{ missiledamage }} magic damage</magicDamage> to all enemies hit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> and <PERSON> Damage", "Bonus Damage", "@AbilityResourceName@ Cost"], "effect": ["{{ hatespikebasedamage }} -> {{ hatespikebasedamageNL }}", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [30, 30, 30, 30, 30], [60, 60, 60, 60, 60], [15, 25, 35, 45, 55], [25, 30, 35, 40, 45], [6, 6, 6, 6, 6], [30, 30, 30, 30, 30], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [-0.25, -0.25, -0.25, -0.25, -0.25]], "effectBurn": [null, "0", "30", "60", "15/25/35/45/55", "25/30/35/40/45", "6", "30", "50", "4", "-0.25"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EvelynnQ.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnW", "name": "Allure", "description": "<PERSON><PERSON> curses her target, causing her next attack or spell after a delay to charm her target and reduce their magic resist.", "tooltip": "<PERSON><PERSON> marks a champion or monster for 5 seconds. If <PERSON><PERSON> hits the target with an Attack or Ability, she will expunge the mark, refund its cost, and <status>Slow</status> the target by {{ slowamount*100 }}% for {{ slowduration }} seconds.<br /><br />If the mark lasts at least 2.5 seconds, expunging it has extra effects:<li>Against champions: <status>Charms</status> them for {{ charmduration }} second(s) and removes <scaleMR>{{ mrshred*100 }}% Magic Resist</scaleMR> for {{ shredduration }} seconds.<li>Against monsters: <status>Charms</status> them for {{ monstercharm }} seconds and deals <magicDamage>{{ monsterdamagetotaltooltip }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Charm Duration", "Monster Charm Duration", "Magic Resist Shred", "Monster Damage", "Cooldown", "Range"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ monstercharm }} -> {{ monstercharmNL }}", "{{ effect9amount*100.000000 }}% -> {{ effect9amountnl*100.000000 }}%", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [1.25, 1.5, 1.75, 2, 2.25], [-0.45, -0.45, -0.45, -0.45, -0.45], [15, 14, 13, 12, 11], [5, 5, 5, 5, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [250, 300, 350, 400, 450], [0.75, 0.75, 0.75, 0.75, 0.75], [0.35, 0.375, 0.4, 0.425, 0.45], [4, 4, 4, 4, 4]], "effectBurn": [null, "2", "1.25/1.5/1.75/2/2.25", "-0.45", "15/14/13/12/11", "5", "1.5", "250/300/350/400/450", "0.75", "0.35/0.375/0.4/0.425/0.45", "4"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1300, 1400, 1500, 1600], "rangeBurn": "1200/1300/1400/1500/1600", "image": {"full": "EvelynnW.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnE", "name": "Whiplash", "description": "<PERSON><PERSON> whips her target with her <PERSON><PERSON>, dealing damage. She then gains Move Speed for a short duration.", "tooltip": "<PERSON><PERSON> whips an enemy, dealing <magicDamage>{{ basedamage }} plus {{ percenthealthbasetooltip }} max Health magic damage</magicDamage>. <PERSON><PERSON> gains <speed>{{ speedamount*100 }}% Move Speed</speed> for {{ speedduration }} seconds.<br /><br />Entering <keywordMajor>Demon Shade</keywordMajor> refreshes this Ability's cooldown and empowers it. When this Ability is empowered, <PERSON><PERSON> dashes to the target and deals <magicDamage>{{ empowereddamage }} plus {{ percenthealthempoweredtooltip }} max Health magic damage</magicDamage> to her target and everyone she passes through instead.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Empowered Damage", "Move Speed", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ empowered<PERSON><PERSON> }} -> {{ empowereddamageNL }}", "{{ speedamount*100.000000 }}% -> {{ speedamountnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.3, 0.35, 0.4, 0.45, 0.5], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [4, 4, 4, 4, 4], [450, 450, 450, 450, 450], [0.8, 0.85, 0.9, 0.95, 1], [2, 2, 2, 2, 2], [1.3, 1.35, 1.4, 1.45, 1.5]], "effectBurn": [null, "0", "0", "0.3/0.35/0.4/0.45/0.5", "2", "3", "4", "450", "0.8/0.85/0.9/0.95/1", "2", "1.3/1.35/1.4/1.45/1.5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [210, 210, 210, 210, 210], "rangeBurn": "210", "image": {"full": "EvelynnE.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnR", "name": "Last Caress", "description": "<PERSON><PERSON> briefly goes untargetable and decimates the area in front of her before warping backwards a long distance.", "tooltip": "<PERSON><PERSON> unleashes her demonic energy, dealing heavy damage, becoming Untargetable and teleporting backwards. She deals <magicDamage>{{ damage }} magic damage</magicDamage>, increased to <magicDamage>{{ critdamage }}</magicDamage> against enemies below <healing>30% Health</healing>. Upon cast, set Demon Shade to a 1.25 second cooldown.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Empowered Damage", "Cooldown"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect1amount*2.400000 }} -> {{ effect1amountnl*2.400000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 250, 375], [1.4, 1.4, 1.4], [2.5, 2.5, 2.5], [150, 225, 300], [3, 3, 3], [5, 4, 3], [0.3, 0.3, 0.3], [700, 700, 700], [30, 45, 60], [0, 0, 0]], "effectBurn": [null, "125/250/375", "1.4", "2.5", "150/225/300", "3", "5/4/3", "0.3", "700", "30/45/60", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EvelynnR.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Demon Shade", "description": "When out of combat, <PERSON><PERSON> enters Demon Shade. <PERSON> Shade heals <PERSON><PERSON> when she is low on health and grants <PERSON><PERSON><PERSON>lage after level 6.", "image": {"full": "Evelynn_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}