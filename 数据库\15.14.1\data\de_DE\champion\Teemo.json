{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Teemo": {"id": "<PERSON><PERSON><PERSON>", "key": "17", "name": "<PERSON><PERSON><PERSON>", "title": "der flinke Späher", "image": {"full": "Teemo.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "17000", "num": 0, "name": "default", "chromas": false}, {"id": "17001", "num": 1, "name": "Glücklicher Elf-Teemo", "chromas": false}, {"id": "17002", "num": 2, "name": "Späher-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17003", "num": 3, "name": "Dachs-Teem<PERSON>", "chromas": false}, {"id": "17004", "num": 4, "name": "Astronauten-Teemo", "chromas": true}, {"id": "17005", "num": 5, "name": "Plüschhäschen-Teemo", "chromas": true}, {"id": "17006", "num": 6, "name": "Super-Teemo", "chromas": false}, {"id": "17007", "num": 7, "name": "Panda-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "17008", "num": 8, "name": "Omegatrupp-Teemo", "chromas": true}, {"id": "17014", "num": 14, "name": "Teufelchen-Teemo", "chromas": true}, {"id": "17018", "num": 18, "name": "Kleine Biene Teemo", "chromas": true}, {"id": "17025", "num": 25, "name": "Seelenblumen-Teemo", "chromas": false}, {"id": "17027", "num": 27, "name": "Seelenblumen-Teemo (Prestige)", "chromas": false}, {"id": "17037", "num": 37, "name": "Feuerwerks-Teemo", "chromas": true}, {"id": "17047", "num": 47, "name": "Weltraum-Groove-Teemo", "chromas": true}], "lore": "Teemo schreckt sogar vor den gefährlichsten und furchterregendsten Problemen nicht zurück. Er erkundet die Welt mit scheinbar grenzenloser Begeisterung und fröhlichem Gemüt. Der Yordle rückt nie von seinen moralischen Prinzipien ab und rühmt sich, stets den Kodex der Bandle-Späher zu befolgen. Manchmal tut er dies derart eifrig, dass er sich der weitreichenden Konsequenzen seiner Aktionen nicht bewusst ist. Zwar halten manche die Existenz des Spähtrupps für bedenklich, doch bei einer Sache können sie sich sicher sein: mit Teemo und seiner Überzeugung ist nicht zu spaßen.", "blurb": "<PERSON><PERSON><PERSON> schreckt sogar vor den gefährlichsten und furchterregendsten Problemen nicht zurück. Er erkundet die Welt mit scheinbar grenzenloser Begeisterung und fröhlichem Gemüt. Der Yordle rückt nie von seinen moralischen Prinzipien ab und rühmt sich, stets...", "allytips": ["<PERSON>emos Pilze eigenen sich hervorragend, um Vasallen auszuschalten.", "Platziere deine Pilze an Schlüsselpositionen der Karte, etwa in der Nähe des Drachens oder <PERSON>, um Gegner aufzu<PERSON>cken, die versuchen, sie zu töten."], "enemytips": ["<PERSON><PERSON><PERSON> „Giftschuss“ ist besonders für Spieler unangenehm, die getroffen werden und sich dann zurückziehen. <PERSON><PERSON><PERSON> daher in sicherer Entfernung, bis du selbst angreifen willst.", "<PERSON>s kann nützlich sein, mit der Linse des Orakels (Schmuck) Pilze an Schlüsselpunkten vorzeitig zu zerstören."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 6}, "stats": {"hp": 615, "hpperlevel": 104, "mp": 334, "mpperlevel": 25, "movespeed": 330, "armor": 24, "armorperlevel": 4.95, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.65, "mpregen": 9.6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3, "attackspeedperlevel": 3.38, "attackspeed": 0.69}, "spells": [{"id": "TeemoQ", "name": "Blendpfeil", "description": "Verschleiert die Sicht eines Gegners mit Hilfe eines mächtigen Giftes, das der Zieleinheit Schaden zufügt und für diese Zeit blendet.", "tooltip": "<PERSON>emo feuert einen Pfeil, der das Ziel {{ blindduration }}&nbsp;Sekunden lang <status>blendet</status> und ihm <magicDamage>{{ calculateddamage }}&nbsp;magischen Schaden</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ blindduration }} -> {{ blinddurationNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoQ.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TeemoW", "name": "<PERSON><PERSON><PERSON>", "description": "Teemo saust umher und erhöht passiv sein Lauftempo, bis er von einem Gegner oder Turm getroffen wird. Teemo kann kurzzeitig rennen, um zusätzliches Lauftempo zu erhalten, das nicht durch Angriffe abgebrochen wird.", "tooltip": "<spellPassive>Passiv:</spellPassive> Teemo erhält <speed>{{ passivemovespeedbonus*100 }}&nbsp;% Lauftempo</speed>, sofern er nicht in den letzten {{ passivecooldownondamagetaken }}&nbsp;Sekunden durch einen Champion oder Turm Schaden erlitten hat.<br /><br /><spellActive>Aktiv:</spellActive> Teemo sprintet und erhält {{ activemovespeedbuffduration }}&nbsp;Sekunden lang <speed>{{ activemovespeedbonus*100 }}&nbsp;% Lauftempo</speed>. Dieser Bonus geht nicht verloren, wenn er getroffen wird.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Passives Lauftempo", "Aktives Lauftempo"], "effect": ["{{ passivemovespeedbonus*100.000000 }}&nbsp;% -> {{ passivemovespeedbonusnl*100.000000 }}&nbsp;%", "{{ activemovespeedbonus*100.000000 }}&nbsp;% -> {{ activemovespeedbonusnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TeemoW.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TeemoE", "name": "<PERSON><PERSON><PERSON>", "description": "Teemo vergiftet mit seinen Angriffen das Ziel und verursacht bei Treffern und 4 Sekunden lang jede Sekunde Schaden.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON><PERSON><PERSON> Angriffe vergiften und verursachen zusätzlich <magicDamage>{{ impactcalculateddamage }}&nbsp;magischen <PERSON>haden</magicDamage> plus <magicDamage>{{ totaldotdamage }}&nbsp;magischen Schaden</magicDamage> über {{ poisonduration }}&nbsp;Sekunden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Einschlagschaden", "Schaden pro Sekunde"], "effect": ["{{ impactbasedamage }} -> {{ impactbasedamageNL }}", "{{ tickbasedamage }} -> {{ tickbasedamageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiv", "maxammo": "-1", "range": [680, 680, 680, 680, 680], "rangeBurn": "680", "image": {"full": "TeemoE.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "Passiv"}, {"id": "TeemoR", "name": "Giftfalle", "description": "Teemo fertigt aus einem der Pilze in seinem Rucksack eine explosive, giftige Falle und wirft diese aus. Wenn ein G<PERSON>ner in die Falle tappt, wird eine Giftwolke freigesetzt, welche die Gegner verlangsamt und ihnen wiederholt Schaden zufügt. Wenn Teemo einen Pilz auf einen bereits platzierten Pilz wirft, wird er darauf abprallen und ihm so mehr Reichweite verschaffen.", "tooltip": "Teemo wirft eine Pilzfalle, die explodiert, wenn ein Gegner auf sie tritt. Die Fallen <status>verlangsamen</status> um {{ slowamount }}&nbsp;% und verursachen <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> über {{ debuffduration }}&nbsp;Sekunden. Gegner werden für dieselbe Dauer aufgedeckt.<br /><br />Fallen bleiben {{ mushroomduration }}&nbsp;Minuten lang bestehen und sind getarnt. Ein Pilz, der auf einen anderen Pilz geworfen wird, prallt ab und landet dann an seiner Position. Diese Fähigkeit hat {{ maxammo }}&nbsp;Aufladungen ({{ ammorechargetime }}&nbsp;Sekunden Aufladungszeit).<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Wurfreichweite", "<PERSON><PERSON>", "Maximale Fallenanzahl", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%", "{{ castrange }} -> {{ castrangeNL }}", "{{ maxbouncedistance }} -> {{ maxbouncedistanceNL }}", "{{ maxammo }} -> {{ maxammoNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 3, "cooldown": [0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [75, 55, 35], "costBurn": "75/55/35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "3", "range": [600, 750, 900], "rangeBurn": "600/750/900", "image": {"full": "TeemoR.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Guerillakriegsführung", "description": "<PERSON>n <PERSON><PERSON><PERSON> stillste<PERSON> und für eine kurze Zeit nichts tut, wird er unsichtbar. Im hohen G<PERSON> kann sich Teemo auch bewegen, ohne wieder sichtbar zu werden. Nach Ende der Unsichtbarkeit hat Teemo das Überraschungsmoment auf seiner Seite, wodurch sein Angriffstempo für ein paar Sekunden lang erhöht ist.", "image": {"full": "TeemoPassive.ASU_Teemo.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}