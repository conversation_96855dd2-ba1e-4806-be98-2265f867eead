{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Alistar": {"id": "Alistar", "key": "12", "name": "アリスター", "title": "ミノタウロスの戦士", "image": {"full": "Alistar.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "12000", "num": 0, "name": "default", "chromas": false}, {"id": "12001", "num": 1, "name": "黒毛アリスター", "chromas": false}, {"id": "12002", "num": 2, "name": "金毛アリスター", "chromas": false}, {"id": "12003", "num": 3, "name": "闘牛士アリスター", "chromas": false}, {"id": "12004", "num": 4, "name": "巨大角戦士アリスター", "chromas": false}, {"id": "12005", "num": 5, "name": "拘束不能アリスター", "chromas": false}, {"id": "12006", "num": 6, "name": "地獄の業火アリスター", "chromas": false}, {"id": "12007", "num": 7, "name": "スイーパー アリスター", "chromas": false}, {"id": "12008", "num": 8, "name": "略奪者アリスター", "chromas": false}, {"id": "12009", "num": 9, "name": "SKT T1 アリスター", "chromas": false}, {"id": "12010", "num": 10, "name": "モーモー アリスター", "chromas": true}, {"id": "12019", "num": 19, "name": "ヘクステック アリスター", "chromas": false}, {"id": "12020", "num": 20, "name": "覇者アリスター", "chromas": true}, {"id": "12022", "num": 22, "name": "漆黒の霧氷アリスター", "chromas": true}, {"id": "12029", "num": 29, "name": "月の聖獣アリスター", "chromas": true}], "lore": "屈強な戦士として恐れられるアリスターは自分の部族を滅ぼしたノクサス帝国に復讐を誓っている。彼は奴隷となり、闘士として戦わされていたものの、不屈の意思の強さで理性を維持し、ただの獣に成り下がってしまうことは免れた。かつての主人たちの鎖から解放された今、彼は虐げられた者や不遇の者たちのために、己の角と蹄と怒りを武器にして戦っている。", "blurb": "屈強な戦士として恐れられるアリスターは自分の部族を滅ぼしたノクサス帝国に復讐を誓っている。彼は奴隷となり、闘士として戦わされていたものの、不屈の意思の強さで理性を維持し、ただの獣に成り下がってしまうことは免れた。かつての主人たちの鎖から解放された今、彼は虐げられた者や不遇の者たちのために、己の角と蹄と怒りを武器にして戦っている。", "allytips": ["「圧砕」を命中させた敵ユニットは「頭突き」で狙いやすくなる。", "アリスターにとって移動速度はとても重要。ブーツは慎重に選ぼう。", "サモナースペル「フラッシュ」をうまく活用しよう。敵ユニットの不意をついて「圧砕」と「頭突き」のコンボを食らわせ、味方チャンピオンのいる方へ弾き飛ばすことができる。"], "enemytips": ["アリスターの高い行動妨害能力とダメージ耐性は厄介だ。アリスターの妨害に気をつけながら、防御力の低い火力系チャンピオンに狙いを定めるのが賢明だろう。", "タワーの近くでは「圧砕」と「頭突き」のコンボ攻撃に注意！", "アリスターがアルティメットスキルを発動した時は、彼への攻撃を中止して効果時間が終わるまで待つか、別の対象を狙うのも手だ。"], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 6, "defense": 9, "magic": 5, "difficulty": 7}, "stats": {"hp": 685, "hpperlevel": 120, "mp": 350, "mpperlevel": 40, "movespeed": 330, "armor": 47, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.85, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.75, "attackspeedperlevel": 2.125, "attackspeed": 0.625}, "spells": [{"id": "Pulverize", "name": "圧砕", "description": "地面をたたきつけ、周囲にいる敵ユニットにダメージを与えてノックアップする。", "tooltip": "地面を叩きつけ、敵を{{ knockupduration }}秒間<status>ノックアップ</status>させ、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "Pulverize.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Headbutt", "name": "頭突き", "description": "対象に「頭突き」を食らわせてダメージを与え、ノックバックさせる。", "tooltip": "敵に頭突きして<status>ノック</status><status>バック</status>させ、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [700, 700, 700, 700, 700], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "700", "0.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "Headbutt.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "AlistarE", "name": "踏破", "description": "周囲の敵を踏みつけてユニットをすり抜けるようになる。これでチャンピオンにダメージを与えた場合、スタックを1つ獲得。スタックが最大になるとチャンピオンに対する次の通常攻撃に追加魔法ダメージとスタン効果を付与する。", "tooltip": "地面を踏みつけてゴースト化し、周囲の敵に{{ e3 }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。衝撃波がチャンピオンにダメージを与えるたびに、スタックを1つ獲得する。<br /><br />{{ e5 }}スタックになると、チャンピオンに対する次の通常攻撃が対象を{{ e6 }}秒間<status>スタン</status>させて、<magicDamage>{{ attackbonusdamage }}の魔法ダメージ</magicDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 110, 140, 170, 200], [50, 50, 50, 50, 50], [5, 5, 5, 5, 5], [350, 350, 350, 350, 350], [5, 5, 5, 5, 5], [1, 1, 1, 1, 1], [5, 5, 5, 5, 5], [20, 20, 20, 20, 20], [15, 15, 15, 15, 15], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/110/140/170/200", "50", "5", "350", "5", "1", "5", "20", "15", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AlistarE.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FerociousHowl", "name": "不屈の意志", "description": "荒々しい雄叫びをあげ、自身に付与された行動妨害効果をすべて解除する。効果時間中は自身が受ける物理ダメージと魔法ダメージを軽減する。", "tooltip": "あらゆる<status>行動妨害効果</status>を直ちに除去し、{{ rduration }}秒間受けるダメージを{{ rdamagereduction }}%軽減する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ軽減"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamagereduction }}% -> {{ rdamagereductionNL }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "FerociousHowl.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "戦士の咆哮", "description": "敵チャンピオンをスタンさせるか弾き飛ばした時、または周囲で敵ユニットが倒されると「咆哮」をチャージする。最大までチャージされると自身および近くにいるすべての味方チャンピオンの体力を回復する。", "image": {"full": "Alistar_E.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}