{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"XinZhao": {"id": "XinZhao", "key": "5", "name": "<PERSON><PERSON>", "title": "il siniscalco di Demacia", "image": {"full": "XinZhao.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "5000", "num": 0, "name": "default", "chromas": false}, {"id": "5001", "num": 1, "name": "Xin Zhao Commando", "chromas": false}, {"id": "5002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "5003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "5004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "5005", "num": 5, "name": "Xin Zhao dei Regni in Guerra", "chromas": true}, {"id": "5006", "num": 6, "name": "<PERSON><PERSON>e Se<PERSON>", "chromas": false}, {"id": "5013", "num": 13, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "5020", "num": 20, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "5027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "5036", "num": 36, "name": "<PERSON><PERSON> d'Artificio", "chromas": true}], "lore": "<PERSON><PERSON> <PERSON> è un guerriero coraggioso, fedele alla dinastia regnante dei Lightshield. Un tempo condannato a combattere nelle fosse di Noxus, è sopravvissuto a innumerevoli scontri ma, dopo essere stato liberato dalle forze demaciane, ha messo la sua vita al servizio dei suoi coraggiosi salvatori. Armato della sua lancia tripla, ora <PERSON><PERSON> combatte per il suo regno adottivo, affrontando con coraggio qualunque avversario.", "blurb": "<PERSON><PERSON> <PERSON> è un guerriero coraggioso, fedele alla dinastia regnante dei Lightshield. Un tempo condannato a combattere nelle fosse di Noxus, è sopravvissuto a innumerevoli scontri ma, dopo essere stato liberato dalle forze demaciane, ha messo la sua vita...", "allytips": ["<PERSON><PERSON> <PERSON> è un ottimo iniziatore dei combattimenti. Guida l'avanzata per iniziare un combattimento e usa la tua abilità suprema per infliggere più danni possibile.", "Cerca di posizionarti in modo che il colpo respingente della tua abilità suprema sia più efficace."], "enemytips": ["<PERSON><PERSON> Zhao è un potente iniziatore sia con la sua carica sia con la sua abilità suprema che infligge danni a tutte le unità intorno a lui. Cerca di tenere la tua squadra divisa finché non ha usato la sua abilità suprema.", "<PERSON><PERSON> <PERSON> dipende molto dal suo Triplo colpo dell'artiglio per i danni e i tempi di ricarica, quindi interrompere la combo avrà effetti drammatici."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 2}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 274, "mpperlevel": 55, "movespeed": 345, "armor": 35, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "XinZhaoQ", "name": "Triplo colpo dell'artiglio", "description": "I prossimi 3 attacchi base di Xin Zhao infliggono più danni e il terzo attacco lancia il bersaglio in aria.", "tooltip": "I 3 prossimi attacchi di Xin Zhao infliggono <physicalDamage>{{ bonusdamage }} danni fisici</physicalDamage> aggiuntivi e riducono la ricarica delle altre abilità di 1 secondo. <PERSON><PERSON><PERSON>, il terzo attacco <status>lancia in aria</status> il bersaglio per {{ e2 }} secondi.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> bonus", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [20, 35, 50, 65, 80], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/35/50/65/80", "0.75", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "XinZhaoQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XinZhaoW", "name": "Il vento diventa fulmine", "description": "<PERSON><PERSON> colpisce davanti a sé con la sua lancia, per poi fare un affondo in avanti, rallentando i nemici coinvolti e march<PERSON>li come Sfidati.", "tooltip": "<PERSON><PERSON> colpisce davanti a sé, infliggendo <physicalDamage>{{ slashdamage }}</physicalDamage> danni fisici, per poi eseguire un affondo in avanti che infligge <physicalDamage>{{ thrustdamage }}</physicalDamage>. I nemici colpiti dall'affondo sono <status>rallentati</status> di un {{ e6 }}% per {{ e7 }} secondi. <br /><br />Campioni e mostri grandi colpiti sono marchiati come <keywordMajor>Sfidati</keywordMajor> per {{ markduration }} secondi e vengono rivelati a meno che non siano <keywordStealth>invisibili</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> at<PERSON> frontale", "<PERSON><PERSON> a<PERSON>o", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ slashbasedamage }} -> {{ slashbasedamageNL }}", "{{ thrustbasedamage }} -> {{ thrustbasedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [140, 140, 140, 140, 140], [200, 200, 200, 200, 200], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0.5", "0.5", "0.5", "50", "1.5", "140", "200", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "XinZhaoW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XinZhaoE", "name": "Carica dell'audace", "description": "Xin Zhao carica un nemico e ottiene velocità d'attacco aumentata, infliggendo danni e rallentando brevemente tutti i nemici nell'area. Carica dell'audace ha una gittata maggiore contro i nemici in Sfida.", "tooltip": "Xin <PERSON> carica un avversario, infliggendo <magicDamage>{{ chargedamage }} danni magici</magicDamage> ai nemici nelle vicinanze e <status>rallentandoli</status> del {{ baseslowamount }}% per {{ e6 }} secondi.<br /><br /><PERSON><PERSON><PERSON>, <PERSON><PERSON> <attackSpeed>{{ e3 }}% velocità d'attacco</attackSpeed> per {{ e4 }} secondi.<br /><br />La gittata di <spellName>Carica dell'audace</spellName> è aumentata contro i nemici in <keywordMajor>Sfida</keywordMajor>. <br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità d'attacco"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect3amount*100.000000 }}% -> {{ effect3amountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [-0.3, -0.3, -0.3, -0.3, -0.3], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [250, 250, 250, 250, 250], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "-0.3", "40/45/50/55/60", "5", "250", "0.5", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "XinZhaoE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "XinZhaoR", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> <PERSON> sfida un nemico che ha danneggiato di recente. <PERSON><PERSON> <PERSON> infligge danni ai nemici nelle vicinanze in base alla loro salute attuale e respinge i personaggi non sfidati, non subendo danni inflitti dai campioni al di fuori del cerchio che si crea.", "tooltip": "L'ultimo campione a cui Xin Zhao ha inflitto danni con un attacco o con <spellName>Carica dell'audace</spellName> è in <keywordMajor>Sfida</keywordMajor> per {{ markduration }} secondi.<br /><br /><PERSON><PERSON> compie una spazzata intorno a sé che infligge <physicalDamage>{{ totaldamage }} più il {{ percentcurrenthealthdamage*100 }}% della salute attuale dei bersagli in danni fisici</physicalDamage> e <status>respinge</status> tutti i nemici <keywordMajor>non in Sfida</keywordMajor>. <br /> <br />Dopo aver lanciato quest'abilità, <PERSON>n Zhao diventa immune ai danni dei nemici al di fuori della gittata della spazzata per {{ missiledefensebaseduration }} secondi. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "XinZhaoR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Determinazione", "description": "Ogni terzo attacco infligge danni bonus e cura Xin <PERSON>.", "image": {"full": "XinZhaoP.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}