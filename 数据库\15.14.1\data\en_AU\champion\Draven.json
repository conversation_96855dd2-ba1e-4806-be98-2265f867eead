{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Draven": {"id": "Draven", "key": "119", "name": "Draven", "title": "the Glorious Executioner", "image": {"full": "Draven.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "119000", "num": 0, "name": "default", "chromas": false}, {"id": "119001", "num": 1, "name": "Soul Reaver Draven", "chromas": false}, {"id": "119002", "num": 2, "name": "Gladiator Draven", "chromas": false}, {"id": "119003", "num": 3, "name": "Primetime Draven", "chromas": true}, {"id": "119004", "num": 4, "name": "Pool Party Draven", "chromas": false}, {"id": "119005", "num": 5, "name": "Beast Hunter Draven", "chromas": false}, {"id": "119006", "num": 6, "name": "Draven Draven", "chromas": false}, {"id": "119012", "num": 12, "name": "Santa Draven", "chromas": false}, {"id": "119013", "num": 13, "name": "Mecha Kingdoms Draven", "chromas": true}, {"id": "119020", "num": 20, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "119029", "num": 29, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "119039", "num": 39, "name": "Fright Night Draven", "chromas": true}, {"id": "119048", "num": 48, "name": "La Ilusión Draven", "chromas": true}, {"id": "119058", "num": 58, "name": "Grand Reckoning Draven", "chromas": true}], "lore": "In Noxus, warriors known as <PERSON><PERSON>oners face one another in arenas where blood is spilled and strength tested—but none has ever been as celebrated as <PERSON><PERSON>. A former soldier, he found that the crowds uniquely appreciated his flair for the dramatic, and his unparalleled skill with his spinning axes. Addicted to the spectacle of his own brash perfection, <PERSON><PERSON> has sworn to defeat whomever he must to ensure that his name is chanted throughout the empire forever more.", "blurb": "In Noxus, warriors known as <PERSON><PERSON>one<PERSON> face one another in arenas where blood is spilled and strength tested—but none has ever been as celebrated as <PERSON><PERSON>. A former soldier, he found that the crowds uniquely appreciated his flair for the dramatic, and...", "allytips": ["If <PERSON><PERSON> doesn't move, <PERSON><PERSON> will fall near his present location. It will fall directly on him, or just to the right or left.", "If <PERSON><PERSON> does move after attacking, Spinning Axe will lead him in the direction of his movement. Use this to control where the Spinning Axe will go."], "enemytips": ["Launch skillshots toward the landing position of Draven's Spinning Axes.", "Disrupt <PERSON><PERSON> with the goal of making him drop his axes. If you do, his power drops dramatically."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 8}, "stats": {"hp": 675, "hpperlevel": 104, "mp": 361, "mpperlevel": 39, "movespeed": 330, "armor": 29, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.7, "mpregen": 8.05, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.6, "attackspeedperlevel": 2.7, "attackspeed": 0.679}, "spells": [{"id": "DravenSpinning", "name": "Spinning Axe", "description": "<PERSON><PERSON>'s next attack will deal bonus physical damage. This axe will ricochet off the target high up into the air. If <PERSON><PERSON> catches it, he automatically readies another Spinning Axe. <PERSON><PERSON> can have two Spinning Axes at once.", "tooltip": "<PERSON><PERSON> readies a <keywordMajor>Spinning Axe</keywordMajor>, causing his next Attack to deal an additional <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> and ricochet into the air. If <PERSON><PERSON> catches it, he readies another <keywordMajor>Spinning Axe</keywordMajor>.<br /><br />Dr<PERSON> can hold two <keywordMajor>Spinning Axes</keywordMajor> at once.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Bonus AD Percentage", "Cooldown"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [100, 100, 100, 100, 100], [75, 85, 95, 105, 115], [30, 35, 40, 45, 50], [5.75, 5.75, 5.75, 5.75, 5.75], [40, 45, 50, 55, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100", "75/85/95/105/115", "30/35/40/45/50", "5.75", "40/45/50/55/60", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DravenSpinning.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenFury", "name": "Blood Rush", "description": "Draven gains increased Move Speed and Attack Speed. The Move Speed bonus decreases rapidly over its duration. Catching a Spinning Axe will refresh the cooldown of Blood Rush.", "tooltip": "<PERSON><PERSON> becomes Ghost<PERSON>, gains <speed>{{ e2 }}% Move Speed</speed> decaying over {{ e3 }} seconds and <attackSpeed>{{ e4 }}% Attack Speed</attackSpeed> for {{ e5 }} seconds.<br /><br />When <PERSON><PERSON> catches a <keywordMajor>Spinning Axe</keywordMajor>, this Ability's cooldown is refreshed.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Speed", "@AbilityResourceName@ Cost", "Move Speed"], "effect": ["{{ e4 }}% -> {{ e4NL }}%", "{{ cost }} -> {{ costNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [4, 5, 6, 7, 8], [50, 55, 60, 65, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [-0.062, -0.069, -0.075, -0.081, -0.087], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/5/6/7/8", "50/55/60/65/70", "1.5", "20/25/30/35/40", "3", "-0.062/-0.069/-0.075/-0.081/-0.087", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "DravenFury.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenDoubleShot", "name": "Stand Aside", "description": "<PERSON><PERSON> throws his axes, dealing physical damage to targets hit and knocking them aside. Targets hit are slowed.", "tooltip": "Draven chucks a sideways axe that deals <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>, <status>Knocks Back</status>, and <status>Slows</status> by {{ e2 }}% for {{ e3 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Cooldown"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [20, 25, 30, 35, 40], [2, 2, 2, 2, 2], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/110/145/180/215", "20/25/30/35/40", "2", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "DravenDoubleShot.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenRCast", "name": "Whirling Death", "description": "Draven hurls two massive axes to deal physical damage to each unit struck. Whirling Death slowly reverses direction and returns to <PERSON><PERSON> after striking an enemy champion. Dr<PERSON> may also activate this ability while the axes are in flight to cause it to return early. Deals less damage for each unit hit and resets when the axes reverse direction. Executes enemies who have less health than Dr<PERSON>'s number of Adoration stacks.", "tooltip": "Draven hurls two massive axes that deal <physicalDamage>{{ rcalculateddamage }} physical damage</physicalDamage>. Upon hitting a champion or <recast>Recasting</recast>, they reverse direction and return to Draven. The axes deal {{ rdamagereductionperhit*100 }}% less damage for each enemy hit, down to a minimum of {{ rmindamagepercent }}%.<br /><br />If <keywordMajor>Whirling Death</keywordMajor> would leave an enemy champion with less health than {{ rpassivestackscoefficient*100 }}% of Dr<PERSON>'s current <keywordMajor>League of Draven</keywordMajor> stacks ({{ rpassivetruedamage }}), he will execute them.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "Bonus AD Ratio"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rcoefficient*100.000000 }}% -> {{ rcoefficientnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20000, 20000, 20000], "rangeBurn": "20000", "image": {"full": "DravenRCast.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "League of Draven", "description": "Draven gains his fans' Adoration when he catches a Spinning Axe or kills a minion, monster, or tower. Killing enemy champions grants Dr<PERSON> bonus gold based on how much Adoration he has.", "image": {"full": "Draven_passive.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}