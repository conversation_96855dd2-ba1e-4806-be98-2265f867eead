{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vayne": {"id": "<PERSON><PERSON>", "key": "67", "name": "<PERSON><PERSON>", "title": "la cacciatrice notturna", "image": {"full": "Vayne.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "67000", "num": 0, "name": "default", "chromas": false}, {"id": "67001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "67002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "67003", "num": 3, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "67004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "67005", "num": 5, "name": "Vayne SKT T1", "chromas": false}, {"id": "67006", "num": 6, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "67010", "num": 10, "name": "<PERSON>ayne Ladra d'anime", "chromas": true}, {"id": "67011", "num": 11, "name": "PROGETTO: <PERSON><PERSON>", "chromas": false}, {"id": "67012", "num": 12, "name": "<PERSON>ayne <PERSON> d'Artificio", "chromas": true}, {"id": "67013", "num": 13, "name": "<PERSON><PERSON> d'Artificio (edizione prestigio)", "chromas": false}, {"id": "67014", "num": 14, "name": "<PERSON><PERSON> spirituale", "chromas": true}, {"id": "67015", "num": 15, "name": "Vayne FPX", "chromas": true}, {"id": "67025", "num": 25, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "67032", "num": 32, "name": "<PERSON><PERSON> Battaglia", "chromas": true}, {"id": "67033", "num": 33, "name": "<PERSON><PERSON> d'Artificio (edizione prestigio 2022)", "chromas": false}, {"id": "67044", "num": 44, "name": "Vayne Portatrice dell'Alba", "chromas": true}, {"id": "67055", "num": 55, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "67064", "num": 64, "name": "<PERSON><PERSON>a Trascesa", "chromas": true}], "lore": "<PERSON><PERSON>ayne è una letale e spietata cacciatrice di mostri demaciana, che ha giurato di dedicare la sua vita alla ricerca del demone che ha assassinato la sua famiglia e vendicarsi. Armata con una balestra da polso e di un cuore colmo di vendetta, è felice solo quando uccide i seguaci delle arti oscure e le loro creazioni, colpendoli dalle ombre con una raffica di dardi d'argento.", "blurb": "<PERSON><PERSON> è una letale e spietata cacciatrice di mostri demaciana, che ha giurato di dedicare la sua vita alla ricerca del demone che ha assassinato la sua famiglia e vendicarsi. Armata con una balestra da polso e di un cuore colmo di vendetta, è...", "allytips": ["Capriola ha molti usi, ma non ti permette di oltrepassare i muri.", "Condanna ti permette sia di inchiodare un bersaglio al muro per assicurarti un'uccisione, sia di scappare da un inseguitore.", "Non ingaggiare per primo in uno scontro tra squadre. <PERSON><PERSON> che siano i tuoi compagni a ingaggiare."], "enemytips": ["Vayne è fragile: aumenta la pressione su di lei e sarà obbligata a giocare più cauta.", "Non dare l'opportunità a Vayne di metterti con le spalle al muro."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 1, "magic": 1, "difficulty": 8}, "stats": {"hp": 550, "hpperlevel": 103, "mp": 232, "mpperlevel": 35, "movespeed": 330, "armor": 23, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.35, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "VayneTumble", "name": "Capriola", "description": "Vayne fa una capriola, cercando di piazzare il suo prossimo colpo con precisione. Il suo prossimo attacco infligge danni bonus.", "tooltip": "Vayne rotola per una breve distanza e infligge <physicalDamage>{{ adratiobonus }} danni fisici</physicalDamage> in più con il suo prossimo attacco.<br /><br /><rules>Questa abilità attiva gli effetti sull'incantesimo quando infligge danni.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Percentuale rapporto attacco fisico"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ totaladratio*100.000000 }}% -> {{ totaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VayneTumble.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>ayneSil<PERSON><PERSON><PERSON><PERSON>", "name": "Dardi d'a<PERSON>o", "description": "Vayne usa come punte dei suoi dardi un raro metallo, tossico per tutte le creature malvagie. Il terzo attacco o abilità consecutivi contro lo stesso bersaglio infliggono una percentuale della salute massima del bersaglio come danni puri bonus.", "tooltip": "<spellPassive>Passiva</spellPassive>: ogni terzo attacco o abilità consecutivi contro un nemico infliggono un ulteriore <trueDamage>{{ totaldamage }} della salute massima del bersaglio in danni puri</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Percentuale danni puri", "<PERSON><PERSON> minimi"], "effect": ["{{ maxhealthratio*100.000000 }}% -> {{ maxhealthrationl*100.000000 }}%", "{{ damagefloor }} -> {{ damagefloorNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiva", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VayneSilveredBolts.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Passiva"}, {"id": "VayneCondemn", "name": "<PERSON><PERSON><PERSON>", "description": "Vayne estrae la sua balestra pesante dalla schiena e spara un grosso dardo al suo bersaglio, infliggendo danni e respingendolo. Se collide contro un muro, il suo avversario rimane impalato, subisce danni bonus ed è stordito.", "tooltip": "Vayne spara un dardo che <status>respinge</status> e infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>. Se il bersaglio colpisce il terreno, subisce <physicalDamage>{{ empowereddamagett }} danni fisici bonus</physicalDamage> e viene <status>stordito</status> per {{ stunduration }} secondi. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "VayneCondemn.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VayneInquisition", "name": "Ora finale", "description": "Preparand<PERSON><PERSON> per un confronto epico, <PERSON><PERSON> guadagna attacco fisico aumentato, Invisibilità durante Capriola, rica<PERSON> ridotta per Capriola e una velocità di movimento bonus maggiore da Cacciatrice notturna.", "tooltip": "Vayne ottiene <physicalDamage>{{ bonusattackdamage }} attacco fisico</physicalDamage> per {{ baseduration }} secondi, che aumentano di {{ durationtoadd }} secondi ogni volta che un campione danneggiato da Vayne muore entro {{ damagedmarkerduration }} secondi. Inoltre, per la durata:<li><spellName>Cacciatrice notturna</spellName> conferisce invece <speed>{{ movementspeed }} velocità di movimento</speed>.<li>La ricarica di <spellName>Capriola</spellName> è ridotta del {{ tumblecdreduction }}% e conferisce <keywordStealth>Invisibilità</keywordStealth> per {{ tumblestealthduration }} secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Attacco fisico bonus", "Riduzione ricarica Capriola"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseduration }} -> {{ basedurationNL }}", "{{ bonusattackdamage }} -> {{ bonusattackdamageNL }}", "{{ tumblecdreduction }}% -> {{ tumblecdreductionNL }}%"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "VayneInquisition.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Cacciatrice notturna", "description": "Vayne dà la caccia ai malfattori in modo implacabile e guadagna velocità di movimento mentre si muove verso i campioni nemici vicini.", "image": {"full": "Vayne_NightHunter.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}