{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Graves": {"id": "<PERSON>", "key": "104", "name": "<PERSON>", "title": "il fuorilegge", "image": {"full": "Graves.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "104000", "num": 0, "name": "default", "chromas": false}, {"id": "104001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "104002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "104003", "num": 3, "name": "Graves Città del crimine", "chromas": false}, {"id": "104004", "num": 4, "name": "Riot Graves", "chromas": false}, {"id": "104005", "num": 5, "name": "Graves Festa in Piscina", "chromas": true}, {"id": "104006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "104007", "num": 7, "name": "<PERSON> Nevicata", "chromas": true}, {"id": "104014", "num": 14, "name": "<PERSON>", "chromas": true}, {"id": "104018", "num": 18, "name": "<PERSON>", "chromas": true}, {"id": "104025", "num": 25, "name": "<PERSON> Battaglia", "chromas": true}, {"id": "104035", "num": 35, "name": "<PERSON>", "chromas": true}, {"id": "104042", "num": 42, "name": "Graves EDG", "chromas": true}, {"id": "104045", "num": 45, "name": "Graves di Porcellana", "chromas": false}], "lore": "<PERSON> è un noto mercenario, ladro e giocatore d'azzardo, ricercato in ogni reame, città e impero in cui è stato. Nonostante il temperamento irascibile, possiede un rigido codice d'onore criminale, spesso affidato alle cure di <PERSON>, il suo fucile a pompa a due canne. Negli ultimi anni ha riformato una travagliata coppia con Twisted Fate, e insieme hanno nuovamente fatto fortuna nel ventre criminale di Bilgewater.", "blurb": "<PERSON> è un noto mercenario, ladro e giocatore d'azzardo, ricercato in ogni reame, città e impero in cui è stato. Nonostante il temperamento irascibile, possiede un rigido codice d'onore criminale, spesso affidato alle cure di Destino, il suo...", "allytips": ["Cortina fumogena può essere usata sia per fuggire sia per prepararsi a un'uccisione.", "Usa Attacco impetuoso per arrivare a corto raggio e attacca il nemico a bruciapelo, in modo da infliggere danni ingenti."], "enemytips": ["<PERSON> infligge per lo più danni fisici, quindi l'armatura è una buona contromossa.", "Uscire dall'area della Cortina fumogena rimuove immediatamente gli effetti negativi."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 325, "mpperlevel": 40, "movespeed": 340, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 425, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.475}, "spells": [{"id": "GravesQLineSpell", "name": "Fine della corsa", "description": "Graves spara un proiettile esplosivo che scoppia dopo 1 secondo, o dopo aver colpito il terreno.", "tooltip": "Graves spara un proiettile carico di polvere da sparo che infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>. Dopo 1 secondo o dopo aver colpito un muro, il proiettile esplode, infliggendo <physicalDamage>{{ totaldetonationdamage }} danni fisici</physicalDamage> lungo la sua traiettoria e ai nemici vicino al punto di impatto.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON>ni detonazione", "Rapporto attacco fisico detonazione", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedetonationdamage }} -> {{ basedetonationdamageNL }}", "{{ baddetonationratio*100.000000 }}% -> {{ baddetonationrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11.25, 9.5, 7.75, 6], "cooldownBurn": "13/11.25/9.5/7.75/6", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "GravesQLineSpell.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesSmokeGrenade", "name": "<PERSON>rtina fumogena", "description": "Graves spara un colpo fumogeno, creando una nuvola di fumo che riduce il raggio visivo. I nemici colpiti dall'impatto iniziale subiscono danni magici e hanno la velocità di movimento ridotta per un breve periodo.", "tooltip": "Graves crea una nuvola di fumo nero che dura 4 secondi, <status>rallentando</status> i nemici al suo interno del {{ e2 }}% e bloccando la loro visuale al di fuori dell'area. L'impatto iniziale infligge <magicDamage>{{ impactdamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 24, 22, 20, 18], "cooldownBurn": "26/24/22/20/18", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [60, 110, 160, 210, 260], [50, 50, 50, 50, 50], [200, 200, 200, 200, 200], [4, 4, 4, 4, 4], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/110/160/210/260", "50", "200", "4", "0.5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "GravesSmokeGrenade.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesMove", "name": "At<PERSON><PERSON> impetuoso", "description": "<PERSON> scatta in avanti, ottenendo un bonus all'armatura per alcuni secondi. <PERSON> <PERSON> scatta verso un campione nemico, ottiene due cariche di Autentica determinazione. Colpire i nemici con attacchi base riduce il tempo di ricarica dell'abilità e ripristina la resistenza.", "tooltip": "Graves scatta e ricarica una <keywordMajor>Cartuccia</keywordMajor> nel suo fucile. Ottiene anche una carica per 4 secondi (massimo {{ e0 }} cariche) o due cariche se scatta verso un campione nemico. Le cariche gli conferiscono <scaleArmor>{{ e5 }} armatura</scaleArmor>. Quando infligge danni a un nemico non minion, le cariche si azzerano.<br /><br />Ogni proiettile degli attacchi di Graves andato a segno riduce il tempo di ricarica di questa abilità di {{ e4 }} secondi.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Armatura", "Ricarica"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [30, 40, 50, 60, 70], [4, 4, 4, 4, 4], [20, 25, 30, 35, 40], [0.5, 0.5, 0.5, 0.5, 0.5], [4, 7, 10, 13, 16], [750, 750, 750, 750, 750], [375, 375, 375, 375, 375], [275, 275, 275, 275, 275], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8]], "effectBurn": [null, "30/40/50/60/70", "4", "20/25/30/35/40", "0.5", "4/7/10/13/16", "750", "375", "275", "60", "8"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [425, 425, 425, 425, 425], "rangeBurn": "425", "image": {"full": "GravesMove.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GravesChargeShot", "name": "<PERSON><PERSON> collaterali", "description": "Graves lancia un colpo esplosivo che infligge danni ingenti al primo campione che colpisce. Dopo aver colpito un campione o aver raggiunto la fine della portata, il colpo esplode causando danni in un'area conica.", "tooltip": "Graves spara una cartuccia esplosiva, saltando all'indietro per il contraccolpo. La cartuccia infligge <physicalDamage>{{ damage }} danni fisici</physicalDamage> al primo nemico colpito. Dopo aver colpito un campione nemico o aver raggiunto la gittata massima, la cartuccia esplode nell'area, infliggendo <physicalDamage>{{ falloffdamage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danno principale", "<PERSON><PERSON> cono", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rfalloffdamage }} -> {{ rfalloffdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GravesChargeShot.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> i<PERSON>", "description": "Il fucile di Graves ha doti uniche. Va ricaricato quando è a corto di munizioni. Gli attacchi sparano 4 proiettili, che non possono passare attraverso le unità. I non-campioni colpiti da più proiettili vengono spinti all'indietro.", "image": {"full": "GravesTrueGrit.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}