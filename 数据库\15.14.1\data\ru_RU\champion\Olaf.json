{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Olaf": {"id": "<PERSON>", "key": "2", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "Берсерк", "image": {"full": "Olaf.png", "sprite": "champion3.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "2000", "num": 0, "name": "default", "chromas": false}, {"id": "2001", "num": 1, "name": "Отрекшийся Олаф", "chromas": false}, {"id": "2002", "num": 2, "name": "Ледниковый Олаф", "chromas": false}, {"id": "2003", "num": 3, "name": "Бролаф", "chromas": true}, {"id": "2004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>, барабанщик Pentakill", "chromas": false}, {"id": "2005", "num": 5, "name": "Мар<PERSON><PERSON><PERSON><PERSON> Олаф", "chromas": false}, {"id": "2006", "num": 6, "name": "Мясник Олаф", "chromas": false}, {"id": "2015", "num": 15, "name": "SKT T1 Олаф", "chromas": false}, {"id": "2016", "num": 16, "name": "Убийца драконов Олаф", "chromas": true}, {"id": "2025", "num": 25, "name": "Страж света Олаф", "chromas": true}, {"id": "2035", "num": 35, "name": "Олаф из Pentakill ''Lost Chapter''", "chromas": true}, {"id": "2044", "num": 44, "name": "Инфернальный Олаф", "chromas": true}], "lore": "Неудержимый разрушитель, вооруженный топорами Олаф хочет лишь одного – умереть в славном бою. Однажды он получил пророчество, которое сулило ему мирную смерть. Среди жителей Локфара – сурового фрельйордского полуострова, откуда он был родом, – такая кончина считалась бесславной; так заканчивали жизнь лишь трусы. Ищущий смерти и подпитываемый яростью, он бродил по земле, убивая одного за другим великих воинов и легендарных чудовищ, в поисках достойного противника, который сможет его остановить. Сейчас жестокий воин Когтя Зимы ждет грядущих войн, чтобы там и закончить свою жизнь.", "blurb": "Неудержимый разрушитель, вооруженный топорами Олаф хочет лишь одного – умереть в славном бою. Однажды он получил пророчество, которое сулило ему мирную смерть. Среди жителей Локфара – сурового фрельйордского полуострова, откуда он был родом, – такая...", "allytips": ["Ярость берсерка, Зверские удары и Рагнарёк делают Олафа неожиданно сильным при низком запасе здоровья.", "Дополнительное восстановление здоровья, которое дают Зверские удары, увеличивает вампиризм из всех источников и усиливает эффекты лечения, наложенные союзниками."], "enemytips": ["Олаф становится опаснее, когда у него мало здоровья. Приберегите ваши ослабляющие умения, чтобы добить его.", "Не давая Олафу подобрать свой топор, вы мешаете ему изматывать вас при игре на линии.", "Во время Рагнарёка защита Олафа ослаблена, несмотря на то что он невосприимчив к ослаблениям. Если вы не можете сбежать от Олафа, когда он использует Рагнарёк, попытайтесь вместе с союзниками сфокусировать на нем урон."], "tags": ["Fighter", "Tank"], "partype": "Мана", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 119, "mp": 316, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.7, "attackspeedperlevel": 2.7, "attackspeed": 0.694}, "spells": [{"id": "OlafAxeThrowCast", "name": "Мощный бросок", "description": "Олаф бросает топор в указанную точку, нанося пораженным врагам урон, уменьшая их броню и замедляя. Олаф может подобрать топор после броска, сбросив перезарядку умения.", "tooltip": "Олаф бросает топор, нанося целям <physicalDamage>{{ totaldamage }} физического урона</physicalDamage>, а также <status>замедляя</status> их на {{ slowamount*100 }}% на время до {{ e3 }} сек. (в зависимости от расстояния полета). <scaleArmor>Броня</scaleArmor> пораженных чемпионов уменьшается на {{ shredamount*100 }}% на {{ debuffduration }} сек.<br /><br />Если Олаф подбирает топор, перезарядка этого умения сокращается до {{ tooltipcdrefund }} сек. Если прошло {{ tooltipcdrefund }} сек., перезарядка сбрасывается полностью.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Замедление", "Урон монстрам", "Затраты маны"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ monsterdamage }} -> {{ monsterdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [80, 125, 170, 215, 260], [30, 30, 30, 30, 30], [2.5, 2.5, 2.5, 2.5, 2.5], [1.5, 1.5, 1.5, 1.5, 1.5], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/125/170/215/260", "30", "2.5", "1.5", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "OlafAxeThrowCast.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "OlafFrenziedStrikes", "name": "Превозмогание", "description": "Скорость атаки Олафа увеличена, и он получает щит.", "tooltip": "Олаф увеличивает <attackSpeed>скорость атаки на {{ attackspeed*100 }}%</attackSpeed> на {{ duration }} сек., а также получает <shield>щит прочностью {{ baseshield }} плюс {{ shieldpercmissinghp*100 }}% от недостающего здоровья (максимальная прочность: {{ maxshieldcalc }}, когда уровень здоровья ниже {{ thresholdformax*100 }}%)</shield> на {{ shieldduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Скорость атаки", "Базовая прочность щита", "Перезарядка"], "effect": ["{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "OlafFrenziedStrikes.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "OlafRecklessStrike", "name": "Безрассудный замах", "description": "Олаф атакует с такой силой, что наносит чистый урон как своей цели, так и самому себе, однако восстанавливает потраченное здоровье, если убьет цель.", "tooltip": "Олаф яростно взмахивает топорами, нанося цели <trueDamage>{{ totaldamage }} чистого урона</trueDamage>. Если враг погибает, затраты на это умение компенсируются.<br /><br />Автоатаки сокращают перезарядку этого умения на 1 сек. (или на 2 сек., если цель – монстр).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "30", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "здоровья", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "OlafRecklessStrike.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "здоровья"}, {"id": "Olaf<PERSON><PERSON><PERSON><PERSON>", "name": "Раг<PERSON><PERSON><PERSON>к", "description": "Броня и сопротивление магии Олафа увеличены. Применив это умение, он становится невосприимчивым к нейтрализующим эффектам до тех пор, пока продолжает атаковать.", "tooltip": "<spellPassive>Пассивно:</spellPassive>  Олаф получает <scaleArmor>{{ resists }} к броне</scaleArmor> и <scaleMR>{{ resists }} к сопротивлению магии</scaleMR>.<br /><br /><spellActive>Активно:</spellActive> Олаф избавляется ото всех <status>обездвиживающих</status> и <status>нейтрализующих</status> эффектов и становится невосприимчивым к ним на {{ duration }} сек. Пока активно, <scaleAD>сила атаки</scaleAD> Олафа увеличена на {{ ad }}. Автоатаки и <spellName>Безрассудные замахи</spellName>, поразившие чемпионов, продлевают действие умения на {{ durationextension }} сек.<br /><br />Кроме того, Олаф увеличивает свою <speed>скорость передвижения на {{ haste*100 }}%</speed> при движении к вражеским чемпионам на {{ hasteduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Броня и сопротивление магии", "Сила атаки", "Скорость передвижения", "Перезарядка"], "effect": ["{{ resists }} -> {{ resistsNL }}", "{{ flatad }} -> {{ flatadNL }}", "{{ haste*100.000000 }} -> {{ hastenl*100.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "OlafRagnarok.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Ярость берсерка", "description": "Олаф увеличивает свою скорость атаки и вампиризм в зависимости от недостающего здоровья.", "image": {"full": "Olaf_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}