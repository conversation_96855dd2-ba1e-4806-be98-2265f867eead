{"type": "others", "version": "15.14.1", "set": "15", "language": "zh_CN", "count": 32, "data": {"TFT15_Item_ProtectorEmblemItem": {"apiName": "TFT15_Item_ProtectorEmblemItem", "name": "圣盾使纹章", "desc": "携带者获得【圣盾使】羁绊。当携带者身上的一个护盾被打破时，治疗携带者和相距最近的那个友军，数额相当于%的护盾初始值。", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/Traits/Spatula/Set15/TFT15_Emblem_Protector.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"HealPercent": 20.0, "Health": 150.0}, "composition": [], "from": null}, "TFT15_Item_SniperEmblemItem": {"apiName": "TFT15_Item_SniperEmblemItem", "name": "狙神纹章", "desc": "携带者获得【狙神】羁绊和距离。", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/Traits/Spatula/Set15/TFT15_Emblem_Sniper.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Health": 150.0, "Range": 2}, "composition": [], "from": null}, "TFT15_CrystalRose_Pass": {"apiName": "TFT15_CrystalRose_Pass", "name": "双倍投入", "desc": "提升接下来4场玩家对战的【水晶能量】获取。但如果你赢了，就会失去你的一大部分【水晶能量】并即刻提现。", "icon": "ASSETS/UX/TraitIcons/Trait_Icon_15_GemForce.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_CrystalRose_Cashout": {"apiName": "TFT15_CrystalRose_Cashout", "name": "打开箱子", "desc": null, "icon": "ASSETS/Maps/TFT/Icons/Augments/ChoiceUI/ADMIN_Armorery_Icon.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_HiddenTech_FlameAffinity": {"apiName": "TFT15_HiddenTech_FlameAffinity", "name": "烈焰亲和", "desc": "受到重伤的友军获得% %i:scaleDA%。重伤：受到的治疗效果降低50%", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_FlameAffinity.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Health": 250.0, "WoundDamageAmp": 0.20000000298023224}, "composition": [], "from": null}, "TFT15_HiddenTech_DormantProphecy": {"apiName": "TFT15_HiddenTech_DormantProphecy", "name": "沉睡预兆", "desc": "战斗开始时：3星的持有者将会升星。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_DormantProphecy.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_HiddenTech_SoulOfASentinel": {"apiName": "TFT15_HiddenTech_SoulOfASentinel", "name": "TFT15_HiddenTech_SoulOfASentinel_Name", "desc": "TFT15_HiddenTech_SoulOfASentinel_Desc", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_SoulOfASentinel.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Armor": 20.0, "Health": 250, "MagicResist": 20.0}, "composition": [], "from": null}, "TFT15_HiddenTech_FireStorm": {"apiName": "TFT15_HiddenTech_FireStorm", "name": "炎暴", "desc": "战斗开始时：重伤所有敌人秒。重伤：受到的治疗效果降低50%", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_FireStorm.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Duration": 10.0, "Health": 250.0}, "composition": [], "from": null}, "TFT15_HiddenTech_StoneSkin": {"apiName": "TFT15_HiddenTech_StoneSkin", "name": "金刚不坏", "desc": "获得护甲。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_StoneSkin.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Armor": 150.0}, "composition": [], "from": null}, "TFT15_HiddenTech_LimitBreak": {"apiName": "TFT15_HiddenTech_LimitBreak", "name": "破敌禁法", "desc": "获得法术加成和%物理加成。生命值低于%时，加成变为3倍。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_LimitBreak.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"AbilityPower": 20.0, "AttackDamage": 20.0, "HealthThreshold": 50.0, "Multiplier": 3.0}, "composition": [], "from": null}, "TFT15_HiddenTech_ShatterSweep": {"apiName": "TFT15_HiddenTech_ShatterSweep", "name": "粉碎横扫", "desc": "在后排开始战斗的友军在对敌人造成物理伤害时击碎其%护甲，持续秒。护甲击碎：降低护甲值", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_ShatterSweep.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"ArmorReduction": 0.20000000298023224, "Duration": 5.0, "Health": 250, "NumRow": 2}, "composition": [], "from": null}, "TFT15_HiddenTech_UnyieldingCovenant": {"apiName": "TFT15_HiddenTech_UnyieldingCovenant", "name": "不屈盟约", "desc": "在至少有名友军存活时(不包括持有者)，该弈子进入不可被选取状态。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_UnyieldingConvenant.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"NumAlliesAlive": 3}, "composition": [], "from": null}, "TFT15_HiddenTech_ArcaneRend": {"apiName": "TFT15_HiddenTech_ArcaneRend", "name": "奥术撕裂", "desc": "在后排开始战斗的友军在对敌人造成魔法伤害时，同时击碎目标%魔抗，持续秒。魔抗击碎：降低魔抗值", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_ArcaneRend.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Duration": 5.0, "Health": 250, "MRReduction": 0.20000000298023224, "NumRow": 2}, "composition": [], "from": null}, "TFT15_HiddenTech_SoulOfAStarGuardian": {"apiName": "TFT15_HiddenTech_SoulOfAStarGuardian", "name": "TFT15_HiddenTech_SoulOfAStarGuardian_Name", "desc": "TFT15_HiddenTech_SoulOfAStarGuardian_Desc", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_SoulOfAStarGuardian.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Health": 250, "{1543aa48}": 0.15000000596046448}, "composition": [], "from": null}, "TFT15_HiddenTech_FusionHex": {"apiName": "TFT15_HiddenTech_FusionHex", "name": "融合格", "desc": "战斗开始时：和距离最近的友军融合，并获得其%的生命值、物理加成和法术加成。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_FusionDance.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"StatPercent": 0.5}, "composition": [], "from": null}, "TFT15_HiddenTech_ShieldMelt": {"apiName": "TFT15_HiddenTech_ShieldMelt", "name": "护盾融化", "desc": "战斗开始时：己方对有护盾的敌人造成%额外伤害。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_ShieldMelt.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"BonusDamage": 30.0, "Health": 250}, "composition": [], "from": null}, "TFT15_HiddenTech_AffinityForCold": {"apiName": "TFT15_HiddenTech_AffinityForCold", "name": "严寒亲和", "desc": "战斗开始时：让后2排的敌人进入冰冷状态。冰冷：受到的治疗效果降低20%", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_AffinityForCold.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Duration": 8.0, "Health": 250, "NumRow": 2}, "composition": [], "from": null}, "TFT15_HiddenTech_TrainingWeights": {"apiName": "TFT15_HiddenTech_TrainingWeights", "name": "训练负重", "desc": "在个回合内，该弈子会受到惩罚，失去% %i:scaleDA%和% %i:scaleDR%，攻击速度上限变为。个回合后，取消惩罚并获得% %i:scaleAS%和% %i:scaleDA%。每个弈子单独计算回合。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_TrainingWeights.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"ASCapPenality": 0.5, "ASReward": 0.8999999761581421, "DamagePenalty": 0.5, "DamageReward": 0.5, "DurabilityPenalty": 0.5, "Health": 100.0, "NumRounds": 5, "{adb8ce88}": 1000.0}, "composition": [], "from": null}, "TFT15_HiddenTech_SoulOfAChallenger": {"apiName": "TFT15_HiddenTech_SoulOfAChallenger", "name": "TFT15_HiddenTech_SoulOfAChallenger_Name", "desc": "TFT15_HiddenTech_SoulOfAChallenger_Desc", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_StoneSkin.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"AS": 20.0, "Health": 250}, "composition": [], "from": null}, "TFT15_HiddenTech_KiBarrier": {"apiName": "TFT15_HiddenTech_KiBarrier", "name": "忍法！气合盾", "desc": "持有者受到来自所有来源的伤害降低。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_KiBarrier.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"FlatReducedDamage": 25.0, "Health": 300.0}, "composition": [], "from": null}, "TFT15_HiddenTech_PetriciteSkin": {"apiName": "TFT15_HiddenTech_PetriciteSkin", "name": "禁魔石肤", "desc": "获得魔法抗性。", "icon": "ASSETS/Maps/TFT/Icons/TFT15/TFT15_HiddenTech_PetriciteSkin.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"MagicResist": 150.0}, "composition": [], "from": null}, "TFT15_Item_DragonFistFighterStance": {"apiName": "TFT15_Item_DragonFistFighterStance", "name": "决斗大师姿态", "desc": "被动：攻击时，对一个附近的敌人造成&nbsp;(%i:scaleAD%)物理伤害。主动：快速打击，对相距最近的个敌人发送&nbsp;(%i:scaleAS%)个残影。每个残影造成&nbsp;(%i:scaleAD%)物理伤害和&nbsp;(%i:scaleAP%)护甲削减。", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/Traits/Set15_DragonFist/TFT15_TraitArmory_DF_Duelist.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"ModifiedArmorShred": 5.0, "ModifiedCloneDamage": 65.0, "ModifiedNumClones": 10.0, "ModifiedPassiveCloneDamage": 40.0, "NumEnemies": 2.0}, "composition": [], "from": null}, "TFT15_Item_DragonFistTankStance": {"apiName": "TFT15_Item_DragonFistTankStance", "name": "主宰姿态", "desc": "暂时获得%伤害减免，然后对目标造成&nbsp;(%i:scaleAD%%i:scaleAP%)物理伤害，对目标身后的所有敌人造成&nbsp;(%i:scaleAD%)物理伤害，并对半径2格内的其他敌人造成&nbsp;(%i:scaleAD%)物理伤害。", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/Traits/Set15_DragonFist/TFT15_TraitArmory_DF_Juggernaut.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"ModifiedAllEnemiesDamage": 150.0, "ModifiedFinalDamage": 200.0, "ModifiedInitialDamage": 500.0, "WindowedDR": 0.6000000238418579}, "composition": [], "from": null}, "TFT15_Item_DragonFistReaperStance": {"apiName": "TFT15_Item_DragonFistReaperStance", "name": "裁决使者姿态", "desc": "对格内生命值最低的那个敌人造成&nbsp;(%i:scaleAD%%i:scaleAP%)物理伤害和秒晕眩。半径1格内的其他敌人们受到&nbsp;(%i:scaleAD%)物理伤害。在用这个技能完成次击杀后，所有敌人都会即刻受到&nbsp;(%i:scaleAD%)物理伤害和秒晕眩。", "icon": "ASSETS/Maps/Particles/TFT/Item_Icons/Traits/Set15_DragonFist/TFT15_TraitArmory_DF_Executioner.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"HexRange": 3.0, "KillStunDuration": 1.5, "ModifiedAOEDamage": 130.0, "ModifiedShockwaveDamage": 180.0, "ModifiedStrikeDamage": 330.0, "NumKills": 3.0, "StunDuration": 1.5}, "composition": [], "from": null}, "TFT15_RoboRanger_Core": {"apiName": "TFT15_RoboRanger_Core", "name": "组合核心", "desc": "【超级战队】弈子们治疗【超级机甲】，数额相当于他们%的伤害值。将我们的力量组合起来！", "icon": "ASSETS/Maps/TFT/Icons/Items/Hexcore/TFT15_ShieldMatrix.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_RoboRanger_Slicer": {"apiName": "TFT15_RoboRanger_Slicer", "name": "究极剑刃升级", "desc": "造成额外真实伤害，数额相当于%的所有造成伤害值。降下正义剑刃！", "icon": "ASSETS/Maps/TFT/Icons/Items/Hexcore/TFT15_RoboRanger_GalioSlicer.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"AS": 25.0, "PercentTrueDamage": 0.10000000149011612, "StatOmnivamp": 0.10000000149011612}, "composition": [], "from": null}, "TFT15_MonsterTrainerChoice_Anivia": {"apiName": "TFT15_MonsterTrainerChoice_Anivia", "name": "拉莫斯", "desc": null, "icon": "ASSETS/Characters/TFT15_Rammus/Skins/Base/Images/TFT15_<PERSON><PERSON>_splash_tile_26.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_MonsterTrainerChoice_Smolder": {"apiName": "TFT15_MonsterTrainerChoice_Smolder", "name": "斯莫德", "desc": null, "icon": "ASSETS/Characters/TFT15_Smolder/HUD/TFT15_Smolder_Square.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_RoboRanger_Sword": {"apiName": "TFT15_RoboRanger_Sword", "name": "强力剑刃", "desc": "攻击会顺劈一个锥形区域，对邻格的敌人们造成%物理伤害。我们以不破决心斩除邪恶！", "icon": "ASSETS/Maps/TFT/Icons/Items/Hexcore/TFT15_RoboRanger_GalioSword.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"AD": 0.25, "ADPercent": 0.5, "{b75d6a7b}": 1}, "composition": [], "from": null}, "TFT15_MonsterTrainerChoice_KogMaw": {"apiName": "TFT15_MonsterTrainerChoice_KogMaw", "name": "克格莫", "desc": null, "icon": "ASSETS/Characters/TFT15_KogMaw/HUD/TFT15_KogMaw_Square.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}, "TFT15_Item_CrystalRoseHellionItem": {"apiName": "TFT15_Item_CrystalRoseHellionItem", "name": "水晶增援", "desc": "你的弈子们获得生命值。每当一个单位阵亡时，一个低一星级的水晶复制体就会加入战斗！", "icon": "ASSETS/Maps/TFT/Icons/Items/Hexcore/TFT15_CrystalRoseHellion_ItemIcon.TFT_Set15.tex", "id": null, "associatedTraits": [], "effects": {"Health": 1000}, "composition": [], "from": null}, "TFT15_HiddenTech_ItemData": {"apiName": "TFT15_HiddenTech_ItemData", "name": "秘术", "desc": "TFT15_HiddenTech_ShopData_Description", "icon": "ASSETS/UX/TFT/Hud/ZAPS/Wands/ZAPS_ScoreboardBadge_EconWand_Self.TFT_Set12.tex", "id": null, "associatedTraits": [], "effects": {}, "composition": [], "from": null}}}