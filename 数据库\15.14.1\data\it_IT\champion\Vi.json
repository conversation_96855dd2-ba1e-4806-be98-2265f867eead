{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vi": {"id": "Vi", "key": "254", "name": "Vi", "title": "la legge di Piltover", "image": {"full": "Vi.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "254000", "num": 0, "name": "default", "chromas": false}, {"id": "254001", "num": 1, "name": "<PERSON>i al Neon", "chromas": false}, {"id": "254002", "num": 2, "name": "Agente Vi", "chromas": true}, {"id": "254003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "254004", "num": 4, "name": "Vi Demoniaca", "chromas": false}, {"id": "254005", "num": 5, "name": "Vi dei Regni in Guerra", "chromas": false}, {"id": "254011", "num": 11, "name": "PROGETTO: Vi", "chromas": false}, {"id": "254012", "num": 12, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "254020", "num": 20, "name": "Vi OPSI", "chromas": true}, {"id": "254029", "num": 29, "name": "Vi Arcane: <PERSON><PERSON><PERSON>a", "chromas": false}, {"id": "254030", "num": 30, "name": "Vi Cuore spezzato", "chromas": true}, {"id": "254039", "num": 39, "name": "Vi Agguato ferino", "chromas": true}, {"id": "254048", "num": 48, "name": "Vi Arcane: Combattente", "chromas": false}], "lore": "Cresciuta fra le pericolose strade di Zaun, Vi è una testa calda. Una donna impulsiva e temibile, con un rispetto molto blando per le autorità. Le sue scorribande giovanili a Piltover e un soggiorno ingiustamente lungo nella Prigione di Acquaferma l'hanno dotata della scaltrezza tipica dei sopravvissuti. Ora collabora con gli Agenti per mantenere la legge a Piltover invece di infrangerla, e sfoggia due possenti guanti hextech che possono sfondare muri e criminali con eguale facilità.", "blurb": "Cresciuta fra le pericolose strade di Zaun, Vi è una testa calda. Una donna impulsiva e temibile, con un rispetto molto blando per le autorità. Le sue scorribande giovanili a Piltover e un soggiorno ingiustamente lungo nella Prigione di Acquaferma...", "allytips": ["Quando pienamente carico, Spaccaferro raddoppia il danno. Ottimo per raggiungere e finire campioni che fuggono.", "Forza implacabile infligge danni pieni a chiunque si trovi nell'onda d'urto. Usala sui minion nella corsia per colpire i campioni che si nascondono dietro.", "Fermati e arrenditi è un potente strumento per ingaggiare, ricordati solo di non usarla troppo lontano dai tuoi alleati."], "enemytips": ["<PERSON>uando carico, Spaccaferro raddoppia il danno. Se Vi inizia a caricare dovresti arretrare o cercare di schivarlo.", "Vi neutralizzerà la tua armatura e guadagnerà velocità d'attacco se ti colpisce tre volte. Cerca di non combattere con lei a lungo.", "Vi diventa inarrestabile mentre usa la sua suprema. Ricorda di conservare i tuoi effetti di spostamento da usare dopo che avrà effettuato la sua carica."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 295, "mpperlevel": 65, "movespeed": 340, "armor": 30, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 10, "hpregenperlevel": 1, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "ViQ", "name": "Spaccaferro", "description": "Vi carica i suoi guanti e scarica un pugno distruttivo, che la trascina in avanti. I nemici che colpisce vengono scaraventati indietro e subiscono una carica di Colpi ammaccanti.", "tooltip": "<charge>Caricamento:</charge> Vi inizia a caricare un pugno potentissimo, <status>rallentando</status> se stessa del {{ e4 }}%.<br /><br /><release>Rilascio:</release> Vi scatta in avanti infliggendo tra <physicalDamage>{{ totaldamage }} e {{ maxdamagetooltip }} danni fisici</physicalDamage> in base al tempo di caricamento e applicando <spellName>Colpi ammaccanti</spellName> a tutti i nemici colpiti. Vi si ferma all'impatto con un campione nemico, <status>sbalzandolo</status> <status>all'indietro</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ mindamage*2.500000 }} -> {{ mindamagenl*2.500000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "15", "6", "5", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ViQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViW", "name": "<PERSON><PERSON>", "description": "I pugni di Vi distruggono l'armatura nemica, infliggendo danni bonus e aumentando la sua velocità d'attacco.", "tooltip": "<spellPassive>Passiva:</spellPassive> Ogni terzo attacco sullo stesso bersaglio infligge <physicalDamage>danni fisici aggiuntivi pari a {{ totaldamagetooltip }} della salute massima</physicalDamage>, riduce la sua <scaleArmor>armatura di un {{ shredamount }}%</scaleArmor> e conferisce a Vi <attackSpeed>{{ attackspeed }}% velocità d'attacco</attackSpeed> per {{ sharedbuffsduration }} secondi. Inoltre, riduce la ricarica rimanente di <spellName>Scudo energetico</spellName> di {{ spell.vipassive:cdreductionon3hit }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> salute massima", "Velocità d'attacco"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ attackspeed }}% -> {{ attackspeedNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiva", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Passiva"}, {"id": "ViE", "name": "Forza implacabile", "description": "Il prossimo attacco di Vi trapassa il bersaglio nemico, infliggendo danni ai nemici dietro.", "tooltip": "Il prossimo attacco di Vi infligge <physicalDamage>{{ totaldamagetooltip }} danni fisici</physicalDamage> al bersaglio e ai nemici dietro di esso.<br /><br />Questa abilità ha 2 cariche ({{ ammorechargetime }} secondo di ricarica).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Tempo di carica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [26, 32, 38, 44, 50], "costBurn": "26/32/38/44/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1.5", "6", "1", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViR", "name": "Fermati e arrenditi", "description": "Vi travolge un nemico, sbal<PERSON>do via chiunque sul percorso. Quando raggiunge il suo bersaglio lo scaraventa in aria, lo insegue saltando e lo sbatte a terra di nuovo.", "tooltip": "Vi isola un campione nemico, rive<PERSON><PERSON> e scattando verso di lui senza poter essere fermata. Quando lo raggiunge, Vi lo <status>sbalza</status> <status>in aria</status> per {{ rstunduration }} secondi e gli infligge <physicalDamage>{{ damage }} danni fisici</physicalDamage>.<br /><br />Qualsiasi altro nemico con cui Vi entra in collisione subisce danni, viene spinto di lato ed è <status>stordito</status> per {{ secondarytargetstunduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ViR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> <PERSON>o", "description": "Vi carica uno scudo col passare del tempo. Lo scudo può essere attivato colpendo un nemico con un'abilità.", "image": {"full": "ViPassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}