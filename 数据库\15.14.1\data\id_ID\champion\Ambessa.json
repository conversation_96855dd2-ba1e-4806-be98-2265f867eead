{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ambessa": {"id": "<PERSON><PERSON><PERSON>", "key": "799", "name": "<PERSON><PERSON><PERSON>", "title": "Matriarch of War", "image": {"full": "Ambessa.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "799000", "num": 0, "name": "default", "chromas": false}, {"id": "799001", "num": 1, "name": "<PERSON><PERSON> of the Wolf Ambessa", "chromas": true}], "lore": "Siapa pun yang mengenal nama Medarda pasti hormat dan segan kepada sang pemimpin keluarga, <PERSON><PERSON><PERSON>. Sebagai jenderal Noxus, ia adalah kombinasi yang mematikan antara kekuatan yang kejam dan tekad yang begitu besar dalam pertempuran. Perannya sebagai pemimpin juga tidak berbeda. Butuh kecerdikan yang luar biasa untuk memerintah keluarga Medarda tanpa menyisakan ruang untuk kegagalan atau belas kasih. Dengan menganut sikap keji kaum <PERSON>, Ambessa akan melakukan berbagai cara demi melindungi legasi keluarganya, meski harus mengorbankan rasa sayang pada anaknya sendiri.", "blurb": "Siapa pun yang mengenal nama Medarda pasti hormat dan segan kepada sang pemim<PERSON> keluarga, <PERSON><PERSON><PERSON>. Sebagai jenderal Noxus, ia adalah kombinasi yang mematikan antara kekuatan yang kejam dan tekad yang begitu besar dalam pertempuran. Perannya sebagai...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "Energy", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 10}, "stats": {"hp": 630, "hpperlevel": 110, "mp": 200, "mpperlevel": 0, "movespeed": 335, "armor": 35, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "AmbessaQ", "name": "Cunning Sweep/Sundering Slam", "description": "Ambessa mengayunkan Twin Drakehound dalam bentuk setengah lingkaran di depannya, mengh<PERSON>lkan damage bonus ke musuh yang terkena tebasannya. Menyerang musuh akan mengubah cast berikutnya dari ability ini selama beberapa saat, menyebabkan dia menghantamkan Twin Drakehound ke bawah dalam satu garis di depannya, mengh<PERSON>lkan damage bonus ke musuh pertama yang terkena.", "tooltip": "<spellActive>Cunning Sweep</spellActive>: <PERSON><PERSON><PERSON> mengayunkan pedang ke depan, men<PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ calc_damage_1_max }} + {{ calc_damage_1_percent_max }} physical damage Health maksimum</physicalDamage> ke musuh di tepi serangan. Semua musuh lainnya menerima {{ calc_damage_1_min_ratio }} damage. Menyerang musuh akan menyiapkan <spellActive>Sundering Slam</spellActive>.<br /><br /><spellActive>Sundering Slam</spellActive>: Ambessa menghantamkan pedangnya ke bawah, men<PERSON><PERSON><PERSON>an <physicalDamage>{{ calc_damage_2_max }} + {{ calc_damage_2_percent_max }} physical damage Health maksimum</physicalDamage> terhadap musuh pertama yang terkena. Semua musuh lainnya menerima {{ calc_damage_2_min_ratio }} damage.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Cunning Sweep | Damage", "Cunning Sweep | Damage Health Maksimum", "Sundering Slam | Damage", "Sundering Slam | Damage Health Maksimum"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ damage_1_base }}-> {{ damage_1_baseNL }}", "{{ damage_1_percent*100.000000 }}%-> {{ damage_1_percentnl*100.000000 }}%", "{{ damage_2_base }}-> {{ damage_2_baseNL }}", "{{ damage_1_percent*100.000000 }}%-> {{ damage_1_percentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "AmbessaQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaW", "name": "Repudiation", "description": "Ambessa mendapa<PERSON> shield, melindungi dirinya se<PERSON>, kemudian menghantam tanah untuk menghasilkan damage ke musuh di sekitar. Jika dia menangkis damage dari non-minion saat melindungi diri, ability ini akan menghasilkan peningkatan damage.", "tooltip": "Ambessa menda<PERSON> <shield>{{ calc_shield }} Shield</shield> selama {{ shield_duration }} detik and melindungi dirinya selama {{ buff_duration }} detik. Dia kemudian menghantam tanah, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ calc_damage_low }} physical damage</physicalDamage> ke musuh di sekitar, yang meningkat ke <physicalDamage>{{ calc_damage_high }} physical damage</physicalDamage> jika dia melindungi diri dari damage champion musuh, monster besar, atau bangunan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaE", "name": "Lacerate", "description": "Ambessa mencambukkan Twin Drakehound di sekelilingnya, <PERSON><PERSON><PERSON><PERSON><PERSON> damage dan menerapkan slow ke musuh di sekitar. <PERSON><PERSON><PERSON> Drakehound's Step dari ability ini menyebabkan dia menyerang untuk kedua kalinya di akhir dash.", "tooltip": "<PERSON><PERSON><PERSON> men<PERSON>n rantain<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ calc_damage_flat }} physical damage</physicalDamage> dan <status>menerapkan efek Slow</status> ke musuh sebesar <status> {{ slow_amount*100 }}%</status>, yang berkurang dalam {{ slow_duration }} detik. Memulai <spellName>Drakehound's Step</spellName> dari Ability ini akan memicu serangan tambahan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "Rasio Attack Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ damage_flat_base }}-> {{ damage_flat_baseNL }}", "{{ adratio*100.000000 }}%-> {{ adrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "AmbessaE.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AmbessaR", "name": "Public Execution", "description": "Ambessa blink ke arah champion musuh terjauh dalam garis yang dipilihnya dan menekan mereka saat tiba. Lalu musuh tersebut dibanting ke tanah sehingga menerima damage dan terkena stun.", "tooltip": "<spellPassive>Pasif</spellPassive>: Am<PERSON><PERSON> men<PERSON>kan <armorPen>{{ armor_penetration*100 }}% %i:scaleAPen% Armor Penetration</armorPen>, dan Ability-nya <healing>memberinya heal sebesar {{ calc_omnivamp }} dari damage yang dihasilkan</healing>.<br /><br /><spellActive>Aktif</spellActive>: Ambes<PERSON> menjadi <attention>Tak Terhen<PERSON>kan</attention> dan blink ke arah champion musuh terjauh dalam garis lurus, <status>Menekan</status> target selama {{ suppress_duration }} detik kemudian membanting target ke tanah, men<PERSON><PERSON><PERSON>an <physicalDamage>{{ calc_damage }} physical damage</physicalDamage> dan <status>memberikan stun</status> ke target selama {{ stun_duration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Armor Penetration", "Heal", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ armor_penetration*100.000000 }}%-> {{ armor_penetrationnl*100.000000 }}%", "{{ omnivamp*100.000000 }}%-> {{ omnivampnl*100.000000 }}%", "{{ damage }}-> {{ damageNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Tan<PERSON> biaya", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AmbessaR.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Tan<PERSON> biaya"}], "passive": {"name": "Drakehound's Step", "description": "Memasukkan perintah serangan atau gerakan saat cast ability akan membuat Ambessa dash dalam jarak dekat setelah ability ini digunakan, sehingga memberikan jangkauan bonus, damage, attack speed, dan pengembalian energi pada serangan selanjutnya.", "image": {"full": "Icon_Ambessa_Passive.Domina.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}