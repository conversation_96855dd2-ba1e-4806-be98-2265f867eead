{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ornn": {"id": "<PERSON><PERSON>", "key": "516", "name": "オーン", "title": "山の下の焔", "image": {"full": "Ornn.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "516000", "num": 0, "name": "default", "chromas": false}, {"id": "516001", "num": 1, "name": "雷帝オーン", "chromas": false}, {"id": "516002", "num": 2, "name": "古の賢樹オーン", "chromas": true}, {"id": "516011", "num": 11, "name": "スペースグルーヴ オーン", "chromas": true}, {"id": "516020", "num": 20, "name": "きかんしゃオーン", "chromas": true}], "lore": "オーンは鍛冶と技巧を司る、フレヨルドの半神半人だ。彼は“炉床の家”と呼ばれる、火山の地下にある溶岩の洞窟をハンマーで叩いて造った巨大な鍛冶場で独り仕事に打ち込んでいる。そこで大釜を火にかけ、鉱石を溶かして精製し、比類なき武具を鍛造しているのだ。他の半神半人たち──特にボリベア──が大地を歩き定命の者たちの営みに干渉を始める時、オーンは立ち上がる。彼の信頼するハンマーと山脈の炎の力を手に、そういった問題児どもを元いた場所に帰すために。", "blurb": "オーンは鍛冶と技巧を司る、フレヨルドの半神半人だ。彼は“炉床の家”と呼ばれる、火山の地下にある溶岩の洞窟をハンマーで叩いて造った巨大な鍛冶場で独り仕事に打ち込んでいる。そこで大釜を火にかけ、鉱石を溶かして精製し、比類なき武具を鍛造しているのだ。他の半神半人たち──特にボリベア──が大地を歩き定命の者たちの営みに干渉を始める時、オーンは立ち上がる。彼の信頼するハンマーと山脈の炎の力を手に、そういった問題児どもを元いた場所に帰すために。", "allytips": ["レーンですばやくアイテムのアップグレード先を選択できるように、ビルドの順番を覚えておこう。", "「溶岩隆起」で相手の行動範囲を制限して脅威を与えよう。", "スキルを使用する順番が重要だ！「脆弱」を最大限に活かせるように使おう。"], "enemytips": ["壁に近づかないようにしよう。スタンさえ受けなければオーンはそれほど怖い存在ではない。", "レーン戦ではオーンを攻撃してアイテムを作らせないようにしよう。"], "tags": ["Tank"], "partype": "マナ", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 660, "hpperlevel": 109, "mp": 341, "mpperlevel": 65, "movespeed": 335, "armor": 33, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "OrnnQ", "name": "溶岩隆起", "description": "地面を叩きつけて裂け目を発生させ、敵ユニットにダメージを与えて移動速度を低下させる。少ししてから、裂け目の終端に溶岩の柱が発生する。", "tooltip": "地面を叩きつけて裂け目を発生させ、敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与え、{{ e6 }}秒間{{ e5 }}%の<status>スロウ効果</status>を付与する。その後{{ e3 }}秒間、裂け目の終端に岩の柱が発生する。 {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [20, 45, 70, 95, 120], [1, 1, 1, 1, 1], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/45/70/95/120", "1", "4", "0", "40", "2", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "OrnnQ.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "OrnnW", "name": "ふいごの息", "description": "前進し、炎を吐き出す。炎の最後の塊が当たった敵は「脆弱」状態になる。", "tooltip": "炎の息を吐きながら足を踏み鳴らして進み、アンストッパブル状態になって、{{ breathduration }}秒かけて<magicDamage>最大体力の{{ maxpercenthpperticktooltip }}%の魔法ダメージ</magicDamage>を与える。炎の最後の一吐きが当たった敵は{{ brittleduration }}秒間<keywordMajor>「脆弱」</keywordMajor>状態になる。<br /><br /><keywordMajor>「脆弱」</keywordMajor>状態の対象に<status>移動不能効果</status>を与えると、その効果時間を30%延長し、<magicDamage>最大体力の{{ brittlepercentmaxhpcalc }}の魔法ダメージ</magicDamage>を与える。オーンの通常攻撃は<keywordMajor>「脆弱」</keywordMajor>状態になった対象を<status>ノック</status><status>バック</status>させ、追加ダメージを与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力割合ダメージ", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ maxpercenthp<PERSON>icktooltip }}% -> {{ maxpercenthpperticktooltipNL }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "OrnnW.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "OrnnE", "name": "灼熱の突撃", "description": "ダッシュして当たった敵ユニットにダメージを与える。ダッシュ中に地形にぶつかると周囲に衝撃波が発生し、敵ユニットにダメージを与えてノックアップする。", "tooltip": "ダッシュして<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。地形にぶつかると衝撃波が発生して敵を{{ knockupduration }}秒間<status>ノックアップ</status>させ、ダッシュ時に当たらなかった敵に対しても同じダメージを与える。<br /><br />このダッシュは溶岩の柱や敵によって作られた地形を破壊する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ダッシュ」基本ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "OrnnE.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "OrnnR", "name": "鍛冶神の呼び声", "description": "指定地点に巨大な精霊を呼び出す。精霊はどんどん速度を上げながらオーンがいる方向に進んでくる。精霊にぶつかった敵ユニットはダメージを受けて移動速度が低下し、「脆弱」状態になる。スキルを再使用するとオーンが精霊に向かって突撃し、彼がぶつかった方向に精霊の進行方向を変える。この精霊に当たった敵ユニットはノックアップされて、最初と同量のダメージを受け、再び「脆弱」が適用される。", "tooltip": "巨大な溶岩の精霊を呼び出す。精霊はオーンがいる方向に突き進み、接触した敵に<magicDamage>{{ rdamagecalc }}の魔法ダメージ</magicDamage>を与えて、{{ brittledurationtooltiponly }}秒間<keywordMajor>「脆弱」</keywordMajor>と{{ rslowpercentbasepremath }}%の<status>スロウ効果</status>を付与する。<br /><br /><recast>再発動</recast>すると、オーンが突撃して頭突きを行う。精霊に向かって突撃すると、精霊の進行方向を変えて強化し、精霊が最初に当たったチャンピオンを{{ rstunduration }}秒、それ以降に当たったチャンピオンを{{ minstun }}秒間<status>ノックアップ</status>させる。また、精霊は<magicDamage>{{ rdamagecalc }}の魔法ダメージ</magicDamage>を与えて、<keywordMajor>「脆弱」</keywordMajor>を再び適用する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rslowpercentbasepremath }} -> {{ rslowpercentbasepremathNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [2500, 2500, 2500], "rangeBurn": "2500", "image": {"full": "OrnnR.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "歩く鍛冶場", "description": "オーンが獲得するあらゆる追加物理防御と追加魔法防御は、獲得量が増加する。<br><br>オーンはどこにいても、ゴールドを消費して消費アイテム以外のアイテムを作り出せる。<br><br>さらに、自身と味方のために名匠アイテムを作り出せる。", "image": {"full": "OrnnP.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}