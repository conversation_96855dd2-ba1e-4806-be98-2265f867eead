{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sejuani": {"id": "<PERSON><PERSON><PERSON>", "key": "113", "name": "<PERSON><PERSON><PERSON>", "title": "Furia del nord", "image": {"full": "Sejuani.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "113000", "num": 0, "name": "default", "chromas": false}, {"id": "113001", "num": 1, "name": "<PERSON><PERSON><PERSON> Forest<PERSON>", "chromas": false}, {"id": "113002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "113003", "num": 3, "name": "Sejuani Tradizionale", "chromas": false}, {"id": "113004", "num": 4, "name": "<PERSON><PERSON>ani <PERSON>liere dell'Orso", "chromas": false}, {"id": "113005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "113006", "num": 6, "name": "<PERSON><PERSON><PERSON> di Bestie", "chromas": false}, {"id": "113007", "num": 7, "name": "<PERSON><PERSON><PERSON>Albe", "chromas": false}, {"id": "113008", "num": 8, "name": "<PERSON><PERSON><PERSON>'Artificio", "chromas": true}, {"id": "113015", "num": 15, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "113016", "num": 16, "name": "PROGETTO: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "113026", "num": 26, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "113036", "num": 36, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Sejuani è la brutale e spietata madre guerriera, figlia del gelo del clan dell'Artiglio d'Inverno, una delle tribù più temute del Freljord. Per sopravvivere, la sua gente è in costante lotta contro gli elementi ed è costretta a razziare continuamente noxiani, demacian<PERSON> e avarosani. Sejuani guida in prima persona gli attacchi più pericolosi in sella al suo cinghiale drüvask Bristle, utilizzando il Vero Ghiaccio per congelare e abbattere i nemici.", "blurb": "Sejuani è la brutale e spietata madre guerriera, figlia del gelo del clan dell'Artiglio d'Inverno, una delle tribù più temute del Freljord. Per sopravvivere, la sua gente è in costante lotta contro gli elementi ed è costretta a razziare continuamente...", "allytips": ["Usa Assalto artico per fuggire dalle situazioni difficili, per interrompere i lanci canalizzati o per inseguire i nemici.", "<PERSON><PERSON> alleati da mischia possono accumulare Congelamento per il tuo Permafrost, quindi tieni d'occhio chi attaccano.", "Conserva Prigione glaciale per gli incontri nei quali i compagni di squadra possono trarne vantaggio."], "enemytips": ["Schivare Assalto artico e Prigione glaciale ti dà un grosso vantaggio.", "Cerca di evitare il secondo colpo di Rabbia dell'inverno, nel quale si concentra la maggior quantità di danni.", "Anche i nemici da mischia possono accumulare Congelamento per Sejuani. Fai attenzione, quando sono vicini."], "tags": ["Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 4}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 400, "mpperlevel": 40, "movespeed": 340, "armor": 34, "armorperlevel": 5.45, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 4, "attackspeedperlevel": 3.5, "attackspeed": 0.688}, "spells": [{"id": "SejuaniQ", "name": "Assalto artico", "description": "Se<PERSON><PERSON> carica in avanti, lanciando in aria i nemici. La carica si ferma dopo aver colpito un campione nemico.", "tooltip": "Sejuani parte alla carica, infliggendo <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage> ai nemici e <status>lanciandoli</status> <status>in aria</status> per {{ e4 }} secondi. La carica si ferma dopo aver colpito un campione nemico.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [19, 17.5, 16, 14.5, 13], "cooldownBurn": "19/17.5/16/14.5/13", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [625, 625, 625, 625, 625], [1000, 1000, 1000, 1000, 1000], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "625", "1000", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "SejuaniQ.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SejuaniW", "name": "Rabbia dell'inverno", "description": "<PERSON><PERSON><PERSON> colpisce due volte con la sua mazza, infliggendo danni, rallentando i nemici e applicando cariche di Congelamento.", "tooltip": "Se<PERSON><PERSON> fa roteare il suo flagello, infliggendo <physicalDamage>{{ firsthitdamagetooltip }} danni fisici</physicalDamage> e <status>respingendo</status> minion e mostri della giungla. Poi attacca di nuovo, infliggendo <physicalDamage>{{ secondhitdamagetooltip }} danni fisici</physicalDamage> e <status>rallentando</status> per breve tempo i bersagli.<br /><br />Entrambi i colpi applicano cariche di <spellName>Permafrost</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>ni primo colpo", "<PERSON><PERSON> secondo colpo", "Ricarica"], "effect": ["{{ basedamageone }} -> {{ basedamageoneNL }}", "{{ basedamagetwo }} -> {{ basedamagetwoNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0.75", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SejuaniW.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SejuaniE", "name": "Permafrost", "description": "<PERSON><PERSON><PERSON> congela e stordisce un campione nemico che ha il massimo di cariche di Congelamento.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi dei campioni da mischia alleati nelle vicinanze applicano una carica a campioni e mostri della giungla.<br /><br /><spellPassive>Attiva:</spellPassive> <PERSON><PERSON><PERSON> infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> al nemico bersaglio con 4 cariche e lo <status>stordisce</status> per {{ e1 }} secondo. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [55, 105, 155, 205, 255], [4, 4, 4, 4, 4], [5, 5, 5, 5, 5], [1100, 1100, 1100, 1100, 1100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [250, 250, 250, 250, 250], [0, 0, 0, 0, 0]], "effectBurn": [null, "1", "0", "55/105/155/205/255", "4", "5", "1100", "0", "0", "250", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [560, 560, 560, 560, 560], "rangeBurn": "560", "image": {"full": "SejuaniE.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SejuaniR", "name": "Prigione glaciale", "description": "Sejuani lancia la sua bola, che congela e stordisce il primo campione colpito e crea una tempesta di ghiaccio che rallenta gli altri nemici.", "tooltip": "Sejuani lancia la sua bola di Vero Ghiaccio, <status>stordendo</status> e rivelando il primo nemico colpito per {{ e2 }} secondo/i e infliggendogli <magicDamage>{{ minordamagetooltip }} danni magici</magicDamage>.<br /><br />Se la bola percorre almeno il 25% della sua gittata, <status>stordisce</status> e rivela il bersaglio per {{ e6 }} secondi. Inolt<PERSON>, crea una tempesta glaciale che <status>rallenta</status> i nemici circostanti del {{ e4 }}% per {{ e3 }} secondi. Tutti i nemici colpiti subiscono <magicDamage>{{ totaldamagetooltip }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> minimi", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 150, 175], [1, 1, 1], [2, 2, 2], [80, 80, 80], [1, 1, 1], [1.5, 1.5, 1.5], [200, 300, 400], [30, 30, 30], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "125/150/175", "1", "2", "80", "1", "1.5", "200/300/400", "30", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "SejuaniR.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Furia del nord", "description": "Quando rimane abbastanza a lungo fuori dai combattimenti, <PERSON><PERSON><PERSON> ottiene Armatura glaciale, che conferisce armatura e resistenza magica e immunità ai rallentamenti. Armatura glaciale rimane per un breve periodo dopo che Sejuani ha subito danni. <PERSON><PERSON><PERSON> può danneggiare un nemico stordito per frantumarlo, infliggendo danni magici ingenti.", "image": {"full": "Sejuani_passive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}