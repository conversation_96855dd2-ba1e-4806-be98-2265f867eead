{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Darius": {"id": "<PERSON>", "key": "122", "name": "다리우스", "title": "녹서스의 실력자", "image": {"full": "Darius.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "122000", "num": 0, "name": "default", "chromas": true}, {"id": "122001", "num": 1, "name": "다리우스 경", "chromas": false}, {"id": "122002", "num": 2, "name": "생체공학 다리우스", "chromas": false}, {"id": "122003", "num": 3, "name": "북방의 왕 다리우스", "chromas": false}, {"id": "122004", "num": 4, "name": "덩크왕 다리우스", "chromas": true}, {"id": "122008", "num": 8, "name": "반항아 다리우스", "chromas": false}, {"id": "122014", "num": 14, "name": "행성 파괴자 다리우스", "chromas": false}, {"id": "122015", "num": 15, "name": "반역왕 다리우스", "chromas": false}, {"id": "122016", "num": 16, "name": "하이 눈 다리우스", "chromas": true}, {"id": "122024", "num": 24, "name": "새해 야수 다리우스", "chromas": true}, {"id": "122033", "num": 33, "name": "범죄 도시 악몽 다리우스", "chromas": false}, {"id": "122043", "num": 43, "name": "영혼의 꽃 다리우스", "chromas": false}, {"id": "122054", "num": 54, "name": "도자기 다리우스", "chromas": false}, {"id": "122064", "num": 64, "name": "거룩한 반역왕 다리우스", "chromas": false}, {"id": "122065", "num": 65, "name": "프레스티지 개선장군 다리우스", "chromas": false}], "lore": "녹서스 그 자체를 상징하는 인물로 다리우스만큼 어울리는 사람도 없을 것이다. 실전에서 단련된 사령관이자 녹서스 내에서조차도 두려움의 대상이니까. 다리우스는 미천한 집안에서 태어났으나 녹서스 제국의 적들을 파죽지세로 베어넘기면서 트리파르 군단 사령관이라는 지금의 자리와 권력을 획득했다. 문제는 그 적들 다수가 녹서스 인이었다는 사실이다. 다리우스는 자신의 명분이 정당하다는 것을 한 번도 의심한 적이 없으며, 도끼를 치켜들 때에도 망설임이 없다. 그러니 다리우스에게 맞서는 자는 자비를 바랄 수 없다.", "blurb": "녹서스 그 자체를 상징하는 인물로 다리우스만큼 어울리는 사람도 없을 것이다. 실전에서 단련된 사령관이자 녹서스 내에서조차도 두려움의 대상이니까. 다리우스는 미천한 집안에서 태어났으나 녹서스 제국의 적들을 파죽지세로 베어넘기면서 트리파르 군단 사령관이라는 지금의 자리와 권력을 획득했다. 문제는 그 적들 다수가 녹서스 인이었다는 사실이다. 다리우스는 자신의 명분이 정당하다는 것을 한 번도 의심한 적이 없으며, 도끼를 치켜들 때에도 망설임이 없다. 그러니...", "allytips": ["학살은 강력한 견제기입니다. 사거리 끝에서 적을 가격하면 효과가 극대화됩니다.", "녹서스의 단두대를 사용하기 전에 공격을 많이 적중시킬수록 더 큰 피해를 입힙니다. 녹서스의 힘 효과를 활용하여 피해량을 극대화하세요.", "다리우스는 생존력을 강화할 때 큰 혜택을 볼 수 있습니다. 교전을 오래 끌수록 위력도 커집니다."], "enemytips": ["다리우스는 포획 스킬이 재사용 대기에 들어가고 나면 견제 공격에 취약해집니다.", "다리우스는 교전 시 탈출기가 별로 없습니다. 유리한 상황일 때는 강하게 압박하세요."], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 9, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 652, "hpperlevel": 114, "mp": 263, "mpperlevel": 58, "movespeed": 340, "armor": 37, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 10, "hpregenperlevel": 0.95, "mpregen": 6.6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 5, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "DariusCleave", "name": "학살", "description": "다리우스가 도끼를 큰 원의 형태로 휘두릅니다. 도끼날에 맞은 적들은 도끼자루에 맞은 적들보다 더 큰 피해를 입습니다. 다리우스는 도끼날에 맞은 적 챔피언과 대형 몬스터의 수에 비례하여 체력이 회복됩니다.", "tooltip": "다리우스가 도끼를 들어 올린 후 주위로 휘둘러 도끼날로는 <physicalDamage>{{ bladedamage }}의 물리 피해</physicalDamage>, 도끼 자루로는 <physicalDamage>{{ handledamage }}의 피해</physicalDamage>를 입힙니다. 도끼 자루에 맞은 적은 <keywordMajor>과다출혈</keywordMajor>이 중첩되지 않습니다.<br /><br />다리우스는 도끼날로 맞힌 적 챔피언과 대형 정글 몬스터 하나당 <healing>잃은 체력의 {{ e5 }}%</healing>를 회복합니다. 최대 <healing>{{ e7 }}%</healing>까지 회복됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "총 공격력 %", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [100, 110, 120, 130, 140], [50, 80, 110, 140, 170], [99, 99, 99, 99, 99], [0.1, 0.1, 0.1, 0.1, 0.1], [17, 17, 17, 17, 17], [35, 35, 35, 35, 35], [51, 51, 51, 51, 51], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/110/120/130/140", "50/80/110/140/170", "99", "0.1", "17", "35", "51", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "DariusCleave.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "DariusNoxianTacticsONH", "name": "마비의 일격", "description": "다리우스의 다음 공격은 적의 대동맥을 가격합니다. 적들이 출혈을 일으키면서 이동 속도가 감소합니다.", "tooltip": "다리우스의 다음 기본 공격은 <physicalDamage>{{ empoweredattackdamage }}의 물리 피해</physicalDamage>를 입히고, {{ e5 }}초 동안 {{ e2 }}% <status>둔화</status>시킵니다.<br /><br />이 스킬로 대상을 처치하면 소모한 마나를 되돌려받고, 재사용 대기시간이 {{ e3 }}% 감소합니다.<br /><br /><rules>이 스킬은 피해를 입힐 때 효과가 발동합니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["총 공격력 %"], "effect": ["{{ effect4amount*100.000000 }} -> {{ effect4amountnl*100.000000 }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [90, 90, 90, 90, 90], [50, 50, 50, 50, 50], [1.4, 1.45, 1.5, 1.55, 1.6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "90", "50", "1.4/1.45/1.5/1.55/1.6", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DariusNoxianTacticsONH.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "DariusAxeGrabCone", "name": "포획", "description": "다리우스가 도끼날을 날카롭게 세워, 물리 피해를 가할 때 대상의 방어력 중 일정 비율을 무시합니다. 스킬을 사용하면 다리우스가 도끼의 갈고리 부분으로 적들을 휘감아 자기 쪽으로 끌어당깁니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 다리우스의 방어구 관통력이 {{ e1 }}% 상승합니다.<br /><br /><spellActive>사용 시:</spellActive> 다리우스가 도끼를 걸어 <status>끌어당기고</status> <status>공중으로 띄워 올린 후</status> {{ e3 }}초 동안 {{ e2 }}% <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["방어구 관통력 %", "소모값 @AbilityResourceName@", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 23.5, 21, 18.5, 16], "cooldownBurn": "26/23.5/21/18.5/16", "cost": [70, 60, 50, 40, 30], "costBurn": "70/60/50/40/30", "datavalues": {}, "effect": [null, [20, 25, 30, 35, 40], [40, 40, 40, 40, 40], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/25/30/35/40", "40", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [535, 535, 535, 535, 535], "rangeBurn": "535", "image": {"full": "DariusAxeGrabCone.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "DariusExecute", "name": "녹서스의 단두대", "description": "다리우스가 적 챔피언에게 뛰어올라 치명적 타격을 가해 고정 피해를 입힙니다. 대상에 중첩된 과다출혈에 비례하여 피해량이 증가합니다. 녹서스의 단두대로 적을 처치하는 경우, 잠시 동안 재사용 대기시간이 초기화됩니다.", "tooltip": "다리우스가 적에게 뛰어올라 치명적 타격을 가하여 <trueDamage>{{ damage }}의 고정 피해</trueDamage>를 입힙니다. 대상의 <keywordMajor>과다출혈</keywordMajor> 중첩 하나당 {{ rdamagepercentperhemostack*100 }}%의 피해를 추가로 입힙니다. 최대 <trueDamage>{{ maximumdamage }}의 피해</trueDamage>가 적용됩니다.<br /><br />이 스킬로 대상을 처치할 경우, 다리우스가 {{ rrecastduration }}초 안에 이 스킬을 <recast>재사용</recast>할 수 있습니다. 스킬 레벨이 3이 되면 이 스킬을 사용할 때 마나가 소모되지 않으며 챔피언을 처치하면 재사용 대기시간이 완전히 초기화됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 0], "costBurn": "100/100/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [460, 460, 460], "rangeBurn": "460", "image": {"full": "DariusExecute.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "과다출혈", "description": "다리우스의 기본 공격과 스킬 공격은 적에게 출혈을 일으켜 5초 동안 물리 피해를 입힙니다. 최대 5회까지 중첩됩니다. 최대 중첩 시 다리우스가 분노하며 공격력이 크게 증가합니다.", "image": {"full": "<PERSON>_<PERSON><PERSON>_Hemorrhage.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}