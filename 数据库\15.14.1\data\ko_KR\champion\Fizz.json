{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "피즈", "title": "대양의 말썽꾸러기", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "아틀란티스 피즈", "chromas": false}, {"id": "105002", "num": 2, "name": "툰드라 피즈", "chromas": false}, {"id": "105003", "num": 3, "name": "낚시꾼 피즈", "chromas": false}, {"id": "105004", "num": 4, "name": "공허의 피즈", "chromas": false}, {"id": "105008", "num": 8, "name": "복실복실 피즈", "chromas": false}, {"id": "105009", "num": 9, "name": "슈퍼 갤럭시 피즈", "chromas": false}, {"id": "105010", "num": 10, "name": "오메가 분대 피즈", "chromas": true}, {"id": "105014", "num": 14, "name": "피즈멍", "chromas": false}, {"id": "105015", "num": 15, "name": "프레스티지 피즈멍", "chromas": false}, {"id": "105016", "num": 16, "name": "작은 악마 피즈", "chromas": true}, {"id": "105025", "num": 25, "name": "프레스티지 피즈멍 (2022)", "chromas": false}, {"id": "105026", "num": 26, "name": "우주비행사 피즈", "chromas": true}, {"id": "105035", "num": 35, "name": "비의 인도자 피즈", "chromas": true}], "lore": "바다와 육지에서 살 수 있는 요들족 피즈는 빌지워터를 둘러싼 산호초에서 살고 있다. 종종 미신을 믿는 선장들이 바다에 바치는 돈을 주워 돌려주기도 하지만, 아무리 신경질적인 뱃사람들이라도 피즈를 화나게 할 만큼 어리석지는 않다. 이 미끈거리는 생명체를 얕잡아본 사람들이 어떻게 되었는지에 대해 수많은 이야기가 전해지고 있기 때문이리라. 이따금 변덕스러운 바다 정령으로 오해받기도 하는 피즈는 바닷속 깊은 곳에 사는 괴물을 부릴 수 있으며, 적과 아군을 모두 놀리면서 즐거워한다고 한다.", "blurb": "바다와 육지에서 살 수 있는 요들족 피즈는 빌지워터를 둘러싼 산호초에서 살고 있다. 종종 미신을 믿는 선장들이 바다에 바치는 돈을 주워 돌려주기도 하지만, 아무리 신경질적인 뱃사람들이라도 피즈를 화나게 할 만큼 어리석지는 않다. 이 미끈거리는 생명체를 얕잡아본 사람들이 어떻게 되었는지에 대해 수많은 이야기가 전해지고 있기 때문이리라. 이따금 변덕스러운 바다 정령으로 오해받기도 하는 피즈는 바닷속 깊은 곳에 사는 괴물을 부릴 수 있으며, 적과 아군을...", "allytips": ["피즈는 유닛을 통과할 수 있으므로, 공격로 대치 단계에서 미니언 사이로 이동하며 심해석 삼지창의 기본 지속 효과를 입히고 몇 초 후 해당 스킬을 사용해 공격해 보세요.", "피즈의 궁극기 미끼 뿌리기는 적 또는 적의 예상 경로를 대상으로 할 수 있습니다.", "피즈의 스킬은 주문력을 기반으로 합니다. 적에 고화력 위주의 챔피언이 많다면 존야의 모래시계나 밴시의 장막을, 체력이 적더라도 생존할 자신이 있다면 리치베인이나 라바돈의 죽음모자를 구매하는 게 좋습니다."], "enemytips": ["피즈가 강화된 스킬을 사용한 후에는 몇 초 동안 기본 공격이 강력해지니 삼지창이 빛날 땐 거리를 유지하세요.", "스킬을 모두 사용할 수 있을 때의 피즈는 매우 잡기 어려우니 스킬을 미리 쓰도록 유도한 후 군중 제어기나 강력한 공격 스킬을 사용해 보세요."], "tags": ["Assassin", "Fighter"], "partype": "마나", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "성게 찌르기", "description": "피즈가 대상을 뚫고 지나가며 마법 피해를 줍니다. 적중 시 효과도 적용됩니다.", "tooltip": "피즈가 적을 관통하며 돌진해 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>에 <magicDamage>{{ qdamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FizzW", "name": "심해석 삼지창", "description": "피즈가 적을 공격하면 출혈 효과가 적용되어 몇 초 동안 마법 피해를 입힙니다. 피즈의 다음 기본 공격이 추가 피해를 입히며, 이후 공격이 잠시 동안 강화됩니다.", "tooltip": "<spellPassive>기본 지속 효과</spellPassive>: 피즈가 적에게 기본 공격을 가하면 출혈을 일으켜 {{ bleedduration }}초 동안 <magicDamage>{{ dotdamage }}의 마법 피해</magicDamage>를 입힙니다. <br /><br /><spellActive>사용 시</spellActive>: 피즈의 다음 기본 공격이 <magicDamage>{{ activedamage }}의 마법 피해</magicDamage>를 추가로 입힙니다. 이 공격으로 대상을 처치하면 피즈가 <scaleMana>{{ onkillmanarefund }}의 마나</scaleMana>를 돌려받고 이 스킬의 재사용 대기시간이 {{ onkillnewcooldown }}초로 감소합니다. 대상을 처치하지 못하면 피즈의 기본 공격이 {{ onhitbuffduration }}초 동안 <magicDamage>{{ onhitbuffdamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 지속 효과 피해량", "사용 시 피해량", "적중 시 피해량", "돌려받는 마나", "소모값 @AbilityResourceName@", "재사용 대기시간"], "effect": ["{{ dotbasedamage }} -> {{ dotbasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }} -> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }} -> {{ onkillmanarefundNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FizzE", "name": "장난치기 / 재간둥이", "description": "피즈가 자신의 창 위로 뛰어올라 목표로 지정할 수 없게 됩니다. 이 상태에서 피즈는 땅을 내리치거나 다시금 뛰어오를 수 있습니다.", "tooltip": "피즈가 삼지창 위에 서고 0.75초 동안 대상으로 지정할 수 없는 상태가 됩니다. 이후 근처 적에게 <magicDamage>{{ edamage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ slowamount*100 }}% <status>둔화</status>시킵니다. <br /><br />피즈가 대상으로 지정할 수 없는 상태에서 이 스킬을 <recast>재사용</recast>하면 다시 돌진하면서 효과가 일찍 끝나며 보다 작은 지역에 피해를 입히고 <status>둔화</status> 효과를 적용하지 않습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "소모값 @AbilityResourceName@", "피해량", "둔화"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "FizzR", "name": "미끼 뿌리기", "description": "피즈가 지정한 방향으로 마법 물고기를 풀어놓으면, 이 물고기를 건드리는 챔피언에게 들러붙어 이동 속도를 늦춥니다. 잠시 후 상어가 땅 밑에서 튀어나와 대상을 띄워올리며 주변에 있는 적은 옆으로 밀어냅니다. 상어에게 맞은 적은 모두 마법 피해를 입으며 속도가 느려집니다.", "tooltip": "피즈가 물고기를 풀어 처음으로 부딪힌 챔피언에게 붙게 합니다. 대상 챔피언은 <keywordStealth>절대 시야</keywordStealth>의 영향을 받으며 물고기가 대상에게 붙기 전 이동한 거리에 비례해 40%~80% <status>둔화</status>됩니다. <br /><br />{{ detonationtime }}초 후 상어가 튀어나와 물고기가 붙은 대상을 1초 동안 <status>공중으로 띄워 올리고</status> 다른 대상을 모두 <status>밀어내며</status> 물고기가 대상에게 붙기 전 이동한 거리에 비례해 <magicDamage>{{ smallsharkdamage }}~{{ bigsharkdamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "작은 상어 피해량", "중간 상어 피해량", "큰 상어 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ smalldamage }} -> {{ smalldamageNL }}", "{{ middamage }} -> {{ middamageNL }}", "{{ bigdamage }} -> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "영리한 싸움꾼", "description": "피즈는 유닛을 통과할 수 있으며 모든 공격으로부터 받는 피해가 고정된 수치만큼 감소합니다.", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}