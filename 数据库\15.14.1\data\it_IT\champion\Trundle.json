{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Trundle": {"id": "Trundle", "key": "48", "name": "Trundle", "title": "il re dei troll", "image": {"full": "Trundle.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "48000", "num": 0, "name": "default", "chromas": false}, {"id": "48001", "num": 1, "name": "Trundle Battitore", "chromas": false}, {"id": "48002", "num": 2, "name": "Trundle da Discarica", "chromas": false}, {"id": "48003", "num": 3, "name": "Trundle Tradizionale", "chromas": false}, {"id": "48004", "num": 4, "name": "<PERSON>mmissa<PERSON>", "chromas": false}, {"id": "48005", "num": 5, "name": "Trundle Distruttore del mondo", "chromas": false}, {"id": "48006", "num": 6, "name": "Trundle <PERSON>", "chromas": true}, {"id": "48012", "num": 12, "name": "Trundle Notte Inquietante", "chromas": true}, {"id": "48021", "num": 21, "name": "Trundle Fan dell'eSport", "chromas": true}], "lore": "Trundle è un massiccio e diabolico troll con un'indole particolarmente sadica e non c'è nulla che possa resistergli, neanche lo stesso <PERSON>. Estremamente territoriale, attacca chiunque osi entrare nel suo dominio. <PERSON><PERSON>, con la sua enorme mazza di <PERSON>ero G<PERSON>, congela il nemico e lo impala con colonne di ghiaccio, ridendo mentre l'avversario si dissangua nella tundra.", "blurb": "Trundle è un massiccio e diabolico troll con un'indole particolarmente sadica e non c'è nulla che possa resistergli, neanche lo stesso <PERSON>. Estremamente territoriale, attacca chiunque osi entrare nel suo dominio. Poi, con la sua enorme mazza di...", "allytips": ["Trundle è molto bravo a combattere nel suo Regno di ghiaccio. Cerca di attirarvi dentro i nemici.", "Usa Sottometti per indebolire un nemico resistente o per creare un bersaglio su cui la tua squadra si può concentrare.", "Morso è ideale per diminuire il danno fisico dei nemici: cerca di concentrarlo su coloro che infliggono più danni."], "enemytips": ["Trundle è molto potente nei combattimenti che rimangono localizzati in una zona. Cerca di attirarlo fuori dal suo Regno di ghiaccio.", "Cerca di uscire fuori subito dal suo Pilastro di ghiaccio, perché rallenta molto."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 6, "magic": 2, "difficulty": 5}, "stats": {"hp": 650, "hpperlevel": 110, "mp": 340, "mpperlevel": 45, "movespeed": 350, "armor": 37, "armorperlevel": 3.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 2.9, "attackspeed": 0.67}, "spells": [{"id": "TrundleTrollSmash", "name": "<PERSON><PERSON><PERSON>", "description": "Trundle morde il suo avversario, infliggendo danni, rallentandolo per un breve periodo e assorbendo una parte di attacco fisico.", "tooltip": "Il prossimo attacco di Trundle infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> e <status>rallenta</status> brevemente di un {{ slowamount*100 }}%. Poi Trundle ottiene <physicalDamage>{{ bonusad }} attacco fisico</physicalDamage> per {{ sapdebuffduration }} secondi e il nemico perde <physicalDamage>{{ sappedad*-1 }} attacco fisico</physicalDamage> per la stessa durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto attacco fisico", "Attacco fisico", "Attacco fisico rimosso"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ adscaling*100.000000 }}% -> {{ adscalingnl*100.000000 }}%", "{{ bonusad }} -> {{ bonusadNL }}", "{{ sappedad*-1.000000 }} -> {{ sappedadnl*-1.000000 }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "TrundleTrollSmash.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "trundledesecrate", "name": "Regno di ghiaccio", "description": "Trundle rende una posizione bersaglio parte del suo regno, guadagnando velocità d'attacco, velocità di movimento e guarigione aumentata da tutte le fonti finché è al suo interno.", "tooltip": "Trundle congela un'area per {{ e4 }} secondi. Mentre è al suo interno, ottiene <speed>{{ e1 }}% velocità di movimento</speed>, <attackSpeed>{{ e2 }}% velocità d'attacco</attackSpeed> e {{ e3 }}% di guarigione aumentata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Velocità d'attacco", "Ricarica"], "effect": ["{{ effect1amount*100.000000 }}% -> {{ effect1amountnl*100.000000 }}%", "{{ effect2amount*100.000000 }}% -> {{ effect2amountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [20, 28, 36, 44, 52], [30, 50, 70, 90, 110], [25, 25, 25, 25, 25], [8, 8, 8, 8, 8], [775, 775, 775, 775, 775], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/28/36/44/52", "30/50/70/90/110", "25", "8", "775", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "trundledesecrate.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TrundleCircle", "name": "Pilastro di ghiaccio", "description": "Trundle crea un pilastro di ghiaccio nel punto bersaglio, che diventa terreno inattraversabile e rallenta tutte le unità nemiche nelle vicinanze.", "tooltip": "Trundle crea un pilastro di ghiaccio per {{ e1 }} secondi e <status>respinge</status> brevemente i nemici su di esso, <status>rallentando</status> i nemici vicini del {{ e2 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Rallentamento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [21, 19.5, 18, 16.5, 15], "cooldownBurn": "21/19.5/18/16.5/15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [34, 38, 42, 46, 50], [360, 360, 360, 360, 360], [225, 225, 225, 225, 225], [150, 150, 150, 150, 150], [225, 225, 225, 225, 225], [400, 400, 400, 400, 400], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "34/38/42/46/50", "360", "225", "150", "225", "400", "60", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "TrundleCircle.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TrundlePain", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Trundle ruba immediatamente una percentuale della salute del bersaglio e una percentuale della sua armatura e resistenza magica. Nei successivi 4 secondi la sua quantità di salute, armatura e resistenza magica raddoppia.", "tooltip": "Trundle assorbe la forza vitale di un campione nemico, infliggendo <magicDamage>{{ totalpercenthpdamage }} della salute massima del bersaglio in danni magici</magicDamage> e rubando il <scaleArmor>{{ armormrshred*100 }}% dell'armatura</scaleArmor> e della <scaleMR>resistenza magica</scaleMR> nell'arco di {{ actualdurationofdrainbuff }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute assorbita", "Ricarica"], "effect": ["{{ percenthpdamage*100.000000 }}% -> {{ percenthpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "TrundlePain.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tributo al re", "description": "Ogni volta che un'unità nemica muore vicino a Trundle, quest'ultimo rigenera una percentuale della salute massima dell'unità morta.", "image": {"full": "Trundle_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}