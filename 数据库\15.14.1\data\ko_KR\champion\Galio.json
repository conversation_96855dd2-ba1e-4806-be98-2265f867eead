{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Galio": {"id": "<PERSON><PERSON><PERSON>", "key": "3", "name": "갈리오", "title": "위대한 석상", "image": {"full": "Galio.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "3000", "num": 0, "name": "default", "chromas": false}, {"id": "3001", "num": 1, "name": "마법 강화 갈리오", "chromas": false}, {"id": "3002", "num": 2, "name": "마법공학 갈리오", "chromas": false}, {"id": "3003", "num": 3, "name": "특공대 갈리오", "chromas": false}, {"id": "3004", "num": 4, "name": "지옥 수문장 갈리오", "chromas": false}, {"id": "3005", "num": 5, "name": "연미복 갈리오", "chromas": false}, {"id": "3006", "num": 6, "name": "꼬끼오", "chromas": true}, {"id": "3013", "num": 13, "name": "지옥의 갈리오", "chromas": true}, {"id": "3019", "num": 19, "name": "용 수호자 갈리오", "chromas": true}, {"id": "3028", "num": 28, "name": "신화 창조자 갈리오", "chromas": true}], "lore": "아스라한 빛의 도시 데마시아의 성문 밖, 거대한 석상 갈리오가 경계의 눈을 늦추지 않고 서 있다. 마법사의 공격으로부터 데마시아를 수호하기 위해 만들어진 갈리오는 강력한 마법의 힘이 그를 깨울 때까지 수십 년, 때로는 수백 년 동안 한자리에 미동도 없이 서있다. 일단 깨어나면 전투의 아찔한 스릴과 데마시아인들을 구한다는 자부심을 음미하며 1분 1초도 허투루 쓰는 법이 없다. 그러나 그가 쟁취한 승리의 향기는 결코 달콤하지만은 않다. 아이러니하게도 그가 물리친 마법의 힘이 그에게 생명을 준 원천이기에 전쟁을 승리로 장식한 후에는 다시 깊은 잠으로 빠져든다.", "blurb": "아스라한 빛의 도시 데마시아의 성문 밖, 거대한 석상 갈리오가 경계의 눈을 늦추지 않고 서 있다. 마법사의 공격으로부터 데마시아를 수호하기 위해 만들어진 갈리오는 강력한 마법의 힘이 그를 깨울 때까지 수십 년, 때로는 수백 년 동안 한자리에 미동도 없이 서있다. 일단 깨어나면 전투의 아찔한 스릴과 데마시아인들을 구한다는 자부심을 음미하며 1분 1초도 허투루 쓰는 법이 없다. 그러나 그가 쟁취한 승리의 향기는 결코 달콤하지만은 않다. 아이러니하게도...", "allytips": ["군중 제어 스킬에 영향을 받고 있는 상태라도 듀란드의 방패를 재사용할 수 있습니다.", "영웅출현은 미니맵의 아군 아이콘에도 사용할 수 있습니다.", "정의의 주먹 사용 시 잠깐 뒤로 물러나는 것을 이용해 적의 스킬을 피할 수도 있습니다."], "enemytips": ["갈리오는 듀란드의 방패로 방어 태세에 돌입하면 움직임이 느려집니다.", "갈리오가 영웅출현 사용 시, 도약하기 전까지 스킬을 방해할 수 있습니다.", "갈리오는 정의의 주먹으로 지형을 넘을 수 없습니다."], "tags": ["Tank", "Mage"], "partype": "마나", "info": {"attack": 1, "defense": 10, "magic": 6, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 126, "mp": 410, "mpperlevel": 40, "movespeed": 340, "armor": 24, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 9.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "GalioQ", "name": "전장의 돌풍", "description": "갈리오가 두 개의 돌풍을 발사해 지속 피해를 입히는 거대한 소용돌이를 만듭니다.", "tooltip": "갈리오가 두 개의 돌풍을 발사해 각각 <magicDamage>{{ qmissiledamage }}의 마법 피해</magicDamage>를 입힙니다. 두 돌풍이 합쳐지면 소용돌이가 일어나 {{ superqduration }}초 동안 <magicDamage>최대 체력의 {{ percentsuperqdamagett }}%에 해당하는 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["돌풍 피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "GalioQ.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GalioW", "name": "듀란드의 방패", "description": "갈리오가 방어 태세를 갖추며 움직임이 느려집니다. 스킬을 다시 사용하면 근처의 적을 모두 도발하고 피해를 줍니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 갈리오가 {{ passiveshieldooctimer }}초 동안 피해를 입지 않으면 <shield>{{ totalpassiveshield }}의 마법 피해를 흡수하는 보호막</shield>을 얻습니다.<br /><br /><charge>충전 시작 시:</charge> 갈리오가 받는 마법 피해가 {{ magicdamagereduction }}, 받는 물리 피해가 {{ physicaldamagereduction }} 감소하며 {{ e3 }}% <status>둔화</status>됩니다.<br /><br /><release>발사 시:</release> {{ e4 }}~{{ e7 }}초 동안 적 챔피언들을 <status>도발</status>하고 <magicDamage>{{ mintotaldamage }}</magicDamage>~<magicDamage>{{ maxtotaldamage }}의 마법 피해</magicDamage>를 입히며, 피해량 감소 효과가 {{ e8 }}초 추가됩니다. 도발 사거리 및 지속시간과 피해량은 충전 시간에 비례합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량 %", "마법 피해량 감소", "물리 피해량 감소", "최대 피해량", "재사용 대기시간"], "effect": ["{{ passiveshieldhealthratio*100.000000 }}% -> {{ passiveshieldhealthrationl*100.000000 }}%", "{{ e1 }}% -> {{ e1NL }}%", "{{ effect1amount*0.500000 }}% -> {{ effect1amountnl*0.500000 }}%", "{{ maximumwbasedamage }} -> {{ maximumwbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [2, 2, 2, 2, 2], [15, 15, 15, 15, 15], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [2, 2, 2, 2, 2], [1.25, 1.25, 1.25, 1.25, 1.25], [4, 4, 4, 4, 4]], "effectBurn": [null, "25/30/35/40/45", "2", "15", "0.5", "0", "1", "1.5", "2", "1.25", "4"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "GalioW.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GalioE", "name": "정의의 주먹", "description": "갈리오가 잠시 뒤로 물러났다가 돌진하며 처음으로 충돌한 적 챔피언을 공중으로 띄웁니다.", "tooltip": "갈리오가 전방으로 돌진해 처음 적중한 적 챔피언을 {{ knockupduration }}초 동안 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 돌진 경로에 있는 다른 적은 모두 <magicDamage>{{ pvedamage }}의 마법 피해</magicDamage>를 입습니다.<br /><br />갈리오의 돌진은 지형에 부딪히면 멈춥니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "GalioE.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GalioR", "name": "영웅출현", "description": "갈리오가 아군이 있는 위치를 착지 지점으로 정해, 해당 지점 주변의 모든 아군에게 마법 보호막을 씌웁니다. 잠시 후 해당 지역에 착지하여 근처의 적을 공중으로 띄웁니다.", "tooltip": "갈리오가 아군 챔피언의 위치를 착지 지점으로 정해, 해당 지점 주변의 모든 아군 챔피언에게 {{ temporarywshieldduration }}초 동안 <spellName>듀란드의 방패</spellName> 기본 지속 효과 <shield>보호막</shield>을 씌웁니다. 이후 착지 지점으로 날아갑니다.<br /><br />착지 시 {{ stundurationouter }}초 동안 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "사거리", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [4000, 4750, 5500], "rangeBurn": "4000/4750/5500", "image": {"full": "GalioR.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "석상의 강타", "description": "몇 초마다 갈리오의 다음 기본 공격이 일정 영역에 추가 마법 피해를 입힙니다.", "image": {"full": "Galio_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}