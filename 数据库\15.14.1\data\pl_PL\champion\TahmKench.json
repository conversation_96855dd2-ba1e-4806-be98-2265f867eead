{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TahmKench": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "key": "223", "name": "<PERSON><PERSON>", "title": "Rzeczny Król", "image": {"full": "TahmKench.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "223000", "num": 0, "name": "default", "chromas": false}, {"id": "223001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "223002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "223003", "num": 3, "name": "Cesarz <PERSON>", "chromas": true}, {"id": "223011", "num": 11, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "223020", "num": 20, "name": "Tahm <PERSON>ch w Samo Południe", "chromas": false}, {"id": "223030", "num": 30, "name": "Tahm Kench ze Zwojów Shan Hai", "chromas": false}], "lore": "<PERSON>, znany pod wieloma innymi imionami, podr<PERSON><PERSON><PERSON>je drogami wodnymi Runeterry, karm<PERSON><PERSON><PERSON> swój niezaspokojony głód cierpieniem innych. Choć może się wydawać niezwykle czarujący i dumny, kroczy przez fizyczny świat jak włóczęga w poszukiwaniu niczego niepodejrzewających ofiar. Smagnięcie jego języka ogłuszy nawet ciężkozbrojnego wojownika z odległości tuzina kroków, a trafić do jego burczącego brzucha to jakby w<PERSON><PERSON><PERSON> do otchłani, z której niepodobna się wydostać.", "blurb": "<PERSON>, znany pod wieloma innymi imionami, podr<PERSON><PERSON><PERSON>je drogami wodnymi Runeterry, ka<PERSON><PERSON><PERSON><PERSON> swój niezaspokojony głód cierpieniem innych. Choć może się wydawać niezwykle czarujący i dumny, kroczy przez fizyczny świat jak włóczęga w poszukiwaniu...", "allytips": ["Twoim najważniejszym zadaniem jako wspierającego jest chronienie wrażliwych członków drużyny. Pamiętaj o zasięgu oraz czasie odnowienia Pożarcia w trakcie walki!", "Zastanów się dobrze, k<PERSON><PERSON> skorzystać z efektu użycia części Grubej Skóry. Czasami zmniejszenie nadchodzących obrażeń tarczą będzie korzy<PERSON>ne, a innym razem bardziej przyda się leczenie."], "enemytips": ["<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON> używa tarczy z Grubej Skóry, to p<PERSON><PERSON><PERSON>j, że zrezygnował właśnie ze sporej ilości leczenia. Nie będzie mógł też kumulować szarego zdrowia, dopóki Gruba Skóra się nie odnowi. Wykorzystaj to na swoją korzyść!", "Uważaj na Nur w Otchłań Kencha — możesz przerwać ładowanie tej umiejętności unieruchamiającymi efektami kontroli tłumu."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 325, "mpperlevel": 50, "movespeed": 335, "armor": 39, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 3.2, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "TahmKenchQ", "name": "Smagnięcie Językiem", "description": "<PERSON><PERSON> smaga swoim jęzorem, zadaj<PERSON>c obrażenia i spowalniając pierwszą trafioną jednostkę. Ponadto leczy się, jeśli trafi wrogiego bohatera.<br><br>Na<PERSON><PERSON><PERSON>ad<PERSON> <spellName>Smakosza</spellName> na wrogich bohaterów. Je<PERSON><PERSON> bohater ma już na sobie 3 ładunki <spellName>Smakosza</spellName>, zost<PERSON>e ogł<PERSON>, a ładunki zużyją się.", "tooltip": "Zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> pierwszemu trafionemu wrogowi i <status>spowalnia</status> go o {{ slowamount*100 }}% na {{ slowduration }} sek. <br /><br />Jeś<PERSON> trafiony zostanie bohater, Ta<PERSON> uleczy się o <healing>{{ baseheal }} pkt. + {{ percenthealthhealing*100 }}% swojego brakującego zdrowia</healing> i nałoży ładunek umiejętności biernej <spellName>Smakosz</spellName>, zadaj<PERSON>c dodatkowo <magicDamage>{{ spell.tahmkenchpassive:totaldamage }} pkt. obrażeń magicznych</magicDamage>. Jeśli ten bohater miał już na sobie 3 ładunki umiejętności biernej <spellName>Smakosz</spellName>, zostanie również <status>ogłuszony</status> na {{ stunduration }} sek., a ładunki zostaną zużyte.<br /><br />Użyj <span class=\"color0bf7de\">Pożarcia</span>, gdy język jest w locie, aby po trafieniu wrogich bohaterów, którzy mają już na sobie 3 ładunki umiejętności biernej <spellName>Smakosz</spellName>, pożreć ich z daleka.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe", "Leczenie", "Procentowe przywracanie brakującego zdrowia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseheal }} -> {{ basehealNL }}", "{{ percenthealthhealing*100.000000 }}% -> {{ percenthealthhealingnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 46, 42, 38, 34], "costBurn": "50/46/42/38/34", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TahmKenchQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TahmKenchW", "name": "Nur w Otchłań", "description": "Tahm Kench nurkuje i ponownie pojawia się w wybranym miejscu, zadając obrażenia oraz podrzucając wszystkich wrogów znajdujących się na danym obszarze.", "tooltip": "Tahm Kench nurkuje i ponownie pojawia się w wybranym miejscu, zadają<PERSON> <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> oraz <status>podrzucając</status> wszystkich wrogów znajdujących się na danym obszarze na {{ knockupduration }} sek. Trafienie co najmniej jednego wrogiego bohatera zmniejsza czas odnowienia i koszt <scaleMana>many</scaleMana> o {{ champrefund*100 }}%. <br /><br />So<PERSON><PERSON><PERSON>y, na których rzucone zostało <span class=\"color0bf7de\">Pożarcie</span>, mogą zabrać się razem z nim, a także wyskoczyć wcześniej.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Obrażenia", "<PERSON><PERSON><PERSON><PERSON>", "Koszt (@AbilityResourceName@)", "<PERSON><PERSON>rot c<PERSON>u odnowienia"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cost }} -> {{ costNL }}", "{{ champrefund*100.000000 }}% -> {{ champrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 75, 90, 105, 120], "costBurn": "60/75/90/105/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1050, 1100, 1150, 1200], "rangeBurn": "1000/1050/1100/1150/1200", "image": {"full": "TahmKenchW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TahmKenchE", "name": "Gruba Skóra", "description": "<passive><PERSON><PERSON><PERSON>:</passive> <PERSON><PERSON> przechowuje procent otrzymywanych obrażeń i leczy się na jego podstawie, kiedy przebywa poza walką.<br><br><active>Użycie:</active> Zamienia całe przechowane obrażenia w tymczasową tarczę.", "tooltip": "<passive>Biernie:</passive> <PERSON><PERSON><PERSON><PERSON> równa {{ greyhealthratio*100 }}% obraż<PERSON><PERSON> otrzymywanych przez Tahma <PERSON> jest przechowywana w jego <spellName>G<PERSON><PERSON><PERSON></spellName>. Zost<PERSON><PERSON> ona zwiększona do {{ greyhealthratioenhanced*100 }}%, jeśli w pobliżu znajduje się co najmniej {{ enhancedthreshold }} wrogich bohaterów. Jeś<PERSON> nie otrzymał obrażeń w ciągu {{ ooctimer }} sek., <spellName>Gruba Skóra</spellName> zostaje szybko zu<PERSON>, by ul<PERSON><PERSON><PERSON> go o {{ greyhealthhealingratio }} jej warto<PERSON>.<br /><br /><active>Użycie:</active> Zamienia obrażenia przechowane w <spellName>Grubej Skórze</spellName> w <shield>tarczę</shield> utr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się przez {{ shieldduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% obrażeń w Grubej Skórze", "Wzmocniony % obrażeń w Grubej Skórze"], "effect": ["{{ greyhealthratio*100.000000 }}% -> {{ greyhealthrationl*100.000000 }}%", "{{ greyhealthratioenhanced*100.000000 }}% -> {{ greyhealthratioenhancednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [2400, 2400, 2400, 2400, 2400], "rangeBurn": "2400", "image": {"full": "TahmKenchE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TahmKenchRWrapper", "name": "Pożarcie", "description": "<PERSON><PERSON> pożera bohatera na kilka sekund, z<PERSON><PERSON><PERSON><PERSON> mu obrażenia magiczne, jeśli jest on wrogiem, lub z<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mu tarczę, jeśli jest sojusznikiem.", "tooltip": "Tahm Kench pożera bohatera na kilka sekund. Umiej<PERSON><PERSON><PERSON><PERSON> może zosta<PERSON> <recast>użyta ponownie</recast>, by wyplu<PERSON> bohatera.<br /><br /><specialRules>Wrodzy bohaterowie:</specialRules> Wymagane 3 ładunki <spellName>Smakosza</spellName>. Zostają pożarci na maks. {{ enemyduration }} sek. i otrzymują <magicDamage>{{ basedamage }} pkt. (+{{ percenthpdamage }} ich maks. zdrowia) obrażeń magicznych</magicDamage>. Na czas trwania tego efektu Tahm Kench zostaje <status>spowolniony</status> o {{ slowamount*100 }}% i <keywordName>uziemiony</keywordName>.<br /><br /><specialRules>Sojuszniczy bohaterowie:</specialRules> Z<PERSON><PERSON><PERSON> pożarci na maks. {{ allyduration }} sek. oraz otrzymują <shield>{{ totalshield }} pkt. tarczy</shield>, która stopniowo zanika po wypluciu. Sojusznicy mogą również zdecydować się na wcześniejsze wyjście. W czasie trwania tego efektu Tahm Kench zostaje <status>uziemiony</status>, ale może użyć umiejętności <keywordName>Nur w Otchłań</keywordName> i zyskuje <speed>{{ allyspeedamount*100 }}% prędkości ruchu</speed> na {{ allyduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tarczy", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ datacooldown }} -> {{ datacooldownNL }}"]}, "maxrank": 3, "cooldown": [0, 0, 0], "cooldownBurn": "0", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. many", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "TahmKenchRWrapper.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ datamanacost }} pkt. many"}], "passive": {"name": "Smakosz", "description": "Tahm Kench korzysta ze swojego masywnego cielska, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> swo<PERSON> ataki, co zapewnia mu dodatkowe obrażenia zależne od jego całkowitego zdrowia. Zadawanie obrażeń wrogim bohaterom kumuluje ładunki <spellName>Smakosza</spellName>. Przy 3 ładunkach może użyć <spellName>Pożarcia</spellName> na wrogim bohaterze.", "image": {"full": "TahmKenchP.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}