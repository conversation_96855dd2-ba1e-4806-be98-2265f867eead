{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rengar": {"id": "<PERSON><PERSON>", "key": "107", "name": "<PERSON><PERSON>", "title": "der stolze Jäger", "image": {"full": "Rengar.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "107000", "num": 0, "name": "default", "chromas": false}, {"id": "107001", "num": 1, "name": "Kopfjäger-Rengar", "chromas": true}, {"id": "107002", "num": 2, "name": "Nachtjäger-Rengar", "chromas": false}, {"id": "107003", "num": 3, "name": "SSW-Rengar", "chromas": false}, {"id": "107008", "num": 8, "name": "Mecha-<PERSON><PERSON>", "chromas": true}, {"id": "107015", "num": 15, "name": "Kuschelkätzchen Rengar", "chromas": true}, {"id": "107023", "num": 23, "name": "Sandwächter Rengar", "chromas": true}, {"id": "107030", "num": 30, "name": "<PERSON><PERSON><PERSON><PERSON> Rengar", "chromas": true}, {"id": "107040", "num": 40, "name": "Straßendämonen-Rengar", "chromas": true}], "lore": "<PERSON><PERSON> ist ein blutdurstiger vastayanischer Trophäenjäger, der für den Nervenkitzel lebt, gefährliche Kreaturen aufzuspüren und zu töten. Er durchstreift die Welt auf der Suche nach den furchterregendsten Bestien, die er finden kann. Dabei hält er vor allem nach Spuren von Kha<PERSON>Zix Ausschau, der Kreatur der Leere, die ihm ein Auge ausgekratzt hat. <PERSON><PERSON> stellt seiner Beute nicht nach, um zu fressen oder sich zu brüsten, sondern einzig und allein um der Schönheit der Jagd willen.", "blurb": "<PERSON><PERSON> ist ein blutdurstiger vastayanischer Trophäenjäger, der für den Nervenkitzel lebt, gefährliche Kreaturen aufzuspüren und zu töten. Er durchstreift die Welt auf der Suche nach den furchterregendsten Bestien, die er finden kann. Dabei hält er vor...", "allytips": ["Nutze Rengars ultimative Fähigkeit, um in Teamkämpfen und bei kleineren Gefechten wichtige Ziele aufzuspüren und diese auszuschalten.", "Ren<PERSON>s Stärke basiert sehr auf seinen verstärkten Fähigkeiten, die er im richtigen Moment einsetzt, also handle bedacht!", "<PERSON><PERSON> sic<PERSON>, dass dich dein Pfad wenn möglich durch hohes Gras führt, wenn du Gegner verfolgst, um Rengars passive Fähigkeit nutzen zu können."], "enemytips": ["<PERSON><PERSON> kann eine verstärkte Variante seiner Fähigkeiten einsetzen, wenn sein Wildheitsbalken voll ist. Versuche ihn anzugreifen, solange dies nicht geschehen ist.", "Rengars passive Fähigkeit erlaubt es ihm, aus hohem Gras zu springen. Vermeide Kämpfe im Umfeld von hohem Gras.", "<PERSON><PERSON> versieht den nächsten Champion mit einem Indikator, wenn er während seiner ultimativen Fähigkeit camoufliert ist."], "tags": ["Assassin", "Fighter"], "partype": "Wildheit", "info": {"attack": 7, "defense": 4, "magic": 2, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 4, "mpperlevel": 0, "movespeed": 345, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.667}, "spells": [{"id": "RengarQ", "name": "Brutalität", "description": "Rengars nächster Angriff spießt das Ziel auf und verursacht zusätzlichen Schaden.<br><br>Wildheitseffekt: Verursacht erhöhten Schaden und verleiht Angriffstempo.", "tooltip": "Rengar erhält für seine nächsten 2&nbsp;<PERSON>riff<PERSON> <attackSpeed>{{ e5 }}&nbsp;% Angriffstempo</attackSpeed>. Der erste Angriff trifft kritisch und verursacht <physicalDamage>{{ F4 }} (%i:scaleAD%%i:scaleCrit%) normalen Schaden</physicalDamage>.<br /><br /><keywordMajor>Maximale Wildheit:</keywordMajor> Der erste Angriff trifft kritisch mit <physicalDamage>{{ F5 }} (%i:scaleLevel%%i:scaleAD%%i:scaleCrit%) normalem Schaden</physicalDamage> und verleiht Rengar {{ e3 }}&nbsp;Sekunde(n) lang <attackSpeed>{{ empoweredqas }}&nbsp;Angriffstempo</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit Gesamtangriffsschaden", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [30, 60, 90, 120, 150], [100, 105, 110, 115, 120], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [40, 40, 40, 40, 40], [3, 3, 3, 3, 3], [0.2, 0.3, 0.4, 0.5, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/60/90/120/150", "100/105/110/115/120", "5", "5", "40", "3", "0.2/0.3/0.4/0.5/0.6", "0", "0", "0"], "vars": [], "costType": "Generiert 1&nbsp;Wildheit", "maxammo": "1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RengarQ.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "Generiert 1&nbsp;Wildheit"}, {"id": "RengarW", "name": "Kampfschrei", "description": "<PERSON><PERSON> brü<PERSON>t laut auf, fügt <PERSON> zu und heilt einen Teil seines kürzlich verlorenen Lebens.<br><br>Wildheitseffekt: Unterbricht außerdem Massenkontrolleffekte.", "tooltip": "<PERSON><PERSON> br<PERSON><PERSON>, <PERSON><PERSON><PERSON> in der Nähe <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>haden</magicDamage> zu und heilt sich um <healing>{{ damagepercentagehealed }}&nbsp;%</healing> des <PERSON>ens, den er in den letzten {{ e3 }}&nbsp;Sekunden erlitten hat.<br /><br /><keywordMajor>Maximale Wildheit:</keywordMajor> Verursacht <magicDamage>{{ totaldamageempowered }}&nbsp;magischen Schaden</magicDamage> und entfernt bestehende Massenkontrolleffekte von Rengar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50", "1.5", "1.5", "100", "0", "0", "0", "0", "0"], "vars": [], "costType": "Generiert 1&nbsp;Wildheit", "maxammo": "1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "RengarW.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "Generiert 1&nbsp;Wildheit"}, {"id": "RengarE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> wirft eine Bola auf sein <PERSON>, wodurch dieses kurzzeitig verlangsamt wird.<br><br>Wildheitseffekt: Hält das Ziel fest.", "tooltip": "<PERSON>gar wirft eine Bola, die <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht sowie den ersten getroffenen Gegner {{ e3 }}&nbsp;Sekunden lang aufdeckt und um {{ e2 }}&nbsp;% <status>verlangsamt</status>.<br /><br /><keywordMajor>Maximale Wildheit:</keywordMajor> Verursacht <physicalDamage>{{ totalempowereddamage }}&nbsp;normalen Schaden</physicalDamage> und <status>hält</status> den Gegner {{ e4 }}&nbsp;Sekunden lang fest.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [55, 100, 145, 190, 235], [30, 45, 60, 75, 90], [1.75, 1.75, 1.75, 1.75, 1.75], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "55/100/145/190/235", "30/45/60/75/90", "1.75", "1.75", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Generiert 1&nbsp;Wildheit", "maxammo": "1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RengarE.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "Generiert 1&nbsp;Wildheit"}, {"id": "RengarR", "name": "Jagdfieber", "description": "Ren<PERSON><PERSON>stinkte helfen ihm dabei, sich zu <font color='#cd90ee'>camouflieren</font>, und enthüllen den nächsten gegnerischen Champion in einem weiten Radius um ihn herum. Während Jagdfieber aktiv ist, erhält Rengar erhöhtes Lauftempo und kann auf den verfolgten Gegner springen, selbst wenn er gerade nicht im hohen Gras ist, wodurch er die Rüstung des Gegners verringert.", "tooltip": "<spellPassive>Passiv:</spellPassive> Rengar führt auch einen Sprungangriff aus, wenn er <keywordStealth>camoufliert</keywordStealth> ist.<br /><br /><spellActive>Aktiv:</spellActive> Rengar erhält {{ stealthduration }}&nbsp;Sekunden lang <speed>{{ stealthms }}&nbsp;% Lauftempo</speed> und <keywordStealth>absolute Sicht</keywordStealth> in einem kleinen Bereich um den nächstbefindlichen gegnerischen Champion herum.<br /><br />Nach {{ fadetime }}&nbsp;Sekunden <keywordStealth>camoufliert</keywordStealth> sich Rengar und kann Gegner auch außerhalb des hohen Grases anspringen. Springt Rengar den nächstbefindlichen Gegner an, fügt er ihm zusätzlich <physicalDamage>{{ bonusdamage }}&nbsp;normalen Schaden</physicalDamage> zu, verringert {{ armorshredduration }}&nbsp;Sekunden lang seine <scaleArmor>Rüstung</scaleArmor> um {{ armorshred }} und beendet diese Fähigkeit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rüstungsverringerung", "<PERSON><PERSON>", "Lauftempo", "Verfolgungsreichweite", "Abklingzeit"], "effect": ["{{ armorshred }} -> {{ armorshredNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ stealthms }}&nbsp;% -> {{ stealthmsNL }}&nbsp;%", "{{ selfvisionrange }} -> {{ selfvisionrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [2500, 3000, 3500], "rangeBurn": "2500/3000/3500", "image": {"full": "RengarR.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Verborgener Jäger", "description": "Während er sich im hohen Gras befindet, springt Rengar durch einen normalen Angriff auf sein Z<PERSON>.<br><br><PERSON><PERSON> erh<PERSON>lt jede<PERSON>, wenn er eine Fähigkeit einsetzt, Wildheit. Bei maximaler Wildheit wird seine nächste Fähigkeit verstärkt.<br><br>Kills von gegnerischen Champions gewähren Rengar Trophäen für seine <font color='#BBFFFF'>Knochenzahn-Halskette</font> und damit zusätzlichen Angriffsschaden.", "image": {"full": "Rengar_P.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}