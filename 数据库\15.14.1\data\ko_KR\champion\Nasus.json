{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nasus": {"id": "<PERSON><PERSON>", "key": "75", "name": "나서스", "title": "사막의 관리자", "image": {"full": "Nasus.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "75000", "num": 0, "name": "default", "chromas": false}, {"id": "75001", "num": 1, "name": "은하계 나서스", "chromas": false}, {"id": "75002", "num": 2, "name": "파라오 나서스", "chromas": false}, {"id": "75003", "num": 3, "name": "공포의 기사 나서스", "chromas": true}, {"id": "75004", "num": 4, "name": "라이엇 경찰견 나서스", "chromas": false}, {"id": "75005", "num": 5, "name": "지옥의 나서스", "chromas": false}, {"id": "75006", "num": 6, "name": "나서스 견공", "chromas": false}, {"id": "75010", "num": 10, "name": "파괴의 신 나서스", "chromas": false}, {"id": "75011", "num": 11, "name": "달빛 수호자 나서스", "chromas": true}, {"id": "75016", "num": 16, "name": "전투 기계 나서스", "chromas": true}, {"id": "75025", "num": 25, "name": "우주 그루브 나서스", "chromas": true}, {"id": "75035", "num": 35, "name": "중무장 거인 나서스", "chromas": true}, {"id": "75045", "num": 45, "name": "어둠의 인도자 나서스", "chromas": true}, {"id": "75054", "num": 54, "name": "운명의 창조자 나서스", "chromas": true}], "lore": "자칼의 머리를 한 위풍당당한 반인반수 형상의 초월체 나서스는 고대 슈리마의 영웅적인 인물이었다. 날카로운 지력을 소유한 그는 지식의 수호자이자 최고의 전략가로서 수세기 동안 슈리마 제국을 번영으로 인도했다. 제국의 몰락 이후엔 칩거에 들어가 전설 속의 존재가 되었다. 하지만 슈리마의 고대 도시가 되살아나면서, 제국의 몰락이 다시는 일어나지 않도록 하겠다는 다짐과 함께 세상 밖으로 나왔다.", "blurb": "자칼의 머리를 한 위풍당당한 반인반수 형상의 초월체 나서스는 고대 슈리마의 영웅적인 인물이었다. 날카로운 지력을 소유한 그는 지식의 수호자이자 최고의 전략가로서 수세기 동안 슈리마 제국을 번영으로 인도했다. 제국의 몰락 이후엔 칩거에 들어가 전설 속의 존재가 되었다. 하지만 슈리마의 고대 도시가 되살아나면서, 제국의 몰락이 다시는 일어나지 않도록 하겠다는 다짐과 함께 세상 밖으로 나왔다.", "allytips": ["게임 초반부터 흡수의 일격으로 꾸준히 미니언을 처치하면 게임 후반에 큰 위력을 발휘할 수 있습니다.", "공격로에 혼자 있다면 영혼의 불길로 미니언들을 편하게 사냥할 수 있습니다. 하지만 동료와 같이 있다면 자칫 적 공격로로 너무 깊숙이 들어가게 되므로 좋지 않은 선택입니다. 흡수의 일격으로 마지막 일격을 날릴지 영혼의 불길로 미니언을 광역 사냥할 것인지 적절히 선택하십시오.", "방어력이 낮다면 궁극기를 사용한 상태에서도 적은 당신을 노릴 것입니다. 물리 공격 아이템 위주로 키울 때도 생존력을 늘려줄 아이템을 구매해 두십시오."], "enemytips": ["궁극기로 변신한 나서스는 다른 챔피언들보다 훨씬 강력합니다. 유리한 상황에서만 나서스와 맞서십시오.", "최고 레벨의 쇠약 스킬은 기본 공격 위주의 챔피언에게 매우 치명적이므로 혼자 싸우지 마십시오.", "나서스는 원거리 공격으로 공략할 수 있습니다. 나서스의 체력이 꽉 차 있을 때는 되도록 덤비지 마십시오."], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 7, "defense": 5, "magic": 6, "difficulty": 6}, "stats": {"hp": 631, "hpperlevel": 104, "mp": 326, "mpperlevel": 62, "movespeed": 350, "armor": 34, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.45, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 67, "attackdamageperlevel": 4, "attackspeedperlevel": 3.48, "attackspeed": 0.638}, "spells": [{"id": "NasusQ", "name": "흡수의 일격", "description": "나서스가 적을 공격하여 피해를 입힙니다. 흡수의 일격으로 대상을 처치할 경우 흡수의 일격 스킬이 강화됩니다.", "tooltip": "나서스의 다음 기본 공격이 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입힙니다. 이 스킬로 적을 처치하면 영구적으로 피해량이 {{ basicstacks }}만큼 증가하고 챔피언, 대형 미니언, 대형 정글 몬스터를 대상으로는 {{ bigstacks }}만큼 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "재사용 대기시간"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7.5, 6.5, 5.5, 4.5, 3.5], "cooldownBurn": "7.5/6.5/5.5/4.5/3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [255, 255, 255, 255, 255], "rangeBurn": "255", "image": {"full": "NasusQ.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NasusW", "name": "쇠약", "description": "나서스가 적 챔피언의 노화를 촉진시켜 이동 속도와 공격 속도를 점진적으로 늦춥니다.", "tooltip": "나서스가 챔피언의 노화를 촉진시켜 {{ slowbase }}% <status>둔화</status>시킵니다. 둔화 효과는 {{ duration }}초 동안 최대 {{ maxslowtooltiponly }}%까지 증가합니다. 대상의 공격 속도는 <status>둔화</status> 효과의 {{ attackspeedslowmult*100 }}%만큼 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최대 둔화", "재사용 대기시간"], "effect": ["{{ maxslowtooltiponly }}% -> {{ maxslowtooltiponlyNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "NasusW.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NasusE", "name": "영혼의 불길", "description": "나서스가 영혼의 불꽃을 일으켜 해당 지역에 서 있는 적에게 피해를 입히고 방어력을 낮춥니다.", "tooltip": "나서스가 영혼의 불길로 <magicDamage>{{ initialdamage }}의 마법 피해</magicDamage>를 입힙니다. 해당 지역 내 적은 방어력이 <scaleArmor>{{ e2 }}%</scaleArmor>만큼 감소하고 {{ e3 }}초 동안 <magicDamage>{{ totaldotdamage }}의 마법 피해</magicDamage>를 입습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최초 피해량", "초당 피해량", "방어 감소 %", "소모값 @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [10, 16, 22, 28, 34], [30, 35, 40, 45, 50], [5, 5, 5, 5, 5], [50, 80, 110, 140, 170], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/16/22/28/34", "30/35/40/45/50", "5", "50/80/110/140/170", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NasusE.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NasusR", "name": "사막의 분노", "description": "나서스가 강력한 모래 폭풍을 소환하여 주변의 적을 강타합니다. 나서스는 폭풍이 부는 동안 체력과 공격 범위가 증가하며, 주변의 적에게 피해를 입히고, 흡수의 일격 재사용 대기시간이 감소하며, 추가 방어력과 마법 저항력을 얻습니다.", "tooltip": "나서스가 15초 동안 모래 폭풍 속에서 힘을 얻어 <healing>최대 체력이 {{ bonushealth }}</healing> 증가하고 <scaleArmor>방어력</scaleArmor>과 <scaleMR>마법 저항력</scaleMR>이 {{ initialresistgain }} 상승합니다.<br /><br />폭풍이 부는 동안 나서스는 매초 <magicDamage>주변 적이 보유한 최대 체력의 {{ damagecalc }}에 해당하는 마법 피해</magicDamage>를 입히며 <spellName>흡수의 일격</spellName> 재사용 대기시간이 {{ qcdr*100 }}% 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["추가 체력", "최대 체력 %", "추가 방어력 및 마법 저항력", "재사용 대기시간"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ aoedamagepercent*100.000000 }}% -> {{ aoedamagepercentnl*100.000000 }}%", "{{ initialresistgain }} -> {{ initialresistgainNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NasusR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "영혼의 포식자", "description": "나서스는 적의 영혼을 흡수하여 생명력 흡수 효과를 얻습니다.", "image": {"full": "Nasus_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}