{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ezreal": {"id": "Ezreal", "key": "81", "name": "Ezreal", "title": "the Prodigal Explorer", "image": {"full": "Ezreal.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "81000", "num": 0, "name": "default", "chromas": false}, {"id": "81001", "num": 1, "name": "Nottingham Ezreal", "chromas": false}, {"id": "81002", "num": 2, "name": "Striker <PERSON>", "chromas": false}, {"id": "81003", "num": 3, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "81004", "num": 4, "name": "Explorer <PERSON>", "chromas": false}, {"id": "81005", "num": 5, "name": "Pulsefire Ezreal", "chromas": false}, {"id": "81006", "num": 6, "name": "TPA Ezreal", "chromas": false}, {"id": "81007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "81008", "num": 8, "name": "Ace of Spades Ezreal", "chromas": false}, {"id": "81009", "num": 9, "name": "Arcade Ezreal", "chromas": false}, {"id": "81018", "num": 18, "name": "Star Guardian Ezreal", "chromas": false}, {"id": "81019", "num": 19, "name": "SSG Ezreal", "chromas": false}, {"id": "81020", "num": 20, "name": "Pajama Guardian Ezreal", "chromas": false}, {"id": "81021", "num": 21, "name": "Battle Academia Ezreal", "chromas": true}, {"id": "81022", "num": 22, "name": "PsyOps Ezreal", "chromas": false}, {"id": "81023", "num": 23, "name": "Prestige PsyOps Ezreal", "chromas": false}, {"id": "81025", "num": 25, "name": "Porcelain Protector Ezreal", "chromas": false}, {"id": "81033", "num": 33, "name": "Faerie Court Ezreal", "chromas": false}, {"id": "81043", "num": 43, "name": "HEARTSTEEL Ezreal", "chromas": false}, {"id": "81044", "num": 44, "name": "Heavenscale Ezreal", "chromas": true}, {"id": "81054", "num": 54, "name": "Prestige Heavenscale Ezreal", "chromas": false}, {"id": "81065", "num": 65, "name": "Masque of the Black Rose Ezreal", "chromas": false}], "lore": "<PERSON><PERSON><PERSON>, petualang gagah yang rupanya berbakat dalam seni magis, menyer<PERSON> katakomba yang telah lama hilang, berge<PERSON> dengan kutukan kuno, dan mengatasi rintangan yang tampak mustahil dilalui. Courage dan bravado dalam dirinya tak kenal batas. Dia lebih suka berimprovisasi untuk keluar dari situasi apa pun. Kadang mengandalkan akalnya, tetapi lebih sering mengandalkan gauntlet Shuriman mistisnya. Dia menggunakannya untuk melepaskan ledakan arcane. Satu hal yang pasti. Setiap E<PERSON><PERSON> muncul, akan selalu ada masalah di belakangnya. Atau di depannya. Mungkin di mana-mana.", "blurb": "<PERSON><PERSON><PERSON>, pet<PERSON><PERSON> gagah yang rupanya berbakat dalam seni magis, men<PERSON><PERSON> katakomba yang telah lama hilang, berge<PERSON> dengan kutukan kuno, dan mengatasi rintangan yang tampak mustahil dilalui. Courage dan bravado dalam dirinya tak kenal batas. Dia lebih...", "allytips": ["<PERSON><PERSON><PERSON>ft untuk membantu membidik skill shot <PERSON><PERSON><PERSON>.", "Kamu bisa memainkan Ezreal sebagai carry Attack Damage atau Ability Power tergantung build-nya.", "Kamu bisa menyesuaikan arah Trueshot Barrage untuk mengenai beberapa gelombang minion atau bahkan Monster."], "enemytips": ["Ezreal adalah champion yang sangat rapuh, jadi bawa per<PERSON>ungan ke hadapannya.", "Ezreal sangat bergantung pada skill shot, jadi pastikan untuk menjaga jarak dengan menempatkan minion di antara kalian.", "Mystic Shot <PERSON><PERSON><PERSON> e<PERSON>k on-hit term<PERSON>uk Crest of Cinders."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 2, "magic": 6, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 102, "mp": 375, "mpperlevel": 70, "movespeed": 325, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.65, "mpregen": 8.5, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "EzrealQ", "name": "Mystic Shot", "description": "Ezreal menembakkan bolt energi dengan damage yang mengurangi semua cooldown miliknya jika serangan itu mengenai unit musuh.", "tooltip": "Ezreal menembakkan bolt energi, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ damage }} physical damage</physicalDamage> pada musuh pertama yang kena dan mengurangi Cooldown Ability sebesar {{ cdrefund }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5.25, 5, 4.75, 4.5], "cooldownBurn": "5.5/5.25/5/4.75/4.5", "cost": [28, 31, 34, 37, 40], "costBurn": "28/31/34/37/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealQ.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealW", "name": "Essence Flux", "description": "Ezreal menembakkan orb yang menempel pada champion atau objektif yang pertama kena. <PERSON><PERSON> men<PERSON>ai musuh, orb Ezreal akan meledak dan mengh<PERSON>lkan damage.", "tooltip": "Ezreal menembakkan orb magis yang menempel pada champion, bangunan, atau monster jungle epik yang pertama kena selama {{ detonationtimeout }} detik. Jika Ezreal mengenai target dengan Serangan atau Ability, orb akan me<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damage }} magic damage</magicDamage>. Meledakkannya dengan Ability akan me-refund Mana cost Ability tersebut plus <scaleMana>{{ manareturn }} Mana</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON>sio Total AP"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ apratio*100.000000 }}%-> {{ aprationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "EzrealW.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealE", "name": "Arcane Shift", "description": "Ezreal melakukan teleport ke lokasi target di sekitar dan menembakkan homing bolt yang menghantam unit musuh terdekat. Memprioritaskan musuh yang terkena Essence Flux.", "tooltip": "Ezreal melakukan teleport, lalu menembakkan bolt ke musuh terdekat yang mengh<PERSON>lkan <magicDamage>{{ damage }} magic damage</magicDamage>. Bolt akan memprioritaskan musuh yang terkena <spellName>Essence Flux</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ e1 }}-> {{ e1NL }}"]}, "maxrank": 5, "cooldown": [26, 23, 20, 17, 14], "cooldownBurn": "26/23/20/17/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [80, 130, 180, 230, 280], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/130/180/230/280", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "EzrealE.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EzrealR", "name": "Trueshot Barrage", "description": "Ezreal mengumpulkan tenaga sebelum menembakkan barrage energi yang menghasilkan damage luar biasa pada tiap unit yang dilewatinya (damage berkurang untuk minion dan monster non-epik).", "tooltip": "Ezreal menembakkan energi luar biasa yang membusur dan menghasilkan <magicDamage>{{ damage }} magic damage</magicDamage>. <PERSON><PERSON> menghasilkan {{ damagereductionwaveclear.0*100 }}% damage pada minion dan monster jungle non-epik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EzrealR.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Rising Spell Force", "description": "Ezreal mendapatkan peningkatan Attack Speed tiap spell mi<PERSON><PERSON> berhasil mengenai target, di-stack hingga 5 kali.", "image": {"full": "Ezreal_RisingSpellForce.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}