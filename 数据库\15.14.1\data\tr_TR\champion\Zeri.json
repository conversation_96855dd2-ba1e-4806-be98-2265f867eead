{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zeri": {"id": "<PERSON><PERSON>", "key": "221", "name": "<PERSON><PERSON>", "title": "Zaun'un Kıvılcımı", "image": {"full": "Zeri.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "221000", "num": 0, "name": "default", "chromas": false}, {"id": "221001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "221010", "num": 10, "name": "Okyanus Şarkısı Zeri", "chromas": true}, {"id": "221019", "num": 19, "name": "Yücelik Yolu Zeri", "chromas": true}, {"id": "221028", "num": 28, "name": "Dehş<PERSON>", "chromas": true}, {"id": "221029", "num": 29, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Zaun'<PERSON>u i<PERSON><PERSON>i sınıfı bir aileden gelme, dikbaşlı ve heyecanlı bir genç kadın olan <PERSON>, sahip olduğu elektrik büyüsünü yoğunlaştırarak özel yapım silahını dolduruyor. Değişken güçleri duygularını, kıvılcımlarıysa hayata olan yıldırım hızında yaklaşımını yansıtıyor. Her<PERSON>e büyük bir şefkatle yaklaşan Zeri, her savaşında ailesine ve evine duyduğu sevgiden güç alıyor. Yardım etmeye fazla istekli olması bazen başına dert açsa da, Zeri'nin yürekten inandığı bir gerçek var: Halkının arkasında durursan halkın da senin arkanda durur.", "blurb": "Zaun'<PERSON>u i<PERSON><PERSON>i sınıfı bir a<PERSON>en gelme, dikbaşlı ve heyecanlı bir genç kadın olan <PERSON>, sahip olduğu elektrik büyüsünü yoğunlaştırarak özel yapım silahını dolduruyor. Değişken güçleri duygularını, kıvılcımlarıysa hayata olan yıldırım hızında yaklaşımını...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 250, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "ZeriQ", "name": "Taramalı", "description": "Taramalı isabet ettiği ilk rakibe saldırı hasarı veren 7 atış yapar. <PERSON><PERSON> yetenek, saldırı sayılır.", "tooltip": "<PERSON><PERSON> isabet ettiği ilk rakibe <physicalDamage>{{ activedamagethatcancrit }} Fiziksel Hasar</physicalDamage> veren {{ numberofmissiles }} atış yapar. Bu yetenek, saldırı sayılır. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Toplam SG Oranı"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "%{{ activeadratio*100.000000 }} -> %{{ activeadrationl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "ZeriQ.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "ZeriW", "name": "Ultraşok Lazeri", "description": "<PERSON>eri isabet ettiği ilk rakibi yavaşlatan ve ona hasar veren bir elektrik dalgası gönderir. Dalga bir duvara isabet ederse, yayılarak uzun menzilli bir lazere dönüşür.", "tooltip": "Zeri isabet ettiği ilk rakibe <physicalDamage>{{ totaldamage }} Fiziksel Hasar</physicalDamage> veren ve onu {{ slowduration }} saniyeliğine %{{ slowpercent*100 }} <status>yavaşlatan</status> bir elektrik dalgası gönderir.<br /><br />Dalga bir yüzey şekline isabet ederse yayılarak etkiyi alana uygulayan ve şampiyonlarla canavarlara kritik vuruş yapan bir lazere dönüşür.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "@AbilityResourceName@ Bedeli", "<PERSON><PERSON><PERSON>"], "effect": ["{{ damage }} -> {{ damageNL }}", "%{{ slowpercent*100.000000 }} -> %{{ slowpercentnl*100.000000 }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "ZeriW.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZeriE", "name": "Kıvılcım Akımı", "description": "Zeri kısa bir mesafe atılarak sonraki 3 Taramalı atışını rakipleri deşip geçecek şekilde güçlendirir. <PERSON><PERSON> ettiği her yüzey şeklinin üstünden atlar ve kayarak ilerler.", "tooltip": "<PERSON>eri kısa bir mesafe atılır ve temas ettiği her yüzey şeklinin üstünden atlayarak atılma menzilini büyük ölçüde arttırır. Sonraki {{ buffduration }} saniye boyunca <spellName>Taramalı</spellName> atışları hedefleri deşip geçerek ilkinden sonraki rakiplere %{{ pendamagepercent*100 }} Hasar, ilk rakibe de isabet halinde fazladan <magicDamage>{{ bonusdamagetotal }} Büyü Hasarı</magicDamage> verir. <br /><br />Bir rakip şampiyona saldırı veya yetenek isabet ettirmek bu yeteneğin bekleme süresini {{ cdreductionperhit }} saniye azaltır. Kritik vuruşlarsa bekleme süresini {{ critcdreductionperhit }} saniye azaltır.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Yüzde Hasarı", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Bedeli"], "effect": ["%{{ pendamagepercent*100.000000 }} -> %{{ pendamagepercentnl*100.000000 }}", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [90, 85, 80, 75, 70], "costBurn": "90/85/80/75/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "ZeriE.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZeriR", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Zeri bir elektrik patlaması oluşturur ve kendisini aşırı yükleyerek hasar artışı ve bir rakip şampiyona her saldırdığında yenilenen ve güçlenen, biriktirilebilir hareket hızı kazanır. Zeri aşırı yüklenmişken Taramalı atışları, daha hızlı ilerleyen ve rakiplerin arasında seken zincirleme yıldırımlar oluşturan bir üçlü atışa dönüşür.", "tooltip": "Zeri bir elektrik patlaması oluşturarak yakınındaki rakiplere <magicDamage>{{ totalactivedamage }} Büyü Hasarı</magicDamage> verir. Bu yetenek bir rakip şampiyona isabet ederse Zeri {{ rduration }} saniyeliğine <attackSpeed>%{{ baseaspercent*100 }} Saldırı Hızı</attackSpeed> ve <speed>%{{ basebonusms*100 }} Hareket Hızı</speed> kazanır. Bir rakip şampiyona saldırı veya yetenek isabet ettirmek etkinin süresini {{ maxhyperchargeduration }} saniye uzatıp bir Aşırı Yük biriktirir. Kritik vuruşlar fazladan 2 yük biriktirir. Her bir yük <speed>%{{ mspercent*100 }} Hareket Hızı</speed> sağlar.<br /><br />Bu süre boyunca <spellName>Taramalı</spellName> saldırıları daha hızlı bir üçlü atışa dönüşür ve yakındaki rakiplere zincirleme bir etkiyle <physicalDamage>{{ chainphysicaldamage }} Fiziksel Hasar</physicalDamage> verir.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "effect": ["{{ activedamage }} -> {{ activedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 75, 70], "cooldownBurn": "80/75/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ZeriR.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Canlı Batarya", "description": "Zeri'nin saldırıları büyü hasarı verir ve yetenek sayılır. Hareket etmek ve Taramalı yeteneğini kullanmak Zeri'nin kıvılcım cihazında enerji biriktirir. Tam yüke ulaştığında bir sonraki saldırısı ilave hasar verir.", "image": {"full": "ZeriP.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}