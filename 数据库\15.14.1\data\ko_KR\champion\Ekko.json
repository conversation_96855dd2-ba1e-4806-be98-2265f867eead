{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ekko": {"id": "<PERSON><PERSON><PERSON>", "key": "245", "name": "에코", "title": "시간을 달리는 소년", "image": {"full": "Ekko.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "245000", "num": 0, "name": "default", "chromas": false}, {"id": "245001", "num": 1, "name": "모래폭풍 에코", "chromas": true}, {"id": "245002", "num": 2, "name": "전학생 에코", "chromas": false}, {"id": "245003", "num": 3, "name": "프로젝트: 에코", "chromas": false}, {"id": "245011", "num": 11, "name": "SKT T1 에코", "chromas": false}, {"id": "245012", "num": 12, "name": "사탕 이리 주시지! 에코", "chromas": true}, {"id": "245019", "num": 19, "name": "True Damage 에코", "chromas": true}, {"id": "245028", "num": 28, "name": "펄스 건 에코", "chromas": true}, {"id": "245036", "num": 36, "name": "아케인 점화단 에코", "chromas": true}, {"id": "245045", "num": 45, "name": "별 수호자 에코", "chromas": true}, {"id": "245046", "num": 46, "name": "프레스티지 별 수호자 에코", "chromas": false}, {"id": "245056", "num": 56, "name": "빅히트 True Damage 에코", "chromas": false}, {"id": "245057", "num": 57, "name": "아케인 최후의 저항 에코", "chromas": true}], "lore": "자운 뒷골목 출신의 천재 소년 에코는 언제든 자신에게 유리하게 시간을 조작할 수 있다. 직접 발명한 Z 드라이브를 이용해 다양한 시공간에서 가능성의 갈래를 탐험하며 완벽한 순간을 포착하는 에코는 한 번의 시행착오도 없이 매번 불가능한 일을 해내는 것처럼 보인다. 누구보다도 자유로운 영혼을 가졌지만 소중한 사람들을 구하기 위해서라면 점화단과 함께 어떤 위험도 무릅쓴다.", "blurb": "자운 뒷골목 출신의 천재 소년 에코는 언제든 자신에게 유리하게 시간을 조작할 수 있다. 직접 발명한 Z 드라이브를 이용해 다양한 시공간에서 가능성의 갈래를 탐험하며 완벽한 순간을 포착하는 에코는 한 번의 시행착오도 없이 매번 불가능한 일을 해내는 것처럼 보인다. 누구보다도 자유로운 영혼을 가졌지만 소중한 사람들을 구하기 위해서라면 점화단과 함께 어떤 위험도 무릅쓴다.", "allytips": ["시공간 붕괴는 탈출기로도 좋지만, 공격적으로도 활용할 수 있는 강력한 스킬입니다. 이 스킬의 피해량을 잘 활용하세요.", "Z 드라이브 공진은 가능한 한 적 챔피언에게 발동시키세요. 이동 속도 상승 효과로 인해 탈출이 쉬워집니다.", "시간 도약은 에코의 다른 스킬과 연계하기 좋습니다. 이 스킬을 활용해 시간의 톱니바퀴를 두 번 적중시키거나 평행 시간 교차를 터뜨리기 좋은 위치를 잡으세요."], "enemytips": ["에코는 궁극기가 재사용 대기에 들어가면 현저하게 약해집니다. 에코의 뒤에 남는 자취를 보고 시공간 붕괴를 쓸 수 있는지 판단하세요.", "에코의 기절 지대는 3초간 충전한 뒤 작동됩니다. 어디에 설치될 지, 스킬을 시전할 때 생기는 이미지를 잘 보세요.", "시간의 톱니바퀴는 두 번째 맞을 때 첫 번째보다 더 큰 피해를 입습니다. 잘 보고 피하세요."], "tags": ["Assassin", "Mage"], "partype": "마나", "info": {"attack": 5, "defense": 3, "magic": 7, "difficulty": 8}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 280, "mpperlevel": 70, "movespeed": 340, "armor": 32, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.688}, "spells": [{"id": "EkkoQ", "name": "시간의 톱니바퀴", "description": "에코가 적 챔피언에게 맞으면 펼쳐져서 시간 왜곡 지대를 만들고, 지대 안에 있는 적 모두를 둔화시키며 피해를 입히는 시간 톱니바퀴를 던집니다. 잠시 후 톱니바퀴는 되감겨 에코에게 돌아오며, 경로 상에 있는 적에게 피해를 입힙니다.", "tooltip": "에코가 장치를 던져 <magicDamage>{{ initialdamage }}의 마법 피해</magicDamage>를 입힙니다. 장치는 챔피언에게 맞거나 사거리 끝에 도달하면 역장을 펼쳐 안에 있는 적을 {{ e2 }}% <status>둔화</status>시킵니다. 이후 에코가 장치를 불러들이며 <magicDamage>{{ recalldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "소모값 @AbilityResourceName@", "나갈 때의 피해량", "둔화", "돌아올 때의 피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ outgoingdamage }} -> {{ outgoingdamageNL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ returndamage }} -> {{ returndamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 85, 100, 115, 130], [40, 45, 50, 55, 60], [40, 65, 90, 115, 140], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [165, 165, 165, 165, 165], [1.75, 1.75, 1.75, 1.75, 1.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/85/100/115/130", "40/45/50/55/60", "40/65/90/115/140", "100", "0", "165", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "EkkoQ.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "EkkoW", "name": "평행 시간 교차", "description": "에코의 기본 공격이 체력이 낮은 적에게 추가 마법 피해를 입힙니다. 에코가 평행 시간 교차를 사용하면 시간의 균열을 열어 몇 초 후에 퍼져나가는 불안정한 특이점을 만들어내며, 이 안에 갇힌 적들을 둔화시킵니다. 에코가 특이점 안에 들어가면 자신은 보호막을 얻으며, 적들의 시간을 멈추며 기절시킵니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 에코의 기본 공격은 체력이 30% 미만인 적에게 <magicDamage>잃은 체력의 {{ missinghealthpercent }}에 해당하는 마법 피해</magicDamage>를 입힙니다.<br /><br /><spellActive>사용 시:</spellActive> 에코가 잠시 후 1.5초 동안 유지되는 시간의 구체를 발사하여 안에 있는 적을 {{ e0 }}% <status>둔화</status>시킵니다. 에코가 구체 안에 들어가면 구체를 폭발시켜 {{ e2 }}초 동안 <status>기절</status>시키고 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [375, 375, 375, 375, 375], [2.25, 2.25, 2.25, 2.25, 2.25], [3, 3, 3, 3, 3], [100, 120, 140, 160, 180], [150, 150, 150, 150, 150], [15, 15, 15, 15, 15], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [2, 2, 2, 2, 2], [40, 40, 40, 40, 40]], "effectBurn": [null, "375", "2.25", "3", "100/120/140/160/180", "150", "15", "1.5", "3", "2", "40"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1600, 1600, 1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "EkkoW.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "EkkoE", "name": "시간 도약", "description": "에코가 회피하며 굴러 Z 드라이브를 충전합니다. 다음 번 공격은 추가 피해를 입히며 시공간을 왜곡하여 에코가 대상 쪽으로 순간이동합니다.", "tooltip": "에코가 돌진합니다. 다음 기본 공격이 강화되어 사거리가 늘어나고 에코가 대상 쪽으로 순간이동하며 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [50, 75, 100, 125, 150], [350, 350, 350, 350, 350], [3, 3, 3, 3, 3], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "50/75/100/125/150", "350", "3", "300", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "EkkoE.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "EkkoR", "name": "시공간 붕괴", "description": "에코가 시공간을 붕괴시켜 대상으로 지정할 수 없게 되며 더 유리한 지점으로 시간을 되돌립니다. 몇 초 전 아무 때로나 돌아가며, 이 동안 입은 피해의 일부가 회복됩니다. 에코가 착지하는 지역 부근의 적들은 큰 피해를 입습니다.", "tooltip": "에코가 시간을 되돌려 경직 상태에 빠지며 4초 전에 있던 지점으로 되돌아가 근처의 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 <healing>체력을 {{ totalbaseheal }}</healing> 회복합니다. 회복량은 이 4초 동안 에코가 잃은 체력에 따라 증가하며, 잃은 체력 1%당 회복량이 {{ percenthealampperpercentmissinghealth }}% 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "회복량", "재사용 대기시간"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ flatheal }} -> {{ flathealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 80, 50], "cooldownBurn": "110/80/50", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [850, 850, 850], "rangeBurn": "850", "image": {"full": "EkkoR.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "Z 드라이브 공진", "description": "같은 대상에 대한 세 번째 기본 공격 및 스킬 공격마다 추가 마법 피해를 입힙니다. 대상이 챔피언일 경우, 에코의 이동 속도가 상승합니다.<br><br>", "image": {"full": "Ekko_P.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}