{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TwistedFate": {"id": "TwistedFate", "key": "4", "name": "Twisted Fate", "title": "der Meister der Karten", "image": {"full": "TwistedFate.png", "sprite": "champion4.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "4000", "num": 0, "name": "default", "chromas": false}, {"id": "4001", "num": 1, "name": "PAX-Twisted Fate", "chromas": false}, {"id": "4002", "num": 2, "name": "Herzbube-Twisted Fate", "chromas": false}, {"id": "4003", "num": 3, "name": "Der prächtige Twisted Fate", "chromas": false}, {"id": "4004", "num": 4, "name": "Tango-Twisted <PERSON>", "chromas": false}, {"id": "4005", "num": 5, "name": "High Noon-Twisted Fate", "chromas": false}, {"id": "4006", "num": 6, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "4007", "num": 7, "name": "Unterwelt-Twisted Fate", "chromas": false}, {"id": "4008", "num": 8, "name": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "4009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON> Twisted <PERSON>", "chromas": false}, {"id": "4010", "num": 10, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON> Fate", "chromas": false}, {"id": "4011", "num": 11, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "4013", "num": 13, "name": "Odyssee-<PERSON>wi<PERSON> Fate", "chromas": true}, {"id": "4023", "num": 23, "name": "DWG-Twisted Fate", "chromas": true}, {"id": "4025", "num": 25, "name": "<PERSON><PERSON><PERSON><PERSON>ra<PERSON>-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "4036", "num": 36, "name": "Weltraum-Groove-<PERSON>wi<PERSON>", "chromas": false}, {"id": "4045", "num": 45, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>wisted <PERSON>", "chromas": false}], "lore": "Twisted Fate ist ein berüchtigter Falschspieler und Schwindler, der sich durch die ganze bekannte Welt gespielt und geschmeichelt hat, was ihm die Feindseligkeit und die Bewunderung der Reichen sowie der Dummen eingebracht hat. Selten nimmt er etwas ernst, er beginnt jeden neuen Tag mit einem spöttischen Grinsen und stolziert unbekümmert los. Twisted Fate hat grundsätzlich immer irgendein Ass im Ärmel.", "blurb": "Twisted Fate ist ein berüchtigter Falschspieler und Schwindler, der sich durch die ganze bekannte Welt gespielt und geschmeichelt hat, was ihm die Feindseligkeit und die Bewunderung der Reichen sowie der Dummen eingebracht hat. Selten nimmt er etwas...", "allytips": ["Finde gemeinsam mit deinen Verbündeten den besten Zeitpunkt, um „Schicksal“ zu benutzen, und so die Gegner in einen Hinterhalt zu locken.", "Champions, die sich in den Schatten verbergen können, entkommen Kämpfen oft mit sehr wenig Leben. <PERSON><PERSON>e „Schicksal“, um getarnte Einheiten aufzudecken und ihnen den Rest zu geben.", "Twisted Fate ist als Angriffsschaden- oder Fähigkeitsstärke-DPS-Charakter einsetzbar, sodass er in viele verschiedene Teamkonstellationen hineinpasst."], "enemytips": ["Konzentriere dich im frühen Spiel darauf, der Fähigkeit „Joker“ auszuweichen, solange dein Champion noch nicht genug Leben hat, um den Treffer wegzustecken.", "<PERSON><PERSON> du wenig <PERSON> hast, ben<PERSON><PERSON> „Schicksal“ als Anhaltspunkt, um dich rechtzeitig in Sicherheit zu bringen. Dies gibt dir einen Vorsprung bei deiner Flucht und ermöglicht es dir, Hinterhalten zu entkommen."], "tags": ["Mage", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 2, "magic": 6, "difficulty": 9}, "stats": {"hp": 604, "hpperlevel": 108, "mp": 333, "mpperlevel": 39, "movespeed": 330, "armor": 24, "armorperlevel": 4.35, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "WildCards", "name": "Joker", "description": "Twisted Fate wirft 3 <PERSON><PERSON>, die jeder Einheit, die sie im Vorbeifliegen treffen, magischen Schaden zufügen.", "tooltip": "Twisted Fate wirft dre<PERSON>, die jeweils <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> verursachen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.75, 5.5, 5.25, 5], "cooldownBurn": "6/5.75/5.5/5.25/5", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "WildCards.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Pick<PERSON>ard", "name": "<PERSON><PERSON><PERSON> e<PERSON>", "description": "Twisted Fate wählt eine magische Karte aus seinem Kartenstapel und benutzt sie für seinen nächsten Angriff, wodurch dieser zusätzliche Effekte verursacht.", "tooltip": "Twisted Fate mischt seine <PERSON>. Dadurch kann er die Fähigkeit <recast>reaktivieren</recast>, um eine von drei Ka<PERSON> auszuwählen und seinen nächsten Angriff zu verstärken.<br /><li>Die blaue Karte verursacht <magicDamage>{{ bluedamage }}&nbsp;magischen Schaden</magicDamage> und stellt <scaleMana>{{ e6 }}&nbsp;<PERSON>a</scaleMana> wieder her.<li>Die rote Karte fügt nahen Gegnern <magicDamage>{{ reddamage }}</magicDamage>&nbsp;Sc<PERSON>en zu und <status>verlangsamt</status> sie 2,5&nbsp;Sekunden lang um {{ e2 }}&nbsp;%.<li>Die goldene Karte verursacht <magicDamage>{{ golddamage }}</magicDamage>&nbsp;Schaden und <status>betäubt</status> {{ e3 }}&nbsp;Sekunde(n) lang.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON>", "Blaue Karte: Manawiederherstellung", "Rote Karte: <PERSON><PERSON><PERSON>", "Rote Karte: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (%)", "Goldene Karte: <PERSON><PERSON><PERSON>", "Goldene Karte: Betäubungsdauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%", "{{ e5 }} -> {{ e5NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [40, 60, 80, 100, 120], [30, 35, 40, 45, 50], [1, 1.25, 1.5, 1.75, 2], [30, 45, 60, 75, 90], [15, 22.5, 30, 37.5, 45], [70, 90, 110, 130, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/60/80/100/120", "30/35/40/45/50", "1/1.25/1.5/1.75/2", "30/45/60/75/90", "15/22.5/30/37.5/45", "70/90/110/130/150", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200, 200, 200], "rangeBurn": "200", "image": {"full": "PickACard.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CardmasterStack", "name": "Kartenstapel", "description": "Twisted Fate verursacht bei jedem 4. <PERSON><PERSON> zusätzlichen Schaden. Außerdem wird sein Angriffstempo erhöht.", "tooltip": "<spellPassive>Passiv:</spellPassive> Twisted Fate erhält <attackSpeed>{{ attackspeedbonus }}&nbsp;% Angriffstempo</attackSpeed> und jeder vierte Angriff verursacht zusätzlich <magicDamage>{{ bonusdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zusätzlicher Schaden", "Angriffstempo"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ attackspeedbonus }}&nbsp;% -> {{ attackspeedbonusNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "4", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiv", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "CardmasterStack.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "Passiv"}, {"id": "Destiny", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Twisted Fate sagt das Schicksal seiner Gegner voraus, deckt dadurch alle gegnerischen Champions auf und ermöglicht so die Verwendung eines Portals, das Twisted Fate in 1,5 Sekunden an jede Zielposition teleportiert.", "tooltip": "Twisted Fate konzentriert sich auf seine <PERSON>, wodurch er {{ e1 }}&nbsp;<PERSON><PERSON><PERSON> lang <keywordStealth>absolute Sicht</keywordStealth> auf alle gegnerischen Champions auf der Karte erhält und die Fähigkeit <recast>reaktivieren</recast> kann.<br /><br /><recast>Reaktivierung</recast>: Twisted Fate teleportiert sich bis zu {{ e4 }}&nbsp;Einheiten weit weg.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ recastduration }} -> {{ recastdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [170, 140, 110], "cooldownBurn": "170/140/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [6, 8, 10], [0, 0, 0], [0, 0, 0], [5500, 5500, 5500], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "6/8/10", "0", "0", "5500", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "Destiny.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Gezinkte Würfel", "description": "<PERSON><PERSON> Twisted Fate eine Einheit tötet, erhält er 1 bis 6 zusätzliches Gold.", "image": {"full": "Cardmaster_SealFate.png", "sprite": "passive4.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}