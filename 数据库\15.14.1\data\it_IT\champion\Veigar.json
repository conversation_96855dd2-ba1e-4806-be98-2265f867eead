{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Veigar": {"id": "<PERSON><PERSON><PERSON>", "key": "45", "name": "<PERSON><PERSON><PERSON>", "title": "il piccolo genio del male", "image": {"full": "Veigar.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "45000", "num": 0, "name": "default", "chromas": false}, {"id": "45001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45003", "num": 3, "name": "Veigar il Grigio", "chromas": false}, {"id": "45004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "45006", "num": 6, "name": "Cattivissi<PERSON>", "chromas": false}, {"id": "45007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "45008", "num": 8, "name": "Veigar Boss Finale", "chromas": true}, {"id": "45009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "45013", "num": 13, "name": "Veigar del Bosco Antico", "chromas": true}, {"id": "45023", "num": 23, "name": "Veigar Cosplayer di Cornofurioso", "chromas": true}, {"id": "45032", "num": 32, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "45041", "num": 41, "name": "<PERSON><PERSON>gar <PERSON> di <PERSON>ri", "chromas": true}, {"id": "45051", "num": 51, "name": "Re Apeigar", "chromas": true}, {"id": "45060", "num": 60, "name": "Veigar Notte Inquietante", "chromas": true}], "lore": "Maestro della stregoneria oscura, Veigar ha accolto poteri ai quali pochi mortali osano avvicinarsi. Uno spirito libero di Bandle City, ha superato i limiti della magia yordle rivolgendosi a testi arcani rimasti nascosti per millenni. <PERSON><PERSON><PERSON>, creatura testarda affascinata dai misteri dell'universo, viene spesso sottovalutato, ma, nonostante si consideri malvagio, possiede un codice morale che lo porta a mettere in discussione le sue profonde motivazioni.", "blurb": "Maestro della stregoneria oscura, Veigar ha accolto poteri ai quali pochi mortali osano avvicinarsi. Uno spirito libero di Bandle City, ha superato i limiti della magia yordle rivolgendosi a testi arcani rimasti nascosti per millenni. Veigar, creatura...", "allytips": ["Usa Punto di non ritorno per aumentare le probabilità di successo con Materia oscura.", "Veigar è veramente affamato di mana e riduzione ricarica. Prova a comprare oggetti con queste statistiche per aumentare l'efficacia della tua passiva e di Colpo distruttivo.", "Veigar è molto fragile. È indispensabile scegliere almeno un incantesimo dell'evocatore difensivo."], "enemytips": ["Materia oscura infligge molti danni, ma può essere evitato. Fai attenzione al suono e all'indicatore visivo in modo da capire dove e quando l'abilità colpirà.", "Punto di non ritorno colpisce solo i nemici sui bordi. Se sei all'interno dell'abilità, puoi ancora muoverti e attaccare.", "L'abilità suprema di Veigar infligge danni aumentati in base alla tua salute mancante."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 580, "hpperlevel": 108, "mp": 490, "mpperlevel": 26, "movespeed": 340, "armor": 18, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.24, "attackspeed": 0.625}, "spells": [{"id": "VeigarBalefulStrike", "name": "Colpo di<PERSON>tti<PERSON>", "description": "Veigar spara un colpo di energia oscura che infligge danni magici ai primi due nemici colpiti. Le unità uccise dal colpo aumentano permanentemente il potere magico di Veigar.", "tooltip": "Veigar scatena un colpo di energia oscura che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai primi due nemici colpiti.<br /><br />Se Veigar uccide un nemico con questa abilità, ottiene {{ spell.veigarpassive:dqkillstacks }} carica/cariche di <keywordMajor>Male fenomenale</keywordMajor>. Minion grandi e mostri grandi conferiscono {{ spell.veigarpassive:dqkillstackslarge }} cariche.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto potere magico totale", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "VeigarBalefulStrike.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VeigarDarkMatter", "name": "Materia oscura", "description": "Veigar evoca una grande massa di materia oscura, facendola cadere dal cielo in una posizione bersaglio. <PERSON><PERSON> schia<PERSON>, la materia oscura infligge danni magici. Le cariche di Male fenomenale riducono la ricarica di Materia oscura.", "tooltip": "Veigar richiama della materia oscura dal cielo, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />Ogni {{ spell.veigarpassive:pstacksperdarkmattercdr }} cariche di <keywordMajor>Male fenomenale</keywordMajor> la ricarica di questa abilità si riduce di un {{ spell.veigarpassive:darkmattercdrincrement*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto potere magico totale", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [1.2, 1.2, 1.2, 1.2, 1.2], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "1.2", "8", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "VeigarDarkMatter.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VeigarEventHorizon", "name": "Punto di non ritorno", "description": "Veigar distorce i confini dello spazio, creando una gabbia che stordisce i nemici che la attraversano.", "tooltip": "Veigar distorce i confini dello spazio, creando una gabbia che <status>stordisce</status> per {{ e1 }} secondi i nemici che la attraversano. La gabbia permane per 3 secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> stordimento:", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18.5, 17, 15.5, 14], "cooldownBurn": "20/18.5/17/15.5/14", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [1.5, 1.75, 2, 2.25, 2.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5/1.75/2/2.25/2.5", "0.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "VeigarEventHorizon.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VeigarR", "name": "Esplosione primordiale", "description": "Scatena un attacco energetico contro il campione nemico bersaglio, infliggendo una grande quantità di danni magici che aumentano in base alla salute mancante del bersaglio.", "tooltip": "Veigar colpisce un campione nemico con la magia primordiale infliggendo tra <magicDamage>{{ mindamage }} e {{ maxdamage }} danni magici</magicDamage>, che aumentano in base alla salute mancante del bersaglio. I danni sono massimizzati contro i nemici con meno del 33% di salute.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto potere magico totale", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio*100.000000 }}% -> {{ aprationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "VeigarR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Potere malefico fenomenale", "description": "Veigar è il male più grande ad aver mai colpito il cuore di Runeterra, e lo sta diventando ancora di più! Colpire un campione nemico con un'abilità o effettuare un'uccisione conferisce a Veigar potere magico aumentato permanentemente.", "image": {"full": "VeigarEntropy.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}