{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Pajz<PERSON>", "description": "<PERSON><PERSON><PERSON> ka<PERSON>.", "tooltip": "{{ shieldduration }} másodpercre <shield>{{ shieldstrength }} er<PERSON><PERSON> p<PERSON></shield> biz<PERSON><PERSON><PERSON>.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerBoost": {"id": "SummonerBoost", "name": "Megtisztulás", "description": "Eltávolít a hősről minden b<PERSON><PERSON><PERSON><PERSON> hatást (kivéve a magatehetetlenséget és a levegőbe lökést) és idézői varázslatból származó gyengí<PERSON>, és kitartást ad.", "tooltip": "Megszüntet minden téged érő tömegir<PERSON><PERSON><PERSON><PERSON><PERSON> hatást (kivéve a <keyword>magatehetetlenséget</keyword> és a <keyword>levegőbe lökést</keyword>) és idézői varázslatból származó gyengí<PERSON> hatást, tov<PERSON><PERSON><PERSON> {{ tenacityduration }} másodpercre {{ tenacityvalue*100 }}% kitartást biztosít.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "Átvillanás", "description": "A hős a kurzor irányába teleportál egy rövid t<PERSON>.", "tooltip": "A hős a kurzor irányába teleportál egy rövid t<PERSON>.<br /><br />Ezután egy teljes körön át nem süthető el újra <rules>(egy kör a vásárlási fázisból és a harci fázisból áll).</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> megn<PERSON> a mozgási sebességed, amely ellenséges hősöktől elfutva még tovább nő.", "tooltip": "<keywordMajor>Aktív varázslat helye:</keywordMajor> az idézői varázslatot adó erősítések ezt a helyet foglalják el.<br /><br />{{ duration }} másodpercre <moveSpeed>{{ basems*100 }}% mozg<PERSON>i se<PERSON>get</moveSpeed> biz<PERSON><PERSON><PERSON>, amely {{ bonusmsperenemybehind*100 }}%-kal nő minden mögötted lévő ellenfél után.", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerDot": {"id": "SummonerDot", "name": "Égetés", "description": "Folyamatos tényleges sebzést okoz, és az időtartama során csökkennek az áldozatra ható gyógyító hatások.", "tooltip": "5 másodperc alatt <trueDamage>{{ tooltiptruedamagecalculation }} tényleges sebzést</trueDamage> okoz a megcélzott ellenséges hősnek, és <keyword>{{ grievousamount*100 }}%-os <PERSON><PERSON><PERSON><PERSON> seb</keyword> hatást alkalmaz erre az időtartamra.<br /><br /><keyword>Seb</keyword>: csökkenti a gyógyító és a regeneráló hatások hatékonyságát.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "Kifárasztás", "description": "Lelassítja a megcélzott ellenséges hőst, és csökkenti az okozott sebzését.", "tooltip": "{{ debuffduration }} másodpercre {{ slow }}%-kal <keyword>lelassítja</keyword> a megcélzott ellenséges hőst, és {{ damagereduction }}%-kal csökkenti az okozott sebzését.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerFlash": {"id": "SummonerFlash", "name": "Átvillanás", "description": "A hős a kurzor irányába teleportál egy rövid t<PERSON>.", "tooltip": "A hős a kurzor irányába teleportál egy rövid t<PERSON>.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerHaste": {"id": "SummonerHaste", "name": "Szellem", "description": "Mozgási sebességet biztosít, és az időtartama alatt a hős áthaladhat az egységeken.", "tooltip": "<speed>{{ movespeedmod }} mozg<PERSON>i se<PERSON>get</speed> bi<PERSON><PERSON><PERSON><PERSON>, é<PERSON> {{ duration }} másodpercre <keyword>anyagtalanná</keyword> v<PERSON>lsz.<br /><br /><keyword>Anyagtalanná válás</keyword>: az anyagtalanná vált egységek áthaladhatnak más egységeken.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerHeal": {"id": "SummonerHeal", "name": "Gyógyítás", "description": "Életerőt állí<PERSON> v<PERSON>, valamint megnöveli a hős és egy kijelölt szövetséges hős mozgási sebességét.", "tooltip": "Viss<PERSON><PERSON><PERSON><PERSON><PERSON> <healing>{{ totalheal }} <PERSON><PERSON><PERSON><PERSON><PERSON></healing>, és <speed>{{ movespeed*100 }}% mozgási sebességet</speed> ad {{ movespeedduration }} másodperc erejéig neked és a megcélzott szövetséges hősnek.<br /><br /><rules>Ha nincs konkrét célpontja, a hatótávolságán belül a legsúlyosabban sérült szövetséges hősre fog hatni.<br />A gyógyítás mértéke feleződik azoknál az egységeknél, akik nemrég Idézői gyógyításban részesültek.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "Megvilágosodás", "description": "Visszatölti a hős és a szövetséges hős manáját.", "tooltip": "Visszatölti a hős maximális man<PERSON>j<PERSON> {{ e1 }}%-át, és a közelben tartózkodó szövetségesei maximális manáj<PERSON>ak {{ e2 }}%-át.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "A Királyhoz!", "description": "Gyorsan a Porokirály színe elé <PERSON>.", "tooltip": "<span class=\"colorFFE076\">Passzív:</span> Am<PERSON>r eltalálsz egy ellenséges hőst egy poróval, a csapatod egy Porojelet kap. A 10. <PERSON><PERSON><PERSON><PERSON> begyűjtésekor a csapat megidézi a Porokirályt, aki az oldalukon fog harcolni. Amíg a Porokirály aktív, egyik csapat sem gyűjthet Porojeleket.<br /><br /><span class=\"colorFFE076\">Aktív:</span> Gyorsan a Porokirály mellé sietsz. Csak akkor használható, ha a te csapatod idézte meg a Porokirályt. <br /><br /><i><span class=\"colorFDD017\">„A porók az igazi közönségkedvencek. Mindenki más csak dísznek van.”</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "Porohajítás", "description": "<PERSON>gy porót hajítasz az ellenfeleidre. Ha eltalálsz valakit, gyo<PERSON>n odaronthatsz hozzá.", "tooltip": "Mess<PERSON><PERSON> hají<PERSON> egy por<PERSON>t, aki {{ f2 }} tényleges sebzést okoz az első eltalált ellenséges egységnek, amelyet <span class=\"coloree91d7\">Igazlátással</span> fel is fed.<br /><br />Ha sikerült eltalálni egy ellenfelet, a képesség 3 másodpercig ismét aktiválható. Ekkor az áldozathoz sietsz, további {{ f2 }} tényleges sebzést okozol neki, és {{ e4 }} másodperccel csökkented a következő Porohajítás töltési idejét.<br /><br />A poróktól nem védenek a varázspajzsok vagy a sz<PERSON>lfalak, mivel ők állatok, nem pedig varáz<PERSON>tok!<br /><br /><i><span class=\"colorFDD017\">„A porók aerodinamikája Runaterra páratlan csodája.”</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerSmite": {"id": "SummonerSmite", "name": "Sújtás", "description": "Tényleges sebzést okoz egy szörnynek vagy minionnak.", "tooltip": "<trueDamage>{{ smitebasedamage }} tényleges sebzést</trueDamage> okoz a megcélzott nagy szörnynek vagy ösvényes minionnak.<br /><br /><trueDamage>{{ firstpvpdamage }} tényleges sebzést</trueDamage> okoz a hősök kísérőinek.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> egy hógolyót hajít az ellenfél irányába. Megjelöli az első eltalált ellenfelet és Igazlátással felfedi. <PERSON> hő<PERSON>, aki <PERSON>, gyo<PERSON>n hozzás<PERSON>t.", "tooltip": "Messzire elhají<PERSON>z egy hó<PERSON>, amely {{ tooltipdamagetotal }} tényleges sebzést okoz az első eltalált ellenséges egységnek, majd <span class=\"coloree91d7\">Igazlátással</span> felfedi. Ha sikerült eltalálni egy ellenfelet, a képesség {{ e3 }}&nbsp;másodpercig ismét aktiválható. Ebben az esetben az Ütközéssel az áldozathoz siethetsz vele, és így további {{ tooltipdamagetotal }} tényleges sebzést okozhatsz. Az Ütközés elsütése {{ e4 }}%-kal csökkenti a Jel töltési idejét.<br /><br /><span class=\"colorFFFF00\">A Jel lövedékeit nem állítják meg a varázspajzsok vagy más, lövedékeket befolyásoló hatások.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> egy hógolyót hajít az ellenfél irányába. Megjelöli az első eltalált ellenfelet és Igazlátással felfedi. <PERSON> hő<PERSON>, aki <PERSON>, gyo<PERSON>n hozzás<PERSON>t.", "tooltip": "Messzire elhají<PERSON>z egy hó<PERSON>, amely {{ tooltipdamagetotal }} tényleges sebzést okoz az első eltalált ellenséges egységnek, majd <span class=\"coloree91d7\">Igazlátással</span> felfedi. Ha sikerült eltalálni egy ellenfelet, a képesség {{ e3 }}&nbsp;másodpercig ismét aktiválható. Ebben az esetben az Ütközéssel az áldozathoz siethetsz vele, és így további {{ tooltipdamagetotal }} tényleges sebzést okozhatsz. Az Ütközés elsütése {{ e4 }}%-kal csökkenti a Jel töltési idejét.<br /><br /><span class=\"colorFFFF00\">A Jel lövedékeit nem állítják meg a varázspajzsok vagy más, lövedékeket befolyásoló hatások.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "Teleport", "description": "<PERSON><PERSON><PERSON>s után megcélozhatatlanná válik, és egy szövetséges egységhez teleportál. Határtalan teleporttá fej<PERSON>k, ami j<PERSON> növeli a teleportálás sebességét. ", "tooltip": "{{ channelduration }} másodpercnyi felkészülés után <keyword>megcélozhatatlanná</keyword> v<PERSON><PERSON>, és a megcélzott szövetséges építményhez, minion<PERSON>z vagy őrszemhez teleportál. <br /><br />{{ upgrademinute }} perc elteltével Határtalan teleporttá fejlődik, ami jelent<PERSON>sen növeli a teleportálás sebességét.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Erre a helyre egy másik hős végső képessége kerül, amelyet a játék elején választhatsz ki. A végső képesség kiválasztására 30 másodperc áll rendelkezésre. Erre készülj fel előre!", "tooltip": "A kiválasztott végső idézői varázslat kerül a helyére.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> és Automatikus sújtás", "description": "Erre a helyre egy másik hős végső képessége kerül, és megkapod az Automatikus sújtás varázslatot. A végső képesség kiválasztására 30 másodperc áll rendelkezésre. Erre készülj fel előre!", "tooltip": "A végső idézői varázslat kerül a helyére.<br /><br />Megkapod az Automatikus sújtást. Az Automatikus sújtás kivégzi az Erősítőszörnyeket és a Hatalmas szövetséges szörnyeket, valamint a Suhanó su<PERSON>.<br /><br /><attention>Az Automatikus sújtásnak nincs töltési ideje.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}