{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "Velk<PERSON> parní golem", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "Rezav<PERSON>litzcrank", "chromas": false}, {"id": "53002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "53003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "53004", "num": 4, "name": "Blitzcrank z autodílny", "chromas": false}, {"id": "53005", "num": 5, "name": "Rozhodně ne Blitzcrank", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "<PERSON>ř<PERSON><PERSON><PERSON><PERSON> Blitzcrank", "chromas": false}, {"id": "53011", "num": 11, "name": "Bitevní boss <PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "53020", "num": 20, "name": "Temný kopiník Blitzcrank", "chromas": false}, {"id": "53021", "num": 21, "name": "Zářný kopiník Blitzcrank", "chromas": false}, {"id": "53022", "num": 22, "name": "Čarodějný kotel Blitzcrank", "chromas": true}, {"id": "53029", "num": 29, "name": "Blitz a Crank z Vesmírného rytmu", "chromas": true}, {"id": "53036", "num": 36, "name": "Vítěz<PERSON><PERSON>", "chromas": true}, {"id": "53047", "num": 47, "name": "Blitzcrank ze Zenitových her", "chromas": true}, {"id": "53056", "num": 56, "name": "Bzzzcrank", "chromas": true}], "lore": "Blitzcrank je ob<PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON> nezničitelný automaton ze Zaunu, kter<PERSON> byl původně stvořen k tomu, aby se staral o likvidaci nebezpečného odpadu. Svůj prvotní účel však považoval za příliš svazující, a tak sám sebe upravil tak, aby mohl co nejlépe sloužit křehkým lidem z Jímky. Blitzcrank zcela nezištně využívá svou sílu a odolnost k ochraně druhých a vystřelovací pěstí nebo výboji energie zneškodňuje každého, kdo dělá problémy.", "blurb": "Blitzcrank je ob<PERSON><PERSON>, t<PERSON><PERSON><PERSON><PERSON> nezničitelný automaton ze Zaunu, k<PERSON><PERSON> byl původně stvořen k tomu, aby se staral o likvidaci nebezpečného odpadu. Svůj prvotní účel však považoval za příliš svazující, a tak sám sebe upravil tak, aby mohl co nejlépe sloužit...", "allytips": ["Kombo 1-2-3 skládající se ze schopností Raketový úchop, <PERSON><PERSON><PERSON><PERSON> a <PERSON>atick<PERSON> pole dokáže osamoceného nepřítele zcela zničit.", "<PERSON><PERSON>ž <PERSON>crankovým úchopem přitáhneš protivníka do dosahu své věže a pak na něj zaú<PERSON><PERSON><PERSON>, vystřelí po něm následně několikrát i tvá věž."], "enemytips": ["Pokud Blitzcrankovi dochází zdraví, jeho pasivní schopnost Manová bariéra kolem něj vytvoří štít.", "<PERSON><PERSON> <PERSON> za poskoky, aby na tebe nemohl seslat Raketový úchop. Tato schopnost přitáhne pouze první nepřátelskou jednotku, na kterou narazí."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "Raketový úchop", "description": "Blitzcrank vystřelí svou pravou ruku, která popadne prvního protivníka, na kterého narazí. Způsobí mu tím poškození a přitáhne jej k sobě.", "tooltip": "Blitzcrank vystřelí svou pravačku, <status>p<PERSON><PERSON><PERSON><PERSON><PERSON></status> k sobě prvního zasaženého nepřítele a způsobí mu <magicDamage>{{ totaldamage }} bodů magického poš<PERSON>zení</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Poškození", "Přebíjecí doba"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "Overdrive", "name": "Turbo", "description": "Blitzcrank aktivuje turbodmychadlo, <PERSON><PERSON><PERSON><PERSON> se mu výrazně zvýší rychlost pohybu i útoků. Po skončení efektu je dočasně zpomalený.", "tooltip": "Blitzcrank se uvede do stavu superrychlosti, <PERSON><PERSON><PERSON><PERSON> na {{ duration }}&nbsp;sek. z<PERSON> <speed>{{ movespeedmod*100 }}%, postupně se snižuj<PERSON><PERSON><PERSON> bonus k rychlosti pohybu</speed> a <attackSpeed>{{ attackspeedmod*100 }}&nbsp;% k rychlosti <PERSON></attackSpeed>.<br /><br />Poté bude Blitzcrank na {{ slowduration }}&nbsp;sek. <status>zpomalený</status> o {{ movespeedmodreduction*100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rychlost pohybu", "Rychlost útoků"], "effect": ["{{ movespeedmod*100.000000 }} % -> {{ movespeedmodnl*100.000000 }} %", "{{ attackspeedmod*100.000000 }} % -> {{ attackspeedmodnl*100.000000 }} %"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "PowerFist", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Blitzcrank nabije svou <PERSON>, jeho následující útok tak způsobí dvojnásobné poškození a vyhodí cíl do vzduchu.", "tooltip": "Blitzcrank nabije svou pěst, tak<PERSON><PERSON> příštím útokem svůj cíl na {{ ccduration }}&nbsp;sek. <status>vyhod<PERSON> do vzduchu</status> a způsobí mu <physicalDamage>{{ totaldamage }} bod<PERSON> fyzického poš<PERSON>zení</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Přebíjecí doba"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "StaticField", "name": "<PERSON><PERSON><PERSON><PERSON> pole", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, na které Blitzcrank zaútočí, b<PERSON><PERSON> a po 1 sek. utrpí poškození elektrickým výbojem. Blitzcrank může tuto schopnost tak<PERSON> akt<PERSON>, čí<PERSON>ž z okolních nepřátel odstraní štíty, způsobí jim poškození a na chvíli je umlčí.", "tooltip": "<spellPassive>Pasivní efekt:</spellPassive> <PERSON><PERSON><PERSON> je tato schopnost k dispozici, jsou Blitzcrankovy pěsti nabité energií a označují nepř<PERSON>tele, na které zaútoč<PERSON>. Po uplynutí 1&nbsp;sekundy projde protivníky výboj, kter<PERSON> jim způsobí <magicDamage>{{ passivedamage }} bodů magického poškození</magicDamage>.<br /><br /><spellActive>Aktivace:</spellActive> Blitzcrank se nabije energií, <PERSON><PERSON><PERSON><PERSON> způsobí okolním nepřátelům <magicDamage>{{ activedamage }} bodů magického poškození</magicDamage> a na {{ silenceduration }}&nbsp;sek. je <status>umlčí</status>. Zároveň jim zničí štíty.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Základní pasivní poš<PERSON>zení", "Závislost pasivní schopnosti na síle schopností", "Základní aktivovatelné poškození", "Přebíjecí doba aktivovatelného efektu"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }} % -> {{ passiveaprationl*100.000000 }} %", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "<PERSON><PERSON> bari<PERSON>", "description": "<PERSON><PERSON>ž <PERSON>litzcrankovi povážlivě klesne zdraví, <PERSON><PERSON><PERSON><PERSON>, jeho<PERSON> síla se odvíjí od jeho many.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}