{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Annie": {"id": "<PERSON>", "key": "1", "name": "<PERSON>", "title": "Enfant des ténèbres", "image": {"full": "Annie.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "1000", "num": 0, "name": "default", "chromas": false}, {"id": "1001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "1002", "num": 2, "name": "<PERSON> chaperon rouge", "chromas": false}, {"id": "1003", "num": 3, "name": "<PERSON> au pays des merveilles", "chromas": false}, {"id": "1004", "num": 4, "name": "<PERSON> reine du bal", "chromas": false}, {"id": "1005", "num": 5, "name": "<PERSON>", "chromas": false}, {"id": "1006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "1007", "num": 7, "name": "<PERSON>", "chromas": false}, {"id": "1008", "num": 8, "name": "<PERSON> panda", "chromas": false}, {"id": "1009", "num": 9, "name": "<PERSON> cœur tendre", "chromas": false}, {"id": "1010", "num": 10, "name": "<PERSON>", "chromas": false}, {"id": "1011", "num": 11, "name": "Super Annie intergalactique", "chromas": false}, {"id": "1012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "1013", "num": 13, "name": "<PERSON> bête lunaire", "chromas": true}, {"id": "1022", "num": 22, "name": "Annie Café Chouchous", "chromas": false}, {"id": "1031", "num": 31, "name": "<PERSON> d'effroi", "chromas": false}, {"id": "1040", "num": 40, "name": "<PERSON> de l'hiver", "chromas": false}, {"id": "1050", "num": 50, "name": "Annie princesse du combat", "chromas": false}], "lore": "<PERSON><PERSON><PERSON>, incroyable<PERSON> p<PERSON><PERSON><PERSON>, <PERSON> est une enfant mage dotée d'extraordinaires pouvoirs de pyrokinésie. Même à l'ombre des montagnes du nord de Noxus, sa magie est un cas unique. Son affinité naturelle avec le feu se manifesta dès sa prime enfance à travers des explosions émotionnelles imprévisibles, même si elle apprit rapidement à contrôler ces éclats. Parmi ses jeux favoris, elle aime invoquer son bien-aimé ours en peluche, Tibbers, sous la forme d'un protecteur enflammé. Perdue dans l'innocence perpétuelle de l'enfance, <PERSON> sautille dans les forêts obscures, à la recherche de quelqu'un avec qui jouer.", "blurb": "<PERSON><PERSON><PERSON>, incroy<PERSON><PERSON> p<PERSON><PERSON><PERSON>, <PERSON> est une enfant mage dotée d'extraordinaires pouvoirs de pyrokinésie. Même à l'ombre des montagnes du nord de Noxus, sa magie est un cas unique. Son affinité naturelle avec le feu se manifesta dès sa prime...", "allytips": ["Conserver un effet d'étourdissement pour son ultime peut permettre un retournement de situation.", "Achever des sbires avec Désintégration permet à Annie de farmer facilement en début de partie.", "Bouclier en fusion est pratique pour préparer un sort étourdissant ; il est souvent profitable d'y consacrer au moins 1 point en début de partie."], "enemytips": ["L'ours d'Annie, <PERSON><PERSON><PERSON>, brûle les ennemis autour de lui. Gardez vos distances quand il est invoqué.", "Le sort d'invocateur Châtiment peut aider à abattre Tibbers.", "Prudence quand un petit tourbillon blanc entoure Annie : cela signifie qu'elle peut vous étourdir."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 10, "difficulty": 6}, "stats": {"hp": 560, "hpperlevel": 96, "mp": 418, "mpperlevel": 25, "movespeed": 335, "armor": 23, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 625, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 2.65, "attackspeedperlevel": 1.36, "attackspeed": 0.61}, "spells": [{"id": "AnnieQ", "name": "Désintégration", "description": "Annie projette une boule d'énergie magique qui inflige des dégâts ; le coût en mana lui est rendu si l'attaque détruit la cible.", "tooltip": "Annie lance une boule de feu, infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>. <PERSON> la cible meurt, Annie récupère le coût en mana de la compétence et son délai de récupération est réduit de 50%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "AnnieQ.png", "sprite": "spell1.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AnnieW", "name": "Incinération", "description": "<PERSON> projette un cône de flammes, infligeant des dégâts à tous les ennemis dans la zone.", "tooltip": "Annie projette une vague de feu qui inflige <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieW.png", "sprite": "spell1.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON>", "name": "Bouclier en fusion", "description": "Oct<PERSON>e à Annie ou à un allié un bonus en vitesse de déplacement et un bouclier, lequel blesse les ennemis qui lui lancent des attaques ou des compétences.", "tooltip": "Annie octroie à un champion allié un <shield>bouclier de {{ shieldblocktotal }} PV</shield> pendant {{ shieldduration }} sec et <speed>+{{ movespeedcalc }} vitesse de déplacement</speed> (diminuant progressivement) pendant {{ movementspeedduration }} sec. Tant que le bouclier est actif, les ennemis qui lancent une attaque ou une compétence sur l'allié protégé subissent <magicDamage>{{ damagereturn }} pts de dégâts magiques</magicDamage> (une fois par bouclier).<br /><br />Tibbers gagne toujours les effets de <spellName>Bouclier en fusion</spellName> quand il est invoqué.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["PV du bouclier", "Dég<PERSON><PERSON> renvoy<PERSON>", "<PERSON><PERSON><PERSON> ré<PERSON>", "Coût en @AbilityResourceName@"], "effect": ["{{ shieldamount }} -> {{ shieldamountNL }}", "{{ damagereflection }} -> {{ damagereflectionNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "AnnieE.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AnnieR", "name": "Invocation : <PERSON><PERSON><PERSON>", "description": "Annie insuffle la vie à son ours Tibbers et inflige des dégâts aux unités dans la zone d'effet. Tibbers peut attaquer et brûle les ennemis proches de lui.", "tooltip": "<spellPassive>Passive :</spellPassive> <PERSON> gagne +{{ rpercentpenbuff*100 }}% de pénétration magique.<br /><br />Annie invoque son ours Tibbers, infligeant <magicDamage>{{ initialburstdamage }} pts de dégâts magiques</magicDamage>. Pendant les {{ tibberslifetime }} prochaines secondes, Tibbers brûle les ennemis proches en leur infligeant <magicDamage>{{ tibbersauradamage }} pts de dégâts magiques par seconde</magicDamage>.<br /><br />Tibbers devient enragé quand il est invoqué, quand <PERSON> étourdit un champion ennemi et quand Annie meurt. Il gagne alors <attackSpeed>+275% de vitesse d'attaque</attackSpeed> et <speed>+100% de vitesse de déplacement</speed> (ce bonus diminue en 3 sec).<br /><br /><recast>Réactivation</recast> : permet de donner des ordres manuellement à Tibbers.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dégâts initiaux", "Dégâts du halo", "Dégâts d'attaque", "Vitesse de déplacement bonus", "<PERSON><PERSON><PERSON> ré<PERSON>", "Pénétration magique"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ auradamage }} -> {{ auradamageNL }}", "{{ tibbersattackdamage }} -> {{ tibbersattackdamageNL }}", "{{ tibbersbonusms }} -> {{ tibbersbonusmsNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rpercentpenbuff*100.000000 }}% -> {{ rpercentpenbuffnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "AnnieR.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pyromanie", "description": "Après qu'Annie a utilisé 4 compétences, sa prochaine compétence offensive étourdit sa cible.<br><br>Annie commence la partie et réapparaît avec Pyromanie disponible.", "image": {"full": "Annie_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}