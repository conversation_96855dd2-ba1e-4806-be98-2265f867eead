{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Samira": {"id": "<PERSON><PERSON>", "key": "360", "name": "<PERSON><PERSON>", "title": "la rosa del deserto", "image": {"full": "Samira.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "360000", "num": 0, "name": "default", "chromas": false}, {"id": "360001", "num": 1, "name": "Samira OPSI", "chromas": true}, {"id": "360010", "num": 10, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "360020", "num": 20, "name": "Samira Mezzogiorno di Fuoco", "chromas": true}, {"id": "360030", "num": 30, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "360033", "num": 33, "name": "<PERSON><PERSON> della Rosa Nera", "chromas": false}], "lore": "Samira guarda la morte negli occhi con sicurezza incrollabile, sempre alla ricerca di un nuovo brivido. <PERSON><PERSON> aver assistito da bambina alla distruzione della sua città a Shurima, trovò la sua vera patria a Noxus, dove forgiò la sua reputazione di elegante scavezzacollo pronta a intraprendere le missioni più rischiose e remunerative. Armata di pistole a polvere pirica e di una spada progettata su misura, Samira adora trovarsi tra la vita e la morte ed eliminare chiunque la ostacoli con stile e fascino.", "blurb": "Samira guarda la morte negli occhi con sicurezza incrollabile, sempre alla ricerca di un nuovo brivido. <PERSON><PERSON> aver assistito da bambina alla distruzione della sua città a Shurima, trovò la sua vera patria a Noxus, dove forgiò la sua reputazione di...", "allytips": [], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 108, "mp": 349, "mpperlevel": 38, "movespeed": 335, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "SamiraQ", "name": "Dono naturale", "description": "Samira spara un colpo o sferra un fendente, infliggendo danni. Se l'abilità viene lanciata durante Scatto selvaggio, colpisce tutti i nemici sul percorso al completamento.", "tooltip": "Samira spara un colpo, infliggendo <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> al primo nemico colpito.<br /><br />Se questa abilità viene lanciata contro un nemico a portata di attacco in corpo a corpo, <PERSON>ra sferra invece un fendente con la spada, infliggendo <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Rapporto attacco fisico totale"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ qadratio*100.000000 }}% -> {{ qadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "SamiraQ.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraW", "name": "<PERSON> turbina<PERSON>", "description": "Samira fa roteare la spada attorno a sé, danne<PERSON><PERSON>do gli avversari e bloccando i proiettili nemici.", "tooltip": "Samira fa roteare la spada attorno a sé per {{ slashduration }} secondi, infliggendo agli avversari due colpi da <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> ciascuno e distruggendo qualsiasi proiettile nemico che entra nell'area.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [30, 28, 26, 24, 22], "cooldownBurn": "30/28/26/24/22", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "SamiraW.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraE", "name": "Scatto sel<PERSON>ggio", "description": "<PERSON><PERSON> scatta attraverso un nemico (strutture incluse), colpendo tutti gli avversari che oltrepassa e ottenendo velocità d'attacco. Uccidere un campione nemico riduce la ricarica dell'abilità.", "tooltip": "<PERSON>ra scatta attraverso un nemico (strutture incluse), infliggendo <magicDamage>{{ dashdamage }} danni magici</magicDamage> a tutti i nemici che riesce a oltrepassare e ottenendo <attackSpeed>{{ bonusattackspeed*100 }}% velocità d'attacco</attackSpeed> per {{ attackspeedduration }} secondi. <br /><br />Se un campione nemico viene ucciso entro 3 secondi da quando Samira lo danneggia, la ricarica di quest'abilità si azzera.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Velocità d'attacco"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ bonusattackspeed*100.000000 }}% -> {{ bonusattackspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraE.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraR", "name": "Grilletto infernale", "description": "<PERSON>ra scatena un fiume di proiettili dalle pistole, attaccando senza sosta i nemici intorno a lei.", "tooltip": "Samira può usare questa abilità solo se il suo attuale grado di <keywordMajor>Stile</keywordMajor> è S. L'utilizzo di questa abilità consuma tutto il suo grado di <keywordMajor>Stile</keywordMajor>.<br /><br />Samira scatena un fiume di proiettili dalle pistole, attaccando senza sosta i nemici intorno a lei per 10 volte in 2 secondi. Ciascun colpo infligge <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> e applica rubavita al {{ lifestealmod*100 }}% dell'efficacia. Inoltre, ogni colpo può mettere a segno colpi critici.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraR.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Impulso temerario", "description": "Samira crea una combo concatenando attacchi base o abilità diverse dal colpo precedente. Gli attacchi di Samira in corpo a corpo infliggono danni magici aggiuntivi. Quando Samira attacca nemici <status>immobilizzati</status>, scatta fino ad averli nella sua gittata d'attacco. Inoltre, se il nemico viene <status>lanciato in aria</status>, rimane <status>in aria</status> per un breve periodo.", "image": {"full": "SamiraP.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}