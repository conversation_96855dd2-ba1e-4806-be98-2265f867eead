{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mel": {"id": "<PERSON>", "key": "800", "name": "<PERSON>", "title": "il riflesso dell'anima", "image": {"full": "Mel.png", "sprite": "champion2.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "800000", "num": 0, "name": "default", "chromas": false}, {"id": "800001", "num": 1, "name": "Mel Arcane: Consigliera", "chromas": true}], "lore": "Mel Medarda è la presunta erede della famiglia Medarda, un tempo una delle casate più potenti di Noxus. All'apparenza è un'elegante aristocratica, ma sotto la superficie si cela un'abile politica che sa trovare sempre un modo per sapere tutto su ogni persona che incontra. Do<PERSON> aver incrociato la strada della misteriosa Rosa Nera, Mel ha scoperto la portata delle menzogne di sua madre e, per una volta, si è ritrovata ad affrontare una situazione potenzialmente al di là del suo controllo. Armata delle abilità magiche che si sono appena risvegliate in lei, è partita verso casa in cerca di risposte... e sebbene molti cerchino ancora di smorzare la luce che brilla in lei, l'anima di Mel resta eternamente ribelle.", "blurb": "Mel Medarda è la presunta erede della famiglia Medarda, un tempo una delle casate più potenti di Noxus. All'apparenza è un'elegante aristocratica, ma sotto la superficie si cela un'abile politica che sa trovare sempre un modo per sapere tutto su ogni...", "allytips": ["Mel può riflettere i proiettili nemici, inclusi potenti incantesimi. Prima di scagliarle proiettili, attendi che lanci Riflesso.", "Più volte Mel ti colpisce, maggiore è il numero di cariche di Sopraffare applicate. Se la tua salute diminuisce troppo, il suo prossimo colpo ti ucciderà, perciò allontanati per un paio di secondi intanto che le cariche di Sopraffare si esauriscono."], "enemytips": ["Mel può riflettere i proiettili nemici, inclusi potenti incantesimi. Prima di scagliarle proiettili, attendi che lanci Riflesso.", "Più volte Mel ti colpisce, maggiore è il numero di cariche di Sopraffare applicate. Se la tua salute diminuisce troppo, il suo prossimo colpo ti ucciderà, perciò allontanati per un paio di secondi intanto che le cariche di Sopraffare si esauriscono."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 93, "mp": 480, "mpperlevel": 28, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.9, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "MelQ", "name": "<PERSON><PERSON><PERSON>", "description": "Mel spara una raffica di proiettili che esplodono attorno a un punto bersaglio, infliggendo danni ripetuti ai nemici nell'area.", "tooltip": "Mel spara una raffica di {{ explosioncount }} proiettili che esplodono attorno a un punto bersaglio.<br /><br />Ogni esplosione infligge <magicDamage>{{ totalexplosiondamage }} danni magici</magicDamage>, aggiungendosi al totale di <magicDamage>{{ alldamagehit }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> da esplosione", "Conteggio esplosioni", "Ricarica", "Costo in mana"], "effect": ["{{ explosiondamage }} -> {{ explosiondamageNL }}", "{{ explosioncount }} -> {{ explosioncountNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "MelQ.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelW", "name": "<PERSON><PERSON>", "description": "Mel forma una barriera attorno a sé che riflette i proiettili nemici verso chi li ha scagliati, prevenendo i danni e conferendole velocità di movimento.", "tooltip": "Mel forma una barriera attorno a sé che riflette i proiettili nemici verso chi li ha scagliati, prevenendo i danni e conferendole <speed>{{ movespeed*100 }}% velocità di movimento decrescente</speed> per {{ duration }} secondo/i.<br /><br />I proiettili riflessi infliggono un <magicDamage>{{ damagepercent }} dei danni originali come danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in mana"], "effect": ["{{ basedamagepercent*100.000000 }}% -> {{ basedamagepercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [35, 32, 29, 26, 23], "cooldownBurn": "35/32/29/26/23", "cost": [80, 60, 40, 20, 0], "costBurn": "80/60/40/20/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "MelW.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelE", "name": "Trappola solare", "description": "Mel lancia una sfera radiosa che infligge danni nel tempo e rallenta i nemici nelle vicinanze, immobiliz<PERSON>do quelli al suo interno.", "tooltip": "Mel lancia una sfera radiosa, <status>immobilizzando</status> i nemici al suo interno per {{ rootduration }} secondi e infliggendo <magicDamage>{{ damage }} danni magici</magicDamage>.<br /><br />La sfera genera attorno a sé un'area avversa che <status>rallenta</status> i nemici di un {{ areaslowamount*100 }}% e infligge <magicDamage>{{ areadamagepersecond }} danni magici al secondo</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata immobilizzazione", "<PERSON><PERSON> al secondo", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ baseareadamage*8.000000 }} -> {{ baseareadamagenl*8.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "MelE.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MelR", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Mel colpisce tutti i nemici marchiati da Sopraffare a prescindere dalla distanza, infliggendo danni aggiuntivi per ogni carica di Sopraffare.<br><br>I livelli di Eclissi dorata aumentano i danni di Sopraffare.", "tooltip": "<spellPassive>Passiva</spellPassive>: i danni di <keywordMajor>Sopraffare</keywordMajor> aumentano a <magicDamage>{{ passiveflatdamage }} danni magici, più {{ passivestackdamage }} danni magici per carica</magicDamage>.<br /><br /><spellActive>Attiva</spellActive>: Mel scatena il suo potere su tutti i nemici affetti da <keywordMajor>Sopraffare</keywordMajor>, infliggendo <magicDamage>{{ ultflatdamage }} danni magici e {{ ultstackdamage }} danni magici per ciascuna carica di <keywordMajor>Sopraffare</keywordMajor></magicDamage>.<br /><br /><rules>Può essere lanciata solo quando un campione nemico è affetto da <keywordMajor>Sopraffare</keywordMajor>.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName><PERSON><PERSON><PERSON><PERSON> do<PERSON></spellName> - <PERSON><PERSON>ssi", "<spellName><PERSON><PERSON><PERSON><PERSON></spellName> - <PERSON><PERSON> per carica", "Ricarica", "<keywordMajor><PERSON><PERSON><PERSON><PERSON></keywordMajor> - <PERSON><PERSON>", "<keywordMajor><PERSON><PERSON><PERSON><PERSON></keywordMajor> - <PERSON><PERSON> per carica"], "effect": ["{{ baseultflatdamage }} -> {{ baseultflatdamageNL }}", "{{ baseultstackdamage }} -> {{ baseultstackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basepassiveflatdamage }} -> {{ basepassiveflatdamageNL }}", "{{ basepassivestackdamage }} -> {{ basepassivestackdamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MelR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Splendore ardente", "description": "Quando Mel usa un'abilità, il suo attacco successivo ottiene tre proiettili bonus, fino a un massimo di nove.<br><br><PERSON>uando <PERSON> infligge danni con un'abilità o un attacco, applica <PERSON><PERSON><PERSON><PERSON>, che si può accumulare all'infinito. Se il nemico viene colpito da Mel con danni di Sopraffare sufficienti a ucciderlo, le cariche vengono consumate e il bersaglio giustiziato.", "image": {"full": "Mel_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}