[{"id": 0, "name": "CRISTALLO", "description": "", "shortDescription": "Regole speciali per il Cristallo", "hasLeaderboard": true, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 0, "rewards": [{"category": "TITLE", "quantity": 1, "title": "A<PERSON>rendis<PERSON>"}]}, "BRONZE": {"value": 750}, "SILVER": {"value": 1650}, "GOLD": {"value": 4300}, "PLATINUM": {"value": 8600}, "DIAMOND": {"value": 13800}, "MASTER": {"value": 24500}, "GRANDMASTER": {"value": 25000}, "CHALLENGER": {"value": 26500}}}, {"id": 1, "name": "IMMAGINAZIONE", "description": "", "shortDescription": "Configurazione IMMAGINAZIONE", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 75}, "BRONZE": {"value": 175}, "SILVER": {"value": 300}, "GOLD": {"value": 700}, "PLATINUM": {"value": 1200}, "DIAMOND": {"value": 2000}, "MASTER": {"value": 3500}}}, {"id": 101000, "name": "Autorità ARAM", "description": "Ottieni progressi dalle sfide nei gruppi Combattente ARAM, Precisione ARAM e Campione ARAM", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Combattente ARAM, Precisione ARAM e Campione ARAM", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101000-IRON.png", "BRONZE": "/challenges-images/101000-BRONZE.png", "SILVER": "/challenges-images/101000-SILVER.png", "GOLD": "/challenges-images/101000-GOLD.png", "PLATINUM": "/challenges-images/101000-PLATINUM.png", "DIAMOND": "/challenges-images/101000-DIAMOND.png", "MASTER": "/challenges-images/101000-MASTER.png", "GRANDMASTER": "/challenges-images/101000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 40}, "BRONZE": {"value": 85}, "SILVER": {"value": 140}, "GOLD": {"value": 360}, "PLATINUM": {"value": 590}, "DIAMOND": {"value": 1075}, "MASTER": {"value": 1850, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Dio di ARAM"}]}}}, {"id": 101100, "name": "Combattente ARAM", "description": "Ottieni progressi dalle sfide nel gruppo Combattente ARAM", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Combattente ARAM", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101100-IRON.png", "BRONZE": "/challenges-images/101100-BRONZE.png", "SILVER": "/challenges-images/101100-SILVER.png", "GOLD": "/challenges-images/101100-GOLD.png", "PLATINUM": "/challenges-images/101100-PLATINUM.png", "DIAMOND": "/challenges-images/101100-DIAMOND.png", "MASTER": "/challenges-images/101100-MASTER.png", "GRANDMASTER": "/challenges-images/101100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 40}, "GOLD": {"value": 115}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 340, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Inarrestabile"}]}, "MASTER": {"value": 650}}}, {"id": 101101, "name": "Minaccia DPS", "description": "Infliggi più di 1800 danni al minuto nelle partite ARAM", "shortDescription": "Infliggi più di 1800 Danni al minuto", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101101-IRON.png", "BRONZE": "/challenges-images/101101-BRONZE.png", "SILVER": "/challenges-images/101101-SILVER.png", "GOLD": "/challenges-images/101101-GOLD.png", "PLATINUM": "/challenges-images/101101-PLATINUM.png", "DIAMOND": "/challenges-images/101101-DIAMOND.png", "MASTER": "/challenges-images/101101-MASTER.png", "GRANDMASTER": "/challenges-images/101101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101101-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 4}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 15}, "CHALLENGER": {"value": 20}}}, {"id": 101102, "name": "Doppia decimazione", "description": "O<PERSON><PERSON> due Pentakill in una singola partita ARAM", "shortDescription": "Otti<PERSON> due Pentakill in una sola partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101102-IRON.png", "BRONZE": "/challenges-images/101102-BRONZE.png", "SILVER": "/challenges-images/101102-SILVER.png", "GOLD": "/challenges-images/101102-GOLD.png", "PLATINUM": "/challenges-images/101102-PLATINUM.png", "DIAMOND": "/challenges-images/101102-DIAMOND.png", "MASTER": "/challenges-images/101102-MASTER.png", "GRANDMASTER": "/challenges-images/101102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101102-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 101103, "name": "Leggenda ARAM", "description": "Arriva a Leggendario nelle partite ARAM", "shortDescription": "Arriva a Leggendario", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101103-IRON.png", "BRONZE": "/challenges-images/101103-BRONZE.png", "SILVER": "/challenges-images/101103-SILVER.png", "GOLD": "/challenges-images/101103-GOLD.png", "PLATINUM": "/challenges-images/101103-PLATINUM.png", "DIAMOND": "/challenges-images/101103-DIAMOND.png", "MASTER": "/challenges-images/101103-MASTER.png", "GRANDMASTER": "/challenges-images/101103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101103-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 10}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 20}, "CHALLENGER": {"value": 30}}}, {"id": 101104, "name": "<PERSON>na amara", "description": "Uccidi avversari che hanno recentemente ricevuto un Incremento salute in ARAM", "shortDescription": "Uccidi i nemici guariti di recente da un Incremento salute", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101104-IRON.png", "BRONZE": "/challenges-images/101104-BRONZE.png", "SILVER": "/challenges-images/101104-SILVER.png", "GOLD": "/challenges-images/101104-GOLD.png", "PLATINUM": "/challenges-images/101104-PLATINUM.png", "DIAMOND": "/challenges-images/101104-DIAMOND.png", "MASTER": "/challenges-images/101104-MASTER.png", "GRANDMASTER": "/challenges-images/101104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>"}]}, "PLATINUM": {"value": 45}, "DIAMOND": {"value": 90}, "MASTER": {"value": 150}}}, {"id": 101105, "name": "<PERSON><PERSON><PERSON>o", "description": "Uccidi nemici vicino a una delle loro torri in ARAM", "shortDescription": "Uccidi i nemici vicino a una delle loro torri", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101105-IRON.png", "BRONZE": "/challenges-images/101105-BRONZE.png", "SILVER": "/challenges-images/101105-SILVER.png", "GOLD": "/challenges-images/101105-GOLD.png", "PLATINUM": "/challenges-images/101105-PLATINUM.png", "DIAMOND": "/challenges-images/101105-DIAMOND.png", "MASTER": "/challenges-images/101105-MASTER.png", "GRANDMASTER": "/challenges-images/101105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 125}, "GOLD": {"value": 250}, "PLATINUM": {"value": 650}, "DIAMOND": {"value": 1500}, "MASTER": {"value": 2500}}}, {"id": 101106, "name": "Eradicazione ARAM", "description": "<PERSON><PERSON><PERSON> in ARAM", "shortDescription": "<PERSON><PERSON><PERSON>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101106-IRON.png", "BRONZE": "/challenges-images/101106-BRONZE.png", "SILVER": "/challenges-images/101106-SILVER.png", "GOLD": "/challenges-images/101106-GOLD.png", "PLATINUM": "/challenges-images/101106-PLATINUM.png", "DIAMOND": "/challenges-images/101106-DIAMOND.png", "MASTER": "/challenges-images/101106-MASTER.png", "GRANDMASTER": "/challenges-images/101106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101106-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 7}, "GRANDMASTER": {"value": 12}, "CHALLENGER": {"value": 20}}}, {"id": 101107, "name": "<PERSON><PERSON><PERSON> campioni, non minion", "description": "Ottieni eliminazioni in ARAM", "shortDescription": "Ottieni eliminazioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101107-IRON.png", "BRONZE": "/challenges-images/101107-BRONZE.png", "SILVER": "/challenges-images/101107-SILVER.png", "GOLD": "/challenges-images/101107-GOLD.png", "PLATINUM": "/challenges-images/101107-PLATINUM.png", "DIAMOND": "/challenges-images/101107-DIAMOND.png", "MASTER": "/challenges-images/101107-MASTER.png", "GRANDMASTER": "/challenges-images/101107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101107-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 200}, "BRONZE": {"value": 800}, "SILVER": {"value": 2000}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 12500}, "DIAMOND": {"value": 20000}, "MASTER": {"value": 30000}, "GRANDMASTER": {"value": 40000}, "CHALLENGER": {"value": 50000}}}, {"id": 101108, "name": "Carry in singolo", "description": "Infliggi almeno il 40% dei danni della tua squadra ai campioni in ARAM", "shortDescription": "Infliggi il 40%+ del danno dei campioni della tua squadra", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101108-IRON.png", "BRONZE": "/challenges-images/101108-BRONZE.png", "SILVER": "/challenges-images/101108-SILVER.png", "GOLD": "/challenges-images/101108-GOLD.png", "PLATINUM": "/challenges-images/101108-PLATINUM.png", "DIAMOND": "/challenges-images/101108-DIAMOND.png", "MASTER": "/challenges-images/101108-MASTER.png", "GRANDMASTER": "/challenges-images/101108-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101108-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 7}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 101200, "name": "Precisione ARAM", "description": "Ottieni progressi dalle sfide nel gruppo Precisione ARAM", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Precisione ARAM", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101200-IRON.png", "BRONZE": "/challenges-images/101200-BRONZE.png", "SILVER": "/challenges-images/101200-SILVER.png", "GOLD": "/challenges-images/101200-GOLD.png", "PLATINUM": "/challenges-images/101200-PLATINUM.png", "DIAMOND": "/challenges-images/101200-DIAMOND.png", "MASTER": "/challenges-images/101200-MASTER.png", "GRANDMASTER": "/challenges-images/101200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 90}, "PLATINUM": {"value": 140}, "DIAMOND": {"value": 250, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Agile"}]}, "MASTER": {"value": 400}}}, {"id": 101201, "name": "Un altro giorno, un altro centro", "description": "<PERSON><PERSON> a segno colpi mirati (abilità a distanza senza bersaglio) sui campioni in ARAM", "shortDescription": "<PERSON>ti a segno colpi mirati sui campioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101201-IRON.png", "BRONZE": "/challenges-images/101201-BRONZE.png", "SILVER": "/challenges-images/101201-SILVER.png", "GOLD": "/challenges-images/101201-GOLD.png", "PLATINUM": "/challenges-images/101201-PLATINUM.png", "DIAMOND": "/challenges-images/101201-DIAMOND.png", "MASTER": "/challenges-images/101201-MASTER.png", "GRANDMASTER": "/challenges-images/101201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 200}, "BRONZE": {"value": 1000}, "SILVER": {"value": 2500}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 15000}, "DIAMOND": {"value": 30000}, "MASTER": {"value": 45000}, "GRANDMASTER": {"value": 60000}, "CHALLENGER": {"value": 75000}}}, {"id": 101202, "name": "Mancato di un soffio", "description": "Schiva colpi mirati (abilità a distanza senza bersaglio) in ARAM", "shortDescription": "Schiva i colpi mirati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101202-IRON.png", "BRONZE": "/challenges-images/101202-BRONZE.png", "SILVER": "/challenges-images/101202-SILVER.png", "GOLD": "/challenges-images/101202-GOLD.png", "PLATINUM": "/challenges-images/101202-PLATINUM.png", "DIAMOND": "/challenges-images/101202-DIAMOND.png", "MASTER": "/challenges-images/101202-MASTER.png", "GRANDMASTER": "/challenges-images/101202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 300}, "BRONZE": {"value": 1200}, "SILVER": {"value": 3600}, "GOLD": {"value": 7200}, "PLATINUM": {"value": 21600}, "DIAMOND": {"value": 44000}, "MASTER": {"value": 78000}, "GRANDMASTER": {"value": 90000}, "CHALLENGER": {"value": 150000}}}, {"id": 101203, "name": "Nevicata", "description": "Colpisci con palle di neve i campioni in ARAM", "shortDescription": "Colpisci con le palle di neve i campioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101203-IRON.png", "BRONZE": "/challenges-images/101203-BRONZE.png", "SILVER": "/challenges-images/101203-SILVER.png", "GOLD": "/challenges-images/101203-GOLD.png", "PLATINUM": "/challenges-images/101203-PLATINUM.png", "DIAMOND": "/challenges-images/101203-DIAMOND.png", "MASTER": "/challenges-images/101203-MASTER.png", "GRANDMASTER": "/challenges-images/101203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Valanga"}]}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1200}, "MASTER": {"value": 3000}, "GRANDMASTER": {"value": 4000}, "CHALLENGER": {"value": 5000}}}, {"id": 101204, "name": "<PERSON><PERSON> gratis", "description": "<PERSON><PERSON><PERSON> minion in ARAM", "shortDescription": "Uccidi i minion", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101204-IRON.png", "BRONZE": "/challenges-images/101204-BRONZE.png", "SILVER": "/challenges-images/101204-SILVER.png", "GOLD": "/challenges-images/101204-GOLD.png", "PLATINUM": "/challenges-images/101204-PLATINUM.png", "DIAMOND": "/challenges-images/101204-DIAMOND.png", "MASTER": "/challenges-images/101204-MASTER.png", "GRANDMASTER": "/challenges-images/101204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 225}, "BRONZE": {"value": 900}, "SILVER": {"value": 2250}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 17000}, "DIAMOND": {"value": 30000}, "MASTER": {"value": 54000}}}, {"id": 101205, "name": "<PERSON><PERSON><PERSON> gratis per la base", "description": "<PERSON><PERSON> giustiziare dalla torre esterna entro i primi 10 minuti in ARAM", "shortDescription": "<PERSON><PERSON> giusti<PERSON> dalla torre entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101205-IRON.png", "BRONZE": "/challenges-images/101205-BRONZE.png", "SILVER": "/challenges-images/101205-SILVER.png", "GOLD": "/challenges-images/101205-GOLD.png", "PLATINUM": "/challenges-images/101205-PLATINUM.png", "DIAMOND": "/challenges-images/101205-DIAMOND.png", "MASTER": "/challenges-images/101205-MASTER.png", "GRANDMASTER": "/challenges-images/101205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101205-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 4}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 18}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 101206, "name": "E il Poro fa Pop!", "description": "Fai esplodere un Poro in ARAM", "shortDescription": "Fai esplodere un Poro", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101206-IRON.png", "BRONZE": "/challenges-images/101206-BRONZE.png", "SILVER": "/challenges-images/101206-SILVER.png", "GOLD": "/challenges-images/101206-GOLD.png", "PLATINUM": "/challenges-images/101206-PLATINUM.png", "DIAMOND": "/challenges-images/101206-DIAMOND.png", "MASTER": "/challenges-images/101206-MASTER.png", "GRANDMASTER": "/challenges-images/101206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101206-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "PoRegalità"}]}}}, {"id": 101300, "name": "Campione ARAM", "description": "Ottieni progressi dalle sfide nel gruppo Campione ARAM", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Campione ARAM", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101300-IRON.png", "BRONZE": "/challenges-images/101300-BRONZE.png", "SILVER": "/challenges-images/101300-SILVER.png", "GOLD": "/challenges-images/101300-GOLD.png", "PLATINUM": "/challenges-images/101300-PLATINUM.png", "DIAMOND": "/challenges-images/101300-DIAMOND.png", "MASTER": "/challenges-images/101300-MASTER.png", "GRANDMASTER": "/challenges-images/101300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 100}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 340, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Abissale"}]}, "MASTER": {"value": 550}}}, {"id": 101301, "name": "<PERSON><PERSON>, tutti campioni", "description": "Ottieni almeno S- con campioni diversi in ARAM", "shortDescription": "O<PERSON><PERSON> almeno S- <em>con campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101301-IRON.png", "BRONZE": "/challenges-images/101301-BRONZE.png", "SILVER": "/challenges-images/101301-SILVER.png", "GOLD": "/challenges-images/101301-GOLD.png", "PLATINUM": "/challenges-images/101301-PLATINUM.png", "DIAMOND": "/challenges-images/101301-DIAMOND.png", "MASTER": "/challenges-images/101301-MASTER.png", "GRANDMASTER": "/challenges-images/101301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}}}, {"id": 101302, "name": "<PERSON><PERSON>, tutti impeccabili", "description": "Ottieni almeno S in ARAM", "shortDescription": "Ottieni valutazioni almeno di grado S", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101302-IRON.png", "BRONZE": "/challenges-images/101302-BRONZE.png", "SILVER": "/challenges-images/101302-SILVER.png", "GOLD": "/challenges-images/101302-GOLD.png", "PLATINUM": "/challenges-images/101302-PLATINUM.png", "DIAMOND": "/challenges-images/101302-DIAMOND.png", "MASTER": "/challenges-images/101302-MASTER.png", "GRANDMASTER": "/challenges-images/101302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 35}, "MASTER": {"value": 75}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 300}}}, {"id": 101303, "name": "Demolizione rapida", "description": "Distruggi la prima torre in ARAM prima che trascorrano cinque minuti", "shortDescription": "Distruggi la prima torre entro 5 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101303-IRON.png", "BRONZE": "/challenges-images/101303-BRONZE.png", "SILVER": "/challenges-images/101303-SILVER.png", "GOLD": "/challenges-images/101303-GOLD.png", "PLATINUM": "/challenges-images/101303-PLATINUM.png", "DIAMOND": "/challenges-images/101303-DIAMOND.png", "MASTER": "/challenges-images/101303-MASTER.png", "GRANDMASTER": "/challenges-images/101303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 30}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 250}}}, {"id": 101304, "name": "<PERSON>o lampo", "description": "Vinci partite di ARAM prima che trascorrano 13 minuti", "shortDescription": "Vinci le partite entro 13 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101304-IRON.png", "BRONZE": "/challenges-images/101304-BRONZE.png", "SILVER": "/challenges-images/101304-SILVER.png", "GOLD": "/challenges-images/101304-GOLD.png", "PLATINUM": "/challenges-images/101304-PLATINUM.png", "DIAMOND": "/challenges-images/101304-DIAMOND.png", "MASTER": "/challenges-images/101304-MASTER.png", "GRANDMASTER": "/challenges-images/101304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101304-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 20}}}, {"id": 101305, "name": "Partecipante attivo", "description": "Ottieni oltre il 90% di partecipazione alle uccisioni in partite di ARAM", "shortDescription": "Ottieni oltre il 90% di partecipazione alle uccisioni nelle partite", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101305-IRON.png", "BRONZE": "/challenges-images/101305-BRONZE.png", "SILVER": "/challenges-images/101305-SILVER.png", "GOLD": "/challenges-images/101305-GOLD.png", "PLATINUM": "/challenges-images/101305-PLATINUM.png", "DIAMOND": "/challenges-images/101305-DIAMOND.png", "MASTER": "/challenges-images/101305-MASTER.png", "GRANDMASTER": "/challenges-images/101305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 125}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 101306, "name": "Non puoi toccarmi", "description": "Vinci partite di ARAM senza essere ucciso da un campione nemico (puoi essere giustiziato)", "shortDescription": "Vinci senza essere ucciso da un nemico", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/101306-IRON.png", "BRONZE": "/challenges-images/101306-BRONZE.png", "SILVER": "/challenges-images/101306-SILVER.png", "GOLD": "/challenges-images/101306-GOLD.png", "PLATINUM": "/challenges-images/101306-PLATINUM.png", "DIAMOND": "/challenges-images/101306-DIAMOND.png", "MASTER": "/challenges-images/101306-MASTER.png", "GRANDMASTER": "/challenges-images/101306-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101306-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Intoccabile"}]}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}}}, {"id": 101307, "name": "NA-RAM", "description": "Vinci partite di ARAM", "shortDescription": "Vinci partite", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/101307-IRON.png", "BRONZE": "/challenges-images/101307-BRONZE.png", "SILVER": "/challenges-images/101307-SILVER.png", "GOLD": "/challenges-images/101307-GOLD.png", "PLATINUM": "/challenges-images/101307-PLATINUM.png", "DIAMOND": "/challenges-images/101307-DIAMOND.png", "MASTER": "/challenges-images/101307-MASTER.png", "GRANDMASTER": "/challenges-images/101307-GRANDMASTER.png", "CHALLENGER": "/challenges-images/101307-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 35}, "GOLD": {"value": 70}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 700}, "CHALLENGER": {"value": 1000}}}, {"id": 103000, "name": "Astuzia e Creatività", "description": "Ottieni progressi dalle sfide nei gruppi Stile, Innovazione e Stratega", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Stile, Innovazione e Stratega", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103000-IRON.png", "BRONZE": "/challenges-images/103000-BRONZE.png", "SILVER": "/challenges-images/103000-SILVER.png", "GOLD": "/challenges-images/103000-GOLD.png", "PLATINUM": "/challenges-images/103000-PLATINUM.png", "DIAMOND": "/challenges-images/103000-DIAMOND.png", "MASTER": "/challenges-images/103000-MASTER.png", "GRANDMASTER": "/challenges-images/103000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 50}, "SILVER": {"value": 100}, "GOLD": {"value": 230}, "PLATINUM": {"value": 400}, "DIAMOND": {"value": 675}, "MASTER": {"value": 1150, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Cervello galattico"}]}}}, {"id": 103100, "name": "Stile", "description": "Ottieni progressi dalle sfide nel gruppo Stile", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Stile", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103100-IRON.png", "BRONZE": "/challenges-images/103100-BRONZE.png", "SILVER": "/challenges-images/103100-SILVER.png", "GOLD": "/challenges-images/103100-GOLD.png", "PLATINUM": "/challenges-images/103100-PLATINUM.png", "DIAMOND": "/challenges-images/103100-DIAMOND.png", "MASTER": "/challenges-images/103100-MASTER.png", "GRANDMASTER": "/challenges-images/103100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 45}, "PLATINUM": {"value": 65}, "DIAMOND": {"value": 110, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Stile"}]}, "MASTER": {"value": 175}}}, {"id": 103101, "name": "<PERSON><PERSON><PERSON> sotto il naso", "description": "Richiama senza farti vedere da un campione nemico nelle vicinanze", "shortDescription": "Richiama senza farti vedere da un campione nelle vicinanze", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103101-IRON.png", "BRONZE": "/challenges-images/103101-BRONZE.png", "SILVER": "/challenges-images/103101-SILVER.png", "GOLD": "/challenges-images/103101-GOLD.png", "PLATINUM": "/challenges-images/103101-PLATINUM.png", "DIAMOND": "/challenges-images/103101-DIAMOND.png", "MASTER": "/challenges-images/103101-MASTER.png", "GRANDMASTER": "/challenges-images/103101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103101-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 10}, "MASTER": {"value": 20}}}, {"id": 103102, "name": "Io sono a un altro livello", "description": "Ottieni eliminazioni entro 5 secondi dopo aver ottenuto un vantaggio in livelli nei primi 10 minuti di una partita", "shortDescription": "Ottieni eliminazioni dopo un vantaggio iniziale in livelli", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/103102-IRON.png", "BRONZE": "/challenges-images/103102-BRONZE.png", "SILVER": "/challenges-images/103102-SILVER.png", "GOLD": "/challenges-images/103102-GOLD.png", "PLATINUM": "/challenges-images/103102-PLATINUM.png", "DIAMOND": "/challenges-images/103102-DIAMOND.png", "MASTER": "/challenges-images/103102-MASTER.png", "GRANDMASTER": "/challenges-images/103102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 120}, "CHALLENGER": {"value": 150}}}, {"id": 103103, "name": "<PERSON>essun luogo è sicuro", "description": "Elimina campioni nemici nella loro fontana", "shortDescription": "Elimina i campioni nella loro fontana", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103103-IRON.png", "BRONZE": "/challenges-images/103103-BRONZE.png", "SILVER": "/challenges-images/103103-SILVER.png", "GOLD": "/challenges-images/103103-GOLD.png", "PLATINUM": "/challenges-images/103103-PLATINUM.png", "DIAMOND": "/challenges-images/103103-DIAMOND.png", "MASTER": "/challenges-images/103103-MASTER.png", "GRANDMASTER": "/challenges-images/103103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103103-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 103200, "name": "Innovazione", "description": "Ottieni progressi dalle sfide nel gruppo Innovazione", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Innovazione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103200-IRON.png", "BRONZE": "/challenges-images/103200-BRONZE.png", "SILVER": "/challenges-images/103200-SILVER.png", "GOLD": "/challenges-images/103200-GOLD.png", "PLATINUM": "/challenges-images/103200-PLATINUM.png", "DIAMOND": "/challenges-images/103200-DIAMOND.png", "MASTER": "/challenges-images/103200-MASTER.png", "GRANDMASTER": "/challenges-images/103200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 30}, "GOLD": {"value": 85}, "PLATINUM": {"value": 175}, "DIAMOND": {"value": 280, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Innovativo"}]}, "MASTER": {"value": 400}}}, {"id": 103201, "name": "Gioco di nicchia", "description": "Elimina un campione nemico nella nicchia", "shortDescription": "Elimina gli avversari nella nicchia", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103201-IRON.png", "BRONZE": "/challenges-images/103201-BRONZE.png", "SILVER": "/challenges-images/103201-SILVER.png", "GOLD": "/challenges-images/103201-GOLD.png", "PLATINUM": "/challenges-images/103201-PLATINUM.png", "DIAMOND": "/challenges-images/103201-DIAMOND.png", "MASTER": "/challenges-images/103201-MASTER.png", "GRANDMASTER": "/challenges-images/103201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 10}, "GOLD": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Giocatore di nicchia"}]}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}}}, {"id": 103202, "name": "Tattiche vegetali", "description": "Lancia te stesso e un campione nemico in direzioni diverse dopo aver colpito un Bulbo esplosivo", "shortDescription": "Usa i Bulbi esplosivi per sfuggire ai nemici", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/103202-IRON.png", "BRONZE": "/challenges-images/103202-BRONZE.png", "SILVER": "/challenges-images/103202-SILVER.png", "GOLD": "/challenges-images/103202-GOLD.png", "PLATINUM": "/challenges-images/103202-PLATINUM.png", "DIAMOND": "/challenges-images/103202-DIAMOND.png", "MASTER": "/challenges-images/103202-MASTER.png", "GRANDMASTER": "/challenges-images/103202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103202-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 60}}}, {"id": 103203, "name": "Posizionamento aggressivo", "description": "Ottieni uccisioni multiple poco dopo aver raggiunto un campione nemico con Flash", "shortDescription": "Ottieni uccisioni multiple dopo aver raggiunto un nemico con Flash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103203-IRON.png", "BRONZE": "/challenges-images/103203-BRONZE.png", "SILVER": "/challenges-images/103203-SILVER.png", "GOLD": "/challenges-images/103203-GOLD.png", "PLATINUM": "/challenges-images/103203-PLATINUM.png", "DIAMOND": "/challenges-images/103203-DIAMOND.png", "MASTER": "/challenges-images/103203-MASTER.png", "GRANDMASTER": "/challenges-images/103203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Sgargiante"}]}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 70}, "MASTER": {"value": 125}}}, {"id": 103204, "name": "Tanti saluti all'ondata", "description": "Uccidi 20 minion entro tre secondi", "shortDescription": "Uccidi 20 minion entro 3 secondi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103204-IRON.png", "BRONZE": "/challenges-images/103204-BRONZE.png", "SILVER": "/challenges-images/103204-SILVER.png", "GOLD": "/challenges-images/103204-GOLD.png", "PLATINUM": "/challenges-images/103204-PLATINUM.png", "DIAMOND": "/challenges-images/103204-DIAMOND.png", "MASTER": "/challenges-images/103204-MASTER.png", "GRANDMASTER": "/challenges-images/103204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103204-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}}}, {"id": 103205, "name": "No davvero!", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> in un combattimento tra campioni con la salute a una sola cifra", "shortDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> in un combattimento tra campioni con la salute a una sola cifra", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103205-IRON.png", "BRONZE": "/challenges-images/103205-BRONZE.png", "SILVER": "/challenges-images/103205-SILVER.png", "GOLD": "/challenges-images/103205-GOLD.png", "PLATINUM": "/challenges-images/103205-PLATINUM.png", "DIAMOND": "/challenges-images/103205-DIAMOND.png", "MASTER": "/challenges-images/103205-MASTER.png", "GRANDMASTER": "/challenges-images/103205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103205-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Sopravvissuto"}]}}}, {"id": 103206, "name": "Un drago per amico", "description": "Uccidi gli avversari con l'aiuto di un mostro epico. I mostri epici includono i draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Uccidi gli avversari con l'aiuto di un mostro epico", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103206-IRON.png", "BRONZE": "/challenges-images/103206-BRONZE.png", "SILVER": "/challenges-images/103206-SILVER.png", "GOLD": "/challenges-images/103206-GOLD.png", "PLATINUM": "/challenges-images/103206-PLATINUM.png", "DIAMOND": "/challenges-images/103206-DIAMOND.png", "MASTER": "/challenges-images/103206-MASTER.png", "GRANDMASTER": "/challenges-images/103206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 18}, "GOLD": {"value": 40}, "PLATINUM": {"value": 120}, "DIAMOND": {"value": 225}, "MASTER": {"value": 420}}}, {"id": 103207, "name": "<PERSON><PERSON><PERSON> carneficina", "description": "Fai in modo che entrambe le squadre subiscano una carneficina nello stesso scontro", "shortDescription": "Fai in modo che entrambe le squadre subiscano una carneficina nello stesso scontro", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103207-IRON.png", "BRONZE": "/challenges-images/103207-BRONZE.png", "SILVER": "/challenges-images/103207-SILVER.png", "GOLD": "/challenges-images/103207-GOLD.png", "PLATINUM": "/challenges-images/103207-PLATINUM.png", "DIAMOND": "/challenges-images/103207-DIAMOND.png", "MASTER": "/challenges-images/103207-MASTER.png", "GRANDMASTER": "/challenges-images/103207-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103207-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Carneficina"}]}}}, {"id": 103300, "name": "Stratega", "description": "Ottieni progressi dalle sfide nel gruppo Stratega", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Stratega", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103300-IRON.png", "BRONZE": "/challenges-images/103300-BRONZE.png", "SILVER": "/challenges-images/103300-SILVER.png", "GOLD": "/challenges-images/103300-GOLD.png", "PLATINUM": "/challenges-images/103300-PLATINUM.png", "DIAMOND": "/challenges-images/103300-DIAMOND.png", "MASTER": "/challenges-images/103300-MASTER.png", "GRANDMASTER": "/challenges-images/103300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tattico"}]}, "MASTER": {"value": 325}}}, {"id": 103301, "name": "Furia del Drago", "description": "Ottieni uccisioni multiple con il Buff del Drago maggiore", "shortDescription": "Ottieni uccisioni multiple con il Drago maggiore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103301-IRON.png", "BRONZE": "/challenges-images/103301-BRONZE.png", "SILVER": "/challenges-images/103301-SILVER.png", "GOLD": "/challenges-images/103301-GOLD.png", "PLATINUM": "/challenges-images/103301-PLATINUM.png", "DIAMOND": "/challenges-images/103301-DIAMOND.png", "MASTER": "/challenges-images/103301-MASTER.png", "GRANDMASTER": "/challenges-images/103301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103301-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}}}, {"id": 103302, "name": "Sterminatore di Nashor", "description": "Uccidi il Barone Nashor in singolo", "shortDescription": "Uccidi il Barone Nashor in singolo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103302-IRON.png", "BRONZE": "/challenges-images/103302-BRONZE.png", "SILVER": "/challenges-images/103302-SILVER.png", "GOLD": "/challenges-images/103302-GOLD.png", "PLATINUM": "/challenges-images/103302-PLATINUM.png", "DIAMOND": "/challenges-images/103302-DIAMOND.png", "MASTER": "/challenges-images/103302-MASTER.png", "GRANDMASTER": "/challenges-images/103302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103302-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 6}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}}}, {"id": 103303, "name": "Selezione dei bersagli", "description": "Elimina campioni da giungla vicino a un mostro epico danneggiato prima che venga ucciso. I mostri epici includono i Draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Elimina i giocatori da giungla vicino a un mostro epico danneggiato", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/103303-IRON.png", "BRONZE": "/challenges-images/103303-BRONZE.png", "SILVER": "/challenges-images/103303-SILVER.png", "GOLD": "/challenges-images/103303-GOLD.png", "PLATINUM": "/challenges-images/103303-PLATINUM.png", "DIAMOND": "/challenges-images/103303-DIAMOND.png", "MASTER": "/challenges-images/103303-MASTER.png", "GRANDMASTER": "/challenges-images/103303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15}, "PLATINUM": {"value": 45}, "DIAMOND": {"value": 80}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 400}}}, {"id": 103304, "name": "La mancanza di rispetto", "description": "Infliggi almeno 2000 danni e distruggi il nexus nemico in inferiorità numerica rispetto ai campioni nemici", "shortDescription": "Distruggi il nexus nemico in inferiorità numerica", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/103304-IRON.png", "BRONZE": "/challenges-images/103304-BRONZE.png", "SILVER": "/challenges-images/103304-SILVER.png", "GOLD": "/challenges-images/103304-GOLD.png", "PLATINUM": "/challenges-images/103304-PLATINUM.png", "DIAMOND": "/challenges-images/103304-DIAMOND.png", "MASTER": "/challenges-images/103304-MASTER.png", "GRANDMASTER": "/challenges-images/103304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/103304-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 120000, "name": "Me<PERSON>enario <PERSON>-<PERSON><PERSON>", "description": "Ottieni progressi dalle sfide nel gruppo Mercenario Caccia-Macchine", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Mercenario Caccia-Macchine", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120000-IRON.png", "BRONZE": "/challenges-images/120000-BRONZE.png", "SILVER": "/challenges-images/120000-SILVER.png", "GOLD": "/challenges-images/120000-GOLD.png", "PLATINUM": "/challenges-images/120000-PLATINUM.png", "DIAMOND": "/challenges-images/120000-DIAMOND.png", "MASTER": "/challenges-images/120000-MASTER.png", "GRANDMASTER": "/challenges-images/120000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 75}, "PLATINUM": {"value": 120}, "DIAMOND": {"value": 180}, "MASTER": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Distru<PERSON><PERSON> di bot"}]}}}, {"id": 120001, "name": "<PERSON><PERSON><PERSON>", "description": "Vinci partite Co-op vs. IA", "shortDescription": "Vinci partite", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120001-IRON.png", "BRONZE": "/challenges-images/120001-BRONZE.png", "SILVER": "/challenges-images/120001-SILVER.png", "GOLD": "/challenges-images/120001-GOLD.png", "PLATINUM": "/challenges-images/120001-PLATINUM.png", "DIAMOND": "/challenges-images/120001-DIAMOND.png", "MASTER": "/challenges-images/120001-MASTER.png", "GRANDMASTER": "/challenges-images/120001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 30}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 150}}}, {"id": 120002, "name": "Annullamento mutevole", "description": "Vinci una partita Co-op vs. IA con campioni differenti", "shortDescription": "Vinci partite <em>con campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120002-IRON.png", "BRONZE": "/challenges-images/120002-BRONZE.png", "SILVER": "/challenges-images/120002-SILVER.png", "GOLD": "/challenges-images/120002-GOLD.png", "PLATINUM": "/challenges-images/120002-PLATINUM.png", "DIAMOND": "/challenges-images/120002-DIAMOND.png", "MASTER": "/challenges-images/120002-MASTER.png", "GRANDMASTER": "/challenges-images/120002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 75}, "MASTER": {"value": 100}}}, {"id": 120003, "name": "<PERSON><PERSON><PERSON> robotico", "description": "Esegui uccisioni in partite Co-op vs. IA", "shortDescription": "<PERSON><PERSON>eni <PERSON>i", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/120003-IRON.png", "BRONZE": "/challenges-images/120003-BRONZE.png", "SILVER": "/challenges-images/120003-SILVER.png", "GOLD": "/challenges-images/120003-GOLD.png", "PLATINUM": "/challenges-images/120003-PLATINUM.png", "DIAMOND": "/challenges-images/120003-DIAMOND.png", "MASTER": "/challenges-images/120003-MASTER.png", "GRANDMASTER": "/challenges-images/120003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/120003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 150}, "SILVER": {"value": 300}, "GOLD": {"value": 750}, "PLATINUM": {"value": 2500}, "DIAMOND": {"value": 5000}, "MASTER": {"value": 7500}}}, {"id": 121000, "name": "Gloriosa Evoluzione", "description": "Ottieni progressi dalle sfide nel gruppo Gloriosa Evoluzione", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Gloriosa Evoluzione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121000-IRON.png", "BRONZE": "/challenges-images/121000-BRONZE.png", "SILVER": "/challenges-images/121000-SILVER.png", "GOLD": "/challenges-images/121000-GOLD.png", "PLATINUM": "/challenges-images/121000-PLATINUM.png", "DIAMOND": "/challenges-images/121000-DIAMOND.png", "MASTER": "/challenges-images/121000-MASTER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 75}, "PLATINUM": {"value": 120}, "DIAMOND": {"value": 180}, "MASTER": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON><PERSON> da Turing"}]}}}, {"id": 121001, "name": "Riempiti di Bot-te", "description": "Vinci partite Co-op vs. IA (intermedie) senza morire con almeno il 20% di partecipazione alle uccisioni", "shortDescription": "Vinci partite Co-op vs. IA senza morire e partecipa al 20% delle uccisioni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121001-IRON.png", "BRONZE": "/challenges-images/121001-BRONZE.png", "SILVER": "/challenges-images/121001-SILVER.png", "GOLD": "/challenges-images/121001-GOLD.png", "PLATINUM": "/challenges-images/121001-PLATINUM.png", "DIAMOND": "/challenges-images/121001-DIAMOND.png", "MASTER": "/challenges-images/121001-MASTER.png", "GRANDMASTER": "/challenges-images/121001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/121001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 30}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 125}}}, {"id": 121002, "name": "Errore 410: Impossibile trovare il jungler", "description": "Come campione da giungla, uccidi il jungler nemico nella sua giungla entro 10 minuti nelle partite Co-op vs. IA (intermedie)", "shortDescription": "Come campione da giungla, uccidi la tua controparte nemica nella sua giungla entro 10 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121002-IRON.png", "BRONZE": "/challenges-images/121002-BRONZE.png", "SILVER": "/challenges-images/121002-SILVER.png", "GOLD": "/challenges-images/121002-GOLD.png", "PLATINUM": "/challenges-images/121002-PLATINUM.png", "DIAMOND": "/challenges-images/121002-DIAMOND.png", "MASTER": "/challenges-images/121002-MASTER.png", "GRANDMASTER": "/challenges-images/121002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/121002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 12}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 50}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 250}}}, {"id": 121003, "name": "Artificiali ma non intelligenti", "description": "Distruggi tutti e tre gli inibitori in meno di 25 minuti nelle partite Co-op vs. IA (intermedie)", "shortDescription": "Distruggi tutti e 3 gli inibitori in meno di 25 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/121003-IRON.png", "BRONZE": "/challenges-images/121003-BRONZE.png", "SILVER": "/challenges-images/121003-SILVER.png", "GOLD": "/challenges-images/121003-GOLD.png", "PLATINUM": "/challenges-images/121003-PLATINUM.png", "DIAMOND": "/challenges-images/121003-DIAMOND.png", "MASTER": "/challenges-images/121003-MASTER.png", "GRANDMASTER": "/challenges-images/121003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/121003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 12}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 60}}}, {"id": 2, "name": "COMPETENZA", "description": "", "shortDescription": "Configurazione ABILITÀ", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 125}, "BRONZE": {"value": 275}, "SILVER": {"value": 500}, "GOLD": {"value": 1100}, "PLATINUM": {"value": 1800}, "DIAMOND": {"value": 3200}, "MASTER": {"value": 5500}}}, {"id": 201000, "name": "Adepto", "description": "Ottieni progressi dalle sfide nel gruppo Adepto", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Adepto", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/201000-IRON.png", "BRONZE": "/challenges-images/201000-BRONZE.png", "SILVER": "/challenges-images/201000-SILVER.png", "GOLD": "/challenges-images/201000-GOLD.png", "PLATINUM": "/challenges-images/201000-PLATINUM.png", "DIAMOND": "/challenges-images/201000-DIAMOND.png", "MASTER": "/challenges-images/201000-MASTER.png", "GRANDMASTER": "/challenges-images/201000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 70}, "PLATINUM": {"value": 110}, "DIAMOND": {"value": 200}, "MASTER": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Letale"}]}}}, {"id": 201001, "name": "Costruzione scadente", "description": "Ottieni un'eliminazione (uccisione o assist) sulla prima torre della partita", "shortDescription": "Distruggi la prima torre", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201001-IRON.png", "BRONZE": "/challenges-images/201001-BRONZE.png", "SILVER": "/challenges-images/201001-SILVER.png", "GOLD": "/challenges-images/201001-GOLD.png", "PLATINUM": "/challenges-images/201001-PLATINUM.png", "DIAMOND": "/challenges-images/201001-DIAMOND.png", "MASTER": "/challenges-images/201001-MASTER.png", "GRANDMASTER": "/challenges-images/201001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 201002, "name": "Efficienza letale", "description": "Uccidi 80 minion della corsia entro 10 minuti", "shortDescription": "Uccidi 80 minion entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201002-IRON.png", "BRONZE": "/challenges-images/201002-BRONZE.png", "SILVER": "/challenges-images/201002-SILVER.png", "GOLD": "/challenges-images/201002-GOLD.png", "PLATINUM": "/challenges-images/201002-PLATINUM.png", "DIAMOND": "/challenges-images/201002-DIAMOND.png", "MASTER": "/challenges-images/201002-MASTER.png", "GRANDMASTER": "/challenges-images/201002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201002-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Minion"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 125}}}, {"id": 201003, "name": "Re demone immortale", "description": "Vinci partite senza morire e con almeno il 30% di partecipazione alle uccisioni", "shortDescription": "Vinci partite senza morire con almeno il 30% di partecipazione alle uccisioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201003-IRON.png", "BRONZE": "/challenges-images/201003-BRONZE.png", "SILVER": "/challenges-images/201003-SILVER.png", "GOLD": "/challenges-images/201003-GOLD.png", "PLATINUM": "/challenges-images/201003-PLATINUM.png", "DIAMOND": "/challenges-images/201003-DIAMOND.png", "MASTER": "/challenges-images/201003-MASTER.png", "GRANDMASTER": "/challenges-images/201003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201003-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 7, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>e immortale"}]}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 100}}}, {"id": 201004, "name": "Incarnazione della morte", "description": "Ottieni 12 eliminazioni (uccisione o assist) sui campioni nemici entro 15 minuti", "shortDescription": "Elimina 12 campioni entro 15 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/201004-IRON.png", "BRONZE": "/challenges-images/201004-BRONZE.png", "SILVER": "/challenges-images/201004-SILVER.png", "GOLD": "/challenges-images/201004-GOLD.png", "PLATINUM": "/challenges-images/201004-PLATINUM.png", "DIAMOND": "/challenges-images/201004-DIAMOND.png", "MASTER": "/challenges-images/201004-MASTER.png", "GRANDMASTER": "/challenges-images/201004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/201004-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 5}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 28}, "MASTER": {"value": 48}, "GRANDMASTER": {"value": 64}, "CHALLENGER": {"value": 96}}}, {"id": 202000, "name": "Ascendente", "description": "Ottieni progressi dalle sfide nei gruppi Dominazione, Impareggiabile e Perfezione", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Dominazione, Impareggiabile e Perfezione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202000-IRON.png", "BRONZE": "/challenges-images/202000-BRONZE.png", "SILVER": "/challenges-images/202000-SILVER.png", "GOLD": "/challenges-images/202000-GOLD.png", "PLATINUM": "/challenges-images/202000-PLATINUM.png", "DIAMOND": "/challenges-images/202000-DIAMOND.png", "MASTER": "/challenges-images/202000-MASTER.png", "GRANDMASTER": "/challenges-images/202000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 75}, "SILVER": {"value": 120}, "GOLD": {"value": 280}, "PLATINUM": {"value": 450}, "DIAMOND": {"value": 825}, "MASTER": {"value": 1450, "rewards": [{"category": "TITLE", "quantity": 1, "title": "G.O.A.T."}]}}}, {"id": 202100, "name": "Dominazione", "description": "Ottieni progressi dalle sfide nel gruppo Dominazione", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Dominazione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202100-IRON.png", "BRONZE": "/challenges-images/202100-BRONZE.png", "SILVER": "/challenges-images/202100-SILVER.png", "GOLD": "/challenges-images/202100-GOLD.png", "PLATINUM": "/challenges-images/202100-PLATINUM.png", "DIAMOND": "/challenges-images/202100-DIAMOND.png", "MASTER": "/challenges-images/202100-MASTER.png", "GRANDMASTER": "/challenges-images/202100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Dominante"}]}, "MASTER": {"value": 400}}}, {"id": 202101, "name": "Orizzonte ardente", "description": "Vinci partite in cui avevi almeno 100 CS di vantaggio sull'avversario nel tuo ruolo in qualsiasi momento della partita", "shortDescription": "Vinci partite con 100+ CS dell'avversario nel tuo ruolo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202101-IRON.png", "BRONZE": "/challenges-images/202101-BRONZE.png", "SILVER": "/challenges-images/202101-SILVER.png", "GOLD": "/challenges-images/202101-GOLD.png", "PLATINUM": "/challenges-images/202101-PLATINUM.png", "DIAMOND": "/challenges-images/202101-DIAMOND.png", "MASTER": "/challenges-images/202101-MASTER.png", "GRANDMASTER": "/challenges-images/202101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202101-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>a calda"}]}, "DIAMOND": {"value": 35}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 250}}}, {"id": 202102, "name": "Dominazione in corsia", "description": "Termina la fase in corsia (14 minuti) con il 20% di oro ed esperienza in più rispetto all'avversario nel tuo ruolo", "shortDescription": "Ottieni il 20% di oro e PE dopo 14 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202102-IRON.png", "BRONZE": "/challenges-images/202102-BRONZE.png", "SILVER": "/challenges-images/202102-SILVER.png", "GOLD": "/challenges-images/202102-GOLD.png", "PLATINUM": "/challenges-images/202102-PLATINUM.png", "DIAMOND": "/challenges-images/202102-DIAMOND.png", "MASTER": "/challenges-images/202102-MASTER.png", "GRANDMASTER": "/challenges-images/202102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 8}, "GOLD": {"value": 20}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 100}, "MASTER": {"value": 180}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 350}}}, {"id": 202103, "name": "Comando in corsia", "description": "Termina la prima fase in corsia (7 minuti) con il 20% di oro ed esperienza in più rispetto all'avversario nel tuo ruolo", "shortDescription": "Ottieni il 20% di oro e PE dopo 7 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202103-IRON.png", "BRONZE": "/challenges-images/202103-BRONZE.png", "SILVER": "/challenges-images/202103-SILVER.png", "GOLD": "/challenges-images/202103-GOLD.png", "PLATINUM": "/challenges-images/202103-PLATINUM.png", "DIAMOND": "/challenges-images/202103-DIAMOND.png", "MASTER": "/challenges-images/202103-MASTER.png", "GRANDMASTER": "/challenges-images/202103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 70}, "MASTER": {"value": 125}, "GRANDMASTER": {"value": 175}, "CHALLENGER": {"value": 250}}}, {"id": 202104, "name": "L'erba alta ha gli occhi", "description": "Termina la partita con il 20% del punteggio di visione in più rispetto all'avversario nel tuo ruolo", "shortDescription": "Completa una partita con il 20% del punteggio di visione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202104-IRON.png", "BRONZE": "/challenges-images/202104-BRONZE.png", "SILVER": "/challenges-images/202104-SILVER.png", "GOLD": "/challenges-images/202104-GOLD.png", "PLATINUM": "/challenges-images/202104-PLATINUM.png", "DIAMOND": "/challenges-images/202104-DIAMOND.png", "MASTER": "/challenges-images/202104-MASTER.png", "GRANDMASTER": "/challenges-images/202104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 45}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 750}, "CHALLENGER": {"value": 1000}}}, {"id": 202105, "name": "Di un altro livello", "description": "Ottieni almeno tre livelli di vantaggio sull'avversario nel tuo ruolo in qualsiasi momento della partita", "shortDescription": "Supera di tre livelli l'avversario nel tuo ruolo in qualsiasi momento", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202105-IRON.png", "BRONZE": "/challenges-images/202105-BRONZE.png", "SILVER": "/challenges-images/202105-SILVER.png", "GOLD": "/challenges-images/202105-GOLD.png", "PLATINUM": "/challenges-images/202105-PLATINUM.png", "DIAMOND": "/challenges-images/202105-DIAMOND.png", "MASTER": "/challenges-images/202105-MASTER.png", "GRANDMASTER": "/challenges-images/202105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 135}, "MASTER": {"value": 240}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 202200, "name": "Impareggiabile", "description": "Ottieni progressi dalle sfide nel gruppo Impareggiabile", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Impareggiabile", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202200-IRON.png", "BRONZE": "/challenges-images/202200-BRONZE.png", "SILVER": "/challenges-images/202200-SILVER.png", "GOLD": "/challenges-images/202200-GOLD.png", "PLATINUM": "/challenges-images/202200-PLATINUM.png", "DIAMOND": "/challenges-images/202200-DIAMOND.png", "MASTER": "/challenges-images/202200-MASTER.png", "GRANDMASTER": "/challenges-images/202200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Impareggiabile"}]}, "MASTER": {"value": 400}}}, {"id": 2022000, "name": "Stagionale 2022", "description": "Ottieni progressi dalle sfide nel gruppo Stagionale 2022", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Stagionale 2022", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2022000-IRON.png", "BRONZE": "/challenges-images/2022000-BRONZE.png", "SILVER": "/challenges-images/2022000-SILVER.png", "GOLD": "/challenges-images/2022000-GOLD.png", "PLATINUM": "/challenges-images/2022000-PLATINUM.png", "DIAMOND": "/challenges-images/2022000-DIAMOND.png", "MASTER": "/challenges-images/2022000-MASTER.png", "GRANDMASTER": "/challenges-images/2022000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 75}, "SILVER": {"value": 115}, "GOLD": {"value": 265}, "PLATINUM": {"value": 455}, "DIAMOND": {"value": 860}, "MASTER": {"value": 1400, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ciallengier '22"}]}}}, {"id": 2022001, "name": "<PERSON><PERSON>, tutti campioni: 2022", "description": "Ottieni almeno S- con campioni diversi in ARAM", "shortDescription": "<PERSON><PERSON><PERSON> S- <em>con campioni differenti</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022001-IRON.png", "BRONZE": "/challenges-images/2022001-BRONZE.png", "SILVER": "/challenges-images/2022001-SILVER.png", "GOLD": "/challenges-images/2022001-GOLD.png", "PLATINUM": "/challenges-images/2022001-PLATINUM.png", "DIAMOND": "/challenges-images/2022001-DIAMOND.png", "MASTER": "/challenges-images/2022001-MASTER.png", "GRANDMASTER": "/challenges-images/2022001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 15}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2022002, "name": "Demolizione rapida: 2022", "description": "Distruggi la prima torre in ARAM prima che trascorrano 5 minuti", "shortDescription": "Distruggi la prima torre entro 5 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022002-IRON.png", "BRONZE": "/challenges-images/2022002-BRONZE.png", "SILVER": "/challenges-images/2022002-SILVER.png", "GOLD": "/challenges-images/2022002-GOLD.png", "PLATINUM": "/challenges-images/2022002-PLATINUM.png", "DIAMOND": "/challenges-images/2022002-DIAMOND.png", "MASTER": "/challenges-images/2022002-MASTER.png", "GRANDMASTER": "/challenges-images/2022002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022002-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 15}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2022003, "name": "Prestazione migliore: 2022", "description": "Ottieni almeno S- un certo numero di volte nella Landa degli evocatori", "shortDescription": "Ottieni almeno S-", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022003-IRON.png", "BRONZE": "/challenges-images/2022003-BRONZE.png", "SILVER": "/challenges-images/2022003-SILVER.png", "GOLD": "/challenges-images/2022003-GOLD.png", "PLATINUM": "/challenges-images/2022003-PLATINUM.png", "DIAMOND": "/challenges-images/2022003-DIAMOND.png", "MASTER": "/challenges-images/2022003-MASTER.png", "GRANDMASTER": "/challenges-images/2022003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 100}}}, {"id": 2022004, "name": "Re demone immortale: 2022", "description": "Vinci partite senza morire e con almeno il 30% di partecipazione alle uccisioni nella Landa degli evocatori", "shortDescription": "Vinci partite senza morire con il 30% di partecipazione alle uccisioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022004-IRON.png", "BRONZE": "/challenges-images/2022004-BRONZE.png", "SILVER": "/challenges-images/2022004-SILVER.png", "GOLD": "/challenges-images/2022004-GOLD.png", "PLATINUM": "/challenges-images/2022004-PLATINUM.png", "DIAMOND": "/challenges-images/2022004-DIAMOND.png", "MASTER": "/challenges-images/2022004-MASTER.png", "GRANDMASTER": "/challenges-images/2022004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022004-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 4}, "MASTER": {"value": 6}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 12}}}, {"id": 2022005, "name": "Comando in corsia: 2022", "description": "Termina la prima fase in corsia (7 minuti) con il 20% di oro ed esperienza in più rispetto all'avversario nel tuo ruolo nella Landa degli evocatori", "shortDescription": "Ottieni il 20% di oro e PE dopo 7 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022005-IRON.png", "BRONZE": "/challenges-images/2022005-BRONZE.png", "SILVER": "/challenges-images/2022005-SILVER.png", "GOLD": "/challenges-images/2022005-GOLD.png", "PLATINUM": "/challenges-images/2022005-PLATINUM.png", "DIAMOND": "/challenges-images/2022005-DIAMOND.png", "MASTER": "/challenges-images/2022005-MASTER.png", "GRANDMASTER": "/challenges-images/2022005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 7}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 2022006, "name": "Prescrizione di dolore: 2022", "description": "Infliggi la maggior quantità di danni ai campioni nella partita nella Landa degli evocatori", "shortDescription": "Infliggi più danni ai campioni nella partita", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022006-IRON.png", "BRONZE": "/challenges-images/2022006-BRONZE.png", "SILVER": "/challenges-images/2022006-SILVER.png", "GOLD": "/challenges-images/2022006-GOLD.png", "PLATINUM": "/challenges-images/2022006-PLATINUM.png", "DIAMOND": "/challenges-images/2022006-DIAMOND.png", "MASTER": "/challenges-images/2022006-MASTER.png", "GRANDMASTER": "/challenges-images/2022006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 115}}}, {"id": 2022007, "name": "Prodigio: 2022", "description": "Diventa leggendario (8 uccisioni consecutive) entro 15 minuti nella Landa degli evocatori", "shortDescription": "Diventa leggendario entro 15 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2022007-IRON.png", "BRONZE": "/challenges-images/2022007-BRONZE.png", "SILVER": "/challenges-images/2022007-SILVER.png", "GOLD": "/challenges-images/2022007-GOLD.png", "PLATINUM": "/challenges-images/2022007-PLATINUM.png", "DIAMOND": "/challenges-images/2022007-DIAMOND.png", "MASTER": "/challenges-images/2022007-MASTER.png", "GRANDMASTER": "/challenges-images/2022007-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022007-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 2022008, "name": "Al prezzo di uno: 2022", "description": "Uccidi 2 giocatori con il lancio di una sola abilità nella Landa degli evocatori", "shortDescription": "Uccidi 2 giocatori con il lancio di una sola abilità", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022008-IRON.png", "BRONZE": "/challenges-images/2022008-BRONZE.png", "SILVER": "/challenges-images/2022008-SILVER.png", "GOLD": "/challenges-images/2022008-GOLD.png", "PLATINUM": "/challenges-images/2022008-PLATINUM.png", "DIAMOND": "/challenges-images/2022008-DIAMOND.png", "MASTER": "/challenges-images/2022008-MASTER.png", "GRANDMASTER": "/challenges-images/2022008-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022008-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 12}, "MASTER": {"value": 18}, "GRANDMASTER": {"value": 25}, "CHALLENGER": {"value": 30}}}, {"id": 2022009, "name": "Solo Bolo: 2022", "description": "O<PERSON>eni uccisioni in singolo nella Landa degli evocatori (senza assist dai campioni alleati)", "shortDescription": "Ottieni uccisioni in singolo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022009-IRON.png", "BRONZE": "/challenges-images/2022009-BRONZE.png", "SILVER": "/challenges-images/2022009-SILVER.png", "GOLD": "/challenges-images/2022009-GOLD.png", "PLATINUM": "/challenges-images/2022009-PLATINUM.png", "DIAMOND": "/challenges-images/2022009-DIAMOND.png", "MASTER": "/challenges-images/2022009-MASTER.png", "GRANDMASTER": "/challenges-images/2022009-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022009-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 100}, "GOLD": {"value": 225}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 600}, "MASTER": {"value": 900}, "GRANDMASTER": {"value": 1200}, "CHALLENGER": {"value": 1500}}}, {"id": 202201, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ottieni il punteggio degli effetti di controllo più alto nella partita", "shortDescription": "Ottieni il punteggio degli effetti di controllo più alto nella partita", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202201-IRON.png", "BRONZE": "/challenges-images/202201-BRONZE.png", "SILVER": "/challenges-images/202201-SILVER.png", "GOLD": "/challenges-images/202201-GOLD.png", "PLATINUM": "/challenges-images/202201-PLATINUM.png", "DIAMOND": "/challenges-images/202201-DIAMOND.png", "MASTER": "/challenges-images/202201-MASTER.png", "GRANDMASTER": "/challenges-images/202201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 160}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 2022010, "name": "Cacciatore di granchi: 2022", "description": "Prendi entrambi gli Argogranchi iniziali nella Landa degli evocatori", "shortDescription": "Prendi entrambi gli Argogranchi iniziali", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022010-IRON.png", "BRONZE": "/challenges-images/2022010-BRONZE.png", "SILVER": "/challenges-images/2022010-SILVER.png", "GOLD": "/challenges-images/2022010-GOLD.png", "PLATINUM": "/challenges-images/2022010-PLATINUM.png", "DIAMOND": "/challenges-images/2022010-DIAMOND.png", "MASTER": "/challenges-images/2022010-MASTER.png", "GRANDMASTER": "/challenges-images/2022010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 8}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 60}, "GRANDMASTER": {"value": 80}, "CHALLENGER": {"value": 100}}}, {"id": 2022011, "name": "Onniveggente: 2022", "description": "Ottieni più di 2 punti di visione al minuto nella Landa degli evocatori", "shortDescription": "Ottieni più di 2 punti di visione al minuto", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022011-IRON.png", "BRONZE": "/challenges-images/2022011-BRONZE.png", "SILVER": "/challenges-images/2022011-SILVER.png", "GOLD": "/challenges-images/2022011-GOLD.png", "PLATINUM": "/challenges-images/2022011-PLATINUM.png", "DIAMOND": "/challenges-images/2022011-DIAMOND.png", "MASTER": "/challenges-images/2022011-MASTER.png", "GRANDMASTER": "/challenges-images/2022011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022011-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 12}, "GOLD": {"value": 22}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 60}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 120}, "CHALLENGER": {"value": 150}}}, {"id": 2022014, "name": "Furto sensazionale: 2022", "description": "Ruba mostri epici nella Landa degli evocatori. I mostri epici includono i draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "<PERSON><PERSON> mostri epici", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022014-IRON.png", "BRONZE": "/challenges-images/2022014-BRONZE.png", "SILVER": "/challenges-images/2022014-SILVER.png", "GOLD": "/challenges-images/2022014-GOLD.png", "PLATINUM": "/challenges-images/2022014-PLATINUM.png", "DIAMOND": "/challenges-images/2022014-DIAMOND.png", "MASTER": "/challenges-images/2022014-MASTER.png", "GRANDMASTER": "/challenges-images/2022014-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022014-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 8}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 2022015, "name": "Serie di serie di uccisioni: 2022", "description": "Ottieni serie di uccisioni nella Landa degli evocatori (puoi ottenerne diverse, una per serie di uccisioni, una per Furia scatenata, ecc.)", "shortDescription": "Ottieni delle serie di uccisioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022015-IRON.png", "BRONZE": "/challenges-images/2022015-BRONZE.png", "SILVER": "/challenges-images/2022015-SILVER.png", "GOLD": "/challenges-images/2022015-GOLD.png", "PLATINUM": "/challenges-images/2022015-PLATINUM.png", "DIAMOND": "/challenges-images/2022015-DIAMOND.png", "MASTER": "/challenges-images/2022015-MASTER.png", "GRANDMASTER": "/challenges-images/2022015-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022015-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 45}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 120}, "MASTER": {"value": 180}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 400}}}, {"id": 2022016, "name": "PENTAKIIIIIIIIL!!: 2022", "description": "<PERSON><PERSON><PERSON> nella Landa degli evocatori", "shortDescription": "Ottieni una pentakill", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022016-IRON.png", "BRONZE": "/challenges-images/2022016-BRONZE.png", "SILVER": "/challenges-images/2022016-SILVER.png", "GOLD": "/challenges-images/2022016-GOLD.png", "PLATINUM": "/challenges-images/2022016-PLATINUM.png", "DIAMOND": "/challenges-images/2022016-DIAMOND.png", "MASTER": "/challenges-images/2022016-MASTER.png", "GRANDMASTER": "/challenges-images/2022016-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022016-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}, "GRANDMASTER": {"value": 4}, "CHALLENGER": {"value": 5}}}, {"id": 2022017, "name": "Dare il buon esempio: 2022", "description": "Ottieni 12 o più assist senza morire nella Landa degli evocatori", "shortDescription": "Ottieni una serie di almeno 12 assist senza morire", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022017-IRON.png", "BRONZE": "/challenges-images/2022017-BRONZE.png", "SILVER": "/challenges-images/2022017-SILVER.png", "GOLD": "/challenges-images/2022017-GOLD.png", "PLATINUM": "/challenges-images/2022017-PLATINUM.png", "DIAMOND": "/challenges-images/2022017-DIAMOND.png", "MASTER": "/challenges-images/2022017-MASTER.png", "GRANDMASTER": "/challenges-images/2022017-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022017-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 60}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 115}, "CHALLENGER": {"value": 155}}}, {"id": 2022018, "name": "Falciata spirituale: 2022", "description": "Conquista Anime dei Draghi senza che la squadra nemica prenda un solo drago nella Landa degli evocatori", "shortDescription": "Reclama un'Anima di Drago 4-0", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022018-IRON.png", "BRONZE": "/challenges-images/2022018-BRONZE.png", "SILVER": "/challenges-images/2022018-SILVER.png", "GOLD": "/challenges-images/2022018-GOLD.png", "PLATINUM": "/challenges-images/2022018-PLATINUM.png", "DIAMOND": "/challenges-images/2022018-DIAMOND.png", "MASTER": "/challenges-images/2022018-MASTER.png", "GRANDMASTER": "/challenges-images/2022018-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022018-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 6}, "MASTER": {"value": 7}, "GRANDMASTER": {"value": 8}, "CHALLENGER": {"value": 10}}}, {"id": 2022019, "name": "Doppio potere: 2022", "description": "Ottieni delle vittorie con un gruppo organizzato da 2 nella coda classificata", "shortDescription": "Vinci con un gruppo organizzato da 2 nella coda classificata", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022019-IRON.png", "BRONZE": "/challenges-images/2022019-BRONZE.png", "SILVER": "/challenges-images/2022019-SILVER.png", "GOLD": "/challenges-images/2022019-GOLD.png", "PLATINUM": "/challenges-images/2022019-PLATINUM.png", "DIAMOND": "/challenges-images/2022019-DIAMOND.png", "MASTER": "/challenges-images/2022019-MASTER.png", "GRANDMASTER": "/challenges-images/2022019-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022019-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 80}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 125}, "CHALLENGER": {"value": 150}}}, {"id": 202202, "name": "Distributore di oscurità", "description": "Elimina il maggior numero di lumi nella partita (devi eliminare almeno un lume)", "shortDescription": "Elimina il maggior numero di lumi nella partita", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202202-IRON.png", "BRONZE": "/challenges-images/202202-BRONZE.png", "SILVER": "/challenges-images/202202-SILVER.png", "GOLD": "/challenges-images/202202-GOLD.png", "PLATINUM": "/challenges-images/202202-PLATINUM.png", "DIAMOND": "/challenges-images/202202-DIAMOND.png", "MASTER": "/challenges-images/202202-MASTER.png", "GRANDMASTER": "/challenges-images/202202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 6}, "SILVER": {"value": 18}, "GOLD": {"value": 36}, "PLATINUM": {"value": 120, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "DIAMOND": {"value": 240}, "MASTER": {"value": 400}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 2022020, "name": "Campione Clash: 2022", "description": "Vinci le Eliminatorie Clash", "shortDescription": "Vinci le Eliminatorie Clash", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2022020-IRON.png", "BRONZE": "/challenges-images/2022020-BRONZE.png", "SILVER": "/challenges-images/2022020-SILVER.png", "GOLD": "/challenges-images/2022020-GOLD.png", "PLATINUM": "/challenges-images/2022020-PLATINUM.png", "DIAMOND": "/challenges-images/2022020-DIAMOND.png", "MASTER": "/challenges-images/2022020-MASTER.png", "GRANDMASTER": "/challenges-images/2022020-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2022020-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 5}, "SILVER": {"value": 8}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 60}, "CHALLENGER": {"value": 70}}}, {"id": 202203, "name": "Prescrizione di dolore", "description": "Infliggi il maggior numero di danni ai campioni nella partita", "shortDescription": "Infliggi più danni ai campioni nella partita", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202203-IRON.png", "BRONZE": "/challenges-images/202203-BRONZE.png", "SILVER": "/challenges-images/202203-SILVER.png", "GOLD": "/challenges-images/202203-GOLD.png", "PLATINUM": "/challenges-images/202203-PLATINUM.png", "DIAMOND": "/challenges-images/202203-DIAMOND.png", "MASTER": "/challenges-images/202203-MASTER.png", "GRANDMASTER": "/challenges-images/202203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 50}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 250}}}, {"id": 202204, "name": "Dispensatore di danni", "description": "Infliggi più di 700 danni al minuto", "shortDescription": "Infliggi oltre 700 danni al minuto", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202204-IRON.png", "BRONZE": "/challenges-images/202204-BRONZE.png", "SILVER": "/challenges-images/202204-SILVER.png", "GOLD": "/challenges-images/202204-GOLD.png", "PLATINUM": "/challenges-images/202204-PLATINUM.png", "DIAMOND": "/challenges-images/202204-DIAMOND.png", "MASTER": "/challenges-images/202204-MASTER.png", "GRANDMASTER": "/challenges-images/202204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 275}, "MASTER": {"value": 480}, "GRANDMASTER": {"value": 640}, "CHALLENGER": {"value": 960}}}, {"id": 202205, "name": "<PERSON><PERSON>", "description": "Vinci partite in cui hai subito il 35% dei danni inflitti ai campioni nella tua squadra", "shortDescription": "Vinci la partita subendo almeno il 35% dei danni della squadra", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/202205-IRON.png", "BRONZE": "/challenges-images/202205-BRONZE.png", "SILVER": "/challenges-images/202205-SILVER.png", "GOLD": "/challenges-images/202205-GOLD.png", "PLATINUM": "/challenges-images/202205-PLATINUM.png", "DIAMOND": "/challenges-images/202205-DIAMOND.png", "MASTER": "/challenges-images/202205-MASTER.png", "GRANDMASTER": "/challenges-images/202205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202205-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 202300, "name": "Perfezione", "description": "Ottieni progressi dalle sfide nel gruppo Perfezione", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Perfezione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202300-IRON.png", "BRONZE": "/challenges-images/202300-BRONZE.png", "SILVER": "/challenges-images/202300-SILVER.png", "GOLD": "/challenges-images/202300-GOLD.png", "PLATINUM": "/challenges-images/202300-PLATINUM.png", "DIAMOND": "/challenges-images/202300-DIAMOND.png", "MASTER": "/challenges-images/202300-MASTER.png", "GRANDMASTER": "/challenges-images/202300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 65}, "PLATINUM": {"value": 105}, "DIAMOND": {"value": 190, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "MASTER": {"value": 400}}}, {"id": 2023000, "name": "Stagionale 2023", "description": "Ottieni progressi dalle sfide nel gruppo Stagionale 2023", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Stagionale 2023", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023000-IRON.png", "BRONZE": "/challenges-images/2023000-BRONZE.png", "SILVER": "/challenges-images/2023000-SILVER.png", "GOLD": "/challenges-images/2023000-GOLD.png", "PLATINUM": "/challenges-images/2023000-PLATINUM.png", "DIAMOND": "/challenges-images/2023000-DIAMOND.png", "MASTER": "/challenges-images/2023000-MASTER.png", "GRANDMASTER": "/challenges-images/2023000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 70}, "SILVER": {"value": 105}, "GOLD": {"value": 235}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 670}, "MASTER": {"value": 1600, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ciallengier '23"}]}}}, {"id": 2023001, "name": "Te<PERSON><PERSON> dalla morte: 2023", "description": "Vinci partite senza morire e con almeno il 30% di partecipazione alle uccisioni", "shortDescription": "Vinci partite senza morire e con almeno il 30% di partecipazione alle uccisioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023001-IRON.png", "BRONZE": "/challenges-images/2023001-BRONZE.png", "SILVER": "/challenges-images/2023001-SILVER.png", "GOLD": "/challenges-images/2023001-GOLD.png", "PLATINUM": "/challenges-images/2023001-PLATINUM.png", "DIAMOND": "/challenges-images/2023001-DIAMOND.png", "MASTER": "/challenges-images/2023001-MASTER.png", "GRANDMASTER": "/challenges-images/2023001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023001-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023002, "name": "È tutto merito mio: 2023", "description": "Infliggi almeno il 40% dei danni totali della tua squadra ai campioni in ARAM", "shortDescription": "Infliggi almeno il 40% del danno dei campioni della tua squadra", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023002-IRON.png", "BRONZE": "/challenges-images/2023002-BRONZE.png", "SILVER": "/challenges-images/2023002-SILVER.png", "GOLD": "/challenges-images/2023002-GOLD.png", "PLATINUM": "/challenges-images/2023002-PLATINUM.png", "DIAMOND": "/challenges-images/2023002-DIAMOND.png", "MASTER": "/challenges-images/2023002-MASTER.png", "GRANDMASTER": "/challenges-images/2023002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023002-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023003, "name": "Inferiorità numerica, superiorità di fuoco: 2023", "description": "Assicurati uccisioni mentre nelle vicinanze ci sono più campioni nemici che alleati", "shortDescription": "Assicurati uccisioni in inferiorità numerica", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023003-IRON.png", "BRONZE": "/challenges-images/2023003-BRONZE.png", "SILVER": "/challenges-images/2023003-SILVER.png", "GOLD": "/challenges-images/2023003-GOLD.png", "PLATINUM": "/challenges-images/2023003-PLATINUM.png", "DIAMOND": "/challenges-images/2023003-DIAMOND.png", "MASTER": "/challenges-images/2023003-MASTER.png", "GRANDMASTER": "/challenges-images/2023003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 150}, "MASTER": {"value": 200}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 300}}}, {"id": 2023004, "name": "Obiettivo conquistato: 2023", "description": "Ruba due mostri epici in una partita. I mostri epici includono i Draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Ruba due mostri epici in una partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023004-IRON.png", "BRONZE": "/challenges-images/2023004-BRONZE.png", "SILVER": "/challenges-images/2023004-SILVER.png", "GOLD": "/challenges-images/2023004-GOLD.png", "PLATINUM": "/challenges-images/2023004-PLATINUM.png", "DIAMOND": "/challenges-images/2023004-DIAMOND.png", "MASTER": "/challenges-images/2023004-MASTER.png", "GRANDMASTER": "/challenges-images/2023004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023004-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023005, "name": "Non c'è fuga o nascondiglio: 2023", "description": "Uccidi nemici vicino a una delle loro torri in ARAM", "shortDescription": "Uccidi i nemici vicino a una delle loro torri", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023005-IRON.png", "BRONZE": "/challenges-images/2023005-BRONZE.png", "SILVER": "/challenges-images/2023005-SILVER.png", "GOLD": "/challenges-images/2023005-GOLD.png", "PLATINUM": "/challenges-images/2023005-PLATINUM.png", "DIAMOND": "/challenges-images/2023005-DIAMOND.png", "MASTER": "/challenges-images/2023005-MASTER.png", "GRANDMASTER": "/challenges-images/2023005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 60}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 140}, "MASTER": {"value": 200}, "GRANDMASTER": {"value": 250}, "CHALLENGER": {"value": 300}}}, {"id": 2023006, "name": "Mira impeccabile: 2023", "description": "Colpisci con palle di neve i campioni in ARAM", "shortDescription": "Colpisci con le palle di neve i campioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023006-IRON.png", "BRONZE": "/challenges-images/2023006-BRONZE.png", "SILVER": "/challenges-images/2023006-SILVER.png", "GOLD": "/challenges-images/2023006-GOLD.png", "PLATINUM": "/challenges-images/2023006-PLATINUM.png", "DIAMOND": "/challenges-images/2023006-DIAMOND.png", "MASTER": "/challenges-images/2023006-MASTER.png", "GRANDMASTER": "/challenges-images/2023006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 100}, "PLATINUM": {"value": 144}, "DIAMOND": {"value": 200}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 300}, "CHALLENGER": {"value": 400}}}, {"id": 2023007, "name": "Uccisore leggendario: 2023", "description": "Uccidi il Barone Nashor in singolo", "shortDescription": "Uccidi il Barone Nashor in singolo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023007-IRON.png", "BRONZE": "/challenges-images/2023007-BRONZE.png", "SILVER": "/challenges-images/2023007-SILVER.png", "GOLD": "/challenges-images/2023007-GOLD.png", "PLATINUM": "/challenges-images/2023007-PLATINUM.png", "DIAMOND": "/challenges-images/2023007-DIAMOND.png", "MASTER": "/challenges-images/2023007-MASTER.png", "GRANDMASTER": "/challenges-images/2023007-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023007-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023008, "name": "Primeggiare su chiunque: 2023", "description": "Ottieni almeno tre livelli di vantaggio sull'avversario nel tuo ruolo in qualsiasi momento della partita", "shortDescription": "Supera di più livelli l'avversario nel tuo ruolo in qualsiasi momento", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023008-IRON.png", "BRONZE": "/challenges-images/2023008-BRONZE.png", "SILVER": "/challenges-images/2023008-SILVER.png", "GOLD": "/challenges-images/2023008-GOLD.png", "PLATINUM": "/challenges-images/2023008-PLATINUM.png", "DIAMOND": "/challenges-images/2023008-DIAMOND.png", "MASTER": "/challenges-images/2023008-MASTER.png", "GRANDMASTER": "/challenges-images/2023008-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023008-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 6}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 20}, "CHALLENGER": {"value": 30}}}, {"id": 2023009, "name": "Ho lasciato il segno: 2023", "description": "Vinci partite dopo uno svantaggio di 15 uccisioni", "shortDescription": "Vinci dopo uno svantaggio di 15 uccisioni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023009-IRON.png", "BRONZE": "/challenges-images/2023009-BRONZE.png", "SILVER": "/challenges-images/2023009-SILVER.png", "GOLD": "/challenges-images/2023009-GOLD.png", "PLATINUM": "/challenges-images/2023009-PLATINUM.png", "DIAMOND": "/challenges-images/2023009-DIAMOND.png", "MASTER": "/challenges-images/2023009-MASTER.png", "GRANDMASTER": "/challenges-images/2023009-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023009-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 202301, "name": "<PERSON>mm<PERSON><PERSON>", "description": "Vinci partite con otto o più uccisioni senza morire", "shortDescription": "Vinci una partita con più di otto uccisioni senza morire", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202301-IRON.png", "BRONZE": "/challenges-images/202301-BRONZE.png", "SILVER": "/challenges-images/202301-SILVER.png", "GOLD": "/challenges-images/202301-GOLD.png", "PLATINUM": "/challenges-images/202301-PLATINUM.png", "DIAMOND": "/challenges-images/202301-DIAMOND.png", "MASTER": "/challenges-images/202301-MASTER.png", "GRANDMASTER": "/challenges-images/202301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202301-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 2023010, "name": "<PERSON><PERSON><PERSON> solo di me: 2023", "description": "Ottieni 12 o più assist senza morire", "shortDescription": "Ottieni una serie di almeno 12 assist senza morire", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023010-IRON.png", "BRONZE": "/challenges-images/2023010-BRONZE.png", "SILVER": "/challenges-images/2023010-SILVER.png", "GOLD": "/challenges-images/2023010-GOLD.png", "PLATINUM": "/challenges-images/2023010-PLATINUM.png", "DIAMOND": "/challenges-images/2023010-DIAMOND.png", "MASTER": "/challenges-images/2023010-MASTER.png", "GRANDMASTER": "/challenges-images/2023010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 9}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 15}, "MASTER": {"value": 20}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2023011, "name": "Prima vittima: 2023", "description": "Distruggi la prima torre in meno di 10 minuti", "shortDescription": "Distruggi la prima torre in meno di 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023011-IRON.png", "BRONZE": "/challenges-images/2023011-BRONZE.png", "SILVER": "/challenges-images/2023011-SILVER.png", "GOLD": "/challenges-images/2023011-GOLD.png", "PLATINUM": "/challenges-images/2023011-PLATINUM.png", "DIAMOND": "/challenges-images/2023011-DIAMOND.png", "MASTER": "/challenges-images/2023011-MASTER.png", "GRANDMASTER": "/challenges-images/2023011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023011-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 2023012, "name": "Ti copro: 2023", "description": "Salva un alleato che avrebbe subito danni letali con una cura o uno scudo", "shortDescription": "Salva un alleato con una cura o uno scudo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023012-IRON.png", "BRONZE": "/challenges-images/2023012-BRONZE.png", "SILVER": "/challenges-images/2023012-SILVER.png", "GOLD": "/challenges-images/2023012-GOLD.png", "PLATINUM": "/challenges-images/2023012-PLATINUM.png", "DIAMOND": "/challenges-images/2023012-DIAMOND.png", "MASTER": "/challenges-images/2023012-MASTER.png", "GRANDMASTER": "/challenges-images/2023012-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023012-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 35}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 2023013, "name": "<PERSON><PERSON>, insieme: 2023", "description": "Gioca con la stessa squadra in tornei Clash differenti", "shortDescription": "Gioca con la stessa squadra nei tornei Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023013-IRON.png", "BRONZE": "/challenges-images/2023013-BRONZE.png", "SILVER": "/challenges-images/2023013-SILVER.png", "GOLD": "/challenges-images/2023013-GOLD.png", "PLATINUM": "/challenges-images/2023013-PLATINUM.png", "DIAMOND": "/challenges-images/2023013-DIAMOND.png", "MASTER": "/challenges-images/2023013-MASTER.png", "GRANDMASTER": "/challenges-images/2023013-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023013-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}}}, {"id": 2023014, "name": "Un quintetto per fare la storia: 2023", "description": "Ottieni vittorie con lo stesso gruppo di 5 giocatori", "shortDescription": "Ottieni delle vittorie con un gruppo organizzato da 5", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023014-IRON.png", "BRONZE": "/challenges-images/2023014-BRONZE.png", "SILVER": "/challenges-images/2023014-SILVER.png", "GOLD": "/challenges-images/2023014-GOLD.png", "PLATINUM": "/challenges-images/2023014-PLATINUM.png", "DIAMOND": "/challenges-images/2023014-DIAMOND.png", "MASTER": "/challenges-images/2023014-MASTER.png", "GRANDMASTER": "/challenges-images/2023014-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023014-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 13}, "CHALLENGER": {"value": 18}}}, {"id": 2023015, "name": "Una piacevole agonia: 2023", "description": "Ottieni il Primo sangue", "shortDescription": "Ottieni il Primo sangue", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2023015-IRON.png", "BRONZE": "/challenges-images/2023015-BRONZE.png", "SILVER": "/challenges-images/2023015-SILVER.png", "GOLD": "/challenges-images/2023015-GOLD.png", "PLATINUM": "/challenges-images/2023015-PLATINUM.png", "DIAMOND": "/challenges-images/2023015-DIAMOND.png", "MASTER": "/challenges-images/2023015-MASTER.png", "GRANDMASTER": "/challenges-images/2023015-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023015-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 13}, "CHALLENGER": {"value": 18}}}, {"id": 2023016, "name": "Immobilizzazione immediata: 2023", "description": "Immobilizza i campioni nemici", "shortDescription": "Immobilizza i nemici", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023016-IRON.png", "BRONZE": "/challenges-images/2023016-BRONZE.png", "SILVER": "/challenges-images/2023016-SILVER.png", "GOLD": "/challenges-images/2023016-GOLD.png", "PLATINUM": "/challenges-images/2023016-PLATINUM.png", "DIAMOND": "/challenges-images/2023016-DIAMOND.png", "MASTER": "/challenges-images/2023016-MASTER.png", "GRANDMASTER": "/challenges-images/2023016-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023016-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 300}, "SILVER": {"value": 600}, "GOLD": {"value": 900}, "PLATINUM": {"value": 1200}, "DIAMOND": {"value": 1500}, "MASTER": {"value": 2000}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 2023017, "name": "È l'ora del banchetto: 2023", "description": "Uccidi i mostri della giungla nella giungla nemica", "shortDescription": "Uccidi i mostri della giungla nella giungla nemica", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023017-IRON.png", "BRONZE": "/challenges-images/2023017-BRONZE.png", "SILVER": "/challenges-images/2023017-SILVER.png", "GOLD": "/challenges-images/2023017-GOLD.png", "PLATINUM": "/challenges-images/2023017-PLATINUM.png", "DIAMOND": "/challenges-images/2023017-DIAMOND.png", "MASTER": "/challenges-images/2023017-MASTER.png", "GRANDMASTER": "/challenges-images/2023017-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023017-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 100}, "PLATINUM": {"value": 200}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 750}, "CHALLENGER": {"value": 1000}}}, {"id": 2023018, "name": "Affrontare l'oscurità: 2023", "description": "Piazza lumi di controllo utili. I lumi utili sono tutti quelli piazzati fuori dalla tua base.", "shortDescription": "Posiziona i lumi di controllo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023018-IRON.png", "BRONZE": "/challenges-images/2023018-BRONZE.png", "SILVER": "/challenges-images/2023018-SILVER.png", "GOLD": "/challenges-images/2023018-GOLD.png", "PLATINUM": "/challenges-images/2023018-PLATINUM.png", "DIAMOND": "/challenges-images/2023018-DIAMOND.png", "MASTER": "/challenges-images/2023018-MASTER.png", "GRANDMASTER": "/challenges-images/2023018-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023018-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 2023019, "name": "Fragile: 2023", "description": "Distruggi le torri", "shortDescription": "Distruggi le torri", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023019-IRON.png", "BRONZE": "/challenges-images/2023019-BRONZE.png", "SILVER": "/challenges-images/2023019-SILVER.png", "GOLD": "/challenges-images/2023019-GOLD.png", "PLATINUM": "/challenges-images/2023019-PLATINUM.png", "DIAMOND": "/challenges-images/2023019-DIAMOND.png", "MASTER": "/challenges-images/2023019-MASTER.png", "GRANDMASTER": "/challenges-images/2023019-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023019-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 202302, "name": "Pro<PERSON><PERSON>", "description": "Di<PERSON>a leggendario (otto u<PERSON> consecutive) entro 15 minuti", "shortDescription": "Diventa leggendario entro 15 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202302-IRON.png", "BRONZE": "/challenges-images/202302-BRONZE.png", "SILVER": "/challenges-images/202302-SILVER.png", "GOLD": "/challenges-images/202302-GOLD.png", "PLATINUM": "/challenges-images/202302-PLATINUM.png", "DIAMOND": "/challenges-images/202302-DIAMOND.png", "MASTER": "/challenges-images/202302-MASTER.png", "GRANDMASTER": "/challenges-images/202302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202302-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Pro<PERSON><PERSON>"}]}, "DIAMOND": {"value": 4}, "MASTER": {"value": 7}}}, {"id": 2023020, "name": "Inarrestabile, imperturbabile: 2023", "description": "Vinci delle partite in coda come riempimento", "shortDescription": "Vinci delle partite in coda come riempimento", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/2023020-IRON.png", "BRONZE": "/challenges-images/2023020-BRONZE.png", "SILVER": "/challenges-images/2023020-SILVER.png", "GOLD": "/challenges-images/2023020-GOLD.png", "PLATINUM": "/challenges-images/2023020-PLATINUM.png", "DIAMOND": "/challenges-images/2023020-DIAMOND.png", "MASTER": "/challenges-images/2023020-MASTER.png", "GRANDMASTER": "/challenges-images/2023020-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2023020-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 7}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 20}, "CHALLENGER": {"value": 25}}}, {"id": 202303, "name": "Invincibile", "description": "Vinci una partita senza morire con campioni differenti", "shortDescription": "Vinci senza morire <em>con campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202303-IRON.png", "BRONZE": "/challenges-images/202303-BRONZE.png", "SILVER": "/challenges-images/202303-SILVER.png", "GOLD": "/challenges-images/202303-GOLD.png", "PLATINUM": "/challenges-images/202303-PLATINUM.png", "DIAMOND": "/challenges-images/202303-DIAMOND.png", "MASTER": "/challenges-images/202303-MASTER.png", "GRANDMASTER": "/challenges-images/202303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 50}, "MASTER": {"value": 75}}}, {"id": 202304, "name": "Dec<PERSON><PERSON>", "description": "Otti<PERSON> due Pentakill in una sola partita", "shortDescription": "Ottieni due pentakill in una partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202304-IRON.png", "BRONZE": "/challenges-images/202304-BRONZE.png", "SILVER": "/challenges-images/202304-SILVER.png", "GOLD": "/challenges-images/202304-GOLD.png", "PLATINUM": "/challenges-images/202304-PLATINUM.png", "DIAMOND": "/challenges-images/202304-DIAMOND.png", "MASTER": "/challenges-images/202304-MASTER.png", "GRANDMASTER": "/challenges-images/202304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202304-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}}}, {"id": 202305, "name": "<PERSON><PERSON>", "description": "Ottieni più di 450 oro al minuto", "shortDescription": "Ottieni più di 450 oro al minuto in una partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/202305-IRON.png", "BRONZE": "/challenges-images/202305-BRONZE.png", "SILVER": "/challenges-images/202305-SILVER.png", "GOLD": "/challenges-images/202305-GOLD.png", "PLATINUM": "/challenges-images/202305-PLATINUM.png", "DIAMOND": "/challenges-images/202305-DIAMOND.png", "MASTER": "/challenges-images/202305-MASTER.png", "GRANDMASTER": "/challenges-images/202305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/202305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 6}, "SILVER": {"value": 14}, "GOLD": {"value": 30}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 180}, "MASTER": {"value": 350}}}, {"id": 2024100, "name": "Stagionale 2024 - Split 1", "description": "Ottieni progressi dalle sfide nel gruppo Stagionale 2024 - Split 1", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Stagionale 2024 - Split 1", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024100-IRON.png", "BRONZE": "/challenges-images/2024100-BRONZE.png", "SILVER": "/challenges-images/2024100-SILVER.png", "GOLD": "/challenges-images/2024100-GOLD.png", "PLATINUM": "/challenges-images/2024100-PLATINUM.png", "DIAMOND": "/challenges-images/2024100-DIAMOND.png", "MASTER": "/challenges-images/2024100-MASTER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 135}, "PLATINUM": {"value": 215}, "DIAMOND": {"value": 380}, "MASTER": {"value": 650, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Creatura del Vuoto"}]}}}, {"id": 2024101, "name": "Armi leggendarie: 2024 - Split 1", "description": "Vinci con diversi ogget<PERSON>", "shortDescription": "Vinci con <em>divers<PERSON> og<PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024101-IRON.png", "BRONZE": "/challenges-images/2024101-BRONZE.png", "SILVER": "/challenges-images/2024101-SILVER.png", "GOLD": "/challenges-images/2024101-GOLD.png", "PLATINUM": "/challenges-images/2024101-PLATINUM.png", "DIAMOND": "/challenges-images/2024101-DIAMOND.png", "MASTER": "/challenges-images/2024101-MASTER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 16}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 60}, "MASTER": {"value": 85}}}, {"id": 2024102, "name": "Contributo di supporto: 2024 - Split 1", "description": "Completa del tutto una missione di un oggetto di supporto (1000 oro) in meno di 14 minuti", "shortDescription": "Completa del tutto la tua missione di supporto entro 14 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024102-IRON.png", "BRONZE": "/challenges-images/2024102-BRONZE.png", "SILVER": "/challenges-images/2024102-SILVER.png", "GOLD": "/challenges-images/2024102-GOLD.png", "PLATINUM": "/challenges-images/2024102-PLATINUM.png", "DIAMOND": "/challenges-images/2024102-DIAMOND.png", "MASTER": "/challenges-images/2024102-MASTER.png", "GRANDMASTER": "/challenges-images/2024102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 55}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 2024103, "name": "Cacciatore di granchi: 2024 - Split 1", "description": "Come giu<PERSON><PERSON>, prendi entrambi gli Argogranchi iniziali", "shortDescription": "Prendi entrambi gli Argogranchi iniziali", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024103-IRON.png", "BRONZE": "/challenges-images/2024103-BRONZE.png", "SILVER": "/challenges-images/2024103-SILVER.png", "GOLD": "/challenges-images/2024103-GOLD.png", "PLATINUM": "/challenges-images/2024103-PLATINUM.png", "DIAMOND": "/challenges-images/2024103-DIAMOND.png", "MASTER": "/challenges-images/2024103-MASTER.png", "GRANDMASTER": "/challenges-images/2024103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 25}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 60}, "CHALLENGER": {"value": 90}}}, {"id": 2024104, "name": "<PERSON>'erba alta ha gli occhi: 2024 - Split 1", "description": "Termina la partita con il 20% del punteggio di visione in più rispetto all'avversario nel tuo ruolo ", "shortDescription": "Termina la partita con il 20% del punteggio di visione in più rispetto all'avversario nel tuo ruolo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024104-IRON.png", "BRONZE": "/challenges-images/2024104-BRONZE.png", "SILVER": "/challenges-images/2024104-SILVER.png", "GOLD": "/challenges-images/2024104-GOLD.png", "PLATINUM": "/challenges-images/2024104-PLATINUM.png", "DIAMOND": "/challenges-images/2024104-DIAMOND.png", "MASTER": "/challenges-images/2024104-MASTER.png", "GRANDMASTER": "/challenges-images/2024104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 40}, "MASTER": {"value": 55}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 2024105, "name": "<PERSON><PERSON>, tutti i campioni: 2024 - Split 1", "description": "Raggiungi il livello S- o superiore con campioni diversi in ARAM", "shortDescription": "<PERSON><PERSON><PERSON> S- <em>con campioni differenti</em> ", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024105-IRON.png", "BRONZE": "/challenges-images/2024105-BRONZE.png", "SILVER": "/challenges-images/2024105-SILVER.png", "GOLD": "/challenges-images/2024105-GOLD.png", "PLATINUM": "/challenges-images/2024105-PLATINUM.png", "DIAMOND": "/challenges-images/2024105-DIAMOND.png", "MASTER": "/challenges-images/2024105-MASTER.png", "GRANDMASTER": "/challenges-images/2024105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 7}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 18}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 50}}}, {"id": 2024106, "name": "Pesca di Wurm: 2024 - Split 1", "description": "Elimina i Baroni", "shortDescription": "Elimina i Baroni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024106-IRON.png", "BRONZE": "/challenges-images/2024106-BRONZE.png", "SILVER": "/challenges-images/2024106-SILVER.png", "GOLD": "/challenges-images/2024106-GOLD.png", "PLATINUM": "/challenges-images/2024106-PLATINUM.png", "DIAMOND": "/challenges-images/2024106-DIAMOND.png", "MASTER": "/challenges-images/2024106-MASTER.png", "GRANDMASTER": "/challenges-images/2024106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024106-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 14}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 30}, "MASTER": {"value": 45}, "GRANDMASTER": {"value": 65}, "CHALLENGER": {"value": 90}}}, {"id": 2024107, "name": "Pro<PERSON><PERSON> sotto il naso: 2024 - Split 1", "description": "Richiama senza farti vedere da un campione nemico nelle vicinanze", "shortDescription": "Richiama senza farti vedere da un campione nelle vicinanze", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024107-IRON.png", "BRONZE": "/challenges-images/2024107-BRONZE.png", "SILVER": "/challenges-images/2024107-SILVER.png", "GOLD": "/challenges-images/2024107-GOLD.png", "PLATINUM": "/challenges-images/2024107-PLATINUM.png", "DIAMOND": "/challenges-images/2024107-DIAMOND.png", "MASTER": "/challenges-images/2024107-MASTER.png", "GRANDMASTER": "/challenges-images/2024107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024107-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}}}, {"id": 2024108, "name": "Sempre in tempo: 2024 - <PERSON> 1", "description": "Uccidi mostri epici entro 30 secondi dalla loro generazione. I mostri epici includono i <PERSON>aghi, le larve del Vuoto, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Uccidi mostri epici entro 30 secondi dalla generazione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024108-IRON.png", "BRONZE": "/challenges-images/2024108-BRONZE.png", "SILVER": "/challenges-images/2024108-SILVER.png", "GOLD": "/challenges-images/2024108-GOLD.png", "PLATINUM": "/challenges-images/2024108-PLATINUM.png", "DIAMOND": "/challenges-images/2024108-DIAMOND.png", "MASTER": "/challenges-images/2024108-MASTER.png", "GRANDMASTER": "/challenges-images/2024108-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024108-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 7}, "PLATINUM": {"value": 11}, "DIAMOND": {"value": 16}, "MASTER": {"value": 22}, "GRANDMASTER": {"value": 30}, "CHALLENGER": {"value": 40}}}, {"id": 2024200, "name": "Stagionale 2024 - Split 2", "description": "Ottieni progressi dalle sfide nel gruppo Stagionale 2024 - Split 2", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Stagionale 2024 - Split 2", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024200-IRON.png", "BRONZE": "/challenges-images/2024200-BRONZE.png", "SILVER": "/challenges-images/2024200-SILVER.png", "GOLD": "/challenges-images/2024200-GOLD.png", "PLATINUM": "/challenges-images/2024200-PLATINUM.png", "DIAMOND": "/challenges-images/2024200-DIAMOND.png", "MASTER": "/challenges-images/2024200-MASTER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 135}, "PLATINUM": {"value": 215}, "DIAMOND": {"value": 380}, "MASTER": {"value": 650, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Infernale"}]}}}, {"id": 2024201, "name": "Distributore d'oscurità: 2024 - Split 2", "description": "Elimina il maggior numero di lumi in una partita", "shortDescription": "Elimina il maggior numero di lumi in una partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024201-IRON.png", "BRONZE": "/challenges-images/2024201-BRONZE.png", "SILVER": "/challenges-images/2024201-SILVER.png", "GOLD": "/challenges-images/2024201-GOLD.png", "PLATINUM": "/challenges-images/2024201-PLATINUM.png", "DIAMOND": "/challenges-images/2024201-DIAMOND.png", "MASTER": "/challenges-images/2024201-MASTER.png", "GRANDMASTER": "/challenges-images/2024201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024201-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 7}, "MASTER": {"value": 10}}}, {"id": 2024202, "name": "Prestazione migliore: 2024 - Split 2", "description": "Ottieni una valutazione almeno di grado S-", "shortDescription": "Ottieni una valutazione almeno di grado S-", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024202-IRON.png", "BRONZE": "/challenges-images/2024202-BRONZE.png", "SILVER": "/challenges-images/2024202-SILVER.png", "GOLD": "/challenges-images/2024202-GOLD.png", "PLATINUM": "/challenges-images/2024202-PLATINUM.png", "DIAMOND": "/challenges-images/2024202-DIAMOND.png", "MASTER": "/challenges-images/2024202-MASTER.png", "GRANDMASTER": "/challenges-images/2024202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 4}, "SILVER": {"value": 7}, "GOLD": {"value": 11}, "PLATINUM": {"value": 16}, "DIAMOND": {"value": 22}, "MASTER": {"value": 30}}}, {"id": 2024203, "name": "Spara e stordisci: 2024 - Split 2", "description": "Immob<PERSON><PERSON> ed elimina un nemico con un alleato", "shortDescription": "Immob<PERSON><PERSON> ed elimina un nemico con un alleato", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024203-IRON.png", "BRONZE": "/challenges-images/2024203-BRONZE.png", "SILVER": "/challenges-images/2024203-SILVER.png", "GOLD": "/challenges-images/2024203-GOLD.png", "PLATINUM": "/challenges-images/2024203-PLATINUM.png", "DIAMOND": "/challenges-images/2024203-DIAMOND.png", "MASTER": "/challenges-images/2024203-MASTER.png", "GRANDMASTER": "/challenges-images/2024203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 120}, "MASTER": {"value": 200}}}, {"id": 2024204, "name": "Bel tentativo: 2024 - Split 2", "description": "Uccidi i nemici sotto la tua torre", "shortDescription": "Uccidi i nemici sotto la tua torre", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024204-IRON.png", "BRONZE": "/challenges-images/2024204-BRONZE.png", "SILVER": "/challenges-images/2024204-SILVER.png", "GOLD": "/challenges-images/2024204-GOLD.png", "PLATINUM": "/challenges-images/2024204-PLATINUM.png", "DIAMOND": "/challenges-images/2024204-DIAMOND.png", "MASTER": "/challenges-images/2024204-MASTER.png", "GRANDMASTER": "/challenges-images/2024204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024204-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 8}, "MASTER": {"value": 15}}}, {"id": 2024205, "name": "<PERSON><PERSON><PERSON> campioni, non minion: 2024 - Split 2", "description": "Ottieni eliminazioni in ARAM", "shortDescription": "Ottieni eliminazioni in ARAM", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024205-IRON.png", "BRONZE": "/challenges-images/2024205-BRONZE.png", "SILVER": "/challenges-images/2024205-SILVER.png", "GOLD": "/challenges-images/2024205-GOLD.png", "PLATINUM": "/challenges-images/2024205-PLATINUM.png", "DIAMOND": "/challenges-images/2024205-DIAMOND.png", "MASTER": "/challenges-images/2024205-MASTER.png", "GRANDMASTER": "/challenges-images/2024205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 60}, "SILVER": {"value": 120}, "GOLD": {"value": 240}, "PLATINUM": {"value": 420}, "DIAMOND": {"value": 750}, "MASTER": {"value": 1000}}}, {"id": 2024206, "name": "Annienta il Vuoto: 2024 - Split 2", "description": "Elimina mostri del Vuoto (<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Rovobestia cremisi del Vuoto e Guardiano di quarzo del Vuoto)", "shortDescription": "Elimina mostri del Vuoto", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024206-IRON.png", "BRONZE": "/challenges-images/2024206-BRONZE.png", "SILVER": "/challenges-images/2024206-SILVER.png", "GOLD": "/challenges-images/2024206-GOLD.png", "PLATINUM": "/challenges-images/2024206-PLATINUM.png", "DIAMOND": "/challenges-images/2024206-DIAMOND.png", "MASTER": "/challenges-images/2024206-MASTER.png", "GRANDMASTER": "/challenges-images/2024206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 40}, "MASTER": {"value": 55}}}, {"id": 2024207, "name": "Scintille e fuochi d'artificio: 2024 - Split 2", "description": "Partecipa a Batti il pugno con almeno un altro giocatore in partite combinate senza IA", "shortDescription": "Partecipa a Batti il pugno in partite combinate", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024207-IRON.png", "BRONZE": "/challenges-images/2024207-BRONZE.png", "SILVER": "/challenges-images/2024207-SILVER.png", "GOLD": "/challenges-images/2024207-GOLD.png", "PLATINUM": "/challenges-images/2024207-PLATINUM.png", "DIAMOND": "/challenges-images/2024207-DIAMOND.png", "MASTER": "/challenges-images/2024207-MASTER.png", "GRANDMASTER": "/challenges-images/2024207-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024207-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 120}, "MASTER": {"value": 160}}}, {"id": 2024208, "name": "Il fuoco divampa: 2024 - Split 2", "description": "Raccogli Braci infernali mentre è attivo il Terreno infernale", "shortDescription": "Raccogli Braci infernali", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024208-IRON.png", "BRONZE": "/challenges-images/2024208-BRONZE.png", "SILVER": "/challenges-images/2024208-SILVER.png", "GOLD": "/challenges-images/2024208-GOLD.png", "PLATINUM": "/challenges-images/2024208-PLATINUM.png", "DIAMOND": "/challenges-images/2024208-DIAMOND.png", "MASTER": "/challenges-images/2024208-MASTER.png", "CHALLENGER": "/challenges-images/2024208-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 30}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 50}, "MASTER": {"value": 60}}}, {"id": 2024300, "name": "Stagionale 2024 - Split 3", "description": "O<PERSON><PERSON> punti dalle sfide nel gruppo", "shortDescription": "<PERSON><PERSON><PERSON> punti dalle sfide nel gruppo Stagionale 2024 - Split 3", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024300-IRON.png", "BRONZE": "/challenges-images/2024300-BRONZE.png", "SILVER": "/challenges-images/2024300-SILVER.png", "GOLD": "/challenges-images/2024300-GOLD.png", "PLATINUM": "/challenges-images/2024300-PLATINUM.png", "DIAMOND": "/challenges-images/2024300-DIAMOND.png", "MASTER": "/challenges-images/2024300-MASTER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 40}, "GOLD": {"value": 100}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 380}, "MASTER": {"value": 600, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ciallengier '24"}]}}}, {"id": 2024301, "name": "Dominazione in corsia: 2024 - Split 3", "description": "Termina la fase in corsia (14 minuti) con il 20% di oro ed esperienza in più rispetto all'avversario nel tuo ruolo ", "shortDescription": "Ottieni un vantaggio del 20% di oro e PE dopo 14 minuti", "hasLeaderboard": false, "levelToIconPath": {"NONE": "/challenges-images/2024301-IRON.png", "BRONZE": "/challenges-images/2024301-BRONZE.png", "SILVER": "/challenges-images/2024301-SILVER.png", "GOLD": "/challenges-images/2024301-GOLD.png", "PLATINUM": "/challenges-images/2024301-PLATINUM.png", "DIAMOND": "/challenges-images/2024301-DIAMOND.png", "MASTER": "/challenges-images/2024301-MASTER.png", "GRANDMASTER": "/challenges-images/2024301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024301-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}}}, {"id": 2024302, "name": "A viso aperto: 2024 - Split 3", "description": "Uccidi nemici vicino a una delle loro torri in ARAM", "shortDescription": "Uccidi nemici vicino a una delle loro torri", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024302-IRON.png", "BRONZE": "/challenges-images/2024302-BRONZE.png", "SILVER": "/challenges-images/2024302-SILVER.png", "GOLD": "/challenges-images/2024302-GOLD.png", "PLATINUM": "/challenges-images/2024302-PLATINUM.png", "DIAMOND": "/challenges-images/2024302-DIAMOND.png", "MASTER": "/challenges-images/2024302-MASTER.png", "GRANDMASTER": "/challenges-images/2024302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 15}, "GOLD": {"value": 20}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 65}, "MASTER": {"value": 100}}}, {"id": 2024303, "name": "Al prezzo di uno: 2024 - Split 3", "description": "Uccidi due giocatori con il lancio di una sola abilità ", "shortDescription": "Uccidi due giocatori con il lancio di una sola abilità ", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024303-IRON.png", "BRONZE": "/challenges-images/2024303-BRONZE.png", "SILVER": "/challenges-images/2024303-SILVER.png", "GOLD": "/challenges-images/2024303-GOLD.png", "PLATINUM": "/challenges-images/2024303-PLATINUM.png", "DIAMOND": "/challenges-images/2024303-DIAMOND.png", "MASTER": "/challenges-images/2024303-MASTER.png", "GRANDMASTER": "/challenges-images/2024303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024303-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 3}, "MASTER": {"value": 5}}}, {"id": 2024304, "name": "Sistema di difesa globale: 2024 - Split 3", "description": "Mantieni un lume di controllo attivo nel fiume o nella metà nemica della mappa per più del 65% della durata della partita", "shortDescription": "Tieni un lume di controllo nella giungla nemica per oltre il 65% della partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024304-IRON.png", "BRONZE": "/challenges-images/2024304-BRONZE.png", "SILVER": "/challenges-images/2024304-SILVER.png", "GOLD": "/challenges-images/2024304-GOLD.png", "PLATINUM": "/challenges-images/2024304-PLATINUM.png", "DIAMOND": "/challenges-images/2024304-DIAMOND.png", "MASTER": "/challenges-images/2024304-MASTER.png", "GRANDMASTER": "/challenges-images/2024304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024304-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}}}, {"id": 2024305, "name": "Maestro zelante: 2024 - Split 3", "description": "Ottieni pietre miliari di Maestria con un qualsiasi campione", "shortDescription": "<PERSON>ttieni pietre miliari di Maestria", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024305-IRON.png", "BRONZE": "/challenges-images/2024305-BRONZE.png", "SILVER": "/challenges-images/2024305-SILVER.png", "GOLD": "/challenges-images/2024305-GOLD.png", "PLATINUM": "/challenges-images/2024305-PLATINUM.png", "DIAMOND": "/challenges-images/2024305-DIAMOND.png", "MASTER": "/challenges-images/2024305-MASTER.png", "GRANDMASTER": "/challenges-images/2024305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 25}}}, {"id": 2024306, "name": "Un Fruttomieloso al giorno: 2024 - Split 3", "description": "Ottieni salute o uno scudo tramite fonti di guarigione legate alla mappa (contano anche i Fruttomielosi, gli altari della salute e i Fiori del Potere)", "shortDescription": "Ripristina salute tramite fonti di guarigione legate alla mappa", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024306-IRON.png", "BRONZE": "/challenges-images/2024305-BRONZE.png", "SILVER": "/challenges-images/2024305-SILVER.png", "GOLD": "/challenges-images/2024305-GOLD.png", "PLATINUM": "/challenges-images/2024305-PLATINUM.png", "DIAMOND": "/challenges-images/2024305-DIAMOND.png", "MASTER": "/challenges-images/2024305-MASTER.png", "GRANDMASTER": "/challenges-images/2024305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1000}, "BRONZE": {"value": 2500}, "SILVER": {"value": 4500}, "GOLD": {"value": 7000}, "PLATINUM": {"value": 10000}, "DIAMOND": {"value": 14000}, "MASTER": {"value": 20000}}}, {"id": 2024307, "name": "Scelte a sorpresa: 2024 - Split 3", "description": "Ottieni 12 pietre miliari di Maestria per i campioni nel tuo Set di Maestria", "shortDescription": "Completa un Set di Maestria", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024307-IRON.png", "BRONZE": "/challenges-images/2024307-BRONZE.png", "SILVER": "/challenges-images/2024307-SILVER.png", "GOLD": "/challenges-images/2024307-GOLD.png", "PLATINUM": "/challenges-images/2024307-PLATINUM.png", "DIAMOND": "/challenges-images/2024307-DIAMOND.png", "MASTER": "/challenges-images/2024307-MASTER.png", "GRANDMASTER": "/challenges-images/2024307-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024307-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 12}}}, {"id": 2024308, "name": "Campione oceanico: 2024 - Split 3", "description": "Vinci partite con campioni differenti (contano tutte le modalità)", "shortDescription": "Vinci partite con <em>campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/2024308-IRON.png", "BRONZE": "/challenges-images/2024308-BRONZE.png", "SILVER": "/challenges-images/2024308-IRON.png", "GOLD": "/challenges-images/2024308-GOLD.png", "PLATINUM": "/challenges-images/2024308-PLATINUM.png", "DIAMOND": "/challenges-images/2024308-DIAMOND.png", "MASTER": "/challenges-images/2024308-MASTER.png", "GRANDMASTER": "/challenges-images/2024308-GRANDMASTER.png", "CHALLENGER": "/challenges-images/2024308-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 20}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 30}, "MASTER": {"value": 35}}}, {"id": 203000, "name": "Potenza", "description": "Ottieni progressi dalle sfide nei gruppi Dono naturale, Colosso, Uccisore e Predatore", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Dono naturale, Colosso, Uccisore e Predatore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203000-IRON.png", "BRONZE": "/challenges-images/203000-BRONZE.png", "SILVER": "/challenges-images/203000-SILVER.png", "GOLD": "/challenges-images/203000-GOLD.png", "PLATINUM": "/challenges-images/203000-PLATINUM.png", "DIAMOND": "/challenges-images/203000-DIAMOND.png", "MASTER": "/challenges-images/203000-MASTER.png", "GRANDMASTER": "/challenges-images/203000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 105}, "SILVER": {"value": 190}, "GOLD": {"value": 440}, "PLATINUM": {"value": 700}, "DIAMOND": {"value": 1250}, "MASTER": {"value": 2100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON><PERSON>"}]}}}, {"id": 203100, "name": "Dono naturale", "description": "Ottieni progressi dalle sfide nel gruppo Dono naturale", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Dono naturale", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203100-IRON.png", "BRONZE": "/challenges-images/203100-BRONZE.png", "SILVER": "/challenges-images/203100-SILVER.png", "GOLD": "/challenges-images/203100-GOLD.png", "PLATINUM": "/challenges-images/203100-PLATINUM.png", "DIAMOND": "/challenges-images/203100-DIAMOND.png", "MASTER": "/challenges-images/203100-MASTER.png", "GRANDMASTER": "/challenges-images/203100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 45}, "GOLD": {"value": 95}, "PLATINUM": {"value": 145}, "DIAMOND": {"value": 250, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON><PERSON>"}]}, "MASTER": {"value": 400}}}, {"id": 203101, "name": "Impossibile sbagliare", "description": "Metti a segno 20 colpi mirati (abilità a distanza senza bersaglio) entro sette minuti", "shortDescription": "<PERSON>ti a segno 20 colpi mirati entro sette minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203101-IRON.png", "BRONZE": "/challenges-images/203101-BRONZE.png", "SILVER": "/challenges-images/203101-SILVER.png", "GOLD": "/challenges-images/203101-GOLD.png", "PLATINUM": "/challenges-images/203101-PLATINUM.png", "DIAMOND": "/challenges-images/203101-DIAMOND.png", "MASTER": "/challenges-images/203101-MASTER.png", "GRANDMASTER": "/challenges-images/203101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203101-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 5}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 25}, "MASTER": {"value": 50}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 203102, "name": "Che schivate!", "description": "Schiva cinque colpi mirati (abilità a distanza senza bersaglio) entro otto secondi", "shortDescription": "Schiva cinque colpi mirati entro otto secondi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203102-IRON.png", "BRONZE": "/challenges-images/203102-BRONZE.png", "SILVER": "/challenges-images/203102-SILVER.png", "GOLD": "/challenges-images/203102-GOLD.png", "PLATINUM": "/challenges-images/203102-PLATINUM.png", "DIAMOND": "/challenges-images/203102-DIAMOND.png", "MASTER": "/challenges-images/203102-MASTER.png", "GRANDMASTER": "/challenges-images/203102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 45}, "GOLD": {"value": 120, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON> vispi"}]}, "PLATINUM": {"value": 350}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2000}}}, {"id": 203103, "name": "Captain <PERSON>", "description": "Purifica te o qualcun altro da un effetto immobilizzante (usando gli incantesimi dell'evocatore Purificazione, Argento Vivo o Benedizione di Mikael) entro 0,25 secondi dall'inizio dell'effetto", "shortDescription": "Purifica un effetto immobilizzante entro 0,25 secondi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203103-IRON.png", "BRONZE": "/challenges-images/203103-BRONZE.png", "SILVER": "/challenges-images/203103-SILVER.png", "GOLD": "/challenges-images/203103-GOLD.png", "PLATINUM": "/challenges-images/203103-PLATINUM.png", "DIAMOND": "/challenges-images/203103-DIAMOND.png", "MASTER": "/challenges-images/203103-MASTER.png", "GRANDMASTER": "/challenges-images/203103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203103-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Captain <PERSON>"}]}}}, {"id": 203104, "name": "Grazie per l'aiuto", "description": "Ruba un mostro epico della giungla senza usare Punizione dell'evocatore. I mostri epici includono i draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Ruba un mostro epico senza Punizione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203104-IRON.png", "BRONZE": "/challenges-images/203104-BRONZE.png", "SILVER": "/challenges-images/203104-SILVER.png", "GOLD": "/challenges-images/203104-GOLD.png", "PLATINUM": "/challenges-images/203104-PLATINUM.png", "DIAMOND": "/challenges-images/203104-DIAMOND.png", "MASTER": "/challenges-images/203104-MASTER.png", "GRANDMASTER": "/challenges-images/203104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 8}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 40}, "MASTER": {"value": 75}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 300}}}, {"id": 203105, "name": "Cariche su cariche su cariche", "description": "Carica completamente il Tomo Mejai ruba-anime entro 20 minuti", "shortDescription": "Carica completamente il Tomo Mejai entro 20 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203105-IRON.png", "BRONZE": "/challenges-images/203105-BRONZE.png", "SILVER": "/challenges-images/203105-SILVER.png", "GOLD": "/challenges-images/203105-GOLD.png", "PLATINUM": "/challenges-images/203105-PLATINUM.png", "DIAMOND": "/challenges-images/203105-DIAMOND.png", "MASTER": "/challenges-images/203105-MASTER.png", "GRANDMASTER": "/challenges-images/203105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203105-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 13}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 50}}}, {"id": 203106, "name": "Al prezzo di uno", "description": "Uccidi due giocatori con il lancio di una sola abilità", "shortDescription": "Uccidi due giocatori con il lancio di una sola abilità", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203106-IRON.png", "BRONZE": "/challenges-images/203106-BRONZE.png", "SILVER": "/challenges-images/203106-SILVER.png", "GOLD": "/challenges-images/203106-GOLD.png", "PLATINUM": "/challenges-images/203106-PLATINUM.png", "DIAMOND": "/challenges-images/203106-DIAMOND.png", "MASTER": "/challenges-images/203106-MASTER.png", "GRANDMASTER": "/challenges-images/203106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203106-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 8}, "PLATINUM": {"value": 23}, "DIAMOND": {"value": 43}, "MASTER": {"value": 73}, "GRANDMASTER": {"value": 100}, "CHALLENGER": {"value": 150}}}, {"id": 203200, "name": "Colosso", "description": "Ottieni progressi dalle sfide nel gruppo Colosso", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Colosso", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203200-IRON.png", "BRONZE": "/challenges-images/203200-BRONZE.png", "SILVER": "/challenges-images/203200-SILVER.png", "GOLD": "/challenges-images/203200-GOLD.png", "PLATINUM": "/challenges-images/203200-PLATINUM.png", "DIAMOND": "/challenges-images/203200-DIAMOND.png", "MASTER": "/challenges-images/203200-MASTER.png", "GRANDMASTER": "/challenges-images/203200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Colosso"}]}, "MASTER": {"value": 250}}}, {"id": 203201, "name": "Neanche un graffio", "description": "Subisci 10.000 danni pre-resistenze dai campioni in un singolo combattimento senza morire", "shortDescription": "Subisci 10.000 danni in un combattimento e sopravvivi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203201-IRON.png", "BRONZE": "/challenges-images/203201-BRONZE.png", "SILVER": "/challenges-images/203201-SILVER.png", "GOLD": "/challenges-images/203201-GOLD.png", "PLATINUM": "/challenges-images/203201-PLATINUM.png", "DIAMOND": "/challenges-images/203201-DIAMOND.png", "MASTER": "/challenges-images/203201-MASTER.png", "GRANDMASTER": "/challenges-images/203201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Unità assoluta"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 65}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 125}}}, {"id": 203202, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "In un combattimento, ottieni uccisioni mentre subisci danni da tutti e cinque i campioni nemici e sopravvivi", "shortDescription": "In un combattimento, ottieni uccisioni mentre resisti ai danni di tutti e cinque i nemici e sopravvivi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203202-IRON.png", "BRONZE": "/challenges-images/203202-BRONZE.png", "SILVER": "/challenges-images/203202-SILVER.png", "GOLD": "/challenges-images/203202-GOLD.png", "PLATINUM": "/challenges-images/203202-PLATINUM.png", "DIAMOND": "/challenges-images/203202-DIAMOND.png", "MASTER": "/challenges-images/203202-MASTER.png", "GRANDMASTER": "/challenges-images/203202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203202-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 8}, "MASTER": {"value": 12}, "GRANDMASTER": {"value": 17}, "CHALLENGER": {"value": 25}}}, {"id": 203203, "name": "Vai dove vuoi", "description": "Subisci tre o più abilità di immobilizzazione in un singolo scontro e sopravvivi", "shortDescription": "Subisci tre effetti immobilizzanti in un combattimento e sopravvivi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203203-IRON.png", "BRONZE": "/challenges-images/203203-BRONZE.png", "SILVER": "/challenges-images/203203-SILVER.png", "GOLD": "/challenges-images/203203-GOLD.png", "PLATINUM": "/challenges-images/203203-PLATINUM.png", "DIAMOND": "/challenges-images/203203-DIAMOND.png", "MASTER": "/challenges-images/203203-MASTER.png", "GRANDMASTER": "/challenges-images/203203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 40}, "SILVER": {"value": 100}, "GOLD": {"value": 250}, "PLATINUM": {"value": 750}, "DIAMOND": {"value": 1500}, "MASTER": {"value": 3000}, "GRANDMASTER": {"value": 4000}, "CHALLENGER": {"value": 5000}}}, {"id": 203300, "name": "Uccisore", "description": "Ottieni progressi dalle sfide nel gruppo Uccisore", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Uccisore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203300-IRON.png", "BRONZE": "/challenges-images/203300-BRONZE.png", "SILVER": "/challenges-images/203300-SILVER.png", "GOLD": "/challenges-images/203300-GOLD.png", "PLATINUM": "/challenges-images/203300-PLATINUM.png", "DIAMOND": "/challenges-images/203300-DIAMOND.png", "MASTER": "/challenges-images/203300-MASTER.png", "GRANDMASTER": "/challenges-images/203300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "MASTER": {"value": 400}}}, {"id": 203301, "name": "Solo Bolo", "description": "<PERSON><PERSON>eni uccisioni in singolo (senza assist dai campioni alleati)", "shortDescription": "Ottieni uccisioni in singolo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203301-IRON.png", "BRONZE": "/challenges-images/203301-BRONZE.png", "SILVER": "/challenges-images/203301-SILVER.png", "GOLD": "/challenges-images/203301-GOLD.png", "PLATINUM": "/challenges-images/203301-PLATINUM.png", "DIAMOND": "/challenges-images/203301-DIAMOND.png", "MASTER": "/challenges-images/203301-MASTER.png", "GRANDMASTER": "/challenges-images/203301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>po solitario"}]}, "PLATINUM": {"value": 1000}, "DIAMOND": {"value": 2000}, "MASTER": {"value": 3000}, "GRANDMASTER": {"value": 4000}, "CHALLENGER": {"value": 5000}}}, {"id": 203302, "name": "Non ho chiesto pronostici", "description": "Ottieni uccisioni mentre nelle vicinanze ci sono più campioni nemici che alleati", "shortDescription": "Esegui uccisioni in inferiorità numerica", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203302-IRON.png", "BRONZE": "/challenges-images/203302-BRONZE.png", "SILVER": "/challenges-images/203302-SILVER.png", "GOLD": "/challenges-images/203302-GOLD.png", "PLATINUM": "/challenges-images/203302-PLATINUM.png", "DIAMOND": "/challenges-images/203302-DIAMOND.png", "MASTER": "/challenges-images/203302-MASTER.png", "GRANDMASTER": "/challenges-images/203302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 90}, "GOLD": {"value": 180}, "PLATINUM": {"value": 540, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Fortunato"}]}, "DIAMOND": {"value": 1080}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 203303, "name": "Senza tregua", "description": "Uccidi campioni nemici mentre sono vicini alla propria torre", "shortDescription": "Uccidi i campioni sotto la loro stessa torre", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203303-IRON.png", "BRONZE": "/challenges-images/203303-BRONZE.png", "SILVER": "/challenges-images/203303-SILVER.png", "GOLD": "/challenges-images/203303-GOLD.png", "PLATINUM": "/challenges-images/203303-PLATINUM.png", "DIAMOND": "/challenges-images/203303-DIAMOND.png", "MASTER": "/challenges-images/203303-MASTER.png", "GRANDMASTER": "/challenges-images/203303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 90}, "GOLD": {"value": 180}, "PLATINUM": {"value": 540}, "DIAMOND": {"value": 1080}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 203304, "name": "Strategia Split push", "description": "Distruggi torri della corsia laterale in singolo (infliggendo la maggior parte dei danni) dopo 14 minuti senza morire", "shortDescription": "Prendi le torri della corsia laterale da solo senza morire", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203304-IRON.png", "BRONZE": "/challenges-images/203304-BRONZE.png", "SILVER": "/challenges-images/203304-SILVER.png", "GOLD": "/challenges-images/203304-GOLD.png", "PLATINUM": "/challenges-images/203304-PLATINUM.png", "DIAMOND": "/challenges-images/203304-DIAMOND.png", "MASTER": "/challenges-images/203304-MASTER.png", "GRANDMASTER": "/challenges-images/203304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203304-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 9}, "SILVER": {"value": 27}, "GOLD": {"value": 60}, "PLATINUM": {"value": 180}, "DIAMOND": {"value": 350}, "MASTER": {"value": 650}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 203305, "name": "Non lasciartelo sfuggire", "description": "Uccidi campioni con salute al massimo entro 1,5 secondi, infliggendo gran parte dei danni (90% o più)", "shortDescription": "Uccidi i campioni con salute al massimo entro 1,5 secondi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203305-IRON.png", "BRONZE": "/challenges-images/203305-BRONZE.png", "SILVER": "/challenges-images/203305-SILVER.png", "GOLD": "/challenges-images/203305-GOLD.png", "PLATINUM": "/challenges-images/203305-PLATINUM.png", "DIAMOND": "/challenges-images/203305-DIAMOND.png", "MASTER": "/challenges-images/203305-MASTER.png", "GRANDMASTER": "/challenges-images/203305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 80}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 300}, "CHALLENGER": {"value": 500}}}, {"id": 203400, "name": "Predatore", "description": "Ottieni progressi dalle sfide nel gruppo Predatore", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Predatore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203400-IRON.png", "BRONZE": "/challenges-images/203400-BRONZE.png", "SILVER": "/challenges-images/203400-SILVER.png", "GOLD": "/challenges-images/203400-GOLD.png", "PLATINUM": "/challenges-images/203400-PLATINUM.png", "DIAMOND": "/challenges-images/203400-DIAMOND.png", "MASTER": "/challenges-images/203400-MASTER.png", "GRANDMASTER": "/challenges-images/203400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 55}, "GOLD": {"value": 150}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 430, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Predatore"}]}, "MASTER": {"value": 725}}}, {"id": 203401, "name": "Chi dorme non piglia draghi", "description": "Partecipa all'abbattimento del primo drago entro 8 minuti", "shortDescription": "Elimina il primo Drago entro otto minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203401-IRON.png", "BRONZE": "/challenges-images/203401-BRONZE.png", "SILVER": "/challenges-images/203401-SILVER.png", "GOLD": "/challenges-images/203401-GOLD.png", "PLATINUM": "/challenges-images/203401-PLATINUM.png", "DIAMOND": "/challenges-images/203401-DIAMOND.png", "MASTER": "/challenges-images/203401-MASTER.png", "GRANDMASTER": "/challenges-images/203401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203401-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 203402, "name": "<PERSON><PERSON><PERSON> di buff", "description": "Elimina mostri della giungla buff dalla giungla nemica", "shortDescription": "Prendi i buff nemici", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203402-IRON.png", "BRONZE": "/challenges-images/203402-BRONZE.png", "SILVER": "/challenges-images/203402-SILVER.png", "GOLD": "/challenges-images/203402-GOLD.png", "PLATINUM": "/challenges-images/203402-PLATINUM.png", "DIAMOND": "/challenges-images/203402-DIAMOND.png", "MASTER": "/challenges-images/203402-MASTER.png", "GRANDMASTER": "/challenges-images/203402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203402-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 16}, "SILVER": {"value": 40}, "GOLD": {"value": 120}, "PLATINUM": {"value": 360}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1200}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 1800}}}, {"id": 203403, "name": "Pulizia totale", "description": "Come jungler, o<PERSON><PERSON> 70 CS dai mostri della giungla entro 10 minuti", "shortDescription": "Ottieni 70 CS dai mostri della giungla entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203403-IRON.png", "BRONZE": "/challenges-images/203403-BRONZE.png", "SILVER": "/challenges-images/203403-SILVER.png", "GOLD": "/challenges-images/203403-GOLD.png", "PLATINUM": "/challenges-images/203403-PLATINUM.png", "DIAMOND": "/challenges-images/203403-DIAMOND.png", "MASTER": "/challenges-images/203403-MASTER.png", "GRANDMASTER": "/challenges-images/203403-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203403-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 4}, "PLATINUM": {"value": 9}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 65}}}, {"id": 203404, "name": "<PERSON><PERSON> poten<PERSON>", "description": "Come jungler, conquista tre dei primi quattro campi dei buff", "shortDescription": "Conquista 3 dei primi 4 campi dei buff", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203404-IRON.png", "BRONZE": "/challenges-images/203404-BRONZE.png", "SILVER": "/challenges-images/203404-SILVER.png", "GOLD": "/challenges-images/203404-GOLD.png", "PLATINUM": "/challenges-images/203404-PLATINUM.png", "DIAMOND": "/challenges-images/203404-DIAMOND.png", "MASTER": "/challenges-images/203404-MASTER.png", "GRANDMASTER": "/challenges-images/203404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203404-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 250}}}, {"id": 203405, "name": "Cacciatore di granchi", "description": "Come giu<PERSON><PERSON>, prendi entrambi gli Argogranchi iniziali", "shortDescription": "Prendi entrambi gli Argogranchi iniziali", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203405-IRON.png", "BRONZE": "/challenges-images/203405-BRONZE.png", "SILVER": "/challenges-images/203405-SILVER.png", "GOLD": "/challenges-images/203405-GOLD.png", "PLATINUM": "/challenges-images/203405-PLATINUM.png", "DIAMOND": "/challenges-images/203405-DIAMOND.png", "MASTER": "/challenges-images/203405-MASTER.png", "GRANDMASTER": "/challenges-images/203405-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203405-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 10, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Cacciatore di granchi"}]}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 203406, "name": "Bersaglio in sicurezza", "description": "Cattura i mostri epici con il jungler nemico nelle vicinanze. I mostri epici includono i draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Sconfiggi i mostri epici con il campione da giungla nemico nelle vicinanze", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203406-IRON.png", "BRONZE": "/challenges-images/203406-BRONZE.png", "SILVER": "/challenges-images/203406-SILVER.png", "GOLD": "/challenges-images/203406-GOLD.png", "PLATINUM": "/challenges-images/203406-PLATINUM.png", "DIAMOND": "/challenges-images/203406-DIAMOND.png", "MASTER": "/challenges-images/203406-MASTER.png", "GRANDMASTER": "/challenges-images/203406-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203406-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 8}, "SILVER": {"value": 18}, "GOLD": {"value": 38}, "PLATINUM": {"value": 98}, "DIAMOND": {"value": 208}, "MASTER": {"value": 328}, "GRANDMASTER": {"value": 450}, "CHALLENGER": {"value": 600}}}, {"id": 203407, "name": "Ora è la mia giungla", "description": "Come campione da giungla, conquista più campi avversari del campione da giungla nemico entro 10 minuti", "shortDescription": "Conquista più campi avversari del campione da giungla nemico entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203407-IRON.png", "BRONZE": "/challenges-images/203407-BRONZE.png", "SILVER": "/challenges-images/203407-SILVER.png", "GOLD": "/challenges-images/203407-GOLD.png", "PLATINUM": "/challenges-images/203407-PLATINUM.png", "DIAMOND": "/challenges-images/203407-DIAMOND.png", "MASTER": "/challenges-images/203407-MASTER.png", "GRANDMASTER": "/challenges-images/203407-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203407-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 13}, "CHALLENGER": {"value": 28}}}, {"id": 203408, "name": "Il gioco più pericoloso", "description": "Come campione da giungla, uccidi la tua controparte nemica nella sua giungla, entro 10 minuti", "shortDescription": "Come campione da giungla, uccidi la tua controparte nemica nella sua giungla entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/203408-IRON.png", "BRONZE": "/challenges-images/203408-BRONZE.png", "SILVER": "/challenges-images/203408-SILVER.png", "GOLD": "/challenges-images/203408-GOLD.png", "PLATINUM": "/challenges-images/203408-PLATINUM.png", "DIAMOND": "/challenges-images/203408-DIAMOND.png", "MASTER": "/challenges-images/203408-MASTER.png", "GRANDMASTER": "/challenges-images/203408-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203408-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 15}, "PLATINUM": {"value": 35, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Invasore"}]}, "DIAMOND": {"value": 70}, "MASTER": {"value": 120}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 203409, "name": "Furto epico", "description": "Ruba due mostri epici in una partita. I mostri epici includono i draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Ruba due mostri epici in una partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/203409-IRON.png", "BRONZE": "/challenges-images/203409-BRONZE.png", "SILVER": "/challenges-images/203409-SILVER.png", "GOLD": "/challenges-images/203409-GOLD.png", "PLATINUM": "/challenges-images/203409-PLATINUM.png", "DIAMOND": "/challenges-images/203409-DIAMOND.png", "MASTER": "/challenges-images/203409-MASTER.png", "GRANDMASTER": "/challenges-images/203409-GRANDMASTER.png", "CHALLENGER": "/challenges-images/203409-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 3}, "MASTER": {"value": 6}}}, {"id": 204000, "name": "Mente superiore", "description": "Ottieni progressi dalle sfide nei gruppi Caposaldo e Visionario", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Caposaldo e Visionario", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/204000-IRON.png", "BRONZE": "/challenges-images/204000-BRONZE.png", "SILVER": "/challenges-images/204000-SILVER.png", "GOLD": "/challenges-images/204000-GOLD.png", "PLATINUM": "/challenges-images/204000-PLATINUM.png", "DIAMOND": "/challenges-images/204000-DIAMOND.png", "MASTER": "/challenges-images/204000-MASTER.png", "GRANDMASTER": "/challenges-images/204000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 130}, "PLATINUM": {"value": 210}, "DIAMOND": {"value": 375}, "MASTER": {"value": 650, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Mente superiore"}]}}}, {"id": 204100, "name": "<PERSON><PERSON><PERSON>", "description": "Ottieni progressi dalle sfide nel gruppo Caposaldo", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Caposaldo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/204100-IRON.png", "BRONZE": "/challenges-images/204100-BRONZE.png", "SILVER": "/challenges-images/204100-SILVER.png", "GOLD": "/challenges-images/204100-GOLD.png", "PLATINUM": "/challenges-images/204100-PLATINUM.png", "DIAMOND": "/challenges-images/204100-DIAMOND.png", "MASTER": "/challenges-images/204100-MASTER.png", "GRANDMASTER": "/challenges-images/204100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "MASTER": {"value": 250}}}, {"id": 204101, "name": "Supporto superiore", "description": "Completa la tua missione di supporto almeno 60 secondi più rapidamente rispetto al supporto nemico", "shortDescription": "Completa la tua missione di supporto più rapidamente del nemico", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204101-IRON.png", "BRONZE": "/challenges-images/204101-BRONZE.png", "SILVER": "/challenges-images/204101-SILVER.png", "GOLD": "/challenges-images/204101-GOLD.png", "PLATINUM": "/challenges-images/204101-PLATINUM.png", "DIAMOND": "/challenges-images/204101-DIAMOND.png", "MASTER": "/challenges-images/204101-MASTER.png", "GRANDMASTER": "/challenges-images/204101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 600}, "GRANDMASTER": {"value": 900}, "CHALLENGER": {"value": 1500}}}, {"id": 204102, "name": "Onniveggente", "description": "Ottieni più di 2 punti di visione al minuto", "shortDescription": "Ottieni più di 2 punti di visione al minuto", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204102-IRON.png", "BRONZE": "/challenges-images/204102-BRONZE.png", "SILVER": "/challenges-images/204102-SILVER.png", "GOLD": "/challenges-images/204102-GOLD.png", "PLATINUM": "/challenges-images/204102-PLATINUM.png", "DIAMOND": "/challenges-images/204102-DIAMOND.png", "MASTER": "/challenges-images/204102-MASTER.png", "GRANDMASTER": "/challenges-images/204102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Onniveggente"}]}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 204103, "name": "Contributo di supporto", "description": "Completa del tutto la missione di un oggetto di supporto in meno di 14 minuti", "shortDescription": "Completa del tutto la tua missione di supporto in 14 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204103-IRON.png", "BRONZE": "/challenges-images/204103-BRONZE.png", "SILVER": "/challenges-images/204103-SILVER.png", "GOLD": "/challenges-images/204103-GOLD.png", "PLATINUM": "/challenges-images/204103-PLATINUM.png", "DIAMOND": "/challenges-images/204103-DIAMOND.png", "MASTER": "/challenges-images/204103-MASTER.png", "GRANDMASTER": "/challenges-images/204103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 204200, "name": "Visionario", "description": "Ottieni progressi dalle sfide nel gruppo Visionario", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Visionario", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/204200-IRON.png", "BRONZE": "/challenges-images/204200-BRONZE.png", "SILVER": "/challenges-images/204200-SILVER.png", "GOLD": "/challenges-images/204200-GOLD.png", "PLATINUM": "/challenges-images/204200-PLATINUM.png", "DIAMOND": "/challenges-images/204200-DIAMOND.png", "MASTER": "/challenges-images/204200-MASTER.png", "GRANDMASTER": "/challenges-images/204200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Visionario"}]}, "MASTER": {"value": 250}}}, {"id": 204201, "name": "Cacciatore di lumi", "description": "Distruggi 10 lumi entro 20 minuti", "shortDescription": "Distruggi 10 lumi entro 20 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204201-IRON.png", "BRONZE": "/challenges-images/204201-BRONZE.png", "SILVER": "/challenges-images/204201-SILVER.png", "GOLD": "/challenges-images/204201-GOLD.png", "PLATINUM": "/challenges-images/204201-PLATINUM.png", "DIAMOND": "/challenges-images/204201-DIAMOND.png", "MASTER": "/challenges-images/204201-MASTER.png", "GRANDMASTER": "/challenges-images/204201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204201-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 15}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 75}}}, {"id": 204202, "name": "Protettore di lumi", "description": "Proteggi lumi restando nelle vicinanze quando un nemico li danneggia, ma sopravvivono", "shortDescription": "Proteggi i lumi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204202-IRON.png", "BRONZE": "/challenges-images/204202-BRONZE.png", "SILVER": "/challenges-images/204202-SILVER.png", "GOLD": "/challenges-images/204202-GOLD.png", "PLATINUM": "/challenges-images/204202-PLATINUM.png", "DIAMOND": "/challenges-images/204202-DIAMOND.png", "MASTER": "/challenges-images/204202-MASTER.png", "GRANDMASTER": "/challenges-images/204202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Guardiano del Lume"}]}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 204203, "name": "<PERSON><PERSON> incredibile", "description": "Uccidi due o più lumi dell'avvistamento con una sola attivazione dell'Occhio", "shortDescription": "Uccidi due lumi con una sola attivazione dell'Occhio", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/204203-IRON.png", "BRONZE": "/challenges-images/204203-BRONZE.png", "SILVER": "/challenges-images/204203-SILVER.png", "GOLD": "/challenges-images/204203-GOLD.png", "PLATINUM": "/challenges-images/204203-PLATINUM.png", "DIAMOND": "/challenges-images/204203-DIAMOND.png", "MASTER": "/challenges-images/204203-MASTER.png", "GRANDMASTER": "/challenges-images/204203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/204203-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 6}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 24}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 75}, "CHALLENGER": {"value": 100}}}, {"id": 210000, "name": "Capolavoro", "description": "Ottieni progressi dalle sfide nel gruppo Capolavoro", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Capolavoro", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/210000-IRON.png", "BRONZE": "/challenges-images/210000-BRONZE.png", "SILVER": "/challenges-images/210000-SILVER.png", "GOLD": "/challenges-images/210000-GOLD.png", "PLATINUM": "/challenges-images/210000-PLATINUM.png", "DIAMOND": "/challenges-images/210000-DIAMOND.png", "MASTER": "/challenges-images/210000-MASTER.png", "GRANDMASTER": "/challenges-images/210000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>"}]}}}, {"id": 210001, "name": "Perfezionista", "description": "Ottieni S+ con campioni differenti", "shortDescription": "Ottieni S+ <em>con campioni differenti</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210001-IRON.png", "BRONZE": "/challenges-images/210001-BRONZE.png", "SILVER": "/challenges-images/210001-SILVER.png", "GOLD": "/challenges-images/210001-GOLD.png", "PLATINUM": "/challenges-images/210001-PLATINUM.png", "DIAMOND": "/challenges-images/210001-DIAMOND.png", "MASTER": "/challenges-images/210001-MASTER.png", "GRANDMASTER": "/challenges-images/210001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 50, "rewards": [{"category": "TITLE", "quantity": 1, "title": "S+"}]}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 160}, "CHALLENGER": {"value": 170}}}, {"id": 210002, "name": "<PERSON>essa <PERSON>, campione diverso", "description": "Ottieni una Pentakill con campioni differenti", "shortDescription": "O<PERSON><PERSON> una Pentakill <em>con campioni differenti</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210002-IRON.png", "BRONZE": "/challenges-images/210002-BRONZE.png", "SILVER": "/challenges-images/210002-SILVER.png", "GOLD": "/challenges-images/210002-GOLD.png", "PLATINUM": "/challenges-images/210002-PLATINUM.png", "DIAMOND": "/challenges-images/210002-DIAMOND.png", "MASTER": "/challenges-images/210002-MASTER.png", "GRANDMASTER": "/challenges-images/210002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}, "GRANDMASTER": {"value": 40}, "CHALLENGER": {"value": 50}}}, {"id": 210003, "name": "Prestazione migliore", "description": "Ottieni almeno S-", "shortDescription": "Ottieni valutazioni almeno di grado S-", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210003-IRON.png", "BRONZE": "/challenges-images/210003-BRONZE.png", "SILVER": "/challenges-images/210003-SILVER.png", "GOLD": "/challenges-images/210003-GOLD.png", "PLATINUM": "/challenges-images/210003-PLATINUM.png", "DIAMOND": "/challenges-images/210003-DIAMOND.png", "MASTER": "/challenges-images/210003-MASTER.png", "GRANDMASTER": "/challenges-images/210003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 300}, "CHALLENGER": {"value": 500}}}, {"id": 210004, "name": "Scalatore di classifiche", "description": "<PERSON><PERSON><PERSON> un livello in Classificata Solo/Duo. Devi finire le partite di piazzamento.", "shortDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> il livello in Coda Solo/Duo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/210004-IRON.png", "BRONZE": "/challenges-images/210004-BRONZE.png", "SILVER": "/challenges-images/210004-SILVER.png", "GOLD": "/challenges-images/210004-GOLD.png", "PLATINUM": "/challenges-images/210004-PLATINUM.png", "DIAMOND": "/challenges-images/210004-DIAMOND.png", "MASTER": "/challenges-images/210004-MASTER.png", "GRANDMASTER": "/challenges-images/210004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 9}, "BRONZE": {"value": 8}, "SILVER": {"value": 7}, "GOLD": {"value": 6}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 3}, "MASTER": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Magistrale"}]}}}, {"id": 210005, "name": "Giocatore UMA", "description": "Ottieni un Rapporto Uccisioni/Morti/Assist superiore a 3 nella coda Classificata Solo/Duo", "shortDescription": "Ottieni un UMA superiore a 3 nella coda Solo/Duo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/210005-IRON.png", "BRONZE": "/challenges-images/210005-BRONZE.png", "SILVER": "/challenges-images/210005-SILVER.png", "GOLD": "/challenges-images/210005-GOLD.png", "PLATINUM": "/challenges-images/210005-PLATINUM.png", "DIAMOND": "/challenges-images/210005-DIAMOND.png", "MASTER": "/challenges-images/210005-MASTER.png", "GRANDMASTER": "/challenges-images/210005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 50}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 250}, "MASTER": {"value": 400, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Giocatore UMA"}]}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 600}}}, {"id": 210006, "name": "Trionfante per sempre", "description": "Chiudi gli split della stagione almeno al livello Oro", "shortDescription": "Chiudi gli split della stagione al livello Oro o superiore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/210006-IRON.png", "BRONZE": "/challenges-images/210006-BRONZE.png", "SILVER": "/challenges-images/210006-SILVER.png", "GOLD": "/challenges-images/210006-GOLD.png", "PLATINUM": "/challenges-images/210006-PLATINUM.png", "DIAMOND": "/challenges-images/210006-DIAMOND.png", "MASTER": "/challenges-images/210006-MASTER.png", "GRANDMASTER": "/challenges-images/210006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/210006-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 2}, "GOLD": {"value": 4}, "PLATINUM": {"value": 6}, "DIAMOND": {"value": 8}, "MASTER": {"value": 10}}}, {"id": 3, "name": "ESPERIENZA", "description": "", "shortDescription": "Configurazione ESPERIENZA", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 150}, "BRONZE": {"value": 275}, "SILVER": {"value": 400}, "GOLD": {"value": 1000}, "PLATINUM": {"value": 1500}, "DIAMOND": {"value": 2700}, "MASTER": {"value": 4500}}}, {"id": 301000, "name": "Determinazione", "description": "Ottieni progressi dalle sfide nei gruppi Cacciatore di mostri, Risoluto e Simbiosi", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Cacciatore di mostri, Risoluto e Simbiosi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301000-IRON.png", "BRONZE": "/challenges-images/301000-BRONZE.png", "SILVER": "/challenges-images/301000-SILVER.png", "GOLD": "/challenges-images/301000-GOLD.png", "PLATINUM": "/challenges-images/301000-PLATINUM.png", "DIAMOND": "/challenges-images/301000-DIAMOND.png", "MASTER": "/challenges-images/301000-MASTER.png", "GRANDMASTER": "/challenges-images/301000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 70}, "GOLD": {"value": 245}, "PLATINUM": {"value": 410}, "DIAMOND": {"value": 840}, "MASTER": {"value": 1300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tenace"}]}}}, {"id": 301100, "name": "Cacciatore di mostri", "description": "Ottieni progressi dalle sfide nel gruppo Cacciatore di mostri", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Cacciatore di mostri", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301100-IRON.png", "BRONZE": "/challenges-images/301100-BRONZE.png", "SILVER": "/challenges-images/301100-SILVER.png", "GOLD": "/challenges-images/301100-GOLD.png", "PLATINUM": "/challenges-images/301100-PLATINUM.png", "DIAMOND": "/challenges-images/301100-DIAMOND.png", "MASTER": "/challenges-images/301100-MASTER.png", "GRANDMASTER": "/challenges-images/301100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 65}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 300, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Grande cacciatore"}]}, "MASTER": {"value": 450}}}, {"id": 301101, "name": "Sempre in tempo", "description": "Uccidi mostri epici entro 30 secondi dalla loro generazione. I mostri epici includono i draghi, il Messaggero della Landa e il Barone Nashor.", "shortDescription": "Uccidi mostri epici entro 30 secondi dalla generazione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301101-IRON.png", "BRONZE": "/challenges-images/301101-BRONZE.png", "SILVER": "/challenges-images/301101-SILVER.png", "GOLD": "/challenges-images/301101-GOLD.png", "PLATINUM": "/challenges-images/301101-PLATINUM.png", "DIAMOND": "/challenges-images/301101-DIAMOND.png", "MASTER": "/challenges-images/301101-MASTER.png", "GRANDMASTER": "/challenges-images/301101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Epico"}]}, "GOLD": {"value": 50}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 250}, "MASTER": {"value": 400}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 301102, "name": "Sconfiggi il Wurm", "description": "La tua squadra sconfigge Baroni entro 27 minuti", "shortDescription": "Sconfiggi Baroni entro 27 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301102-IRON.png", "BRONZE": "/challenges-images/301102-BRONZE.png", "SILVER": "/challenges-images/301102-SILVER.png", "GOLD": "/challenges-images/301102-GOLD.png", "PLATINUM": "/challenges-images/301102-PLATINUM.png", "DIAMOND": "/challenges-images/301102-DIAMOND.png", "MASTER": "/challenges-images/301102-MASTER.png", "GRANDMASTER": "/challenges-images/301102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301102-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 10}, "MASTER": {"value": 15}, "GRANDMASTER": {"value": 25}, "CHALLENGER": {"value": 50}}}, {"id": 301103, "name": "Caccia al drago", "description": "La tua squadra sconfigge i Draghi maggiori entro 28 minuti", "shortDescription": "Sconfiggi i Draghi maggiori entro 28 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301103-IRON.png", "BRONZE": "/challenges-images/301103-BRONZE.png", "SILVER": "/challenges-images/301103-SILVER.png", "GOLD": "/challenges-images/301103-GOLD.png", "PLATINUM": "/challenges-images/301103-PLATINUM.png", "DIAMOND": "/challenges-images/301103-DIAMOND.png", "MASTER": "/challenges-images/301103-MASTER.png", "GRANDMASTER": "/challenges-images/301103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301103-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}, "DIAMOND": {"value": 2}, "MASTER": {"value": 3}, "GRANDMASTER": {"value": 5}, "CHALLENGER": {"value": 8}}}, {"id": 301105, "name": "Falciata spirituale", "description": "Conquista Anime draconiche senza che la squadra nemica prenda un solo drago", "shortDescription": "Reclama Anime dei Draghi 4-0", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301105-IRON.png", "BRONZE": "/challenges-images/301105-BRONZE.png", "SILVER": "/challenges-images/301105-SILVER.png", "GOLD": "/challenges-images/301105-GOLD.png", "PLATINUM": "/challenges-images/301105-PLATINUM.png", "DIAMOND": "/challenges-images/301105-DIAMOND.png", "MASTER": "/challenges-images/301105-MASTER.png", "GRANDMASTER": "/challenges-images/301105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 50}, "MASTER": {"value": 90}, "GRANDMASTER": {"value": 130}, "CHALLENGER": {"value": 250}}}, {"id": 301106, "name": "Barattolo di Wurm", "description": "<PERSON><PERSON><PERSON> tre baroni in una sola partita", "shortDescription": "<PERSON><PERSON><PERSON> tre baroni in una sola partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301106-IRON.png", "BRONZE": "/challenges-images/301106-BRONZE.png", "SILVER": "/challenges-images/301106-SILVER.png", "GOLD": "/challenges-images/301106-GOLD.png", "PLATINUM": "/challenges-images/301106-PLATINUM.png", "DIAMOND": "/challenges-images/301106-DIAMOND.png", "MASTER": "/challenges-images/301106-MASTER.png", "GRANDMASTER": "/challenges-images/301106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301106-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 301107, "name": "Estinzione draconica", "description": "Uccidi due <PERSON><PERSON><PERSON> maggiori in una sola partita", "shortDescription": "Uccidi due <PERSON><PERSON><PERSON> maggiori in una sola partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301107-IRON.png", "BRONZE": "/challenges-images/301107-BRONZE.png", "SILVER": "/challenges-images/301107-SILVER.png", "GOLD": "/challenges-images/301107-GOLD.png", "PLATINUM": "/challenges-images/301107-PLATINUM.png", "DIAMOND": "/challenges-images/301107-DIAMOND.png", "MASTER": "/challenges-images/301107-MASTER.png", "GRANDMASTER": "/challenges-images/301107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301107-CHALLENGER.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 301200, "name": "Risoluto", "description": "Ottieni progressi dalle sfide nel gruppo Risoluto", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Risoluto", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301200-IRON.png", "BRONZE": "/challenges-images/301200-BRONZE.png", "SILVER": "/challenges-images/301200-SILVER.png", "GOLD": "/challenges-images/301200-GOLD.png", "PLATINUM": "/challenges-images/301200-PLATINUM.png", "DIAMOND": "/challenges-images/301200-DIAMOND.png", "MASTER": "/challenges-images/301200-MASTER.png", "GRANDMASTER": "/challenges-images/301200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301200-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 5}, "SILVER": {"value": 25}, "GOLD": {"value": 65}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 220, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Coraggioso"}]}, "MASTER": {"value": 350}}}, {"id": 301201, "name": "Vittoria aperta", "description": "Vinci partite con un nexus esposto", "shortDescription": "Vinci col nexus esposto", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301201-IRON.png", "BRONZE": "/challenges-images/301201-BRONZE.png", "SILVER": "/challenges-images/301201-SILVER.png", "GOLD": "/challenges-images/301201-GOLD.png", "PLATINUM": "/challenges-images/301201-PLATINUM.png", "DIAMOND": "/challenges-images/301201-DIAMOND.png", "MASTER": "/challenges-images/301201-MASTER.png", "GRANDMASTER": "/challenges-images/301201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301201-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Ragazzo persistente"}]}, "DIAMOND": {"value": 4}, "MASTER": {"value": 7}}}, {"id": 301202, "name": "Senza inibizioni", "description": "Vinci partite dopo aver perso un inibitore", "shortDescription": "Vinci dopo aver perso un inibitore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301202-IRON.png", "BRONZE": "/challenges-images/301202-BRONZE.png", "SILVER": "/challenges-images/301202-SILVER.png", "GOLD": "/challenges-images/301202-GOLD.png", "PLATINUM": "/challenges-images/301202-PLATINUM.png", "DIAMOND": "/challenges-images/301202-DIAMOND.png", "MASTER": "/challenges-images/301202-MASTER.png", "GRANDMASTER": "/challenges-images/301202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301202-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 3}, "GOLD": {"value": 7, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Senza inibizioni"}]}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 45}, "MASTER": {"value": 75}}}, {"id": 301203, "name": "<PERSON><PERSON><PERSON>", "description": "Vinci partite dopo uno svantaggio di 15 uccisioni", "shortDescription": "Vinci dopo uno svantaggio di 15 uccisioni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301203-IRON.png", "BRONZE": "/challenges-images/301203-BRONZE.png", "SILVER": "/challenges-images/301203-SILVER.png", "GOLD": "/challenges-images/301203-GOLD.png", "PLATINUM": "/challenges-images/301203-PLATINUM.png", "DIAMOND": "/challenges-images/301203-DIAMOND.png", "MASTER": "/challenges-images/301203-MASTER.png", "GRANDMASTER": "/challenges-images/301203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301203-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 4}, "DIAMOND": {"value": 8}, "MASTER": {"value": 12}}}, {"id": 301204, "name": "Chi ha bisogno di loro", "description": "Vinci una partita pur avendo un compagno AFK", "shortDescription": "Vinci una partita pur avendo un compagno AFK", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301204-IRON.png", "BRONZE": "/challenges-images/301204-BRONZE.png", "SILVER": "/challenges-images/301204-SILVER.png", "GOLD": "/challenges-images/301204-GOLD.png", "PLATINUM": "/challenges-images/301204-PLATINUM.png", "DIAMOND": "/challenges-images/301204-DIAMOND.png", "MASTER": "/challenges-images/301204-MASTER.png", "GRANDMASTER": "/challenges-images/301204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301204-CHALLENGER.png"}, "thresholds": {"PLATINUM": {"value": 1}}}, {"id": 301205, "name": "Nessuna lealtà tra i draghi", "description": "Prendi Draghi maggiori mentre il tuo avversario ha l'Anima del Drago", "shortDescription": "Prendi i Draghi maggiori quando il nemico ha l'Anima del drago", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301205-IRON.png", "BRONZE": "/challenges-images/301205-BRONZE.png", "SILVER": "/challenges-images/301205-SILVER.png", "GOLD": "/challenges-images/301205-GOLD.png", "PLATINUM": "/challenges-images/301205-PLATINUM.png", "DIAMOND": "/challenges-images/301205-DIAMOND.png", "MASTER": "/challenges-images/301205-MASTER.png", "GRANDMASTER": "/challenges-images/301205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301205-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 3}, "PLATINUM": {"value": 5, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Incendiario"}]}, "DIAMOND": {"value": 12}, "MASTER": {"value": 18}}}, {"id": 301300, "name": "Simb<PERSON>i", "description": "Ottieni progressi dalle sfide nel gruppo Simbiosi", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Simbiosi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301300-IRON.png", "BRONZE": "/challenges-images/301300-BRONZE.png", "SILVER": "/challenges-images/301300-SILVER.png", "GOLD": "/challenges-images/301300-GOLD.png", "PLATINUM": "/challenges-images/301300-PLATINUM.png", "DIAMOND": "/challenges-images/301300-DIAMOND.png", "MASTER": "/challenges-images/301300-MASTER.png", "GRANDMASTER": "/challenges-images/301300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 25}, "BRONZE": {"value": 50}, "SILVER": {"value": 75}, "GOLD": {"value": 100}, "PLATINUM": {"value": 105}, "DIAMOND": {"value": 160, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Eccezionale"}]}, "MASTER": {"value": 300}}}, {"id": 301301, "name": "Vittoria perfetta", "description": "Vinci partite perfette, in cui la squadra avversaria non totalizza uccisioni, non ottiene alcun drago, Messagger<PERSON> della Landa o Barone e non distrugge alcuna torre", "shortDescription": "Vinci una partita perfetta", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301301-IRON.png", "BRONZE": "/challenges-images/301301-BRONZE.png", "SILVER": "/challenges-images/301301-SILVER.png", "GOLD": "/challenges-images/301301-GOLD.png", "PLATINUM": "/challenges-images/301301-PLATINUM.png", "DIAMOND": "/challenges-images/301301-DIAMOND.png", "MASTER": "/challenges-images/301301-MASTER.png", "GRANDMASTER": "/challenges-images/301301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301301-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}}}, {"id": 301302, "name": "Dare il buon esempio", "description": "Ottieni 12 o più assist senza morire", "shortDescription": "Ottieni una serie di almeno 12 assist senza morire", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301302-IRON.png", "BRONZE": "/challenges-images/301302-BRONZE.png", "SILVER": "/challenges-images/301302-SILVER.png", "GOLD": "/challenges-images/301302-GOLD.png", "PLATINUM": "/challenges-images/301302-PLATINUM.png", "DIAMOND": "/challenges-images/301302-DIAMOND.png", "MASTER": "/challenges-images/301302-MASTER.png", "GRANDMASTER": "/challenges-images/301302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 180}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 301303, "name": "Vincinibitori", "description": "Distruggi tutti e tre gli inibitori nemici in meno di 25 minuti", "shortDescription": "Distruggi tutti e tre gli inibitori nemici in meno di 25 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301303-IRON.png", "BRONZE": "/challenges-images/301303-BRONZE.png", "SILVER": "/challenges-images/301303-SILVER.png", "GOLD": "/challenges-images/301303-GOLD.png", "PLATINUM": "/challenges-images/301303-PLATINUM.png", "DIAMOND": "/challenges-images/301303-DIAMOND.png", "MASTER": "/challenges-images/301303-MASTER.png", "GRANDMASTER": "/challenges-images/301303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301303-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}}}, {"id": 301304, "name": "Team Diff", "description": "<PERSON>ti a segno carneficine dopo la generazione dei minion, ma entro 15 minuti", "shortDescription": "Metti a segno carneficine tra la generazione dei minion e i 15 minuti di gioco", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/301304-IRON.png", "BRONZE": "/challenges-images/301304-BRONZE.png", "SILVER": "/challenges-images/301304-SILVER.png", "GOLD": "/challenges-images/301304-GOLD.png", "PLATINUM": "/challenges-images/301304-PLATINUM.png", "DIAMOND": "/challenges-images/301304-DIAMOND.png", "MASTER": "/challenges-images/301304-MASTER.png", "GRANDMASTER": "/challenges-images/301304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301304-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}, "GRANDMASTER": {"value": 15}, "CHALLENGER": {"value": 20}}}, {"id": 301305, "name": "<PERSON><PERSON> veloce", "description": "Balla con il Messaggero della Landa nella base nemica alla fine della partita", "shortDescription": "Balla con il Messaggero della Landa nella base nemica", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301305-IRON.png", "BRONZE": "/challenges-images/301305-BRONZE.png", "SILVER": "/challenges-images/301305-SILVER.png", "GOLD": "/challenges-images/301305-GOLD.png", "PLATINUM": "/challenges-images/301305-PLATINUM.png", "DIAMOND": "/challenges-images/301305-DIAMOND.png", "MASTER": "/challenges-images/301305-MASTER.png", "GRANDMASTER": "/challenges-images/301305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301305-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Jitterbug"}]}}}, {"id": 301306, "name": "Facciamo in fretta", "description": "Distruggi il nexus nemico in meno di 15 minuti", "shortDescription": "Distruggi il nexus nemico in meno di 15 minuti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/301306-IRON.png", "BRONZE": "/challenges-images/301306-BRONZE.png", "SILVER": "/challenges-images/301306-SILVER.png", "GOLD": "/challenges-images/301306-GOLD.png", "PLATINUM": "/challenges-images/301306-PLATINUM.png", "DIAMOND": "/challenges-images/301306-DIAMOND.png", "MASTER": "/challenges-images/301306-MASTER.png", "GRANDMASTER": "/challenges-images/301306-GRANDMASTER.png", "CHALLENGER": "/challenges-images/301306-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}}}, {"id": 302000, "name": "Capitano", "description": "Ottieni progressi dalle sfide nei gruppi Strategia, Demolizione, Sinergia e Spirito di Squadra", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Strategia, Demolizione, Sinergia e Spirito di Squadra", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302000-IRON.png", "BRONZE": "/challenges-images/302000-BRONZE.png", "SILVER": "/challenges-images/302000-SILVER.png", "GOLD": "/challenges-images/302000-GOLD.png", "PLATINUM": "/challenges-images/302000-PLATINUM.png", "DIAMOND": "/challenges-images/302000-DIAMOND.png", "MASTER": "/challenges-images/302000-MASTER.png", "GRANDMASTER": "/challenges-images/302000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 45}, "BRONZE": {"value": 95}, "SILVER": {"value": 150}, "GOLD": {"value": 350}, "PLATINUM": {"value": 560}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1700, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Playmaker"}]}}}, {"id": 302100, "name": "Strategia", "description": "Ottieni progressi dalle sfide nel gruppo Strategia", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Strategia", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302100-IRON.png", "BRONZE": "/challenges-images/302100-BRONZE.png", "SILVER": "/challenges-images/302100-SILVER.png", "GOLD": "/challenges-images/302100-GOLD.png", "PLATINUM": "/challenges-images/302100-PLATINUM.png", "DIAMOND": "/challenges-images/302100-DIAMOND.png", "MASTER": "/challenges-images/302100-MASTER.png", "GRANDMASTER": "/challenges-images/302100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 40}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Stratega"}]}, "MASTER": {"value": 475}}}, {"id": 302101, "name": "Festa livello 1", "description": "Ottieni eliminazioni dei campioni nemici prima della generazione dei mostri della giungla (1:30)", "shortDescription": "Ottieni eliminazioni prima della generazione dei mostri della giungla", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302101-IRON.png", "BRONZE": "/challenges-images/302101-BRONZE.png", "SILVER": "/challenges-images/302101-SILVER.png", "GOLD": "/challenges-images/302101-GOLD.png", "PLATINUM": "/challenges-images/302101-PLATINUM.png", "DIAMOND": "/challenges-images/302101-DIAMOND.png", "MASTER": "/challenges-images/302101-MASTER.png", "GRANDMASTER": "/challenges-images/302101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 75}, "MASTER": {"value": 130}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 302102, "name": "Uccidi campioni, non campi", "description": "Come campione da giungla, ottieni uccisioni in corsia superiore, centrale o inferiore o contro giocatori di supporto entro 10 minuti", "shortDescription": "Come campione da giungla, ottieni uccisioni contro i giocatori in corsia entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302102-IRON.png", "BRONZE": "/challenges-images/302102-BRONZE.png", "SILVER": "/challenges-images/302102-SILVER.png", "GOLD": "/challenges-images/302102-GOLD.png", "PLATINUM": "/challenges-images/302102-PLATINUM.png", "DIAMOND": "/challenges-images/302102-DIAMOND.png", "MASTER": "/challenges-images/302102-MASTER.png", "GRANDMASTER": "/challenges-images/302102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Farmatore di campioni"}]}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 125}, "MASTER": {"value": 225}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 650}}}, {"id": 302103, "name": "Presenza travolgente", "description": "Come giocatore in corsia, in una sola partita, esegui uccisioni entro 10 minuti fuori dalla tua corsia (ai danni di chiunque, eccetto il tuo avversario in corsia)", "shortDescription": "Come giocatore in corsia, in 1 partita, esegui uccisioni fuori dalla tua corsia entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302103-IRON.png", "BRONZE": "/challenges-images/302103-BRONZE.png", "SILVER": "/challenges-images/302103-SILVER.png", "GOLD": "/challenges-images/302103-GOLD.png", "PLATINUM": "/challenges-images/302103-PLATINUM.png", "DIAMOND": "/challenges-images/302103-DIAMOND.png", "MASTER": "/challenges-images/302103-MASTER.png", "GRANDMASTER": "/challenges-images/302103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302103-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2}, "DIAMOND": {"value": 3}, "MASTER": {"value": 4}, "GRANDMASTER": {"value": 5}, "CHALLENGER": {"value": 6}}}, {"id": 302104, "name": "Di chi è la corsia", "description": "Come giocatore in corsia, esegui un'eliminazione in tutte e tre le corsie entro 10 minuti", "shortDescription": "Come giocatore in corsia, esegui eliminazioni in tutte le corsie entro 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302104-IRON.png", "BRONZE": "/challenges-images/302104-BRONZE.png", "SILVER": "/challenges-images/302104-SILVER.png", "GOLD": "/challenges-images/302104-GOLD.png", "PLATINUM": "/challenges-images/302104-PLATINUM.png", "DIAMOND": "/challenges-images/302104-DIAMOND.png", "MASTER": "/challenges-images/302104-MASTER.png", "GRANDMASTER": "/challenges-images/302104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302104-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 7}, "MASTER": {"value": 12}, "GRANDMASTER": {"value": 18}, "CHALLENGER": {"value": 50}}}, {"id": 302105, "name": "Sistema di difesa globale", "description": "Mantieni un lume di controllo attivo nel fiume o nella metà nemica della mappa per più del 65% della durata della partita", "shortDescription": "Tieni un lume di controllo nella giungla nemica per oltre il 65% della partita", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302105-IRON.png", "BRONZE": "/challenges-images/302105-BRONZE.png", "SILVER": "/challenges-images/302105-SILVER.png", "GOLD": "/challenges-images/302105-GOLD.png", "PLATINUM": "/challenges-images/302105-PLATINUM.png", "DIAMOND": "/challenges-images/302105-DIAMOND.png", "MASTER": "/challenges-images/302105-MASTER.png", "GRANDMASTER": "/challenges-images/302105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302105-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 4, "rewards": [{"category": "TITLE", "quantity": 1, "title": "In controllo"}]}, "DIAMOND": {"value": 8}, "MASTER": {"value": 12}, "GRANDMASTER": {"value": 15}, "CHALLENGER": {"value": 20}}}, {"id": 302106, "name": "Ribaltone con il Barone", "description": "Genera un vantaggio in oro di 2500 durante il buff del Barone", "shortDescription": "Genera un vantaggio di oro di 2.500 con il buff del Barone", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302106-IRON.png", "BRONZE": "/challenges-images/302106-BRONZE.png", "SILVER": "/challenges-images/302106-SILVER.png", "GOLD": "/challenges-images/302106-GOLD.png", "PLATINUM": "/challenges-images/302106-PLATINUM.png", "DIAMOND": "/challenges-images/302106-DIAMOND.png", "MASTER": "/challenges-images/302106-MASTER.png", "GRANDMASTER": "/challenges-images/302106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302106-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 25}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 302200, "name": "Demolizione", "description": "Ottieni progressi dalle sfide nel gruppo Demolizione", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Demolizione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302200-IRON.png", "BRONZE": "/challenges-images/302200-BRONZE.png", "SILVER": "/challenges-images/302200-SILVER.png", "GOLD": "/challenges-images/302200-GOLD.png", "PLATINUM": "/challenges-images/302200-PLATINUM.png", "DIAMOND": "/challenges-images/302200-DIAMOND.png", "MASTER": "/challenges-images/302200-MASTER.png", "GRANDMASTER": "/challenges-images/302200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Demolitore"}]}, "MASTER": {"value": 250}}}, {"id": 302201, "name": "V<PERSON>, Shelly. Vai!", "description": "Partecipa alla conquista di 2 torri con lo stesso <PERSON> Landa", "shortDescription": "Conquista 2 torri con lo stesso <PERSON> della Landa", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302201-IRON.png", "BRONZE": "/challenges-images/302201-BRONZE.png", "SILVER": "/challenges-images/302201-SILVER.png", "GOLD": "/challenges-images/302201-GOLD.png", "PLATINUM": "/challenges-images/302201-PLATINUM.png", "DIAMOND": "/challenges-images/302201-DIAMOND.png", "MASTER": "/challenges-images/302201-MASTER.png", "GRANDMASTER": "/challenges-images/302201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 20}, "GOLD": {"value": 35}, "PLATINUM": {"value": 105}, "DIAMOND": {"value": 225}, "MASTER": {"value": 400}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 600}}}, {"id": 302202, "name": "Corazze frantumate", "description": "<PERSON><PERSON>ruggi torri prima della caduta della loro corazza (14 minuti di gioco)", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> le torri prima della caduta della corazza", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302202-IRON.png", "BRONZE": "/challenges-images/302202-BRONZE.png", "SILVER": "/challenges-images/302202-SILVER.png", "GOLD": "/challenges-images/302202-GOLD.png", "PLATINUM": "/challenges-images/302202-PLATINUM.png", "DIAMOND": "/challenges-images/302202-DIAMOND.png", "MASTER": "/challenges-images/302202-MASTER.png", "GRANDMASTER": "/challenges-images/302202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 20}, "PLATINUM": {"value": 45, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Taglialegna"}]}, "DIAMOND": {"value": 90}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 225}, "CHALLENGER": {"value": 450}}}, {"id": 302203, "name": "Torre in dieci minuti", "description": "Distruggi la prima torre in meno di 10 minuti", "shortDescription": "Distruggi la prima torre in meno di 10 minuti", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302203-IRON.png", "BRONZE": "/challenges-images/302203-BRONZE.png", "SILVER": "/challenges-images/302203-SILVER.png", "GOLD": "/challenges-images/302203-GOLD.png", "PLATINUM": "/challenges-images/302203-PLATINUM.png", "DIAMOND": "/challenges-images/302203-DIAMOND.png", "MASTER": "/challenges-images/302203-MASTER.png", "GRANDMASTER": "/challenges-images/302203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 70}, "MASTER": {"value": 120}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 200}}}, {"id": 302300, "name": "Sinergia", "description": "Ottieni progressi dalle sfide nel gruppo Sinergia", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Sinergia", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302300-IRON.png", "BRONZE": "/challenges-images/302300-BRONZE.png", "SILVER": "/challenges-images/302300-SILVER.png", "GOLD": "/challenges-images/302300-GOLD.png", "PLATINUM": "/challenges-images/302300-PLATINUM.png", "DIAMOND": "/challenges-images/302300-DIAMOND.png", "MASTER": "/challenges-images/302300-MASTER.png", "GRANDMASTER": "/challenges-images/302300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Sinergia"}]}, "MASTER": {"value": 400}}}, {"id": 302301, "name": "Fanatico dell'erba alta", "description": "Elimina campioni dopo aver atteso nell'erba per almeno 3 secondi con almeno un alleato", "shortDescription": "Ottieni eliminazioni con un alleato dopo esserti nascosto nell'erba alta per 3+ secondi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302301-IRON.png", "BRONZE": "/challenges-images/302301-BRONZE.png", "SILVER": "/challenges-images/302301-SILVER.png", "GOLD": "/challenges-images/302301-GOLD.png", "PLATINUM": "/challenges-images/302301-PLATINUM.png", "DIAMOND": "/challenges-images/302301-DIAMOND.png", "MASTER": "/challenges-images/302301-MASTER.png", "GRANDMASTER": "/challenges-images/302301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 8}, "BRONZE": {"value": 32}, "SILVER": {"value": 64}, "GOLD": {"value": 160, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Fanatico"}]}, "PLATINUM": {"value": 400}, "DIAMOND": {"value": 1080}, "MASTER": {"value": 1920}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3000}}}, {"id": 302302, "name": "Eliminazione di squadra", "description": "Assassina un nemico con almeno un assist alleato. Un assassinio è un'uccisione di un singolo nemico senza concedere un'uccisione in cambio", "shortDescription": "Ottieni delle scelte con almeno un alleato", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302302-IRON.png", "BRONZE": "/challenges-images/302302-BRONZE.png", "SILVER": "/challenges-images/302302-SILVER.png", "GOLD": "/challenges-images/302302-GOLD.png", "PLATINUM": "/challenges-images/302302-PLATINUM.png", "DIAMOND": "/challenges-images/302302-DIAMOND.png", "MASTER": "/challenges-images/302302-MASTER.png", "GRANDMASTER": "/challenges-images/302302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 55}, "BRONZE": {"value": 220}, "SILVER": {"value": 660}, "GOLD": {"value": 1320}, "PLATINUM": {"value": 4125}, "DIAMOND": {"value": 7500}, "MASTER": {"value": 13000}, "GRANDMASTER": {"value": 18000}, "CHALLENGER": {"value": 24000}}}, {"id": 302303, "name": "C'è mancato poco", "description": "Salva un alleato che avrebbe subito danni letali con una cura o uno scudo", "shortDescription": "Salva un alleato con una cura o uno scudo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302303-IRON.png", "BRONZE": "/challenges-images/302303-BRONZE.png", "SILVER": "/challenges-images/302303-SILVER.png", "GOLD": "/challenges-images/302303-GOLD.png", "PLATINUM": "/challenges-images/302303-PLATINUM.png", "DIAMOND": "/challenges-images/302303-DIAMOND.png", "MASTER": "/challenges-images/302303-MASTER.png", "GRANDMASTER": "/challenges-images/302303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 50, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON> custode"}]}, "GOLD": {"value": 150}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 675}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2000}}}, {"id": 302304, "name": "Spara e stordisci", "description": "Immobilizza un nemico, poi eliminalo con un alleato", "shortDescription": "Immob<PERSON><PERSON> ed elimina un nemico con un alleato", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302304-IRON.png", "BRONZE": "/challenges-images/302304-BRONZE.png", "SILVER": "/challenges-images/302304-SILVER.png", "GOLD": "/challenges-images/302304-GOLD.png", "PLATINUM": "/challenges-images/302304-PLATINUM.png", "DIAMOND": "/challenges-images/302304-DIAMOND.png", "MASTER": "/challenges-images/302304-MASTER.png", "GRANDMASTER": "/challenges-images/302304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302304-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 30}, "BRONZE": {"value": 120}, "SILVER": {"value": 300}, "GOLD": {"value": 750}, "PLATINUM": {"value": 2250}, "DIAMOND": {"value": 4000}, "MASTER": {"value": 7200}, "GRANDMASTER": {"value": 10000}, "CHALLENGER": {"value": 12000}}}, {"id": 302305, "name": "Violazione di Insec-urezza", "description": "Respingi i nemici portandoli nella tua squadra ottenendo un'eliminazione", "shortDescription": "Respingi i nemici verso la tua squadra e ottieni un'eliminazione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302305-IRON.png", "BRONZE": "/challenges-images/302305-BRONZE.png", "SILVER": "/challenges-images/302305-SILVER.png", "GOLD": "/challenges-images/302305-GOLD.png", "PLATINUM": "/challenges-images/302305-PLATINUM.png", "DIAMOND": "/challenges-images/302305-DIAMOND.png", "MASTER": "/challenges-images/302305-MASTER.png", "GRANDMASTER": "/challenges-images/302305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 60}, "GOLD": {"value": 150}, "PLATINUM": {"value": 500, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Insec-ticida"}]}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2000}, "CHALLENGER": {"value": 2500}}}, {"id": 302400, "name": "Spirito di squadra", "description": "Ottieni progressi dalle sfide nel gruppo Spirito di Squadra", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Spirito di Squadra", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/302400-IRON.png", "BRONZE": "/challenges-images/302400-BRONZE.png", "SILVER": "/challenges-images/302400-SILVER.png", "GOLD": "/challenges-images/302400-GOLD.png", "PLATINUM": "/challenges-images/302400-PLATINUM.png", "DIAMOND": "/challenges-images/302400-DIAMOND.png", "MASTER": "/challenges-images/302400-MASTER.png", "GRANDMASTER": "/challenges-images/302400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Grintoso"}]}, "MASTER": {"value": 250}}}, {"id": 302401, "name": "Carneficina impeccabile", "description": "Esegui una carneficina ai danni della squadra nemica senza la perdita di nessun alleato", "shortDescription": "Esegui una carneficina ai danni della squadra nemica senza la perdita di nessun alleato", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302401-IRON.png", "BRONZE": "/challenges-images/302401-BRONZE.png", "SILVER": "/challenges-images/302401-SILVER.png", "GOLD": "/challenges-images/302401-GOLD.png", "PLATINUM": "/challenges-images/302401-PLATINUM.png", "DIAMOND": "/challenges-images/302401-DIAMOND.png", "MASTER": "/challenges-images/302401-MASTER.png", "GRANDMASTER": "/challenges-images/302401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302401-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 35}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 170}, "MASTER": {"value": 300}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 750}}}, {"id": 302402, "name": "Il lavoro dei sogni", "description": "Esegui un'eliminazione con il contributo di tutta la squadra (un'uccisione o un assist)", "shortDescription": "Esegui un'eliminazione con tutta la squadra", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302402-IRON.png", "BRONZE": "/challenges-images/302402-BRONZE.png", "SILVER": "/challenges-images/302402-SILVER.png", "GOLD": "/challenges-images/302402-GOLD.png", "PLATINUM": "/challenges-images/302402-PLATINUM.png", "DIAMOND": "/challenges-images/302402-DIAMOND.png", "MASTER": "/challenges-images/302402-MASTER.png", "GRANDMASTER": "/challenges-images/302402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302402-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 12}, "SILVER": {"value": 30}, "GOLD": {"value": 75}, "PLATINUM": {"value": 200}, "DIAMOND": {"value": 400}, "MASTER": {"value": 650}, "GRANDMASTER": {"value": 900}, "CHALLENGER": {"value": 1200}}}, {"id": 302404, "name": "Elegantemente in ritardo", "description": "Ottieni eliminazioni dopo il teletrasporto in uno scontro che coinvolge 4 o più campioni", "shortDescription": "Esegui un abbattimento dopo il teletrasporto in una battaglia con oltre 4 campioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/302404-IRON.png", "BRONZE": "/challenges-images/302404-BRONZE.png", "SILVER": "/challenges-images/302404-SILVER.png", "GOLD": "/challenges-images/302404-GOLD.png", "PLATINUM": "/challenges-images/302404-PLATINUM.png", "DIAMOND": "/challenges-images/302404-DIAMOND.png", "MASTER": "/challenges-images/302404-MASTER.png", "GRANDMASTER": "/challenges-images/302404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/302404-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 50, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Imbucato"}]}, "GOLD": {"value": 100}, "PLATINUM": {"value": 375}, "DIAMOND": {"value": 675}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2500}}}, {"id": 303000, "name": "Alleanza", "description": "Ottieni progressi dalle sfide nei gruppi <PERSON>lash, Sincronia, Armonia e Giramondo", "shortDescription": "Ottieni progressi dalle sfide nei gruppi <PERSON>lash, Sincronia, Armonia e Giramondo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303000-IRON.png", "BRONZE": "/challenges-images/303000-BRONZE.png", "SILVER": "/challenges-images/303000-SILVER.png", "GOLD": "/challenges-images/303000-GOLD.png", "PLATINUM": "/challenges-images/303000-PLATINUM.png", "DIAMOND": "/challenges-images/303000-DIAMOND.png", "MASTER": "/challenges-images/303000-MASTER.png", "GRANDMASTER": "/challenges-images/303000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 30}, "BRONZE": {"value": 60}, "SILVER": {"value": 90}, "GOLD": {"value": 620}, "PLATINUM": {"value": 1030}, "DIAMOND": {"value": 1775}, "MASTER": {"value": 2950, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Giocatore di squadra"}]}}}, {"id": 303200, "name": "Clash", "description": "Ottieni progressi dalle sfide nel gruppo Clash", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303200-IRON.png", "BRONZE": "/challenges-images/303200-BRONZE.png", "SILVER": "/challenges-images/303200-SILVER.png", "GOLD": "/challenges-images/303200-GOLD.png", "PLATINUM": "/challenges-images/303200-PLATINUM.png", "DIAMOND": "/challenges-images/303200-DIAMOND.png", "MASTER": "/challenges-images/303200-MASTER.png", "GRANDMASTER": "/challenges-images/303200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Esperto di Clash"}]}, "MASTER": {"value": 400}}}, {"id": 303201, "name": "Con<PERSON>enti Clash", "description": "Vinci delle partite in Clash", "shortDescription": "Vinci delle partite in Clash", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303201-IRON.png", "BRONZE": "/challenges-images/303201-BRONZE.png", "SILVER": "/challenges-images/303201-SILVER.png", "GOLD": "/challenges-images/303201-GOLD.png", "PLATINUM": "/challenges-images/303201-PLATINUM.png", "DIAMOND": "/challenges-images/303201-DIAMOND.png", "MASTER": "/challenges-images/303201-MASTER.png", "GRANDMASTER": "/challenges-images/303201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 4}, "SILVER": {"value": 10}, "GOLD": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Giocatore di Clash"}]}, "PLATINUM": {"value": 35}, "DIAMOND": {"value": 75}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 225}, "CHALLENGER": {"value": 300}}}, {"id": 303202, "name": "Campione Clash", "description": "Vinci le Eliminatorie Clash", "shortDescription": "Vinci le Eliminatorie Clash", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303202-IRON.png", "BRONZE": "/challenges-images/303202-BRONZE.png", "SILVER": "/challenges-images/303202-SILVER.png", "GOLD": "/challenges-images/303202-GOLD.png", "PLATINUM": "/challenges-images/303202-PLATINUM.png", "DIAMOND": "/challenges-images/303202-DIAMOND.png", "MASTER": "/challenges-images/303202-MASTER.png", "GRANDMASTER": "/challenges-images/303202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 25}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 55}, "CHALLENGER": {"value": 70}}}, {"id": 303203, "name": "<PERSON><PERSON> coordinato", "description": "L'intera squadra possiede loghi Clash", "shortDescription": "L'intera squadra possiede dei loghi Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303203-IRON.png", "BRONZE": "/challenges-images/303203-BRONZE.png", "SILVER": "/challenges-images/303203-SILVER.png", "GOLD": "/challenges-images/303203-GOLD.png", "PLATINUM": "/challenges-images/303203-PLATINUM.png", "DIAMOND": "/challenges-images/303203-DIAMOND.png", "MASTER": "/challenges-images/303203-MASTER.png", "GRANDMASTER": "/challenges-images/303203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 6}, "PLATINUM": {"value": 12}, "DIAMOND": {"value": 21}, "MASTER": {"value": 30}}}, {"id": 303204, "name": "Squadra dei sogni", "description": "Gioca con la stessa squadra in tornei Clash differenti", "shortDescription": "Gioca con la stessa squadra nei tornei Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303204-IRON.png", "BRONZE": "/challenges-images/303204-BRONZE.png", "SILVER": "/challenges-images/303204-SILVER.png", "GOLD": "/challenges-images/303204-GOLD.png", "PLATINUM": "/challenges-images/303204-PLATINUM.png", "DIAMOND": "/challenges-images/303204-DIAMOND.png", "MASTER": "/challenges-images/303204-MASTER.png", "GRANDMASTER": "/challenges-images/303204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 9}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 21}, "MASTER": {"value": 30}}}, {"id": 303205, "name": "Classifica", "description": "Finisci con più vittorie che sconfitte in Clash", "shortDescription": "Ottieni più vittorie che sconfitte in Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303205-IRON.png", "BRONZE": "/challenges-images/303205-BRONZE.png", "SILVER": "/challenges-images/303205-SILVER.png", "GOLD": "/challenges-images/303205-GOLD.png", "PLATINUM": "/challenges-images/303205-PLATINUM.png", "DIAMOND": "/challenges-images/303205-DIAMOND.png", "MASTER": "/challenges-images/303205-MASTER.png", "GRANDMASTER": "/challenges-images/303205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 45}, "MASTER": {"value": 60}}}, {"id": 303300, "name": "Sincronia", "description": "Ottieni progressi dalle sfide nel gruppo Sincronia", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Sincronia", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303300-IRON.png", "BRONZE": "/challenges-images/303300-BRONZE.png", "SILVER": "/challenges-images/303300-SILVER.png", "GOLD": "/challenges-images/303300-GOLD.png", "PLATINUM": "/challenges-images/303300-PLATINUM.png", "DIAMOND": "/challenges-images/303300-DIAMOND.png", "MASTER": "/challenges-images/303300-MASTER.png", "GRANDMASTER": "/challenges-images/303300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "In sincronia"}]}, "MASTER": {"value": 250}}}, {"id": 303301, "name": "<PERSON><PERSON><PERSON> potere", "description": "Ottieni delle vittorie con un gruppo organizzato da 2 nella coda classificata", "shortDescription": "Vinci con un gruppo organizzato da 2 nella coda classificata", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303301-IRON.png", "BRONZE": "/challenges-images/303301-BRONZE.png", "SILVER": "/challenges-images/303301-SILVER.png", "GOLD": "/challenges-images/303301-GOLD.png", "PLATINUM": "/challenges-images/303301-PLATINUM.png", "DIAMOND": "/challenges-images/303301-DIAMOND.png", "MASTER": "/challenges-images/303301-MASTER.png", "GRANDMASTER": "/challenges-images/303301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 60}, "GOLD": {"value": 100}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 750}, "CHALLENGER": {"value": 1500}}}, {"id": 303302, "name": "Amici per sempre", "description": "Ottieni vittorie con lo stesso gruppo di 5 giocatori", "shortDescription": "Ottieni delle vittorie con un gruppo organizzato di 5", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303302-IRON.png", "BRONZE": "/challenges-images/303302-BRONZE.png", "SILVER": "/challenges-images/303302-SILVER.png", "GOLD": "/challenges-images/303302-GOLD.png", "PLATINUM": "/challenges-images/303302-PLATINUM.png", "DIAMOND": "/challenges-images/303302-DIAMOND.png", "MASTER": "/challenges-images/303302-MASTER.png", "GRANDMASTER": "/challenges-images/303302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 25}, "PLATINUM": {"value": 65}, "DIAMOND": {"value": 125}, "MASTER": {"value": 250}, "GRANDMASTER": {"value": 350}, "CHALLENGER": {"value": 500}}}, {"id": 303303, "name": "Divertimento con gli amici", "description": "Gioca partite con un gruppo organizzato di qualsiasi dimensione", "shortDescription": "Gioca delle partite con un gruppo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303303-IRON.png", "BRONZE": "/challenges-images/303303-BRONZE.png", "SILVER": "/challenges-images/303303-SILVER.png", "GOLD": "/challenges-images/303303-GOLD.png", "PLATINUM": "/challenges-images/303303-PLATINUM.png", "DIAMOND": "/challenges-images/303303-DIAMOND.png", "MASTER": "/challenges-images/303303-MASTER.png", "GRANDMASTER": "/challenges-images/303303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 75}, "GOLD": {"value": 135}, "PLATINUM": {"value": 250}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1200}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2000}}}, {"id": 303400, "name": "Armonia", "description": "Ottieni progressi dalle sfide nel gruppo Armonia", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Armonia", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303400-IRON.png", "BRONZE": "/challenges-images/303400-BRONZE.png", "SILVER": "/challenges-images/303400-SILVER.png", "GOLD": "/challenges-images/303400-GOLD.png", "PLATINUM": "/challenges-images/303400-PLATINUM.png", "DIAMOND": "/challenges-images/303400-DIAMOND.png", "MASTER": "/challenges-images/303400-MASTER.png", "GRANDMASTER": "/challenges-images/303400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 100}, "SILVER": {"value": 150}, "GOLD": {"value": 200}, "PLATINUM": {"value": 320}, "DIAMOND": {"value": 580, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Armonia"}]}, "MASTER": {"value": 950}}}, {"id": 303401, "name": "Non puoi nasconderti", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con un'abilità globale", "shortDescription": "Vinci con un gruppo di 5 <em>campioni con almeno 3 abilità globali</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303401-IRON.png", "BRONZE": "/challenges-images/303401-BRONZE.png", "SILVER": "/challenges-images/303401-SILVER.png", "GOLD": "/challenges-images/303401-GOLD.png", "PLATINUM": "/challenges-images/303401-PLATINUM.png", "DIAMOND": "/challenges-images/303401-DIAMOND.png", "MASTER": "/challenges-images/303401-MASTER.png", "GRANDMASTER": "/challenges-images/303401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303401-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303402, "name": "Ha ''Suprema'' nel nome!", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con una suprema ad area di grandi dimensioni", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>hanno una suprema ad area di grandi dimensioni</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303402-IRON.png", "BRONZE": "/challenges-images/303402-BRONZE.png", "SILVER": "/challenges-images/303402-SILVER.png", "GOLD": "/challenges-images/303402-GOLD.png", "PLATINUM": "/challenges-images/303402-PLATINUM.png", "DIAMOND": "/challenges-images/303402-DIAMOND.png", "MASTER": "/challenges-images/303402-MASTER.png", "GRANDMASTER": "/challenges-images/303402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303402-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303403, "name": "<PERSON>i proteggiamo", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con una guarigione o uno scudo. <i>Le guarigioni e gli scudi su se stessi non contano</i>", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>con cure o scudi.</em> ", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303403-IRON.png", "BRONZE": "/challenges-images/303403-BRONZE.png", "SILVER": "/challenges-images/303403-SILVER.png", "GOLD": "/challenges-images/303403-GOLD.png", "PLATINUM": "/challenges-images/303403-PLATINUM.png", "DIAMOND": "/challenges-images/303403-DIAMOND.png", "MASTER": "/challenges-images/303403-MASTER.png", "GRANDMASTER": "/challenges-images/303403-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303403-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303404, "name": "Loro... Non... MUOIONO!", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con un'abilità zombi, di resurrezione o di immunità", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 che <em>sfuggono alla morte</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303404-IRON.png", "BRONZE": "/challenges-images/303404-BRONZE.png", "SILVER": "/challenges-images/303404-SILVER.png", "GOLD": "/challenges-images/303404-GOLD.png", "PLATINUM": "/challenges-images/303404-PLATINUM.png", "DIAMOND": "/challenges-images/303404-DIAMOND.png", "MASTER": "/challenges-images/303404-MASTER.png", "GRANDMASTER": "/challenges-images/303404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303404-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303405, "name": "Dove sono andati?", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con abilità furtive (mimesi o invisibilità incluse)", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>con abilità furtive</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303405-IRON.png", "BRONZE": "/challenges-images/303405-BRONZE.png", "SILVER": "/challenges-images/303405-SILVER.png", "GOLD": "/challenges-images/303405-GOLD.png", "PLATINUM": "/challenges-images/303405-PLATINUM.png", "DIAMOND": "/challenges-images/303405-DIAMOND.png", "MASTER": "/challenges-images/303405-MASTER.png", "GRANDMASTER": "/challenges-images/303405-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303405-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303406, "name": "Qui siamo a posto", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni da ''poke'' (campioni dotati di abilità a lunga gittata e senza bersaglio)", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>da poke</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303406-IRON.png", "BRONZE": "/challenges-images/303406-BRONZE.png", "SILVER": "/challenges-images/303406-SILVER.png", "GOLD": "/challenges-images/303406-GOLD.png", "PLATINUM": "/challenges-images/303406-PLATINUM.png", "DIAMOND": "/challenges-images/303406-DIAMOND.png", "MASTER": "/challenges-images/303406-MASTER.png", "GRANDMASTER": "/challenges-images/303406-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303406-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303407, "name": "Evocatori nella Landa", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni con un'evocazione o una mascotte", "shortDescription": "Vinci con un gruppo di 5 campioni <em>con un'evocazione o una mascotte</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303407-IRON.png", "BRONZE": "/challenges-images/303407-BRONZE.png", "SILVER": "/challenges-images/303407-SILVER.png", "GOLD": "/challenges-images/303407-GOLD.png", "PLATINUM": "/challenges-images/303407-PLATINUM.png", "DIAMOND": "/challenges-images/303407-DIAMOND.png", "MASTER": "/challenges-images/303407-MASTER.png", "GRANDMASTER": "/challenges-images/303407-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303407-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303408, "name": "La varietà è sopravvalutata", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni di una classe (assassino, mago, tiratore, tank, supporto o combattente)", "shortDescription": "Vinci con un gruppo di 5 campioni della stessa classe", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303408-IRON.png", "BRONZE": "/challenges-images/303408-BRONZE.png", "SILVER": "/challenges-images/303408-SILVER.png", "GOLD": "/challenges-images/303408-GOLD.png", "PLATINUM": "/challenges-images/303408-PLATINUM.png", "DIAMOND": "/challenges-images/303408-DIAMOND.png", "MASTER": "/challenges-images/303408-MASTER.png", "GRANDMASTER": "/challenges-images/303408-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303408-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303409, "name": "Vieni qui", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con uno spostamento", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>con spostamenti</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303409-IRON.png", "BRONZE": "/challenges-images/303409-BRONZE.png", "SILVER": "/challenges-images/303409-SILVER.png", "GOLD": "/challenges-images/303409-GOLD.png", "PLATINUM": "/challenges-images/303409-PLATINUM.png", "DIAMOND": "/challenges-images/303409-DIAMOND.png", "MASTER": "/challenges-images/303409-MASTER.png", "GRANDMASTER": "/challenges-images/303409-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303409-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303410, "name": "È una trappola!", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con una trappola", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>con trappole</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303410-IRON.png", "BRONZE": "/challenges-images/303410-BRONZE.png", "SILVER": "/challenges-images/303410-SILVER.png", "GOLD": "/challenges-images/303410-GOLD.png", "PLATINUM": "/challenges-images/303410-PLATINUM.png", "DIAMOND": "/challenges-images/303410-DIAMOND.png", "MASTER": "/challenges-images/303410-MASTER.png", "GRANDMASTER": "/challenges-images/303410-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303410-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303411, "name": "<PERSON><PERSON>", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni che possono creare terreno", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>con abilità per la creazione del terreno</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303411-IRON.png", "BRONZE": "/challenges-images/303411-BRONZE.png", "SILVER": "/challenges-images/303411-SILVER.png", "GOLD": "/challenges-images/303411-GOLD.png", "PLATINUM": "/challenges-images/303411-PLATINUM.png", "DIAMOND": "/challenges-images/303411-DIAMOND.png", "MASTER": "/challenges-images/303411-MASTER.png", "GRANDMASTER": "/challenges-images/303411-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303411-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303412, "name": "Resta in posa", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 3 o più campioni con almeno 2 abilità immobilizzanti", "shortDescription": "Vinci con un gruppo di 5 campioni di cui almeno 3 <em>con almeno 2 abilità immobilizzanti</em>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/303412-IRON.png", "BRONZE": "/challenges-images/303412-BRONZE.png", "SILVER": "/challenges-images/303412-SILVER.png", "GOLD": "/challenges-images/303412-GOLD.png", "PLATINUM": "/challenges-images/303412-PLATINUM.png", "DIAMOND": "/challenges-images/303412-DIAMOND.png", "MASTER": "/challenges-images/303412-MASTER.png", "GRANDMASTER": "/challenges-images/303412-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303412-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 8}, "GRANDMASTER": {"value": 10}, "CHALLENGER": {"value": 15}}}, {"id": 303500, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ottieni progressi dalle sfide nel gruppo Giramondo", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Giramondo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303500-IRON.png", "BRONZE": "/challenges-images/303500-BRONZE.png", "SILVER": "/challenges-images/303500-SILVER.png", "GOLD": "/challenges-images/303500-GOLD.png", "PLATINUM": "/challenges-images/303500-PLATINUM.png", "DIAMOND": "/challenges-images/303500-DIAMOND.png", "MASTER": "/challenges-images/303500-MASTER.png", "GRANDMASTER": "/challenges-images/303500-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303500-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 100}, "SILVER": {"value": 150}, "GOLD": {"value": 215}, "PLATINUM": {"value": 385}, "DIAMOND": {"value": 620, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Abitante di Runeterra"}]}, "MASTER": {"value": 1050}}}, {"id": 303501, "name": "5 in meno di 5'", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Bandle City", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Bandle City</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303501-IRON.png", "BRONZE": "/challenges-images/303501-BRONZE.png", "SILVER": "/challenges-images/303501-SILVER.png", "GOLD": "/challenges-images/303501-GOLD.png", "PLATINUM": "/challenges-images/303501-PLATINUM.png", "DIAMOND": "/challenges-images/303501-DIAMOND.png", "MASTER": "/challenges-images/303501-MASTER.png", "GRANDMASTER": "/challenges-images/303501-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303501-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303502, "name": "Ai posti di combattimento", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Bilgewater", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Bilgewater</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303502-IRON.png", "BRONZE": "/challenges-images/303502-BRONZE.png", "SILVER": "/challenges-images/303502-SILVER.png", "GOLD": "/challenges-images/303502-GOLD.png", "PLATINUM": "/challenges-images/303502-PLATINUM.png", "DIAMOND": "/challenges-images/303502-DIAMOND.png", "MASTER": "/challenges-images/303502-MASTER.png", "GRANDMASTER": "/challenges-images/303502-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303502-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303503, "name": "PER DEMACIA", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Demacia", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Demacia</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303503-IRON.png", "BRONZE": "/challenges-images/303503-BRONZE.png", "SILVER": "/challenges-images/303503-SILVER.png", "GOLD": "/challenges-images/303503-GOLD.png", "PLATINUM": "/challenges-images/303503-PLATINUM.png", "DIAMOND": "/challenges-images/303503-DIAMOND.png", "MASTER": "/challenges-images/303503-MASTER.png", "GRANDMASTER": "/challenges-images/303503-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303503-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303504, "name": "Ice, Ice, Baby", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni dal Freljord", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da<PERSON> Freljord</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303504-IRON.png", "BRONZE": "/challenges-images/303504-BRONZE.png", "SILVER": "/challenges-images/303504-SILVER.png", "GOLD": "/challenges-images/303504-GOLD.png", "PLATINUM": "/challenges-images/303504-PLATINUM.png", "DIAMOND": "/challenges-images/303504-DIAMOND.png", "MASTER": "/challenges-images/303504-MASTER.png", "GRANDMASTER": "/challenges-images/303504-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303504-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303505, "name": "Everybody was <PERSON><PERSON>", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Ionia", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Ionia</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303505-IRON.png", "BRONZE": "/challenges-images/303505-BRONZE.png", "SILVER": "/challenges-images/303505-SILVER.png", "GOLD": "/challenges-images/303505-GOLD.png", "PLATINUM": "/challenges-images/303505-PLATINUM.png", "DIAMOND": "/challenges-images/303505-DIAMOND.png", "MASTER": "/challenges-images/303505-MASTER.png", "GRANDMASTER": "/challenges-images/303505-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303505-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303506, "name": "Elementale, mio caro <PERSON>", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Ixtal", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Ixtal</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303506-IRON.png", "BRONZE": "/challenges-images/303506-BRONZE.png", "SILVER": "/challenges-images/303506-SILVER.png", "GOLD": "/challenges-images/303506-GOLD.png", "PLATINUM": "/challenges-images/303506-PLATINUM.png", "DIAMOND": "/challenges-images/303506-DIAMOND.png", "MASTER": "/challenges-images/303506-MASTER.png", "GRANDMASTER": "/challenges-images/303506-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303506-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303507, "name": "La forza prima di tutto", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Noxus", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Noxus</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303507-IRON.png", "BRONZE": "/challenges-images/303507-BRONZE.png", "SILVER": "/challenges-images/303507-SILVER.png", "GOLD": "/challenges-images/303507-GOLD.png", "PLATINUM": "/challenges-images/303507-PLATINUM.png", "DIAMOND": "/challenges-images/303507-DIAMOND.png", "MASTER": "/challenges-images/303507-MASTER.png", "GRANDMASTER": "/challenges-images/303507-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303507-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303508, "name": "Calcolato", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Piltover", "shortDescription": "Vinci con un gruppo di 5 <em>camp<PERSON>i da Piltover</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303508-IRON.png", "BRONZE": "/challenges-images/303508-BRONZE.png", "SILVER": "/challenges-images/303508-SILVER.png", "GOLD": "/challenges-images/303508-GOLD.png", "PLATINUM": "/challenges-images/303508-PLATINUM.png", "DIAMOND": "/challenges-images/303508-DIAMOND.png", "MASTER": "/challenges-images/303508-MASTER.png", "GRANDMASTER": "/challenges-images/303508-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303508-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303509, "name": "<PERSON><PERSON><PERSON><PERSON> schel<PERSON>ri spa<PERSON>i", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni dalle Isole Ombra", "shortDescription": "Vinci con un gruppo di 5 <em>campioni dalle Isole Ombra</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303509-IRON.png", "BRONZE": "/challenges-images/303509-BRONZE.png", "SILVER": "/challenges-images/303509-SILVER.png", "GOLD": "/challenges-images/303509-GOLD.png", "PLATINUM": "/challenges-images/303509-PLATINUM.png", "DIAMOND": "/challenges-images/303509-DIAMOND.png", "MASTER": "/challenges-images/303509-MASTER.png", "GRANDMASTER": "/challenges-images/303509-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303509-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303510, "name": "Il Disco solare non tramonta mai", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Shurima", "shortDescription": "Vinci con un gruppo di 5 <em>camp<PERSON><PERSON> <PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303510-IRON.png", "BRONZE": "/challenges-images/303510-BRONZE.png", "SILVER": "/challenges-images/303510-SILVER.png", "GOLD": "/challenges-images/303510-GOLD.png", "PLATINUM": "/challenges-images/303510-PLATINUM.png", "DIAMOND": "/challenges-images/303510-DIAMOND.png", "MASTER": "/challenges-images/303510-MASTER.png", "GRANDMASTER": "/challenges-images/303510-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303510-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303511, "name": "Prestazione spettacolare", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Targon", "shortDescription": "Vinci con un gruppo di 5 <em>camp<PERSON><PERSON> da Targon</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303511-IRON.png", "BRONZE": "/challenges-images/303511-BRONZE.png", "SILVER": "/challenges-images/303511-SILVER.png", "GOLD": "/challenges-images/303511-GOLD.png", "PLATINUM": "/challenges-images/303511-PLATINUM.png", "DIAMOND": "/challenges-images/303511-DIAMOND.png", "MASTER": "/challenges-images/303511-MASTER.png", "GRANDMASTER": "/challenges-images/303511-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303511-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303512, "name": "(<PERSON><PERSON><PERSON>)", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni dal Vuoto", "shortDescription": "Vinci con un gruppo di 5 <em>campioni dal Vuoto</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303512-IRON.png", "BRONZE": "/challenges-images/303512-BRONZE.png", "SILVER": "/challenges-images/303512-SILVER.png", "GOLD": "/challenges-images/303512-GOLD.png", "PLATINUM": "/challenges-images/303512-PLATINUM.png", "DIAMOND": "/challenges-images/303512-DIAMOND.png", "MASTER": "/challenges-images/303512-MASTER.png", "GRANDMASTER": "/challenges-images/303512-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303512-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 303513, "name": "Compagni Chemtech", "description": "Con un gruppo organizzato di 5 persone, vinci partite con 5 campioni da Zaun", "shortDescription": "Vinci con un gruppo di 5 <em>campioni da Zaun</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/303513-IRON.png", "BRONZE": "/challenges-images/303513-BRONZE.png", "SILVER": "/challenges-images/303513-SILVER.png", "GOLD": "/challenges-images/303513-GOLD.png", "PLATINUM": "/challenges-images/303513-PLATINUM.png", "DIAMOND": "/challenges-images/303513-DIAMOND.png", "MASTER": "/challenges-images/303513-MASTER.png", "GRANDMASTER": "/challenges-images/303513-GRANDMASTER.png", "CHALLENGER": "/challenges-images/303513-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 6}, "MASTER": {"value": 10}}}, {"id": 4, "name": "LAVORO DI SQUADRA", "description": "", "shortDescription": "Configurazione LAVORO DI SQUADRA", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 200}, "SILVER": {"value": 350}, "GOLD": {"value": 1300}, "PLATINUM": {"value": 2100}, "DIAMOND": {"value": 3800}, "MASTER": {"value": 6500}}}, {"id": 401000, "name": "Saggio", "description": "Ottieni progressi dalle sfide nei grup<PERSON>, Virtuoso e Carneficina", "shortDescription": "Ottieni progressi dalle sfide nei grup<PERSON>, Virtuoso e Carneficina", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401000-IRON.png", "BRONZE": "/challenges-images/401000-BRONZE.png", "SILVER": "/challenges-images/401000-SILVER.png", "GOLD": "/challenges-images/401000-GOLD.png", "PLATINUM": "/challenges-images/401000-PLATINUM.png", "DIAMOND": "/challenges-images/401000-DIAMOND.png", "MASTER": "/challenges-images/401000-MASTER.png", "GRANDMASTER": "/challenges-images/401000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 55}, "BRONZE": {"value": 105}, "SILVER": {"value": 160}, "GOLD": {"value": 350}, "PLATINUM": {"value": 560}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1700, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Saggio"}]}}}, {"id": 401100, "name": "<PERSON>", "description": "Ottieni progressi dalle sfide nel gruppo Guru", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Guru", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401100-IRON.png", "BRONZE": "/challenges-images/401100-BRONZE.png", "SILVER": "/challenges-images/401100-SILVER.png", "GOLD": "/challenges-images/401100-GOLD.png", "PLATINUM": "/challenges-images/401100-PLATINUM.png", "DIAMOND": "/challenges-images/401100-DIAMOND.png", "MASTER": "/challenges-images/401100-MASTER.png", "GRANDMASTER": "/challenges-images/401100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON>"}]}, "MASTER": {"value": 475}, "GRANDMASTER": {"value": 570}, "CHALLENGER": {"value": 690}}}, {"id": 401101, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> tutti", "description": "Fai raggiungere a 150 campioni almeno un certo numero di punti maestria", "shortDescription": "Ottieni punti maestria con 150 campioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401101-IRON.png", "BRONZE": "/challenges-images/401101-BRONZE.png", "SILVER": "/challenges-images/401101-SILVER.png", "GOLD": "/challenges-images/401101-GOLD.png", "PLATINUM": "/challenges-images/401101-PLATINUM.png", "DIAMOND": "/challenges-images/401101-DIAMOND.png", "MASTER": "/challenges-images/401101-MASTER.png", "GRANDMASTER": "/challenges-images/401101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 500}, "SILVER": {"value": 1000}, "GOLD": {"value": 5000}, "PLATINUM": {"value": 10000, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Maestro campione"}]}, "DIAMOND": {"value": 50000}, "MASTER": {"value": 100000}, "GRANDMASTER": {"value": 107500}, "CHALLENGER": {"value": 115000}}}, {"id": 401102, "name": "Maestro saggio", "description": "Ottieni un totale di Punti maestria", "shortDescription": "Ottieni punti maestria", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401102-IRON.png", "BRONZE": "/challenges-images/401102-BRONZE.png", "SILVER": "/challenges-images/401102-SILVER.png", "GOLD": "/challenges-images/401102-GOLD.png", "PLATINUM": "/challenges-images/401102-PLATINUM.png", "DIAMOND": "/challenges-images/401102-DIAMOND.png", "MASTER": "/challenges-images/401102-MASTER.png", "GRANDMASTER": "/challenges-images/401102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1200}, "BRONZE": {"value": 3500}, "SILVER": {"value": 35000}, "GOLD": {"value": 220000}, "PLATINUM": {"value": 900000}, "DIAMOND": {"value": 2500000}, "MASTER": {"value": 5000000}, "GRANDMASTER": {"value": 5000000}, "CHALLENGER": {"value": 5000000}}}, {"id": 401103, "name": "Professionista selettivo", "description": "Ottieni punti Maestria con un singolo campione", "shortDescription": "Ottieni punti Maestria con un singolo campione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401103-IRON.png", "BRONZE": "/challenges-images/401103-BRONZE.png", "SILVER": "/challenges-images/401103-SILVER.png", "GOLD": "/challenges-images/401103-GOLD.png", "PLATINUM": "/challenges-images/401103-PLATINUM.png", "DIAMOND": "/challenges-images/401103-DIAMOND.png", "MASTER": "/challenges-images/401103-MASTER.png", "GRANDMASTER": "/challenges-images/401103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401103-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 850}, "BRONZE": {"value": 1500}, "SILVER": {"value": 9000}, "GOLD": {"value": 38000}, "PLATINUM": {"value": 110000}, "DIAMOND": {"value": 280000}, "MASTER": {"value": 840000}, "GRANDMASTER": {"value": 1000000}, "CHALLENGER": {"value": 1500000}}}, {"id": 401104, "name": "Domina te stesso", "description": "Ottieni Maestria 5 con campioni differenti", "shortDescription": "Ottieni Maestria 5 <em>con campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401104-IRON.png", "BRONZE": "/challenges-images/401104-BRONZE.png", "SILVER": "/challenges-images/401104-SILVER.png", "GOLD": "/challenges-images/401104-GOLD.png", "PLATINUM": "/challenges-images/401104-PLATINUM.png", "DIAMOND": "/challenges-images/401104-DIAMOND.png", "MASTER": "/challenges-images/401104-MASTER.png", "GRANDMASTER": "/challenges-images/401104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 50}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}}}, {"id": 401105, "name": "<PERSON><PERSON> il nemico (Antico)", "description": "Ottieni Maestria 7 con campioni differenti", "shortDescription": "Ottieni Maestria 7 <em>con campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401105-IRON.png", "BRONZE": "/challenges-images/401105-BRONZE.png", "SILVER": "/challenges-images/401105-SILVER.png", "GOLD": "/challenges-images/401105-GOLD.png", "PLATINUM": "/challenges-images/401105-PLATINUM.png", "DIAMOND": "/challenges-images/401105-DIAMOND.png", "MASTER": "/challenges-images/401105-MASTER.png", "GRANDMASTER": "/challenges-images/401105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tuffatore di profondità"}]}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}}}, {"id": 401106, "name": "Eclettico dei campioni", "description": "Vinci una partita con campioni differenti", "shortDescription": "Vinci partite <em>con campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401106-IRON.png", "BRONZE": "/challenges-images/401106-BRONZE.png", "SILVER": "/challenges-images/401106-SILVER.png", "GOLD": "/challenges-images/401106-GOLD.png", "PLATINUM": "/challenges-images/401106-PLATINUM.png", "DIAMOND": "/challenges-images/401106-DIAMOND.png", "MASTER": "/challenges-images/401106-MASTER.png", "GRANDMASTER": "/challenges-images/401106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401106-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}}}, {"id": 401107, "name": "Domina il nemico", "description": "Ottieni Maestria 10 con campioni differenti", "shortDescription": "Ottieni Maestria 10 con <em>campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401105-IRON.png", "BRONZE": "/challenges-images/401105-BRONZE.png", "SILVER": "/challenges-images/401105-SILVER.png", "GOLD": "/challenges-images/401105-GOLD.png", "PLATINUM": "/challenges-images/401105-PLATINUM.png", "DIAMOND": "/challenges-images/401105-DIAMOND.png", "MASTER": "/challenges-images/401105-MASTER.png", "GRANDMASTER": "/challenges-images/401105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 15}, "GOLD": {"value": 25}, "PLATINUM": {"value": 40, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Maestro globale"}]}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}}}, {"id": 401200, "name": "Virtuoso", "description": "Ottieni progressi dalle sfide nel gruppo Virtuoso", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Virtuoso", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401200-IRON.png", "BRONZE": "/challenges-images/401200-BRONZE.png", "SILVER": "/challenges-images/401200-SILVER.png", "GOLD": "/challenges-images/401200-GOLD.png", "PLATINUM": "/challenges-images/401200-PLATINUM.png", "DIAMOND": "/challenges-images/401200-DIAMOND.png", "MASTER": "/challenges-images/401200-MASTER.png", "GRANDMASTER": "/challenges-images/401200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Impareggiabile"}]}, "MASTER": {"value": 475}}}, {"id": 401201, "name": "Maestro assassino (Antico)", "description": "Ottieni Maestria 7 con assassini differenti", "shortDescription": "Ottieni Maestria 7 con <em>assassini</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401201-IRON.png", "BRONZE": "/challenges-images/401201-BRONZE.png", "SILVER": "/challenges-images/401201-SILVER.png", "GOLD": "/challenges-images/401201-GOLD.png", "PLATINUM": "/challenges-images/401201-PLATINUM.png", "DIAMOND": "/challenges-images/401201-DIAMOND.png", "MASTER": "/challenges-images/401201-MASTER.png", "GRANDMASTER": "/challenges-images/401201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Maestro della morte"}]}, "DIAMOND": {"value": 35}, "MASTER": {"value": 45}}}, {"id": 401202, "name": "Maestro combattente (Antico)", "description": "Ottieni Maestria 7 con combattenti differenti", "shortDescription": "Ottieni Maestria 7 con <em>combattenti</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401202-IRON.png", "BRONZE": "/challenges-images/401202-BRONZE.png", "SILVER": "/challenges-images/401202-SILVER.png", "GOLD": "/challenges-images/401202-GOLD.png", "PLATINUM": "/challenges-images/401202-PLATINUM.png", "DIAMOND": "/challenges-images/401202-DIAMOND.png", "MASTER": "/challenges-images/401202-MASTER.png", "GRANDMASTER": "/challenges-images/401202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Signore della guerra"}]}, "DIAMOND": {"value": 50}, "MASTER": {"value": 70}}}, {"id": 401203, "name": "Maestro mago (Antico)", "description": "Ottieni Maestria 7 con maghi differenti", "shortDescription": "Ottieni Maestria 7 con <em>maghi</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401203-IRON.png", "BRONZE": "/challenges-images/401203-BRONZE.png", "SILVER": "/challenges-images/401203-SILVER.png", "GOLD": "/challenges-images/401203-GOLD.png", "PLATINUM": "/challenges-images/401203-PLATINUM.png", "DIAMOND": "/challenges-images/401203-DIAMOND.png", "MASTER": "/challenges-images/401203-MASTER.png", "GRANDMASTER": "/challenges-images/401203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Arcimago"}]}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}}}, {"id": 401204, "name": "Maestro tiratore (Antico)", "description": "Ottieni Maestria 7 con tiratori differenti", "shortDescription": "Ottieni Maestria 7 con <em>tiratori</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401204-IRON.png", "BRONZE": "/challenges-images/401204-BRONZE.png", "SILVER": "/challenges-images/401204-SILVER.png", "GOLD": "/challenges-images/401204-GOLD.png", "PLATINUM": "/challenges-images/401204-PLATINUM.png", "DIAMOND": "/challenges-images/401204-DIAMOND.png", "MASTER": "/challenges-images/401204-MASTER.png", "GRANDMASTER": "/challenges-images/401204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tira<PERSON> scelto"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401205, "name": "Maestro supporto (Antico)", "description": "Ottieni Maestria 7 con supporti differenti", "shortDescription": "Ottieni Maestria 7 con <em>supporti</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401205-IRON.png", "BRONZE": "/challenges-images/401205-BRONZE.png", "SILVER": "/challenges-images/401205-SILVER.png", "GOLD": "/challenges-images/401205-GOLD.png", "PLATINUM": "/challenges-images/401205-PLATINUM.png", "DIAMOND": "/challenges-images/401205-DIAMOND.png", "MASTER": "/challenges-images/401205-MASTER.png", "GRANDMASTER": "/challenges-images/401205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Guardiano"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401206, "name": "Maestro tank (Antico)", "description": "Ottieni Maestria 7 con tank differenti", "shortDescription": "Ottieni Maestria 7 con <em>tank</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401206-IRON.png", "BRONZE": "/challenges-images/401206-BRONZE.png", "SILVER": "/challenges-images/401206-SILVER.png", "GOLD": "/challenges-images/401206-GOLD.png", "PLATINUM": "/challenges-images/401206-PLATINUM.png", "DIAMOND": "/challenges-images/401206-DIAMOND.png", "MASTER": "/challenges-images/401206-MASTER.png", "GRANDMASTER": "/challenges-images/401206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Titano"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 40}}}, {"id": 401207, "name": "Maestro assassino", "description": "Ottieni Maestria 10 con assassini differenti", "shortDescription": "Ottieni Maestria 10 con <em>assassini</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401201-IRON.png", "BRONZE": "/challenges-images/401201-BRONZE.png", "SILVER": "/challenges-images/401201-SILVER.png", "GOLD": "/challenges-images/401201-GOLD.png", "PLATINUM": "/challenges-images/401201-PLATINUM.png", "DIAMOND": "/challenges-images/401201-DIAMOND.png", "MASTER": "/challenges-images/401201-MASTER.png", "GRANDMASTER": "/challenges-images/401201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Mi<PERSON><PERSON>"}]}, "DIAMOND": {"value": 35}, "MASTER": {"value": 45}}}, {"id": 401208, "name": "Maestro combattente", "description": "Ottieni Maestria 10 con combattenti differenti", "shortDescription": "Ottieni Maestria 10 con <em>combattenti</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401202-IRON.png", "BRONZE": "/challenges-images/401202-BRONZE.png", "SILVER": "/challenges-images/401202-SILVER.png", "GOLD": "/challenges-images/401202-GOLD.png", "PLATINUM": "/challenges-images/401202-PLATINUM.png", "DIAMOND": "/challenges-images/401202-DIAMOND.png", "MASTER": "/challenges-images/401202-MASTER.png", "GRANDMASTER": "/challenges-images/401202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Conquistatore"}]}, "DIAMOND": {"value": 50}, "MASTER": {"value": 70}}}, {"id": 401209, "name": "Maestro mago", "description": "Ottieni Maestria 10 con maghi differenti", "shortDescription": "Ottieni Maestria 10 con <em>maghi</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401203-IRON.png", "BRONZE": "/challenges-images/401203-BRONZE.png", "SILVER": "/challenges-images/401203-SILVER.png", "GOLD": "/challenges-images/401203-GOLD.png", "PLATINUM": "/challenges-images/401203-PLATINUM.png", "DIAMOND": "/challenges-images/401203-DIAMOND.png", "MASTER": "/challenges-images/401203-MASTER.png", "GRANDMASTER": "/challenges-images/401203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Stregone supremo"}]}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}}}, {"id": 401210, "name": "Maestro tiratore", "description": "Ottieni Maestria 10 con tiratori differenti", "shortDescription": "Ottieni Maestria 10 con <em>tiratori</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401204-IRON.png", "BRONZE": "/challenges-images/401204-BRONZE.png", "SILVER": "/challenges-images/401204-SILVER.png", "GOLD": "/challenges-images/401204-GOLD.png", "PLATINUM": "/challenges-images/401204-PLATINUM.png", "DIAMOND": "/challenges-images/401204-DIAMOND.png", "MASTER": "/challenges-images/401204-MASTER.png", "GRANDMASTER": "/challenges-images/401204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tiratore"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401211, "name": "Maestro supporto", "description": "Ottieni Maestria 10 con supporti differenti", "shortDescription": "Ottieni Maestria 10 con <em>supporti</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401205-IRON.png", "BRONZE": "/challenges-images/401205-BRONZE.png", "SILVER": "/challenges-images/401205-SILVER.png", "GOLD": "/challenges-images/401205-GOLD.png", "PLATINUM": "/challenges-images/401205-PLATINUM.png", "DIAMOND": "/challenges-images/401205-DIAMOND.png", "MASTER": "/challenges-images/401205-MASTER.png", "GRANDMASTER": "/challenges-images/401205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Guardiano supremo"}]}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 401212, "name": "Maestro tank", "description": "Ottieni Maestria 10 con tank differenti", "shortDescription": "Ottieni Maestria 10 con <em>tank</em> differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/401206-IRON.png", "BRONZE": "/challenges-images/401206-BRONZE.png", "SILVER": "/challenges-images/401206-SILVER.png", "GOLD": "/challenges-images/401206-GOLD.png", "PLATINUM": "/challenges-images/401206-PLATINUM.png", "DIAMOND": "/challenges-images/401206-DIAMOND.png", "MASTER": "/challenges-images/401206-MASTER.png", "GRANDMASTER": "/challenges-images/401206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Colosso"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 40}}}, {"id": 401300, "name": "Carneficina", "description": "Ottieni progressi dalle sfide nel gruppo Carneficina", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Carneficina", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401300-IRON.png", "BRONZE": "/challenges-images/401300-BRONZE.png", "SILVER": "/challenges-images/401300-SILVER.png", "GOLD": "/challenges-images/401300-GOLD.png", "PLATINUM": "/challenges-images/401300-PLATINUM.png", "DIAMOND": "/challenges-images/401300-DIAMOND.png", "MASTER": "/challenges-images/401300-MASTER.png", "GRANDMASTER": "/challenges-images/401300-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401300-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 30}, "SILVER": {"value": 45}, "GOLD": {"value": 100}, "PLATINUM": {"value": 160}, "DIAMOND": {"value": 290, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Il migliore"}]}, "MASTER": {"value": 475}, "GRANDMASTER": {"value": 570}, "CHALLENGER": {"value": 690}}}, {"id": 401301, "name": "Jungle Diff", "description": "Vinci delle partite come campione da giungla", "shortDescription": "Vinci delle partite come campione da giungla", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401301-IRON.png", "BRONZE": "/challenges-images/401301-BRONZE.png", "SILVER": "/challenges-images/401301-SILVER.png", "GOLD": "/challenges-images/401301-GOLD.png", "PLATINUM": "/challenges-images/401301-PLATINUM.png", "DIAMOND": "/challenges-images/401301-DIAMOND.png", "MASTER": "/challenges-images/401301-MASTER.png", "GRANDMASTER": "/challenges-images/401301-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401301-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Jungle Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401302, "name": "Support Diff", "description": "Vinci delle partite come supporto", "shortDescription": "Vinci delle partite come supporto", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401302-IRON.png", "BRONZE": "/challenges-images/401302-BRONZE.png", "SILVER": "/challenges-images/401302-SILVER.png", "GOLD": "/challenges-images/401302-GOLD.png", "PLATINUM": "/challenges-images/401302-PLATINUM.png", "DIAMOND": "/challenges-images/401302-DIAMOND.png", "MASTER": "/challenges-images/401302-MASTER.png", "GRANDMASTER": "/challenges-images/401302-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401302-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Support Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401303, "name": "<PERSON><PERSON>", "description": "Vinci delle partite come carry da corsia inferiore", "shortDescription": "Vinci delle partite come carry da corsia inferiore", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401303-IRON.png", "BRONZE": "/challenges-images/401303-BRONZE.png", "SILVER": "/challenges-images/401303-SILVER.png", "GOLD": "/challenges-images/401303-GOLD.png", "PLATINUM": "/challenges-images/401303-PLATINUM.png", "DIAMOND": "/challenges-images/401303-DIAMOND.png", "MASTER": "/challenges-images/401303-MASTER.png", "GRANDMASTER": "/challenges-images/401303-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401303-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON>"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401304, "name": "Mid Diff", "description": "Vinci delle partite come centrale", "shortDescription": "Vinci delle partite come centrale", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401304-IRON.png", "BRONZE": "/challenges-images/401304-BRONZE.png", "SILVER": "/challenges-images/401304-SILVER.png", "GOLD": "/challenges-images/401304-GOLD.png", "PLATINUM": "/challenges-images/401304-PLATINUM.png", "DIAMOND": "/challenges-images/401304-DIAMOND.png", "MASTER": "/challenges-images/401304-MASTER.png", "GRANDMASTER": "/challenges-images/401304-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401304-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Mid Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401305, "name": "Top Diff", "description": "Vinci delle partite come top", "shortDescription": "Vinci delle partite come top", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401305-IRON.png", "BRONZE": "/challenges-images/401305-BRONZE.png", "SILVER": "/challenges-images/401305-SILVER.png", "GOLD": "/challenges-images/401305-GOLD.png", "PLATINUM": "/challenges-images/401305-PLATINUM.png", "DIAMOND": "/challenges-images/401305-DIAMOND.png", "MASTER": "/challenges-images/401305-MASTER.png", "GRANDMASTER": "/challenges-images/401305-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401305-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Top Diff"}]}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 401306, "name": "Player Diff", "description": "Vinci partite in coda come riempimento, giocando la posizione che ti viene assegnata", "shortDescription": "Vinci delle partite in coda come riempimento", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/401306-IRON.png", "BRONZE": "/challenges-images/401306-BRONZE.png", "SILVER": "/challenges-images/401306-SILVER.png", "GOLD": "/challenges-images/401306-GOLD.png", "PLATINUM": "/challenges-images/401306-PLATINUM.png", "DIAMOND": "/challenges-images/401306-DIAMOND.png", "MASTER": "/challenges-images/401306-MASTER.png", "GRANDMASTER": "/challenges-images/401306-GRANDMASTER.png", "CHALLENGER": "/challenges-images/401306-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 65, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Player Diff"}]}, "DIAMOND": {"value": 90}, "MASTER": {"value": 115}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 300}}}, {"id": 402000, "name": "Medaglia al valore", "description": "Ottieni progressi dalle sfide nei gruppi Carnefice, Commando, Pieno di risorse e Mistico", "shortDescription": "Ottieni progressi dalle sfide nei gruppi Carnefice, Commando, Pieno di risorse e Mistico", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/402000-IRON.png", "BRONZE": "/challenges-images/402000-BRONZE.png", "SILVER": "/challenges-images/402000-SILVER.png", "GOLD": "/challenges-images/402000-GOLD.png", "PLATINUM": "/challenges-images/402000-PLATINUM.png", "DIAMOND": "/challenges-images/402000-DIAMOND.png", "MASTER": "/challenges-images/402000-MASTER.png", "GRANDMASTER": "/challenges-images/402000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 80}, "BRONZE": {"value": 165}, "SILVER": {"value": 250}, "GOLD": {"value": 570}, "PLATINUM": {"value": 910}, "DIAMOND": {"value": 1625}, "MASTER": {"value": 2700, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Veterano"}]}}}, {"id": 402100, "name": "Carnefice", "description": "Ottieni progressi dalle sfide nel gruppo Carnefice", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Carnefice", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402100-IRON.png", "BRONZE": "/challenges-images/402100-BRONZE.png", "SILVER": "/challenges-images/402100-SILVER.png", "GOLD": "/challenges-images/402100-GOLD.png", "PLATINUM": "/challenges-images/402100-PLATINUM.png", "DIAMOND": "/challenges-images/402100-DIAMOND.png", "MASTER": "/challenges-images/402100-MASTER.png", "GRANDMASTER": "/challenges-images/402100-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402100-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 150}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 430, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Carnefice"}]}, "MASTER": {"value": 725}, "GRANDMASTER": {"value": 800}, "CHALLENGER": {"value": 900}}}, {"id": 402101, "name": "<PERSON>mpre più in alto", "description": "Vinci partite classificate Solo/Duo o classificate Flex", "shortDescription": "Vinci delle partite classificate in Solo/Duo o Flex", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402101-IRON.png", "BRONZE": "/challenges-images/402101-BRONZE.png", "SILVER": "/challenges-images/402101-SILVER.png", "GOLD": "/challenges-images/402101-GOLD.png", "PLATINUM": "/challenges-images/402101-PLATINUM.png", "DIAMOND": "/challenges-images/402101-DIAMOND.png", "MASTER": "/challenges-images/402101-MASTER.png", "GRANDMASTER": "/challenges-images/402101-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402101-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 75}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 300}, "MASTER": {"value": 500}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 402102, "name": "Maestro della Landa", "description": "Gioca partite nella Landa degli evocatori. Sono incluse Partita rapida, Draft e le modalità classificate.", "shortDescription": "Gioca delle partite nella Landa degli evocatori", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402102-IRON.png", "BRONZE": "/challenges-images/402102-BRONZE.png", "SILVER": "/challenges-images/402102-SILVER.png", "GOLD": "/challenges-images/402102-GOLD.png", "PLATINUM": "/challenges-images/402102-PLATINUM.png", "DIAMOND": "/challenges-images/402102-DIAMOND.png", "MASTER": "/challenges-images/402102-MASTER.png", "GRANDMASTER": "/challenges-images/402102-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402102-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 60}, "GOLD": {"value": 150}, "PLATINUM": {"value": 300}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1000}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 5000}}}, {"id": 402103, "name": "Leggenda leggendaria", "description": "Diventa leggendario (8-0 uccisioni consecutive)", "shortDescription": "Diventa leggendario", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402103-IRON.png", "BRONZE": "/challenges-images/402103-BRONZE.png", "SILVER": "/challenges-images/402103-SILVER.png", "GOLD": "/challenges-images/402103-GOLD.png", "PLATINUM": "/challenges-images/402103-PLATINUM.png", "DIAMOND": "/challenges-images/402103-DIAMOND.png", "MASTER": "/challenges-images/402103-MASTER.png", "GRANDMASTER": "/challenges-images/402103-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402103-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 65}, "MASTER": {"value": 125}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 200}}}, {"id": 402104, "name": "Serie di serie di uccisioni", "description": "Ottieni una serie di uccisioni (3 o più uccisioni senza morire)", "shortDescription": "Ottieni delle serie di uccisioni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402104-IRON.png", "BRONZE": "/challenges-images/402104-BRONZE.png", "SILVER": "/challenges-images/402104-SILVER.png", "GOLD": "/challenges-images/402104-GOLD.png", "PLATINUM": "/challenges-images/402104-PLATINUM.png", "DIAMOND": "/challenges-images/402104-DIAMOND.png", "MASTER": "/challenges-images/402104-MASTER.png", "GRANDMASTER": "/challenges-images/402104-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402104-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 12}, "SILVER": {"value": 30}, "GOLD": {"value": 80}, "PLATINUM": {"value": 200}, "DIAMOND": {"value": 400}, "MASTER": {"value": 720}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 402105, "name": "Follia di uccisioni multiple", "description": "Ottieni uccisioni multiple", "shortDescription": "Ottieni uccisioni multiple", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402105-IRON.png", "BRONZE": "/challenges-images/402105-BRONZE.png", "SILVER": "/challenges-images/402105-SILVER.png", "GOLD": "/challenges-images/402105-GOLD.png", "PLATINUM": "/challenges-images/402105-PLATINUM.png", "DIAMOND": "/challenges-images/402105-DIAMOND.png", "MASTER": "/challenges-images/402105-MASTER.png", "GRANDMASTER": "/challenges-images/402105-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402105-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 16}, "SILVER": {"value": 40}, "GOLD": {"value": 120}, "PLATINUM": {"value": 300}, "DIAMOND": {"value": 540}, "MASTER": {"value": 960}, "GRANDMASTER": {"value": 1380}, "CHALLENGER": {"value": 1800}}}, {"id": 402106, "name": "PENTAKIIIIIIIIL!!", "description": "<PERSON><PERSON><PERSON>", "shortDescription": "<PERSON><PERSON><PERSON>", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402106-IRON.png", "BRONZE": "/challenges-images/402106-BRONZE.png", "SILVER": "/challenges-images/402106-SILVER.png", "GOLD": "/challenges-images/402106-GOLD.png", "PLATINUM": "/challenges-images/402106-PLATINUM.png", "DIAMOND": "/challenges-images/402106-DIAMOND.png", "MASTER": "/challenges-images/402106-MASTER.png", "GRANDMASTER": "/challenges-images/402106-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402106-CHALLENGER.png"}, "thresholds": {"GOLD": {"value": 1}, "PLATINUM": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Pentakiller"}]}, "DIAMOND": {"value": 3}, "MASTER": {"value": 5}, "GRANDMASTER": {"value": 7}, "CHALLENGER": {"value": 10}}}, {"id": 402107, "name": "Una dura giornata di uccisioni", "description": "<PERSON><PERSON>eni <PERSON>i", "shortDescription": "<PERSON><PERSON>eni <PERSON>i", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402107-IRON.png", "BRONZE": "/challenges-images/402107-BRONZE.png", "SILVER": "/challenges-images/402107-SILVER.png", "GOLD": "/challenges-images/402107-GOLD.png", "PLATINUM": "/challenges-images/402107-PLATINUM.png", "DIAMOND": "/challenges-images/402107-DIAMOND.png", "MASTER": "/challenges-images/402107-MASTER.png", "GRANDMASTER": "/challenges-images/402107-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402107-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 35}, "BRONZE": {"value": 150}, "SILVER": {"value": 350}, "GOLD": {"value": 750}, "PLATINUM": {"value": 2000}, "DIAMOND": {"value": 5000}, "MASTER": {"value": 10000}, "GRANDMASTER": {"value": 15000}, "CHALLENGER": {"value": 25000}}}, {"id": 402108, "name": "Supporto sopra la media", "description": "<PERSON><PERSON><PERSON><PERSON> assist", "shortDescription": "<PERSON><PERSON><PERSON><PERSON> assist", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402108-IRON.png", "BRONZE": "/challenges-images/402108-BRONZE.png", "SILVER": "/challenges-images/402108-SILVER.png", "GOLD": "/challenges-images/402108-GOLD.png", "PLATINUM": "/challenges-images/402108-PLATINUM.png", "DIAMOND": "/challenges-images/402108-DIAMOND.png", "MASTER": "/challenges-images/402108-MASTER.png", "GRANDMASTER": "/challenges-images/402108-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402108-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 50}, "BRONZE": {"value": 200}, "SILVER": {"value": 500}, "GOLD": {"value": 1250}, "PLATINUM": {"value": 3500}, "DIAMOND": {"value": 6750}, "MASTER": {"value": 15000}, "GRANDMASTER": {"value": 20000}, "CHALLENGER": {"value": 35000}}}, {"id": 402109, "name": "<PERSON><PERSON><PERSON> sanguinaria", "description": "Ottieni il Primo sangue", "shortDescription": "Ottieni il Primo sangue", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/402109-IRON.png", "BRONZE": "/challenges-images/402109-BRONZE.png", "SILVER": "/challenges-images/402109-SILVER.png", "GOLD": "/challenges-images/402109-GOLD.png", "PLATINUM": "/challenges-images/402109-PLATINUM.png", "DIAMOND": "/challenges-images/402109-DIAMOND.png", "MASTER": "/challenges-images/402109-MASTER.png", "GRANDMASTER": "/challenges-images/402109-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402109-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 15, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Assetato di sangue"}]}, "PLATINUM": {"value": 40}, "DIAMOND": {"value": 75}, "MASTER": {"value": 150}}}, {"id": 402200, "name": "Commando", "description": "Ottieni progressi dalle sfide nel gruppo Commando", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Commando", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402200-IRON.png", "BRONZE": "/challenges-images/402200-BRONZE.png", "SILVER": "/challenges-images/402200-SILVER.png", "GOLD": "/challenges-images/402200-GOLD.png", "PLATINUM": "/challenges-images/402200-PLATINUM.png", "DIAMOND": "/challenges-images/402200-DIAMOND.png", "MASTER": "/challenges-images/402200-MASTER.png", "GRANDMASTER": "/challenges-images/402200-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402200-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 25}, "BRONZE": {"value": 50}, "SILVER": {"value": 75}, "GOLD": {"value": 165}, "PLATINUM": {"value": 265}, "DIAMOND": {"value": 480, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Commando"}]}, "MASTER": {"value": 800}, "GRANDMASTER": {"value": 960}, "CHALLENGER": {"value": 1160}}}, {"id": 402201, "name": "Furto sensazionale", "description": "Ruba mostri epici. I mostri epici includono i Draghi, il Messaggero della Landa, le larve del Vuoto, <PERSON><PERSON><PERSON> e il <PERSON>.", "shortDescription": "<PERSON><PERSON> mostri epici", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402201-IRON.png", "BRONZE": "/challenges-images/402201-BRONZE.png", "SILVER": "/challenges-images/402201-SILVER.png", "GOLD": "/challenges-images/402201-GOLD.png", "PLATINUM": "/challenges-images/402201-PLATINUM.png", "DIAMOND": "/challenges-images/402201-DIAMOND.png", "MASTER": "/challenges-images/402201-MASTER.png", "GRANDMASTER": "/challenges-images/402201-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402201-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "DIAMOND": {"value": 50}, "MASTER": {"value": 100}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 200}}}, {"id": 402202, "name": "<PERSON>nti tutta, <PERSON><PERSON>!", "description": "Partecipa alla conquista delle torri con il Messaggero della Landa", "shortDescription": "Conquista le torri con il Messaggero della Landa", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402202-IRON.png", "BRONZE": "/challenges-images/402202-BRONZE.png", "SILVER": "/challenges-images/402202-SILVER.png", "GOLD": "/challenges-images/402202-GOLD.png", "PLATINUM": "/challenges-images/402202-PLATINUM.png", "DIAMOND": "/challenges-images/402202-DIAMOND.png", "MASTER": "/challenges-images/402202-MASTER.png", "GRANDMASTER": "/challenges-images/402202-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402202-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300}, "PLATINUM": {"value": 750}, "DIAMOND": {"value": 1400}, "MASTER": {"value": 2500}, "GRANDMASTER": {"value": 3000}, "CHALLENGER": {"value": 4000}}}, {"id": 402203, "name": "Ora diamoci una mossa", "description": "Uccidi gli <PERSON>i", "shortDescription": "Uccidi gli <PERSON>i", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402203-IRON.png", "BRONZE": "/challenges-images/402203-BRONZE.png", "SILVER": "/challenges-images/402203-SILVER.png", "GOLD": "/challenges-images/402203-GOLD.png", "PLATINUM": "/challenges-images/402203-PLATINUM.png", "DIAMOND": "/challenges-images/402203-DIAMOND.png", "MASTER": "/challenges-images/402203-MASTER.png", "GRANDMASTER": "/challenges-images/402203-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402203-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 150}, "GOLD": {"value": 300}, "PLATINUM": {"value": 750}, "DIAMOND": {"value": 1400}, "MASTER": {"value": 2500}, "GRANDMASTER": {"value": 3000}, "CHALLENGER": {"value": 4000}}}, {"id": 402204, "name": "Mangiamo dentro", "description": "Ottieni CS dai mostri della giungla nella tua giungla", "shortDescription": "Uccidi i mostri della giungla nella tua giungla", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402204-IRON.png", "BRONZE": "/challenges-images/402204-BRONZE.png", "SILVER": "/challenges-images/402204-SILVER.png", "GOLD": "/challenges-images/402204-GOLD.png", "PLATINUM": "/challenges-images/402204-PLATINUM.png", "DIAMOND": "/challenges-images/402204-DIAMOND.png", "MASTER": "/challenges-images/402204-MASTER.png", "GRANDMASTER": "/challenges-images/402204-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402204-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 400}, "BRONZE": {"value": 1600}, "SILVER": {"value": 4000}, "GOLD": {"value": 12000}, "PLATINUM": {"value": 30000}, "DIAMOND": {"value": 55000}, "MASTER": {"value": 96000}, "GRANDMASTER": {"value": 125000}, "CHALLENGER": {"value": 175000}}}, {"id": 402205, "name": "Mangiamo fuori", "description": "Uccidi i mostri della giungla nella giungla nemica", "shortDescription": "Uccidi i mostri della giungla nella giungla nemica", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402205-IRON.png", "BRONZE": "/challenges-images/402205-BRONZE.png", "SILVER": "/challenges-images/402205-SILVER.png", "GOLD": "/challenges-images/402205-GOLD.png", "PLATINUM": "/challenges-images/402205-PLATINUM.png", "DIAMOND": "/challenges-images/402205-DIAMOND.png", "MASTER": "/challenges-images/402205-MASTER.png", "GRANDMASTER": "/challenges-images/402205-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402205-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 60}, "BRONZE": {"value": 240}, "SILVER": {"value": 600}, "GOLD": {"value": 1500}, "PLATINUM": {"value": 4000}, "DIAMOND": {"value": 8000}, "MASTER": {"value": 14000}, "GRANDMASTER": {"value": 20000}, "CHALLENGER": {"value": 30000}}}, {"id": 402206, "name": "Caccia alle lucertole", "description": "<PERSON><PERSON><PERSON> i Draghi", "shortDescription": "<PERSON><PERSON><PERSON> i Draghi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402206-IRON.png", "BRONZE": "/challenges-images/402206-BRONZE.png", "SILVER": "/challenges-images/402206-SILVER.png", "GOLD": "/challenges-images/402206-GOLD.png", "PLATINUM": "/challenges-images/402206-PLATINUM.png", "DIAMOND": "/challenges-images/402206-DIAMOND.png", "MASTER": "/challenges-images/402206-MASTER.png", "GRANDMASTER": "/challenges-images/402206-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402206-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 30}, "SILVER": {"value": 75}, "GOLD": {"value": 200}, "PLATINUM": {"value": 550}, "DIAMOND": {"value": 1000}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2500}, "CHALLENGER": {"value": 3500}}}, {"id": 402207, "name": "Pesca di Wurm", "description": "<PERSON><PERSON><PERSON> i Baroni", "shortDescription": "<PERSON><PERSON><PERSON> i Baroni", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402207-IRON.png", "BRONZE": "/challenges-images/402207-BRONZE.png", "SILVER": "/challenges-images/402207-SILVER.png", "GOLD": "/challenges-images/402207-GOLD.png", "PLATINUM": "/challenges-images/402207-PLATINUM.png", "DIAMOND": "/challenges-images/402207-DIAMOND.png", "MASTER": "/challenges-images/402207-MASTER.png", "GRANDMASTER": "/challenges-images/402207-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402207-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 8}, "SILVER": {"value": 20}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 200}, "MASTER": {"value": 350}, "GRANDMASTER": {"value": 500}, "CHALLENGER": {"value": 1000}}}, {"id": 402208, "name": "Raccolta di conchiglie", "description": "<PERSON><PERSON><PERSON> i Messaggeri della Landa", "shortDescription": "<PERSON><PERSON><PERSON> i Messaggeri della Landa", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402208-IRON.png", "BRONZE": "/challenges-images/402208-BRONZE.png", "SILVER": "/challenges-images/402208-SILVER.png", "GOLD": "/challenges-images/402208-GOLD.png", "PLATINUM": "/challenges-images/402208-PLATINUM.png", "DIAMOND": "/challenges-images/402208-DIAMOND.png", "MASTER": "/challenges-images/402208-MASTER.png", "GRANDMASTER": "/challenges-images/402208-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402208-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 65}, "PLATINUM": {"value": 180}, "DIAMOND": {"value": 300}, "MASTER": {"value": 600}, "GRANDMASTER": {"value": 1000}, "CHALLENGER": {"value": 1500}}}, {"id": 402209, "name": "La cena è pronta", "description": "Distruggi le corazze delle torri", "shortDescription": "Distruggi le corazze delle torri", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402209-IRON.png", "BRONZE": "/challenges-images/402209-BRONZE.png", "SILVER": "/challenges-images/402209-SILVER.png", "GOLD": "/challenges-images/402209-GOLD.png", "PLATINUM": "/challenges-images/402209-PLATINUM.png", "DIAMOND": "/challenges-images/402209-DIAMOND.png", "MASTER": "/challenges-images/402209-MASTER.png", "GRANDMASTER": "/challenges-images/402209-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402209-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 35}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1350}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2400}, "CHALLENGER": {"value": 3000}}}, {"id": 402210, "name": "Rovescia-Torri", "description": "Distruggi le torri", "shortDescription": "Distruggi le torri", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402210-IRON.png", "BRONZE": "/challenges-images/402210-BRONZE.png", "SILVER": "/challenges-images/402210-SILVER.png", "GOLD": "/challenges-images/402210-GOLD.png", "PLATINUM": "/challenges-images/402210-PLATINUM.png", "DIAMOND": "/challenges-images/402210-DIAMOND.png", "MASTER": "/challenges-images/402210-MASTER.png", "GRANDMASTER": "/challenges-images/402210-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402210-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 35}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1350}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2400}, "CHALLENGER": {"value": 3000}}}, {"id": 402400, "name": "Pieno di risorse", "description": "Ottieni progressi dalle sfide nel gruppo Pieno di risorse", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Pieno di risorse", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402400-IRON.png", "BRONZE": "/challenges-images/402400-BRONZE.png", "SILVER": "/challenges-images/402400-SILVER.png", "GOLD": "/challenges-images/402400-GOLD.png", "PLATINUM": "/challenges-images/402400-PLATINUM.png", "DIAMOND": "/challenges-images/402400-DIAMOND.png", "MASTER": "/challenges-images/402400-MASTER.png", "GRANDMASTER": "/challenges-images/402400-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402400-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 60}, "GOLD": {"value": 135}, "PLATINUM": {"value": 215}, "DIAMOND": {"value": 380, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Pieno di risorse"}]}, "MASTER": {"value": 650}, "GRANDMASTER": {"value": 780}, "CHALLENGER": {"value": 940}}}, {"id": 402401, "name": "L'oscurità è ovunque", "description": "<PERSON><PERSON><PERSON> i lumi", "shortDescription": "<PERSON><PERSON><PERSON> i lumi", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402401-IRON.png", "BRONZE": "/challenges-images/402401-BRONZE.png", "SILVER": "/challenges-images/402401-SILVER.png", "GOLD": "/challenges-images/402401-GOLD.png", "PLATINUM": "/challenges-images/402401-PLATINUM.png", "DIAMOND": "/challenges-images/402401-DIAMOND.png", "MASTER": "/challenges-images/402401-MASTER.png", "GRANDMASTER": "/challenges-images/402401-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402401-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 50}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 600}, "DIAMOND": {"value": 1200}, "MASTER": {"value": 2400}, "GRANDMASTER": {"value": 3000}, "CHALLENGER": {"value": 3600}}}, {"id": 402402, "name": "<PERSON><PERSON><PERSON> spie inanimate", "description": "Piazza lumi invisibili utili. Un lume è utile se contribuisce al tuo punteggio di visione.", "shortDescription": "Posiziona i lumi invisibili", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402402-IRON.png", "BRONZE": "/challenges-images/402402-BRONZE.png", "SILVER": "/challenges-images/402402-SILVER.png", "GOLD": "/challenges-images/402402-GOLD.png", "PLATINUM": "/challenges-images/402402-PLATINUM.png", "DIAMOND": "/challenges-images/402402-DIAMOND.png", "MASTER": "/challenges-images/402402-MASTER.png", "GRANDMASTER": "/challenges-images/402402-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402402-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 75}, "BRONZE": {"value": 300}, "SILVER": {"value": 750}, "GOLD": {"value": 1500}, "PLATINUM": {"value": 3750}, "DIAMOND": {"value": 9000}, "MASTER": {"value": 15000}, "GRANDMASTER": {"value": 18000}, "CHALLENGER": {"value": 25000}}}, {"id": 402403, "name": "Addio, oscurità!", "description": "Piazza lumi di controllo utili. Un lume è utile se contribuisce al tuo punteggio di visione.", "shortDescription": "Posiziona i lumi di controllo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402403-IRON.png", "BRONZE": "/challenges-images/402403-BRONZE.png", "SILVER": "/challenges-images/402403-SILVER.png", "GOLD": "/challenges-images/402403-GOLD.png", "PLATINUM": "/challenges-images/402403-PLATINUM.png", "DIAMOND": "/challenges-images/402403-DIAMOND.png", "MASTER": "/challenges-images/402403-MASTER.png", "GRANDMASTER": "/challenges-images/402403-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402403-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 40}, "SILVER": {"value": 100}, "GOLD": {"value": 200}, "PLATINUM": {"value": 500}, "DIAMOND": {"value": 900}, "MASTER": {"value": 1800}, "GRANDMASTER": {"value": 2700}, "CHALLENGER": {"value": 3600}}}, {"id": 402404, "name": "Bel tentativo", "description": "Uccidi campioni nemici sotto la tua torre", "shortDescription": "Uccidi i nemici sotto la tua torre", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402404-IRON.png", "BRONZE": "/challenges-images/402404-BRONZE.png", "SILVER": "/challenges-images/402404-SILVER.png", "GOLD": "/challenges-images/402404-GOLD.png", "PLATINUM": "/challenges-images/402404-PLATINUM.png", "DIAMOND": "/challenges-images/402404-DIAMOND.png", "MASTER": "/challenges-images/402404-MASTER.png", "GRANDMASTER": "/challenges-images/402404-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402404-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 16}, "SILVER": {"value": 40}, "GOLD": {"value": 80}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 520}, "MASTER": {"value": 960}, "GRANDMASTER": {"value": 1200}, "CHALLENGER": {"value": 1600}}}, {"id": 402407, "name": "Queste cose hanno un'anima?", "description": "Uccidi i minion", "shortDescription": "Uccidi i minion", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402407-IRON.png", "BRONZE": "/challenges-images/402407-BRONZE.png", "SILVER": "/challenges-images/402407-SILVER.png", "GOLD": "/challenges-images/402407-GOLD.png", "PLATINUM": "/challenges-images/402407-PLATINUM.png", "DIAMOND": "/challenges-images/402407-DIAMOND.png", "MASTER": "/challenges-images/402407-MASTER.png", "GRANDMASTER": "/challenges-images/402407-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402407-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 750}, "BRONZE": {"value": 3000}, "SILVER": {"value": 7500}, "GOLD": {"value": 15000}, "PLATINUM": {"value": 37500}, "DIAMOND": {"value": 67500}, "MASTER": {"value": 120000}, "GRANDMASTER": {"value": 180000}, "CHALLENGER": {"value": 300000}}}, {"id": 402408, "name": "<PERSON>tti devono pagare!", "description": "Ottieni oro della taglia per l'eliminazione dei campioni avversari", "shortDescription": "Ottieni oro per le taglie", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402408-IRON.png", "BRONZE": "/challenges-images/402408-BRONZE.png", "SILVER": "/challenges-images/402408-SILVER.png", "GOLD": "/challenges-images/402408-GOLD.png", "PLATINUM": "/challenges-images/402408-PLATINUM.png", "DIAMOND": "/challenges-images/402408-DIAMOND.png", "MASTER": "/challenges-images/402408-MASTER.png", "GRANDMASTER": "/challenges-images/402408-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402408-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1250}, "BRONZE": {"value": 5000}, "SILVER": {"value": 15000, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Cacciatore di taglie"}]}, "GOLD": {"value": 30000}, "PLATINUM": {"value": 90000}, "DIAMOND": {"value": 160000}, "MASTER": {"value": 300000}, "GRANDMASTER": {"value": 400000}, "CHALLENGER": {"value": 500000}}}, {"id": 402409, "name": "<PERSON><PERSON> pagare e vattene", "description": "<PERSON><PERSON><PERSON> oro", "shortDescription": "<PERSON><PERSON><PERSON> oro", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402409-IRON.png", "BRONZE": "/challenges-images/402409-BRONZE.png", "SILVER": "/challenges-images/402409-SILVER.png", "GOLD": "/challenges-images/402409-GOLD.png", "PLATINUM": "/challenges-images/402409-PLATINUM.png", "DIAMOND": "/challenges-images/402409-DIAMOND.png", "MASTER": "/challenges-images/402409-MASTER.png", "GRANDMASTER": "/challenges-images/402409-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402409-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 55500}, "BRONZE": {"value": 222000}, "SILVER": {"value": 555000}, "GOLD": {"value": 1110000}, "PLATINUM": {"value": 4150000}, "DIAMOND": {"value": 7500000}, "MASTER": {"value": 15000000}, "GRANDMASTER": {"value": 25000000}, "CHALLENGER": {"value": 50000000}}}, {"id": 402500, "name": "<PERSON><PERSON><PERSON>", "description": "Ottieni progressi dalle sfide nel gruppo Mistico", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Mistico", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/402500-IRON.png", "BRONZE": "/challenges-images/402500-BRONZE.png", "SILVER": "/challenges-images/402500-SILVER.png", "GOLD": "/challenges-images/402500-GOLD.png", "PLATINUM": "/challenges-images/402500-PLATINUM.png", "DIAMOND": "/challenges-images/402500-DIAMOND.png", "MASTER": "/challenges-images/402500-MASTER.png", "GRANDMASTER": "/challenges-images/402500-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402500-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 140, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Incantato"}]}, "MASTER": {"value": 250}}}, {"id": 402501, "name": "Medico da campo", "description": "Cura o proteggi a dovere gli alleati (la guarigione e lo scudo su se stessi non contano). La guarigione deve ripristinare salute e lo scudo deve bloccare danni reali", "shortDescription": "Cura o proteggi a dovere gli alleati", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402501-IRON.png", "BRONZE": "/challenges-images/402501-BRONZE.png", "SILVER": "/challenges-images/402501-SILVER.png", "GOLD": "/challenges-images/402501-GOLD.png", "PLATINUM": "/challenges-images/402501-PLATINUM.png", "DIAMOND": "/challenges-images/402501-DIAMOND.png", "MASTER": "/challenges-images/402501-MASTER.png", "GRANDMASTER": "/challenges-images/402501-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402501-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 6000}, "BRONZE": {"value": 24000}, "SILVER": {"value": 72000}, "GOLD": {"value": 150000}, "PLATINUM": {"value": 450000, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Protettore"}]}, "DIAMOND": {"value": 800000}, "MASTER": {"value": 1400000}, "GRANDMASTER": {"value": 2000000}, "CHALLENGER": {"value": 3000000}}}, {"id": 402502, "name": "Immobilizzazione immediata", "description": "Immobilizza i campioni nemici", "shortDescription": "Immobilizza i nemici", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402502-IRON.png", "BRONZE": "/challenges-images/402502-BRONZE.png", "SILVER": "/challenges-images/402502-SILVER.png", "GOLD": "/challenges-images/402502-GOLD.png", "PLATINUM": "/challenges-images/402502-PLATINUM.png", "DIAMOND": "/challenges-images/402502-DIAMOND.png", "MASTER": "/challenges-images/402502-MASTER.png", "GRANDMASTER": "/challenges-images/402502-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402502-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 100}, "BRONZE": {"value": 500}, "SILVER": {"value": 1200}, "GOLD": {"value": 2000}, "PLATINUM": {"value": 6000}, "DIAMOND": {"value": 15000}, "MASTER": {"value": 25000}, "GRANDMASTER": {"value": 40000}, "CHALLENGER": {"value": 65000}}}, {"id": 402503, "name": "Abuso di abilità", "description": "Usa le abilità", "shortDescription": "Usa le abilità", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/402503-IRON.png", "BRONZE": "/challenges-images/402503-BRONZE.png", "SILVER": "/challenges-images/402503-SILVER.png", "GOLD": "/challenges-images/402503-GOLD.png", "PLATINUM": "/challenges-images/402503-PLATINUM.png", "DIAMOND": "/challenges-images/402503-DIAMOND.png", "MASTER": "/challenges-images/402503-MASTER.png", "GRANDMASTER": "/challenges-images/402503-GRANDMASTER.png", "CHALLENGER": "/challenges-images/402503-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1100}, "BRONZE": {"value": 4400}, "SILVER": {"value": 11000}, "GOLD": {"value": 27000}, "PLATINUM": {"value": 65000}, "DIAMOND": {"value": 148000}, "MASTER": {"value": 260000}, "GRANDMASTER": {"value": 400000}, "CHALLENGER": {"value": 650000}}}, {"id": 5, "name": "COLLEZIONE", "description": "", "shortDescription": "Configurazione COLLEZIONE", "hasLeaderboard": false, "levelToIconPath": {}, "thresholds": {"IRON": {"value": 75}, "BRONZE": {"value": 175}, "SILVER": {"value": 300}, "GOLD": {"value": 700}, "PLATINUM": {"value": 1100}, "DIAMOND": {"value": 2000}, "MASTER": {"value": 3500}}}, {"id": 501000, "name": "Esperienza", "description": "Ottieni progressi dalle sfide nel gruppo Esperienza", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Esperienza", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501000-IRON.png", "BRONZE": "/challenges-images/501000-BRONZE.png", "SILVER": "/challenges-images/501000-SILVER.png", "GOLD": "/challenges-images/501000-GOLD.png", "PLATINUM": "/challenges-images/501000-PLATINUM.png", "DIAMOND": "/challenges-images/501000-DIAMOND.png", "MASTER": "/challenges-images/501000-MASTER.png", "GRANDMASTER": "/challenges-images/502000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 85}, "GOLD": {"value": 185}, "PLATINUM": {"value": 295}, "DIAMOND": {"value": 530}, "MASTER": {"value": 875}}}, {"id": 501001, "name": "Pietra miliare", "description": "<PERSON><PERSON><PERSON> traguardi con un qualsiasi Eterno", "shortDescription": "<PERSON><PERSON><PERSON> traguardi con un qualsiasi Eterno", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/501001-IRON.png", "BRONZE": "/challenges-images/501001-BRONZE.png", "SILVER": "/challenges-images/501001-SILVER.png", "GOLD": "/challenges-images/501001-GOLD.png", "PLATINUM": "/challenges-images/501001-PLATINUM.png", "DIAMOND": "/challenges-images/501001-DIAMOND.png", "MASTER": "/challenges-images/501001-MASTER.png", "GRANDMASTER": "/challenges-images/501001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 7}, "SILVER": {"value": 15}, "GOLD": {"value": 75}, "PLATINUM": {"value": 150}, "DIAMOND": {"value": 600}, "MASTER": {"value": 1250}, "GRANDMASTER": {"value": 1500}, "CHALLENGER": {"value": 2500}}}, {"id": 501002, "name": "Mancano molte miglia", "description": "Raggiungi il traguardo 5 o superiore con un Eterno", "shortDescription": "Riaccendi un Eterno", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501002-IRON.png", "BRONZE": "/challenges-images/501002-BRONZE.png", "SILVER": "/challenges-images/501002-SILVER.png", "GOLD": "/challenges-images/501002-GOLD.png", "PLATINUM": "/challenges-images/501002-PLATINUM.png", "DIAMOND": "/challenges-images/501002-DIAMOND.png", "MASTER": "/challenges-images/501002-MASTER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 40}, "MASTER": {"value": 80}}}, {"id": 501003, "name": "Viandante a tutto tondo", "description": "Raggiungi il traguardo 15 o superiore con un Eterno", "shortDescription": "Raggiungi il traguardo 15 o superiore con un Eterno", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501003-IRON.png", "BRONZE": "/challenges-images/501003-BRONZE.png", "SILVER": "/challenges-images/501003-SILVER.png", "GOLD": "/challenges-images/501003-GOLD.png", "PLATINUM": "/challenges-images/501003-PLATINUM.png", "DIAMOND": "/challenges-images/501003-DIAMOND.png", "MASTER": "/challenges-images/501003-MASTER.png", "GRANDMASTER": "/challenges-images/501003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 8}, "DIAMOND": {"value": 12}, "MASTER": {"value": 25}, "GRANDMASTER": {"value": 35}, "CHALLENGER": {"value": 45}}}, {"id": 501004, "name": "<PERSON><PERSON><PERSON> amici", "description": "Raggiungi traguardi con gli Eterni per un singolo campione", "shortDescription": "Raggiungi traguardi con gli Eterni per un singolo campione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/501004-IRON.png", "BRONZE": "/challenges-images/501004-BRONZE.png", "SILVER": "/challenges-images/501004-SILVER.png", "GOLD": "/challenges-images/501004-GOLD.png", "PLATINUM": "/challenges-images/501004-PLATINUM.png", "DIAMOND": "/challenges-images/501004-DIAMOND.png", "MASTER": "/challenges-images/501004-MASTER.png", "GRANDMASTER": "/challenges-images/501004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/501004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 90}, "MASTER": {"value": 125}, "GRANDMASTER": {"value": 150}, "CHALLENGER": {"value": 175}}}, {"id": 501005, "name": "Riattizza la vecchia fornace", "description": "Riaccendi i set di Eterni. Un set è riacceso quando tre o più Eterni raggiungono il livello 5 o superiore", "shortDescription": "Riaccendi un set di Eterni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501005-IRON.png", "BRONZE": "/challenges-images/501005-BRONZE.png", "SILVER": "/challenges-images/501005-SILVER.png", "GOLD": "/challenges-images/501005-GOLD.png", "PLATINUM": "/challenges-images/501005-PLATINUM.png", "DIAMOND": "/challenges-images/501005-DIAMOND.png", "MASTER": "/challenges-images/501005-MASTER.png"}, "thresholds": {"BRONZE": {"value": 1}, "SILVER": {"value": 2}, "GOLD": {"value": 3}, "PLATINUM": {"value": 6}, "DIAMOND": {"value": 15}, "MASTER": {"value": 30}}}, {"id": 501007, "name": "<PERSON><PERSON> assassino", "description": "Riaccendi un set di Eterni per gli assassini", "shortDescription": "Riaccendi un set di Eterni per un <em>assassino</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501007-IRON.png", "BRONZE": "/challenges-images/501007-BRONZE.png", "SILVER": "/challenges-images/501007-SILVER.png", "GOLD": "/challenges-images/501007-GOLD.png", "PLATINUM": "/challenges-images/501007-PLATINUM.png", "DIAMOND": "/challenges-images/501007-DIAMOND.png", "MASTER": "/challenges-images/501007-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501008, "name": "Specialista combattente", "description": "Riaccendi un set di Eterni per i combattenti", "shortDescription": "Riaccendi un set di Eterni per un <em>combattente</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501008-IRON.png", "BRONZE": "/challenges-images/501008-BRONZE.png", "SILVER": "/challenges-images/501008-SILVER.png", "GOLD": "/challenges-images/501008-GOLD.png", "PLATINUM": "/challenges-images/501008-PLATINUM.png", "DIAMOND": "/challenges-images/501008-DIAMOND.png", "MASTER": "/challenges-images/501008-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501009, "name": "Specialista mago", "description": "Riaccendi un set di Eterni per i maghi", "shortDescription": "Riaccendi un set di Eterni per un <em>mago</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501009-IRON.png", "BRONZE": "/challenges-images/501009-BRONZE.png", "SILVER": "/challenges-images/501009-SILVER.png", "GOLD": "/challenges-images/501009-GOLD.png", "PLATINUM": "/challenges-images/501009-PLATINUM.png", "DIAMOND": "/challenges-images/501009-DIAMOND.png", "MASTER": "/challenges-images/501009-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501010, "name": "Specialista tiratore", "description": "Riaccendi un set di Eterni per i tiratori", "shortDescription": "Riaccendi un set di Eterni per un <em>tiratore</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501010-IRON.png", "BRONZE": "/challenges-images/501010-BRONZE.png", "SILVER": "/challenges-images/501010-SILVER.png", "GOLD": "/challenges-images/501010-GOLD.png", "PLATINUM": "/challenges-images/501010-PLATINUM.png", "DIAMOND": "/challenges-images/501010-DIAMOND.png", "MASTER": "/challenges-images/501010-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501011, "name": "Specialista supporto", "description": "Riaccendi un set di Eterni per i supporti", "shortDescription": "Riaccendi un set di Eterni per un <em>supporto</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501011-IRON.png", "BRONZE": "/challenges-images/501011-BRONZE.png", "SILVER": "/challenges-images/501011-SILVER.png", "GOLD": "/challenges-images/501011-GOLD.png", "PLATINUM": "/challenges-images/501011-PLATINUM.png", "DIAMOND": "/challenges-images/501011-DIAMOND.png", "MASTER": "/challenges-images/501011-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 501012, "name": "Specialista tank", "description": "Riaccendi un set di Eterni per i tank", "shortDescription": "Riaccendi un set di Eterni per un <em>tank</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/501012-IRON.png", "BRONZE": "/challenges-images/501012-BRONZE.png", "SILVER": "/challenges-images/501012-SILVER.png", "GOLD": "/challenges-images/501012-GOLD.png", "PLATINUM": "/challenges-images/501012-PLATINUM.png", "DIAMOND": "/challenges-images/501012-DIAMOND.png", "MASTER": "/challenges-images/501012-MASTER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 5}, "MASTER": {"value": 10}}}, {"id": 502000, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Ottieni progressi dalle sfide nel gruppo Esagerato", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Esagerato", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502000-IRON.png", "BRONZE": "/challenges-images/502000-BRONZE.png", "SILVER": "/challenges-images/502000-SILVER.png", "GOLD": "/challenges-images/502000-GOLD.png", "PLATINUM": "/challenges-images/502000-PLATINUM.png", "DIAMOND": "/challenges-images/502000-DIAMOND.png", "MASTER": "/challenges-images/502000-MASTER.png", "GRANDMASTER": "/challenges-images/502000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 15}, "BRONZE": {"value": 25}, "SILVER": {"value": 40}, "GOLD": {"value": 85}, "PLATINUM": {"value": 135}, "DIAMOND": {"value": 240, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON><PERSON>"}]}, "MASTER": {"value": 400}}}, {"id": 502001, "name": "Autorevole", "description": "<PERSON><PERSON><PERSON> titoli per sfide differenti", "shortDescription": "<PERSON><PERSON><PERSON> titoli dalle sfide", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502001-IRON.png", "BRONZE": "/challenges-images/502001-BRONZE.png", "SILVER": "/challenges-images/502001-SILVER.png", "GOLD": "/challenges-images/502001-GOLD.png", "PLATINUM": "/challenges-images/502001-PLATINUM.png", "DIAMOND": "/challenges-images/502001-DIAMOND.png", "MASTER": "/challenges-images/502001-MASTER.png", "GRANDMASTER": "/challenges-images/502001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 30}, "PLATINUM": {"value": 45, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "DIAMOND": {"value": 70}, "MASTER": {"value": 100}}}, {"id": 502002, "name": "Ricorsivo", "description": "Completa le sfide della collezione raggiungendo il livello più alto non in classifica", "shortDescription": "Completa una o più sfide della collezione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502002-IRON.png", "BRONZE": "/challenges-images/502002-BRONZE.png", "SILVER": "/challenges-images/502002-SILVER.png", "GOLD": "/challenges-images/502002-GOLD.png", "PLATINUM": "/challenges-images/502002-PLATINUM.png", "DIAMOND": "/challenges-images/502002-DIAMOND.png", "MASTER": "/challenges-images/502002-MASTER.png", "GRANDMASTER": "/challenges-images/502002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 25}, "MASTER": {"value": 30}}}, {"id": 502003, "name": "Le sfide sono eterne", "description": "Ragg<PERSON><PERSON><PERSON> il livello Diamante nelle sfide", "shortDescription": "Ragg<PERSON>ng<PERSON> il livello Diamante in sfide differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502003-IRON.png", "BRONZE": "/challenges-images/502003-BRONZE.png", "SILVER": "/challenges-images/502003-SILVER.png", "GOLD": "/challenges-images/502003-GOLD.png", "PLATINUM": "/challenges-images/502003-PLATINUM.png", "DIAMOND": "/challenges-images/502003-DIAMOND.png", "MASTER": "/challenges-images/502003-MASTER.png", "GRANDMASTER": "/challenges-images/502003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}}}, {"id": 502004, "name": "Prestazioni magistrali", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> il livello Master nelle s<PERSON>de", "shortDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> il livello Master in sfide differenti", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502004-IRON.png", "BRONZE": "/challenges-images/502004-BRONZE.png", "SILVER": "/challenges-images/502004-SILVER.png", "GOLD": "/challenges-images/502004-GOLD.png", "PLATINUM": "/challenges-images/502004-PLATINUM.png", "DIAMOND": "/challenges-images/502004-DIAMOND.png", "MASTER": "/challenges-images/502004-MASTER.png", "GRANDMASTER": "/challenges-images/502004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 15}, "GOLD": {"value": 30}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 150}, "MASTER": {"value": 300}}}, {"id": 502005, "name": "Capo-lavori in corso", "description": "Raggiungi il livello Oro nei Capolavori", "shortDescription": "Raggiungi il livello Oro nei Capolavori", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/502005-IRON.png", "BRONZE": "/challenges-images/502005-BRONZE.png", "SILVER": "/challenges-images/502005-SILVER.png", "GOLD": "/challenges-images/502005-GOLD.png", "PLATINUM": "/challenges-images/502005-PLATINUM.png", "DIAMOND": "/challenges-images/502005-DIAMOND.png", "MASTER": "/challenges-images/502005-MASTER.png", "GRANDMASTER": "/challenges-images/502005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/502005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 40}, "MASTER": {"value": 50}}}, {"id": 504000, "name": "Tesoro", "description": "Ottieni progressi dalle sfide nel gruppo Tesoro", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Tesoro", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504000-IRON.png", "BRONZE": "/challenges-images/504000-BRONZE.png", "SILVER": "/challenges-images/504000-SILVER.png", "GOLD": "/challenges-images/504000-GOLD.png", "PLATINUM": "/challenges-images/504000-PLATINUM.png", "DIAMOND": "/challenges-images/504000-DIAMOND.png", "MASTER": "/challenges-images/504000-MASTER.png", "GRANDMASTER": "/challenges-images/504000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 25}, "SILVER": {"value": 35}, "GOLD": {"value": 70}, "PLATINUM": {"value": 110}, "DIAMOND": {"value": 200, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Tesoriere"}]}, "MASTER": {"value": 325}}}, {"id": 504001, "name": "<PERSON><PERSON> mascotte", "description": "<PERSON><PERSON><PERSON> loghi squadra Clash", "shortDescription": "<PERSON><PERSON><PERSON> loghi squadra Clash", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504001-IRON.png", "BRONZE": "/challenges-images/504001-BRONZE.png", "SILVER": "/challenges-images/504001-SILVER.png", "GOLD": "/challenges-images/504001-GOLD.png", "PLATINUM": "/challenges-images/504001-PLATINUM.png", "DIAMOND": "/challenges-images/504001-DIAMOND.png", "MASTER": "/challenges-images/504001-MASTER.png", "GRANDMASTER": "/challenges-images/504001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 5}, "PLATINUM": {"value": 10}, "DIAMOND": {"value": 20}, "MASTER": {"value": 40}}}, {"id": 504002, "name": "Icona della Landa", "description": "Ottieni icone dell'evocatore", "shortDescription": "Ottieni Icone dell'evocatore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504002-IRON.png", "BRONZE": "/challenges-images/504002-BRONZE.png", "SILVER": "/challenges-images/504002-SILVER.png", "GOLD": "/challenges-images/504002-GOLD.png", "PLATINUM": "/challenges-images/504002-PLATINUM.png", "DIAMOND": "/challenges-images/504002-DIAMOND.png", "MASTER": "/challenges-images/504002-MASTER.png", "GRANDMASTER": "/challenges-images/504002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Iconico"}]}, "DIAMOND": {"value": 200}, "MASTER": {"value": 400}}}, {"id": 504003, "name": "Collezionista di aspetti lume", "description": "<PERSON><PERSON><PERSON> as<PERSON> lume", "shortDescription": "<PERSON><PERSON><PERSON> as<PERSON> lume", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504003-IRON.png", "BRONZE": "/challenges-images/504003-BRONZE.png", "SILVER": "/challenges-images/504003-SILVER.png", "GOLD": "/challenges-images/504003-GOLD.png", "PLATINUM": "/challenges-images/504003-PLATINUM.png", "DIAMOND": "/challenges-images/504003-DIAMOND.png", "MASTER": "/challenges-images/504003-MASTER.png", "GRANDMASTER": "/challenges-images/504003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 30}, "MASTER": {"value": 60}}}, {"id": 504004, "name": "Emotivo", "description": "<PERSON><PERSON><PERSON> emote", "shortDescription": "<PERSON><PERSON><PERSON> emote", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/504004-IRON.png", "BRONZE": "/challenges-images/504004-BRONZE.png", "SILVER": "/challenges-images/504004-SILVER.png", "GOLD": "/challenges-images/504004-GOLD.png", "PLATINUM": "/challenges-images/504004-PLATINUM.png", "DIAMOND": "/challenges-images/504004-DIAMOND.png", "MASTER": "/challenges-images/504004-MASTER.png", "GRANDMASTER": "/challenges-images/504004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/504004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 40}, "PLATINUM": {"value": 80}, "DIAMOND": {"value": 150}, "MASTER": {"value": 275}}}, {"id": 505000, "name": "Campione", "description": "Ottieni progressi dalle sfide nel gruppo Campione", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Campione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505000-IRON.png", "BRONZE": "/challenges-images/505000-BRONZE.png", "SILVER": "/challenges-images/505000-SILVER.png", "GOLD": "/challenges-images/505000-GOLD.png", "PLATINUM": "/challenges-images/505000-PLATINUM.png", "DIAMOND": "/challenges-images/505000-DIAMOND.png", "MASTER": "/challenges-images/505000-MASTER.png", "GRANDMASTER": "/challenges-images/505000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 35}, "SILVER": {"value": 55}, "GOLD": {"value": 115}, "PLATINUM": {"value": 185}, "DIAMOND": {"value": 340, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Collezionista"}]}, "MASTER": {"value": 550}}}, {"id": 505001, "name": "Il sale della vita", "description": "<PERSON><PERSON><PERSON>", "shortDescription": "<PERSON><PERSON><PERSON> <em>camp<PERSON><PERSON></em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505001-IRON.png", "BRONZE": "/challenges-images/505001-BRONZE.png", "SILVER": "/challenges-images/505001-SILVER.png", "GOLD": "/challenges-images/505001-GOLD.png", "PLATINUM": "/challenges-images/505001-PLATINUM.png", "DIAMOND": "/challenges-images/505001-DIAMOND.png", "MASTER": "/challenges-images/505001-MASTER.png", "GRANDMASTER": "/challenges-images/505001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 25}, "SILVER": {"value": 50}, "GOLD": {"value": 75}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 125}, "MASTER": {"value": 150}}}, {"id": 505002, "name": "Invisibile", "description": "Ottieni campioni assassini", "shortDescription": "<PERSON><PERSON><PERSON> <em>campioni assassini</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505002-IRON.png", "BRONZE": "/challenges-images/505002-BRONZE.png", "SILVER": "/challenges-images/505002-SILVER.png", "GOLD": "/challenges-images/505002-GOLD.png", "PLATINUM": "/challenges-images/505002-PLATINUM.png", "DIAMOND": "/challenges-images/505002-DIAMOND.png", "MASTER": "/challenges-images/505002-MASTER.png", "GRANDMASTER": "/challenges-images/505002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 35}, "MASTER": {"value": 45}}}, {"id": 505003, "name": "Indomito", "description": "Ottieni campioni combattenti", "shortDescription": "<PERSON><PERSON><PERSON> <em>campioni combattenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505003-IRON.png", "BRONZE": "/challenges-images/505003-BRONZE.png", "SILVER": "/challenges-images/505003-SILVER.png", "GOLD": "/challenges-images/505003-GOLD.png", "PLATINUM": "/challenges-images/505003-PLATINUM.png", "DIAMOND": "/challenges-images/505003-DIAMOND.png", "MASTER": "/challenges-images/505003-MASTER.png", "GRANDMASTER": "/challenges-images/505003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 50}, "MASTER": {"value": 70}}}, {"id": 505004, "name": "Incandescente", "description": "<PERSON><PERSON><PERSON> campioni maghi", "shortDescription": "<PERSON><PERSON><PERSON> <em>camp<PERSON><PERSON> maghi</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505004-IRON.png", "BRONZE": "/challenges-images/505004-BRONZE.png", "SILVER": "/challenges-images/505004-SILVER.png", "GOLD": "/challenges-images/505004-GOLD.png", "PLATINUM": "/challenges-images/505004-PLATINUM.png", "DIAMOND": "/challenges-images/505004-DIAMOND.png", "MASTER": "/challenges-images/505004-MASTER.png", "GRANDMASTER": "/challenges-images/505004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 12}, "GOLD": {"value": 18}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}}}, {"id": 505005, "name": "Ingegno<PERSON>", "description": "Ottieni campioni tiratori", "shortDescription": "<PERSON><PERSON><PERSON> <em>campioni tiratori</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505005-IRON.png", "BRONZE": "/challenges-images/505005-BRONZE.png", "SILVER": "/challenges-images/505005-SILVER.png", "GOLD": "/challenges-images/505005-GOLD.png", "PLATINUM": "/challenges-images/505005-PLATINUM.png", "DIAMOND": "/challenges-images/505005-DIAMOND.png", "MASTER": "/challenges-images/505005-MASTER.png", "GRANDMASTER": "/challenges-images/505005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 505006, "name": "Ispiratore", "description": "Ottieni campioni di supporto", "shortDescription": "<PERSON><PERSON><PERSON> <em>campioni di supporto</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505006-IRON.png", "BRONZE": "/challenges-images/505006-BRONZE.png", "SILVER": "/challenges-images/505006-SILVER.png", "GOLD": "/challenges-images/505006-GOLD.png", "PLATINUM": "/challenges-images/505006-PLATINUM.png", "DIAMOND": "/challenges-images/505006-DIAMOND.png", "MASTER": "/challenges-images/505006-MASTER.png", "GRANDMASTER": "/challenges-images/505006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/505006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 30}}}, {"id": 505007, "name": "Invulnerabile", "description": "Ottieni campioni tank", "shortDescription": "<PERSON><PERSON><PERSON> <em>campioni tank</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/505007-IRON.png", "BRONZE": "/challenges-images/505007-BRONZE.png", "SILVER": "/challenges-images/505007-SILVER.png", "GOLD": "/challenges-images/505007-GOLD.png", "PLATINUM": "/challenges-images/505007-PLATINUM.png", "DIAMOND": "/challenges-images/505007-DIAMOND.png", "MASTER": "/challenges-images/505007-MASTER.png", "CHALLENGER": "/challenges-images/505007-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 30}, "MASTER": {"value": 40}}}, {"id": 510000, "name": "Intenditore", "description": "Ottieni progressi dalle sfide nel gruppo Intenditore", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Intenditore", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510000-IRON.png", "BRONZE": "/challenges-images/510000-BRONZE.png", "SILVER": "/challenges-images/510000-SILVER.png", "GOLD": "/challenges-images/510000-GOLD.png", "PLATINUM": "/challenges-images/510000-PLATINUM.png", "DIAMOND": "/challenges-images/510000-DIAMOND.png", "MASTER": "/challenges-images/510000-MASTER.png", "GRANDMASTER": "/challenges-images/510000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 20}, "BRONZE": {"value": 40}, "SILVER": {"value": 70}, "GOLD": {"value": 150}, "PLATINUM": {"value": 240}, "DIAMOND": {"value": 430}, "MASTER": {"value": 725, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Intenditore"}]}}}, {"id": 510001, "name": "Moda d'avanguardia", "description": "<PERSON><PERSON><PERSON> camp<PERSON>i", "shortDescription": "Colleziona gli aspetti campioni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510001-IRON.png", "BRONZE": "/challenges-images/510001-BRONZE.png", "SILVER": "/challenges-images/510001-SILVER.png", "GOLD": "/challenges-images/510001-GOLD.png", "PLATINUM": "/challenges-images/510001-PLATINUM.png", "DIAMOND": "/challenges-images/510001-DIAMOND.png", "MASTER": "/challenges-images/510001-MASTER.png", "GRANDMASTER": "/challenges-images/510001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 30}, "PLATINUM": {"value": 75}, "DIAMOND": {"value": 200}, "MASTER": {"value": 500}}}, {"id": 510003, "name": "Che stile!", "description": "<PERSON><PERSON><PERSON> molti aspetti per un singolo campione", "shortDescription": "<PERSON><PERSON><PERSON> molti aspetti per un singolo campione", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510003-IRON.png", "BRONZE": "/challenges-images/510003-BRONZE.png", "SILVER": "/challenges-images/510003-SILVER.png", "GOLD": "/challenges-images/510003-GOLD.png", "PLATINUM": "/challenges-images/510003-PLATINUM.png", "DIAMOND": "/challenges-images/510003-DIAMOND.png", "MASTER": "/challenges-images/510003-MASTER.png", "GRANDMASTER": "/challenges-images/510003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 7}, "DIAMOND": {"value": 12}, "MASTER": {"value": 15}}}, {"id": 510004, "name": "Serve un guardaroba più grande", "description": "Colleziona 5 o più aspetti per un campione", "shortDescription": "Colleziona 5 o più aspetti per un <em>campione</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510004-IRON.png", "BRONZE": "/challenges-images/510004-BRONZE.png", "SILVER": "/challenges-images/510004-SILVER.png", "GOLD": "/challenges-images/510004-GOLD.png", "PLATINUM": "/challenges-images/510004-PLATINUM.png", "DIAMOND": "/challenges-images/510004-DIAMOND.png", "MASTER": "/challenges-images/510004-MASTER.png", "GRANDMASTER": "/challenges-images/510004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510004-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 5}, "GOLD": {"value": 10}, "PLATINUM": {"value": 25}, "DIAMOND": {"value": 50}, "MASTER": {"value": 75}}}, {"id": 510005, "name": "Aspetto vintage", "description": "Colleziona gli aspetti campioni Antichi", "shortDescription": "Colleziona <em>gli aspetti campioni Antichi</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510005-IRON.png", "BRONZE": "/challenges-images/510005-BRONZE.png", "SILVER": "/challenges-images/510005-SILVER.png", "GOLD": "/challenges-images/510005-GOLD.png", "PLATINUM": "/challenges-images/510005-PLATINUM.png", "DIAMOND": "/challenges-images/510005-DIAMOND.png", "MASTER": "/challenges-images/510005-MASTER.png", "GRANDMASTER": "/challenges-images/510005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 10}, "SILVER": {"value": 20}, "GOLD": {"value": 40}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 100}, "MASTER": {"value": 200}}}, {"id": 510006, "name": "Paramenti vittoriosi", "description": "Colleziona gli aspetti campioni Vittoriosi", "shortDescription": "Colleziona <em>gli aspetti campioni Vittoriosi</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510006-IRON.png", "BRONZE": "/challenges-images/510006-BRONZE.png", "SILVER": "/challenges-images/510006-SILVER.png", "GOLD": "/challenges-images/510006-GOLD.png", "PLATINUM": "/challenges-images/510006-PLATINUM.png", "DIAMOND": "/challenges-images/510006-DIAMOND.png", "MASTER": "/challenges-images/510006-MASTER.png", "GRANDMASTER": "/challenges-images/510006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510006-CHALLENGER.png"}, "thresholds": {"SILVER": {"value": 1}, "GOLD": {"value": 2, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "PLATINUM": {"value": 3}, "DIAMOND": {"value": 4}, "MASTER": {"value": 5}}}, {"id": 510007, "name": "Alta moda", "description": "Colleziona gli aspetti campioni Supremi", "shortDescription": "Colleziona <em>gli aspetti campioni Supremi</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510007-IRON.png", "BRONZE": "/challenges-images/510007-BRONZE.png", "SILVER": "/challenges-images/510007-SILVER.png", "GOLD": "/challenges-images/510007-GOLD.png", "PLATINUM": "/challenges-images/510007-PLATINUM.png", "DIAMOND": "/challenges-images/510007-DIAMOND.png", "MASTER": "/challenges-images/510007-MASTER.png", "GRANDMASTER": "/challenges-images/510007-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510007-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 3}, "GOLD": {"value": 4}, "PLATINUM": {"value": 5}, "DIAMOND": {"value": 6}, "MASTER": {"value": 7}}}, {"id": 510008, "name": "Abiti formali", "description": "Colleziona gli aspetti campioni Mitici", "shortDescription": "Colleziona <em>gli aspetti campioni Mitici</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510008-IRON.png", "BRONZE": "/challenges-images/510008-BRONZE.png", "SILVER": "/challenges-images/510008-SILVER.png", "GOLD": "/challenges-images/510008-GOLD.png", "PLATINUM": "/challenges-images/510008-PLATINUM.png", "DIAMOND": "/challenges-images/510008-DIAMOND.png", "MASTER": "/challenges-images/510008-MASTER.png", "GRANDMASTER": "/challenges-images/510008-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510008-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 20}, "MASTER": {"value": 25}}}, {"id": 510009, "name": "Guardaroba leggendario", "description": "Colleziona gli aspetti campioni Leggendari", "shortDescription": "Colleziona <em>gli aspetti campioni Leggendari</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510009-IRON.png", "BRONZE": "/challenges-images/510009-BRONZE.png", "SILVER": "/challenges-images/510009-SILVER.png", "GOLD": "/challenges-images/510009-GOLD.png", "PLATINUM": "/challenges-images/510009-PLATINUM.png", "DIAMOND": "/challenges-images/510009-DIAMOND.png", "MASTER": "/challenges-images/510009-MASTER.png", "GRANDMASTER": "/challenges-images/510009-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510009-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 7}, "GOLD": {"value": 12}, "PLATINUM": {"value": 20, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Guida di stile"}]}, "DIAMOND": {"value": 30}, "MASTER": {"value": 45}}}, {"id": 510010, "name": "Veste elegante", "description": "Colleziona gli aspetti campioni Epici", "shortDescription": "Colleziona <em>gli aspetti campioni Epici</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510010-IRON.png", "BRONZE": "/challenges-images/510010-BRONZE.png", "SILVER": "/challenges-images/510010-SILVER.png", "GOLD": "/challenges-images/510010-GOLD.png", "PLATINUM": "/challenges-images/510010-PLATINUM.png", "DIAMOND": "/challenges-images/510010-DIAMOND.png", "MASTER": "/challenges-images/510010-MASTER.png", "GRANDMASTER": "/challenges-images/510010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 175}, "MASTER": {"value": 250}}}, {"id": 510011, "name": "<PERSON><PERSON><PERSON>", "description": "Colleziona chroma", "shortDescription": "Colleziona chroma", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510011-IRON.png", "BRONZE": "/challenges-images/510011-BRONZE.png", "SILVER": "/challenges-images/510011-SILVER.png", "GOLD": "/challenges-images/510011-GOLD.png", "PLATINUM": "/challenges-images/510011-PLATINUM.png", "DIAMOND": "/challenges-images/510011-DIAMOND.png", "MASTER": "/challenges-images/510011-MASTER.png", "GRANDMASTER": "/challenges-images/510011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510011-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 5}, "SILVER": {"value": 10}, "GOLD": {"value": 15}, "PLATINUM": {"value": 30, "rewards": [{"category": "TITLE", "quantity": 1, "title": "<PERSON><PERSON><PERSON>"}]}, "DIAMOND": {"value": 60}, "MASTER": {"value": 100}}}, {"id": 510012, "name": "Tutto fa brodo", "description": "Colleziona qualsiasi aspetto non antico, vittorioso, supremo, mitico o leggendario", "shortDescription": "Colleziona <em>qualsiasi aspetto non antico, vittorio<PERSON>, supremo, mitico o leggendario</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/510010-IRON.png", "BRONZE": "/challenges-images/510010-BRONZE.png", "SILVER": "/challenges-images/510010-SILVER.png", "GOLD": "/challenges-images/510010-GOLD.png", "PLATINUM": "/challenges-images/510010-PLATINUM.png", "DIAMOND": "/challenges-images/510010-DIAMOND.png", "MASTER": "/challenges-images/510010-MASTER.png", "GRANDMASTER": "/challenges-images/510010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/510010-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 25}, "GOLD": {"value": 50}, "PLATINUM": {"value": 100}, "DIAMOND": {"value": 175}, "MASTER": {"value": 250}}}, {"id": 600006, "name": "Sono qui da un po'", "description": "Aumenta il livello del tuo Evocatore", "shortDescription": "Aumenta il livello del tuo Evocatore", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/600006-IRON.png", "BRONZE": "/challenges-images/600006-BRONZE.png", "SILVER": "/challenges-images/600006-SILVER.png", "GOLD": "/challenges-images/600006-GOLD.png", "PLATINUM": "/challenges-images/600006-PLATINUM.png", "DIAMOND": "/challenges-images/600006-DIAMOND.png", "MASTER": "/challenges-images/600006-MASTER.png", "GRANDMASTER": "/challenges-images/600006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 65}, "PLATINUM": {"value": 125}, "DIAMOND": {"value": 250}, "MASTER": {"value": 350}, "GRANDMASTER": {"value": 400}, "CHALLENGER": {"value": 500}}}, {"id": 600010, "name": "Primo in vetta", "description": "Raggiungi il primo Challenger della regione nelle classificate in Solo/Duo nello split di una stagione", "shortDescription": "Raggiungi il primo Challenger della regione nelle classificate in Solo/Duo nello split di una stagione", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/600010-IRON.png", "BRONZE": "/challenges-images/600010-BRONZE.png", "SILVER": "/challenges-images/600010-SILVER.png", "GOLD": "/challenges-images/600010-GOLD.png", "PLATINUM": "/challenges-images/600010-PLATINUM.png", "DIAMOND": "/challenges-images/600010-DIAMOND.png", "MASTER": "/challenges-images/600010-MASTER.png", "GRANDMASTER": "/challenges-images/600010-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600010-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 1}, "CHALLENGER": {"value": 1}}}, {"id": 600011, "name": "Il meglio del meglio", "description": "Completa uno split qualsiasi della stagione al livello 1 per le classificate in Solo/Duo", "shortDescription": "Completa uno split della stagione al livello 1 in Solo/Duo", "hasLeaderboard": true, "levelToIconPath": {"IRON": "/challenges-images/600011-IRON.png", "BRONZE": "/challenges-images/600011-BRONZE.png", "SILVER": "/challenges-images/600011-SILVER.png", "GOLD": "/challenges-images/600011-GOLD.png", "PLATINUM": "/challenges-images/600011-PLATINUM.png", "DIAMOND": "/challenges-images/600011-DIAMOND.png", "MASTER": "/challenges-images/600011-MASTER.png", "GRANDMASTER": "/challenges-images/600011-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600011-CHALLENGER.png"}, "thresholds": {"MASTER": {"value": 1}, "GRANDMASTER": {"value": 2}, "CHALLENGER": {"value": 3}}}, {"id": 600012, "name": "Sono arrivate le sfide!", "description": "<PERSON>el caso non lo sapessi. Lo saprai di certo. Ora lo sai. Lo sapevi?", "shortDescription": "Sei sopravvissuto al tooltip di lancio delle sfide", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/600012-IRON.png", "BRONZE": "/challenges-images/600012-BRONZE.png", "SILVER": "/challenges-images/600012-SILVER.png", "GOLD": "/challenges-images/600012-GOLD.png", "PLATINUM": "/challenges-images/600012-PLATINUM.png", "DIAMOND": "/challenges-images/600012-DIAMOND.png", "MASTER": "/challenges-images/600012-MASTER.png", "GRANDMASTER": "/challenges-images/600012-GRANDMASTER.png", "CHALLENGER": "/challenges-images/600012-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 0}}}, {"id": 601000, "name": "Lottatore dell'arena", "description": "Ottieni progressi dalle sfide nel gruppo Lottatore dell'arena", "shortDescription": "Infliggi il maggior numero di danni ai campioni nella partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601000-IRON.png", "BRONZE": "/challenges-images/601000-BRONZE.png", "SILVER": "/challenges-images/601000-SILVER.png", "GOLD": "/challenges-images/601000-GOLD.png", "PLATINUM": "/challenges-images/601000-PLATINUM.png", "DIAMOND": "/challenges-images/601000-DIAMOND.png", "MASTER": "/challenges-images/601000-MASTER.png", "GRANDMASTER": "/challenges-images/601000-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601000-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 20}, "SILVER": {"value": 45}, "GOLD": {"value": 95}, "PLATINUM": {"value": 145, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Lottatore dell'arena"}]}, "DIAMOND": {"value": 250}, "MASTER": {"value": 400}}}, {"id": 601001, "name": "Il fior fiore", "description": "Infliggi il maggior numero di danni ai campioni nella partita", "shortDescription": "Infliggi il maggior numero di danni ai campioni nella partita", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601001-IRON.png", "BRONZE": "/challenges-images/601001-BRONZE.png", "SILVER": "/challenges-images/601001-SILVER.png", "GOLD": "/challenges-images/601001-GOLD.png", "PLATINUM": "/challenges-images/601001-PLATINUM.png", "DIAMOND": "/challenges-images/601001-DIAMOND.png", "MASTER": "/challenges-images/601001-MASTER.png", "GRANDMASTER": "/challenges-images/601001-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601001-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 3}, "SILVER": {"value": 6}, "GOLD": {"value": 10}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 25}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 60}, "CHALLENGER": {"value": 90}}}, {"id": 601002, "name": "<PERSON><PERSON><PERSON> da ballo", "description": "Schiva cinque colpi mirati (abilità a distanza senza bersaglio) entro otto secondi", "shortDescription": "Schiva cinque colpi mirati entro otto secondi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601002-IRON.png", "BRONZE": "/challenges-images/601002-BRONZE.png", "SILVER": "/challenges-images/601002-SILVER.png", "GOLD": "/challenges-images/601002-GOLD.png", "PLATINUM": "/challenges-images/601002-PLATINUM.png", "DIAMOND": "/challenges-images/601002-DIAMOND.png", "MASTER": "/challenges-images/601002-MASTER.png", "GRANDMASTER": "/challenges-images/601002-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601002-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 4}, "BRONZE": {"value": 10}, "SILVER": {"value": 18}, "GOLD": {"value": 32}, "PLATINUM": {"value": 60}, "DIAMOND": {"value": 90}, "MASTER": {"value": 140}, "GRANDMASTER": {"value": 200}, "CHALLENGER": {"value": 300}}}, {"id": 601003, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>ttieni doppie uccisioni", "shortDescription": "<PERSON>ttieni doppie uccisioni", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601003-IRON.png", "BRONZE": "/challenges-images/601003-BRONZE.png", "SILVER": "/challenges-images/601003-SILVER.png", "GOLD": "/challenges-images/601003-GOLD.png", "PLATINUM": "/challenges-images/601003-PLATINUM.png", "DIAMOND": "/challenges-images/601003-DIAMOND.png", "MASTER": "/challenges-images/601003-MASTER.png", "GRANDMASTER": "/challenges-images/601003-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601003-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 5}, "SILVER": {"value": 9}, "GOLD": {"value": 14}, "PLATINUM": {"value": 20}, "DIAMOND": {"value": 28}, "MASTER": {"value": 40}, "GRANDMASTER": {"value": 65}, "CHALLENGER": {"value": 100}}}, {"id": 601004, "name": "Il muro", "description": "Subisci 10.000 danni pre-resistenze dai campioni in un singolo combattimento senza morire", "shortDescription": "Subisci 10.000 danni in un combattimento e sopravvivi", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601004-IRON.png", "BRONZE": "/challenges-images/601004-BRONZE.png", "SILVER": "/challenges-images/601004-SILVER.png", "GOLD": "/challenges-images/601004-GOLD.png", "PLATINUM": "/challenges-images/601004-PLATINUM.png", "DIAMOND": "/challenges-images/601004-DIAMOND.png", "MASTER": "/challenges-images/601004-MASTER.png", "GRANDMASTER": "/challenges-images/601004-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601004-CHALLENGER.png"}, "thresholds": {"BRONZE": {"value": 2}, "SILVER": {"value": 5}, "GOLD": {"value": 9}, "PLATINUM": {"value": 15}, "DIAMOND": {"value": 24}, "MASTER": {"value": 36}, "GRANDMASTER": {"value": 50}, "CHALLENGER": {"value": 70}}}, {"id": 601005, "name": "<PERSON>tro ogni probabil<PERSON>", "description": "Ottieni uccisioni mentre nelle vicinanze ci sono più campioni nemici che alleati", "shortDescription": "Esegui uccisioni in inferiorità numerica", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601005-IRON.png", "BRONZE": "/challenges-images/601005-BRONZE.png", "SILVER": "/challenges-images/601005-SILVER.png", "GOLD": "/challenges-images/601005-GOLD.png", "PLATINUM": "/challenges-images/601005-PLATINUM.png", "DIAMOND": "/challenges-images/601005-DIAMOND.png", "MASTER": "/challenges-images/601005-MASTER.png", "GRANDMASTER": "/challenges-images/601005-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601005-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 1}, "BRONZE": {"value": 2}, "SILVER": {"value": 4}, "GOLD": {"value": 8}, "PLATINUM": {"value": 14}, "DIAMOND": {"value": 22}, "MASTER": {"value": 35}, "GRANDMASTER": {"value": 50}, "CHALLENGER": {"value": 70}}}, {"id": 601006, "name": "Ci sono!", "description": "Salva un alleato che avrebbe subito danni letali con una cura o uno scudo", "shortDescription": "Salva un alleato con una cura o uno scudo", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/601006-IRON.png", "BRONZE": "/challenges-images/601006-BRONZE.png", "SILVER": "/challenges-images/601006-SILVER.png", "GOLD": "/challenges-images/601006-GOLD.png", "PLATINUM": "/challenges-images/601006-PLATINUM.png", "DIAMOND": "/challenges-images/601006-DIAMOND.png", "MASTER": "/challenges-images/601006-MASTER.png", "GRANDMASTER": "/challenges-images/601006-GRANDMASTER.png", "CHALLENGER": "/challenges-images/601006-CHALLENGER.png"}, "thresholds": {"IRON": {"value": 2}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 30}, "DIAMOND": {"value": 45}, "MASTER": {"value": 65}, "GRANDMASTER": {"value": 90}, "CHALLENGER": {"value": 120}}}, {"id": 602000, "name": "Campione dell'Arena", "description": "Ottieni progressi dalle sfide nel gruppo Campione in Arena", "shortDescription": "Ottieni progressi dalle sfide nel gruppo Campione in Arena", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/602000-IRON.png", "BRONZE": "/challenges-images/602000-BRONZE.png", "SILVER": "/challenges-images/602000-SILVER.png", "GOLD": "/challenges-images/602000-GOLD.png", "PLATINUM": "/challenges-images/602000-PLATINUM.png", "DIAMOND": "/challenges-images/602000-DIAMOND.png", "MASTER": "/challenges-images/602000-MASTER.png"}, "thresholds": {"IRON": {"value": 5}, "BRONZE": {"value": 10}, "SILVER": {"value": 15}, "GOLD": {"value": 35}, "PLATINUM": {"value": 55, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Campione dell'Arena"}]}, "DIAMOND": {"value": 100}, "MASTER": {"value": 150}}}, {"id": 602001, "name": "Campione oceanico dell'Arena", "description": "Gioca partite Arena con campioni differenti", "shortDescription": "Gioca partite Arena con <em>campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/602001-IRON.png", "BRONZE": "/challenges-images/602001-BRONZE.png", "SILVER": "/challenges-images/602001-SILVER.png", "GOLD": "/challenges-images/602001-GOLD.png", "PLATINUM": "/challenges-images/602001-PLATINUM.png", "DIAMOND": "/challenges-images/602001-DIAMOND.png", "MASTER": "/challenges-images/602001-MASTER.png"}, "thresholds": {"IRON": {"value": 8}, "BRONZE": {"value": 15}, "SILVER": {"value": 30}, "GOLD": {"value": 55}, "PLATINUM": {"value": 90}, "DIAMOND": {"value": 135}, "MASTER": {"value": 168}}}, {"id": 602002, "name": "Adatta a tutte le situazioni", "description": "Ottieni il primo posto in partite Arena con campioni differenti", "shortDescription": "Ottieni il primo posto in partite Arena con <em>campioni differenti</em>", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/602002-IRON.png", "BRONZE": "/challenges-images/602002-BRONZE.png", "SILVER": "/challenges-images/602002-SILVER.png", "GOLD": "/challenges-images/602002-GOLD.png", "PLATINUM": "/challenges-images/602002-PLATINUM.png", "DIAMOND": "/challenges-images/602002-DIAMOND.png", "MASTER": "/challenges-images/602002-MASTER.png"}, "thresholds": {"IRON": {"value": 3}, "BRONZE": {"value": 6}, "SILVER": {"value": 12}, "GOLD": {"value": 20}, "PLATINUM": {"value": 32}, "DIAMOND": {"value": 45}, "MASTER": {"value": 60, "rewards": [{"category": "TITLE", "quantity": 0, "title": "Dio dell'Arena"}]}}}, {"id": 603000, "name": "Superstite dello Sciame", "description": "Una medaglia Anima Squad per i servizi resi durante l'evento Sciame del 2024", "shortDescription": "Una medaglia Anima Squad per l'evento Sciame 2024", "hasLeaderboard": false, "levelToIconPath": {"IRON": "/challenges-images/603000-IRON.png", "BRONZE": "/challenges-images/603000-BRONZE.png", "SILVER": "/challenges-images/603000-SILVER.png", "GOLD": "/challenges-images/603000-GOLD.png", "PLATINUM": "/challenges-images/603000-PLATINUM.png", "DIAMOND": "/challenges-images/603000-DIAMOND.png", "MASTER": "/challenges-images/603000-MASTER.png"}, "thresholds": {"IRON": {"value": 10}, "BRONZE": {"value": 20}, "SILVER": {"value": 40}, "GOLD": {"value": 70}, "PLATINUM": {"value": 120, "rewards": [{"category": "TITLE", "quantity": 1, "title": "Superstite dello Sciame"}]}, "DIAMOND": {"value": 200}, "MASTER": {"value": 340}}}, {"id": 603001, "name": "Caccia al pesce rosso", "description": "Raccogli 1000 oro", "shortDescription": "Raccogli 1000 oro", "hasLeaderboard": false, "levelToIconPath": {"GOLD": "/challenges-images/603001-GOLD.png"}, "thresholds": {"GOLD": {"value": 1000}}}, {"id": 603002, "name": "Amore/odio per l'anemone", "description": "Sconfigg<PERSON> Briar", "shortDescription": "Sconfigg<PERSON> Briar", "hasLeaderboard": false, "levelToIconPath": {"SILVER": "/challenges-images/603002-SILVER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 603003, "name": "Passivo-aggressivo", "description": "Possiedi 3 passive contemporaneamente", "shortDescription": "Possiedi 3 passive contemporaneamente", "hasLeaderboard": false, "levelToIconPath": {"BRONZE": "/challenges-images/603003-BRONZE.png"}, "thresholds": {"BRONZE": {"value": 1}}}, {"id": 603004, "name": "La vita... ehm... trova sempre un modo", "description": "E<PERSON><PERSON><PERSON> un'arma qualsiasi", "shortDescription": "E<PERSON><PERSON><PERSON> un'arma qualsiasi", "hasLeaderboard": false, "levelToIconPath": {"SILVER": "/challenges-images/603004-SILVER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 603005, "name": "Trattieni il respiro", "description": "Sopravvivi per 15 minuti", "shortDescription": "Sopravvivi per 15 minuti", "hasLeaderboard": false, "levelToIconPath": {"SILVER": "/challenges-images/603005-SILVER.png"}, "thresholds": {"SILVER": {"value": 1}}}, {"id": 603006, "name": "<PERSON><PERSON> es<PERSON>ato", "description": "Raggiungi il livello 30 con un campione qualsiasi", "shortDescription": "Raggiungi il livello 30 con un campione qualsiasi", "hasLeaderboard": false, "levelToIconPath": {"PLATINUM": "/challenges-images/603006-PLATINUM.png"}, "thresholds": {"PLATINUM": {"value": 1}}}, {"id": 603007, "name": "Terrore ittico", "description": "Sconfiggi i nemici", "shortDescription": "Sconfiggi i nemici", "hasLeaderboard": false, "levelToIconPath": {"BRONZE": "/challenges-images/603007-BRONZE.png", "SILVER": "/challenges-images/603007-SILVER.png", "GOLD": "/challenges-images/603007-GOLD.png", "PLATINUM": "/challenges-images/603007-PLATINUM.png", "DIAMOND": "/challenges-images/603007-DIAMOND.png"}, "thresholds": {"BRONZE": {"value": 5000}, "SILVER": {"value": 10000}, "GOLD": {"value": 30000}, "PLATINUM": {"value": 60000}, "DIAMOND": {"value": 100000}}}, {"id": 603008, "name": "Caccia al pesce grosso", "description": "Sconfiggi 10 miniboss a difficoltà 2 o più", "shortDescription": "Sconfiggi 10 miniboss a difficoltà 2 o più", "hasLeaderboard": false, "levelToIconPath": {"PLATINUM": "/challenges-images/603008-PLATINUM.png"}, "thresholds": {"PLATINUM": {"value": 10}}}, {"id": 603009, "name": "Preparazione purrrfetta", "description": "Ottieni almeno 5 armi evolute a difficoltà 3", "shortDescription": "Ottieni almeno 5 armi evolute a difficoltà 3", "hasLeaderboard": false, "levelToIconPath": {"DIAMOND": "/challenges-images/603009-DIAMOND.png"}, "thresholds": {"DIAMOND": {"value": 1}}}, {"id": 603010, "name": "Leggenda di Anima", "description": "Sconfiggi Aatrox a difficoltà 3", "shortDescription": "Sconfiggi Aatrox a difficoltà 3", "hasLeaderboard": false, "levelToIconPath": {"DIAMOND": "/challenges-images/603010-DIAMOND.png"}, "thresholds": {"DIAMOND": {"value": 1}}}]