{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viktor": {"id": "<PERSON>", "key": "112", "name": "<PERSON>", "title": "the Herald of the Arcane", "image": {"full": "Viktor.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "112000", "num": 0, "name": "default", "chromas": false}, {"id": "112001", "num": 1, "name": "Full Machine Viktor", "chromas": false}, {"id": "112002", "num": 2, "name": "Prototype Viktor", "chromas": false}, {"id": "112003", "num": 3, "name": "Creator <PERSON>", "chromas": false}, {"id": "112004", "num": 4, "name": "Death Sworn Viktor", "chromas": false}, {"id": "112005", "num": 5, "name": "PsyOps Viktor", "chromas": true}, {"id": "112014", "num": 14, "name": "High Noon Viktor", "chromas": true}, {"id": "112024", "num": 24, "name": "Arcane Savior Viktor", "chromas": false}], "lore": "The fully biomechanical evolution of his former self, <PERSON> has embraced his Glorious Evolution and become something of a messiah to his followers. He sacrificed his own humanity under the logic that eliminating emotion would thereby eliminate suffering—and now seeks to bring the revelation of the hexcore to the rest of the world, even if they're incapable of understanding the benefit. After all, to this master of the arcane, violence is merely a variable necessary to balance the ultimate equation.", "blurb": "The fully biomechanical evolution of his former self, <PERSON> has embraced his Glorious Evolution and become something of a messiah to his followers. He sacrificed his own humanity under the logic that eliminating emotion would thereby eliminate...", "allytips": ["Hextech Ray is a powerful poke and a strong area of denial tool. Use it in combination with Gravity Field to control your enemy's position.", "Make sure you choose the right augment at the right time."], "enemytips": ["Be careful about how close you let <PERSON> get to you. <PERSON>'s control of the battlefield increases with his proximity to his opponent.", "Be aware of how many augments <PERSON> has upgraded by looking at the color of the light on his staff (purple, yellow, blue, red)."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 600, "hpperlevel": 100, "mp": 405, "mpperlevel": 45, "movespeed": 335, "armor": 23, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 8, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "ViktorQ", "name": "Siphon Power", "description": "<PERSON> blasts an enemy unit with magic damage, which grants him a shield and empowers his next basic attack.<br><br>Augment: <PERSON><PERSON>'s shield is increased by 60% and <PERSON> gains bonus move speed after casting the Ability.<br>", "tooltip": "<PERSON> blasts an enemy, dealing <magicDamage>{{ totalmissiledamage }} magic damage</magicDamage> and granting <PERSON> <shield>{{ shieldlevelscaling }} Shield</shield> for {{ buffduration }} seconds.<br /><br /><PERSON>'s next Attack within 3.5 seconds deals an additional <magicDamage>{{ attacktotaldmg }} magic damage</magicDamage>.<br /><br /><keywordMajor>Upgraded:</keywordMajor> Grants <shield>{{ totalaugmentedshieldvalue }} Shield</shield> and <PERSON> additionally gains <speed>{{ augmentmovespeedbonus }}% Move Speed</speed> for {{ buffduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage (Missile)", "Damage (Attack)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ aabonusdamage }} -> {{ aabonusdamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [2.5, 2.5, 2.5, 2.5, 2.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0.08, 0.08, 0.08, 0.08, 0.08], [0.18, 0.18, 0.18, 0.18, 0.18], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "2.5", "30", "0", "20/45/70/95/120", "0.08", "0.18", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViktorQ.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorW", "name": "Gravity Field", "description": "<PERSON> conjures a heavy gravitational field that slows enemies caught within its radius. Enemies who stay in the field for too long become stunned.<br><br>Augment: <PERSON>'s spells apply a slow to enemies.<br>", "tooltip": "<PERSON> deploys a gravitational imprisonment device for {{ fieldduration }} seconds, <status>Slowing</status> enemies inside it by {{ slowpotency*-1 }}%. Enemies who remain within its radius for 1.25 seconds are <status>Stunned</status> for {{ stunduration }} seconds.<br /><br /><keywordMajor>Upgraded Passive:</keywordMajor> <PERSON>'s Abilities <status>Slow</status> by {{ augmentslow }}% for 1 second.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Movement Slow"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ slowpotency*-1.000000 }}% -> {{ slowpotencynl*-1.000000 }}%"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ViktorW.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorE", "name": "Hextech Ray", "description": "<PERSON> uses his biomechanical arm to fire a Hextech Ray that cuts across the field in a line, dealing damage to all enemies in its path.<br><br>Augment: An explosion follows in the Hextech Ray's wake, dealing magic damage.<br>", "tooltip": "<PERSON> fires a hextech ray in a chosen direction, dealing <magicDamage>{{ laserdamage }} magic damage</magicDamage> to any enemies it hits.<br /><br /><keywordMajor>Upgraded:</keywordMajor> An aftershock follows Hextech Ray, dealing <magicDamage>{{ aftershockdamage }} magic damage</magicDamage>.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage (Hextech Ray)", "Damage (Aftershock)", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamagelaser }} -> {{ basedamagelaserNL }}", "{{ basedamageaftershock }} -> {{ basedamageaftershockNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [20, 60, 100, 140, 180], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "20/60/100/140/180", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "ViktorE.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ViktorR", "name": "Arcane Storm", "description": "<PERSON> conjures an Arcane Storm on the field, dealing magic damage and interrupting enemy channels. The storm periodically does magic damage to all nearby enemies and can be redirected by <PERSON>.<br><br>Augment: The Arcane Storm moves 25% faster, and whenever champions die after taking damage from it, the storm grows and increases in its duration.<br><br>", "tooltip": "<PERSON> conjures an Arcane Storm in an area for {{ stormduration }} seconds, which instantly deals <magicDamage>{{ initialburstdamage }} magic damage</magicDamage> followed by <magicDamage>{{ subsequentburstdamage }} magic damage</magicDamage> per second to surrounding enemies. The storm automatically follows champions it has recently damaged.<br /><br /><recast>Recast:</recast> <PERSON> can manually move the storm.<br /><br /><keywordMajor>Upgraded:</keywordMajor> The storm moves {{ augmentboost*100 }}% faster. If a champion that the storm has damaged dies, the storm increases its size and its duration by {{ tooltip_durationextension }} seconds (up to {{ maxgrowths }} times).<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage (Initial Burst)", "Damage (Periodic Ticks)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ initialburstbasedamage }} -> {{ initialburstbasedamageNL }}", "{{ subsequentburstbasedamage }} -> {{ subsequentburstbasedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "ViktorR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Glorious Evolution", "description": "<PERSON> gains Hex Fragments whenever he kills an enemy. With every 100 Hex Fragments he obtains, <PERSON> permanently augments an active ability. After upgrading all of his basic abilities, he can gather 100 Hex Fragments to augment his ultimate ability.", "image": {"full": "Viktor_Passive.ViktorVGU.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}