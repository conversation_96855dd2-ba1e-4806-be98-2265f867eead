{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nilah": {"id": "<PERSON><PERSON>", "key": "895", "name": "<PERSON><PERSON>", "title": "la gioia travolgente", "image": {"full": "Nilah.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "895000", "num": 0, "name": "default", "chromas": false}, {"id": "895001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "895011", "num": 11, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "895021", "num": 21, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Nilah è un'ascetica guerriera che viene da una terra lontana, alla ricerca degli avversari più letali e titanici del mondo per sfidarli e distruggerli. Avendo ottenuto i suoi poteri dopo aver incontrato un demone della gioia imprigionato per millenni, l'unica emozione che conosce è una felicità incessante - un piccolo prezzo da pagare per l'immensa forza che ora possiede. Canalizzando la forma liquida del demone in una lama di impareggiabile potenza, affronta senza paura pericoli antichi e ormai dimenticati.", "blurb": "Nilah è un'ascetica guerriera che viene da una terra lontana, alla ricerca degli avversari più letali e titanici del mondo per sfidarli e distruggerli. Avendo ottenuto i suoi poteri dopo aver incontrato un demone della gioia imprigionato per millenni...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 10}, "stats": {"hp": 570, "hpperlevel": 101, "mp": 350, "mpperlevel": 35, "movespeed": 340, "armor": 27, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 225, "hpregen": 6, "hpregenperlevel": 0.9, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 2, "attackspeedperlevel": 3, "attackspeed": 0.697}, "spells": [{"id": "NilahQ", "name": "<PERSON> informe", "description": "Con uno schiocco della lama-frusta, <PERSON><PERSON> i nemici che colpisce in linea retta nella direzione scelta. L'azione estende la gittata d'attacco per un breve periodo.", "tooltip": "<spellPassive>Passiva:</spellPassive> Nilah ottiene {{ critarmorpen }} Penetrazione armatura e i suoi attacchi contro i campioni ripristinano <healing>{{ critlifesteal }} danni inflitti come salute</healing>. La guarigione in eccesso viene convertita in uno <shield>scudo</shield> per {{ shieldduration }} secondi.<br /><br /><spellActive>Attiva:</spellActive> Nilah fa schioccare la sua lama-frusta, infliggendo <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> aumentati dalla sua probabilità di colpo critico. Se un'unità o una struttura nemica viene colpita, Nilah ottiene 125 gittata d'attacco e <attackSpeed>{{ bonusattackspeedcalc }}% velocità d'attacco</attackSpeed> e i suoi attacchi colpiscono in un'area conica per <physicalDamage>{{ attacktotaldamagetooltip }} danni fisici</physicalDamage> per {{ buffduration }} secondi.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Percentuale di attacco fisico"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ qadratio*100.000000 }}% -> {{ qadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NilahQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NilahW", "name": "Velo di giubilo", "description": "<PERSON>lah si avvolge nella nebbia, aumentando la velocità di movimento e ottenendo la possibilità di schivare con eleganza tutti gli attacchi in arrivo. Tutti gli alleati che tocca durante la durata della nebbia ottengono questo effetto.", "tooltip": "Nilah si avvolge nella nebbia per {{ baseduration }} secondi, diventando spettrale, ottenendo <speed>{{ movespeedpercent*100 }}% velocità di movimento</speed>, schivando gli attacchi e riducendo i <magicDamage>danni magici</magicDamage> subiti di un {{ magicdamagereduction*100 }}%.<br /><br />Mentre è attiva, toccare i campioni alleati li avvolge nella nebbia, conferendo gli stessi benefici per {{ sharebaseduration }} secondi.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Velocità di movimento", "Costo in @AbilityResourceName@"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ movespeedpercent*100.000000 }}% -> {{ movespeedpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [26, 25, 24, 23, 22], "cooldownBurn": "26/25/24/23/22", "cost": [60, 45, 30, 15, 0], "costBurn": "60/45/30/15/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [150, 150, 150, 150, 150], "rangeBurn": "150", "image": {"full": "NilahW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NilahE", "name": "Sc<PERSON>", "description": "<PERSON><PERSON> scatta con entusiasmo verso il bersaglio, infliggendo danni a tutti i nemici che attraversa.", "tooltip": "<PERSON><PERSON> scatta attraverso un'unità, infliggendo <physicalDamage>{{ dashdamage }} danni fisici</physicalDamage> ai nemici che attraversa.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Tempo di ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "NilahE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NilahR", "name": "Apoteosi", "description": "<PERSON><PERSON><PERSON><PERSON> la lama-frusta con allegra esuberanza, <PERSON><PERSON> infligge danni ai nemici che la circondano, per poi avvicinarli con la sua arma.", "tooltip": "Nilah rotea la sua lama-frusta, infliggendo <physicalDamage>{{ damagepertickcalctooltip }} danni fisici</physicalDamage> in 1 secondo, poi finisce con una raffica di <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> che <status>attira</status> i nemici vicini verso di lei.<br /><br />Nilah cura se stessa e gli alleati vicini di <healing>{{ champhealingpercent }} (+{{ spell.nilahq:critlifesteal }} Lama informe) dei danni inflitti alla salute dei campioni nemici ({{ otherhealingpercent*100 }}% per i bersagli diversi dai campioni)</healing> convertendo tutte le guarigioni in eccesso in uno <shield>scudo</shield> per {{ duration }} secondi.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Danni per tick"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ damagepertick }} -> {{ damagepertickNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NilahR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Gioia inesauribile", "description": "<PERSON><PERSON> ottiene un'esperienza maggiore dando il colpo di grazia ai minion, oltre alla capacità di potenziare e condividere guarigione e scudo dagli alleati nelle vicinanze.", "image": {"full": "NIlahP.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}