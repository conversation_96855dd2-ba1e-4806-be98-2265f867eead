{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Heimerdinger": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "74", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "il riverito inventore", "image": {"full": "Heimerdinger.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "74000", "num": 0, "name": "default", "chromas": false}, {"id": "74001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON> In<PERSON>", "chromas": false}, {"id": "74002", "num": 2, "name": "<PERSON><PERSON><PERSON>nger Esperimento Fallito", "chromas": false}, {"id": "74003", "num": 3, "name": "<PERSON><PERSON>y <PERSON> di Piltover", "chromas": false}, {"id": "74004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74005", "num": 5, "name": "He<PERSON>rdinger Nucleare", "chromas": false}, {"id": "74006", "num": 6, "name": "<PERSON><PERSON><PERSON>nger Addestratore di Draghi", "chromas": false}, {"id": "74015", "num": 15, "name": "<PERSON><PERSON>rdinger Festa in Piscina", "chromas": false}, {"id": "74024", "num": 24, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74033", "num": 33, "name": "<PERSON><PERSON><PERSON><PERSON> Arcane: <PERSON><PERSON>", "chromas": false}], "lore": "L'eccentrico professor <PERSON> è uno degli inventori più visionari e acclamati che il mondo abbia mai conosciuto. In quanto membro con più anzianità della Consulta di Piltover, è stato testimone dell'insaziabile desiderio di progresso della città nei momenti più sfolgoranti e in quelli più bui. Ciononostante, questo brillante scienziato nonché insegnante prosegue nel suo ferreo intento di migliorare la vita degli altri grazie ai suoi congegni anticonvenzionali.", "blurb": "L'eccentrico professor <PERSON> è uno degli inventori più visionari e acclamati che il mondo abbia mai conosciuto. In quanto membro con più anzianità della Consulta di Piltover, è stato testimone dell'insaziabile desiderio di progresso...", "allytips": ["Il piazzamento delle torrette è un fattore decisivo in battaglia. Contro molti nemici il metodo migliore è piazzarle in modo che si coprano a vicenda, ma se hanno molte abilità ad area, le tue torrette saranno distrutte velocemente.", "Usare con successo Granata electrotempesta è fondamentale per far sopravvivere He<PERSON>nger. Il rallentamento e lo stordimento tengono i nemici a bada abbastanza a lungo da punirli, ma sono anche la prima linea difensiva contro gli attacchi a sorpresa.", "Dividere i Micro-razzi permette di infliggere danni in modo più affidabile, con più efficienza, contro bersagli multipli, mentre il fuoco concentrato amplifica l'impatto."], "enemytips": ["È meglio distruggere tutte le torrette di Heimerdinger contemporaneamente, con l'aiuto dei minion, invece di combatterle una alla volta.", "Attenzione al POTENZIAMENTO!!! <PERSON> <PERSON><PERSON>, perché può usarlo per risolvere buona parte dei suoi problemi. Quando ha consumato la sua suprema, vai e colpisci!"], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 8}, "stats": {"hp": 558, "hpperlevel": 101, "mp": 385, "mpperlevel": 20, "movespeed": 340, "armor": 19, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.7, "attackspeedperlevel": 1.36, "attackspeed": 0.658}, "spells": [{"id": "<PERSON><PERSON>rdingerQ", "name": "Torretta H-28G avanzata", "description": "<PERSON><PERSON><PERSON><PERSON> piazza una torretta con cannone rapido dotata di un raggio perforante (le torrette infliggono la metà dei danni alle torri).", "tooltip": "Heimerdinger costruisce una <keywordMajor>torretta</keywordMajor> che attacca i nemici nelle vicinanze. Heimerdinger può avere al massimo {{ maxturrets }} torrette attive contemporaneamente. Le <keywordMajor>torrette</keywordMajor> si caricano lentamente. Quando sono cariche al massimo, lanciano un attacco più potente.<br /><br />Se Heimerdinger si allontana troppo dalle sue <keywordMajor>torrette</keywordMajor>, queste si disattivano dopo 8 secondi.<br /><br />Questa abilità ha {{ maxkits }} cariche.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>e", "<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamagebeam }} -> {{ basedamagebeamNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "900", "0", "0", "0", "0", "0"], "vars": [], "costType": " kit torretta e {{ cost }} mana", "maxammo": "3", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HeimerdingerQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ kitcost }} kit torretta e {{ cost }} mana"}, {"id": "HeimerdingerW", "name": "Micro-razzo hextech", "description": "<PERSON><PERSON><PERSON><PERSON> spara razzi a lunga gittata che convergono verso il puntatore.", "tooltip": "He<PERSON>rdinger spara una raffica di {{ rockets }} missili che infligge <magicDamage>{{ damage }} danni magici</magicDamage> al primo nemico colpito. I colpi dei razzi aggiuntivi infliggono danni ridotti.<br /><br />Danni massimi: <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />Le <keywordMajor>torrette</keywordMajor> vicine guadagnano il 20% di carica per ogni razzo che colpisce un campione.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [12, 18, 24, 30, 36], [25, 25, 25, 25, 25], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [108, 162, 216, 270, 324], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "12/18/24/30/36", "25", "20", "30", "108/162/216/270/324", "5", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1325, 1325, 1325, 1325, 1325], "rangeBurn": "1325", "image": {"full": "HeimerdingerW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HeimerdingerE", "name": "Granata electrotempesta CH-2", "description": "<PERSON><PERSON><PERSON><PERSON> lancia una granata sulla posizione bersaglio, infliggendo danni alle unità nemiche, stordendo chi viene colpito direttamente e rallentando le unità circostanti.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> lancia una granata che infligge <magicDamage>{{ damage }} danni magici</magicDamage> ai nemici in un'area e li <status>rallenta</status> del {{ slowpercent.0*100 }}% per {{ slowduration }} secondi. I nemici al centro dell'esplosione vengono anche <status>storditi</status> per {{ stunduration }} secondi.<br /><br />Colpire un campione carica completamente le <keywordMajor>torrette</keywordMajor> vicine.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [85, 85, 85, 85, 85], "costBurn": "85", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [970, 970, 970, 970, 970], "rangeBurn": "970", "image": {"full": "HeimerdingerE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HeimerdingerR", "name": "POTENZIAMENTO!!!", "description": "<PERSON><PERSON><PERSON><PERSON> inventa un potenziamento che aumenta gli effetti della prossima abilità. ", "tooltip": "Heimerdinger potenzia la sua prossima abilità non suprema.<br /><br /><spellName>Torretta H-28Q Apex:</spellName> piazza una <keywordMajor>torretta</keywordMajor> potenziata che permane per 8 secondi, non conta ai fini del numero massimo di torrette di Heimerdinger e infligge <magicDamage>{{ qultdamage }} danni magici</magicDamage> per colpo e <magicDamage>{{ qultdamagebeam }} danni magici</magicDamage> per colpo caricato. I suoi attacchi infliggono danni ad area e <status>rallentano</status> del 25% per 2 secondi. Inoltre, è immune agli effetti di controllo.<br /><br /><spellName>Salva di razzi hextech:</spellName> spara 4 ondate di razzi che infliggono <magicDamage>{{ wultdamage }} danni magici</magicDamage> ciascuno. I campioni e i mostri della giungla colpiti da più razzi subiscono danni ridotti, mentre i minion subiscono danni aumentati. Danni massimi: <magicDamage>{{ wulttotaldamage }} danni magici</magicDamage> <br /><br /><spellName>Granata stordente CH-3X:</spellName> lancia una granata che rimbalza e rilascia tre scariche, infliggendo <magicDamage>{{ eultdamage }} danni magici</magicDamage>. Le aree dello <status>stordimento</status> e del <status>rallentamento</status> sono più grandi.<br /><br /><recast>Rilancio:</recast> annulla questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> cannone torretta Apex", "<PERSON>ni raggio torretta", "<PERSON><PERSON> razzi", "<PERSON><PERSON> massimi Salva di razzi", "<PERSON><PERSON> granata stordente"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qultbasedamage }} -> {{ qultbasedamageNL }}", "{{ qultbasedamagebeam }} -> {{ qultbasedamagebeamNL }}", "{{ wultbasedamage }} -> {{ wultbasedamageNL }}", "{{ wulttotalbasedamage }} -> {{ wulttotalbasedamageNL }}", "{{ eultbasedamage }} -> {{ eultbasedamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 140, 180], [80, 80, 80], [1.5, 1.5, 1.5], [0.12, 0.12, 0.12], [500, 690, 865], [0.45, 0.45, 0.45], [80, 100, 120], [135, 180, 225], [28, 39, 49], [150, 250, 350]], "effectBurn": [null, "100/140/180", "80", "1.5", "0.12", "500/690/865", "0.45", "80/100/120", "135/180/225", "28/39/49", "150/250/350"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "HeimerdingerR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Affinità Hextech", "description": "Ottieni velocità di movimento quando sei vicino alle torri alleate e alle torrette piazzate da Heimerdinger.", "image": {"full": "Heimerdinger_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}