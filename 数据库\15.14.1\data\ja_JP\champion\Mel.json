{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mel": {"id": "<PERSON>", "key": "800", "name": "メル", "title": "魂の反照", "image": {"full": "Mel.png", "sprite": "champion2.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "800000", "num": 0, "name": "default", "chromas": false}, {"id": "800001", "num": 1, "name": "Arcane 評議員メル", "chromas": true}], "lore": "メル・メダルダは、かつてはノクサスで最大の権勢を誇ったメダルダ家の後継者と目される人物。表向きは優雅な貴族のように見えるが、その実体は、出会う相手のすべてについて知り尽くそうとする、卓越した政治家。謎に満ちた黒薔薇団との邂逅を経て、母の欺瞞の深さを知ることとなったメルは、自分自身の手に余る可能性がある状況に直面する。新たに目覚めた魔法の力を携え、答えを求めて故郷へと出帆したメル。その内なる光を押さえ込もうとする者が後を絶たない中でも、彼女の魂は決して屈することはない。", "blurb": "メル・メダルダは、かつてはノクサスで最大の権勢を誇ったメダルダ家の後継者と目される人物。表向きは優雅な貴族のように見えるが、その実体は、出会う相手のすべてについて知り尽くそうとする、卓越した政治家。謎に満ちた黒薔薇団との邂逅を経て、母の欺瞞の深さを知ることとなったメルは、自分自身の手に余る可能性がある状況に直面する。新たに目覚めた魔法の力を携え、答えを求めて故郷へと出帆したメル。その内なる光を押さえ込もうとする者が後を絶たない中でも、彼女の魂は決して屈することはない。", "allytips": ["メルは敵の発射物を反射することが可能で、強力なスキルも反射できる。強力な発射物を使う際は、メルが「反駁」を使うのを待ってから打つようにしよう。", "メルから攻撃を受ければ受けるほど、「圧倒」のスタックが増加していく。体力が低下し過ぎると、次の攻撃で倒されてしまうので、数秒間その場を離れて、「圧倒」のスタックが消滅するのを待とう。"], "enemytips": ["メルは敵の発射物を反射することが可能で、強力なスキルも反射できる。強力な発射物を使う際は、メルが「反駁」を使うのを待ってから打つようにしよう。", "メルから攻撃を受ければ受けるほど、「圧倒」のスタックが増加していく。体力が低下し過ぎると、次の攻撃で倒されてしまうので、数秒間その場を離れて、「圧倒」のスタックが消滅するのを待とう。"], "tags": ["Mage", "Support"], "partype": "マナ", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 93, "mp": 480, "mpperlevel": 28, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.9, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "MelQ", "name": "輝きの連撃", "description": "指定地点に向かって複数の飛翔物を連続で発射する。飛翔物は爆発して範囲内の敵に繰り返しダメージを与える。", "tooltip": "指定地点に向かって{{ explosioncount }}発の飛翔物を連続で発射する。飛翔物は指定地点の周囲に爆発を引き起こす。<br /><br />爆発ごとに<magicDamage>{{ totalexplosiondamage }}の魔法ダメージ</magicDamage>、最大で合計<magicDamage>{{ alldamagehit }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["爆発ダメージ", "爆発数", "クールダウン", "マナコスト"], "effect": ["{{ explosiondamage }} -> {{ explosiondamageNL }}", "{{ explosioncount }} -> {{ explosioncountNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "MelQ.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MelW", "name": "反駁", "description": "自身の周囲にバリアを形成する。このバリアは敵の発射物をその敵に向けて反射し、自身が受けるダメージを防ぎ、自身の移動速度を増加させる。", "tooltip": "自身の周囲にバリアを形成する。このバリアは敵チャンピオンの発射物を反射し、自身が受けるダメージを防ぎ、自身の<speed>移動速度を{{ movespeed*100 }}%</speed>増加させる。この移動速度は{{ duration }}秒かけて元に戻る。<br /><br />反射された発射物は<magicDamage>元の{{ damagepercent }}のダメージを魔法ダメージ</magicDamage>として与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["反射ダメージ", "クールダウン", "マナコスト"], "effect": ["{{ basedamagepercent*100.000000 }}% -> {{ basedamagepercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [35, 32, 29, 26, 23], "cooldownBurn": "35/32/29/26/23", "cost": [80, 60, 40, 20, 0], "costBurn": "80/60/40/20/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "MelW.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MelE", "name": "陽光の枷", "description": "前方に輝くオーブを発射し、中心にいた敵にはスネア効果を与え、周囲にいた敵にはスロウ効果と継続ダメージを与える。", "tooltip": "輝くオーブを発射し、中心にいた敵には{{ rootduration }}秒間、<status>スネア効果</status>を与え、<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与える。<br /><br />オーブは周囲に敵対的範囲を作り出し、敵に{{ areaslowamount*100 }}%の<status>スロウ効果</status>を与えて、<magicDamage>毎秒{{ areadamagepersecond }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スネア効果時間", "毎秒ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ baseareadamage*8.000000 }} -> {{ baseareadamagenl*8.000000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "MelE.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MelR", "name": "黄金蝕", "description": "距離に関係なく、「圧倒」を付与しているすべての敵を攻撃し、「圧倒」のスタック数に応じて追加ダメージを与える。<br><br>「黄金蝕」のスキルレベルが上がると、「圧倒」のダメージが増加する。", "tooltip": "<spellPassive>自動効果</spellPassive>: <keywordMajor>「圧倒」</keywordMajor>のダメージが<magicDamage>{{ passiveflatdamage }} + スタックごとに{{ passivestackdamage }}の魔法ダメージ</magicDamage>に増加する。<br /><br /><spellActive>発動効果</spellActive>: <keywordMajor>「圧倒」</keywordMajor>を付与されたすべての敵に向けてパワーを解き放ち、<magicDamage>{{ ultflatdamage }} + <keywordMajor>「圧倒」</keywordMajor>のスタックごとに{{ ultstackdamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><rules>敵チャンピオンに<keywordMajor>「圧倒」</keywordMajor>が付与されていない時は使用できない。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<spellName>「黄金蝕」</spellName>の固定ダメージ", "<spellName>「黄金蝕」</spellName>のスタックダメージ", "クールダウン", "<keywordMajor>「圧倒」</keywordMajor>の固定ダメージ", "<keywordMajor>「圧倒」</keywordMajor>のスタックダメージ"], "effect": ["{{ baseultflatdamage }} -> {{ baseultflatdamageNL }}", "{{ baseultstackdamage }} -> {{ baseultstackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basepassiveflatdamage }} -> {{ basepassiveflatdamageNL }}", "{{ basepassivestackdamage }} -> {{ basepassivestackdamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MelR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "灼熱の輝き", "description": "スキルを使用するたび、次の通常攻撃で追加の飛翔物を3発発射する(最大9発までスタック可能)。<br><br>スキルまたは通常攻撃でダメージを与えると、敵に「圧倒」のスタックを付与する。これは無限にスタックする。敵に蓄積した「圧倒」スタックが一定のダメージ量に達すると、スタックを消費して敵にとどめを刺す。", "image": {"full": "Mel_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}