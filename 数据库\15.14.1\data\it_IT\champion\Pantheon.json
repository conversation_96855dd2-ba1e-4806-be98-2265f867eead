{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pantheon": {"id": "Pantheon", "key": "80", "name": "Pantheon", "title": "la lancia indistruttibile", "image": {"full": "Pantheon.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "80000", "num": 0, "name": "default", "chromas": false}, {"id": "80001", "num": 1, "name": "Pantheon Mirmidone", "chromas": false}, {"id": "80002", "num": 2, "name": "Pantheon Spietato", "chromas": false}, {"id": "80003", "num": 3, "name": "Pantheon Perseo", "chromas": false}, {"id": "80004", "num": 4, "name": "Full Metal Pantheon", "chromas": false}, {"id": "80005", "num": 5, "name": "Pantheon Guerriero Legionario", "chromas": false}, {"id": "80006", "num": 6, "name": "Pantheon Ammazzadraghi", "chromas": true}, {"id": "80007", "num": 7, "name": "Pantheon Sopravvissuto <PERSON>", "chromas": false}, {"id": "80008", "num": 8, "name": "Pantheon Fornaio", "chromas": false}, {"id": "80016", "num": 16, "name": "Pantheon Pulsefire", "chromas": true}, {"id": "80025", "num": 25, "name": "Pantheon in Rovina", "chromas": true}, {"id": "80026", "num": 26, "name": "Pantheon Asceso (edizione prestigio)", "chromas": false}, {"id": "80036", "num": 36, "name": "Pantheon Conquistatore cinereo", "chromas": true}, {"id": "80038", "num": 38, "name": "Pantheon Prescelto del Lupo", "chromas": true}], "lore": "Dopo aver involontariamente ospitato l'Incarnazione della Guerra, Atreus è sopravvissuto al giorno in cui il potere celestiale al suo interno è stato ucciso, rifiutandosi di soccombere a un colpo che ha scalzato le stelle dal cielo. Col tempo ha imparato ad accettare la forza della sua mortalità e la testarda resistenza che la accompagna. Atreus ora si oppone al divino nei panni di un Pantheon rinato, con una volontà implacabile che alimenta le armi dell'Incarnazione in battaglia.", "blurb": "Dopo aver involontariamente ospitato l'Incarnazione della Guerra, Atreus è sopravvissuto al giorno in cui il potere celestiale al suo interno è stato ucciso, rifiutandosi di soccombere a un colpo che ha scalzato le stelle dal cielo. Col tempo ha...", "allytips": ["Volontà dei mortali si attiva dopo 5 abilità o attacchi base: pianifica di conseguenza gli scontri per attivarla più di una volta.", "Indebolisci i nemici con Lancia astrale prima di lanciarti.", "Se un nemico sta per uscire dalla portata di Egida devastante, puoi rilanciare subito l'abilità e dare il colpo di scudo."], "enemytips": ["Egida devastante di Pantheon lo rende invulnerabile da davanti. Attaccalo alle spalle o aspetta che finisca.", "Se sei vicino a Pantheon con poca salute, fai attenzione: la Lancia astrale giustizia i nemici con poca salute.", "Gran cometa lascia molti secondi di avviso prima dell'arrivo di Pantheon. Usa questo periodo di tempo per spostarti o per pensare a una strategia."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 109, "mp": 317, "mpperlevel": 31, "movespeed": 345, "armor": 40, "armorperlevel": 4.95, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 6, "hpregenperlevel": 0.65, "mpregen": 7.35, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.95, "attackspeed": 0.658}, "spells": [{"id": "PantheonQ", "name": "Lancia astrale", "description": "Pantheon colpisce con la sua lancia o la scaglia nella direzione scelta.", "tooltip": "<span class=\"colorFF8C00\">Tocca:</span> Pantheon colpisce con la lancia, infliggendo <physicalDamage>{{ tapdamagecalc }} danni fisici</physicalDamage> ai nemici colpiti. Rimborsa un {{ tapcooldownrefund*100 }}% della ricarica di questa abilità.<br /><br /><span class=\"colorFF8C00\">Tieni premuto:</span> Pantheon scaglia la lancia, infliggendo <physicalDamage>{{ holddamagecalc }} danni fisici</physicalDamage> al primo nemico colpito e un {{ damagefalloff*100 }}% in meno ai bersagli successivi. <br /><br />Questa abilità è potenziata se colpisce nemici con meno di un {{ crithealththreshold*100 }}% della loro salute per <physicalDamage>{{ executedamagecalcmodified }} danni fisici</physicalDamage>.<br /><br /><span class=\"colorEDDA74\">Bonus Volontà dei mortali:</span> infligge <physicalDamage>{{ empowereddamagecalc }} danni fisici</physicalDamage> aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base", "Danni base Esecuzione", "Ricarica"], "effect": ["{{ tapdamage }} -> {{ tapdamageNL }}", "{{ executebasedamage }} -> {{ executebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.25, 9.5, 8.75, 8], "cooldownBurn": "11/10.25/9.5/8.75/8", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "PantheonQ.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PantheonW", "name": "<PERSON><PERSON><PERSON> dello scudo", "description": "Pantheon scatta verso un bersaglio, dannegg<PERSON><PERSON><PERSON> e stordendolo.", "tooltip": "Pantheon balza su un bersaglio, <status>stordendolo</status> per {{ stunduration }} secondo e infliggendo <physicalDamage>{{ maxhealthdamagecalc }} danni fisici in base alla salute massima</physicalDamage>.<br /><br /><keywordMajor>Bonus Volontà dei mortali:</keywordMajor> il prossimo attacco di Pantheon colpisce {{ empowerednumhits }} volte, infliggendo un totale di <physicalDamage>{{ empowereddamagemultcalcmodified }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute massima", "Ricarica"], "effect": ["{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PantheonW.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PantheonE", "name": "<PERSON><PERSON><PERSON> devastante", "description": "Pantheon prepara il suo scudo e diventa invulnerabile ai danni da da<PERSON>i, colpendo rapidamente con la sua lancia.", "tooltip": "Pantheon prepara il suo scudo e attacca i nemici nella direzione scelta per {{ shieldduration }} secondi, diventando immune ai danni non inflitti dalle torri da quella direzione e infliggendo <physicalDamage>{{ damagecalc }} danni fisici</physicalDamage> per tutta la durata. Dopo la canalizzazione Pantheon colpisce con lo scudo, infliggendo <physicalDamage>{{ shielddamagecalc }} danni fisici</physicalDamage>.<br /><br /><span class=\"colorEDDA74\">Bonus Volontà dei mortali:</span> quando Pantheon colpisce con lo scudo, ottiene <scaleArmor>{{ resistscalc }} armatura</scaleArmor> e <scaleMR>{{ resistscalc }} resistenza magica</scaleMR> per {{ resistsduration }} secondi e un <speed>{{ speedamount*100 }}% di velocità di movimento</speed> per {{ speedduration }} secondi. <br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> scudo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ shieldbasedamage }} -> {{ shieldbasedamageNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PantheonE.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PantheonR", "name": "Gran cometa", "description": "Pantheon si prepara e balza in cielo, atterrando come cometa nel luogo scelto.", "tooltip": "<spellPassive>Passiva:</spellPassive> Pantheon ottiene {{ armorpenetration*100 }}% penetrazione armatura.<br /><br /><spellActive>Attiva:</spellActive> Pantheon raccoglie le proprie forze e balza in cielo, Poi scaglia la sua lancia a terra infliggendo <physicalDamage>{{ spell.pantheonq:holddamagecalc }} danni fisici</physicalDamage> e <status>rallentando</status> di un {{ spearslow*100 }}% per {{ spearslowduration }} secondi in una piccola area. <br /><br />Alla fine, Pantheon si schianta nell'area bersaglio come un meteorite. Infligge fino a <magicDamage>{{ damagecalc }} danni magici</magicDamage> ai nemici lungo una linea (diminuiscono di un {{ edgedamagereduction*100 }}% ai bordi dell'area).<br /><br />Questa abilità prepara all'istante <span class=\"colorEDDA74\">Volontà dei mortali</span>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Penetrazione armatura"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ armorpenetration*100.000000 }}% -> {{ armorpenetrationnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [180, 165, 150], "cooldownBurn": "180/165/150", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "PantheonR.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Volontà dei mortali", "description": "Dopo alcune abilità o attacchi, la prossima abilità di Pantheon è potenziata.", "image": {"full": "Pantheon_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}