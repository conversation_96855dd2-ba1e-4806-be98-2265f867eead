{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zeri": {"id": "<PERSON><PERSON>", "key": "221", "name": "<PERSON><PERSON>", "title": "Iskierka Zaun", "image": {"full": "Zeri.png", "sprite": "champion5.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "221000", "num": 0, "name": "default", "chromas": false}, {"id": "221001", "num": 1, "name": "Obumarła Róża Zeri", "chromas": true}, {"id": "221010", "num": 10, "name": "Zeri Oceanicznych Pieśni", "chromas": true}, {"id": "221019", "num": 19, "name": "Zeri Nieśmiertelnej Podróży", "chromas": true}, {"id": "221028", "num": 28, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "221029", "num": 29, "name": "<PERSON><PERSON> (Prestiżowa)", "chromas": false}], "lore": "Zeri — <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, charakterna młoda kobieta wywodząca się z klasy robotniczej Zaun — korzysta ze swojej elektrycznej magii, aby ł<PERSON><PERSON>ć samą siebie i swoją niestandardową, st<PERSON><PERSON><PERSON><PERSON> specjalnie dla niej broń. Jej niestabilna moc odzwierciedla jej emocje, a otaczające ją iskry obrazują błyskawicznie szybkie podejście do życia. Zeri jest pełna współczucia względem innych, a miłość do rodziny i domu towarzyszy jej w każdej walce. Chociaż jej szczere chęci pomocy przynoszą czasami odwrotny skutek, Zeri wierzy w jedno: stań murem za swoją społecznością, a społeczność stanie murem za tobą.", "blurb": "Zeri <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, charakterna młoda kobieta wywodząca się z klasy robotniczej Zaun — korzysta ze swojej elektrycznej magii, aby ł<PERSON><PERSON>ć samą siebie i swoją niestandardową, st<PERSON><PERSON><PERSON>ą specjalnie dla niej broń. Jej niestabilna moc odzwierciedla jej...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 250, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "ZeriQ", "name": "Szybka Seria", "description": "Szybka Seria generuje 7 cykli, które zadają obrażenia od ataku pierwszemu trafionemu wrogowi. Ta umiejętność jest traktowana jak atak.", "tooltip": "<PERSON><PERSON> wystrzeliwuje serię {{ numberofmissiles }} pocisków, kt<PERSON>re zadają <physicalDamage>{{ activedamagethatcancrit }} pkt. obrażeń fizycznych</physicalDamage> pierwszemu trafionemu wrogowi. Ta umiej<PERSON>t<PERSON>ść jest traktowana jak atak. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Całkowity współczynnik obrażeń od ataku"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ activeadratio*100.000000 }}% -> {{ activeadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Darmowa", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "ZeriQ.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "Darmowa"}, {"id": "ZeriW", "name": "Laserowy Wstrząs", "description": "Zeri wystrzeliwuje impuls <PERSON><PERSON><PERSON>, kt<PERSON>ry spowalnia pierwszego trafionego wroga i zadaje mu obrażenia. Jeśli impuls trafi w ś<PERSON>, zmienia się w wiązk<PERSON> laserową dalekiego zasięgu.", "tooltip": "<PERSON><PERSON> wystrzeliwuje impuls el<PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> zadaje <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage> i <status>spowalnia </status> pierwszego trafionego wroga o {{ slowpercent*100 }}% na {{ slowduration }} sek.<br /><br />Je<PERSON>li impuls trafi w teren, zmienia się w wiązkę laserową, która wywołuje te same efekty na określonym obszarze oraz trafia krytycznie bohaterów i potwory.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Spowolnienie", "Koszt (@AbilityResourceName@)", "Czas odnowienia"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "ZeriW.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZeriE", "name": "Iskrząca Moc", "description": "Zeri doskakuje na krótki dystans i ładuje 3 kolejne Szybkie Serie, dzięki czemu przebijają one wrogów. Bohaterka przeskoczy i prześlizgnie się nad każdym napotkanym elementem terenu.", "tooltip": "Zeri doskakuje na krótki dystans, przeskakując nad każdym napotkanym elementem terenu, co znacznie zwiększa zasięg jej doskoku. Przez kolejne {{ buffduration }} sek. pociski umiejętności <spellName>Szybka Seria</spellName> przeb<PERSON><PERSON><PERSON> wrogów, zadając {{ pendamagepercent*100 }}% obrażeń po pierwszym trafieniu i dodatkowe <magicDamage>{{ bonusdamagetotal }} pkt. obrażeń magicznych</magicDamage> przy trafieniu pierwszemu trafionemu celowi. <br /><br />Trafienie wrogiego bohatera atakiem lub umiejętnością skraca czas odnowienia tej umiejętności o {{ cdreductionperhit }} sek. Trafienie krytyczne skróci czas odnowienia o {{ critcdreductionperhit }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia procentowe", "Obrażenia podstawowe", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ pendamagepercent*100.000000 }}% -> {{ pendamagepercentnl*100.000000 }}%", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [90, 85, 80, 75, 70], "costBurn": "90/85/80/75/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "ZeriE.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ZeriR", "name": "Piorunuj<PERSON><PERSON> Strzał", "description": "Zeri uwalnia kumulację elektryczności i się przeładowuje, zyskując premię do obrażeń i kumulującą się prędkość ruchu, która się regeneruje i wzmacnia przy każdym trafieniu wrogiego bohatera. Podczas przeładowania Szybka Seria zmienia się w szybszy potrójny wystrzał, który tworzy łańcuch wyładowań pomiędzy wrogami.", "tooltip": "Zeri uwalnia kumulację elek<PERSON>, zadaj<PERSON>c <magicDamage>{{ totalactivedamage }} pkt. obrażeń magicznych</magicDamage> pobliskim wrogom. Jeśli trafi wrogiego bohatera, <PERSON><PERSON> z<PERSON> <attackSpeed>{{ baseaspercent*100 }}% prędkości ataku</attackSpeed> i <speed>{{ basebonusms*100 }}% prędkości ruchu</speed> na {{ rduration }} sek. Trafienie wrogiego bohatera atakiem lub umiejętnością wydłuża czas trwania tej umiejętności oraz dodaje ładunek przeładowania na {{ maxhyperchargeduration }} sek. Trafienia krytyczne dodają 2 dodatkowe ładunki. Ka<PERSON><PERSON> ładunek zapewnia <speed>{{ mspercent*100 }}% prędkości ruchu</speed>.<br /><br />W czasie trwania tego efektu <spellName>Szyb<PERSON></spellName> staje się szy<PERSON>zym potrójnym strza<PERSON>, który łańcuchowo zadaje <physicalDamage>{{ chainphysicaldamage }} pkt. obrażeń fizycznych</physicalDamage> pobliskim wrogom.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ activedamage }} -> {{ activedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 75, 70], "cooldownBurn": "80/75/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ZeriR.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Żywa Bateria", "description": "Ataki Zeri zadają obrażenia magiczne i są traktowane jak umiejętności. Poruszanie się i rzucanie Szybkiej Serii magazynuje energię z Iskropaku Zeri. Przy pełnym naładowaniu jej kolejny atak zadaje obrażenia dodatkowe.", "image": {"full": "ZeriP.png", "sprite": "passive5.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}