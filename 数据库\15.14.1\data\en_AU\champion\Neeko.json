{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Neeko": {"id": "<PERSON><PERSON><PERSON>", "key": "518", "name": "<PERSON><PERSON><PERSON>", "title": "the Curious Chameleon", "image": {"full": "Neeko.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "518000", "num": 0, "name": "default", "chromas": false}, {"id": "518001", "num": 1, "name": "Winter Wonder Neeko", "chromas": true}, {"id": "518010", "num": 10, "name": "Star Guardian Neeko", "chromas": false}, {"id": "518011", "num": 11, "name": "Prestige Star Guardian Neeko", "chromas": false}, {"id": "518012", "num": 12, "name": "Shan <PERSON> Scrolls Neeko", "chromas": true}, {"id": "518021", "num": 21, "name": "Prestige Star Guardian Neeko (2022)", "chromas": false}, {"id": "518022", "num": 22, "name": "Bewitching <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "518031", "num": 31, "name": "Street Demons Neeko", "chromas": true}, {"id": "518040", "num": 40, "name": "Cosplayer <PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Hailing from a long lost tribe of vastaya, <PERSON><PERSON><PERSON> can blend into any crowd by borrowing the appearances of others, even absorbing something of their emotional state to tell friend from foe in an instant. No one is ever sure where—or who—<PERSON><PERSON><PERSON> might be, but those who intend to do her harm will soon witness her true colors revealed, and feel the full power of her primordial spirit magic unleashed upon them.", "blurb": "Hailing from a long lost tribe of vastaya, <PERSON><PERSON><PERSON> can blend into any crowd by borrowing the appearances of others, even absorbing something of their emotional state to tell friend from foe in an instant. No one is ever sure where—or who—<PERSON><PERSON><PERSON> might be...", "allytips": ["You can set her passive to use hotkeys in the options menu. The default is Shift+F1~F5", "Try to use Inherent Glamour sparingly, ineffective uses will put enemies on the alert."], "enemytips": ["Standing behind minions against <PERSON><PERSON><PERSON> is very dangerous as Tangle-Barbs become stronger.", "<PERSON> Blossom's warning visuals are invisible if <PERSON><PERSON><PERSON> is disguised."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 1, "magic": 9, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 104, "mp": 450, "mpperlevel": 30, "movespeed": 340, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "NeekoQ", "name": "Blooming Burst", "description": "<PERSON><PERSON><PERSON> throws a seed dealing magic damage. The seed blooms again on hitting champions or killing units.", "tooltip": "<PERSON><PERSON><PERSON> throws a seed that blooms to deal <magicDamage>{{ explosiondamage }} magic damage</magicDamage>. If it kills a unit or hits a champion or large monster, it will bloom again, dealing <magicDamage>{{ seconddamage }} magic damage</magicDamage>. Max 2 extra blooms.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Initial Damage", "Explosion Damage", "@AbilityResourceName@ Cost", "Bonus Monster Damage", "Cooldown"], "effect": ["{{ zonedamage }} -> {{ zonedamageNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ monsterbonus }} -> {{ monsterbonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NeekoQ.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NeekoW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ter", "description": "<PERSON><PERSON><PERSON> passively deals bonus magic damage every third attack, briefly increasing her move speed. <PERSON><PERSON><PERSON> can activate to send a clone in a direction and recast to change the direction of the clone.", "tooltip": "<passive>Passive:</passive> Every 3rd Attack deals an additional <magicDamage>{{ passivebonusdamagecalc }} bonus magic damage</magicDamage> and increases <PERSON><PERSON><PERSON>'s <speed>Move Speed by {{ passivehaste }}%</speed> for {{ passivehasteduration }} second.<br /><br /><active>Active:</active> <PERSON><PERSON><PERSON> becomes <keywordStealth>invisible</keywordStealth> for {{ stealthduration }} seconds and projects a clone that lasts {{ cloneduration }} seconds. <PERSON><PERSON><PERSON> and the clone gain <speed>{{ haste }}% Move Speed</speed> for {{ hasteduration }} seconds. <br /><br /><rules>The clone can be controlled using the Move Pet Click hotkey or by <recast>Recasting</recast> this Ability.<br />The clone mirrors her spellcasts, emotes, and recall.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Passive Damage", "Active Move Speed", "Passive Move Speed", "Cooldown"], "effect": ["{{ passivedamage }} -> {{ passivedamageNL }}", "{{ haste }}% -> {{ hasteNL }}%", "{{ passivehaste }}% -> {{ passivehasteNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "No Cost", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NeekoW.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "No Cost"}, {"id": "NeekoE", "name": "Tangle-Barbs", "description": "<PERSON><PERSON><PERSON> slings a tangle that damages and roots everything it passes through. If the tangle kills an enemy or passes through a champion, it becomes larger, faster, and roots for longer.", "tooltip": "<PERSON><PERSON><PERSON> slings a tangle that deals <magicDamage>{{ basedamage }} magic damage</magicDamage> and <status>Roots</status> for {{ minrootduration }} seconds.<br /><br />The tangle becomes empowered after hitting an enemy, growing larger, moving faster, and <status>Rooting</status> for {{ maxrootduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Minimum Root Duration", "Empowered Root Duration", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ minrootduration }} -> {{ minrootdurationNL }}", "{{ maxrootduration }} -> {{ maxrootdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "NeekoE.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NeekoR", "name": "Pop Blossom", "description": "After a short preparation, <PERSON><PERSON><PERSON> leaps into the air, knocking up all nearby enemies. Upon landing, nearby enemies are damaged and stunned. The preparation is hidden if <PERSON><PERSON><PERSON> is disguised.", "tooltip": "After a delay <PERSON><PERSON><PERSON> leaps into the air, <status>Knocking Up</status> all nearby enemies for {{ delayuntilexplosion }} seconds. She then crashes down, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> to all nearby enemies and <status>Stunning</status> them for {{ stunduration }} seconds.<br /><br /><rules>This Ability can be prepared in secret if <PERSON><PERSON><PERSON> is disguised. {{ delaybeforepassiveremoval }} seconds after casting this ability <PERSON><PERSON><PERSON> breaks her disguise.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "NeekoR.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Inherent Glamour", "description": "<PERSON><PERSON><PERSON> can look like an ally champion or as other units on the map. Receiving immobilizing crowd control, casting damaging spells, damaging enemy towers as a non-champion, or your disguise taking damage equal to its healthbar breaks the disguise.", "image": {"full": "Neeko_P.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}