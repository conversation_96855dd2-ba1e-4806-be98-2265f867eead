{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sivir": {"id": "<PERSON><PERSON>", "key": "15", "name": "シヴィア", "title": "戦場の女王", "image": {"full": "Sivir.png", "sprite": "champion4.png", "group": "champion", "x": 192, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "15000", "num": 0, "name": "default", "chromas": false}, {"id": "15001", "num": 1, "name": "戦姫シヴィア", "chromas": false}, {"id": "15002", "num": 2, "name": "超一流女優シヴィア", "chromas": false}, {"id": "15003", "num": 3, "name": "狩人シヴィア", "chromas": false}, {"id": "15004", "num": 4, "name": "盗賊の頭シヴィア", "chromas": false}, {"id": "15005", "num": 5, "name": "PAX シヴィア", "chromas": false}, {"id": "15006", "num": 6, "name": "吹雪のシヴィア", "chromas": true}, {"id": "15007", "num": 7, "name": "番人シヴィア", "chromas": false}, {"id": "15008", "num": 8, "name": "勝利の栄光シヴィア", "chromas": false}, {"id": "15009", "num": 9, "name": "ネオPAX シヴィア", "chromas": false}, {"id": "15010", "num": 10, "name": "宅配ピザ シヴィア", "chromas": true}, {"id": "15016", "num": 16, "name": "ブラッドムーン シヴィア", "chromas": true}, {"id": "15025", "num": 25, "name": "オデッセイ シヴィア", "chromas": true}, {"id": "15034", "num": 34, "name": "カフェキューティーズ シヴィア", "chromas": true}, {"id": "15043", "num": 43, "name": "ソーラーエクリプス シヴィア", "chromas": true}, {"id": "15050", "num": 50, "name": "神話創生シヴィア", "chromas": true}, {"id": "15051", "num": 51, "name": "プレステージ神話創生シヴィア", "chromas": false}, {"id": "15061", "num": 61, "name": "原始の襲撃シヴィア", "chromas": true}, {"id": "15070", "num": 70, "name": "アニシヴィアーサリー", "chromas": false}], "lore": "シヴィアは、金のためならどのような仕事でも引き受ける。傭兵の一団を率いる彼女は血も涙もない名将としてその名をあまねく轟かせ、砂漠では彼女に仕事を依頼する者たちが後を絶たない。シヴィアは宝石を鏤めたクロスブレードを自在に操り、金に糸目をつけない雇い主たちのために無数の戦いを制してきた。何事をも恐れぬ覚悟と底無しの野望を抱き、シヴィアはたとえ如何なる危険が待ち受けていようとも、砂に埋もれたシュリーマの墓から揚々と財宝を掘り起こす。そこには莫大な見返りが眠っているのだ。しかしシュリーマの骨の髄を揺るがす太古の力を持ち得たシヴィアは、相反する二つの運命の狭間で苦悶する。", "blurb": "シヴィアは、金のためならどのような仕事でも引き受ける。傭兵の一団を率いる彼女は血も涙もない名将としてその名をあまねく轟かせ、砂漠では彼女に仕事を依頼する者たちが後を絶たない。シヴィアは宝石を鏤めたクロスブレードを自在に操り、金に糸目をつけない雇い主たちのために無数の戦いを制してきた。何事をも恐れぬ覚悟と底無しの野望を抱き、シヴィアはたとえ如何なる危険が待ち受けていようとも、砂に埋もれたシュリーマの墓から揚々と財宝を掘り起こす。そこには莫大な見返りが眠っているのだ。しかしシュリーマの骨の髄を揺るがす太古...", "allytips": ["「ブーメランブレード」は、攻撃距離の限界まで飛ぶと手元に戻ってくる。つまり投げてから移動することで、戻ってくるブレードを別の敵に誘導して当てることも可能だ。", "「跳刃」を発動すると、通常攻撃のタイマーがリセットされる。通常攻撃を命中させた直後に発動すれば、最大限のダメージを与えることができる。", "「スペルシールド」は、スタン効果や足止め効果のあるスキル攻撃を受けそうになった時に備えて温存しておこう。"], "enemytips": ["「ブーメランブレード」は大量のマナを消費するため、1度回避すればシヴィアはしばらく攻撃的に出るのが難しくなる。ブーメランの往路で当たってしまった場合は、復路でも当たらないよう軌道から逃れること。", "シヴィアは、レーンを積極的にプッシュする事を得意とするチャンピオンだ。レーンに長時間放置すると、タワーを破壊されるリスクが高まる。", "シヴィアとレーンで対峙する際は、攻めると見せかけて「スペルシールド」を使わせ、素早く後退してリズムを崩す戦略も有効。"], "tags": ["Marksman"], "partype": "マナ", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 104, "mp": 340, "mpperlevel": 45, "movespeed": 335, "armor": 30, "armorperlevel": 4.45, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "SivirQ", "name": "ブーメランブレード", "description": "クロスブレードをブーメランのように投げ、往復のそれぞれで命中した敵ユニットすべてにダメージを与える。", "tooltip": "クロスブレードをブーメランのように投げ、貫通した敵すべてに<physicalDamage>{{ totaldamage }}</physicalDamage>を与える。チャンピオン以外の対象に命中するたびにダメージが低下し、最小で{{ falloffminimum*100 }}%になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "SivirQ.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SivirW", "name": "跳刃", "description": "次の数回の通常攻撃は攻撃速度が増加し、対象の周囲に跳ね返るようになる。跳ね返った攻撃は与えるダメージが低下する。", "tooltip": "{{ buffduration }}秒間、<attackSpeed>攻撃速度が{{ ricochetattackspeed*100 }}%</attackSpeed>増加し、通常攻撃が強化されて周囲の他の敵に跳ね返るようになる。この通常攻撃は最大{{ maxbounces }}回まで跳ね返り、跳ね返るたびに<physicalDamage>{{ bouncedamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />通常攻撃がクリティカルになった場合は、跳ね返った攻撃もクリティカルになる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃速度", "合計攻撃力反映率"], "effect": ["{{ ricochetattackspeed*100.000000 }}% -> {{ ricochetattackspeednl*100.000000 }}%", "{{ bounceadratio*100.000000 }}% -> {{ bounceadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirW.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SivirE", "name": "スペルシールド", "description": "魔法のバリアを張り、一度だけ敵のスキル攻撃やその付随効果をブロックする。スキルをブロックすると体力が回復して、少しの間だけ移動速度が増加する。", "tooltip": "自身に{{ e1 }}秒間魔法のバリアを展開し、次に敵から受けるスキルを無効化する。スキルを無効化すると<healing>体力が{{ totalheal }}</healing>回復して、「戦駆け」が発動する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["合計攻撃力反映率", "クールダウン"], "effect": ["{{ healratio*100.000000 }}% -> {{ healrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [1.5, 1.5, 1.5, 1.5, 1.5], [55, 55, 55, 55, 55], [60, 60, 60, 60, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.5", "55", "60", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "SivirE.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SivirR", "name": "戦姫の号令", "description": "発動するとシヴィアの号令により、自身と味方の移動速度を一定時間増加させる。また、自身は通常攻撃するたびに、スキルのクールダウンを短縮できる。", "tooltip": "周囲の味方の士気を高め、{{ ultduration }}秒間、自身とその味方の<speed>移動速度を{{ maxms*100 }}%</speed>増加させる。<br /><br />「戦姫の号令」を発動中、シヴィアはチャンピオンに対して通常攻撃するたびに、基本スキルのクールダウンを0.5秒短縮できる。<br /><br />直前にダメージを与えた敵からキルまたはアシストを獲得すると、このスキルの効果時間が更新される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "最高移動速度", "効果時間"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxms*100.000000 }}% -> {{ maxmsnl*100.000000 }}%", "{{ ultduration }} -> {{ ultdurationNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SivirR.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "戦駆け", "description": "敵チャンピオンを攻撃すると移動速度が短時間増加する。", "image": {"full": "Sivir_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 192, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}