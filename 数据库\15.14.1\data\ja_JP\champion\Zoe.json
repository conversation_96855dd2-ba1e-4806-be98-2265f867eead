{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zoe": {"id": "<PERSON>", "key": "142", "name": "ゾーイ", "title": "超常の遊び", "image": {"full": "Zoe.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "142000", "num": 0, "name": "default", "chromas": false}, {"id": "142001", "num": 1, "name": "サイバーポップ ゾーイ", "chromas": false}, {"id": "142002", "num": 2, "name": "プールパーティ ゾーイ", "chromas": true}, {"id": "142009", "num": 9, "name": "スターガーディアン ゾーイ", "chromas": true}, {"id": "142018", "num": 18, "name": "魔紋使いゾーイ", "chromas": false}, {"id": "142019", "num": 19, "name": "プレステージ魔紋使いゾーイ", "chromas": false}, {"id": "142020", "num": 20, "name": "EDG ゾーイ", "chromas": true}, {"id": "142022", "num": 22, "name": "冬の祝福ゾーイ", "chromas": true}, {"id": "142033", "num": 33, "name": "ダークスター ゾーイ", "chromas": true}], "lore": "いたずら、想像、変化を具現化する存在であるゾーイは、時空を駆け抜けて霊峰ターゴンのメッセージ──世界の再編を招く出来事の到来──を告げる。彼女の存在は、現実を支配する聖なる数学に歪みを生み出し、時に激変を引き起こす。意識的にではないし、悪意もない。だからこそゾーイはたっぷりと時間をかけて遊びに集中し、定命の者をからかい、あるいはただ楽しいからという理由で、平然とその義務を果たせるのだろう。ゾーイに出会うと気分が高揚して前向きな気持ちになるが、実際にはそれは常に危険と隣り合わせだ。", "blurb": "いたずら、想像、変化を具現化する存在であるゾーイは、時空を駆け抜けて霊峰ターゴンのメッセージ──世界の再編を招く出来事の到来──を告げる。彼女の存在は、現実を支配する聖なる数学に歪みを生み出し、時に激変を引き起こす。意識的にではないし、悪意もない。だからこそゾーイはたっぷりと時間をかけて遊びに集中し、定命の者をからかい、あるいはただ楽しいからという理由で、平然とその義務を果たせるのだろう。ゾーイに出会うと気分が高揚して前向きな気持ちになるが、実際にはそれは常に危険と隣り合わせだ。", "allytips": ["「パドルスター」は飛距離が長いほどダメージが増加するので、逆方向に飛ばしてから方向転換すれば大量のダメージを与えられる。", "眠っている敵は受けるダメージが倍になるので、眠りから覚ます時は一番ダメージの大きな攻撃を当てよう。", "「スリープバブル」は壁越しなら飛距離が伸びる。隠れる場所を見つけて遠距離からキルを狙おう。"], "enemytips": ["ゾーイの「パドルスター」は飛距離が長いほどダメージが増加する。", "「ポータルジャンプ」使用後は必ず元の位置に戻るので、その瞬間が反撃のチャンスだ。", "「スリープバブル」は壁越しに使用すると飛距離が伸びる。ゾーイを戦場の霧から追い出して、狙いどおりにスキルを使わせないようにしよう。"], "tags": ["Mage"], "partype": "マナ", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 106, "mp": 425, "mpperlevel": 25, "movespeed": 340, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "Zoe<PERSON>", "name": "パドルスター", "description": "途中で進行方向を変えられる星を飛ばす。真っすぐ飛んだ距離の長さに応じてダメージが増加する。", "tooltip": "星を発射する。星は移動距離に応じてダメージが増加し、最初に当たった敵と周囲の敵に<magicDamage>{{ totaldamagetooltip }} - {{ maxdamagetooltip }}の魔法ダメージ</magicDamage>を与える。<br /><br /><recast>再発動</recast>するとゾーイの近くの新たな地点に向けて星が飛ぶ方向を変えられる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8.5, 8, 7.5, 7, 6.5], "cooldownBurn": "8.5/8/7.5/7/6.5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeQ.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ZoeW", "name": "スペルシーフ", "description": "敵のサモナースペルと発動効果アイテムのかけらを拾って1回使用できる。サモナースペルを使用するごとに最大3つの魔法の弾を最も近くにいる対象に向かって飛ばす。", "tooltip": "<spellPassive>自動効果:</spellPassive> 敵がサモナースペルか発動効果アイテムを使用するとスペルのかけらが落ちる。特定のミニオンもゾーイまたは近くにいる味方がキルするとスペルのかけらを落とす。このかけらを拾うと、その効果を1回発動できるようになる。<br /><br /><spellPassive>自動効果:</spellPassive> このスキルかサモナースペルを使用すると{{ e0 }}秒間、<speed>移動速度が{{ e9 }}%</speed>増加し、直前に通常攻撃を行った対象に向けて3つの魔法の弾を飛ばす。この魔法の弾はそれぞれ<magicDamage>{{ missiledamagetooltip }}の魔法ダメージ</magicDamage>を与える。<br /><br /><spellActive>発動効果:</spellActive> 拾ったスペルのかけらの効果を発動する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["合計ダメージ", "移動速度", "移動速度増加時間"], "effect": ["{{ totalbasedamage*3.000000 }} -> {{ totalbasedamagenl*3.000000 }}", "{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ movespeedduration }} -> {{ movespeeddurationNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3000, 4500, 6000, 0, 0], [0.1, 0.1, 0.1, 0.1, 0.1], [2500, 2500, 2500, 2500, 2500], [60, 60, 60, 60, 60], [20, 50, 80, 110, 140], [0.2, 0.2, 0.2, 0.2, 0.2], [0, 0, 0, 0, 0], [30, 40, 50, 60, 70], [2, 2.25, 2.5, 2.75, 3]], "effectBurn": [null, "0", "3000/4500/6000/0/0", "0.1", "2500", "60", "20/50/80/110/140", "0.2", "0", "30/40/50/60/70", "2/2.25/2.5/2.75/3"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [3000, 4500, 6000, 3000, 3000], "rangeBurn": "3000/4500/6000/3000/3000", "image": {"full": "ZoeW.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "ZoeE", "name": "スリープバブル", "description": "対象に眠気を与えてから眠らせる。眠っている間は、対象の魔法防御が低下する。眠りを覚ます攻撃は2倍のダメージを与える(上限あり)。", "tooltip": "バブルを飛び蹴りして<magicDamage>{{ totaldamagetooltip }}の魔法ダメージ</magicDamage>を与える。バブルは何にも当たらなかった場合はトラップになってその場に残る。地形を越えて飛ばすとバブルの飛距離が伸びる。<br /><br />バブルに触れた敵は少し経つと2秒間の<status>眠り</status>に落ち、<scaleMR>魔法防御</scaleMR>が{{ percentpen*100 }}%低下する。通常攻撃かスキルを行うと目を覚ますが、2倍のダメージを与え、最大<trueDamage>{{ breakdamagetooltip }}の確定ダメージ</trueDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "追加ダメージ上限", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [0.1, 0.15, 0.2, 0.25, 0.3], [5, 15, 25, 35, 45], [5, 5, 5, 5, 5], [0.1, 0.15, 0.2, 0.25, 0.3], [70, 110, 150, 190, 230], [0.45, 0.45, 0.45, 0.45, 0.45], [1.4, 1.4, 1.4, 1.4, 1.4], [2.25, 2.25, 2.25, 2.25, 2.25], [1, 1, 1, 1, 1]], "effectBurn": [null, "70/110/150/190/230", "0.1/0.15/0.2/0.25/0.3", "5/15/25/35/45", "5", "0.1/0.15/0.2/0.25/0.3", "70/110/150/190/230", "0.45", "1.4", "2.25", "1"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeE.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ZoeR", "name": "ポータルジャンプ", "description": "近くの指定した位置に1秒間ブリンクして、もとの位置に戻る。", "tooltip": "近くの指定した位置に1秒間ワープする。その後、元の位置にワープして戻る。この間、スキルや通常攻撃は使用できるが移動はできない。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [11, 8, 5], "cooldownBurn": "11/8/5", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [-0.3, -0.4, -0.5], [1.5, 2, 2.5], [4, 4, 4], [0.5, 0.5, 0.5], [3, 3, 3], [100, 200, 300], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "-0.3/-0.4/-0.5", "1.5/2/2.5", "4", "0.5", "3", "100/200/300", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575], "rangeBurn": "575", "image": {"full": "ZoeR.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "スパークル！", "description": "スキル使用後、次に行う通常攻撃が追加魔法ダメージを与える。", "image": {"full": "Zoe_P.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}