{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gangplank": {"id": "Gangplank", "key": "41", "name": "Gangplank", "title": "te<PERSON><PERSON> m<PERSON>", "image": {"full": "Gangplank.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "41000", "num": 0, "name": "default", "chromas": false}, {"id": "41001", "num": 1, "name": "Gangplank fantomatic", "chromas": false}, {"id": "41002", "num": 2, "name": "Gangplank partizan", "chromas": false}, {"id": "41003", "num": 3, "name": "Gangplank marinar", "chromas": false}, {"id": "41004", "num": 4, "name": "Gangplank soldat de jucărie", "chromas": false}, {"id": "41005", "num": 5, "name": "Gangplank forțe speciale", "chromas": false}, {"id": "41006", "num": 6, "name": "Gangplank sultan", "chromas": false}, {"id": "41007", "num": 7, "name": "Căpitanul Gangplank", "chromas": false}, {"id": "41008", "num": 8, "name": "<PERSON><PERSON><PERSON>, corsarul spațial", "chromas": true}, {"id": "41014", "num": 14, "name": "Gangplank la piscină", "chromas": true}, {"id": "41021", "num": 21, "name": "Gangplank FPX", "chromas": true}, {"id": "41023", "num": 23, "name": "Gangplank, trădătorul", "chromas": true}, {"id": "41033", "num": 33, "name": "PROIECT: Gangplank", "chromas": true}], "lore": "Gangplank este fostul rege al tâlharilor, un individ temut, imprevizibil și brutal. <PERSON>, era stăpân peste orașul-port Bilgewater și, cu toate că a fost îndepărtat de la putere, mulți cred că astfel a ajuns și mai periculos decât înainte. Gangplank ar prefera să scalde orașul în sânge decât să-l lase pe mâna altcuiva. Acum, cu pistolul, hangerul și butoaiele cu praf de pușcă la îndemână, e hotărât să recucerească tot ce a pierdut.", "blurb": "Gangplank este fostul rege al tâlharilor, un individ temut, imprevizibil și brutal. <PERSON>, era stăpân peste orașul-port Bilgewater și, cu toate că a fost îndepărtat de la putere, mulți cred că astfel a ajuns și mai periculos decât înainte...", "allytips": ["''Negocierea'' aplică efecte la impact, cum ar fi cele ale ''Ciocanului înghețat'' sau ale ''Satârul negru''.", "Dacă ești atent care adversari de pe hartă au viața scăzută, poți reuși un asasinat-surpriză cu o ''Rafală de tun''.", "Încearcă să plasezi ''<PERSON><PERSON><PERSON> de tun'' pe calea de retragere a adversarilor care fug."], "enemytips": ["''Negociere'' provoacă daune fizice ridicate. Obiectele care oferă armură te pot ajuta dacă un Gangplank inamic se descurcă foarte bine.", "Odată ce Gangplank ajunge nivelul 6, ai grijă la abilitatea sa supremă, ''<PERSON><PERSON><PERSON><PERSON> de tun'' – are o rază imensă!"], "tags": ["Fighter"], "partype": "Mană", "info": {"attack": 7, "defense": 6, "magic": 4, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 114, "mp": 280, "mpperlevel": 60, "movespeed": 345, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 4.2, "attackspeedperlevel": 3.2, "attackspeed": 0.658}, "spells": [{"id": "GangplankQWrapper", "name": "Negociere", "description": "Împușcă ținta și fură aur pentru fiecare unitate inamică ucisă.", "tooltip": "{{Spell_GangplankQWrapper_Tooltip_{{ gamemodeinteger }}}}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Aur furat", "Șerpi de arg<PERSON>", "Cost de @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e5 }} -> {{ e5NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4.5, 4.5, 4.5, 4.5, 4.5], "cooldownBurn": "4.5", "cost": [50, 45, 40, 35, 30], "costBurn": "50/45/40/35/30", "datavalues": {}, "effect": [null, [10, 40, 70, 100, 130], [3, 4, 5, 6, 7], [0, 0, 0, 0, 0], [500, 500, 500, 500, 500], [4, 5, 6, 7, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/40/70/100/130", "3/4/5/6/7", "0", "500", "4/5/6/7/8", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "GangplankQWrapper.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankW", "name": "Vindecare de scorbut", "description": "Gangplank mănâncă portocale pentru a se vindeca de efectele de control al maselor și a-și reface o parte din viață.", "tooltip": "Gangplank mănâncă o portocală uriașă, elimin<PERSON>d toate efectele de <status>neutralizare</status> și vindecându-și <healing>{{ basehealth }} plus {{ e2 }}% din viața lips<PERSON></healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vindecare", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [45, 70, 95, 120, 145], [13, 13, 13, 13, 13], [0.25, 0.25, 0.25, 0.25, 0.25], [200, 200, 200, 200, 200], [30, 40, 50, 60, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/70/95/120/145", "13", "0.25", "200", "30/40/50/60/70", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GangplankW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GangplankE", "name": "Butoi de pulbere", "description": "Gangplank aruncă un butoi de pulbere în zona-țintă. Atunci când atacă un butoi, acesta explodează și provoacă daune AoE, încetinind toți inamicii din apropiere.", "tooltip": "Gangplank plasează un butoi de pulbere care poate fi atacat de Gangplank și de campionii inamici timp de {{ e5 }} secunde. Când îl distruge un inamic, butoiul este dezamorsat. Când îl distruge Gangplank, butoiul explodează, <status>încetinind</status> inamicii cu {{ finalslowamount }}% timp de {{ e2 }} secunde și provocându-le <physicalDamage>daunele din atac</physicalDamage>, ignorând {{ e0 }}% din armură. Campionii suferă încă <physicalDamage>{{ e3 }} daune fizice</physicalDamage>.<br /><br />Viața butoiului scade la fiecare {{ f5 }} secunde. Exploziile butoaielor detonează celelalte butoaie ale căror zone de impact se suprapun, însă nu îi provoacă decât o dată daune aceleiași ținte. Exploziile butoaielor declanșate de <spellName>''Negociere''</spellName> vor oferi aur bonus pentru țintele ucise.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune bonus împotriva campionilor", "Cumuluri maxime", "Încetinire", "<PERSON><PERSON> <PERSON>"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ barrelslow }}% -> {{ barrelslowNL }}%", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [3, 3, 4, 4, 5], [2, 2, 2, 2, 2], [75, 105, 135, 165, 195], [40, 50, 60, 70, 80], [25, 25, 25, 25, 25], [2, 2, 2, 2, 2], [10, 10, 10, 10, 10], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 40, 40, 40, 40]], "effectBurn": [null, "3/3/4/4/5", "2", "75/105/135/165/195", "40/50/60/70/80", "25", "2", "10", "100", "0.5", "40"], "vars": [], "costType": "Fără cost", "maxammo": "3", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "GangplankE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "Fără cost"}, {"id": "GangplankR", "name": "Rafa<PERSON><PERSON> de tun", "description": "Gangplank îi ordonă corabiei sale să tragă cu ghiulele într-o anumită zonă, încetinindu-și inamicii și provocându-le daune.", "tooltip": "Gangplank îi ordonă corăbiei sale să tragă {{ totalwavestooltip }} salve de ghiulele oriunde pe hartă de-a lungul a {{ zoneduration }} secunde. Fiecare salvă <status>încetinește</status> inamicii cu {{ slowpercent }}% timp de {{ slowduration }} secunde și le provoacă <magicDamage>{{ onewavedamage }} daune magice</magicDamage>. Daune maxime: {{ totaldamagetooltip }}<br /><br />Această abilitate poate fi îmbunătățită în magazin prin <spellName>''Negociere''</spellName>.<br /><br /><spellName>Foc după foc</spellName>: trage încă 6 salve de ghiulele.<br /><spellName>Fiica Morții</spellName>: lansează o mega-ghiulea care provoacă <trueDamage>{{ deathsdaughterdamage }} daune reale</trueDamage> și <status>încetinește inamicii</status> cu {{ deathsdaughterslow }}% timp de {{ deathsdaughterslowduration }} secundă.<br /><spellName>Crește moralul</spellName>: aliații aflați în zona de acțiune a ''Rafalei de tun'' primesc <speed>{{ raisemoralehaste }}% viteză de mișcare</speed> timp de {{ raisemoralehasteduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune per salvă", "Timp de reactivare"], "effect": ["{{ damageperwave }} -> {{ damageperwaveNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [1.5, 0.5, 0.5], [0, 0, 0], [300, 300, 300], [75, 75, 75], [1, 1, 1], [40, 40, 40]], "effectBurn": [null, "0", "0", "0", "0", "1.5/0.5/0.5", "0", "300", "75", "1", "40"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [30000, 30000, 30000], "rangeBurn": "30000", "image": {"full": "GangplankR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Proba focului", "description": "O dată la câteva secunde, următorul atac melee al lui Gangplank îi dă foc adversarului.", "image": {"full": "Gangplank_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}