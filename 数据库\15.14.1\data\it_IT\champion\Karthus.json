{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karthus": {"id": "<PERSON><PERSON><PERSON>", "key": "30", "name": "<PERSON><PERSON><PERSON>", "title": "il cantore della morte", "image": {"full": "Karthus.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "30000", "num": 0, "name": "default", "chromas": false}, {"id": "30001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30002", "num": 2, "name": "Statua di Karthus", "chromas": false}, {"id": "30003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "30004", "num": 4, "name": "<PERSON><PERSON><PERSON> Pentakill", "chromas": false}, {"id": "30005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "30010", "num": 10, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "30017", "num": 17, "name": "<PERSON><PERSON><PERSON> III: Lost Chapter", "chromas": true}, {"id": "30026", "num": 26, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "L'araldo dell'oblio, <PERSON><PERSON><PERSON> è uno spirito immortale le cui inquietanti canzoni sono un preludio del suo terrificante aspetto. I viventi temono l'eternità della non morte, ma <PERSON><PERSON><PERSON> vede solo bellezza e purezza nel suo abbraccio, una perfetta unione di vita e morte. Quando <PERSON>rthus lascia le Isole Ombra, è per portare la gioia della morte ai mortali, come apostolo dei non morti.", "blurb": "L'araldo dell'oblio, <PERSON><PERSON><PERSON> è uno spirito immortale le cui inquietanti canzoni sono un preludio del suo terrificante aspetto. I viventi temono l'eternità della non morte, ma <PERSON><PERSON><PERSON> vede solo bellezza e purezza nel suo abbraccio, una perfetta unione di...", "allytips": ["Chiedi sempre ai tuoi alleati di ricordarti di usare Requiem per riuscire a ottenere uccisioni nelle altre corsie.", "Devastazione è molto efficace per finire i minion e aggredire i campioni nemici."], "enemytips": ["Dopo essere stato ucciso, <PERSON><PERSON><PERSON> pu<PERSON> continuare a lanciare abilità per un breve periodo. Allontanati dal suo corpo per essere al riparo.", "Cerca sempre di avere abbastanza salute per sopravvivere a un Requiem, anche se questo vuol dire tornare alla base più spesso per guarire."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 620, "hpperlevel": 110, "mp": 467, "mpperlevel": 31, "movespeed": 335, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 6.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 46, "attackdamageperlevel": 3.25, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "KarthusLayWasteA1", "name": "Devastazione", "description": "<PERSON><PERSON><PERSON> r<PERSON>cia uno scoppio ritardato in un punto, infliggendo danni ai nemici nelle vicinanze. Infligge danni aumentati ai nemici isolati. ", "tooltip": "<PERSON><PERSON><PERSON> crea un'esplosione magica che infligge <magicDamage>{{ qdamage }} danni magici</magicDamage>. Se l'esplosione colpisce un solo nemico, infligge invece <magicDamage>{{ qsingletargetdamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [20, 25, 30, 35, 40], "costBurn": "20/25/30/35/40", "datavalues": {}, "effect": [null, [45, 65, 85, 105, 125], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "45/65/85/105/125", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "KarthusLayWasteA1.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON>all<PERSON>f<PERSON>", "name": "Muro del dolore", "description": "<PERSON><PERSON>us crea uno schermo di energia salassante. Qualsiasi unità nemica attraversi lo schermo ha la velocità di movimento e la resistenza magica ridotte per un certo periodo di tempo.", "tooltip": "<PERSON><PERSON><PERSON> crea un muro che dura per {{ e4 }} secondi. I nemici che lo attraversano perdono <scaleMR>{{ e1 }}% di resistenza magica</scaleMR> per {{ e5 }} secondi e sono <status>rallentati</status> del {{ e3 }}%, che decade nell'arco della durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON> muro", "Rallentamento"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [800, 900, 1000, 1100, 1200], [40, 50, 60, 70, 80], [5, 5, 5, 5, 5], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "800/900/1000/1100/1200", "40/50/60/70/80", "5", "5", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KarthusWallOfPain.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Profanare", "description": "<PERSON>rthus ruba passivamente energia alle sue vittime, ottenendo mana per ogni uccisione. In alternativa, <PERSON>rth<PERSON> può circondarsi delle anime delle sue prede, infliggendo danni ai nemici nelle vicinanze, ma consumando il suo mana molto rapidamente.", "tooltip": "<spellPassive>Passiva: </spellPassive>Quando <PERSON>rth<PERSON> uccide un'unità, recupera <scaleMana>{{ e2 }} mana</scaleMana>.<br /><br /><toggle>Attiva:</toggle> Karthus crea un'aura necrotica, che infligge <magicDamage>{{ totaldps }} danni magici</magicDamage> al secondo ai nemici vicini.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo", "<PERSON><PERSON><PERSON><PERSON> mana", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [30, 42, 54, 66, 78], "costBurn": "30/42/54/66/78", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [10, 20, 30, 40, 50], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "10/20/30/40/50", "0.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana al secondo", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "KarthusDefile.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} mana al secondo"}, {"id": "<PERSON><PERSON><PERSON>Fallen<PERSON>ne", "name": "Requiem", "description": "<PERSON><PERSON> aver canalizzato energia per 3 secondi, <PERSON><PERSON><PERSON> infligge danni a tutti i campioni nemici.", "tooltip": "<PERSON><PERSON><PERSON> canalizza per 3 secondi, poi infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> a tutti i campioni nemici, indipendentemente dalla distanza.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "KarthusFallenOne.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON>ganno alla morte", "description": "<PERSON>uando muore, <PERSON><PERSON><PERSON> si trasforma in spirito e può continuare a lanciare incantesimi.", "image": {"full": "Karthus_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}