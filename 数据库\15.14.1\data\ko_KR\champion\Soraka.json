{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Soraka": {"id": "<PERSON><PERSON><PERSON>", "key": "16", "name": "소라카", "title": "별의 아이", "image": {"full": "Soraka.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "16000", "num": 0, "name": "default", "chromas": false}, {"id": "16001", "num": 1, "name": "나무 요정 소라카", "chromas": false}, {"id": "16002", "num": 2, "name": "신성한 소라카", "chromas": false}, {"id": "16003", "num": 3, "name": "천상의 빛 소라카", "chromas": false}, {"id": "16004", "num": 4, "name": "사신 소라카", "chromas": false}, {"id": "16005", "num": 5, "name": "바나나 수도회 소라카", "chromas": false}, {"id": "16006", "num": 6, "name": "프로그램 소라카", "chromas": false}, {"id": "16007", "num": 7, "name": "별 수호자 소라카", "chromas": false}, {"id": "16008", "num": 8, "name": "파자마 수호자 소라카", "chromas": false}, {"id": "16009", "num": 9, "name": "겨울 동화 소라카", "chromas": true}, {"id": "16015", "num": 15, "name": "빛의 인도자 소라카", "chromas": false}, {"id": "16016", "num": 16, "name": "어둠의 인도자 소라카", "chromas": false}, {"id": "16017", "num": 17, "name": "프레스티지 별 수호자 소라카", "chromas": false}, {"id": "16018", "num": 18, "name": "귀염둥이 카페 소라카", "chromas": true}, {"id": "16027", "num": 27, "name": "영혼의 꽃 소라카", "chromas": true}, {"id": "16037", "num": 37, "name": "불멸의 영웅 소라카", "chromas": true}, {"id": "16044", "num": 44, "name": "요정 왕국 소라카", "chromas": true}], "lore": "타곤 산 너머 천상계의 방랑자 소라카는 필멸자들이 스스로의 폭력성 때문에 고통받는 것을 치유하기 위해 자신의 불멸을 포기했다. 소라카는 만나는 모든 이에게 동정심과 자비의 마음을 심어주려고 노력하며, 심지어 자신에게 해를 입히려 획책하는 자들마저 치유한다. 이 세계의 갈등과 혼란을 모두 지켜보았음에도, 소라카는 아직 룬테라 인들이 잠재력을 모두 끌어낸 것은 아니라고 믿고 있다.", "blurb": "타곤 산 너머 천상계의 방랑자 소라카는 필멸자들이 스스로의 폭력성 때문에 고통받는 것을 치유하기 위해 자신의 불멸을 포기했다. 소라카는 만나는 모든 이에게 동정심과 자비의 마음을 심어주려고 노력하며, 심지어 자신에게 해를 입히려 획책하는 자들마저 치유한다. 이 세계의 갈등과 혼란을 모두 지켜보았음에도, 소라카는 아직 룬테라 인들이 잠재력을 모두 끌어낸 것은 아니라고 믿고 있다.", "allytips": ["소라카는 강력한 치료 능력으로 아군의 진격을 도와주는 강력한 아군입니다.", "아군에게 기원을 사용하면 멀리 떨어진 아군도 위험에서 구해낼 수 있습니다.", "별의 균형을 활용하여 일정 범위 안의 적들을 궁지에 몰아넣을 수 있습니다."], "enemytips": ["소라카가 아군을 치유하기 위해 전방으로 나올 때 공격을 집중하세요.", "소라카가 별의 균형을 견제기로 사용할 때는 긴 재사용 대기시간을 노리세요.", "소라카가 치유하는 아군보다는 소라카에게 직접 공격을 집중하는 편이 더 쉽습니다."], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 2, "defense": 5, "magic": 7, "difficulty": 3}, "stats": {"hp": 605, "hpperlevel": 88, "mp": 425, "mpperlevel": 40, "movespeed": 325, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2.14, "attackspeed": 0.625}, "spells": [{"id": "SorakaQ", "name": "별부름", "description": "지정한 위치에 하늘에서 별이 떨어져 적에게 마법 피해를 입히고 이동 속도를 느려지게 합니다. 별부름이 적 챔피언에게 적중하면 소라카가 체력을 회복합니다.", "tooltip": "소라카가 별을 떨어뜨려 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ movespeedslow*100 }}% <status>둔화</status>시킵니다. <br /><br />적 챔피언에게 적중하면 소라카가 <keywordMajor>별의 가호</keywordMajor>를 얻어 {{ hotduration }}초에 걸쳐 <healing>체력을 {{ totalhot }}</healing> 회복하고 <speed>이동 속도가 {{ movespeedhaste*100 }}%</speed> 증가했다가 원래대로 돌아옵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "체력 회복 (별의 가호)", "이동 속도", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basehot }} -> {{ basehotNL }}", "{{ movespeedhaste*100.000000 }}% -> {{ movespeedhastenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "SorakaQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SorakaW", "name": "은하의 마력", "description": "소라카가 자신의 체력을 일정 부분 희생하여 아군 유닛을 치유합니다.", "tooltip": "소라카가 다른 아군 챔피언의 <healing>체력을 {{ totalheal }}</healing>만큼 회복시킵니다.<br /><br />소라카가 <keywordMajor>별의 가호</keywordMajor>를 받고 있으면 체력 소모량이 {{ percenthealthcostrefund*100 }}% 감소하며 대상도 {{ spell.sorakaq:hotduration }}초 동안 <keywordMajor>별의 가호</keywordMajor>를 받습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "재사용 대기시간", "소모값 @AbilityResourceName@", "체력 소모량 감소"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ percenthealthcostrefund*100.000000 }}% -> {{ percenthealthcostrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "%, 마나 {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SorakaW.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "최대 체력의 {{ percenthealthcost*100 }}%, 마나 {{ cost }}"}, {"id": "SorakaE", "name": "별의 균형", "description": "영역 내의 모든 적을 침묵시키는 구역을 생성합니다. 영역이 해제될 때 아직 안에 남아있는 모든 적은 제자리에 묶입니다.", "tooltip": "소라카가 별의 영역을 생성해 챔피언에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 영역은 {{ rootdelay }}초 동안 유지되며 안에 있는 적을 <status>침묵</status>시킵니다. 영역이 사라지면 안에 있던 챔피언은 {{ rootduration }}초 동안 <status>속박</status>되며 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "속박 지속시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "SorakaE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "SorakaR", "name": "기원", "description": "소라카가 아군에게 희망을 불어넣어 자신과 아군 챔피언들의 체력을 즉시 회복합니다.", "tooltip": "소라카가 신의 권능을 빌어 거리와 관계없이 모든 아군 챔피언의 <healing>체력을 {{ healingcalc }}</healing>만큼 회복시킵니다. 체력이 40% 아래인 대상에게는 회복 효과가 <healing>{{ ampedhealing }}</healing>까지 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["회복량", "재사용 대기시간"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [150, 135, 120], "cooldownBurn": "150/135/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SorakaR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "구원", "description": "소라카는 체력이 낮은 아군 쪽으로 이동할 때 속도가 더 빨라집니다.", "image": {"full": "Soraka_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}