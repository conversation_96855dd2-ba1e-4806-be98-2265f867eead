{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Belveth": {"id": "Belveth", "key": "200", "name": "ベル＝ヴェス", "title": "ヴォイドの女帝", "image": {"full": "Belveth.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "200000", "num": 0, "name": "default", "chromas": false}, {"id": "200001", "num": 1, "name": "バトルボス ベル＝ヴェス", "chromas": true}, {"id": "200010", "num": 10, "name": "宇宙の女傑ベル＝ヴェス", "chromas": true}, {"id": "200019", "num": 19, "name": "プライモーディアン ベル＝ヴェス", "chromas": true}], "lore": "ヴォイドが呑み込んだ街一つ分の物質から生み出された悪夢のような女帝ベル＝ヴェスは、ルーンテラの終焉そのもの…そして彼女が造り出す醜悪な現実の始まりだ。我がものとした地上世界の膨大な歴史、そして知識や記憶に駆り立てられ、彼女はますます増大する新たな経験や感情への渇望を満たそうと、行く手を阻むものすべてを貪り喰う。しかし、たった一つの世界で彼女の欲望が満たされるはずもない。ベル＝ヴェスは飢えた目をヴォイドの古き主たちに向ける…", "blurb": "ヴォイドが呑み込んだ街一つ分の物質から生み出された悪夢のような女帝ベル＝ヴェスは、ルーンテラの終焉そのもの…そして彼女が造り出す醜悪な現実の始まりだ。我がものとした地上世界の膨大な歴史、そして知識や記憶に駆り立てられ、彼女はますます増大する新たな経験や感情への渇望を満たそうと、行く手を阻むものすべてを貪り喰う。しかし、たった一つの世界で彼女の欲望が満たされるはずもない。ベル＝ヴェスは飢えた目をヴォイドの古き主たちに向ける…", "allytips": [], "enemytips": [], "tags": ["Fighter"], "partype": "", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 60, "mpperlevel": 0, "movespeed": 340, "armor": 32, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 1.5, "attackspeedperlevel": 0, "attackspeed": 0.85}, "spells": [{"id": "BelvethQ", "name": "ヴォイドサージ", "description": "選択した方向にダッシュして、接触したすべての敵にダメージを与える。", "tooltip": "ダッシュして接触した敵に<physicalDamage>{{ basedamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />方向ごとに{{ f1 }}秒間のクールダウンが個別に存在する。このクールダウンは、<attackSpeed>攻撃速度</attackSpeed>に応じて短縮される。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "方向ごとのクールダウン", "モンスターに対するダメージ", "ミニオンのダメージ"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ persidecooldown }} -> {{ persidecooldownNL }}", "{{ monstermod }} -> {{ monstermodNL }}", "{{ minonmod*100.000000 }}% -> {{ minonmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethQ.png", "sprite": "spell1.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "BelvethW", "name": "天と地", "description": "尻尾を地面に叩きつけ、敵にダメージを与えてノックアップさせ、スロウ効果を付与する。", "tooltip": "尻尾を叩きつけて<magicDamage>{{ damage }}の魔法ダメージ</magicDamage>を与える。また、敵を{{ duration }}秒間<status>ノックアップ</status>させて、{{ slowduration }}秒間{{ slowpercent*100 }}%の<status>スロウ効果</status>を付与する。チャンピオンに命中すると、その方向の<spellName>「ヴォイドサージ」</spellName>のクールダウンが解消される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果時間", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [715, 715, 715, 715, 715], "rangeBurn": "715", "image": {"full": "BelvethW.png", "sprite": "spell1.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "BelvethE", "name": "ロイヤルストーム", "description": "その場で動きを止めて自身の周囲に強烈な嵐を召喚し、最も体力の低い敵を切り裂いて、ライフスティールとダメージ軽減効果を獲得する。", "tooltip": "詠唱して周囲を切り裂く。この際、{{ drpercent*100 }}%のダメージ軽減効果と{{ totallifesteal }}のライフスティールを獲得し、{{ totalduration }}秒かけて{{ f2.0 }}回の通常攻撃を行う。攻撃回数は<attackSpeed>攻撃速度</attackSpeed>に応じて増加する。通常攻撃のたびに最も体力が低い敵を狙い、対象の減少体力に応じて<physicalDamage>{{ damageperstrike }}</physicalDamage>から<physicalDamage>{{ maxdamageperstriketooltip }}の物理ダメージ</physicalDamage>を与える。<br /><br />別のスキルを使用するか<recast>再発動</recast>すると、このスキルが早めに終了する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "ダメージ軽減", "クールダウン"], "effect": ["{{ damageperhit }} -> {{ damageperhitNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "BelvethE.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "BelvethR", "name": "終わりなき晩餐", "description": "ヴォイドの珊瑚の残片を吸収し、真の姿に変身して最大体力、射程距離、攻撃速度、非戦闘時の移動速度が増加する。ヴォイド出身のエピックモンスターが残したヴォイドの珊瑚の残片を吸収すると、アルティメットスキルの効果時間が増加し、ヴォイドのコバンザメを召喚できるようになる。", "tooltip": "<spellPassive>自動効果:</spellPassive> 同じ対象に通常攻撃を2回行うたびに、追加で<trueDamage>{{ finalonhitdamage }}の確定ダメージ</trueDamage>を与える。この効果は無限にスタックする。チャンピオンかエピックモンスターからキルまたはアシストを獲得すると、そこに「ヴォイドコーラル」が1つ残る。<br /><br /><spellActive>発動効果:</spellActive> 「ヴォイドコーラル」を吸収して<keywordMajor>「ラベンダー」を{{ passivestacksondevour }}</keywordMajor>スタック獲得し、{{ steroidduration }}秒間、真の姿を発現させる。ヴォイドのエピックモンスターが残した「ヴォイドコーラル」を吸収した場合、効果時間が{{ voidduration }}秒間に増加し、周囲で倒されたミニオンが「ヴォイドレモラ」になる。詠唱中は周囲の敵に<status>スロウ効果</status>を付与し、その後爆発を起こして<trueDamage>{{ totalexplosiondamage }} + 減少体力の{{ missinghealthdamage*100 }}%の確定ダメージ</trueDamage>を与える。<br /><br />真の姿となっている間は<healing>最大体力が{{ maxhealthondevour }}</healing>、非戦闘時の<speed>移動速度が{{ oocms }}</speed>、射程距離が{{ bonusaarange }}、<attackSpeed>合計攻撃速度が{{ totalasmod*100 }}%</attackSpeed>増加し、<spellName>「ヴォイドサージ」</spellName>で壁を越えられるようになる。<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["通常攻撃時ダメージ", "爆発ダメージ", "増加体力", "移動速度", "攻撃速度", "「ヴォイドレモラ」の体力"], "effect": ["{{ onhitdamage }} -> {{ onhitdamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basemaxhealth }} -> {{ basemaxhealthNL }}", "{{ oocms }} -> {{ oocmsNL }}", "{{ totalasmod*100.000000 }}% -> {{ totalasmodnl*100.000000 }}%", "{{ voidlinghpscale*100.000000 }}% -> {{ voidlinghpscalenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [1, 1, 1], "cooldownBurn": "1", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "BelvethR.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "死のラベンダー", "description": "大型ミニオン、大型モンスター、チャンピオンからキルまたはアシストを獲得すると、攻撃速度のスタックを恒久的に獲得する。また、スキル使用後に一時的に攻撃速度が増加する。", "image": {"full": "Belveth_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}