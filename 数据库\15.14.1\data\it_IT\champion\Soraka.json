{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Soraka": {"id": "<PERSON><PERSON><PERSON>", "key": "16", "name": "<PERSON><PERSON><PERSON>", "title": "la figlia delle stelle", "image": {"full": "Soraka.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "16000", "num": 0, "name": "default", "chromas": false}, {"id": "16001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "16002", "num": 2, "name": "Soraka la Divina", "chromas": false}, {"id": "16003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "16004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "16005", "num": 5, "name": "Soraka Ordine della Banana", "chromas": false}, {"id": "16006", "num": 6, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "16007", "num": 7, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "16008", "num": 8, "name": "Soraka Guardiana in Pigiama", "chromas": false}, {"id": "16009", "num": 9, "name": "Soraka Prodigio Invernale", "chromas": true}, {"id": "16015", "num": 15, "name": "Soraka Portatrice dell'Alba", "chromas": false}, {"id": "16016", "num": 16, "name": "Soraka Portatrice della Notte", "chromas": false}, {"id": "16017", "num": 17, "name": "Soraka Guardiana Stellare (edizione prestigio)", "chromas": false}, {"id": "16018", "num": 18, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "16027", "num": 27, "name": "<PERSON><PERSON><PERSON>e", "chromas": true}, {"id": "16037", "num": 37, "name": "Soraka Viaggio Immortale", "chromas": true}, {"id": "16044", "num": 44, "name": "<PERSON><PERSON><PERSON> fatata", "chromas": true}], "lore": "Una viandante dalle dimensioni celestiali oltre il Monte Targon, <PERSON><PERSON><PERSON> ha rinunciato alla sua immortalità per proteggere i mortali dai loro istinti più violenti. Ha il compito di diffondere pietà e compassione tra chiunque incontri, arrivando addirittura a curare chi vuole farle del male. E nonostante abbia assistito a tutte le lotte che devastano questo mondo, Soraka crede ancora che il popolo di Runeterra debba raggiungere appieno il suo potenziale.", "blurb": "Una viandante dalle dimensioni celestiali oltre il Monte Targon, <PERSON><PERSON><PERSON> ha rinunciato alla sua immortalità per proteggere i mortali dai loro istinti più violenti. Ha il compito di diffondere pietà e compassione tra chiunque incontri, arrivando...", "allytips": ["Soraka è un'alleata molto potente in battaglia, che usa le sue abilità di guarigione per mantenere il gruppo in salute e in movimento.", "Puoi usare Desiderio sui tuoi alleati dall'altra parte della mappa per salvarli da avvenimenti altrimenti fatali.", "Equinozio può essere impiegato come potente strumento nella gestione delle zone per tenere a bada i nemici."], "enemytips": ["Concentrati su Soraka quando si avventura in prima linea per curare i suoi alleati.", "S<PERSON><PERSON>tta il lungo tempo di ricarica di Equinozio di Soraka se dovesse usarlo per attaccare.", "È più facile concentrarsi su Soraka piuttosto che sull'alleato che sta curando."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 7, "difficulty": 3}, "stats": {"hp": 605, "hpperlevel": 88, "mp": 425, "mpperlevel": 40, "movespeed": 325, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2.14, "attackspeed": 0.625}, "spells": [{"id": "SorakaQ", "name": "<PERSON><PERSON><PERSON> stellare", "description": "Una stella cade dal cielo sulla posizione bersaglio, infliggendo danni magici e rallentando i nemici. Soraka recupera salute se un campione nemico viene colpito da Tempesta stellare.", "tooltip": "Soraka fa cadere una stella che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallenta</status> i nemici di {{ movespeedslow*100 }}% per {{ slowduration }} secondi. <br /><br />Se colpisce un campione nemico, <PERSON><PERSON><PERSON> ottiene <keywordMajor>Rinvigorimento</keywordMajor>, che ripristina <healing>{{ totalhot }} salute</healing> nell'arco di {{ hotduration }} secondi e conferisce <speed>{{ movespeedhaste*100 }}% velocità di movimento</speed> che decresce nell'arco della stessa durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ripristino salute (Rinvigorimento)", "Velocità di movimento", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basehot }} -> {{ basehotNL }}", "{{ movespeedhaste*100.000000 }}% -> {{ movespeedhastenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "SorakaQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SorakaW", "name": "Infusione astrale", "description": "Soraka sacrifica parte della propria salute per curare un campione alleato.", "tooltip": "Soraka ripristina <healing>{{ totalheal }} salute</healing> a un altro campione alleato.<br /><br />Se Soraka è sotto l'effetto di <keywordMajor>Rinvigorimento</keywordMajor>, il costo in salute è ridotto di {{ percenthealthcostrefund*100 }}% e il bersaglio ottiene a sua volta <keywordMajor>Rinvigorimento</keywordMajor> per {{ spell.sorakaq:hotduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON> in salute ridotto"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ percenthealthcostrefund*100.000000 }}% -> {{ percenthealthcostrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% salute massima, {{ cost }} mana", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SorakaW.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ percenthealthcost*100 }}% salute massima, {{ cost }} mana"}, {"id": "SorakaE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Crea una zona in una posizione che silenzia tutti i nemici al suo interno. Quando la zona svanisce, tutti i nemici ancora all'interno rimangono immobilizzati.", "tooltip": "Soraka crea una zona stellata che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai campioni. La zona rimane per {{ rootdelay }} secondi, <status>silenziando</status> i nemici al suo interno. Quando la zona sparisce, i campioni che si trovano ancora al suo interno vengono <status>immobilizzati</status> per {{ rootduration }} secondo/i e subiscono <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Durata immobilizzazione", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "SorakaE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SorakaR", "name": "<PERSON><PERSON><PERSON>", "description": "Soraka riempie i suoi alleati di speranza, ripristinando istantaneamente la sua salute e quella di tutti i campioni alleati.", "tooltip": "Soraka evoca poteri divini per ripristinare <healing>{{ healingcalc }} salute</healing> a tutti i campioni alleati, a prescindere dalla distanza. La guarigione aumenta a <healing>{{ ampedhealing }}</healing> sui bersagli con salute al di sotto del 40%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "Ricarica"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [150, 135, 120], "cooldownBurn": "150/135/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SorakaR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Salvezza", "description": "Soraka corre più velocemente verso gli alleati vicini con poca salute.", "image": {"full": "Soraka_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}