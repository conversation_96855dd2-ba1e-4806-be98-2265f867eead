{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Garen": {"id": "<PERSON><PERSON><PERSON>", "key": "86", "name": "<PERSON><PERSON><PERSON>", "title": "la potenza di Demacia", "image": {"full": "Garen.png", "sprite": "champion1.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "86000", "num": 0, "name": "default", "chromas": true}, {"id": "86001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86002", "num": 2, "name": "Garen Truppa del Deserto", "chromas": false}, {"id": "86003", "num": 3, "name": "Garen Commando", "chromas": false}, {"id": "86004", "num": 4, "name": "<PERSON><PERSON><PERSON> O<PERSON>cu<PERSON>", "chromas": false}, {"id": "86005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86006", "num": 6, "name": "Garen Legione d'Acciaio", "chromas": false}, {"id": "86010", "num": 10, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "86011", "num": 11, "name": "Garen dei Regni in Guerra", "chromas": true}, {"id": "86013", "num": 13, "name": "Garen Di<PERSON>", "chromas": false}, {"id": "86014", "num": 14, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "86022", "num": 22, "name": "Garen dei Regni Mecha", "chromas": false}, {"id": "86023", "num": 23, "name": "Garen dei Regni Mecha (edizione prestigio)", "chromas": false}, {"id": "86024", "num": 24, "name": "Garen dell'Accademia di Battaglia", "chromas": true}, {"id": "86033", "num": 33, "name": "Garen Artefice di Miti", "chromas": true}, {"id": "86044", "num": 44, "name": "Garen Dio Re Caduto", "chromas": false}], "lore": "Un guerriero nobile e orgoglioso, <PERSON><PERSON><PERSON> combatte come membro dell'Indomita avanguardia. I suoi compagni lo stimano e i nemici lo rispettano, anche perché è il rampollo della prestigiosa famiglia Crownguard, con il compito di difendere Demacia e i suoi ideali. Protetto da un'armatura resistente alla magia e armato di una possente spada a due mani, Garen è pronto ad affrontare maghi e stregoni sul campo di battaglia, in un vero e proprio tornado d'acciaio.", "blurb": "Un guerriero nobile e orgoglioso, <PERSON><PERSON><PERSON> combatte come membro dell'Indomita avanguardia. I suoi compagni lo stimano e i nemici lo rispettano, anche perché è il rampollo della prestigiosa famiglia Crownguard, con il compito di difendere Demacia e i suoi...", "allytips": ["La rigenerazione di Garen aumenta sensibilmente se riesce a evitare di subire danni per molti secondi.", "<PERSON><PERSON><PERSON><PERSON> infligge il massimo dei danni solo quando colpisce un bersaglio singolo. Per degli scambi efficaci, prova a posizionarti in modo che venga colpito solo il campione nemico.", "Garen è limitato solo dalle r<PERSON>, quindi gli oggetti come Mannaia oscura gli sono molto utili."], "enemytips": ["Accumula gli oggetti con armatura per diminuire la grande quantità di danni inflitta da Garen.", "Prova a fuggire da Garen se la tua salute scarseggia, perché può eliminarti rapidamente con Giustizia di Demacia.", "Attento ad attaccarlo nell'erba alta: potresti subire tutti i danni di Giudizio.", "<PERSON><PERSON><PERSON><PERSON> infligge il massimo dei danni quando colpisce solo un bersaglio singolo. Se uscire dalla sua portata non è possibile, muoviti tra i minion alleati per ridurre i danni subiti."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 7, "magic": 1, "difficulty": 5}, "stats": {"hp": 690, "hpperlevel": 98, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 38, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 69, "attackdamageperlevel": 4.5, "attackspeedperlevel": 3.65, "attackspeed": 0.625}, "spells": [{"id": "GarenQ", "name": "Colpo decis<PERSON>", "description": "<PERSON>aren ottiene un aumento di velocità di movimento, liberandosi di tutti i rallentamenti. Colpisce un punto vitale con il prossimo attacco, infliggendo danni bonus e silenziando il bersaglio.", "tooltip": "Garen rimuove tutti gli effetti di <status>rallentamento</status> su se stesso e ottiene <speed>{{ movementspeedamount*100 }}% velocità di movimento</speed> per {{ movementspeedduration }} secondo/i.<br /><br />Il suo attacco successivo <status>silenzia</status> per {{ silenceduration }} secondi e infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Durata velocità di movimento"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedduration }} -> {{ movementspeeddurationNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "GarenQ.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GarenW", "name": "Coraggio", "description": "Garen aumenta passivamente armatura e resistenza magica quando uccide i nemici. Può anche attivare l'abilità per ottenere uno scudo e Tenacia per un breve momento, seguito da una quantità minore di riduzione danni per una durata più lunga.", "tooltip": "<spellPassive>Passiva:</spellPassive> <PERSON>are<PERSON> ha <scaleArmor>{{ resistsfortooltip }} armatura</scaleArmor> e <scaleMR>{{ resistsfortooltip }} resistenza magica</scaleMR> bonus. Uccidere unità conferisce permanentemente <attention>{{ resistgainonkilltooltip }} resistenza</attention>, fino a un massimo di <attention>{{ resistmax }}</attention>.<br /><br /><spellActive>Attiva:</spellActive> Garen sfoggia un coraggio d'acciaio per {{ drduration }} secondi, riducendo di un {{ drpercent*100 }}% i danni subiti. Inoltre, ottiene uno <shield>scudo da {{ totalshield }}</shield> e <slow>{{ upfronttenacity*100 }}% Tenacia</slow> per {{ upfrontduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Riduzione danni", "Ricarica"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ drpercent*100.000000 }}% -> {{ drpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GarenW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GarenE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Garen fa roteare rapidamente la spada intorno a sé, infliggendo danni fisici ai nemici vicini.", "tooltip": "Garen fa roteare rapidamente la spada per {{ duration }} secondi, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> {{ f1 }} volte nel corso della durata. Il nemico più vicino subisce <physicalDamage>{{ nearestenemybonus*100 }}% danni aumentati</physicalDamage>. I campioni colpiti da {{ stackstoshred }} colpi perdono un <scaleArmor>{{ shredamount*100 }}% di armatura</scaleArmor> per {{ shredduration }} secondi.<br /><br /><recast>Rilancio</recast>: Garen interrompe in anticipo questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base per rotazione", "Crescita attacco fisico per giro", "Ricarica"], "effect": ["{{ basedamagepertick }} -> {{ basedamagepertickNL }}", "{{ adratiopertick*100.000000 }}% -> {{ adratioperticknl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "GarenE.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GarenR", "name": "Giustizia di Demacia", "description": "Garen chiama a sé la forza di Demacia per cercare di giustiziare un campione nemico.", "tooltip": "Garen chiama a sé la forza di Demacia per uccidere il nemico, infliggendogli <trueDamage>{{ basedamage }} più {{ executedamage*100 }}% della salute mancante al bersaglio in danni puri</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "<PERSON><PERSON>uali salute mancante"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ executedamage*100.000000 }}% -> {{ executedamagenl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "GarenR.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Perseveranza", "description": "Se Garen non ha subito danni o è stato colpito dalle abilità dei nemici, rigenera una percentuale della sua salute massima ogni secondo.", "image": {"full": "Garen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}