{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sett": {"id": "<PERSON><PERSON>", "key": "875", "name": "Сетт", "title": "<PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Sett.png", "sprite": "champion3.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "875000", "num": 0, "name": "default", "chromas": false}, {"id": "875001", "num": 1, "name": "Сетт из Механических царств", "chromas": true}, {"id": "875008", "num": 8, "name": "Обсидиановый дракон Сетт", "chromas": true}, {"id": "875009", "num": 9, "name": "Обсидиановый дракон Сетт (престижный)", "chromas": false}, {"id": "875010", "num": 10, "name": "Тусовый Сетт", "chromas": true}, {"id": "875019", "num": 19, "name": "Пиротехник Сетт", "chromas": true}, {"id": "875038", "num": 38, "name": "Дух цветения Сетт", "chromas": true}, {"id": "875045", "num": 45, "name": "Боевая душа Сетт", "chromas": true}, {"id": "875056", "num": 56, "name": "Сетт из HEARTSTEEL", "chromas": true}, {"id": "875066", "num": 66, "name": "Лучезарный змей Сетт", "chromas": false}], "lore": "Сетт – один из боссов растущего преступного мира Ионии, построивший свою криминальную империю после войны с Ноксусом. Он начинал на арене в Навори как простой боец, но быстро прославился благодаря звериной силе и способности долго терпеть боль. Пробившись на самый верх, Сетт прибрал к рукам бойцовскую яму, в которой раньше сражался.", "blurb": "Сетт – один из боссов растущего преступного мира Ионии, построивший свою криминальную империю после войны с Ноксусом. Он начинал на арене в Навори как простой боец, но быстро прославился благодаря звериной силе и способности долго терпеть боль...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Tank"], "partype": "Выдержка", "info": {"attack": 8, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 670, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 33, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 1.75, "attackspeed": 0.625}, "spells": [{"id": "SettQ", "name": "Сила кулака", "description": "Следующие две атаки Сетта наносят дополнительный урон в зависимости от максимального запаса здоровья цели. Кроме того, он увеличивает свою скорость передвижения при перемещении в сторону вражеских чемпионов.", "tooltip": "Сетт рвется в бой, увеличивая <speed>скорость передвижения на {{ msamount*100 }}%</speed> на {{ msduration }} сек. при движении к вражеским чемпионам.<br /><br />Следующие две автоатаки Сетта дополнительно наносят <physicalDamage>физический урон в размере {{ basedamage }} плюс {{ maxhealthdamagecalc }} от максимального запаса здоровья</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Базовый урон", "% максимального запаса здоровья за 100 силы атаки"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthtadratiotooltip }}% -> {{ maxhealthtadratiotooltipNL }}%"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "SettQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "SettW", "name": "Головоломка", "description": "Пассивно: Сетт сохраняет получаемый урон в виде выдержки. Активно: Сетт расходует всю выдержку, получает щит и бьет по площади. Враги в центре области поражения получают чистый урон, а по краям – физический.", "tooltip": "<spellPassive>Пассивно:</spellPassive> Сетт сохраняет {{ damagestored*100 }}% от полученного урона в виде <keywordMajor>выдержки</keywordMajor> - вплоть до <keywordMajor>{{ maxgrit }}</keywordMajor>. <keywordMajor>Выдержка</keywordMajor> начинает быстро заканчиваться через {{ adrenalinestoragewindow }} сек. после получения урона.<br /><br /><spellActive>Активно:</spellActive> Сетт расходует всю <keywordMajor>выдержку</keywordMajor> и получает <shield>щит прочностью {{ shieldconversion*100 }}% от потраченной выдержки</shield> (прочность щита уменьшается в течение {{ shieldmaxduration }} сек.). После этого Сетт совершает мощный удар, нанося врагам в центре <trueDamage>чистый урон в размере {{ damagecalc }} плюс {{ damageconversion }} от потраченной выдержки</trueDamage> (но не более <trueDamage>{{ f1 }} урона</trueDamage>). Враги по краям получают <physicalDamage>физический урон</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Базовый урон"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [18, 16.5, 15, 13.5, 12], "cooldownBurn": "18/16.5/15/13.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SettW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "SettE", "name": "Мордобой", "description": "Сетт притягивает всех врагов по обе стороны от себя, нанося им урон и оглушая. Если враги были лишь с одной стороны, они замедляются, а не оглушаются.", "tooltip": "Сетт сталкивает врагов по обе стороны от себя друг с другом, нанося им <physicalDamage>{{ damagecalc }} физического урона</physicalDamage> и <status>замедляя</status> на {{ slowamount*100 }}% на {{ slowduration }} сек. Если Сетт схватил хотя бы по одному врагу с каждой стороны, обе цели <status>оглушаются</status> на {{ stunduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Базовый урон"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [490, 490, 490, 490, 490], "rangeBurn": "490", "image": {"full": "SettE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "SettR", "name": "Коронный прием", "description": "Сетт подпрыгивает вместе с вражеским чемпионом вперед, а затем бросает его об землю, нанося урон всем врагам около точки приземления и замедляя их.", "tooltip": "Сетт хватает вражеского чемпиона, <status>подавляет</status> его и переносит вперед, а затем бросает об землю. Все враги вокруг точки приземления получают <physicalDamage>физический урон в размере {{ damagecalc }} плюс {{ maxhealthdamage*100 }}% от дополнительного здоровья основной цели</physicalDamage>, а также <status>замедляются</status> на {{ slowamount*100 }}% на {{ slowduration }} сек. Чем дальше от точки приземления Сетта находятся враги, тем меньше они получают урона.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Урон от дополнительного здоровья", "Базовый урон"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "SettR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Бойцовская выдержка", "description": "При нанесении автоатак Сетт чередует удары слева и справа. Удар справа немного сильнее и быстрее. Кроме того, Сетт ненавидит проигрывать, поэтому его восстановление здоровья растет в зависимости от недостающего здоровья.", "image": {"full": "Sett_P.png", "sprite": "passive3.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}