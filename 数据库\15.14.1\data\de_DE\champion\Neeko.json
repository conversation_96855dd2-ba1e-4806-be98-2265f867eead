{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Neeko": {"id": "<PERSON><PERSON><PERSON>", "key": "518", "name": "<PERSON><PERSON><PERSON>", "title": "das wissbegierige Chamäleon", "image": {"full": "Neeko.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "518000", "num": 0, "name": "default", "chromas": false}, {"id": "518001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "518010", "num": 10, "name": "Sternenwächterin Neeko", "chromas": false}, {"id": "518011", "num": 11, "name": "Sternenwächterin Neeko (Prestige)", "chromas": false}, {"id": "518012", "num": 12, "name": "<PERSON>", "chromas": true}, {"id": "518021", "num": 21, "name": "Sternenwächterin Neeko (Prestige 2022)", "chromas": false}, {"id": "518022", "num": 22, "name": "Hexerei-<PERSON>eek<PERSON>", "chromas": true}, {"id": "518031", "num": 31, "name": "Straßendämonen-Neeko", "chromas": true}, {"id": "518040", "num": 40, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Neeko stammt aus einem längst verlorenen Stamm der Vastaya und kann in jedem Getümmel untertauchen, indem sie das Aussehen anderer annimmt. Sie erfasst dabei ebenfalls einen Teil des Gefühlszustands, um so blitzsch<PERSON> Freund von <PERSON> zu unterscheiden. <PERSON><PERSON><PERSON> weiß je genau, wo – oder wer – <PERSON><PERSON><PERSON> gerade sein mag, aber wer ihr gegenüber böse <PERSON> hegt, bekommt sehr bald ihr wahres Gesicht zu sehen und die volle Macht der spirituellen Urmagie zu spüren.", "blurb": "Neeko stammt aus einem längst verlorenen Stamm der Vastaya und kann in jedem Getümmel untertauchen, indem sie das Aussehen anderer annimmt. Sie erfasst dabei ebenfalls einen Teil des Gefühlszustands, um so blitzschnell Freund von Feind zu unterscheiden...", "allytips": ["Im Einstellungsmenü kannst du ein Tastenkürzel für ihr Passiv festlegen. Die Standardeinstellung ist Umschalttaste+F1~F5.", "„Angeborener Tarnzauber“ solltest du sparsam e<PERSON>etzen, da nach gescheiterten Versuchen deine Gegner immer misstrauischer werden."], "enemytips": ["Es ist sehr g<PERSON><PERSON><PERSON><PERSON>, dich vor Neeko hinter Vasallen zu verstecken, weil ihre Stachelschlinge immer stärker wird.", "Falls Neeko getarnt ist, sind die visuellen Warnungen der Knallblüte nicht sichtbar."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 1, "magic": 9, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 104, "mp": 450, "mpperlevel": 30, "movespeed": 340, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "NeekoQ", "name": "Blühende Bombe", "description": "<PERSON><PERSON><PERSON> wirft ein <PERSON>, das magischen Schaden verursacht. Das Samenkorn blüht erneut auf, wenn es Champions trifft oder Einheiten tötet.", "tooltip": "Neeko wirft ein Same<PERSON>, das aufblüht und <magicDamage>{{ explosiondamage }}&nbsp;magischen <PERSON>haden</magicDamage> verursacht. Wenn es eine Einheit tötet oder einen Champion oder ein großes Monster trifft, blüht es erneut auf und verursacht <magicDamage>{{ seconddamage }}&nbsp;magischen Schaden</magicDamage>. Max. 2&nbsp;weitere Male möglich.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "Explosionsschaden", "Kosten (@AbilityResourceName@)", "Zusätzlicher Schaden an Monstern", "Abklingzeit"], "effect": ["{{ zonedamage }} -> {{ zonedamageNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ monsterbonus }} -> {{ monsterbonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NeekoQ.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NeekoW", "name": "Formspalter", "description": "Neeko verursacht passiv mit jedem 3.&nbsp;<PERSON><PERSON> zusätzlichen magischen Schaden, während sich parallel ihr Lauftempo kurzzeitig erhöht. Neeko kann durch Aktivierung dieser Fähigkeit einen Klon in eine Richtung ausschicken und die Fähigkeit reaktivieren, um die Richtung des Klons zu ändern.", "tooltip": "<passive>Passiv:</passive> <PERSON><PERSON> 3.&nbsp;Angriff verursacht zusätzlich <magicDamage>{{ passivebonusdamagecalc }}&nbsp;magischen <PERSON></magicDamage> und erhöht {{ passivehasteduration }}&nbsp;Sekunde lang Neekos <speed>Lauftempo um {{ passivehaste }}&nbsp;%</speed>.<br /><br /><active>Aktiv:</active> Neeko wird {{ stealthduration }}&nbsp;Sekunden lang <keywordStealth>unsichtbar</keywordStealth> und erschafft einen Klon, der {{ cloneduration }}&nbsp;Sekunden lang bestehen bleibt. Neeko und der Klon erhalten {{ hasteduration }}&nbsp;Sekunden lang <speed>{{ haste }}&nbsp;% Lauftempo</speed>. <br /><br /><rules>Der Klon kann mit dem Tastenkürzel für „Begleiter bewegen“ oder durch <recast>Reaktivierung</recast> der Fähigkeit gesteuert werden.<br />Der Klon imitiert ihre Zauber, ihre Sticker und ihren Rückruf.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Sc<PERSON>en (Passiv)", "Aktives Lauftempo", "Passives Lauftempo", "Abklingzeit"], "effect": ["{{ passivedamage }} -> {{ passivedamageNL }}", "{{ haste }}&nbsp;% -> {{ hasteNL }}&nbsp;%", "{{ passivehaste }}&nbsp;% -> {{ passivehasteNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NeekoW.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "NeekoE", "name": "Stache<PERSON><PERSON>linge", "description": "<PERSON>eek<PERSON> schle<PERSON>rt eine Stache<PERSON>chlinge, die durchschlagenen Gegnern Schaden zufügt und sie festhält. Wenn die Stachelschlinge einen Gegner tötet oder durch einen Champion fliegt, wird sie größer und schneller und hält sie länger fest.", "tooltip": "<PERSON><PERSON><PERSON> schleudert eine <PERSON>ache<PERSON>chlinge, die <magicDamage>{{ basedamage }}&nbsp;magischen Schaden</magicDamage> verursacht und {{ minrootduration }}&nbsp;Sekunden lang <status>festhält</status>.<br /><br />Nachdem sie einen Gegner getroffen hat, wird die Schlinge verstärkt. Sie wird größer, schneller und <status>hält</status> {{ maxrootduration }}&nbsp;Sekunden lang fest.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Mindestfesthaltedauer", "Verlängerte Festhaltedauer", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ minrootduration }} -> {{ minrootdurationNL }}", "{{ maxrootduration }} -> {{ maxrootdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "NeekoE.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NeekoR", "name": "Knallblüte", "description": "Nach kurzer Vorbereitung springt Neeko in die Luft und schleudert alle Gegner in der Nähe hoch. <PERSON><PERSON> sie landet, erle<PERSON> Gegner in der Nähe Schaden und werden betäubt. Die Vorbereitung geschieht im Verborgenen, falls Neeko getarnt ist.", "tooltip": "Nach kurzer Vorbereitung springt Neeko in die Luft, wodurch alle nahen Gegner {{ delayuntilexplosion }}&nbsp;Sekunden lang <status>hochgeschleudert</status> werden. Dann landet sie mit voller Wucht, fügt allen nahen <PERSON>n <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu und <status>betäubt</status> sie {{ stunduration }}&nbsp;Sekunden lang.<br /><br /><rules>Diese Fähigkeit kann im Geheimen vorbereitet werden, wenn Neeko getarnt ist. {{ delaybeforepassiveremoval }}&nbsp;Sekunden nach dem Wirken dieser Fähigkeit wird Neekos Tarnung aufgehoben.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "NeekoR.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Angeb<PERSON><PERSON>za<PERSON>", "description": "<PERSON><PERSON><PERSON> kann sich als verbündeter Champion oder andere Einheit auf der Karte tarnen. Die Tarnung endet, wenn sie von einem bewegungseinschränkenden Massenkontrolleffekt getroffen wird, schadensverursachende Fähigkeiten ausführt, als Einheit, die kein Champion ist, an gegnerischen Türmen Schaden verursacht oder die Tarnung Schaden in Höhe ihres Lebensbalkens nimmt.", "image": {"full": "Neeko_P.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}