{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "フィズ", "title": "波間のトリックスター", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "アトランティス フィズ", "chromas": false}, {"id": "105002", "num": 2, "name": "ツンドラ フィズ", "chromas": false}, {"id": "105003", "num": 3, "name": "漁師フィズ", "chromas": false}, {"id": "105004", "num": 4, "name": "ヴォイド フィズ", "chromas": false}, {"id": "105008", "num": 8, "name": "もこもこしっぽフィズ", "chromas": false}, {"id": "105009", "num": 9, "name": "超銀河フィズ", "chromas": false}, {"id": "105010", "num": 10, "name": "オメガ小隊フィズ", "chromas": true}, {"id": "105014", "num": 14, "name": "フィヌ", "chromas": false}, {"id": "105015", "num": 15, "name": "プレステージ フィヌ", "chromas": false}, {"id": "105016", "num": 16, "name": "ちび悪魔フィズ", "chromas": true}, {"id": "105025", "num": 25, "name": "プレステージ フィヌ(2022)", "chromas": false}, {"id": "105026", "num": 26, "name": "宇宙飛行士フィズ", "chromas": true}, {"id": "105035", "num": 35, "name": "雨の導き手フィズ", "chromas": true}], "lore": "フィズはビルジウォーターを囲む岩礁の間に住む水陸両性のヨードルだ。彼は迷信深い船長が海に投げる捧げものをくすめては返して遊んでいるが、どんな荒くれの船乗りでも彼を敵に回そうとはしない。というのも、このすばしこい生き物の恐ろしさを侮った者たちの話が数多く伝わっているからだ。気まぐれな海の精霊だと勘違いされがちだが、彼は深海の獣どもを操ることもできるらしく、敵も味方もなく、人々をからかっては楽しんでいる。", "blurb": "フィズはビルジウォーターを囲む岩礁の間に住む水陸両性のヨードルだ。彼は迷信深い船長が海に投げる捧げものをくすめては返して遊んでいるが、どんな荒くれの船乗りでも彼を敵に回そうとはしない。というのも、このすばしこい生き物の恐ろしさを侮った者たちの話が数多く伝わっているからだ。気まぐれな海の精霊だと勘違いされがちだが、彼は深海の獣どもを操ることもできるらしく、敵も味方もなく、人々をからかっては楽しんでいる。", "allytips": ["フィズはユニットをすり抜けて移動できる。レーンではミニオンをすり抜けて移動し、敵に「シートライデント」の自動効果を与えて、数秒待ってから発動効果で追撃しよう。", "アルティメットスキルの「フィッシング」は、敵チャンピオンを直接狙って発射することも、敵が向かうエリアを予測してそこを狙って発射することもできる。", "フィズのスキルは、魔力が高くなるほど強力になる。瞬間火力の高いチームが相手なら「ゾーニャの砂時計」や「バンシーヴェール」を購入しよう。体力を強化する必要がないなら「リッチベイン」や「ラバドン デスキャップ」などもオススメだ。"], "enemytips": ["継続ダメージ効果付きの攻撃をしたあと、フィズの通常攻撃は数秒間強化される。トライデントが光っている間はフィズから距離を取ろう！", "スキルがいつでも使用可能な状態のフィズはなかなか捕まえられない。うまく誘って先にスキルを使わせてから、行動妨害やダメージの大きい攻撃を当てよう！"], "tags": ["Assassin", "Fighter"], "partype": "マナ", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "ウニトゲストライク", "description": "対象に向かって突進し反対側へ突き抜ける。命中した対象に魔法ダメージを与え、通常攻撃時効果を発動する。", "tooltip": "敵に向かって突進して反対側へ突き抜け、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>と<magicDamage>{{ qdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FizzW", "name": "シートライデント", "description": "自動効果で通常攻撃が敵を出血させ、数秒間かけて魔法ダメージを与える。発動すると次の通常攻撃を強化して追加ダメージを与え、少しの間だけその後の通常攻撃が強化される。", "tooltip": "<spellPassive>自動効果</spellPassive>: 通常攻撃で敵を出血させ、{{ bleedduration }}秒かけて<magicDamage>{{ dotdamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><spellActive>発動効果</spellActive>: 次の通常攻撃が追加で<magicDamage>{{ activedamage }}の魔法ダメージ</magicDamage>を与える。この通常攻撃で対象を倒した場合、<scaleMana>{{ onkillmanarefund }}マナ</scaleMana>を回復し、このスキルのクールダウンが{{ onkillnewcooldown }}秒まで短縮される。倒せなかった場合は{{ onhitbuffduration }}秒間、通常攻撃が追加で<magicDamage>{{ onhitbuffdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果ダメージ", "発動効果ダメージ", "通常攻撃時ダメージ", "マナ還元", "@AbilityResourceName@コスト", "クールダウン"], "effect": ["{{ dotbasedamage }} -> {{ dotbasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }} -> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }} -> {{ onkillmanarefundNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FizzE", "name": "プレイ/トリックスター", "description": "地面に槍を突き立て飛び上がった後、槍の上に器用に乗って敵から対象指定されなくなる。この状態からその場に着地、または再度ジャンプして別の指定地点に着地し範囲内の敵にダメージを与える。", "tooltip": "槍の上に飛び乗って0.75秒間、対象指定不可になり、その後周囲の敵に<magicDamage>{{ edamage }}の魔法ダメージ</magicDamage>を与えて、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />対象指定不可の間は、このスキルを<recast>再発動</recast>して再度ダッシュすることができる。再発動するとその時点で対象指定不可の状態は解除され、ダメージ範囲は狭くなり、<status>スロウ効果</status>を付与しなくなる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "@AbilityResourceName@コスト", "ダメージ", "スロウ効果"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "FizzR", "name": "フィッシング", "description": "撒き餌として魚を1匹発射する。魚が命中した敵チャンピオンはスロウ状態になり周囲を魚が旋回する。短時間後、地面から巨大なサメが飛びだして対象をノックアップさせ、周囲の敵を横に跳ね飛ばす。命中した敵は全員魔法ダメージとスロウ効果を受ける。", "tooltip": "魚を発射する。魚は最初に命中したチャンピオンに貼り付き、対象の<keywordStealth>真の視界</keywordStealth>を得る。また、魚は命中するまでに飛んだ距離に応じて、40% - 80%の<status>スロウ効果</status>を付与する。<br /><br />{{ detonationtime }}秒後、 地面からサメが飛び出し、魚の貼り付いた対象を1秒間<status>ノックアップ</status>させ、それ以外を<status>ノックバック</status>させる。また、魚が命中するまでに飛んだ距離に応じて、<magicDamage>{{ smallsharkdamage }} - {{ bigsharkdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "小サメのダメージ", "中サメのダメージ", "大サメのダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ smalldamage }} -> {{ smalldamageNL }}", "{{ middamage }} -> {{ middamageNL }}", "{{ bigdamage }} -> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "シーファイター", "description": "ユニットをすり抜け、すべてのダメージソースから受けるダメージを一定量軽減させる。", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}