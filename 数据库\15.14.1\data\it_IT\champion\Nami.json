{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nami": {"id": "<PERSON><PERSON>", "key": "267", "name": "<PERSON><PERSON>", "title": "lo spirito delle maree", "image": {"full": "Nami.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "267000", "num": 0, "name": "default", "chromas": false}, {"id": "267001", "num": 1, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "267002", "num": 2, "name": "Nami Spirito del Fiume", "chromas": false}, {"id": "267003", "num": 3, "name": "Urf il Namintino", "chromas": false}, {"id": "267007", "num": 7, "name": "Nami delle Profondità", "chromas": false}, {"id": "267008", "num": 8, "name": "Nami SKT T1", "chromas": false}, {"id": "267009", "num": 9, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "267015", "num": 15, "name": "<PERSON><PERSON> Splendido", "chromas": true}, {"id": "267024", "num": 24, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "267032", "num": 32, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "267041", "num": 41, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "267042", "num": 42, "name": "Nami Ritmo Spaziale (edizione prestigio)", "chromas": false}, {"id": "267051", "num": 51, "name": "<PERSON><PERSON> della Congrega", "chromas": true}, {"id": "267058", "num": 58, "name": "Nami Artefice di Miti", "chromas": true}], "lore": "<PERSON><PERSON>, una risoluta vastaya del mare, fu la prima della tribù Marai a lasciare l'acqua per avventurarsi sulla terraferma, quando venne infranto l'antico patto con i targoniani. <PERSON><PERSON><PERSON> dagli eventi, ha deciso di completare da sola il rituale sacro per assicurare la sicurezza del suo popolo. Nel caos di questa nuova era, Nami affronta un futuro incerto con coraggio e determinazione, utilizzando la sua asta dello Spirito delle maree per evocare la forza degli oceani.", "blurb": "<PERSON><PERSON>, una risoluta vastaya del mare, fu la prima della tribù Marai a lasciare l'acqua per avventurarsi sulla terraferma, quando venne infranto l'antico patto con i targoniani. <PERSON><PERSON><PERSON> dagli eventi, ha deciso di completare da sola il rituale sacro per...", "allytips": ["Prigione acquatica ha un tempo di ricarica lungo, assicurati di usarla al momento giusto.", "Usare Flusso e riflusso durante uno scontro con campioni nemici potrebbe invertire le sorti della battaglia in tuo favore.", "L'abilità suprema di Nami può essere utile a ingaggiare campioni lontani."], "enemytips": ["Prigione acquatica è una una potente abilità con un lungo tempo di ricarica, approfittane se Nami l'ha già usata.", "Mareggiata ha una gittata piuttosto lunga ma è lenta, tieni gli occhi aperti quando arriva per poterla evitare.", "Evita di combattere un nemico affetto da Benedizione delle maree. L'effetto dura poco, quindi aspettare può essere la soluzione migliore."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 3, "magic": 7, "difficulty": 5}, "stats": {"hp": 560, "hpperlevel": 88, "mp": 365, "mpperlevel": 43, "movespeed": 335, "armor": 29, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.61, "attackspeed": 0.644}, "spells": [{"id": "NamiQ", "name": "Prigione acquatica", "description": "Lancia una bolla verso un'area bersaglio, infliggendo danni e stordendo i nemici all'impatto.", "tooltip": "Nami lancia una bolla che <status>stordisce</status> per {{ e2 }} secondi e infligge <magicDamage>{{ totaldamagett }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [75, 130, 185, 240, 295], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/130/185/240/295", "1.5", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [875, 875, 875, 875, 875], "rangeBurn": "875", "image": {"full": "NamiQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiW", "name": "Flusso e riflusso", "description": "Emette un getto d'acqua che rimbalza tra campioni alleati e nemici, curando gli alleati e danneggiando i nemici.", "tooltip": "Nami emette un getto d'acqua che rimbalza tra campioni alleati e nemici. Ogni campione può essere colpito solo una volta, e il getto colpisce fino a {{ maxtargets }} bersagli.<li>Ripristina <healing>{{ totalheal }} salute</healing> agli alleati prima di rimbalzare su un campione nemico vicino. <li>Infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici prima di rimbalzare su un campione alleato vicino.<br />I danni e la guarigione vengono modificati di un {{ bouncescaling }} a ogni rimbalzo. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione", "<PERSON><PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725, 725, 725], "rangeBurn": "725", "image": {"full": "NamiW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiE", "name": "Benedizione delle maree", "description": "Potenzia un campione alleato per una breve durata. Gli attacchi base e le abilità dell'alleato infliggono danni magici bonus e rallentano il bersaglio.", "tooltip": "Nami potenzia i prossimi {{ hitcount }} attacchi base e abilità di un campione alleato per {{ buffduration }} secondi, permettendogli di <status>rallentare</status> il bersaglio di un {{ totalslow }} per {{ slowduration }} secondo/i e infliggere <magicDamage>{{ totaldamage }} danni magici</magicDamage> aggiuntivi. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslow }}% -> {{ baseslowNL }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [15, 20, 25, 30, 35], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "15/20/25/30/35", "1", "3", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NamiE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NamiR", "name": "Maregg<PERSON><PERSON>", "description": "Evoca un'immensa mareggiata che sbalza in aria, rallenta e danneggia i nemici. Gli alleati colpiti ottengono il doppio dell'effetto di Maree dirompenti.", "tooltip": "Nami evoca un'onda anomala che <status>lancia in aria</status> per 0,5 secondi, <status>rallenta</status> di un {{ e4 }}% e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>. La durata del <status>rallentamento</status> aumenta in base alla distanza coperta dall'onda, fino a un massimo di {{ e5 }} secondi.<br /><br />Gli alleati colpiti dall'onda ottengono il doppio dell'effetto di <spellName><PERSON><PERSON> dirompenti</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.5, 0.5, 0.5], [2, 2, 2], [70, 70, 70], [4, 4, 4], [0.002, 0.002, 0.002], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.5", "2", "70", "4", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2550, 2550, 2550], "rangeBurn": "2550", "image": {"full": "NamiR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Quando le abilità di Nami colpiscono un campione alleato, quest'ultimo guadagna velocità di movimento per un breve periodo di tempo.", "image": {"full": "NamiPassive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}