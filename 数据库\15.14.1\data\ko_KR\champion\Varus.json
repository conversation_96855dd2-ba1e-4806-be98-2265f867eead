{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Varus": {"id": "Varus", "key": "110", "name": "바루스", "title": "응징의 화살", "image": {"full": "Varus.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "110000", "num": 0, "name": "default", "chromas": false}, {"id": "110001", "num": 1, "name": "역병 수정 바루스", "chromas": false}, {"id": "110002", "num": 2, "name": "빛의 바루스", "chromas": false}, {"id": "110003", "num": 3, "name": "설원 특공대 바루스", "chromas": false}, {"id": "110004", "num": 4, "name": "사랑의 추적자 바루스", "chromas": false}, {"id": "110005", "num": 5, "name": "날쌘 화살 바루스", "chromas": false}, {"id": "110006", "num": 6, "name": "암흑의 별 바루스", "chromas": true}, {"id": "110007", "num": 7, "name": "정복자 바루스", "chromas": true}, {"id": "110009", "num": 9, "name": "지옥의 바루스", "chromas": true}, {"id": "110016", "num": 16, "name": "프로젝트: 바루스", "chromas": true}, {"id": "110017", "num": 17, "name": "우주 사냥꾼 바루스", "chromas": true}, {"id": "110034", "num": 34, "name": "하이 눈 바루스", "chromas": true}, {"id": "110044", "num": 44, "name": "서리달 바루스", "chromas": true}, {"id": "110053", "num": 53, "name": "창공 바루스", "chromas": true}, {"id": "110060", "num": 60, "name": "영혼의 꽃 바루스", "chromas": true}], "lore": "바루스는 고대 다르킨 중 한 명으로, 적이 거의 미쳐 버릴 정도로 고통을 준 뒤에야 화살을 날려 숨통을 끊는 것을 즐기는 치명적인 암살자이다. 다르킨 전쟁 말미에는 갇히는 신세가 되었으나 몇백 년 후 아이오니아 사냥꾼 두 명의 육신을 빼앗는 방식으로 탈출했다. 두 사냥꾼은 자신들도 모르게 바루스를 풀어준 형국이 되었고 이제는 그의 육신이 되어 바루스의 정기가 갇힌 활을 짊어질 수밖에 없는 운명이 되었다. 바루스는 이제 잔인한 복수를 위해 자신을 가둔 자를 찾아다니지만, 내면에 있는 필멸자들의 영혼이 바루스에게 끈질기게 저항한다.", "blurb": "바루스는 고대 다르킨 중 한 명으로, 적이 거의 미쳐 버릴 정도로 고통을 준 뒤에야 화살을 날려 숨통을 끊는 것을 즐기는 치명적인 암살자이다. 다르킨 전쟁 말미에는 갇히는 신세가 되었으나 몇백 년 후 아이오니아 사냥꾼 두 명의 육신을 빼앗는 방식으로 탈출했다. 두 사냥꾼은 자신들도 모르게 바루스를 풀어준 형국이 되었고 이제는 그의 육신이 되어 바루스의 정기가 갇힌 활을 짊어질 수밖에 없는 운명이 되었다. 바루스는 이제 잔인한 복수를 위해 자신을 가둔...", "allytips": ["초반에 역병 화살에 투자하면 적 챔피언을 견제하고 미니언에게 최후의 일격을 가하기 쉽습니다.", "단거리 교전 시 꿰뚫는 화살을 사용하려면, 시간을 들여 위력을 높이기보다 빠르게 발사하는 편이 좋습니다.", "꿰뚫는 화살은 사거리가 매우 길기 때문에, 교전 시작 전에 적 챔피언들을 저격하거나 달아나는 적을 처리하는 데 활용하세요."], "enemytips": ["역병에 감염된 경우 바루스의 스킬 공격으로 추가 피해를 입을 수 있습니다.", "적을 처치하거나 어시스트를 올리면 잠시 바루스의 공격 속도가 상승해 훨씬 위협적이 됩니다.", "바루스의 궁극기 부패의 사슬에 닿으면 그 자리에 속박됩니다. 하지만 멀리 달아나면 촉수는 죽습니다."], "tags": ["Marksman", "Mage"], "partype": "마나", "info": {"attack": 7, "defense": 3, "magic": 4, "difficulty": 2}, "stats": {"hp": 600, "hpperlevel": 105, "mp": 320, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.4, "attackspeedperlevel": 3.5, "attackspeed": 0.658}, "spells": [{"id": "VarusQ", "name": "꿰뚫는 화살", "description": "바루스가 조준한 다음 강화된 화살을 발사하면, 조준한 시간에 비례하여 사거리와 피해량이 늘어납니다.", "tooltip": "<attention>충전 시작 시:</attention> 바루스가 다음 화살을 조준하며 {{ e7 }}% <status>둔화</status>됩니다. {{ e5 }}초가 경과한 후 쏘지 않으면 바루스가 스킬을 취소하고 소모한 마나의 {{ e4 }}%를 돌려받습니다.<br /><br /><attention>발사 시:</attention> 바루스가 화살을 발사하여 <physicalDamage>{{ totaldamagemintooltip }}의 물리 피해</physicalDamage>를 입힙니다. 관통당한 적 하나당 {{ e3 }}%씩 피해량이 감소합니다. (최소 {{ e9 }}%) 피해량과 <keywordMajor>역병</keywordMajor> 폭발 효과는 충전 시간에 비례해 최대 {{ maxchargeamp*100 }}%까지 증가합니다. (최대 <physicalDamage>{{ totaldamagemax }}</physicalDamage>){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최대 피해량", "추가 공격력 %", "재사용 대기시간", "마나 소모량"], "effect": ["{{ basedamagemax }} -> {{ basedamagemaxNL }}", "{{ tadratiomax }} -> {{ tadratiomaxNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [3, 3, 3, 3, 3], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [33, 33, 33, 33, 33], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "15", "50", "4", "3", "20", "0", "33", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusQ.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VarusW", "name": "역병 화살", "description": "기본 지속 효과: 바루스의 기본 공격이 추가 마법 피해를 입히고 역병을 붙입니다. 바루스가 이 대상을 다른 스킬로 공격하면 역병이 폭발하며 대상의 최대 체력에 비례한 마법 피해를 입힙니다. 사용 시: 다음번 꿰뚫는 화살이 강화됩니다.", "tooltip": "<spellPassive>기본 지속 효과: </spellPassive>바루스의 기본 공격이 <magicDamage>{{ onhitdamage }}의 마법 피해</magicDamage>를 추가로 입히고 {{ e3 }}초 동안 <keywordMajor>역병</keywordMajor> 중첩을 적용합니다. (최대 {{ e4 }}회 중첩)<br /><br />바루스가 다른 스킬을 사용해 <keywordMajor>역병</keywordMajor> 중첩을 폭발시키면 중첩 횟수당 <magicDamage>최대 체력의 {{ percenthpperstack }}에 해당하는 마법 피해</magicDamage>를 입힙니다. (최대 피해량: <magicDamage>최대 체력의 {{ maxpercenthpperstack }}</magicDamage>) 챔피언과 에픽 몬스터를 대상으로 <keywordMajor>역병</keywordMajor>을 폭발시키면 기본 스킬의 재사용 대기시간이 중첩 횟수당 최대 재사용 대기시간의 {{ cdrperblightstack*100 }}%만큼 감소합니다.<br /><br /><spellActive>사용 시:</spellActive> 다음 <spellName>꿰뚫는 화살</spellName> 스킬이 <magicDamage>잃은 체력의 {{ qempowerpercenthp }}에 해당하는 마법 피해</magicDamage>를 추가로 입힙니다. 피해량은 충전 시간에 비례하여 <magicDamage>잃은 체력의 {{ maxqempowerpercenthp }}</magicDamage>까지 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["공격 시 피해량", "적 최대 체력 비례 피해량", "잃은 체력 비례 피해량"], "effect": ["{{ varuswonhitdamage }} -> {{ varuswonhitdamageNL }}", "{{ basepercenthpperstack*100.000000 }}% -> {{ basepercenthpperstacknl*100.000000 }}%", "{{ wqhealthdamage*100.000000 }}% -> {{ wqhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [40, 40, 40, 40, 40], "cooldownBurn": "40", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0.03, 0.035, 0.04, 0.045, 0.05], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [120, 120, 120, 120, 120], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0.03/0.035/0.04/0.045/0.05", "6", "3", "120", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VarusW.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "VarusE", "name": "퍼붓는 화살", "description": "바루스가 화살을 비처럼 쏟아부어 물리 피해를 주고 지면을 오염시킵니다. 오염된 지면은 적의 이동 속도를 늦추며 자신에 대한 체력 회복 및 재생 효과를 감소시킵니다.", "tooltip": "바루스가 화살을 비처럼 쏟아부어 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 {{ e3 }}초 동안 지면을 오염시켜 적을 {{ slowpercent*-100 }}% <status>둔화</status>시키며 {{ grievousamount*100 }}%의 고통스러운 상처를 남깁니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent*-100.000000 }}% -> {{ slowpercentnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [60, 100, 140, 180, 220], [-0.3, -0.35, -0.4, -0.45, -0.5], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [300, 300, 300, 300, 300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/100/140/180/220", "-0.3/-0.35/-0.4/-0.45/-0.5", "4", "0", "300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "VarusE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VarusR", "name": "부패의 사슬", "description": "바루스가 부패의 촉수를 발사하여 처음 맞은 적 챔피언의 이동을 차단합니다. 촉수는 주변의 감염되지 않은 챔피언들에게 계속 뻗어 나가 닿은 적들을 모두 이동 불가 상태로 만듭니다.", "tooltip": "바루스가 부패의 촉수를 발사해 처음 맞은 챔피언을 {{ e2 }}초 동안 <status>속박</status>하고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. <status>속박</status>된 적은 지속시간에 걸쳐 <keywordMajor>역병</keywordMajor> 중첩이 {{ e4 }}회 쌓입니다.<br /><br />촉수는 대상으로부터 감염되지 않은 적 챔피언에게 뻗어 나가, 닿은 적에게 동일한 양의 피해를 입히고 <status>속박</status>합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 80, 60], "cooldownBurn": "100/80/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [2, 2, 2], [650, 650, 650], [3, 3, 3], [0.5, 0.5, 0.5], [600, 600, 600], [1.75, 1.75, 1.75], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "2", "650", "3", "0.5", "600", "1.75", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "VarusR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "죽지 않는 복수심", "description": "적을 처치하거나 어시스트를 올리면 잠시 바루스의 공격력과 주문력이 상승합니다. 대상이 챔피언인 경우 공격력과 주문력이 더 큰 폭으로 상승합니다.", "image": {"full": "VarusPassive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}