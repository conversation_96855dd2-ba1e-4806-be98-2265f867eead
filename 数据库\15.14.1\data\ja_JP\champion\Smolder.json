{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Smolder": {"id": "<PERSON><PERSON>lder", "key": "901", "name": "スモルダー", "title": "炎の幼龍", "image": {"full": "Smolder.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "901000", "num": 0, "name": "default", "chromas": false}, {"id": "901001", "num": 1, "name": "天なる龍スモルダー", "chromas": true}], "lore": "ノクサスの辺境にある岩だらけの崖に身を隠し、幼きドラゴンは母親に見守られながら、カマヴォールのインペリアルドラゴンの継承者とは何たるかを学んでいる。遊び好きで意欲旺盛なスモルダーは急成長を遂げており、能力を磨けるならどんな機会も逃さない。まだ幼くともスモルダーの力は決して侮れず、火のつくものなら何でも簡単に燃やしてしまう。", "blurb": "ノクサスの辺境にある岩だらけの崖に身を隠し、幼きドラゴンは母親に見守られながら、カマヴォールのインペリアルドラゴンの継承者とは何たるかを学んでいる。遊び好きで意欲旺盛なスモルダーは急成長を遂げており、能力を磨けるならどんな機会も逃さない。まだ幼くともスモルダーの力は決して侮れず、火のつくものなら何でも簡単に燃やしてしまう。", "allytips": ["試合序盤のスモルダーは脆いので、固有スキルのスタックを得ることに集中し、強いドラゴンになるまで生き延びよう！", "スモルダーの活躍は、味方に守ってもらえるかにかかっている。できるだけ敵の攻撃から守ってくれる味方の側にいよう。", "スモルダーは強力な範囲攻撃を持っている。敵が集まっている場所を狙って攻撃を仕掛けよう。"], "enemytips": ["スモルダーの活躍は、味方に守ってもらえるかにかかっている。味方がスモルダーを守れない状況を狙って攻撃しよう。", "スモルダーと対峙するときは、味方同士で1カ所に集まらないようにしよう。", "試合序盤のスモルダーは非常に脆い。スモルダーが真のドラゴンとなる前に、この弱点を突いてリードを築こう！", "スモルダーの飛翔は強力な行動妨害効果であれば中断させることができ、スロウ効果の影響も受ける。"], "tags": ["Marksman", "Mage"], "partype": "マナ", "info": {"attack": 8, "defense": 2, "magic": 5, "difficulty": 6}, "stats": {"hp": 575, "hpperlevel": 100, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 8.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.3, "attackspeedperlevel": 4, "attackspeed": 0.638}, "spells": [{"id": "SmolderQ", "name": "スーパーこげこげブレス", "description": "敵1体に炎のブレスを吐きかける。スタックを獲得するほど、このスキルが強化されていく。", "tooltip": "炎を吐き出し、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_qdamageincrease }}の魔法ダメージ</magicDamage>を与える。対象が倒された場合は、一度のスキル使用につき<scaleMana>マナを{{ manarestore }}</scaleMana>回復する。<br /><br />このスキルは<spellName>「駆けだしドラゴン」</spellName>のスタック数に応じて進化し、以下の効果を獲得する:<li><keywordMajor>{{ stacktier1 }}スタック</keywordMajor>:対象の周囲のすべての敵にダメージを与える。<li><keywordMajor>{{ stacktier2 }}スタック</keywordMajor>:対象の向こう側にも<spellName>{{ tier2_numberofblowback }}</spellName>発の爆発を起こし、このスキルのダメージの{{ tier2_blowbackpercentagedamage }}%を与える。<li><keywordMajor>{{ stacktier3 }}スタック</keywordMajor>:対象を炎上させ、{{ tier3_dotlength }}秒かけて<trueDamage>最大体力の{{ tier3_burn }}の確定ダメージ</trueDamage>を与える。炎上中の敵チャンピオンは、体力が最大値の<trueDamage>{{ tier3_executethreshold }}</trueDamage>未満になると即座にデスする。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [5.5, 5, 4.5, 4, 3.5], "cooldownBurn": "5.5/5/4.5/4/3.5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SmolderQ.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SmolderW", "name": "くしゅん！", "description": "可愛らしいくしゃみと共に炎を吐く。この炎は敵チャンピオンに命中すると爆発する。", "tooltip": "可愛らしいくしゃみと共に炎を吐き、<physicalDamage>{{ initialdamage }}の物理ダメージ</physicalDamage>を与えて、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />チャンピオンに命中すると爆発が起こり、<physicalDamage>{{ explosiondamage }}の物理ダメージ</physicalDamage> + <magicDamage>{{ spell.smolderp:passive_wdamageincrease }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "爆発ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "SmolderW.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SmolderE", "name": "パタパタパタ", "description": "飛翔して地形を無視するようになり、最も体力が低い敵を空から攻撃する。", "tooltip": "{{ duration }}秒間飛翔して<speed>移動速度が{{ movespeed*100 }}%</speed>増加し、地形を無視するようになる。<br /><br />飛翔中は最も体力が低い敵を空から<spellName>{{ totalnumberofattacks }}</spellName>回(端数は切り捨て)攻撃して、命中するごとに<physicalDamage>{{ damageperhit }}の物理ダメージ</physicalDamage> + <magicDamage>{{ spell.smolderp:ebonusdamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "基本ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [24, 22, 20, 18, 16], "cooldownBurn": "24/22/20/18/16", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "SmolderE.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SmolderR", "name": "ママーッ！！", "description": "母ドラゴンを呼び寄せて、上空から炎を吐いてもらう。炎の中心部にいる敵には、追加ダメージとスロウ効果を与える。", "tooltip": "母ドラゴンが上空から炎を吐き、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。中心部にいる敵には<physicalDamage>{{ tooltiponly_totalsweetspotdamage }}の物理ダメージ</physicalDamage>を与え、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を付与する。<br /><br />スモルダーはこの炎を受けると、<healing>体力が{{ momhealcalc }}</healing>回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "体力回復量", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ momheal }} -> {{ momhealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [4200, 4200, 4200], "rangeBurn": "4200", "image": {"full": "SmolderR.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "駆けだしドラゴン", "description": "スキルをチャンピオンに命中させる、または「スーパーこげこげブレス」で敵をキルすると、「駆けだしドラゴン」のスタックを1つ獲得する。スタック数に応じて通常スキルのダメージが増加する。", "image": {"full": "Icons_Smolder_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}