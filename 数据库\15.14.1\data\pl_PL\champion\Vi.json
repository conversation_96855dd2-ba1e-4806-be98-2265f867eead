{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vi": {"id": "Vi", "key": "254", "name": "Vi", "title": "Stróż Prawa z Piltover", "image": {"full": "Vi.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "254000", "num": 0, "name": "default", "chromas": false}, {"id": "254001", "num": 1, "name": "Neonowa Vi", "chromas": false}, {"id": "254002", "num": 2, "name": "Posterunkowa Vi", "chromas": true}, {"id": "254003", "num": 3, "name": "Wytworna Vi", "chromas": false}, {"id": "254004", "num": 4, "name": "Demoniczna Vi", "chromas": false}, {"id": "254005", "num": 5, "name": "Vi z Walczących Królestw", "chromas": false}, {"id": "254011", "num": 11, "name": "PROJEKT: Vi", "chromas": false}, {"id": "254012", "num": 12, "name": "Wyrywająca Serca Vi", "chromas": true}, {"id": "254020", "num": 20, "name": "Vi z Psychoperacji", "chromas": true}, {"id": "254029", "num": 29, "name": "Vi z Miasta pod Miastem z Arcane", "chromas": false}, {"id": "254030", "num": 30, "name": "Vi ze Złaman<PERSON>", "chromas": true}, {"id": "254039", "num": 39, "name": "Vi Pierwotnej Z<PERSON>dzki", "chromas": true}, {"id": "254048", "num": 48, "name": "Zabijaka Vi z Arcane", "chromas": false}], "lore": "Wychowana na niebezpiecznych ulicach Zaun Vi jest impulsywną, gwałtowną i nieustraszoną kobietą, ni<PERSON><PERSON><PERSON><PERSON>ą zbyt wielkiego szacunku do władz. Jej przebie<PERSON>ł<PERSON>ć od zawsze pozwalała jej <PERSON>, czy to podczas jej młodzieńczych wybryków, czy niesprawiedliwie długiego pobytu w Twierdzy Stillwater. Teraz pracuje dla stróżów prawa Piltover, pilnując pokoju zamiast go burzyć. Nosi potężne hextechowe rękawice, które mogą z równą łatwością przebijać ściany i wbijać rozum do głów przestępcom.", "blurb": "Wychowana na niebezpiecznych ulicach Zaun Vi jest impulsywną, gwałtowną i nieustraszoną kobietą, niemają<PERSON>ą zbyt wielkiego szacunku do władz. Jej przebie<PERSON>ł<PERSON>ć od zawsze pozwalała jej p<PERSON>, czy to podczas jej młodzieńczych wybryków, czy...", "allytips": ["W pełni naładowany Niszczyciel Skarbca zada podwójne obrażenia. Idealnie nadaje się do łapania i wykańczania uciekających wrogów.", "Bezlitosna Siła zadaje pełne obrażenia wszystkim w zasięgu fali uderzeniowej. Użyj jej na stworach w alei, by <PERSON><PERSON><PERSON><PERSON> się za nimi wrogów.", "<PERSON><PERSON> Stawiaj Oporu to potężne narzędzie do rozpoczynania walki. Pamiętaj tylko, by nie poz<PERSON><PERSON><PERSON><PERSON> reszty drużyny zbyt daleko w tyle."], "enemytips": ["Naładowany Niszczyciel Skarbca zadaje podwójne obrażenia. Gdy Vi zaczyna nabierać sił, odsuń się i zrób unik.", "Vi zniszczy twoją zbroję i nabierze prędkości ataku, jeśli uda jej się uderzyć cię trzy razy z rzędu. Spróbuj nie wdawać się z nią w dłuższe potyczki.", "Vi nie może by<PERSON> zatrzymana podczas korzystania z superumiejętności. Pam<PERSON>ętaj, by <PERSON><PERSON><PERSON><PERSON> efekty utraty kontroli do czasu, gdy skończy szarżę."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 295, "mpperlevel": 65, "movespeed": 340, "armor": 30, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 10, "hpregenperlevel": 1, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "ViQ", "name": "Niszczyciel Skarbca", "description": "Vi ładuje rękawice i zadaje roztrzaskujący skarbiec cios, którego siła przesuwa ją naprzód. Trafieni przeciwnicy zostają odrzuceni i otrzymują ładunek Wgniatających Uderzeń.", "tooltip": "<charge>Ładowanie:</charge> Vi przygotowuje potężne uderzenie, <status>spowal<PERSON><PERSON><PERSON>c się</status> o {{ e4 }}%.<br /><br /><release>Wypuszczenie:</release> Vi doskakuje do przodu, zadając <physicalDamage>{{ totaldamage }}-{{ maxdamagetooltip }} pkt. obrażeń fizycznych</physicalDamage> w zależności od czasu ładowania i nakładając efekt <spellName>Wgniatających Uderzeń</spellName> na wszystkich trafionych wrogów. Vi zatrzymuje się przy zderzeniu z wrogim bohaterem, <status>odr<PERSON><PERSON>ją<PERSON></status> go <status>do tyłu</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Minimalne obrażenia", "Maksymalne obrażenia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ mindamage*2.500000 }} -> {{ mindamagenl*2.500000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "15", "6", "5", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ViQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ViW", "name": "Wgniatające Uderzenia", "description": "Ciosy Vi niszczą pancerz jej nieprzy<PERSON>la, zadając mu dodatkowe obrażenia i dając jej pręd<PERSON><PERSON><PERSON> ataku.", "tooltip": "<spellPassive>Biernie:</spellPassive> Każdy co 3. atak trafiający ten sam cel zadaje mu dodatkowo <physicalDamage>obrażenia fizyczne równe {{ totaldamagetooltip }} jego maks. zdrowia</physicalDamage>, os<PERSON><PERSON> jego <scaleArmor>pancerz o {{ shredamount }}%</scaleArmor> i zapewnia Vi <attackSpeed>{{ attackspeed }}% prędkości ataku</attackSpeed> na {{ sharedbuffsduration }} sek. Dodatkowo skraca pozostały czas odnowienia <spellName><PERSON><PERSON><PERSON>io<PERSON></spellName> o {{ spell.vipassive:cdreductionon3hit }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia od maksymalnego zdrowia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ attackspeed }}% -> {{ attackspeedNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "ViE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Kolejny atak Vi przechodzi przez cel i zadaje obrażenia wrogom znajdującym się za nim.", "tooltip": "Następny atak Vi zadaje <physicalDamage>{{ totaldamagetooltip }} pkt. obrażeń fizycznych</physicalDamage> celowi i wrogom znajdującym się za nim.<br /><br />Ta umiejętność ma 2 ładunki ({{ ammorechargetime }} sek. odnowienia).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe", "<PERSON>zas ładowania", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [26, 32, 38, 44, 50], "costBurn": "26/32/38/44/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1.5", "6", "1", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "2", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ViR", "name": "<PERSON><PERSON>", "description": "Podczas pogoni za wrogiem, Vi odrzuca na boki wszystkich nieprzyjaciół stojących jej na drodze. Kiedy dociera do wroga, wyrzuca go w powietrze, skacze za nim i ciska nim z powrotem o ziemię.", "tooltip": "Vi obiera za cel wrogiego bohatera, odk<PERSON>wa go i niepowstrzymanie doskakuje w jego stronę. Po dotarciu do celu Vi <status>podrzuca</status> go na {{ rstunduration }} sek. i zadaje <physicalDamage>{{ damage }} pkt. obrażeń fizycznych</physicalDamage>.<br /><br />Pozostali wrogowie, z którymi zderzy się Vi, otr<PERSON><PERSON><PERSON><PERSON> obraż<PERSON>, a także zostają odrzuceni na bok i <status>ogłuszeni</status> na {{ secondarytargetstunduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia podstawowe", "Czas odnowienia"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ViR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON><PERSON> Przeciwuderzenio<PERSON>", "description": "W miarę upływu czasu tarcza Vi się ładuje. Tarcza jest aktywowana przez zaatakowanie wroga umiejętnością.", "image": {"full": "ViPassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}