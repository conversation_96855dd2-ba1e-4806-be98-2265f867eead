{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yorick": {"id": "<PERSON><PERSON>", "key": "83", "name": "ヨリック", "title": "魂の導き手", "image": {"full": "Yorick.png", "sprite": "champion5.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "83000", "num": 0, "name": "default", "chromas": false}, {"id": "83001", "num": 1, "name": "墓守ヨリック", "chromas": false}, {"id": "83002", "num": 2, "name": "Pentakill ヨリック", "chromas": false}, {"id": "83003", "num": 3, "name": "アークライト ヨリック", "chromas": false}, {"id": "83004", "num": 4, "name": "ミャオリック", "chromas": true}, {"id": "83012", "num": 12, "name": "レジスタンス ヨリック", "chromas": true}, {"id": "83021", "num": 21, "name": "Pentakill III: ロストチャプター ヨリック", "chromas": true}, {"id": "83030", "num": 30, "name": "精霊の花祭りヨリック", "chromas": true}, {"id": "83040", "num": 40, "name": "ダークスター ヨリック", "chromas": true}, {"id": "83050", "num": 50, "name": "荒野のヨリック", "chromas": true}], "lore": "ヨリックは忘れ去られて久しいある教団の修道士として唯一生き残った。彼は死者を操ることができるが、その力は恵みであり、また呪いでもある。シャドウアイルに囚われた彼の仲間と呼べるのは、朽ちた屍と、甲高い叫び声を上げながら集まってくる亡霊のみだ。ヨリックの恐ろしい行いは、「破滅」の呪いから故郷を解放したいという彼の崇高な決意と相反するようにも見える。", "blurb": "ヨリックは忘れ去られて久しいある教団の修道士として唯一生き残った。彼は死者を操ることができるが、その力は恵みであり、また呪いでもある。シャドウアイルに囚われた彼の仲間と呼べるのは、朽ちた屍と、甲高い叫び声を上げながら集まってくる亡霊のみだ。ヨリックの恐ろしい行いは、「破滅」の呪いから故郷を解放したいという彼の崇高な決意と相反するようにも見える。", "allytips": ["「葬送」を再び使用するために、「目覚め」を使用する必要はない。", "戦闘中、「霧の乙女」はヨリックを援護しようとするので、攻撃する対象を賢く選択しよう。", "「霧の乙女」を一人でレーンの先に送り込むことも可能だが、彼女はヨリックの戦闘力の大部分を担う存在なので、十分注意しておこう。"], "enemytips": ["「ミストウォーカー」と「霧の乙女」は「スマイト」でダメージを与えて倒すことができる。", "ヨリックと戦う前に彼が召喚したユニットの数を減らしておこう。「ミストウォーカー」は通常攻撃もしくは単体対象スキルで倒すことができる。", "「屍の列」は攻撃すれば破壊できる。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 6, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 36, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON><PERSON>", "name": "葬送", "description": "次に行う通常攻撃が追加ダメージを与えて自身を回復する。対象がチャンピオンか大型モンスターだった場合、または対象が倒された場合は墓が掘られる。", "tooltip": "次の通常攻撃が追加で<physicalDamage>{{ bonusdamage }}の物理ダメージ</physicalDamage>を与え、<healing>{{ qheal }} + 自身の減少体力の{{ missinghealthratio }}%</healing>の体力を回復する。チャンピオン以外に対しては回復量が{{ healreduction }}%低下する。この通常攻撃がチャンピオンまたは大型モンスターに命中するか、対象を倒すと、墓が残る。<br /><br />周囲に3つ以上の墓が存在し、このスキルが使用されていた場合は、<recast>再発動</recast>すると周囲の墓から<keywordMajor>「ミストウォーカー」</keywordMajor>を召喚できる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["追加ダメージ", "体力回復量", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ missinghealthratio }}% -> {{ missinghealthratioNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YorickQ.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "YorickW", "name": "屍の列", "description": "指定した地点に破壊可能な壁を召喚し、敵ユニットの動きを阻止する。", "tooltip": "霊の壁を召喚する。敵はこの壁を通り抜けられないが、味方は通り抜けられる。壁の<healing>体力は{{ wallhealthtooltip }}</healing>で、{{ circleduration }}秒後に消滅する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "体力"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wallhealthtooltip }} -> {{ wallhealthtooltipNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "YorickW.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "<PERSON><PERSON>E", "name": "悲嘆の霧", "description": "「黒き霧」の塊を投げつけて物理防御を低下させ、ダメージとスロウ効果を与え、対象をマークする。召喚したユニットはマークされた対象に向かう際は移動速度が増加する。 ", "tooltip": "「黒き霧」の塊を投げつけて<magicDamage>最大体力の{{ calc_healthdamage }}にあたる魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ calc_slow }}の<status>スロウ効果</status>を付与し、{{ markduration }}秒間チャンピオンとモンスターをマークする。マークされた敵は継続的に周囲の墓を<spellName>「目覚め」</spellName>させ(最大召喚数である{{ spell.yorickpassive:yorickpassiveghoulmax }}体は越えない)、<scaleArmor>物理防御が{{ armorshred*100 }}%低下</scaleArmor>する。<br /><br />自身と召喚したユニットは、マークされた対象に向かう際に<speed>移動速度が{{ hasteamount*100 }}%</speed>増加する。<keywordMajor>「ミストウォーカー」</keywordMajor>はマークされた敵が逃げると1回だけ飛びついて追いかける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["物理防御低下率", "移動速度", "クールダウン", "@AbilityResourceName@コスト", "最大体力ダメージ"], "effect": ["{{ armorshred*100.000000 }}% -> {{ armorshrednl*100.000000 }}%", "{{ hasteamount*100.000000 }}% -> {{ hasteamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ healthdamage }}% -> {{ healthdamageNL }}%"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YorickE.png", "sprite": "spell16.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Yo<PERSON>R", "name": "嘆きの墓標", "description": "ヨリックが「霧の乙女」を召喚する。「霧の乙女」が攻撃している対象を自身が攻撃すると追加ダメージを与える。「霧の乙女」は倒された敵から自動的に「ミストウォーカー」を召喚する。", "tooltip": "<healing>体力{{ yorickbigghoulhealth }}</healing>、魔法攻撃力<magicDamage>{{ yorickbigghouldamage }}</magicDamage>の<keywordMajor>「霧の乙女」</keywordMajor>と{{ rghoulnumbers }}体の<keywordMajor>「ミストウォーカー」</keywordMajor>を召喚する。<keywordMajor>「霧の乙女」</keywordMajor>は周囲で倒された敵から自動的に<keywordMajor>「ミストウォーカー」</keywordMajor>を召喚し、攻撃時に敵チャンピオンをマークする。<keywordMajor>「霧の乙女」</keywordMajor>が攻撃している対象を自身が攻撃すると、<magicDamage>最大体力の{{ rmarkdamagepercent }}%にあたる魔法ダメージ</magicDamage>を与える。<br /><br />10秒後、このスキルを<recast>再発動</recast>すると<keywordMajor>「霧の乙女」</keywordMajor>が解放されて、最も近いレーンを進んでいく。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃力", "マークされた対象へのダメージ", "ミストウォーカー", "クールダウン"], "effect": ["{{ rbigghoulbonusad }} -> {{ rbigghoulbonusadNL }}", "{{ rmarkdamagepercent }}% -> {{ rmarkdamagepercentNL }}%", "{{ rghoulnumbers }} -> {{ rghoulnumbersNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [160, 130, 100], "cooldownBurn": "160/130/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "YorickR.png", "sprite": "spell16.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "魂の導き手", "description": "<font color='#FF9900'>呪われし一群:</font> 周囲の敵に襲いかかり攻撃する「ミストウォーカー」を召喚する。", "image": {"full": "Yo<PERSON>_P.png", "sprite": "passive5.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}