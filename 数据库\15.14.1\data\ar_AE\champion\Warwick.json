{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "وارويك", "title": "غض<PERSON> زون المتحرر", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "وارويك الرمادي", "chromas": true}, {"id": "19002", "num": 2, "name": "أور<PERSON> خ<PERSON><PERSON><PERSON> الب<PERSON>ر", "chromas": false}, {"id": "19003", "num": 3, "name": "وارويك السيئ الضخم", "chromas": false}, {"id": "19004", "num": 4, "name": "وارويك صياد التندرا", "chromas": false}, {"id": "19005", "num": 5, "name": "وارويك الشرس", "chromas": false}, {"id": "19006", "num": 6, "name": "وارويك مخلب النار", "chromas": false}, {"id": "19007", "num": 7, "name": "وارويك الضبع", "chromas": false}, {"id": "19008", "num": 8, "name": "وارويك المغير", "chromas": false}, {"id": "19009", "num": 9, "name": "أورفويك", "chromas": false}, {"id": "19010", "num": 10, "name": "وارويك الحارس القمري", "chromas": true}, {"id": "19016", "num": 16, "name": "وارويك بروجيكت", "chromas": true}, {"id": "19035", "num": 35, "name": "وارويك الزعيم القديم", "chromas": false}, {"id": "19045", "num": 45, "name": "وارويك مباركة الشتاء", "chromas": false}, {"id": "19046", "num": 46, "name": "وارويك مباركة الشتاء برستيج", "chromas": false}, {"id": "19056", "num": 56, "name": "وارويك فاندر آركين", "chromas": false}], "lore": "وارويك وحشٌ يجوب الأزقة المظلمة في زون بحثًا عن فرائسه. بعد تجارب مروعة، اندمج جسد وارويك بنظام معقد من التروس والمضخات، حيث تضخ الآلات غضبًا كيميائيًا يسري في عروقه. يندفع من بين الظلال ليفترس أولئك المجرمين الذين ينشرون الرعب في أعماق المدينة. ينجذب وارويك إلى الدماء، ويصاب بالجنون من رائحتها... فلا ينجو منه من يجرؤ على إراقتها.", "blurb": "وارويك وحشٌ يجوب الأزقة المظلمة في زون بحثًا عن فرائسه. بعد تجارب مروعة، اندمج جسد وارويك بنظام معقد من التروس والمضخات، حيث تضخ الآلات غضبًا كيميائيًا يسري في عروقه. يندفع من بين الظلال ليفترس أولئك المجرمين الذين ينشرون الرعب في أعماق المدينة. ينجذب...", "allytips": ["اتبع آثار صيد الدم للوصول إلى أبطال العدو منخفضي الصحة.", "تتزايد مسافة قدرة الإكراه اللانهائي (R) مع أي سرعة حركة تكسبها، حتى من محفزات الحلفاء وتعويذات المستدعي.", "إن قدرة الفك المفترس (Q) قادرة على تتبع الأعداء الذين يركضون، أو يندفعون، أو ينتقلون سريعًا إن ضغطت باستمرار على الزر."], "enemytips": ["تتسبب هجمات وارويك بعلاجه عندما تنخفض صحته. فادخر تأثيرات التعطيل لديك للإجهاز عليه.", "تتعزز قوة وارويك ضد الأعداء المنخفضي الصحة. فأدر أمور صحتك بشكل جيد لإبقائه بعيدًا عنك.", "يتناسب مدى إلقاء قدرة وارويك الخارقة مع سرعة حركته."], "tags": ["Fighter", "Tank"], "partype": "المانا", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "الفك المفترس", "description": "يندفع وارويك نحو الأمام ويعُض هدفه؛ مما يلحق ضررًا يعتمد على صحته القصوى ويعالج مقابل الضرر الملحق.", "tooltip": "<tap>انقر:</tap> يندفع وارويك إلى الأمام ويعض العدو الهدف، مما يلحق ضرر سحر <magicDamage>{{ basebitedamage }} إضافة إلى نسبة {{ targetpercenthpdamage }}% من الصحة القصوى في هيئة ضرر سحر</magicDamage> <healing>ويشفي {{ e3 }}% من الضرر الملحق</healing>.<br /><br /><hold>الضغط باستمرار:</hold> يندفع وارويك على الهدف وينهشه بمخالبه، ويقفز من ورائه. وبينما تكون مخالبه مثبتة، يتبع وارويك كل حركات الهدف. بعد فك قبضته، يفعل الضرر والعلاج نفسيهما.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["العلاج", "% ضرر صحة", "فترة التبريد", "تكلفة @AbilityResourceName@"], "effect": ["{{ e3 }}%-> {{ e3NL }}%", "{{ targetpercenthpdamage }}%-> {{ targetpercenthpdamageNL }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickW", "name": "صيد الدم", "description": "يستشعر وارويك الأعداء الذين تقل صحتهم عن 50%، فيكسب سرعة حركة باتجاههم وسرعة هجوم عليهم. عند انخفاض صحتهم عن 25%، يدخل في طور الجنون وتتضاعف هذه الزيادات ثلاث مرات.", "tooltip": "<spellPassive>تأثير كامن:</spellPassive> يستشعر وارويك الأبطال ممن تقل صحتهم عن 50%، ليكسب <speed>{{ passivemsbonus }}% سرعة هجوم</speed> نحوهم. تمنح التعويذات والهجمات ضد الأعداء الذين تقل صحتهم عن 50% <speed>{{ passiveasbonus }}% سرعة هجوم</speed>. تزداد التأثيرات بنسبة 200% ضد الأعداء الذين يمتلكون أقل من 25% صحة. <br /><br /><spellActive>تأثير نشط:</spellActive> يستشعر وارويك لفترة وجيزة كل الأعداء، ويكسب التأثير الكامن لهذه القدرة ضد البطل الأقرب إليه لمدة 8 ثوانِ، بغض النظر عن صحته. إذا لم يتم العثور على أبطال، يتم خفض فترة تبريد هذه القدرة بنسبة 30%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["سرعة الحركة", "سرعة الهجوم", "فترة التبريد"], "effect": ["{{ passivemsbonus }}%-> {{ passivemsbonusNL }}%", "{{ passiveasbonus }}%-> {{ passiveasbonusNL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickE", "name": "عواء بدائي", "description": "يكسب وارويك تقليص ضرر لمدة 2.5 ثانية. وفي النهاية، أو في حال إعادة التفعيل، يطلق عواءً يتسبب بهرب الأعداء المجاورين لثانية واحدة.", "tooltip": "يكسب وارويك {{ e1 }}% من تقليص الضرر لمدة 2.5 ثانية. بعد الانتهاء، يعوي وارويك، <status>ليخيف</status> الأعداء المحيطين به لمدة {{ e3 }}ث. يستطيع وارويك <recast>إعادة الإلقاء</recast> لإنهاء هذه القدرة في وقت مبكر.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["تقليص الضرر", "فترة التبريد"], "effect": ["{{ e1 }}%-> {{ e1NL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "WarwickR", "name": "إجبار بلا نهاية", "description": "يقفز وارويك في اتجاه معين (يتناسب مع سرعة حركته الإضافية)، فيقمع أول بطل معادٍ يرتطم به لمدة 1.5 ثانية.", "tooltip": "يثب وارويك مسافة كبيرة تتناسب مع <speed>سرعة حركته</speed>، <status>قامعًا</status> أول بطل يصطدم به في أثناء شق طريقه لمدة {{ rduration }}ث. يهاجم ذلك البطل 3 مرات خلال المدة، ملحقًا <magicDamage>{{ damagecumulative }} ضرر سحر</magicDamage>. وارويك <healing>يتعالج بنسبة 100% من كل الضرر الملحق</healing> خلال التوجيه.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["الضرر", "فترة التبريد"], "effect": ["{{ rbasedamage }}-> {{ rbasedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "الجوع الأزلي", "description": "تلحق هجمات وارويك الأساسية ضرر سحري إضافي. إذا كانت صحة وارويك تحت 50%، يعالج بذات المقدار. إذا كانت صحة وارويك تقل عن 25%، يتضاعف علاجه ثلاث مرات.", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}