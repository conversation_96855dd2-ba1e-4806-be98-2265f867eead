{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Φράγμα", "description": "Αποκτάτε μια Ασπίδα για ένα σύντομο χρονικό διάστημα.", "tooltip": "Αποκτάτε μια Ασπίδα με αντοχή <shield>{{ shieldstrength }}</shield> για {{ shieldduration }} δευτ.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerBoost": {"id": "SummonerBoost", "name": "Εξαγνισμός", "description": "Αφαιρε<PERSON> όλες τις επιδράσεις ακινητοποίησης (εκτ<PERSON>ς από την καταστολή και την εκτίναξη στον αέρα) και τις αποδυναμώσεις από Ξόρκια Επικαλεστή που επηρεάζουν τον Ήρωά σας και δίνει Εμμονή.", "tooltip": "Αφαιρεί όλες τις αποδυναμώσεις Καταστολής Πλήθους (εκτός από την <keyword>Καταστολή</keyword> και την <keyword>Εκτόξευση στον αέρα</keyword>) και τις αποδυναμώσεις από Ξόρκια Επικαλεστή από εσάς και δίνει {{ tenacityvalue*100 }}% Εμμονή για {{ tenacityduration }} δευτ.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "Φλας", "description": "Τηλεμεταφέρει τον Ήρωά σας σε μικρή απόσταση προς το σημείο που βρίσκεται ο κέρσορας.", "tooltip": "Τηλεμεταφέρει τον Ήρωά σας σε μικρή απόσταση προς το σημείο που βρίσκεται ο κέρσορας.<br /><br />Δεν μπορεί να χρησιμοποιηθεί ξανά για έναν ολόκληρο γύρο <rules>(κάθ<PERSON> γύρος αποτελείται από μια φάση αγοράς και μια φάση μάχης).</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "Υποχώρηση", "description": "Αποκτήστε μια σύντομη αύξηση στην Ταχύτητα Κίνησης, που αυξάνεται όσο τρέχετε μακριά από αντίπαλους Ήρωες.", "tooltip": "<keywordMajor>Θέση Ενεργής Ικανότητας:</keywordMajor> Οι Ενισχύσεις που δίνουν ένα Ξόρκι Επικαλεστή θα αντικαταστήσει αυτήν τη θέση.<br /><br />Αποκτήστε <moveSpeed>{{ basems*100 }}% Ταχύτητα Κίνησης</moveSpeed> για {{ duration }} δευτ., που αυξάνεται κατά {{ bonusmsperenemybehind*100 }}% για κάθε αντίπαλο πίσω σας.", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerDot": {"id": "SummonerDot", "name": "Ανάφλεξη", "description": "Προκαλεί Πραγματική Ζημιά στον στοχευμένο αντίπαλο Ήρωα σε βάθος χρόνου και μειώνει τις επιδράσεις Θεραπείας πάνω του για αυτό το χρονικό διάστημα.", "tooltip": "Προκαλε<PERSON> <trueDamage>{{ tooltiptruedamagecalculation }} Πραγματική Ζημιά</trueDamage> στον εχθρικό Ήρωα μέσα σε 5 δευτερόλεπτα και εφαρμόζει <keyword>{{ grievousamount*100 }}% Βαριά Τραύματα</keyword> για αυτό το χρονικό διάστημα.<br /><br /><keyword>Τραύματα</keyword>: Μειώνεται η αποτελεσματικότητα των επιδράσεων Θεραπείας και Αναπλήρωσης Ζωής.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "Εξάντληση", "description": "Επιβραδύνει τον στοχευμένο αντίπαλο Ήρωα και μειώνει τη Ζημιά που προκαλεί.", "tooltip": "<keyword>Επιβραδύνει</keyword> τον στοχευμένο αντίπαλο Ήρωα κατά {{ slow }}% και μειώνει τη Ζημιά που προκαλεί κατά {{ damagereduction }}% για {{ debuffduration }} δευτ.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerFlash": {"id": "SummonerFlash", "name": "Φλας", "description": "Σας τηλεμεταφέρει σε μικρή απόσταση προς τον κέρσορά σας.", "tooltip": "Σας τηλεμεταφέρει σε μικρή απόσταση προς τον κέρσορά σας.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerHaste": {"id": "SummonerHaste", "name": "Φάντασμα", "description": "Αποκτάτε Ταχύτητα Κίνησης και περνάτε μέσα από άλλες μονάδες για τη διάρκεια της επίδρασης.", "tooltip": "Αποκτάτε <speed>{{ movespeedmod }} Ταχύτητα Κίνησης</speed> και γίνεστε <keyword>Φάντασμα</keyword> για {{ duration }} δευτ.<br /><br /><keyword>Φάντασμα</keyword>: Περνάτε μέσα από άλλες μονάδες.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerHeal": {"id": "SummonerHeal", "name": "Θεραπεία", "description": "Αναπληρώνει Ζωή και δίνει Ταχύτητα Κίνησης σε εσάς και στον στοχευμένο σύμμαχο Ήρωα.", "tooltip": "Αναπληρώνει <healing>{{ totalheal }} Ζωή</healing> και σας δίνει <speed>{{ movespeed*100 }}% Ταχύτητα Κίνησης</speed> για {{ movespeedduration }} δευτ. και στον στοχευμένο σύμμαχο Ήρωα.<br /><br /><rules>Αν χρησιμοποιηθεί χωρίς στόχο, θα χρησιμοποιηθεί στον πιο βαριά τραυματισμένο σύμμαχο Ήρωα εντός εμβέλειας.<br />Η Θεραπεία μειώνεται στο μισό σε μονάδες που έχουν επηρεαστεί πρόσφατα από τη Θεραπεία Επικαλεστή.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "Διαύγεια", "description": "Αναπληρώνει Μάνα σε εσάς και στον σύμμαχο Ήρωα.", "tooltip": "Αναπληρώνει το {{ e1 }}% του μέγιστου Μάνα στον Ήρωά σας και το {{ e2 }}% στους κοντινούς συμμάχους.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "Στο Βασιλιά!", "description": "Μετακινηθείτε γρήγορα στο πλευρό του Βασιλιά Πόρο.", "tooltip": "<span class=\"colorFFE076\">Παθητική:</span> Αν χτυπήσετε έναν αντίπαλο Ήρωα με ένα Πόρο, η ομάδα σας αποκτά έναν Πόντο Πόρο. Στους 10 Πόντους Πόρο, η ομάδα σας καλεί τον Βασιλιά των Πόρο, για να πολεμήσει στο πλευρό σας. Όταν ο Βασιλιάς των Πόρο είναι ενεργός, καμία ομάδα δεν μπορεί να αποκτήσει Πόντους Πόρο.<br /><br /><span class=\"colorFFE076\">Ενεργή:</span> Μετακινείστε γρήγορα στο πλευρό του Βασιλιά των Πόρο. Μπορεί να χρησιμοποιηθεί μόνο όταν η ομάδα σας έχει καλέσει τον Βασιλιά των Πόρο. <br /><br /><i><span class=\"colorFDD017\">«Τα Πόρο κάνουν παιχνίδι. Οι υπόλοιποι απλώς είστε θεατές.»</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "Ρίψη Πόρο", "description": "Πετάξτε ένα Πόρο στους εχθρούς σας. Αν βρει στόχο, μπορείτε να μετακινηθείτε γρήγορα στον στόχο σας αμέσως μετά.", "tooltip": "Ρίχνει ένα Πόρο σε μεγάλη απόσταση και προκαλεί {{ f2 }} Πραγματική Ζημιά στην πρώτη εχθρική μονάδα που θα χτυπήσει, προσφέροντας <span class=\"coloree91d7\">Ενόραση</span> προς τον στόχο.<br /><br />Αυτή η ικανότητα μπορεί να χρησιμοποιηθεί ξανά για 3 δευτερόλεπτα, αν χτυπήσει έναν εχθρό, για να εφορμήσει στον στόχο που έχει χτυπήσει, προκαλώντας {{ f2 }} επιπλέον Πραγματική Ζημιά και μειώνοντας τον Χρόνο Επαναφόρτισης της επόμενης Ρίψης Πόρο κατά {{ e4 }} δευτ.<br /><br />Τα Πόρο δεν μπλοκάρονται από ασπίδες ξορκιών ή τείχη ανέμου, καθώς είναι ζώα, όχι ξόρκια!<br /><br /><i><span class=\"colorFDD017\">«Τα Πόρο αποτελούν ένα υπόδειγμα αεροδυναμικής στη Γη των Ρούνων.»</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerSmite": {"id": "SummonerSmite", "name": "Πλήγμα", "description": "Προκαλεί Πραγματική Ζημιά σε ένα τέρας ή υπηρέτη.", "tooltip": "Προκαλεί <trueDamage>{{ smitebasedamage }} Πραγματική Ζημιά</trueDamage> στο στοχευμένο μεγάλο τέρας ή υπηρέτη λωρίδας.<br /><br />Προκαλεί <trueDamage>{{ firstpvpdamage }} Πραγματική Ζημιά</trueDamage> σε συντρόφους Ηρώων.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "Σημάδεμα", "description": "Ρίξτε μια χιονόμπαλα σε ευθεία γραμμή στους αντιπάλους σας. Αν πετύχει κάποιον αντίπαλο, αυτ<PERSON>ς σημαδεύεται, σας δίνει Ενόραση και μπορείτε να μετακινηθείτε γρήγορα στον σημαδεμένο στόχο σας αμέσως μετά.", "tooltip": "Ρίχνει μια χιονόμπαλα σε μεγάλη απόσταση, προκαλεί Πραγματική Ζημιά {{ tooltipdamagetotal }} στην πρώτη εχθρική μονάδα που θα χτυπήσει και δίνει <span class=\"coloree91d7\">Ενόραση</span> του στόχου. Αν πετύχει κάποιον αντίπαλο, μπορείτε να χρησιμοποιήσετε εκ νέου αυτήν την ικανότητα για {{ e3 }} δευτερόλεπτα, για να κάνετε Εφόρμηση στη σημαδεμένη μονάδα και να προκαλέσετε επιπλέον {{ tooltipdamagetotal }} Πραγματική Ζημιά. Η εφόρμηση στον στόχο μειώνει τον Χρόνο Επαναφόρτισης του Σημαδέματος κατά {{ e4 }}%.<br /><br /><span class=\"colorFFFF00\">Οι ασπίδες ικανοτήτων ή η μείωση ζημιάς από βλήματα δεν μπορούν σταματήσουν τα βλήματα του Σημαδέματος.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "Σημάδεμα", "description": "Ρίξτε μια χιονόμπαλα σε ευθεία γραμμή στους αντιπάλους σας. Αν πετύχει κάποιον αντίπαλο, αυτ<PERSON>ς σημαδεύεται, σας δίνει Ενόραση και μπορείτε να μετακινηθείτε γρήγορα στον σημαδεμένο στόχο σας αμέσως μετά.", "tooltip": "Ρίχνει μια χιονόμπαλα σε μεγάλη απόσταση, προκαλεί Πραγματική Ζημιά {{ tooltipdamagetotal }} στην πρώτη εχθρική μονάδα που θα χτυπήσει και δίνει <span class=\"coloree91d7\">Ενόραση</span> του στόχου. Αν πετύχει κάποιον αντίπαλο, μπορείτε να χρησιμοποιήσετε εκ νέου αυτήν την ικανότητα για {{ e3 }} δευτερόλεπτα, για να κάνετε Εφόρμηση στη σημαδεμένη μονάδα και να προκαλέσετε επιπλέον {{ tooltipdamagetotal }} Πραγματική Ζημιά. Η εφόρμηση στον στόχο μειώνει τον Χρόνο Επαναφόρτισης του Σημαδέματος κατά {{ e4 }}%.<br /><br /><span class=\"colorFFFF00\">Οι ασπίδες ικανοτήτων ή η μείωση ζημιάς από βλήματα δεν μπορούν σταματήσουν τα βλήματα του Σημαδέματος.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "Τηλεμεταφορά", "description": "Μετά από μια σύντομη διοχέτευση, δεν μπορείτε να γίνετε στόχος και κινείστε προς μια συμμαχική μονάδα. Αναβαθμίζεται σε Αχαλίνωτη Τηλεμεταφορά, αυξ<PERSON>ν<PERSON>ντας σημαντικά την ταχύτητα μετακίνησης. ", "tooltip": "Μετά από διοχέτευση ενέργειας {{ channelduration }} δευτερολέπτων, <keyword>δεν μπορείτε να γίνετε στόχος</keyword> και μετακινείστε στη θέση ενός στοχευμένου συμμαχικού κτιρίου, Υπηρέτη ή Ματιού. <br /><br />Αναβαθμίζεται σε Αχαλίνωτη Τηλεμεταφορά στα {{ upgrademinute }} λεπτά, αυξάνοντας σημαντικά την ταχύτητα μετακίνησης.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON><PERSON><PERSON> κόστος"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "Προσωρινό", "description": "Αυτή η θέση θα αντικατασταθεί από την Υπέρτατη ενός άλλου Ήρωα που επιλέγεται στην αρχή του παιχνιδιού. Θα υπάρχει χρόνος 30 δευτ. για την επιλογή της Υπέρτατης. Προσοχή!", "tooltip": "Θα αντικατασταθεί από την επιλογή σας για την Υπέρτατη Ικανότητα Επικαλεστή.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "Προσωριν<PERSON> και Επίθεση-Πλήγμα", "description": "Αυτή η θέση θα αντικατασταθεί από την Υπέρτατη ενός άλλου Ήρωα και θα αποκτήσετε την Επίθεση-Πλήγμα. Θα υπάρχει χρόνος 30 δευτ. για την επιλογή της Υπέρτατης. Προσοχή!", "tooltip": "Θα αντικατασταθεί από το Υπέρτατο Ξόρκι Επικαλεστή σας.<br /><br />Αποκτάτε την Επίθεση-Πλήγμα. Η Επίθεση-Πλήγμα θα εκτελέσει συμμαχικά τέρατα ενίσχυσης, επικά τέρατα και Γαριδρομείς όταν τους επιτίθεστε.<br /><br /><attention>Η Επίθεση-Πλήγμα δεν έχει Χρόνο Επαναφόρτισης.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}