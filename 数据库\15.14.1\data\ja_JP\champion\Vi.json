{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vi": {"id": "Vi", "key": "254", "name": "ヴァイ", "title": "ピルトーヴァーの用心棒", "image": {"full": "Vi.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "254000", "num": 0, "name": "default", "chromas": false}, {"id": "254001", "num": 1, "name": "ネオンバスター ヴァイ", "chromas": false}, {"id": "254002", "num": 2, "name": "ヴァイ巡査", "chromas": true}, {"id": "254003", "num": 3, "name": "おしゃれなヴァイ", "chromas": false}, {"id": "254004", "num": 4, "name": "デーモン ヴァイ", "chromas": false}, {"id": "254005", "num": 5, "name": "三国武将ヴァイ", "chromas": false}, {"id": "254011", "num": 11, "name": "PROJECT: Vi", "chromas": false}, {"id": "254012", "num": 12, "name": "愛の鉄拳ヴァイ", "chromas": true}, {"id": "254020", "num": 20, "name": "PsyOps ヴァイ", "chromas": true}, {"id": "254029", "num": 29, "name": "Arcane 地下都市ヴァイ", "chromas": false}, {"id": "254030", "num": 30, "name": "愛の疼きヴァイ", "chromas": true}, {"id": "254039", "num": 39, "name": "原始の襲撃ヴァイ", "chromas": true}, {"id": "254048", "num": 48, "name": "Arcane ブローラー ヴァイ", "chromas": false}], "lore": "ゾウンの貧民街で育ったヴァイは、直情的で頭に血が上りやすく、権力者などというものをほとんど意にも介さない、恐るべき女だ。またかつては若さゆえにしばしば問題を引き起こし、スティルウォーター刑務所で過ごした時間も長いため、生き延びるための知恵に長けた存在でもある。そんな彼女だが、現在はピルトーヴァーの執行官たちと組んで、治安を乱す側ではなく維持する側に立っている。その腕に装着されたヘクステック式パワーグラブは、犯罪者だろうが頑丈な壁だろうが簡単にぶち抜いてしまうだろう。", "blurb": "ゾウンの貧民街で育ったヴァイは、直情的で頭に血が上りやすく、権力者などというものをほとんど意にも介さない、恐るべき女だ。またかつては若さゆえにしばしば問題を引き起こし、スティルウォーター刑務所で過ごした時間も長いため、生き延びるための知恵に長けた存在でもある。そんな彼女だが、現在はピルトーヴァーの執行官たちと組んで、治安を乱す側ではなく維持する側に立っている。その腕に装着されたヘクステック式パワーグラブは、犯罪者だろうが頑丈な壁だろうが簡単にぶち抜いてしまうだろう。", "allytips": ["「真っすぐいってぶっとばす」は、フルチャージすると通常の倍のダメージを与える。逃げる敵をぶん殴るにはうってつけのスキルだ。", "「無慈悲な連撃」は、衝撃波に接れたすべての敵にフルダメージを与える。ミニオンの陰にこそこそ隠れている敵を、驚かせてやろう。", "「突入捜査」は敵に強烈な先制パンチを食らわせるスキルだが、ヴァイは指定した敵チャンピオンのところまで移動してしまう。味方から離れすぎないように注意しよう。"], "enemytips": ["フルチャージした「真っすぐいってぶっとばす」を食らうと、通常の倍のダメージを受ける。ヴァイがチャージを開始したらすぐに距離を取るか、回避を試みよう。", "ヴァイの通常攻撃を3回連続で受けると物理防御が低下するうえ、ヴァイの攻撃速度が増加してしまう。彼女と殴りあうのは避けたほうがいいだろう。", "ヴァイのアルティメットスキルは途中で妨害できない。行動妨害スキルを使うなら、突進が終わってからにしよう。"], "tags": ["Fighter", "Assassin"], "partype": "マナ", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 4}, "stats": {"hp": 655, "hpperlevel": 99, "mp": 295, "mpperlevel": 65, "movespeed": 340, "armor": 30, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 10, "hpregenperlevel": 1, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "ViQ", "name": "真っすぐいってぶっとばす", "description": "ガントレットにエネルギーをチャージした後、指定方向に猛ダッシュしながらパンチを繰り出す。敵ユニットに命中するとダメージとノックバックを与える。これは「メッタ打ち」の連続攻撃回数にもカウントされる。", "tooltip": "<charge>チャージ開始:</charge> 強烈なパンチのチャージを開始して、自身は{{ e4 }}%の<status>スロウ効果</status>を受ける。<br /><br /><release>解放:</release> 前方にダッシュして、当たったすべての敵にチャージ時間に応じて<physicalDamage>{{ totaldamage }}-{{ maxdamagetooltip }}の物理ダメージ</physicalDamage>を与え、<spellName>「メッタ打ち」</spellName>を付与する。敵チャンピオンに当たるとその場で停止し、対象を<status>ノック</status><status>バック</status>する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最小ダメージ", "最大ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ mindamage*2.500000 }} -> {{ mindamagenl*2.500000 }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "15", "6", "5", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250, 250, 250], "rangeBurn": "250", "image": {"full": "ViQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ViW", "name": "メッタ打ち", "description": "ヴァイのパンチが敵の物理防御を破って追加ダメージを与え、さらに自身の攻撃速度が増加する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 同じ対象に通常攻撃が3回命中するたびに、<physicalDamage>最大体力の{{ totaldamagetooltip }}の物理ダメージ</physicalDamage>を追加で与え、{{ sharedbuffsduration }}秒間、対象の<scaleArmor>物理防御を{{ shredamount }}%</scaleArmor>低下させて、自身の<attackSpeed>攻撃速度が{{ attackspeed }}%</attackSpeed>増加する。さらに、<spellName>「ケンカの作法」</spellName>の残りクールダウンが{{ spell.vipassive:cdreductionon3hit }}秒短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最大体力ダメージ", "攻撃速度"], "effect": ["{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ attackspeed }}% -> {{ attackspeedNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "自動効果", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "ViW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "自動効果"}, {"id": "ViE", "name": "無慈悲な連撃", "description": "ヴァイが次の通常攻撃と同時に、衝撃波を放つ。衝撃波は通常攻撃をした対象の背後に広がり、触れた敵にダメージを与える。", "tooltip": "次の通常攻撃が対象とその背後の敵に<physicalDamage>{{ totaldamagetooltip }}の物理ダメージ</physicalDamage>を与える。<br /><br />このスキルは2回までチャージできる({{ ammorechargetime }}秒でリチャージ)。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "チャージ時間", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [26, 32, 38, 44, 50], "costBurn": "26/32/38/44/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [50, 50, 50, 50, 50], [1.5, 1.5, 1.5, 1.5, 1.5], [6, 6, 6, 6, 6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "50", "1.5", "6", "1", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ViE.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "ViR", "name": "突入捜査", "description": "進路にいる敵を跳ね飛ばしながら指定した対象に向かって突撃し、接触と同時に対象をノックアップさせ、追いかけるように自身もジャンプしてから、地面に叩きつけてフィニッシュする。", "tooltip": "敵チャンピオン1体を選び出して可視化し、そこまで阻止不能状態でダッシュする。ダッシュ後、対象を{{ rstunduration }}秒間<status>ノック</status><status>アップ</status>して、<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。<br /><br />当たった他の敵にもダメージを与え、横方向に弾き飛ばし、{{ secondarytargetstunduration }}秒間<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "ViR.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ケンカの作法", "description": "一定時間ごとにシールドをチャージし、スキルが敵に命中した瞬間に発動する。", "image": {"full": "ViPassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}