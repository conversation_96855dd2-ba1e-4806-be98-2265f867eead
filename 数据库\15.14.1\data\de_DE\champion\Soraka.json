{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Soraka": {"id": "<PERSON><PERSON><PERSON>", "key": "16", "name": "<PERSON><PERSON><PERSON>", "title": "das Sternenkind", "image": {"full": "Soraka.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "16000", "num": 0, "name": "default", "chromas": false}, {"id": "16001", "num": 1, "name": "Dryaden-Soraka", "chromas": false}, {"id": "16002", "num": 2, "name": "Göttliche Soraka", "chromas": false}, {"id": "16003", "num": 3, "name": "Himmlische Soraka", "chromas": false}, {"id": "16004", "num": 4, "name": "Schnitter-Sorak<PERSON>", "chromas": false}, {"id": "16005", "num": 5, "name": "Bananenorden-Soraka", "chromas": false}, {"id": "16006", "num": 6, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "16007", "num": 7, "name": "Sternenwächterin Soraka", "chromas": false}, {"id": "16008", "num": 8, "name": "Pyjamawächterin Soraka", "chromas": false}, {"id": "16009", "num": 9, "name": "Winterwunder-<PERSON>rak<PERSON>", "chromas": true}, {"id": "16015", "num": 15, "name": "Rächende Dämmerung Soraka", "chromas": false}, {"id": "16016", "num": 16, "name": "Flammende Finsternis Soraka", "chromas": false}, {"id": "16017", "num": 17, "name": "Sternenwächterin Soraka (Prestige)", "chromas": false}, {"id": "16018", "num": 18, "name": "Konditorei-Sorak<PERSON>", "chromas": true}, {"id": "16027", "num": 27, "name": "Seelenblumen-Soraka", "chromas": true}, {"id": "16037", "num": 37, "name": "Reise der Unsterblichen-Soraka", "chromas": true}, {"id": "16044", "num": 44, "name": "Feenhof-Soraka", "chromas": true}], "lore": "Soraka durchwanderte die himmlischen Dimensionen jenseits des Targon, bevor sie Unsterblichkeit aufgab, um die sterblichen Völker vor ihren eigenen gewalttätigen Instinkten zu schützen. Sie ist bestrebt, allen, die sie trifft, die Tugenden von Mitgefühl und Gnade näherzubringen, und heilt sogar diejenigen, die ihr nichts Gutes wünschen. Und nach allem, was <PERSON><PERSON><PERSON> bi<PERSON> von den Problemen der Welt g<PERSON>hen hat, glaubt sie fest daran, dass die Bewohner von Runeterra ihr Potenzial noch lange nicht ausgeschöpft haben.", "blurb": "Soraka durchwanderte die himmlischen Dimensionen jenseits des Targon, bevor sie Unsterblichkeit aufgab, um die sterblichen Völker vor ihren eigenen gewalttätigen Instinkten zu schützen. Sie ist bestrebt, allen, die sie trifft, die Tugenden von Mitgefühl...", "allytips": ["<PERSON><PERSON><PERSON> ist eine mächtige Verbündete im Kampf, die mit ihrer starken Heilung die Gruppe in Bewegung hält.", "Du kannst „Wunsch“ auf Verbündete auf der anderen Seite der Karte anwenden, um sie vor andernfalls tödlichen Angriffen zu schützen.", "„Äquinoktium“ kann genutzt werden, um Gegner auf Distanz in Scha<PERSON> zu halten."], "enemytips": ["Konzentriere deine Angriffe auf Soraka, wenn sie sich in einem Teamkampf an vorderster Front befindet, um ihre Verbündeten zu heilen.", "Nutze die lange Abklingzeit von „Äquinoktium“ zu deinem Vorteil, falls sie es einsetzt.", "<PERSON><PERSON> ist e<PERSON><PERSON><PERSON>, sich auf Soraka zu konzentrieren als auf den Verbündeten, den sie heilt."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 7, "difficulty": 3}, "stats": {"hp": 605, "hpperlevel": 88, "mp": 425, "mpperlevel": 40, "movespeed": 325, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2.14, "attackspeed": 0.625}, "spells": [{"id": "SorakaQ", "name": "Sternenregen", "description": "An einer Zielposition fällt ein Stern vom Himmel, der magischen Schaden verursacht und getroffene Gegner verlangsamt. Wenn ein gegnerischer Champion von „Sternenregen“ getroffen wird, stellt So<PERSON>a <PERSON> wieder her.", "tooltip": "<PERSON><PERSON>a beschwört einen Stern herab, der <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und {{ slowduration }}&nbsp;Sekunden lang um {{ movespeedslow*100 }}&nbsp;% <status>verlangsamt</status>. <br /><br />Trifft Soraka einen gegnerischen Champion, erhält sie <keywordMajor>Verjüngung</keywordMajor> und stellt über {{ hotduration }}&nbsp;Sekunden hinweg <healing>{{ totalhot }}&nbsp;Leben</healing> wieder her. Außerdem erhält sie <speed>{{ movespeedhaste*100 }}&nbsp;% Lauftempo</speed>, das über diese Dauer abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verjüngung: Lebenswiederherstellung", "Lauftempo", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basehot }} -> {{ basehotNL }}", "{{ movespeedhaste*100.000000 }}&nbsp;% -> {{ movespeedhastenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "SorakaQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SorakaW", "name": "Astralinfusion", "description": "<PERSON><PERSON><PERSON> opfert einen Teil ihres eigenen Lebens, um einen befreundeten Champion zu heilen.", "tooltip": "<PERSON><PERSON>a stellt bei einem anderen verbündeten Champion <healing>{{ totalheal }}&nbsp;<PERSON><PERSON></healing> wieder her.<br /><br />Wenn bei <PERSON><PERSON> <keywordMajor>Verjüngung</keywordMajor> aktiv ist, werden die Lebenskosten um {{ percenthealthcostrefund*100 }}&nbsp;% verringert und das Ziel erhält {{ spell.sorakaq:hotduration }}&nbsp;Sekunden lang <keywordMajor>Verjüngung</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung", "Abklingzeit", "Kosten (@AbilityResourceName@)", "Verringerung der Lebenskosten"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ percenthealthcostrefund*100.000000 }}&nbsp;% -> {{ percenthealthcostrefundnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% maximales <PERSON>, {{ cost }}&nbsp;<PERSON>a", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SorakaW.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ percenthealthcost*100 }}&nbsp;% maximales <PERSON><PERSON>, {{ cost }}&nbsp;Mana"}, {"id": "SorakaE", "name": "Äquinoktium", "description": "<PERSON>ha<PERSON>t an einer gewählten Position ein Gebiet, das alle darin befindlichen Gegner verstummen lässt. Wenn das Gebiet seine Wirkung verliert, werden alle Gegner, die sich noch immer darin befinden, festgehalten.", "tooltip": "<PERSON><PERSON><PERSON> erzeugt e<PERSON>, das Champions <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt. Das Feld bleibt {{ rootdelay }}&nbsp;Sekunden lang bestehen und lässt Gegner im Inneren <status>verstummen</status>. Champions, die noch im Wirkbereich stehen, wenn sich das Feld auflöst, werden {{ rootduration }}&nbsp;Sekunde(n) lang <status>festgehalten</status> und erleiden <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Festhaltedauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "SorakaE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SorakaR", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> erfüllt ihre Verbündeten mit neuer Hoffnung und stellt sofort Leben bei sich selbst und allen verbündeten Champions wieder her.", "tooltip": "<PERSON>raka ruft die himmlischen Mächte an, um bei allen verbündeten Champions <healing>{{ healingcalc }}&nbsp;<PERSON><PERSON></healing> wiederherzustellen, egal wie weit sie entfernt sind. Bei Zielen unter 40&nbsp;% Leben erhöht sich die Heilung auf <healing>{{ ampedhealing }}</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung", "Abklingzeit"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [150, 135, 120], "cooldownBurn": "150/135/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SorakaR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON>rl<PERSON><PERSON>g", "description": "<PERSON><PERSON><PERSON> rennt schneller, wenn sie sich auf Verbündete in der Nähe zu bewegt, die nur wenig Leben haben.", "image": {"full": "Soraka_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}