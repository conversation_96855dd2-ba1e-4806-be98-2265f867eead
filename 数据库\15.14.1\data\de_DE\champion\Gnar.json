{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gnar": {"id": "<PERSON><PERSON>", "key": "150", "name": "<PERSON><PERSON>", "title": "das fehlende Bindeglied", "image": {"full": "Gnar.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "150000", "num": 0, "name": "default", "chromas": false}, {"id": "150001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "150002", "num": 2, "name": "Gentleman-<PERSON><PERSON>", "chromas": false}, {"id": "150003", "num": 3, "name": "Schneetag-Gnar", "chromas": false}, {"id": "150004", "num": 4, "name": "El León Gnar", "chromas": false}, {"id": "150013", "num": 13, "name": "Supergalaktischer Gnar", "chromas": false}, {"id": "150014", "num": 14, "name": "SSG-Gnar", "chromas": false}, {"id": "150015", "num": 15, "name": "Astronauten-Gnar", "chromas": true}, {"id": "150022", "num": 22, "name": "Ahnenholz-Gnar", "chromas": true}, {"id": "150031", "num": 31, "name": "La Ilusión Gnar", "chromas": true}], "lore": "Gnar ist ein urzeitlicher Yordle, dessen spielerische Mätzchen schnell ausarten und ihn in eine gewaltige Bestie der Zerstörung verwandeln können. Er war Jahrtausende lang in wahrem Eis gefangen, konnte sich jedoch befreien und springt nun in einer neuen, exotischen und wundersamen Welt umher. Gnar ist ein großer Fan von G<PERSON>ahr und bewirft seine Gegner mit allem, was er finden kann – sei es sein Knochenzahn-Bumerang oder ein Gebäude.", "blurb": "Gnar ist ein urzeitlicher Yordle, dessen spielerische Mätzchen schnell ausarten und ihn in eine gewaltige Bestie der Zerstörung verwandeln können. Er war Jahrtausende lang in wahrem E<PERSON> gefangen, konnte sich jedoch befreien und springt nun in einer...", "allytips": ["<PERSON>s ist sehr wichtig, wie du deine Wut einsetzt. Versuche, deine Transformationen zeitlich abzupassen, damit du aus beiden Erscheinungsformen das Optimum herausholst.", "Positioniere dich an <PERSON>uern, um deine Gegner gegen diese zu werfen, sodass sie von deiner ultimativen Fähigkeit betäubt werden.", "Kenne deine Stärken! Mini-Gnar ist schnell, verletzlich und verursacht fortdauernd hohen Schaden. Mega-Gnar ist langsam, widerstandsfähig und haut hohe Schadensspitzen raus."], "enemytips": ["Nachdem er sich von <PERSON> zu <PERSON> verwandel<PERSON> hat, kann Gnar 15 Sekunden lang keine Wut aufbauen. Nutze die Gelegenheit, um sein Team anzugreifen.", "Gnars Animationen und Ressourcenanzeige verändern sich, je näher er der Transformation kommt.", "Gnars ultimative Fähigkeit betäubt, falls er dich in eine Mauer wirft. Sei also in der Nähe solcher Mauern vorsichtig."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 5, "magic": 5, "difficulty": 8}, "stats": {"hp": 540, "hpperlevel": 79, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 32, "armorperlevel": 3.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 175, "hpregen": 4.5, "hpregenperlevel": 1.25, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.2, "attackspeedperlevel": 6, "attackspeed": 0.625}, "spells": [{"id": "GnarQ", "name": "Bumerang-Wurf / Felsblock-Wurf", "description": "Gnar wirft einen Bumerang, der Schaden verursacht und verlangsamt, bevor er zu ihm zurückkehrt. Falls er den Bumerang fängt, wird dessen Abklingzeit verringert.<br>Mega-Gnar wirft stattdessen einen Felsblock, der bei der ersten getroffenen Einheit stoppt, Schaden verursacht und alle in der Nähe verlangsamt. Er kann aufgehoben werden, um die Abklingzeit zu verringern.", "tooltip": "<keywordMajor>Mini-Gnar:</keywordMajor> Gnar wirft einen Bumerang, der <physicalDamage>{{ spell.gnarq:minitotaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und Gegner {{ spell.gnarq:slowduration }}&nbsp;Sekunden lang um {{ spell.gnarq:slowamount*100 }}&nbsp;% <status>verlangsamt</status>. Der Bumerang kehrt zurück, nachdem er einen Gegner getroffen hat, und fügt weiteren Zielen verringerten Schaden zu. Jeder Gegner kann nur einmal getroffen werden. Das Fangen des Bumerangs verringert die Abklingzeit um {{ spell.gnarq:minicdrefund*100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bumerang-<PERSON><PERSON><PERSON>", "Felsblock-Schaden", "Verlangsamung", "Felsblock – Verlangsamung", "Abklingzeit"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ megabasedamage }} -> {{ megabasedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ megaslowamount*100.000000 }}&nbsp;% -> {{ megaslowamountnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 17.5, 15, 12.5, 10], "cooldownBurn": "20/17.5/15/12.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "GnarQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GnarW", "name": "Aufgedreht / Prügel", "description": "G<PERSON>s Ang<PERSON> und Fähigkeiten lassen ihn aufdrehen, verursachen zusätzlichen Schaden und gewähren ihm Lauftempo.<br><br>Mega-Gnar ist zu wütend, um aufzudrehen und kann sich stattdessen auf seine Hinterbeine stellen und auf den Boden vor ihm eindreschen, wodurch er die Gegner in diesem Gebiet betäubt.", "tooltip": "<keywordMajor>Mini-Gnar – Passiv:</keywordMajor> <PERSON>er dritte Angriff oder jede dritte Fähigkeit gegen denselben Gegner verursacht zusätzlich <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ spell.gnarw:minitotaldamage }} plus {{ spell.gnarw:minipercenthpdamage*100 }}&nbsp;% des maximalen Lebens und verleiht <speed>{{ spell.gnarr:rhypermovementspeedpercent }}&nbsp;% Lauftempo</speed>, das über {{ minihasteduration }}&nbsp;Sekunden abfällt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Aufgedreht: <PERSON><PERSON><PERSON>", "Aufgedreht: <PERSON><PERSON><PERSON> basierend auf maximalem Le<PERSON> (%)", "Prügel: <PERSON><PERSON><PERSON>"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ minipercenthpdamage*100.000000 }}&nbsp;% -> {{ minipercenthpdamagenl*100.000000 }}&nbsp;%", "{{ megabasedamage }} -> {{ megabasedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GnarW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GnarE", "name": "Hops / K<PERSON>sch", "description": "Gnar springt an einen Ort und hüpft vom Kopf jeder Einheit, auf der er landet, und reist weiter.<br><br>Mega-Gnar ist zu groß, um weiter zu hüpfen, und landet stattdessen mit die Erde zum Beben bringender Kraft, wodurch er in dem Bereich um sich herum Schaden verursacht.", "tooltip": "<keywordMajor>Mini-Gnar:</keywordMajor> Gnar springt und erhält {{ spell.gnare:miniasduration }}&nbsp;Sekunden lang <attackSpeed>{{ spell.gnare:minibas*100 }}&nbsp;% Angriffstempo</attackSpeed>. Falls Gnar auf einer Einheit landet, hüpft er von ihr ein Stückchen weiter. Wenn Gnar auf einen Gegner hüpft, fügt er ihm <physicalDamage>{{ spell.gnare:minitotaldamage }}&nbsp;normalen Schaden</physicalDamage> zu und <status>verlangsamt</status> ihn kurzzeitig um {{ spell.gnare:movespeedmod*-100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Hops-<PERSON><PERSON><PERSON>", "Knirsch-Schaden", "Zusätzliches Angriffstempo", "Abklingzeit"], "effect": ["{{ minidamage }} -> {{ minidamageNL }}", "{{ megadamage }} -> {{ megadamageNL }}", "{{ minibas*100.000000 }}&nbsp;% -> {{ minibasnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "GnarE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "GnarR", "name": "GNAR!", "description": "Mega-Gnar wirft alles um ihn herum in eine ausgewählte Richtung, verursacht dabei Schaden und verlangsamt. <PERSON><PERSON>, der eine Mauer trifft, wird betä<PERSON>t und erleidet zusätzlichen Schaden.", "tooltip": "<keywordMajor>Mini-Gnar – Passiv:</keywordMajor> <PERSON><PERSON><PERSON><PERSON><PERSON> das <speed>Lauftempo</speed> von <spellName>Aufgedreht</spellName>.<br /><br /><keywordMajor>Mega-Gnar:</keywordMajor> Gnar rammt <PERSON>eg<PERSON> in der Nähe, fügt ihnen <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zu, <status>stößt</status> sie <status>zurück</status> und verlangsamt sie {{ rccduration }}&nbsp;Sekunden lang um {{ rslowpercent }}&nbsp;%. <PERSON><PERSON><PERSON>, die gegen eine Mauer geschleudert werden, erleiden <physicalDamage>{{ walldamage }}&nbsp;normalen Schaden</physicalDamage> und werden <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamungs-/Betäubungsdauer", "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rccduration }} -> {{ rccdurationNL }}", "{{ rhypermovementspeedpercent }}&nbsp;% -> {{ rhypermovementspeedpercentNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [90, 60, 30], "cooldownBurn": "90/60/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [590, 590, 590], "rangeBurn": "590", "image": {"full": "GnarR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Wutgen", "description": "Während Gnar kämpft, generiert er Wut. Bei maximaler <PERSON><PERSON> transformiert seine nächste Fähigkeit ihn in Mega-Gnar, wodurch er überlebensfähiger wird und auf neue Fähigkeiten zugreifen kann.", "image": {"full": "Gnar_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}