{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Хранитель времени", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "Рождественский Зилеан", "chromas": false}, {"id": "26002", "num": 2, "name": "Хип<PERSON>и Зилеан", "chromas": false}, {"id": "26003", "num": 3, "name": "Зилеан из пустыни Шурима", "chromas": false}, {"id": "26004", "num": 4, "name": "Путешественник во времени Зилеан", "chromas": false}, {"id": "26005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>овавая Луна", "chromas": false}, {"id": "26006", "num": 6, "name": "Сахарный Зилеан", "chromas": true}, {"id": "26014", "num": 14, "name": "Избранник зимы Зилеан", "chromas": true}], "lore": "Когда-то Зилеан был могущественным магом из Икатии. Но его родина была уничтожена Бездной прямо у него на глазах, и он стал одержим идеей изменить течение времени. Ни минуты не дав себе скорбеть о катастрофических потерях, он обратился к древней магии времени, чтобы просчитать все возможные исходы. Фактически став бессмертным, Зилеан теперь путешествует по прошлому, настоящему и будущему, меняя и деформируя поток времени вокруг себя, в бесконечных поисках того неуловимого момента, который позволит обернуть время вспять и не допустить разрушения Икатии.", "blurb": "Когда-то Зилеан был могущественным магом из Икатии. Но его родина была уничтожена Бездной прямо у него на глазах, и он стал одержим идеей изменить течение времени. Ни минуты не дав себе скорбеть о катастрофических потерях, он обратился к древней магии...", "allytips": ["Бомбу замедленного действия и Перемотку можно совмещать, чтобы прикрепить к цели две бомбы подряд. При этом первая взрывается, как только прикрепляется вторая, обездвиживая окружающих врагов.", "Искривление времени поможет вашим союзникам поймать спасающихся врагов или самим выжить после неудачной битвы.", "Временной сдвиг делает цель гораздо менее привлекательной для врагов, однако используйте его слишком рано - и он пропадет зря."], "enemytips": ["Если вы можете определить время действия умений Зилеанa, вам это может пригодиться: подождите, пока закончится время действия абсолютного умения, и нанесите финальный удар.", "Зилеан уязвим, если команда фокусируется на нем, но в остальном его тяжело убить. Соберитесь командой для нападения на него."], "tags": ["Support", "Mage"], "partype": "Мана", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "Бомба замедленного действия", "description": "Зилеан кидает бомбу в указанную точку. Бомба прикрепляется к первому бойцу, оказавшемуся неподалеку (приоритет отдается чемпионам). Через 3 секунды бомба взрывается, нанося урон всем окружающим врагам. Если бомба взрывается раньше из-за другой бомбы, пораженные враги также оглушаются.", "tooltip": "Зилеан кидает бомбу замедленного действия, которая прикрепляется к первому бойцу, оказавшемуся неподалеку. Через {{ e2 }} сек. бомба взрывается, нанося <magicDamage>{{ totaldamage }} магического урона</magicDamage>.<br /><br />Если бомба прикрепляется к бойцу, к которому уже прикреплена бомба, первая бомба взрывается немедленно, <status>оглушая</status> всех пораженных врагов на {{ e4 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Стоимость – @AbilityResourceName@", "Урон", "Продолжительность оглушения:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "ZileanW", "name": "Перемотка", "description": "Зилеан готовится к грядущей схватке, сокращая время перезарядки остальных базовых умений.", "tooltip": "Зилеан меняет ход времени, сокращая перезарядку всех остальных базовых умений на {{ e2 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " маны", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }} маны"}, {"id": "TimeWarp", "name": "Искривление времени", "description": "Зилеан искажает время вокруг цели, в результате чего враги замедляются, а союзники ускоряются.", "tooltip": "Зилеан <status>замедляет</status> вражеского чемпиона на {{ e2 }}% или дает союзному чемпиону <speed>{{ e2 }}% скорости передвижения</speed> на {{ e1 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Замедление", "Скорость передвижения"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "ChronoShift", "name": "Временной сдвиг", "description": "Зилеан накладывает руну защиты на себя или союзника, которая переносит чемпиона назад во времени, если тот получит смертельный удар.", "tooltip": "Зилеан накладывает на союзного чемпиона защитную временную руну на {{ rduration }} сек. Если этот союзник погибает, руна возвращает его назад во времени, помещая в Стазис на {{ revivestateduration }} сек., возрождая и восстанавливая <healing>{{ rtotalheal }} здоровья</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Стоимость – @AbilityResourceName@", "Лечение"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Время в бутылке", "description": "Зилеан сохраняет время в виде опыта и может передать его союзникам. Когда сохраненного опыта хватает для повышения уровня союзника, Зилеан может щелкнуть по нему правой кнопкой мыши, чтобы передать этот опыт. Сам Зилеан при этом получает столько же опыта, сколько дает.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}