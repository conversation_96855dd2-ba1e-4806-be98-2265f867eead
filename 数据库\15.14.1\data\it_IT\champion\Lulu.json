{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lulu": {"id": "<PERSON>", "key": "117", "name": "<PERSON>", "title": "la sacerdotessa delle fate", "image": {"full": "Lulu.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "117000", "num": 0, "name": "default", "chromas": false}, {"id": "117001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "117002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "117003", "num": 3, "name": "Lulu Addestratrice <PERSON>aghi", "chromas": true}, {"id": "117004", "num": 4, "name": "Lulu Prodigio Invernale", "chromas": false}, {"id": "117005", "num": 5, "name": "Lulu Festa in Piscina", "chromas": true}, {"id": "117006", "num": 6, "name": "<PERSON>", "chromas": false}, {"id": "117014", "num": 14, "name": "Lulu Incantatrice Cosmica", "chromas": true}, {"id": "117015", "num": 15, "name": "Lulu Guardiana in Pigiama", "chromas": false}, {"id": "117026", "num": 26, "name": "<PERSON>", "chromas": true}, {"id": "117027", "num": 27, "name": "<PERSON> Rit<PERSON> (edizione prestigio)", "chromas": false}, {"id": "117037", "num": 37, "name": "Lulu Domatrice di mostri", "chromas": true}, {"id": "117046", "num": 46, "name": "<PERSON>", "chromas": true}], "lore": "La maga yordle Lulu è nota per le sue illusioni oniriche e per le creature da fiaba che si manifestano nei suoi viaggi per Runeterra insieme a Pix. Lulu plasma la realtà a suo piacimento, piegando il tessuto del mondo ed evadendo dai limiti del reame fisico. C'è chi considera la sua magia innaturale, chi la trova pericolosa, ma lei è convinta che a tutti serva un tocco di incanto.", "blurb": "La maga yordle Lulu è nota per le sue illusioni oniriche e per le creature da fiaba che si manifestano nei suoi viaggi per Runeterra insieme a Pix. Lulu plasma la realtà a suo piacimento, piegando il tessuto del mondo ed evadendo dai limiti del reame...", "allytips": ["Lancia luccicante può essere scagliata con delle traiettorie strane in base a dove è il tuo cursore. Muovendo il tuo cursore più vicino a Lulu e Pix cambierà l'area dell'effetto in maniera considerevole.", "Considera di lanciare Aiuto, Pix! sui campioni che attaccano a distanza per il potenziamento di Pix e Crescita selvaggia sui tank o sui guerrieri per maggiore potenza d'ingaggio."], "enemytips": ["I colpi della fata di Lulu possono essere intercettati: nasconditi dietro i tuoi minion per evitare ulteriori attacchi dalla fata.", "Lulu eccelle quando gli avversari si concentrano sulla corsia. Non darle questa occasione! Usa delle tattiche di tormento per obbligare Lulu e la sua partner a uscire fuori dalla corsia."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 565, "hpperlevel": 92, "mp": 350, "mpperlevel": 55, "movespeed": 330, "armor": 26, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.6, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "LuluQ", "name": "Lancia luccicante", "description": "Pix e Lulu sparano entrambe un dardo di energia magica che rallenta in maniera considerevole tutti i nemici colpiti e li danneggia.", "tooltip": "Sia Lulu che Pix sparano un dardo perforante che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e un <status>rallentamento</status> di un {{ slowamount*-100 }}% che decresce nell'arco di {{ slowduration }} secondi.<br /><br />I nemici subiscono <magicDamage>{{ bonusmissiledamage }} danni magici</magicDamage> dai dardi aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "LuluQ.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuluW", "name": "Metamorfosi", "description": "Se lanciato su un alleato, gli conferisce velocità d'attacco e di movimento per un breve periodo. Se lanciato su un nemico, lo trasforma in un adorabile animaletto che non può attaccare né lanciare abilità.", "tooltip": "Se usata su un alleato, <PERSON> for<PERSON> <speed>{{ totalms }} velocità di movimento</speed> e il <attackSpeed>{{ e7 }}% di velocità d'attacco</attackSpeed> per {{ e5 }} secondi.<br /><br />Se usata su un nemico, <PERSON> lo <status>trasforma</status> per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Durata velocità di movimento e velocità d'attacco", "Velocità d'attacco", "Durata trasformazione"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ effect7amount*100.000000 }}% -> {{ effect7amountnl*100.000000 }}%", "{{ e3 }} -> {{ e3NL }}"]}, "maxrank": 5, "cooldown": [18, 18, 18, 18, 18], "cooldownBurn": "18", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [1.2, 1.4, 1.6, 1.8, 2], [-60, -60, -60, -60, -60], [3, 3.25, 3.5, 3.75, 4], [0.01, 0.01, 0.01, 0.01, 0.01], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.25", "0", "1.2/1.4/1.6/1.8/2", "-60", "3/3.25/3.5/3.75/4", "0.01", "20/22.5/25/27.5/30", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "LuluW.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuluE", "name": "Ai<PERSON>, <PERSON><PERSON>!", "description": "Se lanciato su un alleato, ordina a Pix di raggiungerlo e proteggerlo. A quel punto lo segue e o lo aiuta negli attacchi. Se lanciato su un nemico, ordina a Pix di saltargli addosso e danneggiarlo. A quel punto lo segue e ti conferisce la visione di quel nemico.", "tooltip": "Se usata su un alleato, Pix balza verso l'alleato e gli fornisce <spellName>Pix, compagna fatata</spellName> per {{ e1 }} secondi. Se l'alleato è un campione, Pix fornisce anche uno <shield>scudo da {{ totalshield }}</shield> per {{ e7 }} secondi.<br /><br />Se usata su un campione nemico, Pix salta verso il nemico, gli infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e fornisce <keywordStealth>Visione magica</keywordStealth> su di lui per {{ e6 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "<PERSON><PERSON>", "Costo in @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [6, 6, 6, 6, 6], [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [80, 120, 160, 200, 240], [25, 25, 25, 25, 25], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "6", "80/120/160/200/240", "50", "80/120/160/200/240", "25", "4", "2.5", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "LuluE.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuluR", "name": "Crescita selvaggia", "description": "Lulu ingigantisce un alleato, conferendogli una grande quantità di salute bonus e lanciando in aria i nemici nelle vicinanze. Per i prossimi secondi, quell'alleato ottiene un'aura che rallenta i nemici nelle vicinanze.", "tooltip": "<PERSON> ingigantisce un alleato, <status>respingendo</status> i nemici circostanti per {{ knockbackduration }} secondo. L'alleato ingigantito guadagna <healing>{{ totalbonushealth }} salute massima</healing> e <status>rallenta</status> i nemici circostanti di un {{ slowpercent }}% per {{ buffduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute bonus", "Rallentamento", "Ricarica"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "LuluR.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Pix, compagna fatata", "description": "Pix spara dei dardi magici di energia ogni volta che il campione che sta seguendo attacca un'altra unità. <PERSON>i dardi sono guidati, ma possono essere intercettati da altre unità.", "image": {"full": "Lulu_PixFaerieCompanion.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}