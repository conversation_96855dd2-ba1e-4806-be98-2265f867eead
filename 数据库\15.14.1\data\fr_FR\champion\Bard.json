{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Bard", "title": "<PERSON><PERSON><PERSON> errant", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "Bard sylvestre", "chromas": false}, {"id": "432005", "num": 5, "name": "Bard des neiges", "chromas": true}, {"id": "432006", "num": 6, "name": "Bard le barde", "chromas": false}, {"id": "432008", "num": 8, "name": "Bard astronaute", "chromas": true}, {"id": "432017", "num": 17, "name": "Bard Café Chouchous", "chromas": true}, {"id": "432026", "num": 26, "name": "Bard des rouleaux de <PERSON>", "chromas": true}, {"id": "432035", "num": 35, "name": "T1 Bard", "chromas": true}, {"id": "432037", "num": 37, "name": "Bard fleur spirituelle", "chromas": true}], "lore": "Voyageur d'au-delà des étoiles, Bard est un messager des bons augures qui combat pour maintenir l'équilibre dont la vie a besoin pour prospérer dans l'indifférence du chaos. Dans tout Runeterra, sa mystérieuse nature inspire des chants qui ne sont d'accord que sur un point : le vagabond cosmique est attiré par les reliques dotées d'un grand pouvoir magique. Accompagné par un chœur joyeux de Meeps, des esprits qui lui sont dévoués, il n'a d'actions que bénéfiques, même s'il a sa propre manière, un peu étrange, de servir le bien.", "blurb": "Voyageur d'au-delà des étoiles, Bard est un messager des bons augures qui combat pour maintenir l'équilibre dont la vie a besoin pour prospérer dans l'indifférence du chaos. Dans tout Run<PERSON>ra, sa mystérieuse nature inspire des chants qui ne sont...", "allytips": ["Il est important de collecter des carillons pour améliorer les attaques des Meeps, mais ne négligez pas votre partenaire de voie ! Tentez de faire une irruption fracassante sur votre voie en emmenant un allié sur votre Route magique.", "Laissez vos Dons du gardien se charger : ils rendent beaucoup plus de PV une fois leur potentiel max atteint.", "N'oubliez pas que les ennemis peuvent eux aussi emprunter vos Routes magiques et que votre ultime peut toucher vos alliés !"], "enemytips": ["Les adversaires de Bard peuvent eux aussi emprunter la Route magique. Si vous croyez que c'est prudent, vous pouvez suivre Bard.", "Vous pouvez détruire les Dons du gardien de Bard en marchant dessus. Ne laissez pas ses alliés s'en emparer facilement.", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, affecte les alliés, les ennemis, les monstres et les tourelles. Il peut parfois être avantageux de vous laisser toucher !"], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "Lien cosmique", "description": "Bard tire un projectile qui ralentit le premier ennemi touché et qui continue sur sa lancée. S'il touche un mur, il étourdit la cible initiale ; s'il touche un deuxième ennemi, il étourdit les deux cibles.", "tooltip": "Bard tire un projectile d'énergie, infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage> aux deux premiers ennemis touchés. La première cible touchée est <status>ralentie</status> de {{ slowamountpercentage }}% pendant {{ slowduration }} sec.<br /><br />Si le projectile touche un deuxième ennemi ou un mur, tous les ennemis touchés sont <status>étourdis</status> pendant {{ stunduration }} sec.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Du<PERSON>e de ralentissement", "Durée de l'étourdissement :", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Don <PERSON>ardien", "description": "Révèle un sanctuaire de soin qui gagne en puissance pendant quelques secondes. Il disparaît après avoir soigné et accéléré le premier allié qui le touche.", "tooltip": "Bard crée un sanctuaire de soin qui octroie <speed>+{{ calc_movespeed }} vitesse de déplacement</speed> (le bonus diminue en {{ movespeed_duration }} sec) et qui rend au moins <healing>{{ initialheal }} PV</healing> au premier allié qui le ramasse. Le sanctuaire se renforce pour rendre <healing>{{ maxheal }} PV</healing> après {{ chargeuptime }} sec d'existence.<br /><br />Jusqu'à {{ maxpacks }} sanctuaires peuvent être actifs simultanément. Si un champion ennemi marche sur un sanctuaire, ce dernier est détruit.<br /><br />Cette compétence a {{ ammo_limit }} charges.<br /><br />Sanctuaires actifs : {{ f1 }} / {{ f2 }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Soins de base", "Soins max", "Vitesse de d<PERSON>placement"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}% -> {{ movespeed_basenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Route magique", "description": "Bard ouvre un portail dans un élément du décor proche. Les alliés et les ennemis peuvent emprunter la route à sens unique ainsi créée en passant par le portail.", "tooltip": "Bard ouvre un portail à sens unique à travers un obstacle pendant {{ e1 }} sec. Tous les champions peuvent emprunter le portail en cliquant sur l'entrée lorsqu'ils sont à portée.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "<PERSON><PERSON>", "description": "Bard envoie de l'énergie spirituelle dans une zone, ce qui met brièvement en stase toutes les unités et toutes les tourelles.", "tooltip": "Bard projette de l'énergie protectrice dans une zone, mettant en stase toutes les unités et tous les bâtiments touchés pendant {{ rstasisduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Instinct du voyageur", "description": "<font color='#FF9900'>Meeps :</font> Bard attire des esprits inférieurs qui soutiennent ses attaques de base en infligeant des dégâts magiques supplémentaires. Quand Bard a collecté suffisamment de <font color='#cccc00'>carillons</font>, ses Meeps infligeront en plus des dégâts dans une zone et ralentiront les ennemis touchés.<br><br><font color='#FF9900'>Carillons :</font> d'antiques <font color='#cccc00'>carillons</font> que peut collecter Bard apparaissent aléatoirement. Ils lui font gagner de l'expérience, lui rendent du mana et lui octroient un bonus en vitesse de déplacement en dehors des combats.", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}