{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sion": {"id": "Sion", "key": "14", "name": "Sion", "title": "der untote Moloch", "image": {"full": "Sion.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "14000", "num": 0, "name": "default", "chromas": false}, {"id": "14001", "num": 1, "name": "Hextech-Sion", "chromas": false}, {"id": "14002", "num": 2, "name": "Barbaren-Sion", "chromas": false}, {"id": "14003", "num": 3, "name": "Holzfäller-Sion", "chromas": false}, {"id": "14004", "num": 4, "name": "Kriegstreiber-Sion", "chromas": false}, {"id": "14005", "num": 5, "name": "Mecha-Zero-Sion", "chromas": true}, {"id": "14014", "num": 14, "name": "Weltenbrecher-Sion", "chromas": true}, {"id": "14022", "num": 22, "name": "Schwarzfrost-Sion", "chromas": true}, {"id": "14030", "num": 30, "name": "High Noon-Sion", "chromas": true}, {"id": "14040", "num": 40, "name": "Kosmische<PERSON><PERSON>", "chromas": true}, {"id": "14049", "num": 49, "name": "Große Abrechnung-Sion", "chromas": false}], "lore": "<PERSON>on wurde in Noxus als Kriegsheld einer vergangenen Zeit verehrt, da er mit bloßen Händen einen demacianischen König erdrosselt hatte. Das ewige Vergessen bliebt ihm jedoch verwehrt: Er wurde wieder zum Leben erweckt, um seinem Reich sogar über den Tod hinaus zu dienen. Von seiner Menschlichkeit war jedoch keine Spur geblieben und so mordete er alles und jeden in seinem Weg, ganz gleich, unter welcher Flagge sie dienten. Dennoch stürzt sich Sion weiterhin waghalsig und mit primitiver Rüstung, die an sein verrottetes Fleisch geschweißt ist, in den Kampf und versucht, sich zwischen den Schlägen seiner mächtigen Axt daran zu erinnern, wer er wirklich ist.", "blurb": "<PERSON>on wurde in Noxus als Kriegsheld einer vergangenen Zeit verehrt, da er mit bloßen Händen einen demacianischen König erdrosselt hatte. Das ewige Vergessen bliebt ihm jedoch verwehrt: Er wurde wieder zum Leben erweckt, um seinem Reich sogar über den Tod...", "allytips": ["Du kannst deine Richtung nur sehr eingeschränkt verändern, solange „Unaufhaltbarer Ansturm“ aktiv ist. Versuche also, eine möglichst gerade Strecke zu deinem Ziel zu haben.", "„Brüllen des Schlächters“ eignet sich ideal, um einen sehr kräftigen Treffer mit „Dezimierender Schlag“ zu landen.", "<PERSON><PERSON> den Buff von „Seelenschmelze“ kannst du sehen, wie stark dein Schild noch ist, wodurch du die Explosion genau im richtigen Augenblick auslösen kannst."], "enemytips": ["Auch wenn Sion dennoch mit „Dezimierender Schlag“ trifft: Je früher er diesen ausführen muss, umso schwächer ist auch seine Wirkung.", "Nutze die Zeit nach Sions Tod, um dich in Position zu bringen und auf seine Rückkehr zu warten."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 655, "hpperlevel": 87, "mp": 400, "mpperlevel": 52, "movespeed": 345, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 1.3, "attackspeed": 0.679}, "spells": [{"id": "SionQ", "name": "Dezimierender Schlag", "description": "<PERSON>on holt zu einem kräftigen Hieb aus, der alle Gegner vor ihm trifft und Schaden verursacht, sobald er ihn ausführt. Wenn er weit genug ausholen kann, werden getroffene Gegner zusätzlich in die Luft geschleudert und betäubt.", "tooltip": "<charge>Aufladungsbeginn</charge>: <PERSON><PERSON> lädt einen schweren Schlag bis zu 2&nbsp;Sekunden lang auf.<br /><br /><release>Loslassen</release>: <PERSON>on lässt seine Axt niedersausen, <status>verlangsamt</status> Gegner kurzzeitig und fügt ihnen zwischen <physicalDamage>{{ mindamagetotal }} und {{ maxdamagetotal }}&nbsp;normalen Schaden</physicalDamage> zu (abhäng<PERSON> von der Aufladungszeit). Wenn Sion mindestens 1&nbsp;Sekunde lang aufgeladen hat, werden Gegner zwischen {{ basestuntime }} und 2,25&nbsp;Sekunden lang <status>hochgeschleudert</status> und <status>betäubt</status> (abhäng<PERSON> von der Aufladungszeit).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mindestschaden", "<PERSON><PERSON><PERSON>", "Angriffsschadenskalierung", "Angriffsschadenskalierung", "Abklingzeit"], "effect": ["{{ lowdamage }} -> {{ lowdamageNL }}", "{{ highdamage }} -> {{ highdamageNL }}", "{{ adratiomin*100.000000 }}&nbsp;% -> {{ adratiominnl*100.000000 }}&nbsp;%", "{{ adratiomax*100.000000 }}&nbsp;% -> {{ adratiomaxnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [7.5, 7.5, 7.5, 7.5, 7.5], [60, 60, 60, 60, 60], [150, 150, 150, 150, 150], [-0.8, -0.8, -0.8, -0.8, -0.8]], "effectBurn": [null, "0", "0", "0", "0", "2.5", "0", "7.5", "60", "150", "-0.8"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "SionQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SionW", "name": "<PERSON>lensch<PERSON><PERSON>", "description": "Sion schirmt sich selbst ab und kann die Fähigkeit 3 Sekunden später erneut einsetzen, um nahen Gegnern magischen Schaden zuzufügen. Wenn Sion Gegner tötet, erhält er passiv maximales Leben.", "tooltip": "<spellPassive>Passiv</spellPassive>: <PERSON><PERSON> erhält <scaleHealth>{{ hpperkill }}&nbsp;maximales Leben</scaleHealth>, wenn er eine Einheit tötet, bzw. {{ hpperchampkill }}&nbsp;maximales <PERSON><PERSON>, wenn er große Vasallen und große Monster tötet oder an einem Champion-Kill beteiligt ist.<br /><br /><spellActive>Aktiv</spellActive>: Sion erhält 6&nbsp;Sekunden lang einen <shield>Schild</shield> in Höhe von {{ totalshield }}. Wenn der Schild nach {{ e7 }}&nbsp;Sekunden noch aktiv ist, kann Sion die Fähigkeit <recast>reaktivieren</recast>, woraufhin der Schild explodiert und dem Ziel <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ totaldamage }} plus {{ e4 }}&nbsp;% des maximalen Lebens zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "<PERSON><PERSON><PERSON>", "Schildstärke basierend auf max. Leben – Skalierung", "Manakosten", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ shieldpercenthealthtooltip*100.000000 }}&nbsp;% -> {{ shieldpercenthealthtooltipnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [65, 70, 75, 80, 85], "costBurn": "65/70/75/80/85", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [40, 65, 90, 115, 140], [8, 10, 12, 14, 16], [14, 14, 14, 14, 14], [4, 4, 4, 4, 4], [15, 15, 15, 15, 15], [3, 3, 3, 3, 3], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "40/65/90/115/140", "8/10/12/14/16", "14", "4", "15", "3", "15", "6", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "SionW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SionE", "name": "Brüllen des Schlächters", "description": "Sion feuert eine Schockwelle mit kurzer Reichweite ab, die Schaden am ersten getroffenen Gegner verursacht, diesen außerdem verlangsamt und seine Rüstung verringert. Trifft die Schockwelle einen Vasallen oder ein Monster, werden sie zurückgeschleudert und verlangsamen Gegner in ihrem Weg, fügen ihnen Schaden zu und verringern deren Rüstung.", "tooltip": "<PERSON>on feuert eine <PERSON>ckwelle ab, die dem Ziel <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> zufügt, es {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount }}&nbsp;% <status>verlangsamt</status> und {{ armorshredduration }}&nbsp;Sekunden lang <scaleArmor>{{ armorshred }}&nbsp;% Rüstung</scaleArmor> entfernt. Getroffene Einheiten, die keine Champions sind, werden <status>zurückgestoßen</status>. <PERSON><PERSON><PERSON>, die von einer <status>zurückgestoßenen</status> Einheit getroffen werden, erleiden denselben Schaden und dieselben Effekte.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [40, 45, 50, 55, 60], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "40/45/50/55/60", "5", "40/45/50/55/60", "20", "30", "4", "2.5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SionE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SionR", "name": "Unaufhaltbarer Ansturm", "description": "<PERSON>on stürmt in eine Richtung los, wobei er immer schneller wird. Die Laufrichtung kann mit dem Cursor leicht verändert werden. Wenn er mit einem G<PERSON>ner zusamme<PERSON>ßt, schleudert er diesen hoch und fügt ihm Schaden abhängig von seiner zurückgelegten Distanz zu.", "tooltip": "Sion stürmt 8&nbsp;Sekunden lang unaufhaltbar in Richtung des Mauszeigers. <PERSON>on hält an, wenn er mit einem gegnerischen Champion oder einer Mauer kollidiert oder diese Fähigkeit <recast>erneut aktiviert</recast> wird.  <br /><br />Am Ende seines Ansturms verursacht Sion zwischen <physicalDamage>{{ mindamagetotal }} und {{ maxdamagetotal }}&nbsp;normalen Schaden</physicalDamage> (abhäng<PERSON> von der zurückgelegten Distanz). Gegner in Sions Nähe werden zwischen {{ minstunduration }} und {{ maxstunduration }}&nbsp;Sekunden lang <status>betäubt</status> (abhängig von der zurückgelegten Distanz). Gegner in einem größeren Umkreis werden 3&nbsp;Sekunden lang um {{ slowamount }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mindestschaden", "<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ slowamount }}&nbsp;% -> {{ slowamountNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 100, 60], "cooldownBurn": "140/100/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "SionR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON> im Tode", "description": "Nach seinem Tod wird Sion kurzzeitig reanimiert, allerdings fällt sein Leben rapide ab. Seine Angriffe werden extrem schnell, heilen ihn und verursachen zusätzlichen Schaden, der vom maximalen Leben des Ziels abhängt.", "image": {"full": "Sion_Passive1.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}