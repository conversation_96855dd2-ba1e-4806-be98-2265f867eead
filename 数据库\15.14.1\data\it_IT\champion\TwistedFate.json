{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TwistedFate": {"id": "TwistedFate", "key": "4", "name": "Twisted Fate", "title": "il maestro di carte", "image": {"full": "TwistedFate.png", "sprite": "champion4.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "4000", "num": 0, "name": "default", "chromas": false}, {"id": "4001", "num": 1, "name": "PAX Twisted Fate", "chromas": false}, {"id": "4002", "num": 2, "name": "Twisted Fate Fante di Cuori", "chromas": false}, {"id": "4003", "num": 3, "name": "Il Magnifico Twisted Fate", "chromas": false}, {"id": "4004", "num": 4, "name": "Twisted Fate Tango", "chromas": false}, {"id": "4005", "num": 5, "name": "Twisted Fate Mezzogiorno di Fuoco", "chromas": false}, {"id": "4006", "num": 6, "name": "Twisted <PERSON>", "chromas": false}, {"id": "4007", "num": 7, "name": "Twisted Fate dell'Oltretomba", "chromas": false}, {"id": "4008", "num": 8, "name": "Twisted Fate Arbitro", "chromas": false}, {"id": "4009", "num": 9, "name": "Twisted Fate Tagliaborse", "chromas": false}, {"id": "4010", "num": 10, "name": "Twisted <PERSON>", "chromas": false}, {"id": "4011", "num": 11, "name": "Twisted Fate Pulsefire", "chromas": true}, {"id": "4013", "num": 13, "name": "Twisted Fate dell'Odissea", "chromas": true}, {"id": "4023", "num": 23, "name": "Twisted Fate DWG", "chromas": true}, {"id": "4025", "num": 25, "name": "Twisted Fate Incubo nella Città del crimine", "chromas": true}, {"id": "4036", "num": 36, "name": "Twisted <PERSON> Ritmo <PERSON>", "chromas": false}, {"id": "4045", "num": 45, "name": "Twisted <PERSON>", "chromas": false}], "lore": "Twisted Fate è un famigerato giocatore di carte e truffatore. I suoi talenti lo hanno portato in ogni angolo del mondo, dove si è attirato le ire e l'ammirazione dei ricchi e degli stolti. Non prende mai nulla sul serio, affrontando le giornate con un ghigno beffardo e la sicurezza di chi non si preoccupa di niente. Twisted Fate ha sempre un asso nella manica, in tutti i sensi.", "blurb": "Twisted Fate è un famigerato giocatore di carte e truffatore. I suoi talenti lo hanno portato in ogni angolo del mondo, dove si è attirato le ire e l'ammirazione dei ricchi e degli stolti. Non prende mai nulla sul serio, affrontando le giornate con un...", "allytips": ["Coordinati con i tuoi alleati per decidere qual è il momento giusto per usare Destino e tendere un'imboscata ai nemici.", "I personaggi che si nascondono sono soliti fuggire dalla battaglia con poca salute. Approfitta dell'abilità di Destino di rivelare tutte le unità per finirle.", "Twisted Fate è usabile sia con attacco fisico sia con abilità, permettendoti di inserirlo in diverse squadre."], "enemytips": ["Concentrati a schivare le Carte letali quando il tuo campione non ha abbastanza salute per potersi permettere di essere colpito.", "Se hai poca salute, usa Destino come indicatore per correre verso la salvezza. Ti darà un vantaggio alla partenza per scappare da possibili imboscate."], "tags": ["Mage", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 2, "magic": 6, "difficulty": 9}, "stats": {"hp": 604, "hpperlevel": 108, "mp": 333, "mpperlevel": 39, "movespeed": 330, "armor": 24, "armorperlevel": 4.35, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "WildCards", "name": "<PERSON><PERSON>", "description": "Twisted Fate lancia tre carte, che infliggono danni a ogni unità nemica in cui passano attraverso.", "tooltip": "Twisted Fate lancia tre carte in un cono che infliggono <magicDamage>{{ totaldamage }} danni magici</magicDamage> ciascuna.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.75, 5.5, 5.25, 5], "cooldownBurn": "6/5.75/5.5/5.25/5", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "WildCards.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Pick<PERSON>ard", "name": "Scegli una carta", "description": "Twisted Fate sceglie una carta magica dal suo mazzo e la utilizza per il prossimo attacco, provocando effetti bonus.", "tooltip": "Twisted Fate inizia a mischiare il suo mazzo e può <recast>rilanciare</recast> questa abilità per bloccare una carta fra tre e potenziare il suo attacco successivo.<br /><li>La Carta blu infligge <magicDamage>{{ bluedamage }} danni magici</magicDamage> e ripristina <scaleMana>{{ e6 }} mana</scaleMana>.<li>La Carta rossa infligge <magicDamage>{{ reddamage }}</magicDamage> ai nemici vicini e li <status>rallenta</status> di {{ e2 }}% per 2,5 secondi.<li>La Carta d'oro infligge <magicDamage>{{ golddamage }}</magicDamage> e li <status>stordisce</status> per {{ e3 }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> blu", "<PERSON><PERSON><PERSON><PERSON> mana <PERSON> blu", "<PERSON><PERSON> rossa", "% rallentamento Carta rossa", "<PERSON><PERSON>'oro", "Durata stordimento Carta d'oro", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ e5 }} -> {{ e5NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [40, 60, 80, 100, 120], [30, 35, 40, 45, 50], [1, 1.25, 1.5, 1.75, 2], [30, 45, 60, 75, 90], [15, 22.5, 30, 37.5, 45], [70, 90, 110, 130, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/60/80/100/120", "30/35/40/45/50", "1/1.25/1.5/1.75/2", "30/45/60/75/90", "15/22.5/30/37.5/45", "70/90/110/130/150", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200, 200, 200], "rangeBurn": "200", "image": {"full": "PickACard.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CardmasterStack", "name": "<PERSON><PERSON> esplosivi", "description": "Ogni 4 at<PERSON><PERSON>, Twisted <PERSON> infligge danni bonus. <PERSON><PERSON><PERSON>, la sua velocità d'attacco aumenta.", "tooltip": "<spellPassive>Passiva:</spellPassive> Twisted <PERSON> ottiene <attackSpeed>{{ attackspeedbonus }}% velocità d'attacco</attackSpeed> e ogni quarto attacco infligge <magicDamage>{{ bonusdamage }} danni magici</magicDamage> aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> bonus", "Velocità d'attacco"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ attackspeedbonus }}% -> {{ attackspeedbonusNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "4", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Passiva", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "CardmasterStack.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "Passiva"}, {"id": "Destiny", "name": "<PERSON><PERSON>", "description": "Twisted Fate predice il futuro dei suoi avversari, r<PERSON><PERSON><PERSON> tutti i campioni nemici e abilitando l'uso di Portale, che teletrasporta Twisted Fate verso qualsiasi posizione bersaglio in 1,5 secondi.", "tooltip": "Twisted Fate si concentra sulle sue carte, conferendo <keywordStealth>Visione magica</keywordStealth> di tutti i campioni nemici sulla mappa per {{ e1 }} secondi e permettendogli di <recast>rilanciare</recast> questa abilità.<br /><br /><recast>Rilancia</recast>: Twisted Fate si teletrasporta fino a {{ e4 }} unità di distanza.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ recastduration }} -> {{ recastdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [170, 140, 110], "cooldownBurn": "170/140/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [6, 8, 10], [0, 0, 0], [0, 0, 0], [5500, 5500, 5500], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "6/8/10", "0", "0", "5500", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "Destiny.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> truccati", "description": "<PERSON>uando uccide un'unità, Twisted Fate tira i suoi dadi ''fortunati'' e riceve da 1 a 6 unità di oro bonus.", "image": {"full": "Cardmaster_SealFate.png", "sprite": "passive4.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}