{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rumble": {"id": "Rumble", "key": "68", "name": "럼블", "title": "기계 악동", "image": {"full": "Rumble.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "68000", "num": 0, "name": "default", "chromas": false}, {"id": "68001", "num": 1, "name": "정글 속 럼블", "chromas": false}, {"id": "68002", "num": 2, "name": "빌지워터 럼블", "chromas": false}, {"id": "68003", "num": 3, "name": "슈퍼 갤럭시 럼블", "chromas": false}, {"id": "68004", "num": 4, "name": "황야의 폭주족 럼블", "chromas": true}, {"id": "68013", "num": 13, "name": "우주 그루브 럼블", "chromas": true}, {"id": "68023", "num": 23, "name": "귀염둥이 카페 럼블", "chromas": true}], "lore": "럼블은 한 성깔 하는 젊은 요들로, 탁월한 발명가이기도 하다. 두 손과 고철 더미만으로 전기 작살과 소이 로켓을 장착한 거대 기계 로봇을 뚝딱 만들어냈다. 고철부품으로 만든 럼블의 작품을 비웃고 조롱하는 자가 있더라도 럼블은 눈 하나 깜박하지 않는다. 어차피 전장을 휩쓸 화염방사기는 자신의 손에 있으니까.", "blurb": "럼블은 한 성깔 하는 젊은 요들로, 탁월한 발명가이기도 하다. 두 손과 고철 더미만으로 전기 작살과 소이 로켓을 장착한 거대 기계 로봇을 뚝딱 만들어냈다. 고철부품으로 만든 럼블의 작품을 비웃고 조롱하는 자가 있더라도 럼블은 눈 하나 깜박하지 않는다. 어차피 전장을 휩쓸 화염방사기는 자신의 손에 있으니까.", "allytips": ["위험 상태 효과를 유지하면 스킬의 효율을 높일 수 있습니다. 하지만 스킬을 연달아 사용하면 쉽게 과열 상태가 됩니다.", "목표가 화염방사기 발사 거리에서 벗어나지 않도록 하십시오. 오랜 시간에 걸쳐 큰 피해를 입힐 수 있습니다.", "전투에서 이기고 있다면 궁극기를 사용하여 적의 탈출로를 막으십시오."], "enemytips": ["럼블의 열기 게이지를 주의 깊게 살피십시오. 과열 상태가 되어 스킬을 쓸 수 없는 상태가 되면 가까이 다가가 처치하십시오.", "럼블의 궁극기는 영향 지역 안에 있는 유닛에게 큰 피해를 줄 수 있습니다. 미사일이 떨어지는 게 보이면 최대한 빨리 대피하십시오.", "럼블의 스킬은 거의 대부분 마법 피해입니다. 큰 피해를 방지하려면 마법 저항력을 높이십시오."], "tags": ["Fighter", "Mage"], "partype": "열기", "info": {"attack": 3, "defense": 6, "magic": 8, "difficulty": 10}, "stats": {"hp": 655, "hpperlevel": 105, "mp": 150, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 1.55, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.85, "attackspeed": 0.644}, "spells": [{"id": "RumbleFlameThrower", "name": "화염방사기", "description": "럼블이 3초 동안 정면을 향해 원뿔 형태의 공격을 가하며 적에게 불을 붙이고 마법 피해를 입힙니다. 위험 상태일 경우 피해가 증가합니다.", "tooltip": "럼블이 화염방사기를 사용해 {{ flamespitterduration }}초 동안 <magicDamage>{{ flatdamage }} +최대 체력의 {{ healthdamage*100 }}%에 해당하는 마법 피해</magicDamage>를 입힙니다. 미니언 공격 시 피해량이 <attention>{{ minionmod*100 }}%</attention>로 감소합니다.<br /><br /><keywordMajor>위험 상태:</keywordMajor> 마법 피해량이 <magicDamage>{{ empowereddamage }} +최대 체력의 {{ empoweredhealth }}</magicDamage>만큼 증가합니다.<br /><br /><rules>체력 비례 피해량은 몬스터에게 최대 {{ monstercap }}의 피해를 입힙니다. </rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "최대 체력 %", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ healthdamage*100.000000 }}% -> {{ healthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " 열기", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RumbleFlameThrower.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ initialheatcost }} 열기"}, {"id": "RumbleShield", "name": "고철 방패", "description": "럼블이 방어막을 전개하여 피해를 입지 않도록 자신을 보호하고 순간적으로 이동 속도를 높입니다. 위험 상태에서는 방어막의 내구력과 럼블의 이동 속도 증가량이 증가합니다.", "tooltip": "럼블이 방어막을 전개하여 {{ shieldduration.1 }}초 동안 <shield>{{ totalshield }}의 피해를 흡수하는 보호막</shield>을 얻고 {{ movespeedduration }}초 동안 이동 속도가 <speed>{{ movespeed*100 }}%</speed> 증가합니다.<br /><br /><keywordMajor>위험 상태:</keywordMajor> <shield>{{ empoweredshield }}의 피해를 흡수하는 보호막</shield>을 얻고 <speed>이동 속도가 {{ empoweredms }}</speed> 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "이동 속도"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [10, 15, 20, 25, 30], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "10/15/20/25/30", "20", "0", "1.5", "1", "0", "0", "0", "0"], "vars": [], "costType": " 열기", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "RumbleShield.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ heatcost }} 열기"}, {"id": "RumbleGrenade", "name": "전기 작살", "description": "럼블이 작살을 발사하여 대상을 감전시키고 마법 피해를 입히면서 이동 속도와 마법 저항력을 감소시킵니다. 럼블은 최대 2개의 작살을 보유할 수 있습니다. 위험 상태일 경우 피해량과 적 둔화율이 증가합니다.", "tooltip": "럼블이 전기 작살을 발사하여 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ slowduration }}초 동안 {{ baseslowamount }}% <status>둔화</status>시키며 {{ shredduration }}초 동안 <scaleMR>마법 저항력</scaleMR>을 {{ percmagicpen*100 }}% 감소시킵니다.<br /><br />이미 <status>둔화</status> 상태인 적을 이 스킬로 공격하면 <status>둔화</status> 효과가 {{ empoweredslowamount }}%로 증가하며 적의 <scaleMR>마법 저항력</scaleMR>이 {{ enhancedmagicpen*100 }}% 감소합니다.<br /><br /><keywordMajor>위험 상태:</keywordMajor> <magicDamage>{{ empdamage }}의 마법 피해</magicDamage>를 입히고 <status>둔화</status> 효과와 <scaleMR>마법 저항력</scaleMR> 감소 효과가 50% 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "마법 저항력 감소"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslowamount }}% -> {{ baseslowamountNL }}%", "{{ percmagicpen*100.000000 }}% -> {{ percmagicpennl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " 열기", "maxammo": "2", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RumbleGrenade.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ firstcastheatcost }} 열기"}, {"id": "RumbleCarpetBomb", "name": "이퀄라이저 미사일", "description": "럼블이 로켓을 한꺼번에 발사하여 화염의 벽을 만들면, 해당 지점을 지나는 적은 피해를 입고 속도가 느려집니다.", "tooltip": "럼블이 일직선으로 로켓을 발사하여 {{ trailduration }}초 동안 지속되는 불타는 궤적을 만듭니다. 궤적은 적을 {{ slowamount }}% <status>둔화</status>시키고 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />이 스킬을 사용하는 동안 클릭하고 드래그하여 궤적의 방향을 지정할 수 있습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["초당 피해량", "재사용 대기시간"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [1750, 1750, 1750], "rangeBurn": "1750", "image": {"full": "RumbleCarpetBomb.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "고철장 거인", "description": "럼블은 스킬을 사용할 때마다 열기를 얻습니다. 열기가 50%에 달하면 럼블은 위험 상태에 들어갑니다. 위험 상태가 되면 럼블의 모든 스킬은 추가 효과를 얻습니다. 열기가 100%에 달하면 과열 상태가 되어 공격 속도가 증가하고 기본 공격에 마법 피해가 추가되며 몇 초간 스킬을 사용할 수 없게 됩니다.", "image": {"full": "Rumble_JunkyardTitan1.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}