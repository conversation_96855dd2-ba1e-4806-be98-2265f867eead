{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rakan": {"id": "<PERSON><PERSON>", "key": "497", "name": "ラカン", "title": "魅惑の翼", "image": {"full": "Rakan.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "497000", "num": 0, "name": "default", "chromas": false}, {"id": "497001", "num": 1, "name": "宇宙の暁ラカン", "chromas": false}, {"id": "497002", "num": 2, "name": "スイートハート ラカン", "chromas": false}, {"id": "497003", "num": 3, "name": "SSG ラカン", "chromas": false}, {"id": "497004", "num": 4, "name": "iG ラカン", "chromas": false}, {"id": "497005", "num": 5, "name": "スターガーディアン ラカン", "chromas": true}, {"id": "497009", "num": 9, "name": "古の賢樹ラカン", "chromas": true}, {"id": "497018", "num": 18, "name": "アルカナ ラカン", "chromas": true}, {"id": "497027", "num": 27, "name": "破られし盟約ラカン", "chromas": true}, {"id": "497036", "num": 36, "name": "光の目覚めスターガーディアン ラカン", "chromas": false}, {"id": "497037", "num": 37, "name": "龍術師ラカン", "chromas": true}, {"id": "497038", "num": 38, "name": "プレステージ龍術師ラカン", "chromas": false}, {"id": "497047", "num": 47, "name": "バトルアカデミア <PERSON>ン", "chromas": true}], "lore": "気まぐれで魅力的なラカンは、ヴァスタヤの悪名高きトラブルメーカーであり、ロトラン部族史上最高のバトルダンサーだ。アイオニア高地に住む人間たちにとって、彼の名はずっと、無礼講のお祭り、どんちゃん騒ぎ、アナーキーな音楽などと同意義だった。この活力に溢れた旅芸人が反逆者ザヤのパートナーであり、彼女の大義に命を捧げているなどと考える者はまずいない。", "blurb": "気まぐれで魅力的なラカンは、ヴァスタヤの悪名高きトラブルメーカーであり、ロトラン部族史上最高のバトルダンサーだ。アイオニア高地に住む人間たちにとって、彼の名はずっと、無礼講のお祭り、どんちゃん騒ぎ、アナーキーな音楽などと同意義だった。この活力に溢れた旅芸人が反逆者ザヤのパートナーであり、彼女の大義に命を捧げているなどと考える者はまずいない。", "allytips": ["ラカンの能力を最大限に活かすには、味方に側にいてもらう必要がある。", "ラカンがダッシュするスピードは移動速度に応じて上昇する。増加したスピードを活かして敵を驚かせてやろう！", "危険は自らが招いてこその楽しみ。"], "enemytips": ["ラカンの移動スキルは到着地点が事前にわかる。これを活かして優位に立とう。", "素早く行動妨害を与えられるチャンピオンは、ラカンに対して効果的だ。", "周りに味方がいない時にラカンを捕まえれば、彼の機動力を大きく抑制できる。彼が1人の時を狙おう。"], "tags": ["Support"], "partype": "マナ", "info": {"attack": 2, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 315, "mpperlevel": 50, "movespeed": 335, "armor": 30, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 300, "hpregen": 5, "hpregenperlevel": 0.5, "mpregen": 8.75, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.635}, "spells": [{"id": "RakanQ", "name": "キラリ羽根", "description": "魔法の羽根を投げて魔法ダメージを与える。敵チャンピオンかエピックモンスターに当たると、ラカンが味方を回復できる。", "tooltip": "魔法の羽根を投げて、最初に当たった敵ユニットに<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />羽根がチャンピオンかエピックジャングルモンスターに当たると、{{ healdelay }}秒後、または自身が味方チャンピオンに触れた時に、自身と周囲の味方の<healing>体力を{{ totalheal }}</healing>回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RakanQ.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RakanW", "name": "華麗なる登場", "description": "指定地点にダッシュして、到着時に周囲の敵ユニットをノックアップさせる。", "tooltip": "ダッシュしてから回転して宙に舞い上がり、{{ knockupduration }}秒間<status>ノックアップ</status>させて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basecost }} -> {{ basecostNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RakanW.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RakanE", "name": "バトルダンス", "description": "味方チャンピオンのところまで飛びシールドを付与する。このスキルは少しの間だけコスト無しで再使用できる。", "tooltip": "味方チャンピオンのところまでダッシュして、{{ e3 }}秒間<shield>耐久値{{ totalshield }}のシールド</shield>を付与する。<br /><br />このスキルは{{ e2 }}秒以内なら一度だけ<recast>再発動</recast>できる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [3, 3, 3, 3, 3], [20, 18, 16, 14, 12], [40, 45, 50, 55, 60], [700, 700, 700, 700, 700], [1000, 1000, 1000, 1000, 1000], [1150, 1150, 1150, 1150, 1150], [1250, 1250, 1250, 1250, 1250], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "5", "3", "20/18/16/14/12", "40/45/50/55/60", "700", "1000", "1150", "1250", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "RakanE.png", "sprite": "spell10.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "RakanR", "name": "みんなオレに夢中", "description": "移動速度が増加して、触れた敵ユニットにチャームと魔法ダメージを与える。", "tooltip": "{{ e2 }}秒間、<speed>移動速度が{{ e5 }}%</speed>増加する。自身が触れた敵に初回のみ<magicDamage>{{ totaldamagetooltip }}の魔法ダメージ</magicDamage>と{{ e3 }}秒間の<status>チャーム効果</status>を与える。最初にチャンピオンに触れたときには、自身の<speed>移動速度が{{ e6 }}%</speed>増加し、徐々に元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["チャーム効果時間", "ダメージ", "クールダウン"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 200, 300], [4, 4, 4], [1, 1.25, 1.5], [0.25, 0.25, 0.25], [75, 75, 75], [150, 150, 150], [150, 150, 150], [130, 110, 90], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "100/200/300", "4", "1/1.25/1.5", "0.25", "75", "150", "150", "130/110/90", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [150, 150, 150], "rangeBurn": "150", "image": {"full": "RakanR.png", "sprite": "spell10.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "神秘の翼", "description": "ラカンは定期的にシールドを獲得する。", "image": {"full": "Rakan_P.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}