{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Taric": {"id": "<PERSON><PERSON>", "key": "44", "name": "<PERSON><PERSON>", "title": "<PERSON> <PERSON><PERSON><PERSON>", "image": {"full": "Taric.png", "sprite": "champion4.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "44000", "num": 0, "name": "default", "chromas": false}, {"id": "44001", "num": 1, "name": "Smaragd-Taric", "chromas": false}, {"id": "44002", "num": 2, "name": "Rosenquarz-Taric", "chromas": false}, {"id": "44003", "num": 3, "name": "Blutjaspis-Taric", "chromas": false}, {"id": "44004", "num": 4, "name": "Poolparty-Taric", "chromas": true}, {"id": "44009", "num": 9, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "44018", "num": 18, "name": "Weltraum-Groove-Taric", "chromas": true}, {"id": "44027", "num": 27, "name": "Schicksalsbrecher Taric", "chromas": true}], "lore": "Taric ist der Aspekt des Beschützers und verfügt als Avatar von Runeterras Gott des Lebens, der Liebe und der Schönheit über unglaubliche Macht. Wegen eines Pflichtversäumnisses beschämt und von seiner Heimat Demacia verbannt, erklo<PERSON> Taric den Targon um Buße zu tun, nur um dort unter den Sternen eine völlig neue Bestimmung zu entdecken. Nun ist der <PERSON><PERSON><PERSON> von <PERSON> von der Macht des uralten Targon erfüllt und steht wachsam gegen die schleichende Verderbnis der Leere.", "blurb": "Taric ist der Aspekt des Beschützers und verfügt als Avatar von Runeterras Gott des Lebens, der Liebe und der Schönheit über unglaubliche Macht. Wegen eines Pflichtversäumnisses beschämt und von seiner Heimat Demacia verbannt, erk<PERSON><PERSON> den Targon...", "allytips": ["Die Abklingzeitverringerung von „Bravour“ lässt bei Taric die Abklingzeitverringerung von Gegenständen wie „Gefrorenes Herz“, „Eisgeborenen-Handschuhe“ und „Geistessicht“ besonders mächtig werden.", "„Günstling der Sterne“ mit wenig Ladungen zu nutzen, ist nicht besonders mana-effizient, kann aber dank „Bravour“ den Schaden über Zeit deutlich steigern.", "Anstatt „Kosmischer Glanz“ bis zum letzten Moment aufzuheben und zu riskieren, dass jemand durch die Verzögerung stirbt, solltest du ihn e<PERSON>zu<PERSON>zen, sobald du weißt, dass es sicher zum Teamkampf kommen wird."], "enemytips": ["Tarics ultimative Fähigkeit „Kosmischer Glanz“ hat eine lange Verzögerung, bevor der Effekt wirkt. Du solltest so schnell wie möglich entscheiden, ob du dich aus dem Kampf zurückziehst oder versuchst, seine Verbündeten zu töten, bevor die Wirkung eintritt.", "<PERSON><PERSON> reduziert die Abklingzeiten seiner Zauber durch normale Angriffe mit Hilfe von „Bravour“. Versuche, ihn in Teamkämpfen auf Abstand zu halten und in der Lane zu bestrafen, wenn er sich der Vasallenwelle nähert."], "tags": ["Support", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 3}, "stats": {"hp": 645, "hpperlevel": 99, "mp": 300, "mpperlevel": 60, "movespeed": 340, "armor": 40, "armorperlevel": 4.3, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2, "attackspeed": 0.625}, "spells": [{"id": "TaricQ", "name": "Günstling der Sterne", "description": "Heilt je nach Ladung verbündete Champions in der Nähe. Durch „Bravour“ verstärkte Angriffe gewähren 1&nbsp;Ladung „Günstling der Sterne“.", "tooltip": "<spellPassive>Passiv:</spellPassive> Alle {{ stackcooldown }}&nbsp;Sekunden und wenn ein Angriff mit <spellName>Bravour</spellName> trifft, erhält Taric eine Steigerung (maximal {{ e6 }}).<br /><br /><spellActive>Aktiv:</spellActive> Taric verbraucht alle Steigerungen, um pro Steigerung bei verbündeten Champions in der Nähe <healing>{{ healingperstack }}&nbsp;Leben</healing> wiederherzustellen (<healing>{{ maxstackhealing }}</healing> bei {{ e6 }}&nbsp;Steigerungen).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Maximale Aufladungen", "Maximale Heilung"], "effect": ["{{ e6 }} -> {{ e6NL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [25, 25, 25, 25, 25], [1, 1, 1, 1, 1], [0.15, 0.3, 0.45, 0.6, 0.75], [0.75, 1.5, 2.25, 3, 3.75], [25, 50, 75, 100, 125], [1, 2, 3, 4, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25", "1", "0.15/0.3/0.45/0.6/0.75", "0.75/1.5/2.25/3/3.75", "25/50/75/100/125", "1/2/3/4/5", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Mana, alle Aufladungen", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "TaricQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}&nbsp;Mana, alle Aufladungen"}, {"id": "TaricW", "name": "Bastion", "description": "Erhöht passiv die Rüstung von Taric und einem verbündeten Champion mit „Bastion“.<br><br><PERSON>h<PERSON>tzt aktiv einen Verbündeten und gewährt ihm oder ihr „Bastion“, solange sie in Tarics Nähe bleiben. <PERSON><PERSON> wirkt seine Fähigkeiten auch durch seinen Bastionspartner.", "tooltip": "<spellPassive>Passiv: </spellPassive> <PERSON><PERSON> erhält <scaleArmor>{{ bonusarmor }}&nbsp;Rüstung</scaleArmor> und stellt eine Verbindung zwischen ihm und dem Verbündeten her, den er mit dieser Fähigkeit ausgewählt hat. Solange er sich in der Nähe befindet, erhält der Verbündete <scaleArmor>{{ bonusarmor }}&nbsp;Rüstung</scaleArmor> und Taric führt seine Fähigkeiten von seiner Position und der des ausgewählten Verbündeten aus.<br /><br /><spellPassive>Aktiv: </spellPassive>Taric verbindet sich mit einem verbündeten Champion und gewährt ihm {{ e3 }}&nbsp;Sekunden lang einen <shield>Schild</shield> in Höhe von {{ e2 }}&nbsp;% des maximalen Lebens.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rüstung (Passiv)", "Schildstärke – Skalierung"], "effect": ["{{ armorbonuspercentage*100.000000 }}&nbsp;% -> {{ armorbonuspercentagenl*100.000000 }}&nbsp;%", "{{ e2 }}&nbsp;% -> {{ e2NL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [7, 8, 9, 10, 11], [2.5, 2.5, 2.5, 2.5, 2.5], [1000, 1000, 1000, 1000, 1000], [1300, 1300, 1300, 1300, 1300], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "7/8/9/10/11", "2.5", "1000", "1300", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "TaricW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TaricE", "name": "Blenden", "description": "<PERSON><PERSON> bereitet einen Sternlichtstrahl vor, der nach einer kurzen Verzögerung magischen Schaden anrichtet und Gegner betäubt.", "tooltip": "<PERSON><PERSON> erzeugt einen Sternenlichtstrahl, der nach {{ e3 }}&nbsp;Sekunde(n) explodiert, <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht und Gegner {{ e2 }}&nbsp;Sekunden lang <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [90, 130, 170, 210, 250], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/130/170/210/250", "1.5", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "TaricE.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TaricR", "name": "Kosmischer Glanz", "description": "Nach einer Verzögerung umhüllt kosmische Energie nahe Verbündete, die dadurch für kurze Zeit unverwundbar werden.", "tooltip": "<PERSON><PERSON> ruft den Himmel um Schutz an. Nach {{ initialdelay }}&nbsp;Sekunden werden verbündete Champions in der Nähe {{ invulnduration }}&nbsp;Sekunden lang unverwundbar.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 150, 120], "cooldownBurn": "180/150/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "TaricR.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Gewirkte Fähigkeiten verstärken Tarics nächste 2&nbsp;normale Angriffe, die dann zusätzlichen magischen Schaden verursachen, die Abklingzeiten seiner Fähigkeiten verringern und schnell hintereinander ausgelöst werden.", "image": {"full": "Taric_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}