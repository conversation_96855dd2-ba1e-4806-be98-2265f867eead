{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Draven": {"id": "Draven", "key": "119", "name": "Draven", "title": "the Glorious Executioner", "image": {"full": "Draven.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "119000", "num": 0, "name": "default", "chromas": false}, {"id": "119001", "num": 1, "name": "Soul Reaver Draven", "chromas": false}, {"id": "119002", "num": 2, "name": "Gladiator Draven", "chromas": false}, {"id": "119003", "num": 3, "name": "Primetime Draven", "chromas": true}, {"id": "119004", "num": 4, "name": "Pool Party Draven", "chromas": false}, {"id": "119005", "num": 5, "name": "Beast Hunter Draven", "chromas": false}, {"id": "119006", "num": 6, "name": "Draven Draven", "chromas": false}, {"id": "119012", "num": 12, "name": "Santa Draven", "chromas": false}, {"id": "119013", "num": 13, "name": "Mecha Kingdoms Draven", "chromas": true}, {"id": "119020", "num": 20, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "119029", "num": 29, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "119039", "num": 39, "name": "Fright Night Draven", "chromas": true}, {"id": "119048", "num": 48, "name": "La Ilusión Draven", "chromas": true}, {"id": "119058", "num": 58, "name": "Grand Reckoning Draven", "chromas": true}], "lore": "<PERSON>, warrior yang dikenal berna<PERSON>ckoner saling bertarung di arena, tempat darah bertumpahan dan kekuatan diuji. Tetapi tak satu pun yang mendekati kehebatan Draven. Sebagai seorang mantan tentara, dia merasa penonton menyoraki sikap dramatis dan keahlian spinning axes miliknya. Keranjingan mempertontonkan keberingasannya sendiri, Draven bersumpah akan mengalahkan siapa pun agar namanya terus dielu-elukan di kerajaan itu selamanya.", "blurb": "<PERSON>, warrior yang dikenal berna<PERSON>oner saling bertarung di arena, tempat darah bertumpahan dan kekuatan diuji. Tetapi tak satu pun yang mendekati kehebatan Draven. Sebagai seorang mantan tentara, dia merasa penonton menyoraki sikap dramatis dan...", "allytips": ["<PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON> akan jatuh di dekat posisinya. Mungkin jatuh tepat di atasnya, atau sedikit ke kanan atau kiri.", "Jika Draven bergerak <PERSON>, Spinning Axe akan mengarah ke arah gerakannya. <PERSON>akan ini untuk mengontrol ke mana Spinning Axe akan jatuh."], "enemytips": ["Luncurkan skillshot ke arah posisi jatuhnya Spinning Axe milik <PERSON>.", "Ganggu Draven dengan tujuan membuatnya menjatuhkan kapaknya. <PERSON><PERSON> kamu ber<PERSON><PERSON>, kekuatan<PERSON> akan berkurang drastis."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 8}, "stats": {"hp": 675, "hpperlevel": 104, "mp": 361, "mpperlevel": 39, "movespeed": 330, "armor": 29, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.7, "mpregen": 8.05, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.6, "attackspeedperlevel": 2.7, "attackspeed": 0.679}, "spells": [{"id": "DravenSpinning", "name": "Spinning Axe", "description": "Serangan Draven berikutnya akan men<PERSON> physical damage bonus. Kapak ini akan memantul dari target ke udara. Jika Draven men<PERSON>, dia akan otomatis menyiapkan Spinning Axe berikutnya. Draven bisa membawa 2 Spinning Axe sekaligus.", "tooltip": "Draven menyiapkan <keywordMajor>Spinning Axe</keywordMajor>, membuat Serangan berikutnya menghasilkan tambahan <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> dan memantul ke udara. Jika Draven menang<PERSON>nya, dia akan otomatis menyiapkan <keywordMajor>Spinning Axe</keywordMajor> berikutnya.<br /><br />Draven bisa membawa 2 <keywordMajor>Spinning Axe</keywordMajor> sekaligus.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Persentase AD Bonus", "Cooldown"], "effect": ["{{ e5 }}-> {{ e5NL }}", "{{ e2 }}%-> {{ e2NL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [100, 100, 100, 100, 100], [75, 85, 95, 105, 115], [30, 35, 40, 45, 50], [5.75, 5.75, 5.75, 5.75, 5.75], [40, 45, 50, 55, 60], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100", "75/85/95/105/115", "30/35/40/45/50", "5.75", "40/45/50/55/60", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DravenSpinning.png", "sprite": "spell3.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenFury", "name": "Blood Rush", "description": "Draven mendapatkan peningkatan Move Speed dan Attack Speed. Bonus Move Speed akan berkurang dengan cepat selama durasi. Menangkap Spinning Axe akan me-refresh cooldown Blood Rush.", "tooltip": "Draven menjadi Ghost, mendapatkan <speed>{{ e2 }}% Move Speed</speed> yang berkurang dalam {{ e3 }} detik dan <attackSpeed>{{ e4 }}% Attack Speed</attackSpeed> selama {{ e5 }} detik.<br /><br />Saat Draven menangkap <keywordMajor>Spinning Axe</keywordMajor>, Cooldown Ability ini akan di-refresh.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attack Speed", "@AbilityResourceName@ Cost", "Move Speed"], "effect": ["{{ e4 }}%-> {{ e4NL }}%", "{{ cost }}-> {{ costNL }}", "{{ e2 }}%-> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [40, 35, 30, 25, 20], "costBurn": "40/35/30/25/20", "datavalues": {}, "effect": [null, [4, 5, 6, 7, 8], [50, 55, 60, 65, 70], [1.5, 1.5, 1.5, 1.5, 1.5], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [-0.062, -0.069, -0.075, -0.081, -0.087], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/5/6/7/8", "50/55/60/65/70", "1.5", "20/25/30/35/40", "3", "-0.062/-0.069/-0.075/-0.081/-0.087", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "DravenFury.png", "sprite": "spell3.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenDoubleShot", "name": "Stand Aside", "description": "Draven melem<PERSON> kapak, <PERSON><PERSON><PERSON><PERSON><PERSON> physical damage pada target yang kena dan mendorongnya ke samping. Target yang kena mendapat efek slow.", "tooltip": "Draven melemparkan kapak ke samping yang men<PERSON><PERSON>lkan <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>, <status>Knock Back</status>, dan <status>Slow</status> sebesar {{ e2 }}% selama {{ e3 }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Slow", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ e2 }}%-> {{ e2NL }}%", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [75, 110, 145, 180, 215], [20, 25, 30, 35, 40], [2, 2, 2, 2, 2], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/110/145/180/215", "20/25/30/35/40", "2", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "DravenDoubleShot.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DravenRCast", "name": "Whirling Death", "description": "Draven melemparkan dua kapak besar untuk menghasilkan physical damage pada setiap unit yang diserang. Whirling Death perlahan berbalik arah dan kembali ke Draven setelah menyerang champion musuh. Draven juga bisa mengaktifkan ability ini agar kapak yang sedang dilemparkan kembali lebih cepat. <PERSON><PERSON><PERSON><PERSON><PERSON> damage lebih sedikit untuk tiap unit yang kena dan direset saat kapak berbalik arah. Mengeksekusi musuh yang memiliki health kurang dari jumlah stack Adoration milik Draven.", "tooltip": "Draven melemparkan dua kapak besar yang menghasilkan <physicalDamage>{{ rcalculateddamage }} physical damage</physicalDamage>. Saat mengenai champion atau melakukan <recast>Recast</recast>, kapak akan berbalik arah dan kembali ke Draven. Kapak menghasilkan {{ rdamagereductionperhit*100 }}% lebih sedikit damage untuk tiap musuh yang kena, menurun ke minimum {{ rmindamagepercent }}%.<br /><br />Jika <keywordMajor>Whirling Death</keywordMajor> membuat health champion musuh menjadi kurang dari {{ rpassivestackscoefficient*100 }}% stack <keywordMajor>League of Draven</keywordMajor> Draven saat ini ({{ rpassivetruedamage }}), Draven akan mengeksekusi mereka.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "<PERSON>sio <PERSON> Bonus"], "effect": ["{{ rbasedamage }}-> {{ rbasedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ rcoefficient*100.000000 }}%-> {{ rcoefficientnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20000, 20000, 20000], "rangeBurn": "20000", "image": {"full": "DravenRCast.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "League of Draven", "description": "Draven mendapatkan Adoration dari para penggemarnya saat menangkap Spinning Axe atau kill minion, monster, atau turret. Melakukan kill pada champion musuh memberi Draven gold bonus berdasarkan Adoration yang dia miliki.", "image": {"full": "Draven_passive.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}