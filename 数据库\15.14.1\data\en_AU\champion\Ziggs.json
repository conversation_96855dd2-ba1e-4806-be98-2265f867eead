{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ziggs": {"id": "<PERSON><PERSON>", "key": "115", "name": "<PERSON><PERSON>", "title": "the Hexplosives Expert", "image": {"full": "Ziggs.png", "sprite": "champion5.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "115000", "num": 0, "name": "default", "chromas": false}, {"id": "115001", "num": 1, "name": "Mad <PERSON>", "chromas": false}, {"id": "115002", "num": 2, "name": "Major <PERSON>", "chromas": false}, {"id": "115003", "num": 3, "name": "Pool <PERSON> Ziggs", "chromas": false}, {"id": "115004", "num": 4, "name": "Snow Day Ziggs", "chromas": false}, {"id": "115005", "num": 5, "name": "Master <PERSON>ani<PERSON>", "chromas": false}, {"id": "115006", "num": 6, "name": "Battle <PERSON>", "chromas": false}, {"id": "115007", "num": 7, "name": "<PERSON>", "chromas": true}, {"id": "115014", "num": 14, "name": "<PERSON>", "chromas": true}, {"id": "115023", "num": 23, "name": "Hextech <PERSON>", "chromas": false}, {"id": "115024", "num": 24, "name": "BZZZiggs", "chromas": true}, {"id": "115033", "num": 33, "name": "La Ilusión Ziggs", "chromas": true}], "lore": "With a love of big bombs and short fuses, the yordle <PERSON><PERSON> is an explosive force of nature. As an inventor's assistant in Piltover, he was bored by his predictable life and befriended a mad, blue-haired bomber named <PERSON><PERSON>. After a wild night on the town, <PERSON><PERSON> took her advice and moved to Zaun, where he now explores his fascinations more freely, terrorizing the chem-barons and regular citizens alike in his never ending quest to blow stuff up.", "blurb": "With a love of big bombs and short fuses, the yordle <PERSON> is an explosive force of nature. As an inventor's assistant in Piltover, he was bored by his predictable life and befriended a mad, blue-haired bomber named <PERSON><PERSON>. After a wild night on the town...", "allytips": ["Even if you're far from a fight you can still help from afar with Mega Inferno Bomb.", "Slowing your enemies with Hexplosive Minefield makes it easier to land other abilities.", "Knocking yourself over walls with Satchel Charge can be great when chasing after or escaping from enemies."], "enemytips": ["Don't step on <PERSON><PERSON>' mines! They'll slow you and make it much easier for him to hit you with his other abiltiies.", "Many of <PERSON><PERSON>' abilities have long cooldowns. Try catching him just after he's used his abilities.", "<PERSON><PERSON>' ultimate, Mega Inferno Bomb, deals more damage in the center of the explosion."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 4}, "stats": {"hp": 606, "hpperlevel": 106, "mp": 480, "mpperlevel": 23.5, "movespeed": 325, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2, "attackspeed": 0.656}, "spells": [{"id": "ZiggsQ", "name": "Bouncing Bomb", "description": "<PERSON><PERSON> throws a bouncing bomb that deals magic damage.", "tooltip": "<PERSON><PERSON> throws a bouncing bomb that deals <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Total AP Ratio", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio }} -> {{ apratioNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [850, 850, 850, 850, 850], [325, 325, 325, 325, 325], [225, 225, 225, 225, 225], [180, 180, 180, 180, 180], [240, 240, 240, 240, 240], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "850", "325", "225", "180", "240", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "ZiggsQ.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsW", "name": "Satchel Charge", "description": "<PERSON><PERSON> flings an explosive charge that detonates after a delay, or when this ability is activated again. The explosion deals magic damage to enemies, knocking them away. <PERSON><PERSON> is also knocked away, but takes no damage. <br><br><PERSON><PERSON> can use the Satchel to hexplode vulnerable enemy turrets.", "tooltip": "<PERSON><PERSON> flings an explosive charge that detonates after {{ bombduration }} seconds, or when this ability is <recast>Recast</recast>. It deals <magicDamage>{{ totaldamage }} magic damage</magicDamage> to enemies and <status>Knocks</status> them <status>Back</status>. <PERSON><PERSON> is also knocked away, but takes no damage.<br /><br />Satchel Charge will automatically destroy towers below {{ turretdestroypercent*100 }}% of their Health.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "Tower Destruction Threshold"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ turretdestroypercent*100.000000 }}% -> {{ turretdestroypercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "ZiggsW.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsE", "name": "Hexplosive Minefield", "description": "<PERSON><PERSON> scatters proximity mines that detonate on enemy contact, dealing magic damage and slowing. Successive mine detonations on the same target deal reduced damage.", "tooltip": "<PERSON><PERSON> scatters proximity mines that detonate on enemy contact, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> and <status>Slows</status> by {{ slow*-100 }}% for {{ e4 }} seconds. Mines last {{ e3 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mine Damage", "Total AP Ratio", "Slow", "@AbilityResourceName@ Cost"], "effect": ["{{ damagepermine }} -> {{ damagepermineNL }}", "{{ apratiopermine }} -> {{ apratiopermineNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [10, 10, 10, 10, 10], [1.5, 1.5, 1.5, 1.5, 1.5], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [135, 135, 135, 135, 135], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "10", "1.5", "0.4", "0", "135", "150", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZiggsE.png", "sprite": "spell17.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsR", "name": "Mega Inferno Bomb", "description": "<PERSON><PERSON> deploys his ultimate creation, the Mega Inferno Bomb, hurling it an enormous distance. Enemies in the primary blast zone take more damage than those farther away.", "tooltip": "<PERSON><PERSON> hurls his ultimate creation, dealing <magicDamage>{{ empowereddamage }} magic damage</magicDamage> in the center of the blast radius, or <magicDamage>{{ blastdamage }} magic damage</magicDamage> at the edge.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Blast Zone Damage", "Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*0.667000 }} -> {{ basedamagenl*0.667000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 95, 70], "cooldownBurn": "120/95/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [300, 450, 600], [66.6667, 66.6667, 66.6667], [525, 525, 525], [250, 250, 250], [200, 300, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "300/450/600", "66.67", "525", "250", "200/300/400", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "ZiggsR.png", "sprite": "spell17.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Short Fuse", "description": "Periodically, <PERSON><PERSON>' next basic attack deals bonus magic damage. This cooldown is reduced whenever <PERSON><PERSON> uses an ability.", "image": {"full": "ZiggsPassiveReady.png", "sprite": "passive5.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}