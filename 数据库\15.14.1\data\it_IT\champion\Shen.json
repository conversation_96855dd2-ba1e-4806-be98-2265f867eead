{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shen": {"id": "<PERSON>", "key": "98", "name": "<PERSON>", "title": "l'occhio del crepuscolo", "image": {"full": "Shen.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "98000", "num": 0, "name": "default", "chromas": false}, {"id": "98001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "98002", "num": 2, "name": "Shen Yellowjacket", "chromas": false}, {"id": "98003", "num": 3, "name": "<PERSON>", "chromas": true}, {"id": "98004", "num": 4, "name": "<PERSON> di <PERSON>ue", "chromas": false}, {"id": "98005", "num": 5, "name": "Shen Signore della Guerra", "chromas": true}, {"id": "98006", "num": 6, "name": "TPA Shen", "chromas": false}, {"id": "98015", "num": 15, "name": "Shen Pulsefire", "chromas": true}, {"id": "98016", "num": 16, "name": "Shen Infernale", "chromas": true}, {"id": "98022", "num": 22, "name": "Shen OPSI", "chromas": true}, {"id": "98040", "num": 40, "name": "<PERSON>", "chromas": true}, {"id": "98049", "num": 49, "name": "Shen <PERSON>o cinereo", "chromas": true}, {"id": "98051", "num": 51, "name": "<PERSON>", "chromas": false}], "lore": "<PERSON>, l'occhio del crepuscolo, è il capo dei misteriosi guerrieri di Ionia, i Kinkou. Con la speranza di restare libero dalla confusione delle emozioni, del pregiudizio o dell'ego, Shen lotta sempre per proseguire il segreto cammino del giudizio razionale tra il reame spirituale e quello fisico. Per mantenere l'equilibrio tra i due, <PERSON> brandisce lame di acciaio ed energia arcana contro chiunque rappresenti una minaccia.", "blurb": "<PERSON>, l'occhio del crepuscolo, è il capo dei misteriosi guerrieri di Ionia, i Kinkou. Con la speranza di restare libero dalla confusione delle emozioni, del pregiudizio o dell'ego, Shen lotta sempre per proseguire il segreto cammino del giudizio...", "allytips": ["Tieni d'occhio gli alleati e preparati a salvarli col teletrasporto.", "Usa la tua energia per ottenere un vantaggio a lungo termine contro chi usa il mana."], "enemytips": ["Tieniti pronto a schivare la provocazione di Shen e a punirlo se manca il bersaglio.", "<PERSON>uando <PERSON> raggiunge il livello 6, fai attenzione alla sua suprema globale, che può cambiare rapidamente l'esito degli scontri."], "tags": ["Tank"], "partype": "Energia", "info": {"attack": 3, "defense": 9, "magic": 3, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 400, "mpperlevel": 0, "movespeed": 340, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.751}, "spells": [{"id": "ShenQ", "name": "<PERSON><PERSON><PERSON> crepus<PERSON>", "description": "Shen richiama la sua lama spirituale per usarla negli attacchi, infliggendo danni in base alla salute massima del bersaglio. Gli attacchi sono sensibilmente potenziati se si scontra con un campione nemico. Tutti i nemici con cui entra in contatto sono rallentati quando si allontanano da <PERSON>.", "tooltip": "<PERSON> richiama la sua <keywordMajor>Lama spirituale</keywordMajor>. I nemici colpiti vengono <status>rallentati</status> del {{ e4 }}% quando si allontanano da <PERSON> per i {{ e5 }} secondi successivi.<br /><br />I prossimi {{ e3 }} attacchi di Shen infliggono <magicDamage>{{ baseflatdamage }}</magicDamage> più <magicDamage>{{ basepercenthealth }} della salute massima del bersaglio in danni magici</magicDamage>. Se <PERSON> colpisce un campione nemico con la <keywordMajor>Lama spirituale</keywordMajor>, il danno aumenta a <magicDamage>{{ baseflatdamage }}</magicDamage> più <magicDamage>{{ emppercenthealth }} della salute massima del bersaglio in danni magici</magicDamage> e guadagna <attackSpeed>{{ e9 }}% velocità d'attacco</attackSpeed> per quegli attacchi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Percentuale base danni", "Percentuale potenziata danni", "Rallentamento", "Costo in @AbilityResourceName@", "Limite danni ai mostri", "Ricarica"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e6 }}% -> {{ e6NL }}%", "{{ e4 }}% -> {{ e4NL }}%", "{{ cost }} -> {{ costNL }}", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.25, 6.5, 5.75, 5], "cooldownBurn": "8/7.25/6.5/5.75/5", "cost": [140, 130, 120, 110, 100], "costBurn": "140/130/120/110/100", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [2, 2.5, 3, 3.5, 4], [3, 3, 3, 3, 3], [25, 30, 35, 40, 45], [2, 2, 2, 2, 2], [5, 5.5, 6, 6.5, 7], [120, 140, 160, 180, 200], [8, 8, 8, 8, 8], [50, 50, 50, 50, 50], [75, 75, 75, 75, 75]], "effectBurn": [null, "1", "2/2.5/3/3.5/4", "3", "25/30/35/40/45", "2", "5/5.5/6/6.5/7", "120/140/160/180/200", "8", "50", "75"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ShenQ.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ShenW", "name": "Rifugio spirituale", "description": "<PERSON>li attacchi che co<PERSON><PERSON><PERSON>ro <PERSON> o i suoi alleati vicino alla lama spirituale vengono bloccati.", "tooltip": "<PERSON> crea una zona difensiva attorno alla sua <keywordMajor><PERSON> spirituale</keywordMajor> per {{ e1 }} secondi. Gli attacchi contro i campioni alleati nella zona vengono bloccati. <br /><br />Se quando viene creata nella zona non ci sono campioni alleati da proteggere, questa aspetterà per {{ e2 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [1.75, 1.75, 1.75, 1.75, 1.75], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.75", "2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ShenW.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ShenE", "name": "Impeto dell'ombra", "description": "<PERSON> scatta in una direzione, provocando i nemici che incontra.", "tooltip": "<spellPassive>Passiva:</spellPassive> infliggere danni con <spellName>Assalto crepuscolare</spellName> o con questa abilità fa recuperare <keywordMajor>{{ energyrefund }} energia</keywordMajor>.<br /><br /><spellActive>Attiva:</spellActive> Shen scatta in avanti, <status>provocando</status> campioni e mostri della giungla per {{ ccduration }} secondi ed infliggendo <physicalDamage>{{ tauntdamage }} danni fisici</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [150, 150, 150, 150, 150], "costBurn": "150", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ShenE.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ShenR", "name": "Uniti nelle difficoltà", "description": "Shen protegge un campione alleato bersaglio dai danni subiti, e immediatamente dopo si teletrasporta verso la sua posizione.", "tooltip": "<PERSON> conferisce a un campione alleato ovunque nella mappa <shield>uno scudo tra {{ shield }}</shield> e <shield>{{ maxshield }}</shield> in base alla salute mancante per {{ shieldduration }} secondi, con uno scudo massimo del 60% della salute mancante. Dopo aver canalizzato per {{ channelduration }} secondi, Shen si teletrasporta nella posizione dell'alleato con la sua <keywordMajor>Lama spirituale</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Salute scudo minima", "Salute scudo massima", "Ricarica"], "effect": ["{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ baseshieldamount*1.600000 }} -> {{ baseshieldamountnl*1.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [35000, 35000, 35000], "rangeBurn": "35000", "image": {"full": "ShenR.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Barriera di Ki", "description": "<PERSON>po aver lanciato un'abilità, <PERSON> ottiene uno scudo. Usando le abilità su altri campioni si riduce la ricarica di questo effetto.", "image": {"full": "Shen_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}