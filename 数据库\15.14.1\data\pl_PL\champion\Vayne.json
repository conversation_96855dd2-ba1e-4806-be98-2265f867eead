{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vayne": {"id": "<PERSON><PERSON>", "key": "67", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON>na <PERSON>", "image": {"full": "Vayne.png", "sprite": "champion4.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "67000", "num": 0, "name": "default", "chromas": false}, {"id": "67001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "67002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "67003", "num": 3, "name": "Pogromczyni Smoków Vayne ", "chromas": true}, {"id": "67004", "num": 4, "name": "Łamaczka Serc Vayne", "chromas": false}, {"id": "67005", "num": 5, "name": "SKT T1 Vayne", "chromas": false}, {"id": "67006", "num": 6, "name": "Świ<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "67010", "num": 10, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "67011", "num": 11, "name": "PROJEKT: <PERSON><PERSON>", "chromas": false}, {"id": "67012", "num": 12, "name": "Rozrywkowa Vayne", "chromas": true}, {"id": "67013", "num": 13, "name": "Rozrywkowa Vayne (Prestiżowa)", "chromas": false}, {"id": "67014", "num": 14, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "67015", "num": 15, "name": "FPX Vayne", "chromas": true}, {"id": "67025", "num": 25, "name": "Strażniczka Vayne", "chromas": true}, {"id": "67032", "num": 32, "name": "Nietoperz Bojowy <PERSON>", "chromas": true}, {"id": "67033", "num": 33, "name": "Rozrywkowa Vayne (Prestiżowa 2022)", "chromas": false}, {"id": "67044", "num": 44, "name": "<PERSON><PERSON> Brzasku", "chromas": true}, {"id": "67055", "num": 55, "name": "Wysłanniczka Smoków Vayne", "chromas": true}, {"id": "67064", "num": 64, "name": "Przywrócona Legenda Vayne", "chromas": true}], "lore": "<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>cii, to be<PERSON><PERSON><PERSON><PERSON> łowczyni potworów, kt<PERSON>ra poprzysięgła, <PERSON><PERSON> zniszczy demona, kt<PERSON><PERSON> wymordował jej rodzin<PERSON>. Uzbrojona w przymocowaną do nadgarstka kuszę, z sercem wypełnionym żądzą zemsty, odnajduje szczęście jedynie w zabijaniu sług i stworów ciemności za pomocą wystrzeliwanych z cienia srebrnych bełtów.", "blurb": "<PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>cii, to be<PERSON><PERSON><PERSON><PERSON> łowczyni potworów, kt<PERSON>ra poprzysięgła, <PERSON><PERSON> zniszczy demona, kt<PERSON><PERSON> wymordował jej rodzin<PERSON>. Uzbrojona w przymocowaną do nadgarstka kuszę, z sercem wypełnionym żądzą zemsty, odnajduje szczęście jedynie w...", "allytips": ["Akrobacja ma wiele zastosowań, ale nie pozwala przes<PERSON><PERSON><PERSON>.", "Potępienia można użyć, aby przybić cel do przeszkody, co ułatwi zabicie wroga albo ucieczkę przed nim.", "<PERSON><PERSON> walk drużynowych. <PERSON><PERSON><PERSON><PERSON>, a<PERSON> zain<PERSON> je towarzysze."], "enemytips": ["<PERSON>ayne jest delikatna - przyciś<PERSON><PERSON> j<PERSON>, a będzie musiała być ostrożna.", "<PERSON><PERSON> pozwól jej przyprzeć cię do muru."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 1, "magic": 1, "difficulty": 8}, "stats": {"hp": 550, "hpperlevel": 103, "mp": 232, "mpperlevel": 35, "movespeed": 330, "armor": 23, "armorperlevel": 4.6, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.35, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "VayneTumble", "name": "Akrobacja", "description": "<PERSON><PERSON> wykonuje a<PERSON>, by us<PERSON><PERSON><PERSON> się do następnego strzału. <PERSON>j następny atak zada dodatkowe obrażenia.", "tooltip": "Vayne wykonuje kr<PERSON>t<PERSON> akrobację, a jej następny atak zadaje dodatkowo <physicalDamage>{{ adratiobonus }} pkt. obrażeń fizycznych</physicalDamage>.<br /><br /><rules>Ta umiejętność aktywuje efekty zaklęć podczas zadawania obrażeń.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Skalowanie z obrażeniami od ataku"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ totaladratio*100.000000 }}% -> {{ totaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "VayneTumble.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "<PERSON>ayneSil<PERSON><PERSON><PERSON><PERSON>", "name": "Srebrne Beł<PERSON>", "description": "Vayne stosuje bełty zakończone szlachetnym metalem, toksycznym dla złych istot. Trzeci atak z kolei lub użycie umiejętności przeciw temu samemu celowi zadaje mu dodatkowe, nieuchronne obrażenia równe części jego maksymalnego zdrowia.", "tooltip": "<spellPassive>Biernie:</spellPassive> Ka<PERSON><PERSON> co trzeci atak lub użycie umiejętności z rzędu przeciwko temu samemu wrogowi zadaje mu dodatkowo <trueDamage>obrażenia nieuchronne równe {{ totaldamage }} jego maks. zdrowia</trueDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["% nieuchronnych obrażeń", "Minimalne obrażenia"], "effect": ["{{ maxhealthratio*100.000000 }}% -> {{ maxhealthrationl*100.000000 }}%", "{{ damagefloor }} -> {{ damagefloorNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "VayneSilveredBolts.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "VayneCondemn", "name": "Potępienie", "description": "Vayne zdejmuje z pleców ciężką kuszę i wystrzeliwuje w cel ogromny bełt, z<PERSON><PERSON><PERSON><PERSON> obrażenia, które odrzucają go do tyłu. <PERSON><PERSON><PERSON> wróg trafi w teren, zost<PERSON>e przebity, co zada dodatkowe obrażenia i ogłuszy go.", "tooltip": "Vayne wystr<PERSON><PERSON><PERSON><PERSON> pocisk, k<PERSON><PERSON><PERSON> <status>o<PERSON><PERSON><PERSON></status> cel i zadaje <physicalDamage>{{ totaldamage }} pkt. obrażeń fizycznych</physicalDamage>. Jeś<PERSON> cel trafi w teren, otr<PERSON>ma <physicalDamage>{{ empowereddamagett }} pkt. dodatkowych obrażeń fizycznych</physicalDamage> i zostanie <status>ogłuszony</status> na {{ stunduration }} sek. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Obrażenia"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "VayneCondemn.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "VayneInquisition", "name": "<PERSON><PERSON><PERSON>", "description": "Przygotowując się do wiekopomnego starcia, Vayne otrzymuje premię do obrażeń od ataku, staje się niewidzialna podczas Akrobacji, ma kr<PERSON><PERSON><PERSON> czas odnowienia Akrobacji oraz zyskuje większą premię do prędkości ruchu od Nocnej Łowczyni.", "tooltip": "Vayne zyskuje <physicalDamage>{{ bonusattackdamage }} pkt. obrażeń od ataku</physicalDamage> na {{ baseduration }} sek. Ten czas zostaje wydłużony o {{ durationtoadd }} sek., il<PERSON><PERSON><PERSON> bohater, któ<PERSON><PERSON> Vayne zadała obrażenia, ginie w przeciągu {{ damagedmarkerduration }} sek. od ich otrzymania. Dodatkowo podczas trwania umiejętności:<li><spellName>Noc<PERSON></spellName> zapewnia <speed>{{ movementspeed }} jedn. prędkości ruchu</speed>.<li>Czas odnowienia <spellName>Akrobacji</spellName> jest zmniejszony o {{ tumblecdreduction }}% i zapewnia <keywordStealth>niewid<PERSON>ln<PERSON><PERSON><PERSON></keywordStealth> na {{ tumblestealthduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Czas działania", "Dodatkowe obrażenia od ataku", "Skrócenie czasu odnowienia Akrobacji"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseduration }} -> {{ basedurationNL }}", "{{ bonusattackdamage }} -> {{ bonusattackdamageNL }}", "{{ tumblecdreduction }}% -> {{ tumblecdreductionNL }}%"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "VayneInquisition.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON><PERSON>na <PERSON>", "description": "Vayne bezwzględnie poluje na złoczyńców, zyskując prędkość ruchu podczas poruszania się w kierunku wrogich bohaterów.", "image": {"full": "Vayne_NightHunter.png", "sprite": "passive4.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}