{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Twitch": {"id": "Twitch", "key": "29", "name": "Twitch", "title": "il ratto della peste", "image": {"full": "Twitch.png", "sprite": "champion4.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "29000", "num": 0, "name": "default", "chromas": false}, {"id": "29001", "num": 1, "name": "Twitch Re del Crimine", "chromas": false}, {"id": "29002", "num": 2, "name": "Twitch Giochi Invernali", "chromas": false}, {"id": "29003", "num": 3, "name": "Twitch Medioevale", "chromas": true}, {"id": "29004", "num": 4, "name": "Twitch Città del crimine", "chromas": false}, {"id": "29005", "num": 5, "name": "T<PERSON>", "chromas": false}, {"id": "29006", "num": 6, "name": "Twitch Borseggiatore", "chromas": false}, {"id": "29007", "num": 7, "name": "Twitch SSW", "chromas": false}, {"id": "29008", "num": 8, "name": "Twitch Squadra Omega", "chromas": true}, {"id": "29012", "num": 12, "name": "Twitch Re dei Ghiacci", "chromas": true}, {"id": "29027", "num": 27, "name": "Twitch Passoscuro", "chromas": false}, {"id": "29036", "num": 36, "name": "Twitch <PERSON>", "chromas": false}, {"id": "29045", "num": 45, "name": "Twitch Mezzogiorno di Fuoco", "chromas": false}, {"id": "29055", "num": 55, "name": "Twitch Cheddar supremo", "chromas": false}, {"id": "29064", "num": 64, "name": "Twitch Festa in Piscina", "chromas": false}], "lore": "Ratto della peste di Zaun, cultore della sporcizia per passione, Twitch non ha paura di sporcarsi le zampe. Puntando la sua balestra chimica al cuore di Piltover, ha giurato di mostrare alla città sovrastante che i suoi abitanti non sono meno sporchi di lui. Subdolo e furtivo, quando non si aggira nel Sump rovista nella spazzatura alla ricerca di tesori nascosti... e magari di un panino ammuffito.", "blurb": "<PERSON>to della peste di Zaun, cultore della sporcizia per passione, Twitch non ha paura di sporcarsi le zampe. Puntando la sua balestra chimica al cuore di Piltover, ha giurato di mostrare alla città sovrastante che i suoi abitanti non sono meno sporchi di...", "allytips": ["La velocità d'attacco di Twitch è una delle più alte nel gioco; compra oggetti con effetti sul colpo come Mannaia oscura o Speranza perduta.", "Contaminazione ha una grande gittata. Applica più cariche che puoi di Veleno mortale prima di usarlo.", "Puoi anche colpire un nemico fuori dalla tua portata di attacco con Barile di veleno."], "enemytips": ["Twitch è fragile. Lavora con la squadra e concentrati su di lui quando non è più mimetizzato.", "Gli scudi magici non proteggono dal Veleno mortale, ma bloccano gli effetti che Twitch può attivare.", "Se sospetti che Twitch abbia abbandonato la corsia, fallo sapere ai tuoi compagni dicendogli che Twitch è ''disperso''."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 104, "mp": 300, "mpperlevel": 40, "movespeed": 330, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.6, "mpregen": 7.25, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.1, "attackspeedperlevel": 3.38, "attackspeed": 0.679}, "spells": [{"id": "TwitchHideInShadows", "name": "Imboscata", "description": "Twitch entra in Mimesi per un breve periodo di tempo e ottiene velocità di movimento. Quando non è più in Mimesi, Twitch guadagna velocità d'attacco per un breve periodo.<br><br>Quando muore un campione nemico con Veleno mortale, la ricarica di Imboscata si azzera.", "tooltip": "Twitch entra in <keywordStealth><PERSON><PERSON><PERSON></keywordStealth> e ottiene il <speed>{{ e3 }}% di velocità di movimento</speed> per {{ e2 }} secondi. La velocità di movimento aumenta al {{ e3 }}% quando è vicino a un campione nemico che non può vederlo. Quando esce della <keywordStealth><PERSON><PERSON><PERSON></keywordStealth>, Twitch ottiene il <attackSpeed>{{ e1 }}% di velocità d'attacco</attackSpeed> per {{ e6 }} secondi.<br /><br />Se un campione nemico muore mentre è sotto l'effetto di <keywordMajor>Veleno</keywordMajor>, la ricarica di questa abilità si azzera.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Velocità d'attacco"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}% -> {{ e1NL }}%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [45, 50, 55, 60, 65], [10, 11, 12, 13, 14], [30, 30, 30, 30, 30], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [6, 6, 6, 6, 6], [3, 3, 3, 3, 3], [500, 500, 500, 500, 500], [1000, 1000, 1000, 1000, 1000], [30, 30, 30, 30, 30]], "effectBurn": [null, "45/50/55/60/65", "10/11/12/13/14", "30", "1", "1", "6", "3", "500", "1000", "30"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TwitchHideInShadows.png", "sprite": "spell14.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwitchVenomCask", "name": "<PERSON>le di veleno", "description": "Twitch scaglia un barilotto di veleno che esplode in un'area e rallenta i bersagli, cospargendoli di sostanze ad azione venefica.", "tooltip": "Twitch scaglia un barile che aggiunge una carica di <spellName>Veleno mortale</spellName> a tutti i nemici colpiti e si lascia dietro una nube tossica che dura {{ duration }} secondi.<br /><br />I nemici che restano all'interno della nube hanno il {{ totalslowamount }}% di <status>rallentamento</status> e ricevono una carica aggiuntiva di <spellName>Veleno mortale</spellName> al secondo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rallentamento", "Ricarica"], "effect": ["{{ baseslowamount }}% -> {{ baseslowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "TwitchVenomCask.png", "sprite": "spell14.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwitchExpunge", "name": "Contaminazione", "description": "Twitch tormenta ulteriormente i nemici avvelenati con una scarica di terribili malattie.", "tooltip": "Infligge <physicalDamage>{{ basedamage }} danni fisici</physicalDamage> a tutti i nemici vicini affetti da <spellName>Veleno mortale</spellName>, più <physicalDamage>{{ physicaldamageperstack }} danni fisici</physicalDamage> aggiuntivi e <magicDamage>{{ magicdamageperstack }} danni magici</magicDamage> per ogni carica di <spellName>Veleno mortale</spellName>.<br /><br />Danni massimi: <physicalDamage>{{ maxphysicaldamage }} danni fisici</physicalDamage> e <magicDamage>{{ maxmagicdamage }} danni magici</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> accumulati", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basephysicaldamageperstack }} -> {{ basephysicaldamageperstackNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchExpunge.png", "sprite": "spell14.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwitchFullAutomatic", "name": "Spara e prega", "description": "Twitch scatena la potenza della sua balestra, sparando dardi a lunga gittata che perforano i nemici lungo il loro percorso.", "tooltip": "Twitch scatena la sua balestra e ottiene {{ bonusrange }} gittata d'attacco e <scaleAD>{{ bonusad }} attacco fisico</scaleAD>. In<PERSON><PERSON>, i suoi attacchi diventano dei dardi perforanti per {{ duration }} secondi. Questi dardi colpiscono tutti i nemici che attraversano, ma infliggono {{ falloffdamage*100 }}% danni in meno ad ogni nuovo bersaglio, fino a un minimo di {{ minimumfalloffdamage*100 }}% danni.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Attacco fisico"], "effect": ["{{ bonusad }} -> {{ bonusadNL }}"]}, "maxrank": 3, "cooldown": [90, 90, 90], "cooldownBurn": "90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "TwitchFullAutomatic.png", "sprite": "spell14.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Veleno mortale", "description": "Gli attacchi base di Twitch infettano il bersaglio, infliggendo danni puri ogni secondo.", "image": {"full": "Twitch_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}