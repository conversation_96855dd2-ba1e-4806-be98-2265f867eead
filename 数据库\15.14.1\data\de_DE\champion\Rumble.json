{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rumble": {"id": "Rumble", "key": "68", "name": "Rumble", "title": "die mechanisierte Bedrohung", "image": {"full": "Rumble.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "68000", "num": 0, "name": "default", "chromas": false}, {"id": "68001", "num": 1, "name": "Dschungel-Rumble", "chromas": false}, {"id": "68002", "num": 2, "name": "Bilgeratten-Rumble", "chromas": false}, {"id": "68003", "num": 3, "name": "Supergalaktischer Rumble", "chromas": false}, {"id": "68004", "num": 4, "name": "Ödlandbaron Rumble", "chromas": true}, {"id": "68013", "num": 13, "name": "Weltraum-Groove-Rumble", "chromas": true}, {"id": "68023", "num": 23, "name": "Konditorei-Rumble", "chromas": true}], "lore": "Rumble ist ein junger Erfinder mit hitzigem Gemüt. Mit nichts weiter als seinen beiden Händen und einem Schrotthaufen konstruierte dieser quirlige Yordle einen gewaltigen Mech-Anzug, der mit einem Arsenal an elektrifizierten Harpunen und Brandraketen ausgerüstet ist. Auch wenn andere seine Schrottplatzkreationen belächeln, kümmert das Rumble wenig – denn schließlich ist er derjenige mit dem Flammenspeier.", "blurb": "Rumble ist ein junger Erfinder mit hitzigem Gemüt. Mit nichts weiter als seinen beiden Händen und einem Schrotthaufen konstruierte dieser quirlige Yordle einen gewaltigen Mech-Anzug, der mit einem Arsenal an elektrifizierten Harpunen und Brandraketen...", "allytips": ["<PERSON><PERSON><PERSON>, im Gefahrenbereich zu bleiben, um deine Effektivität zu erhöhen. Man überhitzt zu leicht, wenn man die Fähigkeiten häufig einsetzt.", "Versuche deine Ziele in Reichweite des Flammenwerfers zu halten. Er kann auf Dauer viel Schaden verursachen.", "<PERSON><PERSON><PERSON><PERSON> sich ein <PERSON> ab, kannst du die ultimative Fähigkeit nutzen, um einen Fluchtweg zu blockieren."], "enemytips": ["Achte genau auf Rumbles Hitzeleiste. Überhitzt er, schnap<PERSON> ihn dir, während er seine Fähigkeiten nicht einsetzen kann.", "Rumbles ultimative Fähigkeit kann sehr viel Schaden verursachen, wenn du im Wirkbereich bleibst. Siehst du die Raketen, solltest du dich schnellstmöglich wegbewegen.", "Rumble verursacht fast nur magischen Schaden. Gegenstände mit Magieresistenz können diesen erheblich senken."], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 6, "magic": 8, "difficulty": 10}, "stats": {"hp": 655, "hpperlevel": 105, "mp": 150, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 1.55, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.85, "attackspeed": 0.644}, "spells": [{"id": "RumbleFlameThrower", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Rumble grillt Gegner vor sich und verursacht 3&nbsp;Sekunden lang magischen Schaden in einem kegelförmigen Bereich. Befindet er sich im Gefahrenbereich, wird der Schaden erhöht.", "tooltip": "Rumble entzündet seinen Flammenwerfer und verursacht über {{ flamespitterduration }}&nbsp;Sekunden hinweg <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ flatdamage }} plus {{ healthdamage*100 }}&nbsp;% des maximalen Lebens. Vasallen erleiden nur <attention>{{ minionmod*100 }}&nbsp;Schaden</attention>.<br /><br /><keywordMajor>Gefahrenbereich</keywordMajor>: Der Schaden wird auf <magicDamage>{{ empowereddamage }} plus {{ empoweredhealth }} des maximalen Lebens</magicDamage> erhöht.<br /><br /><rules>Der prozentuale Schaden ist gegen Monster begrenzt auf {{ monstercap }}&nbsp;Schaden.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Maximales Leben (%)", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ healthdamage*100.000000 }}&nbsp;% -> {{ healthdamagenl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;<PERSON><PERSON>", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RumbleFlameThrower.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ initialheatcost }}&nbsp;Hitze"}, {"id": "RumbleShield", "name": "Schrottschild", "description": "Rumble erschafft einen Schild, der ihn vor Schaden schützt und ihm kurzzeitig einen Geschwindigkeitsschub gewährt. Befindet er sich im Gefahrenbereich, werden die Schildstärke und das zusätzliche Tempo erhöht.", "tooltip": "Rumble schützt sich {{ shieldduration.1 }}&nbsp;<PERSON>kunden lang mit einer Barriere und erhält dadurch {{ movespeedduration }}&nbsp;Sekunden lang einen <shield>Schild</shield> in <PERSON><PERSON><PERSON> von {{ totalshield }} und <speed>{{ movespeed*100 }}&nbsp;% Lauftempo</speed>.<br /><br /><keywordMajor>Gefahrenbereich</keywordMajor>: Die Barriere gewährt stattdessen einen <shield>Schild</shield> in H<PERSON><PERSON> von {{ empoweredshield }} und <speed>{{ empoweredms }}&nbsp;Lauftempo</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Lauftempo"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ movespeed*100.000000 }}&nbsp;% -> {{ movespeednl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [10, 15, 20, 25, 30], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "10/15/20/25/30", "20", "0", "1.5", "1", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;<PERSON><PERSON>", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "RumbleShield.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ heatcost }}&nbsp;Hitze"}, {"id": "RumbleGrenade", "name": "Elektroharpune", "description": "Rumble feuert eine <PERSON>, die seinem Ziel mit einem Stromschlag magischen Schaden zufügt, es verlangsamt und seine Magieresistenz verringert. Rumble kann 2 Harpunen gleichzeitig tragen. Im Gefahrenbereich wird zusätzlicher Schaden verursacht und das Ziel stärker verlangsamt.", "tooltip": "Rumble feuert eine elektrifizierte Harpune ab, die <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON></magicDamage> verursacht, {{ slowduration }}&nbsp;Sekunden lang um {{ baseslowamount }}&nbsp;% <status>verlangsamt</status> und die <scaleMR>Magieresistenz</scaleMR> des Gegners {{ shredduration }}&nbsp;Sekunden lang um {{ percmagicpen*100 }}&nbsp;% verringert.<br /><br />Wenn er einen Gegner trifft, der bereits durch diese Fähigkeit <status>verlangsamt</status> ist, erhöht sich die <status>Verlangsamung</status> auf {{ empoweredslowamount }}&nbsp;% und die <scaleMR>Magieresistenz</scaleMR> des Gegners wird um {{ enhancedmagicpen*100 }}&nbsp;% verringert.<br /><br /><keywordMajor>Gefahrenbereich:</keywordMajor> Die Harpune verursacht stattdessen <magicDamage>{{ empdamage }}&nbsp;magischen Schaden</magicDamage> und die <status>Verlangsamung</status> sowie die Verringerung der <scaleMR>Magieresistenz</scaleMR> werden um 50&nbsp;% erhöht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Verringerung der Magieresistenz"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslowamount }}&nbsp;% -> {{ baseslowamountNL }}&nbsp;%", "{{ percmagicpen*100.000000 }}&nbsp;% -> {{ percmagicpennl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;<PERSON><PERSON>", "maxammo": "2", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RumbleGrenade.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ firstcastheatcost }}&nbsp;Hitze"}, {"id": "RumbleCarpetBomb", "name": "Der Einebner", "description": "Rumble feuert eine Salve Raketen ab, wodurch er eine Flammenwand er<PERSON>t, die Gegner verlangsamt und schädigt.", "tooltip": "Rumble feuert eine Reihe Raketen ab, die eine brennende Spur hinterlassen, welche {{ trailduration }}&nbsp;Sekunden lang um {{ slowamount }}&nbsp;% <status>verlangsamt</status> und <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.<br /><br />Die Richtung der Spur kann durch Gedrückthalten und Ziehen der Maus bei Verwendung der Fähigkeit kontrolliert werden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde", "Abklingzeit"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [1750, 1750, 1750], "rangeBurn": "1750", "image": {"full": "RumbleCarpetBomb.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Mechanisierter Titan", "description": "Alle von Rumble eingesetzten Fähigkeiten generieren Hitze. <PERSON><PERSON><PERSON> Rumbles Hitze auf 50 % gestiegen ist, erreicht er den Gefahrenbereich und alle seine grundlegenden Fähigkeiten erhalten zusätzliche Effekte. <PERSON><PERSON><PERSON>umbles Hitze auf 100 % an, überhitzt er und erhält zusätzliches Angriffstempo. Außerdem verursacht er zusätzlichen Schaden mit seinen normalen Angriffen, kann aber ein paar Sekunden lang keine Fähigkeiten einsetzen.", "image": {"full": "Rumble_JunkyardTitan1.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}