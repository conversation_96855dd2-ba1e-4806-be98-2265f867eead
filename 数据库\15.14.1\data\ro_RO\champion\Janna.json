{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Janna": {"id": "<PERSON><PERSON>", "key": "40", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>ii", "image": {"full": "Janna.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "40000", "num": 0, "name": "default", "chromas": false}, {"id": "40001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40003", "num": 3, "name": "<PERSON><PERSON>, regina <PERSON>", "chromas": false}, {"id": "40004", "num": 4, "name": "<PERSON><PERSON>ict<PERSON>", "chromas": false}, {"id": "40005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "40006", "num": 6, "name": "<PERSON><PERSON> ", "chromas": false}, {"id": "40007", "num": 7, "name": "<PERSON><PERSON>, Magia Stelelor", "chromas": false}, {"id": "40008", "num": 8, "name": "Jan<PERSON>, sabia sacră", "chromas": true}, {"id": "40013", "num": 13, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "40020", "num": 20, "name": "<PERSON><PERSON>, str<PERSON><PERSON><PERSON> a nisipurilor", "chromas": true}, {"id": "40027", "num": 27, "name": "<PERSON><PERSON>, regina <PERSON><PERSON>", "chromas": true}, {"id": "40036", "num": 36, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "40045", "num": 45, "name": "<PERSON><PERSON> cibernet<PERSON>", "chromas": true}, {"id": "40046", "num": 46, "name": "<PERSON><PERSON> (Prestigiu)", "chromas": false}, {"id": "40056", "num": 56, "name": "<PERSON><PERSON>i", "chromas": true}, {"id": "40066", "num": 66, "name": "<PERSON><PERSON>, întruparea Luminii", "chromas": false}], "lore": "<PERSON><PERSON> e un spirit misterios al elementelor care le poate porunci furtunilor și vijeliilor din Runeterra și care apără oamenii sărmani din Zaun. Unii cred că a luat ființă în urma rugăciunilor marinarilor, ce șopteau cuvinte menite să atragă puterea vânturilor bune atunci când navigau ape înșelătoare sau pătrundeau în mijlocul unor furtuni teribile. Bunăvoința și protecția Jannei au pătruns apoi până în adâncul orașului Zaun, unde a devenit un simbol al speranței pentru oamenii nevoiași. Nimeni nu știe unde sau când se va arăta <PERSON>, dar se spune că de cele mai multe ori apare atunci când este nevoie de ajutorul ei.", "blurb": "<PERSON><PERSON> e un spirit misterios al elementelor care le poate porunci furtunilor și vijeliilor din Runeterra și care apără oamenii sărmani din Zaun. Unii cred că a luat ființă în urma rugăciunilor marinarilor, ce șopteau cuvinte menite să atragă puterea...", "allytips": ["''Ochiul furtunii'' poate fi folosit asupra turnurilor aliate.", "Dacă lansezi rapid un vârtej fără a aștepta încărcarea, vei neutraliza echipa adversă.", "Calculează bine momentul când lansezi abilitatea supremă a Jannei pentru a îndepărta inamicii de un aliat rănit sau chiar pentru a-i separa."], "enemytips": ["Păstrează o abilitate de întrerupere pentru momentul în care Janna <PERSON> folosește abilitatea supremă.", "Ascultă sunetul pregătitor al ''Vârtejului'' dac<PERSON> Janna încearcă să te lovească din afara ecranului sau din tufișuri.", "<PERSON><PERSON> e un adversar cu adevărat redutabil când aplică buff-uri asupra aliaților. Dacă poți să îi hărțuiești aliatul, puterea ei de a îți contracara atacurile va slăbi."], "tags": ["Support", "Mage"], "partype": "Mană", "info": {"attack": 3, "defense": 5, "magic": 7, "difficulty": 7}, "stats": {"hp": 570, "hpperlevel": 90, "mp": 360, "mpperlevel": 50, "movespeed": 325, "armor": 28, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 47, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3, "attackspeed": 0.625}, "spells": [{"id": "<PERSON><PERSON>G<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Creând o modificare localizată a presiunii și temperaturii, <PERSON><PERSON>oat<PERSON> crea o mică furtună care va crește cu timpul. Ea poate activa vraja din nou pentru a dezlănțui furtuna. După ce este dezlănțuită, furtuna va zbura în direcția în care este lansată, provocând daune și aruncând inamicii în sus.", "tooltip": "<PERSON><PERSON> invoc<PERSON> o tornadă care devine tot mai puternică de-a lungul a {{ maxduration }} secunde, apoi se dezlănțuie pe traiectoria sa. Aceasta provoacă <magicDamage>{{ minimumdamage }} - {{ maxdamage }} daune magice</magicDamage> și <status>aruncă în sus</status> inamicii timp de {{ baseknockup }} - {{ maxknockup }} secunde. Distanța, daunele și durata <status>aruncării în sus</status> cresc în funcție de dimensiunea tornadei. <PERSON><PERSON> poate <recast>refolosi</recast> abilitatea pentru a dezlănțui tornada mai devreme.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Daune per secundă de pregătire", "Cost de @AbilityResourceName@"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [90, 95, 100, 105, 110], "costBurn": "90/95/100/105/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1075, 1075, 1075, 1075, 1075], "rangeBurn": "1075", "image": {"full": "HowlingGale.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SowTheWind", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> invocă un spirit elemental al aerului, care îi crește în mod pasiv viteza de mișcare și îi permite să treacă prin unități. Această abilitate poate fi activată împotriva unui inamic pentru a-i provoca daune și a-l încetini.", "tooltip": "<spellPassive><PERSON><PERSON><PERSON><PERSON>:</spellPassive> <PERSON><PERSON> <speed>{{ totalms }} vite<PERSON><PERSON> de <PERSON></speed> și efectul ''Fantomă''.<br /><br /><spellActive>Activă:</spellActive> <PERSON><PERSON> lovește un inamic cu magia elementelor, <status>încetinindu-l</status> cu {{ totalslow }} timp de {{ slowduration }} secunde și provocându-i <magicDamage>{{ totaldamage }} + {{ spell.tailwindself:bonusdamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Încetinire", "Viteză de mișcare pasivă", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ mspercent*100.000000 }}% -> {{ mspercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4294967295, 4294967295, 4294967295, 4294967295, 4294967295], "rangeBurn": "4294967295", "image": {"full": "SowTheWind.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EyeOfTheStorm", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> invoc<PERSON> un ciclon defensiv care apără campionul sau turnul aliat de daune și îi crește daunele din atac.", "tooltip": "<PERSON><PERSON> î<PERSON> oferă unui turn sau campion aliat un <shield>scut în valoare de {{ totalshield }}</shield> timp de {{ shieldduration }} secunde. Cât timp scutul e activ, ținta primește <scaleAD>{{ totalad }} daune din atac</scaleAD>.<br /><br />Janna primește înapoi {{ ecdrefundforcc*100 }}% din timpul de reactivare rămas de fiecare dată când reduce mobilitatea unui campion inamic cu o abilitate.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Integritate scut", "Daune din atac", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ bonusad }} -> {{ bonusadNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EyeOfTheStorm.png", "sprite": "spell5.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ReapTheWhirlwind", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> se înv<PERSON><PERSON>ie într-o furtună magică, aruncându-și inamicii în spate. După ce furtuna s-a potolit, ad<PERSON><PERSON> alinătoare îi vindecă pe aliați cât timp abilitatea este activă.", "tooltip": "<PERSON><PERSON> invocă un muson magic, <status>arun<PERSON><PERSON>d în spate</status> inamicii din apropiere, apoi refăcându-le aliaților din apropiere <healing>{{ totalheal }} via<PERSON><PERSON></healing> de-a lungul a {{ e3 }} secunde. Dacă se mișcă sau folosește o abilitate, musonul se încheie mai devreme.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vindecare pe secundă", "Timp de reactivare"], "effect": ["{{ healbasepersecond }} -> {{ healbasepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 115, 100], "cooldownBurn": "130/115/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [300, 450, 600], [3, 3, 3], [700, 700, 700], [875, 875, 875], [875, 875, 875], [1200, 1200, 1200], [10, 10, 10], [0.5, 0.5, 0.5], [0, 0, 0]], "effectBurn": [null, "0", "300/450/600", "3", "700", "875", "875", "1200", "10", "0.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [725, 725, 725], "rangeBurn": "725", "image": {"full": "ReapTheWhirlwind.png", "sprite": "spell5.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Austru", "description": "Aliații Jannei primesc viteză de mișcare când se îndreaptă spre ea.<br><br><PERSON><PERSON> provo<PERSON> daune magice bonus la impact și cu ''Zefir'' egale cu o parte din bonusul la viteza de mișcare.", "image": {"full": "Janna_Tailwind.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}