{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Amumu": {"id": "<PERSON><PERSON><PERSON>", "key": "32", "name": "<PERSON><PERSON><PERSON>", "title": "die traurige Mumie", "image": {"full": "Amumu.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "32000", "num": 0, "name": "default", "chromas": false}, {"id": "32001", "num": 1, "name": "Ph<PERSON><PERSON>", "chromas": false}, {"id": "32002", "num": 2, "name": "Vancouver-Amum<PERSON>", "chromas": false}, {"id": "32003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32004", "num": 4, "name": "Geschenk-Amumu", "chromas": false}, {"id": "32005", "num": 5, "name": "Beinahe-Ballkönig <PERSON>u", "chromas": false}, {"id": "32006", "num": 6, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "32007", "num": 7, "name": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "32008", "num": 8, "name": "Überraschungsparty-Amumu", "chromas": true}, {"id": "32017", "num": 17, "name": "Infernalischer Amumu", "chromas": true}, {"id": "32023", "num": 23, "name": "Hextech-Amumu", "chromas": false}, {"id": "32024", "num": 24, "name": "Kürbisprinz <PERSON>umu", "chromas": true}, {"id": "32034", "num": 34, "name": "Porzellan-<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "32044", "num": 44, "name": "Herzschmerz-Amumu", "chromas": true}, {"id": "32053", "num": 53, "name": "Teigtaschenschatz-Amumu", "chromas": true}], "lore": "Den Legenden zufolge ist Amumu eine melancholische Seele aus dem uralten Shurima und streift auf der Suche nach einem Freund durch die Welt. Ein alter Fluch hat ihn zu ewiger Einsamkeit verdammt, da seine Berührung den Tod und seine Zuneigung Verfall bringen. <PERSON><PERSON><PERSON><PERSON>, die beha<PERSON>ten, ihn gesehen zu haben, beschreiben ihn als lebenden Kadaver. Er ist klein und in Bandagen gewickelt. Amumu hat Mythen, Lieder und Volksmärchen inspiriert, die von Generation zu Generation weitergegeben wurden – sodass es mittlerweile unmöglich ist, die Wahrheit von der Fiktion zu trennen.", "blurb": "Den Legenden zufolge ist Amumu eine melancholische Seele aus dem uralten Shurima und streift auf der Suche nach einem Freund durch die Welt. Ein alter Fluch hat ihn zu ewiger Einsamkeit verdammt, da seine Berührung den Tod und seine Zuneigung Verfall...", "allytips": ["Amumu ist sehr von seinen Verbündeten abhängig. Versuch mit ihnen zusammen zu arbeiten, um eine maximale Effektivität zu erreichen.", "Amumu profitiert sehr stark von Gegenständen, die die Abklingzeiten herabsetzen, auch wenn es häufig schwer ist, an diese zu gelangen. Hol dir den Buff des Blauen Wächters so oft wie möglich, um keine Attribute für kürzere Abklingzeiten zu opfern.", "„Verzweiflung“ ist besonders gegen robuste Nahkämpfer interessant. Versuche daher in Reichweite der Gegner mit dem meisten Leben zu bleiben."], "enemytips": ["Vermeide es, dich mit anderen Verbündeten Amumu zu nähern, wenn er seine ultimative Fähigkeit bereit hat.", "Unberechenbare Bewegungen oder das Verbergen hinter Vasallen-Wellen erschwert es Amumu, einen Kampf mit dem „Bandagenwurf“ zu eröffnen.", "Amumus „Verzweiflung“ macht für Nahkämpfer das Anhäufen von Gegenständen mit Leben zu einer gefährlichen Angelegenheit."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 3}, "stats": {"hp": 685, "hpperlevel": 94, "mp": 285, "mpperlevel": 40, "movespeed": 335, "armor": 33, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.85, "mpregen": 7.4, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.18, "attackspeed": 0.736}, "spells": [{"id": "BandageToss", "name": "Bandagenwurf", "description": "Amumu schleudert eine klebrige Bandage auf ein Ziel, zieht sich heran, betäubt es und fügt ihm Sc<PERSON>en zu.", "tooltip": "Amumu wirft eine Bandage, zieht sich zu dem ersten getroffenen Gegner, <status>betäubt</status> ihn {{ e2 }}&nbsp;Sekunde(n) lang und fügt ihm <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu.<br /><br />Diese Fähigkeit hat 2&nbsp;Aufladungen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Wiederaufladungsrate", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [70, 95, 120, 145, 170], [1, 1, 1, 1, 1], [1800, 1800, 1800, 1800, 1800], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/95/120/145/170", "1", "1800", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "BandageToss.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AuraofDespair", "name": "Verzweiflung", "description": "Vom Schmerz überwältigt verlieren nahe Gegner pro Sekunde einen Teil ihres maximalen Lebens. Außerdem werden ihre <font color='#9b0f5f'>Flüche</font> erne<PERSON>t.", "tooltip": "<toggle>Aktivierbar:</toggle> Amumu beginnt zu weinen und fügt <PERSON> in der Nähe jede Sekunde <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ basedamage }} plus {{ totalhealthdamage }}&nbsp;% ihres maximalen Lebens zu. Außerdem erneuert er dabei seinen <keywordMajor>Fluch</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden in Höhe eines Prozentsatzes des Lebens"], "effect": ["{{ healthdamage }}&nbsp;% -> {{ healthdamageNL }}&nbsp;%"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [8, 8, 8, 8, 8], "costBurn": "8", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }} pro Sekunde", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "AuraofDespair.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }} pro Sekunde"}, {"id": "Tantrum", "name": "Wutanfall", "description": "Ver<PERSON>ert dauerhaft den normalen Schaden, den Amumu erleiden würde. Er kann seine Wut entfesseln und an Gegnern in der Nähe Schaden verursachen. <PERSON><PERSON>, wenn Amumu getroffen wird, verringert sich die Abklingzeit von „Wutanfall“.", "tooltip": "<spellPassive>Passiv:</spellPassive> Amumu erleidet um {{ damagereduction }}&nbsp;verringerten normalen Schaden. Wenn Amumu von einem Angriff getroffen wird, verringert sich die Abklingzeit dieser Fähigkeit außerdem um {{ e3 }}&nbsp;Sekunden.<br /><br /><spellActive>Aktiv:</spellActive> Amumu bekommt einen Wu<PERSON>fall, der Gegnern in der Nähe <magicDamage>{{ tantrumdamage }}&nbsp;magischen Schaden</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> ve<PERSON>", "Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [5, 7, 9, 11, 13], [65, 95, 125, 155, 185], [0.75, 0.75, 0.75, 0.75, 0.75], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.03, 0.03, 0.03, 0.03, 0.03], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "5/7/9/11/13", "65/95/125/155/185", "0.75", "0", "0", "0", "0.03", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "Tantrum.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CurseoftheSadMummy", "name": "Fluch der traurigen Mumie", "description": "<PERSON><PERSON>u wickelt nahe <PERSON> in Bandagen, die seinen <keywordMajor><PERSON><PERSON><PERSON></keywordMajor> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> zufügen und sie betäuben.", "tooltip": "Amumu wirft seine Bandagen aus, <status>betäubt</status> getroffene G<PERSON>ner {{ rduration }}&nbsp;<PERSON><PERSON><PERSON> lang, fügt ihnen <magicDamage>{{ rcalculateddamage }}&nbsp;magischen Schaden</magicDamage> zu und belegt sie mit einem <keywordMajor>Fluch</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rdamage }} -> {{ rdamageNL }}"]}, "maxrank": 3, "cooldown": [150, 125, 100], "cooldownBurn": "150/125/100", "cost": [100, 150, 200], "costBurn": "100/150/200", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "CurseoftheSadMummy.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Verfluchte Berührung", "description": "Amumus normale Angriffe <font color='#9b0f5f'>verfluchen</font> seine <PERSON> und lassen sie zusätzlichen absoluten Schaden erleiden, wenn sie von magischem Schaden getroffen werden.", "image": {"full": "Amumu_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}