{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "<PERSON><PERSON>", "title": "la gattina magica", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "<PERSON><PERSON> da Battaglia", "chromas": true}, {"id": "350011", "num": 11, "name": "<PERSON>umi <PERSON>rca Cuori", "chromas": true}, {"id": "350019", "num": 19, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "350028", "num": 28, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "350037", "num": 37, "name": "Yuumi EDG", "chromas": true}, {"id": "350039", "num": 39, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "350049", "num": 49, "name": "<PERSON><PERSON> Cibernetica", "chromas": true}, {"id": "350050", "num": 50, "name": "<PERSON>umi <PERSON>ina Cibernetica (edizione prestigio)", "chromas": false}, {"id": "350061", "num": 61, "name": "Yuumi Portatrice della Notte", "chromas": true}], "lore": "<PERSON><PERSON>, una gattina magica di Bandle City, era il famiglio di Norra, un'incantatrice yordle. <PERSON><PERSON> la misteriosa scomparsa della sua padrona, <PERSON><PERSON> è diventata la custode del senziente Libro dei Portali di Norra e insieme viaggiano tra le dimensioni alla sua ricerca. Sempre bisognosa d'affetto, <PERSON><PERSON> cerca amici che la accompagnino nel suo viaggio e li protegge con scudi luminosi e una determinazione incrollabile. Libro fa fatica a mantenerla concentrata sulla missione, visto che l'attenzione di Yuumi viene spesso attirata da distrazioni come pesci e sonnellini. Alla fine, però, ricomincia sempre a cercare la sua amica.", "blurb": "<PERSON><PERSON>, una gattina magica di Bandle City, era il famiglio di Norra, un'incantatrice yordle. <PERSON><PERSON> la misteriosa scomparsa della sua padrona, <PERSON><PERSON> è diventata la custode del senziente Libro dei Portali di Norra e insieme viaggiano tra le dimensioni alla...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "<PERSON><PERSON><PERSON> stregatto", "description": "Yuumi lancia un proiettile che infligge danni e rallenta il primo bersaglio colpito. Infligge danni bonus e rallentamento potenziato se impiega almeno 1,35 secondi per raggiungere il bersaglio. Mentre è con il Migliore amico, il rallentamento è sempre potenziato e conferisce danni bonus sul colpo all'alleato.<br><br><PERSON>uando è collegata, il proiettile può essere controllato con il puntatore per un breve periodo.", "tooltip": "Yuumi evoca un proiettile vagante che infligge <magicDamage>{{ totalmissiledamage }} danni magici</magicDamage> al primo nemico e <status>rallenta</status> il bersaglio di un {{ slowamount }}%.<br /><br />Se lancia l'abilità mentre è <keywordMajor>collegata</keywordMajor>, Yuumi può controllare per un breve periodo il proiettile con il mouse, prima che acceleri in linea retta. Il proiettile accelerato infligge invece <magicDamage>{{ totalmissiledamageempowered }} danni magici</magicDamage> e <status>rallenta</status> il bersaglio di un {{ empoweredslowamount }}% per {{ empoweredslowduration }} secondi.<br /><br /><keywordMajor>Bonus Migliore amico:</keywordMajor> il <status>rallentamento</status> di <spellName>Proiettile stregatto</spellName> sarà sempre potenziato e quando colpisce un campione nemico gli infligge anche <magicDamage>{{ onhitdamagecalc }} danni magici</magicDamage> <OnHit>sul colpo %i:OnHit%</OnHit> per {{ buffduration }} secondi.<br /><br /><rules>I danni bonus sul colpo possono essere aumentati del {{ allycritchancemaxamp*100 }}% in base alla probabilità di colpo critico dell'alleato.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in mana", "Danni base", "<PERSON><PERSON>", "Quantità rallentamento potenziato", "<PERSON><PERSON> sul colpo"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "{{ empoweredslowamount }}% -> {{ empoweredslowamountNL }}%", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YuumiW", "name": "Vicini vicini!", "description": "<PERSON><PERSON> scatta verso un alleato bersaglio, diventando non bersagliabile per tutti tranne che per le torri. Quando è con il suo Migliore amico, ottiene guarigione e potere dello scudo e conferisce all'alleato guarigione sul colpo.", "tooltip": "<spellPassive>Passiva:</spellPassive> quando è con il <keywordMajor>Mi<PERSON>ore amico</keywordMajor>, <PERSON><PERSON> otti<PERSON> un <keywordMajor>{{ healandshieldpower*100 }}% di guarigione e potere dello scudo</keywordMajor> aggiuntivo e il suo alleato ripristina <healing>{{ healthonhit }} salute</healing> <OnHit>sul colpo %i:OnHit%</OnHit>.<br /><br /><spellActive>Attiva:</spellActive> Yuumi scatta verso un campione alleato a cui si <keywordMajor>collega</keywordMajor>. Mentre Yuumi è <keywordMajor>collegata</keywordMajor>, segue i movimenti del suo compagno e non è bersagliabile tranne che dalle torri.<br /><br />Gli effetti <status>immobilizzanti</status> su Yuumi mettono questa abilità in una ricarica da {{ ccattachlockout }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Guarigione sul colpo", "Guarigione bonus e potere dello scudo"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "YuumiE", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Protegge Yuumi e potenzia velocità di movimento e velocità d'attacco. Se è collegata, lo passa al suo alleato.<br>", "tooltip": "<PERSON><PERSON> si protegge, bloccando <shield>{{ totalshielding }} danni</shield> e ottenendo un <attackSpeed>{{ totalattackspeed }}% di velocità d'attacco</attackSpeed> per {{ msduration }} secondi. <PERSON><PERSON> lo scudo persiste, il bersaglio ottiene anche un <speed>{{ msamount }}% di velocità di movimento</speed>.<br /><br />Se Yuumi è <keywordMajor>collegata</keywordMajor>, quest'abilità ha effetto sul suo alleato e gli ripristina anche <magicDamage>{{ manarestore }} mana</magicDamage>, con un incremento fino a un {{ maxmanapercincrease*100 }}% in base al mana mancante del bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Costo in mana", "<PERSON><PERSON><PERSON><PERSON> mana", "Velocità d'attacco"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "YuumiR", "name": "Epilogo", "description": "Yuumi canalizza cinque ondate che danneggiano i nemici e curano gli alleati. <PERSON><PERSON> può muoversi, collegarsi e lanciare Tiragraffi mentre canalizza. Mentre è collegata al suo migliore amico, l'abilità segue anche il mouse.", "tooltip": "Yuumi canalizza per {{ ultduration }} secondi, lanciando {{ numberofwaves }} ondate magiche che hanno effetto su entrambe le squadre. Se l'inizio del lancio avviene quando è <keywordMajor>collegata</keywordMajor>, <PERSON><PERSON> può guidare le ondate con il mouse.<br /><br />I nemici colpiti subiscono <magicDamage>{{ totalmissiledamage }} danni magici</magicDamage> e vengono <status>rallentati</status> di un {{ baseslow*-100 }}% per {{ ccduration }} secondi, con incrementi di un {{ bonusslowperwave*-100 }}% per ogni ondata a segno.<br /><br />I campioni alleati vengono curati di <healing>{{ totalhealperwave }} salute</healing> per ondata. La cura in eccesso viene convertita in uno <shield>scudo</shield>.<br /><br /><keywordMajor>Bonus Migliore amico</keywordMajor>: Per il suo <keywordMajor>Migliore amico</keywordMajor>, la guarigione è aumentata a <healing>{{ enhancedhealperwave }} salute</healing>.<br /><br /><rules>Lanciare <spellName>Vicini vicini!</spellName> blocca le ondate nella direzione attuale.<br />Yuumi può muoversi e lanciare <spellName>Tiragraffi</spellName> durante la canalizzazione.</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base per proiettile", "Guarigione base per ondata"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Amicizia felina", "description": "Periodicamente, quando Yuumi colpisce un campione con un attacco o un'abilità, recupera salute per sé e per il prossimo alleato a cui si collega.<br><br>Quando è collegata, <PERSON>umi genera un legame speciale con i suoi alleati. L'alleato con il legame più forte potenzia le abilità di Yuumi, quando è collegata.", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}