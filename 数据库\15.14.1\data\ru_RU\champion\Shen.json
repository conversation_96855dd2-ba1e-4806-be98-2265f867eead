{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shen": {"id": "<PERSON>", "key": "98", "name": "<PERSON><PERSON><PERSON>", "title": "Око сумрака", "image": {"full": "Shen.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "98000", "num": 0, "name": "default", "chromas": false}, {"id": "98001", "num": 1, "name": "Ледяной Шен", "chromas": false}, {"id": "98002", "num": 2, "name": "Желтый ниндзя Шен", "chromas": false}, {"id": "98003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Шен", "chromas": true}, {"id": "98004", "num": 4, "name": "<PERSON><PERSON>н Кровавая Луна", "chromas": false}, {"id": "98005", "num": 5, "name": "Военачальник Шен", "chromas": true}, {"id": "98006", "num": 6, "name": "TPA Шен", "chromas": false}, {"id": "98015", "num": 15, "name": "Шен Импульсный Огонь", "chromas": true}, {"id": "98016", "num": 16, "name": "Инфернальный Шен", "chromas": true}, {"id": "98022", "num": 22, "name": "Шен из Пси-отряда", "chromas": true}, {"id": "98040", "num": 40, "name": "Шен Грозовой Клинок", "chromas": true}, {"id": "98049", "num": 49, "name": "Пепельный страж Шен", "chromas": true}, {"id": "98051", "num": 51, "name": "Поборник чести Шен", "chromas": false}], "lore": "Шен – это Око сумрака, лидер тайной организации ионийских воинов, известной как Кинку. Он ограждает себя от сбивающих с толку эмоций, предрассудков и эго, следуя скрытой тропой беспристрастного суждения между царством духов и физическим миром. Шен обязан обеспечивать равновесие между двумя мирами и любой, кто грозит его нарушить, отведает его мечей из стали и магической энергии.", "blurb": "Шен – это Око сумрака, лидер тайной организации ионийских воинов, известной как Кинку. Он ограждает себя от сбивающих с толку эмоций, предрассудков и эго, следуя скрытой тропой беспристрастного суждения между царством духов и физическим миром. Шен...", "allytips": ["Следите за союзниками и будьте готовы прийти им на помощь, используя абсолютное умение.", "Используйте тот факт, что ваши умения требуют энергии, а не маны, в свою пользу."], "enemytips": ["Будьте готовы уклониться от Теневого рывка и нанести ответный удар.", "Когда Шен достигнет 6-го уровня, будьте осторожны с его абсолютным умением: оно действует по всей карте и может быстро переломить ход боя."], "tags": ["Tank"], "partype": "Энергия", "info": {"attack": 3, "defense": 9, "magic": 3, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 400, "mpperlevel": 0, "movespeed": 340, "armor": 34, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.751}, "spells": [{"id": "ShenQ", "name": "Сумеречная атака", "description": "Шен призывает призрачный клинок, после чего его следующие автоатаки наносят урон, зависящий от максимального запаса здоровья цели. Если клинок на своем пути к Шену задевает вражеского чемпиона, автоатаки дополнительно существенно усиливаются, а задетый чемпион замедляется при попытке убежать от Шена.", "tooltip": "Шен призывает к себе <keywordMajor>призрачный клинок</keywordMajor>. Пораженные враги <status>замедляются</status> на {{ e4 }}% при движении от Шена в течение следующих {{ e5 }} сек.<br /><br />Кроме того, {{ e3 }} следующие автоатаки Шена дополнительно наносят <magicDamage>магический урон в размере {{ baseflatdamage }} плюс {{ basepercenthealth }} от максимального запаса здоровья</magicDamage>. Если <keywordMajor>призрачный клинок</keywordMajor> задел вражеского чемпиона, урон увеличивается до <magicDamage>{{ baseflatdamage }} плюс {{ emppercenthealth }} от максимального запаса здоровья</magicDamage>, а скорость усиленных автоатак Шена увеличивается на <attackSpeed>{{ e9 }}%</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Базовый процентный урон", "Усиленный процентный урон", "Замедление", "Стоимость – @AbilityResourceName@", "Максимальный урон монстрам", "Перезарядка"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e6 }}% -> {{ e6NL }}%", "{{ e4 }}% -> {{ e4NL }}%", "{{ cost }} -> {{ costNL }}", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.25, 6.5, 5.75, 5], "cooldownBurn": "8/7.25/6.5/5.75/5", "cost": [140, 130, 120, 110, 100], "costBurn": "140/130/120/110/100", "datavalues": {}, "effect": [null, [1, 1, 1, 1, 1], [2, 2.5, 3, 3.5, 4], [3, 3, 3, 3, 3], [25, 30, 35, 40, 45], [2, 2, 2, 2, 2], [5, 5.5, 6, 6.5, 7], [120, 140, 160, 180, 200], [8, 8, 8, 8, 8], [50, 50, 50, 50, 50], [75, 75, 75, 75, 75]], "effectBurn": [null, "1", "2/2.5/3/3.5/4", "3", "25/30/35/40/45", "2", "5/5.5/6/6.5/7", "120/140/160/180/200", "8", "50", "75"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ShenQ.png", "sprite": "spell12.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "ShenW", "name": "Пристанище духа", "description": "Призрачный клинок блокирует автоатаки, направленные против Шена и союзных чемпионов, в зоне вокруг себя.", "tooltip": "Шен создает вокруг своего <keywordMajor>призрачного клинка</keywordMajor> охраняемую зону на {{ e1 }} сек. Автоатаки против союзных чемпионов в этой области блокируются. <br /><br />Если в момент применения рядом с <keywordMajor>призрачным клинком</keywordMajor> нет чемпионов, создание зоны отсрочивается на время до {{ e2 }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [1.75, 1.75, 1.75, 1.75, 1.75], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "1.75", "2", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ShenW.png", "sprite": "spell12.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "ShenE", "name": "Теневой рывок", "description": "Шен совершает рывок в указанном направлении, провоцируя вражеских чемпионов на своем пути.", "tooltip": "<spellPassive>Пассивно:</spellPassive> Шен восстанавливает <keywordMajor>{{ energyrefund }} энергии</keywordMajor>, когда наносит урон с помощью этого умения или <spellName>Сумеречной атаки</spellName>.<br /><br /><spellActive>Активно:</spellActive> Шен совершает рывок, <status>провоцируя</status> чемпионов и лесных монстров на {{ ccduration }} сек., а также нанося им <physicalDamage>{{ tauntdamage }} физического урона</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [150, 150, 150, 150, 150], "costBurn": "150", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ShenE.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "ShenR", "name": "Плечом к плечу", "description": "Шен защищает союзного чемпиона поглощающим урон щитом и через некоторое время телепортируется к нему на помощь.", "tooltip": "Шен выбирает любого союзного чемпиона на карте и накладывает на него <shield>щит прочностью от {{ shield }} до {{ maxshield }}</shield> в зависимости от недостающего здоровья цели на {{ shieldduration }} сек. (максимальная прочность достигается при 60% недостающего здоровья). Спустя {{ channelduration }} сек. подготовки Шен телепортируется к цели со своим <keywordMajor>призрачным клинком</keywordMajor>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Минимальная прочность щита", "Максимальная прочность щита", "Перезарядка"], "effect": ["{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ baseshieldamount*1.600000 }} -> {{ baseshieldamountnl*1.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [200, 180, 160], "cooldownBurn": "200/180/160", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [35000, 35000, 35000], "rangeBurn": "35000", "image": {"full": "ShenR.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Барьер Ци", "description": "После применения умения Шен получает щит. Если умение оказывает воздействие на союзного или вражеского чемпиона, время перезарядки Барьера Ци сокращается.", "image": {"full": "Shen_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}