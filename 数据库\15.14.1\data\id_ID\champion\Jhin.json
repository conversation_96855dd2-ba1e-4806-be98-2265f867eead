{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jhin": {"id": "<PERSON><PERSON>", "key": "202", "name": "<PERSON><PERSON>", "title": "the Virtuoso", "image": {"full": "Jhin.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "202000", "num": 0, "name": "default", "chromas": false}, {"id": "202001", "num": 1, "name": "High Noon Jhin", "chromas": true}, {"id": "202002", "num": 2, "name": "Blood Moon Jhin", "chromas": false}, {"id": "202003", "num": 3, "name": "SKT T1 Jhin", "chromas": false}, {"id": "202004", "num": 4, "name": "PROJECT: <PERSON><PERSON>", "chromas": false}, {"id": "202005", "num": 5, "name": "Dark Cosmic Jhin", "chromas": false}, {"id": "202014", "num": 14, "name": "Shan Hai Scrolls Jhin", "chromas": true}, {"id": "202023", "num": 23, "name": "DWG Jhin", "chromas": true}, {"id": "202025", "num": 25, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "202036", "num": 36, "name": "Soul Fighter Jhin", "chromas": false}, {"id": "202037", "num": 37, "name": "Dark Cosmic Erasure Jhin", "chromas": true}, {"id": "202047", "num": 47, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "<PERSON>hin adalah seorang psikopat kriminal yang sangat teliti dan percaya bahwa pembunuhan adalah seni. <PERSON><PERSON> menja<PERSON>, tetapi dibebaskan oleh elemen bayangan dalam dewan penguasa <PERSON>, pembu<PERSON>h berantai ini sekarang bekerja sebagai assassin kom<PERSON><PERSON><PERSON> mere<PERSON>. Dengan menggunakan pistol sebagai kuasnya, <PERSON>hin menciptakan karya brutal artistik yang membuat korban dan penonton ngeri. Dia mendapat kesenangan dari pertunjukan teaternya yang kejam dan mengerikan, membuatnya jadi pilihan yang cocok untuk mengirimkan pesan paling kuat: teror.", "blurb": "<PERSON><PERSON> adalah seorang psikopat kriminal yang sangat teliti dan percaya bahwa pembunuhan adalah seni. <PERSON><PERSON> menja<PERSON>, tetapi dibe<PERSON>kan oleh elemen bayangan dalam dewan pengua<PERSON>, pembu<PERSON><PERSON> berantai ini sekarang bekerja sebagai assassin...", "allytips": ["Deadly Flourish memiliki jang<PERSON>uan luar biasa. <PERSON><PERSON> mendek<PERSON>, perhatikan musuh yang bisa di-root.", "Ultimamu memberikan damage jauh lebih kecil ke musuh dengan health penuh. Incar target yang sudah lemah saat mereka kabur.", "<PERSON><PERSON> ma<PERSON><PERSON> bi<PERSON> cast spell saat reload. Manfaatkan waktu itu untuk menyerang."], "enemytips": ["Deadly Flourish hanya memberi Root pada mereka yang terkena salah satu dari basic attack, j<PERSON><PERSON><PERSON>, atau sekutu <PERSON> dalam 4 detik terakhir.", "Jhin menempatkan jebakan tak terlihat di seluruh penjuru peta. Hati-hati saat melangkah!", "Serangan <PERSON> lumayan kuat, tetapi dia kehabisan amunisi setelah tembakan keempat. <PERSON>akan momen ini untuk menyerang dan melumpuhkannya."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 2, "magic": 6, "difficulty": 6}, "stats": {"hp": 655, "hpperlevel": 107, "mp": 300, "mpperlevel": 50, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.4, "attackspeedperlevel": 0, "attackspeed": 0.625}, "spells": [{"id": "JhinQ", "name": "Dancing Grenade", "description": "<PERSON>hin melemparkan kotak peluru magis ke arah musuh. Lemparan ini bisa mengenai hingga empat target dengan damage yang berta<PERSON>h tiap kali berhasil kill.", "tooltip": "<PERSON><PERSON> melemparkan kotak peluru yang menghasilkan <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> sebelum memantul ke musuh di sekitar yang belum terkena.<br /><br />Kotak peluru ini bisa mengenai target maksimum {{ tooltipmaxtargetshit }} kali. Musuh yang mati sesaat setelah terkena serangan akan meningkatkan damage serangan berikutnya sebesar {{ percentamponkill*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Rasio Total AD", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ adratio*100.000000 }}%-> {{ adrationl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "JhinQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinW", "name": "Deadly Flourish", "description": "<PERSON>hin mengacungkan tongkatnya, melepaskan satu tembakan dengan range yang sangat jauh. Tembakan ini bisa menembus minion dan monster, tetapi akan terhenti pada champion pertama yang dikenainya. Jika target baru dikenai oleh sekutu <PERSON>, lotus trap, atau menerima damage dari <PERSON>, target tersebut akan terkena root.", "tooltip": "<PERSON>hin melepaskan tembakan jarak jauh, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> pada champion pertama yang terkena dan musuh lain yang dilaluinya.<br /><br />Jika skill ini mengenai champion yang sudah menerima damage dari champion sekutu dalam {{ spottingduration }} detik terakhir, tembakan ini akan menerapkan <status>Root</status> pada mereka selama {{ rootduration }} detik dan memberi Move Speed pada <spellName>Whisper</spellName> Jhin.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ rootduration }}-> {{ rootdurationNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "JhinW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinE", "name": "Captive Audience", "description": "Jhin menempatkan lotus trap tak terlihat yang mekar saat ada yang melangkah di atasnya. Bunganya menerapkan slow pada musuh di sekitar sebelum menghasilkan damage dengan ledakan kelopak gerigi. <br><br><font color='#FFFFFF'>Beauty in Death -</font> Saat Jhin membunuh champion musuh, lotus trap akan mekar di dekat jasadnya.", "tooltip": "<passive>Pasif:</passive> Champion yang dibu<PERSON>h Jhin akan memunculkan dan meledakkan Lotus Trap di lokasinya.<br /><br /><active>Aktif:</active> <PERSON><PERSON> menempatkan Lotus Trap tak terlihat selama {{ trapduration }} menit yang menciptakan zona yang menerapkan <status>Slow</status> sebesar {{ trapslowamount*100 }}% saat diinjak musuh. Setelah {{ trapdetonationtime }} detik, jebakan ini meledak, men<PERSON><PERSON>lkan <magicDamage>{{ totaldamage }} magic damage</magicDamage>.<br /><br />Ability ini memiliki 2 charge (refresh {{ ammorechargeratetooltip }} detik).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ ammorechargetime }}-> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [2, 2, 2, 2, 2], "cooldownBurn": "2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "JhinE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinR", "name": "<PERSON><PERSON><PERSON>", "description": "Jhin melakukan channel dan mengubah Whisper menjadi meriam raksasa yang terpasang di bahunya. Meriam ini bisa melepaskan 4 tembakan super yang melesat sangat jauh serta menembus minion dan monster, tetapi terhenti pada champion pertama yang dikenainya. Whisper melumpuhkan musuh yang terkena, menerapkan slow dan menghasilkan damage eksekusi pada musuh. Tembakan ke-4 dibuat secara istimewa, dengan kekuatan yang dahsyat, dan dijamin menghasilkan critical strike.", "tooltip": "<PERSON><PERSON> bersiap dan melakukan channel, sehingga bisa melepaskan 4 tembakan super, masing-masing mengh<PERSON>lkan antara <physicalDamage>{{ damagecalc }}</physicalDamage> dan <physicalDamage>{{ maxincreasecalc }} physical damage</physicalDamage> pada champion pertama yang terkena berdasarkan persentase health-nya yang hilang, dan menerapkan <status>Slow</status> pada mereka sebesar {{ slowpercent*100 }}% selama {{ slowduration }} detik. Tembakan keempat menghasilkan Critical Strike dengan {{ fourthshotmultiplier*100 }}% damage.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ damage }}-> {{ damageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JhinR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Whisper", "description": "Pistol meriam mi<PERSON>, <PERSON><PERSON><PERSON>, ad<PERSON><PERSON> alat yang dirancang untuk menghasilkan damage dahsyat. Senjata ini bisa menembak dengan kecepatan tetap dan hanya berisi empat tembakan. <PERSON>hin memberikan dark magic ke peluru terakhir yang menghasilkan critical strike dan damage eksekusi bonus. <PERSON>iap kali Whisper mengh<PERSON>lkan crit, <PERSON><PERSON> mendapatkan peningkatan Move Speed signifikan.", "image": {"full": "Jhin_P.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}