{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nunu": {"id": "<PERSON><PERSON><PERSON>", "key": "20", "name": "<PERSON>un<PERSON> <PERSON><PERSON>", "title": "un băiat și un yeti", "image": {"full": "Nunu.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "20000", "num": 0, "name": "default", "chromas": false}, {"id": "20001", "num": 1, "name": "<PERSON>unu <PERSON><PERSON>", "chromas": false}, {"id": "20002", "num": 2, "name": "Nunu <PERSON>, spiridușii Moșului", "chromas": false}, {"id": "20003", "num": 3, "name": "Nunu și Willump din împărăția gunoaielor", "chromas": false}, {"id": "20004", "num": 4, "name": "<PERSON><PERSON><PERSON> <PERSON><PERSON> bot", "chromas": false}, {"id": "20005", "num": 5, "name": "Nunu <PERSON>i Willump demolatori", "chromas": false}, {"id": "20006", "num": 6, "name": "Nunu și Willump TPA", "chromas": false}, {"id": "20007", "num": 7, "name": "Nunu și Willump zombificați", "chromas": false}, {"id": "20008", "num": 8, "name": "Nunu <PERSON><PERSON> hârtie", "chromas": true}, {"id": "20016", "num": 16, "name": "Nunu și Willump distracție în spațiu", "chromas": true}, {"id": "20026", "num": 26, "name": "Nunu și Willump albinuță", "chromas": true}, {"id": "20035", "num": 35, "name": "Nunu și Willump paladini cosmici", "chromas": true}, {"id": "20044", "num": 44, "name": "Nunu și Willump noapte de groază", "chromas": true}], "lore": "Odată ca niciodată, era un băiat care voia să ucidă un monstru fioros ca să le arate tuturor că și el e un erou; cu toate astea, și-a dat seama că bestia, un yeti singuratic și înzestrat cu puteri magice, avea nevoie doar de-un prieten. Legați de o putere antică și de o iubire împărtășită pentru bulgări de z<PERSON>padă, acum Nunu și Willump cutreieră Freljordul în lung și-n lat pentru a da viață aventurilor din imaginația lor. Cei doi speră că, undeva, cumva, o vor găsi pe mama lui Nunu. Dacă o vor putea salva, poate vor deveni cu adevărat eroi...", "blurb": "Odată ca niciodată, era un băiat care voia să ucidă un monstru fioros ca să le arate tuturor că și el e un erou; cu toate astea, și-a dat seama că bestia, un yeti singuratic și înzestrat cu puteri magice, avea nevoie doar de-un prieten. Legați de o...", "allytips": ["''Hap'' îi permite lui Nunu să rămână într-un culoar și să înfrunte adversarii de la distanță.", "Poți întrerupe ''Zero absolut'' mai <PERSON><PERSON><PERSON>, mulțumindu-te cu daune parțiale, dacă adversarul vrea să fugă din raza ta de acțiune.", "E de preferat să amâni folosirea abilității ''Zero absolut'' până după utilizarea rundei inițiale de dezactivări. E mai bine să te abții, nu te grăbi să te angajezi într-o luptă de echipă."], "enemytips": ["Dacă întrerupi pregătirea abilității ''Zero absolut'', daunele suferite de echipa ta vor fi mai mici.", "Folosește ''Flash'' ca să te asiguri că scapi de ''Zero absolut''.", "''Cel mai mare bulgăre'' se deplasează foarte repede, însă nu poate vira la fel de repede, așa că nu încerca să-l eviți fugind în linie dreaptă.  În schimb, virează brusc și neașteptat."], "tags": ["Tank", "Mage"], "partype": "Mană", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 610, "hpperlevel": 90, "mp": 280, "mpperlevel": 42, "movespeed": 345, "armor": 29, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 5, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.625}, "spells": [{"id": "NunuQ", "name": "<PERSON>p", "description": "Willump mușcă dintr-un minion, monstru sau campion inamic, provocându-i daune și vindecându-se.", "tooltip": "Nunu îl roagă pe Willump să muște un inamic, provocându-i <trueDamage>{{ monsterminiondamage }} daune reale</trueDamage> și refăcându-și <healing>{{ monsterhealing }} viață</healing> când mușcă un minion sau un monstru din junglă. Împotriva unui campion, provoacă <magicDamage>{{ totalchampiondamage }} daune magice</magicDamage> și reface <healing>{{ championhealing }} viață</healing>.<br /><br /><healing>Vindecarea</healing> crește cu {{ lowhealthhealingscalar*100 }}% când Nunu și Willump au sub {{ lowhealththreshhold*100 }}% viață.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune împotriva <PERSON>ș<PERSON>lor", "Daune împotriva campionilor", "Vindecare", "Timp de reactivare"], "effect": ["{{ monsterminiondamage }} -> {{ monsterminiondamageNL }}", "{{ championdamage }} -> {{ championdamageNL }}", "{{ basehealing }} -> {{ basehealingNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mană", "maxammo": "-1", "range": [125, 125, 125, 125, 125], "rangeBurn": "125", "image": {"full": "NunuQ.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} mană"}, {"id": "NunuW", "name": "Cel mai mare bulgăre!", "description": "Willump creează un bulgăre de zăpadă care crește în dimensiune și viteză pe măsură ce-l rostogolește.  Bulgărele de zăpadă le provoacă daune inamicilor și-i aruncă în sus.", "tooltip": "Nunu și Willump creează un bulgăre de zăpadă care crește în dimensiune și viteză pe măsură ce-l rostogolesc. Ei iau virajele mai încet în timp ce îl rostogolesc, însă pot crește viteza virajului continuând să vireze în aceeași direcție.<br /><br />Bulgărele provoacă între <magicDamage>{{ noimpactsnowballdamage }}</magicDamage> și <magicDamage>{{ maximumsnowballdamage }} daune magice</magicDamage> și <status>aruncă în sus</status> timp de între {{ baseknockupduration }} și {{ maximumstunduration }} secunde când lovește un campion, monstru mare sau zid. Aceste valori cresc în funcție de distanța parcursă.<br /><br />Nunu și <PERSON> pot <recast>refolosi</recast> abilitatea pentru a da drumul bulgărelui mai devreme.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune de bază", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "NunuW.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuE", "name": "Bătaie cu bulgări", "description": "<PERSON>unu aruncă mai mulți bulgări de zăpadă care le provoacă daune inamicilor.  <PERSON><PERSON><PERSON> ace<PERSON>, Willump țintuiește campionii inamici sau monștrii mari care au fost loviți de un bulgăre.", "tooltip": "Nunu aruncă trei bulgă<PERSON> de <PERSON>, provocând <magicDamage>{{ totalsnowballdamage }} daune magice</magicDamage> per bulgăre și <status>încetinind</status> inamicii loviți de toți trei cu {{ slowamount*-100 }}% timp de {{ slowduration }} secundă. Nunu poate <recast>refolosi</recast> abilitatea de încă două ori.<br /><br />Dup<PERSON> {{ totalspellduration }} secunde, Nunu <status>țintuiește</status> toți inamicii din apropiere care au fost <status>încetiniți</status> de bulgării de zăpadă timp de {{ rootduration }} secunde și le provoacă încă <magicDamage>{{ totalrootdamage }} daune magice</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Cost de man<PERSON>", "Reducere a vitezei de mișcare", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ slowamount*-100.000000 }}% -> {{ slowamountnl*-100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "NunuE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NunuR", "name": "Zero absolut", "description": "Nunu și Willump creează un viscol puternic într-o zonă, care încetinește inamicii și le provoacă daune uriașe la sfârșit.", "tooltip": "Nunu și Willump pregătesc un viscol puternic timp de până la {{ channelduration }} secunde. Inamicii din interior sunt <status>încetiniți</status> cu {{ slowstartamount*-100 }}%, valoare ce crește până la {{ maxslowamount*-100 }}% pe durata pregătirii. Nunu și Willump primesc și un <shield>scut în valoare de {{ totalshieldamount }}</shield> pe durata abilității, care scade apoi de-a lungul a {{ shielddecayduration }} secunde.<br /><br />Când viscolul se încheie, zona se detonează, provocând până la <magicDamage>{{ maximumdamage }} daune magice</magicDamage> în funcție de timpul de pregătire. Nunu și Willump pot <recast>refolosi</recast> abilitatea pentru a încheia viscolul mai devreme.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Valoarea scutului", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseshieldamount }} -> {{ baseshieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "NunuR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON>unu le crește vitezele de atac și de mișcare lui Willump și unui aliat din apropiere, iar atacurile de bază ale lui Willump le provoacă daune inamicilor din jurul țintei.", "image": {"full": "NunuPassive.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}