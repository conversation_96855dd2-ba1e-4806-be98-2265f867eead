{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Camille": {"id": "<PERSON>", "key": "164", "name": "<PERSON>", "title": "l'ombra d'acciaio", "image": {"full": "Camille.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "164000", "num": 0, "name": "default", "chromas": false}, {"id": "164001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "164002", "num": 2, "name": "<PERSON>", "chromas": true}, {"id": "164010", "num": 10, "name": "Camille iG", "chromas": false}, {"id": "164011", "num": 11, "name": "<PERSON>", "chromas": true}, {"id": "164021", "num": 21, "name": "<PERSON>to", "chromas": true}, {"id": "164031", "num": 31, "name": "<PERSON> F<PERSON> dell'Inverno", "chromas": true}, {"id": "164032", "num": 32, "name": "<PERSON> dell'Inverno (edizione prestigio)", "chromas": false}], "lore": "Armata per agire oltre i limiti della legge, <PERSON>'elegante agente capo del Clan Ferros, che contribuisce al buon funzionamento della macchina di Piltover e del suo ventre zaunita. Precisa e versatile, trova qualsiasi lavoro approssimativo come qualcosa di imbarazzante e da correggere. Con una mente affilata quanto le lame che porta, <PERSON> ha potenziato il suo corpo con l'hextech per superare i limiti umani e in molti si chiedono se ormai non sia più una macchina che una donna.", "blurb": "Armata per agire oltre i limiti della legge, <PERSON> l'elegante agente capo del Clan Ferros, che contribuisce al buon funzionamento della macchina di Piltover e del suo ventre zaunita. Precisa e versatile, trova qualsiasi lavoro approssimativo come...", "allytips": ["Prova ad aspettare che l'altra squadra sia distratta dagli scontri con la tua squadra e usa Rampino per colpire i bersagli vulnerabili.", "Usa gli effetti di controllo delle tue abilità per mettere a segno entrambi gli attacchi di Protocollo di precisione sui nemici."], "enemytips": ["Lo scudo di Camille funziona solo contro un tipo di danni alla volta, quindi colpiscila quando è vulnerabile ai tuoi danni.", "Ultimatum hextech ha una gittata di lancio molto corta, quindi cerca di usare Flash per allontanarti, prima che si avvicini."], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 99, "mp": 339, "mpperlevel": 52, "movespeed": 340, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.8, "mpregen": 8.15, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 3.8, "attackspeedperlevel": 2.5, "attackspeed": 0.644}, "spells": [{"id": "CamilleQ", "name": "<PERSON>lo di precisione", "description": "Il prossimo attacco di Camille infligge danni bonus e conferisce velocità di movimento bonus. Questa abilità può essere rilanciata per un breve periodo di tempo. Infligge molti più danni se Camille fa passare del tempo tra i due attacchi.", "tooltip": "Il prossimo attacco base di <PERSON> infligge <physicalDamage>{{ bonusdamage }} danni fisici</physicalDamage> aggiuntivi e le fornisce <speed>{{ msbonus*100 }}% velocità di movimento</speed> per {{ msduration }} secondo/i. L'abilità può essere <recast>rilanciata</recast> entro i {{ qtotalrecasttime }} secondi successivi.<br /><br />Se l'attacco base del <recast>rilancio</recast> colpisce almeno {{ qrampuptime }} secondi dopo il primo, i danni bonus aumentano fino a <physicalDamage>{{ empoweredbonusdamage }}</physicalDamage> e {{ damageconversionpercentage }} dei danni dell'attacco viene convertito in <trueDamage>danni puri</trueDamage>.<br /><br /><rules>Questa abilità attiva gli effetti sull'incantesimo quando infligge danni.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rapporto attacco fisico totale", "Velocità di movimento", "Ricarica"], "effect": ["{{ tadratio*100.000000 }}% -> {{ tadrationl*100.000000 }}%", "{{ msbonus*100.000000 }}% -> {{ msbonusnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "CamilleQ.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleW", "name": "Falciata tattica", "description": "<PERSON> colpisce un'area conica dopo un periodo di tempo, infliggendo danni. I nemici nella metà esterna vengono rallentati e subiscono danni aggiuntivi, curand<PERSON>.", "tooltip": "<PERSON> si prepara e colpisce, infliggendo <physicalDamage>{{ basedamagetotal }} danni fisici</physicalDamage>.<br /><br />I nemici colpiti dalla metà esterna vengono <status>rallentati</status> del {{ slowpercentage }}%, con un effetto che diminuisce nell'arco di {{ slowduration }} secondi, e subiscono <physicalDamage>danni fisici aggiuntivi pari al {{ outeredgetooltip }} della loro salute massima</physicalDamage>. <PERSON> recupera il <healing>{{ outerconehealingratio }}% dei danni bonus inflitti ai campioni come salute</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON>", "<PERSON><PERSON> salute massima", "Ricarica"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ outerconemaxhpdamage*100.000000 }}% -> {{ outerconemaxhpdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 15.5, 14, 12.5, 11], "cooldownBurn": "17/15.5/14/12.5/11", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [610, 610, 610, 610, 610], "rangeBurn": "610", "image": {"full": "CamilleW.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleE", "name": "Ram<PERSON><PERSON>", "description": "<PERSON> si porta verso un muro, bal<PERSON><PERSON> da esso e lanciando in alto i campioni nemici quando atterra.", "tooltip": "Camille spara un rampino che si aggancia agli ostacoli, la trascina per 1 secondo e le permette di <recast>rilanciare</recast> questa abilità.<br /><br /><recast>Rilancio:</recast> <PERSON> scatta dalla parete, entrando in collisione con il primo campione nemico colpito. All'atterraggio, infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> ai nemici vicini e <status>stordisce</status> i campioni nemici per {{ knockupduration }} secondi. Gli scatti verso i campioni nemici coprono il doppio della distanza e conferiscono <attackSpeed>{{ asbuff*100 }}% velocità d'attacco</attackSpeed> per {{ asduration }} secondi all'impatto.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Velocità d'attacco"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ asbuff*100.000000 }}% -> {{ asbuffnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "CamilleE.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "CamilleR", "name": "Ultimatum hextech", "description": "Camille scatta verso un campione bersaglio, ancorandolo all'area. Infligge anche danni magici bonus al bersaglio con gli attacchi base.", "tooltip": "Camille diventa momentaneamente non bersagliabile e balza su un campione nemico, interrompendo le canalizzazioni e bloccandolo in un'area dalla quale non può scappare in nessun modo per {{ rduration }} secondi. Gli altri nemici nelle vicinanze vengono <status>respinti</status>. I suoi attacchi base contro il nemico intrappolato infliggono un <magicDamage>{{ rpercentcurrenthpdamage }}% della salute attuale in danni magici</magicDamage> aggiuntivi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> salute at<PERSON>ali", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ rpercentcurrenthpdamage }}% -> {{ rpercentcurrenthpdamageNL }}%", "{{ rduration }} -> {{ rdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 115, 90], "cooldownBurn": "140/115/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475], "rangeBurn": "475", "image": {"full": "CamilleR.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Difese adattive", "description": "Gli attacchi base sui campioni conferiscono uno scudo pari a una percentuale della salute massima di Camille contro il loro tipo di danni (fisici o magici) per un breve periodo.", "image": {"full": "Camille_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}