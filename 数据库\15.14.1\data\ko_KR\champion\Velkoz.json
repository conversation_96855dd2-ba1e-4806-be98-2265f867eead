{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Velkoz": {"id": "Velkoz", "key": "161", "name": "벨코즈", "title": "공허의 눈", "image": {"full": "Velkoz.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "161000", "num": 0, "name": "default", "chromas": false}, {"id": "161001", "num": 1, "name": "전투 기계 벨코즈", "chromas": false}, {"id": "161002", "num": 2, "name": "빛의 벨코즈", "chromas": false}, {"id": "161003", "num": 3, "name": "나 벨코즈 아니다", "chromas": false}, {"id": "161004", "num": 4, "name": "지옥의 벨코즈", "chromas": true}, {"id": "161011", "num": 11, "name": "어둠서리 벨코즈", "chromas": true}, {"id": "161020", "num": 20, "name": "벌코즈", "chromas": true}], "lore": "벨코즈가 룬테라에 나타난 첫 번째 공허태생인지는 확실하지 않다. 다만 벨코즈만큼 잔혹하면서 계산적인 지성체는 이제껏 존재하지 않았다. 다른 공허태생들은 주변의 모든 것을 닥치는 대로 집어삼키고 부패시키지만, 벨코즈는 현실 세계뿐 아니라 현실 세계에 사는 호전적이고 낯선 생명체들을 자세히 관찰하고 연구하여 공허가 이용할 수 있는 약점을 찾아낸다. 그렇다고 벨코즈가 그저 수동적인 관찰자라는 말은 아니다. 위협을 느끼면 치명적인 플라즈마 광선을 발사하거나 세상의 구성 자체를 분열시키는 방식으로 반격하기 때문이다.", "blurb": "벨코즈가 룬테라에 나타난 첫 번째 공허태생인지는 확실하지 않다. 다만 벨코즈만큼 잔혹하면서 계산적인 지성체는 이제껏 존재하지 않았다. 다른 공허태생들은 주변의 모든 것을 닥치는 대로 집어삼키고 부패시키지만, 벨코즈는 현실 세계뿐 아니라 현실 세계에 사는 호전적이고 낯선 생명체들을 자세히 관찰하고 연구하여 공허가 이용할 수 있는 약점을 찾아낸다. 그렇다고 벨코즈가 그저 수동적인 관찰자라는 말은 아니다. 위협을 느끼면 치명적인 플라즈마 광선을 발사하거나...", "allytips": ["공격로 대치 단계에서 공허 균열을 사용하면 미니언을 처치하면서 적 챔피언에게 유기물 분해 중첩까지 쌓을 수 있습니다. 그 다음 다른 스킬을 연계하세요.", "플라즈마 분열을 대각선으로 발사해 최대 사거리에서 갈라지게 하면 최초 투사체의 사거리 밖에 있는 적을 맞힐 수 있습니다. 대신 후퇴하기는 더 까다롭습니다.", "생물 분해 광선을 사용할 때는 이 스킬을 끊을 수 있는 스킬을 가진 챔피언이 많으므로 매우 주의하셔야 합니다."], "enemytips": ["벨코즈는 전투에서 혼자 남겨지게 되면 매우 위험한 존재가 됩니다. 초반에 먼저 집중 공격해서 처치하세요.", "벨코즈는 기동력이 상당히 낮아 급습에 취약합니다.", "생물 분해 광선은 기절, 침묵, 띄우기로 막을 수 있습니다."], "tags": ["Mage", "Support"], "partype": "마나", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 8}, "stats": {"hp": 590, "hpperlevel": 102, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 22, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1416, "attackspeedperlevel": 1.59, "attackspeed": 0.643}, "spells": [{"id": "VelkozQ", "name": "플라즈마 분열", "description": "벨코즈가 플라즈마 광선을 발사하고 나서 스킬을 재사용하거나 적에게 맞으면 광선이 둘로 갈라집니다. 이 광선은 적중 시 둔화를 적용하고 피해를 입힙니다.", "tooltip": "벨코즈가 플라즈마 광선을 발사해 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ slowamount*100 }}% <status>둔화</status>시킵니다. 둔화 효과는 {{ slowduration }}초에 걸쳐 사라집니다. 사거리 끝에 도달하거나, 대상을 맞히거나, 광선을 <recast>재사용</recast>하면 새로운 두 개의 광선이 90도 각도로 갈라져 발사됩니다.<br /><br />플라즈마 분열로 유닛 처치 시 <scaleMana>마나를 {{ tooltipmanarefund }}</scaleMana> 회복합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["총 피해량", "둔화 지속시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "VelkozQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VelkozW", "name": "공허 균열", "description": "벨코즈가 공허로 통하는 균열을 열어 최초 피해를 입힌 다음, 잠시 시간이 지난 후 두 번째 폭발로 피해를 가합니다.", "tooltip": "벨코즈가 공허로 통하는 균열을 열어 <magicDamage>{{ initialdamage }}의 마법 피해</magicDamage>를 입힙니다. 이후 균열이 폭발하며 <magicDamage>{{ secondarydamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />이 스킬은 2회까지 충전됩니다. ({{ ammorechargetime }}초마다 충전){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최초 피해량", "2차 피해량", "재충전 시간", "소모값 @AbilityResourceName@"], "effect": ["{{ baseinitialdamage }} -> {{ baseinitialdamageNL }}", "{{ basesecondarydamage }} -> {{ basesecondarydamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [30, 50, 70, 90, 110], [45, 75, 105, 135, 165], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0.25, 0.25, 0.25, 0.25, 0.25], [0.5, 0.5, 0.5, 0.5, 0.5], [88, 88, 88, 88, 88], [500, 500, 500, 500, 500], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/50/70/90/110", "45/75/105/135/165", "100", "0", "0.25", "0.5", "88", "500", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "2", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "VelkozW.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VelkozE", "name": "지각 붕괴", "description": "벨코즈가 일정 범위를 폭발시켜 적을 띄우고, 가까이 있는 적들은 약간 멀리 밀어냅니다.", "tooltip": "벨코즈가 가까운 지면을 붕괴시켜 폭발을 일으키며 {{ stunduration }}초 동안 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. 벨코즈와 가까이 있는 적은 <status>공중으로 떠오르는</status> 대신 <status>뒤로 밀려납니다</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13.5, 13, 12.5, 12], "cooldownBurn": "14/13.5/13/12.5/12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "VelkozE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "VelkozR", "name": "생물 분해 광선", "description": "벨코즈가 정신을 집중하고 광선을 발사하면, 광선이 2.5초 동안 커서 위치를 따라가며 마법 피해를 입히고 적을 둔화시킵니다. 유기물 분해 연구가 완료된 적 챔피언은 마법 피해 대신 고정 피해를 입습니다.", "tooltip": "벨코즈가 정신을 집중하여 마우스 커서를 따라가는 에너지 광선을 발사합니다. 이때 2.5초에 걸쳐 총 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ e3 }}% <status>둔화</status>시킵니다. 최근 <spellName>유기물 분해</spellName> 스킬로 피해를 입은 적에게는 <trueDamage>고정 피해</trueDamage>를 입힙니다.<br /><br />광선에 맞은 적에게는 주기적으로 <keywordMajor>분해</keywordMajor> 중첩이 쌓입니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["총 피해량", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [450, 625, 800], [7, 7, 7], [20, 20, 20], [40, 40, 40], [175, 175, 175], [7, 7, 7], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "450/625/800", "7", "20", "40", "175", "7", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1575, 1575, 1575], "rangeBurn": "1575", "image": {"full": "VelkozR.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "유기물 분해", "description": "벨코즈가 스킬을 적중시키면 대상에게 <keywordName>유기물 분해</keywordName> 중첩이 쌓입니다. 3회 중첩 시 대상은 큰 고정 피해를 입습니다.", "image": {"full": "VelKoz_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}