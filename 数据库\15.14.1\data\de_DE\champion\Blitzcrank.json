{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "der große Dampfgolem", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "53002", "num": 2, "name": "<PERSON><PERSON><PERSON>Blitz<PERSON>rank", "chromas": false}, {"id": "53003", "num": 3, "name": "Bumm-Bumm-Blitzcrank", "chromas": false}, {"id": "53004", "num": 4, "name": "Piltover Customs-Blitzcrank", "chromas": false}, {"id": "53005", "num": 5, "name": "Eindeutig nicht Blitzcrank", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Riot-Blitzcrank", "chromas": false}, {"id": "53011", "num": 11, "name": "Schlachtboss-Blitzcrank", "chromas": true}, {"id": "53020", "num": 20, "name": "<PERSON><PERSON><PERSON> Lanze Blitzcrank", "chromas": false}, {"id": "53021", "num": 21, "name": "Aufrichtige Lanze Blitzcrank", "chromas": false}, {"id": "53022", "num": 22, "name": "Hexenkessel-Blitzcrank", "chromas": true}, {"id": "53029", "num": 29, "name": "Weltraum-Groove-Blitz & Crank", "chromas": true}, {"id": "53036", "num": 36, "name": "Siegreicher Blitzcrank", "chromas": true}, {"id": "53047", "num": 47, "name": "Zenitspiele-Blitzcrank", "chromas": true}, {"id": "53056", "num": 56, "name": "Biencrank", "chromas": true}], "lore": "Blitzcrank ist eine massive, beinahe unzerstörbare Maschine aus Zhaun, deren ursprünglicher Zweck die Entsorgung von Giftmüll war. <PERSON><PERSON> <PERSON><PERSON> j<PERSON>, dass ihn seine Hauptaufgabe zu sehr einschränkte, und modifizierte sich selbst, um der zerbrechlichen Gemeinschaft der Grube besser dienen zu können. Selbstlos nutzt Blitzcrank seine Stärke und Widerstandsfähigkeit, um andere zu schützen – mal streckt er seine ausfahrbare, helfende Metallhand aus, mal setzt er Unruhestifter mit Elektroschocks außer Gefecht.", "blurb": "Blitzcrank ist eine massive, beinahe unzerstörbare Maschine aus Zhaun, deren ursprünglicher Zweck die Entsorgung von Giftmüll war. <PERSON><PERSON> <PERSON><PERSON> j<PERSON>, dass ihn seine Hauptaufgabe zu sehr einschränkte, und modifizierte sich selbst, um der zerbrechlichen...", "allytips": ["Eine Kombination aus „Raketengriff“, „Geladene Faust“ und „Statisches Feld“ kann gegen einzelne Gegner verheerend wirken.", "Benutze Blitzcranks „Raketengriff“, um einen Gegner in Schussweite deines Turmes zu ziehen, gef<PERSON><PERSON> von „Geladene Faust“ gibt dies dem Turm genug Zeit, den Gegner mehrmals zu treffen."], "enemytips": ["Blitzcranks passive „Manabarriere“ gewährt ihm einen Schild, wenn er stark geschwächt ist.", "Hinter Vasallen zu bleiben, kann dich vor dem „Raketengriff“ schützen. Blitzcranks „Raketengriff“ greift sich immer nur den ersten Gegner, auf den er trifft."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "Raketengriff", "description": "Blitzcrank fährt seine rechte Hand aus, um damit einen Gegner zu greifen. Dabei verursacht er Schaden und zieht den Gegner zu sich.", "tooltip": "Blitzcrank fährt seine rechte Hand aus, <status>zieht</status> den ersten getroffenen Gegner zu sich und verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Overdrive", "name": "Übertaktet", "description": "Blitzcrank überlädt sich selbst und erhält dadurch stark erhöhtes Lauf- und Angriffstempo. Nach Ablauf des Effekts wird er vorübergehend verlangsamt.", "tooltip": "Blitzcrank übertaktet sich und erhält <speed>{{ movespeedmod*100 }}&nbsp;% abfallendes <PERSON>ftempo</speed> sowie {{ duration }}&nbsp;Sekunde(n) lang <attackSpeed>{{ attackspeedmod*100 }}&nbsp;% Angriffstempo</attackSpeed>.<br /><br />Danach wird Blitzcrank {{ slowduration }}&nbsp;Sekunden lang um {{ movespeedmodreduction*100 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "Angriffstempo"], "effect": ["{{ movespeedmod*100.000000 }}&nbsp;% -> {{ movespeedmodnl*100.000000 }}&nbsp;%", "{{ attackspeedmod*100.000000 }}&nbsp;% -> {{ attackspeedmodnl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PowerFist", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Blitzcrank lädt seine Faust auf, um bei seinem nächsten Angriff doppelten Schaden zu verursachen und das Ziel in die Luft zu schleudern.", "tooltip": "Blitzcrank lädt seine <PERSON> auf, wodurch sein nächster Angriff das Ziel {{ ccduration }}&nbsp;Sekunde(n) lang <status>in die Luft schleudert</status> und <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "StaticField", "name": "Statisches Feld", "description": "Von Blitzcrank angegriffene Gegner werden markiert und erleiden nach 1&nbsp;Sekunde Blitzschaden. Zusätzlich kann Blitzcrank diese Fähigkeit aktivieren, um Schilde von nahen G<PERSON>nern zu entfernen, ihnen Schaden zuzufügen und sie kurzzeitig verstummen zu lassen.", "tooltip": "<spellPassive>Passiv</spellPassive>: <PERSON><PERSON><PERSON><PERSON> diese Fähigkeit verfügbar ist, laden Blitze Blitzcranks Fäuste auf und er markiert <PERSON>, die er angreift. Nach 1&nbsp;Sekunde erleiden die markierten Gegner e<PERSON> Schock, der ihnen <magicDamage>{{ passivedamage }}&nbsp;magischen Schaden</magicDamage> zufügt.<br /><br /><spellActive>Aktiv: </spellActive>Blitzcrank überlädt sich, verursacht an Gegnern in der Nähe <magicDamage>{{ activedamage }}&nbsp;magischen Schaden</magicDamage> und lässt sie {{ silenceduration }}&nbsp;Sekunden lang <status>verstummen</status>. Ihre Schilde werden ebenfalls zerstört.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden – Passiv", "Passive Fähigkeitsstärke – Skalierung", "Grundschaden – Aktiv", "Abklingzeit der aktiven Fähigkeit"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}&nbsp;% -> {{ passiveaprationl*100.000000 }}&nbsp;%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Manabarriere", "description": "Bei niedrigem Leben erhält Blitzcrank einen Schild abhä<PERSON><PERSON> von se<PERSON>m Mana.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}