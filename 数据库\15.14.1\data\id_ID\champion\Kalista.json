{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kalista": {"id": "Kalista", "key": "429", "name": "Kalista", "title": "the Spear of <PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Kalista.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "429000", "num": 0, "name": "default", "chromas": false}, {"id": "429001", "num": 1, "name": "Blood Moon Kalista", "chromas": false}, {"id": "429002", "num": 2, "name": "Worlds 2015 Kalista", "chromas": true}, {"id": "429003", "num": 3, "name": "SKT T1 Kalista", "chromas": false}, {"id": "429005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "429014", "num": 14, "name": "Faerie Court Kalista", "chromas": true}, {"id": "429024", "num": 24, "name": "Dawn<PERSON><PERSON> Kalista", "chromas": true}], "lore": "<PERSON>tu penuh amarah dan dendam, <PERSON><PERSON> adalah ar<PERSON>h pembalasan nan abadi, mimpi buruk berbaju zirah yang dipanggil dari Shadow Isles untuk memburu para penipu dan pengkhianat. <PERSON>reka yang dikhianati mungkin sangat ingin den<PERSON><PERSON>, tetapi <PERSON> hanya akan membalaskan dendam mereka yang rela membayar dengan jiwanya sendiri. <PERSON>reka yang menjadi sasaran amarah Kalista harus berpasrah, karena semua perjanjian yang dibuat dengan pemburu kematian ini hanya akan berakhir dengan tombak dinginnya yang menembus jiwa mereka.", "blurb": "<PERSON><PERSON> penuh amarah dan dendam, <PERSON><PERSON> ad<PERSON> a<PERSON><PERSON>h pembalasan nan abadi, mimpi buruk berbaju zirah yang dipanggil dari Shadow Isles untuk memburu para penipu dan pengkhianat. <PERSON><PERSON>a yang dikhianati mungkin sangat ingin den<PERSON>, tetapi...", "allytips": ["Rend sangat berguna untuk last hit, karena cooldown-nya akan direset jika berhasil membunuh target. ", "Member<PERSON><PERSON> perintah bergerak satu kali untuk mengaktifkan Martial Poise tidak akan menghapus target basic attack Kalista.", "<PERSON><PERSON><PERSON>, Move Speed Kalista secara efektif meningkat dengan Attack Speed."], "enemytips": ["Mobility Kalista bergantung pada serangan yang dilakukan. Ini berarti Mobility-nya rendah saat dia berada di luar range serangannya dan pengurangan Attack Speed mengurangi jarak yang bisa dia tempuh dalam sebuah pertempuran.", "Kalista tidak bisa membatalkan persiapan basic attack-nya.  <PERSON><PERSON><PERSON><PERSON> dia sangat gesit, ini memberi kesempatan untuk mengincar dia dengan spell jika kamu dapat memprediksi saat dia mulai menyerang.", "<PERSON><PERSON> kamu ber<PERSON><PERSON> men<PERSON> dari pan<PERSON>, term<PERSON><PERSON> melal<PERSON> se<PERSON>k, basic attack-nya akan meleset, jatuh tanpa mengenai apa pun."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 4, "difficulty": 7}, "stats": {"hp": 560, "hpperlevel": 114, "mp": 300, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 6.3, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 4, "attackspeedperlevel": 4.5, "attackspeed": 0.694}, "spells": [{"id": "KalistaMysticShot", "name": "<PERSON>", "description": "Melempar tombak dengan cepat yang akan menembus musuh yang terbunuh.", "tooltip": "Kalista melemparkan tombak, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> pada target pertama yang terkena. Jika target terbunuh, tombaknya akan terus melesat, membawa stack <spellName>Rend</spellName> ke target berikutnya yang terkena.<br /><br />Kalista bisa dash setelah menggunakan Ability ini dengan <spellName>Martial Poise</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["@AbilityResourceName@ Cost", "Damage"], "effect": ["{{ cost }}-> {{ costNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "KalistaMysticShot.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KalistaW", "name": "Sentinel", "description": "Mendapat damage bonus ketika Kalista dan Oathsworn-nya menyer<PERSON> target yang sama. <br><br>Aktifkan untuk mengerahkan jiwa yang memantau jalur dan memperlihatkan area di depannya.", "tooltip": "<spellPassive>Pasif:</spellPassive> Saat Kalista dan <keywordMajor>Oathsworn</keywordMajor>-nya menyerang target yang sama, dia akan memberikan <magicDamage>{{ maxhealthdamage*100 }}% magic damage dari Health maksimum</magicDamage>. Efek ini memiliki Cooldown {{ pertargetcooldown }} detik per target dan batas maksimal {{ maximummonsterdamage }} untuk unit non-champion.<br /><br /><spellPassive>Aktif: </spellPassive>Kalista mengerahkan hantu untuk berpatroli di area tertentu sebanyak tiga putaran. Champion yang terdeteksi akan terlihat selama 4 detik. Ability ini memiliki 2 charge (refresh setiap {{ ammorechargetooltip }} detik).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Health Maksimum", "Ammo Recharge", "Batas Damage Monster"], "effect": ["{{ maxhealthdamage*100.000000 }}%-> {{ maxhealthdamagenl*100.000000 }}%", "{{ ammorechargetooltip }}-> {{ ammorechargetooltipNL }}", "{{ maximummonsterdamage }}-> {{ maximummonsterdamageNL }}"]}, "maxrank": 5, "cooldown": [30, 30, 30, 30, 30], "cooldownBurn": "30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 80, 80, 80, 80], [0, 0, 0, 0, 0], [125, 150, 175, 200, 225], [90, 80, 70, 60, 50], [0, 0, 0, 0, 0], [75, 100, 125, 150, 175], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80", "0", "125/150/175/200/225", "90/80/70/60/50", "0", "75/100/125/150/175", "10", "0", "0", "0"], "vars": [], "costType": "Tanpa Biaya", "maxammo": "2", "range": [5000, 5000, 5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "KalistaW.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "Tanpa Biaya"}, {"id": "KalistaExpungeWrapper", "name": "Rend", "description": "Tombak yang dilempar menancap pada sasaran. Aktifkan untuk mencabut tombak, menerapkan slow dan mengh<PERSON>lkan damage yang makin besar.", "tooltip": "<spellPassive>Pasif: </spellPassive>Tombak Kalista akan bertahan di targetnya selama 4 detik, menambah stack beberapa kali.<br /><br /><spellActive>Aktif:</spellActive> Kalista menarik tombak dari musuh di sekitar, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ normaldamage }}</physicalDamage> plus <physicalDamage>{{ additionaldamage }} physical damage</physicalDamage> per tombak setelah tombak pertama. Menerapkan <status>Slow</status> pada musuh yang terkena sebesar <attention>{{ totalslowamount }}</attention> selama {{ slowduration }} detik.<br /><br />Jika Ability ini membunuh setidaknya satu target, Cooldown-nya diperbarui dan <scaleMana>{{ manarefund }} Mana</scaleMana> akan dikembalikan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage per Stack", "Rasio <PERSON> per <PERSON>", "Slow Move Speed", "Penge<PERSON><PERSON>", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ additionalbasedamage }}-> {{ additionalbasedamageNL }}", "{{ additionaladratio*100.000000 }}%-> {{ additionaladrationl*100.000000 }}%", "{{ slowamount*100.000000 }}%-> {{ slowamountnl*100.000000 }}%", "{{ manarefund }}-> {{ manarefundNL }}", "{{ fakedcooldown }}-> {{ fakedcooldownNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "2", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaExpungeWrapper.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KalistaRx", "name": "Fate's Call", "description": "Kalista memanggil pasangan Oathsworn untuk teleportasi ke sisinya. <PERSON><PERSON>a bisa dash ke suatu posisi untuk knock back champion musuh.", "tooltip": "Kalista membekukan <keywordMajor>Oathsworn</keywordMajor> menjadi Stasis dan menariknya mendekat hingga 4 detik. <keywordMajor>Oathsworn</keywordMajor> bisa diklik untuk meluncur sendiri, berhenti pada champion pertama yang terkena dan <status>Knock Back</status> semua musuh di sekitar. Jika <keywordMajor>Oathsworn</keywordMajor> mengenai champion, penempatannya akan berada dalam range serangan maksimumnya.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ e2 }}-> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [1, 1.5, 2], [40, 60, 80], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "1/1.5/2", "40/60/80", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaRx.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> perintah bergerak ketika Kalista mempersiapkan basic attack atau Pierce agar dia melompat dalam jarak pendek saat melancarkan serangannya.", "image": {"full": "Kalista_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}