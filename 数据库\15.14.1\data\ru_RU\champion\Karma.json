{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "Карма", "title": "Просвещенная", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "Карма, богиня Солнца", "chromas": false}, {"id": "43002", "num": 2, "name": "Карма Ветвь Сакуры", "chromas": false}, {"id": "43003", "num": 3, "name": "Традиционная Карма", "chromas": false}, {"id": "43004", "num": 4, "name": "Карма из Ордена лотоса", "chromas": false}, {"id": "43005", "num": 5, "name": "Хранительница Карма", "chromas": false}, {"id": "43006", "num": 6, "name": "Снегурочка Карма", "chromas": false}, {"id": "43007", "num": 7, "name": "Завоевательница Карма", "chromas": true}, {"id": "43008", "num": 8, "name": "Карма Темная Звезда", "chromas": true}, {"id": "43019", "num": 19, "name": "Предвестница зари Карма", "chromas": false}, {"id": "43026", "num": 26, "name": "Карма из Одиссеи", "chromas": false}, {"id": "43027", "num": 27, "name": "Падшая Карма", "chromas": true}, {"id": "43044", "num": 44, "name": "Безмятежный дракон Карма", "chromas": false}, {"id": "43054", "num": 54, "name": "Королева фей Карма", "chromas": false}, {"id": "43061", "num": 61, "name": "Инфернальная Карма", "chromas": false}, {"id": "43070", "num": 70, "name": "Дух цветения Карма", "chromas": false}], "lore": "Ни один смертный не представляет духовные традиции Ионии лучше, чем Карма. Она являет собой древнюю душу, множество раз претерпевшую перевоплощение и сохранившую воспоминания о каждой своей жизни. Мало кто способен постичь природу ее силы – но важно то, что в трудные времена Карма остается лидером своего народа. Она знает, что за мир и гармонию порой приходится дорого платить – как ей самой, так и ее возлюбленной родине.", "blurb": "Ни один смертный не представляет духовные традиции Ионии лучше, чем Карма. Она являет собой древнюю душу, множество раз претерпевшую перевоплощение и сохранившую воспоминания о каждой своей жизни. Мало кто способен постичь природу ее силы – но важно то...", "allytips": ["Разгорающийся огонь подходит для агрессивного стиля игры. Старайтесь как можно чаще поражать врага умениями и автоатаками, чтобы сократить время перезарядки Мантры и продолжить нападение.", "Если вам сложно преследовать врагов при использовании Решимости, замедляйте их Внутренним пламенем или ускоряйтесь, используя Воодушевление.", "Не держите Мантру про запас. Разгорающийся огонь эффективнее всего в командных боях, так как позволяет перезарядить Мантру несколько раз."], "enemytips": ["Пассивное умение Кармы сокращает перезарядку Мантры при поражении чемпионов.", "Сила духа наносит дополнительный урон в области. Старайтесь быстро выйти из круга, чтобы избежать урона.", "Решимость Кармы - отличный способ заставить вас отступить. Если Решимость на вас, отойдите подальше, чтобы не попасть под эффект обездвиживания, и сразу после ищите способ напасть."], "tags": ["Mage", "Support"], "partype": "Мана", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "Внутреннее пламя", "description": "Карма выпускает заряд духовной энергии, который взрывается при столкновении с противником, нанося урон.<br><br>Эффект Мантры: Карма дополнительно увеличивает разрушительную силу Внутреннего пламени, создавая на земле энергетическую аномалию, наносящую урон по прошествии некоторого времени.", "tooltip": "Карма выпускает сгусток энергии, нанося <magicDamage>{{ totaldamage }} магического урона</magicDamage> первой пораженной цели и окружающим врагам, а также <status>замедляя</status> их на {{ slowamount*-100 }}% на {{ slowduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KarmaSpiritBind", "name": "Решимость", "description": "Карма создает связь с выбранным вражеским чемпионом, делая его видимым и нанося ему урон. Если связь не прерывается, враг обездвиживается и вновь получает урон.<br><br>Эффект Мантры: Карма усиливает связь с врагом, восстанавливая себе здоровье и увеличивая продолжительность обездвиживания.", "tooltip": "Карма создает связь между собой и чемпионом или лесным монстром, нанося цели <magicDamage>{{ initialdamage }} магического урона</magicDamage> и раскрывая ее на {{ tetherduration }} сек. Если связь не разрывается, цель снова получает <magicDamage>{{ initialdamage }} магического урона</magicDamage> и <status>обездвиживается</status> на {{ rootduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Продолжительность обездвиживания", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "KarmaSolKimShield", "name": "Воодушевление", "description": "Карма защищает союзника щитом, поглощающим урон и увеличивающим его скорость передвижения.<br><br>Эффект Мантры: щит излучает энергию, которая увеличивает его прочность и накладывает Воодушевление на ближайших союзных чемпионов.", "tooltip": "Карма накладывает на союзного чемпиона <shield>щит прочностью {{ totalshield }}</shield> на {{ shieldduration }} сек. и увеличивает его <speed>скорость передвижения на {{ movespeed*100 }}%</speed> на {{ movespeedduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Прочность щита", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "Мантра", "description": "Карма усиливает свое следующее умение дополнительным эффектом. Мантра доступна на 1-м уровне и не требует очка умений.", "tooltip": "Карма усиливает свое следующее умение, примененное в течение 8 сек.<br /><li><spellName>Внутреннее пламя</spellName>: дополнительно наносит <magicDamage>{{ rqimpactdamage }} магического урона</magicDamage> и оставляет круг пламени, который <status>замедляет</status> врагов и наносит еще <magicDamage>{{ rqfielddamage }} магического урона</magicDamage>.<li><spellName>Решимость</spellName>: Карма восстанавливает <healing>{{ rwhealamount }} от недостающего здоровья</healing> во время образования и разрыва связи, а продолжительность <status>обездвиживания</status> увеличивается на {{ rwbonusroot }} сек.<li><spellName>Воодушевление</spellName>: прочность <shield>щита</shield>, накладываемого на цель, увеличивается на <shield>{{ rebonusshield }}</shield>. Кроме того, Карма накладывает на окружающих чемпионов щит прочностью {{ rebonusshieldarea }} и увеличивает их <speed>скорость передвижения на {{ removespeed*100 }}%</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон Cилы духа при столкновении", "Урон Cилы духа при взрыве", "Увеличение времени обездвиживания Обновления", "Прочность щита Вызова", "Перезарядка"], "effect": ["{{ qbonusdamage }} -> {{ qbonusdamageNL }}", "{{ qdetonationdamage }} -> {{ qdetonationdamageNL }}", "{{ rwbonusroot }} -> {{ rwbonusrootNL }}", "{{ ebonusshield }} -> {{ ebonusshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Разгорающийся огонь", "description": "Умения Кармы, наносящие урон, сокращают перезарядку Мантры.", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}