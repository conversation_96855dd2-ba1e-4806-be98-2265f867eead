{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zoe": {"id": "<PERSON>", "key": "142", "name": "<PERSON>", "title": "der Aspekt des Zwielichts", "image": {"full": "Zoe.png", "sprite": "champion5.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "142000", "num": 0, "name": "default", "chromas": false}, {"id": "142001", "num": 1, "name": "Cyberpop-<PERSON>", "chromas": false}, {"id": "142002", "num": 2, "name": "Poolparty-<PERSON>", "chromas": true}, {"id": "142009", "num": 9, "name": "Sternenwächterin Zoe", "chromas": true}, {"id": "142018", "num": 18, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "142019", "num": 19, "name": "<PERSON><PERSON><PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "142020", "num": 20, "name": "EDG-<PERSON>", "chromas": true}, {"id": "142022", "num": 22, "name": "Wintergeweihte Zoe", "chromas": true}, {"id": "142033", "num": 33, "name": "Sternenvernichter-<PERSON>", "chromas": true}], "lore": "<PERSON> verkörpert den Übermut, die Fantasie und den Wandel. Als kosmische Botin des Targon verkündet sie bedeutende Ereignisse, die die Welten in ihren Grundfesten erschüttern. Ihre bloße Präsenz verzerrt die arkane Mathematik, die sämtliche Realitäten regiert, und manchmal verursacht sie schreckliche Katastrophen ohne Absicht oder Bosheit. Das erklärt vielleicht die unbekümmerte Sorglosigkeit, mit der Zoe ihren Pflichten nachgeht, und warum sie so viel Zeit zum Spielen, Sterbliche Veralbern oder für anderweitigen Zeitvertreib hat. Eine Begegnung mit Zoe kann erfreulich und lebensbereichernd sein, doch birgt sie stets mehr als anfangs angenommen und führt oft zu gefährlichen Situationen.", "blurb": "<PERSON> verkörpert den Übermut, die Fantasie und den Wandel. Als kosmische Botin des Targon verkündet sie bedeutende Ereignisse, die die Welten in ihren Grundfesten erschüttern. Ihre bloße Präsenz verzerrt die arkane Mathematik, die sämtliche Realitäten...", "allytips": ["„Wanderstern“ verursacht mehr Schaden, je weiter er geflogen ist. Wenn du ihn hinter dir wirkst und dann seine Rich<PERSON>g änderst, kannst du massiven Schaden verursachen.", "Schlafende Ziele erleiden doppelten Schaden, also greife sie mit deiner stärksten Schadensfähigkeit an.", "„Schlafblasen-Alarm“ bewegt sich durch Mauern weiter fort. Suche dir ein Versteck und bereite dich darauf vor, deine Gegner aus großer Distanz zu erledigen."], "enemytips": ["<PERSON><PERSON> verursacht mehr Schaden, je weiter er geflogen ist.", "Nachdem sie <PERSON>uer gewirkt hat, muss <PERSON> zu ihrem Ausgangspunkt zurückkehren, was sie verwundbar für Gegenangriffe macht.", "Schlafblasen-<PERSON>arm bewegt sich durch Mauern weiter fort. Hinder<PERSON>, sich im Nebel des Krieges zu verstecken, damit sie niemandem mit dem Zauber überraschen kann."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 106, "mp": 425, "mpperlevel": 25, "movespeed": 340, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.65, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "Zoe<PERSON>", "name": "Wanderstern", "description": "<PERSON> feuert ein <PERSON>, das sie im Flug umlenken kann. Je weiter es geradeaus fliegt, desto höher ist sein <PERSON>en.", "tooltip": "<PERSON> feuert einen <PERSON> ab, der umso mehr Schaden verursacht, je weiter er fliegt, bis er den ersten Gegner und umstehende Gegner trifft (zwischen <magicDamage>{{ totaldamagetooltip }} und {{ maxdamagetooltip }}&nbsp;magischen Schaden</magicDamage>).<br /><br /><PERSON> kann diese Fähigkeit <recast>reaktivieren</recast>, um das Geschoss zu einer Position in der Nähe von Zoe umzuleiten.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8.5, 8, 7.5, 7, 6.5], "cooldownBurn": "8.5/8/7.5/7/6.5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeQ.png", "sprite": "spell17.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZoeW", "name": "Zauberkla<PERSON>", "description": "<PERSON> kann die Überreste gegnerischer Beschwörerzauber und aktiver Gegenstandszauber einsammeln, um sie einmalig selbst zu wirken. <PERSON><PERSON> von ihr selbst gewirkte Beschwörerzauber gewährt ihr 3&nbsp;Ges<PERSON>sse, die sie auf das nächstgelegene Ziel abfeuern kann.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON><PERSON><PERSON> lassen Zaubersplitter fallen, wenn sie einen Beschwörerzauber einsetzen oder einen aktiven Gegenstand benutzen. Bestimmte Vasallen lassen ebenfalls einen Zaubersplitter fallen, wenn <PERSON> oder ein Verbündeter in der Nähe sie tötet. <PERSON> kann diesen Splitter aufsammeln, um die jeweilige Fähigkeit einmal zu wirken.<br /><br /><spellPassive>Passiv:</spellPassive> Wenn Zoe diese Fähigkeit oder einen Beschwörerzauber einsetzt, erhält sie {{ e0 }}&nbsp;Sekunden lang <speed>{{ e9 }}&nbsp;% Lauftempo</speed> und schleudert 3&nbsp;Geschosse auf das Ziel, das Zoe zuletzt angegriffen hat. Diese Geschosse verursachen jeweils <magicDamage>{{ missiledamagetooltip }}&nbsp;magischen Schaden</magicDamage>.<br /><br /><spellActive>Aktiv:</spellActive> Setzt die Fähigkeit ein, die Zoe mit einem Zaubersplitter aufgesammelt hat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> insgesamt", "Lauftempo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "effect": ["{{ totalbasedamage*3.000000 }} -> {{ totalbasedamagenl*3.000000 }}", "{{ movespeedmod*100.000000 }}&nbsp;% -> {{ movespeedmodnl*100.000000 }}&nbsp;%", "{{ movespeedduration }} -> {{ movespeeddurationNL }}"]}, "maxrank": 5, "cooldown": [0.25, 0.25, 0.25, 0.25, 0.25], "cooldownBurn": "0.25", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3000, 4500, 6000, 0, 0], [0.1, 0.1, 0.1, 0.1, 0.1], [2500, 2500, 2500, 2500, 2500], [60, 60, 60, 60, 60], [20, 50, 80, 110, 140], [0.2, 0.2, 0.2, 0.2, 0.2], [0, 0, 0, 0, 0], [30, 40, 50, 60, 70], [2, 2.25, 2.5, 2.75, 3]], "effectBurn": [null, "0", "3000/4500/6000/0/0", "0.1", "2500", "60", "20/50/80/110/140", "0.2", "0", "30/40/50/60/70", "2/2.25/2.5/2.75/3"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [3000, 4500, 6000, 3000, 3000], "rangeBurn": "3000/4500/6000/3000/3000", "image": {"full": "ZoeW.png", "sprite": "spell17.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "ZoeE", "name": "Schlafblasen-Alarm", "description": "Macht das Ziel schläfrig und lässt es einschlafen. Während das Ziel schläft, ist dessen Magieresistenz verringert. Der erste erlittene Schaden, der das Ziel aufweckt, wird bis zu einer Obergrenze verdoppelt.", "tooltip": "<PERSON> tritt eine Blase mit <PERSON>hwu<PERSON> von sich weg, die <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage> verursacht und als Falle liegenbleibt, wenn sie nichts trifft. Die Reichweite der Blase ist erhöht, wenn sie über Terrain fliegt.<br /><br />Nach einer Verzögerung <status>schläft</status> das Opfer 2&nbsp;Sekunden lang ein, wodurch sich seine <scaleMR>Magieresistenz</scaleMR> um {{ percentpen*100 }}&nbsp;% verringert. Angriffe und Fähigkeiten wecken es auf, verursachen aber doppelten Schaden (bis zu <trueDamage>{{ breakdamagetooltip }}&nbsp;absoluten Schaden</trueDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Obergrenze für zusätzlichen Schaden", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [0.1, 0.15, 0.2, 0.25, 0.3], [5, 15, 25, 35, 45], [5, 5, 5, 5, 5], [0.1, 0.15, 0.2, 0.25, 0.3], [70, 110, 150, 190, 230], [0.45, 0.45, 0.45, 0.45, 0.45], [1.4, 1.4, 1.4, 1.4, 1.4], [2.25, 2.25, 2.25, 2.25, 2.25], [1, 1, 1, 1, 1]], "effectBurn": [null, "70/110/150/190/230", "0.1/0.15/0.2/0.25/0.3", "5/15/25/35/45", "5", "0.1/0.15/0.2/0.25/0.3", "70/110/150/190/230", "0.45", "1.4", "2.25", "1"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ZoeE.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZoeR", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Teleportiert dich 1&nbsp;Sekunde lang an einen Zielort in der Nähe. Teleportiere dich dann wieder zurück.", "tooltip": "<PERSON> teleportiert sich 1&nbsp;<PERSON><PERSON><PERSON> lang an einen Zielort in der Nähe. Danach teleportiert sie sich zurück. <PERSON> kann währenddessen Fähigkeiten und Angriffe einsetzen, sich aber nicht bewegen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [11, 8, 5], "cooldownBurn": "11/8/5", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [-0.3, -0.4, -0.5], [1.5, 2, 2.5], [4, 4, 4], [0.5, 0.5, 0.5], [3, 3, 3], [100, 200, 300], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "-0.3/-0.4/-0.5", "1.5/2/2.5", "4", "0.5", "3", "100/200/300", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575], "rangeBurn": "575", "image": {"full": "ZoeR.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Funkenflug!", "description": "Nachdem sie einen Zauber gewirkt hat, veru<PERSON>cht Zoes nächster normaler Angriff zusätzlichen magischen Schaden.", "image": {"full": "Zoe_P.png", "sprite": "passive5.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}