{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mordekaiser": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "82", "name": "Мордекайзер", "title": "Железный призрак", "image": {"full": "Mordekaiser.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "82000", "num": 0, "name": "default", "chromas": false}, {"id": "82001", "num": 1, "name": "Драконий рыцарь Мордекайзер", "chromas": false}, {"id": "82002", "num": 2, "name": "Инфернальный Мордекайзер", "chromas": false}, {"id": "82003", "num": 3, "name": "Морд<PERSON>к<PERSON><PERSON><PERSON><PERSON><PERSON>, гитар<PERSON><PERSON><PERSON> Pentakill", "chromas": false}, {"id": "82004", "num": 4, "name": "Лорд Мордекайзер", "chromas": false}, {"id": "82005", "num": 5, "name": "Трефовый король Мордекайзер", "chromas": false}, {"id": "82006", "num": 6, "name": "Мордекайзер Темная Звезда", "chromas": true}, {"id": "82013", "num": 13, "name": "ПРОЕКТ: Мордекайзер", "chromas": true}, {"id": "82023", "num": 23, "name": "Мордекайзер из Pentakill ''Lost Chapter''", "chromas": true}, {"id": "82032", "num": 32, "name": "Ковбой Мордекайзер", "chromas": true}, {"id": "82042", "num": 42, "name": "Пепельный палач Мордекайзер", "chromas": true}, {"id": "82044", "num": 44, "name": "Старый бог Мордекайзер", "chromas": true}, {"id": "82054", "num": 54, "name": "Сахн-Азал Мордекайзер", "chromas": false}], "lore": "Дважды сраженный и трижды рожденный, Мордекайзер – жестокий военачальник минувшей эпохи, который с помощью некромантии обрекает души на вечное служение. Сейчас уже мало кто помнит его ранние завоевания или знает истинные пределы его могущества – но те древние души, которые это понимают, страшатся его возможного возвращения, ведь следом за миром мертвых Мордекайзер постарается захватить мир живых.", "blurb": "Дважды сраженный и трижды рожденный, Мордекайзер – жестокий военачальник минувшей эпохи, который с помощью некромантии обрекает души на вечное служение. Сейчас уже мало кто помнит его ранние завоевания или знает истинные пределы его могущества – но те...", "allytips": ["Нападение – это ваша защита. Продолжайте сражаться, чтобы повысить прочность щитов умения Несокрушимый.", "Если вы попадете одним и тем же умением по нескольким чемпионам, то сможете быстрее активировать Восхождение тьмы.", "Применяйте Царство смерти к врагам с низким уровнем здоровья, чтобы наверняка убить их и сохранить похищенные показатели до конца командного сражения."], "enemytips": ["Когда Мордекайзер сражается с чемпионами, он получает ауру, которая наносит большой урон, так что держитесь от него подальше.", "Мордекайзер может превратить нанесенный урон в прочный щит, а затем поглотить его, восстановив здоровье.", "Царство смерти полностью отрежет вас от союзников. Старайтесь сохранить умения, повышающие подвижность, чтобы не дать Мордекайзеру до вас добраться."], "tags": ["Fighter", "Mage"], "partype": "Щит", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 645, "hpperlevel": 104, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 37, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 4, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "MordekaiserQ", "name": "Уничтожение", "description": "Мордекайзер бьет по земле булавой, нанося урон каждому пораженному врагу. Если враг один, урон по нему увеличивается.", "tooltip": "Мордекайзер ударяет по земле Вечерней звездой, нанося <magicDamage>{{ qdamage }} магического урона</magicDamage>. Если враг один, урон увеличивается до <magicDamage>{{ empowereddamagetooltip }}</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Урон изолированным целям", "Перезарядка"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ isolationscalar*100.000000 }}% -> {{ isolationscalarnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "MordekaiserQ.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "MordekaiserW", "name": "Несокрушимый", "description": "Мордекайзер накапливает нанесенный и полученный урон, а затем превращает его в щит. Он может поглотить щит, восстановив себе здоровье.", "tooltip": "<passive>Пассивно:</passive> Мордекайзер сохраняет {{ damageconversion*100 }}% от наносимого урона и {{ damagetakenconversion*100 }}% от получаемого урона.<br /><br /><active>Активно:</active> Мордекайзер превращает сохраненный урон в <shield>щит</shield>. Он может <recast>повторно применить</recast> это умение, чтобы восстановить <healing>здоровье в размере {{ healingpercent*100 }}% от оставшейся прочности щита</healing>.<br /><br />Минимальная прочность щита: <shield>{{ minhealthtooltip }}</shield><br />Максимальная прочность щита: <shield>{{ maxhealthtooltip }}</shield>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Процент лечения", "Перезарядка"], "effect": ["{{ healingpercent*100.000000 }}% -> {{ healingpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MordekaiserW.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "MordekaiserE", "name": "Смертельная хватка", "description": "Мордекайзер подтягивает всех врагов в области поражения.", "tooltip": "<spellPassive>Пассивно:</spellPassive> магическое пробивание Мордекайзера увеличено на {{ magicpen*100 }}%.<br /><br /><spellActive>Активно:</spellActive> Мордекайзер подтягивает врагов к себе, нанося <magicDamage>{{ totaldamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Магическое пробивание", "Урон", "Перезарядка"], "effect": ["{{ magicpen*100.000000 }}% -> {{ magicpennl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "MordekaiserE.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "MordekaiserR", "name": "Царство смерти", "description": "Мордекайзер переносит себя и жертву в другое измерение и похищает часть показателей цели. Если Мордекайзер убивает ее, он сохраняет показатели жертвы до ее возрождения.", "tooltip": "Мордекайзер отправляет чемпиона в царство смерти и переносится туда вместе с ним на {{ spiritrealmduration }} сек., похищая на это время {{ statstealpercentscalar*100 }}% от его основных показателей.<br /><br />Если Мордекайзер убивает этого врага в царстве смерти, он поглощает его душу и сохраняет похищенные показатели до его возрождения.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "MordekaiserR.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Восхождение тьмы", "description": "Мордекайзер получает ауру, наносящую большой урон, и ускоряется после попадания 3 атаками или умениями по чемпионам или монстрам.", "image": {"full": "MordekaiserPassive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}