{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Galio": {"id": "<PERSON><PERSON><PERSON>", "key": "3", "name": "ガリオ", "title": "伝説の巨像", "image": {"full": "Galio.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "3000", "num": 0, "name": "default", "chromas": false}, {"id": "3001", "num": 1, "name": "神秘のガリオ", "chromas": false}, {"id": "3002", "num": 2, "name": "ヘクステック ガリオ", "chromas": false}, {"id": "3003", "num": 3, "name": "コマンドー ガリオ", "chromas": false}, {"id": "3004", "num": 4, "name": "地獄の門衛ガリオ", "chromas": false}, {"id": "3005", "num": 5, "name": "おしゃれなガリオ", "chromas": false}, {"id": "3006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "3013", "num": 13, "name": "地獄の業火ガリオ", "chromas": true}, {"id": "3019", "num": 19, "name": "龍の守護者ガリオ", "chromas": true}, {"id": "3028", "num": 28, "name": "神話創生ガリオ", "chromas": true}], "lore": "輝ける都市デマーシアの外側で、石の巨像ガリオはずっと見張りを続けている。敵の魔法使いに対する防御機構として建造された彼は、強力な魔法によって生命が満たされるまで何十年も微動だにせず、ただ立ち続ける。そして、ひとたび活動を始めると、ガリオは動ける時間のほとんどを戦いのスリルと同胞たる国民を守るという稀有な名誉を味わうことに費やすのだ。だが彼の勝利はいつも皮肉なもので、彼が打ち倒すべき魔法こそ彼が動くための原動力であるために、勝利するたびに再び動かぬ彫像となってしまうのだ。", "blurb": "輝ける都市デマーシアの外側で、石の巨像ガリオはずっと見張りを続けている。敵の魔法使いに対する防御機構として建造された彼は、強力な魔法によって生命が満たされるまで何十年も微動だにせず、ただ立ち続ける。そして、ひとたび活動を始めると、ガリオは動ける時間のほとんどを戦いのスリルと同胞たる国民を守るという稀有な名誉を味わうことに費やすのだ。だが彼の勝利はいつも皮肉なもので、彼が打ち倒すべき魔法こそ彼が動くための原動力であるために、勝利するたびに再び動かぬ彫像となってしまうのだ。", "allytips": ["「デュランドの守り」はたとえ行動妨害を受けていたとしても構えを解くことができる。", "「英雄降臨」はミニマップの味方アイコンを指定することでも発動できる。", "「正義の鉄拳」の後ろに下がる動きを使えば敵のスキルを避けられる。"], "enemytips": ["「デュランドの守り」を構えている時は移動速度が低下する。", "「英雄降臨」はガリオがジャンプする前であれば行動妨害で中断させられる。", "「正義の鉄拳」は壁を越えられない。"], "tags": ["Tank", "Mage"], "partype": "マナ", "info": {"attack": 1, "defense": 10, "magic": 6, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 126, "mp": 410, "mpperlevel": 40, "movespeed": 340, "armor": 24, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 9.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "GalioQ", "name": "戦の旋風", "description": "2つの突風を巻き起こす。突風同士は重なり合い、継続ダメージを与える巨大な竜巻となる。", "tooltip": "それぞれ<magicDamage>{{ qmissiledamage }}の魔法ダメージ</magicDamage>を与える2つの突風を放つ。突風同士が交わると竜巻が発生して、<magicDamage>最大体力の{{ percentsuperqdamagett }}%の魔法ダメージ</magicDamage>を{{ superqduration }}秒かけて与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["突風ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "GalioQ.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GalioW", "name": "デュランドの守り", "description": "防御の構えを取り移動速度が低下する。構えを解くと、周囲の敵ユニットにタウント効果とダメージを与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> {{ passiveshieldooctimer }}秒間ダメージを受けずにいると、<shield>{{ totalpassiveshield }}の魔法ダメージを防ぐシールド</shield>を獲得する。<br /><br /><charge>チャージ開始:</charge> 受ける魔法ダメージを{{ magicdamagereduction }}、物理ダメージを{{ physicaldamagereduction }}軽減するようになり、自身が{{ e3 }}%の<status>スロウ効果</status>を受ける。<br /><br /><release>解放:</release> 敵チャンピオンに{{ e4 }} - {{ e7 }}秒間<status>タウント効果</status>を付与し、<magicDamage>{{ mintotaldamage }}</magicDamage> - <magicDamage>{{ maxtotaldamage }}の魔法ダメージ</magicDamage>を与えて、ダメージ軽減効果を{{ e8 }}秒間更新する。タウントの効果時間と効果範囲、ダメージはチャージ時間に応じて増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールドの体力反映率", "魔法ダメージ軽減率", "物理ダメージ軽減率", "最大ダメージ", "クールダウン"], "effect": ["{{ passiveshieldhealthratio*100.000000 }}% -> {{ passiveshieldhealthrationl*100.000000 }}%", "{{ e1 }}% -> {{ e1NL }}%", "{{ effect1amount*0.500000 }}% -> {{ effect1amountnl*0.500000 }}%", "{{ maximumwbasedamage }} -> {{ maximumwbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [2, 2, 2, 2, 2], [15, 15, 15, 15, 15], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [2, 2, 2, 2, 2], [1.25, 1.25, 1.25, 1.25, 1.25], [4, 4, 4, 4, 4]], "effectBurn": [null, "25/30/35/40/45", "2", "15", "0.5", "0", "1", "1.5", "2", "1.25", "4"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "GalioW.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GalioE", "name": "正義の鉄拳", "description": "少し下がってから前方に突進し、最初に当たった敵チャンピオンをノックアップする。", "tooltip": "前方に突進して強烈なパンチをお見舞いし、最初に当たったチャンピオンを{{ knockupduration }}秒間<status>ノックアップ</status>させて、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。突進中に触れた他の敵には<magicDamage>{{ pvedamage }}の魔法ダメージ</magicDamage>を与える。<br /><br />突進は地形に当たると停止する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "GalioE.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "GalioR", "name": "英雄降臨", "description": "味方1体の位置を着地点として指定し、範囲内のすべての味方に魔法ダメージを防ぐシールドを付与する。少ししてから、着地点に向かって落下し、周囲の敵をノックアップする。", "tooltip": "味方チャンピオン1体の位置を着地点として指定し、範囲内の味方チャンピオン全員に{{ temporarywshieldduration }}秒間<spellName>「デュランドの守り」</spellName>の自動効果の<shield>シールド</shield>を付与する。その後、着地点に向かって飛翔する。<br /><br />着地すると、{{ stundurationouter }}秒間敵を<status>ノックアップ</status>させて、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "射程", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [4000, 4750, 5500], "rangeBurn": "4000/4750/5500", "image": {"full": "GalioR.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "巨像の一撃", "description": "数秒毎に、通常攻撃が一定範囲に追加魔法ダメージを与える。", "image": {"full": "Galio_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}