{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Udyr": {"id": "<PERSON><PERSON><PERSON>", "key": "77", "name": "우디르", "title": "정령 주술사", "image": {"full": "Udyr.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "77000", "num": 0, "name": "default", "chromas": false}, {"id": "77001", "num": 1, "name": "검은띠 우디르", "chromas": false}, {"id": "77002", "num": 2, "name": "태고의 우디르", "chromas": false}, {"id": "77003", "num": 3, "name": "정령 수호자 우디르", "chromas": false}, {"id": "77004", "num": 4, "name": "나 우디르 아니다", "chromas": false}, {"id": "77005", "num": 5, "name": "용의 사도 우디르", "chromas": false}, {"id": "77006", "num": 6, "name": "먹그림자 우디르", "chromas": true}], "lore": "현존하는 최강의 정령 주술사 우디르는 프렐요드의 모든 정령과 교감하며 정령이 바라는 바를 마음으로 이해하고 정령의 힘을 불러모아 우디르 특유의 고대 전투 방식을 선보인다. 우디르는 자신의 정신이 다른 이들로 인해 어지럽혀지지 않도록 내면의 균형을 추구하지만 외부의 균형도 함께 추구한다. 프렐요드의 신비로운 힘은 갈등과 역경을 이겨낸 성장이 있어야만 강해질 수 있기 때문이다. 우디르는 평화에 젖어 쇠락하지 않으려면 희생이 필요하다는 사실을 알고 있다.", "blurb": "현존하는 최강의 정령 주술사 우디르는 프렐요드의 모든 정령과 교감하며 정령이 바라는 바를 마음으로 이해하고 정령의 힘을 불러모아 우디르 특유의 고대 전투 방식을 선보인다. 우디르는 자신의 정신이 다른 이들로 인해 어지럽혀지지 않도록 내면의 균형을 추구하지만 외부의 균형도 함께 추구한다. 프렐요드의 신비로운 힘은 갈등과 역경을 이겨낸 성장이 있어야만 강해질 수 있기 때문이다. 우디르는 평화에 젖어 쇠락하지 않으려면 희생이 필요하다는 사실을 알고 있다.", "allytips": ["피해량은 아이템으로 인한 감소 효과가 적용된 후 거북이 태세에 적용되기 때문에 거북이 태세와 방어 아이템을 함께 사용하면 생존력을 크게 높일 수 있습니다.", "우디르는 게임 내 최고의 정글 사냥꾼 중 하나입니다. 이 점을 활용하여 팀원들이 보다 많은 경험치를 얻을 수 있고 전장에 대한 지배력을 높일 수 있습니다."], "enemytips": ["우디르는 원거리 전투에 약하니 가급적 거리를 유지하세요.", "우디르는 각성된 스킬을 사용한 후 한동안 다른 스킬을 각성시킬 수 없습니다."], "tags": ["Fighter", "Tank"], "partype": "마나", "info": {"attack": 8, "defense": 7, "magic": 4, "difficulty": 7}, "stats": {"hp": 664, "hpperlevel": 92, "mp": 271, "mpperlevel": 50, "movespeed": 350, "armor": 31, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 6, "hpregenperlevel": 0.75, "mpregen": 7.5, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3, "attackspeed": 0.65}, "spells": [{"id": "UdyrQ", "name": "야생 발톱", "description": "공격 속도가 증가하고 다음 두 차례 기본 공격으로 추가 물리 피해를 입힙니다. 재사용 시: 공격 속도가 더 많이 증가하고 다음 두 차례 기본 공격으로 번개를 일으켜 대상을 타격합니다.", "tooltip": "<spellActive>발톱 태세:</spellActive> {{ attackspeeddurationbase }}초 동안 <attackSpeed>공격 속도가 {{ attackspeedbase*100 }}%</attackSpeed> 상승하고 기본 공격으로 <OnHit>적중 시</OnHit> %i:OnHit% <physicalDamage>{{ onhitdamage }}의 물리 피해</physicalDamage>를 입힙니다. 이 태세에서 가하는 다음 두 차례 기본 공격으로 <physicalDamage>최대 체력의 {{ maxhponhit1 }}에 해당하는 물리 피해</physicalDamage>를 추가로 입히고 사거리가 {{ attackrange }} 증가합니다.<br /><br /><keywordMajor>각성:</keywordMajor> 추가 <attackSpeed>공격 속도</attackSpeed>가 <attackSpeed>{{ empoweredtotalas }}</attackSpeed>까지 상승하고 최대 체력 비례 피해량이 <physicalDamage>{{ q2totalonhithpdamage }}</physicalDamage>까지 상승합니다. 추가로 다음 두 차례 기본 공격으로 번개를 여섯 번 일으켜 <magicDamage>최대 체력의 {{ empoweredlightningbonusmax }}에 해당하는 마법 피해</magicDamage>를 입힙니다. (고립된 적은 이 피해를 혼자 전부 받지만, 주변에 다른 적이 있으면 번개가 그쪽으로 튑니다.)<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["공격 속도", "최대 체력 비례 피해량 %", "적중 시 피해량"], "effect": ["{{ attackspeedbase*100.000000 }}% -> {{ attackspeedbasenl*100.000000 }}%", "{{ maxhponhitbase*100.000000 }}% -> {{ maxhponhitbasenl*100.000000 }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [20, 20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "UdyrW", "name": "강철 갑옷", "description": "보호막을 얻고 다음 두 차례의 기본 공격을 통해 체력을 회복합니다. 재사용 시: 강화된 보호막을 얻은 후 몇 초에 걸쳐 최대 체력에 비례해 체력을 회복합니다.", "tooltip": "<spellPassive>갑옷 태세:</spellPassive> {{ shieldduration }}초 동안 <shield>{{ totalshield }}의 보호막</shield>을 얻습니다. 다음 두 차례 기본 공격에 생명력 흡수 {{ lifesteal*100 }}% 효과가 부여되고 적중 시 <healing>{{ lifeonhit }}의 체력</healing>을 회복합니다.<br /><br /><keywordMajor>각성:</keywordMajor> {{ shieldduration }}초 동안 <shield>{{ recastshield }}의 보호막</shield>을 얻고 <healing>{{ recastheal }}의 체력</healing>을 회복합니다. 다음 두 차례 기본 공격에 생명력 흡수 {{ lifesteal*200 }}% 효과가 부여되고 적중 시 <healing>{{ lifeonhitawakened }}의 체력</healing>을 회복합니다.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "체력 비례 보호막 %", "생명력 흡수"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ shieldpercenthealth*100.000000 }}% -> {{ shieldpercenthealthnl*100.000000 }}%", "{{ lifesteal*100.000000 }}% -> {{ lifestealnl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "UdyrW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "UdyrE", "name": "불길 쇄도", "description": "이동 속도가 증가합니다. 이 태세를 취한 후 적에게 처음 기본 공격을 가할 때마다 대상을 기절시킵니다. 재사용 시: 몇 초간 이동 속도가 더 많이 증가하고 이동 불가 효과에 면역이 됩니다. ", "tooltip": "<spellActive>쇄도 태세:</spellActive> <speed>이동 속도가 {{ movespeed*100 }}%</speed> 증가했다 {{ movespeedduration }}초에 걸쳐 원래대로 돌아갑니다. 기본 공격 시 대상에게 돌진해 {{ stunduration }} 초간 <status>기절</status>시킵니다. (대상별 재사용 대기시간 {{ icd }}초)<br /><br /><keywordMajor>각성: {{ unstoppableduration }}초간 </keywordMajor><status>이동 불가</status> 및 <status>방해</status> 효과에 면역이 되고 <speed>이동 속도가 {{ movespeedbonus }}</speed> 추가로 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["이동 속도", "유닛당 재사용 대기시간"], "effect": ["{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%", "{{ icd }} -> {{ icdNL }}"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "UdyrE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "UdyrR", "name": "날개 돋친 폭풍", "description": "얼음 폭풍으로 자신을 감싸 주변 적들에게 피해를 입히고 둔화시킵니다. 재사용 시: 적을 쫓아가며 추가 피해를 입히는 폭풍을 풀어놓습니다.", "tooltip": "<spellActive>폭풍 태세:</spellActive> {{ buffduration }}초 동안 얼음 폭풍으로 자신을 감싸 주변 적들에게 매초 <magicDamage>{{ stormdamage }}의 마법 피해</magicDamage>를 입히고 {{ slowpotency*100 }}% <status>둔화</status>시킵니다. 이 태세에서 가하는 다음 두 차례 기본 공격으로 폭풍 안에 있는 적에게 <magicDamage>{{ pulsedamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br /><keywordMajor>각성:</keywordMajor> 우디르가 마지막으로 기본 공격한 적을 따라가며 지속시간에 걸쳐 <magicDamage>최대 체력의 {{ percenthpblast }}에 해당하는 마법 피해</magicDamage>를 추가로 입히고, {{ empoweredslow }}만큼 추가로 <status>둔화</status>시키는 폭풍을 풀어놓습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["초당 피해량", "둔화"], "effect": ["{{ stormbasedamage }} -> {{ stormbasedamageNL }}", "{{ slowpotency*100.000000 }}% -> {{ slowpotencynl*100.000000 }}%"]}, "maxrank": 6, "cooldown": [6, 6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [40, 40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [370, 370, 370, 370, 370, 370], "rangeBurn": "370", "image": {"full": "UdyrR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "가교", "description": "네 가지 기본 스킬로 여러 태세를 오갈 수 있습니다. 재사용 대기 중인 스킬을 재사용하면 재사용 대기 시간을 초기화하고 궁극의 효과를 부여합니다. 또한, 스킬 사용 후 다음 기본 공격 2회의 공격 속도가 증가합니다.", "image": {"full": "Udyr_P.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}