{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Hecarim": {"id": "<PERSON><PERSON><PERSON>", "key": "120", "name": "ヘカリム", "title": "戦場の幻影", "image": {"full": "Hecarim.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "120000", "num": 0, "name": "default", "chromas": false}, {"id": "120001", "num": 1, "name": "血塗られた騎士ヘカリム", "chromas": false}, {"id": "120002", "num": 2, "name": "死神ヘカリム", "chromas": false}, {"id": "120003", "num": 3, "name": "首なし騎士ヘカリム", "chromas": false}, {"id": "120004", "num": 4, "name": "アーケード ヘカリム", "chromas": false}, {"id": "120005", "num": 5, "name": "古の賢樹ヘカリム", "chromas": false}, {"id": "120006", "num": 6, "name": "天地の破壊者ヘカリム", "chromas": false}, {"id": "120007", "num": 7, "name": "ランサーゼロ ヘカリム", "chromas": false}, {"id": "120008", "num": 8, "name": "荒野のヘカリム", "chromas": true}, {"id": "120014", "num": 14, "name": "宇宙の騎兵ヘカリム", "chromas": true}, {"id": "120022", "num": 22, "name": "アルカナ ヘカリム", "chromas": true}, {"id": "120031", "num": 31, "name": "冬の祝福ヘカリム", "chromas": true}, {"id": "120041", "num": 41, "name": "混沌の闇ヘカリム", "chromas": true}], "lore": "生者の魂を永遠に狩るという呪いを受けたヘカリムは人間と獣が融合した亡霊だ。ブレスドアイルが影に飲まれた時、この誇り高き騎士は「破滅」の破壊的エネルギーに、騎士団と騎馬とともに消し去られてしまった。「黒き霧」がルーンテラに現れる時、彼は鎧をまとった蹄で敵を踏み砕き、虐殺に悦びを感じながら破滅的な突撃を指揮している。", "blurb": "生者の魂を永遠に狩るという呪いを受けたヘカリムは人間と獣が融合した亡霊だ。ブレスドアイルが影に飲まれた時、この誇り高き騎士は「破滅」の破壊的エネルギーに、騎士団と騎馬とともに消し去られてしまった。「黒き霧」がルーンテラに現れる時、彼は鎧をまとった蹄で敵を踏み砕き、虐殺に悦びを感じながら破滅的な突撃を指揮している。", "allytips": ["「ソウルドレイン」によって、周囲にいる敵がダメージ (自分以外の味方によるダメージも含む) を受けるたびに、ヘカリムの体力が回復する。乱戦で発動すればヘカリムの生存力が格段にあがるので、効果的に使おう。", "「チャージ」は移動距離に応じてダメージが増加する。「スペクターズ・オンスロート」や「ゴースト」「フラッシュ」などのサモナースペルを使って、ダメージを最大化しよう。"], "enemytips": ["ヘカリムは「ソウルドレイン」によって周囲の敵から体力を吸収するが、耐久力自体はそれほど高くない。 一気に大ダメージを叩き込もう。", "ヘカリムのアルティメットスキルを受けると恐怖で自由に動けなくなる。あまり多くのチームメイトが範囲に同時に入らないようにしよう。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 8, "defense": 6, "magic": 4, "difficulty": 6}, "stats": {"hp": 625, "hpperlevel": 106, "mp": 280, "mpperlevel": 40, "movespeed": 345, "armor": 32, "armorperlevel": 5.45, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.7, "attackspeedperlevel": 2.5, "attackspeed": 0.67}, "spells": [{"id": "HecarimRapidSlash", "name": "ランページ", "description": "周囲の敵を斬りつけて物理ダメージを与える。1体以上の敵にダメージを与えた場合、それ以降に行う「ランページ」のダメージが増加して、クールダウンが短縮される。", "tooltip": "周囲の敵を斬りつけて、<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与える。このスキルが命中すると、クールダウンが{{ rampagecooldownreduction }}秒短縮されて、{{ e6 }}秒間ダメージが{{ rampagebonusdamageperc }}%増加する。この効果は{{ e2 }}回までスタックする。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "マナコスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [28, 26, 24, 22, 20], "costBurn": "28/26/24/22/20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [1, 1, 1, 1, 1], [3, 3, 3, 3, 3], [60, 60, 60, 60, 60], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3", "1", "3", "60", "8", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HecarimRapidSlash.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "HecarimW", "name": "ソウルドレイン", "description": "物理防御と魔法防御を獲得する。また、周囲にいる敵に魔法ダメージを与えて、それらの敵が受けたあらゆるダメージの一定割合を体力として回復する。", "tooltip": "周囲の敵に{{ buffduration }}秒かけて<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。<br /><br /><scaleArmor>物理防御</scaleArmor>と<scaleMR>魔法防御</scaleMR>が<passive>{{ resistamount }}</passive>増加する。また、周囲の敵に与えた<healing>ダメージの{{ leechamount }}%</healing>を体力として回復し、味方が与えた<healing>ダメージからは{{ allytooltipleachvalue }}%</healing>の体力を回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "増加防御力", "体力回復上限", "@AbilityResourceName@コスト"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ resistamount }} -> {{ resistamountNL }}", "{{ minionhealcap }} -> {{ minionhealcapNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "HecarimW.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "HecarimRamp", "name": "チャージ", "description": "移動速度が短時間増加し、ユニットを通り抜けられるようになる。さらに次の通常攻撃に対象をノックバックする効果と、スキル発動後の移動距離に応じた追加物理ダメージが付与される。", "tooltip": "ゴースト化して、<speed>移動速度が{{ minmovespeed*100 }}%</speed>増加する({{ e5 }}秒かけて最大<speed>{{ maxmovespeed*100 }}%</speed>まで増加する)。次の通常攻撃が対象を<status>ノックバック</status>させ、<physicalDamage>{{ mindamage }}</physicalDamage> - <physicalDamage>{{ maxdamage }}の物理ダメージ</physicalDamage>を与える。<status>ノックバック</status>の距離とダメージは、このスキル使用中に移動した距離に応じて増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最小ダメージ", "最大ダメージ", "クールダウン"], "effect": ["{{ minbasedamage }} -> {{ minbasedamageNL }}", "{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [150, 150, 150, 150, 150], [350, 350, 350, 350, 350], [60, 90, 120, 150, 180], [30, 45, 60, 75, 90], [4, 4, 4, 4, 4], [0.65, 0.65, 0.65, 0.65, 0.65], [1200, 1200, 1200, 1200, 1200], [0.25, 0.25, 0.25, 0.25, 0.25], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "150", "350", "60/90/120/150/180", "30/45/60/75/90", "4", "0.65", "1200", "0.25", "2.5", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "HecarimRamp.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "HecarimUlt", "name": "スペクターズ・オンスロート", "description": "亡霊の騎士たちを召喚し、指定地点まで突撃して直線上の敵ユニットに魔法ダメージを与える。ヘカリムは到着と同時に衝撃波を放ち、付近の敵を恐怖に陥れて逃走させる。", "tooltip": "亡霊の騎士たちを召喚して突撃し、<magicDamage>{{ damagedone }}の魔法ダメージ</magicDamage>を与える。突撃の最後に衝撃波を放ち、突撃した距離に応じて{{ feardurationmin }} - {{ feardurationmax }}秒間の<status>フィアー効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [150, 250, 350], [0.75, 0.75, 0.75], [1.5, 1.5, 1.5], [1100, 1100, 1100], [1000, 1000, 1000], [950, 950, 950], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "0.75", "1.5", "1100", "1000", "950", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [50000, 50000, 50000], "rangeBurn": "50000", "image": {"full": "HecarimUlt.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ウォーパス", "description": "増加移動速度の一定割合と同量だけ攻撃力が増加する。", "image": {"full": "Hecarim_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}