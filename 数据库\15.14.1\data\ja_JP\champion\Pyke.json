{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Pyke": {"id": "<PERSON><PERSON>", "key": "555", "name": "パイク", "title": "ブラッドハーバーの殺戮鬼", "image": {"full": "Pyke.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "555000", "num": 0, "name": "default", "chromas": false}, {"id": "555001", "num": 1, "name": "砂の生霊パイク", "chromas": true}, {"id": "555009", "num": 9, "name": "ブラッドムーン パイク", "chromas": true}, {"id": "555016", "num": 16, "name": "PROJECT: <PERSON><PERSON>", "chromas": true}, {"id": "555025", "num": 25, "name": "PsyOps パイク", "chromas": true}, {"id": "555034", "num": 34, "name": "光の番人パイク", "chromas": true}, {"id": "555044", "num": 44, "name": "灰の騎士パイク", "chromas": true}, {"id": "555045", "num": 45, "name": "荘厳の天球パイク", "chromas": true}, {"id": "555053", "num": 53, "name": "ソウルファイター パイク", "chromas": true}, {"id": "555054", "num": 54, "name": "プレステージ ソウルファイター パイク", "chromas": false}, {"id": "555064", "num": 64, "name": "恐怖の夜パイク", "chromas": true}, {"id": "555074", "num": 74, "name": "墨影のパイク", "chromas": true}], "lore": "ビルジウォーターのスロータードックでは名の知れたモリ撃ちだったパイクは、巨大なジョールフィッシュの胃の中で死を迎えるはずであったが、その息を吹き返した。彼は故郷の町の湿っぽい路地や裏通りを音もなく歩き、他者から搾取することで財を成す人々を追い詰めては、新たに身に着けた超自然的な能力で、彼らに速やかにして非情な死を与える。怪物を狩ることを誇りとしていた都市が、今では怪物に狩られているのだ。", "blurb": "ビルジウォーターのスロータードックでは名の知れたモリ撃ちだったパイクは、巨大なジョールフィッシュの胃の中で死を迎えるはずであったが、その息を吹き返した。彼は故郷の町の湿っぽい路地や裏通りを音もなく歩き、他者から搾取することで財を成す人々を追い詰めては、新たに身に着けた超自然的な能力で、彼らに速やかにして非情な死を与える。怪物を狩ることを誇りとしていた都市が、今では怪物に狩られているのだ。", "allytips": ["パイクは非常に耐久力が低いので、戦闘から逃げることをためらってはいけない。敵の視界外にいれば「沈みし者の力」でかなりの体力を回復できる。", "長押しした「ボーンスキューア」で攻撃すると常に一定距離だけ敵を引き寄せる。接近して使用すると敵を自分の背後に放り投げる。", "短く押した「ボーンスキューア」は素早く攻撃できて追加ダメージを与える。", "パイクの攻撃スキルのほとんどは敵から逃げるためにも使える。常に戦闘から離脱する手段を用意しておこう。"], "enemytips": ["パイクは敵チャンピオンから受けたダメージのほとんどを回復できるが、それは彼が視界から消えているときだけだ！", "パイクが「ゴーストウォーター」で近くに隠れているときは、自分の周囲をサメが泳いでいるようなものだ。", "体力が低下した味方の近くにいてはいけない。パイクが「水底の急襲」でその味方にとどめを刺せば、次に狙われるのは自分かもしれない。"], "tags": ["Support", "Assassin"], "partype": "マナ", "info": {"attack": 9, "defense": 3, "magic": 1, "difficulty": 7}, "stats": {"hp": 670, "hpperlevel": 110, "mp": 415, "mpperlevel": 50, "movespeed": 330, "armor": 43, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 2, "attackspeedperlevel": 2.5, "attackspeed": 0.667}, "spells": [{"id": "PykeQ", "name": "ボーンスキューア", "description": "前方にいる1体の敵を突き刺すか、1体の敵を引き寄せる。", "tooltip": "<tap>短く押す(タップ):</tap> 突きを繰り出し、チャンピオンを優先して最初に当たった敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。その後、対象に{{ slowduration }}秒間、{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。<br /><br /><hold>長く押す(ホールド): </hold>銛を投げ、最初に当たった敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与えて自身の方向に<status>引き寄せる</status>。その後、対象に{{ slowduration }}秒間、{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。<br /><br />このスキルが敵チャンピオンに命中するか、詠唱が途中で中断されると、マナコストの{{ manarefund*100 }}%にあたるマナが回復する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PykeQ.png", "sprite": "spell10.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PykeW", "name": "ゴーストウォーター", "description": "カモフラージュ状態になり、移動速度が大きく増加する。移動速度は徐々に元に戻る。", "tooltip": "<keywordStealth>カモフラージュ</keywordStealth>状態になり、<speed>移動速度が{{ movespeed }}%</speed>増加する。この増加移動速度は{{ e0 }}秒かけて元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [45, 45, 45, 45, 45], [0, 0, 0, 0, 0], [0.3, 0.3, 0.3, 0.3, 0.3], [0.03, 0.03, 0.03, 0.03, 0.03], [80, 80, 80, 80, 80], [0.55, 0.55, 0.55, 0.55, 0.55], [8, 8, 8, 8, 8], [0.01, 0.01, 0.01, 0.01, 0.01], [1.15, 1.15, 1.15, 1.15, 1.15], [5, 5, 5, 5, 5]], "effectBurn": [null, "45", "0", "0.3", "0.03", "80", "0.55", "8", "0.01", "1.15", "5"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "PykeW.png", "sprite": "spell10.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PykeE", "name": "亡者の引き波", "description": "亡霊を残してダッシュする。亡霊はパイクのところまで戻ってきて触れた敵チャンピオンをスタンさせる。", "tooltip": "その場に溺死者の亡霊を残してダッシュする。少ししてから亡霊は自身のところまで追い付き、チャンピオンに{{ stunduration }}秒間の<status>スタン効果</status>と<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [100, 150, 200, 250, 300], [1.25, 1.25, 1.25, 1.25, 1.25], [550, 550, 550, 550, 550], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/150/200/250/300", "1.25", "550", "1", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "PykeE.png", "sprite": "spell10.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PykeR", "name": "水底の急襲", "description": "ブリンクして体力の低下した敵にとどめを刺す。とどめを刺すと再度使用可能になり、アシストした味方1人に追加ゴールドを付与する。", "tooltip": "十字型の範囲内のすべての敵チャンピオンに攻撃して、体力が<scaleAD>{{ rdamage }}</scaleAD>を下回る対象のところまで瞬間移動して<danger>とどめを刺す</danger>。体力がこのしきい値を上回っているチャンピオンおよびチャンピオン以外の敵は、代わりにこの体力しきい値の{{ reduceddamage*100 }}%にあたる物理ダメージ(<physicalDamage>{{ reduceddamagefinal }}</physicalDamage>)を受ける。<br /><br />十字の範囲内で敵チャンピオンが倒されると、{{ rrecastduration }}秒間、このスキルをコスト無しで<recast>再発動</recast>可能になる。そのチャンピオンにとどめを刺したのが自身だった場合、最後にアシストした味方もキルゴールドを獲得する。自分以外がとどめを刺した場合、自身もキルゴールドを獲得できる。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "PykeR.png", "sprite": "spell10.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "沈みし者の力", "description": "敵の視界外にいると、直前にチャンピオンからの攻撃で失った体力を回復する。また、パイクはいずれのソースからも増加最大体力を得ることはできず、代わりに増加攻撃力を得る。", "image": {"full": "PykePassive.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}