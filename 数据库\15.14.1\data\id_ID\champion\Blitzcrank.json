{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "the Great Steam Golem", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "53002", "num": 2, "name": "Goalkeeper <PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "53003", "num": 3, "name": "Boom Boom Blitzcrank", "chromas": false}, {"id": "53004", "num": 4, "name": "Piltover Customs Blitzcrank", "chromas": false}, {"id": "53005", "num": 5, "name": "Definitely Not Blitzcrank", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Riot Blitzcrank", "chromas": false}, {"id": "53011", "num": 11, "name": "Battle Boss Blitzcrank", "chromas": true}, {"id": "53020", "num": 20, "name": "Lancer <PERSON>crank", "chromas": false}, {"id": "53021", "num": 21, "name": "Lancer Paragon Blitzcrank", "chromas": false}, {"id": "53022", "num": 22, "name": "Witch's <PERSON><PERSON>", "chromas": true}, {"id": "53029", "num": 29, "name": "Space Groove Blitz & Crank", "chromas": true}, {"id": "53036", "num": 36, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "53047", "num": 47, "name": "Zenith Games Blitzcrank", "chromas": true}, {"id": "53056", "num": 56, "name": "Bee<PERSON><PERSON>rank", "chromas": true}], "lore": "Blitzcrank adalah robot raksasa dari Zaun yang nyaris tak bisa dihancurkan. Awalnya dia dibuat untuk membuang limbah berbahaya. <PERSON><PERSON><PERSON>, dia merasa tujuan tersebut terlalu sempit sehingga dia memodifikasi dirinya sendiri untuk melayani orang-orang yang lemah di Sump. Blitzcrank menggunakan kekuatan dan durability-nya untuk melindungi orang lain. Dia menggunakan lengan besi yang bisa memanjang atau ledakan energi untuk mengusir para pembuat onar.", "blurb": "Blitzcrank adalah robot raksasa dari <PERSON>aun yang nyaris tak bisa dihancurkan. <PERSON>walnya dia dibuat untuk membuang limbah berbahaya. <PERSON><PERSON><PERSON>, dia merasa tujuan tersebut terlalu sempit sehingga dia memodifikasi dirinya sendiri untuk melayani orang-orang yang...", "allytips": ["Kombo 1-2-3 <PERSON><PERSON>, <PERSON>, dan <PERSON> bisa menghancurkan musuh tunggal.", "Gunakan Grab Blitzcrank untuk menarik musuh ke jang<PERSON>uan turret, lalu lanjutkan dengan Power Fist agar turret bisa memberikan hit beberapa kali."], "enemytips": ["<PERSON><PERSON><PERSON>rank Mana Barrier memberinya Shield saat Health-nya rendah.", "Berada di belakang creep bisa membuatmu menghindari Rocket Grab. Rocket Grab Blitzcrank hanya menarik target musuh pertama yang ada di depannya."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "Rocket Grab", "description": "Blitzcrank menembakkan tangan kanannya untuk menangkap musuh di depannya, la<PERSON> men<PERSON><PERSON><PERSON> damage sambil menariknya ke posisinya.", "tooltip": "Blitzcrank menembakkan tangan kanannya, melakukan <status>Pull</status> pada musuh pertama yang terkena ke arahnya dan menghasilkan <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Overdrive", "name": "Overdrive", "description": "Blitzcrank melakukan super charge untuk mendapatkan peningkatan Move Speed dan Attack Speed secara drastis. <PERSON><PERSON><PERSON> akan terkena efek slow sementara setelah efeknya berakhir.", "tooltip": "Blitzcrank melakukan supercharge, mendapatkan <speed>{{ movespeedmod*100 }}% Move Speed yang berkurang</speed> dan <attackSpeed>{{ attackspeedmod*100 }}% Attack Speed</attackSpeed> selama {{ duration }} detik.<br /><br /><PERSON><PERSON><PERSON><PERSON>, Blitzcrank terkena efek <status>Slow</status> sebesar {{ movespeedmodreduction*100 }}% selama {{ slowduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Move Speed", "Attack Speed"], "effect": ["{{ movespeedmod*100.000000 }}%-> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}%-> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PowerFist", "name": "Power Fist", "description": "Blitzcrank melakukan charge ke tinjunya agar serangan berikut<PERSON> mengh<PERSON><PERSON>an damage ganda dan mementalkan target ke udara.", "tooltip": "Blitzcrank melakukan charge ke tin<PERSON>ya, menyebabkan Serangan berikutnya melakukan <status>Knock Up</status> selama {{ ccduration }} detik dan men<PERSON> <physicalDamage>{{ totaldamage }} physical damage</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "StaticField", "name": "Static Field", "description": "<PERSON><PERSON><PERSON> yang diserang oleh Blitzcrank akan ditandai dan menerima lightning damage setelah 1 detik. <PERSON><PERSON> itu, Blitzcrank bisa mengaktifkan ability ini untuk menghilangkan shield musuh di sekitar, memberikan damage dan silence singkat pada mereka.", "tooltip": "<spellPassive>Pasif: </spellPassive>Selagi ability ini siap dipakai, lightning men-charge pukulan Blitzcrank, menandai musuh yang diserang. Set<PERSON>h 1 detik, mereka terkena shock sebesar <magicDamage>{{ passivedamage }} magic damage</magicDamage>.<br /><br /><spellActive>Aktif: </spellActive>Blitzcrank overcharge, men<PERSON><PERSON><PERSON>an <magicDamage>{{ activedamage }} magic damage</magicDamage> dan <status>Silence</status> musuh di sekitar selama {{ silenceduration }} detik. Shield mereka juga hancur.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dam<PERSON> <PERSON>", "Rasio <PERSON> Pasif", "Damage Dasar Aktif", "Cooldown Aktif"], "effect": ["{{ passivebasedamage }}-> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}%-> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }}-> {{ activebasedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Blitzcrank mendapat shield se<PERSON><PERSON> mananya saat health-nya rendah.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}