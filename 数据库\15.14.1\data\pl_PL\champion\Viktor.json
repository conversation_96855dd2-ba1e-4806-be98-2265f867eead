{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viktor": {"id": "<PERSON>", "key": "112", "name": "<PERSON>", "title": "<PERSON><PERSON>", "image": {"full": "Viktor.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "112000", "num": 0, "name": "default", "chromas": false}, {"id": "112001", "num": 1, "name": "W Pełni Mechaniczny Viktor", "chromas": false}, {"id": "112002", "num": 2, "name": "<PERSON>", "chromas": false}, {"id": "112003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "112004", "num": 4, "name": "<PERSON>", "chromas": false}, {"id": "112005", "num": 5, "name": "<PERSON>peracji", "chromas": true}, {"id": "112014", "num": 14, "name": "Viktor w Samo Południe", "chromas": true}, {"id": "112024", "num": 24, "name": "Zbawiciel Viktor z <PERSON>", "chromas": false}], "lore": "Po przejściu ewolucji i staniu się w pełni biomechanicznym tworem Viktor uznał wspaniałość Wielkiej Ewolucji i dla swoich wyznawców stał się kimś w rodzaju mesjasza. Poświęcił własne człowieczeństwo, aby dowieść logiki, zgodnie z którą wyzbycie się emocji miałoby wyeliminować cierpienie. Teraz pragnie ukazać osiągnięcia i możliwości hexrdzenia reszcie świata — nawet jeśli ten nie będzie w stanie zrozumieć płynących z nich korzyści. Dla mistrza arkanów przemoc jest jednak niczym więcej jak tylko zmienną niezbędną do zrównoważenia najdoskonalszego z równań.", "blurb": "Po przejściu ewolucji i staniu się w pełni biomechanicznym tworem Viktor uznał wspaniałość Wielkiej Ewolucji i dla swoich wyznawców stał się kimś w rodzaju mesjasza. Poświęcił własne człowieczeństwo, aby dowieść logiki, zgodnie z którą wyzbycie się...", "allytips": ["Hextechowy Promień to potężne zaklęcie nękające i ograniczające możliwości przeciwników. Użyj go w połączeniu z Polem Grawitacyjnym i kontroluj pozycje wrogów.", "<PERSON><PERSON><PERSON><PERSON><PERSON>, aby wy<PERSON>ć odpowiednie ulepszenie w odpowiedniej chwili."], "enemytips": ["<PERSON><PERSON><PERSON><PERSON>, na ile pozwalasz Viktorowi podejść. Jego kontrola nad polem bitwy wzrasta wraz ze zmniejszeniem odległości do przeciwnika.", "Zwróć uwagę na liczbę ulepszeń Viktora, patrz<PERSON><PERSON> na kolor jego kostura (fioletowy, żółty, niebieski, czerwony)."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 600, "hpperlevel": 100, "mp": 405, "mpperlevel": 45, "movespeed": 335, "armor": 23, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 8, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "ViktorQ", "name": "<PERSON><PERSON><PERSON>", "description": "Viktor razi prz<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> magiczne obrażenia, z<PERSON><PERSON><PERSON><PERSON><PERSON> tarczę i wzmacniając swój następny podstawowy atak.<br><br>Ulepszenie: Tarcza Wyssania Mocy jest zwiększona o 60%, a Viktor zyskuje dodatkową prędko<PERSON> ruchu po rzuceniu umiejętności.<br>", "tooltip": "<PERSON> razi wroga, z<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ totalmissiledamage }} pkt. obrażeń magicznych</magicDamage> i zapewniając sobie <shield>{{ shieldlevelscaling }} pkt. tarczy</shield> na {{ buffduration }} sek.<br /><br />Następny atak Viktora wyprowadzony w ciągu 3,5 sek. zadaje dodatkowo <magicDamage>{{ attacktotaldmg }} pkt. obrażeń magicznych</magicDamage>.<br /><br /><keywordMajor>Ulepszenie:</keywordMajor> Zapewnia Viktorowi <shield>{{ totalaugmentedshieldvalue }} pkt. tarczy</shield> i dodatkowo zyskuje on <speed>{{ augmentmovespeedbonus }}% prędkości ruchu</speed> na {{ buffduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Koszt (@AbilityResourceName@)", "Obrażenia (pocisku)", "Obrażenia (ataku)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ aabonusdamage }} -> {{ aabonusdamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [2.5, 2.5, 2.5, 2.5, 2.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0.08, 0.08, 0.08, 0.08, 0.08], [0.18, 0.18, 0.18, 0.18, 0.18], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "2.5", "30", "0", "20/45/70/95/120", "0.08", "0.18", "0.6", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViktorQ.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ViktorW", "name": "Pole Grawitacyjne", "description": "<PERSON> wytwarza silne pole grawitacyjne, które spowalnia wrogów znajdujących się w jego zasięgu. Wrogowie, któ<PERSON>y będą zbyt długo przebywać wewną<PERSON>z pola, z<PERSON><PERSON><PERSON> og<PERSON>.<br><br>Ulepszenie: <PERSON><PERSON><PERSON><PERSON><PERSON> Viktora spowolnią trafionych wrogów.<br>", "tooltip": "<PERSON> umieszcza urządzenie wytwarzające silne pole grawitacyjne przez {{ fieldduration }} sek. Urządzenie <status>spowalnia</status> wrogów znajdujących się wewnątrz pola o {{ slowpotency*-1 }}%. Wrogowie, któ<PERSON>y pozostają w zasięgu przez 1,25 sek., zostaj<PERSON> <status>ogłuszeni</status> na {{ stunduration }} sek.<br /><br /><keywordMajor>Biernie — Ulepszenie:</keywordMajor> Umiejętności Viktora <status>spowalniają</status> o {{ augmentslow }}% na 1 sek.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Spowolnienie prędkości ruchu"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ slowpotency*-1.000000 }}% -> {{ slowpotencynl*-1.000000 }}%"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ViktorW.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ViktorE", "name": "Hextechowy Promień", "description": "Viktor przy pomocy swojej biomechanicznej ręki uwalnia Hextechowy Promień, który zadaje obrażenia wszystkim trafionym wrogom na swojej drodze.<br><br>Ulepszenie: Wybuchy podążają za Hextechowym Promieniem, zadając obrażenia magiczne.<br>", "tooltip": "<PERSON> wystrzeliwuje Hextechowy Promień w wybranym kierunku, zadając <magicDamage>{{ laserdamage }} pkt. obrażeń magicznych</magicDamage> wszystkim trafionym wrogom.<br /><br /><keywordMajor>Ulepszenie:</keywordMajor> Ścieżka Hextechowego Promienia dodatkowo wybucha, zadając <magicDamage>{{ aftershockdamage }} pkt. obrażeń magicznych</magicDamage>.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia (Hextechowy Promień)", "Obrażenia (Wstrząs)", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamagelaser }} -> {{ basedamagelaserNL }}", "{{ basedamageaftershock }} -> {{ basedamageaftershockNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [20, 60, 100, 140, 180], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "20/60/100/140/180", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "ViktorE.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "ViktorR", "name": "<PERSON><PERSON>za Arkanów", "description": "Viktor tworzy na polu bitwy Burzę Arkanów, która zadaje obrażenia magiczne i przerywa ładowanie zaklęć wrogów. Burza cyklicznie zadaje obrażenia magiczne wszystkim pobliskim wrogom i może zostać przekierowana przez Viktora.<br><br>Ulepszenie: Burza Arkanów przemieszcza się o 25% szybciej, a przy każdej śmierci bohatera, kt<PERSON>ry otrzyma od niej obraż<PERSON>, jej rozmiar się zwię<PERSON>, a czas działania wydłuża.<br><br>", "tooltip": "<PERSON> na {{ stormduration }} sek. <PERSON><PERSON><PERSON> w wybranym miejscu Burzę <PERSON>, która natychmiast zadaje <magicDamage>{{ initialburstdamage }} pkt. obrażeń magicznych</magicDamage>, a następnie <magicDamage>{{ subsequentburstdamage }} pkt. obrażeń magicznych</magicDamage> na sekundę wszystkim pobliskim wrogom. Burza automatycznie podąża za bohaterami, którym ostatnio zadała obrażenia.<br /><br /><recast>Ponowne użycie:</recast> Viktor ręcznie steruje burzą.<br /><br /><keywordMajor>Ulepszenie:</keywordMajor> Burza przemieszcza się o {{ augmentboost*100 }}% szybciej. W przypadku śmierci bohatera, który otrzymał obraż<PERSON> od burzy, zwię<PERSON><PERSON> się jej rozmiar, a czas działania wydłuża się o {{ tooltip_durationextension }} sek. (do maks. {{ maxgrowths }} razy).<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Obrażenia (eksplozywne, zadawane na początku)", "Obrażenia (zadawane w odstępach czasu)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ initialburstbasedamage }} -> {{ initialburstbasedamageNL }}", "{{ subsequentburstbasedamage }} -> {{ subsequentburstbasedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "ViktorR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Wielka Ewolucja", "description": "Viktor zyskuje Hextechowe Fragmenty za każdym razem, gdy zabija wroga. Co każdy 100. zdobyty Hextechowy Fragment Viktor na stałe ulepsza umiejętność do użycia. Po ulepszeniu wszystkich umiejętności podstawowych może zebrać kolejne 100 Hextechowych Fragmentów, aby ulepszyć superumiejętność.", "image": {"full": "Viktor_Passive.ViktorVGU.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}