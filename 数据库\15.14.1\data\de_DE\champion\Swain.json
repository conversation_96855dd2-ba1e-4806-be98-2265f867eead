{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Swain": {"id": "Swain", "key": "50", "name": "Swain", "title": "der noxianische Großgeneral", "image": {"full": "Swain.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "50000", "num": 0, "name": "default", "chromas": false}, {"id": "50001", "num": 1, "name": "<PERSON><PERSON>-Swain", "chromas": false}, {"id": "50002", "num": 2, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "50003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "50004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "50011", "num": 11, "name": "<PERSON>xtech-Swain", "chromas": false}, {"id": "50012", "num": 12, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "50021", "num": 21, "name": "Wintergeweihter Swain", "chromas": true}, {"id": "50032", "num": 32, "name": "Auserwählter des Wolfs-Swain", "chromas": true}, {"id": "50033", "num": 33, "name": "Auserwählter des Wolfs-Swain (Prestige)", "chromas": false}], "lore": "Jericho Swain ist der vision<PERSON><PERSON> von Noxus, einer expansionistischen Nation, in der nur Stärke zählt. Obwohl er in den ionischen Kriegen niedergeschlagen und verkrüppelt wurde – sein linker Arm wurde abgetrennt – brachte er mit skrupelloser Entschlossenheit und einer neuen, dämonischen Hand das Reich unter seine Kontrolle. Jetzt erteilt Swain an vorderster Front Befehle und marschiert gegen eine bevorstehende Finsternis, die nur er sehen kann. Mithi<PERSON><PERSON> von schattenhaften Raben erhascht er in den Leichen, die ihn umgeben, Blicke auf sie. In einem Strudel aus Aufopferung und Geheimnissen ist das größte Geheimnis wohl, dass der wahre Feind in einem selbst schlummert.", "blurb": "Jericho Swain ist der vision<PERSON><PERSON> von Noxus, einer expansionistischen Nation, in der nur Stärke zählt. Obwohl er in den ionischen Kriegen niedergeschlagen und verkrüppelt wurde – sein linker Arm wurde abgetrennt – brachte er mit skrupelloser...", "allytips": ["<PERSON><PERSON> du Schwierigkeiten hast, einen G<PERSON>ner mit „Nimmergeh'r“ festzu<PERSON>en, schleudere es auf Gegner, die sich in der Nähe ihrer Vasallen aufhalten, so dass sie von der Explosion überrascht werden.", "In der Lane kannst du den Durchschlagsschaden von „Griff des Todes“ verwenden, um aus sicherer Distanz <PERSON> an G<PERSON>n zu verursachen.", "<PERSON><PERSON> ist nicht leicht, allein mit „Weitblick des Generals“ einen Treffer zu landen. Wenn du die Fähigkeit jedoch bei einem Schlagabtausch anderswo auf der Karte einsetzt, kannst du abgelenkte oder unter Massenkontrolle stehende Gegner leichter treffen.", "„Dämonische Schwingen“ er<PERSON><PERSON><PERSON> es, <PERSON><PERSON> zu töten, allerd<PERSON> kann man ihm leicht entkommen. Wenn die Gegner über erhebliche Beweglichkeit verfügen, solltest du Gegenstände kaufen, die den Gegner verlangsamen, so dass du sie in Reichweite halten kannst."], "enemytips": ["Swains Passiv ist effektiv, wenn du bewegungsunfähig bist. Sei vorsichtig bei Gegnern, die über bewegungseinschränkende Effekte verfügen.", "Beweglichkeit kontert alle Grundfähigkeiten von <PERSON>: „Griff des Todes“ verursacht mehr Schaden, je näher er an dir dran ist, „Weitblick des Generals“ hat eine lange Verzögerung und „Nimmergeh'r“ wird erst auf dem Rückweg richtig gefährlich.", "Ein Gegenstand mit „Klaffende Wunden“ zwingt <PERSON><PERSON> leichter in die Knie, während „Dämonische Schwingen“ aktiv ist."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 595, "hpperlevel": 99, "mp": 400, "mpperlevel": 29, "movespeed": 330, "armor": 25, "armorperlevel": 4.7, "spellblock": 31, "spellblockperlevel": 1.55, "attackrange": 525, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 10, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "Swain<PERSON>", "name": "Griff des Todes", "description": "Swain ent<PERSON><PERSON>t mehrere dunkle Blitze, die Gegner durchdringen. Getroffene Gegner erleiden mehr Schaden für jeden Blitz, der sie trifft.", "tooltip": "<PERSON>wain entfesselt 5&nbsp;dunkle <PERSON>litz<PERSON>, die <magicDamage>{{ initialdamage }}&nbsp;magischen <PERSON>haden</magicDamage> plus <magicDamage>{{ extraboltdamage }}&nbsp;magischen Schaden</magicDamage> für jeden Blitz nach dem ersten verursachen (max. <magicDamage>{{ maxdamage }}&nbsp;magischen <PERSON>haden</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Blitzschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "SwainQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>wain<PERSON>", "name": "Weitblick des Generals", "description": "Swain öffnet ein Dämonenauge, das Schaden verursacht und Gegner verlangsamt. Getroffene Champions werden aufgedeckt und geben Swain außerdem ein Seelenfragment.", "tooltip": "Swain öffnet ein Dämonenauge, das 1,5&nbsp;Sekunden lang einen Zielort aufdeckt. Dann verursacht es <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> {{ slowduration }}&nbsp;Sekunden lang um {{ slow*-100 }}&nbsp;%.<br /><br />Getroffene Champions gewähren Swain ein <span class=\"size18 colorFF3F3F\">Seelenfragment</span> und werden für {{ revealduration }} Sekunden aufgedeckt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Reichweite", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slow*-100.000000 }}&nbsp;% -> {{ slownl*-100.000000 }}&nbsp;%", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 6000, 6500, 7000, 7500], "rangeBurn": "5500/6000/6500/7000/7500", "image": {"full": "SwainW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Swain<PERSON>", "name": "Nimmergeh'r", "description": "Swain schleudert eine Welle dämonischer Macht nach vorn. Sie kehrt dann zu Swain zurück und hält getroffene Gegner fest. Swain hat die Möglichkeit, alle festgehaltenen Champions näher heranzuziehen. Diese Fähigkeit hat eine kürzere Abklingzeit während „Dämonische Schwingen“.", "tooltip": "Swain entfesselt eine Welle dämonischer Kraft, die wieder zurückkehrt, beim ersten getroffenen Gegner explodiert und <magicDamage>{{ secondarydamage }} magischen Schaden</magicDamage> verursacht. Gegner in dem betroffenen Bereich werden für {{ rootduration }} Sekunden <status>festgehalten</status>.<br /><br /><PERSON>n Swain einen Champion <status>festhält</status>, kann er seine Fähigkeit reaktivieren, um alle Champions, die von <spellName>Nimmergeh'r</spellName> <status>festgehalten</status> werden, zu sich zu ziehen. Dabei erhält er ein <span class=\"size18 colorFF3F3F\">Seelenfragment</span> für jeden von ihnen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e2damage }} -> {{ e2damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "SwainE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SwainR", "name": "Dämonische Schwingen", "description": "Swain verwandelt sich in einen Dämon und entzieht nahen gegnerischen Champions, Vasallen und neutralen Monstern Leben. Swain kann „Dämonenfeuer“ einsetzen, um nahe Gegner mit einer Seelenfeuernova zu dezimieren und zu verlangsamen. Die Verwandlung bleibt für unbegrenzte Zeit bestehen, solange Swain gegnerischen Champions Leben entzieht.", "tooltip": "Swain entfesselt einen Dämonen, der <magicDamage>{{ damagecalc }} magischen Schaden</magicDamage> verursacht und pro Sekunde <healing>{{ healingcalc }} Leben</healing> von Gegnern in der Nähe raubt. Seine dämonische Energie schwindet, kann aber unendlich lange wieder aufgeladen werden, indem du Leben von gegnerischen Champions raubst. Die Energie wird voll aufgeladen, wenn du einen gegnerischen Champion tötest.<br /><br />Nach {{ demonflarecastdelay }}&nbsp;Sekunden, und dann alle {{ demonflarecooldowntooltip }} Sekunden, kann Swain <spellName>Dämonenfeuer</spellName> wirken, wenn er verwandelt ist, wodurch er <magicDamage>{{ demonflaredamagetotal }} magischen Schaden</magicDamage> verursacht und Gegner um {{ demonflareslowamount*100 }}&nbsp;% <status>verlangsamt</status>. Dämonenfeuer verschwindet nach {{ demonflareslowduration }} Sekunden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden (Lebensentzug)", "Heilung (Lebensentzug)", "Dämonenfeuer – Schaden"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ healpersecond }} -> {{ healpersecondNL }}", "{{ demonflaredamagebase }} -> {{ demonflaredamagebaseNL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "SwainR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ausgehungerte Schar", "description": "Swains <PERSON><PERSON> <i>Seelenfragmente</i>, die ihn heilen und sein maximales Leben dauerhaft erhöhen.", "image": {"full": "Swain_P.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}