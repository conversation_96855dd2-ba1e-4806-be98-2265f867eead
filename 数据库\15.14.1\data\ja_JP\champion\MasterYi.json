{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MasterYi": {"id": "MasterYi", "key": "11", "name": "マスター・イー", "title": "ウージューの剣客", "image": {"full": "MasterYi.png", "sprite": "champion2.png", "group": "champion", "x": 0, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "11000", "num": 0, "name": "default", "chromas": false}, {"id": "11001", "num": 1, "name": "暗殺者マスター・イー", "chromas": false}, {"id": "11002", "num": 2, "name": "選ばれしマスター・イー", "chromas": false}, {"id": "11003", "num": 3, "name": "アイオニア マスター・イー", "chromas": false}, {"id": "11004", "num": 4, "name": "マスターサムライー", "chromas": false}, {"id": "11005", "num": 5, "name": "ヘッドハンター マスター・イー", "chromas": true}, {"id": "11009", "num": 9, "name": "PROJECT: Yi", "chromas": false}, {"id": "11010", "num": 10, "name": "宇宙の剣客マスター・イー", "chromas": false}, {"id": "11011", "num": 11, "name": "悠久の剣マスター・イー", "chromas": true}, {"id": "11017", "num": 17, "name": "雪だるま・イー", "chromas": true}, {"id": "11024", "num": 24, "name": "ブラッドムーン マスター・イー", "chromas": false}, {"id": "11033", "num": 33, "name": "PsyOps マスター・イー", "chromas": false}, {"id": "11042", "num": 42, "name": "おしゃれなマスター・イー", "chromas": false}, {"id": "11052", "num": 52, "name": "精霊の花祭りマスター・イー", "chromas": false}, {"id": "11053", "num": 53, "name": "プレステージ精霊の花祭りマスター・イー", "chromas": true}, {"id": "11089", "num": 89, "name": "墨影のマスター・イー", "chromas": false}, {"id": "11096", "num": 96, "name": "天なる龍マスター・イー", "chromas": false}, {"id": "11106", "num": 106, "name": "勝利の栄光マスター・イー", "chromas": false}], "lore": "極限まで心身を鍛え上げたマスター・イーは、もはや心技一体の境地へと達している。武力に訴えるのはやむを得ぬ場合のみと己を律しながらも、その優雅で素早い太刀筋には刹那の迷いも見られない。アイオニアに伝わる武術、ウージュースタイルの現存する最後の伝承者の一人として、マスター・イーは洞察の七つのレンズを使い、その生涯をかけて、彼の部族が残した遺産を伝授するのにふさわしい弟子たちを探している。", "blurb": "極限まで心身を鍛え上げたマスター・イーは、もはや心技一体の境地へと達している。武力に訴えるのはやむを得ぬ場合のみと己を律しながらも、その優雅で素早い太刀筋には刹那の迷いも見られない。アイオニアに伝わる武術、ウージュースタイルの現存する最後の伝承者の一人として、マスター・イーは洞察の七つのレンズを使い、その生涯をかけて、彼の部族が残した遺産を伝授するのにふさわしい弟子たちを探している。", "allytips": ["レーンで遠隔攻撃系のチャンピオンと対決するときは「明鏡止水」のレベルを先に上げる選択肢もある。体力をけずられてリコールしなければならなくなるのを、ある程度防ぐことができるだろう。", "ゲーム序盤は「ウージュースタイル」を発動することでミニオンにとどめを刺しやすくなるだろう。", "敵チャンピオンの手前にいるミニオンを狙って「アルファストライク」を発動すれば、発動終了時に奥へ行き過ぎずにダメージを与えることができる。"], "enemytips": ["マスター・イーは「明鏡止水」によって体力を回復できるものの、ゲーム序盤はダメージが低く脅威ではない。早めに出鼻をくじいておこう。", "「明鏡止水」はクールダウンの長いスキルだ。このスキルを使ったあとの彼には自衛スキルがないので、「明鏡止水」を使わせてから攻めることを意識しよう。", "「ハイランダー」発動中のマスター・イーはスロウ効果を受けない。他の行動妨害効果で足止めしよう。"], "tags": ["Fighter", "Assassin"], "partype": "マナ", "info": {"attack": 10, "defense": 4, "magic": 2, "difficulty": 4}, "stats": {"hp": 669, "hpperlevel": 105, "mp": 251, "mpperlevel": 42, "movespeed": 355, "armor": 33, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.65, "mpregen": 7.25, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.8, "attackspeedperlevel": 2.5, "attackspeed": 0.679}, "spells": [{"id": "AlphaStrike", "name": "アルファストライク", "description": "目にもとまらぬ速さで複数の敵を斬り抜け、物理ダメージを与える。この間、マスター・イーは対象指定されない。このスキルの攻撃はクリティカルを発生させる場合もあり、モンスターには追加物理ダメージを与える。通常攻撃をするたびに、このスキルのクールダウンが短縮される。", "tooltip": "対象指定不可状態になってテレポートし、対象の近くの敵を素早く攻撃して、{{ alphastrikebounces }}回以上攻撃が命中したすべての敵に<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。<br /><br />他の対象がいない場合は同じ敵を何度も攻撃して、2回目以降の攻撃では{{ subsequenthitmultiplier*100 }}%のダメージ(<physicalDamage>{{ subesquentdamage }}</physicalDamage>)を与える。1体の対象には合計で最大<physicalDamage>{{ singletotaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "モンスターへの追加ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ bonusmonsterdamage }} -> {{ bonusmonsterdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19.5, 19, 18.5, 18], "cooldownBurn": "20/19.5/19/18.5/18", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "AlphaStrike.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Meditate", "name": "明鏡止水", "description": "精神を統一して体力を回復する。また効果時間中、受けるダメージを軽減する。さらに、詠唱中は毎秒「ダブルストライク」のスタックを獲得して、「ウージュースタイル」と「ハイランダー」の残り効果時間を一時停止する。", "tooltip": "詠唱し、{{ healduration }}秒かけて<healing>{{ totalheal }}の体力</healing>を回復する。この回復量は自身の減少体力に応じて最大{{ maxmissinghealthpercent*100 }}%まで増加する。<br /><br />詠唱中および詠唱後{{ drlinger }}秒間は、受けるダメージが{{ initialdr }}軽減される。最初の{{ initialextradrduration }}秒間が経過すると、ダメージ軽減割合が{{ damagereduction*100 }}%に低下する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "ダメージ軽減"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ damagereduction*100.000000 }}% -> {{ damagereductionnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "マナ(+毎秒最大マナの{{ percentmanacostpersecond*100 }}%)", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "Meditate.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ basemanacost }}マ<PERSON>(+毎秒最大マナの{{ percentmanacostpersecond*100 }}%)"}, {"id": "WujuStyle", "name": "ウージュースタイル", "description": "通常攻撃が追加確定ダメージを与える。", "tooltip": "{{ duration }}秒間、通常攻撃が追加で<trueDamage>{{ totaldamage }}の確定ダメージ</trueDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "WujuStyle.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "Highlander", "name": "ハイランダー", "description": "一時的に移動速度と攻撃速度が増加し、あらゆるスロウ効果を受けなくなる。発動中にチャンピオンに対するキルまたはアシストを達成すると「ハイランダー」の効果時間が延びる。またこのスキルは自動効果を持ち、チャンピオンに対するキルまたはアシストを達成すると、他のスキルのクールダウンが短縮されるようになる。", "tooltip": "<spellPassive>自動効果:</spellPassive> チャンピオンからキルまたはアシストを獲得すると、通常スキルの残りクールダウンが{{ rcooldownrefund*100 }}%短縮される。<br /><br /><spellActive>発動効果:</spellActive> トランス状態になり、{{ rduration }}秒間、<speed>移動速度が{{ rmsbonus }}%</speed>、<attackSpeed>攻撃速度が{{ rasbonus }}%</attackSpeed>増加して、すべての<status>スロウ効果</status>を受けなくなる。チャンピオンからキルまたはアシストを獲得すると、このスキルの効果時間が{{ rkillassistextension }}秒延長される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["攻撃速度", "移動速度"], "effect": ["{{ rasbonus }}% -> {{ rasbonusNL }}%", "{{ rmsbonus }}% -> {{ rmsbonusNL }}%"]}, "maxrank": 3, "cooldown": [85, 85, 85], "cooldownBurn": "85", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "Highlander.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ダブルストライク", "description": "通常攻撃を数回行うごとに、通常攻撃が2回連続攻撃になる。", "image": {"full": "MasterYi_Passive1.png", "sprite": "passive2.png", "group": "passive", "x": 0, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}