{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nilah": {"id": "<PERSON><PERSON>", "key": "895", "name": "ニーラ", "title": "放たれし喜び", "image": {"full": "Nilah.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "895000", "num": 0, "name": "default", "chromas": false}, {"id": "895001", "num": 1, "name": "スターガーディアン ニーラ", "chromas": true}, {"id": "895011", "num": 11, "name": "盟約の魔女ニーラ", "chromas": true}, {"id": "895021", "num": 21, "name": "墨影のニーラ", "chromas": true}], "lore": "ニーラは遠く離れた土地で苦行を続けていた戦士で、世界で最も恐ろしく、最も巨大な敵を見つけ、それに挑戦して倒すことを求めている。彼女は長く閉じ込められていた歓喜の悪魔と出会ったことで力を手に入れたため、常に絶え間ない歓喜の感情に満たされているが、彼女が手に入れた強大な力と比べれば大した代償ではない。ニーラは流体である悪魔を比類なき力を持つ刃に変え、遠い昔に忘れられていた古代の脅威に堂々と立ち向かう。", "blurb": "ニーラは遠く離れた土地で苦行を続けていた戦士で、世界で最も恐ろしく、最も巨大な敵を見つけ、それに挑戦して倒すことを求めている。彼女は長く閉じ込められていた歓喜の悪魔と出会ったことで力を手に入れたため、常に絶え間ない歓喜の感情に満たされているが、彼女が手に入れた強大な力と比べれば大した代償ではない。ニーラは流体である悪魔を比類なき力を持つ刃に変え、遠い昔に忘れられていた古代の脅威に堂々と立ち向かう。", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "マナ", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 10}, "stats": {"hp": 570, "hpperlevel": 101, "mp": 350, "mpperlevel": 35, "movespeed": 340, "armor": 27, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 225, "hpregen": 6, "hpregenperlevel": 0.9, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 2, "attackspeedperlevel": 3, "attackspeed": 0.697}, "spells": [{"id": "NilahQ", "name": "形なき刃", "description": "鞭の刃を指定した方向に打ちつけ、直線上にいるすべての敵にダメージを与える。この攻撃が命中すると、少しの間だけ射程距離が増加する。", "tooltip": "<spellPassive>自動効果:</spellPassive> 物理防御貫通が{{ critarmorpen }}増加し、チャンピオンに対する通常攻撃で<healing>与ダメージの{{ critlifesteal }}にあたる体力</healing>を回復する。この効果で受けた余剰の体力回復量は{{ shieldduration }}秒間<shield>シールド</shield>に変換される。<br /><br /><spellActive>発動効果:</spellActive> 鞭の刃を叩きつけ、<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与える。このダメージはクリティカル率に応じて増加する。敵のユニットまたは建造物に命中すると{{ buffduration }}秒間、射程距離が125、<attackSpeed>攻撃速度が{{ bonusattackspeedcalc }}%</attackSpeed>増加し、通常攻撃が扇状の範囲に<physicalDamage>{{ attacktotaldamagetooltip }}の物理ダメージ</physicalDamage>を与えるようになる。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "攻撃力の割合"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ qadratio*100.000000 }}% -> {{ qadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NilahQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NilahW", "name": "歓喜のヴェール", "description": "霧に身を包み、移動速度が増加して、あらゆる通常攻撃を軽やかに回避する。また、霧の効果時間中に接触したすべての味方が、この効果を獲得する。", "tooltip": "{{ baseduration }}秒間、霧に身を包んでゴースト化する。この間は、<speed>移動速度が{{ movespeedpercent*100 }}%</speed>増加して、通常攻撃を回避し、受ける<magicDamage>魔法ダメージ</magicDamage>を{{ magicdamagereduction*100 }}%軽減する。<br /><br />発動中は、自身に触れた味方チャンピオンも霧に包まれ、{{ sharebaseduration }}秒間、同じ効果を獲得する。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "移動速度", "@AbilityResourceName@コスト"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ movespeedpercent*100.000000 }}% -> {{ movespeedpercentnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [26, 25, 24, 23, 22], "cooldownBurn": "26/25/24/23/22", "cost": [60, 45, 30, 15, 0], "costBurn": "60/45/30/15/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [150, 150, 150, 150, 150], "rangeBurn": "150", "image": {"full": "NilahW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NilahE", "name": "流撃", "description": "対象に向かって勢いよくダッシュし、その際に接触したすべての敵にダメージを与える。", "tooltip": "ユニットを通り過ぎるようにダッシュし、接触したすべての敵に<physicalDamage>{{ dashdamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "リチャージ時間"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "NilahE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NilahR", "name": "アポテオシス", "description": "あふれ出る喜びの中で鞭の刃を振り回し、周囲の敵にダメージを与えてから、自身の方向に引き寄せる。", "tooltip": "鞭の刃を振り回し、1秒かけて<physicalDamage>{{ damagepertickcalctooltip }}の物理ダメージ</physicalDamage>を与える。そして最後に衝撃波で<physicalDamage>{{ damagecalc }}の物理ダメージ</physicalDamage>を与えて、周囲の敵を自身の方向に<status>引き寄せる</status>。<br /><br />自身および周囲の味方は、<healing>敵チャンピオンの体力に与えたダメージの{{ champhealingpercent }}(+「形なき刃」により{{ spell.nilahq:critlifesteal }})にあたる体力(チャンピオン以外の場合は{{ otherhealingpercent*100 }}%にあたる体力)</healing>を回復し、余剰の体力回復量は{{ duration }}秒間<shield>シールド</shield>に変換される。<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ", "ヒットごとのダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ damagepertick }} -> {{ damagepertickNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NilahR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "終わりなき喜び", "description": "ラストヒットしたミニオンから得る経験値が増加する。また、周囲の味方の体力回復およびシールド効果を強化し、その味方と共有する。", "image": {"full": "NIlahP.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}