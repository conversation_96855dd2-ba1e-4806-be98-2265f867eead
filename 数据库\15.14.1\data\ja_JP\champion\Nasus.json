{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nasus": {"id": "<PERSON><PERSON>", "key": "75", "name": "ナサス", "title": "砂漠の司書", "image": {"full": "Nasus.png", "sprite": "champion2.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "75000", "num": 0, "name": "default", "chromas": false}, {"id": "75001", "num": 1, "name": "銀河ナサス", "chromas": false}, {"id": "75002", "num": 2, "name": "ファラオ ナサス", "chromas": false}, {"id": "75003", "num": 3, "name": "恐怖の騎士ナサス", "chromas": true}, {"id": "75004", "num": 4, "name": "Riot K-9 ナサス", "chromas": false}, {"id": "75005", "num": 5, "name": "地獄の業火ナサス", "chromas": false}, {"id": "75006", "num": 6, "name": "ナサス皇子", "chromas": false}, {"id": "75010", "num": 10, "name": "天地の破壊者ナサス", "chromas": false}, {"id": "75011", "num": 11, "name": "月の守護者ナサス", "chromas": true}, {"id": "75016", "num": 16, "name": "バトルキャスト ナサス", "chromas": true}, {"id": "75025", "num": 25, "name": "スペースグルーヴ ナサス", "chromas": true}, {"id": "75035", "num": 35, "name": "装甲巨兵ナサス", "chromas": true}, {"id": "75045", "num": 45, "name": "混沌の闇ナサス", "chromas": true}, {"id": "75054", "num": 54, "name": "運命の創造者ナサス", "chromas": true}], "lore": "ジャッカルの頭を持つ超越者ナサスは、古代シュリーマで生を受けた。威風堂々たる体躯を誇る彼を、砂漠の民は半神半人と崇めていた。頭脳明晰で学問を尊び、比類なき戦略家でもあったナサスは、その豊富な知識で古代シュリーマ帝国を何百年も続く栄華の時代へと導いた。やがて帝国が没落すると、ナサスは自ら故郷を離れ、彼の名は伝説と化した。だが今、再び古代都市シュリーマが蘇り、ナサスは故郷へと戻ってきた。二度とこの街を崩壊させはしないと、心に誓って。", "blurb": "ジャッカルの頭を持つ超越者ナサスは、古代シュリーマで生を受けた。威風堂々たる体躯を誇る彼を、砂漠の民は半神半人と崇めていた。頭脳明晰で学問を尊び、比類なき戦略家でもあったナサスは、その豊富な知識で古代シュリーマ帝国を何百年も続く栄華の時代へと導いた。やがて帝国が没落すると、ナサスは自ら故郷を離れ、彼の名は伝説と化した。だが今、再び古代都市シュリーマが蘇り、ナサスは故郷へと戻ってきた。二度とこの街を崩壊させはしないと、心に誓って。", "allytips": ["「サイフォンストライク」でミニオンにとどめを刺すとスタックが増え、ゲーム後半で大きなダメージを与えることができるようになる。", "「スピリットファイア」はミニオンにまとめてダメージを与え、効率よく倒すのに役立つ。ただしこのスキルを使うと、範囲内のミニオンすべてにダメージを与えてしまうため、戦線を押しすぎないように注意したい。基本的には「サイフォンストライク」でとどめを刺し、リコールしたいときには「スピリットファイア」でまとめてミニオンを倒すなど、ちょうどいいバランスを見つけよう。", "あまり防御力が低いと、アルティメットスキル発動中でも簡単にやられてしまう。「サイフォンストライク」はスタックを溜めさえすれば攻撃力が上がるので、防御面の強化に重点を置こう。"], "enemytips": ["アルティメットスキルで変身したナサスはリーグ屈指の強さだ。距離をとって変身がとけるのを待つか、味方と強力して一気に倒そう。", "最大レベルまで強化された「ウィザー」は攻撃速度を大きく減少させるため、通常攻撃で戦うタイプのチャンピオンにとって非常にやっかいだ。迂闊にこのスキルの射程距離に入らないようにしよう。", "ナサスとは一定の距離を保ち、遠距離攻撃で地道に削っていく戦い方が有効だ。ナサスの体力が満タンのときに、足を止めて戦わないように気を付けよう。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 7, "defense": 5, "magic": 6, "difficulty": 6}, "stats": {"hp": 631, "hpperlevel": 104, "mp": 326, "mpperlevel": 62, "movespeed": 350, "armor": 34, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.45, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 67, "attackdamageperlevel": 4, "attackspeedperlevel": 3.48, "attackspeed": 0.638}, "spells": [{"id": "NasusQ", "name": "サイフォンストライク", "description": "杖に魔法の力を宿らせ、次の通常攻撃を強化する。このスキルでユニットにとどめを刺すとスタックが増えていき、その数に応じて「サイフォンストライク」の威力が増していく。", "tooltip": "次の通常攻撃が<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。この通常攻撃で敵を倒すたびに、そのダメージが恒久的に{{ basicstacks }}増加する。対象がチャンピオン、大型ミニオン、大型ジャングルモンスターだった場合は{{ bigstacks }}増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["基本ダメージ", "クールダウン"], "effect": ["{{ bonusdamage }} -> {{ bonusdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7.5, 6.5, 5.5, 4.5, 3.5], "cooldownBurn": "7.5/6.5/5.5/4.5/3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [255, 255, 255, 255, 255], "rangeBurn": "255", "image": {"full": "NasusQ.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NasusW", "name": "ウィザー", "description": "指定した敵チャンピオンを老化させる。効果時間中、敵の移動速度と攻撃速度が徐々に減少する。", "tooltip": "チャンピオンを老化させて{{ slowbase }}%の<status>スロウ効果</status>を与える。このスロウ効果は{{ duration }}秒かけて{{ maxslowtooltiponly }}%まで増加する。また、対象の<attackSpeed>攻撃速度</attackSpeed>もこの<status>スロウ効果</status>の{{ attackspeedslowmult*100 }}%分だけ低下する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最大スロウ効果", "クールダウン"], "effect": ["{{ maxslowtooltiponly }}% -> {{ maxslowtooltiponlyNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "NasusW.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NasusE", "name": "スピリットファイア", "description": "指定範囲に神秘的な炎を呼び寄せ、範囲内の敵に魔法ダメージを与えて物理防御を低下させる。", "tooltip": "神秘的な炎を発生させて、<magicDamage>{{ initialdamage }}の魔法ダメージ</magicDamage>を与える。範囲内の敵は<scaleArmor>物理防御が{{ e2 }}%</scaleArmor>低下し、{{ e3 }}秒間かけて<magicDamage>{{ totaldotdamage }}の魔法ダメージ</magicDamage>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["初期ダメージ", "毎秒ダメージ", "物理防御低下率", "@AbilityResourceName@コスト"], "effect": ["{{ e4 }} -> {{ e4NL }}", "{{ e1 }} -> {{ e1NL }}", "{{ effect2amount*-100.000000 }}% -> {{ effect2amountnl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [10, 16, 22, 28, 34], [30, 35, 40, 45, 50], [5, 5, 5, 5, 5], [50, 80, 110, 140, 170], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/16/22/28/34", "30/35/40/45/50", "5", "50/80/110/140/170", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NasusE.png", "sprite": "spell9.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "NasusR", "name": "アヌビスの怒り", "description": "巨大化して、強力な砂嵐を身にまとう。砂嵐をまとっている間は、体力と通常攻撃の射程距離が増加する。またその間は、周囲の敵にダメージを与え、「サイフォンストライク」のクールダウンが短くなり、物理防御と魔法防御が増加する。", "tooltip": "15秒間砂嵐を身にまとって巨大化し、<healing>最大体力が{{ bonushealth }}</healing>増加して、<scaleArmor>物理防御</scaleArmor>と<scaleMR>魔法防御</scaleMR>が{{ initialresistgain }}ずつ増加する。<br /><br />砂嵐発生中は、周囲の敵に<magicDamage>対象の最大体力の{{ damagecalc }}にあたる魔法ダメージ</magicDamage>を毎秒与え、<spellName>「サイフォンストライク」</spellName>のクールダウンが{{ qcdr*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["増加体力", "最大体力 %", "増加物理防御と増加魔法防御", "クールダウン"], "effect": ["{{ bonushealth }} -> {{ bonushealthNL }}", "{{ aoedamagepercent*100.000000 }}% -> {{ aoedamagepercentnl*100.000000 }}%", "{{ initialresistgain }} -> {{ initialresistgainNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NasusR.png", "sprite": "spell9.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "ソウルイーター", "description": "敵の魂のエネルギーを吸い取り、自身のライフスティールが増加する。", "image": {"full": "Nasus_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}