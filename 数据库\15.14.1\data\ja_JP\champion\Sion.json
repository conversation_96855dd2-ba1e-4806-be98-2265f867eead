{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sion": {"id": "Sion", "key": "14", "name": "サイオン", "title": "不死身の重戦車", "image": {"full": "Sion.png", "sprite": "champion4.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "14000", "num": 0, "name": "default", "chromas": false}, {"id": "14001", "num": 1, "name": "ヘクステック サイオン", "chromas": false}, {"id": "14002", "num": 2, "name": "バーバリアン サイオン", "chromas": false}, {"id": "14003", "num": 3, "name": "木こりサイオン", "chromas": false}, {"id": "14004", "num": 4, "name": "戦争狂サイオン", "chromas": false}, {"id": "14005", "num": 5, "name": "メカゼロ サイオン", "chromas": true}, {"id": "14014", "num": 14, "name": "天地の破壊者サイオン", "chromas": true}, {"id": "14022", "num": 22, "name": "漆黒の霧氷サイオン", "chromas": true}, {"id": "14030", "num": 30, "name": "荒野のサイオン", "chromas": true}, {"id": "14040", "num": 40, "name": "宇宙の勇士サイオン", "chromas": true}, {"id": "14049", "num": 49, "name": "最後の清算サイオン", "chromas": false}], "lore": "サイオンはデマーシア王を素手で絞殺したことでノクサス中で崇敬されていた過去の英雄だったが、死してなお帝国に奉仕させるために、死の淵から甦らされた。邪魔する者は敵も味方も見境なく虐殺する彼に、もはやかつての人間性は残っていない。腐った体にボルトで粗野な鎧を取り付け、強力な斧を振りかざして敵に向かって無謀な突撃を繰り返しながら、彼はなんとか自分の真の姿を思い出そうとしている。", "blurb": "サイオンはデマーシア王を素手で絞殺したことでノクサス中で崇敬されていた過去の英雄だったが、死してなお帝国に奉仕させるために、死の淵から甦らされた。邪魔する者は敵も味方も見境なく虐殺する彼に、もはやかつての人間性は残っていない。腐った体にボルトで粗野な鎧を取り付け、強力な斧を振りかざして敵に向かって無謀な突撃を繰り返しながら、彼はなんとか自分の真の姿を思い出そうとしている。", "allytips": ["「猪突猛進」中はほとんど方向の制御ができないため、まっすぐ走れるエリアを狙って発動しよう。", "「殺意の雄叫び」で敵の動きを鈍らせてから「破滅の斧」でダメージを与えるというコンボ技は、極めて使い勝手が良い。", "「魂の炉心」のシールドは残りの耐久値が表示されるので、最適なタイミングで爆発させよう。"], "enemytips": ["サイオンの「破滅の斧」は完全にチャージされる前に発動せざるを得ない状態に追い込むことで、威力を軽減できる。", "サイオンを倒した時は、復活する前に迎え撃つ体勢を整えるかさっさと逃げてしまおう。"], "tags": ["Tank", "Fighter"], "partype": "マナ", "info": {"attack": 5, "defense": 9, "magic": 3, "difficulty": 5}, "stats": {"hp": 655, "hpperlevel": 87, "mp": 400, "mpperlevel": 52, "movespeed": 345, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4, "attackspeedperlevel": 1.3, "attackspeed": 0.679}, "spells": [{"id": "SionQ", "name": "破滅の斧", "description": "サイオンが斧を振り上げ、力を溜めてから前方に振り下ろして、範囲内の敵すべてにダメージを与える。十分に力を溜めた状態で振り下ろすと、ダメージに加えて命中した敵がノックアップし、その後スタン状態になる。", "tooltip": "<charge>チャージ開始</charge>: 斧を振り上げ、最大2秒間チャージする。<br /><br /><release>解放</release>: 斧を振り下ろして短時間の<status>スロウ効果</status>を付与し、チャージ時間に応じて<physicalDamage>{{ mindamagetotal }} - {{ maxdamagetotal }}の物理ダメージ</physicalDamage>を与える。1秒以上チャージしていた場合は敵を<status>ノックアップ</status>させ、チャージ時間に応じて{{ basestuntime }} - 2.25秒間の<status>スタン効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最小ダメージ", "最大ダメージ", "攻撃力反映率", "攻撃力反映率", "クールダウン"], "effect": ["{{ lowdamage }} -> {{ lowdamageNL }}", "{{ highdamage }} -> {{ highdamageNL }}", "{{ adratiomin*100.000000 }}% -> {{ adratiominnl*100.000000 }}%", "{{ adratiomax*100.000000 }}% -> {{ adratiomaxnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [45, 45, 45, 45, 45], "costBurn": "45", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [7.5, 7.5, 7.5, 7.5, 7.5], [60, 60, 60, 60, 60], [150, 150, 150, 150, 150], [-0.8, -0.8, -0.8, -0.8, -0.8]], "effectBurn": [null, "0", "0", "0", "0", "2.5", "0", "7.5", "60", "150", "-0.8"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "SionQ.png", "sprite": "spell12.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SionW", "name": "魂の炉心", "description": "サイオンがシールドを張り、時間が経過するか3秒たった後に再発動すると爆発して、周囲の敵に魔法ダメージを与える。また自動効果として、敵ユニットをキルするたびにサイオンの最大体力が増加する。", "tooltip": "<spellPassive>自動効果</spellPassive>: ユニットをキルすると<scaleHealth>最大体力が{{ hpperkill }}</scaleHealth>増加し、チャンピオンからキルまたはアシストを奪うか、大型ミニオンか大型モンスターをキルすると最大体力が{{ hpperchampkill }}増加する。<br /><br /><spellActive>発動効果</spellActive>: 6秒間<shield>耐久値{{ totalshield }}のシールド</shield>を獲得する。{{ e7 }}秒後もシールドが残っていた場合は、<recast>再発動</recast>するとシールドが爆発して<magicDamage>{{ totaldamage }}(+最大体力の{{ e4 }}%)の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["シールド量", "ダメージ", "シールドへの最大体力反映率", "マナコスト", "クールダウン"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ shieldpercenthealthtooltip*100.000000 }}% -> {{ shieldpercenthealthtooltipnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [65, 70, 75, 80, 85], "costBurn": "65/70/75/80/85", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [40, 65, 90, 115, 140], [8, 10, 12, 14, 16], [14, 14, 14, 14, 14], [4, 4, 4, 4, 4], [15, 15, 15, 15, 15], [3, 3, 3, 3, 3], [15, 15, 15, 15, 15], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "40/65/90/115/140", "8/10/12/14/16", "14", "4", "15", "3", "15", "6", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "SionW.png", "sprite": "spell12.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SionE", "name": "殺意の雄叫び", "description": "サイオンが短射程の衝撃波を発射し、最初に命中した敵にダメージとスロウ効果を与え、さらに物理防御を低下させる。ミニオンおよび中立モンスターに当たった場合は長い距離をノックバックし、接触した敵すべてにダメージとスロウ効果を与え、さらに物理防御を低下させる。", "tooltip": "衝撃波を放って<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、{{ slowduration }}秒間{{ slowamount }}%の<status>スロウ効果</status>を付与し、{{ armorshredduration }}秒間<scaleArmor>物理防御を{{ armorshred }}%</scaleArmor>低下させる。チャンピオン以外に当たると<status>ノックバック</status>させる。<status>ノックバック</status>させたユニットが当たった敵にも、同じダメージと効果を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ効果", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [65, 100, 135, 170, 205], [40, 45, 50, 55, 60], [5, 5, 5, 5, 5], [40, 45, 50, 55, 60], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [4, 4, 4, 4, 4], [2.5, 2.5, 2.5, 2.5, 2.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/100/135/170/205", "40/45/50/55/60", "5", "40/45/50/55/60", "20", "30", "4", "2.5", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "SionE.png", "sprite": "spell12.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "SionR", "name": "猪突猛進", "description": "サイオンが指定方向に突進し、時間とともに加速してゆく。突進中も、わずかに方向を制御できる。敵チャンピオンか壁に衝突すると停止し、敵チャンピオンの場合は突進距離に応じてダメージを与え、さらにノックアップする。", "tooltip": "指定方向に8秒間アンストッパブル状態で突進し、マウスカーソルで方向を操作できる。敵チャンピオンか壁に衝突する、または<recast>再発動</recast>すると停止する。<br /><br />停止すると、移動距離に応じて<physicalDamage>{{ mindamagetotal }} - {{ maxdamagetotal }}の物理ダメージ</physicalDamage>を与える。近くにいる敵は、移動距離に応じて{{ minstunduration }} - {{ maxstunduration }}秒間<status>スタン</status>し、範囲内の敵は3秒間{{ slowamount }}%の<status>スロウ効果</status>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最小ダメージ", "最大ダメージ", "スロウ効果", "クールダウン"], "effect": ["{{ mindamage }} -> {{ mindamageNL }}", "{{ maxdamage }} -> {{ maxdamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 100, 60], "cooldownBurn": "140/100/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [7500, 7500, 7500], "rangeBurn": "7500", "image": {"full": "SionR.png", "sprite": "spell12.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "名誉ある死", "description": "サイオンは死亡後、体力が急速に減っていく状態で一時的に復活する。攻撃速度が飛躍的に上昇して通常攻撃で体力を回復するようになり、対象の最大体力に応じた追加ダメージを与える。", "image": {"full": "Sion_Passive1.png", "sprite": "passive4.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}