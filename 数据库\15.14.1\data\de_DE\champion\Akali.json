{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akali": {"id": "Akali", "key": "84", "name": "Akali", "title": "die rebellische Assassine", "image": {"full": "Akali.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "84000", "num": 0, "name": "default", "chromas": false}, {"id": "84001", "num": 1, "name": "Stechende Akali", "chromas": false}, {"id": "84002", "num": 2, "name": "Infernalische Akali", "chromas": false}, {"id": "84003", "num": 3, "name": "All-Star-Akali", "chromas": false}, {"id": "84004", "num": 4, "name": "Krankenschwester-Akali", "chromas": true}, {"id": "84005", "num": 5, "name": "Blutmond-Akali", "chromas": false}, {"id": "84006", "num": 6, "name": "Silberzahn-Akali", "chromas": false}, {"id": "84007", "num": 7, "name": "Kopfjäger-Akali", "chromas": true}, {"id": "84008", "num": 8, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "84009", "num": 9, "name": "K/DA-Akali", "chromas": false}, {"id": "84013", "num": 13, "name": "K/DA-Akali (Prestige)", "chromas": false}, {"id": "84014", "num": 14, "name": "PROJEKT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "84015", "num": 15, "name": "True Damage Akali", "chromas": true}, {"id": "84032", "num": 32, "name": "K/DA ALL OUT-Akali", "chromas": true}, {"id": "84050", "num": 50, "name": "Gangsteralbtraum-Akali", "chromas": false}, {"id": "84060", "num": 60, "name": "K/DA-Akali (Prestige 2022)", "chromas": false}, {"id": "84061", "num": 61, "name": "Sternenwächterin Akali", "chromas": false}, {"id": "84068", "num": 68, "name": "DRX-Akali", "chromas": false}, {"id": "84070", "num": 70, "name": "Hexenzirkel-Akali", "chromas": false}, {"id": "84071", "num": 71, "name": "Hexenzirkel-Akali (Prestige)", "chromas": true}, {"id": "84082", "num": 82, "name": "Elysische Akali", "chromas": false}, {"id": "84092", "num": 92, "name": "Seelenblumen-Akali", "chromas": false}], "lore": "Akali hat dem Kinkou-Orden und dem Titel der Faust der Schatten entsagt. <PERSON><PERSON> schlägt sie alleine zu und ist bereit, die tödliche Waffe zu sein, die ihr Volk braucht. Obwohl sie an allem festhält, was ihr <PERSON>ster Shen ihr beigebracht hat, hat sie gesch<PERSON><PERSON>, <PERSON><PERSON> gegen seine Feinde zu verteidigen und einen nach dem anderen zu töten. Akali mag geräuschlos zuschlagen, aber ihre Botschaft ist laut und deutlich zu vernehmen: Fürchtet den meisterlosen Assassinen.", "blurb": "Akali hat dem Kinkou-Orden und dem Titel der Faust der Schatten entsagt. <PERSON><PERSON> schlägt sie alleine zu und ist bereit, die tödliche Waffe zu sein, die ihr Volk braucht. Obwohl sie an allem festhält, was ihr Meister Shen ihr beigebracht hat, hat sie...", "allytips": ["Akali ist besonders gut darin, weniger robuste Champions zu eliminieren. Lass dein Team den Kampf eröffnen und greife dann die Champions in den hinteren Reihen an.", "„Zwielicht-Schleier“ schützt dich selbst in den gefährlichsten Momenten. Nutze die gewonnene Zeit, um Energie für einen schnellen Schlag aufzusparen."], "enemytips": ["<PERSON>n sie in ihrem „Zwielicht-Schleier“ verborgen ist, kann Akali immer noch von Fähigkeiten mit Flächenschaden getroffen werden. Dadurch wird ihre Position kurzzeitig offenbart.", "<PERSON><PERSON>is „Fünf-Punkt-Angriff“ ist besonders effektiv, wenn man ihn mit voller Energie und auf maximale Reichweite einsetzt. Verwickle sie in einen Kampf, wenn sie wenig Energie hat, um den Schlagabtausch zu deinen Gunsten zu entscheiden.", "Wenn dein Leben niedrig ist und Akalis ultimative Fähigkeit bereit ist, solltest du besser zur Basis zurückkehren."], "tags": ["Assassin"], "partype": "Energie", "info": {"attack": 5, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 119, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 23, "armorperlevel": 4.7, "spellblock": 37, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.625}, "spells": [{"id": "AkaliQ", "name": "Fünf-Punkt-Angriff", "description": "Akali wirft fünf Kunai und verursacht Schaden basierend auf ihrem zusätzlichen Angriffsschaden sowie ihrer Fähigkeitsstärke. Getroffene Gegner werden außerdem verlangsamt.", "tooltip": "Akali wirft ihre Kunai in einem Bogen und verursacht <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage>. Gegner an der Spitze werden {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercentage*100 }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamagenamed }} -> {{ basedamagenamedNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [110, 100, 90, 80, 70], "costBurn": "110/100/90/80/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "AkaliQ.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliW", "name": "Zwielicht-Schleier", "description": "Akali tarnt sich mit einer Rauchwolke und erhält kurzzeitig erhöhtes Lauftempo. Innerhalb des Schleiers ist Akali unsichtbar und kann nicht von gegnerischen Fähigkeiten oder Angriffen anvisiert werden. Wenn sie angreift oder Fähigkeiten einsetzt, wird sie kurz sichtbar.  ", "tooltip": "Akali lässt eine Rauchbombe fallen, wodurch sich eine Rauchwolke ausbreitet, die {{ baseduration }}&nbsp;Sekunden lang anhält und ihr <speed>{{ movementspeed }}&nbsp;% Lauftempo</speed> g<PERSON><PERSON><PERSON><PERSON>, das im Verlauf von {{ movementspeedduration }}&nbsp;Sekunden abfällt.<br /><br />Während der Schleier aktiv ist, erhöht Akali ihre maximale Energie um {{ energyrestore }}. <br /><br />Innerhalb des Rauchs ist Akali <keywordStealth>unsichtbar</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo", "<PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ movementspeed }}&nbsp;% -> {{ movementspeedNL }}&nbsp;%", "{{ baseduration }} -> {{ basedurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [140, 140, 140, 140, 140], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [250, 250, 250, 250, 250], [60, 65, 70, 75, 80], [0.3, 0.35, 0.4, 0.45, 0.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [5, 5.5, 6, 6.5, 7], [0, 0, 0, 0, 0]], "effectBurn": [null, "140", "4", "0", "250", "60/65/70/75/80", "0.3/0.35/0.4/0.45/0.5", "1", "0", "5/5.5/6/6.5/7", "0"], "vars": [], "costType": "&nbsp;<PERSON><PERSON><PERSON> wieder her.", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AkaliW.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "St<PERSON>t {{ <PERSON><PERSON>ore }}&nbsp;Energie wieder her."}, {"id": "AkaliE", "name": "Shuriken-Salto", "description": "Akali macht einen Salto rückwärts und wirft einen Shuriken nach vorne, der magischen Schaden verursacht. Der erste getroffene Gegner oder die erste getroffene Rauchwolke wird markiert. Aktiviere die Fähigkeit erneut, um zum markierten Ziel zu springen und zusätzlichen Schaden zu verursachen.", "tooltip": "Akali macht einen Salto rückwärts und wirft einen Shuriken nach vorne, der <magicDamage>{{ e1damage }}&nbsp;magischen Schaden</magicDamage> verursacht und den ersten getroffenen Gegner oder die erste getroffene Rauchwolke markiert. Akali kann diese Fähigkeit einmal <recast>reaktivieren</recast>, um zum markierten Ziel zu springen und <magicDamage>{{ e2damagecalc }}&nbsp;magischen Schaden</magicDamage> zu verursachen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Grundschaden"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AkaliE.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliR", "name": "Perfekte Ausführung", "description": "Akali rennt in eine Richtung und verursacht an getroffenen Gegnern Schaden. 2. Aktivierung: Akali springt in eine Richtung und exekutiert alle getroffenen Gegner.", "tooltip": "Akali springt über einen gegnerischen Champion und fügt dabei allen Gegnern auf ihrem Weg <magicDamage>{{ cast1damage }}&nbsp;magischen <PERSON>haden</magicDamage> zu. <br /><br />Akali kann diese Fähigkeit nach {{ cooldownbetweencasts }}&nbsp;Sekunden <recast>reaktivieren</recast>. Dadurch führt sie einen zweiten Sprung aus, durchbohrt dabei getroffene Gegner und fügt ihnen abhängig von ihrem fehlenden Leben <magicDamage>{{ cast2damagemin }}</magicDamage> bis <magicDamage>{{ cast2damagemax }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Anfänglicher Schaden", "Mindestschaden", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ r2basedamage }} -> {{ r2basedamageNL }}", "{{ r2basedamage*3.000000 }} -> {{ r2basedamagenl*3.000000 }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [1, 1, 1], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [675, 675, 675], "rangeBurn": "675", "image": {"full": "AkaliR.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Zeichen der Assassine", "description": "Wenn Akali mit einer Fähigkeit Schaden an einem Champion verursacht, entsteht ein Ring aus Energie um ihn herum. Verlässt Akali den Ring, profitiert ihr nächster Angriff von zusätzlicher Reichweite und zusätzlichem Schaden.", "image": {"full": "Akali_P.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}