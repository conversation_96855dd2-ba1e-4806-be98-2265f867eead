{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "Yone", "title": "ผู้ไม่มีวันถูกลืม", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "Spirit Blossom Yone", "chromas": true}, {"id": "777010", "num": 10, "name": "Battle Academia Yone", "chromas": true}, {"id": "777019", "num": 19, "name": "Dawnbringer Yone", "chromas": true}, {"id": "777026", "num": 26, "name": "Ocean Song Yone", "chromas": true}, {"id": "777035", "num": 35, "name": "Inkshadow Yone", "chromas": true}, {"id": "777045", "num": 45, "name": "HEARTSTEEL Yone", "chromas": true}, {"id": "777046", "num": 46, "name": "Prestige HEARTSTEEL Yone", "chromas": false}, {"id": "777055", "num": 55, "name": "High Noon Yone", "chromas": true}, {"id": "777058", "num": 58, "name": "Peacemaker High Noon Yone", "chromas": false}, {"id": "777065", "num": 65, "name": "Masked Justice <PERSON>", "chromas": false}], "lore": "ในยามมีชีวิต เขาคือ Yone พี่ชายต่างมารดาของ Yasuo และศิษย์ผู้เลื่องชื่อของสำนักดาบประจำหมู่บ้าน ทว่าหลังจากที่สิ้นชีพลงด้วยน้ำมือของผู้เป็นน้องชายตนเอง เขากลับพบว่าตนเองถูกไล่ล่าโดยสิ่งชั่วร้ายจากโลกแห่งวิญญาณ และจำต้องลงมือพิฆาตมันด้วยคมดาบของมันเอง ในตอนนี้ เขาถูกสาปให้ต้องสวมหน้ากากปีศาจร้ายตัวนั้นไว้บนใบหน้า Yone จึงออกเดินทางไล่ล่าอสุรกายเหล่านี้อย่างไม่รู้จักเหน็ดเหนื่อย ด้วยมุ่งหมายจะเข้าใจสิ่งที่เขากลายเป็นให้จงได้", "blurb": "ในยามมีชีวิต เขาคือ Yone พี่ชายต่างมารดาของ Yasuo และศิษย์ผู้เลื่องชื่อของสำนักดาบประจำหมู่บ้าน ทว่าหลังจากที่สิ้นชีพลงด้วยน้ำมือของผู้เป็นน้องชายตนเอง เขากลับพบว่าตนเองถูกไล่ล่าโดยสิ่งชั่วร้ายจากโลกแห่งวิญญาณ และจำต้องลงมือพิฆาตมันด้วยคมดาบของมันเอง...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "กระแสพลัง", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "Mortal Steel", "description": "แทงดาบไปด้านหน้า สร้างความเสียหายให้กับศัตรูทุกตัวในเส้นทาง<br><br>เมื่อใช้สกิลโดน จะได้รับสแตคของ Gathering Storm เป็นช่วงเวลาสั้น ๆ เมื่อมี 2 สแตค การใช้ Mortal Steel ครั้งถัดไปจะพุ่ง Yone ให้ออกไปพร้อมกับแรงลม ทำให้ศัตรูถูก<status>กระแทกลอยขึ้น</status>", "tooltip": "Yone แทงดาบไปข้างหน้า สร้าง<physicalDamage>ความเสียหายกายภาพ {{ qdamage }} หน่วย</physicalDamage><br /><br />เมื่อใช้โดน จะได้รับ 1 สแตคเป็นเวลา {{ buffduration }} วินาที เมื่อมี 2 สแตค สกิลนี้จะทำให้ Yone พุ่งไปข้างหน้าพร้อมกับสร้างคลื่นพายุที่จะกระแทกศัตรูให้<status>ลอยขึ้น</status>เป็นเวลา {{ q3knockupduration }} วินาที และสร้าง<physicalDamage>ความเสียหายกายภาพ {{ qdamage }} หน่วย</physicalDamage> {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ความเสียหายพื้นฐาน"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, {"id": "YoneW", "name": "Spirit Cleave", "description": "ฟันกวาดพื้นที่ข้างหน้า สร้างความเสียหายให้กับศัตรูทุกตัวที่โดนในรูปกรวย Yone จะได้รับโล่ป้องกันความเสียหายที่จะเพิ่มขึ้นตามจำนวนแชมเปี้ยนศัตรูที่โดนฟัน<br><br>คูลดาวน์ของ Spirit Cleave จะลดลงตามความเร็วโจมตี", "tooltip": "Yone ฟันกวาดพื้นที่ข้างหน้า สร้างความเสียหายกายภาพ <physicalDamage>{{ basedamage*0.5 }} หน่วย บวกกับ {{ maxhealthdamage*50 }}% </physicalDamage>จากพลังชีวิตสูงสุด และสร้างความเสียหายเวท <magicDamage>{{ basedamage*0.5 }} หน่วย บวกกับ {{ maxhealthdamage*50 }}% </magicDamage>จากพลังชีวิตสูงสุด <br /><br />หาก Yone โจมตีโดน เขาจะได้รับโล่ป้องกันความเสียหาย <shield>{{ wshield }} หน่วย</shield> เป็นเวลา {{ shieldduration }} วินาที <shield>โล่ป้องกัน</shield>จะเพิ่มขึ้นตามจำนวนแชมเปี้ยนศัตรูที่ถูกฟัน{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ความเสียหายพื้นฐาน", "ความเสียหายทั้งหมดจากพลังชีวิตสูงสุด"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, {"id": "YoneE", "name": "Soul Unbound", "description": "วิญญาณของ Yone จะปล่อยร่างจริงทิ้งไว้ข้างหลัง ได้รับความเร็วเคลื่อนที่ เมื่อสกิลนี้สิ้นสุดลง วิญญาณของ Yone จะถูกบังคับให้กลับไปยังร่างจริงและเขาจะสร้างความเสียหายส่วนหนึ่งของความเสียหายที่ทำได้ในร่างวิญญาณซ้ำอีกครั้ง", "tooltip": "Yone เข้าสู่ร่างวิญญาณเป็นเวลา {{ returntimer }} วินาที ทิ้งร่างมนุษย์ของเขาไว้ข้างหลังและได้รับ<speed>ความเร็วเคลื่อนที่เพิ่ม {{ startingms*100 }}%</speed> ถึง <speed>{{ movementspeed*100 }}%</speed><br /><br />เมื่อหมดเวลา วิญญาณของ Yone จะกลับมาที่ร่างกายของเขาและสร้างความเสียหายอีกครั้งเป็น {{ deathmarkpercent*100 }}% ของความเสียหายจากการโจมตีปกติและสกิลที่เขาทำไว้กับแชมเปี้ยน สกิลนี้สามารถ<recast>กดใช้ซ้ำ</recast>ได้ขณะอยู่ในร่างวิญญาณ<br /><br /><recast>กดใช้ซ้ำ: </recast>กลับสู่ร่างมนุษย์ทันที{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ความเสียหายที่เกิดขึ้นอีกครั้ง", "คูลดาวน์"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}, {"id": "YoneR", "name": "Fate Sealed", "description": "Yone เทเลพอร์ตไปข้างหลังแชมเปี้ยนศัตรูตัวสุดท้ายในเส้นทางพร้อมกับฟาดฟันอย่างนักหน่วงจนศัตรูทุกเป้าหมายที่โดนฟันถูกดึงเข้าหาตัวเขา", "tooltip": "Yone โจมตีศัตรูทุกตัวที่อยู่ในเส้นทางสร้างความเสียหายกายภาพ <physicalDamage>{{ tooltipdamage }} หน่วย</physicalDamage> และความเสียหายเวท <magicDamage>{{ tooltipdamage }} หน่วย</magicDamage> เทเลพอร์ตไปอยู่ข้างหลังแชมเปี้ยนตัวสุดท้ายที่โดนโจมตี และกระแทกพวกเขาให้<status>ลอย</status>เข้าหา Yone{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ความเสียหายพื้นฐาน", "คูลดาวน์"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "ไม่เสียอะไรเลย", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "ไม่เสียอะไรเลย"}], "passive": {"name": "Way of the Hunter", "description": "Yone สร้างความเสียหายเวททุก ๆ การโจมตีครั้งที่สอง ที่จะทำให้โอกาสคริติคอลของเขาเพิ่มขึ้นด้วย", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}