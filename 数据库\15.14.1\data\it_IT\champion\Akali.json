{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Akali": {"id": "Akali", "key": "84", "name": "Akali", "title": "l'assassina solitaria", "image": {"full": "Akali.png", "sprite": "champion0.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "84000", "num": 0, "name": "default", "chromas": false}, {"id": "84001", "num": 1, "name": "Akali Arpione", "chromas": false}, {"id": "84002", "num": 2, "name": "Akali Infernale", "chromas": false}, {"id": "84003", "num": 3, "name": "Akali All-star", "chromas": false}, {"id": "84004", "num": 4, "name": "Akali Infermiera", "chromas": true}, {"id": "84005", "num": 5, "name": "<PERSON>kali Luna di Sangue", "chromas": false}, {"id": "84006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "84007", "num": 7, "name": "Akali Cacciatrice di Teste", "chromas": true}, {"id": "84008", "num": 8, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "84009", "num": 9, "name": "Akali K/DA", "chromas": false}, {"id": "84013", "num": 13, "name": "Akali K/DA (edizione prestigio)", "chromas": false}, {"id": "84014", "num": 14, "name": "PROGETTO: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "84015", "num": 15, "name": "Akali True Damage", "chromas": true}, {"id": "84032", "num": 32, "name": "Akali K/DA ALL OUT", "chromas": true}, {"id": "84050", "num": 50, "name": "Akali Incubo nella Città del crimine", "chromas": false}, {"id": "84060", "num": 60, "name": "Akali K/DA (edizione prestigio 2022)", "chromas": false}, {"id": "84061", "num": 61, "name": "Akali Guardiana Stellare", "chromas": false}, {"id": "84068", "num": 68, "name": "Akali DRX", "chromas": false}, {"id": "84070", "num": 70, "name": "Akali della Congrega", "chromas": false}, {"id": "84071", "num": 71, "name": "Akali della Congrega (edizione prestigio)", "chromas": true}, {"id": "84082", "num": 82, "name": "Akali Empirea", "chromas": false}, {"id": "84092", "num": 92, "name": "Akali Fiore spirituale", "chromas": false}], "lore": "Dopo aver abbandonato l'Ordine Kinkou e il suo titolo di pugno dell'ombra, ora Akali combatte da sola, pronta ad essere l'arma letale di cui la gente ha bisogno. Non ha dimenticato nulla di quanto appreso dal suo maestro <PERSON>, ma ha giurato di difendere Ionia dai suoi nemici... eliminandoli uno alla volta. A<PERSON>i colpisce in silenzio, ma il suo messaggio è forte e chiaro: temi l'assassina senza maestro.", "blurb": "Dopo aver abbandonato l'Ordine Kinkou e il suo titolo di pugno dell'ombra, ora Akali combatte da sola, pronta ad essere l'arma letale di cui la gente ha bisogno. Non ha dimenticato nulla di quanto appreso dal suo maestro <PERSON>, ma ha giurato di difendere...", "allytips": ["Akali è molto brava a uccidere i campioni più deboli. Lascia che la tua squadra ingaggi e colpisci i campioni alle spalle.", "Velo di penombra offre la sicurezza anche nelle situazioni più pericolose. Sfrutta il tempo per risparmiare energia e colpire rapidamente in seguito."], "enemytips": ["Akali può comunque essere colpita dalle abilità ad area, mentre è oscurata dentro Velo di penombra. Farlo rivela momentaneamente la sua posizione.", "Attacco a cinque punte di Akali è potente se usato al massimo della gittata e dell'energia. Attaccala quando ha poca energia per ottimizzare le possibilità di vincere scambi.", "Torna alla tua base se hai poca salute e Akali ha la suprema disponibile."], "tags": ["Assassin"], "partype": "Energia", "info": {"attack": 5, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 119, "mp": 200, "mpperlevel": 0, "movespeed": 345, "armor": 23, "armorperlevel": 4.7, "spellblock": 37, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 50, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3.2, "attackspeed": 0.625}, "spells": [{"id": "AkaliQ", "name": "Attacco a cinque punte", "description": "Akali lancia cinque kunai, rallentando e infliggendo danni in base al suo attacco fisico bonus e al suo potere magico.", "tooltip": "Akali lancia i kunai in un arco, infliggendo <magicDamage>{{ damage }} danni magici</magicDamage> e <status>rallentando</status> i nemici sulla punta del {{ slowpercentage*100 }}% per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamagenamed }} -> {{ basedamagenamedNL }}"]}, "maxrank": 5, "cooldown": [1.5, 1.5, 1.5, 1.5, 1.5], "cooldownBurn": "1.5", "cost": [110, 100, 90, 80, 70], "costBurn": "110/100/90/80/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "AkaliQ.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliW", "name": "Velo di penombra", "description": "Akali lascia una copertura di fumo e ottiene momentaneamente velocità di movimento. Dentro il Velo, Akali ottiene Invisibilità e non può essere bersaglio di attacchi e abilità nemici. Attaccare o utilizzare abilità svela brevemente la sua presenza.  ", "tooltip": "Akali lascia una bomba fumogena che genera una cortina di fumo dalla durata di {{ baseduration }} secondi e le conferisce <speed>{{ movementspeed }}% velocità di movimento</speed>, che decresce nell'arco di {{ movementspeedduration }} secondi.<br /><br />Mentre il Velo è attivo, l'energia massima di Akali aumenta di {{ energyrestore }}. <br /><br />Mentre è all'interno del fumo, Akali è <keywordStealth>invisibile</keywordStealth>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ movementspeed }}% -> {{ movementspeedNL }}%", "{{ baseduration }} -> {{ basedurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [140, 140, 140, 140, 140], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [250, 250, 250, 250, 250], [60, 65, 70, 75, 80], [0.3, 0.35, 0.4, 0.45, 0.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [5, 5.5, 6, 6.5, 7], [0, 0, 0, 0, 0]], "effectBurn": [null, "140", "4", "0", "250", "60/65/70/75/80", "0.3/0.35/0.4/0.45/0.5", "1", "0", "5/5.5/6/6.5/7", "0"], "vars": [], "costType": " energia", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "AkaliW.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "R<PERSON><PERSON><PERSON> {{ <PERSON><PERSON>ore }} energia"}, {"id": "AkaliE", "name": "Cap<PERSON><PERSON> shuriken", "description": "Fa una capriola all'indietro e lancia uno shuriken in avanti, infliggendo danni magici. Il primo nemico o nuvola di fumo colpiti vengono marchiati. Rilancia per scattare verso il bersaglio marchiato, infliggendo danni aggiuntivi.", "tooltip": "Akali fa una capriola all'indietro e lancia uno shuriken, che infligge <magicDamage>{{ e1damage }} danni fisici</magicDamage> e marchia il primo nemico o la prima nuvola di fumo che colpisce. Akali può <recast>rilanciare</recast> questa abilità una volta per scattare verso il bersaglio marchiato, infliggendo <magicDamage>{{ e2damagecalc }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AkaliE.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AkaliR", "name": "Esecuzione perfetta", "description": "<PERSON><PERSON>i balza in una direzione, danne<PERSON><PERSON><PERSON> i nemici che colpisce. Rilancio: A<PERSON>i scatta in una direzione, giustiziando tutti i nemici che colpisce.", "tooltip": "Akali supera con un salto il campione nemico bersaglio, infliggendo <magicDamage>{{ cast1damage }} danni magici</magicDamage> a tutti i nemici nella traiettoria. <br /><br />Akali può <recast>rilanciare</recast> quest'abilità dopo {{ cooldownbetweencasts }} secondi per eseguire uno slancio perforante, scattando e infliggendo da <magicDamage>{{ cast2damagemin }}</magicDamage> a <magicDamage>{{ cast2damagemax }} danni magici</magicDamage> in base alla salute mancante.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "<PERSON><PERSON> minimi", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ r2basedamage }} -> {{ r2basedamageNL }}", "{{ r2basedamage*3.000000 }} -> {{ r2basedamagenl*3.000000 }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [1, 1, 1], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [675, 675, 675], "rangeBurn": "675", "image": {"full": "AkaliR.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON><PERSON> dell'assassino", "description": "Infliggere danni magici a un campione crea un anello di energia intorno a lui. <PERSON><PERSON><PERSON> dall'anello potenzia l'attacco successivo di Akali, conferendogli gittata e danni bonus.", "image": {"full": "Akali_P.png", "sprite": "passive0.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}