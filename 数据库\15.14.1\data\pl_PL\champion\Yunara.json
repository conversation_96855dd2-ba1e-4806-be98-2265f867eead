{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yunara": {"id": "<PERSON><PERSON>", "key": "804", "name": "<PERSON><PERSON>", "title": "Niezłomna Wiara", "image": {"full": "Yunara.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "804000", "num": 0, "name": "default", "chromas": false}, {"id": "804001", "num": 1, "name": "Yunara Źródeł Duchowego Rozkwitu", "chromas": true}], "lore": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w swoim odd<PERSON>u <PERSON>, przez wieki była odizolowana od świata i zamknięta w wymiarze duchowym, g<PERSON><PERSON> doskonaliła swoje umiejętności posługiwania się Aion Er'na – legendarnym reliktem Kinkou. <PERSON>mimo wielu poświęceń, kt<PERSON><PERSON><PERSON> musiała dokon<PERSON>ć, <PERSON><PERSON> wciąż nie zapomniała o swojej przysiędze oczyszczenia krainy z niezgody ani o swojej wierze. <PERSON><PERSON><PERSON> świat, który teraz na nią czeka – wraz z wyłaniającym się na nowo cieniem pradawnego zagrożenia – podda próbie każdą najmniejszą część jej determinacji.", "blurb": "<PERSON><PERSON>, niewzruszona w swoim odd<PERSON>u <PERSON>, przez wieki była odizolowana od świata i zamknięta w wymiarze duchowym, g<PERSON><PERSON> doskonaliła swoje umiejętności posługiwania się Aion Er'na – legendarnym reliktem Kinkou. Pomimo wielu poświęceń, których musiała...", "allytips": [], "enemytips": [], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 2, "magic": 0, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 275, "mpperlevel": 45, "movespeed": 325, "armor": 25, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 575, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.65}, "spells": [{"id": "YunaraQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Yunara zyskuje prędkość ataku, dodatkowe obrażenia przy trafieniu i rozprysk ataku na pobliskich wrogów.", "tooltip": "<spellPassive>Biernie:</spellPassive> <PERSON><PERSON> zadaje <magicDamage>{{ calc_passive_damage }} pkt. obrażeń magicznych</magicDamage> <OnHit>%i:OnHit% przy trafieniu</OnHit>, a ataki zapewniają <evolve>{{ resource_nonchampion }} pkt. Wyzwolenia</evolve> (<evolve>{{ resource_champion }} pkt. Wyzwolenia</evolve> w przypadku bohaterów).<br /><br /><spellPassive>Użycie:</spellPassive> Yunara zużywa <evolve>{{ resource_max }} pkt. Wyzwolenia</evolve> i na {{ buff_duration }} sek. zyskuje <attackSpeed>{{ calc_attack_speed }} prędkości ataku</attackSpeed> i zadaje dodatkowe <magicDamage>{{ calc_damage }} pkt. obrażeń magicznych</magicDamage> <OnHit>%i:OnHit% przy trafieniu</OnHit>. W tym czasie jej ataki rozprzestrzeniają się na pobliskich wrogów, zadając im <physicalDamage>{{ calc_damage_spread }} pkt. obrażeń fizycznych</physicalDamage>.<br /><br /><keywordMajor>Transcendentność</keywordMajor>: Ta umiejętność zostaje natychmiast aktywowana na {{ spell.yunarar:buff_duration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia bierne przy trafieniu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "Obrażenia przy trafieniu po użyciu"], "effect": ["{{ damage_passive }} -> {{ damage_passiveNL }}", "{{ attack_speed*100.000000 }}% -> {{ attack_speednl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. many", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraQ.png", "sprite": "spell16.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ basecost }} pkt. many"}, {"id": "YunaraW", "name": "Łuk Osądu | Łuk Zniszczenia", "description": "Yunara wstrzeliwuje wirujący koralik modlitewny, który zadaje obrażenia i spowalnia wrogów. <PERSON><PERSON><PERSON> jest Transcendentna, zamiast tego wystr<PERSON>iwuje laser, który zadaje obrażenia i spowalnia wrogów.", "tooltip": "<PERSON><PERSON> wystrzel<PERSON>wuje koralik modlitewny, kt<PERSON><PERSON> zadaje <magicDamage>{{ calc_damage_initial }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia o {{ calc_slow }} (efekt zanika w ciągu {{ slow_duration }} sek.).</status> Zadaje dodatkowe <magicDamage>{{ calc_damage_per_second }} pkt. obrażeń magicznych</magicDamage> na sekundę.<br /><br /><keywordMajor>Transcendentność — Łuk Zniszczenia</keywordMajor>: <PERSON><PERSON> wystrzeliwuje laser, który zadaje <magicDamage>{{ spell.yunarar:calc_rw_damage }} pkt. obrażeń magicznych</magicDamage> i <status>spowalnia o {{ spell.yunarar:calc_rw_slow_amount }} (efekt zanika w ciągu {{ spell.yunarar:rw_slow_duration }} sek.).</status>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Obrażenia na sekundę"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ damage_per_second }} -> {{ damage_per_secondNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. many", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "YunaraW.png", "sprite": "spell16.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} pkt. many"}, {"id": "YunaraE", "name": "Kroki Kanmejów | Nietykalny Cień", "description": "Yunara zyskuje zanikającą prędkość ruchu i przenikanie. <PERSON><PERSON><PERSON> jest Transcendentna, zamiast tego wykonuje doskok w wybranym kierunku.", "tooltip": "<PERSON>ara zyskuje <speed>{{ calc_move_speed }} jedn. prędkości ruchu</speed>, zwi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>j się do <speed>{{ calc_move_speed_enhanced }} jedn. prędkości ruchu</speed>, gdy porusza się w kierunku wrogiego bohatera, kt<PERSON>ra zanika po {{ buff_duration }} sek.<br /><br /><keywordMajor>Transcendentność — Nietykalny Cień</keywordMajor>: Yunara doskakuje w wybranym kierunku.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "Czas działania"], "effect": ["{{ move_speed*100.000000 }}% -> {{ move_speednl*100.000000 }}%", "{{ buff_duration }} -> {{ buff_durationNL }}"]}, "maxrank": 5, "cooldown": [7.5, 7.5, 7.5, 7.5, 7.5], "cooldownBurn": "7.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. many", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraE.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ f2.0 }} pkt. many"}, {"id": "YunaraR", "name": "Samotranscendencja", "description": "Yunara wchodzi w stan <PERSON>cenden<PERSON>ji, kt<PERSON><PERSON> ulepsza jej podstawowe umiejętności.", "tooltip": "Yunara na {{ buff_duration }} sek. wchodzi w <keywordMajor>stan Transcendencji</keywordMajor>, kt<PERSON><PERSON> w tym czasie ulepsza jej podstawowe umiejętności.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia", "Łuk Zniszczenia | Obrażenia", "Nietykalny Cień | Prędkość doskoku"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ rw_damage_base }} -> {{ rw_damage_baseNL }}", "{{ re_dash_speed }} -> {{ re_dash_speedNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [0, 0, 0], "rangeBurn": "0", "image": {"full": "YunaraR.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Ślubowanie Pierwotnej Krainy", "description": "Trafienia krytyczne Yunary zadają dodatkowe obrażenia magiczne.", "image": {"full": "Yunara_Passive.Yunara.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}