{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Shaco": {"id": "Shaco", "key": "35", "name": "Shaco", "title": "der dunkle Hofnarr", "image": {"full": "Shaco.png", "sprite": "champion3.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "35000", "num": 0, "name": "default", "chromas": false}, {"id": "35001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "35002", "num": 2, "name": "Königlicher Shaco", "chromas": false}, {"id": "35003", "num": 3, "name": "Nussknacko", "chromas": false}, {"id": "35004", "num": 4, "name": "Werkstatt-Shaco", "chromas": false}, {"id": "35005", "num": 5, "name": "Asylum-Shaco", "chromas": false}, {"id": "35006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "35007", "num": 7, "name": "Joker-Shaco", "chromas": false}, {"id": "35008", "num": 8, "name": "Sternenvernichter-Shaco", "chromas": true}, {"id": "35015", "num": 15, "name": "Arkanist <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "35023", "num": 23, "name": "Gangsteralbtraum-Shaco", "chromas": true}, {"id": "35033", "num": 33, "name": "Wintergeweihter Shaco", "chromas": true}, {"id": "35043", "num": 43, "name": "Soul Fighter Shaco", "chromas": true}, {"id": "35044", "num": 44, "name": "Soul Fighter Shaco (Prestige)", "chromas": false}, {"id": "35054", "num": 54, "name": "Schreckensnacht-Shaco", "chromas": true}, {"id": "35064", "num": 64, "name": "Schachtelkater Shaco", "chromas": true}], "lore": "Die verzauberte Marionette Shaco wurde vor langer Zeit als Spielgerät für einen einsamen Prinzen erschaffen, verwandel<PERSON> sich dann aber in ein mordlustiges und chaosstiftendes Wesen. Durch dunkle Magie und den Verlust seines geliebten Schützlings verdorben, gelüstet es der einst freundlichen Puppe nur noch nach armen Seelen, die sie foltern kann. Er bringt mit Spielzeugen und einfachen Tricks den Tod und amüsiert sich herrlich über seine blutigen „Spielchen“. All diejenigen, die ein finster<PERSON> Glucksen in der Nacht vernehmen, hat der dunkle Hofnarr vielleicht bereits als nächste Spielkameraden auserkoren.", "blurb": "Die verzauberte Marionette Shaco wurde vor langer Zeit als Spielgerät für einen einsamen Prinzen erschaffen, verwandelte sich dann aber in ein mordlustiges und chaosstiftendes Wesen. Durch dunkle Magie und den Verlust seines geliebten Schützlings...", "allytips": ["<PERSON><PERSON><PERSON> „Täuschen“ in Kombination mit Terrain, um sicher zu entkommen.", "Versuche Gegenstände zu wählen, die Treffereffekte haben. Diese funktionieren auch bei deinem „Halluzinations“-Abbild.", "Der Schaden von „Heimtücke“ kann durch Effekte erhöht werden, die deinen kritischen Schaden verbessern, wie sie die Klinge der Unendlichkeit bietet."], "enemytips": ["Falls sich Shaco bereits in der frühen Spielphase gut schlägt, kann es sinnvoll sein, getar<PERSON> in der Nähe seiner Dschungellager zu platzieren.", "Falls Shaco „Täuschen“ benutz<PERSON>, um in einen Kampf einzugreifen, kann er es so schnell nicht wieder benutzen, um herauszukommen. Arbeite mit deinem Team zusammen, um ihn schnell niederzuringen."], "tags": ["Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 6, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 99, "mp": 297, "mpperlevel": 40, "movespeed": 345, "armor": 30, "armorperlevel": 4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 3, "attackspeed": 0.694}, "spells": [{"id": "Deceive", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> wird unsichtbar und teleportiert sich an den Zielort.<br><br>Während er unsichtbar ist, ist sein nächster normaler Angriff verstärkt, wodurch er zusätzlichen Schaden verursacht und kritisch trifft, wenn er hinterrücks angreift.", "tooltip": "Shaco teleportiert sich und wird {{ stealthduration }}&nbsp;Sekunden lang <keywordStealth>unsichtbar</keywordStealth>. Die <keywordStealth>Unsichtbarkeit</keywordStealth> wird nicht aufgehoben, wenn <spellName>Springteufel</spellName> oder <spellName>Halluzination</spellName> eingesetzt werden.<br /><br />Während Shaco <keywordStealth>unsichtbar</keywordStealth> ist, verursacht sein nächster Angriff zusätzlich <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage>. Wird der Angriff hinterrücks ausgeführt, trifft er kritisch und verursacht {{ qcritdamagemod }}&nbsp;Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Tarnungsdauer", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ stealthduration }} -> {{ stealthdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Deceive.png", "sprite": "spell12.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JackInTheBox", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> stellt einen versteckten Springteufel auf. Wenn er ausgelöst wird, greift er nahe G<PERSON>ner an und versetzt sie in Furcht.", "tooltip": "<PERSON><PERSON><PERSON> stellt eine Falle, die sich nach {{ e5 }}&nbsp;Sekunden tarnt und {{ trapduration }}&nbsp;Sekunden lang bestehen bleibt. Sie wird aktiviert, wenn sich ein Gegner nähert oder wenn sie aufgedeckt wird. Gegnerische Champions in der Nähe werden {{ fearduration }}&nbsp;Sekunden lang in <status>Furcht</status> versetzt, und Vasallen sowie Dschungelmonster {{ minionfearduration }}&nbsp;Sekunden lang.<br /><br />Nach ihrer Aktivierung feuert die Falle 5&nbsp;Sekunden lang auf alle Gegner in der Nähe und fügt ihnen <magicDamage>{{ aoedamage }}&nbsp;magischen Schaden</magicDamage> zu (bzw. <magicDamage>{{ stdamage }}&nbsp;magischen Schaden</magicDamage> an einem einzelnen Ziel).<br /><br />Die Angriffe von „Springteufel“ verursachen zusätzlich <magicDamage>{{ monsterbonusdamage }}</magicDamage>&nbsp;Schaden an Monstern.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Zusätzlicher Schaden an Monstern", "Kosten (@AbilityResourceName@)"], "effect": ["{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ fearduration }} -> {{ feardurationNL }}", "{{ monsterbonusdamage }} -> {{ monsterbonusdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [70, 65, 60, 55, 50], "costBurn": "70/65/60/55/50", "datavalues": {}, "effect": [null, [35, 50, 65, 80, 95], [200, 300, 400, 500, 600], [0.5, 0.75, 1, 1.25, 1.5], [40, 40, 40, 40, 40], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/50/65/80/95", "200/300/400/500/600", "0.5/0.75/1/1.25/1.5", "40", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "JackInTheBox.png", "sprite": "spell12.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TwoShivPoison", "name": "Klingengift", "description": "<PERSON>hacos <PERSON>en vergiften Ziele bei jedem Treffer und verringern so deren Lauftempo. Er kann seine Klingen werfen, um Schaden zu verursachen und sein Ziel zu vergiften. Die geworfene Klinge verursacht zusätzlichen Schaden, wenn das Ziel über weniger als 30&nbsp;% Leben verfügt.", "tooltip": "<spellPassive>Passiv:</spellPassive> Wenn die Fähigkeit nicht gerade abklingt, <status>verlangsamen</status> Shacos Angriffe das Ziel {{ slowdurationpassive }}&nbsp;Sekunden lang um {{ slowamount*-100 }}&nbsp;%.<br /><br /><spellActive>Aktiv:</spellActive> Shaco wirft eine Klinge, die dem Ziel <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>haden</magicDamage> zufügt und es {{ slowdurationactive }}&nbsp;Sekunden lang um {{ slowamount*-100 }}&nbsp;% <status>verlangsamt</status>. Liegt das Leben des Ziels unter {{ executehealththreshold*100 }}&nbsp;%, fügt ihm die Klinge stattdessen <magicDamage>{{ totalexecutedamage }}&nbsp;Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}&nbsp;% -> {{ slowamountnl*-100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "TwoShivPoison.png", "sprite": "spell12.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HallucinateFull", "name": "Halluzination", "description": "<PERSON><PERSON><PERSON> erze<PERSON>t in seiner Nähe eine Illusion von sich selbst, die nahe Gegner angreifen kann (verringerter Schaden an Türmen). <PERSON><PERSON> sie stirbt, explodiert sie, erzeugt 3 Mini-Springteufel und verursacht an nahen Gegnern Schaden.", "tooltip": "Shaco verschwindet kurzzeitig und erscheint dann mit einem Klon wieder. Dieser Klon bleibt {{ clonelifetime }}&nbsp;Sekunden lang bestehen und explodiert, wenn er stirbt. Dabei verursacht er <magicDamage>{{ explosiontotaldamage }}&nbsp;magischen Schaden</magicDamage> und hinterlässt 3&nbsp;Mini-<spellName>Springteufel</spellName>, die sofort ausgelöst werden. Der Klon verursacht {{ cloneaadamagepercent*100 }}&nbsp;% von Sha<PERSON>haden und erleidet um {{ cloneincomingdamagepercent*100 }}&nbsp;% erhöhten Schaden.<br /><br />Die Mini-<spellName>Springteufel</spellName> verursachen <magicDamage>{{ aoedamage }}&nbsp;magischen Schaden</magicDamage> (bzw. <magicDamage>{{ stdamage }}&nbsp;magischen Schaden</magicDamage> an einem einzelnen Z<PERSON>) und versetzen Gegner {{ boxfearduration }}&nbsp;Sekunden lang in <status>Furcht</status>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON> bei Tod", "Mini-Springteufel: <PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ stbasedamage }} -> {{ stbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [100, 90, 80], "cooldownBurn": "100/90/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "6", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200], "rangeBurn": "200", "image": {"full": "HallucinateFull.png", "sprite": "spell12.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Heimtücke", "description": "Shacos normale Angriffe und „Klingengift“ verursachen zusätzlichen Schaden, wenn er hinterrücks zuschlägt.", "image": {"full": "Jester_CarefulStrikes.png", "sprite": "passive3.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}