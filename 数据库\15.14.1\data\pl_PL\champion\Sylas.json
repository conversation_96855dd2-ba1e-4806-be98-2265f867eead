{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sylas": {"id": "<PERSON><PERSON><PERSON>", "key": "517", "name": "<PERSON><PERSON><PERSON>", "title": "Wyzwolony z Kajdan", "image": {"full": "Sylas.png", "sprite": "champion4.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "517000", "num": 0, "name": "default", "chromas": false}, {"id": "517001", "num": 1, "name": "Księżycowa Zjawa Sylas", "chromas": true}, {"id": "517008", "num": 8, "name": "<PERSON><PERSON><PERSON>jordu", "chromas": true}, {"id": "517013", "num": 13, "name": "PROJEKT: <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517014", "num": 14, "name": "PROJEKT: <PERSON><PERSON><PERSON> (Prestiżowy)", "chromas": false}, {"id": "517024", "num": 24, "name": "Wilk Bojowy <PERSON>", "chromas": true}, {"id": "517034", "num": 34, "name": "Pogromca Popiołów Sylas", "chromas": true}, {"id": "517036", "num": 36, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "517046", "num": 46, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Wychowany w jednym z gorszych regionów Demacii, <PERSON><PERSON><PERSON> z Ochłapiska stał się symbolem mrocznej strony Wielkiego Miasta. Kiedy by<PERSON>ł<PERSON>, jego zdolność do odszukiwania ukrytej magii przykuła uwagę słynnych łowców magów, którzy ostatecznie wtrącili go do więzienia za obrócenie tej mocy przeciwko nim. Wydost<PERSON><PERSON> się na wolność, <PERSON><PERSON>s wiedzie życie zahartowanego rewolucjonisty i używa magii znajdujących się wokół niego osób, by <PERSON><PERSON><PERSON><PERSON><PERSON> kr<PERSON>lest<PERSON>, któremu kiedyś służył... a grono jego wyznawców złożone z wygnanych magów zdaje się rosnąć z dnia na dzień.", "blurb": "Wychowany w jednym z gorszych regionów Demacii, <PERSON><PERSON><PERSON> z Ochłapiska stał się symbolem mrocznej strony Wielkiego Miasta. Kiedy był <PERSON>ł<PERSON>cem, jego zdolno<PERSON> do odszukiwania ukrytej magii przykuła uwagę słynnych łowców magów, którzy ostatecznie wtrącili go...", "allytips": ["<PERSON>anim uży<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, aż ty lub twój wróg osiągniecie niski poziom zdrowia, ż<PERSON>y uzyskać najlepszy efekt.", "Używaj swoich umiejętności w odpowiednich odstępach, ponieważ wtedy Petrucytowa Eksplozja będzie najskuteczniejsza.", "Sprytne wykorzystywanie superumiejętności wrogów może stworzyć nowe możliwości w walkach drużynowych."], "enemytips": ["Pasek zdrowia Sylasa może być zwodniczy, uważaj na jego Królobójcę!", "Postaraj się walczyć z Sylasem, kiedy nie może zabrać twojej superumiejętności."], "tags": ["Mage", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 4, "magic": 8, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 122, "mp": 400, "mpperlevel": 70, "movespeed": 340, "armor": 29, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.55, "attackrange": 175, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 3.5, "attackspeed": 0.645}, "spells": [{"id": "<PERSON><PERSON>s<PERSON>", "name": "Smagnięcie Łańcuchem", "description": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> przecinają się w wybranym mi<PERSON>, z<PERSON>j<PERSON>c obrażenia i spowalniając wrogów. <br><br>Po chwili magiczna energia eksploduje z miejsca przecięcia się łańcuchów i zadaje obrażenia.", "tooltip": "<PERSON><PERSON><PERSON> <PERSON>, z<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ damage }} pkt. obrażeń magicznych</magicDamage> i <status>spowalniając</status> o {{ slowamountcalc }} na {{ slowduration }} sek. <PERSON>, w którym łańcuchy się przecinają, wybucha, zadaj<PERSON>c dodatkowo <magicDamage>{{ explosiondamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Obrażenia od wybuchu", "Spowolnienie", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ explosionbasedamage }} -> {{ explosionbasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [775, 775, 775, 775, 775], "rangeBurn": "775", "image": {"full": "SylasQ.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SylasW", "name": "Królobójca", "description": "<PERSON><PERSON><PERSON> rzu<PERSON> się na wroga z magiczną mocą, <PERSON><PERSON><PERSON><PERSON><PERSON> obrażenia, i leczy się, je<PERSON><PERSON> jego celem jest wrogi bohater.", "tooltip": "<PERSON><PERSON><PERSON> r<PERSON>ca się na wroga z magiczną mocą, z<PERSON><PERSON><PERSON><PERSON> <magicDamage>{{ mindamage }} pkt. obrażeń magicznych</magicDamage>. <PERSON><PERSON> celem jest bohater, <PERSON><PERSON><PERSON> <PERSON><PERSON> sobie <healing>{{ minhealing }}</healing>-<healing>{{ maxhealing }} pkt. zdrowia</healing> zależnie od swojego brakującego zdrowia (maks. wartość leczenia przy {{ maxexecutethreshold*100 }}% zdrowia lub mniej).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Leczenie", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ healing }} -> {{ healingNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasW.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SylasE", "name": "Ucieczka / Uprowadzenie", "description": "<PERSON><PERSON><PERSON> w wybrane miejsce. Bohater może użyć tej umiejętności ponownie, by <PERSON><PERSON><PERSON><PERSON>ńcuchami i przyciągnąć się do trafionego wroga.", "tooltip": "Sylas pos<PERSON><PERSON> doskakuje i ładuje <recast>ponowne użycie</recast> przez 3,5 sek.<br /><br /><recast>Ponowne użycie:</recast> Sylas rzuca łańcuchami i przyciąga się do pierwszego trafionego wroga, zadając mu <magicDamage>{{ damage }} pkt. obrażeń magicznych</magicDamage> i <status>podrzucając</status> go na {{ knockupduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ pulldamage }} -> {{ pulldamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "SylasE.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SylasR", "name": "<PERSON>rz<PERSON><PERSON><PERSON>", "description": "Sylas kradnie wrogowi superumiejętność i może użyć jej sam.", "tooltip": "<PERSON>ylas przejmuje wrogiego bohatera, d<PERSON><PERSON><PERSON> czemu może użyć kopii jego superumiej<PERSON>ci, zależnie od poziomu superumiejętności Sylasa i jego statystyk.<br /><br /><PERSON><PERSON><PERSON><PERSON><PERSON> wroga sprawi, że nie będzie mógł przejąć go ponownie przez {{ pertargetcooldown }}% (modyfikowane przez przyspieszenie umiejętności Sylasa) czasu odnowienia superumiejętności celu (min. {{ minimumenemycooldown }} sek., przez które Sylas nie może przejąć tego samego bohatera).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas odnowienia"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [80, 55, 30], "cooldownBurn": "80/55/30", "cost": [75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [950, 950, 950], "rangeBurn": "950", "image": {"full": "SylasR.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Petrucytowa Eksplozja", "description": "Po rzuceniu zaklęcia Sylas otrzymuje ładunek Petrucytowej Eksplozji. Podstawowe ataki Sylasa zużyją ładunek i zakręcą jego naładowanymi energią łańcuchami wokół niego, zadając trafionym wrogom dodatkowe obrażenia magiczne. Gdy Sylas dysponuje ładunkiem Petrucytowej Eksplozji, ma zwiększoną prędkość ataku. ", "image": {"full": "SylasP.png", "sprite": "passive4.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}