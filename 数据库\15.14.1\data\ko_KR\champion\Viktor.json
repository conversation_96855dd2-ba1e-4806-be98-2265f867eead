{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Viktor": {"id": "<PERSON>", "key": "112", "name": "빅토르", "title": "아케인의 전령관", "image": {"full": "Viktor.png", "sprite": "champion5.png", "group": "champion", "x": 96, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "112000", "num": 0, "name": "default", "chromas": false}, {"id": "112001", "num": 1, "name": "완성형 빅토르", "chromas": false}, {"id": "112002", "num": 2, "name": "프로토타입 빅토르", "chromas": false}, {"id": "112003", "num": 3, "name": "창조자 빅토르", "chromas": false}, {"id": "112004", "num": 4, "name": "죽음의 사도 빅토르", "chromas": false}, {"id": "112005", "num": 5, "name": "초능력특공대 빅토르", "chromas": true}, {"id": "112014", "num": 14, "name": "하이 눈 빅토르", "chromas": true}, {"id": "112024", "num": 24, "name": "아케인 구원자 빅토르", "chromas": false}], "lore": "과거의 모습을 벗어던지고 완전한 생체기계로 변모한 빅토르는 영광스러운 진화를 받아들이고 추종자들의 구세주와 같은 존재가 되었다. 감정을 제거하면 고통도 제거될 것이라는 논리로 자신의 인간성을 희생한 빅토르는 이제 온 세상에 마공학 핵의 진리를 전파하고자 한다. 설령 세상이 그 혜택을 이해하지 못하더라도 상관없다. 결국 이 비전의 대가에게 폭력이란 궁극적인 상황에서 균형을 잡는 데 필요한 변수일 뿐이니까.", "blurb": "과거의 모습을 벗어던지고 완전한 생체기계로 변모한 빅토르는 영광스러운 진화를 받아들이고 추종자들의 구세주와 같은 존재가 되었다. 감정을 제거하면 고통도 제거될 것이라는 논리로 자신의 인간성을 희생한 빅토르는 이제 온 세상에 마공학 핵의 진리를 전파하고자 한다. 설령 세상이 그 혜택을 이해하지 못하더라도 상관없다. 결국 이 비전의 대가에게 폭력이란 궁극적인 상황에서 균형을 잡는 데 필요한 변수일 뿐이니까.", "allytips": ["마법공학 광선은 매우 강력한 견제기입니다. 중력장과 함께 써서 적이 함부로 움직이지 못하게 하세요.", "증강은 적시에 잘 투자해야 합니다."], "enemytips": ["빅토르와 너무 가까이 붙지 마세요. 빅토르는 적과 가까이 붙을수록 공격이 정확해집니다.", "현재 빅토르가 얼마나 많은 증강으로 업그레이드했는지 눈여겨보세요. 지팡이 끝의 색깔을 보고도 알 수 있습니다. (보라, 노랑, 파랑, 빨강)"], "tags": ["Mage"], "partype": "마나", "info": {"attack": 2, "defense": 4, "magic": 10, "difficulty": 9}, "stats": {"hp": 600, "hpperlevel": 100, "mp": 405, "mpperlevel": 45, "movespeed": 335, "armor": 23, "armorperlevel": 4.4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 8, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 53, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "ViktorQ", "name": "힘의 흡수", "description": "빅토르가 적 유닛에 폭발을 일으켜 마법 피해를 입히며 보호막을 얻고 다음번 기본 공격이 강화됩니다.<br><br>증강: 스킬 사용 시 빅토르의 이동 속도가 빨라지고 힘의 흡수 보호막 흡수량이 60% 증가합니다.<br>", "tooltip": "빅토르가 적에게 폭발을 일으켜 <magicDamage>{{ totalmissiledamage }}의 마법 피해</magicDamage>를 입히며 {{ buffduration }}초 동안 <shield>{{ shieldlevelscaling }}의 피해를 흡수하는 보호막</shield>을 얻습니다.<br /><br />3.5초 안에 기본 공격 시 <magicDamage>{{ attacktotaldmg }}의 마법 피해</magicDamage>를 추가로 입힙니다.<br /><br /><keywordMajor>업그레이드 시:</keywordMajor> {{ buffduration }}초 동안 <shield>{{ totalaugmentedshieldvalue }}의 피해를 흡수하는 보호막</shield>을 얻고 <speed>이동 속도가 {{ augmentmovespeedbonus }}%</speed> 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "소모값 @AbilityResourceName@", "피해량 (폭발)", "피해량 (기본 공격)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ aabonusdamage }} -> {{ aabonusdamageNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [60, 75, 90, 105, 120], [2.5, 2.5, 2.5, 2.5, 2.5], [30, 30, 30, 30, 30], [0, 0, 0, 0, 0], [20, 45, 70, 95, 120], [0.08, 0.08, 0.08, 0.08, 0.08], [0.18, 0.18, 0.18, 0.18, 0.18], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/75/90/105/120", "2.5", "30", "0", "20/45/70/95/120", "0.08", "0.18", "0.6", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ViktorQ.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ViktorW", "name": "중력장", "description": "빅토르가 강력한 중력장을 생성해 해당 지역 안에 들어온 적들을 느리게 만듭니다. 중력장에 오래 머무르는 적은 기절합니다.<br><br>증강: 빅토르의 스킬이 적을 둔화시킵니다.<br>", "tooltip": "빅토르가 중력장 감옥 장치를 배치해 {{ fieldduration }}초 동안 장치 내부의 적을 {{ slowpotency*-1 }}% <status>둔화</status>시킵니다. 범위 안에 1.25초 동안 있는 적은 {{ stunduration }}초 동안 <status>기절</status>합니다.<br /><br /><keywordMajor>업그레이드한 기본 지속 효과:</keywordMajor> 빅토르의 스킬이 1초 동안 {{ augmentslow }}% <status>둔화</status>시킵니다.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "이동 둔화"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ slowpotency*-1.000000 }}% -> {{ slowpotencynl*-1.000000 }}%"]}, "maxrank": 5, "cooldown": [17, 16, 15, 14, 13], "cooldownBurn": "17/16/15/14/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "ViktorW.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ViktorE", "name": "마법공학 광선", "description": "빅토르가 생체기계 팔을 사용해 땅을 일직선으로 가르는 마법공학 광선을 쏘아 경로상의 적 모두에게 피해를 입힙니다.<br><br>증강: 마법공학 광선 파동에 이어 폭발이 일어나 마법 피해를 입힙니다.<br>", "tooltip": "빅토르가 선택한 방향으로 마법공학 광선을 발사하여 적중한 적에게 <magicDamage>{{ laserdamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br /><keywordMajor>업그레이드 시:</keywordMajor> 마법공학 광선을 따라 여진이 일어나며 <magicDamage>{{ aftershockdamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량 (마법공학 광선)", "피해량 (여진)", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamagelaser }} -> {{ basedamagelaserNL }}", "{{ basedamageaftershock }} -> {{ basedamageaftershockNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [20, 60, 100, 140, 180], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "20/60/100/140/180", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "ViktorE.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ViktorR", "name": "아케인 폭풍", "description": "빅토르가 목표 위치에 아케인 폭풍을 일으켜 대상 지역에 있는 적에게 마법 피해를 입히며 적의 정신 집중을 끊습니다. 폭풍은 주위 적에게 주기적으로 마법 피해를 주고 빅토르가 진행 방향을 바꿀 수 있습니다.<br><br>증강: 아케인 폭풍이 25% 빠르게 이동하고 챔피언이 폭풍으로부터 피해를 입은 후 죽을 때마다 폭풍의 크기가 커지고 지속시간이 증가합니다.<br><br>", "tooltip": "빅토르가 일정 지역에 {{ stormduration }}초 동안 아케인 폭풍을 일으켜 즉시 <magicDamage>{{ initialburstdamage }}의 마법 피해</magicDamage>를 입힌 후 주변 적에게 초당 <magicDamage>{{ subsequentburstdamage }}의 마법 피해</magicDamage>를 입힙니다. 폭풍은 최근 피해를 입힌 챔피언을 자동으로 따라갑니다.<br /><br /><recast>재사용 시:</recast> 빅토르가 직접 폭풍을 움직일 수 있습니다.<br /><br /><keywordMajor>업그레이드:</keywordMajor> 폭풍이 {{ augmentboost*100 }}% 빠르게 이동합니다. 폭풍이 피해를 입힌 챔피언이 죽으면 폭풍의 크기가 커지고 지속시간이 {{ tooltip_durationextension }}초 증가합니다. (최대 {{ maxgrowths }}회)<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "피해량 (최초)", "피해량 (지속)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ initialburstbasedamage }} -> {{ initialburstbasedamageNL }}", "{{ subsequentburstbasedamage }} -> {{ subsequentburstbasedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "ViktorR.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "영광스러운 진화", "description": "빅토르가 적을 처치할 때마다 마공학 파편을 얻습니다. 마공학 파편을 100개 획득할 때마다 빅토르의 사용 스킬이 영구적으로 증강됩니다. 기본 스킬을 모두 업그레이드한 후에는 마공학 파편을 100개 모아 궁극기를 증강할 수 있습니다.", "image": {"full": "Viktor_Passive.ViktorVGU.png", "sprite": "passive5.png", "group": "passive", "x": 96, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}