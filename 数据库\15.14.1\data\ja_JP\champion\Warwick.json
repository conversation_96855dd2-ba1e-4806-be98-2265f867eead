{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "ワーウィック", "title": "解き放たれたゾウンの激憤", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "銀狼ワーウィック", "chromas": true}, {"id": "19002", "num": 2, "name": "アーフ・ザ・マナティ", "chromas": false}, {"id": "19003", "num": 3, "name": "悪巧み中のワーウィック", "chromas": false}, {"id": "19004", "num": 4, "name": "ツンドラの狩人ワーウィック", "chromas": false}, {"id": "19005", "num": 5, "name": "獣王ワーウィック", "chromas": false}, {"id": "19006", "num": 6, "name": "火焔の牙ワーウィック", "chromas": false}, {"id": "19007", "num": 7, "name": "ハイエナ ワーウィック", "chromas": false}, {"id": "19008", "num": 8, "name": "略奪者ワーウィック", "chromas": false}, {"id": "19009", "num": 9, "name": "アーフウィック", "chromas": false}, {"id": "19010", "num": 10, "name": "月の守護者ワーウィック", "chromas": true}, {"id": "19016", "num": 16, "name": "PROJECT: Warwick", "chromas": true}, {"id": "19035", "num": 35, "name": "古の神ワーウィック", "chromas": false}, {"id": "19045", "num": 45, "name": "冬の祝福ワーウィック", "chromas": false}, {"id": "19046", "num": 46, "name": "プレステージ冬の祝福ワーウィック", "chromas": false}, {"id": "19056", "num": 56, "name": "Arcane ヴァンダー・ワーウィック", "chromas": false}], "lore": "ワーウィックはゾウンの灰色の路地を徘徊する怪物だ。苦痛を伴う実験によって変性した彼の肉体に融合された、ポンプやシリンダーで構成された複雑なシステムが、錬金術的に合成された憤怒を彼の血流に送り込んでいる。物陰から飛び出しては、都市の深部を脅かしている犯罪者を餌食とするのだ。ワーウィックは血に引き寄せられ、その匂いは彼の正気を失わせる。血を流す者は、決して彼から逃れることはできない。", "blurb": "ワーウィックはゾウンの灰色の路地を徘徊する怪物だ。苦痛を伴う実験によって変性した彼の肉体に融合された、ポンプやシリンダーで構成された複雑なシステムが、錬金術的に合成された憤怒を彼の血流に送り込んでいる。物陰から飛び出しては、都市の深部を脅かしている犯罪者を餌食とするのだ。ワーウィックは血に引き寄せられ、その匂いは彼の正気を失わせる。血を流す者は、決して彼から逃れることはできない。", "allytips": ["「血の追跡(W)」の血跡をたどって体力の低い敵チャンピオンを見つけよう。", "「絶狼牙連撃(R)」の移動距離は増加した移動速度に応じて伸びる。これには味方からのバフやサモナースペルも含まれる。", "「野獣の牙(Q)」はキーを押し続けていれば、移動、ダッシュ、またはテレポートする敵を追いかけることができる。"], "enemytips": ["ワーウィックは体力が低下すると通常攻撃で体力が回復してしまう。トドメを刺す時のために妨害スキルは温存しておこう。", "ワーウィックは体力が低下した敵に対しては非常に強力。体力を維持して彼を寄せつけないようにしよう。", "ワーウィックのアルティメットスキルの射程は移動速度に応じて増加する。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "野獣の牙", "description": "前方にダッシュして対象に噛みつき、対象の最大体力に応じたダメージを与え、与えたダメージに応じて自身の体力を回復する。", "tooltip": "<tap>短く押す(タップ):</tap> 前方にダッシュして噛みつき、<magicDamage>{{ basebitedamage }}(+最大体力の{{ targetpercenthpdamage }}%)の魔法ダメージ</magicDamage>を与え、<healing>与えたダメージの{{ e3 }}%にあたる体力を回復</healing>する。<br /><br /><hold>長押し:</hold> 前方にダッシュして対象に深く噛みつき、対象の背後にジャンプする。噛みつき中は対象が移動すると自身も一緒に移動する。口を離したときに同量のダメージを与えて回復効果を得る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["体力回復量", "体力割合ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ e3 }}% -> {{ e3NL }}%", "{{ targetpercenthpdamage }}% -> {{ targetpercenthpdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "WarwickW", "name": "血の追跡", "description": "体力が50%未満の敵ユニットを感知して、その敵ユニットに向かう際に移動速度と攻撃速度が増加する。その敵ユニットの体力が25%未満に低下した場合は、ワーウィックが狂乱状態になってこれらの効果が3倍になる。", "tooltip": "<spellPassive>自動効果</spellPassive>: 体力が50%未満のチャンピオンを感知し、そのチャンピオンに向かう際に<speed>移動速度が{{ passivemsbonus }}%</speed>増加する。体力が50%未満の敵にスキルを使用するか通常攻撃を行うと、<speed>攻撃速度が{{ passiveasbonus }}%</speed>増加する。これらの効果は体力が25%未満の敵に対しては200%増加する。<br /><br /><spellActive>発動効果:</spellActive> 少しの間だけすべての敵を感知し、体力量にかかわらず、最も近くにいるチャンピオンに対して、このスキルの自動効果を8秒間獲得する。チャンピオンがいなかった場合は、このスキルのクールダウンが30%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "攻撃速度", "クールダウン"], "effect": ["{{ passivemsbonus }}% -> {{ passivemsbonusNL }}%", "{{ passiveasbonus }}% -> {{ passiveasbonusNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "WarwickE", "name": "怒りの咆哮", "description": "2.5秒間、受けるダメージが減少する。効果の終了時、またはスキルを再発動すると、咆哮をあげて周囲の敵ユニットを1秒間逃走させる。", "tooltip": "2.5秒間、{{ e1 }}%のダメージ軽減効果を獲得する。効果時間が終了すると咆哮をあげ、周囲の敵に{{ e3 }}秒間<status>フィアー効果</status>を与える。<recast>再発動</recast>すると、このスキルを早めに終了できる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ軽減", "クールダウン"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "WarwickR", "name": "絶狼牙連撃", "description": "指定方向にジャンプして、最初に接触した敵チャンピオンに1.5秒間サプレッション効果を与える(増加した移動速度に応じて射程が拡大)。", "tooltip": "自身の<speed>移動速度</speed>に応じて長い距離をジャンプし、最初に接触したチャンピオンに{{ rduration }}秒間の詠唱の間<status>サプレッション効果</status>を与える。また、この間に対象のチャンピオンに3回通常攻撃を行って、<magicDamage>{{ damagecumulative }}の魔法ダメージ</magicDamage>を与える。詠唱中に<healing>与えた全ダメージの100%にあたる体力を回復</healing>する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "永遠の渇き", "description": "通常攻撃が追加魔法ダメージを与える。自身の体力が50%未満の場合、追加魔法ダメージと同量の体力を回復する。自身の体力が25%未満の場合、この回復効果は3倍になる。", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}