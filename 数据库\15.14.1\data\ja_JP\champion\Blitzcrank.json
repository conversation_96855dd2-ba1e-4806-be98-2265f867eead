{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "ブリッツクランク", "title": "偉大なるスチームゴーレム", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "錆びついたブリッツクランク", "chromas": false}, {"id": "53002", "num": 2, "name": "ゴールキーパー ブリッツクランク", "chromas": false}, {"id": "53003", "num": 3, "name": "ボクサー ブリッツクランク", "chromas": false}, {"id": "53004", "num": 4, "name": "ピルトーヴァー ブリッツクランク", "chromas": false}, {"id": "53005", "num": 5, "name": "これじゃないブリッツクランク", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Riot ブリッツクランク", "chromas": false}, {"id": "53011", "num": 11, "name": "バトルボス ブリッツクランク", "chromas": true}, {"id": "53020", "num": 20, "name": "ダークランサー ブリッツクランク", "chromas": false}, {"id": "53021", "num": 21, "name": "ライトランサー ブリッツクランク", "chromas": false}, {"id": "53022", "num": 22, "name": "魔女の大釜ブリッツクランク", "chromas": true}, {"id": "53029", "num": 29, "name": "スペースグルーヴ ブリッツ＆クランク", "chromas": true}, {"id": "53036", "num": 36, "name": "勝利の栄光ブリッツクランク", "chromas": true}, {"id": "53047", "num": 47, "name": "至高のゲーム ブリッツクランク", "chromas": true}, {"id": "53056", "num": 56, "name": "ブンブン クランク", "chromas": true}], "lore": "ブリッツクランクはゾウンからやってきた、ほぼ破壊不能な巨大ロボットだ。もともとは危険な廃棄物を処理するために造られたが、その目的には制約が多過ぎると感じた彼は「下層」で苦しんでいる人々を助けるために自らの体を改造した。ブリッツクランクは他者を守るため、危険も省みずにパワーと耐久力を活かした優しい鉄の拳を差し出したり、エネルギーを放出したりして悪者たちをこらしめている。", "blurb": "ブリッツクランクはゾウンからやってきた、ほぼ破壊不能な巨大ロボットだ。もともとは危険な廃棄物を処理するために造られたが、その目的には制約が多過ぎると感じた彼は「下層」で苦しんでいる人々を助けるために自らの体を改造した。ブリッツクランクは他者を守るため、危険も省みずにパワーと耐久力を活かした優しい鉄の拳を差し出したり、エネルギーを放出したりして悪者たちをこらしめている。", "allytips": ["「ロケットグラブ」「パワーフィスト」「イナズマフィールド」の3連コンボは強力だ！", "狙った敵に大ダメージを与えることができる。", "迂闊に近づいてきた敵を「ロケットグラブ」でタワー下まで引きずり込み「パワーフィスト」で追撃すれば、タワーの攻撃をくらわせることができる。"], "enemytips": ["体力残量が少なくなると固有スキルの「マナバリア」が発動し、バリアでダメージを防ぐので要注意。", "「ロケットグラブ」は最初に命中した敵を引き寄せるため、常にミニオンを盾にしていよう。"], "tags": ["Tank", "Support"], "partype": "マナ", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "ロケットグラブ", "description": "指定方向に右手を発射する。最初に触れた敵ユニットを掴んでダメージを与え、スタン効果を付与して自身のそばに引き寄せる。", "tooltip": "右手を発射して最初に命中した敵を自身の方向に<status>引き寄せ</status>、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Overdrive", "name": "オーバードライブ", "description": "エネルギーをスーパーチャージして、移動速度と攻撃速度を大幅に上げる。効果終了後、エネルギーが切れて一時的にスロウ状態になる。", "tooltip": "エネルギーをスーパーチャージして、{{ duration }}秒間、<speed>移動速度を{{ movespeedmod*100 }}%</speed>、<attackSpeed>攻撃速度を{{ attackspeedmod*100 }}%</attackSpeed>増加させる。増加した移動速度は効果時間中に徐々に元に戻る。<br /><br />その後、自身が{{ slowduration }}秒間、{{ movespeedmodreduction*100 }}%の<status>スロウ効果</status>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["移動速度", "攻撃速度"], "effect": ["{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "PowerFist", "name": "パワーフィスト", "description": "拳にエネルギーを充填して次の通常攻撃のダメージを2倍にし、対象をノックアップする。", "tooltip": "拳にエネルギーを充填する。次の通常攻撃で対象を{{ ccduration }}秒間<status>ノックアップ</status>させ、<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "StaticField", "name": "イナズマフィールド", "description": "通常攻撃した敵にマークを付け、1秒後に電撃のダメージを与える。また、発動すると周囲の敵ユニットのシールドを消滅させ、ダメージを与えて短時間のサイレンス効果を与える。", "tooltip": "<spellPassive>自動効果: </spellPassive>このスキルが発動可能な間はブリッツクランクの拳が帯電し、通常攻撃を行った敵にマークを付ける。マークされた敵は1秒後に<magicDamage>{{ passivedamage }}の魔法ダメージ</magicDamage>を受ける。<br /><br /><spellActive>発動効果: </spellActive>オーバーチャージして周囲の敵に<magicDamage>{{ activedamage }}の魔法ダメージ</magicDamage>を与え、{{ silenceduration }}秒間<status>サイレンス状態</status>にする。また、対象のシールドも消滅させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果基本ダメージ", "自動効果の魔力反映率", "発動効果基本ダメージ", "発動効果クールダウン"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}% -> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "マナ<PERSON>リア", "description": "体力が低下すると、マナの量に応じたシールドを獲得する。", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}