{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yuumi": {"id": "<PERSON><PERSON>", "key": "350", "name": "유미", "title": "마법 고양이", "image": {"full": "Yuumi.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "350000", "num": 0, "name": "default", "chromas": false}, {"id": "350001", "num": 1, "name": "전투사관학교 유미 교장선생님", "chromas": true}, {"id": "350011", "num": 11, "name": "사랑의 추적자 유미", "chromas": true}, {"id": "350019", "num": 19, "name": "꿀잼 유미", "chromas": true}, {"id": "350028", "num": 28, "name": "마녀 유미", "chromas": true}, {"id": "350037", "num": 37, "name": "EDG 유미", "chromas": true}, {"id": "350039", "num": 39, "name": "시바견 유미", "chromas": true}, {"id": "350049", "num": 49, "name": "사이버 고양이 유미", "chromas": true}, {"id": "350050", "num": 50, "name": "프레스티지 사이버 고양이 유미", "chromas": false}, {"id": "350061", "num": 61, "name": "어둠의 인도자 유미", "chromas": true}], "lore": "유미는 밴들 시티 출신의 마법 고양이로 한때 요들 마법사 노라와 함께 살았다. 어느 날 노라가 알 수 없는 이유로 종적을 감추자 유미는 자아를 지닌 노라의 책 '관문의 서'의 수호자가 되었으며, 책 페이지 안에 존재하는 차원문을 타고 주인을 찾는 여정에 나섰다. 애정을 갈구하는 유미는 여정을 함께 떠날 친절한 동료들을 구하며, 빛나는 방패와 단호한 결의로 그들을 보호한다. 노라의 책은 유미가 눈앞에 놓인 임무에 집중하도록 하기 위해 애쓰지만, 유미는 낮잠이나 생선 같은 세상 속 즐거움에 이따금 한눈을 팔곤 한다. 물론 유미도 결국은 항상 주인을 찾는 일로 되돌아온다.", "blurb": "유미는 밴들 시티 출신의 마법 고양이로 한때 요들 마법사 노라와 함께 살았다. 어느 날 노라가 알 수 없는 이유로 종적을 감추자 유미는 자아를 지닌 노라의 책 '관문의 서'의 수호자가 되었으며, 책 페이지 안에 존재하는 차원문을 타고 주인을 찾는 여정에 나섰다. 애정을 갈구하는 유미는 여정을 함께 떠날 친절한 동료들을 구하며, 빛나는 방패와 단호한 결의로 그들을 보호한다. 노라의 책은 유미가 눈앞에 놓인 임무에 집중하도록 하기 위해 애쓰지만, 유미는...", "allytips": [], "enemytips": [], "tags": ["Support", "Mage"], "partype": "마나", "info": {"attack": 5, "defense": 1, "magic": 8, "difficulty": 2}, "stats": {"hp": 500, "hpperlevel": 69, "mp": 440, "mpperlevel": 45, "movespeed": 330, "armor": 25, "armorperlevel": 4.2, "spellblock": 25, "spellblockperlevel": 1.1, "attackrange": 425, "hpregen": 5, "hpregenperlevel": 0.55, "mpregen": 10, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3.1, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "YuumiQ", "name": "사르르탄", "description": "유미가 미사일을 발사해 처음으로 맞힌 적에게 피해를 입히고 둔화시킵니다. 미사일이 1.35초 이상 날아가 적중할 경우 피해량이 추가되고 둔화 효과가 강화됩니다. 단짝에게 붙어있을 때는 둔화 효과가 항상 강화되며, 둔화 적용 시 단짝이 강화되어 적중 시 추가 피해를 입힙니다.<br><br>아군과 밀착한 상태일 때는 미사일의 궤도를 잠시 마우스로 조종할 수 있습니다.", "tooltip": "유미가 상황에 따라 방향을 바꿀 수 있는 미사일을 소환하여 처음 적중한 적에게 <magicDamage>{{ totalmissiledamage }}의 마법 피해</magicDamage>를 입히고 {{ slowamount }}% <status>둔화</status>시킵니다.<br /><br /><keywordMajor>밀착 상태</keywordMajor>에서 사용하면 유미가 마우스로 미사일을 조종할 수 있습니다. 일단 속도가 붙은 미사일은 조종할 수 없고 직선으로 날아가며 대상에게 <magicDamage>{{ totalmissiledamageempowered }}의 마법 피해</magicDamage>를 입히고 {{ empoweredslowduration }}초 동안 {{ empoweredslowamount }}% <status>둔화</status>시킵니다.<br /><br /><keywordMajor>단짝 추가 효과:</keywordMajor> <spellName>사르르탄</spellName>의 <status>둔화</status> 효과가 항상 강화되며 적 챔피언에게 둔화 적용 시 {{ buffduration }}초 동안 단짝이 강화되어 <OnHit>적중 시 %i:OnHit%</OnHit> <magicDamage>{{ onhitdamagecalc }}의 마법 피해</magicDamage>를 추가로 입힙니다.<br /><br /><rules>적중 시 추가 피해량은 단짝의 치명타 확률에 따라 {{ allycritchancemaxamp*100 }}% 증가할 수 있습니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["마나 소모량", "기본 피해량", "강화된 피해량", "강화된 둔화 효과", "적중 시 피해량"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ missiledamage }} -> {{ missiledamageNL }}", "{{ empoweredmissiledamage }} -> {{ empoweredmissiledamageNL }}", "{{ empoweredslowamount }}% -> {{ empoweredslowamountNL }}%", "{{ onhitbase }} -> {{ onhitbaseNL }}"]}, "maxrank": 6, "cooldown": [6.5, 6.5, 6.5, 6.5, 6.5, 6.5], "cooldownBurn": "6.5", "cost": [50, 55, 60, 65, 70, 75], "costBurn": "50/55/60/65/70/75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiQ.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "YuumiW", "name": "너랑 유미랑!", "description": "유미가 대상 아군에게 돌진하여 포탑을 제외한 유닛이 대상으로 지정할 수 없는 상태가 됩니다. 단짝에게 붙어있을 때 유미의 체력 회복 및 보호막 효과가 증폭되고 단짝은 적중 시 체력을 회복합니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> <keywordMajor>단짝</keywordMajor>에게 붙어있을 때 유미의 <keywordMajor>체력 회복 및 보호막 효과가 {{ healandshieldpower*100 }}%</keywordMajor> 추가로 증가하며, 단짝은 <healing>체력을 {{ healthonhit }}</healing> 회복합니다. <OnHit>적중 시 %i:OnHit%</OnHit>.<br /><br /><spellActive>사용 시:</spellActive> 유미가 아군 챔피언에게 돌진하여 <keywordMajor>밀착</keywordMajor>합니다. 유미는 <keywordMajor>밀착 상태</keywordMajor>에서 밀착 대상을 따라다니며 포탑을 제외한 유닛이 대상으로 지정할 수 없는 상태가 됩니다.<br /><br />유미에게 <status>이동 불가</status> 효과가 적용되면 이 스킬에 {{ ccattachlockout }}초의 재사용 대기시간이 적용됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["적중 시 체력 회복", "추가 체력 회복 및 보호막 효과"], "effect": ["{{ basehealthonhit }} -> {{ basehealthonhitNL }}", "{{ healandshieldpower*100.000000 }} -> {{ healandshieldpowernl*100.000000 }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiW.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YuumiE", "name": "슈우우웅", "description": "유미가 보호막을 얻고 이동 속도와 공격 속도가 증가합니다. 아군과 밀착된 상태인 경우 이 효과는 유미 대신 해당 아군에게 적용됩니다.<br>", "tooltip": "유미가 <shield>{{ totalshielding }}의 피해</shield>를 흡수하는 보호막을 얻고 {{ msduration }}초 동안 <attackSpeed>공격 속도가 {{ totalattackspeed }}%</attackSpeed> 증가합니다. 보호막이 남아있는 동안 대상의 <speed>이동 속도가 {{ msamount }}%</speed> 증가합니다.<br /><br />유미가 <keywordMajor>밀착</keywordMajor> 상태면 위 효과를 유미 대신 해당 아군에게 적용하고 <magicDamage>마나를 {{ manarestore }}</magicDamage> 회복시킵니다. 마나 회복량은 대상이 잃은 마나에 따라 {{ maxmanapercincrease*100 }}%까지 증가합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["보호막 흡수량", "마나 소모량", "마나 회복", "공격 속도"], "effect": ["{{ baseshielding }} -> {{ baseshieldingNL }}", "{{ cost }} -> {{ costNL }}", "{{ manarestore }} -> {{ manarestoreNL }}", "{{ attackspeedamount }} -> {{ attackspeedamountNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 90, 100, 110, 120], "costBurn": "80/90/100/110/120", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "0", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YuumiE.png", "sprite": "spell16.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "YuumiR", "name": "대단원", "description": "유미가 정신을 집중해 파동을 다섯 번 발사합니다. 파동은 적에게 피해를 입히고 아군의 체력을 회복시킵니다. 정신을 집중하는 동안 이동하거나 밀착할 수 있으며, 슈우우웅! 스킬도 사용할 수 있습니다. 단짝에게 붙어있으면 마우스로 이 스킬을 조종할 수 있습니다.", "tooltip": "유미가 {{ ultduration }}초 동안 정신을 집중해 양 팀 모두에 영향을 주는 마법의 파동을 {{ numberofwaves }}번 발사합니다. 처음 <keywordMajor>밀착</keywordMajor>한 상태에서 시전하면 유미는 마우스를 따라 파동을 조종할 수 있습니다.<br /><br />적중당한 적에게 <magicDamage>{{ totalmissiledamage }}의 마법 피해</magicDamage>를 입히고 {{ ccduration }}초 동안 {{ baseslow*-100 }}% <status>둔화</status>시킵니다. 둔화 효과는 파동에 적중될 때마다 {{ bonusslowperwave*-100 }}% 증가합니다.<br /><br />아군 챔피언은 파동마다 <healing>{{ totalhealperwave }}의 체력</healing>을 회복합니다. 체력 회복 초과분은 <shield>보호막</shield>으로 전환됩니다.<br /><br /><keywordMajor>단짝 보너스:</keywordMajor> 유미의 <keywordMajor>단짝</keywordMajor>은 회복량이 증가해 <healing>{{ enhancedhealperwave }}의 체력</healing>을 회복합니다.<br /><br /><rules><spellName>너랑 유미랑!</spellName>을 시전하면 파동을 현재 방향으로 고정합니다.<br />유미는 정신을 집중하는 동안 이동할 수 있으며 <spellName>슈우우웅</spellName> 스킬을 사용할 수 있습니다.</rules><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "미사일당 기본 피해량", "파동당 기본 회복량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basemissiledamage }} -> {{ basemissiledamageNL }}", "{{ basehealperwave }} -> {{ basehealperwaveNL }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "YuumiR.png", "sprite": "spell16.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "야옹이 친구", "description": "기본 공격이나 스킬로 챔피언을 맞히면 주기적으로 유미가 체력을 회복하고 다음으로 밀착하는 아군의 체력을 회복시킵니다.<br><br>유미는 밀착한 아군과 특별한 유대를 형성합니다. 가장 유대가 긴밀한 아군에게 밀착한 동안 유미의 스킬이 강화됩니다.", "image": {"full": "YuumiP2.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}