{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "<PERSON><PERSON><PERSON>", "title": "ο Σκανδαλιάρης της Παλίρροιας", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "Φιζ της Ατλαντίδας", "chromas": false}, {"id": "105002", "num": 2, "name": "Φιζ της Τούνδρας", "chromas": false}, {"id": "105003", "num": 3, "name": "<PERSON>ιζ ο Ψαράς", "chromas": false}, {"id": "105004", "num": 4, "name": "Φιζ του Κενού", "chromas": false}, {"id": "105008", "num": 8, "name": "Φιζ το <PERSON>", "chromas": false}, {"id": "105009", "num": 9, "name": "Υπεργαλαξιακός Φιζ", "chromas": false}, {"id": "105010", "num": 10, "name": "<PERSON>ιζ Ομάδας Ωμέγα", "chromas": true}, {"id": "105014", "num": 14, "name": "<PERSON>ν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ζ", "chromas": false}, {"id": "105015", "num": 15, "name": "Χνουδωτ<PERSON>ς Φιζ - Έκδοση Κύρους", "chromas": false}, {"id": "105016", "num": 16, "name": "<PERSON>ιζ Διαβολάκος", "chromas": true}, {"id": "105025", "num": 25, "name": "Χν<PERSON>υδω<PERSON><PERSON><PERSON> Φιζ - Έκδοση Κύρους (2022)", "chromas": false}, {"id": "105026", "num": 26, "name": "Αστρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "105035", "num": 35, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> της Β<PERSON><PERSON><PERSON><PERSON><PERSON>ζ", "chromas": true}], "lore": "Ο Φιζ είναι ένα αμφίβιο Γιορντλ που κατοικεί στους υφάλους γύρω από το Μπίλτζγουοτερ. Μαζεύει συχνά τα τάματα και τα χρήματα που πετούν οι προληπτικοί καπετάνιοι στη θάλασσα και τους τα επιστρέφει. Ωστόσ<PERSON>, ακόμη και οι πιο μπαρουτοκαπνισμένοι θαλασσόλυκοι δεν τολμούν να τα βάλουν μαζί του, καθώς κυκλοφορούν πολλές ιστορίες για όσους δοκίμασαν να υποτιμήσουν αυτό το ύπουλο πλάσμα. Πολλοί πιστεύουν ότι πρόκειται για κάποιο παιχνιδιάρικο πνεύμα του ωκεανού, γιατί φαίνεται ότι έχει τη δυνατότητα να ελέγχει τα πλάσματα του βυθού, ενώ του αρέσει πολύ να συγχύζει και να κοροϊδεύει τους εχθρούς... αλλά και τους φίλους του.", "blurb": "Ο Φιζ είναι ένα αμφίβιο Γιορντλ που κατοικεί στους υφάλους γύρω από το Μπίλτζγουοτερ. Μαζεύει συχνά τα τάματα και τα χρήματα που πετούν οι προληπτικοί καπετάνιοι στη θάλασσα και τους τα επιστρέφει. Ωστ<PERSON><PERSON><PERSON>, ακόμη και οι πιο μπαρουτοκαπνισμένοι...", "allytips": ["Ο Φιζ μπορεί να κινείται μέσα από μονάδες, οπότε προσπαθήστε να βρείτε ευκαιρίες να περνάτε μέσα από τους υπηρέτες και να ενεργοποιείτε την παθητική της Τρίαινας Θαλασσόπετρας. Μετ<PERSON> από λίγα δευτερόλεπτα, συνεχίστε με την ενεργή επίθεση της ικανότητας.", "Η Υπέρτατη ικανότητα του Φιζ, το Δα<PERSON>κώνοντας τα Κύματα μπορεί να έχει στόχο οποιονδήποτε εχθρό αλλά και την περιοχή όπου νομίζετε ότι θα κατευθυνθεί.", "Οι ικανότητες του Φιζ δυναμώνουν ανάλογα με την Ισχύ Ικανότητάς του. Αγοράστε αντικείμενα όπως η Κλεψύδρα της Ζόνια ή το Πέπλο της Καταραμένης για να αντιμετωπίσετε ομάδες που κάνουν μεγάλη άμεση ζημιά. Αν μπορείτε να αντέξετε χωρίς την παραπάνω Ζωή, αγοράστε έναν Όλεθρο των Απέθαντων ή ένα Καπέλο του Ράμπαντον."], "enemytips": ["Οι επιθέσεις του Φιζ γίνονται πιο επικίνδυνες για λίγα δευτερόλεπτα, όταν χρησιμοποιεί την ενισχυμένη του επίθεση. Προσπαθήστε να τον κρατήσετε μακριά όταν η τρίαινά του λάμπει!", "Όταν οι ικανότητές του είναι διαθέσιμες, ο Φιζ γίνεται πολύ δύσκολος στόχος, γι' αυτό προσπαθήστε να τον κάνετε να τις χρησιμοποιήσει νωρίς και μετά χτυπήστε τον με επιδράσεις καταστολής ή δυνατές επιθέσεις!"], "tags": ["Assassin", "Fighter"], "partype": "Μάνα", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "Χτύπημα Αχινού", "description": "Ο Φιζ διαπερνά τον αντίπαλο, προ<PERSON><PERSON>λώντας Μαγική Ζημιά και εφαρμόζοντας επιδράσεις που ενεργοποιούνται κατά το χτύπημα.", "tooltip": "Ο Φιζ εφορμά μέσα από έναν εχθρό, προκαλώντας <physicalDamage>{{ totaldamage }} Σωματική Ζημιά</physicalDamage> συν <magicDamage>{{ qdamage }} Μαγική Ζημιά</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzW", "name": "Τρίαινα Θ<PERSON>λασσόπετρας", "description": "Οι επιθέσεις του Φιζ κάνουν τους εχθρούς του να αιμορραγούν, με αποτέλεσμα να δέχονται Μαγική Ζημιά για αρκετά δευτερόλεπτα. Ο Φιζ μπορεί να ενδυναμώσει την επόμενη επίθεσή του για να προκαλέσει μπόνους ζημιά και να ενισχύσει τις επόμενες επιθέσεις του για σύντομο χρονικό διάστημα.", "tooltip": "<spellPassive>Παθητική</spellPassive>: Οι επιθέσεις του Φιζ κάνουν τους εχθρούς του να αιμορραγούν, προκαλώντας <magicDamage>{{ dotdamage }} Μαγική Ζημιά</magicDamage> μέσα σε {{ bleedduration }} δευτ. <br /><br /><spellActive>Ενεργή</spellActive>: Η επόμενη επίθεση του Φιζ προκαλεί επιπλέον <magicDamage>{{ activedamage }} Μαγική Ζημιά</magicDamage>. Αν αυτή η επίθεση σκοτώσει τον στόχο της, ο Φιζ ανακτά <scaleMana>{{ onkillmanarefund }} Μάνα</scaleMana> και μειώνει τον Χρόνο Επαναφόρτισης αυτής της ικανότητας σε {{ onkillnewcooldown }} δευτ. Αν δεν σκοτώσει τον στόχο της, οι επιθέσεις του Φιζ προκαλούν επιπλέον <magicDamage>{{ onhitbuffdamage }} Μαγική Ζημιά</magicDamage> για {{ onhitbuffduration }} δευτ.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ζημιά Παθητικής", "Ενεργή ζημιά", "Ζημιά κατά το χτύπημα", "Επιστροφή Μάνα", "Κόστος @AbilityResourceName@", "<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης"], "effect": ["{{ dotbasedamage }} -> {{ dotbasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }} -> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }} -> {{ onkillmanarefundNL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzE", "name": "Παιχνιδιάρης / Σκανδαλιάρης", "description": "Ο Φιζ πηδάει στον αέρα, στε<PERSON><PERSON>ώνεται με χάρη πάνω στο δόρυ του και δεν μπορεί να γίνει στόχος. Από αυτή τη θέση, ο <PERSON>ιζ μπορεί είτε να χτυπήσει το έδαφος είτε να πηδήξει πάλι προτού προσγειωθεί στο έδαφος.", "tooltip": "Ο Φιζ ανεβαίνει στην τρίαινά του και δεν μπορεί να γίνει στόχος για 0,75 δευτ. Στη συνέχεια, προκαλεί <magicDamage>{{ edamage }} Μαγική Ζημιά</magicDamage> σε κοντινούς εχθρούς και τους <status>Επιβραδύνει</status> κατά {{ slowamount*100 }}% για {{ slowduration }} δευτ. <br /><br />Ο Φιζ μπορεί να κάνει <recast>Νέα χρήση</recast> αυτής της ικανότητας όσο δεν μπορεί να γίνει στόχος για να εφορμήσει ξανά. Σε αυτήν την περίπτωση, η επίδραση λήγει νωρίτερα, προκαλεί ζημιά σε μικρότερη περιοχή και δεν <status>Επιβραδύνει</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Κόστος @AbilityResourceName@", "Ζημιά", "Επιβράδυνση"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzR", "name": "Δαγ<PERSON>ώνοντας τα Κύματα", "description": "Ο Φιζ πετάει ένα ψάρι σε μια κατεύθυνση, το οποίο κολλάει σε όποιον Ήρωα το αγγίξει και τον επιβραδύνει. <PERSON><PERSON><PERSON><PERSON> από μια σύντομη καθυστέρηση, ένα<PERSON> καρχαρίας πετάγεται από το έδαφος, εκτοξεύει στον αέρα τον στόχο και πετάει μακριά όλους τους κοντινούς εχθρούς. Όλοι οι εχθροί που επηρεάζονται δέχονται Μαγική Ζημιά και επιβραδύνονται.", "tooltip": "Ο Φιζ εκτοξεύει ένα ψάρι που κολλάει στον πρώτο αντίπαλο Ήρωα που θα χτυπήσει. Το θύμα δίνει <keywordStealth>Ενόραση</keywordStealth> της θέσης του και <status>Επιβραδύνεται</status> κατά 40% έως 80% ανάλογα με την απόσταση που κάλυψε το ψάρι μέχρι να κολλήσει στον στόχο του. <br /><br />Μετά από {{ detonationtime }} δευτ., ένας καρχαρίας αναδύεται κάτω από τον στόχο, <status>Εκτοξεύοντας στον Αέρα</status> τον στόχο με το ψάρι για 1 δευτ., <status>Απωθώντας</status> όλες τις άλλες μονάδες και προκαλώντας <magicDamage>{{ smallsharkdamage }} έως {{ bigsharkdamage }} Μαγική Ζημιά</magicDamage>, ανάλογα με την απόσταση που κάλυψε το ψάρι μέχρι να κολλήσει στον στόχο του.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>όρτισης", "Ζημιά Μικρού Καρχαρία", "Ζημιά Μεσαίου Καρχαρία", "Ζημιά Μεγάλου Καρχαρία"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ smalldamage }} -> {{ smalldamageNL }}", "{{ middamage }} -> {{ middamageNL }}", "{{ bigdamage }} -> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Ευέλι<PERSON><PERSON><PERSON>εμιστής", "description": "Ο Φιζ μπορεί να περνάει μέσα από μονάδες και δέχεται σταθερή ποσότητα μειωμένης ζημιάς από όλες τις πηγές.", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}