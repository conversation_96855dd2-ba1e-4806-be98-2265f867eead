{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sona": {"id": "<PERSON><PERSON>", "key": "37", "name": "<PERSON><PERSON>", "title": "Ma<PERSON> of the Strings", "image": {"full": "Sona.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "37000", "num": 0, "name": "default", "chromas": false}, {"id": "37001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "37002", "num": 2, "name": "Pentakill Sona", "chromas": false}, {"id": "37003", "num": 3, "name": "Silent Night Sona", "chromas": false}, {"id": "37004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "37005", "num": 5, "name": "Arcade Sona", "chromas": true}, {"id": "37006", "num": 6, "name": "DJ <PERSON><PERSON>", "chromas": false}, {"id": "37007", "num": 7, "name": "Sweetheart Sona", "chromas": false}, {"id": "37009", "num": 9, "name": "Odyssey Sona", "chromas": true}, {"id": "37017", "num": 17, "name": "PsyOps Sona", "chromas": true}, {"id": "37026", "num": 26, "name": "Pentakill III: Lost Chapter Sona", "chromas": true}, {"id": "37035", "num": 35, "name": "Star Guardian Sona", "chromas": true}, {"id": "37045", "num": 45, "name": "Immortal Journey Sona", "chromas": true}, {"id": "37046", "num": 46, "name": "Prestige Immortal Journey Sona", "chromas": false}, {"id": "37056", "num": 56, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "<PERSON><PERSON> is <PERSON><PERSON><PERSON>'s foremost virtuoso of the stringed etwahl, speaking only through her graceful chords and vibrant arias. This genteel manner has endeared her to the highborn, though others suspect her spellbinding melodies to actually emanate magic—a Demacian taboo. Silent to outsiders but somehow understood by close companions, <PERSON><PERSON> plucks her harmonies not only to soothe injured allies, but also to strike down unsuspecting enemies.", "blurb": "<PERSON><PERSON> is <PERSON><PERSON><PERSON>'s foremost virtuoso of the stringed etwahl, speaking only through her graceful chords and vibrant arias. This genteel manner has endeared her to the highborn, though others suspect her spellbinding melodies to actually emanate magic—a...", "allytips": ["Make sure to tag your allies while <PERSON><PERSON>'s auras are active, but avoid getting caught out by enemies.", "Save <PERSON><PERSON><PERSON>o for the game-altering moment.", "Well-timed uses of Aria of Perseverance will grant you maximum survivability."], "enemytips": ["Spread out when you see <PERSON><PERSON> so she can't make your entire team dance.", "Kill <PERSON><PERSON> first, as she will heal up her team if left alone for too long."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 2, "magic": 8, "difficulty": 4}, "stats": {"hp": 550, "hpperlevel": 91, "mp": 340, "mpperlevel": 45, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.3, "attackspeed": 0.644}, "spells": [{"id": "SonaQ", "name": "<PERSON><PERSON><PERSON> of Valor", "description": "<PERSON><PERSON> plays the Hymn of Valor, sends out bolts of sound, dealing magic damage to two nearby enemies, prioritizing champions and monsters. <PERSON><PERSON> gains a temporary aura that grants allies tagged by the zone bonus damage on their next attack against enemies.", "tooltip": "<PERSON><PERSON> deals <magicDamage>{{ totaldamage }} magic damage</magicDamage> to the nearest two enemies, prioritizing champions. She then begins a new <keywordMajor>Melody</keywordMajor>. Gain a stack of <keywordMajor>Accelerando</keywordMajor> for every champion you damage with this.<br /><br /><keywordMajor>Melody:</keywordMajor> <PERSON><PERSON> gains an aura for {{ auraduration }} seconds, granting allied champions an additional <magicDamage>{{ totalonhitdamage }} magic damage</magicDamage> %i:OnHit% on their next Attack within {{ onhitduration }} seconds.<br /><br /><keywordMajor>Power Chord - Staccato:</keywordMajor> Power Chord bonus damage (<magicDamage>{{ totalstaccatodamage }} total magic damage</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage (active)", "Damage (melody)", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "SonaQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaW", "name": "Aria of Perseverance", "description": "<PERSON><PERSON> plays the <PERSON> of Perseverance, sending out protective melodies, healing <PERSON><PERSON> and a nearby wounded ally. <PERSON><PERSON> gains a temporary aura that grants allies tagged by the zone a temporary shield.", "tooltip": "<spellPassive>Active:</spellPassive> <PERSON><PERSON> restores <healing>{{ totalheal }} Health</healing> to herself and a nearby allied champion, prioritizing the most wounded. She then begins a new <keywordMajor>Melody</keywordMajor>.<br /><br /><keywordMajor>Melody:</keywordMajor> <PERSON><PERSON> gains an aura for {{ auraduration }} seconds, granting allied champions <shield>{{ totalshield }} Shield</shield> for {{ shieldduration }} seconds.<br /><br />Gain a stack of <keywordMajor>Accelerando</keywordMajor> whenever you heal another injured ally and every time you protect another ally from at least {{ accelerandoshieldbreakpoint }} damage with this shield.<br /><br /><keywordMajor>Power Chord - Diminuendo:</keywordMajor> Power Chord also reduces physical and magic damage dealt by the target by {{ totaldiminuendoweakenpercent }} for {{ diminuendoduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heal (active)", "Shield (melody)", "@AbilityResourceName@ Cost"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SonaW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaE", "name": "Song of Celerity", "description": "<PERSON><PERSON> plays the Song of Celerity, granting nearby allies bonus Move Speed. <PERSON><PERSON> gains a temporary aura that grants allied champions tagged by the zone bonus Move Speed.", "tooltip": "<spellPassive>Active:</spellPassive> She begins a new <keywordMajor>Melody</keywordMajor> and grants herself <speed>{{ totalselfmovementspeed }} Move Speed</speed> for {{ selfmovementspeeddurationmin }} seconds, extended to up to {{ selfmovementspeeddurationmax }} seconds if she doesn't take damage. <br /><br /><keywordMajor>Melody:</keywordMajor> <PERSON><PERSON> gains an aura for {{ auraduration }} seconds that grants allied champions <speed>{{ totalallymovementspeed }} Move Speed</speed> for {{ allymovementspeedduration }} seconds.<br /><br /><keywordMajor>Power Chord - Tempo:</keywordMajor> Power Chord also <status>Slows</status> the target by {{ totaltempomovespeedslow }} for {{ tempoduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Move Speed"], "effect": ["{{ allybasemovementspeed*100.000000 }}% -> {{ allybasemovementspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "SonaE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaR", "name": "Crescendo", "description": "<PERSON><PERSON> plays her ultimate chord, stunning enemy champions and forcing them to dance and dealing magic damage to them.", "tooltip": "<PERSON><PERSON> strikes an irresistible chord, <status>Stunning</status> enemy for {{ stunduration }} seconds and dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "SonaR.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Power Chord", "description": "<passive>Accelerando</passive>: <PERSON><PERSON> gains non-Ultimate ability haste permanently for her basic abilities as she uses her abilities well, up to a cap. Beyond that cap, further successful uses reduce her ultimate's remaining cooldown instead.<br><br><passive>Power Chord</passive>: Every few spell casts, <PERSON><PERSON>'s next attack will deal bonus magic damage in addition to an additional effect based on what basic Ability <PERSON><PERSON> last activated.", "image": {"full": "Sona_Passive_Charged.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}