{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rammus": {"id": "<PERSON><PERSON>", "key": "33", "name": "<PERSON><PERSON>", "title": "l'arma-dillo", "image": {"full": "Rammus.png", "sprite": "champion3.png", "group": "champion", "x": 240, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "33000", "num": 0, "name": "default", "chromas": false}, {"id": "33001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "33002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "33003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "33004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "33005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "33006", "num": 6, "name": "Full Metal Rammus", "chromas": false}, {"id": "33007", "num": 7, "name": "Rammus Guardiano delle Sabbie", "chromas": false}, {"id": "33008", "num": 8, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "33016", "num": 16, "name": "Rammus Hextech", "chromas": false}, {"id": "33017", "num": 17, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "33026", "num": 26, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Per molti è un idolo, per qualcuno un abominio, ma per tutti è un mistero: Rammus è un essere incredibilmente enigmatico. Protetto da una corazza spinata, genera dubbi e teorie sulla sua origine ovunque vada: alcuni lo ritengono un semidio, altri un oracolo sacro, altri ancora una semplice bestia trasformata dalla magia. Qualunque sia la verità, Rammus continua a procedere lungo il deserto di Shurima senza fermarsi davanti a nulla.", "blurb": "Per molti è un idolo, per qualcuno un abominio, ma per tutti è un mistero: Rammus è un essere incredibilmente enigmatico. Protetto da una corazza spinata, genera dubbi e teorie sulla sua origine ovunque vada: alcuni lo ritengono un semidio, altri un...", "allytips": ["Ruota della distruzione può essere impiegata come meccanismo di fuga.", "Usando la provocazione su un campione vicino alla tua torre permette alla torre di attaccarlo.", "Onda sismica e Posizione difensiva a riccio possono essere usati più avanti nel corso della partita per danneggiare le torri. Se sei coinvolto tuo malgrado in uno scontro tra squadre, potrebbe essere utile disingaggiare e attaccare alcune strutture nemiche."], "enemytips": ["Fai particolare attenzione a quando la Posizione difensiva a riccio è in ricarica. Rammus ha statistiche molto inferiori rispetto a un tank normale quando non è in quella posizione.", "<PERSON><PERSON> tende a puntare tutto su accumulare armatura, il che lo rende vulnerabile ai danni dei maghi quando non è in Posizione difensiva a riccio."], "tags": ["Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 10, "magic": 5, "difficulty": 5}, "stats": {"hp": 675, "hpperlevel": 100, "mp": 310, "mpperlevel": 33, "movespeed": 335, "armor": 35, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.55, "mpregen": 7.85, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.215, "attackspeed": 0.7}, "spells": [{"id": "PowerBall", "name": "Ruota della distruzione", "description": "<PERSON><PERSON> accelera in forma di palla verso i suoi nemici, infliggendo danni e rallentando i bersagli colpiti dall'impatto.", "tooltip": "Rammus si mette a palla, ottiene <speed>{{ minimummovespeed }} velocità di movimento</speed> e accelera fino a <speed>{{ maximummovespeed }} velocità di movimento</speed> in {{ rollduration }} secondi. Rammus si ferma dopo aver colpito un nemico, infliggendo <magicDamage>{{ powerballdamage }} danni magici</magicDamage>, <status>respingendo</status> e <status>rallentando</status> i nemici vicini di un {{ slowpercent }}% per {{ slowduration }} secondo/i.<br /><recast>Rilancio</recast>: <PERSON><PERSON> interrompe in anticipo questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ slowpercent }}% -> {{ slowpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 10.5, 9, 7.5, 6], "cooldownBurn": "12/10.5/9/7.5/6", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [1000, 1000, 1000, 1000, 1000]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "1000"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerBall.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DefensiveBall<PERSON>url", "name": "Posizione difensiva a riccio", "description": "Rammus assume una posizione difensiva, aumentando notevolmente armatura e resistenza magica, e infligge danni di ritorno ai nemici che lo attaccano.", "tooltip": "Ram<PERSON> assume una posizione difensiva per {{ buffduration }} secondi, ottenendo <scaleArmor>{{ bonusarmortooltip }} armatura</scaleArmor> e <scaleMR>{{ bonusmrtooltip }} resistenza magica</scaleMR> e infliggendo <magicDamage>{{ returndamagecalc }} danni magici</magicDamage> ai nemici che lo attaccano.<br /><br /><recast>Rilancio</recast>: <PERSON><PERSON> interrompe in anticipo questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bonus armatura", "Bonus resistenza magica", "Percentuale armatura bonus", "Percentuale resistenza magica bonus"], "effect": ["{{ flatbonusarmor }} -> {{ flatbonusarmorNL }}", "{{ flatbonusmr }} -> {{ flatbonusmrNL }}", "{{ bonusarmorpercent*100.000000 }}% -> {{ bonusarmorpercentnl*100.000000 }}%", "{{ bonusmrpercent*100.000000 }}% -> {{ bonusmrpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DefensiveBallCurl.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PuncturingTaunt", "name": "Provocazione furiosa", "description": "Rammus provoca un campione nemico o un mostro in un'aggressione imprudente verso di lui.", "tooltip": "<PERSON><PERSON> <status>provoca</status> un campione nemico o un mostro per {{ duration }} secondi. I mostri subiscono <magicDamage>{{ monsterdamagecalc }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> ai mostri"], "effect": ["{{ duration }} -> {{ durationNL }}", "{{ monsterdamage }} -> {{ monsterdamageNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "PuncturingTaunt.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Tremors2", "name": "<PERSON><PERSON><PERSON> ascendente", "description": "<PERSON>mus salta in aria e si schianta su un'area bersaglio, infliggendo danni magici e rallentando i nemici. Se Rammus è in Ruota della distruzione, lancia anche in aria i nemici vicini al centro.", "tooltip": "Rammus salta in aria e si schianta su un'area, infliggendo <magicDamage>{{ initialdamagecalc }} danni magici</magicDamage> e <status>rallentando</status> del {{ slowamount*100 }}% per {{ slowduration }} secondi. Se usata durante <spellName>Ruota della distruzione</spellName>, i nemici al centro subiscono <magicDamage>{{ spell.powerball:powerballdamage }} danni magici</magicDamage> aggiuntivi e vengono <status>lanciati in aria</status> per {{ knockupduration }} secondi.<br /><br />Rammus genera poi nell'area {{ numberofpulses }} scosse di assestamento nell'arco di {{ buffduration }} secondi, ripetendo il <status>rallentamento</status>.<br /><br />La gittata di questa abilità aumenta con la <speed>velocità di movimento</speed> di Rammus.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Ricarica"], "effect": ["{{ initialdamage }} -> {{ initialdamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "Tremors2.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> spinato", "description": "<PERSON><PERSON> ottiene attacco fisico bonus che cresce con la sua armatura e la sua resistenza magica.", "image": {"full": "Armordillo_ScavengeArmor.png", "sprite": "passive3.png", "group": "passive", "x": 240, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}