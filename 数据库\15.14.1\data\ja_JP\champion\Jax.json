{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jax": {"id": "Jax", "key": "24", "name": "ジャックス", "title": "最強の武器使い", "image": {"full": "Jax.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "24000", "num": 0, "name": "default", "chromas": false}, {"id": "24001", "num": 1, "name": "マイティー ジャックス", "chromas": false}, {"id": "24002", "num": 2, "name": "アウトロー ジャックス", "chromas": false}, {"id": "24003", "num": 3, "name": "釣り師ジャックス", "chromas": false}, {"id": "24004", "num": 4, "name": "PAX ジャックス", "chromas": false}, {"id": "24005", "num": 5, "name": "ジャクシマス", "chromas": false}, {"id": "24006", "num": 6, "name": "テンプル ジャックス", "chromas": false}, {"id": "24007", "num": 7, "name": "無双ジャックス", "chromas": true}, {"id": "24008", "num": 8, "name": "SKT T1 ジャックス", "chromas": false}, {"id": "24012", "num": 12, "name": "番人ジャックス", "chromas": false}, {"id": "24013", "num": 13, "name": "神の杖ジャックス", "chromas": false}, {"id": "24014", "num": 14, "name": "三国武神ジャックス", "chromas": true}, {"id": "24020", "num": 20, "name": "覇者ジャックス", "chromas": false}, {"id": "24021", "num": 21, "name": "プレステージ覇者ジャックス", "chromas": false}, {"id": "24022", "num": 22, "name": "荘厳の天球ジャックス", "chromas": true}, {"id": "24032", "num": 32, "name": "ネオPAX ジャックス", "chromas": false}, {"id": "24033", "num": 33, "name": "PROJECT: Jax", "chromas": true}], "lore": "ジャックスはイカシアで現存する最後の武器使いだ。独特な武器を使う技術と辛辣な皮肉で彼の右に出る者はいない。傲慢さにより解放されたヴォイドによって故郷が破壊され、ジャックスは仲間とともに残ったわずかな土地を守り抜くことを誓った。魔法が世界に広まり、この眠れる脅威が再び騒動を巻き起こす中、ジャックスはイカシアの最後の灯りを武器に、共に戦ってくれる仲間を求めて、出会ったあらゆる戦士に戦いを挑んでその実力を測りながらヴァロランを旅している。", "blurb": "ジャックスはイカシアで現存する最後の武器使いだ。独特な武器を使う技術と辛辣な皮肉で彼の右に出る者はいない。傲慢さにより解放されたヴォイドによって故郷が破壊され、ジャックスは仲間とともに残ったわずかな土地を守り抜くことを誓った。魔法が世界に広まり、この眠れる脅威が再び騒動を巻き起こす中、ジャックスはイカシアの最後の灯りを武器に、共に戦ってくれる仲間を求めて、出会ったあらゆる戦士に戦いを挑んでその実力を測りながらヴァロランを旅している。", "allytips": ["ジャックスの「リープストライク」は、ワードを含む味方ユニットにも発動できる。", "戦闘から脱出したい時に使おう。", "ジャックスは「グインソー レイジブレード」や「ヘクステック ガンブレード」など", "攻撃力と魔力の両方を増加させるアイテムを持つことで絶大な力を発揮する。"], "enemytips": ["ジャックスには足を止めて戦うよりも、短時間で大ダメージを与えてすぐに距離をとる戦術で立ち向かおう。「アサルトアタック」を封じることで、ジャックスの攻撃力は大幅に減少する。", "ジャックスの「カウンターストライク」は短時間、あらゆる攻撃を回避し最後に周囲の敵ユニットをスタン状態にしてしまう。このスキルの発動中は無茶な攻撃を控えること。"], "tags": ["Fighter"], "partype": "マナ", "info": {"attack": 7, "defense": 5, "magic": 7, "difficulty": 5}, "stats": {"hp": 665, "hpperlevel": 103, "mp": 339, "mpperlevel": 52, "movespeed": 350, "armor": 36, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 68, "attackdamageperlevel": 4.25, "attackspeedperlevel": 3.4, "attackspeed": 0.638}, "spells": [{"id": "JaxQ", "name": "リープストライク", "description": "対象のユニットに跳躍し、敵ユニットの場合は武器で攻撃する。", "tooltip": "味方か敵のユニットまたはワードまで跳躍して、対象が敵の場合は<physicalDamage>{{ totaldamage }}の物理ダメージ</physicalDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "JaxQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "JaxW", "name": "パワーバッシュ", "description": "武器に力を込め、次の攻撃で追加ダメージを与える。", "tooltip": "武器に力を送り込み、次の通常攻撃または<spellName>「リープストライク」</spellName>が<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "JaxE", "name": "カウンターストライク", "description": "短時間、その卓越した戦闘技術を用いてあらゆる通常攻撃を回避した後、すばやく反撃に転じて、周囲の敵ユニットにスタン効果を付与する。", "tooltip": "最大{{ dodgeduration }}秒間、防御態勢を取って通常攻撃を回避し、範囲攻撃スキルから受けるダメージを{{ aoedamagereduction }}%軽減する。{{ dodgeduration }}秒経つか<recast>再発動</recast>すると、周囲の敵に<magicDamage>{{ totaldamage }} + 最大体力の{{ percenthealthdamage }}%の魔法ダメージ</magicDamage>を与え、{{ stunduration }}秒間<status>スタン</status>させる。<br /><br />このダメージは通常攻撃を回避するたびに{{ percentincreasedperdodge*100 }}%、最大<magicDamage>{{ maxdamage }} + 最大体力の{{ maxpercenthealthdamage }}%</magicDamage>まで増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [17, 15, 13, 11, 9], "cooldownBurn": "17/15/13/11/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "JaxE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "JaxR", "name": "ウェポングランドマスター", "description": "3回連続して通常攻撃を行うたびに、追加魔法ダメージを与える。また、このスキルを発動すると周囲にダメージを与え、決意を固めて短時間、物理防御と魔法防御が増加する。", "tooltip": "<spellPassive>自動効果:</spellPassive> {{ passivefallofftime }}秒以内に通常攻撃を3回行うたびに、<magicDamage>{{ onhitdamage }}の魔法ダメージ</magicDamage>を追加で与える。<br /><br /><spellActive>発動効果:</spellActive> 街灯を叩きつけて、周囲の敵に<magicDamage>{{ swingdamagetotal }}の魔法ダメージ</magicDamage>を与える。チャンピオン1体に命中した場合は{{ duration }}秒間、<scaleArmor>物理防御が{{ basearmor }}</scaleArmor>、<scaleMR>魔法防御が{{ basemr }}</scaleMR>増加する。命中したチャンピオンが1体増えるごとに、追加で<scaleArmor>物理防御が{{ bonusarmor }}</scaleArmor>、<scaleMR>魔法防御が{{ bonusmr }}</scaleMR>増加する。この効果時間中は、通常攻撃3回ごとではなく、2回ごとに<magicDamage>魔法ダメージ</magicDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["自動効果ダメージ", "発動効果ダメージ", "基本物理防御", "基本魔法防御", "追加で命中したチャンピオン1体ごとの物理防御", "追加で命中したチャンピオン1体ごとの魔法防御", "クールダウン"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ swingdamagebase }} -> {{ swingdamagebaseNL }}", "{{ baseresists }} -> {{ baseresistsNL }}", "{{ baseresists*0.600000 }} -> {{ baseresistsnl*0.600000 }}", "{{ resistsperextratarget }} -> {{ resistsperextratargetNL }}", "{{ resistsperextratarget*0.600000 }} -> {{ resistsperextratargetnl*0.600000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [260, 260, 260], "rangeBurn": "260", "image": {"full": "JaxR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "アサルトアタック", "description": "ジャックスは通常攻撃ごとにスタックが溜まり、そのスタック数に応じて攻撃速度が増加する。", "image": {"full": "Armsmaster_MasterOfArms.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}