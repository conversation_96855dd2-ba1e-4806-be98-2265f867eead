{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Poppy": {"id": "<PERSON><PERSON>", "key": "78", "name": "<PERSON><PERSON>", "title": "die Hüterin des Hammers", "image": {"full": "Poppy.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "78000", "num": 0, "name": "default", "chromas": false}, {"id": "78001", "num": 1, "name": "Noxus-<PERSON><PERSON>", "chromas": false}, {"id": "78002", "num": 2, "name": "Lollipoppy", "chromas": false}, {"id": "78003", "num": 3, "name": "Schmiede-Poppy", "chromas": false}, {"id": "78004", "num": 4, "name": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "chromas": false}, {"id": "78005", "num": 5, "name": "Kampfmontur-Poppy", "chromas": true}, {"id": "78006", "num": 6, "name": "Scharlachhammer-Poppy", "chromas": false}, {"id": "78007", "num": 7, "name": "Sternenwächterin Poppy", "chromas": false}, {"id": "78014", "num": 14, "name": "Schneekitz-Poppy", "chromas": false}, {"id": "78015", "num": 15, "name": "Hextech-Poppy", "chromas": false}, {"id": "78016", "num": 16, "name": "Astronauten-Poppy", "chromas": true}, {"id": "78024", "num": 24, "name": "Hexerei-Poppy", "chromas": true}, {"id": "78033", "num": 33, "name": "Konditorei-Poppy", "chromas": true}], "lore": "Runeterra leidet sicher nicht unter einem Mangel an tapferen Champions, doch gibt es wenige, die so hartn<PERSON>ckig sind wie Poppy. Mit dem legend<PERSON><PERSON>, einer Waffe doppelt so groß wie sie selbst, hat der entschlossene Yordle unzählige Jahre damit verbracht, im Verborgenen nach dem legendären „Helden von Demacia“ zu suchen, der der rechtmäßige Besitzer des Hammers sein soll. Bis es soweit ist, stürzt sie sich pflichtbewusst in die Schlacht und schlägt mit jedem wirbelnden Schlag die Gegner des Königreichs zurück.", "blurb": "Runeterra leidet sicher nicht unter einem Mangel an tapferen Champions, doch gibt es wenige, die so hartn<PERSON>ckig sind wie Poppy. Mit dem legend<PERSON><PERSON>, einer Waffe doppelt so groß wie sie selbst, hat der entschlossene Yordle unzählige Jahre...", "allytips": ["„Eiserne Botschafterin“ landet eher in der Nähe von Mauern. Versuche, dies mit „Heroischer Ansturm“ für dich zu nutzen.", "„Richtspruch der Hüterin“ kann sofort ausgelöst werden, um den Gegner sofort hochzuschießen. <PERSON><PERSON><PERSON> dies in Duellen."], "enemytips": ["Poppy kann <PERSON>ner in der Nähe mit „Unerschütterliche Präsenz“ vom Springen abhalten.", "<PERSON><PERSON>, ihren Hammer kreisen zu lassen, lädt sie ihren „Richtspruch der Hüterin“ auf.", "Du kannst auf <PERSON>py<PERSON> t<PERSON>en, um ihn ihr vorzuenthalten."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 7, "magic": 2, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 110, "mp": 280, "mpperlevel": 40, "movespeed": 345, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 4, "attackspeedperlevel": 2.5, "attackspeed": 0.658}, "spells": [{"id": "PoppyQ", "name": "Hammerschock", "description": "<PERSON><PERSON> schwingt ihren Hammer, verursacht Schaden und erschafft ein Gebiet, das Gegner verlangsamt und zeitverzögert explodiert.", "tooltip": "Poppy schlägt auf den Boden und verursacht <physicalDamage>normalen Schaden in Höhe von {{ basedamage }}</physicalDamage> plus <physicalDamage>{{ healthdamagepercent }}&nbsp;% des maximalen Lebens</physicalDamage> und hinterlässt eine instabile Fläche. <br /><br />Die instabile Fläche <status>verlangsamt</status> Gegner um {{ e3 }}&nbsp;% und bricht nach {{ e4 }}&nbsp;Sekunde auf, was erneut <physicalDamage>normalen Schaden in Höhe von {{ basedamage }}</physicalDamage> plus <physicalDamage>{{ healthdamagepercent }}&nbsp;% des maximalen Lebens</physicalDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit", "Kosten (@AbilityResourceName@)", "Obergrenze für Monster"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }}&nbsp;% -> {{ e3NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ e6 }} -> {{ e6NL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [30, 55, 80, 105, 130], [0.5, 0.5, 0.5, 0.5, 0.5], [20, 25, 30, 35, 40], [1, 1, 1, 1, 1], [9, 9, 9, 9, 9], [75, 105, 135, 165, 195], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/55/80/105/130", "0.5", "20/25/30/35/40", "1", "9", "75/105/135/165/195", "100", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "PoppyQ.png", "sprite": "spell10.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PoppyW", "name": "Unerschütterliche Präsenz", "description": "Poppy erhält passiv Rüstung und Magieresistenz. Diese Bon<PERSON> werden noch verstärkt, wenn Poppy über wenig Leben verfügt. Poppy kann „Unerschütterliche Präsenz“ aktivieren, um erhöhtes Lauftempo zu erhalten und gegnerische Sprints oder Sprünge in ihrer Umgebung aufzuhalten. Wurde sein Sprint oder Sprung aufgehalten, ist der Gegner verlangsamt und gehemmt.", "tooltip": "<spellPassive>Passiv:</spellPassive> Poppy erhält <scaleArmor>{{ bonusarmor }}&nbsp;Rüstung</scaleArmor> und <scaleMR>{{ bonusmr }}&nbsp;Magieresistenz</scaleMR>. Dieser Bonus verdoppelt sich, wenn Poppys Leben weniger als {{ passiveempoweredhealthpercent*100 }}&nbsp;% beträgt.<br /><br /><spellActive>Aktiv:</spellActive> Poppy erhält <speed>{{ e2 }}&nbsp;% Lauftempo</speed> und erschafft {{ e1 }}&nbsp;Sekunden lang ein Feld um sich herum, das gegnerische Sprünge aufhält. Gegner, die vom Feld aufgehalten werden, werden {{ groundingduration }}&nbsp;Sekunden lang <status>gehemmt</status>, um {{ slowamount*-100 }}&nbsp;% <status>verlangsamt</status> und erleiden zudem <magicDamage>{{ interruptdamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e5 }} -> {{ e5NL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [40, 40, 40, 40, 40], [10, 10, 10, 10, 10], [0.5, 0.5, 0.5, 0.5, 0.5], [70, 110, 150, 190, 230], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2", "40", "10", "0.5", "70/110/150/190/230", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "PoppyW.png", "sprite": "spell10.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PoppyE", "name": "Heroischer Ansturm", "description": "<PERSON><PERSON> stü<PERSON>t zu ihrem Ziel und reißt es mit sich. Falls das Ziel mit Terrain kollidiert, wird es betäubt.", "tooltip": "Poppy rammt e<PERSON>, fügt ihm <physicalDamage>{{ tackledamage }}&nbsp;normalen Schaden</physicalDamage> zu und stößt ihn zurück. Wenn Poppy den Gegner in Terrain stößt, wird dieser {{ e3 }}&nbsp;Se<PERSON>nden lang <status>betäubt</status> und erleidet zusätzlich <physicalDamage>{{ tackledamage }}&nbsp;normalen Schaden</physicalDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Anfänglicher Schaden", "Terrainschaden", "Betäubungsdauer:", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [40, 60, 80, 100, 120], [40, 60, 80, 100, 120], [1.6, 1.7, 1.8, 1.9, 2], [1800, 1800, 1800, 1800, 1800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/60/80/100/120", "40/60/80/100/120", "1.6/1.7/1.8/1.9/2", "1800", "400", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "PoppyE.png", "sprite": "spell10.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PoppyR", "name": "Richtspruch der Hüterin", "description": "Poppy kanalisiert einen Hammerschlag, der Gegner sehr weit weg schlägt.", "tooltip": "<charge>Aufladung:</charge> Poppy beginnt, ihren Hammer bis zu {{ channelmaxduration }}&nbsp;Sekunden lang aufzuladen und <status>verlangsamt</status> sich dabei um {{ selfslow }}&nbsp;%.<br /><br /><release>Auslösung:</release> Poppy rammt ihren Hammer in den Boden und löst eine Schockwelle aus, die dem ersten getroffenen Champion sowie umstehenden Gegnern <physicalDamage>{{ damage }}&nbsp;normalen Schaden</physicalDamage> zufügt und diese in Richtung ihres Nexus <status>zurückschleudert</status>. Während sie sich in der Luft befinden, können sie nicht anvisiert werden. Die Reichweite und die Distanz des <status>Zurückschleuderns</status> erhöhen sich mit der Aufladedauer.<br /><br />Ein nicht aufgeladener Schlag verursacht <physicalDamage>{{ halfdamage }}&nbsp;normalen Schaden</physicalDamage> und getroffene Ziele werden {{ knockupdurationsnap }}&nbsp;Sekunden lang <status>hochgeschleudert</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "PoppyR.png", "sprite": "spell10.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Eiserne Botschafterin", "description": "<PERSON><PERSON> schle<PERSON>rt ihren Faustschild, der von ihrem Ziel abprallt. <PERSON>e kann ihn einsammeln, um vorübergehend vor Schaden bewahrt zu werden.", "image": {"full": "Poppy_Passive.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}