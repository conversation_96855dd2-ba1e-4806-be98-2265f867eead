{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lux": {"id": "<PERSON><PERSON>", "key": "99", "name": "<PERSON><PERSON>", "title": "die Lady des Lichts", "image": {"full": "Lux.png", "sprite": "champion2.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "99000", "num": 0, "name": "default", "chromas": false}, {"id": "99001", "num": 1, "name": "Zauberin-Lux", "chromas": false}, {"id": "99002", "num": 2, "name": "Zauberdieb-Lux", "chromas": false}, {"id": "99003", "num": 3, "name": "Kommando-Lux", "chromas": false}, {"id": "99004", "num": 4, "name": "Imperiale Lux", "chromas": false}, {"id": "99005", "num": 5, "name": "Stahllegion-Lux", "chromas": false}, {"id": "99006", "num": 6, "name": "Sternenwächterin Lux", "chromas": false}, {"id": "99007", "num": 7, "name": "Lux der Elemente", "chromas": false}, {"id": "99008", "num": 8, "name": "Mondkaiserin Lux", "chromas": true}, {"id": "99014", "num": 14, "name": "Pyjamawächterin Lux", "chromas": false}, {"id": "99015", "num": 15, "name": "Academia Certaminis-Lux", "chromas": false}, {"id": "99016", "num": 16, "name": "Academia Certaminis-Lux (Prestige)", "chromas": false}, {"id": "99017", "num": 17, "name": "Kosmische Vernichterin Lux", "chromas": false}, {"id": "99018", "num": 18, "name": "Kosmische Lux", "chromas": false}, {"id": "99019", "num": 19, "name": "Weltraum-Groove-Lux", "chromas": true}, {"id": "99029", "num": 29, "name": "Porzellan-Lux", "chromas": true}, {"id": "99038", "num": 38, "name": "Soul Fighter Lux", "chromas": true}, {"id": "99039", "num": 39, "name": "Academia Certaminis-Lux (Prestige 2022)", "chromas": false}, {"id": "99040", "num": 40, "name": "Porzellan-Lux (Prestige)", "chromas": false}, {"id": "99042", "num": 42, "name": "Elysische Lux", "chromas": true}, {"id": "99061", "num": 61, "name": "Feenhof-Lux", "chromas": true}, {"id": "99070", "num": 70, "name": "Seelenblumen-Lux (Prestige)", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> stammt aus Demacia, einem isolierten Reich, in dem man Magie mit Angst und Misstrauen begegnet. Da sie das Licht nach ihrem Willen beugen konnte, musste sie in jungen Jahren ihre Fähigkeiten geheim halten, um nicht ins Exil geschickt zu werden, und die Ehre und den Ruf ihrer Familie zu schützen. Doch dank ihres Optimismus und ihrer Anpassungsfähigkeit schloss Lux Frieden mit ihren Talenten und setzt sie nun heimlich im Dienste ihres Heimatlandes ein.", "blurb": "<PERSON><PERSON><PERSON> stammt aus Demacia, einem isolierten Reich, in dem man Magie mit Angst und Misstrauen begegnet. Da sie das Licht nach ihrem Willen beugen konnte, musste sie in jungen Jahren ihre Fähigkeiten geheim halten, um nicht ins Exil geschickt...", "allytips": ["Lux kann hervorragend mehrere Gegner festhalten. Versuche „Schillernde Singularität“ einzusetzen, um Gegner am Vorstoß oder an der Flucht zu hindern.", "Solltest du Probleme mit dem Zielen für „Prismatische Barriere“ haben, denke da<PERSON>, dass sie immer wieder zu dir zurückkehrt. Stelle dich einfach so, dass sich deine Verbündeten zwischen dir und der Welle befinden.", "„Schillernde Singularität“ eignet sich hervorragend, um damit die Gegend auszukundschaften. Benutze es bei hohem Gras, bevor du einen Blick hinein wirfst."], "enemytips": ["Lux kann hervorragend mehrere Gegner festhalten. Versuche dein Team zu verteilen und greife dann aus verschiedenen Richtungen an, damit sie nicht alle gleichzeitig erfassen kann.", "Achte auf Lux' „Finales Funkeln“, wenn du nur noch wenige Lebenspunkte besitzt. Ein roter Zielstrahl wird dem vorausgehen, also versuche diesem auszuweichen."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 5}, "stats": {"hp": 580, "hpperlevel": 99, "mp": 480, "mpperlevel": 23.5, "movespeed": 330, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 3.3, "attackspeedperlevel": 3, "attackspeed": 0.669}, "spells": [{"id": "LuxLightBinding", "name": "Lichtfessel", "description": "Lux schleudert eine Kugel aus Licht, die bis zu 2 gegnerische Einheiten festhält und diesen Schaden zufügt.", "tooltip": "Lux feuert eine Kugel aus Licht, welche die ersten beiden Gegner {{ e3 }}&nbsp;Se<PERSON>nden lang <status>festhält</status> und jeweils <magicDamage>{{ totaldamagett }}&nbsp;magischen Schaden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [80, 120, 160, 200, 240], [50, 50, 50, 50, 50], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/120/160/200/240", "50", "2", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1175, 1175, 1175, 1175, 1175], "rangeBurn": "1175", "image": {"full": "LuxLightBinding.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxPrismaticWave", "name": "Prismatische Barriere", "description": "Lux wirft ihren Zauberstab und krümmt so das Licht um alle getroffenen verbündeten Ziele, wodurch diese vor gegnerischem Schaden geschützt werden.", "tooltip": "Lux wirft ihren Stab und verleiht allen Verbündeten, die er berührt, {{ e3 }}&nbsp;Sekunden lang einen <shield><PERSON><PERSON><PERSON></shield> in <PERSON><PERSON><PERSON> von {{ totalshieldtt }}. Danach kehrt der Stab zurück und gewährt dabei einen genauso starken <shield><PERSON><PERSON><PERSON></shield>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildstärke", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [2, 4, 6, 8, 10], [40, 55, 70, 85, 100], [2.5, 2.5, 2.5, 2.5, 2.5], [100, 100, 100, 100, 100], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2/4/6/8/10", "40/55/70/85/100", "2.5", "100", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "LuxPrismaticWave.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxLightStrikeKugel", "name": "Schillernde Singularität", "description": "Schleudert eine Anomalie aus verwirbeltem Licht in ein Gebiet, das Gegner in der Nähe verlangsamt. Lux kann sie sprengen, um an Gegnern im Wirkbereich Schaden zu verursachen.", "tooltip": "Lux erschafft eine Lichtzone, die um {{ e1 }}&nbsp;% <status>verlangsamt</status> und die Umgebung aufdeckt. Nach {{ e3 }}&nbsp;Sekunden oder bei der <recast>Reaktivierung</recast> dieser Fähigkeit explodiert sie, verursacht <magicDamage>{{ totaldamagett }}&nbsp;magischen Schaden</magicDamage> und <status>verlangsamt</status> um zusätzlich {{ slowlingerduration }}&nbsp;Sekunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ slowpercent }}&nbsp;% -> {{ slowpercentNL }}&nbsp;%", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [65, 115, 165, 215, 265], [5, 5, 5, 5, 5], [310, 310, 310, 310, 310], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "25/30/35/40/45", "65/115/165/215/265", "5", "310", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "LuxLightStrikeKugel.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "LuxR", "name": "Finales Funkeln", "description": "Nachdem Lux genug Energie aufgeladen hat, setzt sie einen Lichtstrahl frei, der allen Zielen im Wirkbereich Schaden zufügt. Außerdem wird Lux' passive Fähigkeit ausgelöst und die Dauer des „Erleuchtung“-Debuffs erneuert.", "tooltip": "Lux feuert einen blendenden Lichtstrahl ab, der allen Gegnern in einer Reihe <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 50, 40], "cooldownBurn": "60/50/40", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3340, 3340, 3340], "rangeBurn": "3340", "image": {"full": "LuxR.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Erleuchtung", "description": "Lu<PERSON>' <PERSON><PERSON><PERSON>za<PERSON><PERSON> laden das Ziel einige Sekunden lang mit Lichtenergie auf. Ihr nächster Angriff entzündet diese Energie und fügt so dem Ziel abhäng<PERSON> von Lux' Stufe zusätzlichen magischen Schaden zu.", "image": {"full": "LuxIlluminatingFraulein.png", "sprite": "passive2.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}