{"type": "summoner", "version": "15.14.1", "data": {"SummonerBarrier": {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "الحاجز", "description": "تكسب درعًا مؤقتًا.", "tooltip": "تكسب <shield>{{ shieldstrength }} درع ضرر</shield> لمدة {{ shieldduration }}ث.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "21", "summonerLevel": 4, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [1200], "rangeBurn": "1200", "image": {"full": "SummonerBarrier.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerBoost": {"id": "SummonerBoost", "name": "التطهير", "description": "تزيل كل المعطلات (باستنثاء القمع والإطاحة في الهواء) ومعطلات تعويذة المستدعي التي يخضع بطلك لتأثيرها، وتمنح التماسك.", "tooltip": "تزيل كل مثبطات إعاقة التحكم (باستثناء <keyword>القمع</keyword> و <keyword>الإطاحة في الهواء</keyword>) ومثبطات تعويذة المستدعي وتمنح تماسكًا بنسبة {{ tenacityvalue*100 }}% لمدة {{ tenacityduration }}ث.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "1", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerBoost.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerCherryFlash": {"id": "SummonerCherryFlash", "name": "الانتقال الخاطف", "description": "تنقل بطلك بسرعة لمسافة قصيرة باتجاه موقع المؤشر.", "tooltip": "تنقل بطلك بسرعة لمسافة قصيرة باتجاه موقع المؤشر.<br /><br />لا يمكن إلقاؤها مرة أخرى لجولة كاملة أخرى <rules>(تتكون الجولة من مرحلتي الشراء والقتال).</rules>", "maxrank": 1, "cooldown": [0.25], "cooldownBurn": "0.25", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2202", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerCherryFlash.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerCherryHold": {"id": "SummonerCherryHold", "name": "اهرب!", "description": "اكتساب زيادة قصيرة في سرعة الحركة، تزداد عند الركض بعيدًا عن أبطال الأعداء.", "tooltip": "<keywordMajor>خانة السحر النشط:</keywordMajor> الزيادات التي تمنح سحر المستدعي ستحل محل هذه الخانة.<br /><br />اكتسب <moveSpeed>{{ basems*100 }}% سرعة حركة</moveSpeed> لمدة {{ duration }} من الثواني، تزداد بنسبة {{ bonusmsperenemybehind*100 }}% لكل عدو خلفك.", "maxrank": 1, "cooldown": [45], "cooldownBurn": "45", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "2201", "summonerLevel": 1, "modes": ["CHERRY"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerCherryHold.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerDot": {"id": "SummonerDot", "name": "الإشعال", "description": "تلحق ضررًا حقيقيًا بمرور الوقت ببطل العدو المستهدف وتقلص تأثيرات العلاج لديه طيلة المدة.", "tooltip": "تلحق <trueDamage>{{ tooltiptruedamagecalculation }} ضرر حقيقي</trueDamage> بالبطل المستهدف خلال 5 ثوان، وتسبب <keyword>{{ grievousamount*100 }}% من الجراح الخطيرة</keyword> طيلة تلك المدة.<br /><br /><keyword>جراح</keyword>: تقلل فعالية العلاج وتأثيرات التجدد.", "maxrank": 1, "cooldown": [180], "cooldownBurn": "180", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "14", "summonerLevel": 9, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerDot.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerExhaust": {"id": "SummonerExhaust", "name": "الإجهاد", "description": "تبطئ بطل العدو المستهدف وتقلص الضرر الذي يلحقه.", "tooltip": "<keyword>تبطئ</keyword> بطل العدو المستهدف بنسبة {{ slow }}%، وتقلص الضرر الذي يلحقه بنسبة {{ damagereduction }}% لمدة {{ debuffduration }}ث.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "3", "summonerLevel": 4, "modes": ["ARAM", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "WIPMODEWIP4", "WIPMODEWIP", "SWIFTPLAY", "DOOMBOTSTEEMO", "TUTORIAL", "ULTBOOK", "NEXUSBLITZ", "WIPMODEWIP3", "URF", "WIPMODEWIP5", "ARSR"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [650], "rangeBurn": "650", "image": {"full": "SummonerExhaust.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerFlash": {"id": "SummonerFlash", "name": "الانتقال الخاطف", "description": "تنقلك بسرعة لمسافة قصيرة باتجاه مؤشرك.", "tooltip": "تنقلك بسرعة لمسافة قصيرة باتجاه مؤشرك.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [400], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "400", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "4", "summonerLevel": 7, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "ULTBOOK", "URF", "NEXUSBLITZ", "FIRSTBLOOD", "ONEFORALL", "ARAM", "SNOWURF", "TUTORIAL", "ASSASSINATE", "ARSR", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [425], "rangeBurn": "425", "image": {"full": "SummonerFlash.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerHaste": {"id": "SummonerHaste", "name": "الشبح", "description": "تكسب سرعة الحركة وتتجاهل الاصطدام بالوحدات طوال المدة.", "tooltip": "تكسب <speed>{{ movespeedmod }} سرعة حركة</speed> وتصبح <keyword>شبحيًا</keyword> لمدة {{ duration }}ث.<br /><br /><keyword>تأثير شبحي</keyword>: تتجاهل الاصطدام بالوحدات الأخرى.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "6", "summonerLevel": 1, "modes": ["ARAM", "ASSASSINATE", "CLASSIC", "PRACTICETOOL", "ONEFORALL", "SWIFTPLAY", "BRAWL", "TUTORIAL_MODULE_1", "DOOMBOTSTEEMO", "TUTORIAL", "TUTORIAL_MODULE_2", "FIRSTBLOOD", "ULTBOOK", "NEXUSBLITZ", "URF", "ARSR"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerHaste.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerHeal": {"id": "SummonerHeal", "name": "العلاج", "description": "تستعيد الصحة وتمنح سرعة حركة لك وللبطل الحليف المستهدف.", "tooltip": "تستعيد <healing>{{ totalheal }} صحة</healing> وتمنح <speed>{{ movespeed*100 }}% سرعة حركة</speed> لمدة {{ movespeedduration }}ث لبطلك وللبطل الحليف المستهدف.<br /><br /><rules>في حال إلقاء التعويذة دون تحديد هدف، يتم إلقاؤها على البطل صاحب الجراح الأخطر في مداها.<br />ويتقلص هذا العلاج إلى النصف للوحدات التي تلقت تأثير علاج المستدعي مؤخرًا.</rules>", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "7", "summonerLevel": 1, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "TUTORIAL_MODULE_2", "ULTBOOK", "URF", "NEXUSBLITZ", "ONEFORALL", "ARAM", "TUTORIAL", "ASSASSINATE", "ARSR", "TUTORIAL_MODULE_1", "WIPMODEWIP", "WIPMODEWIP3", "WIPMODEWIP4", "BRAWL", "WIPMODEWIP5"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [875], "rangeBurn": "875", "image": {"full": "SummonerHeal.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerMana": {"id": "Summoner<PERSON>ana", "name": "الوضوح", "description": "تستعيد المانا لك وللبطل الحليف.", "tooltip": "تستعيد {{ e1 }}% من حد المانا الأقصى لدى بطلك و {{ e2 }}% للحلفاء المحيطين.", "maxrank": 1, "cooldown": [240], "cooldownBurn": "240", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [50], [25], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "50", "25", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "13", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [600], "rangeBurn": "600", "image": {"full": "SummonerMana.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerPoroRecall": {"id": "SummonerPoroRecall", "name": "من أجل الملك!", "description": "انتقل بسرعة إلى جانب ملك البورو.", "tooltip": "<span class=\"colorFFE076\">تأثير كامن:</span> تؤدي إصابة بطل معاد بمخلوق بورو إلى منح فريقك علامة بورو. ولدى بلوغ 10 علامات بورو، يستدعي فريقك ملك البورو للقتال إلى جانبه. وأثناء تفعيل ملك البورو، لا يمكن إحراز أي علامة بورو من قبل أي فريق.<br /><br /><span class=\"colorFFE076\">تأثير نشط:</span> اندفع بسرعة إلى جانب ملك البورو. يمكن إلقاؤها فقط أثناء استدعاء ملك البورو لدى فريقك. <br /><br /><i><span class=\"colorFDD017\">''تجر مخلوقات البورو أوتار القلب. أما بقيتكم فتأتي فقط من أجل الركوب.''</span></i></mainText>", "maxrank": 1, "cooldown": [10], "cooldownBurn": "10", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [3000], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "3000", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "30", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [200], "rangeBurn": "200", "image": {"full": "SummonerPoroRecall.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerPoroThrow": {"id": "SummonerPoroThrow", "name": "رمية البورو", "description": "ألق مخلوق بورو على أعدائك. فإن أصاب الهدف، يمكنك الانتقال بسرعة إلى هدفك لإكمال الهجوم.", "tooltip": "ارم مخلوق بورو لمسافة بعيدة، مما يلحق {{ f2 }} ضرر حقيقي بأول وحدة معادية مصابة، ويمنح <span class=\"coloree91d7\">رؤية حقيقية</span> للهدف.<br /><br />يمكن إعادة إلقاء هذه القدرة لمدة 3 ثوان، في حال إصابتها لأحد الأعداء، من أجل الاندفاع نحو الهدف المصاب، مما يلحق {{ f2 }} ضرر حقيقي إضافي، ويقلص فترة التبريد لرمية البورو التالية بمقدار {{ e4 }}ث.<br /><br />ولا يمكن صد مخلوقات البورو من قبل دروع التعويذة أو جدران الرياح لأنها من الحيوانات، وليس تعويذات!<br /><br /><i><span class=\"colorFDD017\">''تعتبر مخلوقات البورو نموذجًا لديناميكية الهواء في رونتيرا''</span></i></mainText>", "maxrank": 1, "cooldown": [20], "cooldownBurn": "20", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [20], [10], [3], [5], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "20", "10", "3", "5", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "31", "summonerLevel": 1, "modes": ["KINGPORO"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [2500], "rangeBurn": "2500", "image": {"full": "SummonerPoroThrow.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerSmite": {"id": "SummonerSmite", "name": "صاعقة السماء", "description": "تلحق ضررًا حقيقيًا بوحش أو تابع.", "tooltip": "تلحق <trueDamage>{{ smitebasedamage }} ضرر حقيقي</trueDamage> بالوحش الكبير أو تابع المسار المستهدف.<br /><br />تلحق <trueDamage>{{ firstpvpdamage }} ضرر حقيقي</trueDamage> بالحيوانات المرافقة للأبطال.", "maxrank": 1, "cooldown": [15], "cooldownBurn": "15", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [15], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "15", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "11", "summonerLevel": 3, "modes": ["DOOMBOTSTEEMO", "PRACTICETOOL", "CLASSIC", "SWIFTPLAY", "URF", "NEXUSBLITZ", "ONEFORALL", "TUTORIAL", "ARSR"], "costType": "بلا تكاليف", "maxammo": "2", "range": [500], "rangeBurn": "500", "image": {"full": "SummonerSmite.png", "sprite": "spell0.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerSnowURFSnowball_Mark": {"id": "SummonerSnowURFSnowball_Mark", "name": "العلامة", "description": "ترمي كرة ثلج في خط مستقيم على أعدائك. إذا أصابت أحد الأعداء، يصبح موسومًا، مما يمنحك رؤية حقيقية، وعندها يستطيع بطلك الانتقال بسرعة إلى الهدف الموسوم لمتابعة الهجوم.", "tooltip": "ترمي كرة ثلج لمسافة بعيدة، ملحقًا {{ tooltipdamagetotal }} من الضرر الحقيقي بأول وحدة معادية تصاب بها، كما تمنح <span class=\"coloree91d7\">رؤية حقيقية</span> للهدف. إذا أصابت أحد الأعداء، يمكن إلقاء هذه القدرة من جديد لمدة {{ e3 }} ثانية كي تندفع نحو الوحدة الموسومة، حيث تلحق زيادة {{ tooltipdamagetotal }} من الضرر الحقيقي. يقلص الاندفاع نحو الهدف فترة تبريد الوسمة بنسبة {{ e4 }}%.<br /><br /><span class=\"colorFFFF00\">لا يمكن إيقاف مقذوفات الوسمة من قبل دروع التعويذة أو تأثير تخفيف المقذوفات.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "39", "summonerLevel": 6, "modes": ["SNOWURF"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [8000], "rangeBurn": "8000", "image": {"full": "SummonerSnowURFSnowball_Mark.png", "sprite": "spell0.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerSnowball": {"id": "SummonerSnowball", "name": "العلامة", "description": "ترمي كرة ثلج في خط مستقيم على أعدائك. إذا أصابت أحد الأعداء، يصبح موسومًا، مما يمنحك رؤية حقيقية، وعندها يستطيع بطلك الانتقال بسرعة إلى الهدف الموسوم لمتابعة الهجوم.", "tooltip": "ترمي كرة ثلج لمسافة بعيدة، ملحقًا {{ tooltipdamagetotal }} من الضرر الحقيقي بأول وحدة معادية تصاب بها، كما تمنح <span class=\"coloree91d7\">رؤية حقيقية</span> للهدف. إذا أصابت أحد الأعداء، يمكن إلقاء هذه القدرة من جديد لمدة {{ e3 }} ثانية كي تندفع نحو الوحدة الموسومة، حيث تلحق زيادة {{ tooltipdamagetotal }} من الضرر الحقيقي. يقلص الاندفاع نحو الهدف فترة تبريد الوسمة بنسبة {{ e4 }}%.<br /><br /><span class=\"colorFFFF00\">لا يمكن إيقاف مقذوفات الوسمة من قبل دروع التعويذة أو تأثير تخفيف المقذوفات.</span>", "maxrank": 1, "cooldown": [80], "cooldownBurn": "80", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [10], [5], [3], [25], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "10", "5", "3", "25", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "32", "summonerLevel": 6, "modes": ["ARAM", "FIRSTBLOOD"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [1600], "rangeBurn": "1600", "image": {"full": "SummonerSnowball.png", "sprite": "spell0.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "SummonerTeleport": {"id": "SummonerTeleport", "name": "الانتقال السريع", "description": "بعد فترة تحضير قصيرة، تصبح غير قابل للاستهداف وتنتقل إلى وحدة حليفة. تتم الترقية إلى الانتقال السريع الجامح، مما يزيد من سرعة الانتقال بشكل كبير. ", "tooltip": "بعد التحضير لمدة {{ channelduration }}ث، تصبح <keyword>غير قابل للاستهداف</keyword> وتنتقل إلى البناء أو التابع أو الكاشف الحليف المنشود. <br /><br />تتم ترقيتها إلى الانتقال السريع الجامح عند مرور {{ upgrademinute }}د، مما يزيد من سرعة الانتقال بشكل كبير.", "maxrank": 1, "cooldown": [300], "cooldownBurn": "300", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "12", "summonerLevel": 7, "modes": ["CLASSIC", "ONEFORALL", "ASSASSINATE", "ULTBOOK", "PRACTICETOOL", "TUTORIAL", "ARSR", "WIPMODEWIP4", "DOOMBOTSTEEMO", "CS", "SWIFTPLAY"], "costType": "بلا تكاليف", "maxammo": "-1", "range": [25000], "rangeBurn": "25000", "image": {"full": "SummonerTeleport.png", "sprite": "spell0.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "بلا تكاليف"}, "Summoner_UltBookPlaceholder": {"id": "Summoner_UltBookPlaceholder", "name": "Placeholder", "description": "سيتم استبدال هذه الخانة بقدرة خارقة أخرى لبطل تم تحديدها في بداية اللعبة. سيكون لديك 30 ثانية لتحديد قدرة خارقة. استعد!", "tooltip": "سيحل محلها اختيارك لتعويذة المستدعي الخارقة الخاصة بك.{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "54", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookPlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}, "Summoner_UltBookSmitePlaceholder": {"id": "Summoner_UltBookSmitePlaceholder", "name": "Placeholder وهجوم صاعقة السماء", "description": "ستُستبدل قدرة خارقة أخرى لبطل بهذه الخانة وستكتسب هجوم صاعقة السماء. سيكون لديك 30 ثانية لتحديد قدرة خارقة. استعد!", "tooltip": "سيحل محلها تعويذة المستدعي الخارقة الخاصة بك.<br /><br />تكسب هجوم صاعقة السماء. سيعدم هجوم صاعقة السماء وحوش المحفزات والوحوش الخارقة ومخلوقات سكاتل كراب عند الهجوم عليها.<br /><br /><attention>ليس لهجوم صاعقة السماء فترة تبريد.</attention>{{ spellmodifierdescriptionappend }}", "maxrank": 1, "cooldown": [0], "cooldownBurn": "0", "cost": [0], "costBurn": "0", "datavalues": {}, "effect": [null, [0], [0], [0], [0], [0], [0], [0], [0], [0], [0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "key": "55", "summonerLevel": 1, "modes": ["ULTBOOK"], "costType": "&nbsp;", "maxammo": "-1", "range": [400], "rangeBurn": "400", "image": {"full": "Summoner_UltBookSmitePlaceholder.png", "sprite": "spell0.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "&nbsp;"}}}