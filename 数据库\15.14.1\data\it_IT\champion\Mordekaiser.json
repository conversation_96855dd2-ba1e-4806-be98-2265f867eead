{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mordekaiser": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "82", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "il revenant di ferro", "image": {"full": "Mordekaiser.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "82000", "num": 0, "name": "default", "chromas": false}, {"id": "82001", "num": 1, "name": "Mordekaiser Cavaliere del Drago", "chromas": false}, {"id": "82002", "num": 2, "name": "Mordekaiser Infernale", "chromas": false}, {"id": "82003", "num": 3, "name": "Mordekaiser dei Pentakill", "chromas": false}, {"id": "82004", "num": 4, "name": "Lord <PERSON>", "chromas": false}, {"id": "82005", "num": 5, "name": "Mordekaiser Re di Fiori", "chromas": false}, {"id": "82006", "num": 6, "name": "Mordekaiser Stella Oscura", "chromas": true}, {"id": "82013", "num": 13, "name": "PROGETTO: <PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "82023", "num": 23, "name": "Mordekaiser Pentakill III: Lost Chapter", "chromas": true}, {"id": "82032", "num": 32, "name": "Mordekaiser Mezzogiorno di Fuoco", "chromas": true}, {"id": "82042", "num": 42, "name": "Mordekaiser Guardiatombe cinereo", "chromas": true}, {"id": "82044", "num": 44, "name": "Mordekaiser Dio Antico", "chromas": true}, {"id": "82054", "num": 54, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Ucciso due volte e nato tre volte, <PERSON>rdekaiser è un brutale signore della guerra di un'epoca dimenticata che usa il suo potere da negromante per condannare le anime alla servitù eterna. Pochi ricordano le sue antiche conquiste e conoscono la vera portata della sua potenza. Le anime antiche che sanno chi è temono il giorno del suo ritorno, perché potrebbe dichiararsi re dei vivi e dei morti.", "blurb": "Ucciso due volte e nato tre volte, Mordekaiser è un brutale signore della guerra di un'epoca dimenticata che usa il suo potere da negromante per condannare le anime alla servitù eterna. Pochi ricordano le sue antiche conquiste e conoscono la vera...", "allytips": ["L'attacco è la tua difesa. Continua a combattere per accumulare scudi sempre più grandi con Incrollabile.", "Colpire più campioni con la stessa abilità può attivare più rapidamente Ascesa delle Tenebre.", "Usa Regno della Morte su un nemico con poca salute per garantire l'uccisione e tenere le sue statistiche per il resto del combattimento a squadre."], "enemytips": ["Mordekaiser crea una potente aura che infligge danni mentre combatte contro i campioni, quindi tieniti alla dovuta distanza.", "I danni che infligge possono essere convertiti in un grande scudo e consumati per ottenere salute.", "Regno della Morte ti separa completamente dai compagni di squadra. Cerca di conservare le abilità per la mobilità per fuggire da Mordekaiser quando sei al suo interno."], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 645, "hpperlevel": 104, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 37, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 4, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "MordekaiserQ", "name": "Annientamento", "description": "Mordekaiser colpisce il terreno con la sua mazza infliggendo danni a ogni nemico colpito. I danni aumentano quando si colpisce un singolo nemico.", "tooltip": "Mordekaiser colpisce il terreno con Crepuscolo infliggendo <magicDamage>{{ qdamage }} danni magici</magicDamage>, che aumentano a <magicDamage>{{ empowereddamagetooltip }}</magicDamage> se colpisce un solo nemico.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> is<PERSON>", "Ricarica"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ isolationscalar*100.000000 }}% -> {{ isolationscalarnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "MordekaiserQ.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "MordekaiserW", "name": "Incrollabile", "description": "Mordekaiser accumula i danni che infligge e subisce per creare uno scudo. <PERSON><PERSON>ò consumare lo scudo per curarsi.", "tooltip": "<passive>Passiva:</passive> Mordekaiser conserva {{ damageconversion*100 }}% danni inflitti e {{ damagetakenconversion*100 }}% danni subiti.<br /><br /><active>Attiva:</active> Mordekaiser converte i danni conservati in uno <shield>scudo</shield>. Può <recast>rilanciare</recast> questa abilità per recuperare <healing>salute pari a {{ healingpercent*100 }}% dello scudo rimanente</healing>.<br /><br />Scudo minimo: <shield>{{ minhealthtooltip }}</shield><br />Scudo massimo: <shield>{{ maxhealthtooltip }}</shield>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Percentuale guarigione", "Ricarica"], "effect": ["{{ healingpercent*100.000000 }}% -> {{ healingpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MordekaiserW.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "MordekaiserE", "name": "Stretta mortale", "description": "Mordekaiser attira i nemici in un'area.", "tooltip": "<spellPassive>Passiva:</spellPassive> Mordekaiser ottiene il {{ magicpen*100 }}% di penetrazione magica.<br /><br /><spellActive>Attiva:</spellActive> Mordekaiser attira i nemici verso di sé, infliggendo <magicDamage>{{ totaldamage }} danni magici.</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Percentuale penetrazione magica", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ magicpen*100.000000 }}% -> {{ magicpennl*100.000000 }}%", "{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "MordekaiserE.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "MordekaiserR", "name": "Regno della Morte", "description": "Mordekaiser trascina la sua vittima in un'altra dimensione e ruba parte delle sue statistiche. Se la uccide, tiene le statistiche rubate fino alla rigenerazione della vittima.", "tooltip": "Mordekaiser porta con sé un campione nel Regno della Morte per {{ spiritrealmduration }} secondi, rubando il {{ statstealpercentscalar*100 }}% delle sue statistiche principali per la durata.<br /><br />Se Mordekaiser uccide il nemico nel Regno della Morte consuma la sua anima e mantiene le statistiche rubate fino alla rigenerazione del bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "MordekaiserR.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Ascesa delle Tenebre", "description": "Mordekaiser ottiene una potente aura di danni e velocità di movimento dopo aver mandato a segno 3 attacchi o abilità contro campioni o mostri.", "image": {"full": "MordekaiserPassive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}