{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kaisa": {"id": "<PERSON><PERSON>", "key": "145", "name": "Kai'Sa", "title": "Figlia del vuoto", "image": {"full": "Kaisa.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "145000", "num": 0, "name": "default", "chromas": false}, {"id": "145001", "num": 1, "name": "Kai'Sa Angelo dei proiettili", "chromas": true}, {"id": "145014", "num": 14, "name": "Kai'Sa K/DA", "chromas": false}, {"id": "145015", "num": 15, "name": "Kai'Sa K/DA (edizione prestigio)", "chromas": false}, {"id": "145016", "num": 16, "name": "Kai'Sa iG", "chromas": false}, {"id": "145017", "num": 17, "name": "Kai'Sa Arcade", "chromas": false}, {"id": "145026", "num": 26, "name": "Kai'Sa K/DA ALL OUT", "chromas": false}, {"id": "145027", "num": 27, "name": "Kai'Sa K/DA ALL OUT (edizione prestigio)", "chromas": false}, {"id": "145029", "num": 29, "name": "Kai'Sa Drago della Laguna", "chromas": false}, {"id": "145039", "num": 39, "name": "Kai'Sa K/DA (edizione prestigio 2022)", "chromas": false}, {"id": "145040", "num": 40, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "145048", "num": 48, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "145059", "num": 59, "name": "<PERSON><PERSON><PERSON>uggit<PERSON> celeste", "chromas": false}, {"id": "145069", "num": 69, "name": "Kai<PERSON><PERSON>", "chromas": false}, {"id": "145070", "num": 70, "name": "<PERSON><PERSON><PERSON> Trascesa", "chromas": false}, {"id": "145071", "num": 71, "name": "<PERSON><PERSON><PERSON>", "chromas": false}], "lore": "Reclamata dal Vuoto quando era soltanto una bambina, Kai'<PERSON> è riuscita a sopravvivere con la sua incredibile tenacia e la sua forza di volontà. L'esperienza l'ha resa una cacciatrice letale, nonché araldo di un destino che molti non vorrebbero mai vedere compiuto. Essendo entrata in una difficile simbiosi con un carapace del Vuoto, dovrà presto decidere se perdonare i mortali che la considerano un mostro, per affrontare insieme l'oscurità... o se dimenticarli, mentre il Vuoto consuma tutto ciò che si è lasciata alle spalle.", "blurb": "Reclamata dal Vuoto quando era soltanto una bambina, <PERSON>'<PERSON> è riuscita a sopravvivere con la sua incredibile tenacia e la sua forza di volontà. L'esperienza l'ha resa una cacciatrice letale, nonché araldo di un destino che molti non vorrebbero mai...", "allytips": ["Prova a isolare i carry nemici per colpirli con Pioggia di Icathia.", "Collabora con i tuoi compagni per preparare la suprema e ottimizzare i danni con la passiva.", "Assicurati di acquistare oggetti che evolveranno almeno 1 o 2 delle tue abilità."], "enemytips": ["Kai'Sa è bravissima ad eliminare i nemici isolati. Contro di lei è meglio stare uniti.", "<PERSON>'Sa è molto vulnerabile ai colpi a distanza di maghi e carry a lunga gittata.", "Assicurati di piazzare lumi nei tuoi punti ciechi per vedere arrivare <PERSON>Sa."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 345, "mpperlevel": 40, "movespeed": 335, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.6, "attackspeedperlevel": 1.8, "attackspeed": 0.644}, "spells": [{"id": "KaisaQ", "name": "Pioggia di Icathia", "description": "Kai'Sa spara uno sciame di missili che vanno alla ricerca dei bersagli nelle vicinanze.<br><br>Arm<PERSON> vivente: Pioggia di Icathia viene potenziata e lancia più proiettili.", "tooltip": "Kai'Sa lancia {{ e2 }} missili che si dividono tra i nemici nelle vicinanze, infliggendo ciascuno <physicalDamage>{{ totalindividualmissiledamage }} danni fisici</physicalDamage>, fino a un massimo di {{ maxdamagedisplay }}. I proiettili che colpiscono ripetutamente campioni o mostri infliggono un {{ extrahitreduction*100 }}% dei danni.<br /><br /><keywordMajor>Evoluzione</keywordMajor>: Kai'Sa spara invece {{ e7 }} missili.<br />Attuale: <physicalDamage>{{ f11.1 }}/{{ e6 }} attacco fisico bonus</physicalDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> per proiettile"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [6, 6, 6, 6, 6], [0.25, 0.25, 0.25, 0.25, 0.25], [2, 2, 2, 2, 2], [0.35, 0.35, 0.35, 0.35, 0.35], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/55/70/85/100", "6", "0.25", "2", "0.35", "100", "12", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KaisaQ.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaW", "name": "Cercatore del Vuoto", "description": "Kai'Sa spara un missile a lunga gittata, march<PERSON><PERSON> i nemici con la sua passiva.<br><br><PERSON><PERSON> vivente: Cercatore del Vuoto viene potenziato e applica più marchi della passiva e riduce la ricarica sui campioni colpiti.", "tooltip": "Kai'Sa spara un'esplosione di Vuoto che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>, applica {{ e4 }} cariche di <keywordMajor>Plasma</keywordMajor> e conferisce <keywordStealth>Visione magica</keywordStealth> sul primo nemico colpito per {{ spell.kaisapassive:pduration }} secondi.<br /><br /><keywordMajor>Evoluzione</keywordMajor>: Kai'Sa applica {{ e5 }} cariche di <keywordMajor>Plasma</keywordMajor> e colpendo un campione riduce il tempo di ricarica di un {{ e3 }}%.<br />Attuale: <scaleAP>{{ f2.1 }}/{{ e2 }} potere magico</scaleAP>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "Costo in mana"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [30, 55, 80, 105, 130], [100, 100, 100, 100, 100], [75, 75, 75, 75, 75], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/55/80/105/130", "100", "75", "2", "3", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "KaisaW.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaE", "name": "Sovraccarico", "description": "Kai'Sa aumenta brevemente la sua velocità di movimento, poi aumenta la velocità d'attacco.<br><br>Arma vivente: Sovraccarico viene potenziato e conferisce brevemente Invisibilità.", "tooltip": "<PERSON>'Sa sovraccarica la sua energia del Vuoto, ottenendo <speed>{{ totalmovespeed }} velocità di movimento</speed> e diventando spettrale mentre carica, poi ottiene <attackSpeed>{{ e5 }}% velocità d'attacco</attackSpeed> per {{ e2 }} secondi.<br /><br />Gli attacchi riducono di {{ e4 }} secondi il tempo di ricarica di questa abilità.<br /><br /><keywordMajor>Evoluzione</keywordMajor>: Kai'Sa diventa <keywordStealth>invisibile</keywordStealth> per {{ e7 }} secondi.<br />Attuale: <attackSpeed>{{ f10.1 }}/{{ e6 }}% velocità d'attacco bonus</attackSpeed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Velocità di movimento", "Velocità d'attacco"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ effect1amount*100.000000 }}% -> {{ effect1amountnl*100.000000 }}%", "{{ effect5amount*100.000000 }}% -> {{ effect5amountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0.55, 0.6, 0.65, 0.7, 0.75], [4, 4, 4, 4, 4], [1.2, 1.2, 1.2, 1.2, 1.2], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.55/0.6/0.65/0.7/0.75", "4", "1.2", "0.5", "40/50/60/70/80", "100", "0.5", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "KaisaE.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaR", "name": "<PERSON><PERSON><PERSON> o<PERSON>", "description": "Kai'Sa scatta vicino a un campione nemico.", "tooltip": "<PERSON>'Sa si teletrasporta vicino a un campione nemico colpito da <keywordMajor>Plasma</keywordMajor> e ottiene <shield>uno Scudo da {{ rcalculatedshieldvalue }}</shield> per {{ rshieldduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Gitt<PERSON>", "Ricarica", "Quantità scudo", "Rapporto attacco fisico"], "effect": ["{{ rrange }} -> {{ rrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rbasevalue }} -> {{ rbasevalueNL }}", "{{ rtotaladratio*100.000000 }}% -> {{ rtotaladrationl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "KaisaR.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON>a pelle", "description": "Gli attacchi base di Kai'Sa accumulano Plasma, infliggendo danni magici bonus crescenti. Gli effetti immobilizzanti degli alleati aiutano ad accumulare Plasma. Inoltre, gli acquisti degli oggetti di Kai'Sa migliorano le sue abilità di base rendendole più potenti.", "image": {"full": "Kaisa_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}