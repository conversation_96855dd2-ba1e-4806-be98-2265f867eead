{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Karma": {"id": "<PERSON>rma", "key": "43", "name": "<PERSON>rma", "title": "cea iluminată", "image": {"full": "Karma.png", "sprite": "champion1.png", "group": "champion", "x": 336, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "43000", "num": 0, "name": "default", "chromas": false}, {"id": "43001", "num": 1, "name": "<PERSON><PERSON>, zeiț<PERSON> soarelui", "chromas": false}, {"id": "43002", "num": 2, "name": "Sakura Karma", "chromas": false}, {"id": "43003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "43004", "num": 4, "name": "<PERSON><PERSON> Ordinul Lotusului", "chromas": false}, {"id": "43005", "num": 5, "name": "<PERSON><PERSON> gard<PERSON>", "chromas": false}, {"id": "43006", "num": 6, "name": "<PERSON><PERSON>, minunea i<PERSON>ii", "chromas": false}, {"id": "43007", "num": 7, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "43008", "num": 8, "name": "<PERSON><PERSON>, steaua întunecată", "chromas": true}, {"id": "43019", "num": 19, "name": "<PERSON><PERSON>, întruparea Luminii", "chromas": false}, {"id": "43026", "num": 26, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "43027", "num": 27, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "43044", "num": 44, "name": "<PERSON><PERSON>, dragonul serenității", "chromas": false}, {"id": "43054", "num": 54, "name": "<PERSON><PERSON>, regina <PERSON>", "chromas": false}, {"id": "43061", "num": 61, "name": "<PERSON><PERSON> infernal<PERSON>", "chromas": false}, {"id": "43070", "num": 70, "name": "<PERSON><PERSON>, mug<PERSON>l <PERSON><PERSON>r", "chromas": false}], "lore": "Niciun muritor nu reprezintă tradițiile spirituale ale Ioniei mai bine decât <PERSON>, o ființă binecuvântată cu o putere pe care puțini o pot înțelege. Ea este reîncarnarea unui suflet străvechi care s-a reîntrupat de nenumărate ori, purtând mereu cu ea amintirile din viețile anterioare. A făcut tot ce a putut pentru a-și îndruma poporul în timpuri de restriște, deși știe că pacea și armonia pot fi obținute uneori doar cu un preț considerabil – atât pentru ea, cât și pentru Ionia ei iubită.", "blurb": "Ni<PERSON>un muritor nu reprezintă tradițiile spirituale ale Ioniei mai bine dec<PERSON><PERSON>, o ființă binecuvântată cu o putere pe care puțini o pot înțelege. Ea este reîncarnarea unui suflet străvechi care s-a reîntrupat de nenumărate ori, purtând mereu cu ea...", "allytips": ["''Înflăcărarea'' încurajează un stil de joc agresiv. Încearcă să-ți aplici abilitățile și atacurile de bază direct asupra adversarului, ca să reduci pasiv timpul de reactivare al ''Mantrei'' și să-ți continui ofensiva.", "Când <PERSON> ''Focalizare'', încetinește-ți adversarii cu ''Flacără interioară'' sau crește-ți viteza de mișcare cu ''Inspirație'' dacă ți-e greu să ții pasul cu ținta. ", "Nu-ți fie teamă să folosești des ''Mantra''. ''Înflăcărarea'' e cea mai puternică în luptele de echipă, facilitând încărcarea ''Mantrei'' de mai multe ori."], "enemytips": ["Pasiva Karmei reduce timpul de reactivare al ''Mantrei'' de fiecare dată când lovește un campion inamic cu abilități sau atacuri de bază. Nu-i permite Karmei să te lovească direct!", "''Lumina spirituală'' a Karmei erupe și provoacă daune bonus în zona în care este plasată. Reacționează rapid și ieși din cerc pentru a nu suferi daune mari.", "''Focalizarea'' e un instrument puternic pentru a ieși din luptă. Stai la distanță pentru a nu fi țintuit(ă) și încearcă să ataci după aceea."], "tags": ["Mage", "Support"], "partype": "Mană", "info": {"attack": 1, "defense": 7, "magic": 8, "difficulty": 5}, "stats": {"hp": 630, "hpperlevel": 109, "mp": 374, "mpperlevel": 40, "movespeed": 335, "armor": 28, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 13, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 51, "attackdamageperlevel": 3.3, "attackspeedperlevel": 2.3, "attackspeed": 0.625}, "spells": [{"id": "KarmaQ", "name": "Flacără interioară", "description": "<PERSON>rma la<PERSON>ază o sferă de energie spirituală care explodează și provoacă daune atunci când lovește o unitate inamică.<br><br>Bonus ''Mantră'': pe lângă explozie, ''Mantra'' crește puterea distructivă a ''Flăcării interioare'', creând un cataclism care provoacă daune după un scurt timp.", "tooltip": "<PERSON><PERSON> c<PERSON> o explozie de energie și le provoacă <magicDamage>{{ totaldamage }} daune magice</magicDamage> primei ținte lovite și inamicilor din jur, <status>încetinindu-i</status> cu {{ slowamount*-100 }}% timp de {{ slowduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [40, 50, 60, 70, 80], "costBurn": "40/50/60/70/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "KarmaQ.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSpiritBind", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Karma creează o legătură între ea și un inamic-țintă, provocându-i daune și dezvăluindu-l. Dacă legătura nu este ruptă, inamicul va fi țintuit și va suferi din nou daune.<br><br>Bonus ''Mantră'': <PERSON>rma întărește legătura, vindecându-se și prelungind durata țintuirii.", "tooltip": "Karma se leagă de un campion sau un monstru din junglă, provocându-i <magicDamage>{{ initialdamage }} daune magice</magicDamage> și dezvăluindu-l timp de {{ tetherduration }} sec. Dacă legătura nu se rupe, ținta suferă din nou <magicDamage>{{ initialdamage }} daune magice</magicDamage> și este <status>țintuită</status> timp de {{ rootduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "<PERSON><PERSON><PERSON>", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "KarmaSpiritBind.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KarmaSolKimShield", "name": "Inspirație", "description": "Karma invocă un scut protector care absoarbe daunele primite și crește viteza de mișcare a aliatului protejat.<br><br>Bonus ''Mantră'': energia pe care o degajă ținta augmentează scutul inițial și oferă ''Inspirație'' campionilor aliați din apropiere.", "tooltip": "<PERSON><PERSON> îi oferă unui campion aliat un <shield>scut în valoare de {{ totalshield }}</shield> timp de {{ shieldduration }} secunde și <speed>{{ movespeed*100 }}% vitez<PERSON> de mișcare</speed> timp de {{ movespeedduration }} secunde.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Valoarea scutului", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "KarmaSolKimShield.png", "sprite": "spell6.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>și augmentează următoarea abilitate, pentru un efect suplimentar. ''Mantra'' este disponibilă la nivelul 1 și nu necesită un punct de abilitate.", "tooltip": "Karma își îmbunătățește următoarea abilitate folosită în decurs de 8 secunde.<br /><li><spellName>Flac<PERSON>ră interioară</spellName>: provoac<PERSON> înc<PERSON> <magicDamage>{{ rqimpactdamage }} daune magice</magicDamage> și lasă în urmă un cerc de foc, <status>încetinind</status> inamicii și provocându-le <magicDamage>{{ rqfielddamage }} daune magice</magicDamage> suplimentare.<li><spellName>Focalizare</spellName>: Karma va reface <healing>{{ rwhealamount }} din viața lipsă</healing> la începutul și la sfârșitul legăturii și va <status>țintui</status> inamicul cu {{ rwbonusroot }} sec. mai mult.<li><spellName>Inspirație</spellName>: Karma of<PERSON><PERSON> un <shield>scut mai mare cu {{ rebonusshield }}</shield> țintei sale și protejează cu un scut și aliații din apropierea țintei, oferindu-le un <shield>scut în valoare de {{ rebonusshieldarea }}</shield> și <speed>{{ removespeed*100 }}% viteză de mișcare</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Impact ''Lumină spirituală''", "Daune cerc ''Lumină spirituală''", "Extindere țintuire ''Reînnoire''", "Scut ''S<PERSON>dare''", "Timp de reactivare"], "effect": ["{{ qbonusdamage }} -> {{ qbonusdamageNL }}", "{{ qdetonationdamage }} -> {{ qdetonationdamageNL }}", "{{ rwbonusroot }} -> {{ rwbonusrootNL }}", "{{ ebonusshield }} -> {{ ebonusshieldNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 4, "cooldown": [40, 38, 36, 34], "cooldownBurn": "40/38/36/34", "cost": [0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0], [0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "KarmaMantra.png", "sprite": "spell6.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "Fără cost"}], "passive": {"name": "Înflă<PERSON><PERSON><PERSON><PERSON>", "description": "Abilitățile ofensive ale Karmei vor reduce timpul de reactivare al ''Mantrei''.", "image": {"full": "Karma_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 336, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}