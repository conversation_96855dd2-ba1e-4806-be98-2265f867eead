{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Yone": {"id": "Yone", "key": "777", "name": "요네", "title": "잊히지 못한 자", "image": {"full": "Yone.png", "sprite": "champion5.png", "group": "champion", "x": 0, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "777000", "num": 0, "name": "default", "chromas": false}, {"id": "777001", "num": 1, "name": "영혼의 꽃 요네", "chromas": true}, {"id": "777010", "num": 10, "name": "전투사관학교 요네", "chromas": true}, {"id": "777019", "num": 19, "name": "빛의 인도자 요네", "chromas": true}, {"id": "777026", "num": 26, "name": "바다의 노래 요네", "chromas": true}, {"id": "777035", "num": 35, "name": "먹그림자 요네", "chromas": true}, {"id": "777045", "num": 45, "name": "HEARTSTEEL 요네", "chromas": true}, {"id": "777046", "num": 46, "name": "프레스티지 HEARTSTEEL 요네", "chromas": false}, {"id": "777055", "num": 55, "name": "하이 눈 요네", "chromas": true}, {"id": "777058", "num": 58, "name": "평화의 사도 하이 눈 요네", "chromas": false}, {"id": "777065", "num": 65, "name": "정의의 가면 요네", "chromas": false}], "lore": "생전에 야스오의 이부형제였던 요네는 마을 검술 학교에서 촉망받는 학생이었다. 그러나 형제의 손에 죽음을 맞이한 후 영혼 세계의 악령에 쫓기자 악령의 검으로 악령을 죽일 수밖에 없었다. 악령의 가면을 써야 하는 저주를 받게 된 요네는 자신이 어떤 존재가 되었는지 알아내기 위해 쉬지 않고 그 악령과 같은 존재들을 뒤쫓는다.", "blurb": "생전에 야스오의 이부형제였던 요네는 마을 검술 학교에서 촉망받는 학생이었다. 그러나 형제의 손에 죽음을 맞이한 후 영혼 세계의 악령에 쫓기자 악령의 검으로 악령을 죽일 수밖에 없었다. 악령의 가면을 써야 하는 저주를 받게 된 요네는 자신이 어떤 존재가 되었는지 알아내기 위해 쉬지 않고 그 악령과 같은 존재들을 뒤쫓는다.", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "기류", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 8}, "stats": {"hp": 620, "hpperlevel": 105, "mp": 500, "mpperlevel": 0, "movespeed": 345, "armor": 33, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "YoneQ", "name": "필멸의 검", "description": "전방으로 검을 내질러 일직선상의 모든 적에게 피해를 입힙니다.<br><br>적중 시, 몇 초간 폭풍 구름 효과가 1회 중첩됩니다. 2회 중첩되면 전방으로 돌진하며 돌풍을 날려 적을 <status>공중으로 띄워 올립니다</status>.", "tooltip": "전방으로 검을 내질러 <physicalDamage>{{ qdamage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />적중 시, {{ buffduration }}초간 1회 중첩됩니다. 2회 중첩되면 요네가 전방으로 돌진하며 돌풍을 날려 {{ q3knockupduration }}초 동안 적을 <status>공중으로 띄워 올리고</status> <physicalDamage>{{ qdamage }}의 물리 피해</physicalDamage>를 입힙니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "YoneQ.png", "sprite": "spell16.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YoneW", "name": "영혼 가르기", "description": "전방 원뿔 범위 내 모든 적을 가르며 보호막을 획득합니다. 적중한 챔피언 수만큼 보호막 흡수량이 증가합니다.<br><br>영혼 가르기의 재사용 대기시간과 시전 시간은 공격 속도에 비례합니다.", "tooltip": "요네가 전방을 가르며 <physicalDamage>{{ basedamage*0.5 }}+최대 체력의 {{ maxhealthdamage*50 }}%에 해당하는 물리 피해</physicalDamage> 및 <magicDamage>{{ basedamage*0.5 }}+최대 체력의 {{ maxhealthdamage*50 }}%에 해당하는 마법 피해</magicDamage>를 입힙니다.<br /><br />요네의 공격이 적중하면 {{ shieldduration }}초 동안 <shield>{{ wshield }}의 보호막</shield>을 얻습니다. 적중한 챔피언 수만큼 <shield>보호막</shield> 흡수량이 증가합니다. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "최대 체력 비례 총 피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "YoneW.png", "sprite": "spell16.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YoneE", "name": "영혼해방", "description": "요네의 영혼이 육신을 떠나 이동 속도가 증가합니다. 지속시간이 끝나면 요네의 영혼은 다시 육신으로 돌아오며 영혼 상태에서 입힌 피해량의 일부를 다시 입힙니다.", "tooltip": "요네가 {{ returntimer }}초 동안 영혼 상태가 되어 육신을 떠나고 <speed>이동 속도가 {{ startingms*100 }}%</speed>에서 <speed>{{ movementspeed*100 }}%</speed>까지 점차 증가합니다. <br /><br />영혼 상태가 끝나면 다시 육신으로 돌아오며 영혼 상태에서 챔피언에게 입힌 모든 기본 공격 및 스킬 피해량의 {{ deathmarkpercent*100 }}%를 다시 입힙니다. 영혼 상태에서 스킬을 <recast>재사용</recast>할 수 있습니다.<br /><br /><recast>재사용 시: </recast>영혼 상태를 더 일찍 종료합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["2차 피해량", "재사용 대기시간"], "effect": ["{{ deathmarkpercent*100.000000 }}% -> {{ deathmarkpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19, 16, 13, 10], "cooldownBurn": "22/19/16/13/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "YoneE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "YoneR", "name": "운명봉인", "description": "요네가 강력한 일격을 날리며 경로에 있는 마지막 챔피언 뒤로 순간적으로 이동하고 적중한 모든 적을 자신 쪽으로 끌어당깁니다.", "tooltip": "요네가 경로에 있는 모든 적을 공격해 <physicalDamage>{{ tooltipdamage }}의 물리 피해</physicalDamage>와 <magicDamage>{{ tooltipdamage }}의 마법 피해</magicDamage>를 입히고 경로에 있는 마지막 챔피언 뒤로 순간이동해 적중한 모든 적을 자신 쪽으로 끌어당기며 <status>공중으로 띄워 올립니다</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 피해량", "재사용 대기시간"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "YoneR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}], "passive": {"name": "사냥꾼의 길", "description": "요네가 두 번째 공격을 할 때마다 마법 피해를 입힙니다. 또한 요네의 치명타 확률이 증가합니다.", "image": {"full": "YonePassive.png", "sprite": "passive5.png", "group": "passive", "x": 0, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}