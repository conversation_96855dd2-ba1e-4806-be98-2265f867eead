{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Jhin": {"id": "<PERSON><PERSON>", "key": "202", "name": "<PERSON><PERSON>", "title": "der Virtuose", "image": {"full": "Jhin.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "202000", "num": 0, "name": "default", "chromas": false}, {"id": "202001", "num": 1, "name": "High Noon-Jhin", "chromas": true}, {"id": "202002", "num": 2, "name": "Blu<PERSON><PERSON>-Jhin", "chromas": false}, {"id": "202003", "num": 3, "name": "SKT T1-Jhin", "chromas": false}, {"id": "202004", "num": 4, "name": "PROJEKT: <PERSON><PERSON>", "chromas": false}, {"id": "202005", "num": 5, "name": "Kosmischer Vernichter Jhin", "chromas": false}, {"id": "202014", "num": 14, "name": "<PERSON>", "chromas": true}, {"id": "202023", "num": 23, "name": "DWG-Jhin", "chromas": true}, {"id": "202025", "num": 25, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "202036", "num": 36, "name": "Soul Fighter Jhin", "chromas": false}, {"id": "202037", "num": 37, "name": "Ultrakosmischer Vernichter Jhin", "chromas": true}, {"id": "202047", "num": 47, "name": "Sagenschöpfer <PERSON>", "chromas": false}], "lore": "Jhin ist ein akribischer und krimineller Psychopath, der Mord für Kunst hält. Einst war er ein ionischer Gefangener, doch zwielichtige Gestalten in Ionias Herrscherrat befreiten den Serienmörder. Jetzt dient er in ihren Ränkespielen als Assassine. Jhin sieht seine Waffe als Instrument, mit dem er brutale Kunstwerke erschafft, die sowohl seinen Opfern wie auch den Zeugen seiner Taten Albträume bescheren. Seine grausamen Inszenierungen bereiten ihm eine diabolische Genugtuung, sodass er die 1. Wahl ist, wenn es darum geht, die wirkungsvollste aller Botschaften zu senden: Angst und Schrecken.", "blurb": "<PERSON>hin ist ein akribischer und krimineller Psychopath, der Mord für Kunst hält. Einst war er ein ionischer Gefangener, doch zwielichtige Gestalten in Ionias Herrscherrat befreiten den Serienmörder. Jetzt dient er in ihren Ränkespielen als Assassine. Jhin...", "allytips": ["„Tödliche Fanfare“ verfügt über eine unglaubliche Reichweite. Wenn du dich in einen Kampf begibst, solltest du frühzeitig Ausschau nach Zielen halten, die du festhalten kannst.", "Deine ultimative Fähigkeit fügt G<PERSON>nern mit vollem Leben erheblich weniger Schaden zu. Konzentriere dich auf geschwächte Ziele, die fliehen wollen.", "Während du nachlädst, kannst du immer noch Zauber wirken. <PERSON>h dir das zu <PERSON>utze!"], "enemytips": ["„Tödliche Fanfare“ hält nur diejenigen fest, die innerhalb der letzten 4&nbsp;Se<PERSON><PERSON> von Jhins normalen Angriffen, Fallen oder Verbündeten getroffen wurden.", "<PERSON><PERSON> platziert überall auf der Karte unsichtbare Fallen. Pass auf, wo du hintrittst!", "<PERSON><PERSON><PERSON> Angriffe sind ziemlich stark, aber nach dem vierten Schuss geht ihm die Munition aus. In diesem Zeitfenster kannst du dich auf ihn stürzen und ihn niedermähen."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 10, "defense": 2, "magic": 6, "difficulty": 6}, "stats": {"hp": 655, "hpperlevel": 107, "mp": 300, "mpperlevel": 50, "movespeed": 330, "armor": 24, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 3.75, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 4.4, "attackspeedperlevel": 0, "attackspeed": 0.625}, "spells": [{"id": "JhinQ", "name": "Tanzende Granate", "description": "<PERSON><PERSON> schießt eine magische Patrone auf einen Gegner. Diese kann bis zu vier Ziele treffen und ihr Sc<PERSON>en erhöht sich jedesmal, wenn sie tötet.", "tooltip": "<PERSON>hin feuert eine Patrone auf einen <PERSON>, die <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> verursacht und dann zum nächsten Ziel springt, das noch nicht getroffen wurde.<br /><br />Die Patrone kann bis zu {{ tooltipmaxtargetshit }}&nbsp;Ziele treffen. G<PERSON><PERSON>, die kurz nachdem sie getroffen wurden sterben, erhöhen den Schaden von darauffolgenden Treffern um {{ percentamponkill*100 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit Gesamtangriffsschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ adratio*100.000000 }}&nbsp;% -> {{ adrationl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "JhinQ.png", "sprite": "spell5.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinW", "name": "Tödliche Fanfare", "description": "<PERSON><PERSON> schwingt seinen Stock und feuert einen einzigen Schuss mit unglaublicher Reichweite ab. Der Schuss durchschlägt Vasallen und Monster, bleibt aber im ersten getroffenen Champion stecken. Wen<PERSON> das Ziel vor Kurzem von J<PERSON>s Verbündeten, Lotusfallen oder Jhin selbst Schaden erlitten hat, wird es festgewurzelt.", "tooltip": "Jhin feuert einen Schuss mit großer Reichwei<PERSON> ab, der dem ersten getroffenen Champion und anderen auf dem Weg getroffenen Gegnern <physicalDamage>{{ totaldamage }}&nbsp;normalen Schaden</physicalDamage> zufügt.<br /><br />Wenn diese Fähigkeit einen gegnerischen Champion trifft, der in den letzten {{ spottingduration }}&nbsp;Sekunden durch einen Verbündeten Schaden erlitten hat, wird der Gegner {{ rootduration }}&nbsp;Sekunden lang <status>festgehalten</status> und Jhin erhält das Lauftempo von <spellName>Whisper</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Festhaltedauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "JhinW.png", "sprite": "spell5.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinE", "name": "Gefesseltes Publikum", "description": "<PERSON><PERSON> platziert eine unsichtbare <PERSON>falle, die erbl<PERSON>ht, wenn jemand auf sie tritt. Sie verlangsamt G<PERSON>ner in der Nähe, bevor sie durch eine Explosion aus gezackten Blättern Schaden verursacht. <br><br><font color='#FFFFFF'>Schönheit des Todes –</font> Wenn Jhin einen gegnerischen Champion tötet, erblüht neben der Leiche eine Lotusfalle.", "tooltip": "<passive>Passiv:</passive> <PERSON><PERSON> einen Champion tötet, erscheint an dessen Position eine explodierende Lotusfalle.<br /><br /><active>Aktiv:</active> <PERSON><PERSON> platziert {{ trapduration }}&nbsp;Minuten lang eine unsichtbare Lotusfalle, die eine Zone erschafft, in der Gegner bei Berührung um {{ trapslowamount*100 }}&nbsp;% <status>verlangsamt</status> werden. Nach {{ trapdetonationtime }}&nbsp;Sekunden explodiert die Falle und verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>. <br /><br />Diese Fähigkeit hat 2&nbsp;Aufladungen ({{ ammorechargeratetooltip }}&nbsp;Sekunden Aufladungszeit).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Wiederaufladungsrate"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [2, 2, 2, 2, 2], "cooldownBurn": "2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "JhinE.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "JhinR", "name": "Letzte Verbeugung", "description": "<PERSON>hin kanalisiert und transformiert Whisper in eine Megakanone, die er auf der Schulter trägt. Whisper kann 4 Superschüsse abfeuern, die eine extrem hohe Reichweite haben und Vasallen sowie Monster durchschlagen, jedoch im ersten getroffenen Champion steckenbleiben. Whisper verlangsamt die getroffenen Gegner und verursacht Schaden an Zielen mit niedrigem Leben. Der 4. Sc<PERSON><PERSON> ist perfekt, besonders mächtig und ein garantiert kritischer Treffer.", "tooltip": "<PERSON><PERSON> geht in Position und kanalisiert, wodurch er 4&nbsp;Superschüsse abfeuern kann, von denen jeder dem ersten getroffenen Champion zwischen <physicalDamage>{{ damagecalc }}</physicalDamage> und <physicalDamage>{{ maxincreasecalc }}&nbsp;normalen Schaden</physicalDamage> zufügt (basierend auf dem Prozentsatz des fehlenden Lebens), und ihn {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercent*100 }}&nbsp;% <status>verlangsamt</status>. Der vierte Schuss trifft kritisch und verursacht {{ fourthshotmultiplier*100 }}&nbsp;% Schaden.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "JhinR.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Whisper", "description": "<PERSON><PERSON><PERSON> Pistole Whisper ist ein präzises Tötungsinstrument, das für maximalen Schaden entwickelt wurde. Sie verfügt über eine festgelegte Feuerrate und fasst 4 Schuss Munition. <PERSON>hin verzaubert die letzte Kugel mit dunkler Magie, damit sie kritisch trifft und zusätzlichen Schaden an Zielen mit niedrigem Leben verursacht. Wenn Whisper kritisch trifft, wird <PERSON>hin dadurch inspiriert und erhält einen Lauftemposchub.", "image": {"full": "Jhin_P.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}