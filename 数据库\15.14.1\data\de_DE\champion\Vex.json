{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vex": {"id": "Vex", "key": "711", "name": "Vex", "title": "die Schwarzseherin", "image": {"full": "Vex.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "711000", "num": 0, "name": "default", "chromas": false}, {"id": "711001", "num": 1, "name": "Rächende Dämmerung Vex", "chromas": true}, {"id": "711010", "num": 10, "name": "Elysische Vex", "chromas": true}, {"id": "711020", "num": 20, "name": "Sterndeuterin Vex", "chromas": true}], "lore": "Im schwarzen Herzen der Schatteninseln stapft ein einsamer Yordle durch den gespenstischen Nebel, zufrieden in seinem düsteren Elend. Mit einem endlosen Vorrat an Teenagerdepressionen und einem mächtigen Schatten im Schlepptau lebt Vex in ihrem persönlichen Stück Düsternis, weit weg von der abstoßenden Fröhlichkeit der „normalen“ Welt. Obwohl es ihr an Ehrgeiz mangelt, lässt sie Farbe und Glück keine Chance und hält alle Möchtegern-Eindringlinge mit ihrer magischen Melancholie auf.", "blurb": "Im schwarzen Herzen der Schatteninseln stapft ein einsamer Yordle durch den gespenstischen Nebel, zufrieden in seinem düsteren Elend. Mit einem endlosen Vorrat an Teenagerdepressionen und einem mächtigen Schatten im Schlepptau lebt Vex in ihrem...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 0, "defense": 0, "magic": 0, "difficulty": 0}, "stats": {"hp": 590, "hpperlevel": 104, "mp": 490, "mpperlevel": 32, "movespeed": 335, "armor": 23, "armorperlevel": 4.45, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 54, "attackdamageperlevel": 2.75, "attackspeedperlevel": 1, "attackspeed": 0.669}, "spells": [{"id": "VexQ", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Feuert ein schadensverursachendes Geschoss ab, das während des Flugs beschleunigt.", "tooltip": "Vex entfesselt eine Welle aus Nebel, die <magicDamage>{{ qdamagecalc }}&nbsp;magischen Schaden</magicDamage> verursacht. Nach einer Verzögerung wird die Welle kleiner und schneller.<br /><br />Aktiviert <keywordMajor>Verderben</keywordMajor> bei getroffenen Gegnern.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kosten (@AbilityResourceName@)", "Abklingzeit", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "VexQ.png", "sprite": "spell15.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VexW", "name": "Abstand halten", "description": "Du erhältst einen Schild und fügst nahen Gegnern Schaden zu.", "tooltip": "Vex erhält {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON><PERSON><PERSON></shield> in <PERSON><PERSON><PERSON> von {{ shieldcalc }} und löst eine <PERSON> aus, die <magicDamage>{{ wdamagecalc }}&nbsp;magischen <PERSON>en</magicDamage> verursacht.<br /><br />Aktiviert <keywordMajor>Verderben</keywordMajor> bei getroffenen Gegnern.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schildstärke", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shieldamount }} -> {{ shieldamountNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "VexW.png", "sprite": "spell15.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VexE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Besch<PERSON><PERSON><PERSON> einen Bereich, der Schaden verursacht und verlangsamt sowie Gegner mit „Verderben“ belegt.", "tooltip": "Vex sendet Schatten zu einem Ort. Dabei wird er auf seinem Weg immer größer. Bei der Ankunft fügt er dem Ziel <magicDamage>{{ edamagecalc }}&nbsp;magischen <PERSON>en</magicDamage> zu und <status>verlangsamt</status> es {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%.<br /><br />Tötest du einen Gegner mit dieser Fähigkeit, wird die Abklingzeit von <keywordMajor>Verhängnis und Verderben</keywordMajor> um {{ gloomcdnonchamptooltip*100 }}&nbsp;% verringert.<br /><br />Getroffene Gegner werden mit <keywordMajor>Verderben</keywordMajor> belegt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Skalierung mit gesamter Fähigkeitsstärke"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*100.000000 }}&nbsp;% -> {{ slowamountnl*100.000000 }}&nbsp;%", "{{ apratio*100.000000 }} -> {{ aprationl*100.000000 }}"]}, "maxrank": 5, "cooldown": [13, 13, 13, 13, 13], "cooldownBurn": "13", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "VexE.png", "sprite": "spell15.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "VexR", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>uert ein Geschoss ab, das einen gegnerischen Champion markiert. Bei Reaktivierung springt Vex zu ihm und fügt ihm Schaden zu.", "tooltip": "<PERSON>hatten stürmt aufgeregt nach vorn, verursacht dabei <magicDamage>{{ spell.vexr:rdamagecalc }}&nbsp;magischen Schaden</magicDamage> und markiert den ersten getroffenen gegnerischen Champion 4&nbsp;Sekunden lang.<br /><br /><recast>Reaktivierung</recast>: Springe zum markierten Champion und füge ihm bei der Ankunft <magicDamage>{{ spell.vexr:recastdamagecalc }}&nbsp;magischen Schaden</magicDamage> zu.<br /><br />Stirbt der markierte Champion innerhalb von {{ spell.vexr:takedownwindow }}&nbsp;Sekunden, nachdem er durch diese Fähigkeit Schaden erlitten hat, wird ihre Abklingzeit kurzzeitig zurückgesetzt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Schaden der Reaktivierung", "Reichweite"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ recastdamage }} -> {{ recastdamageNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "VexR.png", "sprite": "spell15.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Verhängnis und Verderben", "description": "Vex wird regelmäßig verstärkt. Dadurch versetzt ihre nächste Grundfähigkeit Gegner in Furcht und unterbricht deren Sprünge. Wenn ein <PERSON> in der Nähe einen Sprung einsetzt, wird er von Vex markiert. Der Verbrauch dieser Markierung verursacht zusätzlichen Schaden und verringert die Abklingzeit ihres verstärkten Zustands.", "image": {"full": "Icons_Vex_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}