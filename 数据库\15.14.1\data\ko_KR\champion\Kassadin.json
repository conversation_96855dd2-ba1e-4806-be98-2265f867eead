{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kassadin": {"id": "<PERSON><PERSON><PERSON>", "key": "38", "name": "카사딘", "title": "공허의 방랑자", "image": {"full": "Kassadin.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "38000", "num": 0, "name": "default", "chromas": false}, {"id": "38001", "num": 1, "name": "얼음축제 카사딘", "chromas": false}, {"id": "38002", "num": 2, "name": "심해의 카사딘", "chromas": false}, {"id": "38003", "num": 3, "name": "인간 시절의 카사딘", "chromas": false}, {"id": "38004", "num": 4, "name": "선구자 카사딘", "chromas": false}, {"id": "38005", "num": 5, "name": "우주의 지배자 카사딘", "chromas": false}, {"id": "38006", "num": 6, "name": "카사딘 백작", "chromas": true}, {"id": "38014", "num": 14, "name": "마법공학 카사딘", "chromas": false}, {"id": "38015", "num": 15, "name": "칼날폭풍 카사딘", "chromas": false}, {"id": "38024", "num": 24, "name": "용술사 카사딘", "chromas": false}], "lore": "이 세계에서도 가장 어두운 곳을 불타는 검을 휘둘러 헤쳐나가는 카사딘은 자신에게 주어진 시간이 많지 않음을 잘 알고 있다. 원래 슈리마 구석구석 가보지 않은 곳이 없는 여행 안내자이자 모험가였던 그는 슈리마 남쪽의 평화로운 부족 마을에서 가정을 꾸리고 살았다. 하지만 어느 날, 그가 살던 곳을 공허가 덮쳐 집어삼켰다. 카사딘은 복수를 맹세했고, 신비로운 유물과 금지된 기술을 닥치는 대로 조합하여 앞으로의 투쟁에 쓸 무기를 만들었다. 황무지 이케시아를 찾아 나선 카사딘은 아무리 끔찍스럽고 무시무시한 공허 생명체라도 맞서 싸울 각오가 되어 있다. 예언자를 자처하는 말자하와 대면할 그 날을 위해.", "blurb": "이 세계에서도 가장 어두운 곳을 불타는 검을 휘둘러 헤쳐나가는 카사딘은 자신에게 주어진 시간이 많지 않음을 잘 알고 있다. 원래 슈리마 구석구석 가보지 않은 곳이 없는 여행 안내자이자 모험가였던 그는 슈리마 남쪽의 평화로운 부족 마을에서 가정을 꾸리고 살았다. 하지만 어느 날, 그가 살던 곳을 공허가 덮쳐 집어삼켰다. 카사딘은 복수를 맹세했고, 신비로운 유물과 금지된 기술을 닥치는 대로 조합하여 앞으로의 투쟁에 쓸 무기를 만들었다. 황무지 이케시아를...", "allytips": ["카사딘은 여러 가지 방면으로 성장시킬 수 있는 챔피언입니다. 마나와 주문력을 늘려 마법 공격형 챔피언으로 성장시킬 수도 있으며 재사용 대기시간 감소와 마법 저항력 위주의 아이템을 장착하면 마법사 사냥꾼으로 둔갑합니다.", "카사딘의 궁극기는 사용법이 다양하고 재사용 대기시간도 비교적 짧으므로 자주 활용하십시오.", "푸른 파수꾼을 처치하여 균열 이동을 연속해서 사용할 때 증가하는 마나 소모량에 대비하십시오."], "enemytips": ["카사딘은 주로 마법 피해를 줍니다. 카사딘에 대항하려면 헤르메스의 발걸음이나 밴시의 장막과 같은 마법 저항력 아이템을 구매하세요.", "카사딘은 균열 이동을 연속으로 사용할수록 점점 더 많은 마나를 사용하게 됩니다. 카사딘을 쫓을 때는 이 점을 유념하세요.", "주위에서 스킬이 6번 시전되면 카사딘이 힘의 파동을 사용할 수 있으므로, 스킬 사용에 주의하세요."], "tags": ["Assassin", "Mage"], "partype": "마나", "info": {"attack": 3, "defense": 5, "magic": 8, "difficulty": 8}, "stats": {"hp": 646, "hpperlevel": 119, "mp": 400, "mpperlevel": 87, "movespeed": 335, "armor": 21, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 150, "hpregen": 6, "hpregenperlevel": 0.5, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.9, "attackspeedperlevel": 3.7, "attackspeed": 0.64}, "spells": [{"id": "NullLance", "name": "무의 구체", "description": "카사딘이 대상에게 공허 에너지로 구성된 구체를 발사하여 피해를 입히고 정신 집중을 끊습니다. 여분의 에너지는 카사딘의 몸을 감싸 잠시 동안 보호막을 형성하며 마법 피해를 흡수합니다.", "tooltip": "카사딘이 공허 에너지 구체를 발사하여 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 정신 집중을 끊습니다. 또한 1.5초 동안 <shield>{{ totalshield }}의 마법 피해를 흡수하는 보호막</shield>을 얻습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "보호막 흡수량", "소모값 @AbilityResourceName@", "재사용 대기시간"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [65, 95, 125, 155, 185], [0, 0, 0, 0, 0], [80, 110, 140, 170, 200], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "65/95/125/155/185", "0", "80/110/140/170/200", "1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "NullLance.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "<PERSON>herBlade", "name": "황천의 검", "description": "기본 지속 효과: 카사딘의 기본 공격이 추가 마법 피해를 입힙니다. 사용 시: 카사딘의 다음 기본 공격이 큰 추가 마법 피해를 입히고 마나가 회복됩니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 카사딘의 기본 공격이 <magicDamage>{{ onhitdamage }}의 마법 피해</magicDamage>를 추가로 입힙니다.<br /><br /><spellActive>사용 시:</spellActive> 카사딘이 검을 충전하여 다음 기본 공격으로 <magicDamage>{{ activedamage }}의 마법 피해</magicDamage>를 입히고 <scaleMana>잃은 마나의 {{ e1 }}%</scaleMana>를 회복합니다. (챔피언 공격 시 <scaleMana>{{ e4 }}%</scaleMana>로 증가){{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["사용 시 피해량", "기본 마나 회복량", "챔피언 마나 회복량"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e1 }}% -> {{ e1NL }}%", "{{ e4 }}% -> {{ e4NL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [1, 1, 1, 1, 1], "costBurn": "1", "datavalues": {}, "effect": [null, [4, 4.5, 5, 5.5, 6], [20, 20, 20, 20, 20], [50, 75, 100, 125, 150], [20, 22.5, 25, 27.5, 30], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "4/4.5/5/5.5/6", "20", "50/75/100/125/150", "20/22.5/25/27.5/30", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "NetherBlade.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "ForcePulse", "name": "힘의 파동", "description": "카사딘이 자신의 영역 안에서 사용된 스킬에서 에너지를 뽑아냅니다. 충전이 완료되면 힘의 파동을 사용하여 정면 원뿔 반경에 있는 적의 속도를 늦추고 피해를 입힐 수 있습니다.", "tooltip": "<spellPassive>기본 지속 효과:</spellPassive> 카사딘 근처에서 스킬을 사용하면 <spellName>힘의 파동의</spellName> 재사용 대기시간이 {{ reductionperspellcast }}초 감소합니다.<br /><br /><spellActive>사용 시:</spellActive> 카사딘이 공허의 파동을 발사해 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ e3 }}초 동안 {{ e2 }}% <status>둔화</status>시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "둔화", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [21, 20, 19, 18, 17], "cooldownBurn": "21/20/19/18/17", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [50, 60, 70, 80, 90], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "50/60/70/80/90", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "ForcePulse.png", "sprite": "spell6.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "RiftWalk", "name": "균열 이동", "description": "카사딘이 가까운 지역으로 순간 이동하면서 주변 적 유닛에게 피해를 입힙니다. 짧은 시간 동안 여러 번의 균열 이동을 시전하면 마나를 추가로 소모하는 대신 추가 피해를 입힐 수 있습니다.", "tooltip": "카사딘이 근처로 순간이동하며 <magicDamage>{{ basedamage }}의 마법 피해</magicDamage>를 입힙니다.<br /><br />다음 {{ rstackduration }}초 안에 이 스킬을 연속으로 사용하면 두 배의 마나를 소모하며 <magicDamage>{{ bonusdamage }}의 마법 피해</magicDamage>를 추가로 입힙니다. 마나 소모량 및 피해량 증가는 최대 {{ maxstacks }}회까지 중첩됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "중첩당 피해량", "재사용 대기시간"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ stackdamage }} -> {{ stackdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [5, 3.5, 2], "cooldownBurn": "5/3.5/2", "cost": [40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [500, 500, 500], "rangeBurn": "500", "image": {"full": "RiftWalk.png", "sprite": "spell6.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "공허석", "description": "카사딘이 받는 마법 피해가 감소하며, 유닛과의 충돌을 무시합니다.", "image": {"full": "Ka<PERSON>din_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}