{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Bard": {"id": "Bard", "key": "432", "name": "Bard", "title": "der wandelnde Beschützer", "image": {"full": "Bard.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "432000", "num": 0, "name": "default", "chromas": true}, {"id": "432001", "num": 1, "name": "Ahnenholz-Bard", "chromas": false}, {"id": "432005", "num": 5, "name": "Schneetag-Bard", "chromas": true}, {"id": "432006", "num": 6, "name": "<PERSON><PERSON>, der Barde", "chromas": false}, {"id": "432008", "num": 8, "name": "Astronauten-Bard", "chromas": true}, {"id": "432017", "num": 17, "name": "Konditorei-Bard", "chromas": true}, {"id": "432026", "num": 26, "name": "Shan <PERSON>-Bard", "chromas": true}, {"id": "432035", "num": 35, "name": "T1-Bard", "chromas": true}, {"id": "432037", "num": 37, "name": "Seelenblumen-Bard", "chromas": true}], "lore": "Bard ist ein Reisender aus fernen Galaxien. Er lässt sich vom Zufall treiben und ist darauf bedacht, ein Gleichgewicht zu erhalten, in dem das Leben der Gleichgültigkeit des Chaos standhalten kann. Viele Bewohner Runeterras kennen Lieder, die von seinem außergewöhnlichen Wesen handeln, doch alle haben gemein, dass dieser intergalaktische Herumtreiber von mächtigen magischen Artefakten angezogen wird. E<PERSON> ist unmöglich, seine Taten als bösartig anzu<PERSON>hen, denn er ist von einem Chor fröhlicher Meeps umgeben und dient stets einem übergeordneten Wohl ... auf seine eigene seltsame Art.", "blurb": "Bard ist ein Reisender aus fernen Galaxien. Er lässt sich vom Zufall treiben und ist darauf bedacht, ein Gleichgewicht zu erhalten, in dem das Leben der Gleichgültigkeit des Chaos standhalten kann. Viele Bewohner Runeterras kennen Lieder, die von seinem...", "allytips": ["<PERSON><PERSON> ist wichtig, <PERSON><PERSON><PERSON> zu sammeln, um die Angriffe deiner Meeps zu stärken, aber lasse deinen Lanepartner nicht im Regen stehen! Versuche dich und deinen Verbündeten durch den Einsatz von „Magische Reise“ in die Lane zu bringen und so einen erfolgreichen Start zu ermöglichen.", "<PERSON>se deinem „Schrein des Beschützers“ Zeit zum Aufladen - er heilt um einiges effektiver, wenn er volle Kraft besitzt.", "Ver<PERSON><PERSON> nicht, dass auch Gegner die Eingänge von „Magische Reise“ nutzen können und dass deine ultimative Fähigkeit auch deine Verbündeten trifft!"], "enemytips": ["<PERSON>ch Bards Gegenspieler können die Eingänge zu den Tunneln von „Magische Reise“ nutzen, um sich fortzubewegen. Du kannst ihm also folgen, wenn du glaubst, dass dies sicher ist.", "Du kannst Bards Heilschreine zerstören, indem du einfach über sie hinweg läufst. Überlasse sie seinen Verbündeten nicht kampflos.", "Bards ultimative Fähigkeit, „Innehalten“, wirkt sich auf Verbündete, Gegner und Gebäude gleichermaßen aus. Manch<PERSON> kann es zu deinem Vorteil sein, wenn du in den Wirkungsbereich springst!"], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 4, "magic": 5, "difficulty": 9}, "stats": {"hp": 630, "hpperlevel": 103, "mp": 350, "mpperlevel": 50, "movespeed": 335, "armor": 34, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 6, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "BardQ", "name": "Kosmische Bindung", "description": "Bard feuert ein Projektil ab, das den ersten getroffenen Gegner verlangsamen wird, bevor es sich weiterbewegt. Trifft es auf eine <PERSON>, wird das ursprüngliche Ziel betäubt; trifft es auf einen weiteren Gegner, werden beide betäubt.", "tooltip": "Bard feuert ein Energiegeschoss ab, das den ersten beiden getroffenen Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zufügt. Das erste getroffene Ziel wird {{ slowduration }}&nbsp;Sek. lang um {{ slowamountpercentage }}&nbsp;% <status>verlangsamt</status>.<br /><br />Wenn das Geschoss einen zweiten Gegner oder eine Mauer trifft, werden alle getroffenen Gegner {{ stunduration }}&nbsp;Sek. lang <status>betäubt</status>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>uer", "Betäubungsdauer:", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowduration }} -> {{ slowdurationNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BardQ.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardW", "name": "Schrein des Beschützers", "description": "Enthü<PERSON>t einen heilenden Schrein, der über kurze Zeit stärker wird und verschwindet, nachdem er den ersten Verbündeten, der ihn berührt, geheil<PERSON> hat.", "tooltip": "Bard erschafft einen heilenden Schrein, der <speed>{{ calc_movespeed }}&nbsp;Lauftempo</speed> gew<PERSON><PERSON><PERSON>, das über {{ movespeed_duration }}&nbsp;Sekunden hinweg abfällt, und den ersten Verbündeten, der ihn berü<PERSON>t, um mindestens <healing>{{ initialheal }}&nbsp;Leben</healing> heilt. Der Schrein wird größer und stellt {{ chargeuptime }}&nbsp;Sekunden nach seiner Entstehung <healing>{{ maxheal }}&nbsp;<PERSON><PERSON></healing> wieder her.<br /><br />Es können bis zu {{ maxpacks }}&nbsp;Schreine gleichzeitig aktiv sein. Wenn ein gegnerischer Champion einen Schrein berührt, wird dieser zerstört.<br /><br />Diese Fähigkeit hat {{ ammo_limit }}&nbsp;Ladungen.<br /><br />Aktuell aktive Schreine: {{ f1 }} / {{ f2 }}{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Grundheilung", "Maximale Heilung", "Lauftempo"], "effect": ["{{ e5 }} -> {{ e5NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ movespeed_base*100.000000 }}&nbsp;% -> {{ movespeed_basenl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [25, 50, 75, 100, 125], [50, 87.5, 125, 162.5, 200], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "3", "0", "25/50/75/100/125", "50/87.5/125/162.5/200", "0", "5", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "BardW.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardE", "name": "Magische Reise", "description": "Bard <PERSON> in der Umgebung ein Portal. Verbündete und Gegner können es gleichermaßen nutzen, um in einer Richtung durch das Terrain zu reisen, indem sie das Portal betreten.", "tooltip": "Bard öffnet ein einbahniges Portal, das {{ e1 }}&nbsp;Sekunden lang bestehen bleibt und durch Terrain führt. Jeder Champion kann das Portal betreten, wenn er sich in der Nähe des Eingangs befindet.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 20.5, 19, 17.5, 16], "cooldownBurn": "22/20.5/19/17.5/16", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [33, 33, 33, 33, 33], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "33", "900", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BardE.png", "sprite": "spell1.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BardR", "name": "Innehalten", "description": "Bard sendet magische Energie in einem Bogen an eine Zielposition, wodurch alle getroffenen Champions, Vasallen, Monster und Türme kurze Zeit in Stase versetzt werden.", "tooltip": "Bard schleudert schützende Magie in einen Bereich und versetzt alle getroffenen Einheiten und Gebäude {{ rstasisduration }}&nbsp;Sekunden lang in Stase.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3400, 3400, 3400], "rangeBurn": "3400", "image": {"full": "BardR.png", "sprite": "spell1.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "<font color='#FF9900'>Meeps:</font> Bard zieht kleine Geister an, die seinen normalen Angriffen zusätzlichen magischen Schaden verleihen. Wenn Bard genug <font color='#cccc00'>Glocken</font> eingesammelt hat, verursachen seine Meeps außerdem Flächenschaden und verlangsamen getroffene Gegner.<br><br><font color='#FF9900'>Glocken:</font> Es erscheinen zufällig uralte <font color='#cccc00'>Glocken</font>, die Bard einsammeln kann. Diese gewähren Erfahrung, stellen <PERSON> wieder her und gewähren außerhalb des Kampfes Lauftempo.", "image": {"full": "Bard_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}