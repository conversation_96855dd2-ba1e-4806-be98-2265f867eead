{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gwen": {"id": "<PERSON>", "key": "887", "name": "<PERSON>", "title": "La Sacra ricamatrice", "image": {"full": "Gwen.png", "sprite": "champion1.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "887000", "num": 0, "name": "default", "chromas": false}, {"id": "887001", "num": 1, "name": "<PERSON>", "chromas": true}, {"id": "887011", "num": 11, "name": "<PERSON>", "chromas": true}, {"id": "887020", "num": 20, "name": "<PERSON>", "chromas": true}, {"id": "887030", "num": 30, "name": "<PERSON> g<PERSON>", "chromas": true}], "lore": "Gwen era una bambola, ora portata in vita dalla magia, che brandisce gli strumenti che l'hanno creata. Porta con sé il peso dell'amore della sua creatrice e non dà nulla per scontato. A lei obbedisce la Sacra Nebbia, un'antica magia protettiva che ha benedetto le sue forbici, gli aghi e i fili. Tutto è nuovo per lei, ma <PERSON> è decisa a lottare con gioia per il bene sopravvissuto in un mondo in frantumi.", "blurb": "Gwen era una bambola, ora portata in vita dalla magia, che brandisce gli strumenti che l'hanno creata. Porta con sé il peso dell'amore della sua creatrice e non dà nulla per scontato. A lei obbedisce la Sacra Nebbia, un'antica magia protettiva che ha...", "allytips": ["Sempre all'attacco - Oltre a infliggere danni bonus, gli attacchi di Gwen potenziano o ripristinano molte delle sue abilità.", "Gwen può comunque infliggere danni fuori dalle sue <PERSON>, soprattutto con la gittata della sua suprema.", "Alcune delle abilità di Gwen possono applicare la sua passiva a più nemici, quindi mira ai gruppi per ottenere il massimo dei danni e della guarigione."], "enemytips": ["Il velo di Sacra Nebbia di Gwen la segue solo una volta, dopo di che si dissipa alla sua uscita.", "Gwen deve colpire qualcosa per lanciare di nuovo la sua Suprema, quindi prova a schivarla tra un lancio e l'altro.", "Gwen deve attaccare un po' di volte per preparare i suoi danni, quindi cerca di giocare d'anticipo."], "tags": ["Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 7, "defense": 4, "magic": 5, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 110, "mp": 330, "mpperlevel": 40, "movespeed": 340, "armor": 36, "armorperlevel": 4.9, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 9, "hpregenperlevel": 0.9, "mpregen": 7.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 63, "attackdamageperlevel": 3, "attackspeedperlevel": 2.25, "attackspeed": 0.69}, "spells": [{"id": "GwenQ", "name": "Zac, zac!", "description": "Gwen fa scattare rapidamente le forbici per un massimo di sei volte in un'area a cono, infliggendo danni magici. Gwen infligge danni puri alle unità al centro e applica la sua passiva a ogni unità con ogni taglio.", "tooltip": "<spellPassive>Passiva</spellPassive>: <PERSON> ottiene una carica quando colpisce un nemico con un attacco (massimo 4, dura {{ buffduration }} secondi).<br /><br /><spellActive>Attiva</spellActive>: consuma cariche. Gwen taglia una volta per <magicDamage>{{ miniswipedamage }} danni magici</magicDamage>, taglia di nuovo per ogni munizione consumata, per poi tagliare un'ultima volta per <magicDamage>{{ finalswipedamage }} danni magici</magicDamage>.<br /><br />Il centro di ogni colpo infligge un {{ truedamageconversion*100 }}% dei danni in <trueDamage>danni puri</trueDamage> e applica <spellName>Dono tagliente</spellName> ai nemici colpiti.<br /><rules><br />Infligge un {{ minionmod*100 }}% dei danni ai minion.<br />I minion con meno di un {{ executethreshold*100 }}% di salute subiscono un {{ executebonus }}% di danni bonus invece dei danni ridotti.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> co<PERSON> finale"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ swipedamagebase }} -> {{ swipedamagebaseNL }}"]}, "maxrank": 5, "cooldown": [6.5, 5.75, 5, 4.25, 3.5], "cooldownBurn": "6.5/5.75/5/4.25/3.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "GwenQ.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenW", "name": "Sacra Nebbia", "description": "<PERSON> evoca una nebbia che la protegge dai nemici all'esterno. P<PERSON>ò essere bersagliata solo dai nemici che entrano nella nebbia.", "tooltip": "Gwen evoca la Sacra Nebbia, rendendosi non bersagliabile per tutti i nemici (tranne le torri) fuori dalla zona per {{ zoneduration }} secondi o fino a che non ne esce. Mentre è nella Nebbia, <PERSON> ottiene {{ totalresists }} <scaleArmor>armatura</scaleArmor> e <scaleMR>resistenza magica</scaleMR>.<br /><br /><PERSON> <recast>lanciare nuovamente</recast> questa abilità per richiamare a sé la Nebbia. Verrà automaticamente <recast>rilanciata</recast> la prima volta che Gwen cerca di uscire dalla zona.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [24, 22.5, 21, 19.5, 18], "cooldownBurn": "24/22.5/21/19.5/18", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GwenW.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenE", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Gwen scatta per una breve distanza e poi ottiene velocità d'attacco, gittata d'attacco e danni magici <OnHit>sul colpo</OnHit> per qualche secondo. Se colpisce un nemico durante questo periodo, la ricarica dell'abilità viene parzialmente rimborsata. ", "tooltip": "Gwen scatta e potenzia i suoi attacchi per {{ buffduration }} secondi.<br /><br />Gli attacchi potenziati ottengono <attackSpeed>{{ bonusattackspeed }} velocità d'attacco</attackSpeed>, <magicDamage>{{ onhitdamage }} danni magici</magicDamage> %i:OnHit% <OnHit>sul colpo</OnHit>, {{ bonusattackrange }} gittata e il primo a colpire un nemico rimborsa il {{ cdrefund*100 }}% della ricarica di questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Velocità d'attacco", "<PERSON><PERSON> sul colpo"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ baseattackspeed }}% -> {{ baseattackspeedNL }}%", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "GwenE.png", "sprite": "spell4.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GwenR", "name": "<PERSON><PERSON> fatale", "description": "Gwen scaglia un ago che rallenta i nemici colpiti, infligge danni magici e applica Dono tagliente ai campioni colpiti.<br><br>Questa abilità può essere lanciata altre due volte e ogni lancio scaglia aghi aggiuntivi, infliggendo più danni. ", "tooltip": "<spellActive>Primo lancio:</spellActive> scaglia un ago che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>, <status>rallenta</status> di un {{ initialslow*-100 }}% per {{ debuffduration }} secondi e applica <spellName>Dono tagliente</spellName> a tutti i nemici colpiti. <PERSON> può <recast>rilanciare</recast> questa abilità fino a 2 volte in più entro 6 secondi ({{ lockouttime }} secondo/i di ricarica tra i lanci).<br /><br /><recast>Secondo lancio:</recast> spara tre aghi che infliggono <magicDamage>{{ totaldamage3 }} danni magici</magicDamage><br /><recast>Terzo lancio:</recast> spara cinque aghi che infliggono <magicDamage>{{ totaldamage5 }} danni magici</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Danni base"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1200, 1200], "rangeBurn": "1200", "image": {"full": "GwenR.png", "sprite": "spell4.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> tag<PERSON>e", "description": "Gli attacchi di Gwen infliggono danni magici bonus in base alla salute dei bersagli. Lei si cura per una parte dei danni inflitti ai campioni da questo effetto. ", "image": {"full": "Gwen_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}