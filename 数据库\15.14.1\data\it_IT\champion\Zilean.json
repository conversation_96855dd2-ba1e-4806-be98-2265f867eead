{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Zilean": {"id": "<PERSON><PERSON><PERSON>", "key": "26", "name": "<PERSON><PERSON><PERSON>", "title": "il custode del tempo", "image": {"full": "Zilean.png", "sprite": "champion5.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "26000", "num": 0, "name": "default", "chromas": false}, {"id": "26001", "num": 1, "name": "Zilean <PERSON>", "chromas": false}, {"id": "26002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "26003", "num": 3, "name": "Zilean del Deserto di Shurima", "chromas": false}, {"id": "26004", "num": 4, "name": "Zilean Macchina del Tempo", "chromas": false}, {"id": "26005", "num": 5, "name": "<PERSON><PERSON>an Luna di Sangue", "chromas": false}, {"id": "26006", "num": 6, "name": "<PERSON><PERSON><PERSON> al dol<PERSON>tto", "chromas": true}, {"id": "26014", "num": 14, "name": "Zilean Favore dell'Inverno", "chromas": true}], "lore": "Un tempo potente mago di Icathia, <PERSON>ilean divenne ossessionato dal passare del tempo dopo aver assistito alla distruzione della sua terra a opera del Vuoto. Incapace di piangere la sua perdita neanche per un istante, si rivolse all'antica magia temporale per prevedere i possibili futuri. <PERSON><PERSON><PERSON> immortale, ora <PERSON>ilean percorre passato, presente e futuro piegando e deviando lo scorrere del tempo intorno a sé, sempre alla ricerca dello sfuggente momento che possa cambiare gli eventi e salvare Icathia dalla distruzione.", "blurb": "Un tempo potente mago di Icathia, <PERSON><PERSON>an divenne ossessionato dal passare del tempo dopo aver assistito alla distruzione della sua terra a opera del Vuoto. Incapace di piangere la sua perdita neanche per un istante, si rivolse all'antica magia temporale...", "allytips": ["Puoi combinare l'uso di Bomba temporale e Ricarica accelerata per piazzare due bombe a tempo su un bersaglio molto velocemente. Piazzare la seconda bomba farà esplodere la prima, stordendo i nemici nelle vicinanze.", "Incantesimo temporale è una maniera efficace di finire i nemici, o di scappare da una battaglia persa.", "Spostamento temporale è un grande deterrente quando cercano di attaccare i tuoi alleati più forti, ma lanciare Spostamento temporale troppo presto nel combattimento può far cambiare bersaglio, renden<PERSON>lo meno efficace."], "enemytips": ["Se riesci a stare dietro alla velocità di Zilean, a volte potrebbe essere più vantaggioso aspettare che la sua abilità suprema finisca per infliggere il colpo di grazia.", "<PERSON><PERSON><PERSON> è fragile, se la squadra si concentra su di lui, altrimenti è molto difficile da uccidere. Se decidi di eliminarlo, fallo come una squadra."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 5, "magic": 8, "difficulty": 6}, "stats": {"hp": 574, "hpperlevel": 96, "mp": 452, "mpperlevel": 50, "movespeed": 335, "armor": 24, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.5, "mpregen": 11.35, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 3, "attackspeedperlevel": 2.13, "attackspeed": 0.658}, "spells": [{"id": "ZileanQ", "name": "Bomba temporale", "description": "Lancia una bomba nell'area bersaglio che si attacca a qualunque unità ci vada vicino (priorità ai campioni). Esplode dopo 3 secondi, infliggendo danni ad area. Se una Bomba temporale viene fatta esplodere prima da un'altra Bomba temporale, stordisce i nemici.", "tooltip": "Zilean lancia una bomba a tempo che si attacca alla prima unità che entra in una piccola area attorno ad essa. Dopo {{ e2 }} secondi, la bomba esplode, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />Piazzare una seconda bomba su un'unità che ne ha già una, fa scoppiare immediatamente la prima e <status>stordisce</status> i nemici coinvolti nella detonazione per {{ e4 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>", "<PERSON><PERSON> stordimento:"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ bombbasedamage }} -> {{ bombbasedamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [75, 115, 165, 230, 300], [3, 3, 3, 3, 3], [7, 7, 7, 7, 7], [1.1, 1.2, 1.3, 1.4, 1.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/115/165/230/300", "3", "7", "1.1/1.2/1.3/1.4/1.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZileanQ.png", "sprite": "spell17.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZileanW", "name": "Ricarica accelerata", "description": "Zilean si prepara per i successivi incontri, riducendo i tempi di ricarica di tutte le sue altre abilità base.", "tooltip": "Zilean riavvolge il tempo, riducendo la ricarica delle sue altre abilità di base di {{ e2 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [35, 35, 35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [20, 30, 40, 50, 60], [10, 10, 10, 10, 10], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/30/40/50/60", "10", "35", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "ZileanW.png", "sprite": "spell17.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ e3 }} mana"}, {"id": "TimeWarp", "name": "Incantesimo temporale", "description": "Zilean piega il tempo attorno a qualsiasi unità, diminuendo la velocità di movimento di un nemico o aumentando la velocità di movimento di un alleato per un breve periodo.", "tooltip": "<PERSON><PERSON>an <status>rallenta</status> un campione nemico del {{ e2 }}% o conferisce <speed>{{ e2 }}% velocità di movimento</speed> a un campione alleato per {{ e1 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Rallentamento", "Velocità di movimento"], "effect": ["{{ e2 }}% -> {{ e2NL }}%", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [2.5, 2.5, 2.5, 2.5, 2.5], [40, 55, 70, 85, 99], [1.5, 1.5, 1.5, 1.5, 1.5], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "2.5", "40/55/70/85/99", "1.5", "8", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TimeWarp.png", "sprite": "spell17.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ChronoShift", "name": "Spostamento temporale", "description": "Zilean piazza una runa temporale protettiva su un campione alleato, riportandolo indietro nel tempo a prima che subisse un danno letale.", "tooltip": "Zilean conferisce una runa temporale protettiva a un campione alleato per {{ rduration }} secondi. Se il bersaglio muore, la runa riavvolge il tempo e lo mette in Stasi per {{ revivestateduration }} secondi, per poi riportarlo in vita, ripristinando <healing>{{ rtotalheal }} salute.</healing>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "Guarigione"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ rbaseheal }} -> {{ rbasehealNL }}"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [125, 150, 175], "costBurn": "125/150/175", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "ChronoShift.png", "sprite": "spell17.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Tempo in bottiglia", "description": "Zilean immagazzina il tempo sotto forma di Esperienza e può conferirlo ai suoi alleati. Quando ha accumulato abbastanza Esperienza per completare l'aumento di livello di un alleato, può cliccarlo con il pulsante destro per conferirgliela. Zilean riceve la stessa quantità di Esperienza che conferisce.", "image": {"full": "Zilean_Passive.png", "sprite": "passive5.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}