{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Brand": {"id": "Brand", "key": "63", "name": "Brand", "title": "la vendetta ardente", "image": {"full": "Brand.png", "sprite": "champion0.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "63000", "num": 0, "name": "default", "chromas": false}, {"id": "63001", "num": 1, "name": "Brand Apocalittico", "chromas": false}, {"id": "63002", "num": 2, "name": "<PERSON> Vandalo", "chromas": false}, {"id": "63003", "num": 3, "name": "Brand Crio-nucleo", "chromas": false}, {"id": "63004", "num": 4, "name": "Brand Zombi", "chromas": false}, {"id": "63005", "num": 5, "name": "Brand Fuoco Spirituale", "chromas": false}, {"id": "63006", "num": 6, "name": "Brand Boss da Battaglia", "chromas": false}, {"id": "63007", "num": 7, "name": "Brand della Luce", "chromas": true}, {"id": "63008", "num": 8, "name": "Brand Drago Immortale", "chromas": true}, {"id": "63021", "num": 21, "name": "<PERSON> Ruba<PERSON>", "chromas": true}, {"id": "63022", "num": 22, "name": "Brand Rubacuori (edizione prestigio)", "chromas": false}, {"id": "63033", "num": 33, "name": "Brand Demoni della strada", "chromas": true}, {"id": "63042", "num": 42, "name": "Brand Empireo", "chromas": true}], "lore": "La creatura nota come Brand, un tempo chiamata <PERSON>, membro di una tribù del gelido Freljord, è una lezione sulla tentazione presentata dal potere assoluto. Durante la ricerca delle leggendarie rune del mondo, <PERSON>gan tradì i suoi compagni e le tenne per se stesso. Un istante dopo smise di essere uomo. Con l'anima bruciata e un corpo di fiamme vive, Brand ora vaga per Valoran alla ricerca di altre rune, giurando vendetta per dei torti che non avrebbe mai potuto subire neppure in una dozzina di vite mortali.", "blurb": "La creatura nota come Brand, un tempo chiamata <PERSON>, membro di una tribù del gelido Freljord, è una lezione sulla tentazione presentata dal potere assoluto. Durante la ricerca delle leggendarie rune del mondo, <PERSON>gan tradì i suoi compagni e le...", "allytips": ["Puoi dissuadere i nemici dallo stare vicino ai loro minion incendian<PERSON>li, usando Conflagrazione.", "Puoi usare le abilità di Brand in varie combinazioni per massimizzare i danni in situazioni differenti.", "Cataclisma di fuoco rimbalza a caso tra i nemici: prova a lanciarlo contro un piccolo gruppo di nemici, se vuoi colpire lo stesso nemico più volte."], "enemytips": ["Brand deve prima colpire con una delle sue abilità per iniziare la sua combo. Se riesci a schivare Fuoco ardente o Pilastro di fiamme disturberai il suo ritmo.", "Muoviti lontano dai tuoi alleati quando vedi che Cataclisma di fuoco è stato lanciato. La velocità del proiettile è bassa all'inizio e dovrebbe dare il tempo alla tua squadra di reagire.", "L'abilità passiva di Brand gli permette di eccellere contro le squadre raggruppate. Cerca di dividerti dai compagni, contro di lui."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 4}, "stats": {"hp": 570, "hpperlevel": 105, "mp": 469, "mpperlevel": 21, "movespeed": 340, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 9, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.681}, "spells": [{"id": "BrandQ", "name": "Fuoco ardente", "description": "Brand lancia una palla di fuoco che infligge danni magici. Se il bersaglio è in fiamme, Fuoco ardente lo stordisce per @StunDuration@ secondi.", "tooltip": "Brand lancia una palla di fuoco che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> al primo nemico colpito.<br /><br />Se il bersaglio è <keywordMajor>in fiamme</keywordMajor>, viene <status>stordito</status> per {{ stunduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [70, 100, 130, 160, 190], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/100/130/160/190", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1050, 1050, 1050, 1050, 1050], "rangeBurn": "1050", "image": {"full": "BrandQ.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandW", "name": "Pilastro di fiamme", "description": "Dopo un breve ritardo, Brand crea un Pilastro di fiamme nell'area bersaglio, infliggendo danni magici alle unità nemiche nella zona. Le unità in fiamme subiscono il 25% di danni in più.", "tooltip": "Brand crea una colonna di fuoco puro, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>.<br /><br />Le unità <keywordMajor>in fiamme</keywordMajor> subiscono invece <magicDamage>{{ empowereddamage }} danni</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [20, 40, 60, 80, 100], [0.25, 0.25, 0.25, 0.25, 0.25], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "20/40/60/80/100", "0.25", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "BrandW.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandE", "name": "Conflagrazione", "description": "Brand lancia contro il bersaglio una potente scarica che si propaga ai nemici vicini, infliggendo danni magici. Se il bersaglio è in fiamme, la propagazione di Conflagrazione è raddoppiata.", "tooltip": "Brand lancia una potente scarica contro il bersaglio, infliggendo <magicDamage>{{ edamagecalc }} danni magici</magicDamage> alle unità circostanti.<br /><br />Se il bersaglio è <keywordMajor>in fiamme</keywordMajor>, la propagazione è raddoppiata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [375, 375, 375, 375, 375], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "375", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "BrandE.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BrandR", "name": "Cataclisma di fuoco", "description": "Brand scatena un devastante fiume di fuoco che rimbalza tra lui e i nemici vicini fino a 5 volte, infliggendo danni magici agli avversari a ogni rimbalzo. I rimbalzi danno la priorità al raggiungimento del massimo delle cariche di Fiammata sui campioni. Se il bersaglio è in fiamme, Cataclisma di fuoco lo rallenta brevemente.", "tooltip": "Brand scatena un devastante fiume di fuoco che può rimbalzare tra lui e un altro nemico fino a 5 volte, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> agli avversari a ogni rimbalzo. I rimbalzi danno la priorità al raggiungimento del massimo delle cariche di <keywordMajor>Fiammata</keywordMajor> sui campioni.<br /><br />Se il bersaglio è <keywordMajor>in fiamme</keywordMajor>, viene <status>rallentato</status> brevemente del {{ slowamount }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> per rim<PERSON>zo", "Rallentamento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount }}% -> {{ slowamountNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 100, 90], "cooldownBurn": "110/100/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750], "rangeBurn": "750", "image": {"full": "BrandR.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>", "description": "Le abilità di Brand infiammano i suoi bersagli, infliggendo danni nell'arco di 4 secondi, cumulabili fino a 3 volte. Se Brand uccide un nemico in fiamme ottiene mana. Quando Fiammata raggiunge il massimo delle cariche su un campione o un mostro grande, Fiammata diventa instabile. Esplode in 2 secondi, applicando gli effetti delle abilità e infliggendo danni ingenti in un'area attorno alla vittima.", "image": {"full": "BrandP.png", "sprite": "passive0.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}