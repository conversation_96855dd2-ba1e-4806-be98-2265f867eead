{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"DrMundo": {"id": "DrMundo", "key": "36", "name": "Dr. <PERSON>", "title": "nebu<PERSON><PERSON> <PERSON> Zaun", "image": {"full": "DrMundo.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "36000", "num": 0, "name": "default", "chromas": false}, {"id": "36001", "num": 1, "name": "Dr. <PERSON>", "chromas": false}, {"id": "36002", "num": 2, "name": "Mister <PERSON>", "chromas": false}, {"id": "36003", "num": 3, "name": "Mundo corporatist", "chromas": true}, {"id": "36004", "num": 4, "name": "Mundo Mundo", "chromas": false}, {"id": "36005", "num": 5, "name": "Mundo că<PERSON>ul", "chromas": false}, {"id": "36006", "num": 6, "name": "<PERSON>ndo, furie nestăpânită", "chromas": false}, {"id": "36007", "num": 7, "name": "Mundo TPA", "chromas": false}, {"id": "36008", "num": 8, "name": "Mundo la piscină", "chromas": false}, {"id": "36009", "num": 9, "name": "Mundo El Macho", "chromas": false}, {"id": "36010", "num": 10, "name": "Mundo, prințul înghețat", "chromas": true}, {"id": "36021", "num": 21, "name": "<PERSON>. <PERSON><PERSON> demonii str<PERSON>", "chromas": true}], "lore": "<PERSON><PERSON><PERSON> de legat, ucigaș fără milă și teribil de violet, Dr. <PERSON><PERSON> este motivul pentru care mulți dintre cetățenii Zaunului se închid în case în nopțile deosebit de întunecate. Acum un doctor auto-proclamat, a fost cândva un pacient al celui mai infam ospiciu din Zaun. Dup<PERSON> ''vindecarea'' tuturor membrilor personalului, Dr. Mundo și-a început activitatea în secțiile goale în care cândva a fost tratat și a început să imite procedurile extrem de imorale la care a fost adesea supus el însuși. Cu un dulap plin de medicamente și fără cunoștințe medicale, devine din ce în ce mai monstruos cu fiecare injecție pe care și-o administrează și îngrozește bieții ''pacienți'' care trec pe lângă cabinetul lui.", "blurb": "<PERSON><PERSON><PERSON> de legat, ucigaș fără milă și teribil de violet, Dr. <PERSON><PERSON> este motivul pentru care mulți dintre cetățenii Zaunului se închid în case în nopțile deosebit de întunecate. Acum un doctor auto-proclamat, a fost cândva un pacient al celui mai infam...", "allytips": ["Un ''Sadism'' folosit la momentul oportun poate păcăli campionii inamici să te atace chiar și când nu îți pot produce suficiente daune cât să te ucidă.", "''Înfățișarea sufletului'' îți crește vindecarea de la abilitatea ta supremă și îți reduce timpii de reactivare ai tuturor abilităților.", "Satârele sunt foarte bune la uciderea monștrilor neutri. În loc să revii în bază, vânează monștri neutri până te poți vindeca folosindu-ți abilitatea supremă."], "enemytips": ["Încearcă să-ți sincronizezi abilitățile mai puternice cu cele ale aliaților imediat după ce Dr. Mundo își folosește abilitatea supremă; dacă nu îl puteți omorî rapid, se va vindeca și va scăpa.", "Încearcă să folo<PERSON> ''Igniție'' când Dr<PERSON> ''Sadismul'' pentru a-l face să se vindece mai puțin."], "tags": ["Tank", "Fighter"], "partype": "Inexistentă", "info": {"attack": 5, "defense": 7, "magic": 6, "difficulty": 5}, "stats": {"hp": 640, "hpperlevel": 103, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 32, "armorperlevel": 3.7, "spellblock": 29, "spellblockperlevel": 2.3, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.3, "attackspeed": 0.67}, "spells": [{"id": "DrMundoQ", "name": "Fierăstrău infectat", "description": "Dr. <PERSON><PERSON> aruncă un fierăstrău infectat, provocându-i primului inamic lovit daune în funcție de viața actuală a acestuia și încetinindu-l.", "tooltip": "Dr. <PERSON>ndo î<PERSON><PERSON> arun<PERSON><PERSON> fier<PERSON><PERSON><PERSON><PERSON><PERSON>, provocând <magicDamage>daune magice în valoare de {{ currenthealthdamage*100 }}% din viața actuală</magicDamage> primului inamic lovit și <status>încetinindu-l</status> cu {{ slowamount*100 }}% timp de {{ slowduration }} secunde.<br /><br /><PERSON><PERSON><PERSON> fierăstrăul lovește un campion sau un monstru, Dr. <PERSON>ndo î<PERSON><PERSON> reface <healing>{{ healthrestoreonhitchampionmonster }} viață</healing>. Dac<PERSON> lovește un non-campion sau un non-monstru, Dr. Mundo î<PERSON> reface <healing>{{ healthrestoreonhitminion }} viață</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune din viața actuală", "Daune minime", "Limită daune împotriva monștrilor", "Cost de viață"], "effect": ["{{ currenthealthdamage*100.000000 }}% -> {{ currenthealthdamagenl*100.000000 }}%", "{{ minimumdamage }} -> {{ minimumdamageNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}", "{{ healthcost }} -> {{ healthcostNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " viață", "maxammo": "-1", "range": [975, 975, 975, 975, 975], "rangeBurn": "975", "image": {"full": "DrMundoQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ healthcost }} viață"}, {"id": "DrMundoW", "name": "Defibrilator", "description": "Dr. <PERSON><PERSON> se electrocutează, provocându-le daune persistente inamicilor din apropiere și stocând o parte din daunele pe care le suferă. La sfârșitul efectului sau dacă refolosește abilitatea, Dr. <PERSON><PERSON> le provoacă o rafală de daune inamicilor din apropiere. Dacă rafala lovește un inamic, Mundo își reface viață egală cu un procent din daunele stocate.", "tooltip": "Dr. <PERSON><PERSON>nc<PERSON><PERSON> un defibrilator, provocându-le <magicDamage>{{ damagepertick*4 }} daune magice</magicDamage> pe secundă inamicilor din apropiere timp de până la {{ duration }} secunde. În plus, stochează {{ grayhealthstorageinitial }} din daunele suferite în primele {{ grayhealthinitialduration }} secunde și {{ grayhealthstorage*100 }}% pentru restul duratei ca viață cenușie și poate <recast>reactiva</recast> abilitatea.<br /><br /><recast>Reactivare:</recast> detonează defibrilatorul, provocându-le <magicDamage>{{ totaldamage }} daune magice</magicDamage> inamicilor din apropiere. Dacă lovește cel puțin un campion, Dr. Mundo își reface <healing>{{ grayhealthbigmod*100 }}% din viața cenușie</healing>. <PERSON><PERSON><PERSON>, î<PERSON>i reface <healing>{{ grayhealthsmallmod*100 }}% din viața cenușie</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune per declan<PERSON>are", "Daune refolosire", "Timp de reactivare"], "effect": ["{{ damagepertick }} -> {{ damagepertickNL }}", "{{ recastbasedamage }} -> {{ recastbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [17, 16.5, 16, 15.5, 15], "cooldownBurn": "17/16.5/16/15.5/15", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% din viața actuală", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "DrMundoW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ currenthealthcost*100 }}% din viața actuală"}, {"id": "DrMundoE", "name": "Lovi<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> – Dr. <PERSON><PERSON> prime<PERSON> daune bonus din atac, care cresc în funcție de viața sa maximă.<br><br><PERSON>ivă – Dr. <PERSON><PERSON> î<PERSON>i aruncă trusa ''medicală'' către un inamic, provocându-i daune suplimentare în funcție de viața sa lipsă. Da<PERSON><PERSON> inamicul moare, e înlăturat, provocându-le daune inamicilor prin care trece.", "tooltip": "<spellPassive>Pasiv<PERSON>:</spellPassive> Dr. <PERSON><PERSON> <physicalDamage>{{ passivebonusad }} daune din atac</physicalDamage>.<br /><br /><spellActive>Activă:</spellActive> Dr. <PERSON><PERSON> love<PERSON>te violent cu trusa ''medicală'', iar următorul său atac provoacă <physicalDamage>{{ additionaldamage }} daune fizice</physicalDamage> suplimentare, care cresc cu până la {{ maxdamageamptooltip }} în funcție de viața sa lipsă. Dacă inamicul este ucis, Mundo îl aruncă în spate, provocându-le <physicalDamage>{{ additionaldamage }} daune fizice</physicalDamage> inamicilor prin care trece.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Timp de reactivare", "Daune de bază", "Cost de viață", "Viață în daune din atac"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ flathealthcost }} -> {{ flathealthcostNL }}", "{{ healthtoadratio*100.000000 }}% -> {{ healthtoadrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [9, 8.25, 7.5, 6.75, 6], "cooldownBurn": "9/8.25/7.5/6.75/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " viață", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "DrMundoE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ flathealthcost }} viață"}, {"id": "DrMundoR", "name": "Doză maximă", "description": "Dr. <PERSON><PERSON><PERSON>i injectează substanțe chimice, refăcându-și instantaneu un procent din viața lipsă. Apoi primește un bonus la viteza de mișcare și își regenerează o parte din viața sa maximă de-a lungul unei perioade îndelungate.", "tooltip": "Dr. <PERSON>ndo se injectează cu substanțe chimice, primind <healing>viață maximă în valoare de {{ missinghealthheal*100 }}% din viața lipsă</healing>, <speed>{{ speedboostamount*100 }}% vitez<PERSON> de mișcare</speed> și regenerându-și <healing>{{ maxhealthhot*100 }}% din viața maximă</healing> de-a lungul a {{ duration }} secunde.<br /><br />La nivelul 3, ambele efecte de vindecare cresc cu încă {{ bonuspernearbychampion*100 }}% per campion inamic din apropiere.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Viaț<PERSON> bonus", "Viteză de mișcare", "% viață maximă"], "effect": ["{{ missinghealthheal*100.000000 }}% -> {{ missinghealthhealnl*100.000000 }}%", "{{ speedboostamount*100.000000 }}% -> {{ speedboostamountnl*100.000000 }}%", "{{ maxhealthhot*100.000000 }}% -> {{ maxhealthhotnl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "Fără cost", "maxammo": "-1", "range": [20, 20, 20], "rangeBurn": "20", "image": {"full": "DrMundoR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "Fără cost"}], "passive": {"name": "Merge unde vrea", "description": "Dr. <PERSON>ndo rezistă în fața următorului efect de imobilizare care îl lovește, pierzând în schimb viață și aruncând un flacon cu substanțe chimice în apropiere. Dr. Mundo îl poate ridica călcând pe el, refăcându-și viață și reducând timpul de reactivare al acestei abilități.<br><br>Dr. Mundo are și regenerarea vieții crescută semnificativ.<br>", "image": {"full": "DrMundo_P.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}