{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Blitzcrank": {"id": "Blitzcrank", "key": "53", "name": "Blitzcrank", "title": "il grande golem a vapore", "image": {"full": "Blitzcrank.png", "sprite": "champion0.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "53000", "num": 0, "name": "default", "chromas": true}, {"id": "53001", "num": 1, "name": "Blitzcrank <PERSON>", "chromas": false}, {"id": "53002", "num": 2, "name": "Blitzcrank Portiere", "chromas": false}, {"id": "53003", "num": 3, "name": "Blitzcrank Boom Boom", "chromas": false}, {"id": "53004", "num": 4, "name": "Blitzcrank Truccato a Piltover", "chromas": false}, {"id": "53005", "num": 5, "name": "Sicuramente non Blitzcrank", "chromas": false}, {"id": "53006", "num": 6, "name": "iBlitzcrank", "chromas": false}, {"id": "53007", "num": 7, "name": "Riot Blitzcrank ", "chromas": false}, {"id": "53011", "num": 11, "name": "Blitzcrank Boss da Battaglia", "chromas": true}, {"id": "53020", "num": 20, "name": "Blitzcrank Lanciere Solitario", "chromas": false}, {"id": "53021", "num": 21, "name": "Blitzcrank Lanciere Eroico", "chromas": false}, {"id": "53022", "num": 22, "name": "Blitzcrank Intruglio Magico", "chromas": true}, {"id": "53029", "num": 29, "name": "Blitz e Crank Rit<PERSON>", "chromas": true}, {"id": "53036", "num": 36, "name": "Blitzcrank Vittorioso", "chromas": true}, {"id": "53047", "num": 47, "name": "Blitzcrank dei Giochi dello Zenith", "chromas": true}, {"id": "53056", "num": 56, "name": "<PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Blitzcrank è un enorme e quasi indistruttibile automa di Zaun, progettato in origine per eliminare i rifiuti tossici. Trovando il suo scopo troppo limitante, si modificò per servire i fragili abitanti del Sump. Blitzcrank mette la sua forza al servizio del prossimo, usando generosamente i suoi pugni metallici e la sua energia per sottomettere i facinorosi.", "blurb": "Blitzcrank è un enorme e quasi indistruttibile automa di Zaun, progettato in origine per eliminare i rifiuti tossici. Trovando il suo scopo troppo limitante, si modificò per servire i fragili abitanti del Sump. Blitzcrank mette la sua forza al servizio...", "allytips": ["La combo 1-2-3 di Presa razzo, Superpugno e Campo statico può devastare un singolo avversario.", "Usare la presa di Blitzcrank per tirare un nemico nella gittata della torre seguita da un Superpugno permette alla torre di colpirlo diverse volte."], "enemytips": ["L'abilità passiva di Blitzcrank, Barriera di mana, gli conferisce uno scudo quando ha la salute bassa.", "Stai dietro ai minion per non farti prendere dalla Presa razzo. La Presa razzo di Blitzcrank tira a sé il primo nemico che incontra."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 8, "magic": 5, "difficulty": 4}, "stats": {"hp": 600, "hpperlevel": 109, "mp": 267, "mpperlevel": 40, "movespeed": 325, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 8.5, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.13, "attackspeed": 0.625}, "spells": [{"id": "RocketGrab", "name": "Presa razzo", "description": "Blitzcrank spara la sua mano destra alla ricerca di un avversario da afferrare, infliggendogli danni e portandolo a sé.", "tooltip": "Blitzcrank spara la sua mano destra, <status>tirando</status> il primo nemico colpito verso di sé e infliggendogli <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [100, 100, 100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [90, 140, 190, 240, 290], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "90/140/190/240/290", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1079, 1079, 1079, 1079, 1079], "rangeBurn": "1079", "image": {"full": "RocketGrab.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Overdrive", "name": "Ipervelocità", "description": "Blitzcrank si sovraccarica per ottenere molta velocità di movimento e di attacco. Al termine dell'effetto, rallenta temporaneamente.", "tooltip": "Blitzcrank si sovraccarica, ottenendo <speed>{{ movespeedmod*100 }}% velocità di movimento decrescente</speed> e <attackSpeed>{{ attackspeedmod*100 }}% velocità d'attacco</attackSpeed> per {{ duration }} secondi.<br /><br /><PERSON><PERSON><PERSON><PERSON><PERSON>, Blitzcrank viene <status>rallentato</status> di un {{ movespeedmodreduction*100 }}% per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità di movimento", "Velocità d'attacco"], "effect": ["{{ movespeedmod*100.000000 }}% -> {{ movespeedmodnl*100.000000 }}%", "{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [15, 15, 15, 15, 15], "cooldownBurn": "15", "cost": [75, 75, 75, 75, 75], "costBurn": "75", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "Overdrive.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "PowerFist", "name": "Superpugno", "description": "Blitzcrank carica il suo pugno in modo che il prossimo attacco infligga il doppio dei danni e lanci il bersaglio in aria.", "tooltip": "Blitzcrank carica il pugno e con il prossimo attacco <status>lancia in aria</status> per {{ ccduration }} secondo/i e infligge <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 25, 25, 25, 25], "costBurn": "25", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "PowerFist.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "StaticField", "name": "Campo statico", "description": "I nemici attaccati da Blitzcrank vengono marchiati e subiscono danni da fulmini dopo 1 secondo. In più, Blitzcrank può attivare questa abilità per rimuovere gli scudi dei nemici vicini, dannegg<PERSON><PERSON>li e silenziandoli per poco tempo.", "tooltip": "<spellPassive>Passiva: </spellPassive>mentre quest'abilità è disponibile, i pugni di Blitzcrank sono carichi di fulmini e marchiano chi viene attaccato. Dopo 1 secondo, sono folgorati per <magicDamage>{{ passivedamage }} danni magici</magicDamage>.<br /><br /><spellActive>Attiva: </spellActive>Blitzcrank si sovraccarica e infligge <magicDamage>{{ activedamage }} danni magici</magicDamage> ai nemici vicini, <status>silenziandoli</status> per {{ silenceduration }} secondi. I loro scudi vengono inoltre distrutti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni base della passiva", "Rapporto potere magico passiva", "Danni base dell'attiva", "Ricarica attiva"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ passiveapratio*100.000000 }}% -> {{ passiveaprationl*100.000000 }}%", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [60, 40, 20], "cooldownBurn": "60/40/20", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "StaticField.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Barriera di mana", "description": "Blitzcrank ottiene uno scudo in base al suo mana quando ha poca salute.", "image": {"full": "Blitzcrank_ManaBarrier.png", "sprite": "passive0.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}