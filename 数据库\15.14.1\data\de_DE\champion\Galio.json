{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Galio": {"id": "<PERSON><PERSON><PERSON>", "key": "3", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON>", "image": {"full": "Galio.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "3000", "num": 0, "name": "default", "chromas": false}, {"id": "3001", "num": 1, "name": "Verzauberter Galio", "chromas": false}, {"id": "3002", "num": 2, "name": "Hextech-Galio", "chromas": false}, {"id": "3003", "num": 3, "name": "Kommando-Galio", "chromas": false}, {"id": "3004", "num": 4, "name": "Torwächter Galio", "chromas": false}, {"id": "3005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "3006", "num": 6, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "3013", "num": 13, "name": "Infernalischer Galio", "chromas": true}, {"id": "3019", "num": 19, "name": "Drachenwächter Galio", "chromas": true}, {"id": "3028", "num": 28, "name": "Sagenschöpfer Galio", "chromas": true}], "lore": "Außerhalb der glänzenden Stadt Demacia hält der steinerne Koloss Galio aufmerksam Wache. Er wurde erbaut, um feindlichen Magiern standhalten zu können, und steht oft Dekaden lang regungslos an einem Ort, bis mächtige Magie ihn erneut erweckt. <PERSON><PERSON>d er aktiviert ist, nutzt Galio seine Zeit ausgiebig, rauscht mit Freude durch die Luft und genießt die seltene Ehre, seine Landsleute schützen zu dürfen. Jeder Triumph ist jedoch immer auch bittersüß, denn die Magie, die er bekämpft, ist die Quelle seiner Lebenskraft, und so fällt er nach jedem <PERSON> wieder in einen tiefen Schlaf.", "blurb": "Außerhalb der glänzenden Stadt Demacia hält der steinerne Koloss Galio aufmerksam Wache. Er wurde erbaut, um feindlichen Magiern standhalten zu können, und steht oft Dekaden lang regungslos an einem Ort, bis mächtige Magie ihn erneut erweckt. Sobald er...", "allytips": ["<PERSON> kannst „<PERSON><PERSON><PERSON> auch ausführen, wenn du unter Massenkontrolle stehst.", "Du kannst die Symbole der Verbündeten auf der Minikarte nutzen, um „Heldenhafter Auftritt“ auszuführen.", "Du kannst dir den Rückwärtsschritt von „Durchschlagende Gerechtigkeit“ zu<PERSON><PERSON> machen, um gegnerischen Fähigkeiten auszuweichen."], "enemytips": ["<PERSON><PERSON><PERSON> bewegt sich langsamer, während er „<PERSON><PERSON><PERSON> auflädt.", "„Heldenhafter Auftritt“ kann unterbrochen werden, bevor sich <PERSON>o in die Lüfte schwingt.", "G<PERSON><PERSON> kann mit „Durchschlagende Gerechtigkeit“ keine Mauern durchqueren."], "tags": ["Tank", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 1, "defense": 10, "magic": 6, "difficulty": 5}, "stats": {"hp": 600, "hpperlevel": 126, "mp": 410, "mpperlevel": 40, "movespeed": 340, "armor": 24, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 150, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 9.5, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 3.5, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "GalioQ", "name": "Winde des Krieges", "description": "Galio schickt 2&nbsp;Windstöße los, die aufeinandertreffen und sich zu einem gewaltigen Tornado vereinigen, der Schaden über Zeit verursacht.", "tooltip": "Galio feuert zwei Windstöße ab, die jeweils <magicDamage>{{ qmissiledamage }}&nbsp;magischen Schaden</magicDamage> verursachen. Sobald die Windstöße aufeinandertreffen, vereinigen sie sich zu einem Tornado, der über {{ superqduration }}&nbsp;Sekunden hinweg <magicDamage>magischen Schaden</magicDamage> in Hö<PERSON> von {{ percentsuperqdamagett }}&nbsp;% des maximalen Lebens verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Windstoßschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "GalioQ.png", "sprite": "spell4.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GalioW", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> lädt eine defensive Haltung auf und bewegt sich langsamer. Entlädt Galio die aufgeladene Energie, verspottet er nahe Gegner und fügt ihnen Schaden zu.", "tooltip": "<spellPassive>Passiv:</spellPassive> Wen<PERSON> {{ passiveshieldooctimer }}&nbsp;Seku<PERSON>(n) lang keinen Schaden erleidet, erhält er einen <shield>Magieschild</shield> in <PERSON><PERSON><PERSON> von {{ totalpassiveshield }}.<br /><br /><charge>Aufladungsbeginn:</charge> Galio verringert eingehenden magischen Schaden um {{ magicdamagereduction }}, eingehenden normalen Schaden um {{ physicaldamagereduction }} und <status>verlangsamt</status> sich selbst um {{ e3 }}&nbsp;%.<br /><br /><release>Aktivierung:</release> Galio <status>verspottet</status> gegnerische Champions zwischen {{ e4 }} und {{ e7 }}&nbsp;Sekunde(n) lang, fügt ihnen zwischen <magicDamage>{{ mintotaldamage }}</magicDamage> und <magicDamage>{{ maxtotaldamage }}&nbsp;magischen Schaden</magicDamage> zu und erneuert die Schadensverringerung für {{ e8 }}&nbsp;Sekunden. Dauer, Schaden und Reichweite der Verspottung erhöhen sich mit der Aufladedauer.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schildlebensrate", "Schadensverringerung (magischer Schaden)", "Schadensverringerung (normaler Schaden)", "<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ passiveshieldhealthratio*100.000000 }}&nbsp;% -> {{ passiveshieldhealthrationl*100.000000 }}&nbsp;%", "{{ e1 }}&nbsp;% -> {{ e1NL }}&nbsp;%", "{{ effect1amount*0.500000 }}&nbsp;% -> {{ effect1amountnl*0.500000 }}&nbsp;%", "{{ maximumwbasedamage }} -> {{ maximumwbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 17, 16, 15, 14], "cooldownBurn": "18/17/16/15/14", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [2, 2, 2, 2, 2], [15, 15, 15, 15, 15], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [1, 1, 1, 1, 1], [1.5, 1.5, 1.5, 1.5, 1.5], [2, 2, 2, 2, 2], [1.25, 1.25, 1.25, 1.25, 1.25], [4, 4, 4, 4, 4]], "effectBurn": [null, "25/30/35/40/45", "2", "15", "0.5", "0", "1", "1.5", "2", "1.25", "4"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "GalioW.png", "sprite": "spell4.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GalioE", "name": "Durchschlagende Gerechtigkeit", "description": "G<PERSON>o macht einen kleinen Schritt zurück, um dann anzugreifen und den ersten gegnerischen Champion, den er trifft, in die Luft zu schleudern.", "tooltip": "<PERSON>ali<PERSON> springt mit einem mächtigen <PERSON>eb nach vorne, wodurch der erste getroffene Champion {{ knockupduration }}&nbsp;Sekunden lang <status>hochgeschleudert</status> wird und <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> erleidet. <PERSON><PERSON>, die im Weg stehen, erleiden <magicDamage>{{ pvedamage }}&nbsp;magischen Schaden</magicDamage>.<br /><br /><PERSON><PERSON><PERSON> ble<PERSON><PERSON> stehen, wenn er Terrain trifft.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "GalioE.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "GalioR", "name": "Heldenhafter Auftritt", "description": "Galio wählt die Position eines verbündeten Champions als Landeplatz aus und gewährt allen Verbündeten in der Umgebung einen magischen Schild. Nach kurzer Verzögerung schlägt Galio am Zielort ein und schleudert nahe Gegner in die Luft.", "tooltip": "Galio wählt die Position eines verbündeten Champions als Landeplatz aus und gewährt allen verbündeten Champions im Wirkbereich {{ temporarywshieldduration }}&nbsp;Sekunden lang den passiven <shield><PERSON><PERSON><PERSON></shield> seines <spellName><PERSON><PERSON><PERSON></spellName>. Dann fliegt <PERSON>o zu seinem Zielort.<br /><br /><PERSON><PERSON> G<PERSON>o landet, <status>schleudert</status> er Einheiten {{ stundurationouter }}&nbsp;Sekunden lang hoch und verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Reichweite", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [4000, 4750, 5500], "rangeBurn": "4000/4750/5500", "image": {"full": "GalioR.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Alle paar Sekunden verursacht Galios nächster normaler Angriff zusätzlichen magischen Flächenschaden.", "image": {"full": "Galio_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}