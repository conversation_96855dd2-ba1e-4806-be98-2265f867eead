{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Rumble": {"id": "Rumble", "key": "68", "name": "Rumble", "title": "la minaccia meccanizzata", "image": {"full": "Rumble.png", "sprite": "champion3.png", "group": "champion", "x": 96, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "68000", "num": 0, "name": "default", "chromas": false}, {"id": "68001", "num": 1, "name": "Rumble Tropicale", "chromas": false}, {"id": "68002", "num": 2, "name": "Rumble Topo da Stiva", "chromas": false}, {"id": "68003", "num": 3, "name": "Rumble Megagalattico", "chromas": false}, {"id": "68004", "num": 4, "name": "Rumble Barone delle Terre Desolate", "chromas": true}, {"id": "68013", "num": 13, "name": "Rumble <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "68023", "num": 23, "name": "<PERSON><PERSON>", "chromas": true}], "lore": "Rumble è un giovane inventore con un certo caratterino. <PERSON><PERSON><PERSON><PERSON><PERSON> solo le sue mani e un cumulo di rottami, l'energico yordle ha costruito un colossale mech dotato di un arsenale di arpioni elettrificati e razzi incendiari. Anche se alcuni deridono le sue creazioni, Rumble non se ne cura: dopotutto, lui ha un lanciafiamme dalla sua.", "blurb": "Rumble è un giovane inventore con un certo caratterino. <PERSON><PERSON><PERSON><PERSON><PERSON> solo le sue mani e un cumulo di rottami, l'energico yordle ha costruito un colossale mech dotato di un arsenale di arpioni elettrificati e razzi incendiari. Anche se alcuni deridono le...", "allytips": ["Tieni il ritmo e cerca di stare nella Zona pericolosa per massimizzare la tua efficacia. È facile surriscaldarsi se usi troppo rapidamente le tue abilità.", "Cerca di tenere i bersagli nella gittata del tuo Sputafuoco. Può infliggere diversi danni nel tempo.", "Se stai vincendo un combattimento, puoi usare la tua abilità suprema per bloccare la via di fuga del nemico."], "enemytips": ["Fai particolare attenzione alla barra del calore di Rumble. Se lo vedi surriscal<PERSON>si, uccidilo mentre le sue abilità sono disabilitate.", "L'abilità suprema di Rumble può infliggere molti danni se resti all'interno dell'area d'effetto. Quando vedi i missili cadere, allontanati il prima possibile.", "Rumble infligge praticamente solo danni magici. Concentrati su alzare la resistenza magica per ridurre i suoi danni."], "tags": ["Fighter", "Mage"], "partype": "Calore", "info": {"attack": 3, "defense": 6, "magic": 8, "difficulty": 10}, "stats": {"hp": 655, "hpperlevel": 105, "mp": 150, "mpperlevel": 0, "movespeed": 345, "armor": 36, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 1.55, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.85, "attackspeed": 0.644}, "spells": [{"id": "RumbleFlameThrower", "name": "Sputafuoco", "description": "Rumble incendia i nemici davanti a sé, infliggendo danni magici per 3 secondi agli avversari nell'area conica innanzi a lui. Mentre è nella Zona pericolosa, i danni sono aumentati.", "tooltip": "Rumble attiva il suo lanciafiamme, infliggendo <magicDamage>{{ flatdamage }} più un {{ healthdamage*100 }}% della salute massima del bersaglio in danni magici</magicDamage> per {{ flamespitterduration }} secondi, ridotti al <attention>{{ minionmod*100 }}%</attention> contro i minion.<br /><br /><keywordMajor>Zona pericolosa:</keywordMajor> il danno aumenta a <magicDamage>{{ empowereddamage }} più {{ empoweredhealth }} della salute massima</magicDamage> del bersaglio.<br /><br /><rules>La percentuale di danni è limitata a {{ monstercap }} danni contro i mostri. </rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "% salute massima", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ healthdamage*100.000000 }}% -> {{ healthdamagenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Calore", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "RumbleFlameThrower.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ initialheatcost }} Calore"}, {"id": "RumbleShield", "name": "<PERSON><PERSON> di rottami", "description": "Rumble alza uno scudo che lo protegge dai danni e gli conferisce un bonus al movimento. Mentre è nella Zona pericolosa, la forza dello scudo e il bonus alla velocità sono aumentati.", "tooltip": "Rumble alza una barriera, guadagnan<PERSON> <shield>uno scudo da {{ totalshield }}</shield> per {{ shieldduration.1 }} secondi e <speed>{{ movespeed*100 }}% velocità di movimento</speed> per {{ movespeedduration }} secondo.<br /><br /><keywordMajor>Zona pericolosa:</keywordMajor> la barriera conferisce <shield>uno scudo da {{ empoweredshield }}</shield> e <speed>{{ empoweredms }} velocità di movimento</speed>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "Velocità di movimento"], "effect": ["{{ baseshield }} -> {{ baseshieldNL }}", "{{ movespeed*100.000000 }}% -> {{ movespeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [10, 15, 20, 25, 30], [20, 20, 20, 20, 20], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "10/15/20/25/30", "20", "0", "1.5", "1", "0", "0", "0", "0"], "vars": [], "costType": " Calore", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "RumbleShield.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ heatcost }} <PERSON><PERSON>"}, {"id": "RumbleGrenade", "name": "Arpione elettrico", "description": "Rumble lancia un arpione, elettriz<PERSON>do il bersaglio con danni magici, rallentando la sua velocità di movimento e riducendo la sua resistenza magica. Rumble può trasportare fino a 2 arpioni per volta. Mentre è nella Zona pericolosa, i danni e la percentuale di rallentamento sono aumentati.", "tooltip": "Rumble spara un arpione elettrificato che infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>, <status>rallenta</status> di un {{ baseslowamount }}% per {{ slowduration }} secondi, e riduce la <scaleMR>resistenza magica</scaleMR> del nemico di un {{ percmagicpen*100 }}% per {{ shredduration }} secondi.<br /><br />Colpire un nemico già <status>rallentato</status> da questa abilità aumenta il <status>rallentamento</status> a {{ empoweredslowamount }}% e riduce la <scaleMR>resistenza magica</scaleMR> del nemico di un {{ enhancedmagicpen*100 }}%<br /><br /><keywordMajor>Zona pericolosa:</keywordMajor> l'arpione infligge <magicDamage>{{ empdamage }} danni magici</magicDamage> e il <status>rallentamento</status> e la riduzione di <scaleMR>resistenza magica</scaleMR> aumentano del 50%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Resistenza magica ridotta"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseslowamount }}% -> {{ baseslowamountNL }}%", "{{ percmagicpen*100.000000 }}% -> {{ percmagicpennl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " Calore", "maxammo": "2", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "RumbleGrenade.png", "sprite": "spell11.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ firstcastheatcost }} Calore"}, {"id": "RumbleCarpetBomb", "name": "<PERSON><PERSON><PERSON>", "description": "Rumble lancia una salva di razzi, creando un muro di fiamme che infligge danni e rallenta i nemici.", "tooltip": "Rumble lancia una fila di razzi creando una scia bruciante che <status>rallenta</status> del {{ slowamount }}% e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> per {{ trailduration }} secondi.<br /><br />Rumble può controllare la direzione della scia cliccando e trascinando il mouse mentre lancia questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo", "Ricarica"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1750, 1750, 1750], "rangeBurn": "1750", "image": {"full": "RumbleCarpetBomb.png", "sprite": "spell11.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "Titano della discarica", "description": "Ogni abilità che Rumble lancia gli dà Calore. Quando raggiunge il 50% di Calore, si trova nella Zona pericolosa, che conferisce a tutte le sue abilità degli effetti bonus. Quando raggiunge il 100% di Calore, inizia il Surriscaldamento, ottiene velocità d'attacco bonus e infligge danni bonus con i suoi attacchi base, ma non può lanciare abilità per alcuni secondi.", "image": {"full": "Rumble_JunkyardTitan1.png", "sprite": "passive3.png", "group": "passive", "x": 96, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}