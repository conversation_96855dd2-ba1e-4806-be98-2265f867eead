{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MonkeyKing": {"id": "MonkeyKing", "key": "62", "name": "Wukong", "title": "re<PERSON><PERSON>", "image": {"full": "MonkeyKing.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "62000", "num": 0, "name": "default", "chromas": false}, {"id": "62001", "num": 1, "name": "Wukong vulcanic", "chromas": false}, {"id": "62002", "num": 2, "name": "General<PERSON> Wu<PERSON>g", "chromas": false}, {"id": "62003", "num": 3, "name": "Wukong dragonul de jad", "chromas": true}, {"id": "62004", "num": 4, "name": "Wukong din lumea de apoi", "chromas": false}, {"id": "62005", "num": 5, "name": "Wukong, războinicul selenar", "chromas": false}, {"id": "62006", "num": 6, "name": "Wukong, lăncierul norilor", "chromas": false}, {"id": "62007", "num": 7, "name": "Wukong de la Academia de Luptă", "chromas": true}, {"id": "62016", "num": 16, "name": "Wukong, spiritul pădurii", "chromas": true}], "lore": "Wukong este un vastaya viclean, care-și foloseș<PERSON> forța, agilitatea și inteligența pentru a-și încurca inamicii și a obține victoria în luptă. După ce a descoperit un prieten pe viață în războinicul cunoscut drept Master Yi, Wukong a devenit ultimul discipol al artei marțiale antice numite Wuju. Înarmat cu sceptrul său magic, Wukong încearcă să oprească distrugerea Ioniei.", "blurb": "Wukong este un vastaya viclean, care-și foloseș<PERSON> forța, agilitatea și inteligența pentru a-și încurca inamicii și a obține victoria în luptă. După ce a descoperit un prieten pe viață în războinicul cunoscut drept Master Yi, Wukong a devenit ultimul...", "allytips": ["''Derutarea'' și ''Atacul norului'' pot fi combinate eficient pentru o incursiune rapidă asupra inamicului, urmată de retragere înainte să poată riposta.", "Încearcă să folosești ''Derutare'' lângă tufișuri pentru a-ți face inamicul să riposteze exagerat."], "enemytips": ["Wukong va folosi adesea ''Derutare'' după ''Atacul norului''. Încearcă să aștepți puțin înainte să-ți folosești abilitățile, pentru a-l lovi pe adevăratul Wukong.", "Wukong devine mai greu de ucis când este înconjurat de inamici. Încearcă să-l i<PERSON>, pentru a fi în avantaj."], "tags": ["Fighter", "Tank"], "partype": "Mană", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 330, "mpperlevel": 65, "movespeed": 340, "armor": 31, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.69}, "spells": [{"id": "MonkeyKingDoubleAttack", "name": "Explozie zdrobitoare", "description": "Următorul atac al lui Wukong are rază de atac mai mare, provoacă daune bonus și reduce armura țintei timp de câteva secunde.", "tooltip": "Următorul atac al lui Wukong și al <keywordMajor>clonei</keywordMajor> sale primește {{ attackrangebonus }} raz<PERSON> suplimentară, provoacă <physicalDamage>{{ bonusdamagett }} daune fizice</physicalDamage> bonus și reduce <scaleArmor>armura țintei cu {{ armorshredpercent*100 }}%</scaleArmor> timp de {{ shredduration }} secunde.<br /><br />Timpul de reactivare al abilității este redus cu {{ cooldowndecrease }} secunde de fiecare dată când Wukong sau <keywordMajor>clona</keywordMajor> sa lovește un inamic cu un atac sau o abilitate.<br /><br /><rules>Această abilitate declanșează efectele vrăjilor când provoacă daune.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "% Reducere a armurii", "Rază", "Timp de reactivare"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ armorshredpercent*100.000000 }}% -> {{ armorshredpercentnl*100.000000 }}%", "{{ attackrangebonus }} -> {{ attackrangebonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 275, 300, 325, 350], "rangeBurn": "250/275/300/325/350", "image": {"full": "MonkeyKingDoubleAttack.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingDecoy", "name": "R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Wukong devine <font color='#91d7ee'>invizibil</font> și se năpustește într-o direcție, lăsând în urmă o clonă care va ataca inamicii din apropiere.", "tooltip": "Wukong se năpustește și devine <keywordStealth>invizibil</keywordStealth> timp de {{ stealthduration }} secu<PERSON><PERSON>, lăsând în spate o <keywordMajor>clon<PERSON></keywordMajor> staționară timp de {{ cloneduration }} secunde.<br /><br /><keywordMajor>Clona</keywordMajor> atacă inamicii din apropiere cărora Wukong le-a provocat recent daune și îi imită suprema, fiecare lovitură provocându-le {{ clonedamagemod*100 }}% din daunele obișnuite.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Procent daune", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ clonedamagemod*100.000000 }}% -> {{ clonedamagemodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "MonkeyKingDecoy.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingNimbus", "name": "Atacul norului", "description": "Wukong se năpustește spre un inamic-țintă și trimite clone care atacă inamicii din apropierea țintei, provocându-le daune.", "tooltip": "Wukong se năpustește spre un inamic, trimi<PERSON>ând <keywordMajor>clone</keywordMajor> care imită năpustirea spre cel mult {{ extratargets }} alți inamici din apropiere. Fiecare inamic lovit suferă <magicDamage>{{ totaldamage }} daune magice</magicDamage>. El și <keywordMajor>clona</keywordMajor> sa primesc apoi <attackSpeed>{{ attackspeed*100 }}% viteză de atac</attackSpeed> timp de {{ attackspeedduration }} secunde.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune", "Viteză de atac", "Timp de reactivare", "Cost de @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.25, 8.5, 7.75, 7], "cooldownBurn": "10/9.25/8.5/7.75/7", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MonkeyKingNimbus.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MonkeyKingSpinToWin", "name": "Ciclon", "description": "Wukong își extinde toiagul și îl învârte în mod repetat, primind viteză de mișcare.<br><br>Inamicii loviți suferă daune și sunt aruncați în sus.", "tooltip": "Wukong primește <speed>{{ movespeed*100 }}% vitez<PERSON> de mișcare</speed> și își rotește toiagul, <status>aruncând în sus</status> inamicii din apropiere timp de {{ knockupduration }} secunde și provocându-le <physicalDamage>daune fizice în valoare de {{ totaldamagett }} plus {{ percenthpdamagett }} din viața maximă</physicalDamage> de-a lungul a {{ spinduration }} secunde.<br /><br />Această abilitate poate fi folosită din nou în decurs de {{ recastwindow }} secunde, înainte să intre în perioada de reactivare.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Daune din viața maximă", "Timp de reactivare"], "effect": ["{{ basepercentmaxhpdmgpersec*200.000000 }}% -> {{ basepercentmaxhpdmgpersecnl*200.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [315, 315, 315], "rangeBurn": "315", "image": {"full": "MonkeyKingSpinToWin.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON>", "description": "Când se luptă cu campioni și <PERSON>, Wukong primește armură și regenerare a vieții maxime, care se cumulează.", "image": {"full": "MonkeyKingStoneSkin.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}