{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Neeko": {"id": "<PERSON><PERSON><PERSON>", "key": "518", "name": "니코", "title": "알쏭달쏭 카멜레온", "image": {"full": "Neeko.png", "sprite": "champion3.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "518000", "num": 0, "name": "default", "chromas": false}, {"id": "518001", "num": 1, "name": "겨울 동화 니코", "chromas": true}, {"id": "518010", "num": 10, "name": "별 수호자 니코", "chromas": false}, {"id": "518011", "num": 11, "name": "프레스티지 별 수호자 니코", "chromas": false}, {"id": "518012", "num": 12, "name": "전설의 산수화 니코", "chromas": true}, {"id": "518021", "num": 21, "name": "프레스티지 별 수호자 니코 (2022)", "chromas": false}, {"id": "518022", "num": 22, "name": "마녀 니코", "chromas": true}, {"id": "518031", "num": 31, "name": "거리의 악마 니코", "chromas": true}, {"id": "518040", "num": 40, "name": "코스플레이어 니코", "chromas": true}], "lore": "오랜 세월 잊힌 바스타야의 한 부족 출신인 니코는 다른 이의 모습을 빌려 어느 무리에든 뒤섞일 수 있으며, 심지어 상대의 감정을 흡수하여 적과 아군을 한눈에 구분할 수 있다. 그 누구도 니코가 어디 있는지, 정체가 무엇인지 확신할 수 없지만, 악의를 가지고 접근하는 자는 원초적 영혼 마법의 무시무시한 힘과 함께 그녀의 진정한 모습을 보게 될 것이다.", "blurb": "오랜 세월 잊힌 바스타야의 한 부족 출신인 니코는 다른 이의 모습을 빌려 어느 무리에든 뒤섞일 수 있으며, 심지어 상대의 감정을 흡수하여 적과 아군을 한눈에 구분할 수 있다. 그 누구도 니코가 어디 있는지, 정체가 무엇인지 확신할 수 없지만, 악의를 가지고 접근하는 자는 원초적 영혼 마법의 무시무시한 힘과 함께 그녀의 진정한 모습을 보게 될 것이다.", "allytips": ["설정 메뉴에서 기본 지속 효과에 단축키를 설정할 수 있습니다. 기본 설정은 Shift+F1~F5입니다.", "태고의 마력을 너무 남발하지는 마세요. 적을 필요 이상으로 경계하게 만들 수도 있습니다."], "enemytips": ["니코가 주변에 있을 때는 미니언 뒤에 자리잡지 마세요. 강화된 칭칭올가미에 맞을 수도 있습니다.", "니코가 변신 상태일 때는 만개의 경고 효과가 보이지 않습니다."], "tags": ["Mage", "Support"], "partype": "마나", "info": {"attack": 1, "defense": 1, "magic": 9, "difficulty": 5}, "stats": {"hp": 610, "hpperlevel": 104, "mp": 450, "mpperlevel": 30, "movespeed": 340, "armor": 21, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7.5, "hpregenperlevel": 0.75, "mpregen": 7, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 48, "attackdamageperlevel": 2.5, "attackspeedperlevel": 3.5, "attackspeed": 0.625}, "spells": [{"id": "NeekoQ", "name": "꽃망울 폭발", "description": "니코가 씨앗을 던져 마법 피해를 입힙니다. 씨앗이 적 챔피언에게 피해를 입히거나 유닛을 처치하면 한 번 더 폭발합니다.", "tooltip": "니코가 폭발하는 씨앗을 던져 <magicDamage>{{ explosiondamage }}의 마법 피해</magicDamage>를 입힙니다. 씨앗이 폭발하여 유닛을 처치하거나 챔피언 또는 대형 몬스터에게 피해를 입히면 다시 폭발하여 <magicDamage>{{ seconddamage }}의 마법 피해</magicDamage>를 입힙니다. 최대 두 번까지 추가로 폭발합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["최초 피해량", "폭발 피해량", "소모값 @AbilityResourceName@", "추가 몬스터 피해량", "재사용 대기시간"], "effect": ["{{ zonedamage }} -> {{ zonedamageNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}", "{{ cost }} -> {{ costNL }}", "{{ monsterbonus }} -> {{ monsterbonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "NeekoQ.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NeekoW", "name": "형상 분리", "description": "니코가 기본 지속 효과로 세 번째 기본 공격마다 추가 마법 피해를 입히고 잠시 이동 속도가 증가합니다. 사용 시에는 자신을 복제한 형상을 지정한 방향으로 보낼 수 있으며, 재사용 시 방향을 변경할 수 있습니다.", "tooltip": "<passive>기본 지속 효과:</passive> 세 번째 기본 공격마다 <magicDamage>{{ passivebonusdamagecalc }}의 추가 마법 피해</magicDamage>를 입히고 {{ passivehasteduration }}초 동안 <speed>이동 속도가 {{ passivehaste }}%</speed> 증가합니다.<br /><br /><active>사용 시:</active> 니코가 {{ stealthduration }}초 동안 <keywordStealth>투명</keywordStealth> 상태가 되며 {{ cloneduration }}초 동안 유지되는 복제 형상을 만들어 지정한 방향으로 보냅니다. 니코와 복제 형상은 {{ hasteduration }}초 동안 <speed>{{ haste }}%의 추가 이동 속도</speed>를 얻습니다. <br /><br /><rules>클릭하여 소환수 이동 단축키를 사용하거나 이 스킬을 <recast>재사용</recast>해 분신을 조종할 수 있습니다.<br />분신은 니코의 스킬, 감정표현, 귀환을 따라 합니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["기본 지속 효과 피해량", "사용 시 이동 속도", "기본 지속 효과 이동 속도", "재사용 대기시간"], "effect": ["{{ passivedamage }} -> {{ passivedamageNL }}", "{{ haste }}% -> {{ hasteNL }}%", "{{ passivehaste }}% -> {{ passivehasteNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "소모값 없음", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "NeekoW.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "소모값 없음"}, {"id": "NeekoE", "name": "칭칭올가미", "description": "니코가 올가미를 던져 적중한 모든 적에게 피해를 입히고 속박합니다. 올가미로 적을 맞히면 올가미가 더 커지고, 더 빠르게 날아가며 속박 지속시간도 길어집니다.", "tooltip": "니코가 올가미를 던져 <magicDamage>{{ basedamage }}의 마법 피해</magicDamage>를 입히고 {{ minrootduration }}초 동안 <status>속박</status>합니다.<br /><br />올가미는 적을 맞히면 강화되어 크기가 커지고 더 빠르게 날아가며, {{ maxrootduration }}초 동안 <status>속박</status>합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "최소 속박 지속시간", "증가된 속박 지속시간", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ minrootduration }} -> {{ minrootdurationNL }}", "{{ maxrootduration }} -> {{ maxrootdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "NeekoE.png", "sprite": "spell9.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "NeekoR", "name": "만개", "description": "니코가 잠시 준비한 후 공중으로 도약하며 주변 모든 적을 공중에 띄웁니다. 땅에 착지하면 주변 적이 피해를 입고 기절합니다. 변신 상태에서는 준비하는 모습이 적에게 보이지 않습니다.", "tooltip": "잠시 후 니코가 공중으로 도약해 {{ delayuntilexplosion }}초 동안 주변의 모든 적을 <status>공중으로 띄워 올립니다</status>. 이후 떨어지며 주변의 모든 적에게 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입히고 {{ stunduration }}초 동안 <status>기절시킵니다</status>.<br /><br /><rules>니코가 변신 상태인 경우 이 스킬의 준비 동작이 적에게 보이지 않습니다. 이 스킬을 사용하면 {{ delaybeforepassiveremoval }}초 후 변신이 해제됩니다.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 105, 90], "cooldownBurn": "120/105/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "NeekoR.png", "sprite": "spell9.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "태고의 마력", "description": "니코가 아군 챔피언 또는 맵에 있는 다른 유닛 중 하나로 변신합니다. 이동 불가 군중 제어기에 당하거나 피해를 입히는 스킬을 사용하거나 챔피언이 아닌 상태에서 적 포탑에 피해를 입히거나 변장한 유닛의 체력만큼 피해를 입으면 변신이 풀립니다.", "image": {"full": "Neeko_P.png", "sprite": "passive3.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}