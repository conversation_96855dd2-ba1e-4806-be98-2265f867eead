{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Soraka": {"id": "<PERSON><PERSON><PERSON>", "key": "16", "name": "Сорака", "title": "Звездное дитя", "image": {"full": "Soraka.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "16000", "num": 0, "name": "default", "chromas": false}, {"id": "16001", "num": 1, "name": "Дриада Сорака", "chromas": false}, {"id": "16002", "num": 2, "name": "Божественная Сорака", "chromas": false}, {"id": "16003", "num": 3, "name": "Целестинец Сорака", "chromas": false}, {"id": "16004", "num": 4, "name": "Жнец Сорака", "chromas": false}, {"id": "16005", "num": 5, "name": "Сорака из Ордена банана", "chromas": false}, {"id": "16006", "num": 6, "name": "Программа Сорака", "chromas": false}, {"id": "16007", "num": 7, "name": "Звездная защитница Сорака", "chromas": false}, {"id": "16008", "num": 8, "name": "Защитница в пижаме Сорака", "chromas": false}, {"id": "16009", "num": 9, "name": "Снегурочка Сорака", "chromas": true}, {"id": "16015", "num": 15, "name": "Предвестница зари Сорака", "chromas": false}, {"id": "16016", "num": 16, "name": "Предвестница ночи Сорака", "chromas": false}, {"id": "16017", "num": 17, "name": "Звездная защитница Сорака (престижный)", "chromas": false}, {"id": "16018", "num": 18, "name": "Сорака из кафе красоты", "chromas": true}, {"id": "16027", "num": 27, "name": "Дух цветения Сорака", "chromas": true}, {"id": "16037", "num": 37, "name": "Сорака бессмертного пути", "chromas": true}, {"id": "16044", "num": 44, "name": "Сорака из Королевства фей", "chromas": true}], "lore": "Сорака – странница из небесных миров, лежащих за пределами горы Таргон. Она пожертвовала своим бессмертием, чтобы оградить смертные расы от их же собственной жестокости. Она старается проявить сострадание и милосердие к каждому, кого встречает, исцеляя даже тех, кто хотел причинить ей вред. И несмотря на все невзгоды, которые Сорака видела в этом мире, она по-прежнему верит, что жители Рунтерры способны раскрыть свой истинный потенциал.", "blurb": "Сорака – странница из небесных миров, лежащих за пределами горы Таргон. Она пожертвовала своим бессмертием, чтобы оградить смертные расы от их же собственной жестокости. Она старается проявить сострадание и милосердие к каждому, кого встречает, исцеляя...", "allytips": ["Сорака - хороший товарищ. Она эффективно восстанавливает здоровье, что позволяет более эффективно вести осады.", "Желание не имеет ограничения по дальности, используйте его для спасения союзников по всей карте.", "Равноденствие может быть использовано в качестве мощного инструмента контроля территории."], "enemytips": ["Сконцентрируйте свои атаки на Сораке, если она выходит на передовую, чтобы лечить союзников.", "Если Сорака изматывает вас Равноденствием, пользуйтесь большой продолжительностью перезарядки этого умения.", "Проще сфокусироваться на Сораке, чем на союзнике, которого она лечит."], "tags": ["Support", "Mage"], "partype": "Мана", "info": {"attack": 2, "defense": 5, "magic": 7, "difficulty": 3}, "stats": {"hp": 605, "hpperlevel": 88, "mp": 425, "mpperlevel": 40, "movespeed": 325, "armor": 32, "armorperlevel": 5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 2.5, "hpregenperlevel": 0.5, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 50, "attackdamageperlevel": 3, "attackspeedperlevel": 2.14, "attackspeed": 0.625}, "spells": [{"id": "SorakaQ", "name": "Звездопад", "description": "По призыву Сораки с неба в указанную область падает звезда, нанося магический урон всем врагам в зоне поражения и замедляя их. Если умение поражает вражеского чемпиона, Сорака восстанавливает себе здоровье.", "tooltip": "По призыву Сораки с неба в указанную область падает звезда, которая наносит <magicDamage>{{ totaldamage }} магического урона</magicDamage> и <status>замедляет</status> врагов на {{ movespeedslow*100 }}% на {{ slowduration }} сек. <br /><br />При поражении вражеского чемпиона Сорака получает <keywordMajor>Обновление</keywordMajor>, которое восстанавливает ей <healing>{{ totalhot }} здоровья</healing> в течение {{ hotduration }} сек. и увеличивает ее <speed>скорость передвижения на {{ movespeedhaste*100 }}%</speed> (уменьшается в течение того же времени).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Восстановление здоровья (Обновление)", "Скорость передвижения", "Перезарядка", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basehot }} -> {{ basehotNL }}", "{{ movespeedhaste*100.000000 }}% -> {{ movespeedhastenl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [810, 810, 810, 810, 810], "rangeBurn": "810", "image": {"full": "SorakaQ.png", "sprite": "spell13.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SorakaW", "name": "Звездная пыль", "description": "Сорака жертвует частью своего запаса здоровья, восстанавливая здоровье другому союзному чемпиону.", "tooltip": "Сорака восстанавливает <healing>{{ totalheal }} здоровья</healing> другому союзному чемпиону.<br /><br />Если на Сораку действует <keywordMajor>Обновление</keywordMajor>, затраты здоровья этого умения снижаются на {{ percenthealthcostrefund*100 }}%, а цель тоже получает <keywordMajor>Обновление</keywordMajor> на {{ spell.sorakaq:hotduration }} сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Лечение", "Перезарядка", "Стоимость – @AbilityResourceName@", "Уменьшение затрат здоровья"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ percenthealthcostrefund*100.000000 }}% -> {{ percenthealthcostrefundnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% от максимального запаса здоровья, {{ cost }} маны", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "SorakaW.png", "sprite": "spell13.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ percenthealthcost*100 }}% от максимального запаса здоровья, {{ cost }} маны"}, {"id": "SorakaE", "name": "Равноденствие", "description": "Сорака создает в указанной области зону молчания. Враги, не успевшие покинуть ее до окончания времени ее действия, дополнительно обездвиживаются.", "tooltip": "Сорака создает звездное поле, которое наносит <magicDamage>{{ totaldamage }} магического урона</magicDamage> чемпионам. Это поле существует {{ rootdelay }} сек. и накладывает <status>безмолвие</status> на врагов внутри. Когда поле исчезает, чемпионы внутри <status>обездвиживаются</status> на {{ rootduration }} сек. и получают <magicDamage>{{ totaldamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка", "Продолжительность обездвиживания", "Стоимость – @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rootduration }} -> {{ rootdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [925, 925, 925, 925, 925], "rangeBurn": "925", "image": {"full": "SorakaE.png", "sprite": "spell13.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "SorakaR", "name": "Желание", "description": "Сорака наполняет сердца всех союзных чемпионов надеждой, восстанавливая себе и им здоровье.", "tooltip": "Сорака обращается к небесным силам, восстанавливая <healing>{{ healingcalc }} здоровья</healing> всем союзным чемпионам независимо от расстояния до них. Эффективность лечения увеличивается на <healing>{{ ampedhealing }}</healing>, если у цели менее 40% здоровья.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Лечение", "Перезарядка"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [150, 135, 120], "cooldownBurn": "150/135/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "SorakaR.png", "sprite": "spell13.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Спасение", "description": "Скорость передвижения Сораки увеличена при движении в сторону союзников, у которых осталось мало здоровья.", "image": {"full": "Soraka_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}