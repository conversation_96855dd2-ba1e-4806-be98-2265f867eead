{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ryze": {"id": "Ryze", "key": "13", "name": "Ryze", "title": "der Runenmagier", "image": {"full": "Ryze.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "13000", "num": 0, "name": "default", "chromas": false}, {"id": "13001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "13002", "num": 2, "name": "Stammes-Ryze", "chromas": false}, {"id": "13003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "13004", "num": 4, "name": "Triumphierender Ryze", "chromas": false}, {"id": "13005", "num": 5, "name": "Professor <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "13006", "num": 6, "name": "Zombie-R<PERSON>ze", "chromas": false}, {"id": "13007", "num": 7, "name": "Dunkelkristall-Ryze", "chromas": false}, {"id": "13008", "num": 8, "name": "Piraten-Ryze", "chromas": false}, {"id": "13009", "num": 9, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "13010", "num": 10, "name": "SKT T1-Ryze", "chromas": true}, {"id": "13011", "num": 11, "name": "WM 2019-Ryze", "chromas": true}, {"id": "13013", "num": 13, "name": "Sandwächter Ryze", "chromas": true}, {"id": "13020", "num": 20, "name": "Arkana-Ryze", "chromas": true}, {"id": "13029", "num": 29, "name": "Blutmond-Ryze", "chromas": true}], "lore": "Ryze gilt weit und breit als einer der fähigsten Zauberer Runeterras, und ist ein hartgesottener Erzmagier, dem eine unglaublich schwere Bürde auf den Schultern lastet. Seine unerschütterliche Entschlossenheit und seine immensen arkanen Kräfte helfen ihm auf der unermüdlichen Jagd nach den Weltrunen – Bruchstücken reiner Magie, die einst die Welt aus dem Nichts geformt haben. Er muss diese Artefakte auftreiben, bevor sie in die falschen Hände geraten, denn Ryze ist sich des Unheils bewusst, das sie über Runeterra bringen könnten.", "blurb": "Ryze gilt weit und breit als einer der fähigsten Zauberer Runeterras, und ist ein hartgesottener Erzmagier, dem eine unglaublich schwere Bürde auf den Schultern lastet. Seine unerschütterliche Entschlossenheit und seine immensen arkanen Kräfte helfen...", "allytips": ["Benutze die passive Komponente von Überladung, um entweder den Schaden oder das Lauftempo auf den Maximalwert zu erhöhen.", "Die kurze Abklingzeit von „Zauberkaskade“ ermöglicht es, die Kaskade an viele Gegner zu heften.", "Ryze kann sich bewegen und weitere Zauber wirken, während sich das Weltentor aufbaut, ohne dass es dadurch abbricht."], "enemytips": ["<PERSON><PERSON><PERSON> ist besonders gefährlich für Gegner, die von ''Kaskade'' betroffen sind.", "Verwende die Aufladezeit von Weltentor, um dich darauf vorzubereiten, dass bald jemand aus dem Portal kommt.", "Massenkontrolleffekte gegen Ryze brechen die Aufladung des Weltentors ab."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 10, "difficulty": 7}, "stats": {"hp": 645, "hpperlevel": 124, "mp": 300, "mpperlevel": 70, "movespeed": 340, "armor": 22, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 8, "hpregenperlevel": 0.8, "mpregen": 8, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 2.11, "attackspeed": 0.658}, "spells": [{"id": "RyzeQWrapper", "name": "Überladung", "description": "„Überladung“ hat außerdem eine passive Komponente, dank der ihre Abklingzeit durch andere Grundfähigkeiten zurückgesetzt und eine Rune aufgeladen wird. Aktiviert Ryze „Überladung“ mit 2 aufgeladenen Runen, erhält er einen kurzen Schub zusätzliches Lauftempo.<br><br>Bei Aktivierung entlädt Ryze pure Energie in einer geraden Linie und verursacht Schaden am ersten getroffenen Gegner. Überladen verursacht zusätzlichen Schaden und springt auf Gegner in der Nähe über, die mit Kaskade markiert sind.", "tooltip": "<spellPassive>Passiv:</spellPassive> <spellName>Runenkäfig</spellName> und <spellName>Zauberkaskade</spellName> setzen die Abklingzeit dieser Fähigkeit zurück und laden {{ runeduration }}&nbsp;Sekunden lang eine Rune auf (max. {{ maximumrunes }} Runen).<br /><br /><spellActive>Aktiv:</spellActive> Ryze feuert einen Magieschuss ab, der dem ersten getroffenen Gegner <magicDamage>{{ qdamagecalc }}&nbsp;magischen Schaden</magicDamage> zufügt. Ist das Ziel mit <keywordMajor>Zauberkaskade</keywordMajor> belegt, wird sie verbraucht und diese Fähigkeit verursacht um {{ spell.ryzer:overloaddamagebonus }}&nbsp;% erhöhten Schaden. Außerdem springt sie auf nahe Gegner mit <keywordMajor>Zauberkaskade</keywordMajor> über.<br /><br />Ryze entlädt zudem alle Runen und er erhält {{ movementspeedduration }}&nbsp;Sekunden lang <speed>{{ movementspeedamount }}&nbsp;% Lauftempo</speed>, wenn {{ maximumrunes }} Runen geladen waren.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Lauftempo", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ movementspeedamount }}&nbsp;% -> {{ movementspeedamountNL }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 38, 36, 34, 32], "costBurn": "40/38/36/34/32", "datavalues": {}, "effect": [null, [70, 110, 150, 190, 230], [25, 40, 55, 70, 85], [50, 75, 100, 125, 150], [25, 28, 31, 24, 37], [2, 2, 2, 2, 2], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0.015, 0.015, 0.015, 0.015, 0.015], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/110/150/190/230", "25/40/55/70/85", "50/75/100/125/150", "25/28/31/24/37", "2", "2", "3", "0.01", "2", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "RyzeQWrapper.png", "sprite": "spell11.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeW", "name": "Runenkäfig", "description": "Ryze fängt eine Zieleinheit in einem Runenkäfig ein, der Schaden verursacht und verhindert, dass diese sich bewegen kann. Ist das Ziel mit „Zauberkaskade“ belegt, wird es stattdessen festgehalten.", "tooltip": "Ryze fügt dem <PERSON> <magicDamage>{{ wdamagecalc }}&nbsp;magischen Schaden</magicDamage> zu und <status>verlangsamt</status> es {{ ccduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%. Wenn das Ziel mit <keywordMajor>Zauberkaskade</keywordMajor> belegt ist, wird sie verbraucht und das Ziel wird <status>festgehalten</status> anstatt <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10.5, 10, 9.5, 9], "cooldownBurn": "11/10.5/10/9.5/9", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeW.png", "sprite": "spell11.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeE", "name": "Zauberkaskade", "description": "Ryze entfesselt eine Kugel reiner magischer Energie, die einem Gegner Schaden zufügt und alle nahen G<PERSON>ner schwächt. Ryzes Zauber haben zusätzliche Wirkungen gegen diese Einheit.", "tooltip": "Ryze feuert eine Kugel ab, die <magicDamage>{{ edamagecalc }}&nbsp;magischen Schaden</magicDamage> verursacht und das Ziel sowie Gegner in der Nähe {{ debuffduration }}&nbsp;Sekunden lang mit <keywordMajor>Zauberkaskade</keywordMajor> belegt. <PERSON><PERSON>ner, die bereits mit <keywordMajor>Zauberkaskade</keywordMajor> belegt sind, breiten sie weiter aus.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Manakosten"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.25, 3, 2.75, 2.5], "cooldownBurn": "3.5/3.25/3/2.75/2.5", "cost": [35, 45, 55, 65, 75], "costBurn": "35/45/55/65/75", "datavalues": {}, "effect": [null, [80, 90, 100, 110, 120], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.1, 0.1, 0.1, 0.1, 0.1], [1.5, 1.5, 1.5, 1.5, 1.5], [1.5, 1.5, 1.5, 1.5, 1.5], [4, 4, 4, 4, 4], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/90/100/110/120", "40/50/60/70/80", "100", "0.1", "1.5", "1.5", "4", "1", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [615, 615, 615, 615, 615], "rangeBurn": "615", "image": {"full": "RyzeE.png", "sprite": "spell11.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "RyzeR", "name": "Weltentor", "description": "Passiv verursacht „Überladung“ an Zielen, die mit „Zauberkaskade“ belegt sind, sogar noch höheren Schaden.<br><br>Bei Aktivierung erschafft Ryze ein Portal zu einem nahegelegenen Ort. Nach ein paar Sekunden werden Verbündete in der Nähe des Portals dorthin teleportiert.", "tooltip": "<spellPassive>Passiv:</spellPassive> Der zusätzliche Schaden von <spellName>Überladung</spellName> gegen <PERSON>, die mit <keywordMajor>Zauberkaskade</keywordMajor> belegt sind, ist auf {{ overloaddamagebonus }}&nbsp;% erhöht.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON><PERSON>ze öffnet ein Portal zu einem anderen Ort. Nach {{ chargetimetooltip }}&nbsp;Sekunden werden alle Verbündeten in der Nähe des Portals an diesen Ort teleportiert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Überladung: Schadenserhöhung"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ overloaddamagebonus }}&nbsp;% -> {{ overloaddamagebonusNL }}&nbsp;%"]}, "maxrank": 3, "cooldown": [180, 160, 140], "cooldownBurn": "180/160/140", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [6, 6, 6], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "6", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "RyzeR.png", "sprite": "spell11.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Arkane Meisterschaft", "description": "<mainText><PERSON><PERSON><PERSON> Zauber verursachen zusätzlichen Schaden basierend auf seinem zusätzlichen Mana und sein maximales Mana erhöht sich mit seiner Fähigkeitsstärke.</mainText>", "image": {"full": "Ryze_P.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}