{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kaisa": {"id": "<PERSON><PERSON>", "key": "145", "name": "Kai'Sa", "title": "Daughter of the Void", "image": {"full": "Kaisa.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "145000", "num": 0, "name": "default", "chromas": false}, {"id": "145001", "num": 1, "name": "Bullet Angel <PERSON>", "chromas": true}, {"id": "145014", "num": 14, "name": "K/DA Kai'Sa", "chromas": false}, {"id": "145015", "num": 15, "name": "Prestige K/DA Kai'Sa", "chromas": false}, {"id": "145016", "num": 16, "name": "iG Kai'Sa", "chromas": false}, {"id": "145017", "num": 17, "name": "Arcade Kai'Sa", "chromas": false}, {"id": "145026", "num": 26, "name": "K/DA ALL OUT Kai'Sa", "chromas": false}, {"id": "145027", "num": 27, "name": "Prestige K/DA ALL OUT Kai'Sa", "chromas": false}, {"id": "145029", "num": 29, "name": "Lagoon Dragon Kai'Sa", "chromas": false}, {"id": "145039", "num": 39, "name": "Prestige K/DA Kai'Sa (2022)", "chromas": false}, {"id": "145040", "num": 40, "name": "Star Guardian Kai'Sa", "chromas": false}, {"id": "145048", "num": 48, "name": "Inkshadow Kai'Sa", "chromas": false}, {"id": "145059", "num": 59, "name": "Heavenscale Kai'Sa", "chromas": false}, {"id": "145069", "num": 69, "name": "Dark Star Kai'Sa", "chromas": false}, {"id": "145070", "num": 70, "name": "Risen Legend <PERSON>", "chromas": false}, {"id": "145071", "num": 71, "name": "Immortalized Legend Kai'Sa", "chromas": false}], "lore": "Claimed by the Void when she was only a child, <PERSON><PERSON><PERSON> managed to survive through sheer tenacity and strength of will. Her experiences have made her a deadly hunter and, to some, the harbinger of a future they would rather not live to see. Having entered into an uneasy symbiosis with a living Void carapace, the time will soon come when she must decide whether to forgive those mortals who would call her a monster, and defeat the coming darkness together… or simply to forget, as the Void consumes the world that left her behind.", "blurb": "Claimed by the Void when she was only a child, <PERSON><PERSON><PERSON> managed to survive through sheer tenacity and strength of will. Her experiences have made her a deadly hunter and, to some, the harbinger of a future they would rather not live to see. Having entered...", "allytips": ["Try to catch enemy carries alone to burst them with Icathian Rain.", "Work with your teammates to set up your Ultimate and optimize damage with your passive.", "Make sure to buy items that will evolve at least 1 or 2 of your spells."], "enemytips": ["<PERSON><PERSON><PERSON> is very good at picking off isolated enemies. Stick together against her.", "<PERSON><PERSON><PERSON> is very vulnerable to getting outranged by mages and long range carries.", "Make sure to place wards in your blind spots so you can see <PERSON><PERSON><PERSON> coming."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 345, "mpperlevel": 40, "movespeed": 335, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.6, "attackspeedperlevel": 1.8, "attackspeed": 0.644}, "spells": [{"id": "KaisaQ", "name": "Icathian Rain", "description": "Kai'<PERSON> shoots a swarm of missiles that seek out nearby targets.<br><br>Living Weapon: Icathian Rain is upgraded to shoot more missiles.", "tooltip": "<PERSON>'<PERSON> launches {{ e2 }} missiles that split among nearby enemies, each dealing <physicalDamage>{{ totalindividualmissiledamage }} physical damage</physicalDamage>, up to a maximum of {{ maxdamagedisplay }}. Additional missile hits on champions or monsters deal {{ extrahitreduction*100 }}% damage.<br /><br /><keywordMajor>Evolved</keywordMajor>: <PERSON>'<PERSON> instead fires {{ e7 }} missiles.<br />Current: <physicalDamage>{{ f11.1 }}/{{ e6 }} Bonus Attack Damage</physicalDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage Per Missile"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [6, 6, 6, 6, 6], [0.25, 0.25, 0.25, 0.25, 0.25], [2, 2, 2, 2, 2], [0.35, 0.35, 0.35, 0.35, 0.35], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/55/70/85/100", "6", "0.25", "2", "0.35", "100", "12", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KaisaQ.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaW", "name": "Void Seeker", "description": "<PERSON><PERSON><PERSON> shoots a long range missile, marking enemies with her passive.<br><br>Living Weapon: Void Seeker is upgraded to apply more passive marks and reduce cooldown on champion hit.", "tooltip": "<PERSON><PERSON><PERSON> fires a void blast that deals <magicDamage>{{ totaldamage }} magic damage</magicDamage>, applies {{ e4 }} stacks of <keywordMajor>Plasma</keywordMajor>, and grants <keywordStealth>True Sight</keywordStealth> of the first enemy hit for {{ spell.kaisapassive:pduration }} seconds.<br /><br /><keywordMajor>Evolved</keywordMajor>: <PERSON><PERSON><PERSON> instead applies {{ e5 }} stacks of <keywordMajor>Plasma</keywordMajor> and hitting a champion reduces the Cooldown by {{ e3 }}%.<br />Current: <scaleAP>{{ f2.1 }}/{{ e2 }} Ability Power</scaleAP>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [30, 55, 80, 105, 130], [100, 100, 100, 100, 100], [75, 75, 75, 75, 75], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/55/80/105/130", "100", "75", "2", "3", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "KaisaW.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaE", "name": "Supercharge", "description": "<PERSON><PERSON><PERSON> briefly increases her Move Speed, then increases her Attack Speed.<br><br>Living Weapon: Supercharge is upgraded to briefly grant Invisibility.", "tooltip": "<PERSON><PERSON><PERSON> supercharges her void energy, gaining <speed>{{ totalmovespeed }} Move Speed</speed> and becoming Ghost<PERSON> while charging, then gaining <attackSpeed>{{ e5 }}% Attack Speed</attackSpeed> for {{ e2 }} seconds.<br /><br />Attacks reduce this Ability's Cooldown by {{ e4 }} seconds.<br /><br /><keywordMajor>Evolved</keywordMajor>: <PERSON><PERSON><PERSON> also becomes <keywordStealth>Invisible</keywordStealth> for {{ e7 }} seconds.<br />Current: <attackSpeed>{{ f10.1 }}%/{{ e6 }}% Bonus Attack Speed</attackSpeed>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Move Speed", "Attack Speed"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ effect1amount*100.000000 }}% -> {{ effect1amountnl*100.000000 }}%", "{{ effect5amount*100.000000 }}% -> {{ effect5amountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0.55, 0.6, 0.65, 0.7, 0.75], [4, 4, 4, 4, 4], [1.2, 1.2, 1.2, 1.2, 1.2], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.55/0.6/0.65/0.7/0.75", "4", "1.2", "0.5", "40/50/60/70/80", "100", "0.5", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "KaisaE.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaR", "name": "Killer Instinct", "description": "<PERSON><PERSON><PERSON> dashes near an enemy champion.", "tooltip": "Kai'Sa warps near an enemy champion affected by <keywordMajor>Plasma</keywordMajor> and gains <shield>{{ rcalculatedshieldvalue }} Shield</shield> for {{ rshieldduration }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Range", "Cooldown", "Shield Amount", "Attack Damage Ratio"], "effect": ["{{ rrange }} -> {{ rrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ rbasevalue }} -> {{ rbasevalueNL }}", "{{ rtotaladratio*100.000000 }}% -> {{ rtotaladrationl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "KaisaR.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Second Skin", "description": "<PERSON><PERSON><PERSON>'s basic attacks stack Plasma, dealing increasing bonus magic damage. Allies' immobilizing effects help stack Plasma. Additionally, <PERSON><PERSON><PERSON>'s item purchases upgrade her basic spells to have more powerful properties.", "image": {"full": "Kaisa_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}