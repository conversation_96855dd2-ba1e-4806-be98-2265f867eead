{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Lillia": {"id": "Lillia", "key": "876", "name": "リリ<PERSON>", "title": "はにかみ屋の花", "image": {"full": "Lillia.png", "sprite": "champion2.png", "group": "champion", "x": 96, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "876000", "num": 0, "name": "default", "chromas": false}, {"id": "876001", "num": 1, "name": "精霊の花祭りリリア", "chromas": true}, {"id": "876010", "num": 10, "name": "混沌の闇リリア", "chromas": true}, {"id": "876019", "num": 19, "name": "山海絵巻伝リリア", "chromas": true}, {"id": "876028", "num": 28, "name": "妖精の王宮リリア", "chromas": true}], "lore": "極度の恥ずかしがり屋であるリリアは子鹿の妖精で、不安を胸に秘めつつもアイオニアの森の中を歩き回っている。彼女は定命の者たちの謎めいた性質に怯えながらも強い興味をいだいており、彼らの側に身を隠しながら、なぜ彼らの夢が古の「夢の木」に到達しなくなったのか理由を探ろうとしている。現在は魔法の枝を持ってアイオニアを旅しながら、人々のまだ見ぬ夢を探している。その夢を見つけて初めて、リリアは自ら花開き、他者の恐怖を取り除いてその内に眠る輝きを見つけてあげることができる。ひぃあ！", "blurb": "極度の恥ずかしがり屋であるリリアは子鹿の妖精で、不安を胸に秘めつつもアイオニアの森の中を歩き回っている。彼女は定命の者たちの謎めいた性質に怯えながらも強い興味をいだいており、彼らの側に身を隠しながら、なぜ彼らの夢が古の「夢の木」に到達しなくなったのか理由を探ろうとしている。現在は魔法の枝を持ってアイオニアを旅しながら、人々のまだ見ぬ夢を探している。その夢を見つけて初めて、リリアは自ら花開き、他者の恐怖を取り除いてその内に眠る輝きを見つけてあげることができる。ひぃあ！", "allytips": [], "enemytips": [], "tags": ["Fighter", "Mage"], "partype": "マナ", "info": {"attack": 0, "defense": 2, "magic": 10, "difficulty": 8}, "stats": {"hp": 605, "hpperlevel": 105, "mp": 410, "mpperlevel": 50, "movespeed": 330, "armor": 22, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 1.55, "attackrange": 325, "hpregen": 2.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.95, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2.7, "attackspeed": 0.625}, "spells": [{"id": "LilliaQ", "name": "花開く風", "description": "自動効果で、スキルが敵に命中するたびに増加移動速度のスタックを獲得する。発動効果で周囲の敵に魔法ダメージを与え、端にいる対象には追加確定ダメージを与える。", "tooltip": "<spellPassive>自動効果:</spellPassive> スキルが命中すると{{ pranceduration }}秒間<speed>移動速度が{{ prancespeed }}</speed>増加する。この効果は最大{{ prancemaxstacks }}回までスタックする。<br /><br /><spellActive>発動効果:</spellActive> 香炉を振り回して、<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与え、外縁部では追加で<trueDamage>{{ bonustruedamage }}の確定ダメージ</trueDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "移動速度", "魔法ダメージ", "確定ダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ prancebonusperstack*100.000000 }}% -> {{ prancebonusperstacknl*100.000000 }}%", "{{ flatdamagebase }} -> {{ flatdamagebaseNL }}", "{{ flatdamagetrue }} -> {{ flatdamagetrueNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "LilliaQ.png", "sprite": "spell7.png", "group": "spell", "x": 288, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LilliaW", "name": "ひゃっ、あぶない！", "description": "周囲の敵にダメージを与える。中央にいた敵には、より大きなダメージを与える。", "tooltip": "杖を振り上げてから強烈な一撃を放ち<magicDamage>{{ flatdamage }}の魔法ダメージ</magicDamage>を与える。中央にいた敵には代わりに<magicDamage>{{ flatdamagesweetspot }}のダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ flatdamagebase }} -> {{ flatdamagebaseNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [500, 500, 500, 500, 500], "rangeBurn": "500", "image": {"full": "LilliaW.png", "sprite": "spell7.png", "group": "spell", "x": 336, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LilliaE", "name": "コロコロの種", "description": "落下時に当たった敵にダメージとスロウ効果を与える種を投げる。何にも当たらなかった場合は、壁か対象に当たるまで転がり続ける。", "tooltip": "「コロコロの種」を投げる。この種は落下時に当たった敵に<magicDamage>{{ impactdamagetotal }}の魔法ダメージ</magicDamage>を与えて可視化し、{{ slowduration }}秒間{{ slowamount*100 }}%の<status>スロウ効果</status>を与える。敵に当たらなかった場合は、敵か地形に当たるまで転がり続ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン"], "effect": ["{{ impactdamage }} -> {{ impactdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [70, 70, 70, 70, 70], "costBurn": "70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "LilliaE.png", "sprite": "spell7.png", "group": "spell", "x": 384, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "LilliaR", "name": "夢見の子守唄", "description": "「夢のかけら」を受けているすべての敵に眠気を付与してから眠らせる。眠った敵は、強制的に目覚めさせられた際に追加ダメージを受ける。", "tooltip": "<keywordMajor>「夢のかけら」</keywordMajor>を受けている敵のチャンピオンすべてに、{{ drowsyduration }}秒間<status>眠気</status>を付与する。その後、対象は{{ sleepduration }}秒間<status>眠り</status>に落ちる。<br /><br />対象はダメージを受けて目覚めた際に、追加で<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "目覚めダメージ"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ breakdamagebase }} -> {{ breakdamagebaseNL }}"]}, "maxrank": 3, "cooldown": [150, 130, 110], "cooldownBurn": "150/130/110", "cost": [50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [1600, 1600, 1600], "rangeBurn": "1600", "image": {"full": "LilliaR.png", "sprite": "spell7.png", "group": "spell", "x": 432, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "夢を集める大枝", "description": "スキルでチャンピオンかモンスターを攻撃すると、最大体力に応じた追加ダメージを継続的に与える。", "image": {"full": "Lillia_Icon_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 96, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}