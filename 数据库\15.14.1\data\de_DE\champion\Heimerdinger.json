{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Heimerdinger": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "74", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "der verehrte Erfinder", "image": {"full": "Heimerdinger.png", "sprite": "champion1.png", "group": "champion", "x": 192, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "74000", "num": 0, "name": "default", "chromas": false}, {"id": "74001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> He<PERSON>", "chromas": false}, {"id": "74002", "num": 2, "name": "<PERSON>plo<PERSON><PERSON>", "chromas": false}, {"id": "74003", "num": 3, "name": "Piltover Customs-He<PERSON>rdinger", "chromas": false}, {"id": "74004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74005", "num": 5, "name": "Biowaffen-<PERSON><PERSON><PERSON>nger", "chromas": false}, {"id": "74006", "num": 6, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74015", "num": 15, "name": "Poolparty<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74024", "num": 24, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "74033", "num": 33, "name": "Professor <PERSON><PERSON> (Arcane)", "chromas": false}], "lore": "Der exzentrische Professor <PERSON> ist einer der innovativsten und angesehensten Erfinder, die die Welt je gekannt hat. Als dienstältestes Mitglied des Rats von Piltover hat er sowohl die besten als auch die schlechtesten Seiten des unaufhörlichen Strebens der Stadt nach Fortschritt erlebt. Nichtsdestotrotz wird dieser brillante Wissenschaftler und Lehrer immer bestrebt sein, mit seinen unkonventionellen Geräten das Leben anderer zu verbessern.", "blurb": "Der exzentrische Professor <PERSON> ist einer der innovativsten und angesehensten Erfinder, die die Welt je gekannt hat. Als dienstältestes Mitglied des Rats von Piltover hat er sowohl die besten als auch die schlechtesten Seiten des...", "allytips": ["Die Platzierung der Geschütztürme kann kampfentscheidend sein. Gegen die meisten Gegner funktionieren Geschütztürme am besten, wenn sie einander unterstützen, doch hat der Gegner viel Flächenschaden, könnten deine Türme schnell zerstört werden. Mit in Büschen platzierten Geschütztürmen hast du den Überraschungseffekt auf deiner Seite.", "Die exakte Zündung von „Elektronensturm-Granate“ ist für Heimerdinger überlebenswichtig. Die Verlangsamung und die Betäubung halten Gegner lange genug fest, um ihnen zuzusetzen. Sie dient aber auch als erste Abwehrmethode gegen einen Überraschungsangriff.", "Eine weitere Streuung von „Hextech-Mikroraketen“ sorgt für zuverlässigeren Schaden und ist gegen mehrere Ziele effektiver. Den größten Schaden verursacht aber ein fokussierter Angriff."], "enemytips": ["<PERSON><PERSON> ist be<PERSON>, Heimerdingers Geschütztürme alle auf einmal mithilfe der Vasallen zu zerstören, anstatt sie einzeln zu bekämpfen.", "<PERSON>üte dich vor Heimerdingers „Aufrüsten!!!“, denn er kann es einsetzen, um fast allem, was ihm zusetzt, etwas entgegenzusetzen. Sobald seine ultimative Fähigkeit raus ist, schnappst du ihn dir."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 8}, "stats": {"hp": 558, "hpperlevel": 101, "mp": 385, "mpperlevel": 20, "movespeed": 340, "armor": 19, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 56, "attackdamageperlevel": 2.7, "attackspeedperlevel": 1.36, "attackspeed": 0.658}, "spells": [{"id": "<PERSON><PERSON>rdingerQ", "name": "H-28G-Evolutions-Geschützturm", "description": "He<PERSON>rdinger errichtet einen Schnellfeuer-Geschützturm, der mit einem sekundären durchdringenden Strahlenangriff ausgestattet ist (Geschütztürme verursachen an Türmen nur halben Schaden).", "tooltip": "<PERSON><PERSON>rdi<PERSON> stellt einen <keywordMajor><PERSON><PERSON></keywordMajor> auf, der Gegner in der Nähe angreift. Es können {{ maxturrets }}&nbsp;Türme gleichzeitig aktiv sein. <keywordMajor>T<PERSON>rm<PERSON></keywordMajor> laden sich langsam auf. Bei maximaler Ladung feuern sie einen stärkeren Angriff ab.<br /><br /><PERSON><PERSON> sich zu weit entfernt, deaktivieren sich seine <keywordMajor>Türme</keywordMajor> nach 8&nbsp;Sekunden.<br /><br />Die<PERSON> Fähigkeit hat {{ maxkits }}&nbsp;Ladungen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Kanonenschaden", "Strahlenschaden"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamagebeam }} -> {{ basedamagebeamNL }}"]}, "maxrank": 5, "cooldown": [1, 1, 1, 1, 1], "cooldownBurn": "1", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [900, 900, 900, 900, 900], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "900", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;Geschützturm-Bausatz und {{ cost }}&nbsp;Mana", "maxammo": "3", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "HeimerdingerQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ kitcost }}&nbsp;Geschützturm-Bausatz und {{ cost }}&nbsp;Mana"}, {"id": "HeimerdingerW", "name": "Hextech-Mikroraketen", "description": "<PERSON><PERSON><PERSON><PERSON> feuert eine Reihe von Raketen mit hoher Reichweite ab, die am Mauszeiger zusammenlaufen.", "tooltip": "<PERSON><PERSON>rdi<PERSON> entfesselt ein Sperrfeuer aus {{ rockets }}&nbsp;<PERSON><PERSON><PERSON>, das dem ersten getroffenen Gegner <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> zufügt. Zusätzliche Raketen verursachen verringerten Schaden.<br /><br />Maximaler Schaden: <magicDamage>{{ totaldamage }}&nbsp;magischer <PERSON>haden</magicDamage>.<br /><br /><keywordMajor>Türme</keywordMajor> in der Nähe erhalten 20&nbsp;% Aufladung pro Rakete, die einen Champion trifft.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [60, 90, 120, 150, 180], [12, 18, 24, 30, 36], [25, 25, 25, 25, 25], [20, 20, 20, 20, 20], [30, 30, 30, 30, 30], [108, 162, 216, 270, 324], [5, 5, 5, 5, 5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "60/90/120/150/180", "12/18/24/30/36", "25", "20", "30", "108/162/216/270/324", "5", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1325, 1325, 1325, 1325, 1325], "rangeBurn": "1325", "image": {"full": "HeimerdingerW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HeimerdingerE", "name": "CH-2-Elektronensturm-Granate", "description": "<PERSON><PERSON><PERSON><PERSON> wirft eine Granate an einen Zielort, mit der er Schaden an gegnerischen Einheiten verursacht und die alle direkt getroffenen Einheiten betäubt. Außerdem werden Einheiten in der Nähe verlangsamt.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> wirft eine Granate, die Gegnern in einem Bereich <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> zufügt und sie {{ slowduration }}&nbsp;Sekunden lang um {{ slowpercent.0*100 }}&nbsp;% <status>verlangsamt</status>. <PERSON><PERSON><PERSON> in der Mitte des Bereichs werden außerdem {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status>.<br /><br />Wird ein Champion getroffen, laden sich <keywordMajor>Geschütztürme</keywordMajor> in der Nähe komplett auf.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [11, 11, 11, 11, 11], "cooldownBurn": "11", "cost": [85, 85, 85, 85, 85], "costBurn": "85", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [970, 970, 970, 970, 970], "rangeBurn": "970", "image": {"full": "HeimerdingerE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "HeimerdingerR", "name": "AUFRÜSTEN!!!", "description": "He<PERSON>rdinger entwirft eine Aufwertung, wodurch die Effekte seiner nächsten Fähigkeit verstärkt sind.", "tooltip": "Heimerdinger rüstet seine nächste Fähigkeit (außer die ultimative Fähigkeit) auf.<br /><br /><spellName>H-28Q-Apex-Geschützturm</spellName>: Heimerdinger platziert 8&nbsp;Sekunden lang einen aufgerüsteten <keywordMajor>Turm</keywordMajor>, der nicht zum Turmmaximum zählt und <magicDamage>{{ qultdamage }}&nbsp;magischen Schaden</magicDamage> pro Schuss sowie <magicDamage>{{ qultdamagebeam }}&nbsp;magischen Schaden</magicDamage> pro aufgeladenem Schuss verursacht. Die Angriffe verursachen Flächenschaden, <status>verlangsamen</status> 2&nbsp;Sekunden lang um 25&nbsp;% und der Turm ist immun gegen Massenkontrolle.<br /><br /><spellName>Hextech-Raketenschwarm:</spellName> Feuert 4&nbsp;Raketenwellen ab, die jeweils <magicDamage>{{ wultdamage }}&nbsp;magischen Schaden</magicDamage> verursachen. Champions und Dschungelmonster, die von mehreren Raketen getroffen werden, erleiden verringerten Schaden und Vasallen erhöhten Schaden. Maximaler Schaden: <magicDamage>{{ wulttotaldamage }}&nbsp;magischer Schaden</magicDamage>. <br /><br /><spellName>CH-3X-Blitzgranate:</spellName> Heimerdinger wirft eine springende Granate, die sich dreimal entlädt und <magicDamage>{{ eultdamage }}&nbsp;magischen Schaden</magicDamage> verursacht. Sowohl <status>Betäubungs</status>- als auch <status>Verlangsamungs</status>reichweite sind größer.<br /><br /><recast>Reaktivierung:</recast> Bricht diese Fähigkeit ab.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Schaden durch Apex-Geschützturm-Kanone", "Schaden durch Geschützturmstrahl", "Schaden durch Raketenschwarm", "<PERSON><PERSON> durch Raketenschwarm", "Schaden durch Blitzgranate"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qultbasedamage }} -> {{ qultbasedamageNL }}", "{{ qultbasedamagebeam }} -> {{ qultbasedamagebeamNL }}", "{{ wultbasedamage }} -> {{ wultbasedamageNL }}", "{{ wulttotalbasedamage }} -> {{ wulttotalbasedamageNL }}", "{{ eultbasedamage }} -> {{ eultbasedamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [100, 140, 180], [80, 80, 80], [1.5, 1.5, 1.5], [0.12, 0.12, 0.12], [500, 690, 865], [0.45, 0.45, 0.45], [80, 100, 120], [135, 180, 225], [28, 39, 49], [150, 250, 350]], "effectBurn": [null, "100/140/180", "80", "1.5", "0.12", "500/690/865", "0.45", "80/100/120", "135/180/225", "28/39/49", "150/250/350"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1], "rangeBurn": "1", "image": {"full": "HeimerdingerR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Hextech-Affinität", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> das Lauftempo in der Nähe von verbündeten Türmen und Heimerdingers Geschütztürmen.", "image": {"full": "Heimerdinger_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 192, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}