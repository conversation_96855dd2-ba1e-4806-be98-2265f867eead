{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sona": {"id": "<PERSON><PERSON>", "key": "37", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": {"full": "Sona.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "37000", "num": 0, "name": "default", "chromas": false}, {"id": "37001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "37002", "num": 2, "name": "Sona z Pentakill", "chromas": false}, {"id": "37003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "37004", "num": 4, "name": "<PERSON><PERSON> z <PERSON>", "chromas": true}, {"id": "37005", "num": 5, "name": "Arcade Sona", "chromas": true}, {"id": "37006", "num": 6, "name": "DJ <PERSON><PERSON>", "chromas": false}, {"id": "37007", "num": 7, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "37009", "num": 9, "name": "Sona z Odysei", "chromas": true}, {"id": "37017", "num": 17, "name": "Sona z Psychoperacji", "chromas": true}, {"id": "37026", "num": 26, "name": "Son<PERSON> z Pentakill III: Lost Chapter", "chromas": true}, {"id": "37035", "num": 35, "name": "Czarodziejka Gwiazd Sona", "chromas": true}, {"id": "37045", "num": 45, "name": "Sona Nieśmiertelnej Podróży", "chromas": true}, {"id": "37046", "num": 46, "name": "Sona Nieśmiertelnej <PERSON> (Prestiżowa)", "chromas": false}, {"id": "37056", "num": 56, "name": "Zwycięska Sona", "chromas": true}], "lore": "Sona jest najprzedniej<PERSON>ą wirtuozką etwahlu w Demacii. Za pomocą swojego instrumentu przemawia pełnymi wdzięku akordami i niesamowitymi ariami. Dzięki swoim dystyngowanym manierom zjednała sobie serca szlachty, lecz niektórzy podejrzewają, że jej czarujące melodie emanują magią, która jest zakazana w Demacii. Cicha dla niez<PERSON>, jakoś rozumiana przez blis<PERSON> tow<PERSON>y, Sona wygrywa harmonie nie tylko po to, by nie<PERSON><PERSON> ukojenie rannym sojusznikom, ale i by powa<PERSON><PERSON> niczego niespodziewających się wrogów.", "blurb": "Sona jest najprzedniejszą wirtuozką etwahlu w Demacii. Za pomocą swojego instrumentu przemawia pełnymi wdzięku akordami i niesamowitymi ariami. Dzięki swoim dystyngowanym manierom zjednała sobie serca szlachty, lecz niektórzy podejrzewają, że jej...", "allytips": ["<PERSON>aj się by<PERSON> b<PERSON>, akty<PERSON><PERSON><PERSON><PERSON> aurę <PERSON>, ale uwa<PERSON>, by nie schwy<PERSON>ł cię wróg.", "Zachowaj Crescendo na krytyczny moment.", "Użycie Arii Wytrwałości w odpowiedniej chwili zwiększa twoje szanse na przeżycie."], "enemytips": ["<PERSON>e pozostawaj w grupie, je<PERSON><PERSON>a jest w pobliżu. Tym sposobem nie zmusi całej twojej drużyny do tańca.", "Zabijaj <PERSON> jako pier<PERSON>. Inaczej uleczy całą swoją drużynę."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 2, "magic": 8, "difficulty": 4}, "stats": {"hp": 550, "hpperlevel": 91, "mp": 340, "mpperlevel": 45, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.3, "attackspeed": 0.644}, "spells": [{"id": "SonaQ", "name": "<PERSON><PERSON><PERSON>", "description": "Sona gra Hymn <PERSON>, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fale dźwiękowe, które zadają obrażenia magiczne dwóm najbliższym wrogom (priorytetowo traktuje bohaterów i potwory). Sona otrzymuje tymczasową aurę, która daje sojusznikom przebywającym w strefie wokół niej dodatkowe obrażenia przy ich następnym ataku.", "tooltip": "Sona zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage> dwóm najbliż<PERSON>ym wrogom (priorytetowo traktując bohaterów). Następnie zaczyna grać nową <keywordMajor>melodi<PERSON></keywordMajor>. Otrzymu<PERSON><PERSON>adunek <keywordMajor>Accelerando</keywordMajor> za każdego bohatera, któremu zadasz obrażenia za pomocą tej umiejętności.<br /><br /><keywordMajor>Melodia:</keywordMajor> Sona na {{ auraduration }} sek. zyskuje aurę, która daje sojuszniczym bohaterom premię w wysokości <magicDamage>{{ totalonhitdamage }} pkt. obrażeń magicznych</magicDamage> %i:OnHit% do ich kolejnego ataku wykonanego w ciągu {{ onhitduration }} sek.<br /><br /><keywordMajor>Akord Potęgi — Staccato:</keywordMajor> Dodatkowe obrażenia Akordu Potęgi (w sumie <magicDamage>{{ totalstaccatodamage }} pkt. obrażeń magicznych</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia (użycie)", "Obrażenia (melodia)", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "SonaQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SonaW", "name": "<PERSON>", "description": "Sona gra <PERSON>, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> osłaniające melodie, które leczą Sonę i najbliższego rannego sojusznika. Sona otrzymuje tymczasową aurę, która daje sojusznikom przebywającym w strefie wokół niej tymczasową tarczę.", "tooltip": "<spellPassive>Użycie:</spellPassive> Sona przywraca <healing>{{ totalheal }} pkt. zdrowia</healing> sobie i pobliskiemu sojuszniczemu bohaterowi (priorytetowo traktując bohaterów o najniższym poziomie zdrowia). Następnie zaczyna grać nową <keywordMajor>melodię</keywordMajor>.<br /><br /><keywordMajor>Melodia:</keywordMajor> Sona na {{ auraduration }} sek. zyskuje aurę, która zapewnia sojuszniczym bohaterom <shield>{{ totalshield }} pkt. tarczy</shield> na {{ shieldduration }} sek.<br /><br />Zyskujesz ładunek <keywordMajor>Accelerando</keywordMajor> za każdym razem, gdy uleczysz rannego sojusznika lub za pomocą tarczy ochronisz sojusznika przed co najmniej {{ accelerandoshieldbreakpoint }} pkt. obrażeń.<br /><br /><keywordMajor>Akord Potęgi — Diminuendo:</keywordMajor> Akord Potęgi zmniejsza również obrażenia fizyczne i magiczne zadawane przez cel o {{ totaldiminuendoweakenpercent }} na {{ diminuendoduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Leczenie (użycie)", "Tarcza (melodia)", "Koszt (@AbilityResourceName@)"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SonaW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SonaE", "name": "Pieśń Prędkości", "description": "Sona gra Pieśń P<PERSON>, zapewniając pobliskim sojusznikom dodatkową prędkość ruchu. Sona zyskuje tymczasową aurę, która daje sojuszniczym bohaterom przebywającym w strefie wokół niej dodatkową prędkość ruchu.", "tooltip": "<spellPassive>Użycie:</spellPassive> Sona zaczyna grać nową <keywordMajor>melodię</keywordMajor> i otrzymuje <speed>{{ totalselfmovementspeed }} prędkości ruchu</speed> na {{ selfmovementspeeddurationmin }} sek. Czas trwania efektu może zostać wydłużony do {{ selfmovementspeeddurationmax }} sek., jeśli bohaterka nie otrzyma obrażeń. <br /><br /><keywordMajor>Melodia:</keywordMajor> Sona na {{ auraduration }} sek. zyskuje aurę, która zapewnia sojuszniczym bohaterom <speed>{{ totalallymovementspeed }} prędkości ruchu</speed> na {{ allymovementspeedduration }} sek.<br /><br /><keywordMajor>Akord Potęgi — Tempo:</keywordMajor> Akord Potęgi na {{ tempoduration }} sek. <status>spowalnia</status> cel o {{ totaltempomovespeedslow }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu"], "effect": ["{{ allybasemovementspeed*100.000000 }}% -> {{ allybasemovementspeednl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "SonaE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "SonaR", "name": "Crescendo", "description": "Sona gra swoje <PERSON>ydzieło, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wrogich bohaterów, zmuszając ich do tańca i zadając im obrażenia magiczne.", "tooltip": "Sona gra hipnotyzuj<PERSON>ą melodię, która na {{ stunduration }} sek. <status>og<PERSON><PERSON><PERSON></status> wroga i zadaje mu <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "SonaR.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Akord Po<PERSON>ę<PERSON>", "description": "<passive>Accelerando</passive>: Sona na stałe zyskuje przyspieszenie podstawowych umiejętności za ich poprawne używanie, aż do osiągnięcia limitu. Gdy to nast<PERSON><PERSON>, kolejne udane użycia będą skracać czas odnowienia jej superumiejętności.<br><br><passive>Akord Potęgi</passive>: Co kilka rzuconych zaklęć kolejny atak Sony zada dodatkowe obrażenia magiczne oraz wywoła specjalny efekt, w zależności od tego, której podstawowej umiejętności użyła jako ostatniej.", "image": {"full": "Sona_Passive_Charged.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}