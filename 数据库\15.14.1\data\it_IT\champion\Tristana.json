{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tristana": {"id": "<PERSON><PERSON>", "key": "18", "name": "<PERSON><PERSON>", "title": "l'artigliere degli yordle", "image": {"full": "Tristana.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "18000", "num": 0, "name": "default", "chromas": false}, {"id": "18001", "num": 1, "name": "<PERSON>a <PERSON>", "chromas": false}, {"id": "18002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18006", "num": 6, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "18010", "num": 10, "name": "Tristana Addestratrice di Draghi", "chromas": true}, {"id": "18011", "num": 11, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "18012", "num": 12, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "18024", "num": 24, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "18033", "num": 33, "name": "Tristana Cosplayer di Pinguì", "chromas": true}, {"id": "18040", "num": 40, "name": "Tristana He<PERSON>", "chromas": false}, {"id": "18041", "num": 41, "name": "Tristana Fuoco d'Artificio", "chromas": true}, {"id": "18051", "num": 51, "name": "Tristana Fiore spirituale", "chromas": true}, {"id": "18061", "num": 61, "name": "Tristana <PERSON> fatata", "chromas": true}], "lore": "Mentre molti altri yordle si dedicano alle scoperte, alle invenzioni o agli intrighi, Tristana è stata sempre affascinata dalle avventure dei grandi guerrieri. Ha sentito molto parlare di Runeterra, delle sue fazioni e delle sue guerre, e ha pensato di essere anche lei degna di divenire materia delle leggende. Dopo aver messo piede nel mondo per la prima volta, ha imbracciato il suo fidato cannone Boomer e ora si lancia in battaglia con coraggio e ottimismo.", "blurb": "Mentre molti altri yordle si dedicano alle scoperte, alle invenzioni o agli intrighi, Tristana è stata sempre affascinata dalle avventure dei grandi guerrieri. Ha sentito molto parlare di Runeterra, delle sue fazioni e delle sue guerre, e ha pensato di...", "allytips": ["L'enorme cannone permette a Tristana di colpire i bersagli a grande distanza. Approfittane per non farti mettere le mani addosso dai nemici.", "Usa Salto razzo per balzare in avanti dopo aver potenziato Carica esplosiva su un nemico e finirlo con una raffica di danni.", "Usa Fuoco rapido per potenziare più facilmente Carica esplosiva sui campioni nemici."], "enemytips": ["Se vedi Tristana attivare Fuoco rapido in un combattimento, stordiscila e arretra finché l'abilità non finisce.", "Stai lontano dai tuoi minion in corsia per subire meno danni collaterali da Carica esplosiva."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 5, "difficulty": 4}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 300, "mpperlevel": 32, "movespeed": 325, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.5, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 1.5, "attackspeed": 0.656}, "spells": [{"id": "TristanaQ", "name": "Fuoco rapido", "description": "Tristana spara rapidamente con la sua arma, aumentando la sua velocità d'attacco per un breve periodo di tempo.", "tooltip": "Tristana mette l'arma in modalità automatica e ottiene un <attackSpeed>{{ attackspeedmod*100 }}% di velocità d'attacco</attackSpeed> per {{ buffduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Velocità d'attacco", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TristanaQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TristanaW", "name": "Salto del razzo", "description": "Tristana spara al suolo per sollevarsi in aria verso una posizione distante, infliggendo danni e rallentando i nemici circostanti a dove atterra per un breve periodo.", "tooltip": "Tristana si lancia in aria, infliggendo <magicDamage>{{ landingdamage }} danni magici</magicDamage> e <status>rallentando</status> i nemici di un {{ slowmod*-100 }}% per {{ slowduration }} secondi quando atterra.<br /><br />Le eliminazioni dei campioni e le detonazioni di <spellName>Carica esplosiva</spellName> con cariche massime sui campioni ripristinano la ricarica di questa abilità.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TristanaW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TristanaE", "name": "Carica esplosiva", "description": "Quando Tristana uccide un'unità, le sue palle di cannone esplodono in schegge, infliggendo danni ai nemici circostanti. Può essere attivata per piazzare una bomba su un bersaglio, che esplode dopo una breve durata, infliggendo danni alle unità circostanti.", "tooltip": "<spellPassive>Passiva:</spellPassive> gli attacchi di Tristana che uccidono i nemici infliggono <magicDamage>{{ passivedamage }} danni magici</magicDamage> alle unità vicine.<br /><br /><spellActive>Attiva:</spellActive> Tristana attacca una bomba a un'unità o a una torre, infliggendo <physicalDamage>{{ activedamage }} danni fisici</physicalDamage> ai nemici vicini dopo {{ activeduration }} secondi. I danni sono aumentati del {{ critchanceamp*100 }}% della probabilità di colpo critico e del {{ activeperstackamp*100 }}% ogni volta che Tristana colpisce con un attacco o un'abilità (massimo 4 cariche).<br /><br />A {{ activemaxstacks }} cariche, la bomba esplode immediatamente (massimo <physicalDamage>{{ activemaxdamage }} danni fisici</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni esplosione passiva", "Danni base carica", "Rapporto attacco fisico", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ activebadratio*100.000000 }}% -> {{ activebadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "TristanaR", "name": "Palla di cannone", "description": "Tristana carica una palla di cannone gigante e la spara contro un'unità nemica. Così facendo infligge danni magici e respinge il bersaglio. Se il bersaglio resiste alla bomba di Carica esplosiva, il raggio dell'esplosione è raddoppiato.", "tooltip": "Tristana spara un'enorme palla di cannone, che infligge <magicDamage>{{ damagecalc }} danni magici</magicDamage> al bersaglio, lo <status>respinge</status> e lo <status>stordisce</status> insieme ai nemici vicini per {{ stunduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Distanza respingimento", "<PERSON><PERSON> stordimento:"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ knockbackdistance }} -> {{ knockbackdistanceNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Supermira", "description": "Aumenta la gittata d'attacco di Tristana con il livello.", "image": {"full": "Tristana_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}