{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"KSante": {"id": "KSante", "key": "897", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON> <PERSON><PERSON>z von <PERSON>", "image": {"full": "KSante.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "897000", "num": 0, "name": "default", "chromas": false}, {"id": "897001", "num": 1, "name": "Elysischer K'Sante", "chromas": true}, {"id": "897002", "num": 2, "name": "Elysischer K'Sante (Prestige)", "chromas": false}, {"id": "897008", "num": 8, "name": "HEARTSTEEL-<PERSON><PERSON>Sante", "chromas": true}], "lore": "Aufsässig und kühn kämpft K'Sante gegen kolossale Kreaturen und skrupellose Aufgestiegene, um seine Heimat Nazumah, eine wichtige Oase inmitten der Sandlandschaften von Shurima, zu beschützen. Doch nach einer heftigen Auseinandersetzung mit seinem ehemaligen Partner erkennt K'Sante, dass er sein unbeirrbares Streben nach Erfolg zügeln muss, um zu einem Krieger zu werden, der würdig ist, seine Stadt anzuführen. Nur so kann er es vermeiden, seinem eigenen Stolz zum Opfer zu fallen, und die nötige Weisheit erlangen, um die bösartigen Monster zu bezwingen und sein Volk zu beschützen.", "blurb": "Aufsässig und kühn kämpft K'Sante gegen kolossale Kreaturen und skrupellose Aufgestiegene, um seine Heimat Nazumah, eine wichtige Oase inmitten der Sandlandschaften von Shurima, zu beschützen. Doch nach einer heftigen Auseinandersetzung mit seinem...", "allytips": ["<PERSON><PERSON><PERSON> „Schallwelle“ vor „Zorn des Drachen“, um Gegner mit „Resonanzschlag“ zu verfolgen.", "<PERSON><PERSON>e „Unruhe“ besser aus, indem du normale Angriffe zwischen Zauber einstreust, um mehr Schaden zu verursachen und den Energieverlust zu verringern.", "„Lebensretter“ und „Eiserner Wille“ sind mächtige Werkzeuge im Dschungel."], "enemytips": ["<PERSON><PERSON><PERSON> ve<PERSON>, um Lee Sins ultimative Fähigkeit, „Zorn des Drachen“, weniger stark wirken zu lassen.", "Lee Sin kann mit „Eiserner Wille“ und „Verkrüppeln“ leicht normalen Schaden kontern, aber er ist weiterhin gegen magischen Schaden verwundbar.", "<PERSON> baut <PERSON> da<PERSON>, seine Fähigkeitenkombinationen einzusetzen. <PERSON><PERSON> ihn kampfunfähig, damit er diese nicht mehr effektiv nutzen kann."], "tags": ["Tank", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 8, "magic": 7, "difficulty": 9}, "stats": {"hp": 625, "hpperlevel": 120, "mp": 320, "mpperlevel": 60, "movespeed": 330, "armor": 36, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 2.1, "attackrange": 150, "hpregen": 9.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 1, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 3.5, "attackspeedperlevel": 2.5, "attackspeed": 0.688}, "spells": [{"id": "KSanteQ", "name": "Ntofo-Schlag", "description": "<PERSON><PERSON><PERSON><PERSON> schlägt zu, beschädigt und verlangsamt Gegner in einer kurzen Linie.<br><br>Bei einem Treffer erhält er eine Steigerung „Ntofo-Schlag“. Bei 2 Steigerungen feuert K'Sante eine Schockwelle ab, die Feinde anzieht.<br><br>Keine Zurückhaltung: Abklingzeit wird verringert.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> schlägt mit seiner Waffe und verursacht <physicalDamage>{{ basedamage }} normalen Schaden</physicalDamage> und <status>verlangsamt</status> Gegner {{ slowduration }} Sekunden lang um {{ slowpercent*100 }}&nbsp;%. Bei einem Treffer erhält er {{ recastwindow }}&nbsp;Sekunden lang eine Steigerung von „Ntofo-Schlag“. Bei 2&nbsp;Steigerungen feuert K'Sante stattdessen eine Schockwelle ab, die Gegner {{ stunduration }}&nbsp;Sekunde lang <status>betäubt</status> und <status>heranzieht</status>.<br /><br /><keywordMajor>Keine Zurückhaltung</keywordMajor>: Die Abklingzeit ist um {{ rcooldownreduction.0*100 }}&nbsp;Sekunde verringert.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ flatdamage }} -> {{ flatdamageNL }}"]}, "maxrank": 5, "cooldown": [3.5, 3.5, 3.5, 3.5, 3.5], "cooldownBurn": "3.5", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450, 450, 450], "rangeBurn": "450", "image": {"full": "KSanteQ.png", "sprite": "spell7.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteW", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lädt sich auf, erleidet verringerten Schaden, springt dann nach vorne und stößt dabei Gegner zurück und betäubt sie.<br><br><PERSON><PERSON> Zurückhaltung: Verursachter Schaden wird erh<PERSON>, <PERSON><PERSON><PERSON><PERSON> stößt aber nicht länger zurück oder betäubt.", "tooltip": "K'<PERSON><PERSON> hebt seine Waffen und nimmt bis zu {{ mindurationtooltip }}–{{ maxduration.1 }}&nbsp;Sekunden lang eine defensive Haltung ein, wodurch er unaufhaltsam wird und eingehenden Schaden um {{ damagereduction*100 }}&nbsp;% verringert. Dann stürmt er vorwärts, verursacht <physicalDamage>normalen Schaden in Höhe von {{ basedamage }}+{{ totalmaxhealthdamage }} des maximalen Lebens</physicalDamage>. Er <status>stößt getroffene Gegner zurück</status> und <status>betäubt</status> sie bis zu {{ minknockbackduration }}–{{ maxknockbackduration }}&nbsp;Sekunden lang (abhäng<PERSON> von der Vorbereitungsdauer).<br /><br /><keywordMajor>Keine Zurückhaltung</keywordMajor>: Die Abklingzeit dieser Fähigkeit wird zurückgesetzt. Verursacht zusätzlich {{ rdamageincreasemin*100 }}–{{ rdamageincreasemax*100 }}&nbsp;% Schaden als <trueDamage>normalen Schaden</trueDamage> (abhängig von der Aufladezeit), die Schadensreduzierung wird auf {{ rdamagereduction*100 }}&nbsp;% erhöht und das Sprungtempo wird erhöht, aber K'Sante <status>stößt nicht länger zurück</status> oder <status>betäubt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KSanteW.png", "sprite": "spell7.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteE", "name": "Beinarbeit", "description": "<PERSON><PERSON><PERSON><PERSON> springt und erhält einen Schild. Wenn du einen Verbündeten anvisierst, rennt K'Sante zu dem Verbündeten mit erhöhter Reichweite und beide erhalten einen Schild.<br><br><PERSON><PERSON> Zurückhaltung: Die Abklingzeit wird verringert und das Tempo erhöht.", "tooltip": "<PERSON><PERSON><PERSON><PERSON> springt und erhält für {{ shieldduration }} Sekunden einen <shield>{{ totalshield }} Schild</shield>. Wenn er zu einem Verbündeten springt, ist die zurückgelegte Distanz erheblich erhöht und der Verbündete erhält ebenfalls einen <shield>Schild</shield>.<br /><br /><keywordMajor>Keine Zurückhaltung</keywordMajor>: Die Abklingzeit wird um {{ cooldownmodao*100 }}&nbsp;% verringert und das Sprungtempo dieser Fähigkeit ist erhöht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ shieldbaseamountfast }} -> {{ shieldbaseamountfastNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [525, 525, 525, 525, 525], "rangeBurn": "525", "image": {"full": "KSanteE.png", "sprite": "spell7.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KSanteR", "name": "<PERSON><PERSON>", "description": "K'<PERSON><PERSON> stößt einen Gegner zurück und schleudert ihn durch jede Wand in seinem Weg. Dann geht K'Sante in die Vollen und stürzt sich auf den Feind, wobei er mehr Schaden, Heilung und veränderte Fähigkeiten auf Kosten einer verringerten Verteidigung erhält.", "tooltip": "<PERSON><PERSON><PERSON><PERSON>ert seine <PERSON>, um einen gegnerischen Champion <status>zurückzustoßen</status>, was ihm <physicalDamage>{{ basedamage }} normalen Schaden</physicalDamage> zufügt, und rennt dann hinter ihm her und erhält {{ alloutduration }}&nbsp;Sekunden lang <keywordMajor>Keine Zurückhaltung</keywordMajor>. Wenn der Gegner gegen eine Wand stößt, wird er durch die Wand <status>zurückgestoßen</status> und K'Sante verursacht erneut <physicalDamage>{{ totaldamageslamdown }} normalen Schaden</physicalDamage>.<br /><br /><keywordMajor>Keine Zurückhalt<PERSON></keywordMajor>: K'Santes Fähigkeiten werden verbessert und er erhält <attackSpeed>{{ attackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed>, {{ armorpenpercent*100 }}&nbsp;% zusätzliche Rüstungsdurchdringung und <omnivamp>{{ omnivamp*100 }}&nbsp;% Omnivampir</omnivamp>. Er verliert jedoch <healing>{{ healthlost*100 }}&nbsp;% maximales Leben</healing>, <scaleArmor>{{ defenseslost*100 }}&nbsp;% zusätzliche Rüstung</scaleArmor> und <scaleMR>{{ defenseslost*100 }}&nbsp;% zusätzliche Magieresistenz</scaleMR>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Schaden durch Folgeangriff", "Abklingzeit", "Angriffstempo"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slamdownstrikedamage }} -> {{ slamdownstrikedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ attackspeed*100.000000 }}&nbsp;% -> {{ attackspeednl*100.000000 }}&nbsp;%"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [250, 250, 250], "rangeBurn": "250", "image": {"full": "KSanteR.png", "sprite": "spell7.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "K'Santes Fähigkeiten markieren Ziele, die bei seinem nächsten Angriff erhöhten Schaden erleiden.<br><br><PERSON><PERSON>: K'Sant<PERSON> verursacht mit all seinen Angriffen und Fähigkeiten erhöhten Schaden.", "image": {"full": "Icons_KSante_P.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}