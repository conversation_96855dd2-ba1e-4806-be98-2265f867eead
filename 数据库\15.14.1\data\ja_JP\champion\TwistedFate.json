{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TwistedFate": {"id": "TwistedFate", "key": "4", "name": "ツイステッド・フェイト", "title": "不敗のイカサマ師", "image": {"full": "TwistedFate.png", "sprite": "champion4.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "4000", "num": 0, "name": "default", "chromas": false}, {"id": "4001", "num": 1, "name": "PAX ツイステッド・フェイト", "chromas": false}, {"id": "4002", "num": 2, "name": "ジャックハート ツイステッド・フェイト", "chromas": false}, {"id": "4003", "num": 3, "name": "壮麗なカード師ツイステッド・フェイト", "chromas": false}, {"id": "4004", "num": 4, "name": "タンゴ ツイステッド・フェイト", "chromas": false}, {"id": "4005", "num": 5, "name": "荒野のツイステッド・フェイト", "chromas": false}, {"id": "4006", "num": 6, "name": "マスケット ツイステッド・フェイト", "chromas": false}, {"id": "4007", "num": 7, "name": "冥府の世界ツイステッド・フェイト", "chromas": false}, {"id": "4008", "num": 8, "name": "レッドカード ツイステッド・フェイト", "chromas": false}, {"id": "4009", "num": 9, "name": "怪盗ツイステッド・フェイト", "chromas": false}, {"id": "4010", "num": 10, "name": "ブラッドムーン ツイステッド・フェイト", "chromas": false}, {"id": "4011", "num": 11, "name": "パルスファイア ツイステッド・フェイト", "chromas": true}, {"id": "4013", "num": 13, "name": "オデッセイ ツイステッド・フェイト", "chromas": true}, {"id": "4023", "num": 23, "name": "DWG ツイステッド・フェイト", "chromas": true}, {"id": "4025", "num": 25, "name": "クライムシティー ナイトメア ツイステッド・フェイト", "chromas": true}, {"id": "4036", "num": 36, "name": "スペースグルーヴ ツイステッド・フェイト", "chromas": false}, {"id": "4045", "num": 45, "name": "勝利の栄光ツイステッド・フェイト", "chromas": false}], "lore": "たかが知れたこの世界でギャンブルし、魅了することで渡って来たツイステッド・フェイトは悪名高きいかさまトランプ師である。この詐欺師は、成金や愚か者どもの厭悪と羨望の的となっている。物事を真面目に受け止めることなどほとんどなく、毎日を嘲笑し、無関心に闊歩する。そしていつでも、ありとあらゆる“切り札”を袖に忍ばせている。", "blurb": "たかが知れたこの世界でギャンブルし、魅了することで渡って来たツイステッド・フェイトは悪名高きいかさまトランプ師である。この詐欺師は、成金や愚か者どもの厭悪と羨望の的となっている。物事を真面目に受け止めることなどほとんどなく、毎日を嘲笑し、無関心に闊歩する。そしていつでも、ありとあらゆる“切り札”を袖に忍ばせている。", "allytips": ["味方と協力し、敵の不意をつけるタイミングで「デスティニー」を発動しよう。", "ステルス状態になれるチャンピオンは、それに頼って体力残量がかなり低下するまで逃走しないことが多い。「デスティニー」を使えば、ステルス状態の敵の居場所が分かるので、うまく利用してとどめを刺そう。", "ツイステッド・フェイトは、攻撃力を増加させても魔力を増加させても有効なチャンピオンだ。チームの構成に応じて様々な役割を果たすことができる。"], "enemytips": ["ゲーム序盤は特に、体力残量が低下したら「ワイルドカード」を避けるのに集中すること。", "こちらの体力残量が少ないときにツイステッド・フェイトが「デスティニー」を発動したら、ギャンクのリスクを減らすためにも、すかさず安全な場所に退避しよう。"], "tags": ["Mage", "Marksman"], "partype": "マナ", "info": {"attack": 6, "defense": 2, "magic": 6, "difficulty": 9}, "stats": {"hp": 604, "hpperlevel": 108, "mp": 333, "mpperlevel": 39, "movespeed": 330, "armor": 24, "armorperlevel": 4.35, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "WildCards", "name": "ワイルドカード", "description": "扇状を描くように敵を貫通する3枚のカードが投げられ、命中した敵ユニットにそれぞれダメージを与える。", "tooltip": "3枚のカードを投げ、それぞれが<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.75, 5.5, 5.25, 5], "cooldownBurn": "6/5.75/5.5/5.25/5", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "WildCards.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "Pick<PERSON>ard", "name": "ドロー", "description": "デッキから魔法のカードを1枚選び、次の通常攻撃に使用する。この通常攻撃には追加効果がつく。", "tooltip": "カードのシャッフルを開始し、<recast>再発動</recast>で3枚のカードから1枚ピックする。選んだカードによって、次の通常攻撃に異なる効果が付与される。<br /><li>ブルーカードは<magicDamage>{{ bluedamage }}の魔法ダメージ</magicDamage>を与えて、<scaleMana>マナを{{ e6 }}</scaleMana>回復する。<li>レッドカードは周囲の敵に<magicDamage>{{ reddamage }}の魔法ダメージ</magicDamage>を与えて、2.5秒間{{ e2 }}%の<status>スロウ効果</status>を付与する。<li>ゴールドカードは<magicDamage>{{ golddamage }}の魔法ダメージ</magicDamage>を与えて、{{ e3 }}秒間<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ブルーカード: 基本ダメージ", "ブルーカード: マナ回復量", "レッドカード: 基本ダメージ", "レッドカード: スロウ効果 %", "ゴールドカード: 基本ダメージ", "ゴールドカード: スタン効果時間", "@AbilityResourceName@コスト"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ e5 }} -> {{ e5NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [40, 60, 80, 100, 120], [30, 35, 40, 45, 50], [1, 1.25, 1.5, 1.75, 2], [30, 45, 60, 75, 90], [15, 22.5, 30, 37.5, 45], [70, 90, 110, 130, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/60/80/100/120", "30/35/40/45/50", "1/1.25/1.5/1.75/2", "30/45/60/75/90", "15/22.5/30/37.5/45", "70/90/110/130/150", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [200, 200, 200, 200, 200], "rangeBurn": "200", "image": {"full": "PickACard.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "CardmasterStack", "name": "スタックデッキ", "description": "通常攻撃4回ごとに追加ダメージがつき、攻撃速度が増加する。", "tooltip": "<spellPassive>自動効果:</spellPassive> <attackSpeed>攻撃速度が{{ attackspeedbonus }}%</attackSpeed>増加する。また、通常攻撃4回ごとに<magicDamage>{{ bonusdamage }}の魔法ダメージ</magicDamage>を追加で与える。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["追加ダメージ", "攻撃速度"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ attackspeedbonus }}% -> {{ attackspeedbonusNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "4", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "固有スキル", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "CardmasterStack.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "固有スキル"}, {"id": "Destiny", "name": "デスティニー", "description": "敵の運命を予知し、敵のチャンピオン全員を可視状態にする。また、1.5秒後に指定位置へワープする「ゲート」を使用できるようになる。", "tooltip": "カードに意識を集中し、{{ e1 }}秒間マップ上にいるすべての敵チャンピオンの<keywordStealth>真の視界</keywordStealth>を得て、<recast>再発動</recast>が可能になる。<br /><br /><recast>再発動</recast>: 距離{{ e4 }}ユニット以内の場所にワープする。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["効果時間", "クールダウン"], "effect": ["{{ recastduration }} -> {{ recastdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [170, 140, 110], "cooldownBurn": "170/140/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [6, 8, 10], [0, 0, 0], [0, 0, 0], [5500, 5500, 5500], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "6/8/10", "0", "0", "5500", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "Destiny.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "イカサマダイス", "description": "ユニットを1体倒すたびに「幸運のサイコロ」を振り、1～6の追加ゴールドを獲得する。", "image": {"full": "Cardmaster_SealFate.png", "sprite": "passive4.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}