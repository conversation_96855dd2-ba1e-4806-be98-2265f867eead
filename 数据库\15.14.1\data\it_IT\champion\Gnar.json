{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gnar": {"id": "<PERSON><PERSON>", "key": "150", "name": "<PERSON><PERSON>", "title": "l'anello mancante", "image": {"full": "Gnar.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "150000", "num": 0, "name": "default", "chromas": false}, {"id": "150001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "150002", "num": 2, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "150003", "num": 3, "name": "<PERSON><PERSON> Nevicata", "chromas": false}, {"id": "150004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "150013", "num": 13, "name": "Gnar Megagalattico", "chromas": false}, {"id": "150014", "num": 14, "name": "Gnar SSG", "chromas": false}, {"id": "150015", "num": 15, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "150022", "num": 22, "name": "Gnar del Bosco Antico", "chromas": true}, {"id": "150031", "num": 31, "name": "Gnar la Ilusión", "chromas": true}], "lore": "Gnar è uno yordle primordiale con un carattere giocoso, con degli improvvisi scatti di rabbia che lo trasformano in un enorme belva distruttrice. Congelato nel Vero Ghiaccio per migliaia di anni, questo curioso yordle si è liberato e saltella in un mondo per lui completamente nuovo, esotico e meraviglioso. Deliziato dal pericolo, Gnar lancia ciò che gli capita per le mani ai nemici, che si tratti del suo boomerang di denti o di un edificio nei pressi.", "blurb": "Gnar è uno yordle primordiale con un carattere giocoso, con degli improvvisi scatti di rabbia che lo trasformano in un enorme belva distruttrice. Congelato nel Vero Ghiaccio per migliaia di anni, questo curioso yordle si è liberato e saltella in un...", "allytips": ["Gestire la Rabbia è importante. Temporeggia con le trasformazioni per avere il massimo dei benefici da entrambe.", "Posizionati vicino ai muri per attirare i nemici e stordirli con l'abilità suprema.", "Conosci i tuoi punti di forza! Quando è mini, G<PERSON> è veloce, fragile e ha degli alti danni sostenuti. Quando è grande è lento, è resistente e infligge alti danni a cadenza lenta."], "enemytips": ["Gnar non può ottenere Rabbia per 15 secondi, quando si trasforma da grande a piccolo. Sfrutta il momento per ingaggiare la sua squadra.", "Le animazioni di Gnar e la barra delle risorse cambiano, quando sta per trasformarsi. ", "La suprema di Gnar stordisce, se lancia l'avversario contro un muro. Stai attento, se combatti in prossimità di un muro."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 5, "magic": 5, "difficulty": 8}, "stats": {"hp": 540, "hpperlevel": 79, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 32, "armorperlevel": 3.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 175, "hpregen": 4.5, "hpregenperlevel": 1.25, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.2, "attackspeedperlevel": 6, "attackspeed": 0.625}, "spells": [{"id": "GnarQ", "name": "Lancio del boomerang/SPLAT!", "description": "Gnar lancia un boomerang che infligge danni e rallenta i nemici che colpisce, per poi tornare da lui. Se afferra il boomerang la ricarica si riduce.<br><br>Mega Gnar lancia un masso che si ferma alla prima unità colpita, infliggendo danni e rallentando i nemici nelle vicinanze. Può essere raccolto per ridurre la ricarica.", "tooltip": "<keywordMajor>Mini Gnar:</keywordMajor> Gnar lancia un boomerang che infligge <physicalDamage>{{ spell.gnarq:minitotaldamage }} danni fisici</physicalDamage> e <status>rallenta</status> i nemici del {{ spell.gnarq:slowamount*100 }}% per {{ spell.gnarq:slowduration }} secondi. Dopo aver colpito un nemico il boomerang torna indietro, infliggendo danni ridotti ai bersagli colpiti durante il ritorno. Ogni nemico può essere colpito solo una volta. Afferrare il boomerang riduce la sua ricarica del {{ spell.gnarq:minicdrefund*100 }}%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> boomerang", "<PERSON><PERSON>", "Rallentamento", "Quantità rallentamento masso", "Ricarica"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ megabasedamage }} -> {{ megabasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ megaslowamount*100.000000 }}% -> {{ megaslowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 17.5, 15, 12.5, 10], "cooldownBurn": "20/17.5/15/12.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "GnarQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GnarW", "name": "Iperattività/BAM!", "description": "Gli attacchi e le abilità di Gnar lo infervorano, facendogli infliggere danni bonus e conferendogli velocità di movimento.<br><br>Mega Gnar è troppo arrabbiato per essere iperattivo, quindi si alza in posizione eretta e colpisce un'area davanti a lui, stordendo i nemici.", "tooltip": "<keywordMajor>Mini Gnar - Passiva:</keywordMajor> ogni terzo attacco base o abilità usata contro lo stesso nemico infligge <magicDamage>{{ spell.gnarw:minitotaldamage }} (+ {{ spell.gnarw:minipercenthpdamage*100 }}% salute massima del bersaglio) danni magici</magicDamage> e fornisce <speed>{{ spell.gnarr:rhypermovementspeedpercent }}% velocità di movimento</speed> che decresce nell'arco di {{ minihasteduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "% danni salute iperattività", "Danni BAM!"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ minipercenthpdamage*100.000000 }}% -> {{ minipercenthpdamagenl*100.000000 }}%", "{{ megabasedamage }} -> {{ megabasedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GnarW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GnarE", "name": "Hop!/CRASH!", "description": "Gnar salta in un punto e rimbalza sulla testa dell'unità su cui atterra, andando più lontano.<br><br>Mega Gnar è troppo grande per rimbalzare, quindi atterra con una forza devastante che infligge danni ad area.", "tooltip": "<keywordMajor>Mini Gnar:</keywordMajor> Gnar balza e ottiene <attackSpeed>{{ spell.gnare:minibas*100 }}% velocità d'attacco</attackSpeed> per {{ spell.gnare:miniasduration }} secondi. Se atterra su un'unità, rimbalza e va più lontano. Se rimbalza su un nemico, gli infligge <physicalDamage>{{ spell.gnare:minitotaldamage }} danni fisici</physicalDamage> e lo <status>rallenta</status> dell'{{ spell.gnare:movespeedmod*-100 }}% per un breve periodo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON>ni <PERSON>!", "Danni CRASH!", "Velocità d'attacco bonus", "Ricarica"], "effect": ["{{ minidamage }} -> {{ minidamageNL }}", "{{ megadamage }} -> {{ megadamageNL }}", "{{ minibas*100.000000 }}% -> {{ minibasnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "GnarE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "GnarR", "name": "GNAR!", "description": "Mega Gnar lancia i nemici che ha intorno nella direzione scelta, da<PERSON><PERSON><PERSON><PERSON><PERSON> e rallentandoli. Se un nemico colpisce un muro, viene stordito e subisce danni bonus.", "tooltip": "<keywordMajor>Mini Gnar - Passiva:</keywordMajor> aumenta il bonus alla <speed>velocità di movimento</speed> di <spellName>Iperattività</spellName>.<br /><br /><keywordMajor>Mega Gnar: </keywordMajor> Gnar lancia i nemici nelle vicinanze, infliggendo <physicalDamage>{{ damage }} danni fisici</physicalDamage>, <status>respingendoli</status> e <status>rallentandoli</status> del {{ rslowpercent }}% per {{ rccduration }} secondi. I nemici che colpiscono un muro subiscono invece <physicalDamage>{{ walldamage }} danni fisici</physicalDamage> e sono <status>storditi</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento/Durata stordimento", "Iper Velocità di movimento", "Ricarica"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rccduration }} -> {{ rccdurationNL }}", "{{ rhypermovementspeedpercent }}% -> {{ rhypermovementspeedpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [90, 60, 30], "cooldownBurn": "90/60/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "-1", "range": [590, 590, 590], "rangeBurn": "590", "image": {"full": "GnarR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}], "passive": {"name": "<PERSON>", "description": "Gnar genera Rabbia mentre è in combattimento. Quando la Rabbia è al massimo, la prossima abilità lo trasforma in Mega Gnar, rendendolo più resistente e dandogli accesso a nuove abilità.", "image": {"full": "Gnar_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}