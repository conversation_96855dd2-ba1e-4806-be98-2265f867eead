{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kaisa": {"id": "<PERSON><PERSON>", "key": "145", "name": "Kai'Sa", "title": "Daughter of the Void", "image": {"full": "Kaisa.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "145000", "num": 0, "name": "default", "chromas": false}, {"id": "145001", "num": 1, "name": "Bullet Angel <PERSON>", "chromas": true}, {"id": "145014", "num": 14, "name": "K/DA Kai'Sa", "chromas": false}, {"id": "145015", "num": 15, "name": "Prestige K/DA Kai'Sa", "chromas": false}, {"id": "145016", "num": 16, "name": "iG Kai'Sa", "chromas": false}, {"id": "145017", "num": 17, "name": "Arcade Kai'Sa", "chromas": false}, {"id": "145026", "num": 26, "name": "K/DA ALL OUT Kai'Sa", "chromas": false}, {"id": "145027", "num": 27, "name": "Prestige K/DA ALL OUT Kai'Sa", "chromas": false}, {"id": "145029", "num": 29, "name": "Lagoon Dragon Kai'Sa", "chromas": false}, {"id": "145039", "num": 39, "name": "Prestige K/DA Kai'Sa (2022)", "chromas": false}, {"id": "145040", "num": 40, "name": "Star Guardian Kai'Sa", "chromas": false}, {"id": "145048", "num": 48, "name": "Inkshadow Kai'Sa", "chromas": false}, {"id": "145059", "num": 59, "name": "Heavenscale Kai'Sa", "chromas": false}, {"id": "145069", "num": 69, "name": "Dark Star Kai'Sa", "chromas": false}, {"id": "145070", "num": 70, "name": "Risen Legend <PERSON>", "chromas": false}, {"id": "145071", "num": 71, "name": "Immortalized Legend Kai'Sa", "chromas": false}], "lore": "Di<PERSON>lik oleh Void saat masih kecil, <PERSON><PERSON><PERSON> ber<PERSON><PERSON> bertahan hidup karena keuletan dan kekuatan tekadnya. Pengalaman membuatnya jadi pemburu mematikan. <PERSON><PERSON> sebagian orang, dia adalah pertanda masa depan yang tak ingin mereka lihat. Setelah memasuki simbiosis yang tidak mengenakkan dengan karapas Void yang hidup, akan segera tiba masa ketika dia harus memutuskan apakah akan memaafkan manusia yang menyebutnya monster, dan mengalahkan kegelapan yang menyertainya ... atau melupakannya saja, saat Void melahap dunia yang meninggalkannya.", "blurb": "Di<PERSON>lik oleh Void saat masih kecil, <PERSON><PERSON><PERSON> ber<PERSON><PERSON> bertahan hidup karena keuletan dan kekuatan tekadnya. Pengalaman membuatnya jadi pemburu mematikan. <PERSON><PERSON> se<PERSON>ian orang, dia adalah pertanda masa depan yang tak ingin mereka lihat. Setelah memasuki...", "allytips": ["Coba tangkap carry musuh sendirian untuk member<PERSON><PERSON> mereka dengan Icathian Rain.", "<PERSON><PERSON><PERSON> sama dengan timmu untuk menyiapkan Ultima dan mengo<PERSON><PERSON><PERSON> damage dengan pasif.", "Pastikan kamu membeli item yang akan mengevolusi minimal 1 atau 2 spell-mu."], "enemytips": ["<PERSON><PERSON><PERSON> sangat bagus dalam menyerang musuh yang sendirian. <PERSON><PERSON> sendirian saat melawannya.", "Kai'Sa sangat rentan melawan mage dan carry long range.", "Pastikan untuk menempatkan ward di titik buta agar kamu bisa melihat Kai'Sa datang."], "tags": ["Marksman", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 345, "mpperlevel": 40, "movespeed": 335, "armor": 27, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 59, "attackdamageperlevel": 2.6, "attackspeedperlevel": 1.8, "attackspeed": 0.644}, "spells": [{"id": "KaisaQ", "name": "Icathian Rain", "description": "Kai'Sa menembakkan rentetan misil yang mengejar target di sekitar.<br><br>Living Weapon: Icathian Rain di-upgrade untuk menembakkan lebih banyak misil.", "tooltip": "Kai'<PERSON> meluncurkan {{ e2 }} misil yang menyebar di antara musuh di sekitar, masing-masing mengh<PERSON>lkan <physicalDamage>{{ totalindividualmissiledamage }} physical damage</physicalDamage>, hingga maksimum {{ maxdamagedisplay }}. Misil tambahan yang mengenai champion atau monster menghasilkan {{ extrahitreduction*100 }}% damage.<br /><br /><keywordMajor>Berevolusi</keywordMajor>: Kai'Sa jadi menembakkan {{ e7 }} misil sebagai gantinya.<br />Saat ini: <physicalDamage>{{ f11.1 }}/{{ e6 }} Attack Damage Bonus</physicalDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage Per <PERSON>"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ basedamage }}-> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [40, 55, 70, 85, 100], [6, 6, 6, 6, 6], [0.25, 0.25, 0.25, 0.25, 0.25], [2, 2, 2, 2, 2], [0.35, 0.35, 0.35, 0.35, 0.35], [100, 100, 100, 100, 100], [12, 12, 12, 12, 12], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/55/70/85/100", "6", "0.25", "2", "0.35", "100", "12", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "KaisaQ.png", "sprite": "spell5.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaW", "name": "Void Seeker", "description": "<PERSON><PERSON><PERSON> menem<PERSON>kkan misil jarak jauh, menandai musuh dengan ability pasifnya.<br><br>Living Weapon: Void Seeker di-upgrade untuk menerapkan lebih banyak tanda pasif dan mengurangi cooldown saat mengenai champion.", "tooltip": "Kai'<PERSON> menembakkan ledakan Void yang menghasilkan <magicDamage>{{ totaldamage }} magic damage</magicDamage>, menerapkan {{ e4 }} stack <keywordMajor>Plasma</keywordMajor>, dan memberikan <keywordStealth>True Sight</keywordStealth> pada musuh pertama yang terkena selama {{ spell.kaisapassive:pduration }} detik.<br /><br /><keywordMajor>Evolved</keywordMajor>: Kai'Sa jadi menerapkan {{ e5 }} stack <keywordMajor>Plasma</keywordMajor> dan serangan yang mengenai champion akan mengurangi Cooldown sebesar {{ e3 }}%.<br />Saat ini: <scaleAP>{{ f2.1 }}/{{ e2 }} Ability Power</scaleAP>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Damage", "<PERSON><PERSON><PERSON>"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ e1 }}-> {{ e1NL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [55, 60, 65, 70, 75], "costBurn": "55/60/65/70/75", "datavalues": {}, "effect": [null, [30, 55, 80, 105, 130], [100, 100, 100, 100, 100], [75, 75, 75, 75, 75], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "30/55/80/105/130", "100", "75", "2", "3", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [3000, 3000, 3000, 3000, 3000], "rangeBurn": "3000", "image": {"full": "KaisaW.png", "sprite": "spell5.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaE", "name": "Supercharge", "description": "Kai'Sa meningkatkan Move Speed untuk sesaat, lalu meningkatkan Attack Speed.<br><br>Living Weapon: Supercharge ditingkatkan untuk memberikan Invisibility sesaat.", "tooltip": "Kai'Sa mengaktifkan supercharge energi Void-nya, mendapatkan <speed>{{ totalmovespeed }} Move Speed</speed> dan menjadi Ghost saat charge, lalu mendapatkan <attackSpeed>{{ e5 }}% Attack Speed</attackSpeed> selama {{ e2 }} detik.<br /><br />Serangan mengurangi Cooldown Ability ini sebanyak {{ e4 }} detik.<br /><br /><keywordMajor>Evolved</keywordMajor>: Kai'Sa juga menjadi <keywordStealth>Invisible</keywordStealth> selama {{ e7 }} detik.<br />Saat ini: <attackSpeed>{{ f10.1 }}%/{{ e6 }}% Attack Speed Bonus</attackSpeed>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Move Speed", "Attack Speed"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ effect1amount*100.000000 }}%-> {{ effect1amountnl*100.000000 }}%", "{{ effect5amount*100.000000 }}%-> {{ effect5amountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14.5, 13, 11.5, 10], "cooldownBurn": "16/14.5/13/11.5/10", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0.55, 0.6, 0.65, 0.7, 0.75], [4, 4, 4, 4, 4], [1.2, 1.2, 1.2, 1.2, 1.2], [0.5, 0.5, 0.5, 0.5, 0.5], [40, 50, 60, 70, 80], [100, 100, 100, 100, 100], [0.5, 0.5, 0.5, 0.5, 0.5], [0.6, 0.6, 0.6, 0.6, 0.6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.55/0.6/0.65/0.7/0.75", "4", "1.2", "0.5", "40/50/60/70/80", "100", "0.5", "0.6", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "KaisaE.png", "sprite": "spell6.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KaisaR", "name": "Killer Instinct", "description": "Kai'<PERSON> dash ke dekat champion musuh.", "tooltip": "<PERSON><PERSON><PERSON> berpindah ke dekat champion musuh yang terdampak <keywordMajor>Plasma</keywordMajor> dan mendapatkan <shield>{{ rcalculatedshieldvalue }} Shield</shield> selama {{ rshieldduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Range", "Cooldown", "Jumlah Shield", "Rasio Attack Damage"], "effect": ["{{ rrange }}-> {{ rrangeNL }}", "{{ cooldown }}-> {{ cooldownNL }}", "{{ rbasevalue }}-> {{ rbasevalueNL }}", "{{ rtotaladratio*100.000000 }}%-> {{ rtotaladrationl*100.000000 }}%"]}, "maxrank": 3, "cooldown": [120, 90, 60], "cooldownBurn": "120/90/60", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [2000, 2500, 3000], "rangeBurn": "2000/2500/3000", "image": {"full": "KaisaR.png", "sprite": "spell6.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Second Skin", "description": "Basic attack Kai'<PERSON> menambah stack Plasma, menghasilkan magic damage bonus yang terus meningkat. Efek immobilize sekutu membantu stack Plasma. <PERSON><PERSON> itu, pembelian item <PERSON>'<PERSON> meng-upgrade spell dasarnya dengan efek yang lebih kuat.", "image": {"full": "Kaisa_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}