{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Gnar": {"id": "<PERSON><PERSON>", "key": "150", "name": "ナー", "title": "ミッシングリンク", "image": {"full": "Gnar.png", "sprite": "champion1.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "150000", "num": 0, "name": "default", "chromas": false}, {"id": "150001", "num": 1, "name": "怪獣ナー", "chromas": true}, {"id": "150002", "num": 2, "name": "ジェントルマン ナー", "chromas": false}, {"id": "150003", "num": 3, "name": "雪遊びナー", "chromas": false}, {"id": "150004", "num": 4, "name": "エル・レオン ナー", "chromas": false}, {"id": "150013", "num": 13, "name": "超銀河ナー", "chromas": false}, {"id": "150014", "num": 14, "name": "SSG ナー", "chromas": false}, {"id": "150015", "num": 15, "name": "宇宙飛行士ナー", "chromas": true}, {"id": "150022", "num": 22, "name": "古の賢樹ナー", "chromas": true}, {"id": "150031", "num": 31, "name": "ラ・イルシオン ナー", "chromas": true}], "lore": "ナーは原始時代のヨードルで、おどけて悪ふざけをしていたかと思えば、あっという間にそれが幼児の怒りとなって爆発し、巨大な破壊の野獣に変身する。真なる氷に何千年間も閉じ込められていた彼にとって、一変した世界は見たこともないような不思議でいっぱいだ。彼は自分の骨牙のブーメランであろうが近くにあった建物であろうが、手当たり次第に敵に向かって投げつけては危険な状況を楽しんでいる。", "blurb": "ナーは原始時代のヨードルで、おどけて悪ふざけをしていたかと思えば、あっという間にそれが幼児の怒りとなって爆発し、巨大な破壊の野獣に変身する。真なる氷に何千年間も閉じ込められていた彼にとって、一変した世界は見たこともないような不思議でいっぱいだ。彼は自分の骨牙のブーメランであろうが近くにあった建物であろうが、手当たり次第に敵に向かって投げつけては危険な状況を楽しんでいる。", "allytips": ["怒りのたまり具合をコントロールすることが極めて重要。変身のタイミングを調整して、両形態の強みを存分に生かそう。", "敵を壁際に誘い込み、アルティメットスキルでスタンさせよう。", "両形態の特徴をよく知っておこう。ミニナーは打たれ弱いが、ちょこまか動き回って相手をかく乱しダメージを与え続けることができる。メガナーは足が遅いがタフで、高威力の範囲ダメージが得意だ。"], "enemytips": ["ナーの巨大化が解けてから15秒間は、怒りがたまらなくなる。この隙に反撃しよう。", "ナーのアニメーションとリソースゲージは、変身が近づくと変化する。", "ナーのアルティメットスキルで壁に叩きつけられるとスタン状態になるため、壁際で戦う時は用心すること。 "], "tags": ["Fighter", "Tank"], "partype": "怒り", "info": {"attack": 6, "defense": 5, "magic": 5, "difficulty": 8}, "stats": {"hp": 540, "hpperlevel": 79, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 32, "armorperlevel": 3.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 175, "hpregen": 4.5, "hpregenperlevel": 1.25, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 3.2, "attackspeedperlevel": 6, "attackspeed": 0.625}, "spells": [{"id": "GnarQ", "name": "ブーメラン/ぽいっ", "description": "ナーが投げるブーメランは、命中した敵にダメージとスロウ効果を与える。戻ってきたブーメランをキャッチすると、クールダウンが短縮される。<br><br>メガナーは、ブーメランのかわりに岩石を投げる。敵に命中するとその場に落下して、付近の敵すべてにダメージとスロウ効果を与える。落ちた岩石を拾うとクールダウンが短縮される。", "tooltip": "<keywordMajor>ミニナー:</keywordMajor> ブーメランを投げて<physicalDamage>{{ spell.gnarq:minitotaldamage }}の物理ダメージ</physicalDamage>を与え、{{ spell.gnarq:slowduration }}秒間{{ spell.gnarq:slowamount*100 }}%の<status>スロウ効果</status>を付与する。ブーメランは敵に当たると戻ってくるが、その際に与えるダメージは減少する。それぞれの敵に1回しか当たらない。ブーメランをキャッチすると、このスキルのクールダウンが{{ spell.gnarq:minicdrefund*100 }}%短縮される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ブーメラン基本ダメージ", "岩石基本ダメージ", "スロウ効果", "岩石のスロウ効果", "クールダウン"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ megabasedamage }} -> {{ megabasedamageNL }}", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ megaslowamount*100.000000 }}% -> {{ megaslowamountnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [20, 17.5, 15, 12.5, 10], "cooldownBurn": "20/17.5/15/12.5/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [1100, 1100, 1100, 1100, 1100], "rangeBurn": "1100", "image": {"full": "GnarQ.png", "sprite": "spell4.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "GnarW", "name": "ごきげん/こてんぱん", "description": "ナーが通常攻撃とスキルで相手にマークをつけるようになる。マークがたまった敵を攻撃するとナーは興奮し、追加ダメージを与えて移動速度が増加する。<br><br>メガナーは興奮を通りこし、怒りが爆発する。片腕を目の前におもいきり振り下ろし、範囲内の敵ユニットにダメージを与えてスタン効果を付与する。", "tooltip": "<keywordMajor>ミニナー自動効果:</keywordMajor> 同じ敵に通常攻撃かスキルが3回命中するたび、<magicDamage>{{ spell.gnarw:minitotaldamage }}(+最大体力の{{ spell.gnarw:minipercenthpdamage*100 }}%)の魔法ダメージ</magicDamage>を追加で与え、<speed>移動速度が{{ spell.gnarr:rhypermovementspeedpercent }}%</speed>増加する。この移動速度は{{ minihasteduration }}秒かけて元に戻る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ごきげん」の追加ダメージ", "「ごきげん」の最大体力に対するダメージ率", "「こてんぱん」の基本ダメージ"], "effect": ["{{ minibasedamage }} -> {{ minibasedamageNL }}", "{{ minipercenthpdamage*100.000000 }}% -> {{ minipercenthpdamagenl*100.000000 }}%", "{{ megabasedamage }} -> {{ megabasedamageNL }}"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "GnarW.png", "sprite": "spell4.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "GnarE", "name": "ぴょんぴょん/ドーン！", "description": "ナーがジャンプする。ユニットの上に着地すると、頭の上をはねてさらに遠くまでジャンプする。<br><br>メガナーは体が大きすぎて弾まない。そのかわり、着地するときに体をたたきつけて衝撃波をうみだし、周囲の敵にダメージを与える。着地地点にいる敵にはスロウ効果を与える。", "tooltip": "<keywordMajor>ミニナー:</keywordMajor> 跳躍して{{ spell.gnare:miniasduration }}秒間<attackSpeed>攻撃速度が{{ spell.gnare:minibas*100 }}%</attackSpeed>増加する。ユニットの上に着地した場合は、もう一度跳ねてさらに遠くに移動する。敵の上で跳ねると<physicalDamage>{{ spell.gnare:minitotaldamage }}の物理ダメージ</physicalDamage>を与え、短時間{{ spell.gnare:movespeedmod*-100 }}%の<status>スロウ効果</status>を付与する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["「ぴょんぴょん」ダメージ", "「ドーン！」ダメージ", "増加攻撃速度", "クールダウン"], "effect": ["{{ minidamage }} -> {{ minidamageNL }}", "{{ megadamage }} -> {{ megadamageNL }}", "{{ minibas*100.000000 }}% -> {{ minibasnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [22, 19.5, 17, 14.5, 12], "cooldownBurn": "22/19.5/17/14.5/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "GnarE.png", "sprite": "spell4.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "GnarR", "name": "ナー！", "description": "メガナーが周りのモノを根こそぎ指定方向へ投げ、命中した敵にダメージとスロウ効果を与える。投げられた敵が壁にぶつかるとスタン状態になり、追加ダメージを受ける。", "tooltip": "<keywordMajor>ミニナー自動効果:</keywordMajor> <spellName>「ごきげん」</spellName>から得る<speed>移動速度</speed>が増加する。<br /><br /><keywordMajor>メガナー: </keywordMajor>周囲の敵を放り投げ、<physicalDamage>{{ damage }}の物理ダメージ</physicalDamage>を与えて<status>ノックバック</status>させ、{{ rccduration }}秒間{{ rslowpercent }}%の<status>スロウ効果</status>を付与する。壁に衝突した敵には代わりに<physicalDamage>{{ walldamage }}の物理ダメージ</physicalDamage>を与え、<status>スタン</status>させる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "スロウ/スタン効果時間", "「ごきげん」から得る移動速度", "クールダウン"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ rccduration }} -> {{ rccdurationNL }}", "{{ rhypermovementspeedpercent }}% -> {{ rhypermovementspeedpercentNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [90, 60, 30], "cooldownBurn": "90/60/30", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [590, 590, 590], "rangeBurn": "590", "image": {"full": "GnarR.png", "sprite": "spell4.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "ぷんすこ", "description": "ナーは戦闘中に「怒り」が溜まっていく。「怒り」が最大の状態でスキルを使用するとメガナーに変身し、ステータスとスキルが変化する。", "image": {"full": "Gnar_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}