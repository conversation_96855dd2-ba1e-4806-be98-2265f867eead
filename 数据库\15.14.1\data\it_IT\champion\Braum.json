{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Braum": {"id": "<PERSON><PERSON><PERSON>", "key": "201", "name": "<PERSON><PERSON><PERSON>", "title": "il cuore del Freljord", "image": {"full": "Braum.png", "sprite": "champion0.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "201000", "num": 0, "name": "default", "chromas": false}, {"id": "201001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "201002", "num": 2, "name": "Braum El Tigre", "chromas": false}, {"id": "201003", "num": 3, "name": "Braum Cuordileone", "chromas": false}, {"id": "201010", "num": 10, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "201011", "num": 11, "name": "Braum Città del crimine", "chromas": true}, {"id": "201024", "num": 24, "name": "Braum Corsa al dolcetto", "chromas": true}, {"id": "201033", "num": 33, "name": "Braum Festa in Piscina", "chromas": true}, {"id": "201042", "num": 42, "name": "Braum Mastro fuochista", "chromas": true}], "lore": "Dotato di enormi bicipiti e di un cuore ancora più grande, Braum è l'eroe più amato del Freljord. In ogni locanda a nord di Frostheld si brinda alla sua forza leggendaria, che stando ai racconti ha abbattuto un'intera foresta di querce in una sola notte e ha sbriciolato una montagna con un pugno. Con una porta incantata come scudo, Braum si aggira per il gelido nord con dei baffi impeccabili e un sorriso grande quanto i suoi muscoli. Un vero amico dei bisognosi.", "blurb": "Dotato di enormi bicipiti e di un cuore ancora più grande, Braum è l'eroe più amato del Freljord. In ogni locanda a nord di Frostheld si brinda alla sua forza leggendaria, che stando ai racconti ha abbattuto un'intera foresta di querce in una sola notte...", "allytips": ["Fai squadra con i tuoi alleati per caricare Colpi stordenti, incoraggiandoli a sferrare attacchi base sui nemici marchiati.", "Balza davanti agli amici meno resistenti e proteggili dai proiettili con Indistruttibile.", "Frattura glaciale lascia una potente zona di rallentamento, posizionala bene per dividere gli scontri tra squadre e rallentare l'avanzata del nemico."], "enemytips": ["Per usare Colpi stordenti, Braum deve mettere a segno Morsi del freddo o un attacco base. Se vieni marchiato, allontanati dalla sua gittata prima di essere colpito altre 3 volte per evitare lo stordimento.", "La suprema di Braum ha un lungo tempo di lancio, approfittane per schivarla. Camminare sul terreno ghiacciato ti rallenterà, posizionati in modo da non doverlo attraversare.", "Indistruttibile fornisce a Braum una difesa forte, aspetta che termini o cerca di guadagnare una posizione vantaggiosa."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 3, "defense": 9, "magic": 4, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 112, "mp": 311, "mpperlevel": 45, "movespeed": 335, "armor": 42, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 1, "mpregen": 7, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.2, "attackspeedperlevel": 3.5, "attackspeed": 0.644}, "spells": [{"id": "BraumQ", "name": "Morsi del freddo", "description": "Braum lancia del ghiaccio con lo scudo, rallentando e infliggendo danni magici.<br><br>Applica una carica di <font color='#FFF673'>Colpi stordenti</font>.", "tooltip": "Braum lancia del ghiaccio con lo scudo, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> al primo bersaglio colpito e <status>rallentandolo</status> del {{ e2 }}%. Il rallentamento decade nell'arco di {{ e5 }} secondi.<br /><br />Applica una carica di <keywordMajor>Colpi stordenti</keywordMajor>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [45, 50, 55, 60, 65], "costBurn": "45/50/55/60/65", "datavalues": {}, "effect": [null, [75, 125, 175, 225, 275], [70, 70, 70, 70, 70], [30, 30, 30, 30, 30], [0.02, 0.02, 0.02, 0.02, 0.02], [2, 2, 2, 2, 2], [1050, 1050, 1050, 1050, 1050], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "75/125/175/225/275", "70", "30", "0.02", "2", "1050", "4", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "BraumQ.png", "sprite": "spell2.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumW", "name": "<PERSON><PERSON> di me", "description": "Braum salta verso un campione o un minion alleato. <PERSON>uando arriva, lui e l'alleato ottengono armatura e resistenza magica per qualche secondo.", "tooltip": "Braum salta verso un campione o un minion alleato. Quando arriva, conferisce al bersaglio <scaleArmor>{{ grantedallyarmor }} armatura</scaleArmor> e <scaleMR>{{ grantedallymr }} resistenza magica</scaleMR> per {{ e1 }} secondi. Braum ottiene <scaleArmor>{{ grantedbraumarmor }} armatura</scaleArmor> e <scaleMR>{{ grantedbraummr }} resistenza magica</scaleMR> per la stessa durata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Resistenze base", "Ricarica"], "effect": ["{{ baseresists }} -> {{ baseresistsNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [3, 3, 3, 3, 3], [750, 750, 750, 750, 750], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "3", "750", "0", "0", "0", "40", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "BraumW.png", "sprite": "spell2.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumE", "name": "Indistruttibile", "description": "Braum alza il suo scudo in una direzione per alcuni secondi, intercettando e distruggendo tutti i proiettili che lo colpiscono. Nega tutti i danni del primo attacco e riduce quelli degli attacchi successivi provenienti da questa direzione.", "tooltip": "Braum solleva lo scudo per {{ e2 }} secondi, intercettando i proiettili nemici provenienti dalla direzione scelta, che lo colpiscono e vengono distrutti. Il primo proiettile bloccato da Braum non infligge danni e quelli successivi infliggono il {{ e3 }}% di danni in meno.<br /><br />Braum ottiene <speed>{{ e4 }}% velocità di movimento</speed> mentre lo scudo è sollevato.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Riduzione danni", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e3 }}% -> {{ e3NL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [3, 3.25, 3.5, 3.75, 4], [35, 40, 45, 50, 55], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "3/3.25/3.5/3.75/4", "35/40/45/50/55", "10", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "BraumE.png", "sprite": "spell2.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "BraumRWrapper", "name": "Frattura glaciale", "description": "Braum colpisce il terreno, lanciando in aria i nemici nelle vicinanze e in linea retta davanti a lui. Lungo la linea rimane una frattura che rallenta i nemici.", "tooltip": "Braum colpisce il terreno, creando una spaccatura che <status>lancia in aria</status> i nemici vicini sulla sua traiettoria, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage>. Il primo bersaglio colpito viene <status>lanciato in aria</status> per una durata tra {{ minknockup }} e {{ maxknockup }} secondo/i, che aumenta in base alla distanza da Braum. Tutti gli altri bersagli colpiti vengono <status>lanciati in aria</status> per {{ minknockup }} secondi.<br /><br />La spaccatura crea anche un'area per {{ slowzoneduration }} secondi, che <status>rallenta</status> del {{ movespeedmod }}%.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> lancio", "Rallentamento", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ firstknockupduration }} -> {{ firstknockupdurationNL }}", "{{ movespeedmod }}% -> {{ movespeedmodNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "BraumRWrapper.png", "sprite": "spell2.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Colpi s<PERSON>i", "description": "Gli attacchi base di Braum applicano Colpi stordenti. Dopo l'applicazione della prima carica, anche gli attacchi base <font color='#FFF673'>alleati</font> accumulano Colpi stordenti. <br><br>Al raggiungimento di 4 cariche, il bersaglio viene stordito e subisce danni magici. Per i prossimi secondi, non può ricevere altre cariche, ma subisce danni magici bonus dagli attacchi di Braum.", "image": {"full": "Braum_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}