{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Kalista": {"id": "Kalista", "key": "429", "name": "Kalista", "title": "la lancia della vendetta", "image": {"full": "Kalista.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "429000", "num": 0, "name": "default", "chromas": false}, {"id": "429001", "num": 1, "name": "Kalista Luna di Sangue", "chromas": false}, {"id": "429002", "num": 2, "name": "Kalista Mondiali 2015", "chromas": true}, {"id": "429003", "num": 3, "name": "Kalista SKT T1", "chromas": false}, {"id": "429005", "num": 5, "name": "Kalista Predona", "chromas": true}, {"id": "429014", "num": 14, "name": "Kalista Corte Fatata", "chromas": true}, {"id": "429024", "num": 24, "name": "Kalista Portatrice dell'Alba", "chromas": true}], "lore": "<PERSON><PERSON>tro dell'ira e della vendetta, Kalista è uno spirito immortale, un incubo corazzato venuto dalle Isole Ombra per dare la caccia ai traditori e ai disonesti. Chi viene tradito può gridare vendetta, ma Kalista risponde solo a chi è pronto a pagare con la sua anima. Chi finisce nel mirino dell'ira di Kalista deve accettare la sua fine, perché ogni patto siglato con la triste cacciatrice finisce sempre con il gelido fuoco perforante delle sue lance.", "blurb": "<PERSON><PERSON>tro dell'ira e della vendetta, Kalista è uno spirito immortale, un incubo corazzato venuto dalle Isole Ombra per dare la caccia ai traditori e ai disonesti. Chi viene tradito può gridare vendetta, ma Kalista risponde solo a chi è pronto a pagare con...", "allytips": ["Lacerazione è un ottimo modo per dare i colpi di grazia, visto che la ricarica si azzera all'uccisione di un personaggio. ", "Immettere un comando di movimento una volta per attivare Postura marziale non azzera il bersaglio dell'attacco base di Kalista.", "Per la sua passiva, la velocità di movimento di Kalista viene aumentata dalla velocità d'attacco."], "enemytips": ["La mobilità di Kalista dipende dagli attacchi. Questo significa che è bassa quando è fuori dalla sua portata di attacco e che i rallentamenti riducono la distanza che può coprire in uno scontro.", "Kalista non può annullare l'animazione del suo attacco base. È molto mobile, ma questa particolarità crea una finestra per mettere a segno le abilità, se si prevede quando inizierà ad attaccare.", "Se rompi la linea visiva con Kali<PERSON>, anche con l'erba alta, i suoi attacchi base ti mancheranno, schia<PERSON><PERSON><PERSON> al suolo."], "tags": ["Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 2, "magic": 4, "difficulty": 7}, "stats": {"hp": 560, "hpperlevel": 114, "mp": 300, "mpperlevel": 45, "movespeed": 330, "armor": 24, "armorperlevel": 5.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 6.3, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 4, "attackspeedperlevel": 4.5, "attackspeed": 0.694}, "spells": [{"id": "KalistaMysticShot", "name": "Perforazione", "description": "Lancia una rapida lancia che passa oltre i nemici che uccide.", "tooltip": "Kalista scaglia una lancia, infliggendo <physicalDamage>{{ totaldamage }} danni fisici</physicalDamage> al primo bersaglio colpito. Se il nemico muore, la lancia prosegue, trasferendo le cariche di <spellName>Lacerazione</spellName> al bersaglio colpito successivo.<br /><br /><PERSON><PERSON> aver usato questa abilità, Kalista può scattare con <spellName>Postura marziale</spellName>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON>"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [9, 9, 9, 9, 9], "cooldownBurn": "9", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "KalistaMysticShot.png", "sprite": "spell6.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KalistaW", "name": "Sentinella", "description": "Ottiene danni bonus quando <PERSON> e chi ha stretto il Patto con lei colpiscono lo stesso bersaglio. <br><br>Attiva per inviare un'anima a pattugliare, rivelando l'area davanti al suo campo visivo.", "tooltip": "<spellPassive>Passiva:</spellPassive> quando <PERSON> e il suo <keywordMajor>Compagno di patto</keywordMajor> attaccano lo stesso bersaglio, infligge il <magicDamage>{{ maxhealthdamage*100 }}% della salute massima in danni magici</magicDamage>. <PERSON><PERSON> effetto ha una ricarica di {{ pertargetcooldown }} secondi per bersaglio e un limite di {{ maximummonsterdamage }} danni contro i non campioni.<br /><br /><spellPassive>Attiva:</spellPassive> Kalista invia un fantasma a pattugliare una zona per tre giri. I campioni visti dal fantasma vengono rivelati per 4 secondi. Questa abilità ha 2 cariche ({{ ammorechargetooltip }} secondi di ricarica).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> salute massima", "Ricarica munizioni", "Limite danni ai mostri"], "effect": ["{{ maxhealthdamage*100.000000 }}% -> {{ maxhealthdamagenl*100.000000 }}%", "{{ ammorechargetooltip }} -> {{ ammorechargetooltipNL }}", "{{ maximummonsterdamage }} -> {{ maximummonsterdamageNL }}"]}, "maxrank": 5, "cooldown": [30, 30, 30, 30, 30], "cooldownBurn": "30", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 80, 80, 80, 80], [0, 0, 0, 0, 0], [125, 150, 175, 200, 225], [90, 80, 70, 60, 50], [0, 0, 0, 0, 0], [75, 100, 125, 150, 175], [10, 10, 10, 10, 10], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "80", "0", "125/150/175/200/225", "90/80/70/60/50", "0", "75/100/125/150/175", "10", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON> costo", "maxammo": "2", "range": [5000, 5000, 5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "KalistaW.png", "sprite": "spell6.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON> costo"}, {"id": "KalistaExpungeWrapper", "name": "Lacerazione", "description": "<PERSON><PERSON> attacchi impalano i bersagli con le lance. Attiva per strappare le lance, rallentando e infliggendo danni crescenti.", "tooltip": "<spellPassive>Passiva: </spellPassive>le lance di Kalista restano infilzate nei bersagli per 4 secondi, accumulandosi per un numero illimitato di volte.<br /><br /><spellActive>Attiva:</spellActive> Kalista strappa le lance dai bersagli vicini, infliggendo <physicalDamage>{{ normaldamage }}</physicalDamage> più <physicalDamage>{{ additionaldamage }} danni fisici</physicalDamage> per ogni lancia dopo la prima. <status>Rallenta</status> i nemici colpiti di <attention>{{ totalslowamount }}</attention> per {{ slowduration }} secondi.<br /><br />Se questa abilità uccide almeno un bersaglio, la sua ricarica si azzera e rimborsa <scaleMana>{{ manarefund }} mana</scaleMana>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "<PERSON><PERSON> accumulati", "Rapporto attacco fisico per carica", "Rallentamento", "<PERSON><PERSON><PERSON><PERSON> mana", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ additionalbasedamage }} -> {{ additionalbasedamageNL }}", "{{ additionaladratio*100.000000 }}% -> {{ additionaladrationl*100.000000 }}%", "{{ slowamount*100.000000 }}% -> {{ slowamountnl*100.000000 }}%", "{{ manarefund }} -> {{ manarefundNL }}", "{{ fakedcooldown }} -> {{ fakedcooldownNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "2", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaExpungeWrapper.png", "sprite": "spell6.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "KalistaRx", "name": "Chiamata del fato", "description": "Kalista teletrasporta a sé il suo compagno di Patto, che ottiene l'abilità di scattare verso una posizione, respingendo i campioni nemici.", "tooltip": "<PERSON><PERSON> mette in Stasi il suo <keywordMajor>Compagno di patto</keywordMajor> e lo attira a sé per un massimo di 4 secondi. Il <keywordMajor>Compagno di patto</keywordMajor> può cliccare sul terreno per lanciarsi verso una posizione, fermandosi contro il primo campione colpito e <status>respingendo</status> tutti i nemici vicini. Se il <keywordMajor>Compagno di patto</keywordMajor> colpisce un campione, viene posizionato alla massima gittata di attacco dal bersaglio.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON> lancio"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [160, 140, 120], "cooldownBurn": "160/140/120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [1, 1.5, 2], [40, 60, 80], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "1/1.5/2", "40/60/80", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "KalistaRx.png", "sprite": "spell6.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> marziale", "description": "Se Kalista immette un comando di movimento mentre prepara il suo attacco base o Perforazione, si muoverà di una breve distanza al lancio dell'attacco.", "image": {"full": "Kalista_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}