{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Illaoi": {"id": "<PERSON><PERSON><PERSON>", "key": "420", "name": "<PERSON><PERSON><PERSON>", "title": "the Kraken Priestess", "image": {"full": "Illaoi.png", "sprite": "champion1.png", "group": "champion", "x": 288, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "420000", "num": 0, "name": "default", "chromas": false}, {"id": "420001", "num": 1, "name": "Void Bringer <PERSON>", "chromas": false}, {"id": "420002", "num": 2, "name": "<PERSON>", "chromas": true}, {"id": "420010", "num": 10, "name": "Cosmic Invoker <PERSON><PERSON>", "chromas": true}, {"id": "420018", "num": 18, "name": "<PERSON>", "chromas": true}, {"id": "420027", "num": 27, "name": "Battle Bear Illaoi", "chromas": true}], "lore": "Kekuatan fisik Illaoi hanya kalah oleh keyakinannya yang teguh. Sebagai nabi dari Great Kraken, dia menggunakan patung emas besar untuk merenggut roh musuh dari tubuh mereka dan mematahkan persepsi mereka tentang realitas. <PERSON><PERSON><PERSON> yang menantang “Truth Bearer of Nagakabouros” akan segera menyadari bahwa dewa Serpent Isles pun bertempur di pihak Illaoi.", "blurb": "Kekuatan fisik Illaoi hanya kalah oleh keyakinannya yang teguh. Sebagai nabi dari Great Kraken, dia menggunakan patung emas besar untuk merenggut roh musuh dari tubuh mereka dan mematahkan persepsi mereka tentang realitas. Se<PERSON><PERSON> yang menantang “Truth...", "allytips": ["Tentacle adalah sumber kekuatan yang besar. <PERSON><PERSON> bertarung tanpanya.", "Spirit mewarisi health target saat ini. <PERSON><PERSON> tuju<PERSON><PERSON> membuat ", "<PERSON><PERSON><PERSON>", ", coba kurangi health lawan dulu agar lebih mudah membunuh spirit-nya.", "Leap of Faith paling bagus digunakan untuk melanjutkan serangan yang kuat atau saat diserang. Hati-hati jadi yang pertama masuk pertarungan."], "enemytips": ["Bunuh Tentacle sebanyak mungkin untuk memudahkan pertarungan melawan Illaoi.", "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON>, law<PERSON><PERSON> jika memung<PERSON>. Memberikan damage pada <PERSON> akan mengurangi durasi arwah.", "<PERSON><PERSON> be<PERSON> saat <PERSON> of Faith untuk mengurangi jumlah Tentacle yang tersedia bagi Illaoi."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 6, "magic": 3, "difficulty": 4}, "stats": {"hp": 656, "hpperlevel": 115, "mp": 350, "mpperlevel": 50, "movespeed": 350, "armor": 35, "armorperlevel": 5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 9.5, "hpregenperlevel": 0.8, "mpregen": 7.5, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 65, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "IllaoiQ", "name": "Tentacle Smash", "description": "Meningkatkan damage yang dihasilkan oleh Tentacle. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> mengempaskan Tentacle yang mengh<PERSON>lkan physical damage.", "tooltip": "<passive>Pasif:</passive> Damage dari <keywordMajor>Slam</keywordMajor> meningkat sebesar <physicalDamage>{{ tentacledamageamp*100 }}%</physicalDamage> (saat ini <physicalDamage>{{ f1 }} physical damage</physicalDamage>).<br /><br /><active>Aktif:</active> Illaoi mengayunkan patungnya, menyebabkan Tentacle melakukan <keywordMajor>Slam</keywordMajor> ke depan.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bonus Slam", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ tentacledamageamp*100.000000 }}%-> {{ tentacledamageampnl*100.000000 }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [10, 10, 10, 10, 10], [200, 200, 200, 200, 200], [800, 800, 800, 800, 800], [10, 8, 6, 4, 2], [5, 5, 5, 5, 5], [0.1, 0.15, 0.2, 0.25, 0.3], [1.2, 1.2, 1.2, 1.2, 1.2], [-0.3, -0.35, -0.4, -0.45, -0.5], [1.5, 1.5, 1.5, 1.5, 1.5], [0, 0, 0, 0, 0]], "effectBurn": [null, "10", "200", "800", "10/8/6/4/2", "5", "0.1/0.15/0.2/0.25/0.3", "1.2", "-0.3/-0.35/-0.4/-0.45/-0.5", "1.5", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "IllaoiQ.png", "sprite": "spell5.png", "group": "spell", "x": 96, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IllaoiW", "name": "<PERSON><PERSON><PERSON>", "description": "Illaoi melompat ke arah target, <PERSON><PERSON><PERSON><PERSON><PERSON> physical damage dan menyebabkan Tentacle di sekitar ikut menyerang target.", "tooltip": "Serangan Il<PERSON>oi berikutnya membuat dia melompat ke arah target, <PERSON><PERSON><PERSON><PERSON><PERSON> tambahan <physicalDamage>{{ healthpercenttotaltooltip }} physical damage Health maksimum</physicalDamage>. Saat dia menyerang, Tentacle di sekitar akan <keywordMajor>Slam</keywordMajor> ke arah target.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Damage Minimum"], "effect": ["{{ effect1amount*100.000000 }}%-> {{ effect1amountnl*100.000000 }}%", "{{ wmindamage }}-> {{ wmindamageNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0.03, 0.035, 0.04, 0.045, 0.05], [2, 2, 2, 2, 2], [300, 300, 300, 300, 300], [6, 6, 6, 6, 6], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0.03/0.035/0.04/0.045/0.05", "2", "300", "6", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "IllaoiW.png", "sprite": "spell5.png", "group": "spell", "x": 144, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IllaoiE", "name": "Test of Spirit", "description": "Illaoi merenggut spirit dari tubuh musuh, mema<PERSON><PERSON><PERSON> untuk berdiri di hadapannya. Spirit meneruskan persentase damage yang diterima ke target aslinya. <PERSON><PERSON> terbunuh, atau jika target berada terlalu jauh dari spirit-nya, target akan menjadi <font color='#669900'>Vessel</font> dan mulai memunculkan Tentacle.", "tooltip": "Illaoi merenggut spirit dari champion musuh selama {{ spiritduration }} detik. Spirit bisa terkena damage seperti champion, dan {{ echopercenttooltiponly }} dari damage tersebut akan diteruskan ke pemiliknya.<br /><br />Jika spirit tersebut mati atau target keluar dari j<PERSON>, target menjadi ditandai selama {{ vesselduration }} detik dan terkena <status>Slow</status> sebesar {{ slowamount*100 }}% selama {{ slowduration }} detik. Musuh yang ditandai akan memunculkan Tentacle setiap <scaleLevel>{{ f1 }}</scaleLevel> detik.<br /><br />Tentacle akan otomatis melakukan <keywordMajor>Slam</keywordMajor> pada spirit dan musuh yang ditandai setiap {{ timebetweenvesseltentacleslams }} detik sekali.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Echo", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }}%-> {{ e1NL }}%", "{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15, 14, 13, 12], "cooldownBurn": "16/15/14/13/12", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [25, 30, 35, 40, 45], [10, 10, 10, 10, 10], [7, 7, 7, 7, 7], [1500, 1500, 1500, 1500, 1500], [1.5, 1.5, 1.5, 1.5, 1.5], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [80, 80, 80, 80, 80], [8, 8, 8, 8, 8], [4, 3, 0, 0, 0]], "effectBurn": [null, "25/30/35/40/45", "10", "7", "1500", "1.5", "3", "0", "80", "8", "4/3/0/0/0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "IllaoiE.png", "sprite": "spell5.png", "group": "spell", "x": 192, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "IllaoiR", "name": "<PERSON><PERSON> of Faith", "description": "<PERSON><PERSON><PERSON> mengempaskan patungnya ke tanah, <PERSON><PERSON><PERSON><PERSON><PERSON> physical damage pada musuh di sekitar. <PERSON><PERSON> akan muncul untuk setiap champion musuh yang terkena.", "tooltip": "<PERSON><PERSON><PERSON> mengempaskan patungnya ke tanah, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ damagecalc }} physical damage</physicalDamage> ke musuh di sekitar dan memunculkan satu Tentacle untuk setiap champion musuh yang terkena.<br /><br /><PERSON><PERSON><PERSON> {{ duration }} detik berik<PERSON>, Tentacle menjadi tidak bisa ditarget dan melakukan <keywordMajor>Slam</keywordMajor> 50% lebih cepat, dan <spellName>Harsh Lesson</spellName> memiliki cooldown 2 detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ basedamage }}-> {{ basedamageNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 95, 70], "cooldownBurn": "120/95/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [450, 450, 450], "rangeBurn": "450", "image": {"full": "IllaoiR.png", "sprite": "spell5.png", "group": "spell", "x": 240, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Prophet of an Elder God", "description": "<PERSON><PERSON><PERSON> dan <font color='#669900'>Vessel</font> yang dibuatnya memunculkan Tentacle pada medan yang tak bisa dilewati di dekatnya. Tentacle menghantam spirit, <font color='#669900'>Vessel</font>, dan korban <PERSON><PERSON>h <PERSON>laoi. Tentacle menghasilkan physical damage pada musuh yang terkena, dan akan heal <PERSON><PERSON><PERSON> jika menghasilkan damage pada champion.", "image": {"full": "Illaoi_P.png", "sprite": "passive1.png", "group": "passive", "x": 288, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}