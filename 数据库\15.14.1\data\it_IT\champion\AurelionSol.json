{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"AurelionSol": {"id": "AurelionSol", "key": "136", "name": "Aurelion Sol", "title": "il forgiatore di stelle", "image": {"full": "AurelionSol.png", "sprite": "champion0.png", "group": "champion", "x": 48, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "136000", "num": 0, "name": "default", "chromas": false}, {"id": "136001", "num": 1, "name": "Aurelion Sol Signore C<PERSON>eo", "chromas": false}, {"id": "136002", "num": 2, "name": "Mecha Aurelion Sol", "chromas": true}, {"id": "136011", "num": 11, "name": "Aurelion Sol Drago della Tempesta", "chromas": false}, {"id": "136021", "num": 21, "name": "Aurelion Sol Nerinchiostro", "chromas": false}, {"id": "136031", "num": 31, "name": "Aurelion Sol Protettore di Porcellana", "chromas": false}], "lore": "Un tempo Aurelion Sol omaggiò il grande vuoto del cosmo con meraviglie celestiali da lui stesso ideate. Ora, è costretto ad esercitare il suo straordinario potere su richiesta di un impero dello spazio che lo ha indotto alla servitù. Con un forte desiderio di tornare a plasmare le stelle, Aurelion Sol strapperà via le tantissime stelle al cielo se sarà costretto, pur di ottenere la sua libertà.", "blurb": "Un tempo Aurelion Sol omaggiò il grande vuoto del cosmo con meraviglie celestiali da lui stesso ideate. Ora, è costretto ad esercitare il suo straordinario potere su richiesta di un impero dello spazio che lo ha indotto alla servitù. Con un forte...", "allytips": [], "enemytips": [], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 8, "difficulty": 7}, "stats": {"hp": 600, "hpperlevel": 90, "mp": 530, "mpperlevel": 40, "movespeed": 335, "armor": 22, "armorperlevel": 4, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 8, "mpregenperlevel": 0.75, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3.2, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "AurelionSolQ", "name": "So<PERSON>io di luce", "description": "Aurelion Sol canalizza per qualche secondo il suo soffio draconico, infliggendo danni al primo nemico colpito e danni ad area ridotti ai nemici vicini. Infligge danni bonus per ogni secondo in cui il soffio è canalizzato direttamente su un nemico, che aumentano in base alla quantità di Polvere di stelle raccolta. Se il bersaglio è un campione, questa abilità raccoglie Polvere di stelle.", "tooltip": "Aurelion Sol soffia fuoco stellare fino a un massimo di {{ maxchannelduration }} secondi, infliggendo <magicDamage>{{ damagepersecond }} danni magici</magicDamage> al secondo al primo nemico colpito e un {{ aoemodifier*100 }}% dei danni ai nemici circostanti.<br /><br />Ogni secondo di soffio sullo stesso nemico infligge una raffica di <magicDamage>{{ burstdamage }} danni magici</magicDamage> più <magicDamage>{{ burstbonustruedamagetochamps }} danni magici in base alla salute massima</magicDamage> del bersaglio e assorbe <span class=\"color3458eb\">{{ qmassstolen }} Polvere di stelle</span> se sono campioni.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Costo in @AbilityResourceName@", "<PERSON><PERSON> al secondo", "Danni a raffica", "Durata massima di canalizzazione"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ rankdamagepersecond }} -> {{ rankdamagepersecondNL }}", "{{ rankburstdamage }} -> {{ rankburstdamageNL }}", "{{ maxchannelduration }} -> {{ maxchanneldurationNL }}"]}, "maxrank": 5, "cooldown": [3, 3, 3, 3, 3], "cooldownBurn": "3", "cost": [35, 40, 45, 50, 55], "costBurn": "35/40/45/50/55", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " mana al secondo", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolQ.png", "sprite": "spell1.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ manacostpersecond }} mana al secondo"}, {"id": "AurelionSolW", "name": "Volo astrale", "description": "Aurelion Sol vola oltre gli ostacoli nella direzione bersaglio. Mentre è in questo stato, può lanciare altre abilità. Durante il volo, Soffio di luce non ha più un tempo di ricarica o una durata massima di canalizzazione e infligge danni aumentati.<br><br>Il tempo di ricarica rimanente di Volo astrale viene ridotto quando un campione nemico muore dopo essere stato danneggiato di recente da Aurelion Sol.<br><br>La Polvere di stelle aumenta la gittata massima di Volo astrale.", "tooltip": "Aurelion Sol vola in una direzione. Mentre vola, <spellName>Soffio di luce</spellName> non ha ricarica, nessuna durata massima di canalizzazione e infligge danni fissi aumentati di un {{ truedamagebonus*100 }}%.<br /><br />Le eliminazioni di campioni entro {{ resetwindow }} secondi dal danneggiamento rimborsano un {{ tooltiptakedowncooldownmultiplier }}% della ricarica di questa abilità.<br /><br /><recast>Rilancio:</recast> interrompe il volo in anticipo.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Percentuale danni magici bonus", "Costo in @AbilityResourceName@", "Ricarica"], "effect": ["{{ truedamagebonus*100.000000 }}% -> {{ truedamagebonusnl*100.000000 }}%", "{{ cost }} -> {{ costNL }}", "{{ cd }} -> {{ cdNL }}"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1500, 1500, 1500, 1500, 1500], "rangeBurn": "1500", "image": {"full": "AurelionSolW.png", "sprite": "spell1.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolE", "name": "Singolarità", "description": "Aurelion Sol evoca un buco nero, infliggendo danni ai nemici e attirandoli lentamente verso il centro. Questa abilità conferisce Polvere di stelle ogni volta che un nemico muore nel buco nero e per ogni secondo trascorso al suo interno da un campione nemico. Il centro del buco nero giustizia i nemici sotto una certa percentuale della loro salute massima. La Polvere di stelle aumenta l'area della Singolarità e la soglia di esecuzione.", "tooltip": "Aurelion Sol evoca un buco nero che infligge <magicDamage>{{ damagepersecond }} danni magici</magicDamage> al secondo e <status>trascina</status> i nemici verso il centro per {{ duration }} secondi. I nemici al centro con meno di un <scaleHealth>{{ currentexecutionthreshold }}% della salute massima</scaleHealth> muoiono all'istante.<br /><br />Il buco nero assorbe <span class=\"color3458eb\">Polvere di stelle</span> quando i nemici muoiono al suo interno e ogni secondo in cui un campione nemico è dentro di esso.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> al secondo"], "effect": ["{{ basedamagepersecond }} -> {{ basedamagepersecondNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [90, 90, 90, 90, 90], "costBurn": "90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "AurelionSolE.png", "sprite": "spell1.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "AurelionSolR", "name": "<PERSON> cadente/I cieli discendono", "description": "Stella cadente: Aurelion Sol schianta una stella al suolo. L'impatto infligge danni magici e stordisce i nemici, conferendo anche Polvere di stelle per ogni campione nemico colpito. Guadagnando abbastanza Polvere di stelle, la prossima Stella cadente di Aurelion Sol si trasforma in I cieli discendono.<br><br>I cieli discendono: Aurelion Sol trascina una gigantesca stella giù dal cielo, aumentando in modo significativo le dimensioni della zona d'impatto e i danni inflitti e lanciando in aria i nemici invece di stordirli. Un'enorme onda d'urto si propaga dai bordi della zona d'impatto, infliggendo danni e rallentando i campioni colpiti. La Polvere di stelle aumenta la zona d'impatto di Stella cadente e di I cieli discendono.", "tooltip": "Aurelion Sol sceglie una stella nel cielo e la schianta a terra, infliggendo <magicDamage>{{ maxdamagetooltip }} danni magici</magicDamage>, <status>stordendo</status> i nemici per {{ stunduration }} secondo/i e assorbendo <span class=\"color3458eb\">{{ massstolen }} Polvere di stelle</span> per ogni campione colpito.<br /><br />La raccolta di <span class=\"color3458eb\">{{ calamitystacks }} Polvere di stelle</span> trasforma la prossima <spellName>Stella cadente</spellName> in <spellName>I cieli discendono</spellName>.<br /><br /><spellName>I cieli discendono</spellName>: Aurelion Sol trascina giù dal cosmo un'intera costellazione, infliggendo <magicDamage>{{ r2damage }} danni magici</magicDamage> in un'area più ampia, <status>scagliando in aria</status> i nemici colpiti per {{ stunduration }} secondo/i e scatenando un'enorme onda d'urto che infligge <magicDamage>{{ shockwavedamage }} danni magici</magicDamage> ai campioni e ai mostri epici e <status>rallenta</status> di un {{ shockwaveslow*100 }}% per 1 secondo tutti i nemici colpiti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>'<PERSON>rto"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*1.250000 }} -> {{ basedamagenl*1.250000 }}", "{{ basedamage*0.900000 }} -> {{ basedamagenl*0.900000 }}"]}, "maxrank": 3, "cooldown": [120, 110, 100], "cooldownBurn": "120/110/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1250, 1250, 1250], "rangeBurn": "1250", "image": {"full": "AurelionSolR.png", "sprite": "spell1.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON>o", "description": "Le abilità con danni di Aurelion Sol riducono i nemici in cariche di <font color='#3458eb'>Polvere di stelle</font>, che migliora permanentemente ciascuna delle sue abilità. ", "image": {"full": "AurelionSolP.png", "sprite": "passive0.png", "group": "passive", "x": 48, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}