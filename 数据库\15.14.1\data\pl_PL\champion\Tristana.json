{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Tristana": {"id": "<PERSON><PERSON>", "key": "18", "name": "<PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "Tristana.png", "sprite": "champion4.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "18000", "num": 0, "name": "default", "chromas": false}, {"id": "18001", "num": 1, "name": "Riot Girl <PERSON>", "chromas": false}, {"id": "18002", "num": 2, "name": "Świąteczny Elf Tristana", "chromas": false}, {"id": "18003", "num": 3, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18004", "num": 4, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18005", "num": 5, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "18006", "num": 6, "name": "Tristana z Wyrzutnią Rakiet", "chromas": true}, {"id": "18010", "num": 10, "name": "Tristana Treserka Smoków", "chromas": true}, {"id": "18011", "num": 11, "name": "Czarują<PERSON> Tristan<PERSON>", "chromas": false}, {"id": "18012", "num": 12, "name": "<PERSON><PERSON> z Oddziału Omega", "chromas": true}, {"id": "18024", "num": 24, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "18033", "num": 33, "name": "Tristana w Przebraniu Pingu", "chromas": true}, {"id": "18040", "num": 40, "name": "Hextechowa Tristana", "chromas": false}, {"id": "18041", "num": 41, "name": "Rozrywkowa Tristana", "chromas": true}, {"id": "18051", "num": 51, "name": "<PERSON><PERSON>", "chromas": true}, {"id": "18061", "num": 61, "name": "Tristana z Wróżkowego Dworu", "chromas": true}], "lore": "Inni Yordlowie wykorzystują własną energię, by by<PERSON>, wyna<PERSON>zcami lub po prostu psotnikami. <PERSON><PERSON> zawsze pociągały przygody wielkich wojowników. Słyszała wiele o Runeterze, jej frak<PERSON> oraz wojnach i wierzyła, że tacy jak ona też mogą stać się godnymi legend. Postawiwszy pierwszy krok w tym świecie, złapała za swoje wierne działo — Boomera, i teraz rzuca się do walki z niezłomną odwagą i optymizmem.", "blurb": "Inni Yordlowie wykorzystują własną energię, by by<PERSON>, wyna<PERSON>zcami lub po prostu psotnikami. <PERSON><PERSON> z<PERSON> pociągały przygody wielkich wojowników. Słyszała wiele o Runeterze, jej frak<PERSON>ch oraz wojnach i wierzyła, że tacy jak ona też mogą stać...", "allytips": ["Olbrzymia broń Tristany pozwala jej atakować wrogów z dużej odległości. Wykorzystaj to, aby przes<PERSON><PERSON><PERSON><PERSON>ć przeciwnikowi w nękaniu cię.", "Po zebraniu ładunków Ładunku Wybuchowego na swoim celu, użyj Rakietowego Skoku, aby go wykończyć.", "Użyj Szybkiego Ostrzału, aby szyb<PERSON>j skumulować ładunki Ładunku Wybuchowego na wrogich bohaterach."], "enemytips": ["<PERSON><PERSON><PERSON>, że Tristana używa Szybkiego Ostrzału w czasie walki, ogł<PERSON><PERSON> ją i wycofaj się do chwili, w której umiejętność przestanie działać.", "Trzymaj się z dala od stworów w alei, aby nie otr<PERSON><PERSON>ć przypadkowych obrażeń od Ładunku Wybuchowego."], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 3, "magic": 5, "difficulty": 4}, "stats": {"hp": 640, "hpperlevel": 102, "mp": 300, "mpperlevel": 32, "movespeed": 325, "armor": 30, "armorperlevel": 4, "spellblock": 28, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 4, "hpregenperlevel": 0.5, "mpregen": 7.2, "mpregenperlevel": 0.45, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 1.5, "attackspeed": 0.656}, "spells": [{"id": "TristanaQ", "name": "Szybki Ostrzał", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku Tristany zwiększa się na pewien czas.", "tooltip": "<PERSON><PERSON> s<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <attackSpeed>{{ attackspeedmod*100 }}% pręd<PERSON>ści ataku</attackSpeed> na {{ buffduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ attackspeedmod*100.000000 }}% -> {{ attackspeedmodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [20, 20, 20, 20, 20], "rangeBurn": "20", "image": {"full": "TristanaQ.png", "sprite": "spell14.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TristanaW", "name": "Rakietowy Skok", "description": "Tristana strzela w ziemię, by <PERSON><PERSON><PERSON><PERSON><PERSON> w inny obszar. W miejscu lądowania zadaje obrażenia okolicznym jednostkom i spowalnia je na chwilę.", "tooltip": "<PERSON>a katapultuje się, po wyl<PERSON><PERSON><PERSON><PERSON> z<PERSON> <magicDamage>{{ landingdamage }} pkt. obrażeń magicznych</magicDamage> i <status>spowalniając</status> wrog<PERSON> o {{ slowmod*-100 }}% na {{ slowduration }} sek.<br /><br />Udziały w zabójstwach bohaterów oraz detonacja umiejętności <spellName>Ładunek Wybuchowy</spellName> przy maks. liczbie ładunków odświeżają czas odnowienia tej umiejętności.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 20, 18, 16, 14], "cooldownBurn": "22/20/18/16/14", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "TristanaW.png", "sprite": "spell14.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TristanaE", "name": "Ładunek Wybuchowy", "description": "Po zabiciu jednostki pocisk Tristany wybucha, z<PERSON><PERSON><PERSON><PERSON> pobliskim wrogom obrażenia. Umiej<PERSON>t<PERSON>ść może zostać użyta, aby na wybranym przeciwniku umieścić bombę, która po chwili wybucha i zadaje obrażenia pobliskim jednostkom.", "tooltip": "<spellPassive>B<PERSON>nie:</spellPassive> <PERSON><PERSON> Tristany, które zabij<PERSON> cel, zadad<PERSON><PERSON> <magicDamage>{{ passivedamage }} pkt. obrażeń magicznych</magicDamage> pobliskim wrogom.<br /><br /><spellActive>Użycie:</spellActive> Tristana przyczepia do wroga lub wieży bombę, która zadaje pobliskim wrogom <physicalDamage>{{ activedamage }} pkt. obrażeń fizycznych</physicalDamage> po {{ activeduration }} sek. Obrażenia są zwiększone o {{ critchanceamp*100 }}% szansy na trafienie krytyczne i o {{ activeperstackamp*100 }}% z każdym trafionym atakiem lub umiejętnością Tristany (maks. 4 ładunki).<br /><br />Przy następującej liczbie ładunków: {{ activemaxstacks }} bomba wybucha natychmiast (maks. <physicalDamage>{{ activemaxdamage }} pkt. obraż<PERSON><PERSON> fizycznych</physicalDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Bierne obrażenia wybuchu", "Podstawowe obrażenia ładunku", "Współczynnik obrażeń od ataku", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ passivebasedamage }} -> {{ passivebasedamageNL }}", "{{ activebasedamage }} -> {{ activebasedamageNL }}", "{{ activebadratio*100.000000 }}% -> {{ activebadrationl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 15.5, 15, 14.5, 14], "cooldownBurn": "16/15.5/15/14.5/14", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaE.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "TristanaR", "name": "Niszczący Strzał", "description": "Tristana ładuje wielki pocisk do swojej broni i strzela w przeciwnika. Pocisk zadaje magiczne obrażenia i odrzuca przeciwnika. <PERSON><PERSON><PERSON> cel ma na sobie Ładunek Wybuchowy, obszar wybuchu bomby jest podwojony.", "tooltip": "Tristana wystrzeliwuje olbrzymią kulę armatnią, która zadaje <magicDamage>{{ damagecalc }} pkt. obrażeń magicznych</magicDamage> swojemu celowi i <status>odr<PERSON><PERSON></status> oraz <status>og<PERSON><PERSON><PERSON></status> cel i pobliskich wrogów na {{ stunduration }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> odrzucenia", "Czas działania ogłuszenia:"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ knockbackdistance }} -> {{ knockbackdistanceNL }}", "{{ stunduration }} -> {{ stundurationNL }}"]}, "maxrank": 3, "cooldown": [100, 100, 100], "cooldownBurn": "100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [550, 550, 550], "rangeBurn": "550", "image": {"full": "TristanaR.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "<PERSON>", "description": "<PERSON><PERSON><PERSON>g ataku Tristany zwiększa się za każdym razem, gdy zdobędzie ona poziom.", "image": {"full": "Tristana_Passive.png", "sprite": "passive4.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}