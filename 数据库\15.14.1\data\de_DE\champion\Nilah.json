{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nilah": {"id": "<PERSON><PERSON>", "key": "895", "name": "<PERSON><PERSON>", "title": "die ungezügelte Freude", "image": {"full": "Nilah.png", "sprite": "champion3.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "895000", "num": 0, "name": "default", "chromas": false}, {"id": "895001", "num": 1, "name": "Sternenwächterin <PERSON>", "chromas": true}, {"id": "895011", "num": 11, "name": "Hexenzirkel-<PERSON>lah", "chromas": true}, {"id": "895021", "num": 21, "name": "Tintenschatten-<PERSON><PERSON>", "chromas": true}], "lore": "Nilah ist eine asketische Kriegerin aus einem weit entfernten Land, die nach den tödlichsten, riesigsten Kontrahenten sucht, um sie herauszufordern und niederzustrecken. Da sie ihre Macht einer Begegnung mit dem seit langer Zeit gefangen gehaltenen Dämon der Freude zu verdanken hat, empfindet sie keine andere Emotion als unendliche Euphorie – ein kleiner Preis für die unermessliche Kraft, über die sie nun verfügt. Sie formt die flüssige Gestalt des Dämons zu einer Klinge von unerreichter Macht und trotzt den uralten, längst vergessenen Bedrohungen.", "blurb": "Nilah ist eine asketische Kriegerin aus einem weit entfernten Land, die nach den tödlichsten, riesigsten Kontrahenten sucht, um sie herauszufordern und niederzustrecken. Da sie ihre Macht einer Begegnung mit dem seit langer Zeit gefangen gehaltenen...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 4, "magic": 4, "difficulty": 10}, "stats": {"hp": 570, "hpperlevel": 101, "mp": 350, "mpperlevel": 35, "movespeed": 340, "armor": 27, "armorperlevel": 4.5, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 225, "hpregen": 6, "hpregenperlevel": 0.9, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 2, "attackspeedperlevel": 3, "attackspeed": 0.697}, "spells": [{"id": "NilahQ", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON> s<PERSON> in eine gewählte Richtung mit ihrer Peitschenklinge zu und verursacht Schaden an allen getroffenen Gegnern. Diese Aktion erhöht kurzzeitig ihre Angriffsreichweite.", "tooltip": "<spellPassive>Passiv:</spellPassive> Nilah erhält {{ critarmorpen }}&nbsp;Rüstungsdurchdringung und ihre Angriffe gegen Champions stellen <healing>Leben</healing> in <PERSON><PERSON><PERSON> von {{ critlifesteal }} des verursachten Schadens wieder her. Jegliche Überheilung wird {{ shieldduration }}&nbsp;Sekunden lang in einen <shield>Schild</shield> umgewandelt.<br /><br /><spellActive>Aktiv:</spellActive> Nilah schlägt mit ihrer Peitschenklinge zu und verursacht <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage> (erhöht durch ihre Chance auf kritische Treffer). Wenn eine gegnerische Einheit oder ein Gebäude getroffen wird, erhält Nilah 125 Angriffsreichweite sowie <attackSpeed>{{ bonusattackspeedcalc }}&nbsp;% Angriffstempo</attackSpeed> und ihre Angriffe verursachen in einem kegelförmigen Bereich {{ buffduration }}&nbsp;Sekunden lang <physicalDamage>{{ attacktotaldamagetooltip }}&nbsp;normalen Schaden</physicalDamage>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Prozentualer Angriffsschaden"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ qadratio*100.000000 }}&nbsp;% -> {{ qadrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NilahQ.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NilahW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> versch<PERSON>t sich, erhöht ihr Lauftempo und weicht allen eingehenden Angriffen elegant und anmutig aus. Alle Verbündeten, die sie im verschleierten Zustand berührt, erhalten diesen Effekt ebenfalls.", "tooltip": "<PERSON><PERSON> verschleiert sich {{ baseduration }}&nbsp;<PERSON><PERSON><PERSON> lang, erh<PERSON><PERSON> „Geist“, <speed>{{ movespeedpercent*100 }}&nbsp;% Lauftempo</speed>, weicht Angriffen aus und verringert erlittenen <magicDamage>magischen Schaden</magicDamage> um {{ magicdamagereduction*100 }}&nbsp;%.<br /><br />Während die Fähigkeit aktiv ist, kann sie verbündete Champions durch Berührung ebenfalls verschleiern und sie profitieren {{ sharebaseduration }}&nbsp;Sekunden lang von den gleichen Vorteilen.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "Lauftempo", "Kosten (@AbilityResourceName@)"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ movespeedpercent*100.000000 }}&nbsp;% -> {{ movespeedpercentnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [26, 25, 24, 23, 22], "cooldownBurn": "26/25/24/23/22", "cost": [60, 45, 30, 15, 0], "costBurn": "60/45/30/15/0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [150, 150, 150, 150, 150], "rangeBurn": "150", "image": {"full": "NilahW.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NilahE", "name": "Stromschnellen", "description": "<PERSON><PERSON> stürzt sich schwungvoll auf ihr Ziel und fügt allen auf dem Weg getroffenen Gegnern Schaden zu.", "tooltip": "<PERSON><PERSON> stü<PERSON>t durch eine Einheit hindurch und fügt allen getroffenen Gegnern auf ihrem Weg <physicalDamage>{{ dashdamage }}&nbsp;normalen Schaden</physicalDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Wiederaufladungsrate"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ ammorechargetime }} -> {{ ammorechargetimeNL }}"]}, "maxrank": 5, "cooldown": [0.5, 0.5, 0.5, 0.5, 0.5], "cooldownBurn": "0.5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "2", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "NilahE.png", "sprite": "spell9.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NilahR", "name": "Apotheose", "description": "<PERSON><PERSON> lässt ausgelassen ihre Peitschenklinge kreisen und fügt <PERSON> in ihrem Umkreis Schaden zu, bevor sie diese mit ihrer Waffe zu sich zieht.", "tooltip": "Nilah wirbelt ihre Peitschenklinge und verursacht 1&nbsp;Sekunde lang <physicalDamage>{{ damagepertickcalctooltip }}&nbsp;normalen Schaden</physicalDamage>. Danach verursacht sie zusätzlich <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage> und <status>zieht</status> <PERSON><PERSON><PERSON> in der Nähe zu sich heran.<br /><br />Nilah heilt sich und nahe Verbündete um <healing>{{ champhealingpercent }} (+{{ spell.nilahq:critlifesteal }} Formlose Klinge) des verursachten Schadens an gegnerischen Champions ({{ otherhealingpercent*100 }}&nbsp;% für Einheiten, die keine Champions sind)</healing>. Jegliche Überheilung wird {{ duration }}&nbsp;Sekunden lang in einen <shield>Schild</shield> umgewandelt.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit", "<PERSON><PERSON><PERSON>", "Schaden pro Zeiteinheit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ damagepertick }} -> {{ damagepertickNL }}"]}, "maxrank": 3, "cooldown": [110, 95, 80], "cooldownBurn": "110/95/80", "cost": [80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400], "rangeBurn": "400", "image": {"full": "NilahR.png", "sprite": "spell9.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Unend<PERSON> Freude", "description": "Nilah erhält erhöhte Erfahrung durch letzte Treffer bei Vasallen und kann die Heil- und Schildkraft von Verbündeten in ihrer Nähe verstärken und teilen.", "image": {"full": "NIlahP.png", "sprite": "passive3.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}