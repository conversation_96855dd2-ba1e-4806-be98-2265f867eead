{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Mordekaiser": {"id": "<PERSON><PERSON><PERSON><PERSON>", "key": "82", "name": "<PERSON><PERSON><PERSON><PERSON>", "title": "der eiserne Albtraum", "image": {"full": "Mordekaiser.png", "sprite": "champion2.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "82000", "num": 0, "name": "default", "chromas": false}, {"id": "82001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "82002", "num": 2, "name": "Infernalischer Mordekaiser", "chromas": false}, {"id": "82003", "num": 3, "name": "Pentakill-Mordekaiser", "chromas": false}, {"id": "82004", "num": 4, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "82005", "num": 5, "name": "Kreuz-König-Mordekaiser", "chromas": false}, {"id": "82006", "num": 6, "name": "Sternenvernichter-Mordekaiser", "chromas": true}, {"id": "82013", "num": 13, "name": "PROJEKT: <PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "82023", "num": 23, "name": "Pentakill III: Lost Chapter-<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "82032", "num": 32, "name": "High Noon-Mordekaiser", "chromas": true}, {"id": "82042", "num": 42, "name": "Aschfahler Grabritter Mordekaiser", "chromas": true}, {"id": "82044", "num": 44, "name": "<PERSON><PERSON> <PERSON>", "chromas": true}, {"id": "82054", "num": 54, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>kaiser", "chromas": false}], "lore": "Mordekaiser ist ein brutaler Kriegsfürst aus einer längst vergangenen Epoche, der zweimal ums Leben gebracht sowie dreimal neu geboren wurde und mit seinen nekromantischen Zauberkräften Seelen in ewige Knechtschaft zwingt. Diejenigen, die sich an seine früheren Eroberungszüge erinnern oder die wahren Ausmaße seiner Kräfte kennen, sind inzwischen gering an der Zahl – doch es gibt noch ein paar dieser uralten Seelen und sie fürchten den Tag, an dem er zurückkehren und die Herrschaft über die Lebenden sowie die Toten an sich reißen könnte.", "blurb": "Mordekaiser ist ein brutaler Kriegsfürst aus einer längst vergangenen Epoche, der zweimal ums Leben gebracht sowie dreimal neu geboren wurde und mit seinen nekromantischen Zauberkräften Seelen in ewige Knechtschaft zwingt. Diejenigen, die sich an seine...", "allytips": ["Angriff ist deine Verteidigung. Kämpf weiter, um größere unzerstörbare Schilde zu erschaffen.", "<PERSON><PERSON><PERSON>, mehrere Champions mit derselben Fähigkeit zu treffen, damit du „Aufziehende Dunkelheit“ möglichst schnell aktivieren kannst.", "W<PERSON><PERSON> „Todesreich“ auf einen Gegner mit wenig <PERSON>ben, damit dir der Kill sicher ist und du seine Werte bis zum Ende des Teamkampfs behalten kannst."], "enemytips": ["<PERSON><PERSON><PERSON><PERSON> baut sich im Kampf gegen Champions eine starke Schadensaura auf, also bleib besser auf Distanz.", "<PERSON>, den er verursacht, wandelt er in einen großen Schild um, den er wiederum konsumieren kann, um zu heilen.", "W<PERSON><PERSON><PERSON> der Wirkdauer von „Todesreich“ bist du von deinen Teamkameraden komplett isoliert. Heb dir einige Mobilitätsfähigkeiten für den Fall auf, dass du darin gefangen bist und vor Mordekaiser fliehen musst."], "tags": ["Fighter", "Mage"], "partype": "<PERSON><PERSON><PERSON>", "info": {"attack": 4, "defense": 6, "magic": 7, "difficulty": 4}, "stats": {"hp": 645, "hpperlevel": 104, "mp": 100, "mpperlevel": 0, "movespeed": 335, "armor": 37, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 5, "hpregenperlevel": 0.75, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 4, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "MordekaiserQ", "name": "Verwüstung", "description": "Mordekaiser schmettert seinen Streitkolben auf den Boden und fügt jedem getroffenen Gegner Schaden zu. Der Schaden ist bei Treffern eines einzelnen Champions erhöht.", "tooltip": "Mordekaiser schmettert Nachtgrauen auf den Boden und verursacht <magicDamage>{{ qdamage }}&nbsp;magischen <PERSON>haden</magicDamage>, der auf <magicDamage>{{ empowereddamagetooltip }}</magicDamage> erhöht wird, wenn er nur einen einzigen Gegner trifft.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Isolationsschaden", "Abklingzeit"], "effect": ["{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ isolationscalar*100.000000 }}&nbsp;% -> {{ isolationscalarnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7, 6, 5, 4], "cooldownBurn": "8/7/6/5/4", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [675, 675, 675, 675, 675], "rangeBurn": "675", "image": {"full": "MordekaiserQ.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "MordekaiserW", "name": "Unzerstörbar", "description": "Mordekaiser speichert den von ihm verursachten Schaden und erschafft daraus einen Schild. Er kann den Schild verbrauchen, um sich zu heilen.", "tooltip": "<passive>Passiv:</passive> Mordekaiser speichert {{ damageconversion*100 }}&nbsp;% des Schadens, den er verursacht und {{ damagetakenconversion*100 }}&nbsp;% des Schadens, den er erleidet.<br /><br /><active>Aktiv:</active> Mordekaiser erhält den gespeicherten Schaden als <shield>Schild</shield>. Er kann diese Fähigkeit <recast>reaktivieren</recast>, um <healing>Leben</healing> in Höhe von {{ healingpercent*100 }}&nbsp;% des verbleibenden Schilds wiederherzustellen.<br /><br />Minimaler Schild:<shield>{{ minhealthtooltip }}</shield><br />Maximaler Schild: <shield>{{ maxhealthtooltip }}</shield>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Prozent Heilung", "Abklingzeit"], "effect": ["{{ healingpercent*100.000000 }}&nbsp;% -> {{ healingpercentnl*100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [12, 11, 10, 9, 8], "cooldownBurn": "12/11/10/9/8", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "MordekaiserW.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "MordekaiserE", "name": "Umklammerung des Unheils", "description": "Mordekaiser zieht alle Gegner in einem Bereich zu sich heran.", "tooltip": "<spellPassive>Passiv:</spellPassive> Mordekaiser erhält {{ magicpen*100 }}&nbsp;% Magiedurchdringung.<br /><br /><spellActive>Aktiv:</spellActive> Mordekai<PERSON> zieht G<PERSON>ner in seine Richtung und verursacht <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden.</magicDamage>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Prozent Magiedurchdringung", "<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ magicpen*100.000000 }}&nbsp;% -> {{ magicpennl*100.000000 }}&nbsp;%", "{{ damage }} -> {{ damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [18, 16, 14, 12, 10], "cooldownBurn": "18/16/14/12/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [700, 700, 700, 700, 700], "rangeBurn": "700", "image": {"full": "MordekaiserE.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "MordekaiserR", "name": "Todesreich", "description": "Mordekaiser zerrt seinen Gegner in eine andere Dimension und stiehlt einen Teil seiner Werte. Tötet er sein <PERSON>, behält er die Werte bis zu dessen Wiedererscheinen.", "tooltip": "Mordekaiser verbannt einen Champion {{ spiritrealmduration }}&nbsp;Sekunden lang zu sich ins Todesreich und übernimmt für die Dauer der Fähigkeit {{ statstealpercentscalar*100 }}&nbsp;% seiner Grundwerte.<br /><br />Tötet Mordekaiser den Gegner im Todesreich, entzieht er dessen Seele und behält die Werte, bis das Ziel wiedererscheint.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Abklingzeit"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "MordekaiserR.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Aufziehende Dunkelheit", "description": "Mordekaiser erhält eine starke Schadensaura und Lauftempo, nachdem 3 Angriffe oder Fähigkeiten gegnerische Champions oder Monster getroffen haben.", "image": {"full": "MordekaiserPassive.png", "sprite": "passive2.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}