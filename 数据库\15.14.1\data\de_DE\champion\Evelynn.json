{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Evelynn": {"id": "<PERSON><PERSON>", "key": "28", "name": "<PERSON><PERSON>", "title": "Umarmung der Qual", "image": {"full": "Evelynn.png", "sprite": "champion1.png", "group": "champion", "x": 48, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "28000", "num": 0, "name": "default", "chromas": false}, {"id": "28001", "num": 1, "name": "Schatten-<PERSON><PERSON>", "chromas": false}, {"id": "28002", "num": 2, "name": "Maskeraden-Evelynn", "chromas": false}, {"id": "28003", "num": 3, "name": "Tango-<PERSON><PERSON>", "chromas": false}, {"id": "28004", "num": 4, "name": "Tresorknacker-Evelynn", "chromas": false}, {"id": "28005", "num": 5, "name": "Blutmond-<PERSON><PERSON>", "chromas": false}, {"id": "28006", "num": 6, "name": "K/DA-<PERSON><PERSON>", "chromas": false}, {"id": "28007", "num": 7, "name": "K/DA-<PERSON><PERSON> (Prestige)", "chromas": false}, {"id": "28008", "num": 8, "name": "Zuckerschock-<PERSON><PERSON>", "chromas": true}, {"id": "28015", "num": 15, "name": "K/DA ALL OUT-Evelynn", "chromas": true}, {"id": "28024", "num": 24, "name": "Hexenzirkel-Evelynn", "chromas": true}, {"id": "28031", "num": 31, "name": "K/DA-Evelynn (Prestige 2022)", "chromas": false}, {"id": "28032", "num": 32, "name": "<PERSON>lenblumen-<PERSON><PERSON>", "chromas": true}, {"id": "28042", "num": 42, "name": "Soul Fighter Evelyn<PERSON>", "chromas": true}, {"id": "28052", "num": 52, "name": "High Noon-Evelynn", "chromas": true}, {"id": "28053", "num": 53, "name": "High Noon-Evelynn (Prestige)", "chromas": false}, {"id": "28064", "num": 64, "name": "Flammende Finsternis Evelynn", "chromas": false}], "lore": "In den dunklen Nischen von Runeterra lauert die Dämonin Evelynn ihrem nächsten Opfer auf. Sie lockt ihre Beute mit der sinnlichen Form einer Frau an, doch sobald sie ihrem Charme erliegt, zeigt <PERSON> ihre wahre Gestalt. Dann setzt sie ihr Opfer unsäglichen Qualen aus und labt sich an seinem Schmerz. Für die Dämonin sind diese Begegnungen nur harmlose Affären. Für das restliche Runeterra jedoch sind sie schaurige Geschichten aus dem Ruder gelaufener Lust und grausige Beispiele für die Kosten übermäßiger Begierde.", "blurb": "In den dunklen Nischen von Runeterra lauert die Dämonin Evelynn ihrem nächsten Opfer auf. Sie lockt ihre Beute mit der sinnlichen Form einer Frau an, doch sobald sie ihrem Charme er<PERSON>gt, zeigt <PERSON> ihre wahre Gestalt. Dann setzt sie ihr Opfer...", "allytips": ["Die Aktivierungsdauer von „Tödliche Verlockung“ mag lang ersche<PERSON>, doch das Bezaubern und die Verringerung der Magieresistenz verschaffen Evelynn einen deutlichen Vorteil, weshalb sich das Warten lohnt.", "<PERSON>n du getarnt bist, achte da<PERSON>, wann dich gegnerische Champions (fast) enttarnen. Erkennen lässt sich das an gelb- und rotglühenden Augen über nahen gegnerischen Champions.", "Wenn du nur noch wenig <PERSON> hast, kannst du die Heilwirkung von „Dämonenschatten“ und Camouflage zu deinem Vorteil nutzen, um schließlich wieder in den Kampf zurückzukehren und deine Gegner zu überraschen."], "enemytips": ["<PERSON><PERSON>e magische Augen, um Evelynns Position leichter herauszufinden und auf ihre Angriffe aus dem Hinterhalt vorbereitet zu sein.", "Nimm dich besonders vor Evelynns Bezaubern&nbsp;– „Tödliche Verlockung“&nbsp;– in Acht. Schütze markierte Verbündete oder, falls du selbst markiert sein solltest, halte dich von ihrem vermutlichen Erscheinungsort fern und bleibe hinter Verbündeten.", "<PERSON><PERSON> <PERSON>, dass Evelynn einen Hinterhalt auf einen deiner Teamkameraden plant, dann setze entsprechende Signale auf der Minikarte und schreibe es ihm im Chat."], "tags": ["Assassin", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 2, "magic": 7, "difficulty": 10}, "stats": {"hp": 642, "hpperlevel": 98, "mp": 315, "mpperlevel": 42, "movespeed": 335, "armor": 37, "armorperlevel": 4.7, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 8.5, "hpregenperlevel": 0.75, "mpregen": 8.11, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3, "attackspeedperlevel": 2.1, "attackspeed": 0.667}, "spells": [{"id": "Evelynn<PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> schlägt mit ihren Peitschenschwingen zu und fügt der ersten getroffenen Einheit Schaden zu. Danach kann Evelynn nahe Gegner ein paar Mal mit Stacheln durchbohren.", "tooltip": "<PERSON><PERSON> schlägt mit ihrem Peitschenschwingen zu, was dem ersten getroffenen Gegner <magicDamage>{{ missiledamage }}&nbsp;magischen Schaden</magicDamage> zuf<PERSON><PERSON> und bewirkt, dass Evelynns nächste 3&nbsp;Angriffe oder Fähigkeiten an dieser Einheit zusätzlich <magicDamage>{{ totalbonusdamage }}&nbsp;magischen Schaden</magicDamage> verursachen. Evelynn kann diese Fähigkeit bis zu {{ qstackcount }}-mal <recast>reaktivieren</recast>.<br /><br /><recast>Reaktivierung:</recast> Evelynn feuert eine Reihe Stacheln durch den nächstbefindlichen Gegner und fügt dabei allen getroffenen Gegnern <magicDamage>{{ missiledamage }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden (Peitsche/Stachel)", "Zusätzlicher Schaden", "Kosten (@AbilityResourceName@)"], "effect": ["{{ hatespikebasedamage }} -> {{ hatespikebasedamageNL }}", "{{ bonusdamagebase }} -> {{ bonusdamagebaseNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [4, 4, 4, 4, 4], "cooldownBurn": "4", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [30, 30, 30, 30, 30], [60, 60, 60, 60, 60], [15, 25, 35, 45, 55], [25, 30, 35, 40, 45], [6, 6, 6, 6, 6], [30, 30, 30, 30, 30], [50, 50, 50, 50, 50], [4, 4, 4, 4, 4], [-0.25, -0.25, -0.25, -0.25, -0.25]], "effectBurn": [null, "0", "30", "60", "15/25/35/45/55", "25/30/35/40/45", "6", "30", "50", "4", "-0.25"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800, 800, 800], "rangeBurn": "800", "image": {"full": "EvelynnQ.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnW", "name": "Tödliche Verlockung", "description": "<PERSON><PERSON> be<PERSON>t ihr Ziel mit eine<PERSON> Flu<PERSON>, wodurch ihr nächster Angriff oder ihre nächste gewirkte Fähigkeit nach einer Verzögerung ihr Ziel bezaubert und seine Magieresistenz verringert.", "tooltip": "<PERSON><PERSON> markiert 5&nbsp;Sekunden lang einen Champion oder ein Monster. Wenn Evelynn das Ziel mit einem Angriff oder einer Fähigkeit trifft, entfernt sie die Markierung, erstattet ihre Kosten zurück und <status>verlangsamt</status> das Ziel {{ slowduration }}&nbsp;Sekunden lang um {{ slowamount*100 }}&nbsp;%.<br /><br />Wenn die Markierung mindestens 2,5&nbsp;Sekunden lang anhält, hat das Entfernen zusätzliche Effekte:<li>Gegen Champions: <status>Bezaubert</status> sie {{ charmduration }}&nbsp;Seku<PERSON>(n) lang und entfernt {{ shredduration }}&nbsp;Sekunden lang <scaleMR>{{ mrshred*100 }}&nbsp;% Magieresistenz</scaleMR>.<li>Gegen Monster: <status>Bezaubert</status> sie {{ monstercharm }}&nbsp;Sekunden lang und fügt ihnen <magicDamage>{{ monsterdamagetotaltooltip }}&nbsp;magischen Schaden</magicDamage> zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Bezauberungsdauer gegen Monster", "Magieresistenzverringerung", "<PERSON><PERSON><PERSON> an <PERSON>", "Abklingzeit", "Reichweite"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ monstercharm }} -> {{ monstercharmNL }}", "{{ effect9amount*100.000000 }}&nbsp;% -> {{ effect9amountnl*100.000000 }}&nbsp;%", "{{ e7 }} -> {{ e7NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ castrange }} -> {{ castrangeNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [2, 2, 2, 2, 2], [1.25, 1.5, 1.75, 2, 2.25], [-0.45, -0.45, -0.45, -0.45, -0.45], [15, 14, 13, 12, 11], [5, 5, 5, 5, 5], [1.5, 1.5, 1.5, 1.5, 1.5], [250, 300, 350, 400, 450], [0.75, 0.75, 0.75, 0.75, 0.75], [0.35, 0.375, 0.4, 0.425, 0.45], [4, 4, 4, 4, 4]], "effectBurn": [null, "2", "1.25/1.5/1.75/2/2.25", "-0.45", "15/14/13/12/11", "5", "1.5", "250/300/350/400/450", "0.75", "0.35/0.375/0.4/0.425/0.45", "4"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1200, 1300, 1400, 1500, 1600], "rangeBurn": "1200/1300/1400/1500/1600", "image": {"full": "EvelynnW.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnE", "name": "Peitschenschlag", "description": "<PERSON><PERSON> führt mit ihren Schwingen einen Peitschenschlag auf ihr Ziel aus und fügt ihm dabei Schaden zu. Danach ist ihr Lauftempo für kurze Zeit erhöht.", "tooltip": "<PERSON><PERSON> attackiert einen G<PERSON> und fügt ihm <magicDamage>magischen <PERSON></magicDamage> in <PERSON><PERSON><PERSON> von {{ basedamage }} plus {{ percenthealthbasetooltip }} des max. Lebens zu. Evelynn erhält {{ speedduration }}&nbsp;Sekunden lang <speed>{{ speedamount*100 }}&nbsp;% Lauftempo</speed>.<br /><br /><PERSON><PERSON> in den <keywordMajor>Dämonenschatten</keywordMajor> wird die Abklingzeit von „Peitschenschlag“ zurückgesetzt und die Fähigkeit verstärkt. Wenn diese Fähigkeit verstärkt ist, springt Evelynn zu ihrem Ziel und fügt ihm und allen Gegnern auf ihrem Weg <magicDamage>magischen Schaden</magicDamage> in Hö<PERSON> von {{ empowereddamage }} plus {{ percenthealthempoweredtooltip }} des max. Lebens zu.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verstärkter Schaden", "Lauftempo", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ empowered<PERSON><PERSON> }} -> {{ empowereddamageNL }}", "{{ speedamount*100.000000 }}&nbsp;% -> {{ speedamountnl*100.000000 }}&nbsp;%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0.3, 0.35, 0.4, 0.45, 0.5], [2, 2, 2, 2, 2], [3, 3, 3, 3, 3], [4, 4, 4, 4, 4], [450, 450, 450, 450, 450], [0.8, 0.85, 0.9, 0.95, 1], [2, 2, 2, 2, 2], [1.3, 1.35, 1.4, 1.45, 1.5]], "effectBurn": [null, "0", "0", "0.3/0.35/0.4/0.45/0.5", "2", "3", "4", "450", "0.8/0.85/0.9/0.95/1", "2", "1.3/1.35/1.4/1.45/1.5"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [210, 210, 210, 210, 210], "rangeBurn": "210", "image": {"full": "EvelynnE.png", "sprite": "spell3.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "EvelynnR", "name": "Letzte Liebkosung", "description": "<PERSON><PERSON> kann für kurze Zeit nicht anvisiert werden und dezimiert die Gegner im Bereich vor sich, bevor sie sich über eine große Strecke nach hinten teleportiert.", "tooltip": "<PERSON><PERSON> entfesselt ihre dämonische Energie, verursacht schweren Schaden, kann nicht länger anvisiert werden und teleportiert sich nach hinten. Sie verursacht <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage>, der auf <magicDamage>{{ critdamage }}</magicDamage> erhöht wird, wenn das Leben der Gegner unter <healing>30&nbsp;% Leben</healing> liegt. Bei Aktivierung wird für „Dämonenschatten“ eine Abklingzeit von 1,25&nbsp;Sekunden ausgelöst.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verstärkter Schaden", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ effect1amount*2.400000 }} -> {{ effect1amountnl*2.400000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 250, 375], [1.4, 1.4, 1.4], [2.5, 2.5, 2.5], [150, 225, 300], [3, 3, 3], [5, 4, 3], [0.3, 0.3, 0.3], [700, 700, 700], [30, 45, 60], [0, 0, 0]], "effectBurn": [null, "125/250/375", "1.4", "2.5", "150/225/300", "3", "5/4/3", "0.3", "700", "30/45/60", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "EvelynnR.png", "sprite": "spell3.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Dämonenschatten", "description": "Wenn sie sich nicht im Kampf befindet, hüllt sich Evelynn in ihren Dämonenschatten. Der Dämonenschatten heilt <PERSON>, wenn sie über wenig Leben verfügt und gewährt ihr ab Stufe 6 Camouflage.", "image": {"full": "Evelynn_Passive.png", "sprite": "passive1.png", "group": "passive", "x": 48, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}