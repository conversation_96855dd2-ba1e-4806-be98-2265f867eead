{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Swain": {"id": "Swain", "key": "50", "name": "Swain", "title": "il gran generale di Noxus", "image": {"full": "Swain.png", "sprite": "champion4.png", "group": "champion", "x": 432, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "50000", "num": 0, "name": "default", "chromas": false}, {"id": "50001", "num": 1, "name": "Swain del Fronte del Nord", "chromas": false}, {"id": "50002", "num": 2, "name": "Swain <PERSON>", "chromas": false}, {"id": "50003", "num": 3, "name": "Swain <PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "50004", "num": 4, "name": "Swain <PERSON><PERSON>i", "chromas": true}, {"id": "50011", "num": 11, "name": "<PERSON>wain <PERSON><PERSON><PERSON>", "chromas": false}, {"id": "50012", "num": 12, "name": "Swain <PERSON>", "chromas": true}, {"id": "50021", "num": 21, "name": "Swain <PERSON><PERSON><PERSON> dell'Inverno", "chromas": true}, {"id": "50032", "num": 32, "name": "Swain <PERSON><PERSON><PERSON><PERSON> del Lupo", "chromas": true}, {"id": "50033", "num": 33, "name": "Swain <PERSON><PERSON><PERSON><PERSON> del Lupo (edizione prestigio)", "chromas": false}], "lore": "Jericho Swain è il visionario sovrano di Noxus, una nazione espansionista che rispetta solo la forza. Nonostante sia stato menomato nelle guerre di Ionia, dove ha perso il braccio sinistro, ha assunto il controllo dell'impero grazie a una determinazione incrollabile... e a una nuova mano demoniaca. Ora Swain comanda le sue truppe dalla prima linea, in marcia contro un'oscurità che solo lui è in grado di vedere nei macabri corvi che si radunano attorno ai cadaveri che si lascia alle spalle. In un vortice di sacrifici e segreti, il segreto più grande è che il vero nemico è dentro se stessi.", "blurb": "<PERSON> Swain è il visionario sovrano di Noxus, una nazione espansionista che rispetta solo la forza. Nonostante sia stato menomato nelle guerre di Ionia, dove ha perso il braccio sinistro, ha assunto il controllo dell'impero grazie a una determinazione...", "allytips": ["Se hai problemi a immobilizzare un nemico con Preghiera maledetta, prova a scagliarla agli avversari quando sono vicini ai loro minion, cos<PERSON> da farli sorprendere dall'esplosione.", "Quando sei in corsia, prova a usare i danni penetranti di Mano della morte per colpire i nemici a distanza di sicurezza.", "Visione dell'impero è difficile da mandare a segno di per sé; cerca sulla mappa schermaglie in cui i nemici sono distratti o stanno subendo effetti di controllo per usarla con maggiore facilità.", "Ascensione demoniaca può rendere Swain molto difficile da uccidere, ma è abbastanza facile da evitare. Prova a comprare oggetti in grado di rallentare gli avversari per tenerli a gittata quando hanno una buona mobilità."], "enemytips": ["La passiva di Swain è molto potente se sei immobilizzato. Fai molta attenzione ai nemici che hanno effetti di immobilizzazione.", "Un'alta mobilità è la migliore difesa contro tutte le abilità di base di Swain: <PERSON><PERSON> della morte infligge più danni a distanza ravvicinata, Visione dell'impero ha un ritardo molto lungo e Preghiera maledetta deve iniziare a tornare verso di lui per essere pericolosa.", "Acquistare un oggetto con Ferita grave renderà Swain molto più facile da uccidere durante la sua Ascensione demoniaca."], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 9, "difficulty": 8}, "stats": {"hp": 595, "hpperlevel": 99, "mp": 400, "mpperlevel": 29, "movespeed": 330, "armor": 25, "armorperlevel": 4.7, "spellblock": 31, "spellblockperlevel": 1.55, "attackrange": 525, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 10, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 2.7, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "Swain<PERSON>", "name": "<PERSON><PERSON> della morte", "description": "Swain scatena vari dardi di potere infernale che attraversano i nemici. I nemici subiscono più danni per ogni dardo che li colpisce.", "tooltip": "Swain scatena 5 dardi infernali, infliggendo <magicDamage>{{ initialdamage }} danni magici</magicDamage> più <magicDamage>{{ extraboltdamage }} danni magici</magicDamage> per ogni dardo dopo il primo (massimo <magicDamage>{{ maxdamage }} danni magici</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [750, 750, 750, 750, 750], "rangeBurn": "750", "image": {"full": "SwainQ.png", "sprite": "spell13.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "<PERSON>wain<PERSON>", "name": "Visione dell'impero", "description": "Swain apre un occhio demoniaco che infligge danni e rallenta i nemici. I campioni colpiti vengono rivelati e conferiscono a Swain un frammento d'anima.", "tooltip": "Swain apre un occhio demoniaco che rivela una posizione per 1.5 secondi, poi infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallenta</status> di un {{ slow*-100 }}% per {{ slowduration }} secondi.<br /><br />I campioni colpiti conferiscono a Swain un <span class=\"size18 colorFF3F3F\">frammento d'anima</span> e vengono rivelati per {{ revealduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rallentamento", "Gitt<PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%", "{{ castrange }} -> {{ castrangeNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [22, 21, 20, 19, 18], "cooldownBurn": "22/21/20/19/18", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5500, 6000, 6500, 7000, 7500], "rangeBurn": "5500/6000/6500/7000/7500", "image": {"full": "SwainW.png", "sprite": "spell13.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "Swain<PERSON>", "name": "Pregh<PERSON> maledetta", "description": "Swain lancia in avanti un'onda di potere demoniaco. Poi questa torna da Swain e immobilizza i nemici colpiti. Swain può quindi scegliere di trascinare a sé tutti i campioni immobilizzati. Questa abilità ha una ricarica ridotta durante Ascensione demoniaca.", "tooltip": "Swain scaglia in avanti un'onda di potere demoniaco che torna verso di lui, esplodendo contro il primo nemico colpito per <magicDamage>{{ secondarydamage }} danni magici</magicDamage> e <status>immobilizzando</status> i nemici nell'area per {{ rootduration }} secondi.<br /><br /><status>Immobilizzando</status> un campione, Swain può riattivare l'abilità per trascinare verso di lui tutti i campioni <status>immobilizzati</status> da <spellName>Preghiera maledetta</spellName>, ottenendo un <span class=\"size18 colorFF3F3F\">frammento d'anima</span> per ciascuno.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ e2damage }} -> {{ e2damageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 11.5, 11, 10.5, 10], "cooldownBurn": "12/11.5/11/10.5/10", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "SwainE.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SwainR", "name": "Ascensione demoniaca", "description": "Swain si trasforma in un demone e assorbe salute dai campioni nemici vicini, dai minion e dai mostri neutrali. Swain può lanciare Esplosione demoniaca per decimare e rallentare i nemici vicini con un'esplosione di fuoco spirituale. Questa forma è indefinita finché Swain assorbe i campioni nemici.", "tooltip": "Swain libera il demone, che infligge <magicDamage>{{ damagecalc }} danni magici</magicDamage> e assorbe <healing>{{ healingcalc }} salute</healing> al secondo dai nemici nelle vicinanze. La sua energia demoniaca si esaurisce nel tempo, ma può essere ricaricata a tempo indeterminato consumando i campioni nemici e si ripristina completamente all'eliminazione dei campioni.<br /><br />Dopo {{ demonflarecastdelay }} secondi e, di seguito, ogni {{ demonflarecooldowntooltip }} secondi, Swain può lanciare <spellName>Esplosione demoniaca</spellName> mentre è trasformato, infliggendo <magicDamage>{{ demonflaredamagetotal }} danni magici</magicDamage> e <status>rallentando</status> i nemici di un {{ demonflareslowamount*100 }}%, che decresce nell'arco di {{ demonflareslowduration }} secondo/i.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> assor<PERSON>", "Guarigione assorbimento", "Danni Esplosione demoniaca"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ healpersecond }} -> {{ healpersecondNL }}", "{{ demonflaredamagebase }} -> {{ demonflaredamagebaseNL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650], "rangeBurn": "650", "image": {"full": "SwainR.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Stormo famelico", "description": "I corvi di Swain raccolgono <i>frammenti d'anima</i> che lo curano e aumentano in modo permanente la sua salute massima.", "image": {"full": "Swain_P.png", "sprite": "passive4.png", "group": "passive", "x": 432, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}