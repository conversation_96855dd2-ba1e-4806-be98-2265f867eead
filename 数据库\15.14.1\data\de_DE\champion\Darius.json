{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Darius": {"id": "<PERSON>", "key": "122", "name": "<PERSON>", "title": "die <PERSON> von Noxus", "image": {"full": "Darius.png", "sprite": "champion0.png", "group": "champion", "x": 240, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "122000", "num": 0, "name": "default", "chromas": true}, {"id": "122001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "122002", "num": 2, "name": "Bioschmiede-Darius", "chromas": false}, {"id": "122003", "num": 3, "name": "Waidkönig<PERSON>Darius", "chromas": false}, {"id": "122004", "num": 4, "name": "Dunk<PERSON>", "chromas": true}, {"id": "122008", "num": 8, "name": "Akademie-Darius", "chromas": false}, {"id": "122014", "num": 14, "name": "<PERSON><PERSON><PERSON>ensnova-<PERSON>", "chromas": false}, {"id": "122015", "num": 15, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "122016", "num": 16, "name": "High Noon-<PERSON>", "chromas": true}, {"id": "122024", "num": 24, "name": "Mondzodiak-Darius", "chromas": true}, {"id": "122033", "num": 33, "name": "Gangsteralbtraum-Darius", "chromas": false}, {"id": "122043", "num": 43, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "122054", "num": 54, "name": "Porzellan-Darius", "chromas": false}, {"id": "122064", "num": 64, "name": "Himmlischer Gottkönig Darius", "chromas": false}, {"id": "122065", "num": 65, "name": "Triumphierender <PERSON> Darius (Prestige)", "chromas": false}], "lore": "Es gibt kein großartigeres Symbol für noxianische Macht als Darius, den gefürchtetsten und kampferprobtesten Kommandanten der Nation. Aus bescheidenen Verhältnissen stieg er zur Hand von Noxus auf und schlägt sich durch die Gegnerscharen des Reichs, von denen viele ebenfalls Noxianer sind. Er zweifelt nie an der Rechtmäßigkeit seiner Sache und zögert nie, sobald er seine Axt einmal erhoben hat. Wer sich gegen den Kommandanten der Trifarianischen Legion stellt, wird erbarmung<PERSON>los niedergestreckt.", "blurb": "Es gibt kein großartigeres Symbol für noxianische Macht als Darius, den gefürchtetsten und kampferprobtesten Kommandanten der Nation. Aus bescheidenen Verhältnissen stieg er zur Hand von Noxus auf und schlägt sich durch die Gegnerscharen des Reichs, von...", "allytips": ["„Dezimieren“ ist eine mächtige Fähigkeit, um Gegnern zuzusetzen.", "„Noxianische Guillotine“ verursacht mehr Schaden, je mehr Treffer du zuvor erzielen kannst. Nutze „Noxianische Macht“, um maximalen Schaden zu verursachen.", "<PERSON> profit<PERSON>t vor allem von mehr Durchhaltevermögen. Je länger du einen Kampf hina<PERSON> kannst, umso mächtiger wird er."], "enemytips": ["<PERSON><PERSON><PERSON>' „Axtgriff“ abklingt, ist er verwundbarer und ihm kann leichter zugesetzt werden.", "<PERSON> kann nur begrenzt aus Kämpfen entkommen. <PERSON>t du einen Vorteil, solltest du diesen unbedingt ausnutzen."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 1, "difficulty": 2}, "stats": {"hp": 652, "hpperlevel": 114, "mp": 263, "mpperlevel": 58, "movespeed": 340, "armor": 37, "armorperlevel": 5.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 10, "hpregenperlevel": 0.95, "mpregen": 6.6, "mpregenperlevel": 0.35, "crit": 0, "critperlevel": 0, "attackdamage": 64, "attackdamageperlevel": 5, "attackspeedperlevel": 1, "attackspeed": 0.625}, "spells": [{"id": "DariusCleave", "name": "Dezimieren", "description": "<PERSON> holt <PERSON> und schwingt seine Axt in einem weiten Kreis. Von der Klinge getroffene Gegner erleiden mehr Schaden als Gegner, die vom Schaft getroffen werden. <PERSON> heilt sich abh<PERSON><PERSON><PERSON> von den gegnerischen Champions und großen Monstern, die von der Klinge getroffen werden.", "tooltip": "<PERSON> hebt seine Axt, schwingt sie und verursacht mit der Klinge <physicalDamage>{{ bladedamage }}</physicalDamage> und dem Griff <physicalDamage>{{ handledamage }}&nbsp;normalen Schaden</physicalDamage>. <PERSON><PERSON><PERSON>, die mit dem Griff getroffen werden, erhalten keine Steigerung von <keywordMajor>Blutung</keywordMajor>.<br /><br /><PERSON> stellt pro gegnerischem Champion und großem Dschungelmonster, die er mit der Klinge trifft, <healing>{{ e5 }}&nbsp;% seines fehlenden Lebens</healing> wieder her, bis zu einem Maximum von <healing>{{ e7 }}&nbsp;%</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Skalierung mit Gesamtangriffsschaden", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e2 }} -> {{ e2NL }}", "{{ e1 }}&nbsp;% -> {{ e1NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [25, 30, 35, 40, 45], "costBurn": "25/30/35/40/45", "datavalues": {}, "effect": [null, [100, 110, 120, 130, 140], [50, 80, 110, 140, 170], [99, 99, 99, 99, 99], [0.1, 0.1, 0.1, 0.1, 0.1], [17, 17, 17, 17, 17], [35, 35, 35, 35, 35], [51, 51, 51, 51, 51], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "100/110/120/130/140", "50/80/110/140/170", "99", "0.1", "17", "35", "51", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1, 1, 1, 1, 1], "rangeBurn": "1", "image": {"full": "DariusCleave.png", "sprite": "spell2.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusNoxianTacticsONH", "name": "Verkrüppelnder Schlag", "description": "<PERSON>' n<PERSON>chs<PERSON> Angriff trifft die Hauptschlagader eines Gegners. <PERSON><PERSON><PERSON><PERSON> dieser verblutet, ist sein Lauftempo verlangsamt.", "tooltip": "<PERSON><PERSON> <PERSON><PERSON>chs<PERSON> Angriff verursacht <physicalDamage>{{ empoweredattackdamage }}&nbsp;normalen Schaden</physicalDamage> und <status>verlangsamt</status> das Ziel {{ e5 }}&nbsp;Sekunde(n) lang um {{ e2 }}&nbsp;%.<br /><br />Diese Fähigkeit erstattet ihre Manakosten zurück und verringert die Abklingzeit um {{ e3 }}&nbsp;%, wenn sie das Ziel tötet.<br /><br /><rules>Diese Fähigkeit löst Zaubereffekte aus, wenn sie Schaden verursacht.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Skalierung mit Gesamtangriffsschaden"], "effect": ["{{ effect4amount*100.000000 }} -> {{ effect4amountnl*100.000000 }}"]}, "maxrank": 5, "cooldown": [5, 5, 5, 5, 5], "cooldownBurn": "5", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [90, 90, 90, 90, 90], [50, 50, 50, 50, 50], [1.4, 1.45, 1.5, 1.55, 1.6], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "90", "50", "1.4/1.45/1.5/1.55/1.6", "1", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [300, 300, 300, 300, 300], "rangeBurn": "300", "image": {"full": "DariusNoxianTacticsONH.png", "sprite": "spell2.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusAxeGrabCone", "name": "<PERSON>rg<PERSON><PERSON>n", "description": "<PERSON> s<PERSON> seine <PERSON>, wodurch sein normaler Schaden passiv einen Prozentsatz der Rüstung seines Ziels ignoriert. Wird die Fähigkeit aktiviert, sch<PERSON><PERSON><PERSON> Darius seinen Axthaken nach seinen Gegnern und zieht sie zu sich heran.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON> er<PERSON> {{ e1 }}&nbsp;% Rüstungsdurchdringung.<br /><br /><spellActive>Aktiv:</spellActive> <PERSON> schwingt seine Axt, <status>zieht</status> alle <PERSON><PERSON> vor sich heran, <status>schleudert sie hoch</status> und <status>verlangsamt</status> sie {{ e3 }}&nbsp;Sekunden lang um {{ e2 }}&nbsp;%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Prozentuale Rüstungsdurchdringung", "Kosten (@AbilityResourceName@)", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cost }} -> {{ costNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [26, 23.5, 21, 18.5, 16], "cooldownBurn": "26/23.5/21/18.5/16", "cost": [70, 60, 50, 40, 30], "costBurn": "70/60/50/40/30", "datavalues": {}, "effect": [null, [20, 25, 30, 35, 40], [40, 40, 40, 40, 40], [1, 1, 1, 1, 1], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "20/25/30/35/40", "40", "1", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [535, 535, 535, 535, 535], "rangeBurn": "535", "image": {"full": "DariusAxeGrabCone.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "DariusExecute", "name": "Noxianische Guillotine", "description": "<PERSON> springt auf einen gegnerischen Champion zu und führt einen verheerenden Schlag aus, der absoluten Schaden verursacht. Der Schaden erhöht sich mit jeder Steigerung von „Blutung“ des Ziels. Wenn „Noxianische Guillotine“ jemanden tötet, wird ihre Abklingzeit für kurze Zeit zurückgesetzt.", "tooltip": "<PERSON> springt zu einem Gegner und führt einen tödlichen Schlag aus, der <trueDamage>{{ damage }}&nbsp;absoluten Schaden</trueDamage> verursacht. <PERSON><PERSON><PERSON> jede <keywordMajor>Blutung</keywordMajor> auf dem Ziel verursacht diese Fähigkeit zusätzlich {{ rdamagepercentperhemostack*100 }}&nbsp;% <PERSON><PERSON><PERSON>, bis zu einem Maximum von <trueDamage>{{ maximumdamage }}&nbsp;Schaden</trueDamage>.<br /><br />Wird das Ziel getötet, kann Darius die Fähigkeit innerhalb von {{ rrecastduration }}&nbsp;Sekunden <recast>reaktivieren</recast>. Auf Rang&nbsp;3 hat diese Fähigkeit keine Manakosten und Kills setzen die Abklingzeit komplett zurück.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 0], "costBurn": "100/100/0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [460, 460, 460], "rangeBurn": "460", "image": {"full": "DariusExecute.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Blutung", "description": "<PERSON> Angriff<PERSON> und schadensverursachende Fähigkeiten fügen Gegnern eine 5&nbsp;Sekunden lang anhaltende Blutung zu, die normalen Schaden verursacht und bis zu 5-mal gesteigert werden kann. <PERSON> wird wütend und erhält gewaltigen Angriffsschaden, wenn sein Ziel die maximalen Steigerungen erreicht.", "image": {"full": "<PERSON>_<PERSON><PERSON>_Hemorrhage.png", "sprite": "passive0.png", "group": "passive", "x": 240, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}