{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Malphite": {"id": "Malphite", "key": "54", "name": "말파이트", "title": "거석의 파편", "image": {"full": "Malphite.png", "sprite": "champion2.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "54000", "num": 0, "name": "default", "chromas": false}, {"id": "54001", "num": 1, "name": "토끼풀 말파이트", "chromas": false}, {"id": "54002", "num": 2, "name": "산호초 말파이트", "chromas": false}, {"id": "54003", "num": 3, "name": "대리석 말파이트", "chromas": false}, {"id": "54004", "num": 4, "name": "흑요석 말파이트", "chromas": false}, {"id": "54005", "num": 5, "name": "빙하의 말파이트", "chromas": false}, {"id": "54006", "num": 6, "name": "메카 말파이트", "chromas": true}, {"id": "54007", "num": 7, "name": "철갑병기 말파이트", "chromas": false}, {"id": "54016", "num": 16, "name": "오디세이 말파이트", "chromas": true}, {"id": "54023", "num": 23, "name": "암흑의 별 말파이트", "chromas": false}, {"id": "54024", "num": 24, "name": "프레스티지 암흑의 별 말파이트", "chromas": false}, {"id": "54025", "num": 25, "name": "FPX 말파이트", "chromas": true}, {"id": "54027", "num": 27, "name": "고대 신 말파이트", "chromas": true}, {"id": "54037", "num": 37, "name": "달빛 수호자 말파이트", "chromas": true}, {"id": "54048", "num": 48, "name": "수영장 파티 말파이트", "chromas": true}], "lore": "암석으로 이루어진 거대한 자연물 말파이트는 혼란스러운 세상에 평화로운 질서를 가져오기 위해 노력하고 있다. 말파이트는 원래 다른 세계에서 '거석'이라 불리는 돌기둥의 일부이자 거석을 지키는 파수꾼으로 태어났으며, 막강한 자연의 힘으로 거석을 보호하려 했으나 실패하고 말았다. 거석이 파괴된 후, 유일하게 살아남은 말파이트는 이제 룬테라의 물렁물렁한 생명체와 그들의 변덕스러운 성미를 참아내며 마지막 생존자에게 어울리는 새로운 임무를 찾고 있다.", "blurb": "암석으로 이루어진 거대한 자연물 말파이트는 혼란스러운 세상에 평화로운 질서를 가져오기 위해 노력하고 있다. 말파이트는 원래 다른 세계에서 '거석'이라 불리는 돌기둥의 일부이자 거석을 지키는 파수꾼으로 태어났으며, 막강한 자연의 힘으로 거석을 보호하려 했으나 실패하고 말았다. 거석이 파괴된 후, 유일하게 살아남은 말파이트는 이제 룬테라의 물렁물렁한 생명체와 그들의 변덕스러운 성미를 참아내며 마지막 생존자에게 어울리는 새로운 임무를 찾고 있다.", "allytips": ["방어력은 화강암 방패를 뚫고 들어오는 피해를 줄여주므로, 난폭한 일격은 적이 가하는 물리 피해에 대한 방어막을 강화해줍니다.", "말파이트의 스킬은 높은 방어력을 기반으로 위력이 증가하지만, 일부 게임에서는 마법 저항력을 올려야 하기도 합니다. 이럴 경우 군단의 방패, 헤르메스의 발걸음, 수호 천사를 구매하십시오."], "enemytips": ["물리 피해 위주의 챔피언이라면 말파이트와 싸울 때에는 아군 뒤로 피하십시오. 지면 강타에 당하면 가할 수 있는 피해량이 현저하게 감소하기 때문입니다.", "말파이트는 정글링이 가능한 탱커 중 하나입니다. 말파이트에게 강타 주문이 있을 경우 주의하십시오."], "tags": ["Tank", "Mage"], "partype": "마나", "info": {"attack": 5, "defense": 9, "magic": 7, "difficulty": 2}, "stats": {"hp": 665, "hpperlevel": 104, "mp": 280, "mpperlevel": 60, "movespeed": 335, "armor": 37, "armorperlevel": 4.95, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 7, "hpregenperlevel": 0.55, "mpregen": 7.3, "mpregenperlevel": 0.55, "crit": 0, "critperlevel": 0, "attackdamage": 62, "attackdamageperlevel": 4, "attackspeedperlevel": 3.4, "attackspeed": 0.736}, "spells": [{"id": "SeismicShard", "name": "지진의 파편", "description": "말파이트가 땅을 통해 대지의 조각을 날려 피해를 입히며 3초 동안 이동 속도를 훔칩니다.", "tooltip": "말파이트가 대지의 조각을 날려 <magicDamage>{{ qdamagecalc }}의 마법 피해</magicDamage>를 입히며 {{ slowduration }}초 동안 {{ e2 }}% <status>둔화</status>시킵니다. 말파이트는 {{ slowduration }}초 동안 대상의 <speed>이동 속도</speed>도 <status>둔화</status>된 만큼 훔칩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["소모값 @AbilityResourceName@", "피해량", "둔화"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ basedamage }} -> {{ basedamageNL }}", "{{ e2 }}% -> {{ e2NL }}%"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [70, 75, 80, 85, 90], "costBurn": "70/75/80/85/90", "datavalues": {}, "effect": [null, [70, 120, 170, 220, 270], [20, 25, 30, 35, 40], [3, 3, 3, 3, 3], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/120/170/220/270", "20/25/30/35/40", "3", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [625, 625, 625, 625, 625], "rangeBurn": "625", "image": {"full": "SeismicShard.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "Obdu<PERSON>", "name": "천둥소리", "description": "말파이트가 굉음을 내며 공격합니다. 이후 수 초 동안 말파이트의 공격이 전방에 여진을 일으킵니다.", "tooltip": "<spellPassive>기본 지속 효과: </spellPassive>말파이트가 <scaleArmor>{{ bonusarmorpassive*100 }}%의 방어력(%i:scaleArmor%{{ f1 }})</scaleArmor>을 얻습니다. 이 효과는 <spellName>화강암 방패</spellName>가 활성화된 동안 <scaleArmor>{{ bonusarmorpassive*300 }}%(%i:scaleArmor%{{ f2 }})</scaleArmor>까지 증가합니다.<br /><br /><spellPassive>사용 시: </spellPassive>말파이트의 다음 공격이 <physicalDamage>{{ totalbonusdamage }}의 물리 피해</physicalDamage>를 입히고 여진을 생성해 해당 방향에 <physicalDamage>{{ thunderclapsplash }}의 물리 피해</physicalDamage>를 입힙니다. 다음 {{ thunderclapbuffduration }}초 동안 공격 시 여진이 생성됩니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["소모값 @AbilityResourceName@", "방어력", "피해량", "여진 광역 피해량", "재사용 대기시간"], "effect": ["{{ cost }} -> {{ costNL }}", "{{ bonusarmorpassive*100.000000 }}% -> {{ bonusarmorpassivenl*100.000000 }}%", "{{ thunderclapbasedamage }} -> {{ thunderclapbasedamageNL }}", "{{ thunderclapsplashdamage }} -> {{ thunderclapsplashdamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Obduracy.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "Landslide", "name": "지면 강타", "description": "말파이트가 땅을 내리쳐 충격파를 만들어 냅니다. 충격파는 말파이트 방어력의 일부에 해당하는 마법 피해를 입히며, 이에 맞은 적은 잠시 동안 공격 속도가 감소합니다.", "tooltip": "말파이트가 바닥을 내려쳐 주변 적에게 <magicDamage>{{ edamagecalc }}의 마법 피해</magicDamage>를 입히고 {{ duration }}초 동안 적들의 <attackSpeed>공격 속도를 {{ asreduction }}%</attackSpeed> 감소시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "공격 속도 감소량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ asreduction }}% -> {{ asreductionNL }}%"]}, "maxrank": 5, "cooldown": [7, 7, 7, 7, 7], "cooldownBurn": "7", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "Landslide.png", "sprite": "spell8.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "UFSlash", "name": "멈출 수 없는 힘", "description": "말파이트가 목표 위치로 돌진하여 적에게 피해를 입히고 공중으로 띄워 올립니다.", "tooltip": "말파이트가 산사태와 같은 힘으로 돌진하며 저지 불가 상태가 됩니다. 돌진이 끝나면 말파이트가 {{ knockupduration }}초 동안 대상을 <status>공중으로 띄워 올리고</status> <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["재사용 대기시간", "피해량"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ e2 }} -> {{ e2NL }}"]}, "maxrank": 3, "cooldown": [130, 105, 80], "cooldownBurn": "130/105/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [1.5, 1.75, 2], [200, 300, 400], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "1.5/1.75/2", "200/300/400", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "UFSlash.png", "sprite": "spell8.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "화강암 방패", "description": "말파이트는 여러 겹의 돌로 감싸여 있어 자신의 최대 체력 10%만큼의 피해를 흡수할 수 있습니다. 말파이트가 몇 초 동안 공격 받지 않으면 보호막이 재생성됩니다.", "image": {"full": "Malphite_GraniteShield.png", "sprite": "passive2.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}