{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Samira": {"id": "<PERSON><PERSON>", "key": "360", "name": "<PERSON><PERSON>", "title": "die Wüstenrose", "image": {"full": "Samira.png", "sprite": "champion3.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "360000", "num": 0, "name": "default", "chromas": false}, {"id": "360001", "num": 1, "name": "PsyOps-<PERSON><PERSON>", "chromas": true}, {"id": "360010", "num": 10, "name": "Weltraum-Groove-<PERSON><PERSON>", "chromas": true}, {"id": "360020", "num": 20, "name": "High Noon-Samira", "chromas": true}, {"id": "360030", "num": 30, "name": "Soul Fighter Samira", "chromas": false}, {"id": "360033", "num": 33, "name": "<PERSON><PERSON> der Schwarzen <PERSON>", "chromas": false}], "lore": "<PERSON><PERSON> blickt dem Tod mit unnachgiebigem Selbstbewusstsein ins Auge und sucht überall nach Nervenkitzel. Nachdem ihr shurimanisches Zuhause zerstört wurde, als sie noch ein Kind war, fand sie ihre wahre Berufung in Noxus, wo sie als stilsichere Draufgängerin an gefährlichen Missionen des höchsten Kalibers teilnahm. Mit Schwarzpulver-Pistolen und einer personalisierten Klinge strebt Samira nach lebensgefährlichen Situationen und eliminiert alle, die sich ihr in den Weg stellen.", "blurb": "<PERSON><PERSON> blickt dem Tod mit unnachgiebigem Selbstbewusstsein ins Auge und sucht überall nach Nervenkitzel. Nachdem ihr shurimanisches Zuhause zerstört wurde, als sie noch ein Kind war, fand sie ihre wahre Berufung in Noxus, wo sie als stilsichere...", "allytips": [], "enemytips": [], "tags": ["Marksman", "Assassin"], "partype": "<PERSON><PERSON>", "info": {"attack": 8, "defense": 5, "magic": 3, "difficulty": 6}, "stats": {"hp": 630, "hpperlevel": 108, "mp": 349, "mpperlevel": 38, "movespeed": 335, "armor": 26, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 3.25, "hpregenperlevel": 0.55, "mpregen": 8.2, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 57, "attackdamageperlevel": 3, "attackspeedperlevel": 3.3, "attackspeed": 0.658}, "spells": [{"id": "SamiraQ", "name": "<PERSON><PERSON>", "description": "<PERSON>ra feuert einen Schuss ab oder schwingt ihr Schwert und verursacht Schaden. Wird die Fähigkeit während „Ungezügelter Rausch“ eingesetzt, trifft sie beim Abschluss alle Gegner auf ihrem Weg.", "tooltip": "Samira feuert einen <PERSON> ab, der dem ersten getroffenen Gegner <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage> zufügt.<br /><br />Wenn diese Fähigkeit in Nahkampfreichweite gewirkt wird, greift Samira stattdessen mit ihrem Schwert an und verursacht <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage>. {{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Skalierung mit Gesamtangriffsschaden"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ qadratio*100.000000 }}&nbsp;% -> {{ qadrationl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [6, 5, 4, 3, 2], "cooldownBurn": "6/5/4/3/2", "cost": [30, 30, 30, 30, 30], "costBurn": "30", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [950, 950, 950, 950, 950], "rangeBurn": "950", "image": {"full": "SamiraQ.png", "sprite": "spell11.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraW", "name": "Klingenwirb<PERSON>", "description": "<PERSON>ra wirbelt ihr Schwert um sich herum, fügt <PERSON>en zu und zerstört gegnerische Geschosse.", "tooltip": "Samira lässt {{ slashduration }}&nbsp;Sekunden lang ihr Schwert um sich herumwirbeln, fügt <PERSON> zweimal je <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage> zu und zerstört gegnerische Geschosse, die in den Bereich eintreten.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [30, 28, 26, 24, 22], "cooldownBurn": "30/28/26/24/22", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [325, 325, 325, 325, 325], "rangeBurn": "325", "image": {"full": "SamiraW.png", "sprite": "spell11.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraE", "name": "Ungezügelter Rausch", "description": "<PERSON>ra springt durch e<PERSON> Gegner (einschließlich Gebäude), fügt dabei getroffenen Gegnern Schaden zu und erhält Angriffstempo. Wenn ein gegnerischer Champion getötet wird, wird die Abklingzeit dieser Fähigkeit zurückgesetzt.", "tooltip": "<PERSON>ra springt durch e<PERSON> Gegner (einschließlich Gebäude), fügt dabei getroffenen Gegnern <magicDamage>{{ dashdamage }}&nbsp;magischen Schaden</magicDamage> zu und erhält {{ attackspeedduration }}&nbsp;Sekunden lang <attackSpeed>{{ bonusattackspeed*100 }}&nbsp;% Angriffstempo</attackSpeed>. <br /><br />Die Abklingzeit dieser Fähigkeit wird zurückgesetzt, wenn ein gegnerischer Champion innerhalb von 3&nbsp;Sekunden getötet wird, nachdem er durch Samira Schaden erlitten hat.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Angriffstempo"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ bonusattackspeed*100.000000 }}&nbsp;% -> {{ bonusattackspeednl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraE.png", "sprite": "spell11.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SamiraR", "name": "Infernaler Abzug", "description": "<PERSON><PERSON> entfesselt einen Kugelhagel aus ihren Waffen, der alle Gegner um sie herum trifft.", "tooltip": "<PERSON>ra kann diese Fähigkeit nur einsetzen, wenn ihr aktueller <keywordMajor>Style</keywordMajor>-Rang S beträgt. Bei ihrem Einsatz werden alle <keywordMajor>Style</keywordMajor>-Ränge verbraucht.<br /><br /><PERSON>ra feuert eine Kugelsalve aus ihren Waffen ab, die alle Gegner um sie herum über 2 Sekunden hinweg 10-mal trifft, wobei jeder <PERSON> <physicalDamage>{{ damagecalc }}&nbsp;normalen Schaden</physicalDamage> verursacht und Lebensraub mit {{ lifestealmod*100 }}&nbsp;% Effektivität auslöst. Jeder Schuss kann zudem kritisch treffen.<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [5, 5, 5], "cooldownBurn": "5", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600], "rangeBurn": "600", "image": {"full": "SamiraR.png", "sprite": "spell11.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Draufg<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> baut eine <PERSON> auf, indem sie mit Angriffen oder Fähigkeiten trifft, ohne etwas zweimal hintereinander einzusetzen. <PERSON><PERSON> Ang<PERSON> in Nahkampfreichweite verursachen zusätzlichen magischen Schaden. Greift Samira Gegner an, die von <status>bewegungsunfähig machenden</status> Effekten betroffen sind, sprintet sie in ihre Angriffsreichweite. Wenn der Gegner <status>hochgeschleudert</status> wurde, hält sie ihn zudem für kurze Zeit <status>hochgeschleudert</status>.", "image": {"full": "SamiraP.png", "sprite": "passive3.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}