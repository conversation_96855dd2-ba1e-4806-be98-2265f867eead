{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fizz": {"id": "Fizz", "key": "105", "name": "Fizz", "title": "the Tidal Trickster", "image": {"full": "Fizz.png", "sprite": "champion1.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "105000", "num": 0, "name": "default", "chromas": true}, {"id": "105001", "num": 1, "name": "Atlantean Fizz", "chromas": false}, {"id": "105002", "num": 2, "name": "Tundra Fizz", "chromas": false}, {"id": "105003", "num": 3, "name": "Fisherman Fizz", "chromas": false}, {"id": "105004", "num": 4, "name": "Void Fizz", "chromas": false}, {"id": "105008", "num": 8, "name": "Cottontail Fizz", "chromas": false}, {"id": "105009", "num": 9, "name": "Super Galaxy Fizz", "chromas": false}, {"id": "105010", "num": 10, "name": "Omega Squad Fizz", "chromas": true}, {"id": "105014", "num": 14, "name": "Fuzz Fizz", "chromas": false}, {"id": "105015", "num": 15, "name": "Prestige Fuzz Fizz", "chromas": false}, {"id": "105016", "num": 16, "name": "Little Devil Fizz", "chromas": true}, {"id": "105025", "num": 25, "name": "Prestige Fuzz Fizz (2022)", "chromas": false}, {"id": "105026", "num": 26, "name": "Astronaut Fizz", "chromas": true}, {"id": "105035", "num": 35, "name": "Rain Shepherd Fizz", "chromas": true}], "lore": "Fizz adalah yordle amfibi. Dia tinggal di antara terumbu karang di sekitar Bilgewater. Dia kerap mengambil dan mengembalikan perpuluhan yang dilemparkan ke laut oleh kapten yang percaya takhayul. Bahkan pelaut berpengalaman pun memilih untuk tak melewatinya, karena sudah banyak kisah tentang mereka yang meremehkan sosok licin ini. Sering disalahartikan sebagai roh laut yang sering berubah-ubah, dia tampaknya mampu memerintah monster di laut dalam, juga senang mengacaukan sekutu dan musuhnya.", "blurb": "Fizz adalah yordle amfibi. Dia tinggal di antara terumbu karang di sekitar Bilgewater. Dia kerap mengambil dan mengembalikan perpuluhan yang dilemparkan ke laut oleh kapten yang percaya takhayul. Bahkan pelaut berpengalaman pun memilih untuk tak...", "allytips": ["Karena Fizz bisa bergerak menembus unit, cari kesempatan di jalur untuk jalan melewati minion dan menerapkan pasif Seastone Trident. Lanjutkan dengan serangan aktif ability beberapa detik kemudian.", "Ability Ultima Fizz, <PERSON><PERSON> the <PERSON>, bisa dibidik ke musuh atau ke area yang kamu pikir akan mereka tuju.", "Spell Fizz meningkat sesuai Ability Power. Pertimbangkan item seperti Zhonya's Hourglass atau Banshee's Veil melawan tim dengan burst tinggi, dan item seperti Lich Bane atau Rabadon's Deathcap jika kamu pikir bisa bertahan tanpa Health tambahan."], "enemytips": ["Serangan Fizz menjadi lebih berbahaya setelah dia menggunakan serangan yang diperkuat. Pastikan untuk menjauh ketika trisulanya menyala!", "Fizz bisa menjadi sasaran yang sulit saat Ability-nya tidak cooldown. Jebak dia agar mengguna<PERSON><PERSON> lebih dulu, kem<PERSON>an ikuti dengan crowd control atau serangan kuat!"], "tags": ["Assassin", "Fighter"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 4, "magic": 7, "difficulty": 6}, "stats": {"hp": 640, "hpperlevel": 106, "mp": 317, "mpperlevel": 52, "movespeed": 335, "armor": 22, "armorperlevel": 4.6, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8, "hpregenperlevel": 0.7, "mpregen": 6, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 58, "attackdamageperlevel": 3, "attackspeedperlevel": 3.1, "attackspeed": 0.658}, "spells": [{"id": "FizzQ", "name": "Urchin Strike", "description": "Fizz melakukan dash menuju target, menghasilkan magic damage dan men<PERSON><PERSON><PERSON> efek on-hit.", "tooltip": "Fizz melakukan dash melewati musuh, <PERSON><PERSON><PERSON><PERSON><PERSON> <physicalDamage>{{ totaldamage }} physical damage</physicalDamage> plus <magicDamage>{{ qdamage }} magic damage</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown"], "effect": ["{{ e1 }}-> {{ e1NL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [50, 50, 50, 50, 50], "costBurn": "50", "datavalues": {}, "effect": [null, [10, 25, 40, 55, 70], [0, 0, 0, 0, 0], [650, 750, 850, 950, 1050], [1.5, 1.5, 1.5, 1.5, 1.5], [600, 600, 600, 600, 600], [1, 1, 1, 1, 1], [0.35, 0.35, 0.35, 0.35, 0.35], [40, 40, 40, 40, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "10/25/40/55/70", "0", "650/750/850/950/1050", "1.5", "600", "1", "0.35", "40", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [550, 550, 550, 550, 550], "rangeBurn": "550", "image": {"full": "FizzQ.png", "sprite": "spell3.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzW", "name": "Seastone Trident", "description": "Serangan Fizz melukai musuh, mengh<PERSON>lkan magic damage selama beberapa detik. Fizz bisa memperkuat serangan berikutnya, menghasilkan damage bonus dan memperkuat serangan selanjutnya dalam waktu singkat.", "tooltip": "<spellPassive>Pasif</spellPassive>: Serangan Fizz menyebabkan musuh terluka, <PERSON><PERSON><PERSON><PERSON>an <magicDamage>{{ dotdamage }} magic damage</magicDamage> selama {{ bleedduration }} detik. <br /><br /><spellActive>Aktif</spellActive>: Serangan Fizz berikutnya menghasilkan <magicDamage>{{ activedamage }} magic damage</magicDamage> tambahan. Jika Serangan ini berhasil kill target, Fizz me-refund <scaleMana>{{ onkillmanarefund }} Mana</scaleMana> dan mengurangi cooldown Ability ini menjadi {{ onkillnewcooldown }} detik. Jika gagal kill, Serangan Fizz menghasilkan tambahan <magicDamage>{{ onhitbuffdamage }} magic damage</magicDamage> selama {{ onhitbuffduration }} detik.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage Pasif", "Damage Aktif", "Damage On-Hit", "Penge<PERSON><PERSON>", "@AbilityResourceName@ Cost", "Cooldown"], "effect": ["{{ dotbasedamage }}-> {{ dotbasedamageNL }}", "{{ activebasedamage }}-> {{ activebasedamageNL }}", "{{ onhitbuffbasedamage }}-> {{ onhitbuffbasedamageNL }}", "{{ onkillmanarefund }}-> {{ onkillmanarefundNL }}", "{{ cost }}-> {{ costNL }}", "{{ cooldown }}-> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [7, 6, 5, 4, 3], "cooldownBurn": "7/6/5/4/3", "cost": [30, 40, 50, 60, 70], "costBurn": "30/40/50/60/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "FizzW.png", "sprite": "spell3.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzE", "name": "Playful/Trickster", "description": "Fizz melompat ke udara, mendarat dengan anggun di atas tombaknya, dan membuatnya tak bisa ditarget. <PERSON>i posisi ini, Fizz bisa menghantam tanah atau memilih untuk melompat lagi sebelum menghantam tanah.", "tooltip": "Fizz melompat ke trisula, menjadi tak bisa ditarget selama 0,75 detik, set<PERSON><PERSON><PERSON> akan mengh<PERSON>lkan <magicDamage>{{ edamage }} magic damage</magicDamage> ke musuh di sekitar dan menerapkan <status>Slow</status> sebesar {{ slowamount*100 }}% selama {{ slowduration }} detik. <br /><br />Fizz bisa <recast>Recast</recast> Ability ini saat tak bisa ditarget untuk kembali melakukan dash, yang akan mengakhiri efek ini lebih awal, menghasilkan damage di area yang lebih sempit, dan tidak menerapkan <status>Slow</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "@AbilityResourceName@ Cost", "Damage", "Slow"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ cost }}-> {{ costNL }}", "{{ basedamage }}-> {{ basedamageNL }}", "{{ slowamount*100.000000 }}%-> {{ slowamountnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 14, 12, 10, 8], "cooldownBurn": "16/14/12/10/8", "cost": [75, 80, 85, 90, 95], "costBurn": "75/80/85/90/95", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "FizzE.png", "sprite": "spell4.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FizzR", "name": "Chum the Waters", "description": "Fizz melemparkan ikan yang menempel pada champion yang kena, menerapkan slow pada target. <PERSON><PERSON><PERSON> jeda singkat, hiu akan muncul dari tanah, mengempas target ke atas, dan memukul tiap musuh di sekitar ke samping. Semua musuh yang kena akan menerima magic damage dan terkena slow.", "tooltip": "Fizz meluncurkan ikan yang akan menempel pada champion pertama yang terkena. Korban akan terkena <keywordStealth>True Sight</keywordStealth> dan <status>Slow</status> sebesar 40% hingga 80% berdasarkan seberapa jauh ikan meluncur sebelum menempel. <br /><br />Setelah {{ detonationtime }} detik hiu mengerupsi target, menerapkan <status>Knock Up</status> pada target yang tertempel ikan selama 1 detik, menerapkan <status>Knock Back</status> ke yang lainnya, dan menghasilkan antara <magicDamage>{{ smallsharkdamage }} hingga {{ bigsharkdamage }} magic damage</magicDamage> berdasarkan seberapa jauh ikan meluncur sebelum menempel.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Cooldown", "Small Shark Damage", "Medium Shark Damage", "Big Shark Damage"], "effect": ["{{ cooldown }}-> {{ cooldownNL }}", "{{ smalldamage }}-> {{ smalldamageNL }}", "{{ middamage }}-> {{ middamageNL }}", "{{ bigdamage }}-> {{ bigdamageNL }}"]}, "maxrank": 3, "cooldown": [100, 85, 70], "cooldownBurn": "100/85/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1300, 1300, 1300], "rangeBurn": "1300", "image": {"full": "FizzR.png", "sprite": "spell4.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Nimble Fighter", "description": "Fizz dapat bergerak melewati unit dan menerima jumlah damage dikurangi dari semua sumber.", "image": {"full": "Fizz_P.png", "sprite": "passive1.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}