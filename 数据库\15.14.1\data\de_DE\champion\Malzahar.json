{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Malzahar": {"id": "<PERSON><PERSON><PERSON>", "key": "90", "name": "<PERSON><PERSON><PERSON>", "title": "der Prophet der <PERSON>", "image": {"full": "Malzahar.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "90000", "num": 0, "name": "default", "chromas": false}, {"id": "90001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "90002", "num": 2, "name": "Schattenprinz Malzahar", "chromas": false}, {"id": "90003", "num": 3, "name": "Dschinn-Malzahar", "chromas": false}, {"id": "90004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "90005", "num": 5, "name": "Schneetag-Malzahar", "chromas": true}, {"id": "90006", "num": 6, "name": "Schlachtboss-Malzahar", "chromas": false}, {"id": "90007", "num": 7, "name": "Hextech-Malzahar", "chromas": false}, {"id": "90009", "num": 9, "name": "Weltenbrecher-Malzahar", "chromas": true}, {"id": "90018", "num": 18, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "90028", "num": 28, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "90038", "num": 38, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "90039", "num": 39, "name": "Ely<PERSON><PERSON>", "chromas": true}, {"id": "90049", "num": 49, "name": "Schicksalsbrecher Malzahar", "chromas": true}], "lore": "Malzahar ist ein fanatischer <PERSON>her, der sich der Vereinigung allen Lebens verschrieben hat. E<PERSON> glau<PERSON>, die wiedererstarkte Leere sei der Pfad zu Runeterras Erlösung. In den Wüstenebenen von Shurima folgte er den Stimmen, die in seinem Verstand flüsterten, den ganzen Weg bis zum alten Icathia. Inmitten der Ruinen des Landes starrte er in das dunkle Herz des Ursprungs der Leere, die ihm neue Kräfte und eine neue Aufgabe gab. Malzahar sieht sich jetzt als Führer, der andere zu seiner Vision bekehrt … oder die Geschöpfe der Leere, die dort unten lauern, freilässt.", "blurb": "Malzahar ist ein fanatischer Seher, der sich der Vereinigung allen Lebens verschrieben hat. <PERSON><PERSON> glaubt, die wiedererstarkte Leere sei der Pfad zu Runeterras Erlösung. In den Wüstenebenen von Shurima folgte er den Stimmen, die in seinem Verstand...", "allytips": ["Warte mit der Nutzung des Leerenschwarms, bis Gegner nahe sind, die der Begleiter der Leere angreifen oder töten kann.", "<PERSON>utze Ruf der Leere und Leerengriff, um die Dauer der Üblen Visionen auf Feinden aufzufrischen.", "<PERSON><PERSON><PERSON> in der Lane zu vermeiden, maximiert die Zeit, in der Leerenverschiebung aktiv ist, was <PERSON>zahars Sicherheit massiv erhöht."], "enemytips": ["<PERSON><PERSON> mit einem Zauber Gegner trifft, die von Üblen Visionen betroffen sind, wird deren Dauer aufgefrischt.", "<PERSON><PERSON>, die von Üblen Visionen betroffen sind. Du weißt nie, wann sie sterben und die Visionen an dich weiterreichen.", "<PERSON>za<PERSON> ist besonders g<PERSON><PERSON><PERSON><PERSON>, wenn er seinen Leerenschwarm aufgebaut hat."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 6}, "stats": {"hp": 580, "hpperlevel": 101, "mp": 375, "mpperlevel": 28, "movespeed": 335, "armor": 18, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "MalzaharQ", "name": "<PERSON><PERSON>", "description": "Malzahar öffnet 2 Portale in die Leere. Nach kurzer Zeit fliegen Geschosse aus diesen Portalen in das jeweils andere, die an allen getroffenen Gegnern dazwischen Schaden verursachen und Champions zum Schweigen bringen.", "tooltip": "Malzahar ö<PERSON>net zwei Portale in die Leere. Nach kurzer Zeit fliegen Geschosse aus diesen Portalen, die Gegnern <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage> zufügen und sie {{ e2 }}&nbsp;Sekunde(n) lang <status>verstummen</status> lassen.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Kosten (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [1, 1.25, 1.5, 1.75, 2], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "1/1.25/1.5/1.75/2", "0.4", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "MalzaharQ.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MalzaharW", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Malzahar ruft Begleiter der Leere herbei, die nahe Gegner angreifen.", "tooltip": "<spellPassive>Passiv:</spellPassive> Malzahars andere Fähigkeiten gewähren ihm eine Steigerung, wenn sie aktiviert werden (maximal {{ stackcap }}).<br /><br /><spellActive>Aktiv:</spellActive> Malzahar beschwört einen Begleiter der Leere und einen zusätzlichen Begleiter für jede Steigerung. Begleiter der Leere bleiben {{ voidlingduration }}&nbsp;Sekunden lang bestehen und verursachen <magicDamage>{{ voidlingbonusdamagetooltip }}&nbsp;magischen Schaden</magicDamage> pro Treffer.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden der Begleiter der Leere", "Begleiter der Leere: Lebensdauer", "Kosten (@AbilityResourceName@)"], "effect": ["{{ voidlingbasedamage }} -> {{ voidlingbasedamageNL }}", "{{ voidlingduration }} -> {{ voidlingdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [25000, 25000, 25000, 25000, 25000], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "25000", "2", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [150, 150, 150, 150, 150], "rangeBurn": "150", "image": {"full": "MalzaharW.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MalzaharE", "name": "Üble Visionen", "description": "Malzahar infiziert den Verstand seines Ziels mit grausamen Visionen und verursacht so Schaden. Werden Malzahars andere Zauber auf das Ziel genutzt, frischt dies die Visionen auf.<br><br>Stirbt das Ziel, während es von Visionen betroffen ist, springen diese auf einen nahe stehenden Gegner über, um diesen heimzusuchen und Malzahar erhält Mana. Malzahars Begleiter der Leere greifen bevorzugt Ziele an, die unter Visionen leiden.", "tooltip": "Malzahar löst schreckliche Visionen aus, die über {{ duration }}&nbsp;Sekunden hinweg <magicDamage>{{ totaldamage }}&nbsp;magischen <PERSON>haden</magicDamage> verursachen. Wenn das Opfer während dieser Zeit mit <spellName><PERSON>uf <PERSON>re</spellName> oder <spellName>Leerengriff</spellName> belegt wird, erneuern sich die Visionen.<br /><br />Stirbt das Opfer, erhält Malzahar <scaleMana>{{ manarestore }}&nbsp;Mana</scaleMana> und die Visionen springen auf den nächsten Gegner über.<br /><br /><rules>„Üble Visionen“ exekutiert Vasallen mit weniger als {{ minionexecutethreshold }}&nbsp;Leben.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "8", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MalzaharE.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MalzaharR", "name": "<PERSON>rengriff", "description": "Malzahar konzentriert die Essenz der Leere, um einen gegnerischen Champion in einer Zone schädigender negativer Energie zu unterdrücken.", "tooltip": "Malzahar <status>unter<PERSON><PERSON>t</status> einen gegnerischen Champion und fügt ihm über {{ e4 }}&nbsp;Sekunden <magicDamage>{{ totaldamagetooltip }}&nbsp;magischen Schaden</magicDamage> zu. Er erzeugt ein Feld aus negativer Energie um sein Ziel herum, das diesem über {{ e3 }}&nbsp;Sekunden hinweg <magicDamage>{{ zonedamagetooltip }} seines max. Lebens als magischen Schaden</magicDamage> zufügt.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Leerengriff: <PERSON><PERSON><PERSON>", "Leerenfeld: Schaden pro Sekunde", "Abklingzeit"], "effect": ["{{ beamdamage }} -> {{ beamdamageNL }}", "{{ maxhealthdamage }}&nbsp;% -> {{ maxhealthdamageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [5, 5, 5], [2.5, 2.5, 2.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "5", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MalzaharR.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Leerenverschiebung", "description": "<PERSON>n Malzahar eine Zeit lang weder Schaden erleidet noch von Massenkontrolle betroffen ist, erhält er enorme Schadensverringerung und Immunität gegen Massenkontrolle, die noch kurz anhält, nachdem er Schaden erlitten hat.", "image": {"full": "Malzahar_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}