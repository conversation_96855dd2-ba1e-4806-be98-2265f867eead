{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"TwistedFate": {"id": "TwistedFate", "key": "4", "name": "Twisted Fate", "title": "<PERSON><PERSON><PERSON>", "image": {"full": "TwistedFate.png", "sprite": "champion4.png", "group": "champion", "x": 48, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "4000", "num": 0, "name": "default", "chromas": false}, {"id": "4001", "num": 1, "name": "Twisted Fate z PAX", "chromas": false}, {"id": "4002", "num": 2, "name": "W<PERSON> <PERSON><PERSON>", "chromas": false}, {"id": "4003", "num": 3, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON> Twisted <PERSON>", "chromas": false}, {"id": "4004", "num": 4, "name": "Tango Twisted Fate", "chromas": false}, {"id": "4005", "num": 5, "name": "Twisted Fate w Samo Południe", "chromas": false}, {"id": "4006", "num": 6, "name": "Twisted <PERSON>", "chromas": false}, {"id": "4007", "num": 7, "name": "Twisted Fate z Podziemi", "chromas": false}, {"id": "4008", "num": 8, "name": "Twisted Fate Sędzia", "chromas": false}, {"id": "4009", "num": 9, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "4010", "num": 10, "name": "Twisted Fate Krwawego Księżyca", "chromas": false}, {"id": "4011", "num": 11, "name": "Cybernetyczny Twisted Fate", "chromas": true}, {"id": "4013", "num": 13, "name": "Twisted Fate z Odysei", "chromas": true}, {"id": "4023", "num": 23, "name": "DWG Twisted Fate", "chromas": true}, {"id": "4025", "num": 25, "name": "Twisted Fate z Koszmaru Miasta Zbrodni", "chromas": true}, {"id": "4036", "num": 36, "name": "Twisted Fate na Kosmofazie", "chromas": false}, {"id": "4045", "num": 45, "name": "Zwycięski Twisted Fate", "chromas": false}], "lore": "Twisted Fate to ni<PERSON><PERSON><PERSON> szuler i oszust, k<PERSON><PERSON><PERSON> w<PERSON>, co chce, zdobywa hazardem i urokiem. Zapracował sobie przez to zarówno na wrogość, jak i podziw bogatych i głupich. Rzadko kiedy zachowuje powagę, witaj<PERSON>c każdy dzień prześmiewczym uśmieszkiem i niefrasobliwym nadęciem. W każdym możliwym znaczeniu tego słowa, Twisted Fate zawsze ma asa w rękawie.", "blurb": "Twisted Fate to ni<PERSON><PERSON><PERSON> szuler i oszust, k<PERSON><PERSON><PERSON> w<PERSON>, co chce, zdobywa hazardem i urokiem. Zapracował sobie przez to zarówno na wrogość, jak i podziw bogatych i głupich. Rzadko kiedy zachowuje powagę, wit<PERSON><PERSON><PERSON> każdy dzień prześmiewczym uśmieszkiem i...", "allytips": ["Zaplanuj użycie Przeznaczenia tak, aby skoord<PERSON>ć zasadzkę wraz ze swoją drużyną.", "Bohaterowie potrafiący się ukrywać często uciekają z pola bitwy, mając mało zdrowia. Użyj Przeznaczenia, aby ich ujawnić i wykończyć.", "Twisted Fate może korzystać zarówno z obrażeń od ataku, jaki i mocy umiejętności, co czyni z niego bardzo wszechstronnego bohatera."], "enemytips": ["Uważaj na Jokery we wczesnym stadium gry, gdy twój bohater ma mało zdrowia.", "Je<PERSON><PERSON> masz mało zdrowia, użyj Przeznaczenia do przygotowania ucieczki. Da ci to przewagę w razie zasadzki."], "tags": ["Mage", "Marksman"], "partype": "<PERSON><PERSON>", "info": {"attack": 6, "defense": 2, "magic": 6, "difficulty": 9}, "stats": {"hp": 604, "hpperlevel": 108, "mp": 333, "mpperlevel": 39, "movespeed": 330, "armor": 24, "armorperlevel": 4.35, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 525, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2.5, "attackspeed": 0.625}, "spells": [{"id": "WildCards", "name": "<PERSON><PERSON>", "description": "Twisted Fate rzuca trzy karty, które zadają obrażenia każdemu wrogowi, przez którego przelecą.", "tooltip": "Twisted Fate r<PERSON><PERSON> trzema karta<PERSON>, s<PERSON><PERSON><PERSON><PERSON><PERSON> kt<PERSON><PERSON>ch każda zadaje <magicDamage>{{ totaldamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.75, 5.5, 5.25, 5], "cooldownBurn": "6/5.75/5.5/5.25/5", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [10000, 10000, 10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "WildCards.png", "sprite": "spell14.png", "group": "spell", "x": 96, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "Pick<PERSON>ard", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Twisted Fate wybiera magiczną kartę z talii oraz używa jej do następnego ataku. Karta ma dodatkowe efekty.", "tooltip": "Twisted Fate zaczyna taso<PERSON> swoj<PERSON> tali<PERSON>, d<PERSON><PERSON><PERSON> czemu mo<PERSON> <recast>ponown<PERSON> u<PERSON></recast> tej umie<PERSON>, by w<PERSON><PERSON><PERSON> jedn<PERSON> z trzech kart, w<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> swój następny atak.<br /><li>Niebieska Karta zadaje <magicDamage>{{ bluedamage }} pkt. obrażeń magicznych</magicDamage> i przywraca <scaleMana>{{ e6 }} pkt. many</scaleMana>.<li>Czerwon<PERSON> Karta zadaje <magicDamage>{{ reddamage }} pkt. obrażeń magicznych</magicDamage> pobliskim wrogom i <status>spowalnia</status> ich o {{ e2 }}% na 2,5 sek.<li>Złota Karta zadaje <magicDamage>{{ golddamage }} pkt. obrażeń magicznych</magicDamage> i <status>ogłusza</status> na {{ e3 }} sek.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia Niebieskiej Karty", "Przywrócenie many Niebieskiej Karty", "Obrażenia Czerwonej Karty", "Procent spo<PERSON>lnienia Czerwonej Karty", "Obrażenia Złotej Karty", "Czas działania ogłuszenia Złotej Karty", "Koszt (@AbilityResourceName@)"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e6 }} -> {{ e6NL }}", "{{ e4 }} -> {{ e4NL }}", "{{ e2 }}% -> {{ e2NL }}%", "{{ e5 }} -> {{ e5NL }}", "{{ e3 }} -> {{ e3NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [40, 60, 80, 100, 120], [30, 35, 40, 45, 50], [1, 1.25, 1.5, 1.75, 2], [30, 45, 60, 75, 90], [15, 22.5, 30, 37.5, 45], [70, 90, 110, 130, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "40/60/80/100/120", "30/35/40/45/50", "1/1.25/1.5/1.75/2", "30/45/60/75/90", "15/22.5/30/37.5/45", "70/90/110/130/150", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [200, 200, 200, 200, 200], "rangeBurn": "200", "image": {"full": "PickACard.png", "sprite": "spell14.png", "group": "spell", "x": 144, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "CardmasterStack", "name": "<PERSON> <PERSON> Rękawa", "description": "Co 4 ataki Twisted Fate zadaje dodatkowe obrażenia. Ponadto ma zwiększoną prędkość ataku.", "tooltip": "<spellPassive>B<PERSON><PERSON>:</spellPassive> Twisted <PERSON> zyskuje <attackSpeed>{{ attackspeedbonus }}% prędko<PERSON>ci ataku</attackSpeed>, a każdy co 4. atak zadaje dodatkowo <magicDamage>{{ bonusdamage }} pkt. obrażeń magicznych</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Dodatkowe obrażenia", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku"], "effect": ["{{ damage }} -> {{ damageNL }}", "{{ attackspeedbonus }}% -> {{ attackspeedbonusNL }}%"]}, "maxrank": 5, "cooldown": [0, 0, 0, 0, 0], "cooldownBurn": "0", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [4, 4, 4, 4, 4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "4", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON><PERSON>", "maxammo": "-1", "range": [0, 0, 0, 0, 0], "rangeBurn": "0", "image": {"full": "CardmasterStack.png", "sprite": "spell14.png", "group": "spell", "x": 192, "y": 96, "w": 48, "h": 48}, "resource": "<PERSON><PERSON><PERSON>"}, {"id": "Destiny", "name": "Przeznaczenie", "description": "Twisted Fate przewiduje przyszło<PERSON>ć swoich wrogów, ujawniając ich położenie na mapie i pozwalając na użycie Bramy, która teleportuje go w wybrane miejsce w 1,5 sekundy.", "tooltip": "Twisted Fate skupia się na swoich kartach, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <keywordStealth>prawdziwe widzenie</keywordStealth> wszyst<PERSON><PERSON> wrogich bohaterów na mapie na {{ e1 }} sek. i może <recast>ponownie użyć</recast> tej umie<PERSON>.<br /><br /><recast>Ponowne użycie:</recast> Twisted Fate teleportuje się na odległość maks. {{ e4 }} jedn.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Czas działania", "Czas odnowienia"], "effect": ["{{ recastduration }} -> {{ recastdurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [170, 140, 110], "cooldownBurn": "170/140/110", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [6, 8, 10], [0, 0, 0], [0, 0, 0], [5500, 5500, 5500], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "6/8/10", "0", "0", "5500", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [5500, 5500, 5500], "rangeBurn": "5500", "image": {"full": "Destiny.png", "sprite": "spell14.png", "group": "spell", "x": 240, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Sprzyjające Szczęście", "description": "Po zabiciu jednostki Twisted Fate rzuca swoją „szczęśliwą” kością i otrzymuje dodatkowo 1-6 szt. złota.", "image": {"full": "Cardmaster_SealFate.png", "sprite": "passive4.png", "group": "passive", "x": 48, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}