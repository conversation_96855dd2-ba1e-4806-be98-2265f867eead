{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"MonkeyKing": {"id": "MonkeyKing", "key": "62", "name": "ウーコン", "title": "美猴王", "image": {"full": "MonkeyKing.png", "sprite": "champion2.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "62000", "num": 0, "name": "default", "chromas": false}, {"id": "62001", "num": 1, "name": "火山岩ウーコン", "chromas": false}, {"id": "62002", "num": 2, "name": "ウーコン大将", "chromas": false}, {"id": "62003", "num": 3, "name": "翡翠龍ウーコン", "chromas": true}, {"id": "62004", "num": 4, "name": "冥府の世界ウーコン", "chromas": false}, {"id": "62005", "num": 5, "name": "暁光のウーコン", "chromas": false}, {"id": "62006", "num": 6, "name": "ランサーストラタス ウーコン", "chromas": false}, {"id": "62007", "num": 7, "name": "バトルアカデミア ウーコン", "chromas": true}, {"id": "62016", "num": 16, "name": "古の賢樹ウーコン", "chromas": true}], "lore": "ヴァスタヤのトリックスターであるウーコンは、自身の強さと俊敏さ、さらに賢さを駆使して敵を翻弄し、優位に立って戦うことを得意とする。彼はマスター・イーという名の戦士を生涯の友として見出し、古来より伝わる伝説の武術「ウージュー」を学ぶ最後の弟子となった。魔法の棍を得物に、ウーコンはアイオニアを滅亡から守るための手段を探し求めている。", "blurb": "ヴァスタヤのトリックスターであるウーコンは、自身の強さと俊敏さ、さらに賢さを駆使して敵を翻弄し、優位に立って戦うことを得意とする。彼はマスター・イーという名の戦士を生涯の友として見出し、古来より伝わる伝説の武術「ウージュー」を学ぶ最後の弟子となった。魔法の棍を得物に、ウーコンはアイオニアを滅亡から守るための手段を探し求めている。", "allytips": ["「代わり身の術」と「乱像猿技」は組み合わせて使うと効果的。素早く敵を攻撃し、反撃される前に退散できる。", "茂みの近くで「代わり身の術」を使えば、敵をより効果的にかく乱することができる。"], "enemytips": ["ウーコンは「乱像猿技」を使ったあとに「代わり身の術」を発動させることが多い。攻撃するときは一瞬タイミングを待って、確実に本体に命中させよう。", "ウーコンは、多数の敵に囲まれていると防御力が増す。ウーコンを相手に有利に戦うには、少数戦をすることも考慮にいれよう。"], "tags": ["Fighter", "Tank"], "partype": "マナ", "info": {"attack": 8, "defense": 5, "magic": 2, "difficulty": 3}, "stats": {"hp": 610, "hpperlevel": 99, "mp": 330, "mpperlevel": 65, "movespeed": 340, "armor": 31, "armorperlevel": 4.7, "spellblock": 28, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3.5, "hpregenperlevel": 0.65, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 66, "attackdamageperlevel": 3.5, "attackspeedperlevel": 3, "attackspeed": 0.69}, "spells": [{"id": "MonkeyKingDoubleAttack", "name": "強棒打", "description": "次の通常攻撃の射程が増加して追加ダメージを与え、対象の物理防御を数秒間低下させる。", "tooltip": "自身と<keywordMajor>分身</keywordMajor>が行う次の通常攻撃の射程距離が{{ attackrangebonus }}増加する。また対象に<physicalDamage>{{ bonusdamagett }}の物理ダメージ</physicalDamage>を追加で与えて、{{ shredduration }}秒間<scaleArmor>物理防御を{{ armorshredpercent*100 }}%</scaleArmor>低下させる。<br /><br />自身または<keywordMajor>分身</keywordMajor>が敵を通常効果かスキルで攻撃するたびに、このスキルのクールダウンが{{ cooldowndecrease }}秒短縮される。<br /><br /><rules>このスキルはダメージを与えた際にスキル命中時効果を発動する。</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "物理防御低下率", "射程", "クールダウン"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ armorshredpercent*100.000000 }}% -> {{ armorshredpercentnl*100.000000 }}%", "{{ attackrangebonus }} -> {{ attackrangebonusNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [20, 20, 20, 20, 20], "costBurn": "20", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [250, 275, 300, 325, 350], "rangeBurn": "250/275/300/325/350", "image": {"full": "MonkeyKingDoubleAttack.png", "sprite": "spell8.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MonkeyKingDecoy", "name": "戦士の幻惑", "description": "<font color='#91d7ee'>インビジブル</font>状態になって指定方向にダッシュする。元いた場所には近くの敵を攻撃する分身が残る。", "tooltip": "ダッシュして{{ stealthduration }}秒間<keywordStealth>インビジブル</keywordStealth>状態になり、発動地点に自身の動かない<keywordMajor>分身</keywordMajor>を{{ cloneduration }}秒間出現させる。<br /><br /><keywordMajor>分身</keywordMajor>はウーコンが直前にダメージを与えた近くにいる敵を攻撃し、ウーコンがアルティメットスキルを使用するとそれを模倣する。<keywordMajor>分身</keywordMajor>の与えるダメージは通常の{{ clonedamagemod*100 }}%になる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ割合", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ clonedamagemod*100.000000 }}% -> {{ clonedamagemodnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 19, 18, 17, 16], "cooldownBurn": "20/19/18/17/16", "cost": [60, 55, 50, 45, 40], "costBurn": "60/55/50/45/40", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [275, 275, 275, 275, 275], "rangeBurn": "275", "image": {"full": "MonkeyKingDecoy.png", "sprite": "spell8.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MonkeyKingNimbus", "name": "乱像猿技", "description": "指定した対象に向かって突撃し、複数の残像を発生させる。残像は対象の近くにいる敵ユニットを攻撃し、対象それぞれにダメージを与える。", "tooltip": "敵1体に向かってダッシュし、<keywordMajor>分身</keywordMajor>を放つ。<keywordMajor>分身</keywordMajor>は対象の近くにいる別の敵に向かって同じようにダッシュする(最大{{ extratargets }}体まで)。命中した敵にはそれぞれ<magicDamage>{{ totaldamage }}の魔法ダメージ</magicDamage>を与える。その後、自身と分身が{{ attackspeedduration }}秒間<attackSpeed>{{ attackspeed*100 }}%の攻撃速度</attackSpeed>を獲得する。<br /><br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["ダメージ", "攻撃速度", "クールダウン", "@AbilityResourceName@コスト"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ attackspeed*100.000000 }}% -> {{ attackspeednl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9.25, 8.5, 7.75, 7], "cooldownBurn": "10/9.25/8.5/7.75/7", "cost": [30, 35, 40, 45, 50], "costBurn": "30/35/40/45/50", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MonkeyKingNimbus.png", "sprite": "spell8.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}, {"id": "MonkeyKingSpinToWin", "name": "旋風猿舞", "description": "如意棒を伸ばして回転し、自身の移動速度が増加する。<br><br>如意棒に触れた敵にダメージを与えてノックアップさせる。", "tooltip": "<speed>{{ movespeed*100 }}%の移動速度</speed>を獲得して如意棒を振り回し、周囲の敵を{{ knockupduration }}秒間<status>ノックアップ</status>させて<physicalDamage>{{ totaldamagett }}(+最大体力の{{ percenthpdamagett }})の物理ダメージ</physicalDamage>を{{ spinduration }}秒かけて与える。<br /><br />このスキルは{{ recastwindow }}秒以内であれば再び発動させることが可能で、その後クールダウンに入る。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["最大体力ダメージ", "クールダウン"], "effect": ["{{ basepercentmaxhpdmgpersec*200.000000 }}% -> {{ basepercentmaxhpdmgpersecnl*200.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [130, 110, 90], "cooldownBurn": "130/110/90", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "{{ abilityresourcename }}", "maxammo": "-1", "range": [315, 315, 315], "rangeBurn": "315", "image": {"full": "MonkeyKingSpinToWin.png", "sprite": "spell8.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }}{{ abilityresourcename }}"}], "passive": {"name": "岩の皮膚", "description": "チャンピオンまたはモンスターと戦闘中は、スタック可能な物理防御と最大体力に応じた自動回復を獲得する。", "image": {"full": "MonkeyKingStoneSkin.png", "sprite": "passive2.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}