{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vladimir": {"id": "Vladimir", "key": "8", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title": "Алый жнец", "image": {"full": "Vladimir.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "8000", "num": 0, "name": "default", "chromas": false}, {"id": "8001", "num": 1, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "8002", "num": 2, "name": "<PERSON>а<PERSON><PERSON><PERSON><PERSON> Владимир", "chromas": false}, {"id": "8003", "num": 3, "name": "Носферату Владимир", "chromas": false}, {"id": "8004", "num": 4, "name": "Ванд<PERSON><PERSON> Вла<PERSON>и<PERSON><PERSON>р", "chromas": false}, {"id": "8005", "num": 5, "name": "Кровавый лорд Владимир", "chromas": false}, {"id": "8006", "num": 6, "name": "<PERSON><PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ь душ", "chromas": false}, {"id": "8007", "num": 7, "name": "Старшеклассник Владимир", "chromas": false}, {"id": "8008", "num": 8, "name": "Влади<PERSON>ир из Темных вод", "chromas": true}, {"id": "8014", "num": 14, "name": "Предвестник ночи Владимир", "chromas": true}, {"id": "8021", "num": 21, "name": "Космический голод Владимир", "chromas": true}, {"id": "8030", "num": 30, "name": "Владимир из кафе красоты", "chromas": true}, {"id": "8039", "num": 39, "name": "Владимир из расколотого ордена", "chromas": true}, {"id": "8048", "num": 48, "name": "Владимир на маскараде Черной Розы", "chromas": false}], "lore": "Изувер, жаждущий крови смертных, Владимир играл не последнюю роль в становлении Ноксуса еще с самых ранних дней империи. Магия крови позволяет ему не только продлевать свою жизнь, но и с легкостью управлять умами и телами других. Благодаря этому умению он создал фанатичный культ собственной личности в роскошных салонах ноксианской аристократии. А в темных переулках оно же помогает ему с легкостью находить жертв и высасывать из них всю кровь до последней капли.", "blurb": "Изувер, жаждущий крови смертных, Влади<PERSON>ир играл не последнюю роль в становлении Ноксуса еще с самых ранних дней империи. Магия крови позволяет ему не только продлевать свою жизнь, но и с легкостью управлять умами и телами других. Благодаря этому умению...", "allytips": ["Переливание наносит урон мгновенно, а лечит после небольшой задержки. Это умение является одним из лучших инструментов для добивания.", "Используйте Заражение крови, когда поблизости много врагов.", "Алый омут позволяет уклониться от уже запущенных в вас умений, но не прерывает уже наложенные эффекты урона во времени."], "enemytips": ["Старайтесь убить Владимира до того, как сработает Заражение крови, потому что иначе он восстановит здоровье за каждого поврежденного чемпиона.", "Попытайтесь заставить Владимира использовать Алый омут в начале боя: так затраты здоровья на него будут максимальны.", "Предметы, наносящие урон, зависящий от максимального запаса здоровья врага, такие как Мучения Лиандри и Клинок уничтоженного короля, очень эффективны против Владимира. "], "tags": ["Mage", "Fighter"], "partype": "Багряный прилив", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 7}, "stats": {"hp": 607, "hpperlevel": 110, "mp": 2, "mpperlevel": 0, "movespeed": 330, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "VladimirQ", "name": "Переливание", "description": "Владимир высасывает жизненную силу врага. Когда ресурс Владимира заполнится, Переливание нанесет огромный урон и восстановит здоровье Владимиру.", "tooltip": "Владимир вытягивает жизненную силу цели, нанося ей <magicDamage>{{ basedamagetooltip }} магического урона</magicDamage> и восстанавливая себе <healing>{{ basehealtooltip }} здоровья</healing>. Использовав это умение дважды, Владимир <speed>ускоряется на {{ movementspeedonq2 }}%</speed> на 0.5 сек., а его следующее применение этого умения усиливается на {{ e8 }} сек.<br /><br />Усиленная версия этого умения наносит <magicDamage>{{ empowereddamagetooltip }} магического урона</magicDamage> и дополнительно лечит в размере <healing>{{ empoweredhealtooltip }} плюс {{ empoweredhealpercenttooltip }} от недостающего здоровья</healing>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Лечение", "Перезарядка"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 7.9, 6.8, 5.7, 4.6], "cooldownBurn": "9/7.9/6.8/5.7/4.6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 100, 120, 140, 160], [20, 25, 30, 35, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0.04, 0.04, 0.04, 0.04, 0.04], [85, 85, 85, 85, 85], [2.5, 2.5, 2.5, 2.5, 2.5], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/100/120/140/160", "20/25/30/35/40", "0", "0", "5", "0.04", "85", "2.5", "35", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}, {"id": "VladimirSanguinePool", "name": "Алый омут", "description": "Владимир погружается в омут крови и становится недосягаемым на 2 сек., замедляя врагов над собой и вытягивая у них здоровье.", "tooltip": "Владимир погружается в омут крови на 2 сек., увеличивая свою <speed>скорость передвижения на {{ hasteboost*100 }}% (эффект ослабевает со временем)</speed> на {{ hasteduration }} сек., а также становясь <keyword>недосягаемым</keyword> и <keyword>призрачным</keyword>. Враги в омуте <status>замедляются</status> на {{ movespeedmod*-100 }}%.<br /><br />Владимир наносит <magicDamage>{{ totaldamage }} магического урона</magicDamage> и восстанавливает себе <healing>{{ totalheal }} здоровья</healing> за каждого врага на протяжении действия умения.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон", "Перезарядка"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 25, 22, 19, 16], "cooldownBurn": "28/25/22/19/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% текущего здоровья", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "VladimirSanguinePool.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ healthcost*100 }}% текущего здоровья"}, {"id": "VladimirE", "name": "Потоки крови", "description": "Владимир тратит собственное здоровье, чтобы наполнить резервуар крови. После использования он нанесет урон по площади, но вражеские бойцы могут его заблокировать.", "tooltip": "<charge>Начало подготовки:</charge> Владимир заполняет резервуар крови, расходуя до <span class=\"colorCC3300\">{{ chargehealthtooltip }} здоровья</span>. При максимальном уровне заряда Владимир <status>замедляется</status> на 20%.<br /><br /><release>Применение:</release> Владимир выпускает фонтан крови, поражая окружающих врагов и нанося от <magicDamage>{{ mindamagetooltip }}</magicDamage> до <magicDamage>{{ maxdamagetooltip }} магического урона</magicDamage> в зависимости от времени подготовки. Если подготовка длилась хотя бы 1 сек., умение также <status>замедляет</status> цели на {{ slowpercent }}% на 0.5 сек.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Минимальный урон", "Максимальный урон", "Замедление", "Перезарядка"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e0 }} -> {{ e0NL }}", "{{ e9 }}% -> {{ e9NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11, 9, 7, 5], "cooldownBurn": "13/11/9/7/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [30, 45, 60, 75, 90], [6, 6, 6, 6, 6], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [40, 45, 50, 55, 60], [60, 90, 120, 150, 180]], "effectBurn": [null, "0", "8", "30/45/60/75/90", "6", "150", "0", "1.5", "1", "40/45/50/55/60", "60/90/120/150/180"], "vars": [], "costType": "% от макс. запаса здоровья ({{ chargehealthtooltip }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "Стоимость: {{ e2 }}% от макс. запаса здоровья ({{ chargehealthtooltip }})"}, {"id": "VladimirHemoplague", "name": "Заражение крови", "description": "Владимир за<PERSON>а<PERSON><PERSON><PERSON>т всех противников в выбранной области. В течение действия умения пораженные враги получают увеличенный урон; кроме того, по окончании действия умения им наносится дополнительный магический урон.", "tooltip": "Владимир заражает всех врагов в выбранной области. В течение {{ e4 }} сек. пораженные враги получают на {{ e2 }}% больше урона. По окончании действия умения Владимир наносит <magicDamage>{{ damage }} магического урона</magicDamage> всем пораженным врагам. Владимир восстанавливает себе <healing>{{ damage }} здоровья</healing>, если поражает чемпиона, и еще <healing>{{ secondaryhealingtooltip }} здоровья</healing> за каждого последующего чемпиона.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Урон"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [10, 10, 10], [100, 100, 100], [4, 4, 4], [40, 40, 40], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "10", "100", "4", "40", "0", "0", "0", "0", "0"], "vars": [], "costType": "Без стоимости", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "VladimirHemoplague.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "Без стоимости"}], "passive": {"name": "Багряный пакт", "description": "Каждые 30 дополнительного здоровья Владимира увеличивают его силу умений на 1. Каждая единица силы умений Владимира увеличивает его запас здоровья на 1,6. Эти эффекты не влияют друг на друга.", "image": {"full": "VladimirP.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}