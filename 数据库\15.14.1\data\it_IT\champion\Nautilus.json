{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nautilus": {"id": "Na<PERSON><PERSON>", "key": "111", "name": "Na<PERSON><PERSON>", "title": "il titano delle profondità", "image": {"full": "Nautilus.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "111000", "num": 0, "name": "default", "chromas": false}, {"id": "111001", "num": 1, "name": "Nautilus <PERSON>", "chromas": false}, {"id": "111002", "num": 2, "name": "Nautilus Sotterraneo", "chromas": false}, {"id": "111003", "num": 3, "name": "Astronautil<PERSON>", "chromas": true}, {"id": "111004", "num": 4, "name": "Nautilus Inquisitore", "chromas": false}, {"id": "111005", "num": 5, "name": "Nautilus Distruttore del mondo", "chromas": false}, {"id": "111006", "num": 6, "name": "Nautilus Conquistatore", "chromas": false}, {"id": "111009", "num": 9, "name": "Nautilus Pergamene Shan Hai", "chromas": false}, {"id": "111018", "num": 18, "name": "Nautilus Notte Inquietante", "chromas": false}, {"id": "111027", "num": 27, "name": "Nautilus Paladino <PERSON>", "chromas": false}, {"id": "111036", "num": 36, "name": "Na<PERSON>lus <PERSON>", "chromas": false}], "lore": "Una leggenda solitaria, vecchia come i primi moli di Bilgewater, il colosso corazzato noto come Nautilus si aggira nelle buie acque fuori dalle coste delle Isole Fiammablu. Spinto da un tradimento dimenticato, colpisce senza preavviso con la sua enorme ancora per salvare i miserabili e trascinare gli avidi verso il loro destino. Dicono che attacchi chi dimentica di pagare il ''tributo di Bilgewater'', trascinand<PERSON> sotta<PERSON>, come un monito ferreo per il quale nessuno può sottrarsi alle profondità.", "blurb": "Una leggenda solitaria, vecchia come i primi moli di Bilgewater, il colosso corazzato noto come Nautilus si aggira nelle buie acque fuori dalle coste delle Isole Fiammablu. Spinto da un tradimento dimenticato, colpisce senza preavviso con la sua enorme...", "allytips": ["Mentre sferri un assalto, punta Ancora del terrore contro i muri e usa Frantumazione terrena per avere una più alta probabilità di colpire.", "Frantumazione terrena ha un piccolo ritardo dell'esplosione dall'attivazione dell'abilità: usalo a tuo vantaggio quando scappi o quando i nemici stanno cercando di ingaggiare come deterrente."], "enemytips": ["Se Nautilus usa Frantumazione terrena vicino a te, rimani fermo finché l'effetto ondata finisce prima di correre. Se corri prima direttamente nelle esplosioni secondarie, sarai da<PERSON> e rallentato.", "Se Nautilus ha uno scudo di protezione, è in grado di infliggere una grande quantità di danni ad area con i suoi attacchi base: valuta se è il caso di abbattergli lo scudo."], "tags": ["Tank", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 4, "defense": 6, "magic": 6, "difficulty": 6}, "stats": {"hp": 646, "hpperlevel": 100, "mp": 400, "mpperlevel": 47, "movespeed": 325, "armor": 39, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.65, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1, "attackspeed": 0.706}, "spells": [{"id": "NautilusAnchorDrag", "name": "Ancora del terrore", "description": "Nautilus lancia la sua ancora in avanti.  Se colpisce un campione, Nautilus avvicina se stesso e l'avversario, infliggendogli danni magici.  Se colpisce un ostacolo, Nautilus si trascina verso di esso.", "tooltip": "Nautilus lancia la sua ancora in avanti. Se colpisce un campione, Nautilus avvicina se stesso e l'avversario, infliggendogli <magicDamage>{{ qdamagecalc }} danni magici</magicDamage> e <status>storden<PERSON>lo</status> per una brave durata. Se colpisce un ostacolo, Nautilus si trascina verso di esso.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON><PERSON>"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "0", "0.5", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "NautilusAnchorDrag.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusPiercingGaze", "name": "Furia del Titano", "description": "Nautilus ottiene uno scudo temporaneo.  Mentre lo scudo è attivo, i suoi attacchi base infliggono danni nel tempo al suo bersaglio e ai nemici circostanti.", "tooltip": "Nautilus ottiene uno <shield>scudo da {{ shieldcalc }}</shield> per {{ shieldduration }} secondi. <PERSON><PERSON> lo <shield>scudo</shield> è attivo, gli attacchi base di Nautilus infliggono <magicDamage>{{ dotdamagecalc }} danni magici </magicDamage> aggiuntivi nell'arco di 2 secondi al suo bersaglio e a tutti i nemici circostanti.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Quantità scudo", "<PERSON><PERSON> magici", "% salute massima"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ dotdamagebase }} -> {{ dotdamagebaseNL }}", "{{ shieldhealthratio*100.000000 }}% -> {{ shieldhealthrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "NautilusPiercingGaze.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusSplashZone", "name": "Frantumazione terrena", "description": "Nautilus crea tre onde esplosive attorno a sé. Ogni esplosione infligge danni e rallenta i nemici.", "tooltip": "Nautilus crea tre onde esplosive attorno a sé, ciascuna delle quali infligge <magicDamage>{{ damagecalc }} danni magici</magicDamage> ai nemici nell'area e li <status>rallenta</status> del {{ slowpercent*100 }}% per {{ slowduration }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "Costo in @AbilityResourceName@", "<PERSON><PERSON>", "Rallentamento"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NautilusSplashZone.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "NautilusGrandLine", "name": "Carica di profondità", "description": "Nautilus spara un'onda d'urto nel suolo che insegue un avversario. Questa onda lacera la terra sopra di sé, lanciando i nemici in aria. Quando raggiunge l'avversario, l'onda d'urto erutta, e lancia il bersaglio in aria, storden<PERSON><PERSON>.", "tooltip": "Nautilus scaglia un'onda d'urto che insegue un campione nemico ed esplode quando lo raggiunge, infliggendogli <magicDamage>{{ primarytargetdamage }} danni magici</magicDamage>, <status>lanciandolo in aria</status> e <status>stordendolo</status> per {{ stunduration }} secondo/i. Gli altri nemici colpiti dall'onda d'urto vengono <status>lanciati in aria</status> e <status>storditi</status>, e subiscono <magicDamage>{{ secondarytargetdamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Ricarica", "<PERSON>ni che vanno oltre il bersaglio", "<PERSON><PERSON> stordimento:", "<PERSON><PERSON> da esplosione"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ primarydamage }} -> {{ primarydamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "NautilusGrandLine.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> travolgente", "description": "Il primo attacco base di Nautilus contro un bersaglio infligge danni fisici aggiuntivi e lo immobilizza brevemente.", "image": {"full": "Nautilus_StaggeringBlow.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}