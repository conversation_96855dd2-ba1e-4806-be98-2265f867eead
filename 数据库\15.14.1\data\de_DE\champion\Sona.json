{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Sona": {"id": "<PERSON><PERSON>", "key": "37", "name": "<PERSON><PERSON>", "title": "die Virtuosin", "image": {"full": "Sona.png", "sprite": "champion4.png", "group": "champion", "x": 336, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "37000", "num": 0, "name": "default", "chromas": false}, {"id": "37001", "num": 1, "name": "Musen<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "37002", "num": 2, "name": "Pentakill-Sona", "chromas": false}, {"id": "37003", "num": 3, "name": "Sternsinger-<PERSON><PERSON>", "chromas": false}, {"id": "37004", "num": 4, "name": "Qin<PERSON>Son<PERSON>", "chromas": true}, {"id": "37005", "num": 5, "name": "Arcade-Sona", "chromas": true}, {"id": "37006", "num": 6, "name": "DJ <PERSON><PERSON>", "chromas": false}, {"id": "37007", "num": 7, "name": "Herzblatt-Sona", "chromas": false}, {"id": "37009", "num": 9, "name": "Odyssee-Sona", "chromas": true}, {"id": "37017", "num": 17, "name": "PsyOps-Sona", "chromas": true}, {"id": "37026", "num": 26, "name": "Pentakill III: Lost Chapter-<PERSON>a", "chromas": true}, {"id": "37035", "num": 35, "name": "Sternenwächterin Sona", "chromas": true}, {"id": "37045", "num": 45, "name": "Reise der Unsterblichen-Sona", "chromas": true}, {"id": "37046", "num": 46, "name": "Reise der Unsterblichen-Sona (Prestige)", "chromas": false}, {"id": "37056", "num": 56, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}], "lore": "Sona ist in Demacia die führende Virtuosin am Etwahl, eine<PERSON> Saiteninstrument, durch das sie mit eleganten Akkorden und lebendigen Arien spricht. Ihr vornehmes Gebaren hat sie bei den Hochgeborenen beliebt gemacht, doch einige vermuten Magie hinter ihren fesselnden Melodien – ein Tabu in Demacia. Fremden gegenüber still und von engen Freunden irgendwie verstanden, zupft Sona ihre Melodien nicht nur, um Verbündete zu heilen, sondern auch, um nichtsahnende Gegner zu erledigen.", "blurb": "<PERSON>a ist in Demacia die führende Virtuosin am Etwahl, einem Saiteninstrument, durch das sie mit eleganten Akkorden und lebendigen Arien spricht. Ihr vornehmes Gebaren hat sie bei den Hochgeborenen beliebt gemacht, doch einige vermuten Magie hinter ihren...", "allytips": ["Markiere deine Verbündeten, während Sonas Auren aktiv sind, lass dich aber nicht von G<PERSON>n er<PERSON>.", "<PERSON>re dir „Crescendo“ für den entscheidenden Wendepunkt auf.", "Wenn du „Arie der Beharrlichkeit“ zeitlich geschickt einsetzt, steigerst du deine Überlebenschancen."], "enemytips": ["<PERSON><PERSON><PERSON><PERSON> euch, wenn sich <PERSON> n<PERSON>, damit sie nicht das gesamte Team zum Tanzen bringen kann.", "<PERSON><PERSON>e Sona zu<PERSON>t aus, denn sie kann ihr Team wieder heilen, wenn man ihr zu lange Zeit gibt."], "tags": ["Support", "Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 5, "defense": 2, "magic": 8, "difficulty": 4}, "stats": {"hp": 550, "hpperlevel": 91, "mp": 340, "mpperlevel": 45, "movespeed": 325, "armor": 26, "armorperlevel": 4.2, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 11.5, "mpregenperlevel": 0.4, "crit": 0, "critperlevel": 0, "attackdamage": 49, "attackdamageperlevel": 3, "attackspeedperlevel": 2.3, "attackspeed": 0.644}, "spells": [{"id": "SonaQ", "name": "<PERSON><PERSON>ne des Heldenmutes", "description": "Sona spielt die Hymne des Heldenmutes, sendet Klangblitze aus und fügt 2 nahen Gegnern magischen Schaden zu, wobei Champions und Monster priorisiert werden. Sona erhält eine vorübergehende Aura, die Verbündeten, die von der Zone markiert sind, beim nächsten Angriff gegen Gegner zusätzlichen Schaden gewährt.", "tooltip": "Sona fügt den nächsten zwei Gegnern <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> zu und priorisiert dabei Champions. Dann beginnt sie eine neue <keywordMajor>Melodie</keywordMajor>. Du erhältst eine Steigerung von <keywordMajor>Accelerando</keywordMajor> für jeden Champion, dem du damit Schaden zufügst.<br /><br /><keywordMajor>Melodie:</keywordMajor> Sona erhält {{ auraduration }}&nbsp;Sekunden lang eine Aura, die verbündeten Champions bei ihren nächsten Angriff innerhalb von {{ onhitduration }}&nbsp;Sekunden zusätzlich <magicDamage>{{ totalonhitdamage }}&nbsp;magischen Schaden</magicDamage> %i:OnHit% gewährt.<br /><br /><keywordMajor>Powerakkord – Staccato:</keywordMajor> Zusätzlicher Schaden mit „Powerakkord“ (insgesamt <magicDamage>{{ totalstaccatodamage }}&nbsp;magischer Schaden</magicDamage>).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden (aktiv)", "<PERSON><PERSON><PERSON> (Melodie)", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ baseonhitdamage }} -> {{ baseonhitdamageNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "SonaQ.png", "sprite": "spell13.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaW", "name": "Arie der Beharrlichkeit", "description": "Sona spielt die „Arie der Beharrlichkeit“, deren beschützende Melodie Sona und einen verwundeten nahen Verbündeten heilt. Sona erhält eine vorübergehende Aura, die Verbündeten, die von der Zone markiert sind, einen temporären <PERSON> gewährt.", "tooltip": "<spellPassive>Aktiv:</spellPassive> Sona stellt bei sich selbst und bei einem verbündeten Champion in der Nähe <healing>{{ totalheal }}&nbsp;Le<PERSON></healing> wieder her und priorisiert den am stärksten verwundeten Champion. Dann beginnt sie mit einer neuen <keywordMajor>Melodie</keywordMajor>.<br /><br /><keywordMajor>Melodie:</keywordMajor> Sona erhält {{ auraduration }}&nbsp;Sekunden lang eine Aura, die verbündeten Champions {{ shieldduration }}&nbsp;Sekunden lang einen <shield><PERSON><PERSON><PERSON> in Hö<PERSON> von </shield>{{ totalshield }} gewährt.<br /><br />Du erhältst eine Steigerung von <keywordMajor>Accelerando</keywordMajor>, wenn du einen verwundeten Verbündeten heilst und jedes Mal, wenn du mit diesem Schild einen Verbündeten vor mindestens {{ accelerandoshieldbreakpoint }}&nbsp;Schaden abschirmst.<br /><br /><keywordMajor>Powerakkord – Diminuendo:</keywordMajor> „Powerakkord“ verringert außerdem {{ diminuendoduration }}&nbsp;Sekunden lang normalen und magischen Schaden, den das Ziel verursacht, um {{ totaldiminuendoweakenpercent }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Heilung (aktiv)", "<PERSON><PERSON><PERSON> (Melodie)", "Kosten (@AbilityResourceName@)"], "effect": ["{{ baseheal }} -> {{ basehealNL }}", "{{ baseshield }} -> {{ baseshieldNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 10, 10, 10, 10], "cooldownBurn": "10", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "SonaW.png", "sprite": "spell13.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaE", "name": "Lied der Flinkheit", "description": "Sona spielt das „Lied der Flinkheit“, das nahen Verbündeten zusätzliches Lauftempo gewährt. Sona erhält eine vorübergehende Aura, die verbündeten Champions, die von der Zone markiert sind, zusätzliches Lauftempo gewährt.", "tooltip": "<spellPassive>Aktiv:</spellPassive> Sie beginnt eine neue <keywordMajor>Melodie</keywordMajor> und gewährt sich selbst {{ selfmovementspeeddurationmin }}&nbsp;Sekunden lang <speed>{{ totalselfmovementspeed }}&nbsp;Lauftempo</speed> (verlängert auf bis zu {{ selfmovementspeeddurationmax }}&nbsp;<PERSON><PERSON><PERSON>, wenn sie keinen Schaden erleidet). <br /><br /><keywordMajor>Melodie:</keywordMajor> Sona erhält {{ auraduration }}&nbsp;Sekunden lang eine Aura, die verbündeten Champions {{ allymovementspeedduration }}&nbsp;Sekunden lang <speed>{{ totalallymovementspeed }}&nbsp;Lauftempo</speed> gewährt.<br /><br /><keywordMajor>Powerakkord – Tempo:</keywordMajor> „Powerakkord“ <status>verlangsamt</status> das Ziel außerdem {{ tempoduration }}&nbsp;Sekunden lang um {{ totaltempomovespeedslow }}.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Lauftempo"], "effect": ["{{ allybasemovementspeed*100.000000 }}&nbsp;% -> {{ allybasemovementspeednl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [14, 14, 14, 14, 14], "cooldownBurn": "14", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [430, 430, 430, 430, 430], "rangeBurn": "430", "image": {"full": "SonaE.png", "sprite": "spell13.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "SonaR", "name": "Crescendo", "description": "Sona spielt ihren ultimativen Akkord, der gegnerische Champions betäubt, sie zu einem Tanz z<PERSON>t und ihnen magischen Schaden zufügt.", "tooltip": "<PERSON>a schlägt einen unwiderstehlichen Akkord an, der Gegner {{ stunduration }}&nbsp;Sekunden lang <status>betäubt</status> und <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 120, 100], "cooldownBurn": "140/120/100", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900], "rangeBurn": "900", "image": {"full": "SonaR.png", "sprite": "spell13.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Powerakkord", "description": "<passive>Accelerando</passive>: Sona erhält dauerhaft Fähigkeitstempo für ihre Grundfähigkeiten (Ult ausgenommen), wenn sie ihre Fähigkeiten gut einsetzt (bis zu einer Obergrenze). <PERSON>bald die Obergrenze erreicht ist, verringern erfolgreiche Einsätze stattdessen die verbleibende Abklingzeit ihrer ultimativen Fähigkeit.<br><br><passive>Powerakkord</passive>: Nachdem Sona ihre Fähigkeiten einige Male eingesetzt hat, verursacht ihr nächster Angriff zusätzlichen magischen Schaden sowie einen zusätzlichen Effekt, der darauf basiert, welche Grundfähigkeit Sona zuletzt aktiviert hat.", "image": {"full": "Sona_Passive_Charged.png", "sprite": "passive4.png", "group": "passive", "x": 336, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}