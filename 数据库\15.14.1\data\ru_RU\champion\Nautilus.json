{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Nautilus": {"id": "Na<PERSON><PERSON>", "key": "111", "name": "Наутилус", "title": "Ти<PERSON><PERSON><PERSON> глубин", "image": {"full": "Nautilus.png", "sprite": "champion3.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "111000", "num": 0, "name": "default", "chromas": false}, {"id": "111001", "num": 1, "name": "Наутилус из бездны", "chromas": false}, {"id": "111002", "num": 2, "name": "Подземный Наутилус", "chromas": false}, {"id": "111003", "num": 3, "name": "Астронаутилус", "chromas": true}, {"id": "111004", "num": 4, "name": "Хранитель Наутилус", "chromas": false}, {"id": "111005", "num": 5, "name": "Разрушитель миров Наутилус", "chromas": false}, {"id": "111006", "num": 6, "name": "Завоеватель Наутилус", "chromas": false}, {"id": "111009", "num": 9, "name": "Наутилус со Свитков Шань хай", "chromas": false}, {"id": "111018", "num": 18, "name": "Наути<PERSON><PERSON><PERSON> из Мир<PERSON> ужасов", "chromas": false}, {"id": "111027", "num": 27, "name": "Космический паладин Наутилус", "chromas": false}, {"id": "111036", "num": 36, "name": "Кристаллический мучитель Наутилус", "chromas": false}], "lore": "Ровесник первых древних пристаней Билджвотера, закованный в броню гигант Наутилус скитается по морскому дну близ островов Синего Пламени. Не сумев отомстить тому, кто его предал, Наутилус теперь орудует громадным якорем, спасая несчастных и без предупреждения поражая алчных. Говорят, что он приходит за теми, кто не платит ''билджвотерское подношение'', и утягивает их на дно. Наутилус – железное напоминание о том, что глубину не обманешь.", "blurb": "Ровесник первых древних пристаней Билджвотера, закованный в броню гигант Наутилус скитается по морскому дну близ островов Синего Пламени. Не сумев отомстить тому, кто его предал, Наутилус теперь орудует громадным якорем, спасая несчастных и без...", "allytips": ["Устраивая засаду, помните, что не обязательно попадать Якорной цепью прямо по врагу. Можно промахнуться, но попасть в элемент ландшафта и притянуться прямо к противнику, а затем замедлить его Сейсмической волной.", "Сейсмическая волна срабатывает не сразу; это можно использовать при побеге или просто в случае, когда нужно не дать врагам приблизиться."], "enemytips": ["Если Наутилус использует Сейсмическую волну, находясь прямо рядом с вами, не двигайтесь с места, пока действие умения не закончится. Если вы сорветесь слишком рано, вы можете попасть под все три выброса. Это нанесет вам большой урон и замедлит на большее количество времени.", "Когда Наутилус защищен своим щитом, его автоатаки наносят значительный урон по площади. Может иметь смысл уничтожить его щит."], "tags": ["Tank", "Support"], "partype": "Мана", "info": {"attack": 4, "defense": 6, "magic": 6, "difficulty": 6}, "stats": {"hp": 646, "hpperlevel": 100, "mp": 400, "mpperlevel": 47, "movespeed": 325, "armor": 39, "armorperlevel": 4.95, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 8.5, "hpregenperlevel": 0.55, "mpregen": 8.65, "mpregenperlevel": 0.5, "crit": 0, "critperlevel": 0, "attackdamage": 61, "attackdamageperlevel": 3.3, "attackspeedperlevel": 1, "attackspeed": 0.706}, "spells": [{"id": "NautilusAnchorDrag", "name": "Якорная цепь", "description": "Наутилус бросает вперед свой якорь. Если якорь попадает во врага, они с Наутилусом притягиваются друг к другу, а цель получает магический урон. Если якорь попадает в элемент ландшафта, Наутилус притягивается к нему.", "tooltip": "Наутилус бросает вперед свой якорь. Если якорь попадает во врага, они с Наутилусом притягиваются друг к другу. При этом цель получает <magicDamage>{{ qdamagecalc }} магического урона</magicDamage> и ненадолго <status>оглушается</status>. Если якорь попадает в элемент ландшафта, Наутилус притягивается к нему.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Урон"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [70, 115, 160, 205, 250], [0, 0, 0, 0, 0], [0.5, 0.5, 0.5, 0.5, 0.5], [0.5, 0.5, 0.5, 0.5, 0.5], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/115/160/205/250", "0", "0.5", "0.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [1150, 1150, 1150, 1150, 1150], "rangeBurn": "1150", "image": {"full": "NautilusAnchorDrag.png", "sprite": "spell9.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "NautilusPiercingGaze", "name": "<PERSON><PERSON><PERSON>в титана", "description": "Наутилус ненадолго накладывает на себя щит. Пока существует щит, автоатаки Наутилуса наносят периодический урон цели и окружающим врагам.", "tooltip": "Наутилус на {{ shieldduration }} сек. получает <shield>щит прочностью {{ shieldcalc }}</shield>. Пока существует <shield>щит</shield>, автоатаки Наутилуса дополнительно наносят <magicDamage>{{ dotdamagecalc }} магического урона</magicDamage> в течение 2 сек. цели и всем окружающим врагам.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Прочность щита", "Магический урон", "% от максимального запаса здоровья"], "effect": ["{{ shieldbase }} -> {{ shieldbaseNL }}", "{{ dotdamagebase }} -> {{ dotdamagebaseNL }}", "{{ shieldhealthratio*100.000000 }}% -> {{ shieldhealthrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [60, 60, 60, 60, 60], "costBurn": "60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "NautilusPiercingGaze.png", "sprite": "spell9.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "NautilusSplashZone", "name": "Сейсмическая волна", "description": "Наутилус создает вокруг себя три взрывные волны. Каждый взрыв наносит урон и замедляет врагов.", "tooltip": "Наутилус создает вокруг себя три взрывные волны, каждая из которых наносит <magicDamage>{{ damagecalc }} магического урона</magicDamage> пораженным врагам и <status>замедляет</status> их на {{ slowpercent*100 }}% (эффект ослабевает в течение {{ slowduration }} сек.).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Стоимость – @AbilityResourceName@", "Урон", "Замедление"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}", "{{ damagebase }} -> {{ damagebaseNL }}", "{{ slowpercent*100.000000 }}% -> {{ slowpercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [7, 6.5, 6, 5.5, 5], "cooldownBurn": "7/6.5/6/5.5/5", "cost": [50, 60, 70, 80, 90], "costBurn": "50/60/70/80/90", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "NautilusSplashZone.png", "sprite": "spell9.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}, {"id": "NautilusGrandLine", "name": "Глу<PERSON><PERSON>нный заряд", "description": "Наутилус создает подземную ударную волну, преследующую выбранного вражеского чемпиона. По ходу движения волна разрывает землю над собой, нанося урон всем врагам на своем пути и подбрасывая их в воздух. Когда волна настигает свою цель, та тоже получает урон и подбрасывается в воздух, а затем оглушается.", "tooltip": "Наутилус выпускает ударную волну, которая преследует вражеского чемпиона. Волна наносит цели <magicDamage>{{ primarytargetdamage }} магического урона</magicDamage>, а также <status>подбрасывает</status> и <status>оглушает</status> ее на {{ stunduration }} сек. Другие враги, пораженные волной, также <status>подбрасываются</status>, <status>оглушаются</status> и получают <magicDamage>{{ secondarytargetdamage }} магического урона</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Перезарядка", "Урон при прохождении", "Продолжительность оглушения:", "Урон от взрыва"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ primarydamage }} -> {{ primarydamageNL }}", "{{ stunduration }} -> {{ stundurationNL }}", "{{ secondarydamage }} -> {{ secondarydamageNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": ": {{ cost }}", "maxammo": "-1", "range": [825, 825, 825], "rangeBurn": "825", "image": {"full": "NautilusGrandLine.png", "sprite": "spell9.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }}: {{ cost }}"}], "passive": {"name": "Ошеломляющий удар", "description": "Первая автоатака Наутилуса против цели наносит больше физического урона и ненадолго обездвиживает.", "image": {"full": "Nautilus_StaggeringBlow.png", "sprite": "passive3.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}