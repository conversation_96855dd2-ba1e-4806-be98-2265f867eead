{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Fiddlesticks": {"id": "Fiddlesticks", "key": "9", "name": "Fiddlesticks", "title": "das uralte Unheil", "image": {"full": "Fiddlesticks.png", "sprite": "champion1.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "9000", "num": 0, "name": "default", "chromas": false}, {"id": "9001", "num": 1, "name": "Gespenstischer Fiddlesticks", "chromas": false}, {"id": "9002", "num": 2, "name": "Britannien-Fiddlesticks", "chromas": false}, {"id": "9003", "num": 3, "name": "Bandito-Fiddlesticks", "chromas": true}, {"id": "9004", "num": 4, "name": "Kürbiskopf-Fiddlesticks", "chromas": false}, {"id": "9005", "num": 5, "name": "Klabautersticks", "chromas": false}, {"id": "9006", "num": 6, "name": "Überraschungsparty-Fiddlesticks", "chromas": true}, {"id": "9007", "num": 7, "name": "Zuckersticks", "chromas": false}, {"id": "9008", "num": 8, "name": "Auferstandener Fiddlesticks", "chromas": false}, {"id": "9009", "num": 9, "name": "Prätorianer Fiddlesticks", "chromas": true}, {"id": "9027", "num": 27, "name": "<PERSON>enne<PERSON><PERSON>ddle<PERSON>s", "chromas": true}, {"id": "9037", "num": 37, "name": "<PERSON><PERSON><PERSON>-Fiddlesticks", "chromas": true}], "lore": "In Runeterra ist etwas erwacht. Etwas Uraltes. Etwas Schreckliches. Das zeitlose Grauen namens Fiddlesticks lauert an den Grenzen der sterblichen Gesellschaft und fühlt sich von Orten angezogen, in denen Paranoia allgegenwärtig ist. Dort nährt es sich an seinen angsterfüllten Opfern. Mit seiner geschärften Sense erntet die hagere Kreatur, deren Gestalt eine krude Nachahmung eines Menschen ist, die Furcht selbst. Wer das Pech hat, diese Ernte zu überleben, wird mit einem gebrochenen Verstand zurückgelassen. Nehmt euch vor dem Ruf der Krähe in Acht – oder vor dem Flüstern einer Gestalt, die fast menschlich wirkt … Fiddlesticks ist zurückgekehrt.", "blurb": "In Runeterra ist etwas erwacht. Etwas Uraltes. Etwas Schreckliches. Das zeitlose Grauen namens Fiddlesticks lauert an den Grenzen der sterblichen Gesellschaft und fühlt sich von Orten angezogen, in denen Paranoia allgegenwärtig ist. Dort nährt es sich...", "allytips": [], "enemytips": [], "tags": ["Mage", "Support"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 3, "magic": 9, "difficulty": 9}, "stats": {"hp": 650, "hpperlevel": 106, "mp": 500, "mpperlevel": 28, "movespeed": 335, "armor": 34, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 480, "hpregen": 5.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 2.65, "attackspeedperlevel": 2.11, "attackspeed": 0.625}, "spells": [{"id": "FiddleSticksQ", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> Fiddlesticks Gegnern mit Fähigkeiten Schaden zufügt, während er nicht gesehen wird, oder er einen Gegner mit der Aktivierung von „Schrecken“ anvisiert, versetzt er die Zieleinheit in Furcht. Diese flieht daraufhin panisch für eine bestimmte Zeit.", "tooltip": "<spellPassive>Passiv:</spellPassive> <PERSON><PERSON>ddlesticks ungesehen und außerhalb des Kampfes oder als <keywordMajor>Vogelscheuche</keywordMajor> getarnt einem <PERSON>ner mit einer Fähigkeit Schaden zufügt, wird dieser {{ fearduration }}&nbsp;Sekunde(n) lang in <status>Furcht</status> versetzt.<br /><br /><spellActive>Aktiv:</spellActive> Versetzt einen Gegner {{ fearduration }}&nbsp;Sekunde(n) lang in <status>Furcht</status> und fügt ihm <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ totalpercenthealthdamage }} seines aktuellen Lebens zu. Wenn das Ziel vor Kurzem von Fiddlesticks <status>in Furcht versetzt</status> wurde, fügt er ihm stattdessen <magicDamage>magischen Schaden</magicDamage> in Höhe von {{ totalpercenthealthdamagefeared }} seines aktuellen Lebens zu.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Abklingzeit", "Aktuelles Leben %"], "effect": ["{{ fearduration }} -> {{ feardurationNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ maxhealthdamage*100.000000 }}&nbsp;% -> {{ maxhealthdamagenl*100.000000 }}&nbsp;%"]}, "maxrank": 5, "cooldown": [15, 14.5, 14, 13.5, 13], "cooldownBurn": "15/14.5/14/13.5/13", "cost": [65, 65, 65, 65, 65], "costBurn": "65", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [575, 575, 575, 575, 575], "rangeBurn": "575", "image": {"full": "FiddleSticksQ.png", "sprite": "spell3.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksW", "name": "Ertragreiche Ernte", "description": "Fiddlesticks entzieht nahen Gegnern Leben. Am Ende des Effekts fügt er zusätzlichen Exekutionsschaden zu.", "tooltip": "Fiddlesticks kanalisiert und entzieht die Seelen naher Gegner. Diese erleiden 2&nbsp;Sekunden lang <magicDamage>{{ draindamagecalc }}&nbsp;magischen Schaden</magicDamage> pro Sekunde sowie <magicDamage>magischen Schaden</magicDamage> in <PERSON>ö<PERSON> von {{ percentfortooltip }}&nbsp;% ihres fehlenden Lebens, wenn der Effekt endet. Fiddlesticks heilt sich um <healing>{{ vamppercentage }}&nbsp;% des Schadens</healing>.<br /><br />Wenn Fiddlesticks die Kanalisierung ohne Unterbrechung beendet, wird die verbleibende Abklingzeit um 60&nbsp;% verringert.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde", "Schaden abhängig vom fehlenden Leben", "Prozentuale Heilung", "Abklingzeit"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ percentfortooltip }}&nbsp;% -> {{ percentfortooltipNL }}&nbsp;%", "{{ vamppercentage }}&nbsp;% -> {{ vamppercentageNL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [10, 9.5, 9, 8.5, 8], "cooldownBurn": "10/9.5/9/8.5/8", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "FiddleSticksW.png", "sprite": "spell3.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksE", "name": "Jetzt ist Sense", "description": "Fiddlesticks schwingt seine Sense. Alle getroffenen Gegner werden verlangsamt und Gegner in der Mitte des Sensenschlags zusätzlich verstummt.", "tooltip": "Fiddlesticks entfesselt dunkle Magie, die <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> verursacht und {{ silenceduration }}&nbsp;Sekunden lang um {{ slowamount*-100 }}&nbsp;% <status>verlangsamt</status>. <PERSON><PERSON><PERSON> in der Mitte <status>verstummen</status> außerdem für die Dauer.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit", "Kosten (@AbilityResourceName@)"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ slowamount*-100.000000 }}&nbsp;% -> {{ slowamountnl*-100.000000 }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [10, 9, 8, 7, 6], "cooldownBurn": "10/9/8/7/6", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "FiddleSticksE.png", "sprite": "spell3.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "FiddleSticksR", "name": "Krähensturm", "description": "Ein Krähenschwarm umgibt Fiddlesticks und verursacht an allen gegnerischen Einheiten in der Nähe pro Sekunde Schaden.", "tooltip": "Fiddlesticks kanalisiert {{ channeltime }}&nbsp;<PERSON>ku<PERSON> lang, teleportiert und entfesselt einen Krähenschwarm, der über {{ duration }}&nbsp;Sekunden hinweg <magicDamage>{{ totaldamage }}&nbsp;magischen Schaden</magicDamage> verursacht.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Schaden pro Sekunde", "Abklingzeit"], "effect": ["{{ damagepersecond }} -> {{ damagepersecondNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [125, 225, 325], [5, 5, 5], [1.5, 1.5, 1.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "125/225/325", "5", "1.5", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [800, 800, 800], "rangeBurn": "800", "image": {"full": "FiddleSticksR.png", "sprite": "spell3.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON> harm<PERSON> Vogelscheuche", "description": "Fiddlesticks' <PERSON><PERSON><PERSON> wird durch Abbilder einer Vogelscheuche ersetzt.", "image": {"full": "FiddlesticksP.png", "sprite": "passive1.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}