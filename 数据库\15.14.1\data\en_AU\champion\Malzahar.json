{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Malzahar": {"id": "<PERSON><PERSON><PERSON>", "key": "90", "name": "<PERSON><PERSON><PERSON>", "title": "the Prophet of the Void", "image": {"full": "Malzahar.png", "sprite": "champion2.png", "group": "champion", "x": 384, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "90000", "num": 0, "name": "default", "chromas": false}, {"id": "90001", "num": 1, "name": "Vizier <PERSON>", "chromas": false}, {"id": "90002", "num": 2, "name": "Shadow Prince <PERSON>", "chromas": false}, {"id": "90003", "num": 3, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "90004", "num": 4, "name": "Overlord <PERSON>", "chromas": false}, {"id": "90005", "num": 5, "name": "Snow Day Malzahar", "chromas": true}, {"id": "90006", "num": 6, "name": "Battle Boss <PERSON>", "chromas": false}, {"id": "90007", "num": 7, "name": "Hextech Malzahar", "chromas": false}, {"id": "90009", "num": 9, "name": "Worldbreaker <PERSON><PERSON><PERSON>", "chromas": true}, {"id": "90018", "num": 18, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "90028", "num": 28, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "90038", "num": 38, "name": "Three Honors Malzahar", "chromas": false}, {"id": "90039", "num": 39, "name": "<PERSON><PERSON><PERSON><PERSON>", "chromas": true}, {"id": "90049", "num": 49, "name": "Fatebreaker <PERSON><PERSON><PERSON>", "chromas": true}], "lore": "A zealous seer dedicated to the unification of all life, <PERSON><PERSON><PERSON> truly believes the newly emergent Void to be the path to Runeterra's salvation. In the desert wastes of Shurima, he followed the voices that whispered in his mind, all the way to ancient Icathia. Amidst the ruins of that land, he gazed into the dark heart of the Void itself, and was gifted new power and purpose. <PERSON><PERSON><PERSON> now sees himself as a shepherd, empowered to bring others into the fold… or to release the voidling creatures that dwell beneath.", "blurb": "A zealous seer dedicated to the unification of all life, <PERSON><PERSON><PERSON> truly believes the newly emergent Void to be the path to Runeterra's salvation. In the desert wastes of Shurima, he followed the voices that whispered in his mind, all the way to ancient...", "allytips": ["Wait to cast Void Swarm until there are nearby enemies for the Voidling to attack or kill.", "Use Call of the Void and Nether Grasp to renew the duration of Malefic Visions on enemies.", "Avoiding damage in lane maximizes the uptime on Void Shift, increasing <PERSON><PERSON><PERSON>'s safety dramatically."], "enemytips": ["When <PERSON><PERSON><PERSON> hits a spell on enemies affected by Malefic Visions, the Visions are refreshed.", "Avoid minion waves that have been affected by Malefic Visions. You never know when they may die and pass the Visions onto you.", "<PERSON><PERSON><PERSON> is especially dangerous when he has built up his Void Swarm."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 2, "magic": 9, "difficulty": 6}, "stats": {"hp": 580, "hpperlevel": 101, "mp": 375, "mpperlevel": 28, "movespeed": 335, "armor": 18, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 500, "hpregen": 6, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 1.5, "attackspeed": 0.625}, "spells": [{"id": "MalzaharQ", "name": "Call of the Void", "description": "<PERSON><PERSON><PERSON> opens up two portals to the Void. After a short delay, they fire projectiles that deal Magic Damage and silence enemy champions.", "tooltip": "<PERSON><PERSON><PERSON> opens two portals to the Void that fire projectiles inward, dealing <magicDamage>{{ totaldamagetooltip }} magic damage</magicDamage> and <status>Silencing</status> for {{ e2 }} second(s).{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Silence Duration", "@AbilityResourceName@ Cost"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 6, 6, 6, 6], "cooldownBurn": "6", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [1, 1.25, 1.5, 1.75, 2], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "1/1.25/1.5/1.75/2", "0.4", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "MalzaharQ.png", "sprite": "spell8.png", "group": "spell", "x": 0, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MalzaharW", "name": "Void Swarm", "description": "<PERSON><PERSON><PERSON> summons Voidlings to attack nearby enemies.", "tooltip": "<spellPassive>Passive:</spellPassive> <PERSON><PERSON><PERSON>'s other Abilities give him a stack when cast (max {{ stackcap }}).<br /><br /><spellActive>Active:</spellActive> <PERSON><PERSON><PERSON> summons a Voidling plus an additional Voidling per stack. Voidlings last {{ voidlingduration }} seconds and deal <magicDamage>{{ voidlingbonusdamagetooltip }} magic damage</magicDamage> each hit.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Voidling Damage", "Voidling Duration", "@AbilityResourceName@ Cost"], "effect": ["{{ voidlingbasedamage }} -> {{ voidlingbasedamageNL }}", "{{ voidlingduration }} -> {{ voidlingdurationNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 8, 8, 8, 8], "cooldownBurn": "8", "cost": [40, 45, 50, 55, 60], "costBurn": "40/45/50/55/60", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [25000, 25000, 25000, 25000, 25000], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "25000", "2", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "0", "range": [150, 150, 150, 150, 150], "rangeBurn": "150", "image": {"full": "MalzaharW.png", "sprite": "spell8.png", "group": "spell", "x": 48, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MalzaharE", "name": "Malefic Visions", "description": "<PERSON><PERSON><PERSON> infects his target's mind with cruel visions of their demise, dealing damage over time. Using <PERSON><PERSON><PERSON>'s other spells on the target will refresh the visions.<br><br>If the target dies while afflicted by the visions, they pass on to a nearby enemy unit and <PERSON><PERSON><PERSON> gains <PERSON><PERSON>. <PERSON><PERSON><PERSON>'s Voidlings are attracted to affected units.", "tooltip": "<PERSON><PERSON><PERSON> inflicts terrible visions, dealing <magicDamage>{{ totaldamage }} magic damage</magicDamage> over {{ duration }} seconds. Applying <spellName>Call of the Void</spellName> or <spellName>Nether Grasp</spellName> to the victim during this time refreshes the visions.<br /><br />If the victim dies, <PERSON><PERSON><PERSON> gains <scaleMana>{{ manarestore }} Mana</scaleMana> and the visions spread to the nearest enemy.<br /><br /><rules>Malefic Visions executes minions below {{ minionexecutethreshold }} Health.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Damage", "Cooldown", "@AbilityResourceName@ Cost"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [11, 10, 9, 8, 7], "cooldownBurn": "11/10/9/8/7", "cost": [60, 70, 80, 90, 100], "costBurn": "60/70/80/90/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [2, 2, 2, 2, 2], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "8", "2", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [650, 650, 650, 650, 650], "rangeBurn": "650", "image": {"full": "MalzaharE.png", "sprite": "spell8.png", "group": "spell", "x": 96, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "MalzaharR", "name": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> channels the essence of the Void to suppress an enemy champion over a zone of damaging negative energy.", "tooltip": "<PERSON><PERSON><PERSON> <status>Suppresses</status> an enemy champion and deals <magicDamage>{{ totaldamagetooltip }} magic damage</magicDamage> over {{ e4 }} seconds. A zone of negative energy is created around his target dealing <magicDamage>{{ zonedamagetooltip }} max Health magic damage</magicDamage> over {{ e3 }} seconds.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON> G<PERSON> Damage", "Null Zone Damage Per Second", "Cooldown"], "effect": ["{{ beamdamage }} -> {{ beamdamageNL }}", "{{ maxhealthdamage }}% -> {{ maxhealthdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [140, 110, 80], "cooldownBurn": "140/110/80", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [5, 5, 5], [2.5, 2.5, 2.5], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "5", "2.5", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [700, 700, 700], "rangeBurn": "700", "image": {"full": "MalzaharR.png", "sprite": "spell8.png", "group": "spell", "x": 144, "y": 48, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "Void Shift", "description": "When he hasn't recently taken damage or been crowd controlled, <PERSON><PERSON><PERSON> gains massive damage reduction and crowd control immunity, lingering for a short period after taking damage.", "image": {"full": "Malzahar_Passive.png", "sprite": "passive2.png", "group": "passive", "x": 384, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}