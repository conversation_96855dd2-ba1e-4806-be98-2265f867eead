{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Briar": {"id": "<PERSON><PERSON><PERSON>", "key": "233", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON>aim insatiable", "image": {"full": "Briar.png", "sprite": "champion0.png", "group": "champion", "x": 432, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "233000", "num": 0, "name": "default", "chromas": false}, {"id": "233001", "num": 1, "name": "Briar ville démoniaque", "chromas": true}, {"id": "233010", "num": 10, "name": "<PERSON><PERSON><PERSON> prim<PERSON>", "chromas": true}], "lore": "Née d'une expérience ratée menée par la Rose noire, <PERSON><PERSON><PERSON> a besoin d'un pilori spécial pour contrôler son irrépressible soif de sang. Après des années d'enfermement, cette arme vivante s'est aujourd'hui défaite de ses entraves et parcourt le monde librement. Plus personne ne la contrôle. Elle assouvit sa soif de connaissance et de sang au gré de ses envies, et savoure chaque occasion de se déchaîner, même si mettre fin à sa frénésie sanguinaire n'est jamais facile.", "blurb": "Née d'une expérience ratée menée par la Rose noire, <PERSON><PERSON><PERSON> a besoin d'un pilori spécial pour contrôler son irrépressible soif de sang. Après des années d'enfermement, cette arme vivante s'est aujourd'hui défaite de ses entraves et parcourt le monde...", "allytips": [], "enemytips": [], "tags": ["Fighter", "Assassin"], "partype": "<PERSON><PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 625, "hpperlevel": 95, "mp": 0, "mpperlevel": 0, "movespeed": 340, "armor": 30, "armorperlevel": 4.2, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 0, "hpregenperlevel": 0, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.5, "attackspeedperlevel": 2, "attackspeed": 0.644}, "spells": [{"id": "BriarQ", "name": "À table !", "description": "Briar bondit sur une unité, et frappe les ennemis avec un méchant coup de pied, les étourdissant et brisant leur armure.", "tooltip": "Briar bondit sur une cible, l'<status>étourdissant</status> pendant {{ stunduration }} sec, lui infligeant <magicDamage>{{ totaldamage }} pts de dégâts magiques</magicDamage> et réduisant son <scaleArmor>armure</scaleArmor> et sa <scaleMR>résistance magique</scaleMR> de {{ shredpercent*100 }}% pendant {{ shredduration }} sec.<br /><br /><rules>Sous l'effet de <keywordMajor>Folie sanguinaire</keywordMajor>, Briar ne priorise plus les champions si la compétence vise un sbire ou un monstre.</rules>{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Réduction d'armure et de résistance magique", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredpercent*100.000000 }}% -> {{ shredpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 12, 11, 10, 9], "cooldownBurn": "13/12/11/10/9", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% des PV actuels.", "maxammo": "-1", "range": [475, 475, 475, 475, 475], "rangeBurn": "475", "image": {"full": "BriarQ.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 48, "w": 48, "h": 48}, "resource": "Coûte {{ spell.briarp:currenthealthpercentcost*100 }}% des PV actuels."}, {"id": "BriarW", "name": "Folie sanguinaire / Juste un croc", "description": "Briar bondit en avant et fait éclater son pilori. Elle succombe à la Folie sanguinaire et poursuit sans relâche l'ennemi le plus proche (en priorisant les champions). Sous l'effet de la frénésie, ses vitesses de déplacement et d'attaque augmentent et ses attaques infligent des dégâts de zone autour de sa cible.<br><br>Briar peut réactiver la compétence pour prendre un CROC sur sa cible à sa prochaine attaque, ce qui inflige des dégâts supplémentaires en fonction des PV manquants et soigne Briar proportionnellement aux dégâts qu'elle inflige.", "tooltip": "Briar bondit et entre dans une <keywordMajor>Folie sanguinaire</keywordMajor>. Elle applique une provocation sur elle-même contre l'ennemi le plus proche pendant {{ berserkduration }} sec, en priorisant les champions. Tant qu'elle est dans une <keywordMajor>Folie sanguinaire</keywordMajor>, elle gagne <attackSpeed>+{{ berserkas*100 }}% de vitesse d'attaque</attackSpeed> et <speed>+{{ berserkms*100 }}% de vitesse de déplacement</speed>, et ses attaques infligent <physicalDamage>{{ totalaoedamage }} pts de dégâts physiques</physicalDamage> aux ennemis autour de sa cible.<br /><br />Briar peut <recast>relancer</recast> cette compétence pour renforcer sa prochaine attaque. Cela inflige <physicalDamage>{{ totalattackbonusdamage }} + {{ totalattackpercentmissinghealth }}% des PV manquants en dégâts physiques</physicalDamage> et <healing>rend à Briar des PV équivalents à {{ attackmaxhpheal }} + {{ attackhealpercent*100 }}% des dégâts infligés</healing>.<br />{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Vitesse d'attaque", "Vitesse de d<PERSON>placement", "Ratio des dégâts de zone en fonction des dégâts d'attaque", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Soins", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ berserkas*100.000000 }}% -> {{ berserkasnl*100.000000 }}%", "{{ berserkms*100.000000 }}% -> {{ berserkmsnl*100.000000 }}%", "{{ aoeattackdamagepercent*100.000000 }}% -> {{ aoeattackdamagepercentnl*100.000000 }}%", "{{ attackbonusdamage }} -> {{ attackbonusdamageNL }}", "{{ attackhealpercent*100.000000 }}% -> {{ attackhealpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [14, 13, 12, 11, 10], "cooldownBurn": "14/13/12/11/10", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% des PV actuels.", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "BriarW.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 48, "w": 48, "h": 48}, "resource": "Coûte {{ spell.briarp:currenthealthpercentcost*100 }}% des PV actuels."}, {"id": "BriarE", "name": "<PERSON><PERSON>", "description": "Briar récupère ses esprits, se libère de la Folie sanguinaire et canalise son énergie dans un puissant cri qui inflige des dégâts et ralentit les ennemis. Lorsqu'elle charge son cri, les dégâts qu'elle subit sont réduits et elle se soigne d'une partie de ses PV max. Un cri entièrement chargé repousse les ennemis, inflige des dégâts supplémentaires et étourdit ceux qui touchent un mur.", "tooltip": "<charge>Début de la charge</charge> : Briar sort de sa <keywordMajor>Folie sanguinaire</keywordMajor> et rassemble de l'énergie, ce qui réduit les dégâts qu'elle subit de {{ drpercent }}% et lui rend <healing>{{ percentmaxhpheal }} PV</healing> en 1 sec.<br /><br /><release>Relâchement</release> : <PERSON><PERSON><PERSON> pousse un cri qui inflige jusqu'à <magicDamage>{{ damage }} pts de dégâts magiques</magicDamage> aux ennemis en fonction de la durée de la charge et les <status>ralentit</status> de {{ slowpercent*100 }}% pendant {{ slowduration }} sec. Lorsque le cri est entièrement chargé, il <status>repousse</status> les ennemis, infligeant <magicDamage>{{ wallhitdamage }} pts de dégâts magiques</magicDamage> à ceux qui touchent un mur et les <status>étourdissant</status> pendant {{ wallstunduration }} sec.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dégâts supplémentaires", "Ratio de soins"], "effect": ["{{ maxbasedamage }} -> {{ maxbasedamageNL }}", "{{ wallhitbasedamage }} -> {{ wallhitbasedamageNL }}", "{{ healhppercent*100.000000 }}% -> {{ healhppercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% des PV actuels.", "maxammo": "-1", "range": [400, 400, 400, 400, 400], "rangeBurn": "400", "image": {"full": "BriarE.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 48, "w": 48, "h": 48}, "resource": "Coûte {{ spell.briarp:currenthealthpercentcost*100 }}% des PV actuels."}, {"id": "BriarR", "name": "Vol mortel", "description": "Briar lance l'hémolithe de son pilori à travers la carte, marquant le premier champion ennemi touché comme sa proie. Briar vole jusqu'à lui, effrayant les autres ennemis proches à son arrivée, et entre dans un état d'hémomanie complète. Elle pourchasse sa proie jusqu'à la mort. Elle gagne les avantages de Folie sanguinaire ainsi que de l'armure, de la résistance magique, du vol de vie et de la vitesse de déplacement supplémentaires.", "tooltip": "Briar lance l'hémolithe de son pilori à travers la carte et s'envole jusqu'au premier ennemi touché par la gemme, le marquant comme sa proie. Lorsqu'elle atterrit, elle inflige <magicDamage>{{ damage }} pts de dégâts magiques</magicDamage> à tout ce qui l'entoure et oblige les ennemis qui ne sont pas sa proie à <status>fuir</status> pendant {{ fearduration }} sec. Elle rentre ensuite dans une <keywordMajor>Folie sanguinaire</keywordMajor> et pourchasse sa proie jusqu'à la mort. Pendant la durée de l'effet, elle gagne +{{ totalresists }} <scaleArmor>armure</scaleArmor> et <scaleMR>résistance magique</scaleMR>, +{{ lifestealpercent*100 }}% de vol de vie et <speed>+{{ extramovespeedpercent*100 }}% de vitesse de déplacement</speed> bonus.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vol de vie", "Vitesse de d<PERSON>placement", "<PERSON><PERSON><PERSON> ré<PERSON>"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ lifestealpercent*100.000000 }}% -> {{ lifestealpercentnl*100.000000 }}%", "{{ extramovespeedpercent*100.000000 }}% -> {{ extramovespeedpercentnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "% des PV actuels.", "maxammo": "-1", "range": [10000, 10000, 10000], "rangeBurn": "10000", "image": {"full": "BriarR.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 48, "w": 48, "h": 48}, "resource": "Coûte {{ spell.briarp:currenthealthpercentcost*100 }}% des PV actuels."}], "passive": {"name": "Malé<PERSON> écarlate", "description": "Les attaques et compétences de Briar infligent un saignement cumulable qui la soigne d'une partie des dégâts qu'il inflige. Du fait de sa faim permanente, Briar manque de régénération naturelle des PV, mais moins elle a de PV, plus ses soins sont renforcés.", "image": {"full": "BriarP.png", "sprite": "passive0.png", "group": "passive", "x": 432, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}