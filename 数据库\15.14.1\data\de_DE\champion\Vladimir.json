{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Vladimir": {"id": "Vladimir", "key": "8", "name": "Vladimir", "title": "der blutrote Schnitter", "image": {"full": "Vladimir.png", "sprite": "champion5.png", "group": "champion", "x": 144, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "8000", "num": 0, "name": "default", "chromas": false}, {"id": "8001", "num": 1, "name": "<PERSON>", "chromas": false}, {"id": "8002", "num": 2, "name": "Marquis <PERSON>", "chromas": false}, {"id": "8003", "num": 3, "name": "Nosferatu-Vladimir", "chromas": false}, {"id": "8004", "num": 4, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "8005", "num": 5, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chromas": false}, {"id": "8006", "num": 6, "name": "Seelenstehler-Vladimir", "chromas": false}, {"id": "8007", "num": 7, "name": "Akademie-Vladimir", "chromas": false}, {"id": "8008", "num": 8, "name": "<PERSON><PERSON><PERSON> Frei<PERSON>", "chromas": true}, {"id": "8014", "num": 14, "name": "Flammende Finsternis Vladimir", "chromas": true}, {"id": "8021", "num": 21, "name": "Kosmischer Verschlinger Vladimir", "chromas": true}, {"id": "8030", "num": 30, "name": "Konditorei-Vladimir", "chromas": true}, {"id": "8039", "num": 39, "name": "Gebrochener Pakt-Vladimir", "chromas": true}, {"id": "8048", "num": 48, "name": "<PERSON><PERSON> der Schwarzen Rose <PERSON>", "chromas": false}], "lore": "<PERSON> ist ein b<PERSON><PERSON>, den es nach sterblichem Blut dürstet, und er hat bereits seit den Anfängen des Reiches in allen Angelegenheiten von Noxus seine Finger im Spiel. Neben einem unnatürlichen langen Leben erlaubt ihm sein Wissen um die Blutmagie, die Seelen und Körper anderer zu kontrollieren. In den extravaganten Salons der noxianischen Aristokratie konnte er so einen fanatischen Kult um seine Person aufbauen, während er in den hintersten Gassen seine Gegner unbemerkt ausbluten lassen kann.", "blurb": "<PERSON> ist ein b<PERSON><PERSON>, den es nach sterblichem Blut dürstet, und er hat bereits seit den Anfängen des Reiches in allen Angelegenheiten von Noxus seine Finger im Spiel. Neben einem unnatürlichen langen Leben erlaubt ihm sein Wissen um die...", "allytips": ["„Transfusion“ verursacht sofort Schaden am Gegner, noch bevor Vladimir geheilt wird, was sie zu einem der besten Finisher im Spiel macht.", "Z<PERSON>bere „Blutseuche“ so, dass sie möglichst viele Feinde gleichzeitig trifft.", "„Blutlache“ wehrt Projektile ab, deshalb ist sie bestens geeignet, um Kampfunfähigkeits-Fertigkeiten auszuweichen."], "enemytips": ["<PERSON><PERSON><PERSON>, <PERSON>, bevor die Blutseuche explodiert, denn sie heilt ihn für jeden betroffenen gegnerischen Champion.", "<PERSON><PERSON> <PERSON>, zu Beginn des Kampfes Blutlache einzusetzen, erh<PERSON><PERSON> sich für ihn die Lebenskosten der Fähigkeit.", "Gegenstä<PERSON>, die Lebensanhäufung entgegenwirken, wie Liandrys Qual und Klinge des gestürzten Königs, sind gegen Vladimir sehr effektiv."], "tags": ["Mage", "Fighter"], "partype": "<PERSON><PERSON><PERSON>", "info": {"attack": 2, "defense": 6, "magic": 8, "difficulty": 7}, "stats": {"hp": 607, "hpperlevel": 110, "mp": 2, "mpperlevel": 0, "movespeed": 330, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 450, "hpregen": 7, "hpregenperlevel": 0.6, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3, "attackspeedperlevel": 2, "attackspeed": 0.658}, "spells": [{"id": "VladimirQ", "name": "Transfusion", "description": "Vladimir raubt dem gegnerischen Ziel Leben. <PERSON><PERSON> „Transfusion“ 2-mal aktiviert hat, werden Schaden und Heilung der Fähigkeit kurzzeitig massiv erhöht.", "tooltip": "<PERSON> entzieht seinem <PERSON>, fügt ihm dabei <magicDamage>{{ basedamagetooltip }}&nbsp;magischen Schaden</magicDamage> zu und stellt <healing>{{ basehealtooltip }}&nbsp;<PERSON><PERSON></healing> wieder her. Nachdem Vladimir diese Fähigkeit zweimal eingesetzt hat, erhält er 0,5&nbsp;Sekunden lang <speed>{{ movementspeedonq2 }}&nbsp;% Lauftempo</speed> und verstärkt {{ e8 }}&nbsp;Sekunden lang den nächsten Einsatz dieser Fähigkeit.<br /><br />Die verstärkte Version dieser Fähigkeit verursacht <magicDamage>{{ empowereddamagetooltip }}&nbsp;magischen Schaden</magicDamage> und stellt zusätzlich <healing>{{ empoweredhealtooltip }} plus {{ empoweredhealpercenttooltip }} des fehlenden Lebens</healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Heilung", "Abklingzeit"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ e2 }} -> {{ e2NL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 7.9, 6.8, 5.7, 4.6], "cooldownBurn": "9/7.9/6.8/5.7/4.6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [80, 100, 120, 140, 160], [20, 25, 30, 35, 40], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [5, 5, 5, 5, 5], [0.04, 0.04, 0.04, 0.04, 0.04], [85, 85, 85, 85, 85], [2.5, 2.5, 2.5, 2.5, 2.5], [35, 35, 35, 35, 35], [0, 0, 0, 0, 0]], "effectBurn": [null, "80/100/120/140/160", "20/25/30/35/40", "0", "0", "5", "0.04", "85", "2.5", "35", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirQ.png", "sprite": "spell15.png", "group": "spell", "x": 0, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}, {"id": "VladimirSanguinePool", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON> vers<PERSON>t in einer Lache aus Blut und kann 2 Sekunden lang nicht anvisiert werden. Außerdem werden alle Gegner über der Lache verlangsamt, und <PERSON> entzieht ihnen Leben.", "tooltip": "Vladimir versinkt 2&nbsp;<PERSON><PERSON><PERSON> lang in einer Blutlache, wodurch er <speed>{{ hasteboost*100 }}&nbsp;% verfallendes Lauftempo</speed> für {{ hasteduration }}&nbsp;Sekunde erhält und nicht <keyword>anvisiert werden kann</keyword> und <keyword>geister<PERSON></keyword> wird, während er Gegner in der Lache um {{ movespeedmod*-100 }}&nbsp;% <status>verlangsamt</status>.<br /><br /><PERSON> ve<PERSON>cht <magicDamage>{{ totaldamage }} magischen Schaden</magicDamage> und stellt über die Dauer hinweg pro Gegner <healing>{{ totalheal }}&nbsp;Leben</healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>", "Abklingzeit"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [28, 25, 22, 19, 16], "cooldownBurn": "28/25/22/19/16", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "&nbsp;% des aktuellen Lebens", "maxammo": "-1", "range": [350, 350, 350, 350, 350], "rangeBurn": "350", "image": {"full": "VladimirSanguinePool.png", "sprite": "spell15.png", "group": "spell", "x": 48, "y": 144, "w": 48, "h": 48}, "resource": "Kostet {{ healthcost*100 }}&nbsp;% des aktuellen Lebens"}, {"id": "VladimirE", "name": "<PERSON><PERSON> aus Blut", "description": "<PERSON> be<PERSON>t mit seinem <PERSON>, ein Blutreservoir aufzuladen, das bei Entfesselung, Umgebungsschaden um ihn herum anrichtet, aber von gegnerischen Einheiten blockiert werden kann.", "tooltip": "<charge>Vorbereitung: </charge><PERSON> opfert bis zu <span class=\"colorCC3300\">{{ chargehealthtooltip }}&nbsp;Leben</span>, um ein Blutreservoir aufzuladen. Bei voller Aufladung wird Vladimir um 20&nbsp;% <status>verlangsamt</status>.<br /><br /><release>Auslösung: </release><PERSON> entfesselt einen Ring aus Blutsalven, der umstehenden Gegnern abhängig von der Aufladungsdauer zwischen <magicDamage>{{ mindamagetooltip }}</magicDamage> und <magicDamage>{{ maxdamagetooltip }}&nbsp;magischen Schaden</magicDamage> zufügt. Wenn diese Fähigkeit mindestens 1&nbsp;Sekunde lang aufgeladen wurde, werden die Ziele 0,5&nbsp;Sekunden lang um {{ slowpercent }}&nbsp;% <status>verlangsamt</status>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Mindestschaden", "<PERSON><PERSON><PERSON>", "Verlangsamung", "Abklingzeit"], "effect": ["{{ e3 }} -> {{ e3NL }}", "{{ e0 }} -> {{ e0NL }}", "{{ e9 }}&nbsp;% -> {{ e9NL }}&nbsp;%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [13, 11, 9, 7, 5], "cooldownBurn": "13/11/9/7/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [8, 8, 8, 8, 8], [30, 45, 60, 75, 90], [6, 6, 6, 6, 6], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [1.5, 1.5, 1.5, 1.5, 1.5], [1, 1, 1, 1, 1], [40, 45, 50, 55, 60], [60, 90, 120, 150, 180]], "effectBurn": [null, "0", "8", "30/45/60/75/90", "6", "150", "0", "1.5", "1", "40/45/50/55/60", "60/90/120/150/180"], "vars": [], "costType": "&nbsp;% des max. Lebens ({{ chargehealthtooltip }})", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "VladimirE.png", "sprite": "spell15.png", "group": "spell", "x": 96, "y": 144, "w": 48, "h": 48}, "resource": "Kanalisierungskosten {{ e2 }}&nbsp;% des max. <PERSON>s ({{ chargehealthtooltip }})"}, {"id": "VladimirHemoplague", "name": "Blutseuche", "description": "<PERSON> infiziert ein Gebiet mit einer bösartigen Seuche. Betroffene Gegner erleiden während der Wirkungsdauer erhöhten Schaden durch Angriffe. Nach ein paar Sekunden fügt „Blutseuche“ betroffenen Gegnern magischen Schaden zu und heilt Vladimir für jeden getroffenen Champion.", "tooltip": "<PERSON> infiziert Gegner mit einer ansteckenden Seuche, wodurch sie {{ e4 }}&nbsp;Sekunden lang um {{ e2 }}&nbsp;% erhöhten Schaden aus allen Quellen erleiden. Nach Ablauf der Fähigkeit fügt Vladimir allen Infizierten <magicDamage>{{ damage }}&nbsp;magischen Schaden</magicDamage> zu. Wird ein Champion getroffen, stellt <PERSON> <healing>{{ damage }}&nbsp;<PERSON><PERSON></healing> wieder her. Pro weiteren getroffenen Champion stellt <PERSON> zu<PERSON> <healing>{{ secondaryhealingtooltip }}&nbsp;<PERSON><PERSON></healing> wieder her.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON>"], "effect": ["{{ e1 }} -> {{ e1NL }}"]}, "maxrank": 3, "cooldown": [120, 120, 120], "cooldownBurn": "120", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [150, 250, 350], [10, 10, 10], [100, 100, 100], [4, 4, 4], [40, 40, 40], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "150/250/350", "10", "100", "4", "40", "0", "0", "0", "0", "0"], "vars": [], "costType": "<PERSON><PERSON>", "maxammo": "-1", "range": [625, 625, 625], "rangeBurn": "625", "image": {"full": "VladimirHemoplague.png", "sprite": "spell15.png", "group": "spell", "x": 144, "y": 144, "w": 48, "h": 48}, "resource": "<PERSON><PERSON>"}], "passive": {"name": "Blutroter Pakt", "description": "Für jeweils 30 Punkte zusätzliches Leben erhält Vladimir 1 Fähigkeitsstärke und je 1 Punkt Fähigkeitsstärke gewährt Vladimir 1,6 zusätzliches Leben (ist nicht mit sich selbst kumulativ).", "image": {"full": "VladimirP.png", "sprite": "passive5.png", "group": "passive", "x": 144, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}