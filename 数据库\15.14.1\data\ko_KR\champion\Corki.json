{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Corki": {"id": "<PERSON><PERSON>", "key": "42", "name": "코르키", "title": "대담한 폭격수", "image": {"full": "Corki.png", "sprite": "champion0.png", "group": "champion", "x": 192, "y": 96, "w": 48, "h": 48}, "skins": [{"id": "42000", "num": 0, "name": "default", "chromas": false}, {"id": "42001", "num": 1, "name": "UFO 코르키", "chromas": false}, {"id": "42002", "num": 2, "name": "얼음 썰매 코르키", "chromas": false}, {"id": "42003", "num": 3, "name": "붉은 남작 코르키", "chromas": false}, {"id": "42004", "num": 4, "name": "스피드광 코르키", "chromas": false}, {"id": "42005", "num": 5, "name": "우르프 라이더 코르키", "chromas": false}, {"id": "42006", "num": 6, "name": "불꽃놀이 코르키", "chromas": true}, {"id": "42007", "num": 7, "name": "Fnatic 코르키", "chromas": false}, {"id": "42008", "num": 8, "name": "아케이드 코르키", "chromas": true}, {"id": "42018", "num": 18, "name": "웰시 코르키", "chromas": true}, {"id": "42026", "num": 26, "name": "우주비행사 코르키", "chromas": true}], "lore": "요들 비행기 조종사 코르키는 다른 어떤 것보다 좋아하는 두 가지가 있다. 바로 비행과 멋들어진 콧수염이다. 좋아하는 순서대로 나열한 것은 아니지만. 코르키는 밴들 시티를 떠난 후 필트오버에 정착하며 찾아낸 경이로운 비행기와 사랑에 빠졌다. 코르키는 비행장치를 개발하기 위해 혼신의 노력을 쏟았고, 시끄러운 뱀 편대라 불리는 베테랑 공군 정예 부대를 이끌게 되었다. 포화 속에서도 평정심을 잃지 않으며 제2의 고향의 창공을 누비는 코르키에게 마음껏 미사일 포화를 퍼부어 해결하지 못했던 문제는 하나도 없다.", "blurb": "요들 비행기 조종사 코르키는 다른 어떤 것보다 좋아하는 두 가지가 있다. 바로 비행과 멋들어진 콧수염이다. 좋아하는 순서대로 나열한 것은 아니지만. 코르키는 밴들 시티를 떠난 후 필트오버에 정착하며 찾아낸 경이로운 비행기와 사랑에 빠졌다. 코르키는 비행장치를 개발하기 위해 혼신의 노력을 쏟았고, 시끄러운 뱀 편대라 불리는 베테랑 공군 정예 부대를 이끌게 되었다. 포화 속에서도 평정심을 잃지 않으며 제2의 고향의 창공을 누비는 코르키에게 마음껏 미사일...", "allytips": ["인광탄 스킬은 주변 수풀에 숨은 적을 찾을 때 유용합니다.", "발키리 스킬은 방어용으로 사용할 수 있으니 탈출시에 활용해 보십시오.", "코르키는 개틀링 건 스킬을 사용하는 동안에도 계속 공격이 가능합니다. 코르키를 완벽하게 사용하려면 개틀링 건 스킬을 최대한 활용하십시오."], "enemytips": ["코르키의 미사일 폭격 스킬에 주의하세요. 미사일 폭격 스킬은 광역 피해를 입히기 때문에 미니언 뒤에 숨어있어도 피해를 입을 수 있습니다.", "코르키는 발키리나 특급 폭탄 배송을 사용하고 나면 도망갈 수단이 사라집니다. 이 스킬 이후 코르키를 집중적으로 공격하세요."], "tags": ["Marksman", "Mage"], "partype": "마나", "info": {"attack": 8, "defense": 3, "magic": 6, "difficulty": 6}, "stats": {"hp": 610, "hpperlevel": 100, "mp": 350, "mpperlevel": 40, "movespeed": 325, "armor": 27, "armorperlevel": 4.5, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 5.5, "hpregenperlevel": 0.55, "mpregen": 7.4, "mpregenperlevel": 0.7, "crit": 0, "critperlevel": 0, "attackdamage": 52, "attackdamageperlevel": 2, "attackspeedperlevel": 2.8, "attackspeed": 0.644}, "spells": [{"id": "PhosphorusBomb", "name": "인광탄", "description": "코르키가 지정한 위치에 인광탄을 던져, 해당 지역 안의 적들에게 마법 피해를 입힙니다. 또한, 이 공격은 지속시간 동안 해당 지역의 유닛들을 드러내 줍니다.", "tooltip": "코르키가 폭탄을 던져 <magicDamage>{{ totaldamage }}의 마법 피해</magicDamage>를 입힙니다. {{ revealduration }}초 동안 폭탄에 맞은 지역과 챔피언이 드러납니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [9, 8.5, 8, 7.5, 7], "cooldownBurn": "9/8.5/8/7.5/7", "cost": [60, 65, 70, 75, 80], "costBurn": "60/65/70/75/80", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "PhosphorusBomb.png", "sprite": "spell2.png", "group": "spell", "x": 192, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "CarpetBomb", "name": "발키리", "description": "코르키가 목표 위치로 비행하며 폭탄을 투하하여 불타는 흔적을 남겨, 그 안에 있는 적에게 피해를 입힙니다.", "tooltip": "코르키가 비행하며 경로를 {{ trailduration }}초 동안 불태웁니다. 경로에 있는 적들은 지속시간 동안 <magicDamage>{{ maximumdamage }}의 마법 피해</magicDamage>를 입습니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["지속 피해", "재사용 대기시간", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "CarpetBomb.png", "sprite": "spell2.png", "group": "spell", "x": 240, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "GGun", "name": "개틀링 건", "description": "코르키가 정면에 원뿔 형태로 개틀링 건을 발사하여 적의 방어력과 마법 저항력을 낮추고 피해를 입힙니다.", "tooltip": "코르키가 전방에 개틀링 건을 발사하여 {{ sprayduration }}초 동안 <physicalDamage>{{ totaldamage }}의 물리 피해</physicalDamage>를 입히고 <scaleMR>마법 저항력</scaleMR>과 <scaleArmor>방어력을 최대 {{ shredmax*-1 }}</scaleArmor>만큼 감소시킵니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량", "방어력 / 마법 저항력 감소", "소모값 @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ shredmax*-1.000000 }} -> {{ shredmaxnl*-1.000000 }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [12, 12, 12, 12, 12], "cooldownBurn": "12", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "-1", "range": [600, 600, 600, 600, 600], "rangeBurn": "600", "image": {"full": "GGun.png", "sprite": "spell2.png", "group": "spell", "x": 288, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}, {"id": "MissileBarrage", "name": "미사일 폭격", "description": "코르키가 목표 지점에 충돌 시 폭발하는 미사일을 발사하여 해당 지역에 있는 적들에게 피해를 입힙니다. 코르키는 시간이 지날수록 미사일을 저장하여 최대치까지 장전해 둘 수 있습니다. 3번째 발사하는 미사일은 '큰 놈'으로, 추가 피해를 입힙니다.", "tooltip": "코르키가 처음으로 적을 맞히면 폭발하는 미사일을 발사하여 주변 적에게 <physicalDamage>{{ rsmallmissiledamage }}의 물리 피해</physicalDamage>를 입힙니다. 세 번째 미사일은 매번 <physicalDamage>{{ rbigmissiledamage }}의 물리 피해</physicalDamage>를 입힙니다.<br /><br />이 스킬은 최대 {{ maxammotooltip }}회 충전됩니다. 챔피언을 상대로 기본 공격 적중 시 충전 시간이 <attention>{{ attackrefund }}</attention>초 감소합니다.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["피해량"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}"]}, "maxrank": 3, "cooldown": [2, 2, 2], "cooldownBurn": "2", "cost": [35, 35, 35], "costBurn": "35", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ cost }}", "maxammo": "4", "range": [1225, 1225, 1225], "rangeBurn": "1225", "image": {"full": "MissileBarrage.png", "sprite": "spell2.png", "group": "spell", "x": 336, "y": 144, "w": 48, "h": 48}, "resource": "{{ abilityresourcename }} {{ cost }}"}], "passive": {"name": "마법공학 탄약", "description": "코르키의 기본 공격 피해량의 일부가 추가 <trueDamage>고정 피해</trueDamage>로 전환됩니다.", "image": {"full": "Corki_RapidReload.png", "sprite": "passive0.png", "group": "passive", "x": 192, "y": 96, "w": 48, "h": 48}}, "recommended": []}}}