{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Warwick": {"id": "Warwick", "key": "19", "name": "Warwick", "title": "Rozkiełznany Gniew Zaun", "image": {"full": "Warwick.png", "sprite": "champion5.png", "group": "champion", "x": 240, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "19000", "num": 0, "name": "default", "chromas": false}, {"id": "19001", "num": 1, "name": "<PERSON><PERSON><PERSON>", "chromas": true}, {"id": "19002", "num": 2, "name": "Wilk w Foczej Skórze", "chromas": false}, {"id": "19003", "num": 3, "name": "Wielki Zły Warwick", "chromas": false}, {"id": "19004", "num": 4, "name": "Śnieżny Warwick", "chromas": false}, {"id": "19005", "num": 5, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "19006", "num": 6, "name": "Warwick Ognisty Kieł", "chromas": false}, {"id": "19007", "num": 7, "name": "<PERSON>", "chromas": false}, {"id": "19008", "num": 8, "name": "<PERSON>", "chromas": false}, {"id": "19009", "num": 9, "name": "Urfwick", "chromas": false}, {"id": "19010", "num": 10, "name": "Księżycowy Strażnik Warwick", "chromas": true}, {"id": "19016", "num": 16, "name": "PROJEKT: Warwick", "chromas": true}, {"id": "19035", "num": 35, "name": "Pradawny Bóg Warwick", "chromas": false}, {"id": "19045", "num": 45, "name": "Warwick Wybraniec Zimy", "chromas": false}, {"id": "19046", "num": 46, "name": "Warwick Wybraniec Zimy (Prestiżowy)", "chromas": false}, {"id": "19056", "num": 56, "name": "<PERSON><PERSON> z <PERSON>ane", "chromas": false}], "lore": "Warwick to potwór, który poluje w mrocznych zaułkach Zaun. Przeszedł przemianę w wyniku bolesnych eksperymentów, a jego ciało połączyło się ze skomplikowanym systemem pomp i zbiorników, które wypełniają go alchemicznym gniewem. Kryjąc się w cieniach, poluje na przestępców, którzy terroryzują mieszkańców miasta. Zapach krwi doprowadza go do szaleństwa. Nikt, kto ją przelewa, przed nim nie ucieknie.", "blurb": "Warwick to potwór, który poluje w mrocznych zaułkach Zaun. Przeszedł przemianę w wyniku bolesnych eksperymentów, a jego ciało połączyło się ze skomplikowanym systemem pomp i zbiorników, które wypełniają go alchemicznym gniewem. Kryjąc się w cieniach...", "allytips": ["Podążaj za śladami Krwawych Łowów, aby odn<PERSON>ć przeciwników z niskim poziomem zdrowia.", "Zasięg Bezkresne<PERSON> (R) skaluje się z prędkością ruchu, nawet tą otrzymaną z sojuszniczych wzmocnień i czarów przywoływacza.", "Paszcza Bestii (Q) będzie podążała za przeciwnikami, którzy uciekają, doska<PERSON>ją lub teleportują się, je<PERSON><PERSON> klawisz będzie trzymany."], "enemytips": ["Ataki Warwicka uzdrawiają go, gdy ma mało zdrowia. Zachowajcie efekty kontroli tłumu, aby go wyk<PERSON>ńczyć.", "Warwick zyskuje siłę w walce z przeciwnikami mającymi mało zdrowia. Zachowajcie wysoki poziom, aby nie dać mu przewagi.", "Zasięg użycia superumiejętności Warwicka skaluje się z jego prędkością ruchu."], "tags": ["Fighter", "Tank"], "partype": "<PERSON><PERSON>", "info": {"attack": 9, "defense": 5, "magic": 3, "difficulty": 3}, "stats": {"hp": 620, "hpperlevel": 99, "mp": 280, "mpperlevel": 35, "movespeed": 335, "armor": 33, "armorperlevel": 4.4, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 125, "hpregen": 4, "hpregenperlevel": 0.75, "mpregen": 7.45, "mpregenperlevel": 0.6, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 2.75, "attackspeedperlevel": 2.3, "attackspeed": 0.638}, "spells": [{"id": "WarwickQ", "name": "Paszcza Bestii", "description": "Warwick rzuca się do przodu i gryzie cel, zadając obrażenia zależne od jego maksymalnego poziomu zdrowia i uzdrawiając się o taką samą wartość.", "tooltip": "<tap>W<PERSON><PERSON><PERSON><PERSON>:</tap> Warwick wykonuje wypad do przodu i gryzie, zadając <magicDamage>obrażenia magiczne równe {{ basebitedamage }} pkt. plus {{ targetpercenthpdamage }}% maksymalnego zdrowia celu</magicDamage> oraz <healing>lecząc się o {{ e3 }}% zadanych obrażeń</healing>.<br /><br /><hold>Przytrzymaj:</hold> Warwick wykonuje wypad i przyczepia się do celu, skacząc na jego plecy. Gdy Warwick jest przyczepiony, podąża za celem. Po puszczeniu zadaje takie same obrażenia i leczy się o taką samą wartość.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Leczenie", "% obrażeń od zdrowia", "Czas odnowienia", "Koszt (@AbilityResourceName@)"], "effect": ["{{ e3 }}% -> {{ e3NL }}%", "{{ targetpercenthpdamage }}% -> {{ targetpercenthpdamageNL }}%", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [8, 7.5, 7, 6.5, 6], "cooldownBurn": "8/7.5/7/6.5/6", "cost": [80, 85, 90, 95, 100], "costBurn": "80/85/90/95/100", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [100, 150, 200, 250, 300], [25, 37.5, 50, 62.5, 75], [150, 165, 180, 195, 210], [450, 450, 450, 450, 450], [200, 200, 200, 200, 200], [300, 300, 300, 300, 300], [425, 425, 425, 425, 425], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "100/150/200/250/300", "25/37.5/50/62.5/75", "150/165/180/195/210", "450", "200", "300", "425", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [365, 365, 365, 365, 365], "rangeBurn": "365", "image": {"full": "WarwickQ.png", "sprite": "spell15.png", "group": "spell", "x": 384, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "WarwickW", "name": "Krwawe Łowy", "description": "Warwick wyczuwa wrogów mających mniej niż 50% zdrowia, zyskując prędkość ruchu, gdy zmierza w ich stronę, i prędko<PERSON> ataku, gdy ich atakuje. Gdy poziom ich zdrowia spadnie poniżej 25%, Warwick wpada w szał i premie się potrajają.", "tooltip": "<spellPassive>Biernie:</spellPassive> Warwick wyczuwa bohaterów mających mniej niż 50% zdrowia, zys<PERSON>jąc <speed>{{ passivemsbonus }}% prędkości ruchu</speed>, gdy zmierza w ich stronę. Zaklęcia i ataki przeciwko wrogom mającym mniej niż 50% zdrowia zapewniają <speed>{{ passiveasbonus }}% prędkości ataku</speed>. Efekty wzrastają o 200% przeciwko wrogom mającym mniej niż 25% zdrowia. <br /><br /><spellActive>Użycie:</spellActive> Warwick przez chwilę wyczuwa wszystkich wrogów i na 8 sek. zyskuje bierne efekty tej umiejętności przeciwko najbliższemu bohaterowi, bez względu na poziom jego zdrowia. Jeś<PERSON> nie wykryje żadnego bohatera, czas odnowienia tej umiejętności zostaje skrócony o 30%.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ruchu", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ataku", "Czas odnowienia"], "effect": ["{{ passivemsbonus }}% -> {{ passivemsbonusNL }}%", "{{ passiveasbonus }}% -> {{ passiveasbonusNL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [80, 70, 60, 50, 40], "cooldownBurn": "80/70/60/50/40", "cost": [55, 55, 55, 55, 55], "costBurn": "55", "datavalues": {}, "effect": [null, [35, 42.5, 50, 57.5, 65], [70, 80, 90, 100, 110], [10, 15, 20, 25, 30], [80, 90, 100, 110, 120], [30, 30, 30, 30, 30], [8, 8, 8, 8, 8], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/42.5/50/57.5/65", "70/80/90/100/110", "10/15/20/25/30", "80/90/100/110/120", "30", "8", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "2", "range": [4000, 4000, 4000, 4000, 4000], "rangeBurn": "4000", "image": {"full": "WarwickW.png", "sprite": "spell15.png", "group": "spell", "x": 432, "y": 144, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "WarwickE", "name": "Pierwotne Wycie", "description": "Warwick otrzymuje mniej obrażeń przez 2,5 sek. Po zakończeniu efektu lub ponownym użyciu wyje, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, że pobliscy przeciwnicy uciekają przez 1 sek.", "tooltip": "Warwick zyskuje zmniejszenie obrażeń równe {{ e1 }}% na 2,5 sek. Po zakończeniu tego efektu Warwick wyje, <status>p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></status> pobliskich wrogów na {{ e3 }} sek. <PERSON> m<PERSON> <recast>pono<PERSON><PERSON></recast> tej <PERSON>, by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON> w<PERSON>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Zmniejszenie obrażeń", "Czas odnowienia"], "effect": ["{{ e1 }}% -> {{ e1NL }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [15, 14, 13, 12, 11], "cooldownBurn": "15/14/13/12/11", "cost": [40, 40, 40, 40, 40], "costBurn": "40", "datavalues": {}, "effect": [null, [35, 40, 45, 50, 55], [2.75, 2.75, 2.75, 2.75, 2.75], [1, 1, 1, 1, 1], [1, 1, 1, 1, 1], [800, 800, 800, 800, 800], [400, 400, 400, 400, 400], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "35/40/45/50/55", "2.75", "1", "1", "800", "400", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [375, 375, 375, 375, 375], "rangeBurn": "375", "image": {"full": "WarwickE.png", "sprite": "spell16.png", "group": "spell", "x": 0, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}, {"id": "WarwickR", "name": "Bezkresna <PERSON>", "description": "Warwick rzuca się w danym kierunku (odległ<PERSON><PERSON><PERSON> skoku skaluje się z jego dodatkową prędkością ruchu) i na 1,5 sek. przygważdża pierwszego bohatera, którego trafi.", "tooltip": "Warwick skacze na ogromną odleg<PERSON>, kt<PERSON>ra zwiększa się wraz z jego <speed>pręd<PERSON>ścią ruchu</speed>, a następnie <status>przygważdża</status> pierwszego bohatera na swojej drodze na {{ rduration }} sek. W tym czasie trzykrotnie go atakuje, zadając <magicDamage>{{ damagecumulative }} pkt. obrażeń magicznych</magicDamage>. Warwick leczy się o <healing>100% wszystkich zadanych obrażeń</healing> podczas przygotowania umiejętności.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Obrażenia", "Czas odnowienia"], "effect": ["{{ rbasedamage }} -> {{ rbasedamageNL }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [110, 90, 70], "cooldownBurn": "110/90/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " pkt. ({{ abilityresourcename }})", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "WarwickR.png", "sprite": "spell16.png", "group": "spell", "x": 48, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} pkt. ({{ abilityresourcename }})"}], "passive": {"name": "Wieczny Głód", "description": "Ataki podstawowe Warwicka zadają dodatkowe obrażenia magiczne. <PERSON><PERSON><PERSON> ma mniej niż połowę zdrowia, zostaje uleczony o wartość dodatkowych obrażeń. Jeśli ma mniej niż 25% zdrowia, efekt leczenia zostaje potrojony.", "image": {"full": "WarwickP.png", "sprite": "passive5.png", "group": "passive", "x": 240, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}