{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Aatrox": {"id": "Aatrox", "key": "266", "name": "エイトロックス", "title": "ダーキンの暴剣", "image": {"full": "Aatrox.png", "sprite": "champion0.png", "group": "champion", "x": 0, "y": 0, "w": 48, "h": 48}, "skins": [{"id": "266000", "num": 0, "name": "default", "chromas": false}, {"id": "266001", "num": 1, "name": "正義の執行者エイトロックス", "chromas": false}, {"id": "266002", "num": 2, "name": "メカトロックス", "chromas": true}, {"id": "266003", "num": 3, "name": "海賊狩りエイトロックス", "chromas": false}, {"id": "266007", "num": 7, "name": "ブラッドムーン エイトロックス", "chromas": false}, {"id": "266008", "num": 8, "name": "プレステージ ブラッドムーン エイトロックス", "chromas": false}, {"id": "266009", "num": 9, "name": "勝利の栄光エイトロックス", "chromas": true}, {"id": "266011", "num": 11, "name": "オデッセイ エイトロックス", "chromas": true}, {"id": "266020", "num": 20, "name": "プレステージ ブラッドムーン エイトロックス(2022)", "chromas": false}, {"id": "266021", "num": 21, "name": "ルナーエクリプス エイトロックス", "chromas": true}, {"id": "266030", "num": 30, "name": "DRX エイトロックス", "chromas": true}, {"id": "266031", "num": 31, "name": "プレステージ DRX エイトロックス", "chromas": false}, {"id": "266033", "num": 33, "name": "プライモーディアン エイトロックス", "chromas": true}], "lore": "ヴォイドからシュリーマを守り抜いた誇り高き存在であったエイトロックスとその同胞は、いつしかルーンテラにとってヴォイドを上回る脅威となり、狡猾な定命の者の魔法の前に敗れ去った。数世紀にも及ぶ幽閉を経て、エイトロックスは彼の精髄を封じていた魔の武器を手にした愚か者の肉体を奪い、再び自由の身となることに成功した。奪った肉体をかつての姿へと変え、ルーンテラを闊歩する彼は、長らく望んできた復讐──世界を終焉させる機会をうかがっている。", "blurb": "ヴォイドからシュリーマを守り抜いた誇り高き存在であったエイトロックスとその同胞は、いつしかルーンテラにとってヴォイドを上回る脅威となり、狡猾な定命の者の魔法の前に敗れ去った。数世紀にも及ぶ幽閉を経て、エイトロックスは彼の精髄を封じていた魔の武器を手にした愚か者の肉体を奪い、再び自由の身となることに成功した。奪った肉体をかつての姿へと変え、ルーンテラを闊歩する彼は、長らく望んできた復讐──世界を終焉させる機会をうかがっている。", "allytips": ["敵に当てやすいように「ダーキンブレード」使用中に「影進撃」を使用しよう。", "「炎獄の鎖」のような行動妨害スキルや味方の移動不能効果を使えば「ダーキンブレード」を当てやすくなる。", "確実に戦闘に持ち込めそうなら「ワールドエンダー」を使おう。"], "enemytips": ["エイトロックスの攻撃はタイミングを読みやすいので、攻撃が当たる前に攻撃範囲の外に出よう。", "「炎獄の鎖」は左右またはエイトロックスに向かって移動すると避けやすくなる。", "エイトロックスがアルティメットスキルを使ったら距離を取り、復活されないようにしよう。"], "tags": ["Fighter"], "partype": "ブラッドウェル", "info": {"attack": 8, "defense": 4, "magic": 3, "difficulty": 4}, "stats": {"hp": 650, "hpperlevel": 114, "mp": 0, "mpperlevel": 0, "movespeed": 345, "armor": 38, "armorperlevel": 4.8, "spellblock": 32, "spellblockperlevel": 2.05, "attackrange": 175, "hpregen": 3, "hpregenperlevel": 0.5, "mpregen": 0, "mpregenperlevel": 0, "crit": 0, "critperlevel": 0, "attackdamage": 60, "attackdamageperlevel": 5, "attackspeedperlevel": 2.5, "attackspeed": 0.651}, "spells": [{"id": "AatroxQ", "name": "ダーキンブレード", "description": "大剣を叩きつけて物理ダメージを与える。剣を3回振ることが可能で、振るごとに効果範囲が変化する。", "tooltip": "大剣を叩きつけて<physicalDamage>{{ qdamage }}の物理ダメージ</physicalDamage>を与える。剣先が当たった場合は対象を少しの間<status>ノックアップ</status>させて、代わりに<physicalDamage>{{ qedgedamage }}</physicalDamage>のダメージを与える。このスキルは2回<recast>再発動</recast>可能で、発動するごとに効果範囲が変わり、ダメージも25%増加する。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ", "合計攻撃力反映率"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ qbasedamage }} -> {{ qbasedamageNL }}", "{{ qtotaladratio*100.000000 }}% -> {{ qtotaladrationl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [14, 12, 10, 8, 6], "cooldownBurn": "14/12/10/8/6", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxQ.png", "sprite": "spell0.png", "group": "spell", "x": 384, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "AatroxW", "name": "炎獄の鎖", "description": "地面を叩きつけて、最初に命中した敵にダメージを与える。チャンピオンと大型モンスターは数秒以内に攻撃範囲から出なければ、中央に引き寄せられて再度ダメージを受ける。", "tooltip": "鎖を放ち、最初に命中した敵に{{ wslowduration }}秒間{{ wslowpercentage*-100 }}%の<status>スロウ効果</status>を与え、<physicalDamage>{{ wdamage }}の物理ダメージ</physicalDamage>を与える。チャンピオンと大型ジャングルモンスターは、命中時に発生したエリアから{{ wslowduration }}秒以内に出なければ、中心に向かって<status>引き寄せられ</status>、再び同量のダメージを受ける。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン", "ダメージ", "スロウ効果"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}", "{{ wbasedamage }} -> {{ wbasedamageNL }}", "{{ wslowpercentage*-100.000000 }}% -> {{ wslowpercentagenl*-100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [825, 825, 825, 825, 825], "rangeBurn": "825", "image": {"full": "AatroxW.png", "sprite": "spell0.png", "group": "spell", "x": 432, "y": 48, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "AatroxE", "name": "影進撃", "description": "自動効果として、敵チャンピオンにダメージを与えると体力が回復する。発動すると、指定方向に向かってダッシュする。", "tooltip": "<spellPassive>自動効果:</spellPassive> 敵チャンピオンに与えたダメージの<lifeSteal>{{ totalevamp }}</lifeSteal>にあたる体力を回復する。<br /><br /><spellActive>発動効果:</spellActive> ダッシュする。このスキルは他のスキルの発動中にも使用できる。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["クールダウン"], "effect": ["{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 5, "cooldown": [9, 8, 7, 6, 5], "cooldownBurn": "9/8/7/6/5", "cost": [0, 0, 0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxE.png", "sprite": "spell0.png", "group": "spell", "x": 0, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}, {"id": "AatroxR", "name": "ワールドエンダー", "description": "悪魔形態を解放して周囲の敵ミニオンにフィアー効果を与え、攻撃力、回復量、移動速度が増加する。キルまたはアシストを獲得した場合、この効果が延長される。", "tooltip": "悪魔としての真の姿をさらし、{{ rminionfearduration }}秒間周囲のミニオンに<status>フィアー効果</status>を与えて、<speed>移動速度が{{ rmovementspeedbonus*100 }}%</speed>増加する({{ rduration }}秒かけて元に戻る)。また、この効果時間中は<scaleAD>攻撃力が{{ rtotaladamp*100 }}%</scaleAD>、<healing>自己回復量が{{ rhealingamp*100 }}%</healing>増加する。<br /><br />チャンピオンからキルまたはアシストを奪うと効果時間が{{ rextension }}秒延長されて、<speed>移動速度</speed>の増加効果が更新される。{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["合計攻撃力増加量", "回復量増加", "移動速度", "クールダウン"], "effect": ["{{ rtotaladamp*100.000000 }}% -> {{ rtotaladampnl*100.000000 }}%", "{{ rhealingamp*100.000000 }}% -> {{ rhealingampnl*100.000000 }}%", "{{ rmovementspeedbonus*100.000000 }}% -> {{ rmovementspeedbonusnl*100.000000 }}%", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 100, 80], "cooldownBurn": "120/100/80", "cost": [0, 0, 0], "costBurn": "0", "datavalues": {}, "effect": [null, [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": "コスト無し", "maxammo": "-1", "range": [25000, 25000, 25000], "rangeBurn": "25000", "image": {"full": "AatroxR.png", "sprite": "spell0.png", "group": "spell", "x": 48, "y": 96, "w": 48, "h": 48}, "resource": "コスト無し"}], "passive": {"name": "死兆の構え", "description": "一定時間ごとに、次の通常攻撃が対象の最大体力に応じた追加<physicalDamage>物理ダメージ</physicalDamage>を与え、同量の体力を回復する。 ", "image": {"full": "Aatrox_Passive.png", "sprite": "passive0.png", "group": "passive", "x": 0, "y": 0, "w": 48, "h": 48}}, "recommended": []}}}