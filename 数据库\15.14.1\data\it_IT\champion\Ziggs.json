{"type": "champion", "format": "standAloneComplex", "version": "15.14.1", "data": {"Ziggs": {"id": "<PERSON><PERSON>", "key": "115", "name": "<PERSON><PERSON>", "title": "l'esperto di hexplosivi", "image": {"full": "Ziggs.png", "sprite": "champion5.png", "group": "champion", "x": 336, "y": 48, "w": 48, "h": 48}, "skins": [{"id": "115000", "num": 0, "name": "default", "chromas": false}, {"id": "115001", "num": 1, "name": "<PERSON><PERSON>", "chromas": false}, {"id": "115002", "num": 2, "name": "<PERSON><PERSON><PERSON>", "chromas": false}, {"id": "115003", "num": 3, "name": "<PERSON>iggs Festa in Piscina", "chromas": false}, {"id": "115004", "num": 4, "name": "<PERSON><PERSON> Nevicata", "chromas": false}, {"id": "115005", "num": 5, "name": "Ziggs Maestro dell'Arcano", "chromas": false}, {"id": "115006", "num": 6, "name": "<PERSON><PERSON> Boss da Battaglia", "chromas": false}, {"id": "115007", "num": 7, "name": "<PERSON>iggs dell'Odissea", "chromas": true}, {"id": "115014", "num": 14, "name": "<PERSON>iggs Corsa al dolcetto", "chromas": true}, {"id": "115023", "num": 23, "name": "<PERSON>iggs Hextech", "chromas": false}, {"id": "115024", "num": 24, "name": "BZZZiggs", "chromas": true}, {"id": "115033", "num": 33, "name": "Ziggs la Ilusión", "chromas": true}], "lore": "Amante di grandi bombe e di micce corte, lo yordle <PERSON> è un'esplosiva forza della natura. Un tempo assistente di un inventore a Piltover, la routine lo annoiava e così divenne amico di una bombarola fuori di testa dai capelli blu, una certa Jinx. Dopo una nottata di follie in città, <PERSON><PERSON> seguì il suo consiglio e si trasferì a Zaun, dove ora può dedicarsi più liberamente alla sua passione terrorizzando baroni chimici e comuni cittadini nella sua incessante ricerca di cose da far saltare in aria.", "blurb": "Amante di grandi bombe e di micce corte, lo yordle <PERSON> è un'esplosiva forza della natura. Un tempo assistente di un inventore a Piltover, la routine lo annoiava e così divenne amico di una bombarola fuori di testa dai capelli blu, una certa Jinx. Dopo...", "allytips": ["Anche se sei lontano da un combattimento puoi sempre contribuire con Mega bomba infernale.", "Rallentare i tuoi nemici con Campo minato hexplosivo ti rende più facile colpirli con altre abilità.", "Lanciarti al di là dei muri con Carica satchel è perfetto quando devi inseguire i nemici o scappare dagli inseguitori."], "enemytips": ["Non calpestare le mine di Ziggs! Ti rallenteranno e sarai un bersaglio più facile per le sue altre abilità.", "Molte delle abilità di Ziggs hanno dei tempi di ricarica lunghi. Cerca di prenderlo appena dopo averle usate.", "L'abilità suprema di Ziggs, Mega bomba infernale, infligge più danni al centro dell'esplosione."], "tags": ["Mage"], "partype": "<PERSON><PERSON>", "info": {"attack": 2, "defense": 4, "magic": 9, "difficulty": 4}, "stats": {"hp": 606, "hpperlevel": 106, "mp": 480, "mpperlevel": 23.5, "movespeed": 325, "armor": 21, "armorperlevel": 4.7, "spellblock": 30, "spellblockperlevel": 1.3, "attackrange": 550, "hpregen": 6.5, "hpregenperlevel": 0.6, "mpregen": 8, "mpregenperlevel": 0.8, "crit": 0, "critperlevel": 0, "attackdamage": 55, "attackdamageperlevel": 3.1, "attackspeedperlevel": 2, "attackspeed": 0.656}, "spells": [{"id": "ZiggsQ", "name": "<PERSON><PERSON><PERSON>ola<PERSON>", "description": "<PERSON><PERSON> lancia una bomba che rimbalza e infligge danni magici.", "tooltip": "<PERSON><PERSON> lancia una bomba che rimbalza e infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage>.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto potere magico totale", "Ricarica", "Costo in @AbilityResourceName@"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ apratio }} -> {{ apratioNL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [6, 5.5, 5, 4.5, 4], "cooldownBurn": "6/5.5/5/4.5/4", "cost": [50, 55, 60, 65, 70], "costBurn": "50/55/60/65/70", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [850, 850, 850, 850, 850], [325, 325, 325, 325, 325], [225, 225, 225, 225, 225], [180, 180, 180, 180, 180], [240, 240, 240, 240, 240], [70, 70, 70, 70, 70], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "850", "325", "225", "180", "240", "70", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [850, 850, 850, 850, 850], "rangeBurn": "850", "image": {"full": "ZiggsQ.png", "sprite": "spell17.png", "group": "spell", "x": 288, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsW", "name": "<PERSON><PERSON> satchel", "description": "<PERSON><PERSON> scaglia una carica esplosiva che esplode dopo un periodo di tempo, o quando l'abilità viene attivata nuovamente. L'esplosione infligge danni magici ai nemici, respingen<PERSON><PERSON>. <PERSON><PERSON> viene respinto, ma non subisce danni. <br><br><PERSON><PERSON> può usare Carica satchel per far hexplodere le torri nemiche vulnerabili.", "tooltip": "<PERSON><PERSON> scaglia una carica esplosiva che esplode dopo {{ bombduration }} secondi, o quando l'abilità viene <recast>rilanciata</recast>. Infligge <magicDamage>{{ totaldamage }} danni magici</magicDamage> ai nemici colpiti, <status>respingendoli</status>. <PERSON><PERSON> viene respinto, ma non subisce danni.<br /><br />Carica satchel distrugge automaticamente le torri che hanno meno di un {{ turretdestroypercent*100 }}% di salute.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Ricarica", "Soglia distruzione torre"], "effect": ["{{ e1 }} -> {{ e1NL }}", "{{ cooldown }} -> {{ cooldownNL }}", "{{ turretdestroypercent*100.000000 }}% -> {{ turretdestroypercentnl*100.000000 }}%"]}, "maxrank": 5, "cooldown": [20, 18, 16, 14, 12], "cooldownBurn": "20/18/16/14/12", "cost": [80, 80, 80, 80, 80], "costBurn": "80", "datavalues": {}, "effect": [null, [70, 105, 140, 175, 210], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "70/105/140/175/210", "0", "0", "0", "0", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [1000, 1000, 1000, 1000, 1000], "rangeBurn": "1000", "image": {"full": "ZiggsW.png", "sprite": "spell17.png", "group": "spell", "x": 336, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsE", "name": "Campo minato hexplosivo", "description": "<PERSON>iggs sparge delle mine di prossimità che detonano a contatto con un nemico, infliggendo danni magici e rallentandolo. Le detonazioni successive contro lo stesso bersaglio infliggono danni ridotti.", "tooltip": "Ziggs sparge delle mine di prossimità che detonano a contatto con un nemico, infliggendo <magicDamage>{{ totaldamage }} danni magici</magicDamage> e <status>rallentandolo</status> di un {{ slow*-100 }}% per {{ e4 }} secondi. Le mine permangono per {{ e3 }} secondi.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["<PERSON><PERSON>", "Rapporto potere magico totale", "Rallentamento", "Costo in @AbilityResourceName@"], "effect": ["{{ damagepermine }} -> {{ damagepermineNL }}", "{{ apratiopermine }} -> {{ apratiopermineNL }}", "{{ slow*-100.000000 }}% -> {{ slownl*-100.000000 }}%", "{{ cost }} -> {{ costNL }}"]}, "maxrank": 5, "cooldown": [16, 16, 16, 16, 16], "cooldownBurn": "16", "cost": [70, 80, 90, 100, 110], "costBurn": "70/80/90/100/110", "datavalues": {}, "effect": [null, [0, 0, 0, 0, 0], [0, 0, 0, 0, 0], [10, 10, 10, 10, 10], [1.5, 1.5, 1.5, 1.5, 1.5], [0.4, 0.4, 0.4, 0.4, 0.4], [0, 0, 0, 0, 0], [135, 135, 135, 135, 135], [150, 150, 150, 150, 150], [0, 0, 0, 0, 0], [0, 0, 0, 0, 0]], "effectBurn": [null, "0", "0", "10", "1.5", "0.4", "0", "135", "150", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [900, 900, 900, 900, 900], "rangeBurn": "900", "image": {"full": "ZiggsE.png", "sprite": "spell17.png", "group": "spell", "x": 384, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}, {"id": "ZiggsR", "name": "Mega bomba infernale", "description": "<PERSON>iggs schiera la sua ultima creazione, la Mega bomba infernale, scagliandola a enorme distanza. I nemici nella zona principale dell'esplosione subiscono più danni di quelli più lontani dal centro.", "tooltip": "<PERSON><PERSON> scatena la sua più grande creazione, che infligge <magicDamage>{{ empowereddamage }} danni magici</magicDamage> al centro dell'esplosione o <magicDamage>{{ blastdamage }} danni magici</magicDamage> al limite della sua portata.{{ spellmodifierdescriptionappend }}", "leveltip": {"label": ["Danni zona dell'esplosione", "<PERSON><PERSON>", "Ricarica"], "effect": ["{{ basedamage }} -> {{ basedamageNL }}", "{{ basedamage*0.667000 }} -> {{ basedamagenl*0.667000 }}", "{{ cooldown }} -> {{ cooldownNL }}"]}, "maxrank": 3, "cooldown": [120, 95, 70], "cooldownBurn": "120/95/70", "cost": [100, 100, 100], "costBurn": "100", "datavalues": {}, "effect": [null, [300, 450, 600], [66.6667, 66.6667, 66.6667], [525, 525, 525], [250, 250, 250], [200, 300, 400], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0], [0, 0, 0]], "effectBurn": [null, "300/450/600", "66.67", "525", "250", "200/300/400", "0", "0", "0", "0", "0"], "vars": [], "costType": " {{ abilityresourcename }}", "maxammo": "-1", "range": [5000, 5000, 5000], "rangeBurn": "5000", "image": {"full": "ZiggsR.png", "sprite": "spell17.png", "group": "spell", "x": 432, "y": 0, "w": 48, "h": 48}, "resource": "{{ cost }} {{ abilityresourcename }}"}], "passive": {"name": "<PERSON><PERSON><PERSON> corta", "description": "Periodicamente, il prossimo attacco base di Ziggs infligge danni magici bonus. Questo tempo di ricarica diminuisce ogni volta che Ziggs usa un'abilità.", "image": {"full": "ZiggsPassiveReady.png", "sprite": "passive5.png", "group": "passive", "x": 336, "y": 48, "w": 48, "h": 48}}, "recommended": []}}}